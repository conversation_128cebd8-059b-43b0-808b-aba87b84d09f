.container {
  height: calc(70vh - 28px);
  position: relative;
  .title_box {
    position: relative;
    .title {
      font-size: 17px;
      color: #000;
      font-weight: 500;
      line-height: 24px;
      text-align: center;
    }
    .title_btn {
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      padding: 0 16px;
    }
  }

  .search_box {
    padding: 15px 16px 0;
    background: #fff;
    .search_content {
      background: #F5F5F5;
      border-radius: 23px;
      display: flex;
      align-items: center;
      padding: 0 12px;
      .search_icon {
        width: 20px;
        height: 20px;
      }
      .search_input {
        flex: 1;
        height: 40px;
        :global {
          .adm-input {
            height: 100%;
          }
          .adm-input-element {
            height: 100%;
            font-size: 14px;
            color: #000;
            padding-left: 8px;
          }
        }
      }
    }
  }

  .data_box {
    padding: 16px 16px 0;
    height: calc(70vh - 155px);
    overflow-y: auto;
    .item_box {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      margin-bottom: 16px;
      .avatar {
        width: 44px;
        min-width: 44px;
        height: 44px;
        border-radius: 50%;
        margin-right: 8px;

        .no_comment_head{
          width: 44px;
          height: 44px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          font-weight: 500;
          color: #fff;
          white-space: nowrap;
        }

        img {
          width: 44px;
          height: 44px;
          border-radius: 50%;
        }
      }
      .info_box {
        flex: 1;
        .info_1 {
          display: flex;
          align-items: center;
          .info_name {
            font-size: 15px;
            color: #000;
            font-weight: 500;
            margin-right: 4px;
            line-height: 21px;
            word-break:break-all;
          }
          .info_phone {
            font-size: 13px;
            color: #999;
            line-height: 18px;
          }
        }
        .info_2 {
          font-size: 13px;
          color: #999;
          line-height: 18px;
          word-break: break-all;

          .lines {
            width: 1px;
            height: 12px;
            background: #999;
            margin: 0 8px;
            display: inline-block;
            position: relative;
            top: 1px;
          }
        }
      }
      .item_btn {
        height: 29px;
        line-height: 29px;
        text-align: center;
        padding: 0 8px;
        min-width: 48px;
        border-radius: 18px;
        font-size: 12px;
        color: #0095FF;
        background: #E6F4FF;
        white-space: nowrap;
        flex-shrink: 0;
        margin-left: 10px;
      }

      .item_active_btn {
        height: 29px;
        line-height: 29px;
        text-align: center;
        padding: 0 8px;
        min-width: 48px;
        border-radius: 18px;
        font-size: 12px;
        white-space: nowrap;
        color: #999;
        background: #F5F5F5;
        flex-shrink: 0;
        margin-left: 10px;
      }
    }
  }

  .fixed_box {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding-bottom: constant(safe-area-inset-bottom);//兼容 IOS<11.2
    padding-bottom: env(safe-area-inset-bottom);//兼容 IOS>11.2
    .btn_box {
      padding: 0 16px 8px;
      .btn {
        height: 40px;
        line-height: 40px;
        background: #0095FF;
        border-radius: 20px;
        text-align: center;
        font-size: 16px;
        color: #fff;
      }
    }
  }
  @media (max-height: 500px) {
    .fixed_box {
      display: none;
    }
  }
}

.nodata {
  font-size: 15px;
  font-family: PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 17px;
  margin-top: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .empty_title {
    font-size: 15px;
    color: #333;
    line-height: 21px;
    margin-bottom: 8px;
  }
  .empty_msg {
    font-size: 14px;
    color: #999;
    line-height: 20px;
  }

  img {
    width: 150px;
    height: 113px;
  }
}