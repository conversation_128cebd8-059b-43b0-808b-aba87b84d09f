/**
 * @Description: 收藏
 */
import React, { useEffect, useState, useCallback, useRef } from 'react';
import styles from './index.less';
import { history, connect } from 'umi';
import { Spin } from 'antd';
import { InfiniteScroll } from 'antd-mobile';
import SpaceList from '@/components/SpaceList'; // 空间列表
import MeetingCard from '@/components/MeetingCardInfo';            // 会议卡片组件
import CaseList from '@/components/CaseList'; // 病例列表
import NavBar from '@/components/NavBar'; // 头部返回组件
import noDataImg from '@/assets/GlobalImg/no_data.png'; // 暂无数据图片
import { getOperatingEnv } from '@/utils/utils';

// tab切换列表数据
const tabList = [
  {id: 1, text: '直播'},
  {id: 3, text: '会议'},
  {id: 2, text: '病例'}
]

const Index: React.FC = (props: any) => {
  const {dispatch, loading, tabState} = props || {};
  const { collectTabState } = tabState || {}; // 从仓库中取值
  const [tabType, setTabType] = useState(collectTabState || 1); // tab状态
  const [caseList, setCaseList] = useState<any[]>([]); // 病例列表
  const [spaceList, setSpaceList] = useState<any[]>([]); // 空间列表
  const [total, setTotal] = useState(0); // 总条数
  const [pageNum, setPageNum] = useState(1); // 当前页码
  const scrollDomRef = useRef<HTMLDivElement|null>(null);

  useEffect(() => {
    if (getOperatingEnv() === '4' ) {
      // 设置pc tab页为收藏
      dispatch({
        type: 'pcAccount/save',
        payload: {
          tabState: 4,
          subTabState: null,
        }
      })
      history.replace('/UserInfo')
    }
  }, [dispatch]);

  // 收藏病例列表
  const initCaseList = useCallback((currentPageNum: number) => {
    return dispatch({
      type: 'userInfoStore/getCollectList',
      payload: {
        pageNum: currentPageNum,
        pageSize: 30,
      }
    }).then(res => {
      const { code, content } = res || {};
      if(code == 200) {
        const { total: resTotal, resultList } = content || {};
        setCaseList(prevList => currentPageNum == 1 ? resultList : [...prevList, ...resultList])
        setPageNum(currentPageNum)
        setTotal(resTotal)
      } else {
        console.log('数据失败!')
      }
    })
  }, [dispatch])

  // 收藏空间列表
  const initSpaceList = useCallback((currentPageNum: number,tabType: number) => {
    return dispatch({
      type: 'userInfoStore/getStarSpaceCollect',
      payload: {
        pageNum: currentPageNum,
        pageSize: 30,
        starSpaceType:tabType==3?2:1
      }
    }).then((res) => {
      const { code, content } = res || {};
      if(res && code == 200) {
        const { total: resTotal, resultList } = content || {};
        setSpaceList(prevList => currentPageNum == 1 ? resultList : [...prevList, ...resultList])
        setPageNum(currentPageNum)
        setTotal(resTotal)
      } else {
        console.log('数据失败!')
      }
    })
  }, [dispatch])

  // 初始化、tab切换后调用接口（tabType 为 1 时，请求 直播列表，为2时，请求 病例列表, 为3时 请求 会议列表）
  useEffect(() => {
    if(tabType == 1||tabType == 3) {
      initSpaceList(1,tabType)
    } else {
      initCaseList(1)
    }
    // 切换后滚动到顶部
    scrollDomRef.current?.scrollTo({top: 0});
  }, [initCaseList, initSpaceList, tabType])

  // 加载更多（tabType 1 空间列表 2 病例列表 3会议列表）
  const loadMore = async () => {
    if(tabType == 1||tabType == 3) {
      await initSpaceList(pageNum + 1,tabType)
    } else {
      await initCaseList(pageNum + 1)
    }
  }

  // tab切换事件
  const tabSwitchFn = (item: { id: any; text?: string; }) => {
    setTabType(item.id)
    setTotal(0) // 清空total值，（病例和空间下拉加载hasmore用的是同一个total）
    // 保存到仓库中当前切换的tab值
    dispatch({
      type: 'tabState/save',
      payload: {
        collectTabState: item.id
      }
    })
  }

  const getCollectListLoading = !!loading.effects['userInfoStore/getCollectList']; // 获取收藏列表loading
  const getStarSpaceCollectLoading = !!loading.effects['userInfoStore/getStarSpaceCollect']; // 获取收藏空间列表loading

  return (
    <div className={styles.warp}>
      <NavBar title={'收藏'} onBack={()=>{history.replace('/UserInfo')}} />
      <div className={styles.collect_wrap}>
        <div className={styles.collect_tab_box}>
          {
            tabList.map((item) => {
              return <div key={item.id} className={tabType === item.id ? styles.tab_active : styles.tab_init} onClick={()=>tabSwitchFn(item)}>{item.text}</div>
            })
          }
        </div>
        <Spin spinning={getCollectListLoading || getStarSpaceCollectLoading}>
          <div className={styles.collect_content} ref={scrollDomRef}>
            {
              tabType == 1 ?
              <>
                {spaceList && spaceList.length ?
                  <>
                    <SpaceList componentData={{dataList: spaceList, config: {number: 2}}} style={{paddingTop: '8px'}} />
                    <InfiniteScroll threshold={100} loadMore={loadMore} hasMore={total > spaceList.length}/>
                  </>
                  :
                  <div className={styles.none_data}>
                    <img src={noDataImg} alt="" />
                    暂无收藏直播
                  </div>
                }
              </>:tabType == 3?<div className={styles.meeting_list_wrap}>
                {spaceList && spaceList.length ?
                  <>
                    {
                      spaceList.map((item:any, index:number) => {
                        return (
                          <div key={index} className={styles.meeting_item_wrap}>
                            <MeetingCard item={item} style={{minHeight: 'auto', paddingBottom: 12}} />
                          </div>
                        )
                      })
                    }
                    <InfiniteScroll threshold={100} loadMore={loadMore} hasMore={total > spaceList.length}/>
                  </>
                  :
                  <div className={styles.none_data}>
                    <img src={noDataImg} alt="" />
                    暂无收藏会议
                  </div>
                }
              </div>:
              <>
                {
                  caseList && caseList.length ?
                  <>
                    <CaseList componentData={{dataList: caseList}} style={{paddingTop: '16px'}} isShowImage={true}/>
                    <InfiniteScroll threshold={100} loadMore={loadMore} hasMore={total > caseList.length}/>
                  </> :
                  <div className={styles.none_data}>
                    <img src={noDataImg} alt="" />
                    暂无收藏病例
                  </div>
                }
              </>
            }
          </div>
        </Spin>
      </div>
    </div>
  )
}
export default connect(({ userInfoStore, loading, tabState }: any) => ({userInfoStore, loading, tabState}))(Index)
