import React from 'react'
import classNames from 'classnames'
import { stringify } from 'qs'
import PropTypes from 'prop-types'
import request from '@/utils/request'
import { getHeaders } from '@/utils/utils'
import { Tooltip, Popover, Upload, message } from 'antd'
import {
  CaretDownOutlined, BoldOutlined, ItalicOutlined, StrikethroughOutlined, FontColorsOutlined, UnderlineOutlined,
  NumberOutlined, VideoCameraAddOutlined, AlignLeftOutlined, AlignCenterOutlined, AlignRightOutlined,
} from '@ant-design/icons'
import styles from './index.less'
import Emoji from './Emoji'
import Topic from './Topic'
import Color from './Color'
import FontSize from './FontSize'
import Align from './Align'

class Index extends React.Component {
  static propTypes = {
    quillRef: PropTypes.object,                            // 编辑器ref
    quillHistoryStack: PropTypes.object,                   // 编辑历史记录
    quillFormat: PropTypes.object,                         // 编辑格式
    getUploadVideoOrImageLoading: PropTypes.func,          // loading
  }
  static defaultProps = {
    quillRef: {},
    quillHistoryStack: {},
    quillFormat: {},
    getUploadVideoOrImageLoading: () => {},
  }

  constructor(props) {
    super(props)
    this.state = {
      colorPopoverVisible: false,      // 颜色
      emojiPopoverVisible: false,      // 表情
      topicPopoverVisible: false,      // 话题
      fontSizePopoverVisible: false,   // 字体大小
      alignPopoverVisible: false,      // 对齐方式
    }
  }

  componentDidMount() {

  }

  // 撤销
  undoFn = () => {
    if (!this.props.quillHistoryStack.undo || this.props.quillHistoryStack.undo.length == 0) {
      return
    }
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.undoFn) {
      this.props.quillRef.current.undoFn()
    }
  }

  // 重做
  redoFn = () => {
    if (!this.props.quillHistoryStack.redo || this.props.quillHistoryStack.redo.length == 0) {
      return
    }
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.redoFn) {
      this.props.quillRef.current.redoFn()
    }
  }

  // 设置字体大小
  fontSizeFn = (value) => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.fontSizeFn) {
      this.props.quillRef.current.fontSizeFn(value)
    }
  }

  // 加粗
  boldFn = () => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.boldFn) {
      this.props.quillRef.current.boldFn()
    }
  }

  // 倾斜
  italicFn = () => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.italicFn) {
      this.props.quillRef.current.italicFn()
    }
  }

  // 删除线
  strikethroughFn = () => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.strikethroughFn) {
      this.props.quillRef.current.strikethroughFn()
    }
  }

  // 下划线
  underlineFn = () => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.underlineFn) {
      this.props.quillRef.current.underlineFn()
    }
  }

  // 对齐方式
  alignFn = (value) => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.alignFn) {
      this.props.quillRef.current.alignFn(value)
    }
  }

  // 文字颜色
  fontColorFn = (value) => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.fontColorFn) {
      this.props.quillRef.current.fontColorFn(value)
    }
  }

  // 添加话题
  topicFn = (value) => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.topicFn) {
      this.props.quillRef.current.topicFn(value)
    }
  }

  // 添加表情
  emojiFn = (emojiFileName) => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.emojiFn) {
      this.props.quillRef.current.emojiFn(emojiFileName)
    }
  }

  // 添加图片
  imageFn = (value) => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.imageFn) {
      this.props.quillRef.current.imageFn(value)
    }
  }

  // 添加图片进度条
  imageProgressFn = (value) => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.imageProgressFn) {
      this.props.quillRef.current.imageProgressFn(value)
    }
  }

  // 图片进度条改变
  imageProgressChangeFn = (value) => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.imageProgressChangeFn) {
      this.props.quillRef.current.imageProgressChangeFn(value)
    }
  }

  // 删除进度条
  imageProgressDeleteFn = (value) => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.imageProgressDeleteFn) {
      this.props.quillRef.current.imageProgressDeleteFn(value)
    }
  }

  // 添加视频
  videoFn = (value) => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.videoFn) {
      this.props.quillRef.current.videoFn(value)
    }
  }

  // 添加视频进度条
  videoProgressFn = (value) => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.videoProgressFn) {
      this.props.quillRef.current.videoProgressFn(value)
    }
  }

  // 视频进度条改变
  videoProgressChangeFn = (value) => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.videoProgressChangeFn) {
      this.props.quillRef.current.videoProgressChangeFn(value)
    }
  }

  // 删除进度条
  videoProgressDeleteFn = (value) => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.videoProgressDeleteFn) {
      this.props.quillRef.current.videoProgressDeleteFn(value)
    }
  }

  // 设置字体大小浮窗是否显示
  handleFontSizeOpenChange = (visible) => {
    this.setState({
      fontSizePopoverVisible: visible,
    })
  }

  // 选择字体大小回调
  fontSizeItemOnClick = (value) => {
    this.fontSizeFn(value)
    this.handleFontSizeOpenChange(false)
  }

  // 打开颜色弹窗
  handleColorOpenChange = (visible) => {
    this.setState({
      colorPopoverVisible: visible,
    })
  }

  // 选择颜色回调
  colorItemOnClick = (value) => {
    this.fontColorFn(value)
    this.handleColorOpenChange(false)
  }

  // 打开、关闭对齐方式弹窗
  handleAlignOpenChange = (visible) => {
    this.setState({
      alignPopoverVisible: visible,
    })
  }

  // 选择对齐方式回调
  alignItemOnClick = (value) => {
    this.alignFn(value)
    this.handleAlignOpenChange(false)
  }

  // 打开、关闭表情弹窗
  handleEmojiOpenChange = (visible) => {
    this.setState({
      emojiPopoverVisible: visible,
    })
  }

  // 选择表情回调
  emojiOnClick = (emojiFileName) => {
    this.emojiFn(emojiFileName)
    this.handleEmojiOpenChange(false)
  }

  // 打开、关闭话题弹窗
  handleTopicOpenChange = (visible) => {
    this.setState({
      topicPopoverVisible: visible,
    })
  }

  // 选择话题回调
  topicItemOnClick = (value) => {
    console.log('选择话题回调', value)
    this.topicFn(value)
    this.handleTopicOpenChange(false)
  }

  // 阻止默认事件
  onMouseDown = (e) => {
    e.preventDefault()
  }

  // 上传校验
  beforeUpload = (file) => {
    const isSize = file.size / 1024 / 1024 < 15
    if (!isSize) {
      message.error('超过15M限制，不允许上传~')
      return false
    }

    const { name: fileName } = file || {}
    const isJpgOrPng = file.type === 'image/jpg' || file.type === 'image/jpeg' ||  file.type === 'image/png' || file.type === 'image/gif'
    const suffix = fileName.substring(fileName.lastIndexOf('.')+1).toLowerCase()
    const isSuffixByJpgOrPng = (suffix === 'jpg' || suffix === 'jpeg' || suffix === 'png' || suffix === 'gif')
    if (!isJpgOrPng || !isSuffixByJpgOrPng) {
      message.error('只能上传JPG、JPEG、PNG、GIF格式的图片~')
      return false
    }
    this.props.getUploadVideoOrImageLoading(true)
    // 插入进度条
    this.imageProgressFn({
      code: file.uid,
      name: file.name,
      percent: 0,
    })
    return true
  }

  // 上传完成回调
  uploadOnChange = (info) => {
    console.log('上传完成回调',info)
    if (info && !info.file.status) {
      return
    }

    if (info.file.status === 'uploading') {
      // 更新进度条
      this.imageProgressChangeFn({
        code: info.file.uid,
        name: info.file.name,
        percent: info.file.percent.toFixed(1) + '%',
      })
    }

    if (info && info.file.status === 'error') {
      message.error('上传失败')
      this.props.getUploadVideoOrImageLoading(false)
      // 删除进度条
      this.imageProgressDeleteFn({
        code: info.file.uid,
      })
      return
    }

    if (info && info.file.status === 'done') {
      this.props.getUploadVideoOrImageLoading(false)
      const res = info.file.response || {}
      const { code, content, msg } = res
      if (code == 200 && content) {
        this.imageFn({
          code: info.file.uid,
          src: content.fileUrlView,
        })
      } else {
        message.error(msg || '上传失败')
        // 删除进度条
        this.imageProgressDeleteFn({
          code: info.file.uid,
        })
      }
    }
  }

  // 上传校验
  beforeUploadVideo = (file) => {
    const isSize = file.size / 1024 / 1024 < 200
    if (!isSize) {
      message.error('超过200M限制，不允许上传~')
      return false
    }
    const isVideo = file.type == 'video/mp4' || file.type == 'video/webm' || file.type == 'video/ogg'
    if (!isVideo) {
      message.error('只能上传mp4、webm、ogg格式的视频~')
      return false
    }
    this.props.getUploadVideoOrImageLoading(true)
    return true
  }

  // 上传完成回调
  uploadVideoOnChange = (info) => {
    if (info && !info.file.status) {
      return
    }

    if (info.file.status === 'uploading') {

    }

    if (info && info.file.status === 'error') {
      message.error('上传失败')
      this.props.getUploadVideoOrImageLoading(false)
      const res = info.file.response || {}
      const { code } = res
      if (code) {
        // 删除进度条
        this.videoProgressDeleteFn({
          code: code,
        })
      }
      return
    }

    if (info && info.file.status === 'done') {
      this.props.getUploadVideoOrImageLoading(false)
      const res = info.file.response || {}
      const { code, src, name } = res
      this.videoFn({
        code: code,
        src: src,
        name: name,
      })
    }
  }

  // 自定义上传方法
  uploadCustomRequest = ({onProgress,onError,onSuccess,data,filename,file,withCredentials,action,headers}) => {
    const fileNameOrigin = file.name
    const fileType = file.type
    const fileSuffix = fileNameOrigin.substring(file.name.lastIndexOf('.')+1)
    request(`/api/server/base/oss/signaturecom?fileType=23&contentType=${fileType}&fileSuffix=${fileSuffix}`, {
      method: 'GET',
      headers,
      isNoNeedCommonParams: true,
    }).then(res => {
      const { code, content } = res
      if (code == 200 && content) {
        // onSuccess(res)
        // 生成的签名URL。
        const url = content.signUrl;
        const fileName = content.fileName;
        const fileUrlView = content.fileUrlView;
        // 插入进度条
        this.videoProgressFn({
          code: fileName,
          name: fileNameOrigin,
          percent: 0,
        })

        let request = new XMLHttpRequest()
        request.onreadystatechange = () => {
          if (request.readyState == 4) {
            if (request.status == 200) {
              // 上传成功
              onSuccess({
                code: fileName,
                src: fileUrlView,
                name: fileNameOrigin,
              })
            } else {
              // 上传失败
              onError({
                code: fileName,
              })
            }
          }
        }
        request.upload.onprogress = (e) => {
          if (e.lengthComputable) {
            const percent = (e.loaded / e.total * 100).toFixed(1)
            // onProgress({percent: Number(percent)})
            console.log('进度：' + percent)
            // 更新进度条
            this.videoProgressChangeFn({
              code: fileName,
              name: fileNameOrigin,
              percent: percent + '%',
            })

          }
        }
        request.open('PUT', url);
        request.setRequestHeader('Content-type', fileType);
        request.send(new File([file], fileName));
      } else {
        onError()
      }
    }).catch(err => {
      onError()
    })
  }

  render() {
    const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    const { quillHistoryStack, quillFormat } = this.props
    const {
      colorPopoverVisible, emojiPopoverVisible, topicPopoverVisible, fontSizePopoverVisible, alignPopoverVisible,
    } = this.state

    return (
      <div id="toolbar" className={styles.toolbar}>
        <div className={styles.toolbar_wrap}>
          <Tooltip title="撤销">
            <div className={classNames(styles.toolbar_item, {
              [styles.disabled]: !quillHistoryStack.undo || quillHistoryStack.undo.length == 0,
            })} onClick={this.undoFn} onMouseDown={this.onMouseDown}>
              <i className={styles.toolbar_item_icon_undo}></i>
            </div>
          </Tooltip>
        </div>
        <div className={styles.toolbar_wrap}>
          <Tooltip title="重做">
            <div className={classNames(styles.toolbar_item, {
              [styles.disabled]: !quillHistoryStack.redo || quillHistoryStack.redo.length == 0,
            })} onClick={this.redoFn} onMouseDown={this.onMouseDown}>
              <i className={styles.toolbar_item_icon_redo}></i>
            </div>
          </Tooltip>
        </div>
        <div className={styles.toolbar_divider}></div>
        <div className={styles.toolbar_wrap}>
          <Tooltip title="字号">
            <Popover
              getPopupContainer={() => document.getElementById('toolbar')}
              trigger="click"
              onOpenChange={this.handleFontSizeOpenChange}
              visible={fontSizePopoverVisible}
              overlayClassName={styles.format_pop}
              content={<FontSize itemOnClick={this.fontSizeItemOnClick} selectedValue={quillFormat.size || '16px'} />}
              placement="bottom"
              showArrow={false}
            >
              <div className={classNames(styles.toolbar_item, styles.toolbar_item_font_size)} onMouseDown={this.onMouseDown}>
                <span className={styles.toolbar_item_value}>{quillFormat.size || '16px'}</span>
                <CaretDownOutlined />
              </div>
            </Popover>
          </Tooltip>
        </div>
        <div className={styles.toolbar_wrap}>
          <Tooltip title="加粗">
            <div className={classNames(styles.toolbar_item, {
              [styles.active]: quillFormat.bold,
            })} onClick={this.boldFn} onMouseDown={this.onMouseDown}>
              <BoldOutlined />
            </div>
          </Tooltip>
        </div>
        <div className={styles.toolbar_wrap}>
          <Tooltip title="倾斜">
            <div className={classNames(styles.toolbar_item, {
              [styles.active]: quillFormat.italic,
            })} onClick={this.italicFn} onMouseDown={this.onMouseDown}>
              <ItalicOutlined />
            </div>
          </Tooltip>
        </div>
        <div className={styles.toolbar_wrap}>
          <Tooltip title="删除线">
            <div className={classNames(styles.toolbar_item, {
              [styles.active]: quillFormat.strike,
            })} onClick={this.strikethroughFn} onMouseDown={this.onMouseDown}>
              <StrikethroughOutlined />
            </div>
          </Tooltip>
        </div>
        <div className={styles.toolbar_wrap}>
          <Tooltip title="下划线">
            <div className={classNames(styles.toolbar_item, {
              [styles.active]: quillFormat.underline,
            })} onClick={this.underlineFn} onMouseDown={this.onMouseDown}>
              <UnderlineOutlined />
            </div>
          </Tooltip>
        </div>
        <div className={styles.toolbar_divider}></div>
        <div className={styles.toolbar_wrap}>
          <Tooltip title="对齐方式">
            <Popover
              getPopupContainer={() => document.getElementById('toolbar')}
              trigger="click"
              onOpenChange={this.handleAlignOpenChange}
              visible={alignPopoverVisible}
              overlayClassName={styles.format_pop}
              content={<Align itemOnClick={this.alignItemOnClick} selectedValue={quillFormat.align || 'left'} />}
              placement="bottom"
              showArrow={false}
            >
              <div className={classNames(styles.toolbar_item, styles.toolbar_item_align)} onMouseDown={this.onMouseDown}>
                {
                  quillFormat.align == 'center' ? <AlignCenterOutlined />
                  : quillFormat.align == 'right' ? <AlignRightOutlined />
                  : <AlignLeftOutlined />
                }
                <CaretDownOutlined style={{fontSize: 12, marginLeft: 4}} />
              </div>
            </Popover>
          </Tooltip>
        </div>
        <div className={styles.toolbar_wrap}>
          <Tooltip title="文字颜色">
            <Popover
              getPopupContainer={() => document.getElementById('toolbar')}
              trigger="click"
              onOpenChange={this.handleColorOpenChange}
              visible={colorPopoverVisible}
              overlayClassName={styles.format_pop}
              content={<Color itemOnClick={this.colorItemOnClick} />}
              placement="bottom"
              showArrow={false}
            >
              <div className={styles.toolbar_item} onMouseDown={this.onMouseDown}>
                <FontColorsOutlined style={{color: quillFormat.color || '#000'}} />
              </div>
            </Popover>
          </Tooltip>
        </div>
        <div className={styles.toolbar_divider}></div>
        <div className={styles.toolbar_wrap}>
          <Tooltip title="添加图片">
            <div className={styles.toolbar_item} onMouseDown={this.onMouseDown}>
              <i className={styles.toolbar_item_icon_image}></i>
              <Upload
                headers={getHeaders()}
                accept="image/*"
                action={`/api/server/base/uploadFile?${stringify({ fileType: 23, userId: UserInfo?.friUserId})}`}
                onChange={this.uploadOnChange}
                beforeUpload={this.beforeUpload}
                showUploadList={false}
              />
            </div>
          </Tooltip>
        </div>
        <div className={styles.toolbar_wrap}>
          <Tooltip title="添加话题">
            <Popover
              getPopupContainer={() => document.getElementById('toolbar')}
              trigger="click"
              onOpenChange={this.handleTopicOpenChange}
              visible={topicPopoverVisible}
              overlayClassName={styles.format_pop}
              destroyTooltipOnHide
              content={<Topic itemOnClick={this.topicItemOnClick}/>}
              placement="bottom"
              showArrow={false}
            >
              <div className={styles.toolbar_item} onMouseDown={this.onMouseDown}>
                <NumberOutlined />
              </div>
            </Popover>
          </Tooltip>
        </div>
        <div className={styles.toolbar_wrap}>
          <Tooltip title="添加表情">
            <Popover
              getPopupContainer={() => document.getElementById('toolbar')}
              trigger="click"
              onOpenChange={this.handleEmojiOpenChange}
              visible={emojiPopoverVisible}
              overlayClassName={styles.format_pop}
              content={<Emoji emojiOnClick={this.emojiOnClick}/>}
              placement="bottom"
              showArrow={false}
            >
              <div className={styles.toolbar_item} onMouseDown={this.onMouseDown}>
                <i className={styles.toolbar_item_icon_emoji}></i>
              </div>
            </Popover>
          </Tooltip>
        </div>
        <div className={styles.toolbar_wrap}>
          <Tooltip title="添加视频">
            <div className={styles.toolbar_item} onMouseDown={this.onMouseDown}>
              <VideoCameraAddOutlined />
              <Upload
                headers={getHeaders()}
                accept="video/mp4"
                action={`/api/server/base/uploadFile?${stringify({ fileType: 23, userId: UserInfo?.friUserId})}`}
                onChange={this.uploadVideoOnChange}
                beforeUpload={this.beforeUploadVideo}
                showUploadList={false}
                customRequest={this.uploadCustomRequest}
              />
            </div>
          </Tooltip>
        </div>
      </div>
    )
  }
}

export default Index
