export default {
  namespace: 'tabState',
  state: {
    collectTabState: 1, // 收藏tab状态
    interestTabState: 0, // 关注tab状态
  },

  effects: {},

  reducers: {
    // 保存数据
    save(state, { payload }) {
      return {
        ...state,
        ...payload,
      }
    },
    // 清空数据
    clean(state, { payload }) {
      return {
        ...state,
        collectTabState: 1,
        interestTabState: 0
      }
    },
  },

  subscriptions: {
    setup({ dispatch, history }) {
      return history.listen(({ pathname, search }) => {
        if (
          pathname.indexOf('/UserInfo/Collect') == -1 &&
          pathname.indexOf('/Case/CaseDetails') == -1 &&
          pathname.indexOf('/UserInfo/Interest') == -1 &&
          pathname.indexOf('/Expert/ExpertDetails') == -1&&
          pathname.indexOf('/PlanetChatRoom') == -1&&
          pathname.indexOf('/Square') == -1
        ) {
          dispatch({
            type: 'clean',
          })
        }
      })
    }
  }
}
