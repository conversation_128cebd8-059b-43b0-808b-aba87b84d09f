import React, {Component} from 'react';
import {GridContent} from '@ant-design/pro-layout';
import styles from './style.less';
import {Button, Modal, Tag} from "antd";
import {toothUtils} from "@/utils/ToothSelect";

//牙位选择快捷按钮
const arrTag = [
  {id: '1', name: '乳牙'},
  {id: '2', name: '全口'},
  {id: '3', name: '左上'},
  {id: '4', name: '左下'},
  {id: '5', name: '右上'},
  {id: '6', name: '右下'},
  {id: '7', name: '上半口'},
  {id: '8', name: '下半口'},
  {id: '9', name: '清除'},
];
//恒牙面
const arrCircle = [
  {index: '8', num: '8', states: 'false'},
  {index: '7', num: '7', states: 'false'},
  {index: '6', num: '6', states: 'false'},
  {index: '5', num: '5', states: 'false'},
  {index: '4', num: '4', states: 'false'},
  {index: '3', num: '3', states: 'false'},
  {index: '2', num: '2', states: 'false'},
  {index: '1', num: '1', states: 'false'},
];
//恒牙
const arrCircleNum = [
  {index: '1', num: '1', states: 'false'},
  {index: '2', num: '2', states: 'false'},
  {index: '3', num: '3', states: 'false'},
  {index: '4', num: '4', states: 'false'},
  {index: '5', num: '5', states: 'false'},
  {index: '6', num: '6', states: 'false'},
  {index: '7', num: '7', states: 'false'},
  {index: '8', num: '8', states: 'false'},
];
//乳牙面
const arrEnglist = [
  {index: '5', letter: 'E'},
  {index: '4', letter: 'D'},
  {index: '3', letter: 'C'},
  {index: '2', letter: 'B'},
  {index: '1', letter: 'A'}
];
//乳牙对应标识
const arrEnglistOrder = [
  {index: '1', letter: 'A'},
  {index: '2', letter: 'B'},
  {index: '3', letter: 'C'},
  {index: '4', letter: 'D'},
  {index: '5', letter: 'E'}
];
//选中取值
const quzhi = {
  A: 1,
  B: 2,
  C: 3,
  D: 4,
  E: 5,
  8: 8,
  7: 7,
  6: 6,
  5: 5,
  4: 4,
  3: 3,
  2: 2,
  1: 1,
}
//质检中选择牙位
class ToothSelectQuality extends Component {
  constructor(props) {
    super(props);
    this.state = {
      tooth: {//牙位
        englishVal: [],//按钮
        englishValTopRight: [],//右上
        englishValBotLeft: [],//左下
        englishValBotRight: [],//左下
      },
      toothSelect: {//牙位选中
        englishVal1: [],//左上
        englishValTopRight1: [],//右上
        englishValBotLeft1: [],//左下
        englishValBotRight1: [],//右下
        englishVal2: [],//左上
        englishValTopRight2: [],//右上
        englishValBotLeft2: [],//左下
        englishValBotRight2: [],//右下
      },
    }
  }
  //初始化
  componentDidMount() {
    this.props.onRef(this);
    let toothPosition = this.props.toothPosition;
    var p = /[a-z]/i;
    let check = {
      englishVal: [],//左上
      englishValTopRight: [],//右上
      englishValBotLeft: [],//左下
      englishValBotRight: [],//右下
    }
    let select = {
      englishVal1: [],//左上
      englishValTopRight1: [],//右上
      englishValBotLeft1: [],//左下
      englishValBotRight1: [],//右下
      englishVal2: [],//左上
      englishValTopRight2: [],//右上
      englishValBotLeft2: [],//左下
      englishValBotRight2: [],//右下
    }
    if(toothPosition) {
      let toothArr = toothPosition.trim().split(";");
      for(let t of toothArr){
        let str =  t[0];
        if(str==="1"){
          check.englishVal.push(t[1]);
          if(p.test(t[1])){
            select.englishVal2[quzhi[t[1]]] = true;
          }else{
            select.englishVal1[quzhi[t[1]]] = true;
          }
        }else if(str==="2"){
          check.englishValTopRight.push(t[1]);
          if(p.test(t[1])){
            select.englishValTopRight2[quzhi[t[1]]] = true;
          }else{
            select.englishValTopRight1[quzhi[t[1]]] = true;
          }
        }else if(str==="4"){
          check.englishValBotLeft.push(t[1]);
          if(p.test(t[1])){
            select.englishValBotLeft2[quzhi[t[1]]] = true;
          }else{
            select.englishValBotLeft1[quzhi[t[1]]] = true;
          }
        }else if(str==="3"){
          check.englishValBotRight.push(t[1]);
          if(p.test(t[1])){
            select.englishValBotRight2[quzhi[t[1]]] = true;
          }else{
            select.englishValBotRight1[quzhi[t[1]]] = true;
          }
        }
      }
    }

    this.state.tooth = check;
    this.setState({
      toothSelect:select
    })
  }
  //获取牙位数据
  getTooth(){
    let e = this.state.tooth;
    let tl=1+e.englishVal.join(";1");
    let tr=2+e.englishValTopRight.join(";2");
    let bl=4+e.englishValBotLeft.join(";4");
    let br=3+e.englishValBotRight.join(";3");
    return (e.englishVal.length>0?tl.substr(0,tl.length)+";":"")+
      (e.englishValTopRight.length>0?tr.substr(0,tr.length)+";":"")+
      (e.englishValBotLeft.length>0?bl.substr(0,bl.length)+";":"")+
      (e.englishValBotRight.length>0?br.substr(0,br.length):"");
  }


  //恒牙点击事件
  btnClickNum = (e, uid, item, o,) => {
    let result = {};
    if(o!=true){
      result = toothUtils.selectTooth(this.state.tooth,this.state.toothSelect,"englishVal",uid,item.num,1);
    }else{
      result = toothUtils.noSelectTooth(this.state.tooth,this.state.toothSelect,"englishVal",uid,item.num,1);
    }
    this.state.tooth = result.tooth;
    return this.setState({
      toothSelect: result.toothSelect,
    });

  };
  //乳牙点击事件
  btnClickLetter = (e, index, item,o) => {
    let result = {};
    if(o!=true) {
      result = toothUtils.selectTooth(this.state.tooth,this.state.toothSelect,"englishVal",index,item.letter,2);
    }else{
      result = toothUtils.noSelectTooth(this.state.tooth,this.state.toothSelect,"englishVal",index,item.letter,2);
    }
    this.state.tooth = result.tooth;
    return this.setState({
      toothSelect: result.toothSelect,
    });

  };
  //右侧恒牙牙位点击事件
  btnClickRightNum = (e, index, item,o) => {
    let result = {};
    if(o!=true) {
      result = toothUtils.selectTooth(this.state.tooth,this.state.toothSelect,"englishValTopRight",index,item.num,1);
    }else{
      result = toothUtils.noSelectTooth(this.state.tooth,this.state.toothSelect,"englishValTopRight",index,item.num,1);
    }
    this.state.tooth = result.tooth;
    return this.setState({
      toothSelect: result.toothSelect,
    });

  };
  //右侧乳牙点击事件
  btnClickRightLetter = (e, index, item,o) => {
    let result = {};
    if(o!=true) {
      result = toothUtils.selectTooth(this.state.tooth,this.state.toothSelect,"englishValTopRight",index,item.letter,2);
    }else{
      result = toothUtils.noSelectTooth(this.state.tooth,this.state.toothSelect,"englishValTopRight",index,item.letter,2);
    }
    this.state.tooth = result.tooth;
    return this.setState({
      toothSelect: result.toothSelect,
    });
  };
  //左侧乳牙点击事件
  btnClickLeftLetter = (e, index, item,o) => {
    let result = {};
    if(o!=true) {
      result = toothUtils.selectTooth(this.state.tooth,this.state.toothSelect,"englishValBotLeft",index,item.letter,2);
    }else{
      result = toothUtils.noSelectTooth(this.state.tooth,this.state.toothSelect,"englishValBotLeft",index,item.letter,2);
    }
    this.state.tooth = result.tooth;
    return this.setState({
      toothSelect: result.toothSelect,
    });

  };
  //右侧恒牙点击事件
  btnClickLeftNum = (e, index, item,o) => {
    let result = {};
    if(o!=true) {
      result = toothUtils.selectTooth(this.state.tooth,this.state.toothSelect,"englishValBotLeft",index,item.num,1);
    }else{
      result = toothUtils.noSelectTooth(this.state.tooth,this.state.toothSelect,"englishValBotLeft",index,item.num,1);
    }
    this.state.tooth = result.tooth;
    return this.setState({
      toothSelect: result.toothSelect,
    });
  };
//右下乳牙点击事件
  btnClickRightBotLetter = (e, index, item,o) => {
    let result = {};
    if(o!=true) {
      result = toothUtils.selectTooth(this.state.tooth,this.state.toothSelect,"englishValBotRight",index,item.letter,2);
    }else{
      result = toothUtils.noSelectTooth(this.state.tooth,this.state.toothSelect,"englishValBotRight",index,item.letter,2);
    }
    this.state.tooth = result.tooth;
    return this.setState({
      toothSelect: result.toothSelect,
    });

  };
  //右下恒牙点击事件
  btnClickRightBotNum = (e, index, item,o) => {
    let result = {};
    if(o!=true) {
      result = toothUtils.selectTooth(this.state.tooth,this.state.toothSelect,"englishValBotRight",index,item.num,1);
    }else{
      result = toothUtils.noSelectTooth(this.state.tooth,this.state.toothSelect,"englishValBotRight",index,item.num,1);
    }
    this.state.tooth = result.tooth;
    return this.setState({
      toothSelect: result.toothSelect,
    });
  }
  //按钮点击事件
  tagClickWmDisp = (e, id, item) => {
    let key = this.state.key;
    let index = this.state.index;
    this.setState({
      hoverIndex: id,
    });

    if (id == 1) {
      this.state.tooth.englishVal = [];//初始化参数
      this.state.tooth.englishValTopRight = [];//初始化参数
      this.state.tooth.englishValBotLeft = []//初始化参数
      this.state.tooth.englishValBotRight = []//初始化参数
      for (let i = 0; i < arrEnglist.length; i++) {
        this.state.tooth.englishVal.push(arrEnglist[i].letter);//左上8-1
        this.state.tooth.englishValBotLeft.push(arrEnglist[i].letter);//左下8-1
        this.state.toothSelect.englishVal2[arrEnglist[i].index]=true;//右上
        this.state.toothSelect.englishValBotLeft2[arrEnglist[i].index]=true;//右下8-1
      }
      for (let i = 0; i < arrEnglistOrder.length; i++) {
        this.state.tooth.englishValTopRight.push(arrEnglistOrder[i].letter);//右上
        this.state.tooth.englishValBotRight.push(arrEnglistOrder[i].letter)//右下
        this.state.toothSelect.englishValTopRight2[arrEnglistOrder[i].index]=true;//右上
        this.state.toothSelect.englishValBotRight2[arrEnglistOrder[i].index]=true;//右下
      }

      for(let i=0;i<arrCircle.length;i++){
        this.state.tooth.englishVal.push();//左上8-1
        this.state.tooth.englishValBotLeft.push();//左下8-1
        this.state.toothSelect.englishVal1[arrCircle[i].index]=false;//左上
        this.state.toothSelect.englishValBotLeft1[arrCircle[i].index]=false;//左下
      }
      for(let i=0;i<arrCircleNum.length;i++){
        this.state.tooth.englishValTopRight.push();//右上
        this.state.tooth.englishValBotRight.push()//右下
        this.state.toothSelect.englishValTopRight1[arrCircleNum[i].index]=false;//右上
        this.state.toothSelect.englishValBotRight1[arrCircleNum[i].index]=false;//右下
      }
      this.setState({
        tooth: this.state.tooth,
        toothSelect: this.state.toothSelect
      })
    } else if (id == 2) {
      this.state.tooth.englishVal = [];//初始化参数
      this.state.tooth.englishValTopRight = [];//初始化参数
      this.state.tooth.englishValBotLeft = []//初始化参数
      this.state.tooth.englishValBotRight = []//初始化参数

      for(let i=0;i<arrCircle.length;i++){
        this.state.tooth.englishVal.push(arrCircle[i].num);//左上8-1
        this.state.tooth.englishValBotLeft.push(arrCircle[i].num);//左下8-1
        this.state.toothSelect.englishVal1[arrCircle[i].index]=true;//左上
        this.state.toothSelect.englishValBotLeft1[arrCircle[i].index]=true;//左下
      }
      for(let i=0;i<arrCircleNum.length;i++){
        this.state.tooth.englishValTopRight.push(arrCircleNum[i].num);//右上
        this.state.tooth.englishValBotRight.push(arrCircleNum[i].num)//右下
        this.state.toothSelect.englishValTopRight1[arrCircleNum[i].index]=true;//右上
        this.state.toothSelect.englishValBotRight1[arrCircleNum[i].index]=true;//右下
      }


      for (let i = 0; i < arrEnglist.length; i++) {
        this.state.tooth.englishVal.push();
        this.state.tooth.englishValBotLeft.push();
        this.state.toothSelect.englishVal2[arrEnglist[i].index]=false;
        this.state.toothSelect.englishValBotLeft2[arrEnglist[i].index]=false;
      }
      for (let i = 0; i < arrEnglistOrder.length; i++) {
        this.state.tooth.englishValTopRight.push();
        this.state.tooth.englishValBotRight.push();
        this.state.toothSelect.englishValTopRight2[arrEnglistOrder[i].index]=false;
        this.state.toothSelect.englishValBotRight2[arrEnglistOrder[i].index]=false;
      }
      this.setState({
        tooth: this.state.tooth,
        toothSelect: this.state.toothSelect
      })
    } else if (id == 3) {
      this.state.tooth.englishVal = [];//初始化参数
      this.state.tooth.englishValTopRight = [];//初始化参数
      this.state.tooth.englishValBotLeft = []//初始化参数
      this.state.tooth.englishValBotRight = []//初始化参数

      for(let i=0;i<arrCircleNum.length;i++){
        this.state.tooth.englishValTopRight.push(arrCircleNum[i].num);//右上
        this.state.toothSelect.englishValTopRight1[arrCircleNum[i].index]=true;//右上
        this.state.tooth.englishValBotRight.push()//右下
        this.state.toothSelect.englishValBotRight1[arrCircleNum[i].index]=false;//右下
      }
      for(let i=0;i<arrCircle.length;i++){
        this.state.tooth.englishVal.push();//左上8-1
        this.state.tooth.englishValBotLeft.push();//左下8-1
        this.state.toothSelect.englishVal1[arrCircle[i].index]=false;//左上
        this.state.toothSelect.englishValBotLeft1[arrCircle[i].index]=false;//左下
      }


      for (let i = 0; i < arrEnglist.length; i++) {
        this.state.tooth.englishVal.push();
        this.state.tooth.englishValBotLeft.push();
        this.state.toothSelect.englishVal2[arrEnglist[i].index]=false;
        this.state.toothSelect.englishValBotLeft2[arrEnglist[i].index]=false;
      }
      for (let i = 0; i < arrEnglistOrder.length; i++) {
        this.state.tooth.englishValTopRight.push();
        this.state.tooth.englishValBotRight.push()
        this.state.toothSelect.englishValTopRight2[arrEnglistOrder[i].index]=false;
        this.state.toothSelect.englishValBotRight2[arrEnglistOrder[i].index]=false;
      }
      this.setState({
        tooth: this.state.tooth,
        toothSelect: this.state.toothSelect
      })

    } else if (id == 4) {
      this.state.tooth.englishVal = [];//初始化参数
      this.state.tooth.englishValTopRight = [];//初始化参数
      this.state.tooth.englishValBotLeft = []//初始化参数
      this.state.tooth.englishValBotRight = []//初始化参数

      for(let i=0;i<arrCircleNum.length;i++){
        this.state.tooth.englishValBotRight.push(arrCircleNum[i].num)//右下
        this.state.toothSelect.englishValBotRight1[arrCircleNum[i].index]=true;//右下
        this.state.tooth.englishValTopRight.push();//右上
        this.state.toothSelect.englishValTopRight1[arrCircleNum[i].index]=false;//右上
      }
      for (let i = 0; i < arrEnglist.length; i++) {
        this.state.tooth.englishVal.push();
        this.state.tooth.englishValBotLeft.push();
        this.state.toothSelect.englishVal2[arrEnglist[i].index]=false;
        this.state.toothSelect.englishValBotLeft2[arrEnglist[i].index]=false;
      }
      for (let i = 0; i < arrEnglistOrder.length; i++) {
        this.state.tooth.englishValTopRight.push();
        this.state.tooth.englishValBotRight.push()
        this.state.toothSelect.englishValTopRight2[arrEnglistOrder[i].index]=false;
        this.state.toothSelect.englishValBotRight2[arrEnglistOrder[i].index]=false;
      }

      for(let i=0;i<arrCircle.length;i++){
        this.state.tooth.englishVal.push();//左上8-1
        this.state.tooth.englishValBotLeft.push();//左下8-1
        this.state.toothSelect.englishVal1[arrCircle[i].index]=false;//左上
        this.state.toothSelect.englishValBotLeft1[arrCircle[i].index]=false;//左下
      }
      this.setState({
        tooth: this.state.tooth,
        toothSelect: this.state.toothSelect
      })

    } else if (id == 5) {
      this.state.tooth.englishVal = [];//初始化参数
      this.state.tooth.englishValTopRight = [];//初始化参数
      this.state.tooth.englishValBotLeft = []//初始化参数
      this.state.tooth.englishValBotRight = []//初始化参数
      for(let i=0;i<arrCircle.length;i++){
        this.state.tooth.englishVal.push(arrCircle[i].num);//左上8-1
        this.state.toothSelect.englishVal1[arrCircle[i].index]=true;//左上
        this.state.tooth.englishValBotLeft.push();//左下8-1
        this.state.toothSelect.englishValBotLeft1[arrCircle[i].index]=false;//左下
      }

      for(let i=0;i<arrCircleNum.length;i++){
        this.state.tooth.englishValBotRight.push()//右下
        this.state.toothSelect.englishValBotRight1[arrCircleNum[i].index]=false;//右下
        this.state.tooth.englishValTopRight.push();//右上
        this.state.toothSelect.englishValTopRight1[arrCircleNum[i].index]=false;//右上
      }
      for (let i = 0; i < arrEnglist.length; i++) {
        this.state.tooth.englishVal.push();
        this.state.tooth.englishValBotLeft.push();
        this.state.toothSelect.englishVal2[arrEnglist[i].index]=false;
        this.state.toothSelect.englishValBotLeft2[arrEnglist[i].index]=false;
      }
      for (let i = 0; i < arrEnglistOrder.length; i++) {
        this.state.tooth.englishValTopRight.push();
        this.state.tooth.englishValBotRight.push()
        this.state.toothSelect.englishValTopRight2[arrEnglistOrder[i].index]=false;
        this.state.toothSelect.englishValBotRight2[arrEnglistOrder[i].index]=false;
      }
      this.setState({
        tooth: this.state.tooth,
        toothSelect: this.state.toothSelect
      })

    } else if (id == 6) {
      this.state.tooth.englishVal = [];//初始化参数
      this.state.tooth.englishValTopRight = [];//初始化参数
      this.state.tooth.englishValBotLeft = []//初始化参数
      this.state.tooth.englishValBotRight = []//初始化参数
      for(let i=0;i<arrCircle.length;i++){
        this.state.tooth.englishValBotLeft.push(arrCircle[i].num);//左下8-1
        this.state.toothSelect.englishValBotLeft1[arrCircle[i].index]=true;//左下
        this.state.tooth.englishVal.push();//左上8-1
        this.state.toothSelect.englishVal1[arrCircle[i].index]=false;//左上
      }

      for(let i=0;i<arrCircleNum.length;i++){
        this.state.tooth.englishValBotRight.push()//右下
        this.state.toothSelect.englishValBotRight1[arrCircleNum[i].index]=false;//右下
        this.state.tooth.englishValTopRight.push();//右上
        this.state.toothSelect.englishValTopRight1[arrCircleNum[i].index]=false;//右上
      }
      for (let i = 0; i < arrEnglist.length; i++) {
        this.state.tooth.englishVal.push();
        this.state.tooth.englishValBotLeft.push();
        this.state.toothSelect.englishVal2[arrEnglist[i].index]=false;
        this.state.toothSelect.englishValBotLeft2[arrEnglist[i].index]=false;
      }
      for (let i = 0; i < arrEnglistOrder.length; i++) {
        this.state.tooth.englishValTopRight.push();
        this.state.tooth.englishValBotRight.push()
        this.state.toothSelect.englishValTopRight2[arrEnglistOrder[i].index]=false;
        this.state.toothSelect.englishValBotRight2[arrEnglistOrder[i].index]=false;
      }
      this.setState({
        tooth: this.state.tooth,
        toothSelect: this.state.toothSelect
      })

    } else if (id == 7) {
      this.state.tooth.englishVal = [];//初始化参数
      this.state.tooth.englishValTopRight = [];//初始化参数
      this.state.tooth.englishValBotLeft = []//初始化参数
      this.state.tooth.englishValBotRight = []//初始化参数

      for(let i=0;i<arrCircle.length;i++){
        this.state.tooth.englishVal.push(arrCircle[i].num);//左上8-1
        this.state.toothSelect.englishVal1[arrCircle[i].index]=true;//左上
        this.state.tooth.englishValBotLeft.push();//左下8-1
        this.state.toothSelect.englishValBotLeft1[arrCircle[i].index]=false;//左下
      }
      for(let i=0;i<arrCircleNum.length;i++){
        this.state.tooth.englishValTopRight.push(arrCircleNum[i].num);//右上
        this.state.toothSelect.englishValTopRight1[arrCircleNum[i].index]=true;//右上
        this.state.tooth.englishValBotRight.push()//右下
        this.state.toothSelect.englishValBotRight1[arrCircleNum[i].index]=false;//右下
      }

      for (let i = 0; i < arrEnglist.length; i++) {
        this.state.tooth.englishVal.push();
        this.state.tooth.englishValBotLeft.push();
        this.state.toothSelect.englishVal2[arrEnglist[i].index]=false;
        this.state.toothSelect.englishValBotLeft2[arrEnglist[i].index]=false;
      }
      for (let i = 0; i < arrEnglistOrder.length; i++) {
        this.state.tooth.englishValTopRight.push();
        this.state.tooth.englishValBotRight.push()
        this.state.toothSelect.englishValTopRight2[arrEnglistOrder[i].index]=false;
        this.state.toothSelect.englishValBotRight2[arrEnglistOrder[i].index]=false;
      }
      this.setState({
        tooth: this.state.tooth,
        toothSelect: this.state.toothSelect
      })

    } else if (id == 8) {
      this.state.tooth.englishVal = [];//初始化参数
      this.state.tooth.englishValTopRight = [];//初始化参数
      this.state.tooth.englishValBotLeft = [];//初始化参数
      this.state.tooth.englishValBotRight = [];//初始化参数
      for(let i=0;i<arrCircle.length;i++){
        this.state.tooth.englishValBotLeft.push(arrCircle[i].num);//左下8-1
        this.state.toothSelect.englishValBotLeft1[arrCircle[i].index]=true;//左下
        this.state.tooth.englishVal.push();//左上8-1
        this.state.toothSelect.englishVal1[arrCircle[i].index]=false;//左上
      }
      for(let i=0;i<arrCircleNum.length;i++){
        this.state.tooth.englishValBotRight.push(arrCircleNum[i].num)//右下
        this.state.toothSelect.englishValBotRight1[arrCircleNum[i].index]=true;//右下
        this.state.tooth.englishValTopRight.push();//右上
        this.state.toothSelect.englishValTopRight1[arrCircleNum[i].index]=false;//右上
      }

      for (let i = 0; i < arrEnglist.length; i++) {
        this.state.tooth.englishVal.push();
        this.state.tooth.englishValBotLeft.push();
        this.state.toothSelect.englishVal2[arrEnglist[i].index]=false;
        this.state.toothSelect.englishValBotLeft2[arrEnglist[i].index]=false;
      }
      for (let i = 0; i < arrEnglistOrder.length; i++) {
        this.state.tooth.englishValTopRight.push();
        this.state.tooth.englishValBotRight.push()
        this.state.toothSelect.englishValTopRight2[arrEnglistOrder[i].index]=false;
        this.state.toothSelect.englishValBotRight2[arrEnglistOrder[i].index]=false;
      }
      this.setState({
        tooth: this.state.tooth,
        toothSelect: this.state.toothSelect
      })

    } else if (id == 9) {
      this.state.tooth.englishVal = [];//初始化参数
      this.state.tooth.englishValTopRight = [];//初始化参数
      this.state.tooth.englishValBotLeft = [];//初始化参数
      this.state.tooth.englishValBotRight = [];//初始化参数
      for(let i=0;i<arrCircle.length;i++){
        this.state.tooth.englishValBotLeft.push();//左下8-1
        this.state.toothSelect.englishValBotLeft1[arrCircle[i].index]=false;//左下
        this.state.tooth.englishVal.push();//左上8-1
        this.state.toothSelect.englishVal1[arrCircle[i].index]=false;//左上
      }
      for(let i=0;i<arrCircleNum.length;i++){
        this.state.tooth.englishValBotRight.push()//右下
        this.state.toothSelect.englishValBotRight1[arrCircleNum[i].index]=false;//右下
        this.state.tooth.englishValTopRight.push();//右上
        this.state.toothSelect.englishValTopRight1[arrCircleNum[i].index]=false;//右上
      }

      for (let i = 0; i < arrEnglist.length; i++) {
        this.state.tooth.englishVal.push();
        this.state.tooth.englishValBotLeft.push();
        this.state.toothSelect.englishVal2[arrEnglist[i].index]=false;
        this.state.toothSelect.englishValBotLeft2[arrEnglist[i].index]=false;
      }
      for (let i = 0; i < arrEnglistOrder.length; i++) {
        this.state.tooth.englishValTopRight.push();
        this.state.tooth.englishValBotRight.push()
        this.state.toothSelect.englishValTopRight2[arrEnglistOrder[i].index]=false;
        this.state.toothSelect.englishValBotRight2[arrEnglistOrder[i].index]=false;
      }
      this.setState({
        tooth: this.state.tooth,
        toothSelect: this.state.toothSelect
      })
    }

  };


  render() {

    return (
      <GridContent >
        <div>
          {
            arrTag.map((item, index) => {
              return <Tag
                className={[this.state.hoverIndex === item.id ? (styles.tableSearch) : (styles.tableSearchBtns)].join(' ')}
                onClick={(e) => this.tagClickWmDisp(e, item.id, item)} id="tagwm"
                key={index}>{item.name}</Tag>
            })
          }
        </div>
        <div className={styles.tableAll}>
          <table className={styles.table}>
            <tbody>
            <tr>
              <td className={styles.td}>
                <div className={`${styles.btn_tooth} ${styles.tooth_left_top}`} id="tagColor2">
                  {
                    arrCircle.map((item, index) => {
                      return <Button
                        className={[this.state.toothSelect.englishVal1[item.index] ? (styles.tableSearch) : (styles.tableSearchBtns)].join(' ')}
                        type="" shape="circle" key={index}
                        onClick={(e) => this.btnClickNum(e, item.index, item,this.state.toothSelect.englishVal1[item.index])}
                      >{item.num}</Button>
                    })
                  }
                </div>
                <div className={styles.btn_tooth_Eng} id="babyTeethE2">
                  {
                    arrEnglist.map((item, index) => {
                      return <Button
                        className={[this.state.toothSelect.englishVal2[item.index] ? (styles.tableSearch) : (styles.tableSearchBtns)].join(' ')}
                        type="" shape="circle" key={index}
                        onClick={(e) => this.btnClickLetter(e, item.index, item,this.state.toothSelect.englishVal2[item.index])}
                      >{item.letter}</Button>
                    })
                  }
                </div>
              </td>
              <td>
                <div className={styles.btn_tooth} id="tagColor22">
                  {
                    arrCircleNum.map((item, index) => {
                      return <Button
                        className={[this.state.toothSelect.englishValTopRight1[item.index] ? (styles.tableSearch) : (styles.tableSearchBtns)].join(' ')}
                        type="" shape="circle" key={index}
                        onClick={(e) => this.btnClickRightNum(e, item.index, item,this.state.toothSelect.englishValTopRight1[item.index] )}
                      >{item.num}</Button>
                    })
                  }
                </div>
                <div className={styles.btn_tooth_Eng_left} id="babyTeethOrder2">
                  {
                    arrEnglistOrder.map((item, index) => {
                      return <Button
                        className={[this.state.toothSelect.englishValTopRight2[item.index] ? (styles.tableSearch) : (styles.tableSearchBtns)].join(' ')}
                        type="" shape="circle" key={index}
                        onClick={(e) => this.btnClickRightLetter(e, item.index, item,this.state.toothSelect.englishValTopRight2[item.index])}
                      >{item.letter}</Button>
                    })
                  }
                </div>
              </td>
            </tr>
            </tbody>
            <tbody>
            <tr>
              <td>
                <div className={styles.btn_tooth_Eng} id="babyTeethBot2">
                  {
                    arrEnglist.map((item, index) => {
                      return <Button
                        className={[this.state.toothSelect.englishValBotLeft2[item.index]? (styles.tableSearch) : (styles.tableSearchBtns)].join(' ')}
                        type="" shape="circle" key={index}
                        onClick={(e) => this.btnClickLeftLetter(e, item.index, item,this.state.toothSelect.englishValBotLeft2[item.index])}
                      >{item.letter}</Button>
                    })
                  }
                </div>
                <div className={`${styles.btn_tooth} ${styles.tooth_left_top}`} id="tagColor32">
                  {
                    arrCircle.map((item, index) => {
                      return <Button
                        className={[this.state.toothSelect.englishValBotLeft1[item.index] ? (styles.tableSearch) : (styles.tableSearchBtns)].join(' ')}
                        type="" shape="circle" key={index}
                        onClick={(e) => this.btnClickLeftNum(e, item.index, item,this.state.toothSelect.englishValBotLeft1[item.index])}
                      >{item.num}</Button>
                    })
                  }
                </div>

              </td>
              <td className={styles.bottom_td}>
                <div className={styles.btn_tooth_Eng_left} id="babyTeethBotRig2">
                  {
                    arrEnglistOrder.map((item, index) => {
                      return <Button
                        className={[this.state.toothSelect.englishValBotRight2[item.index] ? (styles.tableSearch) : (styles.tableSearchBtns)].join(' ')}
                        type="" shape="circle" key={index}
                        onClick={(e) => this.btnClickRightBotLetter(e, item.index, item,this.state.toothSelect.englishValBotRight2[item.index])}
                      >{item.letter}</Button>
                    })
                  }
                </div>
                <div className={styles.btn_tooth} id="tagColor42">
                  {
                    arrCircleNum.map((item, index) => {
                      return <Button
                        className={[this.state.toothSelect.englishValBotRight1[item.index] ? (styles.tableSearch) : (styles.tableSearchBtns)].join(' ')}
                        type="" shape="circle" key={index}
                        onClick={(e) => this.btnClickRightBotNum(e, item.index, item,this.state.toothSelect.englishValBotRight1[item.index])}
                      >{item.num}</Button>
                    })
                  }
                </div>
              </td>
            </tr>
            </tbody>
          </table>
        </div>

      </GridContent>
    );
  }
}

export default ToothSelectQuality
