/**
 * @Description: 海报弹窗（APP）
 * 使用方法：
 * ① 引入：import PosterModal from '@/pages/Poster/PosterModal'
 *         import { useRef } from 'react'
 * ② 使用：const posterModalRef = useRef(null)
 *         <PosterModal ref={posterModalRef}/>
 * ③ 调用：posterModalRef && posterModalRef.current.init(type, spaceState, routerType)
 *   参数说明：type 1 生成海报，2 分享，3 空间详情页分享     spaceState 空间数据      routerType 路由跳转方式，push或者replace，默认push
 */
import React, { useEffect, useState, forwardRef, useImperativeHandle } from 'react'
import { createPortal } from 'react-dom'
import { connect, history } from 'umi'
import classNames from 'classnames'
import html2canvas from 'html2canvas'
import request from '@/utils/request'
import { getOperatingEnv, saveImageInApp, shareWeChatInApp, shareInApp, getShareUrl, WxAppIdByPublicAccount, isIOS, randomColor, processNames } from '@/utils/utils'
import { Toast, Popup, Mask } from 'antd-mobile'
import { Typography, message } from 'antd'
import styles from './index.less'

// 图片、icon
import wechat from '@/assets/GlobalImg/wechat.png'
import wechat_friend from '@/assets/GlobalImg/wechat_friend.png'
import enterprise_wechat from '@/assets/GlobalImg/enterprise_wechat.png'
import copy_link from '@/assets/GlobalImg/copy_link.png'
import download_poster from '@/assets/GlobalImg/download_poster.png'

import PosterTemplateDom from '../Components/PosterTemplateDom'       // 海报dom

// 模板数据list
const templateDataSource = [
  { id: 1, outerBgColor1: '#CEF9FF', outerBgColor2: '#E1E5FF', textColor: '#000', bgColor1: '#95C4FF', bgColor2: '#4E41FF', imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/poster_bg_1.png' },
  { id: 2, outerBgColor1: '#FFE483', outerBgColor2: '#FFFCEA', textColor: '#000', bgColor1: '#FFDD47', bgColor2: '#FF5C00',  imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/poster_bg_2.png' },
  { id: 3, outerBgColor1: '#FFCDDA', outerBgColor2: '#FFF4F7', textColor: '#000', bgColor1: '#FFE2E2', bgColor2: '#FF48B6',  imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/poster_bg_3.png' },
  { id: 4, outerBgColor1: '#B3E794', outerBgColor2: '#F1FBE7', textColor: '#fff', bgColor1: '#CADE90', bgColor2: '#00B15C',  imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/poster_bg_4.png' },
  { id: 5, outerBgColor1: '#C9E5FF', outerBgColor2: '#EAFAFF', textColor: '#fff', bgColor1: '#86E4FF', bgColor2: '#0067FF',  imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/poster_bg_5.png' },
  { id: 6, outerBgColor1: '#9FF6DF', outerBgColor2: '#EAFFF5', textColor: '#fff', bgColor1: '#5AEDB8', bgColor2: '#039BB0',  imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/poster_bg_6.png' },
]

let Index: React.FC = (props: any) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')   // 用户信息
  // 获取当前操作环境，2 微信浏览器   5 FRIDAY app   6 Jarvis App   7 企微浏览器
  const env = getOperatingEnv()

  const { refInstance, dispatch } = props

  // 初始模板数据
  const initialTemplateData = {
    id: 1,
    outerBgColor1: '#CEF9FF',      // 页面背景色
    outerBgColor2: '#E1E5FF',
    textColor: '#000',             // 文字颜色
    bgColor1: '#95C4FF',           // 主持人背景色
    bgColor2: '#4E41FF',
    imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/poster_bg_1.png',  // 背景图片
  }

  const [shareTipsByH5Visible, setShareTipsByH5Visible] = useState(false) // 分享提示弹窗（手机自带浏览器环境）
  const [shareTipsVisible, setShareTipsVisible] = useState(false)         // 分享提示箭头弹窗（微信浏览器环境）
  const [shareLinkVisible, setShareLinkVisible] = useState(false)         // 分享弹窗
  const [sharePosterVisible, setSharePosterVisible] = useState(false)     // 分享海报弹窗（APP环境）
  const [imgUrl, setImgUrl] = useState(null)                              // 海报图片url
  const [templateData, setTemplateData] = useState(initialTemplateData)   // 当前模板数据
  const [loadingCreatePoster, setLoadingCreatePoster] = useState(false)   // 创建海报loading
  const [spaceState, setSpaceState] = useState({})                        // 直播or会议数据
  const [initType, setInitType] = useState(null)                          // 初始化时的type

  // 初始化方法，type，1 生成海报，2 分享，3 空间详情页分享
  const init = async (type, data, routerType) => {
    // 添加判断
    if (!type) {
      Toast.show('type缺失~')
      return
    }
    if (!data) {
      Toast.show('data缺失~')
      return
    }
    await setInitType(type)
    if (type == 1) {
      if (env == '5' || env == '6') {
        // 因为不同位置传过来的数据结构不同，所以统一在这里获取一遍数据
        Toast.show({
          icon: 'loading',
          content: '',
          duration: 0,
        })
        const content = await getSpacePosterInfo(data.id)
        Toast.clear()

        await setSpaceState(content || {})
        await setSharePosterVisible(true)
      } else {
        // 目前只有创建直播or会议成功页，生成海报进来是replace
        if (routerType == 'replace') {
          history.replace(`/Poster?id=${data.id}`)
        } else {
          history.push(`/Poster?id=${data.id}`)
        }
      }
    } else if (type == 2 || type == 3) {
      // 因为不同位置传过来的数据结构不同，所以统一在这里获取一遍数据
      Toast.show({
        icon: 'loading',
        content: '',
        duration: 0,
      })
      const content = await getSpacePosterInfo(data.id)
      Toast.clear()

      await setSpaceState(content || {})
      await setShareLinkVisible(true)
    }
  }

  // 转发ref，返回值作为父组件ref.current的值
  useImperativeHandle(refInstance, () => {
    return {
      init: init,
    }
  }, [])

  useEffect(() => {
    if (sharePosterVisible) {
      beforeCreatePoster()
    }
  }, [sharePosterVisible])

  // 清空数据
  const resetState = () => {
    setImgUrl(null)
    setTemplateData(initialTemplateData)
    setLoadingCreatePoster(false)
    setSpaceState({})
  }

  // 获取直播or会议海报信息
  const getSpacePosterInfo = (id) => {
    return dispatch({
      type: 'userInfoStore/getSpacePosterInfo',
      payload: {
        spaceId: id,                   // 空间ID
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200) {
        return content || {}
      } else {
        Toast.show(msg || '数据加载失败')
        return {}
      }
    }).catch(err => {
      return {}
    })
  }

  // 延迟加载
  const beforeCreatePoster = () => {
    setLoadingCreatePoster(true)
    // 加定时器是为了toast不被弹窗覆盖
    setTimeout(() => {
      Toast.show({
        icon: 'loading',
        content: '',
        duration: 0,
      })
    }, 0)
    // 为了保证生成的图片中不会出现文字错位
    setTimeout(() => {
      createPoster()
    }, 1000)
  }

  // 生成海报
  const createPoster = () => {
    html2canvas(document.querySelector('#poster_dom'),{
      foreignObjectRendering: false,
      backgroundColor: 'transparent',
      removeContainer: true,
      useCORS: true,
      // allowTaint:true,
      // proxy: 'https://dhealth-test.friday.tech/'
    }).then(canvas => {
      const url = canvas.toDataURL('image/png')
      uploadImg(url)
    }).catch(err => {
      setLoadingCreatePoster(false)
      Toast.clear()
    })
  }

  // 上传图片获取链接
  const uploadImg = (url) => {
    // const UserInfo = JSON.parse(localStorage.getItem('userInfo')) || {};
    const imgFile = dataURLtoFile(url, 'poster.png')
    let formData = new FormData()
    formData.append('file', imgFile)
    formData.append('fileType', 15)
    request('/api/server/base/uploadFile', {
      method: 'POST',
      body: formData,
      // headers:{// token
      //   access_token: localStorage.getItem('access_token'),
      //   username: getOperatingEnv() == 5 ? localStorage.getItem('user_name') : UserInfo?.phone,
      //   client: getOperatingEnv() == 5 ? localStorage.getItem('client') : 'WX',   // 5 表示在FRIDAY APP中
      //   type: getOperatingEnv() == 1 ? '' : 1,
      // }
    }).then(res => {
      setLoadingCreatePoster(false)
      Toast.clear()

      const { code, content, msg } = res
      if (code == 200) {
        setImgUrl(content.fileUrlView)      // 海报图片url
      } else {
        Toast.show(msg || '数据加载失败')
      }
    }).catch(err => {
      setLoadingCreatePoster(false)
      Toast.clear()
    })
  }

  // base64图片转文件
  const dataURLtoFile = (dataurl, fileName) => {
    let arr = dataurl.split(','),
      mime = arr[0].match(/:(.*?);/)[1],
      bstr = atob(arr[1]),
      n = bstr.length,
      u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr],fileName, { type: mime });
  }

  // 点击切换模板
  const onClickTemplate = (obj) => {
    if (loadingCreatePoster) {
      return
    }
    // 保存更新模板数据
    setTemplateData({
      ...obj,
    })
    // 更新海报
    beforeCreatePoster()
  }

  // 点击图片
  const onClickImg = (e) => {
    e.preventDefault()
    e.stopPropagation()
  }

  // 复制成功
  const onCopy = () => {
    Toast.show('复制成功')
    // 关闭海报弹窗，并清空数据
    onCloseSharePosterPopup()
    // 关闭分享弹窗，并清空数据
    onCloseShareLinkPopup()
  }

  // APP，点击保存海报
  const onClickDownloadPoster = () => {
    if (loadingCreatePoster) {
      return
    }

    // 调用APP保存图片
    saveImageInApp(imgUrl).then(res => {
      if (res) {
        Toast.show('保存成功')
      } else {
        Toast.show('保存失败')
      }
    })

    // 关闭海报弹窗，并清空数据
    onCloseSharePosterPopup()
  }

  // APP，点击分享海报到微信、朋友圈
  const onClickPosterWeChat = (scene) => {
    if (loadingCreatePoster) {
      return
    }

    // 调用APP微信分享
    shareWeChatInApp({
      type: 'image',        // type取link（默认）或image
      url: imgUrl,          // url为网页链接或图片链接，必传
      thumbnail: '',        // 缩略图链接
      title: '',            // 标题
      description: '',      // 描述
      scene: scene,         // scene取session（默认）或timeline
    }).then(res => {})

    // 关闭海报弹窗，并清空数据
    onCloseSharePosterPopup()
  }

  // 点击分享海报到企微
  const onClickPosterSystem = () => {
    if (loadingCreatePoster) {
      return
    }

    // 调用APP系统分享
    shareInApp({
      type: 'image',        // type取link（默认）或image
      url: imgUrl,          // url为网页链接或图片链接，必传
    }).then(res => {})

    // 关闭海报弹窗，并清空数据
    onCloseSharePosterPopup()
  }

  // 关闭分享海报弹窗，并清空数据
  const onCloseSharePosterPopup = () => {
    setSharePosterVisible(false)
    // 清空数据
    resetState()
  }

  // 分享弹窗，点击分享海报
  const onClickSharePoster = () => {
    // 关闭分享弹窗
    setShareLinkVisible(false)
    if (env == '5' || env == '6') {
      // 打开分享海报弹窗
      setSharePosterVisible(true)
    } else {
      history.push(`/Poster?id=${spaceState.id}`)
    }
  }

  // 分享弹窗，点击微信、朋友圈
  const onClickShareWeChat = async (scene) => {
    if (env == '5' || env == '6') {
      // 调用APP微信分享
      shareWeChatInApp({
        type: 'link',        // type取link（默认）或image
        url: `${window.location.origin}/PlanetChatRoom/${spaceState.starSpaceType == 2 ? 'Meet' : 'Live'}/${spaceState.id}?shareUserId=${UserInfo?.friUserId}&isShare=1` + (spaceState.starSpaceType == 2 && spaceState.password ? `&pwd=${spaceState.password}` : ''),          // url为网页链接或图片链接，必传
        thumbnail: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/DigitalHealthShareImg.png', // 缩略图链接
        title: '【FRIDAY医生星球】牙医都来这里学习和交流！',            // 标题
        description: spaceState.name,      // 描述
        scene: scene,         // scene取session（默认）或timeline
      }).then(res => {})
      // 关闭分享弹窗，并清空数据
      onCloseShareLinkPopup()
    } else if (env == '2' || env == '7') {
      // 关闭分享弹窗
      setShareLinkVisible(false)
      // 微信浏览器环境分享
      Toast.show({
        icon: 'loading',
        content: '',
        duration: 0,
      })
      await onShareAppMessage()
      Toast.clear()
      await setShareTipsVisible(true)
    } else {
      // 关闭分享弹窗
      setShareLinkVisible(false)
      // 打开引导提示弹窗
      setShareTipsByH5Visible(true)
    }
  }

  // 分享弹窗，点击分享到企业微信
  const onClickShareSystem = async () => {
    if (env == '5' || env == '6') {
      // 调用APP系统分享
      shareInApp({
        type: 'link',        // type取link（默认）或image
        url: `${window.location.origin}/PlanetChatRoom/${spaceState.starSpaceType == 2 ? 'Meet' : 'Live'}/${spaceState.id}?shareUserId=${UserInfo?.friUserId}&isShare=1` + (spaceState.starSpaceType == 2 && spaceState.password ? `&pwd=${spaceState.password}` : ''),          // url为网页链接或图片链接，必传
      }).then(res => {})

      // 关闭分享弹窗，并清空数据
      onCloseShareLinkPopup()
    } else if (env == '7') {
      // 关闭分享弹窗
      setShareLinkVisible(false)
      // 微信浏览器环境分享
      Toast.show({
        icon: 'loading',
        content: '',
        duration: 0,
      })
      await onShareAppMessage()
      Toast.clear()
      await setShareTipsVisible(true)
    } else {
      // 关闭分享弹窗，并清空数据
      onCloseShareLinkPopup()
    }
  }

  // 微信分享配置
  const onShareAppMessage = async () => {
    const shareUrl = `${window.location.origin}/PlanetChatRoom/${spaceState.starSpaceType == 2 ? 'Meet' : 'Live'}/${spaceState.id}?shareUserId=${UserInfo?.friUserId}&isShare=1` + (spaceState.starSpaceType == 2 && spaceState.password ? `&pwd=${spaceState.password}` : '')
    const url = window.location.href
    const shareUrl2 = getShareUrl(shareUrl)
    console.log('shareUrl----------', url, shareUrl, shareUrl2)

    await dispatch({
      type: 'userInfoStore/getJsapiTicket',
      payload: {
        currentUrl: url,                                   // 页面url
        appId: WxAppIdByPublicAccount,                     // 公众号appId
      },
    }).then(res => {
      if (res && res.code == 200) {
        wx.config({
          debug: false,
          appId: res.content.appId,
          timestamp: res.content.timestamp,
          nonceStr: res.content.nonceStr,
          signature: res.content.signature,
          jsApiList: [
            'updateAppMessageShareData',
            'updateTimelineShareData',
            'onMenuShareTimeline',
            'onMenuShareAppMessage',
            'onMenuShareQQ',
            'onMenuShareWeibo',
            'onMenuShareQZone',
          ],
        })
        wx.ready(() => {
          const shareDate = {
            title: '【FRIDAY医生星球】牙医都来这里学习和交流！',
            desc: spaceState.name,
            link: shareUrl2,
            imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/DigitalHealthShareImg.png',
          };
          console.log(shareDate)
          wx.updateAppMessageShareData(shareDate);
          wx.updateTimelineShareData(shareDate);
          wx.onMenuShareTimeline(shareDate);
          wx.onMenuShareAppMessage(shareDate);
          wx.onMenuShareQQ(shareDate);
          wx.onMenuShareWeibo(shareDate);
          wx.onMenuShareQZone(shareDate);
        })
      } else {
        // Toast.show('请求微信配置失败～！')
      }
    })
  }

  // 关闭分享弹窗，并清空数据
  const onCloseShareLinkPopup = () => {
    setShareLinkVisible(false)
    // 清空数据
    resetState()
  }

  // 关闭分享提示箭头弹窗（微信浏览器环境），并清空数据
  const onCloseShareTipsModal = () => {
    setShareTipsVisible(false)
    // 清空数据
    resetState()
  }

  // 关闭分享提示弹窗（手机自带浏览器环境），并清空数据
  const onCloseShareTipsByH5Modal = () => {
    setShareTipsByH5Visible(false)
    // 清空数据
    resetState()
  }

  // 点击立即转发按钮
  const onClickForwardBtn = () => {
    const { dispatch } = props
    dispatch({
      type: 'graphicsText/insideShareStarSpace',
      payload: {
        spaceId: spaceState.id,                   // 空间ID
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200) {
        message.success('转发成功')
        // 关闭分享弹窗，并清空数据
        onCloseShareLinkPopup()
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch(err => {})
  }

  // 点击分享您的想法
  const onClickSendPost = () => {
    history.push(`/CreateGraphicsText/CreatePost?spaceId=${1}`)
  }

  return (
    <>
      {/* 生成海报弹窗（APP环境） */}
      <Popup
        className={styles.popup}
        onMaskClick={onCloseSharePosterPopup}
        visible={sharePosterVisible}
      >
        {/* 修改挂载的 HTML 节点 */
          sharePosterVisible && document.getElementsByClassName(styles.popup) && document.getElementsByClassName(styles.popup)[0] &&
          createPortal((
            <div className={styles.poster_image} onClick={onCloseSharePosterPopup}>
              <img src={imgUrl} width={'auto'} height={'auto'} alt="" onClick={onClickImg}/>
            </div>
          ), document.getElementsByClassName(styles.popup)[0])
        }

        {/* 海报dom-用于生成图片 */}
        <PosterTemplateDom templateData={templateData} data={spaceState}/>

        <div className={styles.header} onClick={onCloseSharePosterPopup}>
          <div className={styles.line}></div>
        </div>

        {/* 模板 */}
        <div className={styles.bottom_wrap}>
          <p className={styles.bottom_title}>选择模板</p>
          <div className={styles.bottom_template_wrap}>
            {
              templateDataSource.map(item => {
                return (
                  <div
                    key={item.id}
                    className={classNames(styles.template_option, {
                      [styles.checked]: templateData.id == item.id,
                    })}
                    onClick={() => onClickTemplate(item)}
                  >
                    <img src={item.imgUrl} width={71} height={90} alt=""/>
                  </div>
                )
              })
            }
          </div>

          <div className={styles.bottom_btn_wrap}>
            {/* APP中微信分享，android暂不支持，所以先隐藏 */}
            {
              isIOS() &&
              <>
                <div className={styles.btn_item} onClick={() => onClickPosterWeChat('session')}>
                  <img src={wechat} width={48} height={48} alt=""/>
                  <p>转发给朋友</p>
                </div>
                <div className={styles.btn_item} onClick={() => onClickPosterWeChat('timeline')}>
                  <img src={wechat_friend} width={48} height={48} alt=""/>
                  <p>分享到朋友圈</p>
                </div>
              </>
            }

            {/* 系统分享 */}
            <div className={styles.btn_item} onClick={onClickPosterSystem}>
              <img src={enterprise_wechat} width={48} height={48} alt=""/>
              <p>转发到企业微信</p>
            </div>

            {/* 复制链接 */}
            <Typography.Paragraph copyable={{
              text: `${window.location.origin}/PlanetChatRoom/${spaceState.starSpaceType == 2 ? 'Meet' : 'Live'}/${spaceState.id}?shareUserId=${UserInfo?.friUserId}&isShare=1` + (spaceState.starSpaceType == 2 && spaceState.password ? `&pwd=${spaceState.password}` : ''),
              icon: [
                <div className={styles.btn_item}>
                  <img src={copy_link} width={48} height={48} alt=""/>
                  <p>复制链接</p>
                </div>,
                <div className={styles.btn_item}>
                  <img src={copy_link} width={48} height={48} alt=""/>
                  <p>复制链接</p>
                </div>
              ],
              tooltips: [false, false],
              onCopy: onCopy,
            }}></Typography.Paragraph>

            <div className={styles.btn_item} onClick={onClickDownloadPoster}>
              <img src={download_poster} width={48} height={48} alt=""/>
              <p>保存海报</p>
            </div>
          </div>
        </div>
      </Popup>

      {/* 分享弹窗 */}
      <Popup
        className={styles.popup_share_by_app}
        onMaskClick={onCloseShareLinkPopup}
        visible={shareLinkVisible}
      >
        <div className={styles.header} onClick={onCloseShareLinkPopup}>
          <div className={styles.line}></div>
        </div>

        {/* 空间详情页点击分享时显示 */}
        {
          initType == 3 &&
          <div className={styles.in_space_details_wrap}>
            <div className={styles.forward_btn_wrap}>
              <div className={styles.left}>分享到广场</div>
              <div className={styles.right_btn} onClick={onClickForwardBtn}>立即转发</div>
            </div>
            <div className={styles.post_btn} onClick={onClickSendPost}>分享您的想法...</div>
            <div className={styles.space_wrap}>
              <div
                className={styles.left_cover_image}
                style={
                  spaceState.spaceCoverUrlShow ? {backgroundImage: `url(${spaceState.spaceCoverUrlShow})`}
                    : {backgroundColor: `${randomColor(spaceState.hostUserInfo && spaceState.hostUserInfo.wxUserId)}`}
                }
              >
                {/* 封面中的标题 */}
                {spaceState.spaceCoverUrlShow && spaceState.isTemplateCover == 1 && <div className={styles.title_in_cover_image}>{spaceState.name}</div>}

                {spaceState.spaceCoverUrlShow ? '' : processNames(spaceState.hostUserInfo && spaceState.hostUserInfo.name)}
              </div>
              <div className={styles.right}>
                <div className={styles.space_title}>{spaceState.name}</div>
                <div className={styles.space_introduce}>{spaceState.intro}</div>
              </div>
            </div>
          </div>
        }

        <div className={styles.bottom_btn_wrap}>
          <div className={styles.btn_item} onClick={onClickSharePoster}>
            <img src={download_poster} width={48} height={48} alt=""/>
            <p>分享海报</p>
          </div>
          {/* android-APP环境暂不支持，所以先隐藏 */}
          {
            (env == '5' || env == '6') && !isIOS() ? null :
            <>
              <div className={styles.btn_item} onClick={() => onClickShareWeChat('session')}>
                <img src={wechat} width={48} height={48} alt=""/>
                <p>转发给朋友</p>
              </div>
              <div className={styles.btn_item} onClick={() => onClickShareWeChat('timeline')}>
                <img src={wechat_friend} width={48} height={48} alt=""/>
                <p>分享到朋友圈</p>
              </div>
            </>
          }

          {/* 系统分享 */}
          {
            (env == '5' || env == '6' || env == '7') &&
            <div className={styles.btn_item} onClick={onClickShareSystem}>
              <img src={enterprise_wechat} width={48} height={48} alt=""/>
              <p>转发到企业微信</p>
            </div>
          }

          {/* 复制链接 */}
          <Typography.Paragraph copyable={{
            text: `${window.location.origin}/PlanetChatRoom/${spaceState.starSpaceType == 2 ? 'Meet' : 'Live'}/${spaceState.id}?shareUserId=${UserInfo?.friUserId}&isShare=1` + (spaceState.starSpaceType == 2 && spaceState.password ? `&pwd=${spaceState.password}` : ''),
            icon: [
              <div className={styles.btn_item}>
                <img src={copy_link} width={48} height={48} alt=""/>
                <p>复制链接</p>
              </div>,
              <div className={styles.btn_item}>
                <img src={copy_link} width={48} height={48} alt=""/>
                <p>复制链接</p>
              </div>
            ],
            tooltips: [false, false],
            onCopy: onCopy,
          }}></Typography.Paragraph>
        </div>
      </Popup>

      {/* 分享提示箭头弹窗（微信浏览器环境） */}
      <Mask style={{ '--z-index': "1010" }} opacity={0.7} visible={shareTipsVisible} onMaskClick={onCloseShareTipsModal} />
      {/* 修改挂载的 HTML 节点 */
        shareTipsVisible && createPortal((
          <div className={styles.fixed_share_box}>
            <i className={styles.icon1}></i>
            <div className={styles.message_box}>
              <div>点击右上角</div>
              <div>发送到 微信好友 或者 分享到朋友圈</div>
            </div>
            <div className={styles.icon_box}>
              <i className={styles.icon2}></i>
              <i className={styles.icon3}></i>
            </div>
          </div>
        ), document.body)
      }

      {/* 分享提示弹窗（手机自带浏览器环境） */}
      <Mask style={{ '--z-index': "1010" }} opacity={0.7} visible={shareTipsByH5Visible} onMaskClick={onCloseShareTipsByH5Modal} />
      {/* 修改挂载的 HTML 节点 */
        shareTipsByH5Visible && createPortal((
          <div className={styles.fixed_share_box} style={{top: '40%', left: '50%', right: 'initial', transform: 'translateX(-50%)'}}>
            <div className={styles.message_box}>
              <div>请使用浏览器自带分享功能</div>
              <div>发送到 微信 或者 朋友圈</div>
            </div>
            <div className={styles.icon_box}>
              <i className={styles.icon2}></i>
              <i className={styles.icon3}></i>
            </div>
          </div>
        ), document.body)
      }

    </>
  )
}

// forwardRef和connect的联合使用。https://www.jianshu.com/p/80961a8887c7
Index = connect(({ loading }: any) => ({ loading }))(Index)
export default forwardRef((props, ref) => <Index {...props} refInstance={ref}/>)
