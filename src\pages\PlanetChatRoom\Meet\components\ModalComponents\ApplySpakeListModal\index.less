.popup_container {
  :global {
    .adm-popup-body {
      border-radius: 16PX 16PX 0 0;
    }
  }
}

.spin {
  height: 100%;
  :global {
    .ant-spin-container {
      height: 100%;
    }
  }
}

.container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: nowrap;
  flex-direction: column;
}

// 头部
.header_line {
  flex-shrink: 0;
  width: 100%;
  height: 28PX;
  display: flex;
  align-items: center;
  justify-content: center;
  .header_line_bar {
    width: 48PX;
    height: 4PX;
    background: #D0D4D7;
    border-radius: 4PX;
  }
}

// 标题
.header_title_wrap {
  width: 100%;
  display: flex;
  flex-wrap: nowrap;
  align-items: flex-end;
  column-gap: 8PX;
  padding: 16PX 0 12PX 16PX;
  .title {
    font-size: 17PX;
    color: #000;
    font-weight: 500;
    line-height: 24PX;
  }
}

// 搜索
.search_wrap {
  width: 100%;
}

// 用户列表
.user_list_wrap {
  flex: 1;
  overflow-y: auto;
  padding: 16PX 16PX 16PX 16PX;
  position: relative;
  .user_item {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    padding: 8PX 0;
    .left {
      flex: 1;
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      overflow: hidden;
      padding-right: 8PX;
      .avatar_wrap {
        width: 24PX;
        height: 24PX;
        flex-shrink: 0;
        margin-right: 12PX;
      }
      .user_name {
        font-size: 14PX;
        color: #000;
        height: 20PX;
        line-height: 18PX;
        margin-right: 12PX;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .right {
      flex-shrink: 0;
      display: flex;
      font-size: 14PX;
      color: #0095FF;
      line-height: 20PX;
    }
  }
}
