import React, { useState, useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { connect } from 'umi';
import { randomColor, processNames } from '@/utils/utils'
import styles from './index.less';
import {commentsLike, getCommentList} from '@/services/recommended'
import Avatar from '@/components/Avatar';
import { parseText } from '@/utils/im-index'
import CommentModal from '@/pages/CreateGraphicsText/ComponentsH5/CommentModal'
import {Spin} from "antd";
import classNames from "classnames";

interface PropsType {
  data: any,
}

const Index: React.FC<PropsType> = forwardRef((props: PropsType) => {
  const { imageTextId,dispatch,loading, updateCommentFooter } = props;

  const [visible, setVisible] = useState(false)
  const [commnetData, setCommnetData] = useState(null)

  useEffect(() => {
    if(imageTextId){
      commentsList()
    }
  },[imageTextId])

  useImperativeHandle(props.onRef, () => ({
    commentsList: commentsList,
  }))

  const commentsList = async ()=>{
    let DataByCommnetList = await getCommentList({
      imageTextId:imageTextId
    })
    const { code,content } = DataByCommnetList || {}
    if (code == 200 && content) {
      setCommnetData({
        ...content,
      })
      updateCommentFooter(content.commentsCount)
    }else {
      setCommnetData(null)
    }
  }

  const browserBack = () => {
    setVisible(false)
  }

  // 交互优化
  useEffect(() => {
    window.addEventListener('popstate', browserBack)
    return () => {
      window.removeEventListener('popstate', browserBack)
    }
  }, [])

  // 收藏
  const collect = async (value) => {
    if(!value){ return }
    let commnetListByList = commnetData.commentsList.map((item,index)=>{
      let likeNum = !value.likeFlag ? item.likeNum + 1 : item.likeNum - 1;
      return item.id == value.id ? {
        ...item,
        likeFlag:!value.likeFlag,
        likeNum: likeNum == 0 ? null : likeNum
      } : item
    })
    await dispatch({
      type:'recommended/save',
      payload: {
        dataByGetCommentList:commnetListByList
      }
    })
    let DataBycommentsLike = await dispatch({
      type:'recommended/commentsLike',
      payload: {
        imageTextId:value.imageTextId, // id
        commentsId :value.id, // 评论id
        status: !value.likeFlag ? 1 : 0, // 状态
      }
    })

    const {code} = DataBycommentsLike || {}
    if (code == 200 && commnetData && Array.isArray(commnetData.commentsList)) {
      let newCommentsList = commnetData.commentsList.map((item,index)=>{
        if(item.id == value.id){
          let likeFlag = !item.likeFlag;
          item.likeFlag = likeFlag
          item.likeNum = likeFlag ? item.likeNum + 1 : item.likeNum - 1;
        }
        return item
      })
      setCommnetData({...commnetData, commentsList:newCommentsList})
    }
  }

  // 打开弹窗
  const commentModalShow = (item) => {
    window.history.pushState(null, null, document.URL)
    setVisible(item)
  }

  // 关闭弹窗
  const commentModalHide = () => {
    window.history.go(-1)
    commentsList()
    setVisible(false)
  }

  return (
    <>
      <Spin spinning={!!loading.effects['recommended/getCommentList'] }>
        <div className={styles.container}>
          <div className={styles.container_title}>评论 {commnetData!=null&&commnetData.commentsCount}</div>

          {commnetData!=null&&Array.isArray(commnetData.commentsList) && commnetData.commentsList.map((item: any, index: number) => {
            const {
              commentsContent,          //: [评论/回复内容]
              commentsDate,             //: [评论/回复日期]
              commentsUserName,         //: [评论/回复用户名称]:"志君"
              createUserId,             //: 60
              headUrlShow,              //: [头像展示路径]:null
              likeFlag,                 //: [当前用户评论点赞标志]: false
              likeNum,                  //: [点赞数量]:null
              replyNum,                 //: [评论回复数量]:null
            } = item || {};
            return (
              <div key={index} className={styles.comment_item}>
                <div className={styles.comment_header}>
                  <Avatar
                    userInfo={{
                      userId:createUserId,
                      name:commentsUserName,
                      headUrlShow:headUrlShow,
                    }}
                    size={32}
                  ></Avatar>
                  <div className={styles.right}>
                    <div className={styles.right_content}>
                      <div className={styles.user_box}>{commentsUserName}</div>
                      <div className={styles.time}>{commentsDate}</div>
                    </div>
                    <div className={styles.like}>
                      <i
                        className={classNames({
                          [styles.like]:true,
                          [styles.like_active]:likeFlag,
                        })}
                        onClick={()=>{collect(item)}}>
                      </i>
                      <span>{likeNum}</span>
                    </div>
                  </div>
                </div>
                <div
                  className={styles.comment_content}
                  dangerouslySetInnerHTML={{ __html: parseText(commentsContent) }}
                ></div>
                <div className={styles.comment_reply_box}>
                  <div className={styles.reply_btn} onClick={()=>{
                    commentModalShow(item)
                  }}>
                    <span>{replyNum > 0 ? `${replyNum}条` : ""}回复</span>
                    <i></i>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </Spin>
      <CommentModal
        visible={visible}
        onCancel={commentModalHide}
      />
    </>
  )
})

export default connect(({ userInfoStore,recommended, loading }: any) => ({ userInfoStore,recommended, loading }))(Index)
