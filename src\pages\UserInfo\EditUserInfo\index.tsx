/**
 * @Description: h5端-我的-编辑账户页
 */
import React, { useEffect, useState } from 'react'
import { connect, history } from "umi";
import styles from './index.less';
import { Spin, Button, Input, Form, Upload, message } from 'antd';
import { Toast } from 'antd-mobile';
import { getOperatingEnv } from '@/utils/utils';
import {stringify} from "qs";
import editIcon from '@/assets/GlobalImg/right_arrow.png';
import NavBar from '@/components/NavBar';

const Index: React.FC = (props: any) => {
	const { dispatch, loading } = props || {};
	const [isEditMode, setIsEditMode] = useState(false); // 是否编辑 true:是, false: 不是编辑
	const [userInfo, setUserInfo] = useState({}); // 用户信息
	const [userName, setUserName] = useState(''); // 姓名
	const [userNickName, setUserNickName] = useState(''); // 昵称
	const [loadingByUpload, setLoadingByUpload] = useState(false);  // loading
	const [fileListByState, setFileListByState] = useState(null); // 头像文件
	const [form] = Form.useForm();
	const userInfoData = JSON.parse(localStorage.getItem('userInfo') || '{}');

	useEffect(() => {
    initialization();
  }, [isEditMode])

  // 初始化获取用户是否登录
	const initialization = ()=>{
    dispatch({
      type: 'userInfoStore/getUserInfo',
      payload: {
        wxUserId: userInfoData && userInfoData.friUserId,
      }
    }).then(res => {
      const { code, content } = res || {};
      if(code == 200) {
				const { name, nickName } = content;
				setUserName(name);
				setUserNickName(nickName);
				form.setFieldValue('name', name);
				form.setFieldValue('nickName', nickName);
				localStorage.setItem('userInfo', JSON.stringify({
          ...userInfoData,
          headUrl: content.headUrlShow,
					name: content.name,
					nickName: nickName
        }))
        setUserInfo(content);

      } else {
        setUserInfo(null);
      }
    }).catch(err=>{
      message.error(err.msg);
    })
	}

	// 上传图片headers
  const getHeaders=() =>{
    // 获取环境类型，1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用   5 FRIDAY app中使用
    const env = getOperatingEnv()
    // FRIDAY APP中本地存储值：access_token、client、phone、user_name、wxuserId、userInfo
    return {// token
      access_token:localStorage.getItem('access_token'),
      username: env == 5 ? localStorage.getItem('user_name') : localStorage.getItem('vxOpenIdCipherText') ? localStorage.getItem('vxOpenIdCipherText')  : userInfoData?.phone,
      client: env == 5 ? localStorage.getItem('client') : 'WX',
      type: env == 1 ? null : 1, // h5 传1
    }
  }

	// 上传事件
	const onChangeByUpload = (info)=>{
    let fileList;
    fileList = info && info.fileList;
    if (info.file.status === 'uploading') {
      setLoadingByUpload(true);
      return;
    }

    // 判断当前上传已被拦截
    if (info && !info.file.status) { fileList = null;return;}
    if (info && info.file.status === 'error') {
      setLoadingByUpload(false);
      message.error('上传失败');fileList = null;return
    }
    if (info && info.file.status === 'done') {
      setLoadingByUpload(false);
      if(info && info.file.response && info.file.response.code != 200) {
        message.error(info.file.response.msg ? info.file.response.msg : '上传失败')
        fileList = [];
        return
      }
    }

    if (info.file.type === "image/png" || info.file.type === "image/jpeg" || info.file.type === "image/gif") {
      if(info.file.response && info.file.response.code== 200 && info.file.response.content) {
        setFileListByState(info.file.response.content)
      }
    }
  }

	// 上传校验规则
	const beforeUpload = (file) => {
    const isSize = file.size / 1024 / 1024 < 15;
    if (!isSize) {
      message.error('超过15M限制，不允许上传~');
      return false;
    }

    const { name:fileName } = file || {}
    // 添加对文件后缀名的限制
    let suffix = fileName.substring(fileName.lastIndexOf('.')+1)
    let isJpgOrPng = file.type === 'image/jpg' || file.type === 'image/jpeg' ||  file.type === 'image/png';
    // 文件后缀名可以大写,所以需要添加大写后缀名的判断
    let isSuffixByJpgOrPng = (
      suffix === 'jpg'
      || suffix === 'JPG'
      || suffix === 'jpeg'
      || suffix === 'JPEG'
      || suffix === 'png'
      || suffix === 'PNG'
    )
    if (!isJpgOrPng || !isSuffixByJpgOrPng) {
      message.error('只能上传JPG 、JPEG  、PNG 格式的图片~');
      return false;
    }
    return isJpgOrPng;
  };

	// 保存-修改用户信息
	const submitUserInfo = () => {
		dispatch({
      type: 'userInfoStore/editUserinfo',
      payload: {
				headUrl: fileListByState && fileListByState.fileUrl || undefined, // 上传头像短路径
				name: userName || undefined, // 名字
				nickName: userNickName || '', // 昵称
      }
    }).then(res => {
      const { code, content } = res || {};
      if(code == 200) {
				Toast.show({icon: 'success', content: '保存成功'})
				setIsEditMode(false);
				initialization()
      }
    }).catch(err=>{
      message.error(err.msg);
    })
	}

	// 取消
	const cancelUserInfo = () => {
		form.resetFields()
		setFileListByState(null);
		setUserName('');
		setUserNickName('');
		setIsEditMode(false);
	}

	const getUserInfoLoading = !!loading.effects['userInfoStore/getUserInfo'];
	const editUserinfoLoading = !!loading.effects['userInfoStore/editUserinfo'];

	return (
		<Spin spinning={getUserInfoLoading || editUserinfoLoading}>
			<div className={styles.Information_wrap}>
				<NavBar title={'账户信息'}></NavBar>
				{
					!isEditMode ?
					// 正常显示样式
					<div className={styles.Information_box}>
						<div className={styles.Information_info_box_chever}>
							<div className={styles.Information_info_content}>
								<div className={styles.Informatio_info_box_Item_Left}>我的头像</div>
								<div className={styles.Informatio_info_box_Item_Right}>
									<div className={styles.item_HeadPicture}>
										{userInfo && userInfo.headUrlShow && <img className={styles.item_HeadPicture_img} src={userInfo.headUrlShow} alt="" />}
									</div>
								</div>
							</div>
							<div className={styles.Information_info_content}>
								<div className={styles.Informatio_info_box_Item_Left}>昵称</div>
								<div className={styles.Informatio_info_box_Item_Right}>
									{userInfo && userInfo.nickName ? userInfo.nickName : <span style={{color: "#ccc"}}>输入昵称</span>}

								</div>
							</div>
							<div className={styles.Information_info_content}>
								<div className={styles.Informatio_info_box_Item_Left}>姓名</div>
								<div className={styles.Informatio_info_box_Item_Right}>{userInfo && userInfo.name || ''}</div>
							</div>
							<div className={styles.Information_info_content}>
								<div className={styles.Informatio_info_box_Item_Left}>手机号</div>
								<div className={styles.Informatio_info_box_Item_Right}>{userInfo && userInfo.phone || ''}</div>
							</div>
						</div>
						<div className={styles.Informatio_btn_edit_account} onClick={() => {setIsEditMode(true)}}>
							编辑账户
						</div>
					</div> :
					// 编辑样式
					<div className={styles.Information_box}>
						<Form form={form} validateTrigger="onBlur" onFinish={submitUserInfo}>
							<div className={styles.Information_info_box_chever}>
								<div className={styles.Information_info_content}>
									<div className={styles.Informatio_info_box_Item_Left}>我的头像</div>
									<Spin spinning={loadingByUpload}>
									<div className={styles.Informatio_info_box_Item_Right}>
										<div className={styles.item_HeadPicture}><img src={fileListByState?.fileUrlView || userInfo.headUrlShow} alt="" style={{ width: '100%' }} /></div>
										<Upload
											headers={getHeaders()}
											accept="image/*"
											action={`/api/server/base/uploadFile?${stringify({ fileType: 1, userId: userInfoData?.friUserId})}`}
											listType="picture-card"
											className={styles.edit_head_picture}
											onChange={onChangeByUpload}
											onRemove={()=>{}}
											beforeUpload={beforeUpload}
											showUploadList={false}
										>
										</Upload>
										<span className={styles.edit_right_arrow}><img src={editIcon} alt="" /></span>
									</div>
									</Spin>
								</div>
								<div className={styles.Information_info_content_edit}>
									<div className={styles.Informatio_info_box_Item_Left}>昵称</div>
									<div className={styles.Informatio_info_box_Item_Right}>
										<Form.Item
											label=""
											name="nickName"
											rules={[
												{pattern: /^[\u4E00-\u9FA5A-Za-z0-9]+$/, message: '仅支持中、英文和数字!'},
												{max: 12, message: '输入昵称过长!'}
											]}
										>
											<Input type="text" autoComplete="off" bordered={false} maxLength={12} placeholder='输入昵称' onChange={(e) => setUserNickName(e.target.value)} />
										</Form.Item>
										<span className={styles.edit_right_arrow}><img src={editIcon} alt="" /></span>
									</div>
								</div>
								<div className={styles.Information_info_content_edit}>
									<div className={styles.Informatio_info_box_Item_Left}>姓名</div>
									<div className={styles.Informatio_info_box_Item_Right}>
										<Form.Item
											label=""
											name="name"
											rules={[
												{pattern: /^[\u4E00-\u9FA5A-Za-z]+$/, message: '仅支持中、英文!'},
												{max: 32, message: '输入姓名过长!'},
											]}
										>
											<Input type="text" autoComplete="off" bordered={false} placeholder='请输入姓名' onChange={(e) => setUserName(e.target.value)} />
										</Form.Item>
										<span className={styles.edit_right_arrow}><img src={editIcon} alt="" /></span>
									</div>
								</div>
								<div className={styles.Information_info_content}>
									<div className={styles.Informatio_info_box_Item_Left}>手机号</div>
									<div className={styles.Informatio_info_box_Item_Right}>{userInfo && userInfo.phone || ''}</div>
								</div>
							</div>

							<div className={styles.Informatio_edit_Wrap}>
								<Button className={styles.Informatio_edit_cancel} onClick={cancelUserInfo}>
									取消
								</Button>
								<Button className={styles.Informatio_edit_save} htmlType='submit' loading={editUserinfoLoading}>
									保存
								</Button>
							</div>
						</Form>
					</div>
				}
			</div>
		</Spin>
	)
}

export default connect(({ userInfoStore, loading }: any) => ({userInfoStore, loading}))(Index)
