.filter_box {
  position: fixed;
  z-index: 999;
  top: 96px;
  left: 0;
  right: 0;
  background: #fff;
  display: none;
  padding: 0 6px 16px;
  &.filter_box_pc {
    max-width: 750px;
    margin: 0 auto;
  }
  &.filter_box_show {
    display: block;
  }
  .filter_content_box {
    padding-top: 12px;
    max-height: 378px;
    overflow-y: auto;
  }
  .filter_title {
    font-size: 14px;
    color: #999;
    line-height: 20px;
    margin-bottom: 12px;
    padding: 4px 6px 0;
  }
  .filter_content {
    display: flex;
    flex-wrap: wrap;
    position: relative;
    .filter_item_box {
      padding: 0 6px;
      width: 33.33%;
      margin-bottom: 16px;
    }
    .filter_item {
      text-align: center;
      height: 32px;
      line-height: 32px;
      font-size: 14px;
      color: #666;
      background: #F5F5F5;
      border-radius: 16px;
      &.checked {
        background: #EDF9FF;
        color: #0095FF;
      }
    }
    &.filter_content_long {
      .filter_item_box {
        width: 50%;
      }
      .filter_item_separator {
        position: absolute;
        top: 16px;
        left: 50%;
        transform: translateX(-50%);
        width: 6px;
        height: 1px;
        background: #CCC;
      }
    }
    &.filter_content_time {
      .filter_item {
        color: #ccc;
        &.value {
          color: #999;
        }
      }
    }
  }
  .filter_btn_box {
    display: flex;
    text-align: center;
    line-height: 40px;
    font-size: 15px;
    font-weight: 500;
    margin-top: 16px;
    & > div {
      flex: 1;
      height: 40px;
      border-radius: 22px;
    }
    .filter_btn_cancel {
      background: #EDF9FF;
      color: #0095FF;
      margin-right: 16px;
    }
    .filter_btn_ok {
      background: #0095FF;
      color: #fff;
    }
  }
}
