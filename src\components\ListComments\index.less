.operation_area_content {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;

  .operation_btn {
    display: flex;
    align-items: center;

    .operation_btn_icon {
      // width: 20px;
      height: 20px;
      margin-right: 16px;
      display: flex;
      align-items: center;

      .operation_btn_num {
        margin-left: 5px;
        font-size: 12px;
      }
    }
  }

  .gdp {
    display: flex;
    align-items: center;
    font-size: 12px;
    font-weight: 400;
    color: #777777;
    line-height: 18px;
  }
}

.like_text_content_Warp {
  display: flex;
  align-items: stretch;
  margin-top: 8px;
}

.like_text_content {
  display: flex;
  align-items: center;
  height: 18px;
  /* font-size: 12px; */
  font-weight: 400;
  color: #777777;
  /* margin-bottom: 8px; */
  margin-right: 5px;

  .right_avatar {
    width: 23px;
    min-width: 23px;
    height: 23px;
    border: 1px solid #fff;
    border-radius: 50%;
    margin-right: -8px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    font-size: 10px;
    font-weight: 500;
    color: #fff;
    text-align: center;
    line-height: 24px;
    white-space: nowrap;
    &:last-child {
      margin-right: 0;
    }
  }

  .like_text {
    margin-left: 8px;
    span {
      color: #000000;
    }
  }
}

.my_comments_content {
  width: 100%;
  display: flex;
  align-items: baseline;
  margin-top: 8px;

  .my_comments_name {
    font-size: 13px;
    font-weight: 500;
    color: #000000;
    line-height: 18px;
    flex-shrink: 0;
    margin-right: 4px;
  }

  .my_comments_text {
    word-break: break-all;
    font-size: 13px;
    font-weight: 400;
    color: #000000;
    line-height: 18px;
  }
}

.comments_input_content {
  display: flex;
  align-items: center;
  margin-top: 8px;

  .left_picture {
    width: 24px;
    min-width: 24px;
    height: 24px;
    border-radius: 50%;
    position: relative;
    margin-right: 8px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    font-size: 12px;
    font-weight: 500;
    color: #fff;
    text-align: center;
    line-height: 24px;
    white-space: nowrap;
    flex-shrink: 0;
  }

  .comments_input {
    font-size: 13px;
    font-weight: 400;
    color: #666666;
    line-height: 18px;
    flex: 1;
  }
}

.fixed_footer_wrap{
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 998;
  background: #FFF;
}
.input_wrap {
  padding: 8px 12px;
  display: flex;
  align-items: flex-end;
  flex-wrap: nowrap;
  box-shadow: 0 -3px 5px 0 rgba(0,0,0,0.1);
  .textarea_wrap {
    flex: 1;
    background: #F5F5F5;
    border-radius: 25px;
    display: flex;
    align-items: center;
    :global {
      .adm-text-area {
        position: relative;
      }
      .adm-text-area-element {
        font-size: 14px;
        color: #000;
        padding: 7px 16px;
      }
    }
    .textarea_Icon {
      width: 22px;
      height: 20px;
      background: url("../../assets/GlobalImg/smile.png") no-repeat;
      background-size: 100% 100%;
      margin-right: 8px;
      cursor: pointer;
      user-select: none;
    }
  }
  .btn_wrap {
    flex-shrink: 0;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    margin-left: 6px;
    & > i {
      width: 32px;
      height: 36px;
      background: url("../../assets/GlobalImg/smile.png") no-repeat center;
      background-size: 20px 20px;
    }
    & > span {
      font-size: 14px;
      color: #0095FF;
      font-weight: 500;
      margin-left: 8px;
      height: 36px;
      line-height: 37px;
    }
  }
}
