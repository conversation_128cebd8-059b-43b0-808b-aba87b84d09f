/**
 * @Description: 移动端附件组件
 * @author: 赵斐
 */
import React from 'react';
import docxIcon from '@/assets/Consultation/H5/docx_icon.png'
import xlsxIcon from '@/assets/Consultation/H5/xlsx_icon.png'
import zipIcon from '@/assets/Consultation/H5/zip_icon.png'
import pptxIcon from '@/assets/Consultation/H5/pptx_icon.png'
import pdfIcon from '@/assets/Consultation/H5/pdf_icon.png'
import stlIcon from '@/assets/Consultation/H5/stl_icon.png'
import styles from './index.less'

interface PropsType {
  dataSource: any,   // 附件数据
  isOpenAnnexStatus: boolean,   // 是否展示打开附件按钮
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { isOpenAnnexStatus, dataSource } = props;

  /**
   * 获取文件格式icon
   * @param suffix   文件后缀
   * @returns
   */
  const annexFormatFun = (suffix: string): any => {
    switch (suffix) {
      case 'docx':
        return <img src={docxIcon} alt={suffix} />
      case 'doc':
        return <img src={docxIcon} alt={suffix} />
      case 'xlsx':
        return <img src={xlsxIcon} alt={suffix} />
      case 'xls':
        return <img src={xlsxIcon} alt={suffix} />
      case 'zip':
        return <img src={zipIcon} alt={suffix} />
      case 'pptx':
        return <img src={pptxIcon} alt={suffix} />
      case 'ppt':
        return <img src={pptxIcon} alt={suffix} />
      case 'pdf':
        return <img src={pdfIcon} alt={suffix} />
      case 'stl':
        return <img src={stlIcon} alt={suffix} />
      default:
        return ''
    }
  }

  /**
 * 文件大小处理
 * @param val
 * @returns
 */
  const sizeFun = (val: number) => {
    let result = Math.ceil(val / 1024);
    return result.toFixed(1);
  }

  /**
   * 点击打开附件
   * @param url 附件地址
   */
  const look = (url: string) => {
    window.open(`http://www.pfile.com.cn/api/profile/onlinePreview?url=${url}`)
    // window.open(`https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(url)}`)
  }
  return (
    <>
      {
        dataSource.map((item: any, index: number) => {
          return (
            <div className={styles.content_list} key={index}>
              <div className={styles.annex_format}>
                {annexFormatFun(item.fileSuffix)}
              </div>
              <div className={styles.annex_content}>
                <span className={styles.annex_name}>{item.fileName}.{item.fileSuffix}</span>
                <span className={styles.annex_size}>{sizeFun(item.fileSize)}kb</span>
              </div>
              {
                item.fileSuffix != "zip" && isOpenAnnexStatus ? <div className={styles.annex_look} onClick={() => { look(item.fileUrlShow) }}>打开</div> : null
              }
            </div>
          )
        })
      }
    </>
  )
}
export default Index
