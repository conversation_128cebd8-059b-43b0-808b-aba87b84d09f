.container {
  height: 100%;
  background: #EEF3F9;
  display: flex;
  flex-wrap: nowrap;
  flex-direction: column;
  align-items: center;
  .content {
    flex: 1;
    width: 100%;
    overflow-y: auto;
    .content_inner {
      max-width: 1228px;
      min-height: 100%;
      padding: 16px 0;
      margin: 0 auto;
      display: flex;
      flex-wrap: nowrap;
      flex-direction: column;
    }
  }
}

.header {
  width: 100%;
  flex-shrink: 0;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  height: 48px;
  .header_icon {
    width: 40px;
    height: 40px;
    background: url("../../../../assets/GlobalImg/pc_goback.png") no-repeat center;
    background-size: 20px 20px;
    margin-right: 8px;
    cursor: pointer;
  }
  .header_title {
    font-size: 18px;
    color: #000;
    font-weight: 600;
    line-height: 34px;
  }
}
.box {
  width: 100%;
  flex: 1;
  background: #fff;
  border-radius: 8px;
  padding-bottom: 16px;
}

.step_title {
  font-size: 20px;
  color: #000;
  font-weight: 500;
  line-height: 28px;
  text-align: center;
  margin-bottom: 32px;
  margin-top: 36px;
}
.form_wrap {
  width: 744px;
  margin: 0 auto;
}
.form_error_along {
  padding-left: 116px;
  font-size: 14px;
  color: #ff4d4f;
}
.form_title {
  font-size: 14px;
  color: #000;
  font-weight: 500;
  line-height: 26px;
  margin-bottom: 8px;
  margin-top: 20px;
  .required_mark {
    color: #FF5F57;
  }
}
.form_item_box {
  display: flex;
  flex-wrap: nowrap;
  .item_label {
    width: 104px;
    line-height: 26px;
    white-space: nowrap;
    margin-right: 12px;
    font-size: 14px;
    color: #000;
    text-align: right;
  }
  .item_content {
    flex: 1;
    :global {
      .ant-checkbox-wrapper + .ant-checkbox-wrapper {
        margin-left: 0;
      }
      .ant-checkbox-wrapper {
        color: #000;
        margin-right: 32px;
        &:last-child {
          margin-right: 0;
        }
      }
      textarea.ant-input {
        resize: none;
        padding: 12px;
        //line-height: 23px;
        //height: 72px;
        color: #000;
      }
      input.ant-input {
        height: 34px;
        color: #000;
      }
    }
    &.checkbox_content {
      padding-top: 2px;
    }
    &.image_content, &.file_content {
      .upload_box {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        .upload_btn {
          position: relative;
          margin-right: 12px;
          border-radius: 3px;
          display: flex;
          align-items: center;
          justify-content: center;
          :global {
            .ant-upload-picture-card-wrapper {
              width: 100%;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;
              z-index: 99;
            }
            .ant-upload.ant-upload-select-picture-card {
              width: 100%;
              height: 100%;
              display: block !important;
              opacity: 0;
            }
          }
        }
        .upload_text {
          font-size: 12px;
          color: #666;
          line-height: 26px;
        }
      }
    }
    &.image_content {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 14px;
      .image_item {
        position: relative;
        width: 60px;
        min-width: 60px;
        height: 60px;
        margin-right: 8px;
        margin-bottom: 8px;
        border-radius: 2px;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        .image_delete_btn {
          position: absolute;
          right: -5px;
          top: -5px;
          font-size: 10px;
          color: rgba(0,0,0,0.5);
          padding: 2px;
          cursor: pointer;
          :global {
            .anticon {
              display: block;
            }
          }
        }
      }
      .upload_box {
        .upload_btn {
          width: 60px;
          height: 60px;
          margin-bottom: 8px;
          background: #F8F8F8;
          font-size: 18px;
          color: #ccc;
        }
        .upload_text {
          margin-bottom: 8px;
        }
      }
    }
    &.file_content {
      .upload_box {
        .upload_btn {
          width: 80px;
          height: 30px;
          line-height: 30px;
          margin-bottom: 12px;
          background: #0095FF;
          font-size: 14px;
          color: #fff;
        }
        .upload_text {
          margin-bottom: 12px;
        }
      }
    }
    .description_content {
      height: 180px;
      border: 1px solid #d9d9d9;
      border-radius: 2px;
      padding: 12px;
      color: #000;
      overflow-y: auto;
      :global {
        .ant-input {
          height: 24px;
          border: 0;
          outline: 0;
          box-shadow: none;
          padding-left: 0;
          padding-right: 0;
        }
      }
      .child_item {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        .child_item_label {
          white-space: nowrap;
        }
        .child_item_content {
          flex: 1;
        }
      }
    }
  }
}
.case_description_template {
  display: flex;
  padding-left: 116px;
  padding-top: 12px;
  .template_item {
    height: 28px;
    line-height: 28px;
    padding: 0 8px;
    border-radius: 2px;
    border: 1px solid #EEE;
    font-size: 14px;
    color: #666;
    margin-right: 8px;
    cursor: pointer;
    &.checked {
      color: #0095FF;
      border-color: #0095FF;
    }
    :global {
      .anticon {
        margin-right: 2px;
      }
    }
  }
}
.case_file_list {
  margin-left: 116px;
  background: #F8F8F8;
  padding: 0 12px;
  border-radius: 3px;
  .file_item {
    height: 61px;
    border-bottom: 1px solid #D9D9D9;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &:last-child {
      border-bottom: 0;
    }
    .file_item_info {
      display: flex;
      align-items: center;
      .info_icon {
        width: 36px;
        height: 36px;
        background-repeat: no-repeat;
        background-position: center;
        background-size: 100% 100%;
        margin-right: 16px;
        &.doc, &.docx {
          background-image: url("../../../../assets/Consultation/H5/docx_icon.png");
        }
        &.xls, &.xlsx {
          background-image: url("../../../../assets/Consultation/H5/xlsx_icon.png");
        }
        &.ppt, &.pptx {
          background-image: url("../../../../assets/Consultation/H5/pptx_icon.png");
        }
        &.zip {
          background-image: url("../../../../assets/Consultation/H5/zip_icon.png");
        }
        &.pdf {
          background-image: url("../../../../assets/Consultation/H5/pdf_icon.png");
        }
        &.stl {
          background-image: url("../../../../assets/Consultation/H5/stl_icon.png");
        }
      }
      .info_name {
        font-size: 14px;
        color: #000;
        line-height: 20px;
      }
      .info_size {
        font-size: 12px;
        color: #999;
        line-height: 17px;
      }
    }
    .file_item_btn {
      font-size: 14px;
      color: #FF5F57;
      cursor: pointer;
    }
  }
}
.step_btn_box {
  display: flex;
  justify-content: center;
  margin-top: 36px;
  :global {
    .ant-btn {
      height: 36px;
      line-height: 36px;
      min-width: 106px;
      padding: 0 15px;
    }
  }
  .btn_prev {
    color: #0095FF;
    border: 1px solid #0095FF;
    margin-right: 16px;
  }
}
