// 图片组件的config.number，兼容老数据的转换处理
export function getNewImageNumber(oldNumber) {
  switch (oldNumber) {
    case 1:
      return 11
    case 2:
      return 12
    case 3:
      return 13
    case 4:
      return 14
    case 5:
      return 50
    case 6:
      return 60
    default:
      return oldNumber
  }
}

/* 首页友盟埋点统计 */
// 首页，搜索词搜索次数排行，记录搜索词
export function page_search(label) {
  window._czc && window._czc.push(['_trackEvent', '首页-搜索框点击量', 'page_search', label])
}

// 首页，banner点击量，记录第几个
export function page_banner(moduleIndex, label) {
  window._czc && window._czc.push(['_trackEvent', '首页-banner点击量', `page_banner_${moduleIndex}`, `第${moduleIndex}个banner，第${label}张图`])
}

// 首页，金刚区点击量，记录第几个
export function jinGang_click(moduleIndex, label) {
  window._czc && window._czc.push(['_trackEvent', '首页-金刚区点击量', `jinGang_click_${moduleIndex}`, `第${moduleIndex}个金刚区，第${label}张图`])
}

// 首页，3个运营位，记录第几个
export function ad_click(moduleIndex, label) {
  window._czc && window._czc.push(['_trackEvent', '首页-3个运营位', `ad_click_${moduleIndex}`, `第${moduleIndex}个运营位，第${label}张图`])
}

// 首页，更多空间点击量（相当于第一个一行一个图片点击量）
export function morespace_click() {
  window._czc && window._czc.push(['_trackEvent', '首页-更多空间点击量', 'morespace_click'])
}

// 首页，空间点击量
export function space_click(moduleIndex, label) {
  window._czc && window._czc.push(['_trackEvent', '首页-空间点击量', `space_click_${moduleIndex}`, `第${moduleIndex}个空间组件，第${label}个空间`])
}

// 首页，更多王国点击量（相当于第二个一行一个图片点击量）
export function morekingdom_click() {
  window._czc && window._czc.push(['_trackEvent', '首页-更多王国点击量', 'morekingdom_click'])
}

// 首页，王国点击量
export function kingdom_click(moduleIndex, label) {
  window._czc && window._czc.push(['_trackEvent', '首页-王国点击量', `kingdom_click_${moduleIndex}`, `第${moduleIndex}个王国组件，第${label}个王国`])
}

// 首页，学术更多课程点击量（相当于第三个一行一个图片点击量）
export function morecourse_click() {
  window._czc && window._czc.push(['_trackEvent', '首页-学术更多课程点击量', 'morecourse_click'])
}

// 首页，分类组件点击量（每一个分类组件都有分类标签，需要记录每个分类标签点击量和分类标签下的组件点击量）
export function group_click(moduleIndex, label) {
  window._czc && window._czc.push(['_trackEvent', '首页-分类组件点击量', `group_click_${moduleIndex}`, label])
}

// 首页，病例点击量
export function case_click(moduleIndex, label) {
  window._czc && window._czc.push(['_trackEvent', '首页-病例点击量', `case_click_${moduleIndex}`, `第${moduleIndex}个病例组件，第${label}个病例`])
}

// 首页，图文点击量
export function imageText_click(moduleIndex, label) {
  window._czc && window._czc.push(['_trackEvent', '首页-左图右文点击量', `imageText_click_${moduleIndex}`, `第${moduleIndex}个左图右文组件，第${label}个左图右文`])
}

// 首页，更多5A课程点击量（相当于第四个一行一个图片点击量）
export function more5Acourse_click() {
  window._czc && window._czc.push(['_trackEvent', '首页-更多5A课程点击量', 'more5Acourse_click'])
}

// 首页，更多运营课程点击量（相当于第五个一行一个图片点击量）
export function moreOPcourse_click() {
  window._czc && window._czc.push(['_trackEvent', '首页-更多运营课程点击量', 'moreOPcourse_click'])
}

// 首页，更多护理课程点击量（相当于第六个一行一个图片点击量）
export function moreNERcourse_click() {
  window._czc && window._czc.push(['_trackEvent', '首页-更多护理课程点击量', 'moreNERcourse_click'])
}

// 首页，更多病例点击量（相当于第七个一行一个图片点击量）
export function moreCasecourse_click() {
  window._czc && window._czc.push(['_trackEvent', '首页-更多病例点击量', 'moreCasecourse_click'])
}

// 首页，底部菜单广场按钮点击量
export function square_click() {
  window._czc && window._czc.push(['_trackEvent', '首页-底部菜单广场按钮点击量', 'square_click'])
}
