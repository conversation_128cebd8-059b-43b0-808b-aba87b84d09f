import React, { useEffect, useState, useRef } from 'react'
import { connect, history } from 'umi'
import classNames from 'classnames'
import { getIsIniPhoneAndWeixin, processNames, randomColor, gdpFormat } from '@/utils/utils'
import { message, Spin } from 'antd'
import { Toast } from 'antd-mobile'
import styles from './index.less'

import NavBar from '@/components/NavBar'
import ArticleCard from '@/components/ArticleCard'
import UserCardByImageText from '@/components/UserCardByImageText'
import CommentList from '@/pages/CreateGraphicsText/ComponentsH5/CommentList'
import CommentFooter from '@/pages/CreateGraphicsText/ComponentsH5/CommentFooter'
import UserCardByNavBar from '@/pages/CreateGraphicsText/ComponentsH5/UserCardByNavBar'

import { saveCommentsOrReply } from '@/services/recommended';

const Index: React.FC = (props: any) => {
  const { id } = history.location.query
  const { dispatch } = props;
  const refContainer = useRef(null)
  const refUserCard = useRef(null)
  const commentListRef = useRef(null)
  const [detailsState, setDetailsState] = useState<any>(null)                  // 详情数据
  const [navBarToggle, setNavBarToggle] = useState(false)                      // 导航状态
  const [loadingImgTextInfoById, setLoadingImgTextInfoById] = useState(false)  // loading

  const {
    createUserId,             // 创建人id
    userName,                 // 用户名称
    headUrlShow,              // 头像
    isExperts,                // 是否是专家
    operateDateDescs,          // 创建时间
    gdp,                      // GDP
    isFocus,                  // 是否关注
    expertsInfo,               // 专家信息
    forwardDescribe,           // 转发描述
    forwardSquareRecommendDto,   // 转发图文信息
  } = detailsState || {};

  // 滚动事件
  useEffect(() => {
    console.log('[]')
    document.getElementById('container') && document.getElementById('container').addEventListener('scroll', handleScroll)

    return () => {
      document.getElementById('container') && document.getElementById('container').removeEventListener('scroll', handleScroll)
    }
  }, [])

  // 获取图文详情
  useEffect(() => {
    imgTextInfoById()
  }, [])

  // 滚动事件
  const handleScroll = () => {
    const scrollTop = refContainer.current.scrollTop + 44
    const offset = refUserCard.current.offsetHeight + refUserCard.current.offsetTop
    if (scrollTop >= offset) {
      setNavBarToggle(true)
    } else {
      setNavBarToggle(false)
    }
  }

  // 图文详情
  const imgTextInfoById = (isLocalUpdate = false) => {
    if (!isLocalUpdate) {
      setLoadingImgTextInfoById(true)
    }
    dispatch({
      type: 'graphicsText/imgTextInfoById',
      payload: {
        imageTextId: id,                         // 图文ID
      }
    }).then(res => {
      setLoadingImgTextInfoById(false)
      const { code, content, msg } = res
      if (code == 200 && content) {
        setDetailsState(content)
      } else {
        Toast.show(msg || '数据加载失败')
      }
    }).catch(err => {
    })
  }

  // 关注或取消关注回调
  const handleFollowAndCheck = () => {
    imgTextInfoById(true)
  }

  // 图文点击事件
  const onClickImageTextContent = (e) => {
    console.log(e.target,e)
    if (e.target && e.target.dataset && e.target.dataset.type == 'user') {
      history.push(`/Expert/ExpertDetails?id=${e.target.dataset.id}`)
      return
    }
    if (e.target && e.target.dataset && e.target.dataset.type == 'topic') {
      history.push(`/CreateGraphicsText/TopicHome?topicId=${e.target.dataset.id}`)
      return
    }
    if (e.target && e.target.parentNode && e.target.parentNode.dataset && e.target.parentNode.dataset.type == 'topic') {
      history.push(`/CreateGraphicsText/TopicHome?topicId=${e.target.parentNode.dataset.id}`)
      return
    }
  }

  // 点击跳转帖子详情页
  const goToPost = (imageTextId) => {
    history.push(`/CreateGraphicsText/PostDetails?id=${imageTextId}`)
  }

  // 点击跳转空间详情页
  const goToSpace = (imageTextId) => {
    history.push(`/CreateGraphicsText/SpaceDetails?id=${imageTextId}`)
  }

  // 提交评论
  const onPost = async ({ commentInputInfo,  value}) => {
    if(commentInputInfo && value) {
      let params = {
        imageTextId: commentInputInfo.id,                     //  number 非必须  上级图文主键ID
        imageTextType: commentInputInfo.imageType,            //	number 非必须  上级图文类型
        commentsContent: value,                               //	string 非必须  上级评论/回复内容
        commentsType: 0,                                      // 评论类型
      }
      const data = await saveCommentsOrReply(params);
      const { code, content } = data || {}
      if (code == 200) {
        message.success('评论成功!')
        commentListRef?.current?.commentsList()
      }else {
        message.error('评论失败!')
      }
    }
  }

  // 更新评论数据
  const updateCommentFooter = (commentsCount) => {
    setDetailsState(prevState => {
      return {
        ...prevState,
        commentsCount
      }
    })
  }

  return (
    <Spin spinning={loadingImgTextInfoById} wrapperClassName={styles.spin}>
      {
        navBarToggle ?
          <UserCardByNavBar
            headUrlShow={headUrlShow}
            userName={userName}
            createUserId={createUserId}
            isExperts={isExperts}
            operateDateDescs={operateDateDescs}
            isFocus={isFocus}
            expertsInfo={expertsInfo}
            handleFollowAndCheck={handleFollowAndCheck}
          />
          :
          <NavBar title="转发" bordered />
      }

      <div id="container" className={styles.container} ref={refContainer} style={getIsIniPhoneAndWeixin() ? { paddingBottom: '86px' } : {}}>
        <div ref={refUserCard} className={styles.user_card_wrap}>
          <UserCardByImageText
            headUrlShow={headUrlShow}
            userName={userName}
            createUserId={createUserId}
            isExperts={isExperts}
            operateDateDescs={operateDateDescs}
            isFocus={isFocus}
            expertsInfo={expertsInfo}
            handleFollowAndCheck={handleFollowAndCheck}
            isShowMoreOperate={false}
          />
        </div>
        <div className={styles.image_text_content_wrap}>
          <div className="ql-editor" onClick={onClickImageTextContent} dangerouslySetInnerHTML={{__html: forwardDescribe}}></div>
        </div>
        {
          forwardSquareRecommendDto ?
            <div className={styles.forward_content}>
            {
              forwardSquareRecommendDto.imageType == 1 ?
                <ArticleCard style={{padding: 0, marginBottom: 0, background: 'none'}} item={forwardSquareRecommendDto}/>
                : forwardSquareRecommendDto.imageType == 2 ?
                <div className={styles.post_content} onClick={() => goToPost(forwardSquareRecommendDto.id)}>
                  <div
                    className={styles.init_img}
                    style={
                      forwardSquareRecommendDto.textImgList && forwardSquareRecommendDto.textImgList[0] ?
                        {backgroundImage: `url(${forwardSquareRecommendDto.textImgList[0].imageUrlShow})`}
                        : forwardSquareRecommendDto.headUrlShow ?
                        {backgroundImage: `url(${forwardSquareRecommendDto.headUrlShow})`}
                        : {background: randomColor(forwardSquareRecommendDto.createUserId)}
                    }
                  >
                    {
                      forwardSquareRecommendDto.textImgList && forwardSquareRecommendDto.textImgList[0] || forwardSquareRecommendDto.headUrlShow ? null
                        : processNames(forwardSquareRecommendDto.userName)
                    }
                  </div>
                  <div
                    className={classNames('ql-editor', styles.text)}
                    dangerouslySetInnerHTML={{__html: forwardSquareRecommendDto.imageTextContent}}
                  ></div>
                </div>
                : forwardSquareRecommendDto.imageType == 4 ?
                  <div className={styles.space_wrap} onClick={() => goToSpace(forwardSquareRecommendDto.id)}>
                    <div
                      className={styles.left_cover_image}
                      style={
                        forwardSquareRecommendDto.textImgList && forwardSquareRecommendDto.textImgList[0] ? {backgroundImage: `url(${forwardSquareRecommendDto.textImgList[0].imageUrlShow})`}
                          : {backgroundColor: `${randomColor(forwardSquareRecommendDto.createUserId)}`}
                      }
                    >
                      {forwardSquareRecommendDto.textImgList && forwardSquareRecommendDto.textImgList[0] && forwardSquareRecommendDto.isTemplateCover == 1 ? <div className={styles.title_in_cover_image}>{forwardSquareRecommendDto.imageTitle}</div> : null}
                      {forwardSquareRecommendDto.textImgList && forwardSquareRecommendDto.textImgList[0] ? '' : processNames(forwardSquareRecommendDto.userName)}
                    </div>
                    <div className={styles.right}>
                      <div className={styles.space_title}>{forwardSquareRecommendDto.imageTitle}</div>
                      <div className={styles.space_introduce}>{forwardSquareRecommendDto.imageTextContent}</div>
                    </div>
                  </div>
                  : null
            }
          </div>
            : loadingImgTextInfoById ? null
            : <div className={styles.no_forward_content}>原内容已下架</div>
        }
        <div className={styles.gdp_wrap}>
          <i></i>
          <span>{gdpFormat(gdp)}GDP</span>
        </div>
        <CommentList
          onRef={commentListRef}
          imageTextId={id}
          updateCommentFooter={updateCommentFooter}
        />
      </div>
      <CommentFooter
        imageTextId={id}
        imgTextInfo={detailsState}
        onPost={onPost}
      />
    </Spin>
  )
}

export default connect(({ recommended,loading }: any) => ({ recommended,loading }))(Index)
