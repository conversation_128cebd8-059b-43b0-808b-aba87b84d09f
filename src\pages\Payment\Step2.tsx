import React, { useState,useEffect } from 'react';
import { history,connect } from 'umi';
import styles from './Step2.less';
import classNames from "classnames";
import {Button, message, Spin, Upload} from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import { debounce } from 'lodash'
import Navigation from "@/pages/Payment/components/PC/Navigation";
import { getOperatingEnv,WxAppIdByPublicAccount,WxAppIdByMini } from "@/utils/utils";
import {payOrder, confirmOrder, getWxMpOpenId, getJsapiTicket, getOrderIsPay} from "@/services/payment";
import QRCode from 'qrcode.react';
import { stringify } from "qs";
import BreadcrumbByPayment from "@/pages/Payment/components/PC/BreadcrumbByPayment";
import NavBar from '@/components/NavBar'         // 导航条

// 购买信息组件
const InformationBox: React.FC<{
  Information: object;
}> = ({ Information }) => {
  const {
    serviceValue,   //:'个人版1季度',
    enterpriseValue,//:'杭州佳沃思医疗科技有限公司',
    phoneValue,     //:'***********',
    planValue,      //:"600",
    planUnit,       //:"元/季",
    planPrice,
    totalPrice,
    planNum,
  } = Information || {};
  console.log('InformationInformation :: ',Information);

  return (
    <div className={styles.PurchaseInformationBox}>
      <div className={styles.PurchaseInformationBox_title}>购买信息</div>
      <div className={styles.PurchaseInformationBox_content}>

        <div className={styles.PurchaseInformationBox_content_item}>
          <div className={styles.PurchaseInformationBox_content_item_box}>
            <div className={styles.PurchaseInformationBox_content_item_box_lable}>订购服务：</div>
            <div className={styles.PurchaseInformationBox_content_item_box_value}>{serviceValue}</div>
          </div>
          <div className={styles.PurchaseInformationBox_content_item_box}></div>
        </div>

        <div className={styles.PurchaseInformationBox_content_item}>
          <div className={styles.PurchaseInformationBox_content_item_box}>
            <div className={styles.PurchaseInformationBox_content_item_box_lable}>企业全称：</div>
            <div className={styles.PurchaseInformationBox_content_item_box_value}>{enterpriseValue}</div>
          </div>
          <div className={styles.PurchaseInformationBox_content_item_box}>
            <div className={styles.PurchaseInformationBox_content_item_box_lable}>联系电话：</div>
            <div className={styles.PurchaseInformationBox_content_item_box_value}>{phoneValue}</div>
          </div>
        </div>

        <div className={styles.PurchaseInformationBox_content_item}>
          <div className={styles.PurchaseInformationBox_content_item_box}>
            <div className={styles.PurchaseInformationBox_content_item_box_lable}>购买方案：</div>
            <div className={styles.PurchaseInformationBox_content_item_box_value}>
              <span className={styles.text_red}>{planPrice}</span>
              <span> 元{planUnit} </span>
            </div>
          </div>
          <div className={styles.PurchaseInformationBox_content_item_box}>
            <div className={styles.PurchaseInformationBox_content_item_box_lable}>订单金额：</div>
            <div className={styles.PurchaseInformationBox_content_item_box_value}>
              <span className={styles.text_red}>{totalPrice}</span>
              <span> 元</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

const Payment: React.FC = (props: any) => {
  const [loading, setLoading] = useState(false);
  const [loadingByUpload, setLoadingByUpload] = useState(false);      // loading by upload
  const [imageUrl, setImageUrl] = useState<string>();
  const [pageType, setPageType] = useState(null);                        // 1 PC端  2移动端
  const [contentByConfirmOrder, setContentByConfirmOrder] = useState({}); // 获取确认订单的数据详情
  // 支付方式
  const [selectPayType, setSelectPayType] = useState('1');             // 选择的支付方式 1 微信支付  2 对公转账 (默认微信支付)
  const [wxPayQRCodeByURL, setWxPayQRCodeByURL] = useState(null);        // PC中生成微信支付二维码
  const [fileListByState,setFileListByState] = useState(null);           //
  let pollingId = null; // 支付状态计时器id
  const UerInfo = JSON.parse(localStorage.getItem('userInfo')) || {};
  // http://localhost:8000/Payment/Step2/54?random=0.4809346642312058
  const uploadButton = (
    <div>
      {loadingByUpload ? <LoadingOutlined /> : <i className={styles.LocalUpload}/>}
      <div style={{ marginTop: 8,color: '#999999' }}>{loadingByUpload ? '正在上传' : '点击上传'}</div>
    </div>
  );

  // 当前页面视口是否小于750
  let updateType = () => {
    // let clientWidth = document.documentElement.clientWidth;
    // let type = clientWidth > 750 ? 1 : 2;
    let env = getOperatingEnv() // 获取当前操作环境 1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用
    let type = env == 4 ? 1 : 2;
    setPageType(type);
  };
  updateType = debounce(updateType, 100);
  window.addEventListener('resize', updateType);

  useEffect(() => {
    updateType();
    initializeData();
  }, []);


  // 由于小程序支付失败会导致 调转到重新支付时
  // 当前webView存储的token有可能已丢失
  const getUserInfo = async () => {
    // 首先判定环境 如果当前环境是小程序端则从url中获取token和openid
    // 获取当前操作环境 1:微信浏览器 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用
    const env = getOperatingEnv()
    let access_token = localStorage.getItem('access_token');
    // 当前跳转环境非小程序端并且无登录token则记录当前页面跳转到登录-已在request中处理
    if(env == 1){
      // 当前环境是小程序端
      // 获取地址栏上的hash值
      const hash = window.location.hash;
      if (hash) {
        const encodedMessage = hash.slice(1);
        const decodedMessage = JSON.parse(decodeURIComponent(encodedMessage));
        const {
          access_token: access_tokenByMini,
          vxOpenIdCipherText: vxOpenIdCipherTextByMini,
        } = decodedMessage || {}
        // 保存从小程序来的token值和vxOpenIdCipherText
        localStorage.setItem('access_token', access_tokenByMini);
        localStorage.setItem('vxOpenIdCipherText', vxOpenIdCipherTextByMini);
      }
    }
  }

  // 初始化数据
  // 1. 根据地址栏的订单号获取订单详情
  // 2. 如果是当前在微信浏览器中使用,将地址栏参数code获取微信工作号openId
  const initializeData = async ()=>{
    let { match: { params: { orderId } },location } = props;
    let { query:query_location } = location || {}
    let { code:code_query,} = query_location || {}
    // 保存订单号
    sessionStorage.setItem('orderId',orderId);
    // 1. 由于小程序支付失败会导致 调转到重新支付时,当前webView存储的token有可能已丢失
    await getUserInfo();

    // 2. 根据地址栏的订单号获取订单详情
    setLoading(true)
    let dataByConfirmOrder = await confirmOrder({ orderId:orderId })
    setLoading(false)
    if(dataByConfirmOrder.code === 200 && dataByConfirmOrder.content) {
      // 获取订单数据详情相关信息
      setContentByConfirmOrder(dataByConfirmOrder.content);
    }else if (dataByConfirmOrder.code == 422 && dataByConfirmOrder.msg == "订单已支付") {
      if(getOperatingEnv() != 1) {
        history.replace(`/Payment/Step3/${orderId}?random=${Math.random()}`)
        return
      }else {
        wx && wx.miniProgram.navigateTo({url: `/pages/MemberPaymentPage/PaymentState/index?orderId=${orderId}`})
      }
    }else {
      message.warning(dataByConfirmOrder && dataByConfirmOrder.msg ? dataByConfirmOrder.msg : "获取订单信息失败")
    }
    // 3. 如果是当前在微信浏览器中使用,将地址栏参数code获取微信工作号openId
    // 获取当前操作环境 1:微信小程序 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用
    const env = getOperatingEnv()
    if(env == 2) { // 当前在微信浏览器中使用
      // 先授权
      // 当前在微信浏览器中使用
      // 如果是微信授权后转发回此页面会携带code=081mf30w36eow03Is32w3VtcAZ3mf30C&state=STATE%23wechat_redirect
      // 使用地址栏携带的code换取用户的openid
      const openIdByWx = localStorage.getItem('openIdByWx')
      // 当前存在微信公众号OpenId 则直接使用不重复调用接口
      if(!!openIdByWx) { return }

      if(!!code_query) {
        setLoading(true)
        const openIdContentByWx = await getWxMpOpenId({
          appId:WxAppIdByPublicAccount,
          code:code_query
        })
        setLoading(false)
        const { code,content } = openIdContentByWx || {}
        if(code == 200) {
          // 保存微信公众号OpenId
          localStorage.setItem('openIdByWx',content);
        }else {
          message.warning(openIdContentByWx && openIdContentByWx.msg ? openIdContentByWx.msg : '微信授权code失效')
        }
      }else {
        // 如果当前地址栏为携带code则触发跳转微信授权
        // 跳转公众号微信授权 授权成功后会携带code返回当前页面
        goWxPublicAccountAuth()
      }
    }else if(env == 4){ // PC浏览器使用
      // 生成付款二维码
      createWxPayORCode()
    }else if(env == 3){
      getOrderIsPayByCallBack()
    }else if(env == 5){
      getOrderIsPayByCallBack()
    }else if(env == 6){
      getOrderIsPayByCallBack()
    }
  }



  // 跳转公众号微信授权 授权成功后会携带code返回当前页面
  const goWxPublicAccountAuth = () => {
    let { location} = props
    let { query } = location || {}
    let newRandom = Math.random().toString(36).substr(2, 9);

    let params = {
      appid: WxAppIdByPublicAccount,
      redirect_uri: `${window.location.href.split('?')[0]}?${stringify({ random:newRandom })}`,
      response_type: 'code',  // 写死
      scope: 'snsapi_base',
      state: 'STATE#wechat_redirect',
    }
    window.location.href = `https://www.friday.tech/getWXcodeInfo.html?${stringify(params)}`
    // window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?${stringify(params)}#wechat_redirect`
  }


  // 判定当前设备是android还是ios
  // 场景类型，H5支付时必传，示例值：iOS, Android, Wap(PC)
  const getEnvByPage = () => {
    const userAgent = navigator.userAgent;
    const isAndroid = userAgent.indexOf('Android') > -1 || userAgent.indexOf('Adr') > -1;
    const isIOS = userAgent.indexOf('iPhone') > -1 && userAgent.indexOf('iPad') > -1 && userAgent.indexOf('iPod') == -1;
    return isAndroid? 'Android' : isIOS? 'iOS' : 'Wap';
  };



  // 点击创建支付二维码
  const createWxPayORCode = async ()=>{
    let {match: {params: {orderId}}} = props
    const env = getEnvByPage()
    const userInfoData = JSON.parse(localStorage.getItem('userInfo'));
    const { friUserId:id } = userInfoData || {} // 当前登录人id
    let params = {
      appId:WxAppIdByMini,     // 微信小程序-用户appid，微信小程序支付时必传
      orderId:orderId,
      payType: '3',            // 付款方式 1微信小程序 2微信H5 3微信二维码 4对公账户
      publicPayImgUrl:null,   // 对公账户方式必传，上传付款回执的图片路径
      wxOpenid:null,          // 微信小程序-用户openid，微信小程序支付时必传
      wxType:env,             // 场景类型，H5支付时必传，示例值：iOS, Android, Wap
      wxUserId:id,            // 微信小程序-用户id，微信小程序支付时必传
      userId:id,
    }
    setLoading(true)
    const DataByOrder = await payOrder(params);
    setLoading(false)
    console.log('DataByOrder :: ',DataByOrder);
    if (DataByOrder && DataByOrder.code == 200 && DataByOrder.content){
      const {
        payType, // : 3
        qrUrl,   // : "weixin://wxpay/bizpayurl?pr=YPfvFrjzz"
      } = DataByOrder.content || {}
      if(qrUrl) {
        setWxPayQRCodeByURL(qrUrl)
        // 生成后轮询监听支付状态
        getOrderIsPayByCallBack()
      }
    }else {
      message.warning(DataByOrder?.msg || "获取微信支付二维码失败" )
    }
  }

  // 上传回执单图片的headers
  const getHeaders=() =>{
    // 获取环境类型，1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用   5 FRIDAY app中使用
    const env = getOperatingEnv()
    // FRIDAY APP中本地存储值：access_token、client、phone、user_name、wxuserId、userInfo
    return {// token
      access_token:localStorage.getItem('access_token'),
      username: env == 5 ? localStorage.getItem('user_name') : localStorage.getItem('vxOpenIdCipherText') ? localStorage.getItem('vxOpenIdCipherText')  : UerInfo.phone,
      client: env == 5 ? localStorage.getItem('client') : 'WX',
      type: env == 1 ? null : 1,
    }
  }

  //
  const beforeUpload = (file) => {
    // const isGIF = file.type === "image/gif";
    const isSize = file.size / 1024 / 1024 < 15;
    if (!isSize) {
      message.error('超过15M限制，不允许上传~');
      return false;
    }

    const { name:fileName } = file || {}
    // 添加对文件后缀名的限制
    let suffix = fileName.substring(fileName.lastIndexOf('.')+1)
    let isJpgOrPng = file.type === 'image/jpg' || file.type === 'image/jpeg' ||  file.type === 'image/png';
    // 文件后缀名可以大写,所以需要添加大写后缀名的判断
    let isSuffixByJpgOrPng = (
      suffix === 'jpg'
      || suffix === 'JPG'
      || suffix === 'jpeg'
      || suffix === 'JPEG'
      || suffix === 'png'
      || suffix === 'PNG'
    )
    if (!isJpgOrPng || !isSuffixByJpgOrPng) {
      message.error('只能上传JPG 、JPEG  、PNG 格式的图片~');
      return false;
    }
    return isJpgOrPng;
  };

  const onChangeByUpload = (info)=>{
    let fileList;
    fileList = info && info.fileList;
    if (info.file.status === 'uploading') {
      setLoadingByUpload(true);
      return;
    }

    // 判断当前上传已被拦截
    if (info && !info.file.status) { fileList = null;return;}
    if (info && info.file.status === 'error') {
      setLoadingByUpload(false);
      message.error('上传失败');fileList = null;return
    }
    if (info && info.file.status === 'done') {
      setLoadingByUpload(false);
      if(info && info.file.response && info.file.response.code != 200) {
        message.error(info.file.response.msg ? info.file.response.msg : '上传失败')
        fileList = [];
        return
      }
    }

    if (info.file.type === "image/png" || info.file.type === "image/jpeg" || info.file.type === "image/gif") {
      if(info.file.response && info.file.response.code== 200 && info.file.response.content) {
        setFileListByState(info.file.response.content)
      }
    }
  }

  // [移动端-提交订单]提交确认订单
  const submit = debounce( (values)=> {
    // 获取当前操作环境 1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用
    const env = getOperatingEnv()
    // 选择的支付方式 1 微信支付  2 对公转账 (默认微信支付)
    if (selectPayType == 1) {
      // 选择使用微信支付
      if (env == 1) {
        // 微信小程序支付,[支付][微信小程序支付] 跳转到小程序支付页面
        payOrderByMini()
      } else if (env == 2) {
        // 当前在微信浏览器中使用,[支付][微信浏览器内支付] 从微信本地唤起支付
        payOrderByPublicAccount()
      } else if (env == 3) {
        // 移动端H5支付
        payOrderByH5()
      } else if (env == 4) {
        // PC浏览器扫码支付
        // submit此方法不会被PC触发
      } else if (env == 5) {
        // app中支付
        payOrderByH5()
      } else if (env == 6) {
        // app中支付
        payOrderByH5()
      }
    }else {
      // 选择使用对公转账方式
      payOrderBySubmit()
    }
  },500)

  // [支付][微信小程序支付] 跳转到小程序支付页面
  const payOrderByMini = async ()=>{
    let {match: { params: { orderId } }} = props
    wx && wx.miniProgram.navigateTo({url: `/pages/MemberPaymentPage/Payment/index?orderId=${orderId}`})
  }

  // [支付][微信浏览器内支付] 从微信本地唤起支付
  const payOrderByPublicAccount = async (values)=> {
    let {match: { params: { orderId } }} = props
    const env = getEnvByPage()
    const userInfoData = JSON.parse(localStorage.getItem('userInfo'));
    const { friUserId:id } = userInfoData || {} // 当前登录人id

    // 请求[getJsapiTicket]获取[wx.config]的配置
    const jsapiTicketContent = await getJsapiTicket({
      appId:WxAppIdByPublicAccount,
      currentUrl:window.location.href.split('#')[0],
    })
    const { code:codeByJsapi, content:contentByJsapi} = jsapiTicketContent || {}
    if(codeByJsapi != 200){
      message.warning(jsapiTicketContent && jsapiTicketContent.msg ?  jsapiTicketContent.msg : '获取微信配置失败!')
      return
    }

    // ① 先配置[wx.config]
    wx && wx.config({
      debug: false,
      appId: contentByJsapi.appId,
      timestamp: parseInt(contentByJsapi.timestamp),
      nonceStr: contentByJsapi.nonceStr,
      signature: contentByJsapi.signature,
      jsApiList: ['chooseWXPay'],              // 必填，随意一个接口即可
      // openTagList:['wx-open-launch-weapp'], // 填入打开小程序的开放标签名
    })

    // ② 请求payOrder获取唤起微信支付的签名等信息
    let params = {
      appId:contentByJsapi.appId,
      orderId:orderId,
      payType:'5',            // 付款方式 1微信小程序 2微信外H5 3微信二维码 4对公账户 5微信内浏览器H5
      publicPayImgUrl:null,   // 对公账户方式必传，上传付款回执的图片路径
      wxOpenid:localStorage.getItem('openIdByWx'), // 微信小程序-用户openid，微信小程序支付时必传
      wxType:env,             // 场景类型，H5支付时必传，示例值：iOS, Android, Wap
      wxUserId:id,            // 微信小程序-用户id，微信小程序支付时必传
      userId:id,
    }
    const DataByOrder = await payOrder(params);
    /**
     * content: {,…}
     * h5Url: "https://wx.tenpay.com/cgi-bin/mmpayweb-bin/checkmweb?prepay_id=wx21113054440209c05bbf16b7fd95d60000&package=1510157264"
     * payType: 2
     * enMsg: null
     * */
    if(DataByOrder.code == 200 && DataByOrder.content){
      /**
       * jsapiResult: {appId: "wx0070ba793e934dd6", timeStamp: "1682069842", nonceStr: "4xmpx4GcafctVYmGz8JdCZYrT7ECzNCy",…}
       * appId: "wx0070ba793e934dd6"
       * nonceStr: "4xmpx4GcafctVYmGz8JdCZYrT7ECzNCy"
       * packageValue: "prepay_id=wx211737221369596cb785ca6a7f1b520000"
       * paySign: "Tmn7KCQAKU5isaVjnUPErAmFVFNvdMT2sGO2zCCHELweiqq1xux0rDzIs+sEcag+vWk5ZZqLevU1O+ECw5EYbSUu/bdnI0lJ2PDZdvFIwhskVJzDrVNzr9tuSuPwKYaZrB+RAiUFeEuXStznKxRF+CpHllh6PrxD0NFZTEsF7l/A3CD+nyDnznEderdk5IDFfMZzXFOdtN0VKoiho7bMRvMk0WqCHwCS1C5aFgPao5FIoJ0N/9ASqTgyhSzi9N9oDYOPOM8OPjfQzbF/KYa4Pzftg4GXO1FCtBo/7z8plURzzgxJ7pYoRE2JseR9/ru4nhOJjMIR/IKr8icQ7P+fXA=="
       * signType: "RSA"
       * timeStamp: "1682069842"
       */
      const {
        jsapiResult,
      } = DataByOrder.content || {}
      const {
        timeStamp,
        nonceStr,
        packageValue, // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=\*\*\*）
        paySign, // 支付签名
        signType,// // 微信支付V3的传入RSA,微信支付V2的传入格式与V2统一下单的签名格式保持一致
        appId,
      } = jsapiResult || {}

      wx && wx.chooseWXPay({
        appId:appId,
        timestamp: timeStamp,   // 支付签名时间戳，注意微信jssdk中的所有使用timestamp字段均为小写。但最新版的支付后台生成签名使用的timeStamp字段名需大写其中的S字符
        nonceStr: nonceStr,     // 支付签名随机串，不长于 32 位
        package: packageValue,  // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=\*\*\*）
        signType: signType,     // 微信支付V3的传入RSA,微信支付V2的传入格式与V2统一下单的签名格式保持一致
        paySign: paySign,       // 支付签名
        success: function (res) {
          // 支付成功后的回调函数
          message.success('支付成功');
          // 跳转到支付成功页面
          goStep3()
        },
        cancel: function(res) {
          message.warning('微信支付取消')
        },
        fail:function(res){
          message.warning('微信支付失败')
          goStep3()
        }
      });
    }else {
      message.warning(DataByOrder && DataByOrder.msg ? DataByOrder.msg : '唤起微信支付失败!');
    }
  }

  // [支付] 使用对公转账方式的支付
  const payOrderBySubmit = async () => {
    let {match: {params: {orderId}}} = props
    const env = getEnvByPage()
    const envByOp = getOperatingEnv()
    const userInfoData = JSON.parse(localStorage.getItem('userInfo'));
    const { friUserId:id } = userInfoData || {} // 当前登录人id
    const { fileUrl } = fileListByState || {}
    if(!fileUrl){
      message.warning('请上传付款回执单!')
      return
    }
    let params = {
      appId:WxAppIdByMini,       // 微信外浏览器h5支付使用小程序appid
      orderId:orderId,           // 订单号
      payType:'4',               // 付款方式 1微信小程序 2微信H5 3微信二维码 4对公账户
      publicPayImgUrl:fileUrl,   // 对公账户方式必传，上传付款回执的图片路径
      wxOpenid:localStorage.getItem('openIdByWx'),          // 微信小程序-用户openid，微信小程序支付时必传
      wxType:env,             // 场景类型，H5支付时必传，示例值：iOS, Android, Wap
      wxUserId:id,            // 微信小程序-用户id，微信小程序支付时必传
      userId:id,
    }
    setLoading(true)
    const DataByOrder = await payOrder(params);
    setLoading(false)
    if (DataByOrder && DataByOrder.code == 200 && DataByOrder.content){
      // payType: 4
      // publicPay: true
      if (envByOp == 1) {  // 当前是小程序内提交对公转账
        wx && wx.miniProgram.navigateTo({url: `/pages/MemberPaymentPage/PaymentState/index?orderId=${orderId}`})
      }else {
        goStep3()
      }
    }else {
      message.warning(DataByOrder && DataByOrder.msg ? DataByOrder.msg : '提交对公转账表单失败!');
    }
  }

  // [支付][微信外环境的H5支付]跳转到支付的外连接
  const  payOrderByH5= async ()=> {
    let {match: {params: {orderId}}} = props
    const env = getEnvByPage()
    const userInfoData = JSON.parse(localStorage.getItem('userInfo'));
    const { friUserId:id } = userInfoData || {} // 当前登录人id
    let params = {
      appId:WxAppIdByMini,    // 微信外浏览器h5支付使用小程序appid
      orderId:orderId,
      payType:'2',            // 付款方式 1微信小程序 2微信H5 3微信二维码 4对公账户
      publicPayImgUrl:null,   // 对公账户方式必传，上传付款回执的图片路径
      wxOpenid:null,          // 微信小程序-用户openid，微信小程序支付时必传
      wxType:env,             // 场景类型，H5支付时必传，示例值：iOS, Android, Wap
      wxUserId:id,            // 微信小程序-用户id，微信小程序支付时必传
      userId:id,
    }
    const DataByOrder = await payOrder(params);
    if (DataByOrder && DataByOrder.code == 200 && DataByOrder.content){
      // 添加判定当前是app环境并且是 ios设备中
      // 如果是ios中的app环境，则修改到专用的支付结果页面
      const userAgent = window.navigator.userAgent;
      let openv = getOperatingEnv();
      if ((openv == 5 || openv == 6) && /iPhone|iPad|iPod/i.test(userAgent)) {
        let redirect_url = `${window.location.origin}/Payment/Step3ByApp/${orderId}?env=${openv}`
        DataByOrder.content.h5Url && (window.location.href = `${DataByOrder.content.h5Url}&${stringify({
          redirect_url:redirect_url,
        })}`)
      }else {
        DataByOrder.content.h5Url && (window.location.href = `${DataByOrder.content.h5Url}`)
      }
      getOrderIsPayByCallBack();
    }else {
      message.warning(DataByOrder && DataByOrder.msg ? DataByOrder.msg : '获取跳转微信支付链接失败!');
    }
  }

  // [支付]PC扫码支付后没有支付回调需要接口轮询判定支付状态
  // 当支付二维码生成之后每5秒获取状态一次

  // 修改设置轮询器在getOrderIsPayByCallBack调用之后
  // 0到1分钟每10秒钟调用g一次getOrderIsPay
  // 1到2分钟每20秒调用一次getOrderIsPay
  // 2到5分钟每30秒调用一次getOrderIsPay
  // 五分钟以后每60秒调用一次getOrderIsPay

  const getOrderIsPayByCallBack = async (interval) => {
    const startTime = Date.now()
    const { match: { params: { orderId } }, location } = props
    if(!!pollingId){ clearInterval(pollingId);pollingId = null }
    pollingId = null // 定义轮询定时器 ID

    // 定义轮询函数
    const polling = async () => {
      const dataByOrder = await getOrderIsPay({ orderId: orderId })

      if (dataByOrder && dataByOrder.code === 200) {
        const {
          payFlag, // : true已完成 false未完成
          payType  // : 0 无此订单  1微信小程序 2微信H5 3微信二维码 4对公账户 5其他浏览器
        } = dataByOrder?.content || {}

        if (payType == 3 && payFlag) {
          // 支付完成,跳转到支付完成页面
          goStep3();
          // 清除轮询定时器
          clearInterval(pollingId)
        }else if(payType == 2 && payFlag){
          // 支付完成,跳转到支付完成页面
          goStep3();
          // 清除轮询定时器
          clearInterval(pollingId)
        }
      }
    }
    // 开始轮询，每 5 秒钟调用一次 polling 函数
    pollingId = setInterval(polling, 5000)
  }

  const getOrderIsPayByCallBackAndPolling = async () => {
    const startTime = Date.now()
    setInterval(() => {
      let interval = 10000 // 默认轮询时间间隔为10秒
      const elapsed = (Date.now() - startTime) / 1000 // 计算已经过去的时间（秒）
      if (elapsed >= 60) {
        interval = 20000
      }
      if (elapsed >= 120) {
        interval = 30000
      }
      if (elapsed >= 300) {
        interval = 60000
      }
      getOrderIsPayByCallBack(interval)
    },1000);
  }

  // 支付完成携带订单id
  const goStep3 = () => {
    const { match: { params: { orderId } } } = props
    let env = getOperatingEnv() // 获取当前操作环境 1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用
    if (env == 1) {

    }else {
      // 支付完成,跳转到支付完成页面
      history.replace(`/Payment/Step3/${orderId}?random=${Math.random()}`)
    }
  }


  // 回显订单详情信息
  // 获取确认订单的数据详情
  const {
    bankInfo:bankInfoByContent,                            // : {bankName: "杭州银行江城支行", bankCode: "************", accountName: "杭州金曜日医疗科技有限公司",…}
    orderId:orderIdByContent,                              // : 37
    orderOrganizationName:orderOrganizationNameByContent,  // : "企业名单1111"
    orderOrganizationTel:orderOrganizationTelByContent,    // : "***********"
    planServiceName:planServiceNameByContent,              // : "个人版1季度"
    totalPrice:totalPriceByCotent,                         // : 0.01
    memberTypeCode,//: 2 // 1 个人版 2 企业版
  } = contentByConfirmOrder || {}


  const {
    accountName,      //: "杭州金曜日医疗科技有限公司"
    accountNo,        //: "3301040160021230811" // ���行账号
    bankCode,         //: "************"        // 银行编码
    bankName,         //: "杭州银行江城支行"
    publicKeyRandom,  //: "960641"
  } = bankInfoByContent || {}
  console.log('bankInfoByContentbankInfoByContent : ',contentByConfirmOrder);

  // 微信浏览器使用
  let OperatingEnv = getOperatingEnv();



  return (
    <div>
      {pageType == 1 &&  // 当前
        <div className={styles.page_warp}>
          <Spin spinning={loading}>
          <div className={styles.page_content}>
            {/* ------面包屑------ */}
            <BreadcrumbByPayment items={['FRIDAY解决方案', memberTypeCode == 2? '企业版' : '个人版', '支付方式']}/>
            {/* ------title导航条------ */}
            <Navigation activeStep={2} />  {/* activeStep: 1表示当前处于第1个导航项, 1:信息填写并提交,2:支付方式,3:支付结果 */}

            {/* -------购买信息------- */}
            <div style={{marginTop:'20px'}}>
            <InformationBox
              Information={{
                serviceValue:planServiceNameByContent,
                enterpriseValue:orderOrganizationNameByContent,
                phoneValue:orderOrganizationTelByContent,
                planValue:totalPriceByCotent,
                ...contentByConfirmOrder,
              }}
            />
            </div>

            {/* --------支付方式------- */}
            <div style={{marginTop:'20px'}} className={styles.MethodPaymentWrap}>
              <div className={styles.MethodPaymentItem}>
                <div className={styles.MethodPaymentLable}>支付方式：</div>
                <div className={styles.MethodPaymentValue}>
                  <div className={styles.MethodPaymentValue_option}>

                    <div onClick={()=>{
                      // _选择的支付方式 1 微信支付  2 对公转账 (默认微信支付)_
                      setSelectPayType('1')
                    }} className={
                      classNames({
                        [styles.MethodPaymentValueItem]:true,
                        [styles.MethodPaymentValueItem_selected]:selectPayType == '1',
                      })
                    }>
                      <div className={styles.MethodPaymentValueItemBox}>
                        <div className={styles.MethodPaymentValueItemIconWechatBay}/>
                        <div className={styles.MethodPaymentValueItemText}>微信支付</div>
                      </div>
                    </div>

                    {memberTypeCode == 2 &&
                      <div onClick={()=>{
                        setSelectPayType('2')
                      }}
                        className={
                          classNames({
                            [styles.MethodPaymentValueItem]:true,
                            [styles.MethodPaymentValueItem_selected]:selectPayType == '2',
                          })
                        }
                      >
                        <div className={styles.MethodPaymentValueItemBox}>
                          <div className={styles.MethodPaymentValueItemIconCorporateTransfer}/>
                          <div className={styles.MethodPaymentValueItemText}>对公转账</div>
                        </div>
                      </div>
                    }
                  </div>
                  {memberTypeCode == 2 &&
                    <div className={styles.PaymentMethodPaymentPromptBox}>
                      <i className={styles.PaymentMethodPaymentPromptIcon}/>
                      <div className={styles.PaymentMethodPaymentPromptText}>支付成功后1~3个工作日会有工作人员联系您签订线下协议</div>
                    </div>
                  }
                </div>
              </div>

              {/* -----微信支付码----- */}
              {selectPayType == '1' && (
                <div className={styles.WechatBay_Box}>
                  <div className={styles.WechatBay_Box_Wrap}>
                    <div className={styles.WechatBay_Box_icon}/>
                    <div className={styles.WechatBay_title_Box}>
                      <div className={styles.WechatBay_title_text}>微信支付</div>
                      <div className={styles.WechatBay_title_Desc}>使用微信扫码支付</div>
                    </div>
                  </div>

                  <div onClick={createWxPayORCode} className={styles.WechatBay_Box_QRCode}>
                    {/* 这里放二维码 */}
                    {!wxPayQRCodeByURL && (
                      <div className={styles.WechatBay_Box_QRCode_img}/>
                      )
                    }
                    {!!wxPayQRCodeByURL &&
                      <QRCode value={wxPayQRCodeByURL} className={styles.WechatBay_Box_QRCode_img}/>
                    }
                    <div>
                      <div className={styles.WechatBay_Box_QRCode_text}>打开手机端微信</div>
                      <div className={styles.WechatBay_Box_QRCode_text}>扫一扫继续付款</div>
                    </div>
                  </div>
                </div>)
              }

              {/* -------对公转账-------- */}
              {selectPayType == '2' &&
                <div className={styles.PaymentCorporateTransfer_wrap}>
                  <div className={styles.PaymentCorporateTransfer}>

                    <div className={styles.PaymentCorporateTransfer_item}>
                      <div className={styles.PaymentCorporateTransfer_item_lable}>企业全称：</div>
                      <div className={styles.PaymentCorporateTransfer_item_value}>{accountName}</div>
                    </div>
                    <div className={styles.PaymentCorporateTransfer_item}>
                      <div className={styles.PaymentCorporateTransfer_item_lable}>银行账户：</div>
                      <div className={styles.PaymentCorporateTransfer_item_value}>{accountNo}</div>
                    </div>
                    <div className={styles.PaymentCorporateTransfer_item}>
                      <div className={styles.PaymentCorporateTransfer_item_lable}>开户行：</div>
                      <div className={styles.PaymentCorporateTransfer_item_value}>{bankName}</div>
                    </div>
                    <div className={styles.PaymentCorporateTransfer_item}>
                      <div className={styles.PaymentCorporateTransfer_item_lable}>转账备注：</div>
                      <div className={styles.PaymentCorporateTransfer_item_value}>{publicKeyRandom}{"(打款时请备注此信息)"}</div>
                    </div>
                    <div className={styles.PaymentCorporateTransfer_item}>
                      <div className={styles.PaymentCorporateTransfer_item_lable}>转账金额：</div>
                      <div style={{color:'#ED3232'}} className={styles.PaymentCorporateTransfer_item_value}>{totalPriceByCotent}元</div>
                    </div>
                    <div className={classNames(styles.PaymentCorporateTransfer_item,styles.PaymentCorporateTransfer_item_upload)}>
                      <div className={styles.PaymentCorporateTransfer_item_lable}>付款回执：</div>
                      <div className={styles.PaymentCorporateTransfer_item_value}>
                        <div className={styles.PaymentCorporateTransfer_item_value_upload}>
                          <Upload
                            headers={getHeaders()}
                            accept="image/*"
                            action={`/api/server/base/uploadFile?${stringify({ fileType: 10,userId: UerInfo.friUserId})}`}
                            listType="picture-card"
                            className="avatar-uploader"
                            onChange={onChangeByUpload}
                            onRemove={()=>{}}
                            beforeUpload={beforeUpload}
                            // fileList={fileListByState ? [fileListByState] : []}
                            showUploadList={false}
                          >
                            {fileListByState?.fileUrlView ? <div className={styles.imgWarp}><img src={fileListByState.fileUrlView} alt="avatar" style={{ width: '100%' }} /></div> : uploadButton}
                          </Upload>
                        </div>  {/* 上传付款回执 */}
                      </div>
                    </div>

                    <div className={styles.PaymentCorporateTransfer_submit_wrap}>
                      <Button onClick={payOrderBySubmit}>确认并提交</Button>
                    </div>

                  </div>
                </div>
              }
            </div>
          </div>
          </Spin>
        </div>
      }

      {/* ------------移动端布局------------ */}
      <div>

        {pageType == 2 &&  // 当前
          <Spin spinning={loading}>
          <div className={classNames(styles.Mobile_Wrap, {
            [styles.in_app_Mobile_Wrap]: OperatingEnv == 5,
          })}>

            {OperatingEnv != 1 &&  // 1 小程序端
              <div className={styles.Mobile_title_statusbar}></div>
            }

            {OperatingEnv != 1 && OperatingEnv != 2 && OperatingEnv != 5 &&  // 1 小程序端、2 微信浏览器端、5 app中 无需展示title
              <div className={styles.Mobile_title_Wrap}>
                  <div className={styles.Mobile_title}>确认订单</div>
              </div>
            }

            {OperatingEnv == 1 &&
              <div style={{height:'22px'}}></div>
            }

            {/* app环境中 */}
            {
              OperatingEnv == 5 &&
              <NavBar title="确认订单" style={{background: 'linear-gradient(180deg, #EDE7FF 0%, #eee9ff 100%)'}}/>
            }

            <div className={styles.Mobile_Content_box}>
              <div className={styles.Mobile_box_Confirm_Order}>
                <div className={styles.Mobile_box_Confirm_Order_item}>
                  <div className={styles.Mobile_box_Confirm_Order_title}>订购服务</div>
                  <div className={styles.Mobile_box_Confirm_Order_value}>{planServiceNameByContent}</div>
                </div>
                <div className={styles.Mobile_box_Confirm_Order_item}>
                  <div className={styles.Mobile_box_Confirm_Order_title}>企业名称</div>
                  <div className={styles.Mobile_box_Confirm_Order_value}>{orderOrganizationNameByContent}</div>
                </div>
                <div className={styles.Mobile_box_Confirm_Order_item}>
                  <div className={styles.Mobile_box_Confirm_Order_title}>联系电话</div>
                  <div className={styles.Mobile_box_Confirm_Order_value}>{orderOrganizationTelByContent}</div>
                </div>
                <div className={styles.Mobile_box_Confirm_Line}></div>
                <div className={styles.Mobile_box_Confirm_Order_item_amount}>
                  <div className={styles.Mobile_box_Confirm_Order_title}>订单金额</div>
                  <div className={styles.Mobile_box_Confirm_Order_value}>
                    <div className={styles.Mobile_box_Confirm_Order_amount}>
                      <div className={styles.Mobile_box_Confirm_Order_amount_unit}>¥</div>
                      <div className={styles.Mobile_box_Confirm_Order_amount_num}>{totalPriceByCotent}</div>
                    </div>
                  </div>
                </div>
              </div>

              <div className={styles.Mobile_box_Confirm_Order_item_option}>
                <div className={styles.Mobile_box_Confirm_Order_item_title}>支付方式</div>
                <div className={styles.Mobile_box_Confirm_Order_item_option_box}>
                  <div
                    className={styles.Mobile_box_Confirm_Order_item_option_item}
                    onClick={() => {
                      setSelectPayType(1)
                    }}
                  >
                    <div className={styles.Mobile_box_Confirm_Order_item_option_item_text}>
                      <div
                        className={styles.Mobile_box_Confirm_Order_item_option_item_icon_wxIcon}
                      ></div>
                      <div className={styles.item_text}>微信支付</div>
                    </div>
                    <div
                      className={
                        classNames(
                          {
                            [styles.Mobile_box_Confirm_Order_item_option_item_icon_check]:true,
                            [styles.Mobile_box_Confirm_Order_item_option_item_icon_check_active]:selectPayType == 1,
                          })}
                    ></div>
                  </div>
                  {memberTypeCode == 2 &&
                    <div
                      className={styles.Mobile_box_Confirm_Order_item_option_item}
                      onClick={() => {
                        setSelectPayType(2)
                      }}
                    >
                      <div className={styles.Mobile_box_Confirm_Order_item_option_item_text}>
                        <div className={styles.Mobile_box_Confirm_Order_item_option_item_icon_CorporateTransfer}></div>
                        <div className={styles.item_text}>对公转账</div>
                      </div>
                      <div
                        className={
                          classNames(
                            {
                              [styles.Mobile_box_Confirm_Order_item_option_item_icon_check]:true,
                              [styles.Mobile_box_Confirm_Order_item_option_item_icon_check_active]:selectPayType == 2,
                            })}
                      ></div>
                    </div>
                  }
                </div>
              </div>

              {selectPayType == 2 &&
                <div className={styles.Mobile_box_Confirm_Order_enterprise_form}>
                  <div className={styles.Mobile_box_Confirm_Order_enterprise_form_item}>
                    <div className={styles.Mobile_box_Confirm_Order_enterprise_form_item_lable}>企业全称</div>
                    <div className={styles.Mobile_box_Confirm_Order_enterprise_form_item_value}>{accountName}</div>
                  </div>
                  <div className={styles.Mobile_box_Confirm_Order_enterprise_form_item}>
                    <div className={styles.Mobile_box_Confirm_Order_enterprise_form_item_lable}>银行账户</div>
                    <div className={styles.Mobile_box_Confirm_Order_enterprise_form_item_value}>{accountNo}</div>
                  </div>
                  <div className={styles.Mobile_box_Confirm_Order_enterprise_form_item}>
                    <div className={styles.Mobile_box_Confirm_Order_enterprise_form_item_lable}>开户行</div>
                    <div className={styles.Mobile_box_Confirm_Order_enterprise_form_item_value}>{bankName}</div>
                  </div>
                  <div className={styles.Mobile_box_Confirm_Order_enterprise_form_item}>
                    <div className={styles.Mobile_box_Confirm_Order_enterprise_form_item_lable}>转账备注</div>
                    <div className={styles.Mobile_box_Confirm_Order_enterprise_form_item_value}>{publicKeyRandom}{"(打款时请备注此信息)"}</div>
                  </div>
                  <div className={styles.Mobile_box_Confirm_Order_enterprise_form_item}>
                    <div className={styles.Mobile_box_Confirm_Order_enterprise_form_item_lable}>转账金额</div>
                    <div className={styles.Mobile_box_Confirm_Order_enterprise_form_item_value}>{totalPriceByCotent}元</div>
                  </div>
                  <div className={styles.Mobile_box_Confirm_Order_enterprise_form_item}>
                    <div className={styles.Mobile_box_Confirm_Order_enterprise_form_item_lable}>付款回执</div>
                    <div className={styles.Mobile_box_Confirm_Order_enterprise_form_item_value}>
                      <Upload
                        headers={getHeaders()}
                        accept="image/*"
                        action={`/api/server/base/uploadFile?${stringify({ fileType: 10,userId: UerInfo.friUserId})}`}
                        listType="picture-card"
                        className="avatar-uploader"
                        onChange={onChangeByUpload}
                        onRemove={()=>{}}
                        beforeUpload={()=>{}}
                        // fileList={fileListByState ? [fileListByState] : []}
                        showUploadList={false}
                      >
                        {fileListByState?.fileUrlView ? <div className={styles.imgWarp}>
                          <img src={fileListByState.fileUrlView} alt="avatar" style={{ width: '100%' }} />
                        </div> : uploadButton}
                      </Upload>

                    </div>
                  </div>
                </div>
              }

              <div className={styles.Mobile_submit_warp}>
                {memberTypeCode == 2 &&
                  <div className={styles.Mobile_submit_agree}>
                    支付成功后1~3个工作日会有工作人员联系您签订线下协议
                  </div>
                }
                <div className={styles.Mobile_submit_btn_wrap}>
                  <div onClick={submit} className={styles.Mobile_submit_btn}>确认并支付</div>
                </div>
              </div>
            </div>


            {/*<div>
              <Button className={styles.Mobile_submit_btn} onClick={payOrderBySubmit}>对公转账</Button>
            </div>

            <div>
              <Button className={styles.Mobile_submit_btn} onClick={payOrderByPublicAccount}>[微信浏览器内]公众号支付</Button>
            </div>

            <div>
              <Button className={styles.Mobile_submit_btn} onClick={payOrderByH5}>[微信浏览器外]网页版支付</Button>
            </div>

            <div>
              <Button className={styles.Mobile_submit_btn} onClick={payOrderByMini}>[小程序内]跳转页面</Button>
            </div>

            <div>
              <Button className={styles.Mobile_submit_btn} onClick={payOrderToQRcode}>扫码支付</Button>
            </div>*/}

          </div>
          </Spin>
        }
      </div>

    </div>
  );
};

export default connect(({ login, loading }: any) => ({
  login, loading
}))(Payment)
