.container {
  height: calc(70vh - 28px);
  position: relative;
  .title {
    font-size: 17px;
    color: #000;
    font-weight: 500;
    line-height: 24px;
    margin-bottom: 20px;
    text-align: center;
  }

  .contentList {
    width: 100%;

    .tips_box {
      width: 100%;
      display: flex;
      flex-wrap: nowrap;
      min-height: 34px;
      padding: 0 22px;
      margin-bottom: 16px;
      font-size: 12px;
      color: #999;
      line-height: 17px;
      word-break: break-all;
      text-align: center;
      justify-content: center;
    }

    .tab_wrap {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
       padding: 0 14px;
      box-sizing: border-box;

      .tab_list_box {
        flex: 0 0 33.33%;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 24px;

        .tab_item {
          width: 68px;
          min-width: 68px;
          height: auto;

          img {
            width: 68px;
            height: 68px;
          }
        }

        .tab_name {
          margin-top: 16px;
          font-size: 15px;
          color: #000;
          font-weight: 500;
          text-align: center;
          line-height: 13px;
          white-space: nowrap;
        }
      }
    }
  }

  .tabs_box {
    padding: 0 24px;
    display: flex;
    justify-content: space-between;

    .tabs_item {
      width: 160px;
      border-radius: 4px;
      height: 38px;
      line-height: 39px;
      text-align: center;
      background: #F5F5F5;
      font-size: 16px;
      color: #999;
      margin: 0 auto;
      &.checked {
        color: #0095FF;
        background: #EDF9FF;
      }
    }
    .tabs_item + .tabs_item {
      margin-left: 8px;
    }
  }

  .data_box {
    height: calc(70vh - 317px);
    overflow-y: auto;
    padding: 16px 24px 0;
    .data_item {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      margin-bottom: 16px;
      .avatar {
        width: 32px;
        height: 32px;
        border-radius: 2px;
        margin-right: 8px;

        .no_comment_head{
          width: 32px;
          height: 32px;
          border-radius: 2px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          font-weight: 500;
          color: #fff;
          white-space: nowrap;
        }

        img {
          width: 32px;
          height: 32px;
          border-radius: 2px;
        }
      }
      .data_name {
        flex: 1;
        font-size: 14px;
        color: #000;
        line-height: 20px;
        word-break: break-all;
      }
      .data_btn {
        height: 29px;
        line-height: 29px;
        text-align: center;
        padding: 0 8px;
        min-width: 48px;
        border-radius: 18px;
        font-size: 12px;
        color: #0095FF;
        background: #E6F4FF;
        white-space: nowrap;
        flex-shrink: 0;
        &.checked {
          color: #999;
          background: #F5F5F5;
        }
      }
    }
  }

  .fixed_box {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding-bottom: constant(safe-area-inset-bottom);//兼容 IOS<11.2
    padding-bottom: env(safe-area-inset-bottom);//兼容 IOS>11.2
    .btn_box {
      padding: 0 16px 8px;
      .btn {
        height: 40px;
        line-height: 40px;
        background: #0095FF;
        border-radius: 20px;
        text-align: center;
        font-size: 16px;
        color: #fff;
      }
    }

    .disable_btn_box {
      padding: 0 16px 8px;
      .btn {
        height: 40px;
        line-height: 40px;
        background: #F5F5F5;
        border-radius: 20px;
        text-align: center;
        font-size: 16px;
        color: #999999;
      }
    }
  }
}
