/**
 * @Description: 小程序码页面
 */
import React, { useState, useEffect } from 'react'
import { history,connect } from 'umi'
import classNames from 'classnames'
import { getOperatingEnv } from '@/utils/utils'
import { InfoCircleOutlined } from '@ant-design/icons'
import { Spin } from 'antd'
import styles from './index.less'

import NavBar from '@/components/NavBar'         // 导航条

const Index: React.FC = (props: any) => {
  const { dispatch, loading } = props
  const { query } = history.location

  const [miniProgramImage, setMiniProgramImage] = useState(null)

  useEffect(() => {
    qrCodeGenerator()
  }, [])

  // 生成小程序码
  const qrCodeGenerator = () => {
    const params = {
      resourceType: query.resourceType,                    // 链接资源类型，1 病例，2 专家
      resourceId: query.resourceId,                        // 资源ID
    }
    dispatch({
      type: 'activity/qrCodeGenerator',
      payload: {
        type: query.resourceType,                          // 1 病例，2 专家
        dataJson: JSON.stringify(params),                  // 需要保存的数据，（在小程序端再调接口查出来）
      }
    }).then(res => {
      if (res && res.status == '401') {
        return
      }
      const reader = new FileReader()
      reader.readAsDataURL(res)
      reader.onloadend = function () {
        // 将读取的图片数据流转换为 base64 格式
        const base64data = reader.result
        setMiniProgramImage(base64data)
      }
    })
  }

  // loading
  const loadingQrCodeGenerator = !!loading.effects['activity/qrCodeGenerator']

  return (
    <div className={styles.container}>
      {/* app环境中 */}
      {
        (getOperatingEnv() == 5 || getOperatingEnv() == 6) &&
        <NavBar style={{background: '#FAFAFA'}}/>
      }
      <div className={classNames(styles.content, {
        [styles.in_app_content]: getOperatingEnv() == 5 ,
      })}>
        <Spin spinning={loadingQrCodeGenerator}>
          <div className={styles.title_box}>
            <InfoCircleOutlined />
            <div>若您需要查看该内容,请前往小程序查看</div>
          </div>
          <div className={styles.mini_program_image_box}>
            <img className={styles.img} src={miniProgramImage} alt=""/>
          </div>
        </Spin>
      </div>
    </div>
  )
}

export default connect(({ activity, loading }: any) => ({activity, loading}))(Index)
