/**
 * @Description: PC端正畸病例卡片信息
 * @author: 赵斐
 */
import React, { useState, useEffect, useMemo } from 'react'
import { history } from 'umi'
import { stringify } from 'qs'
import { getArrailUrl } from '@/utils/utils'
import styles from './index.less'


interface PropsType {
  isShowBtn?: boolean,                 // 是否展示编辑按钮，true 展示，false 不展示
  isDoctor?: number,                   // 是否为医生，1 是医生（隐藏头部），0 不是医生
  sourceType?: number,                 // 来源，1 创建指导第一步，2 指导详情，3 我的指导列表，不同来源点击编辑时逻辑不一样
  caseData: any,                       // 病例数据dto
  tenantId: number,                     // 租户ID
  consultationType: string,            // 指导类型
  // onStepChange?: any,                      // sourceType为1或者3时，PC端，点击编辑时回调
  // expertsUserId?: any,                      // 专家的id
}
const Index: React.FC<PropsType> = (props: any) => {
  // 是否嵌套在5i5ya的iframe中
  const isInIframe = (self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya')

  const {
    isShowBtn = true,
    isDoctor = 0,
    caseData = {},
    tenantId,
  } = props

  const {
    caseName,   // 病例名称
    consultationId,
    customerId,
    orthodonticCaseDictDtoList,  // 病例详情字典
  } = caseData || {}

  // iframe 嵌入到我爱我牙   不显示编辑
  const isShowEditBtn = useMemo(() => {
    return isShowBtn && !tenantId;
  }, [isShowBtn, tenantId]);

  // 查看病例详情
  const lookCaseDetails = () => {
    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'pathname',       // 页面地址onchange事件
        pathnameByChild: `/ConsultationModule/OrthodonticCasesDetail/${consultationId}`,  // 路由信息
        searchByChild: `?${stringify({
          customerId: customerId,                // 指导ID
          tenantId: tenantId,                // 指导ID
        })}`,  // 路由信息
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
      return
    }

    console.log(consultationId, "==", customerId, "==", tenantId)
    history.push(`/ConsultationModule/OrthodonticCasesDetail/${consultationId}`,{
      customerId:customerId,
      tenantId:tenantId,
    })
  }

  // 编辑
  const caseEdit = (e) => {
    e.stopPropagation()
    e.preventDefault()

    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'pathname',       // 页面地址onchange事件
        pathnameByChild: '/CreationOrthodontics/Step1',  // 路由信息
        searchByChild: `?${stringify({
          consultationId,                // 指导ID
          tenantId,
          customerId,
          type: 0,
        })}`,  // 路由信息
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
      return
    }

    history.push({
      pathname: '/CreationOrthodontics/Step1',
      query: {
        consultationId,                // 指导ID
        tenantId,
        customerId,
        type: 0,

      }
    })
  }

  // 影像展示
  const imageDisplayFun = () => {
    if (Array.isArray(orthodonticCaseDictDtoList) && orthodonticCaseDictDtoList.length) {
      const result = orthodonticCaseDictDtoList.find(item => item.id === 7);
      const { subsetList } = result || {}
      if (Array.isArray(subsetList) && subsetList.length) {
        let lenghtNum = subsetList.filter(v => v.fileUrlShow != null).length
        return (
          <div className={styles.case_img}>
            {
              subsetList.map((item, index) => {
                if (!item.fileUrlShow) {
                  return
                }
                return <div key={index} className={styles.img} style={{ backgroundImage: `url(${item.fileUrlShow})` }}>
                  {
                    lenghtNum > 6 ? <span className={styles.img_length}>+{lenghtNum - 5}张</span> : null
                  }
                </div>
              })
            }
          </div>
        )
      }
    }
  }
  // 方案展示
  const programmeFun = () => {
    if (Array.isArray(orthodonticCaseDictDtoList) && orthodonticCaseDictDtoList.length) {
      const result = orthodonticCaseDictDtoList.find(item => item.id === 6);
      const { subsetList } = result || {}
      if (Array.isArray(subsetList) && subsetList.length) {
        return (
          <p>
            治疗方案：<br />
            {
              subsetList.map((item: any, index: number) => {
                return <span key={index}>{item.inputContent}</span>
              })
            }
          </p>
        )
      }
    }

  }

  // 诊断展示
  const diagnosisFun = () => {
    if (Array.isArray(orthodonticCaseDictDtoList) && orthodonticCaseDictDtoList.length) {
      const result = orthodonticCaseDictDtoList.find(item => item.id === 4);
      const { subsetList } = result || {}
      if (Array.isArray(subsetList) && subsetList.length) {
        return (
          <>
            {
              subsetList.map((item: any, index: number) => {
                if (item.id != 223) {
                  return (
                    <>
                      <span key={index}>{index ? '、' : null}{item.dictName}</span>
                      {
                        item.subsetList.map((val: any, idx: number) => {
                          if (val.id != 215 && val.operateType == 1 && val.isCheck == 1) {
                            return (<span key={idx}>{val.dictName}</span>)
                          }

                        })
                      }
                    </>
                  )
                }
              })
            }
          </>
        )
      }
    }
  }

  return (
    <div className={styles.wrap}>
      <div className={styles.header}>
        <div className={styles.header_title}>正畸方案</div>
        {
          isShowEditBtn && isDoctor != 1 ? <div className={styles.header_btn} onClick={caseEdit}>编辑</div> : null
        }

      </div>
      <div className={styles.content} onClick={lookCaseDetails}>
        <div className={styles.case_title}>{caseName}</div>
        <div className={styles.case_course}>
          诊断：
          {
            diagnosisFun()
          }
        </div>
        {
          imageDisplayFun()
        }
        <div className={styles.case_content}>
          {programmeFun()}
        </div>
      </div>
    </div>
  )
}
export default Index
