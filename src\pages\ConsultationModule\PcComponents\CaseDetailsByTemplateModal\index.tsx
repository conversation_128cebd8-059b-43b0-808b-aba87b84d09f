/**
 * @Description: PC端，病例详情弹窗，模板
 */
import React from 'react'
import { screenData } from '@/utils/utils'
import { Modal ,Image } from 'antd'
import styles from './index.less'
interface PropsType {
  visible: boolean,                    // true，false
  caseData: any,                       // 病例数据
  onCancel: any,                       // 关闭弹窗
}

const Index: React.FC<PropsType> = (props: any) => {
  const { visible, caseData = {} } = props

  return (
    <>
      <Modal
        title="病例详情"
        width={791}
        visible={visible}
        onCancel={props.onCancel}
        className={styles.modal}
        destroyOnClose
        footer={null}
      >

        <div className={styles.container}>
          <div className={styles.case_title}>{caseData.caseName}</div>
          {/*
            caseData.depSubjectDictsStrList && caseData.depSubjectDictsStrList.length > 0 &&
            <div className={styles.case_tag}>
              {
                caseData.depSubjectDictsStrList.map((item, index) => <div key={index} className={styles.tag}>{item}</div>)
              }
            </div>
          */}

          <div className={styles.details_header}>基本信息<i></i></div>
          <div className={styles.details_wrap}>
            <div className={styles.details_item_horizontal}>
              <div className={styles.item_label}>年龄</div>
              <div className={styles.item_value}>{screenData(caseData.age)}</div>
            </div>
            <div className={styles.details_item_horizontal}>
              <div className={styles.item_label}>性别</div>
              <div className={styles.item_value}>{caseData.sex}</div>
            </div>
          </div>

          <div className={styles.details_item}>
            <div className={styles.item_label}>主诉</div>
            <div className={styles.item_value}>{caseData.chiefComplaint || '--'}</div>
          </div>
          <div className={styles.details_item}>
            <div className={styles.item_label}>现病史</div>
            <div className={styles.item_value}>{caseData.presentDisease || '--'}</div>
          </div>
          <div className={styles.details_item}>
            <div className={styles.item_label}>既往史</div>
            <div className={styles.item_value}>{caseData.previousHistory || '--'}</div>
          </div>
          <div className={styles.details_item}>
            <div className={styles.item_label}>全身健康情况</div>
            <div className={styles.item_value}>{caseData.wholeHealth || '--'}</div>
          </div>

          <div className={styles.details_header}>检查及诊断<i></i></div>
          <div className={styles.details_item}>
            <div className={styles.item_label}>检查</div>
            <div className={styles.item_value}>{caseData.checkUp || '--'}</div>
          </div>
          <div className={styles.details_item}>
            <div className={styles.item_label}>诊断</div>
            <div className={styles.item_value}>{caseData.diagnosis || '--'}</div>
          </div>

          <div className={styles.details_header}>治疗方案<i></i></div>
          <div className={styles.details_item}>
            <div className={styles.item_value}>{caseData.treatmentPlanList && caseData.treatmentPlanList.length > 0 ? caseData.treatmentPlanList[0] : '--'}</div>
          </div>

          <div className={styles.details_header}>影像资料<i></i></div>
          <div className={styles.details_item_img}>
            <Image.PreviewGroup>
            {
              caseData.consultationCaseMediaDtoList && caseData.consultationCaseMediaDtoList.length > 0 &&
              caseData.consultationCaseMediaDtoList.map((item, index) => {
                if (item.type == 0) {
                  return <div key={index} className={styles.img}>
                      <Image width={198} height={198} src={item.fileUrlShow}/>
                  </div>
                }
                return null
              })
            }
            </Image.PreviewGroup>
          </div>
        </div>

      </Modal>
    </>
  )
}

export default Index
