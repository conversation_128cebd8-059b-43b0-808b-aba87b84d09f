

const dentalAssistantBanner={
  pageTitle:'FRIDAY 牙医助手',
  title:'3大数智化赋能价值',
  subhead:'提技术、创增长',
  descPC:'依托瑞尔集团(HK6639)二十五年的成功经验，FRIDAY牙医助手通过为会员医生提供学习提升、技术巩固，收益保障三大数智化权益，帮助牙医会员切实提升医疗技术，为门诊增长“造血”，推动诊所团队可持续发展。',
  descH5:'依托瑞尔集团(HK6639)二十五年的成功经验，FRIDAY\n牙医助手通过为会员医生提供学习提升、技术巩固，收\n益保障三大数智化权益，帮助牙医会员切实提升医疗技\n术，为门诊增长“造血”，推动诊所团队可持续发展。',
  PCimg:'https://static.jwsmed.com/public/DigitalHealth/Business/assets/DentalAssistant/dentalAssistantBannerPC.png',
  H5img:'https://static.jwsmed.com/public/DigitalHealth/Business/assets/DentalAssistant/dentalAssistantBannerH5.png'
}

const dentalAssistantInterestList=[
  {
    title:'价值一   学习提升',
    subhead:'加入瑞尔学堂全国学习体系\n与八大专科委员会百位专家建立联系\n把控医疗风险、提升服务价值',
    minText:'',
    PCimg:'https://static.jwsmed.com/public/DigitalHealth/Business/assets/DentalAssistant/AssistantInterest1PC.png',
    H5img:'https://static.jwsmed.com/public/DigitalHealth/Business/assets/DentalAssistant/AssistantInterest1H5.png',
    desc:'· 理论、实操并重      · 方案把关         · 会诊支持',
    markTextArr:[],
    linkUrl:'/MedicalEscort'
  },
  {
    title:'价值二   技术巩固',
    subhead:'加入瑞尔集团医生星球\n获得专属口腔机构管理的医疗质量系统支持\n提升诊所管理效率',
    minText:'',
    PCimg:'https://static.jwsmed.com/public/DigitalHealth/Business/assets/DentalAssistant/AssistantInterest3PC.png',
    H5img:'https://static.jwsmed.com/public/DigitalHealth/Business/assets/DentalAssistant/AssistantInterest3H5.png',
    desc:'· 集团病例研讨小组    · 优秀病例    · 专业提升精品课    · 基础规范培训    · 门诊经营管理      · 学术峰会 · 成长激励',
    markTextArr:[
      {
        name:'5A门诊医疗管理系统',
        linkUrl:''
      },
      {
        name:'FRIDAY智慧仓库',
        linkUrl:''
      }
    ],
    linkUrl:'/AllianceCollection'
  },
  {
    title:'价值三   收益保障',
    subhead:'加入集团联盟集采体系\n同享大客户VIP服务\n无需打包采购亦能享受优价',
    minText:'用得多，省得多',
    PCimg:'https://static.jwsmed.com/public/DigitalHealth/Business/assets/DentalAssistant/AssistantInterest2PC.png',
    H5img:'https://static.jwsmed.com/public/DigitalHealth/Business/assets/DentalAssistant/AssistantInterest2H5.png',
    desc:'· 种植体      · 隐形正畸     ·义齿     ·设备',
    markTextArr:[],
    linkUrl:'/AbilityPromotion'
  }
]


const mockData = [
  {
    title:'旗舰版',
    subhead:'牙医助手',
    content:[
      {
        title:'品牌背书',
        content:[
          {
            type:1,
            title:'FRIDAY数智化口腔联盟成员单位权威授牌',
            subhead:'',
            priceORtime:'1',
            unit:'/家门店',
          },
          {
            type:1,
            title:'高质量发展新闻通稿（FRIDAY独家定制版）',
            subhead:'',
            priceORtime:'1',
            unit:'/篇',
          },
          {
            type:1,
            title:'高质量发展视频专访（FRIDAY独家定制版）',
            subhead:'',
            priceORtime:'',
            unit:'',
            disabled: 1,
          }
          
        ]
      },
        {
          title:'学习提升',
          content:[
            {
              type:2,
              title:'加入上市集团内训体系',
              subhead:'',
              priceORtime:'18000',
              unit:'元/年学习金',
            },
            {
              type:2,
              title:'集团标杆诊所研学',
              subhead:'',
              priceORtime:'',
              unit:'',
              disabled: 1,
            },
            {
              type:2,
              title:'1V1专家咨询',
              subhead:'（价值1500元/小时，每次不超过1小时）',
              priceORtime:'3',
              unit:'次/年',
            },
            {
              type:2,
              title:'定制专属学习计划',
              subhead:'',
              priceORtime:'',
              unit:'',
              disabled: 1,
            },
            {
              type:2,
              title:'集团内刊',
              subhead:'',
              priceORtime:'',
              unit:'',
            }
          ]
        },
        {
          title:'带教巩固',
          content:[
            {
              type:3,
              title:'职能带教（运营/客服/护理类）',
              subhead:'',
              priceORtime:'',
              unit:'',
              unitDiffText: '不超过<span className={styles.fsC}>6</span>/人',
              unitDiff: 1,
            },
            {
              type:3,
              title:'医疗带教（种植/正畸/修复/早矫）',
              subhead:'',
              priceORtime:'学科四选二',
              unit:'',
              unitSmall: 1,
            },
            {
              type:3,
              title:'集团复杂病例研讨',
              subhead:'',
              priceORtime:'',
              unit:'',
              scratchOutLine:false,
            },
            {
              type:3,
              title:'学习报告',
              subhead:'',
              priceORtime:'',
              unit:'',
              scratchOutLine:false,
            },
            {
              type:3,
              title:'奖学金评比',
              subhead:'',
              priceORtime:'',
              unit:'',
              scratchOutLine:false,
            }
          ]
        },
        {
          title:'共享集团学习资源',
          content:[
            {
              type:4,
              title:'瑞尔学堂会员专区课程',
              subhead:'',
              priceORtime:'',
              unit:'',
              unitDiffText: '不超过<span className={styles.fsC}>10</span>/个账号',
              unitDiff: 1,
            },
            {
              type:4,
              title:'集团大客户学习提升资源',
              subhead:'',
              priceORtime:'',
              unit:'',
            },
            {
              type:4,
              title:'行业峰会',
              subhead:'',
              priceORtime:'',
              unit:'',
            }
          ]
        },
        {
          title:'降本增效',
          content:[
            {
              type:5,
              title:'加入FRIDAY DDSO集采',
              subhead:'',
              priceORtime:'1',
              unit:'/家门店',
            },
            {
              type:5,
              title:'FRIDAY 智慧存ERP系统',
              subhead:'',
              priceORtime:'1',
              unit:'/家门店',
            },
            {
              type:5,
              title:'FRIDAY 5A服务系统',
              subhead:'',
              priceORtime:'1',
              unit:'/家门店',
            }
          ]
        }
      ],
    btnText:'咨询客服购买'
  },
  {
    title:'尊享版',
    subhead:'牙医助手',
    content:[
      {
        title:'品牌背书',
        content:[
          {
            type:1,
            title:'FRIDAY数智化口腔联盟成员单位权威授牌',
            subhead:'',
            priceORtime:'3',
            unit:'/家门店',
          },
          {
            type:1,
            title:'高质量发展新闻通稿（FRIDAY独家定制版）',
            subhead:'',
            priceORtime:'1',
            unit:'/篇',
          },
          {
            type:1,
            title:'高质量发展视频专访（FRIDAY独家定制版）',
            subhead:'',
            priceORtime:'1',
            unit:'/次',
          }
          
        ]
      },
        {
          title:'学习提升',
          content:[
            {
              type:2,
              title:'加入上市集团内训体系',
              subhead:'',
              priceORtime:'54000',
              unit:'元/年学习金',
            },
            {
              type:2,
              title:'集团标杆诊所研学',
              subhead:'',
              priceORtime:'1',
              unit:'次/上限5人',
            },
            {
              type:2,
              title:'1V1专家咨询',
              subhead:'（价值1500元/小时，每次不超过1小时）',
              priceORtime:'6',
              unit:'次/年',
            },
            {
              type:2,
              title:'定制专属学习计划',
              subhead:'',
              priceORtime:'',
              unit:'',
            },
            {
              type:2,
              title:'集团内刊',
              subhead:'',
              priceORtime:'',
              unit:'',
            }
          ]
        },
        {
          title:'带教巩固',
          content:[
            {
              type:3,
              title:'职能带教（运营/客服/护理类）',
              subhead:'',
              priceORtime:'',
              unit:'',
              unitDiffText: '不超过<span className={styles.fsC}>20</span>/个人',
              unitDiff: 1,
            },
            {
              type:3,
              title:'医疗带教（种植/正畸/修复/早矫）',
              subhead:'',
              priceORtime:'全学科开放',
              unit:'',
              unitSmall: 1,
            },
            {
              type:3,
              title:'集团复杂病例研讨',
              subhead:'',
              priceORtime:'',
              unit:'',
              scratchOutLine:false,
            },
            {
              type:3,
              title:'学习报告',
              subhead:'',
              priceORtime:'',
              unit:'',
              scratchOutLine:false,
            },
            {
              type:3,
              title:'奖学金评比',
              subhead:'',
              priceORtime:'',
              unit:'',
              scratchOutLine:false,
            }
          ]
        },
        {
          title:'共享集团学习资源',
          content:[
            {
              type:4,
              title:'瑞尔学堂会员专区课程',
              subhead:'',
              priceORtime:'',
              unit:'',
              unitDiffText: '不超过<span className={styles.fsC}>30</span>/个账号',
              unitDiff: 1,
              
            },
            {
              type:4,
              title:'集团大客户学习提升资源',
              subhead:'',
              priceORtime:'',
              unit:'',
            },
            {
              type:4,
              title:'行业峰会',
              subhead:'',
              priceORtime:'',
              unit:'',
            }
          ]
        },
        {
          title:'降本增效',
          content:[
            {
              type:5,
              title:'加入FRIDAY DDSO集采',
              subhead:'',
              priceORtime:'3',
              unit:'/家门店',
            },
            {
              type:5,
              title:'FRIDAY 智慧存ERP系统',
              subhead:'',
              priceORtime:'3',
              unit:'/家门店',
            },
            {
              type:5,
              title:'FRIDAY 5A服务系统',
              subhead:'',
              priceORtime:'3',
              unit:'/家门店',
            }
          ]
        }
      ],
    btnText:'咨询客服购买'
  }
]
export { dentalAssistantBanner, dentalAssistantInterestList, mockData }
