.Mobile_Wrap {
  width: 100%;
  min-width: 285px;
  height: 100vh;
  // background: linear-gradient(180deg, rgba(255,255,255,0) 0%, #E3EBFE 25%, rgba(255,255,255,0) 100%);
  background: #F5F6F8;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  // padding-left: 12px;
  // padding-right: 12px;

  .Mobile_title_Wrap {
    width: 100%;
    height: 44px;
    margin-bottom: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: transparent;

    .Mobile_title {
      font-size: 17px;
      font-weight: 500;
      color: #000000;
    }
  }

  .Mobile_box_info {
    width: 100%;
    min-height: 147px;
    background: #FFFFFF;
    border-radius: 0px 0px 0px 0px;
    opacity: 1;

    .Mobile_box_title {

    }

  }

  .Mobile_user_info {
    width: 100%;
    min-height: 80px;
    margin-top: 10px;
    background: #FFFFFF;
  }

  .Mobile_info_content {
    width: 100%;
    min-height: 207px;
    margin-top: 10px;
    background: #FFFFFF;
    opacity: 1;
    padding-left: 16px;
    padding-right: 16px;
    padding-top: 19px;
    padding-bottom: 19px;
  }

  .blueText {
    color: #0095FF;
    margin-right: 4px;
    cursor: pointer;
    user-select: none;
  }

  .Mobile_info_content_item {
    display: flex;
    justify-content: space-between;
    // margin-bottom: 12px;

    .Mobile_info_content_item_left {
      color: #666666;
      text-align: left;
      font-size: 13px;
    }

    .Mobile_info_content_item_right {
      color: #000000;
      text-align: right;
      font-size: 13px;
    }
  }

  .Mobile_box_bottom_warp {
    width: 100%;
    height: calc(58px + 34px);
    background: #FFFFFF;
    border-radius: 0px 0px 0px 0px;
    opacity: 1;
    border-top: 1px solid #EEEEEE;
    position: absolute;
    bottom: 0;
    padding-left: 20px;
    padding-right: 20px;
    padding-top: 24px;
    display: flex;
    justify-content: space-between;

    .Mobile_box {
      display: flex;
      height: 34px;
      align-items: flex-end;

      .Mobile_text {
        font-size: 13px;
        font-weight: 400;
        color: #999999;
        margin-right: 8px;
      }

      .Mobile_price {
        font-size: 14px;
        font-weight: bold;
        color: #FF5F57;
        line-height: 24px;
      }
    }

    .Mobile_pay {
      width: 128px;
      height: 40px;
      background: #0095FF;
      border-radius: 20px 20px 20px 20px;
      opacity: 1;
      font-size: 16px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 40px;
      text-align: center;
      cursor: pointer;
      user-select: none;
    }
    .Mobile_pay:active {
      background: rgba(0, 149, 255, 0.73);
    }
  }

  .Orderheader_Warp {
    margin-bottom: 10px;
  }

}
