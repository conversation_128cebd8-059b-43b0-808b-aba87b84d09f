/**
 * @Description: pc端-个人中心-关注tab页
 */
import React, { useState, useEffect, useRef } from 'react';
import styles from './index.less';
import { Spin } from 'antd';
import { history, connect } from 'umi';
import ExpertList from '@/components/ExpertList';
import noDataImg from '@/assets/GlobalImg/no_data.png';
import InfiniteScroll from 'react-infinite-scroller';
import { throttle } from 'lodash';
import {stringify} from "qs"

// value值用来请求接口传值, 是否是专家：0:否，1:是, 不传是全部
const tabList = [
  { id: 1, text: '全部', value: null },
  { id: 2, text: '专家', value: 1 },
  { id: 3, text: '普通用户', value: 0 },
];

const initStatePage = {
  pageNum: 1,
  hasMore: true,  // 加载更多
  loadMore: false,
}
const initState = {
  total: 0,
  listDate: [],
}

const Index: React.FC = (props: any) => {
  const { dispatch, loading,pcAccount } = props || {};
  const { query } = history.location

  const [tabType, setTabType] = useState(query.subTabKey || pcAccount?.subTabState || 1); // 当前tab切换状态
  const [state, setState] = useState(initState)             // 列表数据
  const [statePage, setStatePage] = useState(initStatePage)  // 当前分页
  const { total, listDate } = state
  const { pageNum, hasMore, loadMore } = statePage || {}
  const scrollParentRef = useRef<HTMLDivElement | null>(null);
  const scrollRef = useRef();

  useEffect(() => {
    getInitData(1, tabType)
  }, []);

  const getInitData = (page:any, tabVal?: any) => {
    dispatch({
      type: 'userInfoStore/getH5FocusList',
      payload: {
        pageNum: page,
        pageSize: 30,
        isExperts: tabVal == 3 ? 0 : tabVal == 2 ? 1 : null, // 是否是专家：0:否，1:是, 不传是全部
      },
    }).then((res: any) => {
      const { code, content } = res || {};
      if (code == 200) {
        const { total: resTotal, resultList } = content || {};
        if (res && code == 200) {
          let data = page == 1 ? [] : listDate;
          data = data.concat(resultList || []);

          if (Array.isArray(data) && data.length == 0) {
            setState({
              ...state,
              listDate: [],
              total: 0,
            })
            return
          }
          setState({
            ...state,
            listDate: [...data],
            total: resTotal,
          })
          setStatePage({
            ...statePage,
            loadMore: false,
            hasMore: data.length !== resTotal,
            pageNum: page,
          })
        }

      }
    }).catch((err:any) => {
      console.log(err)
    });
  }

  // 滚动加载分页
  let handleInfiniteOnLoad = () => {
    if (listDate.length > total - 1) {
      setStatePage({
        ...statePage,
        loadMore: false,
        hasMore: false
      })
      return;
    }
    const pages = pageNum + 1;
    setStatePage({
      ...statePage,
      loadMore: true,
    })
    getInitData(pages)
  }

  handleInfiniteOnLoad = throttle(handleInfiniteOnLoad, 100);

  // tab切换
  const tabSwitch = async(val:any) => {
    scrollRef?.current && (scrollRef.current.scrollTop = 0);

    history.replace(`${history.location.pathname}?${stringify({
      ...history.location.query,
      subTabKey: val,
    })}`)

    setTabType(val);
    dispatch({
      type: 'pcAccount/save',
      payload: {
        subTabState: val
      }
    })
    setState({
      ...state,
      listDate: [],
      total: 0,
    })
    await getInitData(1, val)
  };

  const getH5FocusListLoading = !!loading.effects['userInfoStore/getH5FocusList']; // loading

  return (
    <div className={styles.interest_wrap}>
      <div className={styles.tab_box}>
        {tabList.map((item) => {
          return (
            <span
              key={item.id}
              className={tabType == item.id ? styles.tab_item_active : ''}
              onClick={() => tabSwitch(item.id)}
            >
              {item.text}
            </span>
          );
        })}
      </div>
      <Spin spinning={getH5FocusListLoading}>
        <div className={styles.interest_content} ref={scrollRef}>
          {listDate && listDate.length ? (
            <InfiniteScroll
              loadMore={handleInfiniteOnLoad}
              threshold={50}
              pageStart={1}
              initialLoad={false}
              hasMore={!loadMore && hasMore}
              useWindow={false}
              getScrollParent={() => scrollParentRef.current}
            >
              <ExpertList isHideContent={true} dataSource={listDate} />
            </InfiniteScroll>
          ) : (
            <div className={styles.none_data}>
              <img src={noDataImg} alt="" />
              暂无关注
            </div>
          )}
        </div>
      </Spin>
    </div>
  );
};
export default connect(({ tabState, userInfoStore,pcAccount, loading }: any) => ({
  tabState,
  userInfoStore,
  pcAccount,
  loading,
}))(Index);
