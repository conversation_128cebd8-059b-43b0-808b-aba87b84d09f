import React from 'react';
import { Popup } from 'antd-mobile';
import styles from './index.less';

interface PropsType {
  visible: boolean,                    // 弹窗是否显示
  outerChain: string,                  // 外链地址
  imageTitle: string,                  // 标题
  onCancel: () => {},
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { visible, outerChain, imageTitle } = props

  return (
    <Popup
      visible={visible}
      onMaskClick={props.onCancel}
      className={styles.popup_container}
      bodyStyle={{ height: '100%' }}
      destroyOnClose
    >
      <div className={styles.container}>
        <div className={styles.header_line} onClick={props.onCancel}>
          <div className={styles.header_line_bar}></div>
        </div>
        <div className={styles.title_wrap}>
          <i className={styles.btn} onClick={props.onCancel}></i>
          <div
            className={styles.text}
            dangerouslySetInnerHTML={{__html: imageTitle}}
          ></div>
        </div>
        <div className={styles.iframe_wrap}>
          <iframe width={'100%'} height={'100%'} src={outerChain}></iframe>
        </div>
      </div>
    </Popup>
  )
}
export default Index;
