/**
 * @Description: 选择观众
 */
import React, { useState, useCallback } from 'react'
import { connect } from 'umi'
import { Toast } from 'antd-mobile'
import styles from './index.less'
import GoBackIcon from '@/assets/GlobalImg/go_back.png' // 返回图片
import rightArrowIcon from '@/assets/GlobalImg/right_arrow.png'; // 右箭头小图标
import checkIcon from '@/assets/GlobalImg/check_icon.png'; // 勾选小图标

const Index: React.FC = (props: any) => {
  const { goBack, userInfoStore, dispatch } = props || {};
  const { allViewersState } = userInfoStore || {};
  const [isAllUser, setIsAllUser] = useState(allViewersState); // 是否选中所有人

  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  const { isBizUser } = UserInfo || {}; // 当前用户是否有企业，从登录接口中取值

  // 点击所有人为观众时，赋值并重置数据
  const allAudience = () => {
    goBack(1);
    setIsAllUser(true);

    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        spectatorType: 0, // 观众类型 0默认所有人 1指定王国成员可见 2指定企业品牌用户可见
        selectedKingdomAudience: [], // 重置选择王国成员数据
        allViewersState: true, // 观众选中了所有人
        enterpriseUserData: [], // 选择可见企业/品牌用户，当前企业下的机构数据
        enterpriseUserTab: 0, // 选择可见企业/品牌用户，页面tab
        enterpriseUserSelectData: [], // 选择可见企业/品牌用户，选择后的数据
        enterpriseText: '所有人可见', // 观众展示文案
        spaceTypeId: null, // 空间类型 1专家讲课 2指导 3复杂病例讨论
        spaceTypeName: null, // 空间类型名称
      }
    })
  }

  // 获取创建王国或加入王国相关数据，查询是否有我加入或我创建的王国
  const kingdomFn = useCallback(() => {
    dispatch({
      type: 'userInfoStore/getCreateAndJoinKingdomList',
    }).then((res:any) => {
      const {code, content} = res || {};
      if(res && code == 200) {
        if(content) {
          dispatch({
            type: 'userInfoStore/setTaskListState',
            payload: {
              kingdomListArr: content, // 观众-王国列表数据
            }
          })
          goBack(11);
          return;
        }
      } else {
        Toast.show({content: <div>您当前无可选择王国，<br />请先创建/加入其他王国</div> })
      }
    }).catch((err:any) => console.log(err))
  }, [dispatch])

  return (
    <div className={styles.container}>
      <div className={styles.title_box}>
        <div className={styles.title_btn} onClick={()=>goBack(1)}>
          <img src={GoBackIcon} width={12} height={24} alt=""/>
        </div>
        <div className={styles.title}>选择观众</div>
      </div>
      <div className={styles.wrap}>
        <div className={styles.list_title_init} onClick={allAudience}>
          <div className={styles.list_title}>所有人</div>
          {isAllUser ? <img className={styles.right_check} src={checkIcon} alt="" /> : null}
        </div>
        <div className={styles.list} onClick={kingdomFn}>
          <div className={styles.list_title}>王国成员</div>
          <img className={styles.right_arrow} src={rightArrowIcon} alt="" />
        </div>
        { isBizUser ?
          <div className={styles.list} onClick={()=>{goBack(12)}}>
            <div className={styles.list_title}>企业/品牌用户</div>
            <img className={styles.right_arrow} src={rightArrowIcon} alt="" />
          </div> : null
        }
      </div>
    </div>
  )
}

export default connect(({ userInfoStore, loading }: any) => ({userInfoStore, loading}))(Index)
