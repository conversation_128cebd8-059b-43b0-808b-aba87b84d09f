import React, { useState } from 'react'
import styles from './index.less';

// @ts-ignore
const Index = (props) => {
  const { dataSource, currentType, whereStatus, searchValue } = props;
  const [imgShowList, setImgShowList] = useState([])

  // 处理页面数据格式
  const formatRule = (value) => {
    return value.replace(/\\n/g, '\n')
  }

  // 图片展开方法
  const onClickImgShowFun = (index)=>{
    let arr = []
    arr = imgShowList
    arr.push(index)
    setImgShowList([...arr])
  }
  return (
    <div>
      {
        dataSource.map((item, index) => {
          return <div key={index}>
            {
              item.content && currentType == 1 ? <div className={styles.basic_info_content_desc}>{formatRule(item.content)}</div> : null
            }
            {
             item.titleName && currentType == 3 ? <div className={styles.basic_title}>{item.titleName}</div>:null
            }
            {
              Array.isArray(item.imgContent) && item.imgContent.length ? <>
                {
                  Array.isArray(item.imgContent) && item.imgContent.length > 1 && !imgShowList.includes(index) ? <div onClick={() => { onClickImgShowFun(index) }} className={styles.basic_info_icon_hide}>
                    <img className={styles.basic_info_icon} src={item.imgContent[0].imgPathShowUrl} />
                    <div className={styles.basic_info_icon_hide_mask}></div>
                    <div className={styles.basic_info_icon_hide_number}>
                      +{item.imgContent.length}张
                    </div>
                  </div> : <>
                    {
                      item.imgContent.map((val, idx) => {
                        return <div className={styles.basic_info_icon_wrap} key={idx}>
                          {
                            val.imgPathShowUrl ? <img className={styles.basic_info_icon} src={val.imgPathShowUrl} /> : null
                          }
                          {val.imgName ? <div className={styles.basic_icon_title_name}>{val.imgName}</div> : null}
                        </div>
                      })
                    }
                  </>
                }
              </> : null
            }
            {
              item.content && currentType==2 || currentType==3? <div className={styles.basic_info_content_desc} style={dataSource.length - 1 == index && currentType==3? { marginTop: 16, marginBottom: 32 }:dataSource.length - 1 == index ? { marginTop: 16, marginBottom: 0 } : { marginTop: 16, marginBottom: 16 }}>{formatRule(item.content)}</div> : null
            }
          </div>
        })
      }
    </div>
  )
}
export default Index
