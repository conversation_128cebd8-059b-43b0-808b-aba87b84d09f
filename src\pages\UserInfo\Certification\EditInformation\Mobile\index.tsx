/**
 * @Description: 实名认证-填写认证信息（H5）
 */
import React, { useState, useEffect } from 'react';
import { connect, history } from 'umi';
import classNames from 'classnames'
import { cloneDeep } from 'lodash'
import heic2any from 'heic2any'
import { stringify } from 'qs'
import {backInApp, getFileType, getOperatingEnv, useThrottle, closeInApp} from '@/utils/utils';
import { Toast, Input, Modal } from 'antd-mobile'
import { message, Upload, Spin } from 'antd';
import styles from './index.less';

// 图片icon
import add_icon from '@/assets/GlobalImg/add.png' // 加号
import close_icon from '@/assets/Case/close_icon.png' // 叉号

import NavBar from '@/components/NavBar'; // 头部返回
import SubmitSuccessModalByH5 from '../../SubmitSuccessModalByH5' // 提交成功提示弹窗

const Index: React.FC = (props: any) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}') // 用户信息
  const { dictId } = history.location.query
  const { loading, dispatch } = props;

  const [realNameError, setRealNameError] = useState(''); // 真实姓名输入框错误
  const [unitWorkNameError, setUnitWorkNameError] = useState(''); // 单位/学校输入框错误
  const [specialityNameError, setSpecialityNameError] = useState(''); // 职务/专业输入框错误

  const [realName, setRealName] = useState('') // 真实姓名
  const [phone, setPhone] = useState(UserInfo?.phone || '') // 手机号
  const [unitWorkName, setUnitWorkName] = useState('') // 单位/学校
  const [specialityName, setSpecialityName] = useState('') // 职务/专业
  const [credentials, setCredentials] = useState([]) // 认证证明
  const [submitSuccessModalByH5Visible, setSubmitSuccessModalByH5Visible] = useState(false); // 提交成功提示弹窗

  useEffect(() => {
    getEchoSelfAuthInfo()
  }, [])

  // 个人实名认证回显-星球-实名认证版本
  const getEchoSelfAuthInfo = () => {
    dispatch({
      type: 'userInfoStore/getEchoSelfAuthInfo',
      payload: {}
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200 && content) {
        setRealName(content.realName)
        setPhone(content.phone || UserInfo?.phone)
        setUnitWorkName(content.unitWorkName)
        setSpecialityName(content.specialityName)
        setCredentials(content.credentials || [])
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch(err => {})
  }


  // 上传图片headers
  const getHeaders=() =>{
    // 获取环境类型，1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用   5 FRIDAY app中使用
    const env = getOperatingEnv()

    // FRIDAY APP中本地存储值：access_token、client、phone、user_name、wxuserId、userInfo
    return {// token
      access_token:localStorage.getItem('access_token') || '',
      username: env == '5' ? localStorage.getItem('user_name') : localStorage.getItem('vxOpenIdCipherText') ? localStorage.getItem('vxOpenIdCipherText')  : UserInfo?.phone,
      client: env == '5' ? localStorage.getItem('client') : 'WX',
      type: env == '1' ? '' : '1', // h5 传1
    }
  }

  // 上传校验规则，图片
  const beforeUpload = (file) => {
    const isSize = file.size / 1024 / 1024 < 15
    if (!isSize) {
      message.error('超过15M限制，不允许上传~')
      return false
    }

    const { name: fileName } = file || {}
    // 添加对文件后缀名的限制
    const suffix = fileName.substring(fileName.lastIndexOf('.')+1).toLowerCase()
    const isJpgOrPng = file.type === 'image/jpg' || file.type === 'image/jpeg' ||  file.type === 'image/png'
    const isSuffixByJpgOrPng = (suffix === 'jpg' || suffix === 'jpeg' || suffix === 'png')
    if (!isJpgOrPng || !isSuffixByJpgOrPng) {
      Toast.show({content: '只能上传JPG、JPEG、PNG格式的图片~'})
      return false
    }

    return new Promise((resolve, reject) => {
      const fileReaderBuffer = new FileReader();
      fileReaderBuffer.onload = async () => {
        const type = getFileType(fileReaderBuffer);
        if (type === 'unknown') {
          reject()
          return;
        }
        if (type.includes('/heic')) {
          heic2any({ blob: file, toType: 'image/jpeg' }).then((blob) => {
            resolve(blob)
          }).catch((err) => {
            reject()
          });
        } else {
          resolve(file)
        }
      };
      fileReaderBuffer.readAsArrayBuffer(file);
    })
  }

  // 上传完成回调，图片
  const uploadOnChange = (info) => {
    if (info.file.status === 'uploading') {
      Toast.show({
        icon: 'loading',
        content: '',
        duration: 0,
      })
    }

    // 状态不为uploading时，代表上传事件结束
    if (info.file.status != 'uploading') {
      Toast.clear()
    }

    // 判断当前上传已被拦截
    if (info && !info.file.status) {
      return
    }

    // 上传结束
    if (info && info.file.status === 'error') {
      message.error('上传失败')
      return
    }

    if (info && info.file.status === 'done') {
      const file = info.file
      const res = info.file.response || {}
      const { code, content, msg } = res
      if (code == 200 && content) {
        setCredentials(prevState => {
          if (prevState.length >= 3) {
            return
          }
          const prevStateClone = cloneDeep(prevState)
          prevStateClone.push({
            credentialName: info.file.name, // 图片名
            credentialUrl: content.fileUrl, // 图片地址
            credentialUrlView: content.fileUrlView, // 图片全地址
          })
          return prevStateClone
        })
      } else {
        message.error(msg || '上传失败')
      }
    }
  }

  // 修改真实姓名
  const onChangeRealName = (value) => {
    setRealName(value)
    setRealNameError('') // 清除红字提示
  }

  // 修改单位/学校
  const onChangeUnitWorkName = (value) => {
    setUnitWorkName(value)
    setUnitWorkNameError('') // 清除红字提示
  }

  // 修改职务/专业
  const onChangeSpecialityName = (value) => {
    setSpecialityName(value)
    setSpecialityNameError('') // 清除红字提示
  }

  // 删除认证证明
  const onClickDeleteImgIcon = (index) => {
    const credentialsClone = cloneDeep(credentials)
    credentialsClone.splice(index, 1)
    setCredentials(credentialsClone)
  }

  // 点击提交
  let onClickSubmit = () => {
    // 表单校验
    let formIsHaveError = false // 表单校验是否有错误
    // 中英文、空格正则
    const pattern = /^[~a-zA-Z\u4e00-\u9fa5\s/]+$/
    // 校验姓名
    if (!realName) {
      setRealNameError('请输入真实姓名')
      formIsHaveError = true
    } else if (!pattern.test(realName)) {
      setRealNameError('请输入中英文')
      formIsHaveError = true
    }
    // 检验单位/学校
    if (!unitWorkName) {
      setUnitWorkNameError('请输入单位/学校名称')
      formIsHaveError = true
    } else if (!pattern.test(unitWorkName)) {
      setUnitWorkNameError('请输入中英文')
      formIsHaveError = true
    }
    // 校验职务/专业
    if (specialityName && !pattern.test(specialityName)) {
      setSpecialityNameError('请输入中英文')
      formIsHaveError = true
    }

    if (formIsHaveError) {
      return
    }

    Toast.show({
      icon: 'loading',
      content: '',
      duration: 0,
    })
    dispatch({
      type: 'userInfoStore/saveSelfAuth',
      payload: {
        identityType: dictId, // 身份类型ID
        realName, // 真实姓名
        specialityName, // 职务/专业
        unitWorkName, // 单位/学校
        credentials: credentials, // 认证证明
      }
    }).then(res => {
      Toast.clear()
      const { code, content, msg } = res
      if (code == 200 && content) {
        setSubmitSuccessModalByH5Visible(true)
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch(err => {})
  }
  onClickSubmit = useThrottle(onClickSubmit, 500)

  // 关闭认证成功提示弹窗
  const submitSuccessModalByH5Close = () => {
    if (getOperatingEnv() == 5) {
      closeInApp()
      return
    }
    history.replace(`/UserInfo`)
  }

  const onFocus = (e) => {
    console.log('onFocus',e,e.target)
    document.documentElement.scrollTop=0
    const ss = document.documentElement.clientHeight
    setTimeout(() => {
      Modal.alert({
        content:'1'+ss,
      })
    },1000)
  }
  const onBlur = (e) => {
    console.log('onBlur',e,e.target)
    const ss = document.documentElement.clientHeight
    setTimeout(() => {
      Modal.alert({
        content:'2'+ss,
      })
    },1000)
  }

  // loading
  const loadingGetEchoSelfAuthInfo = !!loading.effects['userInfoStore/getEchoSelfAuthInfo']

  return (
    <Spin wrapperClassName={styles.spin} spinning={loadingGetEchoSelfAuthInfo}>
      {/* 导航栏 */}
      <NavBar title={'个人认证'}></NavBar>
      {/* 内容 */}
      <div className={styles.container}>
        <div className={styles.gray_bar}></div>
        <div className={styles.content}>
          <h1 className={styles.content_title}>填写认证信息</h1>
          <p className={styles.content_tips}>所有信息仅用于资料认证，我们将严格保护您的隐私</p>

          <div className={classNames(styles.form_item_wrap, {
            [styles.error]: !!realNameError,
          })}>
            <div className={styles.form_item_label}>
              <span className={styles.remark}>*</span>
              真实姓名
            </div>
            <div className={styles.form_item_content}>
              <Input
                placeholder="输入真实姓名"
                style={{'--color': '#000', '--font-size': '16px', '--text-align': 'right'}}
                value={realName}
                onChange={onChangeRealName}
                maxLength={20}
              />
            </div>
          </div>
          {/* 错误提示msg */}
          {
            !!realNameError && <div className={styles.form_error_tips}>{realNameError}</div>
          }

          {/* 手机号 */}
          <div className={styles.form_item_wrap}>
            <div className={styles.form_item_label}>
              <span className={styles.remark}>*</span>
              手机号
            </div>
            <div className={styles.form_item_content}>
              <Input value={phone} readOnly style={{'--color': '#000', '--font-size': '16px', '--text-align': 'right'}}/>
            </div>
          </div>

          <div className={classNames(styles.form_item_wrap, {
            [styles.error]: !!unitWorkNameError,
          })}>
            <div className={styles.form_item_label}>
              <span className={styles.remark}>*</span>
              单位/学校
            </div>
            <div className={styles.form_item_content}>
              <Input
                placeholder="输入单位名称/学校名称"
                style={{'--color': '#000', '--font-size': '16px', '--text-align': 'right'}}
                value={unitWorkName}
                onChange={onChangeUnitWorkName}
                maxLength={30}
              />
            </div>
          </div>
          {/* 错误提示msg */}
          {
            !!unitWorkNameError && <div className={styles.form_error_tips}>{unitWorkNameError}</div>
          }

          <div className={classNames(styles.form_item_wrap, {
            [styles.error]: !!specialityNameError,
          })}>
            <div className={styles.form_item_label}>职务/专业</div>
            <div className={styles.form_item_content}>
              <Input
                placeholder="输入职务/专业"
                style={{'--color': '#000', '--font-size': '16px', '--text-align': 'right'}}
                value={specialityName}
                onChange={onChangeSpecialityName}
                maxLength={20}
              />
            </div>
          </div>
          {/* 错误提示msg */}
          {
            !!specialityNameError && <div className={styles.form_error_tips}>{specialityNameError}</div>
          }

          <div className={styles.form_upload_img}>
            <div className={styles.upload_img_title}>认证证明</div>
            <div className={styles.upload_img_tips}>您可以上传医师证、助理医师资格证、护士证、毕业证、学生证、身份证、工作胸牌等工作相关证件照片</div>
            <div className={styles.upload_img_list}>

              {
                credentials.map((item, index) => {
                  return (
                    <div key={item.credentialUrlView} className={styles.upload_img_item}>
                      <img src={item.credentialUrlView} width={112} height={112} alt=""/>
                      {/* 删除icon */}
                      <div className={styles.delete_icon_wrap} onClick={() => onClickDeleteImgIcon(index)}>
                        <img src={close_icon} width={20} height={20} alt=""/>
                      </div>
                    </div>
                  )
                })
              }

              {/* 上传按钮 */}
              <div className={styles.upload_img_btn} style={credentials.length >= 3 ? {display: 'none'} : {}}>
                <Upload
                  headers={getHeaders()}
                  accept="image/*"
                  listType="picture-card"
                  action={`/api/fri-uc/self-auth/upload-credential`}
                  onChange={uploadOnChange}
                  beforeUpload={beforeUpload}
                  showUploadList={false}
                  // multiple={true}
                  // customRequest={uploadCustomRequest}
                >
                  <div>
                    <img src={add_icon} width={40} height={40} alt=""/>
                  </div>
                </Upload>
              </div>
            </div>
          </div>

        </div>
        <div className={styles.btn_wrap}>
          <div className={classNames(styles.btn, {
            [styles.disabled]: !realName || !unitWorkName,
          })} onClick={onClickSubmit}>提交认证</div>
        </div>
      </div>

      {/* 认证成功提示弹窗 */}
      <SubmitSuccessModalByH5 visible={submitSuccessModalByH5Visible} onCancel={submitSuccessModalByH5Close}/>
    </Spin>
  )
}

export default connect(({ userInfoStore, loading }: any) => ({ userInfoStore, loading}))(Index)
