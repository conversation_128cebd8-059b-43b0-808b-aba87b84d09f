.tab_content_list {
  width: 100%;
  height: 100%;
  padding: 20px 0;
  background: #fff;
  border-radius: 8px 8px 8px 8px;

  .tab_space_title {
    font-size: 14px;
    font-weight: 400;
    color: #000000;
    line-height: 16px;
    margin-bottom: 12px;
    padding-left: 20px;
  }
  .tab_spaceRoleType_list{
    padding-left: 20px;
    border-bottom: 1px solid #E9EEF2;
    span{
      display: inline-block;
      font-size: 14px;
      color: #666666;
      line-height: 14px;
      padding-bottom: 12px;
      margin-right: 20px;
      cursor: pointer;
    }
    .spaceRoleTypeActive{
      color: #0095FF;
    }
  }
  .tab_spaceStatus_list{
    padding: 20px;
    position: relative;
    >span{
      display: inline-block;
      font-size: 14px;
      color: #666666;
      background: #F3F3F3;
      line-height: 14px;
      padding: 6px 12px;
      border-radius: 14px;
      margin-right: 16px;
      cursor: pointer;
    }
    .spaceStatusActive{
      color: #0095FF;
      background: #E1F1FE;
    }
    i{
      position: absolute;
      right:20px;
      top: 20px;
      font-style: normal;
      cursor: pointer;
      .screen_icon{
        width: 18px;
        height: 18px;
        margin-right: 4px;
        cursor: pointer;
        vertical-align:text-bottom;
      }
    }
    .screen_btnActive{
      color: #0095FF;
    }
  }
  .tab_content_listBody{
    overflow-y: auto;
    height: calc(100% - 129px);
  }
  .scroll_box {
    width: 100%;
  }
  .space_wrap {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 30px;
    padding: 0 10px;
    box-sizing: border-box;

    .space_list {
      width: 25%;
      margin-bottom: 16px;
      padding: 0 10px;
    }

    .no_data_wrap {
      width: 100%;
      display: flex;
      justify-content: center;
    }
  }
  .screen_modal{
    :global{
      .ant-modal-header,.ant-modal-body{
        padding: 15px;
      }
      .ant-modal-header{
        padding-bottom:10px;
        font-size: 18px;
        border-bottom:0;
      }
      .ant-modal-body{
        padding-top:5px;
      }
    }
    .screen_box{
      .isBizTitle{
        font-size: 14px;
        font-weight: 600;
        color:#000000;
      }
      .isBizSelectBox{
        margin-top:10px;
        :global{
          .ant-checkbox-group{
            width: auto!important;
            display: inline-block;
            >div{
              display: inline-block;
            }
          }
        }
      }
    }
    .footer{
      text-align: right;
      .btn_primary{
        background: #409EFF;
        border-radius: 4px;
        margin-left: 10px;
      }
    }
  }
}
