/**
 * @Description: 移动端指导订单信息卡片
 * @author: 赵斐
 */
import React from 'react';
import arrowIcon from '@/assets/Consultation/H5/arrow_icon.png'
import { priceFormat } from '@/utils/utils'
import styles from './index.less'

interface PropsType {
  isPc?: boolean,             // 区分是PC、H5引用
  data:any,                   // 指导订单信息
  goPay: (val?:any) => void,  // 点击查看订单详情
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { isPc = false , data ,goPay } = props;
  const {
    orderNumber,   // 指导订单
    status,        // 支付状态(0已取消、1未支付(待评估)、2去支付(已评估)、3已支付)
    amount,        // 账单金额
    consultationId, // 指导ID
    thisUserIsExperts,  // 1 专家  0 用户
  } = data || {}

  // 点击支付详情\点击去支付
  const goPayDetails = () => {
    goPay(consultationId)
  }

  return (
    <div className={styles.wrap}>
      <div className={styles.header}>
        <span className={styles.header_title}>指导订单</span>
        {
          status == 1 || status == 2 && thisUserIsExperts!=1?
          <span className={styles.header_status} style={isPc?{cursor:'pointer'}:{}} onClick={() => { goPayDetails() }}>去支付</span> :
          <span className={styles.header_status} style={isPc?{cursor:'pointer'}:{}} onClick={() => { goPayDetails() }}>{isPc?'订单详情':'支付详情'}</span>
        }

        <span className={styles.header_icon}><img src={arrowIcon} alt="icon" /></span>
      </div>
      <div className={styles.content}>
        <p>指导订单：{orderNumber}</p>
        <p>支付状态：{status == 1?"待支付":status == 2?"待支付":status == 3?"已支付":"已取消"}</p>
        <p>指导金总计：¥{priceFormat(amount)}</p>
      </div>
    </div>
  )
}
export default Index
