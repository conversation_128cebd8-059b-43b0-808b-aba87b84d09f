import React from 'react';
import styles from './index.less';
import {Modal} from "antd";
import {userTokenInvalid} from "@/utils/request"; // 导入弹窗

const UserTokenInvalidModal = ({
     visible,   // visible 是否显示弹窗
     onClose,   // 是否取消
}) => {
  // 获取当前页面路径

  return (
    <div className={styles.WarnModalWarp_NoRem}>
      <Modal
        open={visible}
        closeOnAction
        onClose={onClose}
        content={
          <div className={styles.WarnModal}>
            <div className={styles.WarnModalTitle}>
              <i className={styles.SpatialDetail_modal_warn_icon}></i>
              <div className={styles.SpatialDetail_modal_warn_title}>立即登录，畅享精彩内容！</div>
            </div>
            <div className={styles.WarnModalBtnWarp}>
              <div onClick={onClose} className={styles.CancelBtn}>
                暂不登录
              </div>
              <div onClick={async ()=>{ userTokenInvalid('/PlanetChatRoom') }} className={styles.EnterBtn}>
                立即登录
              </div>
            </div>
          </div>
        }
      />
    </div>
  );
};

export default UserTokenInvalidModal;
