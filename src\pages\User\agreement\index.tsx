/**
 * 隐私政策页面相关页面
 */
import React, { useEffect, useState } from 'react';
import classNames from "classnames";
import styles from './index.less';
import {Helmet} from "react-helmet";


const Agreement: React.FC = (props: any) => {
  // agreementId: 协议id
  // privacyPolicy: 隐私政策
  // businessAgreement: 企业会员服务协议
  let {match: {params: {agreementId}}} = props

  // 生成8位随机数
  const pageKeyByRendom = Math.random().toString(36).substr(2, 8);


  return (
    <div className={classNames({
      [styles.agreementWarp]: true,
    })}>

      <Helmet>
        {agreementId == 'privacyPolicy' && <title>隐私政策</title>}
        {agreementId == 'businessAgreement' && <title>企业会员服务协议</title>}
      </Helmet>

      <div className={styles.agreementBox}>
        {Array.from({length: agreementId == 'privacyPolicy' ? 15 : 9}).map((item, index) => {
          try {
            return (
              <img
                key={index}
                src={`https://static.jwsmed.com/public/DigitalHealth/Business/assets/${agreementId}/${index + 1}.png?pageKeyByRendom=${pageKeyByRendom}`}
                alt=""
                onError={(e) => { e.target.style.display = 'none'; }}
              />
            )
          }catch (e) {
            console.log(e)
          }
        })}
      </div>
    </div>
  )
}
export default Agreement
