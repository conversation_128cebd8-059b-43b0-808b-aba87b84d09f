/**
 * @Description: 会议详情-打卡列表弹窗
 */
import React, { useState, useEffect } from 'react';
import { history, connect } from 'umi'
import { Popup, InfiniteScroll } from 'antd-mobile';
import styles from './index.less';

import Avatar from '@/pages/PlanetChatRoom/components/Avatar' // 头像组件

interface PropsType {
  visible: boolean,          // 弹窗是否显示
  onCancel: () => void,      // 关闭弹窗
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {
    visible,
    dispatch,
    PlanetChatRoom, // models数据
  } = props

  const {
    SpaceInfo, // 空间信息
    signInObj, // 查看空间打卡签到对象
    signInList, // 查看空间打卡签到列表
  } = PlanetChatRoom || {}

  const [hasMoreBySignInList, setHasMoreBySignInList] = useState(false) // 是否还有更多数据

  useEffect(() => {
    if (visible) {
      loadMore()
    } else {
      // 清空数据
      cleanState()
    }
  }, [visible])

  // 清空数据
  const cleanState = async () => {
    await setHasMoreBySignInList(false)
    await dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {
        signInList: null,
        signInObj: null,
      }
    })
  }

  // 加载更多
  const loadMore = async () => {
    const { total, pageNum, pageSize } = signInObj || {};
    const dataByGetSignInList = await dispatch({
      type: 'PlanetChatRoom/getSignInList',
      payload: {
        spaceId: SpaceInfo.id,
        pageNum: pageNum ? pageNum + 1 : 1,
        pageSize: 30,
      },
    });
    const { content } = dataByGetSignInList || {};
    const { resultList } = content || {};
    await setHasMoreBySignInList(Array.isArray(resultList) && resultList.length > 0);
  }

  return (
    <Popup
      visible={visible}
      onMaskClick={props.onCancel}
      className={styles.popup_container}
      bodyStyle={{ height: '50vh' }}
      destroyOnClose
    >
      <div className={styles.container}>
        {/* 头部 */}
        <div className={styles.header_line} onClick={props.onCancel}>
          <div className={styles.header_line_bar}></div>
        </div>

        <div className={styles.header_title}>打卡详情</div>

        <div className={styles.sign_in_list_wrap}>
          {
            signInList && signInList.length > 0 && signInList.map(item => {
              return (
                <div className={styles.sign_in_item}>
                  <div className={styles.left}>
                    <div className={styles.avatar_wrap}>
                      <Avatar userInfo={item} size={24}/>
                    </div>
                    <div className={styles.name}>{item.name}</div>
                    <div className={styles.phone}>尾号{item.phoneTail}</div>
                  </div>
                  <div className={styles.right}>{item.createDate}</div>
                </div>
              )
            })
          }

          <InfiniteScroll
            loadMore={loadMore}
            threshold={20}
            hasMore={hasMoreBySignInList}
          />
        </div>
      </div>
    </Popup>
  )
}
export default connect(({ PlanetChatRoom, loading }: any) => ({PlanetChatRoom, loading}))(Index)
