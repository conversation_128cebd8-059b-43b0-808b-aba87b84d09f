import React, {useEffect, useState,} from 'react';
import {connect} from 'umi';
import styles from './index.less';
import classNames from 'classnames';
import Stream from '@/componentsByTRTC/Stream';
import {randomColor} from '@/utils/utils';
import Avatar from '@/pages/PlanetChatRoom/components/Avatar';

type propsType = {
  global: any;
};

const Index: React.FC<propsType> = (props) => {
  let {RTC, PlanetChatRoom, showStream, streamUser, randomNum} = props || {};

  const {SpaceInfo, isMobile} = PlanetChatRoom || {};

  const {wxUserId} = SpaceInfo || {};

  let isHost = streamUser?.imUserId == SpaceInfo?.hostUserInfo?.imUserId;
  const [randomNumState, setRandomNumState] = useState(Math.random());
  useEffect(() => {
    setRandomNumState(randomNum ? randomNum : Math.random());
  }, [randomNum]);

  return (
    <>
      {showStream &&
        (showStream.hasAudio || (showStream.streamType == 'local' && !showStream.mutedVideo)) && (
          <div
            key={`${showStream.stream.getUserId()}_${showStream.stream.getType()}_PrimaryStream_${randomNumState}`}
            className={classNames({
              [styles.isModeMatrix_camera_picture_camera_item]: true,
              [styles.isModeMatrix_camera_picture_camera_item_hidden]:
              showStream &&
              (!showStream.hasVideo ||
                (showStream.streamType == 'local' && showStream.mutedVideo)),
            })}
          >
            <div
              className={classNames({
                [styles.StreamWarp]: true,
                [styles.StreamWarpHidden]:
                showStream &&
                (!showStream.hasVideo ||
                  (showStream.streamType == 'local' && showStream.mutedVideo)),
              })}
            >
              <Stream
                key={`${showStream.stream.getUserId()}_${showStream.stream.getType()}_PrimaryStream_${randomNumState}`}
                stream={showStream.stream}
                config={showStream}
                init={(dom) => {
                  let config = {
                    objectFit: 'contain', // contain , cover
                  };
                  return RTC && RTC.playStream(showStream.stream, dom, config);
                }}
              ></Stream>
            </div>
            {showStream &&
              (!showStream.hasVideo ||
                (showStream.streamType == 'local' && showStream.mutedVideo)) && (
                <div className={styles.headUrlWarp}>
                  <div
                    style={{background: wxUserId ? randomColor(wxUserId) : 'none'}}
                    className={styles.video_Title_box_left_avatar}
                  >
                    <Avatar userInfo={streamUser} size={44} isPc={!isMobile}></Avatar>
                  </div>
                </div>
              )}

            <div
              className={classNames({
                [styles.HorizontalLiveRoom_camera_picture_camera_bottom_box]: true,
                [styles.HorizontalLiveRoom_camera_picture_camera_bottom_box_lianmai]:
                streamUser && streamUser.statusType == 1,
              })}
            >
              <div className={styles.HorizontalLiveRoom_camera_picture_camera_bottom_box_Name_Warp}>
                {isHost && (
                  <div className={styles.isHost}>
                    <i></i>
                  </div>
                )}
                <div className={styles.UserInfo}>
                  <div
                    className={classNames({
                      [styles.HorizontalLiveRoom_camera_picture_mic_icon]:
                      !showStream.mutedAudio && showStream.audioVolume == 0,
                      [styles.HorizontalLiveRoom_camera_picture_forbidden_mic_icon]:
                      showStream.mutedAudio,
                      [styles.HorizontalLiveRoom_camera_picture_forbidden_Lianmai]:
                      !showStream.mutedAudio && showStream.audioVolume > 0,
                    })}
                  ></div>
                  <div
                    className={classNames({
                      [styles.HorizontalLiveRoom_camera_picture_camera_bottom_box_Name]: true,
                      [styles.HorizontalLiveRoom_camera_picture_camera_bottom_box_Name_lianmai]:
                      streamUser && streamUser.statusType == 1,
                    })}
                  >
                    {streamUser && streamUser.name}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
    </>
  );
};
export default connect(({ global, PlanetChatRoom, loading }: any) => ({
  global,
  PlanetChatRoom,
  loading,
}))(Index);
