.modal {
  :global {
    .ant-modal-header {
      padding: 12px 24px;
    }
    .ant-modal-title {
      font-size: 16px;
      color: #303133;
      font-weight: 500;
      line-height: 26px;
    }
    .ant-modal-body {
      padding: 16px 24px 32px;
    }
  }
}
.item {
  display: flex;
  flex-wrap: nowrap;
  line-height: 20px;
  font-size: 14px;
  margin-bottom: 16px;
  .label {
    white-space: nowrap;
    color: #666;
  }
  .value {
    word-break: break-all;
    color: #000;
    :global {
      .ant-checkbox-inner, .ant-checkbox-checked::after {
        border-radius: 50%;
        user-select: none;
        cursor: pointer;
      }
      .ant-checkbox-wrapper {
        color: #000;
        user-select: none;
        cursor: pointer;
      }
      .ant-checkbox-wrapper-checked {
        color: #0095FF;
        user-select: none;
        cursor: pointer;
      }
    }

  }
  .price_value {
    color: #FF5F57;
    font-weight: 500;
  }
}
.qrcode {
  border-radius: 3px;
  border: 1px solid #EEE;
  width: 160px;
  margin: 0 auto 24px;
  padding: 12px 16px 12px;
  font-size: 12px;
  color: #666;
  line-height: 17px;
  text-align: center;
  img {
    width: 100%;
    height: auto;
    margin-bottom: 8px;
  }
}

.pay_btn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 112px;
  height: 24px;
  margin: 0 auto;
  border: 1px solid #0095FF;
  border-radius: 3px;
  color: #0095FF;
  font-size: 12px;
  cursor: pointer;
  :global {
    .anticon {
      margin-right: 5px;
    }
  }
}

.content {
  padding: 0 16px 16px;
  .qr_code{
    width: 180px;
    // height: 180px;
    margin: 0 auto;
    margin-top: 24px;
  }
  .word {
    margin: 16px 0 28px;
    font-size: 14px;
    font-weight: 400;
    color: #999999;
    line-height: 16px;
    text-align: center;
  }
}


.modal_pay {
  :global {

  }
}
