/**
 * @Description: PC端，帖子卡片
 */
import React from 'react'
import classNames from 'classnames'
import { message, Typography } from 'antd'
import { PictureOutlined } from '@ant-design/icons'
import styles from './index.less'

interface PropsType {
  isMyPages: boolean,        // 是否是自己的页面
  data: object,              // 数据
  onLow: () => {},           // 下架回调
  onDelete: () => {},        // 删除回调
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { isMyPages, data } = props

  // 下架
  const onLow = () => {
    props.onLow(data.id)
  }

  // 删除
  const onDelete = () => {
    props.onDelete(data.id)
  }

  // 复制空间链接
  const onCopy = () => {
    message.success('复制成功')
  }

  return (
    <div className={styles.wrap}>
      <div className={classNames('ql-editor', styles.post_title)} dangerouslySetInnerHTML={{__html: data.imageTextContent}}></div>
      <div className={styles.cover_img_box}>
        <div className={styles.cover_img_wrap} style={data.imageUrls && data.imageUrls[0] ? {backgroundImage: `url(${data.imageUrls[0]})`} : {}}>
          {
            data.imageUrls && data.imageUrls[0] ? null :
              <div className={styles.no_cover_wrap}>
                <PictureOutlined/>
                <div style={{marginTop: 5}}>无封面</div>
              </div>
          }
        </div>
        <div className={styles.date}>{data.editDate}</div>
      </div>
      {/* 状态：1.审核通过 0.未审核 2.审核未通过 3.草稿 */}
      {
        data.status != 3 &&
        <div className={styles.status_wrap}>
          <span className={classNames(styles.status, {
            [styles.status_0]: data.status == 0,
            [styles.status_1]: data.status == 1,
            [styles.status_2]: data.status == 2,
          })}>
            {
              data.status == 0 ? '审核中' :
                data.status == 1 ? '已发布'
                  : data.status == 2 ? '审核不通过'
                  : ''
            }
          </span>
        </div>
      }

      <div className={styles.details_box}>
        {
          data.status != 3 ?
            <div className={styles.details_data}>
              <span>点赞{data.spotLikeSize || 0}</span> · <span>评论{data.imageComments || 0}</span> · <span>浏览量{data.imagePv || 0}</span> · <span>转发{data.imageForward || 0}</span>
            </div>
            :
            <div></div>
        }
        {/* 是我的主页时有操作按钮 */}
        {
          isMyPages ?
            <div className={styles.btn_wrap}>
              {
                data.status == 1 &&
                <Typography.Paragraph copyable={{
                  text: `${window.location.origin}/CreateGraphicsText/PostDetails?id=${data.id}`,
                  icon: [<div>复制链接</div>, <div>复制链接</div>],
                  tooltips: ['', ''],
                  onCopy: onCopy,
                }}></Typography.Paragraph>
              }

              {
                data.status != 3 &&
                <div onClick={onLow}>下架</div>
              }

              {
                data.status == 3 &&
                <div onClick={onDelete}>删除</div>
              }
            </div>
            : null
        }

      </div>

    </div>
  )
}

export default Index
