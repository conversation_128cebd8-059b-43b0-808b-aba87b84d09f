video::-internal-media-controls-download-button {
  display: none;
}
video::-webkit-media-controls-enclosure {
  overflow: hidden;
}
video::-webkit-media-controls-panel {
  width: calc(100% + 30px);
}

video::-webkit-media-controls-fullscreen-button {
  display: none;
}

video::-webkit-media-controls-play-button {
  display: none;
}

video::-webkit-media-controls-timeline {
  display: none;
}

video::-webkit-media-controls-current-time-display {
  display: none;
}

video::-webkit-media-controls-time-remaining-display {
  display: none;
}

video::-webkit-media-controls-mute-button {
  display: none;
}

video::-webkit-media-controls-toggle-closed-captions-button {
  display: none;
}

video::-webkit-media-controls-enclosure {
  display: none;
}

video::-webkit-media-controls-volume-slider {
  display: none;
}

.video_warp_Box_silderWarp {
  position: relative;
  width: 100%;
  height: 100%;
}

.video_warp_Box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.video {
  // -width: 100%;
  // -height:100%;
  // height: calc(100%);
  // aspect-ratio: 16/9;

  .videoJs {
    width: 100%;
    height: 100%;
  }

  :global {
    .video-js .vjs-tech {
      height: 100vh;
    }

    .video-react .video-react-big-play-button {
      display: none;
    }

    .video-react-has-started .video-react-control-bar {
      display: none;
    }

    .vjs-has-started .vjs-control-bar,
    .vjs-audio-only-mode .vjs-control-bar {
      display: none;
    }
    .vjs-control-bar {
      display: none;
    }
    .video-js .vjs-big-play-button {
      display: none;
    }
    /* .vjs-poster {
       display: none;
     }*/
  }
}

.video_warp {
  width: 100%;
  min-height: 215px;
  aspect-ratio: 16/9;
}

.video_warp_HorizontalLiveRoom {
  width: 100%;
  height: 100%;
}

.video_ModeratorControl_VerticalLiveRoom {
  position: absolute;
  bottom: 0px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 48px;
  padding-right: 10px;
  padding-left: 10px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000000 100%);
  user-select: none;
}

.playBtn {
  width: 32px;
  height: 32px;
  margin-right: 6px;
  background: url('~@/assets/PlanetChatRoom/HorizontalLiveRoom_play_Icon.png') no-repeat;
  background-size: cover;
  cursor: pointer;
  opacity: 1;
  user-select: none;
}

.PauseBtn {
  width: 32px;
  height: 32px;
  margin-right: 6px;
  background: url('~@/assets/PlanetChatRoom/HorizontalLiveRoom_Pause_Icon.png') no-repeat;
  background-size: cover;
  cursor: pointer;
  opacity: 1;
  user-select: none;
}

.video_Progress_bar_warp {
  // height: 22px;
  display: flex;
  align-items: center;
  width: 100%;
  color: #ffffff;
  font-weight: 500;
  font-size: 12px;
  line-height: 14px;
  // padding-bottom: 12px;
}

.video_Progress_bar_time_left {
  padding-right: 8px;
  color: #ffffff;
  font-weight: 500;
  font-size: 12px;
  line-height: 14px;
}

.video_Progress_bar_time_right {
  padding-left: 8px;
  color: #ffffff;
  font-weight: 500;
  font-size: 12px;
  line-height: 14px;
}

.video_Progress_bar {
  flex: 1;
  :global {
    .ant-slider-rail,
    .ant-slider-track {
      height: 2px;
    }
    .ant-slider-rail {
      background-color: rgba(255, 255, 255, 0.5);
    }
    .ant-slider-handle {
      width: 10px;
      height: 10px;
    }
  }
}
