.spin {
  height: 100%;
  :global {
    .ant-spin-container {
      height: 100%;
    }
  }
}
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #eef3f9;
}

.content {
  flex: 1;
  padding: 16px 0;
  overflow-y: auto;
}

.content_inner {
  width: 816px;
  min-height: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
}

.nav_bar {
  flex-shrink: 0;
  width: 100%;
  display: flex;
  align-items: center;
  background: #fff;
  padding: 15px 20px;
  margin-bottom: 16px;
  border-radius: 8px;
  i {
    display: inline-block;
    width: 32px;
    height: 32px;
    background: url("../../../../../assets/GlobalImg/pc_goback.png") no-repeat center;
    background-size: 24px 24px;
    margin-right: 4px;
    cursor: pointer;
  }
  span {
    font-size: 20px;
    color: #000;
    line-height: 32px;
  }
}

.wrapper {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  padding: 0 40px;
  :global {
    .ant-form-item-label {
      width: 108px;
      text-align: left;
    }
    .ant-form-item-label > label {
      font-size: 14px;
      color: #000;
    }
  }

  .title_box{
    border-bottom: 1px solid #ddd;
    padding: 24px 0 16px;
    :global{
      .ant-form-item {
        margin-bottom: 0;
      }
      .ant-input-lg {
        padding-left: 0;
        padding-right: 0;
        font-size: 22px;
        border: 0 !important;
        box-shadow: none !important;
      }
    }
  }

  .form_input_text {
    margin-top: 32px;
  }

  .form_radio{
    margin-top: 24px;

    :global{
      .ant-radio-wrapper{
        margin-right: 25px;
      }
      .ant-form-item{
        margin-bottom: 12px;
      }
    }
  }

  .from_content {
    margin-top: 24px;

    :global {
      .ant-cascader-menu{
        width: 212px;
      }
      .ant-cascader-menu-item:hover{
        color: #0095FF;
        background: none;
      }
      .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled), .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled):hover{
        background: none;
        color: #0095FF;
        font-weight: 500;
      }
    }
  }

  .upload_wrap{
    padding-left: 108px;
    display: flex;
    align-items: flex-end;

    .link_img_wrap {
      :global {
        .ant-upload {
          display: block;
        }
      }
      .upload_box {
        width: 351px;
        height: 142px;
        background: #F8F8F8;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px 4px 0 0;
        cursor: pointer;
        .upload_img {
          width: 100%;
          height: 100%;
          background-repeat: no-repeat;
          background-position: center;
          background-size: cover;
        }
        .add_img {
          display: flex;
          flex-direction: column;
          align-items: center;
          font-size: 13px;
          color: #999;
          line-height: 18px;
          .init_upload_img {
            width: 48px;
            height: 48px;
            margin-bottom: 8px;
          }
        }
      }
      .img_link_text {
        height: 34px;
        padding: 0 4px;
        background: #F5F6F8;
        display: flex;
        align-items: center;
        :global {
          .ant-typography {
            margin-bottom: 0;
            font-size: 13px;
            color: #0095FF;
            width: 100%;
          }
          .ant-typography-edit {
            font-size: 14px;
          }
          div.ant-typography-edit-content {
            left: 0;
            margin-top: 0;
            margin-bottom: 0;
          }
          .ant-input {
            font-size: 13px;
          }
        }
      }
    }

    .preview_btn {
      font-size: 16px;
      color: #0095FF;
      margin-left: 16px;
      cursor: pointer;
    }
  }

  .init_img_wrap {
    display: flex;
    padding-left: 108px;
    align-items: flex-end;

    .init_img_list {
      width: 351px;
      height: 64px;
      border-radius: 4px;
      border: 1px solid #EBEBEB;
      padding: 8px 0 0 8px;
      display: flex;
      flex-wrap: nowrap;
      .init_bg {
        flex-shrink: 0;
      }

      .init_info {
        flex: 1;
        overflow: hidden;
        padding: 0 8px 8px 8px;
        :global {
          .ant-typography {
            margin-bottom: 0;
            font-size: 13px;
            color: #0095FF;
          }
          .ant-typography-edit {
            font-size: 14px;
          }
          div.ant-typography-edit-content {
            left: 0;
            margin-top: 0;
            margin-bottom: 0;
          }
          .ant-input {
            font-size: 13px;
          }
        }

        .init_info_title {
          font-size: 13px;
          color: #666;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }

    .preview_btn {
      font-size: 16px;
      color: #0095FF;
      margin-left: 16px;
      cursor: pointer;
    }
  }
}

.bottom_tips_bar {
  position: fixed;
  z-index: 990;
  left: 0;
  right: 0;
  bottom: 79px;
  width: 100%;
  height: 33px;
  background: #FFF7DA;
  border-bottom: 1px solid #EFD989;
  text-align: center;
  line-height: 33px;
  font-size: 12px;
  color: #8C772B;
  .bar_btn_wrap {
    position: absolute;
    width: 12px;
    height: 12px;
    right: 16px;
    top: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 9px;
  }
}

.footer {
  background: #fff;
  border-top: 1px solid #ddd;
  height: 80px;
  flex-shrink: 0;
  .footer_content {
    width: 816px;
    height: 100%;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .footer_content_left {
      font-size: 16px;
      color: #999;
      & > span + span {
        margin-left: 16px;
      }
    }
    .footer_content_right {
      :global {
        .ant-btn + .ant-btn {
          margin-left: 24px;
        }
        .ant-btn {
          height: 38px;
          padding-left: 20px;
          padding-right: 20px;
        }
      }
    }
  }
}

// 关联王国下拉框
.custom_dropdown_render {
  :global {
    ::-webkit-scrollbar{
      width: 7px;
      height: 7px;
    }
    ::-webkit-scrollbar-thumb{
      border-radius:10px;
      border: 2px solid #fff;
      -webkit-box-shadow: inset 0 0 0 5px rgba(0,0,0,0.2);
      background: rgba(0,0,0,0.2);
    }
    ::-webkit-scrollbar-track{
      // -webkit-box-shadow: inset 0 0 0 5px rgba(0,0,0,0.2);
      border-radius:0px;
      background:#fff;
    }
  }
  .select_dropdown_container {
    padding-bottom: 16px;
    max-height: 256px;
    overflow-y: auto;
    .select_dropdown_title {
      font-size: 14px;
      color: #666;
      padding: 12px 16px 4px;
    }
    .kingdom_item {
      padding: 12px 16px;
      border-bottom: 1px solid #F5F6F8;
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      cursor: pointer;
      &:hover {
        background: #f5f5f5;
      }
      &.selected {
        background: #e6f7ff;
      }
      & > i {
        width: 40px;
        height: 40px;
        flex-shrink: 0;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        margin-right: 8px;
        font-style: normal;
        line-height: 40px;
        color: #fff;
        font-size: 12px;
        text-align: center;
        white-space: nowrap;
      }
      .kingdom_item_details {
        flex: 1;
        .kingdom_name {
          font-size: 15px;
          color: #000;
          font-weight: 500;
          line-height: 21px;
          margin-bottom: 2px;
        }
        .kingdom_info_box {
          display: flex;
          align-items: center;
          font-size: 12px;
          color: #999;
          line-height: 17px;
          & > span + span {
            margin-left: 4px;
          }
        }
      }
    }
  }
}
