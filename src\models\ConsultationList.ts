import {
  getConsultationList,
  getConsultationOrderInfo,
  submitConsultationPictureOrderPay,
  submitConsultationVideoOrderPay,
} from "@/services/consultation/ConsultationList";

const initializedState = {}

export default {
  namespace: 'ConsultationList',
  state: {
    ...initializedState
  },
  effects: {
    // 登录后获取该用户的IM秘钥信息
    * getConsultationList({payload}, {call, put}) {
      const response = yield call(getConsultationList, payload);
      const {content, code} = response || {};
      if (code == 200 && !!content) {
        yield put({
          type: 'setState',
          payload: content
        })
      }
      return response
    },
  },
  // 查询指导订单详情
  * getConsultationOrderInfo({payload}, {call, put}) {
    const response = yield call(getConsultationOrderInfo, payload);
    const {content, code} = response || {};
    return response
  },

  // 图文指导提交订单或去支付
  * submitConsultationPictureOrderPay({payload}, {call, put}) {
    const response = yield call(submitConsultationPictureOrderPay, payload);
    const {content, code} = response || {};
    return response
  },
  // 视频指导提交订单或去支付
  * submitConsultationVideoOrderPay({payload}, {call, put}) {
    const response = yield call(submitConsultationVideoOrderPay, payload);
    const {content, code} = response || {};
    return response
  },
  reducers: {
    // 更新状态值数据
    setState(state, {payload}) {
      return {
        ...state,
        ...payload
      }
    },
    // 清空数据
    clean(state, {payload}) {
      return {
        ...state,
        ...initializedState,
      }
    }
  },
};
