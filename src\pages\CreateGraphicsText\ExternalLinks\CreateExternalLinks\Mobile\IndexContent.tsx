import React, { useEffect, useState } from 'react';
import { connect, history, useAliveController } from 'umi';
import classNames from 'classnames';
import { stringify } from 'qs';
import { getHeaders, useThrottle } from '@/utils/utils';
import { Upload, Spin } from 'antd';
import { Form, Input, Radio, Toast } from 'antd-mobile';
import { CloseOutlined } from '@ant-design/icons'
import styles from './index.less';
import NavBar from '@/components/NavBar';
import linkBgIcon from '@/assets/GlobalImg/link_bg.png';
import uploadImgIcon from '@/assets/GlobalImg/add.png';
import linkIcon from '@/assets/GlobalImg/link.png';

const Index: React.FC = (props: any) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  const { id } = history.location.query
  const { loading, dispatch, graphicsText } = props
  const { selectedKingdomId, selectedKingdomName } = graphicsText
  const [form] = Form.useForm();

  const initialDetailsState = {
    textImgList: [],                   // 封面
    imageTitle: '',                    // 标题
    outerChain: '',                    // 链接地址
  }
  const [detailsState, setDetailsState] = useState(initialDetailsState)
  const [textImgListType, setTextImgListType] = useState(1)          // 有无封面
  const [refreshPageState, setRefreshPageState] = useState(false)    // 刷新数据
  const [isHaveKingdomData, setIsHaveKingdomData] = useState(true)
  const [bottomTipsBarVisible, setBottomTipsBarVisible] = useState(true)
  const { drop, clear } = useAliveController()             // 清空缓存

  useEffect(() => {
    console.log('useEffect')
    if (id) {
      // 编辑图文
      imgTextInfoUpdateId()
    }
    getCreateAndJoinKingdomList()
  }, [])

  // 编辑图文
  const imgTextInfoUpdateId = () => {
    dispatch({
      type: 'graphicsText/imgTextInfoUpdateId',
      payload: {
        imageTextId: id,                                // 图文ID
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200 && content) {
        if (content.createUserId != UserInfo.friUserId) {
          Toast.show('您没有编辑权限')
          setTimeout(() => {
            goBack()
          }, 1000)
          return
        }
        // 回显
        dispatch({
          type: 'graphicsText/save',
          payload: {
            selectedKingdomId: content.kingdomId,
            selectedKingdomName: content.kingdomName,
          }
        })
        setDetailsState(content)
        if (content.textImgList && content.textImgList.length > 0) {
          setTextImgListType(2)
        }
        form.setFieldsValue({
          imageTitle: content.imageTitle || undefined,
          outerChainPaperwork: content.outerChainPaperwork || undefined,
          outerChain: content.outerChain || undefined,
        })
      } else {
        Toast.show(msg || '数据加载失败')
      }
    }).catch(err => {})
  }

  // 王国数据
  const getCreateAndJoinKingdomList = () => {
    dispatch({
      type: 'userInfoStore/getCreateAndJoinKingdomList',
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200 && content) {
        if (content['1'] || content['2']) {
          setIsHaveKingdomData(true)
        } else {
          setIsHaveKingdomData(false)
        }
      } else {
        setIsHaveKingdomData(false)
      }
    }).catch()
  }

  // 选择王国
  const selectKingdom = () => {
    if (isHaveKingdomData) {
      history.push('/CreateGraphicsText/SelectKingdom')
    }
  }

  // 输入标题
  const onChangeImageTitle = (value) => {
    console.log(value)
    setDetailsState({
      ...detailsState,
      imageTitle: value,
    })
  }

  // 输入链接地址
  const onChangeOuterChain = (value) => {
    console.log(value)
    setDetailsState({
      ...detailsState,
      outerChain: value,
    })
  }

  // 选择封面设置
  const onChangeTextImgListType = (value) => {
    setTextImgListType(value)
    if (value == 1) {
      setDetailsState({
        ...detailsState,
        textImgList: [],     // 封面数据
      })
    }
  }

  // 上传校验
  const beforeUpload = (file) => {
    const isSize = file.size / 1024 / 1024 < 15
    if (!isSize) {
      Toast.show('超过15M限制，不允许上传~')
      return false
    }

    const { name: fileName } = file || {}
    const suffix = fileName.substring(fileName.lastIndexOf('.')+1).toLowerCase()
    const isJpgOrPng = file.type === 'image/jpg' || file.type === 'image/jpeg' ||  file.type === 'image/png' || file.type === 'image/gif'
    const isSuffixByJpgOrPng = suffix === 'jpg' || suffix === 'jpeg' || suffix === 'png' || suffix === 'gif'
    if (!isJpgOrPng || !isSuffixByJpgOrPng) {
      Toast.show({content: '只能上传JPG、JPEG、PNG、GIF格式的图片~'})
      return false
    }
    return true
    // return new Promise((resolve, reject) => {
    //   const fileReaderBuffer = new FileReader();
    //   fileReaderBuffer.onload = async () => {
    //     const type = getFileType(fileReaderBuffer);
    //     if (type === 'unknown') {
    //       Toast.show('图片格式错误')
    //       reject()
    //       return;
    //     }
    //     if (type.includes('/heic')) {
    //       heic2any({ blob: file, toType: 'image/jpeg' }).then((blob) => {
    //         resolve(blob)
    //       }).catch((err) => {
    //         Toast.show('上传失败')
    //         reject()
    //       });
    //     } else {
    //       resolve(file)
    //     }
    //   };
    //   fileReaderBuffer.readAsArrayBuffer(file);
    // })
  };

  // 上传
  const onChangeByUpload = (info) => {
    console.log('uploadOnChange',info)
    if (info && !info.file.status) {
      return
    }

    if (info.file.status === 'uploading') {
      Toast.show({
        icon: 'loading',
        content: '',
        duration: 0,
      })
    }

    if (info && info.file.status === 'error') {
      Toast.clear()
      Toast.show('上传失败')
      return
    }

    if (info && info.file.status === 'done') {
      Toast.clear()
      const res = info.file.response || {}
      const { code, content, msg } = res
      if (code == 200) {
        setDetailsState({
          ...detailsState,
          textImgList: [{
            imageUrlShow: content && content.fileUrlView,
            isCover: 1,
          }]
        })
      } else {
        Toast.show(msg || '上传失败')
      }
    }
  }

  // 存草稿
  let submitDraft = () => {
    const values = form.getFieldsValue()
    editImgTextInfo(1, values)
  }
  submitDraft = useThrottle(submitDraft, 500)

  // 发布前
  let beforeSubmit = (isDisabled) => {
    if (isDisabled) {
      return
    }
    submit()
  }
  beforeSubmit = useThrottle(beforeSubmit, 500)

  // 发布
  const submit = () => {
    form.validateFields().then(values => {
      console.log('发布',values)
      editImgTextInfo(2, values)
    }).catch(err => {
      console.log('发布',err)
    })
  }

  // 提交数据
  const editImgTextInfo = async (saveType, values) => {
    Toast.show({
      icon: 'loading',
      content: '',
      duration: 0,
    })
    // 清除缓存
    await clear()
    dispatch({
      type: 'graphicsText/editImgTextInfo',
      payload: {
        forwardDescribe: null,                   // 转发描述
        id: id || null,                          // 图文ID
        outerChain: values.outerChain && values.outerChain.trim() || null,                                   // 链接地址
        outerChainPaperwork: values.outerChainPaperwork && values.outerChainPaperwork.trim() || '了解更多',      // 按钮文案
        imageTextId: null,                       // 图文ID  转发时存在
        imageTitle: values.imageTitle && values.imageTitle.trim() || null,     // 标题
        imageType: 3,                            // 类型
        textImgList: detailsState.textImgList,   // 图片信息
        isForward: 0,                            // 是否转发
        kingdomId: selectedKingdomId,            // 王国id
        saveType,                                // 提交类型
      }
    }).then(res => {
      Toast.clear()
      const { code, content, msg } = res
      if (code == 200 && (content || content == 0)) {
        if (saveType == 1) {
          Toast.show('保存成功')
          if (!id) {
            history.replace(`/CreateGraphicsText/CreateExternalLinks?id=${content}`)
            setRefreshPageState(!refreshPageState)
          }
        } else {
          history.replace('/Square?tabKey=2&publish=1&isAuditing=1')
        }
      } else {
        Toast.show(msg || '数据加载失败')
      }
    }).catch(err => {})
  }

  // 隐藏黄色提示条
  const bottomTipsBarHide = () => {
    setBottomTipsBarVisible(false)
  }

  // 返回
  const goBack = () => {
    if (history.length > 1) {
      history.goBack()
    } else {
      history.replace('/')
    }
  }

  const loadingImgTextInfoUpdateId = !!loading.effects['graphicsText/imgTextInfoUpdateId']

  return (
    <Spin spinning={loadingImgTextInfoUpdateId} wrapperClassName={styles.spin}>
      <NavBar
        title="发外链"
        RightRender={() => (
          <div className={styles.nav_bar_right}>
            <span className={styles.nar_bar_link_btn} onClick={submitDraft}>存草稿</span>
            <span className={classNames(styles.nar_bar_btn, {
              [styles.disabled]: !detailsState.imageTitle || !detailsState.outerChain,
            })} onClick={() => beforeSubmit(!detailsState.imageTitle || !detailsState.outerChain)}>提交</span>
          </div>
        )}
        bordered
      />
      <div className={styles.container}>
        {
          bottomTipsBarVisible &&
          <div className={styles.bottom_tips_bar}>
            为了维护良好的社区氛围，每篇文章和外链都会经过审核，请耐心等待，我们将尽快审核并发布你的文章。
            <div className={styles.bar_btn_wrap} onClick={bottomTipsBarHide}>
              <CloseOutlined />
            </div>
          </div>
        }
        <div className={styles.select_kingdom_box}>
          <div className={styles.select_left} onClick={selectKingdom}>
            <i className={styles.select_left_icon_1}></i>
            <span>选择关联王国</span>
            <i className={styles.select_left_icon_2}></i>
            <span className={styles.divider}></span>
          </div>
          {
            selectedKingdomName ?
              <div className={styles.select_right}>{selectedKingdomName}</div>
              :
              <div className={styles.select_right_default}>
                {
                  isHaveKingdomData ? '不关联王国，默认只发布在个人主页'
                    : '当前无可关联王国'
                }
              </div>
          }
        </div>
        <div className={styles.form_content}>
          <Form form={form}>
            <Form.Item
              name="imageTitle"
              rules={[
                { required: true, whitespace: true, message: '请输入标题' },
                { min: 2, max: 50, message: '请输入标题（2-50个字）' },
                { pattern: /^[~\s!！@#$￥%^…&*()_—+{}：:‘’“”《》？"|<>?`\-=[\];',.。，；（）、a-zA-Z0-9\u4e00-\u9fa5/]+$/, message: '标题不能包含特殊字符' },
              ]}
            >
              <Input autoComplete="off" maxLength={50} placeholder='填写标题会有更多的转化~ 2-50个字(必填)' onChange={onChangeImageTitle} />
            </Form.Item>
            <Form.Item
              name="outerChainPaperwork"
            >
              <Input autoComplete="off" maxLength={8} placeholder='填写跳转文案，2-8个字，默认：了解更多' />
            </Form.Item>
            <Form.Item
              name="outerChain"
              rules={[
                { required: true, whitespace: true, message: '请输入链接地址' },
                { max: 200, message: '不能超过200个字' },
                { pattern: /^(http|https):\/\/[a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,}(\/\S*)?$/, message: '请输入正确的链接地址' }
              ]}
            >
              <Input autoComplete="off" maxLength={200} placeholder='输入链接地址(必填)' onChange={onChangeOuterChain} />
            </Form.Item>

            <div className={styles.text_img_list_type_wrap}>
              <Radio.Group value={textImgListType} onChange={onChangeTextImgListType}>
                <Radio value={1}>无封面</Radio>
                <Radio value={2}>有封面</Radio>
              </Radio.Group>
            </div>

            <Form.Item
              noStyle
              shouldUpdate={true}
            >
              {
                ({getFieldValue}) => <>
                  {
                    textImgListType == 1 ?
                      <div className={styles.init_img_list}>
                        <div className={styles.init_bg}>
                          <img src={linkBgIcon} alt="" width={48} height={48} />
                        </div>
                        <div className={styles.init_info}>
                          <div className={styles.init_info_title}>
                            {
                              getFieldValue('imageTitle') && getFieldValue('imageTitle').trim() || '在标题位置填写的内容将在这里显示'
                            }
                          </div>
                          <div className={styles.init_info_btn}>
                            <img src={linkIcon} width={16} height={16} alt=""/>
                            <span>
                              {
                                getFieldValue('outerChainPaperwork') && getFieldValue('outerChainPaperwork').trim() || '了解更多'
                              }
                            </span>
                          </div>
                        </div>
                      </div>
                      :
                      <div className={styles.link_img_wrap}>
                        <Upload
                          headers={getHeaders()}
                          accept="image/*"
                          action={`/api/server/base/uploadFile?${stringify({ fileType: 23, userId: UserInfo?.friUserId })}`}
                          onChange={onChangeByUpload}
                          beforeUpload={beforeUpload}
                          showUploadList={false}
                        >
                          <div className={styles.upload_box}>
                            {
                              detailsState.textImgList && detailsState.textImgList[0] && detailsState.textImgList[0].imageUrlShow ?
                                <div className={styles.upload_img} style={{backgroundImage: `url(${detailsState.textImgList[0].imageUrlShow})`}} />
                                :
                                <div className={styles.add_img}>
                                  <img className={styles.init_upload_img} src={uploadImgIcon} alt="" />
                                  <div>添加封面</div>
                                </div>
                            }
                          </div>
                        </Upload>
                        <div className={styles.img_link_text}>
                          <img src={linkIcon} width={16} height={16} alt=""/>
                          <span>
                              {
                                getFieldValue('outerChainPaperwork') && getFieldValue('outerChainPaperwork').trim() || '了解更多'
                              }
                            </span>
                        </div>
                      </div>
                  }
                </>
              }
            </Form.Item>
          </Form>
        </div>
      </div>
    </Spin>
  )
}
export default connect(({ graphicsText, loading }: any) => ({ graphicsText, loading}))(Index)
