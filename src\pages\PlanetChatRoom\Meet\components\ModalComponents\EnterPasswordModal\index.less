.popup_container {
  :global {
    .adm-popup-body {
      border-radius: 16PX 16PX 0 0;
    }
  }
}

.container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: nowrap;
  flex-direction: column;
}

// 头部
.header_line {
  flex-shrink: 0;
  width: 100%;
  height: 28PX;
  display: flex;
  align-items: center;
  justify-content: center;
  .header_line_bar {
    width: 48PX;
    height: 4PX;
    background: #D0D4D7;
    border-radius: 4PX;
  }
}

.header_title {
  flex-shrink: 0;
  width: 100%;
  font-size: 17PX;
  color: #000;
  font-weight: 500;
  line-height: 24PX;
  text-align: center;
  padding-bottom: 24PX;
}

.content {
  padding: 0 16PX;
}

.contentBox {
  .pwdTitle {
    font-size: 14px;
    font-weight: 500;
    color: #666666;
    line-height: 16px;
    margin-bottom: 16px;
  }
  .stress {
    color: #FF5F57;
    margin-right: 3px;
  }
}

.PwdFlex {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .PwdInput {
    width: 56px;
    height: 68px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    opacity: 1;
    border: 1px solid #CCCCCC;
    font-size: 40px;
    justify-content: center;
    align-items: center;
    text-align: center;
  }

  .line_pwd {
    width: 22px;
    height: 1px;
    background: #CCCCCC;
    border-radius: 0px 0px 0px 0px;
    opacity: 1;
  }

}

.allowApplications_box {
  margin-top: 40px;

  .spin_box {
    margin-right: 10px;
  }

  .allowApplications_box_warp {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .allowApplications_box_text {
    font-weight: 400;
    font-size: 14px;
    color: #0095FF;
    line-height: 14px;
    text-align: center;
  }
}
