/**
 * @Description: 选择主持人
 */
import React, { useState, useEffect } from 'react'
import { connect } from 'umi'
import { Input } from 'antd-mobile';
import styles from './index.less';
import GoBackIcon from '@/assets/GlobalImg/go_back.png';
import SearchIcon from '@/assets/GlobalImg/search.png';
import noDataImg from '@/assets/GlobalImg/no_data.png';
import { Spin } from 'antd';
import { processNames, randomColor } from '@/utils/utils';

const Index: React.FC = (props: any) => {
  const { userInfoStore, dispatch, goBack, loading } = props;
  const { selectedCompere } = userInfoStore || {};
  const [list, setList] = useState([]); // 数据
  const [selectHost, setSelectHost] = useState({}); // 选中的主持人
  const [isHasData, setIsHasData] = useState(0); // 是否搜索出数据

  // 获取主持人列表
  const getListData = (val) => {
    dispatch({
      type: 'userInfoStore/searchUserListByQueryKey',
      payload: {
        queryKey: val && val.trim(),
      }
    }).then(res => {
      if(res && res.code == 200) {
        if(res.content && res.content.length) {
          setList(res.content);
          setIsHasData(0)
        } else {
          setList([]);
          setIsHasData(1)
        }
      }
    })
  }

  // 将仓库中的值赋值到state上
  useEffect(() => {
    setSelectHost(selectedCompere)
  },[selectedCompere])

  // input 搜索主持人
  const changeInputFn = (val) => {
    if(!val || !val.trim()) return setList([]);
    getListData(val)
  }

  // 选择事件
  const selectBtnFn = (item: Record<string,any>) => {
    // 判断选的是否一致, 修改已选择展示按钮
    if(item.id == selectHost?.id) {
      setSelectHost({});
    } else {
      setSelectHost(item)
    }
  }

  // 确定
  const submitFn = () => {
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        createModalVisible: false,
        selectedCompere: selectHost.id ? selectHost : null
      }
    })
  }

  const searchUserListByQueryKeyLoading = !!loading.effects['userInfoStore/searchUserListByQueryKey'] // loading
  return (
    <div className={styles.container}>
      <div className={styles.title_box}>
        <div className={styles.title_btn} onClick={()=>{
          dispatch({
            type: 'userInfoStore/setTaskListState',
            payload: { createModalVisible:false, }
          })
        }}>
          <img src={GoBackIcon} width={12} height={24} alt=""/>
        </div>
        <div className={styles.title}>选择主持人</div>
      </div>

      <div className={styles.search_box}>
        <div className={styles.search_content}>
          <div className={styles.search_icon}>
            <img src={SearchIcon} width={20} height={20} style={{display: 'block'}} alt=""/>
          </div>
          <div className={styles.search_input}>
            <Input
              placeholder="搜索主持人"
              clearable
              onChange={changeInputFn}
            />
          </div>
        </div>
      </div>
      <Spin spinning={searchUserListByQueryKeyLoading}>
        <div className={styles.data_box}>
          {list && list.length >= 1 ?
            <div className={styles.list_box}>
              {
                list && list.map((item:any) => {
                  return <div key={item.id} className={styles.item_box}>
                  <div className={styles.avatar}>
                    {
                      item.headUrlShow ?
                      <img src={item.headUrlShow} alt="" /> :
                      <div className={styles.no_comment_head} style={{background:randomColor(item?.id)}}>{processNames(item?.name)}</div>
                    }
                  </div>
                  <div className={styles.info_box}>
                    <div className={styles.info_1}>
                      <div className={styles.info_name} dangerouslySetInnerHTML={{__html: item.highlightName}}></div>
                      <div className={styles.info_phone}>{item.phone}</div>
                    </div>
                    <div className={styles.info_2}>
                      <span>{item.postTitleDictName}</span>
                      {item.organizationName ? <><span className={styles.lines}></span><span>{item.organizationName}</span></> : null}
                    </div>
                  </div>
                  {
                    <div
                      className={selectHost?.id === item.id ? styles.item_active_btn : styles.item_btn}
                      onClick={() => selectBtnFn(item)}
                    >
                      {selectHost?.id === item.id ? '已选择' : '选择'}
                    </div>
                  }
                </div>
                })
              }
            </div> : null
          }
          {
            isHasData == 1 ?
            <div className={styles.nodata}>
              <img src={noDataImg} alt="" />
              <div className={styles.empty_title}>暂无该搜索结果</div>
              <div className={styles.empty_msg}>请试试其他搜索关键词</div>
            </div> : null
          }
        </div>
      </Spin>
      <div className={styles.fixed_box}>
        <div className={styles.btn_box}>
          <div className={styles.btn} onClick={submitFn}>确定</div>
        </div>
      </div>
    </div>
  )
}

export default connect(({ userInfoStore, loading }: any) => ({userInfoStore, loading}))(Index)
