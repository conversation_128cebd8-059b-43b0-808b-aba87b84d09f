import React, {useState, useEffect, useRef} from 'react';
import { history,connect } from 'umi';
import styles from "./index.less";
import {Input, Form, Modal, Spin, message} from "antd";
const  { TextArea } = Input;
import { Select } from 'antd';

const { Option } = Select;
import {
  getFieldDecoratorByitemByLv3, scrollToTop,
  setFormValues
} from "@/pages/CreationOrthodontics/components/CreationFormUtils";
import {getToothBitInfoByToothPosition} from "@/utils/ToothSelect";
import ToothSelectQuality from "@/components/ToothSelectQuality";
import {editConsultationNodeAndStatus} from "@/services/CreationOrthodontics";
import {stringify} from "qs";
import {useDebounce, getArrailUrl} from "@/utils/utils";

const Step4: React.FC = (props) => {
  // 是否嵌套在5i5ya的iframe中
  const isInIframe = (self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya')

  const [ form] = Form.useForm();
  const { CreationOrthodontics, dispatch,loading } = props || {}
  const { medicalRecordJson,DictionaryData,DataBymedicalRecordJson } = CreationOrthodontics || {}  // 正畸病例字典结构
  const [ visibleBySelectParentModal,setVisibleBySelectParentModal ] = useState(null)
  const [ isSubmitLoading,setIsSubmitLoading ] = useState(null)
  const { location } = history || {}
  const { pathname,query } = location || {}
  const {
    id:consultationId,       // :consultationId 8dad6e1a68714dc7b1f908902ad2d4f4
    customerId,              // 客户id
    createUserId,            // 创建人id
    tenantId,                // 租户id
    orderCaseTemplate,       // "orderCaseTemplate": 0, -- 订单病例模板 1通用病例 2正畸病例
  } = DictionaryData || {}
  const {
    pageFrom, //  'ConsultationDetails'详情来的保持原逻辑
  } = query || {}

  // 11、正畸影像：
  const checkJson11  = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item)=>{return item.dictCode == '11'})
  // 12、其他影像：
  const checkJson12  = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item)=>{return item.dictCode == '12'})
  // 13、其他附件：
  const checkJson13  = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item)=>{return item.dictCode == '13'})

  useEffect(()=>{scrollToTop()},[]);

  const echoFormValue = ()=> {
    if (checkJson11 && checkJson12 && checkJson13) {
      let FormArr =
        [].concat(checkJson11.subsetList)
          .concat(checkJson12.subsetList)
          .concat(checkJson13.subsetList)
      setFormValues(FormArr,form)
    }
  }

  const onFormLayoutChangeBySet = async () => {
    await setIsSubmitLoading(null);
    await onFormLayoutChange();
  }

  const onFormLayoutChange = useDebounce((value) => {
    let valuesByForm = form.getFieldsValue();
    onFinish(valuesByForm);
  },3000);

  useEffect(() => {
    echoFormValue()
  },[DataBymedicalRecordJson])


  // 问题清单
  // getFieldDecoratorBy
  const getFieldDecoratorByProblems = ()=>{
    return (
      <>
        {checkJson11 && checkJson12 && [checkJson11,checkJson12,checkJson13].map((itemByLv1) => {
          return (
            <div key={itemByLv1.id} style={{marginBottom:'30px'}}>
              <div className={styles.title_span}> { itemByLv1.id == 7 && <span style={{color:'red'}}>*</span> } {itemByLv1.dictName}：</div>
                <div className={styles.item_value_content}>
                  {Array.isArray(itemByLv1.subsetList) && itemByLv1.subsetList.map((itemByLv2) => {
                    return getFieldDecoratorByitemByLv3({
                      item:itemByLv2,
                      form:form,
                      onChange:onFormLayoutChangeBySet
                    })
                  })}
                </div>
            </div>
          )
        })}
      </>
    )
  }

  const onFinish= (value)=>{
    const { errorFields } = value || {}

    let formDataArr = Object.keys(value).map((key) => {
      return { id: key,  inputContent: value[key] }
    })

    dispatch({
      type: 'CreationOrthodontics/saveDataByMedicalRecordJson',
      payload: {
        processNode:5,         // 提交病例
        formDataArr:formDataArr,
        isSubmit:true,
      }
    }).then(()=>{
      if(isSubmitLoading == 4) {  // 去确认-提交病例页面
        if(!errorFields || Array.isArray(errorFields) && errorFields.length == 0) {
          // 在5i5ya的iframe中
          if (isInIframe) {
            let postData = {
              dataType: 'pathname',                   // 页面地址onchange事件
              pathnameByChild: `/ConsultationModule/OrthodonticCasesDetail/${consultationId}`,           // 路由信息
              searchByChild: `?${stringify({
                type:2,
              })}`,  // 路由信息
            }
            console.log('子级发送数据：', postData, getArrailUrl())
            dispatch({ type:'CreationOrthodontics/clean' })
            window.parent.postMessage(postData, getArrailUrl())
          }else {
            history.replace(`/ConsultationModule/OrthodonticCasesDetail/${consultationId}?${
              stringify({
                type:2,
                pageFrom:pageFrom ? pageFrom+'Step5' : 'Step5'
              })
            }`)
          }

          dispatch({ type:'CreationOrthodontics/clean' })
        }else {
          setIsSubmitLoading(false);
        }
      }else if(isSubmitLoading == 3) { // 保存草稿
        message.success('保存草稿成功!')
        setIsSubmitLoading(false);
      }else if(isSubmitLoading == 2) { // 提交病例-弹出选择上级医生弹窗
        if(!errorFields || Array.isArray(errorFields) && errorFields.length == 0) {
          // 在5i5ya的iframe中
          if (isInIframe) {
            dispatch({
              type: 'CreationOrthodontics/editConsultationNodeAndStatus',
              payload: {
                consultationId: consultationId,
                tenantId:tenantId,
                customerId: customerId,
                type: 9,          // 2:正畸病例被查看, 9:正畸确认上级医生, 10:正畸审核驳回, 11:正畸审核通过, 12:正畸结束指导)
                // expertsId:id,     // [string] 是 上级医生ID
                // expertsName:name,  // [string] 是 上级医生名称
                auditStatus:null,  // [string] 是 审核状态(1通过、2驳回)
                auditOpinion:null, //[string] 是 输入内容
              }
            }).then(res => {
              const { code, content, msg } = res || {}
              if (code == 200) {
                const postData = {
                  dataType: 'pathname',       // 页面地址onchange事件
                  pathnameByChild: '/UserInfo',  // 路由信息
                  searchByChild: `?subTabKey=&tabKey=6`,  // 路由信息
                }
                console.log('子级发送数据：', postData, getArrailUrl())
                dispatch({ type:'CreationOrthodontics/clean' })
                window.parent.postMessage(postData, getArrailUrl())
              } else {
                message.error(msg || '数据加载失败')
              }
            })
          } else {
            // 弹出选择上级医生弹窗
            setVisibleBySelectParentModal(true)
          }
        }else {
          setIsSubmitLoading(false);
        }
      }else if(isSubmitLoading == 1) {  // 保存病例-返回到我的会诊列表页
        setIsSubmitLoading(false);
        // 在5i5ya的iframe中
        if (isInIframe) {
          const postData = {
            dataType: 'pathname',       // 页面地址onchange事件
            pathnameByChild: '/UserInfo',  // 路由信息
            searchByChild: `?subTabKey=&tabKey=6`,  // 路由信息
          }
          console.log('子级发送数据：', postData, getArrailUrl())
          dispatch({ type:'CreationOrthodontics/clean' })
          window.parent.postMessage(postData, getArrailUrl())
          return
        }
        dispatch({
          type: 'pcAccount/save',
          payload: {
            tabState: 6,
            subTabState: null,
          }
        })
        history.replace('/UserInfo')
      }

    })
  }

  return (
    <div className={styles.page_info}>
      <div className={styles.warp_content}>
        {/* 标题 */}
        <div className={styles.title_box}>
          <div>影像资料</div>
        </div>
        {/* 内容 */}
        <Form
          form={form}
          initialValues={{}}
          onValuesChange={onFormLayoutChangeBySet}
          onFinish={onFinish}
          onFinishFailed={onFinish}
        >
          <div className={styles.content_warp}>
            {getFieldDecoratorByProblems()}
          </div>
        </Form>

        {/* 上一步下一步 */}
        <Spin spinning={isInIframe && !!loading.effects['CreationOrthodontics/editConsultationNodeAndStatus']}>
          <div className={styles.submitWarp}>
            <div className={styles.submitBox}>
              <div
                onClick={()=>{
                  if (form) {
                    /* history.replace(`/CreationOrthodontics/Step4?${
                       stringify({consultationId,customerId,tenantId})
                     }`)*/
                    let value = form.getFieldsValue()
                    onFinish(value,false);

                    // 在5i5ya的iframe中
                    if (isInIframe) {
                      const postData = {
                        dataType: 'goBack',       // 页面地址onchange事件
                      }
                      console.log('子级发送数据：', postData, getArrailUrl())
                      window.parent.postMessage(postData, getArrailUrl())
                      return
                    }

                    history.replace(`/CreationOrthodontics/Step4?${stringify(history.location.query)}`);
                  }
                }}
                className={styles.submit_btn_Cancel}>上一步</div>

              {/* 保存草稿 提交病例 */}
              {orderCaseTemplate != 2 &&
                <>
                  <div
                    onClick={()=>{
                      if (form) {
                        setIsSubmitLoading(1)
                        form.submit()
                      }
                    }}
                    className={styles.submit_btn_Cancel}>
                    保存草稿
                  </div>
                  <div
                    onClick={()=>{
                      if (form) {
                        setIsSubmitLoading(2)
                        form.submit()
                      }
                    }}
                    className={styles.submit_btn_Enter}>
                    提交病例
                  </div>
                </>
            }

            {orderCaseTemplate == 2 &&
              <>
                <div
                  onClick={()=>{
                    if (form) {
                      setIsSubmitLoading(3);
                      form.submit();
                    }
                  }}
                  className={styles.submit_btn_Cancel}>
                  保存草稿
                </div>
                <div
                  onClick={()=>{
                    if (form) {
                      setIsSubmitLoading(4);
                      form.submit();
                    }
                  }}
                  className={styles.submit_btn_Enter}>
                  去确认
                </div>
              </>
            }
            </div>
          </div>
        </Spin>
      </div>
      <SelectParentModal
        dispatch={dispatch}
        CreationOrthodontics={CreationOrthodontics}
        loading={loading}
        visible={visibleBySelectParentModal}
        onOk={(superDoctorListSelect)=>{
          setVisibleBySelectParentModal(false)

          // 在5i5ya的iframe中
          if (isInIframe) {
            const postData = {
              dataType: 'pathname',       // 页面地址onchange事件
              pathnameByChild: '/UserInfo',  // 路由信息
              searchByChild: `?subTabKey=&tabKey=6`,  // 路由信息
            }
            console.log('子级发送数据：', postData, getArrailUrl())
            window.parent.postMessage(postData, getArrailUrl())
            return
          }

          // 设置pc tab页为设置
          dispatch({
            type: 'pcAccount/save',
            payload: {
              tabState: 6,
              subTabState: null,
            }
          })
          history.replace('/UserInfo')
        }}
        onCancel={()=>{
          setVisibleBySelectParentModal(false)
        }}
      ></SelectParentModal>
    </div>
  )
}

// 选择上级医生弹窗
export const SelectParentModal = (props: any) => {
  const { visible, onOk, onCancel,dispatch,CreationOrthodontics,loading } = props || {}
  const { superDoctorList,DictionaryData } = CreationOrthodontics || {}
  const [ superDoctorListSelect,setSuperDoctorListSelect] = useState(null)
  const {
    id:consultationId,       // :consultationId 8dad6e1a68714dc7b1f908902ad2d4f4
    customerId,              // 客户id
    createUserId,            // 创建人id
    tenantId,                // 租户id
  } = DictionaryData || {}
  useEffect(()=>{
    if(visible) {
      dispatch({
        type: 'CreationOrthodontics/getSuperDoctorList',
        payload: {
          tenantId: tenantId,
        }
      })
    }
  },[visible])

  useEffect(()=>{
    if(Array.isArray(superDoctorList) && superDoctorList.length > 0 && superDoctorList[0].id) {
      setSuperDoctorListSelect(superDoctorList[0].id)
    }
  },[superDoctorList])

  const editConsultationNodeAndStatus = async ()=>{
    let ObjSuperDoctorListSelect = superDoctorList.find((item)=>{
      return item.id == superDoctorListSelect
    })
    const { id,name } = ObjSuperDoctorListSelect || {}
    let res = await dispatch({
          type: 'CreationOrthodontics/editConsultationNodeAndStatus',
          payload: {
            consultationId: consultationId,
            tenantId:tenantId,
            customerId: customerId,
            type: 9,          // 2:正畸病例被查看, 9:正畸确认上级医生, 10:正畸审核驳回, 11:正畸审核通过, 12:正畸结束指导)
            expertsId:id,     // [string] 是 上级医生ID
            expertsName:name,  // [string] 是 上级医生名称
            auditStatus:null,  // [string] 是 审核状态(1通过、2驳回)
            auditOpinion:null, //[string] 是 输入内容
          }
    })
    const { code,content } = res || {}
    if (code == 200) { onOk() }
  }

  return (
    <Modal
      title="选择上级医生"
      open={visible}
      destroyOnClose={true}
      maskClosable={false}
      closable={true}
      onOk={()=>{ editConsultationNodeAndStatus(); }}
      onCancel={()=>{ onCancel() }}
      okText="确认"
      cancelText="取消"
      className={styles.select_tooth}
      width={474}
    >
      <Spin spinning={
        !!loading.effects['CreationOrthodontics/getSuperDoctorList'] ||
        !!loading.effects['CreationOrthodontics/editConsultationNodeAndStatus']
      }>
        <div>
          <div className={styles.Modal_title}>请选择上级医生审核您的方案</div>
          <div>
            <Select
              value={superDoctorListSelect}
              style={{ width: '100%' }}
              onChange={(value)=>{ setSuperDoctorListSelect(value); }}
            >
              {Array.isArray(superDoctorList) && superDoctorList.map((item,index)=>{
                return (
                  <Option key={index} value={item.id}>{item.name}</Option>
                )
              })}
            </Select>
          </div>
        </div>
      </Spin>
    </Modal>
  )
}

export default connect(({ CreationOrthodontics,pcAccount, loading }: any) => ({
  CreationOrthodontics,pcAccount, loading
}))(Step4)

