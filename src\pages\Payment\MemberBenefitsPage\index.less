@import (reference) '~antd/es/style/themes/index';

.containerByPC {
  .Float_box {
    position: fixed;
    top: 50%;
    right: 12px;
    z-index: 888;
    transform: translateY(-50%);
    display:block !important;
    img {
      width: 42px;
      height: auto;
      margin-bottom: 16px;
      cursor: pointer;
    }
  }

  .Web1 {
    width: 100%;
    height: 800px;
    background-image: url('https://static.jwsmed.com/public/DigitalHealth/Business/assets/MemberBenefitsPage/solution_bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    .pageTitle {
      margin-bottom: 16px;
      padding-top: 197px;
      color: #ffffff;
      font-weight: 600;
      font-size: 80px;
      line-height: 94px;
      text-align: center;
    }

    .solutionDesc {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 63px;
      color: #ffffff;
      font-weight: 400;
      font-size: 32px;
      line-height: 38px;

      .web1_text {
        margin-bottom: 8px;
      }
    }

    .smallestitemWrap {
      display: flex;
      justify-content: space-between;
      box-sizing: border-box;
      width: 1190px;
      margin: 0 auto;
      padding: 0 80px;

      .smallestitemItem {
        text-align: center;

        .smallestitem_num {
          margin-bottom: 12px;
          color: #ffffff;
          font-weight: bold;
          font-size: 44px;
          line-height: 52px;
        }

        .special_text {
          margin-left: 8px;
          color: #ffffff;
          font-weight: 400;
          font-size: 18px;
          line-height: 21px;
        }

        .smallestitem_desc {
          color: #ffffff;
          font-weight: 400;
          font-size: 18px;
          line-height: 21px;
        }
      }
    }

    .space-y-222 > *:not(:first-child) {
      margin-top: 222px;
    }

    .section_2 {
      padding-left: 32px;
      background-color: #ffffffe6;
      backdrop-filter: blur(4px);
    }

    .image_2 {
      width: 258px;
      height: 35px;
    }

    .space-x-70 > *:not(:first-child) {
      margin-left: 70px;
    }

    .group {
      margin-top: 20px;
    }

    .space-x-54 > *:not(:first-child) {
      margin-left: 54px;
    }

    .group_2 {
      width: 62px;
    }

    .font_1 {
      color: #ef441f;
      font-size: 16px;

      line-height: 14px;
    }

    .text {
      color: #000000;
      font-size: 15px;
    }

    .divider {
      height: 4px;
      background-color: #5291e9;
    }

    .font_2 {
      color: #000000;
      font-size: 16px;

      line-height: 14px;
    }

    .text_2 {
      font-size: 15px;
    }

    .text_3 {
      font-size: 15px;
    }

    .space-x-40 > *:not(:first-child) {
      margin-left: 40px;
    }

    .text_4 {
      font-size: 15px;
    }

    .image {
      width: 183px;
      height: 53px;
    }

    .group_3 {
      padding-right: 204px;
      padding-left: 212px;
    }

    .text_5 {
      color: #ffffff;
      font-weight: 600;
      font-size: 80px;

      line-height: 74.5px;
    }

    .group_4 {
      margin-top: 36px;
    }

    .space-y-8 > *:not(:first-child) {
      margin-top: 8px;
    }

    .font_3 {
      color: #ffffff;
      font-size: 32px;

      line-height: 39px;
    }

    .text_6 {
      line-height: 38px;
    }

    .text_7 {
      line-height: 38px;
    }

    .group_5 {
      margin-top: 70px;
    }

    .font_4 {
      color: #ffffff;
      font-weight: 700;
      font-size: 44px;

      line-height: 32px;
    }

    .group_6 {
      margin-left: 76px;
    }

    .space-x-6 > *:not(:first-child) {
      margin-left: 6px;
    }

    .text_8 {
      line-height: 31.5px;
    }

    .font_5 {
      color: #ffffff;
      font-size: 18px;

      line-height: 16.5px;
    }

    .text_13 {
      margin-top: 20px;
      opacity: 0.9;
    }

    .text_9 {
      margin-left: 74px;
    }

    .text_10 {
      margin-left: 70px;
    }

    .text_11 {
      margin-left: 102px;
    }

    .text_12 {
      margin-left: 112px;
    }

    .group_7 {
      padding-left: 8px;
    }

    .group_8 {
      margin-left: 86px;
    }

    .space-x-72 > *:not(:first-child) {
      margin-left: 72px;
    }

    .text_14 {
      opacity: 0.8;
    }

    .text_16 {
      margin-left: 96px;
    }

    .text_15 {
      line-height: 17px;
    }

    .space-y-14 > *:not(:first-child) {
      margin-top: 14px;
    }

    .space-y-22 > *:not(:first-child) {
      margin-top: 22px;
    }
  }

  .Web2 {
    box-sizing: border-box;
    width: 100%;
    margin-top: 60px;
    padding: 0 170px;
    padding-bottom: 53px;

    .group_9 {
      margin-top: 74px;
      padding: 0 12px;
    }

    .web2_title {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      height: 83px;
      color: #2f3745;
      font-weight: 600;
      font-size: 42px;
      line-height: 49px;
      text-align: center;

      &::after {
        display: inline-block;
        width: 48px;
        height: 8px;
        background: #267cfd;
        border-radius: 22px 22px 22px 22px;
        box-shadow: 0 4px 4px 0 rgb(38 124 253 / 10%);
        content: '';
      }
    }

    .space-x-14 > *:not(:first-child) {
      margin-left: 14px;
    }

    .group_10 {
      margin-bottom: 3px;
    }

    .group_11 {
      width: 156px;
    }

    .space-y-16 > *:not(:first-child) {
      margin-top: 16px;
    }

    .section_3 {
      padding: 4px;
      background-color: #267cfd;
    }

    .image_3 {
      width: 146px;
      height: 146px;
      border-radius: 1px;
    }

    .font_7 {
      color: #ffffff;
      font-size: 14px;

      line-height: 16.5px;
    }

    .text_17 {
      margin: 6px 4px 0 2px;
      line-height: 17px;
      text-align: center;
    }

    .image_7 {
      width: 146px;
      height: 146px;
    }

    .image_6 {
      width: 450px;
      height: 571px;
      margin-top: 110px;
    }

    .space-y-55 > *:not(:first-child) {
      margin-top: 55px;
    }

    .image_4 {
      width: 48px;
      height: 8px;
      margin-left: 62px;
      border-radius: 22px;
      filter: drop-shadow(0px 4px 2px #267cfd1a);
    }

    .image_5 {
      width: 588px;
      height: 600px;
    }
  }

  .Web3 {
    width: 100%;

    .doctor_bg_content {
      width: 100%;
      height: 506px;
      padding-top: 48px;
      background: url('https://static.jwsmed.com/public/DigitalHealth/Business/assets/MemberBenefitsPage/doctor_bg.png');
      background-size: 100% 100%;
    }

    .box {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .Web3_font_6 {
      color: #2f3745;
      font-weight: 600;
      font-size: 42px;

    }

    .Web3_text_18 {
      margin-bottom: 16px;
      color: #ffffff;
    }

    .line_2 {
      width: 48px;
      height: 8px;
      margin-bottom: 24px;
      background: #f0f6ff;
      border-radius: 8px;
    }

    .text_3 {
      width: 645px;
      margin-bottom: 55px;
      color: #ffffff;
      font-weight: 400;
      font-size: 20px;
      line-height: 23px;
      text-align: center;

      .text_3_br {
        display: none;
      }
    }

    .Bg_white {
      width: 100%;
      height: 320px;
    }

    .wrap_list {
      display: flex;
      justify-content: space-between;
      width: 1101px;
      margin: -240px auto;

      .wrap_list_item {
        width: 264px;
        height: 358px;
        background: #ffffff;
        border-radius: 2px 2px 2px 2px;
        box-shadow: 0px 6px 26px 0px rgba(15, 27, 49, 0.1);

        .wrap_list_title_img {
          position: relative;
          top: -20px;
          display: flex;
          justify-content: center;
          width: 100%;

          img {
            width: 71px;
            height: 60px;
          }
        }

        .wrap_list_item_content {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-between;
          box-sizing: border-box;
          height: 300px;
          padding: 0 20px 20px;

          .wrap_list_item_box {
          }

          .text_1 {
            margin-bottom: 8px;
            color: #2f3745;
            font-weight: 500;
            font-size: 18px;

            line-height: 21px;
            text-align: center;
          }

          .text_2 {
            color: #555c6a;
            font-weight: 400;
            font-size: 14px;

            line-height: 16px;
          }

          .image {
            width: 100%;
            height: 188px;

            img {
              width: 100%;
              height: auto;
            }
          }
        }
      }
    }
  }

  .Web4 {
    width: 100%;
    min-height: 200px;

    .font_6 {
      margin-bottom: 16px;
      color: #2f3745;
      font-weight: 600;
      font-size: 42px;
      line-height: 39px;
    }

    .title_advantage {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      height: 83px;
      color: #2f3745;
      font-weight: 600;
      font-size: 42px;
      line-height: 49px;
      text-align: center;

      &::after {
        display: inline-block;
        width: 48px;
        height: 8px;
        background: #267cfd;
        border-radius: 22px 22px 22px 22px;
        box-shadow: 0 4px 4px 0 rgb(38 124 253 / 10%);
        content: '';
      }
    }

    .bg_content {
      position: relative;
      display: flex;
      justify-content: center;
      width: 100%;
      height: 716px;
      margin-top: 48px;
      background: url('https://static.jwsmed.com/public/DigitalHealth/Business/assets/MemberBenefitsPage/advantage_bg.png')
        no-repeat;
      background-size: 100% 100%;

      .web4_bg_wrapper {
        position: relative;
        width: 398px;
        height: 716px;

        .img_content_wrap {
          display: flex;
          align-items: center;
          width: 398px;
          height: 100%;

          img {
            width: 100%;
            height: auto;
          }
        }

        .web4_text_box {
          position: absolute;
          box-sizing: border-box;
          width: 383px;
          height: auto;
          padding: 16px;
          background: #ffffff;
          border-radius: 12px 12px 12px 12px;

          .web4_img_title {
            margin-bottom: 8px;
            color: #2f3745;
            font-weight: 500;
            font-size: 20px;

            line-height: 23px;
          }

          .web4_img_text {
            color: #a2a8b2;
            font-weight: 400;
            font-size: 16px;

            line-height: 19px;
          }

          &.web4_text_box1 {
            top: 60px;
            left: 50%;
            transform: translateX(-50%);
          }

          &.web4_text_box2 {
            top: 50%;
            left: -85%;
            transform: translateY(-75%);
          }

          &.web4_text_box3 {
            top: 50%;
            right: -85%;
            transform: translateY(-75%);
          }

          &.web4_text_box4 {
            bottom: 100px;
            left: -75%;
          }

          &.web4_text_box5 {
            right: -75%;
            bottom: 100px;
          }
        }
      }
    }
  }

  .Web5 {
    padding-top: 60px;

    .bg {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      height: 83px;
      color: #2f3745;
      font-weight: 600;
      font-size: 42px;
      line-height: 49px;
      text-align: center;

      &::after {
        display: inline-block;
        width: 48px;
        height: 8px;
        background: #267cfd;
        border-radius: 22px 22px 22px 22px;
        box-shadow: 0 4px 4px 0 rgba(38, 124, 253, 0.1);
        content: '';
      }
    }

    .banner {
      display: flex;
      justify-content: space-between;
      width: 1100px;
      margin: 58px auto 60px;

      .web5_list_box {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 289px;

        .web5_list_img {
          width: 62px;
          height: 62px;
          margin-bottom: 20px;

          img {
            width: 100%;
            height: auto;
          }
        }

        .web5_list_item {
          text-align: center;

          .web5_list_price {
            margin-bottom: 5px;
            color: #2f3745;

            font-weight: 500;
            font-size: 20px;
            line-height: 23px;
          }

          .web5_list_text {
            color: #555c6a;

            font-weight: 400;
            font-size: 16px;
            line-height: 19px;
          }
        }
      }
    }

    .content {
      padding: 60px 0 40px;
      background-color: #f9f9f9;

      .title {
        color: #2f3745;
        font-size: 24px;
        text-align: center;

        .lineHeight {
          color: #267cfd;
        }
      }

      .subTitle {
        color: #888;
        font-size: 18px;
        text-align: center;
      }

      .table {
        width: 1100px;
        margin: 0 auto;
        margin-top: 36px;
        background-color: #fff;

        .tableTitle {
          color: #2f3745;
          font-size: 16px;
          line-height: 19px;
          background-color: #eaeaf0;
        }

        tr {
          height: 65px;
          border-bottom: 1px solid #eee;
        }

        td {
          padding-left: 35px;
          color: #555c6a;
          font-size: 16px;
          line-height: 19px;

          &.heightLight {
            color: #ef441f;
          }
        }
      }

      .subTip {
        height: 22px;
        margin-top: 16px;
        color: #555c6a;
        font-size: 16px;
        line-height: 19px;
        text-align: center;
      }

      .button {
        position: relative;
        display: block;
        width: 350px;
        height: 52px;
        margin: 24px auto;
        color: #fff;
        font-size: 20px;
        line-height: 52px;
        text-align: center;
        text-indent: -20px;
        background: #267cfd;
        border: 1px solid #267cfd;
        cursor: pointer;

        &::after {
          position: absolute;
          top: 22px;
          right: 80px;
          display: block;
          width: 8px;
          height: 8px;
          border: 1px solid #fff;
          border-bottom-color: transparent;
          border-left-color: transparent;
          transform: rotateZ(45deg);
          content: '';
        }
      }
    }
  }

  .Web6 {
    width: 1100px;
    height: 774px;
    margin: 0 auto;
    padding: 60px 0 80px;
    background: #fff;

    .web6_title {
      width: 100%;
      margin-bottom: 36px;
      color: #2f3745;
      font-weight: 500;
      font-size: 30px;

      line-height: 35px;
      text-align: center;
    }

    .web6_content {
      display: flex;
      flex-wrap: wrap;
      width: 100%;

      .web6_img_1 {
        flex-shrink: 0;
        width: 50%;
        height: 278px;
        background: url('https://static.jwsmed.com/public/DigitalHealth/Business/assets/MemberBenefitsPage/jc_img_1.png')
          no-repeat;
        background-size: 100% 100%;
      }

      .web6_img_2 {
        flex-shrink: 0;
        width: 50%;
        height: 278px;
        padding-top: 87px;
        padding-left: 22px;
        color: #2f3745;
        font-weight: 500;
        font-size: 32px;

        line-height: 38px;
        background: url('https://static.jwsmed.com/public/DigitalHealth/Business/assets/MemberBenefitsPage/jc_img_2.png')
          no-repeat;
        background-size: 100% 100%;
      }

      .web6_img_3,
      .web6_img_4,
      .web6_img_5 {
        flex-shrink: 0;
        width: 33.3%;
        height: 278px;
        padding-top: 87px;
        padding-left: 22px;
        color: #2f3745;
        font-weight: 500;
        font-size: 32px;

        line-height: 38px;
        background: url('https://static.jwsmed.com/public/DigitalHealth/Business/assets/MemberBenefitsPage/jc_img_3.png')
          no-repeat;
        background-size: 100% 100%;
      }

      .web6_img_4 {
        flex-shrink: 0;
        width: 33.3%;
        height: 278px;
        background: url('https://static.jwsmed.com/public/DigitalHealth/Business/assets/MemberBenefitsPage/jc_img_4.png')
          no-repeat;
        background-size: 100% 100%;
      }

      .web6_img_5 {
        flex-shrink: 0;
        width: 33.3%;
        height: 278px;
        background: url('https://static.jwsmed.com/public/DigitalHealth/Business/assets/MemberBenefitsPage/jc_img_5.png')
          no-repeat;
        background-size: 100% 100%;
      }
    }
  }

  .Web7 {
    width: 100%;
    height: 504px;
    margin: 0 auto;
    padding: 80px 0;
    background: #f9f9f9;

    .web7_title {
      margin-bottom: 11px;
      color: #2f3745;
      font-weight: 500;
      font-size: 30px;

      line-height: 35px;
      text-align: center;
    }

    .web7_content_pc {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      width: 1100px;
      margin: 0 auto;

      .web7_img {
        flex-shrink: 0;
        width: 20%;
        height: 72px;
        margin-top: 25px;
        text-align: center;

        img {
          width: 200px;
          height: 92px;
        }
      }

      .web7_img_text {
        flex-shrink: 0;
        width: 60%;
        height: 72px;
        margin-top: 25px;
        text-align: center;

        img {
          width: 651px;
          height: 92px;
        }
      }
    }

    .web7_content_h5 {
      display: none;
    }
  }
  .dentalAssistantBanner {
    height: 320px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    h3 {
      font-size: 24px;
    }
    h1 {
      color: #9fc8eb;
      font-size: 50px;
    }
    h2 {
      font-size: 28px;
    }
    p {
      width: 85%;
      font-size: 16px;
    }
  }
  .dentalAssistantInterest {
    width: 1200px;
    height: auto;
    margin: 0 auto;
    padding-top: 60px;
    .dentalAssistantInterestItem {
      padding-bottom: 50px;
      h3 {
        font-size: 28px;
        font-weight: 600!important;
        margin-bottom: 0;
        i {
          font-size: 32px;
        }
      }
      h2 {
        font-size: 20px;
        font-weight: 500!important;
        margin-bottom: 10px;
      }
      .InterestItemPart1 {
        img {
          transition: 0.3s;
        }
        width: 50%;
        overflow: hidden;
      }
      .InterestItemPart2 {
        width: 50%;
        padding-left: 40px;
        background: #f9f9f9;
        .InterestItemSubhead {
          margin-bottom: 10px;
          font-size: 16px;
          white-space: pre-wrap;
        }
        i,
        .InterestLink {
          font-size: 20px;
        }
        i {
          margin-bottom: 10px;
        }
        > span {
          font-size: 16px;
        }
        .InterestLink {
          margin-bottom: 0;
          i {
            display: inline-block;
            width: 32px;
            height: 32px;
          }
          a {
            display: inline;
            margin: 10px 20px 0 0;
            color: #203b83;
          }
        }
      }
    }
    .dentalAssistantInterestItem:hover {
      .InterestItemPart1 img {
        transform: scale(1.05);
      }
    }
  }
  .Web8 {
    width: 1100px;
    height: auto;
    margin: 0 auto;
    padding-bottom: 60px;
    background: #fff;

    .web8_tab {
      display: none;
    }

    .web8_title {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      height: 83px;
      margin-top: 60px;
      margin-bottom: 58px;
      color: #2f3745;
      font-weight: 600;
      font-size: 42px;

      line-height: 49px;
      text-align: center;

      &::after {
        display: inline-block;
        width: 48px;
        height: 8px;
        background: #267cfd;
        border-radius: 22px 22px 22px 22px;
        box-shadow: 0 4px 4px 0 rgba(38, 124, 253, 0.1);
        content: '';
      }
    }
    .web8_content_wrap {
      width: 100%;
      position:relative;
      .web8_modal {
        position: absolute;
        z-index:9999;
        top:50%;
        left:50%;
        width: 440px;
        height: 440px;
        margin-top:0px;
        margin-left: -220px;
        background:#fff;
        border-radius: 2px 2px 2px 2px;
        .consult_customer_title {
          position: relative;
          box-sizing: border-box;
          height: 133px;
          background: linear-gradient(180deg, #eff8ff 0%, rgba(255, 255, 255, 0) 100%);
          .cctitle {
            padding-top: 72px;
            color: #191919;
            font-weight: 600;
            font-size: 32px;
            line-height: 38px;
            text-align: center;
          }

          .ccicon {
            position: absolute;
            top: 24px;
            right: 24px;
            width: 32px;
            height: 32px;
            span {
              display: inline-block;
              width: 18px;
              height: 2px;
              background: #000000;
            }
            span:nth-of-type(1) {
              position: relative;
              top: 0px;
              left: 10px;
              transform: rotate(45deg);
            }
            span:nth-of-type(2) {
              position: relative;
              top: -22px;
              left: 10px;
              transform: rotate(-45deg);
            }
          }
        }
        .consult_customer_con {
          display: flex;
          justify-content: center;
          margin-top: 8px;
          img {
            width: 234px;
            height: 234px;
          }
        }
      }
    }
    .web8_content {
      display: flex;
      justify-content: space-between;
      width: 100%;
      margin-bottom: 67px;



      .web8_left_content,
      .web8_right_content {
        transition: all 0.2s linear;
        width: 527px;
        height: auto;
        padding-bottom: 60px;
        background: #ffffff;
        border-radius: 2px 2px 2px 2px;
        box-shadow: 0px 6px 26px 0px rgba(15, 27, 49, 0.1);
        .web8_left_title,
        .web8_right_title {
          span {
            margin-left: 20px;
            color: #2f3745;
            font-weight: 500;
            font-size: 22px;
          }
        }
        .web8_left_title {
          box-sizing: border-box;
          width: 100%;
          height: 104px;
          padding: 32px 0 24px 32px;
          color: #2f3745;
          font-weight: 600;
          font-size: 36px;
          line-height: 42px;
          background: url('https://static.jwsmed.com/public/DigitalHealth/Business/assets/MemberBenefitsPage/payment_1.png')
            no-repeat;
          background-size: 100% 100%;
        }
        .web8_right_title {
          box-sizing: border-box;
          width: 100%;
          height: 104px;
          padding: 32px 0 24px 32px;
          color: #2f3745;
          font-weight: 600;
          font-size: 36px;
          line-height: 42px;
          background: url('../../../assets/Payment/payment.png') no-repeat;
          background-size: 100% 100%;
        }
      }


      .web8_right_content {
        padding-bottom: 26px;
      }
      .web8_left_content:hover,.web8_right_content:hover {
        transform: translateY(-10px);
      }
      .web8_list_box {
        padding-top: 26px;
        padding-right: 20px;
        padding-left: 30px;
        .web8_list_item {
          margin-bottom: 24px;
          h4 {
            margin-bottom: 12px;
            color: #2f3745;
            font-weight: 600;
            font-size: 18px;
          }
          .web8_item_content {
            .memberitem {
              display: flex;
              align-items: center;
              justify-content: space-between;
              height: 22px;
              margin-bottom: 0;
              font-size: 16px;
              line-height: 22px;
              div:nth-child(1) {
                i {
                  display: inline-block;
                  width: 8px;
                  height: 8px;
                  margin-right: 8px;
                  font-style: normal;
                  border-radius: 50%;
                }
              }
              div:nth-child(2) {
                span:nth-of-type(1) {
                  color: #ef441f;
                  font-weight: 600;
                  font-size: 22px;
                }

                span:nth-of-type(2) {
                  color: #aaaaaa;
                  font-size: 16px;
                  font-style: normal;
                }
              }
              .fs16 {
                font-size: 16px !important;
              }
              .fsC {
                font-size: 22px !important;
              }
              .disabledType {
                display: inline-block;
                color: rgba(47, 55, 69, 0.5);
                text-decoration: line-through;
              }
              .type1 {
                i {
                  background: #267cfd;
                }
              }
              .type2 {
                i {
                  background: #f1bc00;
                }
              }
              .type3 {
                i {
                  background: #28e75e;
                }
              }
              .type4 {
                i {
                  background: #9e80f7;
                }
              }
              .type5 {
                i {
                  background: #ff7b72;
                }
              }
            }
            > div {
              margin-bottom: 12px;
              > i {
                display: inline-block;
                padding-left: 16px;
                color: #666666;
                font-size: 14px;
                font-style: normal;
              }
            }
          }
        }
      }

      .web8_price_content {
        display: flex;
        margin-top: 8px;
        margin-bottom: 48px;
        padding-left: 32px;

        .web8_price_item {
          display: flex;
          align-items: center;
          margin-right: 32px;
          color: #999999;
          font-weight: 400;
          font-size: 24px;

          line-height: 28px;

          .unit {
            position: relative;
            top: 5px;
            margin-right: 8px;
            color: #ef441f;
            font-weight: 500;
            font-size: 32px;

            line-height: 38px;
          }

          .money {
            margin-right: 8px;
            color: #ef441f;
            font-weight: bold;
            font-size: 48px;

            line-height: 56px;
          }
        }
      }

      .btn {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        cursor: pointer;

        span {
          display: flex;
          box-sizing: border-box;
          padding: 15px 96px;
          color: #ffffff;
          font-weight: 400;
          font-size: 20px;

          line-height: 23px;
          background: #013396;
          border-radius: 50px;
          box-shadow: 0px 4px 6px 0px rgba(38, 124, 253, 0.28);
        }
      }

      .tips {
        margin-top: 12px;
        color: #fb6535;
        font-weight: 400;
        font-size: 16px;

        line-height: 19px;
        text-align: center;
      }
    }

    .web8_apply_member {
      margin-top: 67px;

      .web8_member_title {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        height: 83px;
        margin-top: 60px;
        margin-bottom: 58px;
        color: #2f3745;
        font-weight: 600;
        font-size: 42px;

        line-height: 49px;
        text-align: center;

        &::after {
          display: inline-block;
          width: 48px;
          height: 8px;
          background: #267cfd;
          border-radius: 22px 22px 22px 22px;
          box-shadow: 0 4px 4px 0 rgba(38, 124, 253, 0.1);
          content: '';
        }
      }

      .web8_member_content {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 24px;
        background: #ffffff;
        border-radius: 2px 2px 2px 2px;
        box-shadow: 0px 6px 26px 0px rgba(15, 27, 49, 0.1);

        .web8_member_content_text {
          color: #555c6a;
          font-weight: 400;
          font-size: 16px;

          line-height: 28px;
        }

        .web8_member_copy_btn {
          display: block;
          margin-top: 16px;
          padding: 2px 8px;
          color: #555c6a;
          font-weight: 400;
          font-size: 14px;

          line-height: 24px;
          border: 1px solid #cccccc;
          border-radius: 2px 2px 2px 2px;
          cursor: pointer;
        }
      }
    }

    .web8_FAQ_content {
      margin-top: 80px;

      .FAQ_title {
        margin-bottom: 20px;
        color: #2f3745;
        font-weight: 900;
        font-size: 20px;
        line-height: 23px;
      }

      .FAQ_box {
        margin-bottom: 16px;

        .FAQ_list {
          display: flex;
          justify-content: space-between;
          width: 100%;
          padding: 16px 37px;
          color: #2f3745;
          font-weight: 500;
          font-size: 18px;

          background: linear-gradient(180deg, rgba(249, 249, 249, 0) 0%, #f9f9f9 100%);
          border-radius: 2px 2px 2px 2px;
          cursor: pointer;

          .arrow {
            width: 30px;
            height: 30px;
            cursor: pointer;

            img {
              width: 100%;
              height: auto;
            }
          }
        }

        .FAQ_text {
          padding: 20px 37px;
          color: #000000;
          font-weight: 400;
          font-size: 16px;

          line-height: 19px;
        }
      }
    }
  }

  .PaymentBtn_box {
    display: none;
  }
}

.containerByMobile {
  padding-top: 44px;
  .Float_box {
    display: none;
  }

  .Web1 {
    box-sizing: border-box;
    width: 100%;
    height: 313px;
    padding: 0 19px;
    background-image: url('https://static.jwsmed.com/public/DigitalHealth/Business/assets/MemberBenefitsPage/solution_bg_phone.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    .pageTitle {
      margin-bottom: 12px;
      padding-top: 43px;
      color: #ffffff;
      font-weight: 500;
      font-size: 24px;
      line-height: 28px;
      text-align: center;
    }

    .solutionDesc {
      box-sizing: border-box;
      margin-bottom: 24px;
      color: #ffffff;
      font-weight: 400;
      font-size: 15px;
      line-height: 18px;
      text-align: center;

      .web1_text {
        display: none;
      }
    }
    .smallestitemWrap {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      box-sizing: border-box;
      width: 100%;
      padding: 0 21px;

      .smallestitemItem {
        min-width: 26%;
        margin-bottom: 20px;
        color: #fff;
        color: #ffffff;
        font-weight: bold;
        font-size: 24px;
        line-height: 28px;
        text-align: center;

        .smallestitem_num {
          margin-bottom: 4px;
          color: #ffffff;
          font-weight: bold;
          font-size: 24px;
          line-height: 28px;
        }

        .special_text {
          margin-left: 8px;
          color: #ffffff;
          font-weight: 400;
          font-size: 18px;
          line-height: 21px;
        }

        .smallestitem_desc {
          color: #ffffff;
          font-weight: 400;
          font-size: 12px;
          line-height: 14px;
        }
      }
    }
  }

  .Web2 {
    margin-top: 24px;
    padding: 0 12px;

    .group_9 {
      margin: 0;
    }

    .web2_title {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      height: 40px;
      color: #2f3745;
      font-weight: 600;
      font-size: 20px;
      line-height: 23px;
      text-align: center;

      &::after {
        display: inline-block;
        width: 32px;
        height: 4px;
        background: #267cfd;
        border-radius: 22px 22px 22px 22px;
        box-shadow: 0 4px 4px 0 rgb(38 124 253 / 10%);
        content: '';
      }
    }

    .image_6 {
      width: 100%;
      height: auto;
      margin-top: 0;
    }
    .image_5 {
      width: 100%;
      height: auto;
    }

    .space-x-14 {
      flex-direction: column;

      .space-y-55 {
        margin-top: 24px;
        margin-left: 0;
      }
    }
  }

  .Web3 {
    width: 100%;
    padding-top: 24px;

    .doctor_bg_content {
      width: 100%;
      height: 338px;
      padding-top: 24px;
      background: url('https://static.jwsmed.com/public/DigitalHealth/Business/assets/MemberBenefitsPage/doctor_bg.png');
      background-size: 100% 100%;
    }

    .box {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .Bg_white {
      display: none;
    }

    .wrap_list {
      flex-wrap: wrap;
      width: 100%;
      margin: 0;
      margin-top: -140px;
      padding: 0 12px;

      .wrap_list_item {
        width: 100%;
        height: auto;
        background: #fff;
        box-shadow: 0 6px 26px 0 rgba(15, 27, 49, 0.1);

        .wrap_list_title_img {
          position: relative;
          top: -30px;
          display: flex;
          justify-content: center;
          width: 100%;

          img {
            width: 94px;
            height: 80px;
          }
        }

        &:not(:last-child) {
          margin-bottom: 50px;
        }

        .wrap_list_item_content {
          height: auto;
          padding: 0 24px 24px;

          .text_1 {
            margin-bottom: 16px;
            color: #2f3745;
            font-weight: 500;
            font-size: 20px;
            line-height: 23px;
            text-align: center;
          }

          .text_2 {
            margin-bottom: 15px;
            color: #555c6a;
            font-weight: 400;
            font-size: 16px;
            line-height: 19px;
          }

          .image {
            width: 100%;
            height: auto;

            img {
              width: 100%;
              height: auto;
            }
          }
        }
      }
    }

    .Web3_font_6 {
      margin-bottom: 8px;
      color: #ffffff;
      font-weight: 600;
      font-size: 20px;
      line-height: 23px;
    }
    .line_2 {
      width: 32px;
      height: 4px;
      margin-bottom: 24px;
      background: #267cfd;
      border-radius: 22px 22px 22px 22px;
    }

    .text_3 {
      width: 100%;
      padding: 0 14px;
      color: #ffffff;
      font-weight: 400;
      font-size: 14px;
      line-height: 16px;
      text-align: center;

      .text_3_br {
        display: block;
      }
    }
  }

  .Web4 {
    margin-top: 40px;

    .font_6 {
      margin-bottom: 16px;
      color: #2f3745;
      font-weight: 600;
      font-size: 42px;
      line-height: 39px;
    }

    .title_advantage {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      height: 36px;
      color: #2f3745;
      color: #2f3745;
      font-weight: 600;
      font-size: 20px;
      line-height: 23px;
      text-align: center;

      &::after {
        display: inline-block;
        width: 32px;
        height: 4px;
        background: #267cfd;
        border-radius: 22px 22px 22px 22px;
        box-shadow: 0 4px 4px 0 rgb(38 124 253 / 10%);
        content: '';
      }
    }

    .bg_content {
      width: 100%;
      height: auto;
      margin-top: 24px;

      .web4_bg_wrapper {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: auto;

        .img_content_wrap {
          display: none;
        }

        .web4_bg_item {
          position: relative;
          width: 100%;
          padding: 31px 12px 15px;
          background: url('https://static.jwsmed.com/public/DigitalHealth/Business/assets/MemberBenefitsPage/advantage_bg_h5.png')
            no-repeat;
          background-size: 100% 100%;

          &:last-of-type {
            padding-bottom: 40px;
          }

          span {
            position: absolute;
            top: 10px;
            left: 50%;
            z-index: 0;
            display: block;
            width: 50px;
            height: 50px;
            background: url('https://static.jwsmed.com/public/DigitalHealth/Business/assets/MemberBenefitsPage/advantage_h5.png')
              no-repeat;
            background-size: 50px 50px;
            transform: translateX(-50%);
          }

          &:nth-of-type(2) {
            padding-top: 56px;

            span {
              position: absolute;
              top: 32px;
              left: 50%;
              z-index: 0;
              display: block;
              width: 50px;
              height: 50px;
              background: url('https://static.jwsmed.com/public/DigitalHealth/Business/assets/MemberBenefitsPage/advantage_h5.png')
                no-repeat;
              background-size: 50px 50px;
              transform: translateX(-50%);
            }
          }
        }

        .web4_text_box {
          position: relative;
          z-index: 2;
          box-sizing: border-box;
          width: 100%;
          padding: 16px;
          background: #ffffff;
          border-radius: 12px 12px 12px 12px;

          .web4_img_title {
            margin-bottom: 8px;
            color: #2f3745;
            font-weight: 500;
            font-size: 20px;
            line-height: 23px;
            text-align: center;
          }

          .web4_img_text {
            color: #a2a8b2;
            font-weight: 400;
            font-size: 16px;
            line-height: 19px;
          }

          &.web4_text_box1,
          &.web4_text_box2,
          &.web4_text_box3,
          &.web4_text_box4,
          &.web4_text_box5 {
            position: relative;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            transform: translate(0);
          }
        }
      }
    }
  }

  .Web5 {
    padding-top: 21px;

    .bg {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      height: 36px;
      color: #2f3745;
      color: #2f3745;
      font-weight: 600;
      font-size: 20px;
      line-height: 23px;
      text-align: center;

      &::after {
        display: inline-block;
        width: 32px;
        height: 4px;
        background: #267cfd;
        border-radius: 22px 22px 22px 22px;
        box-shadow: 0 4px 4px 0 rgba(38, 124, 253, 0.1);
        content: '';
      }
    }
    .banner {
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      width: 100%;
      margin: 24px 0 40px;
      padding: 0 18px 0 12px;

      .web5_list_box {
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        justify-content: space-between;
        width: 100%;
        margin-bottom: 20px;

        &:last-of-type {
          margin-bottom: 0;
        }

        .web5_list_img {
          flex-shrink: 0;
          width: 36px;
          height: 36px;

          img {
            width: 100%;
            height: auto;
          }
        }

        .web5_list_item {
          flex: 1;
          margin-left: 20px;
          text-align: left;

          .web5_list_price {
            margin-bottom: 5px;
            color: #2f3745;
            font-weight: 500;
            font-size: 18px;
            line-height: 21px;
          }

          .web5_list_text {
            color: #555c6a;
            font-weight: 400;
            font-size: 14px;
            line-height: 18px;
          }
        }
      }
    }
    .content {
      box-sizing: border-box;
      padding: 0 12px;
      background-color: #fff;

      .title {
        margin-bottom: 16px;
        color: #555c6a;
        font-weight: 400;
        font-size: 15px;
        line-height: 21px;
        text-align: left;

        .lineHeight {
          color: #267cfd;
        }
      }

      .subTitle {
        color: #555c6a;
        font-weight: 400;
        font-size: 15px;
        line-height: 21px;
        text-align: left;
      }

      .table {
        width: 100%;
        margin: 0 auto;
        margin-top: 12px;
        background-color: #fff;

        .tableTitle {
          color: #2f3745;
          font-weight: 400;
          font-size: 12px;
          line-height: 14px;
          background-color: #eaeaf0;
        }
        tr {
          height: 65px;
          border-right: 1px solid #eee;
          border-bottom: 1px solid #eee;
          border-left: 1px solid #eee;
        }
        td {
          padding-left: 12px;
          color: #555c6a;
          font-weight: 400;
          font-size: 12px;
          line-height: 14px;

          &.heightLight {
            color: #ef441f;
          }
        }
      }

      .subTip {
        margin-top: 10px;
        color: #ef441f;
        font-weight: 400;
        font-size: 12px;
        line-height: 14px;
        text-align: center;
      }
      .button {
        position: relative;
        display: block;
        width: 166px;
        height: 36px;
        margin: 7px auto;
        color: #fff;
        color: #ffffff;
        font-weight: 400;
        font-size: 14px;
        line-height: 36px;
        text-align: center;
        text-indent: -20px;
        background: #267cfd;
        border: 1px solid #267cfd;

        &::after {
          position: absolute;
          top: 15px;
          right: 20px;
          display: block;
          width: 8px;
          height: 8px;
          border: 1px solid #fff;
          border-bottom-color: transparent;
          border-left-color: transparent;
          transform: rotateZ(45deg);
          content: '';
        }
      }
    }
  }

  .Web6 {
    width: 100%;
    height: auto;
    padding: 36px 12px 0;
    background: #fff;

    .web6_title {
      margin-bottom: 24px;
      color: #000000;
      font-weight: 500;
      font-weight: 600;
      font-size: 16px;
      line-height: 19px;
      text-align: center;
    }

    .web6_content {
      display: flex;
      flex-direction: column;
      width: 100%;

      .web6_img_1 {
        display: none;
      }

      .web6_img_2,
      .web6_img_3,
      .web6_img_4,
      .web6_img_5 {
        margin-bottom: 12px;
        padding-top: 26px;
        padding-left: 45%;
        color: #2f3745;
        font-weight: 500;
        font-size: 16px;
        line-height: 19px;
      }

      .web6_img_2 {
        width: 100%;
        height: 114px;
        background: url('https://static.jwsmed.com/public/DigitalHealth/Business/assets/MemberBenefitsPage/jc_img_h5_2.png')
          no-repeat;
        background-size: 100% 100%;
      }

      .web6_img_3 {
        width: 100%;
        height: 114px;
        background: url('https://static.jwsmed.com/public/DigitalHealth/Business/assets/MemberBenefitsPage/jc_img_h5_3.png')
          no-repeat;
        background-size: 100% 100%;
      }

      .web6_img_4 {
        width: 100%;
        height: 114px;
        background: url('https://static.jwsmed.com/public/DigitalHealth/Business/assets/MemberBenefitsPage/jc_img_h5_4.png')
          no-repeat;
        background-size: 100% 100%;
      }

      .web6_img_5 {
        width: 100%;
        height: 114px;
        margin-bottom: 0;
        background: url('https://static.jwsmed.com/public/DigitalHealth/Business/assets/MemberBenefitsPage/jc_img_h5_5.png')
          no-repeat;
        background-size: 100% 100%;
      }
    }
  }

  .Web7 {
    width: 100%;
    height: auto;
    margin: 0 auto;
    padding: 0;
    padding-top: 40px;
    background: #fff;

    .web7_title {
      margin-bottom: 16px;
      color: #000000;
      font-weight: 500;
      font-weight: 600;
      font-size: 16px;
      line-height: 19px;
      text-align: center;
    }

    .web7_content_pc {
      display: none;
    }

    .web7_content_h5 {
      display: flex;
      flex-wrap: wrap;
      box-sizing: border-box;
      padding: 0 17px;

      .table {
        width: 100%;

        tr {
          width: 100%;

          td {
            width: 33.3%;
            border: 1px solid #e2e2e2;

            img {
              width: 100%;
              height: auto;
            }
          }

          &.special {
            td {
              width: 100%;
              padding: 6px 0;
              color: #a2a8b2;
              font-weight: 400;
              font-size: 12px;
              line-height: 14px;
              text-align: center;
              border: 1px solid #e2e2e2;
            }
          }
        }
      }
    }
  }
  .dentalAssistantBanner {
    height: 313px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    padding: 0 12px;
    h3 {
      font-size: 20px;
    }
    h1 {
      font-size: 32px;
    }
    h2 {
      margin-top: 10px;
      margin-bottom: 10px;
      color: #9fc8eb;
      font-size: 24px;
    }
    p {
      font-size: 14px;
    }
  }
  .dentalAssistantInterest {
    width: 100%;
    padding: 0 12px;
    padding-top: 36px;
    h3 {
      width: 100%;
      font-size: 20px;
      text-align: center;
    }
    .dentalAssistantInterestItem {
      flex-wrap: wrap;
      padding-bottom: 36px;
      .InterestItemPart1 {
        width: 50%;
      }
      .InterestItemPart2 {
        width: 50%;
        padding-left: 12px;
        .InterestItemSubhead {
          margin-bottom: 5px;
          font-size: 12px;
        }
        i {
          margin-bottom: 5px;
          font-size: 11px;
        }
        > span {
          font-size: 12px;
        }
        .InterestLink {
          flex-flow: wrap;
          align-items: center;
          margin-bottom: 0;
          i {
            display: inline-block;
            width: 14px;
            height: 14px;
          }
          a {
            display: inline;
            margin-bottom: 0;
            color: #203b83;
          }
        }
      }
    }
  }
  .Web8 {
    width: 100%;
    height: auto;
    margin: 0 auto;
    padding-bottom: 80px;
    background: #fff;

    .web8_title {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      height: 36px;
      margin-top: 40px;
      margin-bottom: 24px;
      color: #2f3745;
      font-weight: 600;
      font-size: 20px;
      line-height: 23px;
      text-align: center;

      &::after {
        display: inline-block;
        width: 32px;
        height: 4px;
        background: #267cfd;
        border-radius: 22px 22px 22px 22px;
        box-shadow: 0 4px 4px 0 rgba(38, 124, 253, 0.1);
        content: '';
      }
    }

    .web8_tab {
      display: flex;
      justify-content: center;
      width: 100%;
      margin-bottom: 20px;

      .web8_tab_init {
        padding: 4px 16px;
        color: #000000;
        font-weight: 400;
        font-size: 16px;
        line-height: 19px;
        border-radius: 20px;

        &.web8_tab_active {
          padding: 4px 16px;
          color: #ffffff;
          font-weight: 400;
          font-size: 16px;
          line-height: 19px;
          background: #013396;
          border: 1px solid#013396;
          border-radius: 20px;
        }

        &:nth-of-type(1) {
          margin-right: 14px;
        }
      }
    }

    .web8_content_wrap {
      position:relative;
      .web8_modal {
        position: absolute;
        z-index:9999;
        top:50%;
        left:50%;
        width: 440px;
        height: 440px;
        margin-top:0px;
        margin-left: -220px;
        background:#fff;
        border-radius: 2px 2px 2px 2px;
        .consult_customer_title {
          position: relative;
          box-sizing: border-box;
          height: 133px;
          background: linear-gradient(180deg, #eff8ff 0%, rgba(255, 255, 255, 0) 100%);
          .cctitle {
            padding-top: 72px;
            color: #191919;
            font-weight: 600;
            font-size: 32px;
            line-height: 38px;
            text-align: center;
          }

          .ccicon {
            position: absolute;
            top: 24px;
            right: 24px;
            width: 32px;
            height: 32px;
            span {
              display: inline-block;
              width: 18px;
              height: 2px;
              background: #000000;
            }
            span:nth-of-type(1) {
              position: relative;
              top: 0px;
              left: 10px;
              transform: rotate(45deg);
            }
            span:nth-of-type(2) {
              position: relative;
              top: -22px;
              left: 10px;
              transform: rotate(-45deg);
            }
          }
        }
        .consult_customer_con {
          display: flex;
          justify-content: center;
          margin-top: 8px;
          img {
            width: 234px;
            height: 234px;
          }
        }
      }
    }
    .web8_content {
      display: flex;
      justify-content: space-between;
      margin-bottom: 40px;
      padding: 0 12px;
      overflow: auto;
      .web8_left_content,
      .web8_right_content {
        width: 85%;
        height: auto;
        padding-bottom: 40px;
        background: #ffffff;
        border-radius: 2px 2px 2px 2px;
        box-shadow: 0 6px 26px 0 rgba(15, 27, 49, 0.1);
        .web8_left_title,
        .web8_right_title {
          span {
            margin-left: 10px;
            color: #2f3745;
            font-weight: 500;
            font-size: 14px;
          }
        }
        .web8_left_title {
          box-sizing: border-box;
          width: 100%;
          height: 39px;
          padding: 9px 16px;
          color: #2f3745;
          font-weight: 600;
          font-size: 16px;
          line-height: 19px;
          background: url('https://static.jwsmed.com/public/DigitalHealth/Business/assets/MemberBenefitsPage/payment_1.png')
            no-repeat;
          background-size: 100% 100%;
        }

        .web8_right_title {
          box-sizing: border-box;
          width: 100%;
          height: 39px;
          padding: 9px 16px;
          color: #2f3745;
          font-weight: 600;
          font-size: 16px;
          line-height: 19px;
          background: url('../../../assets/Payment/payment.png') no-repeat;
          background-size: 100% 100%;
        }
      }

      .web8_right_content {
        margin-left: 16px;
        padding-bottom: 17px;
      }

      .web8_list_box {
        padding-top: 12px;
        padding-right: 12px;
        padding-left: 16px;

        .web8_list_item {
          margin-bottom: 8px;
          h4 {
            margin-bottom: 12px;
            color: #2f3745;
            font-weight: 600;
          }
          .web8_item_content {
            .memberitem {
              display: flex;
              align-items: center;
              justify-content: space-between;
              height: 18px;
              margin-bottom: 0;
              font-size: 13px;
              line-height: 18px;
              div:nth-child(1) {
                i {
                  display: inline-block;
                  width: 8px;
                  height: 8px;
                  margin-right: 8px;
                  font-style: normal;
                  border-radius: 50%;
                }
              }
              div:nth-child(2) {
                span:nth-of-type(1) {
                  color: #ef441f;
                  font-weight: 600;
                  font-size: 14px;
                }
                span:nth-of-type(2) {
                  color: #aaaaaa;
                  font-size: 10px;
                  font-style: normal;
                }
              }
              .fs16 {
                font-size: 13px !important;
              }
              .fsC {
                font-size: 14px !important;
              }
              .disabledType {
                display: inline-block;
                color: rgba(47, 55, 69, 0.5);
                text-decoration: line-through;
              }
              .type1 {
                i {
                  background: #267cfd;
                }
              }
              .type2 {
                i {
                  background: #f1bc00;
                }
              }
              .type3 {
                i {
                  background: #28e75e;
                }
              }
              .type4 {
                i {
                  background: #9e80f7;
                }
              }
              .type5 {
                i {
                  background: #ff7b72;
                }
              }
            }
            > div {
              margin-bottom: 8px;
              > i {
                display: inline-block;
                padding-left: 16px;
                color: #666666;
                font-size: 10px;
                font-style: normal;
              }
            }
          }
        }
      }

      .web8_price_content {
        display: flex;
        margin-top: 0;
        margin-bottom: 17px;
        padding-left: 16px;

        .web8_price_item {
          display: flex;
          align-items: center;
          margin-right: 24px;
          color: #999999;
          font-weight: 400;
          font-size: 14px;
          line-height: 16px;

          .unit {
            position: relative;
            top: 5px;
            margin-right: 4px;
            color: #ef441f;
            font-weight: 500;
            font-size: 20px;
            line-height: 23px;
          }

          .money {
            margin-right: 4px;
            color: #ef441f;
            font-weight: bold;
            font-size: 32px;
            line-height: 38px;
          }
        }
      }

      .btn {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;

        span {
          display: flex;
          box-sizing: border-box;
          padding: 8px 28px;
          color: #ffffff;
          font-weight: 400;
          font-size: 16px;
          line-height: 19px;
          background: #013396;
          border-radius: 20px;
          box-shadow: 0 4px 6px 0 rgba(38, 124, 253, 0.28);
        }
      }

      .tips {
        margin-top: 7px;
        color: #fb6535;
        font-weight: 400;
        font-size: 12px;
        line-height: 14px;
        text-align: center;
      }
    }

    .web8_content_y0 {
      transform: translateX(0);
    }

    .web8_content_y1 {
      transform: translateX(-70%);
    }

    .web8_apply_member {
      box-sizing: border-box;
      margin-top: 0;
      padding: 0 12px;

      .web8_member_title {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        height: 36px;
        margin-top: 0;
        margin-bottom: 24px;
        color: #2f3745;
        font-weight: 600;
        font-size: 20px;
        line-height: 23px;
        text-align: center;

        &::after {
          display: inline-block;
          width: 32px;
          height: 4px;
          background: #267cfd;
          border-radius: 22px 22px 22px 22px;
          box-shadow: 0 4px 4px 0 rgba(38, 124, 253, 0.1);
          content: '';
        }
      }

      .web8_member_content {
        display: flex;
        flex-direction: column;
        align-items: center;
        box-sizing: border-box;
        box-sizing: border-box;
        padding: 24px 12px;
        background: #ffffff;
        border-radius: 2px 2px 2px 2px;
        box-shadow: 0 6px 26px 0 rgba(15, 27, 49, 0.1);

        .web8_member_content_text {
          color: #555c6a;
          font-weight: 400;
          font-size: 14px;
          line-height: 24px;
        }

        .web8_member_copy_btn {
          display: block;
          margin-top: 18px;
          padding: 2px 8px;
          color: #555c6a;
          font-weight: 400;
          font-size: 14px;
          line-height: 24px;
          border: 1px solid #cccccc;
          border-radius: 2px 2px 2px 2px;
        }
      }
    }

    .web8_FAQ_content {
      box-sizing: border-box;
      margin-top: 40px;
      padding: 0 12px;

      .FAQ_title {
        margin-bottom: 14px;
        color: #2f3745;
        font-weight: 500;
        font-size: 14px;
        line-height: 16px;
      }

      .FAQ_box {
        margin-bottom: 16px;

        .FAQ_list {
          display: flex;
          justify-content: space-between;
          width: 100%;
          padding: 10px 12px;
          color: #2f3745;
          font-weight: 500;
          font-size: 14px;
          background: linear-gradient(180deg, rgba(249, 249, 249, 0) 0%, #f9f9f9 100%);
          border-radius: 2px 2px 2px 2px;

          .arrow {
            width: 20px;
            height: 20px;
            cursor: pointer;

            img {
              width: 100%;
              height: auto;
            }
          }
        }

        .FAQ_text {
          padding: 10px 12px;
          color: #000000;
          font-weight: 400;
          font-size: 12px;
          line-height: 16px;
        }
      }
    }

  }

  .PaymentBtn_box {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 999;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    width: 100%;
    padding: 0 12px;
    background: #fff;

    .PaymentBtn_person {
      width: 48%;

      .PaymentBtn_content {
        position: relative;
        box-sizing: border-box;
        width: 100%;
        padding: 18px 0;
        background: #ffffff;
        border: 1px solid #267cfd;
        border-radius: 0 2px 2px 2px;
        box-shadow: 0 4px 6px 0 rgba(38, 124, 253, 0.28);

        .PaymentBtn_icon {
          position: absolute;
          top: -3px;
          left: 0;
          display: block;

          img {
            width: 53px;
            height: 23px;
          }
        }

        .PaymentBtn_btn {
          display: inline-block;
          width: 100%;
          color: #267cfd;
          font-weight: 400;
          font-size: 18px;
          line-height: 21px;
          text-align: center;
        }
      }
    }

    .PaymentBtn_enterprise {
      width: 48%;

      .PaymentBtn_content {
        position: relative;
        box-sizing: border-box;
        width: 100%;
        padding: 18px 0;
        background: #267cfd;
        border: 1px solid #fff;
        border-radius: 0 2px 2px 2px;
        box-shadow: 0 4px 6px 0 rgba(38, 124, 253, 0.28);

        .PaymentBtn_icon {
          position: absolute;
          top: -3px;
          left: 0;
          display: block;

          img {
            width: 53px;
            height: 23px;
          }
        }

        .PaymentBtn_btn {
          display: inline-block;
          width: 100%;
          color: #ffffff;
          font-weight: 400;
          font-size: 18px;
          line-height: 21px;
          text-align: center;
        }
      }
    }

    .PaymentBtn_price {
      box-sizing: border-box;
      padding: 5px 14px;
      color: #267cfd;
      font-weight: 400;
      font-size: 14px;
      line-height: 16px;
      background: rgba(38, 124, 253, 0.1);
      border: 1px solid #267cfd;
      border-radius: 2px 2px 2px 2px;
    }
  }
}

.dentalAssistantBanner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  h3,
  h1,
  h2,
  p {
    width: 100%;
    margin: 0;
    text-align: center;
  }
  h3,
  h1,
  p {
    color: #fff;
  }
  h2 {
    color: #fff;
  }
  p {
    white-space: pre-wrap;
  }
}
.dentalAssistantInterest {
  .dentalAssistantInterestItem {
    display: flex;
    .InterestItemPart1 {
      img {
        width: 100%;
      }
    }
    .InterestItemPart2 {
      display: flex;
      flex-direction: column;
      justify-content: center;
      h3,
      > i {
        color: #000000;
        font-weight: 900;
        white-space: pre-wrap;
      }
      p {
        color: #666666;
      }
      > span {
        display: block;
        margin-bottom: 8px;
        color: #ef441f;
      }
      .InterestLink {
        display: flex;
        color: #203b83;
        cursor: pointer;
        i {
          margin-bottom: 0;
          background-image: url('https://static.jwsmed.com/public/DigitalHealth/Business/assets/DentalAssistant/linkUrlIcon.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
        }
      }
    }
  }
  i {
    font-style: normal;
  }
}

