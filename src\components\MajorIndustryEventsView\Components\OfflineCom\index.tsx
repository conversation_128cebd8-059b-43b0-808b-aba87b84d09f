import React, { useState, useEffect } from 'react';
import classNames from 'classnames';
import Item from '../Components/NormalItem';
import styles from './index.less';
// 暂无数据
import NullData from '../../Components/NullData';

interface IProps {
  dataList?: any;
  styleType?: string;
  citys?: string[];
  pcOrMobileMode?: string;
  getFilterParams: (type: string, params: any) => void;
}
const Index: React.FC<IProps> = ({ dataList, styleType, citys, getFilterParams, pcOrMobileMode }) => {
  const [selectedCity, setSelectedCity] = useState('推荐')

  const handleSelected = (v: string) => {
    setSelectedCity(v)

  }
  useEffect(() => {
    getFilterParams('city', selectedCity)
  }, [selectedCity])

  return <>
    {/* 城市筛选 */}
    <div className={classNames(styles.offlineCom_city, {
      [styles.offlineCom_mobile_city]: pcOrMobileMode === 'mobile',
      [styles.offlineCom_pc_city]: pcOrMobileMode === 'pc',
    }
    )}>
      {citys?.map(item => <span
        key={item}
        className={classNames(styles.city_item, { [styles.active]: item == selectedCity })}
        onClick={() => handleSelected(item)}>{item}</span>)}
    </div>


    {/* 列表展示 */}
    {dataList.length ? <div
      className={classNames(styles.normal_wrapper, {
        [styles.normal_mobile_wrapper]: pcOrMobileMode === 'mobile',
        [styles.normal_pc_wrapper]: pcOrMobileMode === 'pc'
      })}>
      {dataList.map((item, index) => {
        return <Item
          itemData={item}
          styleType={styleType}
          key={`${item.id}-${index}`}
          pcOrMobileMode={pcOrMobileMode}
        />
      })}</div> : <NullData />}
  </>
}

export default Index; 