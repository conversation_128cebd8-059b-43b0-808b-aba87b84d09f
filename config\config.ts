// https://umijs.org/config/
import { defineConfig } from 'umi';

import defaultSettings from './defaultSettings';
import proxy from './proxy';
import routes from './routes';
import PostcssPxtorem from 'postcss-pxtorem';

const { REACT_APP_ENV } = process.env;

export default defineConfig({
  hash: true,
  antd: {},
  dva: {
    hmr: true,
  },
  layout: {
    // https://umijs.org/zh-CN/plugins/plugin-layout
    locale: true,
    siderWidth: 208,
    ...defaultSettings,
  },
  // https://umijs.org/zh-CN/plugins/plugin-locale
  locale: {
    // default zh-CN
    default: 'zh-CN',
    antd: true,
    // default true, when it is true, will use `navigator.language` overwrite default
    baseNavigator: false,
  },
  dynamicImport: {
    loading: '@/components/PageLoading/index.tsx',
  },
  chunks: ['vendors', 'umi'],
  chainWebpack: function (config, { webpack }) {
    config.merge({
      optimization: {
        splitChunks: {
          chunks: 'all',
          minSize: 30000,
          minChunks: 3,
          automaticNameDelimiter: '.',
          cacheGroups: {
            vendor: {
              name: 'vendors',
              test({ resource }) {
                return /[\\/]node_modules[\\/]/.test(resource);
              },
              priority: 10,
            },
          },
        },
      }
    });
    config.module
      .rule('mjs')
      .test(/\.mjs$/)
      .include
      .add(/node_modules/)
      .end()
      .type('javascript/auto');
  },
  targets: {
    ie: 11,
  },
  // umi routes: https://umijs.org/docs/routing
  routes,
  access: {},
  // Theme for antd: https://ant.design/docs/react/customize-theme-cn
  theme: {
    // 如果不想要 configProvide 动态设置主题需要把这个设置为 default
    // 只有设置为 variable， 才能使用 configProvide 动态设置主色调
    // https://ant.design/docs/react/customize-theme-variable-cn
    'root-entry-name': 'variable',
  },
  externals: {
    'react':'window.React',
    'react-dom': 'window.ReactDOM',
    'moment':'moment',
    'trtc-js-sdk':'TRTC',
    'video.js':'videojs',
    'tim-js-sdk':'TIM',
    'tim-upload-plugin':'TIMUploadPlugin',
    'tim-profanity-filter-plugin':'TIMProfanityFilterPlugin',
    'TEduBoard':'TEduBoard',
  },
  scripts: process.env.NODE_ENV === 'development'?[
    'https://js.5i5ya.com/public/PluginsDir/react/17.0.2/react.development.js',
    'https://js.5i5ya.com/public/PluginsDir/react-dom/17.0.2/react-dom.development.js',
    'https://js.5i5ya.com/public/PluginsDir/moment/2.29.1/moment.min.js',
    'https://js.5i5ya.com/public/PluginsDir/moment/2.29.1/locale/zh-cn.js',
  ]:[
    'https://js.5i5ya.com/public/PluginsDir/react/17.0.2/react.production.min.js',
    'https://js.5i5ya.com//public/PluginsDir/react-dom/17.0.2/react-dom.production.min.js',
    'https://js.5i5ya.com/public/PluginsDir/moment/2.29.1/moment.min.js',
    'https://js.5i5ya.com/public/PluginsDir/moment/2.29.1/locale/zh-cn.js',
  ],
  // esbuild is father build tools
  // https://umijs.org/plugins/plugin-esbuild
  // esbuild: {},
  title: false,
  ignoreMomentLocale: true,
  devtool:process.env.NODE_ENV === 'development'? 'source-map':false,
  // devtool:false,    // 开发注释
  proxy: proxy(REACT_APP_ENV || 'dev'),
  manifest: {
    basePath: process.env.NODE_ENV === 'development'?'/':'https://js0-test.5i5ya.com/',
  },
  publicPath: process.env.NODE_ENV === 'development'?'/':'https://js0-test.5i5ya.com/',
  // fastRefresh: {},
  // mfsu:{},
  nodeModulesTransform: { type: 'none' },
  polyfill: {
    imports: [
      'core-js/stable',
    ]
  },
  // terserOptions: {
  //   compress: {
  //     drop_console:true
  //   },
  // },
  // webpack5: {},
  // exportStatic: {},
  // 开发注释
  extraPostCSSPlugins: [
    PostcssPxtorem({
      rootValue: 16,
      selectorBlackList: ['node_modules', '.hairlines', '.am-', 'ant','adm','.adm'],
      exclude: function (file){
        return file.indexOf('PlanetChatRoom') === -1;
      },
      propList: ['*'],
    }),
  ],
});
