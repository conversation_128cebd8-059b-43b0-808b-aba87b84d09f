.spin {
  height: 100%;
  & > :global(.ant-spin-container) {
    height: 100%;
  }
}

.container {
  height: 100%;
  overflow-y: auto;
  background: #fff;
  padding: 44px 0 34px;
  display: flex;
  flex-direction: column;
}

.gray_bar {
  width: 100%;
  height: 8px;
  flex-shrink: 0;
  background: #F5F6F8;
}

.content {
  width: 100%;
  flex: 1;
  padding: 20px 0 40px 16px;
  .content_title {
    font-size: 24px;
    color: #000;
    font-weight: 500;
    line-height: 34px;
    margin-bottom: 8px;
  }

  .content_tips {
    font-size: 12px;
    color: #999;
    line-height: 17px;
    margin-bottom: 28px;
  }

  .info_item_wrap {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #E1E4E7;
    padding-right: 16px;
    min-height: 62px;
    column-gap: 12px;
    .info_item_label {
      flex-shrink: 0;
      width: 72px;
      white-space: nowrap;
      font-size: 16px;
      color: #000;
      line-height: 22px;
    }
    .info_item_value {
      text-align: right;
      word-break: break-all;
      font-size: 16px;
      color: #666;
      line-height: 22px;
    }
    .info_item_img_wrap {
      height: 40px;
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #0095FF;
    }
  }
}

.btn_wrap {
  width: 100%;
  flex-shrink: 0;
  padding: 0 16px 11px;
  .btn {
    white-space: nowrap;
    width: 100%;
    height: 40px;
    line-height: 40px;
    background: #0095FF;
    color: #fff;
    border-radius: 20px;
    font-size: 16px;
    text-align: center;
  }
}
