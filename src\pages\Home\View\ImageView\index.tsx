/**
 * @Description: 图片组件
 */
import React from 'react'
import { history } from 'umi'
import classNames from 'classnames'
import {
  getNewImageNumber, page_banner, jinGang_click, ad_click,
  morespace_click, morekingdom_click, morecourse_click, more5Acourse_click, moreOPcourse_click, moreNERcourse_click, moreCasecourse_click,
  group_click,
} from '../../utils'
import { Swiper, Toast } from 'antd-mobile'
import styles from './index.less'

interface PropsType {
  componentData: any,                                      // 组件数据，格式：{ dataList: [], config: { number: xx } }
  isHomePage?: any,                                         // 是否为首页，1是，0否
  moduleIndex?: any,                                        // 当前组件在所有图片（一行一个）组件中的索引
  isClassifyGuide?: any,                                   // 是否在分类导航组件中，1是
  classifyGuideTabIndex?: any,                             // 在分类导航组件中时，选中的tab标签
  moduleIndexBanner?: any,                                 // 当前组件在所有图片（轮播，banner）组件中的索引
  moduleIndexJinGang?: any,                                // 当前组件在所有图片（一行5个，金刚区）组件中的索引
  moduleIndexAD?: any,                                     // 当前组件在所有图片（一行3个，运营位）组件中的索引
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {
    componentData, isHomePage, moduleIndex, isClassifyGuide, classifyGuideTabIndex,
    moduleIndexBanner, moduleIndexJinGang, moduleIndexAD,
  } = props
  // config: { number: 11/12/13/14/15/50/60 } (11~15)-10， 一行几个，50 轮播，60 横向滑动
  const { config } = componentData
  const dataList = componentData.dataList || []
  const newNumber = getNewImageNumber(config.number)

  // 点击图片跳转，linkType：1 H5链接，2 小程序页面，3 小鹅通链接，4 鹅直播链接
  const goToUrl = (item, index) => {
    // 链接地址正则
    const pattern = /^((https|http):\/\/)[^\s]+/
    if (!item.linkType) {
      return
    } else if (item.linkType == 1) {
      // 跳转h5
      if (item.linkUrl) {
        if (pattern.test(item.linkUrl)) {
          // 跳转项目内地址
          if (item.linkUrl.indexOf(window.location.origin) > -1) {

            const linkPathname = item.linkUrl.split(window.location.origin)[1]
            // 避免路由相同跳转后页面不刷新
            if (linkPathname.indexOf('/home?') > -1) {
              window.location.href = item.linkUrl
            } else {
              history.push(linkPathname)
            }
          } else {
            window.location.href = item.linkUrl
          }
        } else {
          Toast.show('链接地址不正确')
        }
      }

    } else if (item.linkType == 2) {
      // 跳转小程序
      if (item.resourceType && item.resourceId) {
        history.push({
          pathname: '/Home/MiniProgramCode',
          query: {
            resourceType: item.resourceType,           // 链接资源类型，1 病例，2 专家
            resourceId: item.resourceId,               // 资源ID
          }
        })
      }

    } else {
      // 跳转小鹅通、鹅直播
      if (item.linkUrl) {
        if (pattern.test(item.linkUrl)) {
          window.location.href = item.linkUrl
        } else {
          Toast.show('链接地址不正确')
        }
      }
    }

    // 首页友盟统计，isHomePage=1
    if (isHomePage != 1) {
      return
    }
    // 在分类导航组件中
    if (isClassifyGuide == 1) {
      // 首页，分类组件点击量
      group_click(moduleIndex, `第${moduleIndex}个分类组件，第${classifyGuideTabIndex}个标签，第${index + 1}个图片`)
      return
    }

    // 下面都是不再分类导航组件中的
    if (newNumber == 50) {
      // 首页，banner点击量，记录第几个
      page_banner(moduleIndexBanner, index + 1)

    } else if (newNumber == 15) {
      // 首页，金刚区点击量，记录第几个
      jinGang_click(moduleIndexJinGang, index + 1)

    } else if (newNumber == 13) {
      // 首页，3个运营位，记录第几个
      ad_click(moduleIndexAD, index + 1)

    } else if (newNumber == 11) {
      // 第几个一行一个的图片
      if (moduleIndex == 1) {
        // 首页，更多空间点击量（相当于第一个一行一个图片点击量）
        morespace_click()

      } else if (moduleIndex == 2) {
        // 首页，更多王国点击量（相当于第二个一行一个图片点击量）
        morekingdom_click()

      } else if (moduleIndex == 3) {
        // 首页，学术更多课程点击量（相当于第三个一行一个图片点击量）
        morecourse_click()

      } else if (moduleIndex == 4) {
        // 首页，更多5A课程点击量（相当于第四个一行一个图片点击量）
        more5Acourse_click()

      } else if (moduleIndex == 5) {
        // 首页，更多运营课程点击量（相当于第五个一行一个图片点击量）
        moreOPcourse_click()

      } else if (moduleIndex == 6) {
        // 首页，更多护理课程点击量（相当于第六个一行一个图片点击量）
        moreNERcourse_click()

      } else if (moduleIndex == 7) {
        // 首页，更多病例点击量（相当于第七个一行一个图片点击量）
        moreCasecourse_click()
      }
    }
  }

  return (
    <div className={classNames(styles.image_container, {
      [styles.one]: newNumber == 11,
      [styles.two]: newNumber == 12,
      [styles.three]: newNumber == 13,
      [styles.four]: newNumber == 14,
      [styles.five]: newNumber == 15,
      [styles.fifty]: newNumber == 50,
      [styles.sixty]: newNumber == 60,
    })}>
      {
        newNumber == 50 ?
          <Swiper loop={true} autoplay={true}>
            {
              dataList.map((item, index) => {
                return (
                  <Swiper.Item key={item.imgUrlDto && item.imgUrlDto.fileUrl}>
                    <div className={styles.item_box} onClick={() => goToUrl(item, index)}>
                      <img className={styles.img} src={item.imgUrlDto && item.imgUrlDto.fileUrlView} alt=""/>
                    </div>
                  </Swiper.Item>
                )
              })
            }
          </Swiper>
          : newNumber == 60 ?
          <Swiper rubberband={false} stuckAtBoundary={true} slideSize={45.86}>
            {
              dataList.map(item => {
                return (
                  <Swiper.Item key={item.imgUrlDto && item.imgUrlDto.fileUrl}>
                    <div className={styles.item_box} onClick={() => goToUrl(item)}>
                      <img className={styles.img} src={item.imgUrlDto && item.imgUrlDto.fileUrlView} alt=""/>
                    </div>
                  </Swiper.Item>
                )
              })
            }
          </Swiper>
          :
          dataList.map((item, index) => {
            return (
              <div key={item.imgUrlDto && item.imgUrlDto.fileUrl} className={styles.item_box} onClick={() => goToUrl(item, index)}>
                <img className={styles.img} src={item.imgUrlDto && item.imgUrlDto.fileUrlView} alt=""/>
              </div>
            )
          })
      }
    </div>
  )
}

export default Index
