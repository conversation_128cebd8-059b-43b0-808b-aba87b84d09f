
// 移动端
.forgetpwd_wrap {
  width: 100%;
  height: 100vh;
  background: #fff;
  position: relative;

  .pc_gohome {
    display: none;
  }

  .forgetpwdNavBar {
    position: relative!important;
    display: block;
    background: none!important;
    z-index: 99!important;
  }

  .forgetpwd_bg_box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 250px;
    z-index: 0;

    img {
      width: 100%;
      height: auto;
    }
  }

  .forgetpwd_title_wrap {
    width: 100%;
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 44px;
    margin-bottom: 48px;

    .forgetpwd_title {
      font-size: 17px;
      font-weight: 500;
      color: #000000;
      line-height: 20px;
    }
  }

  .forgetpwd_content_title {
    position: relative;
    z-index: 2;
    padding-left: 16px;
    margin-bottom: 38px;
    padding-top: 28px;

    .forgetpwd_img {
      width: 124px;
      height: 26px;
      margin-bottom: 16px;

      img {
        width: 100%;
        height: auto;
      }
    }

    .forgetpwd_text {
      font-size: 24px;
      font-weight: 600;
      color: #000;
      line-height: 34px;
    }
  }

  .forgetpwd_form_wrap {
    padding: 0 16px;
    box-sizing: border-box;
    position: relative;
    z-index: 10;
    .forgetpwd_form_input {
      height: 54px;
      border-bottom: 1px solid #EEF3F9;
      margin-bottom: 10px;

      :global {
        .ant-form-item-control-input {
          min-height: 54px;
        }
        .ant-form-item-explain {
          margin-left: 12px;
        }
      }
    }

    .forgetpwd_form_input1 {
      height: 54px;
      border-bottom: 1px solid #EEF3F9;
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;

      :global {
        .ant-form-item-control-input {
          min-height: 54px;
        }
        .ant-form-item-explain {
          margin-left: 12px;
        }
      }
    }

    .forgetpwd_form_input2 {
      height: 54px;
      border-bottom: 1px solid #EEF3F9;
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      .phone_code {
        flex: 1;
      }

      .sendCode {
        flex: 1;
        flex-shrink: 0;
        padding-top: 16px;
        font-size: 16px;
        font-weight: 400;
        color: #0095FF;
        line-height: 19px;
        cursor: pointer;
      }

      :global {
        .ant-form-item-control-input {
          min-height: 54px;
        }
        .ant-form-item-explain {
          margin-left: 12px;
        }
      }
    }
    .forgetpwd_form_input3 {
      height: 54px;
      border-bottom: 1px solid #EEF3F9;
      margin-bottom: 30px;

      :global {
        .ant-form-item-control-input {
          min-height: 54px;
        }
        .ant-form-item-explain {
          margin-left: 12px;
        }
      }
    }
    .forgetpwd_form_input4 {
      height: 54px;
      border-bottom: 1px solid #EEF3F9;
      margin-bottom: 50px;

      :global {
        .ant-form-item-control-input {
          min-height: 54px;
        }
        .ant-form-item-explain {
          margin-left: 12px;
        }
      }
    }
    .forget_password{
      cursor: pointer;
      font-size: 16px;
      text-align: right;
      margin-bottom: 10px;
      color: #0095FF;
    }
    .forgetpwd_Btn {
      width: 100%;
      height: 40px;
      border: none;
      background: #0095FF;
      border-radius: 20px;
      font-size: 16px;
      font-weight: 500;
      color: #FFFFFF;
      margin-bottom: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

// pc端
.pc_forgetpwd_wrap {
  width: 100%;
  height: 100vh;
  background: #fff;
  position: relative;
  background: url("https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/Global_pc_bg.png") no-repeat;
  background-size: contain;
  display: flex;
  justify-content: center;
  position: relative;

  .forgetpwdNavBar {
    display: none!important;
  }
  .pc_gohome {
    position: absolute;
    top: 24px;
    left: 24px;
    display: block;
    font-size: 24px;
    font-weight: 600;
    color: #000000;
    line-height: 28px;
    cursor: pointer;

    img {
      width: 24px;
      height: 24px;
      margin-right: 16px;
      position: relative;
      top: -3px;
    }
  }

  .forgetpwd_bg_wrap {
    width: 343px;
    height: 100vh;
  }
  .forgetpwd_bg_box {
    display: none;
  }
  .forgetpwd_title_wrap {
    width: 100%;
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 44px;
    margin-bottom: 48px;

    .forgetpwd_title {
      font-size: 17px;
      font-weight: 500;
      color: #000000;
      line-height: 20px;
    }
  }

  .forgetpwd_content_title {
    position: relative;
    z-index: 2;
    margin-bottom: 38px;
    padding-top: 106px;

    .forgetpwd_img {
      width: 124px;
      height: 26px;
      margin-bottom: 16px;

      img {
        width: 100%;
        height: auto;
      }
    }

    .forgetpwd_text {
      font-size: 24px;
      font-weight: 600;
      color: #000;
      line-height: 34px;
    }
  }

  .forgetpwd_form_wrap {
    box-sizing: border-box;
    position: relative;
    z-index: 10;

    .forgetpwd_form_input {
      height: 54px;
      border-bottom: 1px solid #EEF3F9;
      margin-bottom: 10px;
      :global {
        .ant-form-item-control-input {
          min-height: 54px;
        }
        .ant-form-item-explain {
          margin-left: 12px;
        }
      }
    }
    .forgetpwd_form_input1 {
      height: 54px;
      border-bottom: 1px solid #EEF3F9;
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;

      :global {
        .ant-form-item-control-input {
          min-height: 54px;
        }
        .ant-form-item-explain {
          margin-left: 12px;
        }
      }
    }

    .forgetpwd_form_input2 {
      height: 54px;
      border-bottom: 1px solid #EEF3F9;
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;

      .phone_code {
        flex: 1;
      }

      .sendCode {
        flex: 1;
        flex-shrink: 0;
        padding-top: 16px;
        font-size: 16px;
        font-weight: 400;
        color: #0095FF;
        line-height: 19px;
        cursor: pointer;
      }

      :global {
        .ant-form-item-control-input {
          min-height: 54px;
        }
        .ant-form-item-explain {
          margin-left: 12px;
        }
      }
    }
    .forgetpwd_form_input3 {
      height: 54px;
      border-bottom: 1px solid #EEF3F9;
      margin-bottom:30px;
      :global {
        .ant-form-item-control-input {
          min-height: 54px;
        }
        .ant-form-item-explain {
          margin-left: 12px;
        }
      }
    }
    .forgetpwd_form_input4 {
      height: 54px;
      border-bottom: 1px solid #EEF3F9;
      margin-bottom: 50px;
      :global {
        .ant-form-item-control-input {
          min-height: 54px;
        }
        .ant-form-item-explain {
          margin-left: 12px;
        }
      }
    }
    .forgetpwd_Btn {
      width: 100%;
      height: 40px;
      border: none;
      background: #0095FF;
      border-radius: 20px;
      font-size: 16px;
      font-weight: 500;
      color: #FFFFFF;
      margin-bottom: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
