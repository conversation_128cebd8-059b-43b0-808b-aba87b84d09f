import { useState, useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import styles from './index.less';
import { Input } from 'antd';
import searchIcon from '@/assets/GlobalImg/search.png'; // 搜索小图标

type PropsType = {
  isHistoryStatus: number;  // 1 历史搜索 2、列表页面搜索
  inputPlaceholder?: string; // 搜索框默认展示内容
  inputChangeFn?: (v: string) => void; // input事件
  cancelBtnFn?: () => void;         //  isHistoryStatus ==1  返回上一页 isHistoryStatus == 2 清空输入框
  onPressEnterFun?: () => void;    // 回车方法
  defaultInputValue?: string,
  isFocus?: boolean,                   // 是否自动聚焦
}

const Index = (props: PropsType, refInstance) => {
  const { inputChangeFn, cancelBtnFn, onPressEnterFun, isHistoryStatus, inputPlaceholder, defaultInputValue, isFocus } = props || {};
  const [inputVal, setInputVal] = useState(defaultInputValue || '');

  useEffect(() => {
    // 进入页面，输入框自动获取焦点
    if (isFocus && ref && ref.current) {
      ref.current.focus()
    }
  }, [])

  const ref = useRef(null)

  // 转发ref，返回值作为父组件ref.current的值
  useImperativeHandle(refInstance, () => {
    return {
      resetInput: resetInput,
    }
  }, [])

  // 清空输入框数据
  const resetInput = () => {
    setInputVal("")
  }

  // input 输入事件
  const inputOnChange = (val: any) => {
    setInputVal(val);
    inputChangeFn && inputChangeFn(val);
  }
  // 清除输入框数据
  const clearInput = () => {
    setInputVal("")
    cancelBtnFn && cancelBtnFn()
  }

  // 点击回车跳转或者搜索
  const onPressEnter = () => {
    // 让输入框失去焦点，手机键盘收起
    if (ref && ref.current) {
      ref.current.blur()
    }
    onPressEnterFun && onPressEnterFun()
  }
  return <div className={styles.search_input_wrap}>
    <div className={styles.search_input_box}>
      <Input
        ref={ref}
        allowClear
        className={styles.expert_input}
        value={inputVal}
        onChange={(e) => { inputOnChange(e.target.value) }}
        placeholder={inputPlaceholder}
        onPressEnter={() => { onPressEnter() }}
        prefix={<img alt='搜索' src={searchIcon} />}
      />
    </div>
    <div className={styles.search_close} onClick={() => { clearInput() }}>取消</div>
  </div>
}

export default forwardRef(Index)
