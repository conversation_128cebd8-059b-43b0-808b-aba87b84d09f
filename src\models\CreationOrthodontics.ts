import {
  editConsultationInfo,
  editConsultationNodeAndStatus,
  editOrthodonticCaseInfo,
  getConsultationOrthodonticCaseInfo,
  getOrthodonticCaseInfo,
  getSuperDoctorList,
} from '@/services/CreationOrthodontics';
import {message} from 'antd';
import {cloneDeep} from 'lodash'

const initializedState = {
  DictionaryData: null,     //
  medicalRecordJson: null,  // 正畸病例字典结构
  DataBymedicalRecordJson: null,
  superDoctorList: null,    // 上级医生集合
}

const getFormDataByStep = (step, DataBymedicalRecordJsonByParams) => {
  let DataBymedicalRecordJson = cloneDeep(DataBymedicalRecordJsonByParams);
  if (step == 1) {
    // 主诉
    const checkJson1 = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item) => {
      return item.dictCode == '1'
    })
    // 现病史
    const checkJson2 = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item) => {
      return item.dictCode == '2'
    })
    // 既往史
    const checkJson3 = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item) => {
      return item.dictCode == '3'
    })
    // 全身健康状况
    const checkJson4 = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item) => {
      return item.dictCode == '4'
    })
    return [checkJson1, checkJson2, checkJson3, checkJson4]
  } else if (step == 2) {
    const checkJson5 = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item) => {
      return item.dictCode == 5
    })
    // 检查json
    const checkJson6 = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item) => {
      return item.dictCode == '6'
    })
    // 模型分析
    const itemByModelAnalysis = checkJson6 && checkJson6.subsetList && checkJson6.subsetList.find((item) => {
      return item.dictCode == '1'
    })
    // "侧位片分析"
    const itemByLateralFilmAnalysis = checkJson6 && checkJson6.subsetList && checkJson6.subsetList.find((item) => {
      return item.dictCode == '2'
    })
    // "全景片分析" Panoramic Film Analysis
    const itemByPanoramicFilmAnalysis = checkJson6 && checkJson6.subsetList && checkJson6.subsetList.find((item) => {
      return item.dictCode == '3'
    })
    let ArrByForm = [];
    if (checkJson5 && Array.isArray(checkJson5.subsetList)) {
      ArrByForm = ArrByForm.concat(checkJson5.subsetList)
    }
    itemByModelAnalysis && ArrByForm.push(itemByModelAnalysis);
    itemByLateralFilmAnalysis && ArrByForm.push(itemByLateralFilmAnalysis);
    itemByPanoramicFilmAnalysis && ArrByForm.push(itemByPanoramicFilmAnalysis);
    updateIsCheck(ArrByForm);
    return ArrByForm;
  } else if (step == 3) {
    // 问题清单
    const checkJson7 = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item) => {
      return item.dictCode == '7'
    })
    // 诊断
    const checkJson8 = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item) => {
      return item.dictCode == '8'
    })
    let ArrByForm = [checkJson7, checkJson8];
    updateIsCheck(ArrByForm);
    return ArrByForm;
  } else if (step == 4) {
    // 治疗目标
    const checkJson9 = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item) => {
      return item.dictCode == '9'
    })
    // 治疗方案
    const checkJson10 = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item) => {
      return item.dictCode == '10'
    })
    let ArrByForm = [checkJson9, checkJson10];
    updateIsCheck(ArrByForm);
    return ArrByForm;
  } else if (step == 5) {
    // 11、正畸影像：
    const checkJson11 = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item) => {
      return item.dictCode == '11'
    })
    // 12、其他影像：
    const checkJson12 = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item) => {
      return item.dictCode == '12'
    })
    // 13、其他附件：
    const checkJson13 = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item) => {
      return item.dictCode == '13'
    })
    let ArrByForm = [checkJson11, checkJson12, checkJson13];
    return ArrByForm;
  }
}

const updateItems = (arr1, arr2) => {
  arr2.forEach(item2 => {
    const indexToUpdate = arr1.findIndex(item1 => item1.id === item2.id);
    if (indexToUpdate !== -1) {
      arr1[indexToUpdate] = {...arr1[indexToUpdate], ...item2};
    }
  });
  return arr1;
}

// check
function updateIsCheck(data) {
  if (Array.isArray(data)) {
    data.forEach(item => {
      updateIsCheck(item);
    });
  } else if (data !== null && typeof data === 'object') {
    if (data.hasOwnProperty('isCheck') && !!data.isCheck) {
      data.isCheck = 1;
    } else {
      data.isCheck = 0;
    }
    if (data.hasOwnProperty('toothPosition') && !!data.toothPosition) {
      data.toothPosition = data.toothPosition.rawData;
    }
    if (data.hasOwnProperty('subsetList') && Array.isArray(data.subsetList)) {
      updateIsCheck(data.subsetList);
    }
  }
}


// 根据id找到对应的数据并更新inputContent中的数据
function updateInputContentById(obj, id, content) {
  if (obj.id == 23 || obj.id == 24 || obj.id == 25) {
    // console.log('objobj123123 :: ',obj,content);
  }
  if (obj.id == id) {
    // 操作类型(0无操作、1选中、2输入、3牙位、4图片、5文档) operateType;
    const {operateType} = obj || {};
    if (operateType == 1) {       // 1选中
      obj.isCheck = !!content ? content : false;
    } else if (operateType == 2) { // 2输入
      obj.inputContent = content;
    } else if (operateType == 3) { // 3牙位
      obj.toothPosition = content
    } else if (operateType == 4) { // 4图片
      if (content) {
        obj.fileName = content.fileName
        obj.fileSize = content.fileSize
        obj.fileSuffix = content.fileSuffix
        obj.fileUrl = content.fileUrl
        obj.fileUrlView = content.fileUrlView
      }
    } else if (operateType == 5) { // 5文档
      if (content) {
        obj.subsetList = content;
      }
    }

  } else if (obj.subsetList && obj.subsetList.length > 0 && obj.operateType != 5) {
    for (let i = 0; i < obj.subsetList.length; i++) {
      updateInputContentById(obj.subsetList[i], id, content);
    }
  }
  return obj;
}


export default {
  namespace: 'CreationOrthodontics',
  state: {
    ...initializedState
  },
  effects: {
    /**
     * 获取上级医生集合
     */
    * getSuperDoctorList({payload}, {call, put}) {
      const response = yield call(getSuperDoctorList, payload)
      if (response && response.code == 200) {
        const {content} = response || {};
        yield put({
          type: 'setState',
          payload: {
            superDoctorList: content || []
          }
        })
      }
    },
    /**
     * editConsultationNodeAndStatus
     * 正畸修改指导订单节点和状态
     * */
    * editConsultationNodeAndStatus({payload}, {call, put}) {
      const response = yield call(editConsultationNodeAndStatus, payload)
      return response;
    },
    /**
     * 参数名 类型 必填 说明  数据字典
     * consultationId [string] 订单ID
     * tenantId [string] 是 租户ID
     * customerId [long] 患者ID
     * type [string] type(0查全部、1基本信息、2检查及分析、3问题清单及诊断、4治疗方案、5影像资料)
     */
    * getDictionaryData({payload}, {call, put, select}) {
      /*const { content,code } = response || {};
      if (code == 200 && !!content) {*/
      const {type, consultationId, tenantId, customerId, orthodonticConsultationId} = payload || {};
      let response = null;
      // 判定是专家会诊创建的正畸病例类型数据
      if (!!orthodonticConsultationId) {
        response = yield call(getConsultationOrthodonticCaseInfo, {
          consultationId: orthodonticConsultationId,
          type: type,
        })
      } else {
        response = yield call(getOrthodonticCaseInfo, payload)
      }
      let DataBymedicalRecordJson = yield select(state => state.CreationOrthodontics.DataBymedicalRecordJson);
      if (response && response.code == 200) {
        let {content} = response || {};
        const {
          consultationCaseInfoDto,
          id: consultationId,          // :consultationId 8dad6e1a68714dc7b1f908902ad2d4f4
          customerId,                 // 客户id
          tenantId,                   // 租户id
          orderCaseTemplate,          // "orderCaseTemplate": 0, -- 订单病例模板 1通用病例 2正畸病例
        } = content || {};

        const {orthodonticCaseDictDtoList} = consultationCaseInfoDto || {};
        if (type == 0) {
          yield put({
            type: 'setState',
            payload: {
              DictionaryData: content,
              medicalRecordJson: orthodonticCaseDictDtoList,
              DataBymedicalRecordJson: orthodonticCaseDictDtoList
            }
          })
          return content
        } else {
          if (DataBymedicalRecordJson) {
            let arr1 = updateItems(DataBymedicalRecordJson, orthodonticCaseDictDtoList)
            yield put({
              type: 'setState',
              payload: {
                DictionaryData: content,
                medicalRecordJson: arr1,
                DataBymedicalRecordJson: arr1
              }
            })
            return content
          } else {
            yield put({
              type: 'setState',
              payload: {
                DictionaryData: content,
                medicalRecordJson: orthodonticCaseDictDtoList,
                DataBymedicalRecordJson: orthodonticCaseDictDtoList
              }
            })
            return content
          }
        }
      } else {
        yield put({
          type: 'setState',
          payload: {
            medicalRecordJson: null,
            DataBymedicalRecordJson: null
          }
        })
        return null
      }
    },

    // 新建正畸方案病例信息
    * createOrthodonticCaseInfo({payload}, {call, put, select}) {
      const {processNode} = payload || {};
      // 1 取出数据
      let DataBymedicalRecordJson = yield select(state => state.CreationOrthodontics.DataBymedicalRecordJson);
      let DictionaryDataJson = yield select(state => state.CreationOrthodontics.DictionaryData)
      const {
        consultationCaseInfoDto, // 用户信息
        id: consultationId,       // :consultationId 8dad6e1a68714dc7b1f908902ad2d4f4
        customerId,              // 客户id
        createUserId,            // 创建人id
        tenantId,                // 租户id
      } = DictionaryDataJson || {};
      if (!DictionaryDataJson) {
        return
      }
      let DataBymedicalRecordJsonClone = cloneDeep(DataBymedicalRecordJson);
      let orthodonticCaseDictDtoList = getFormDataByStep(processNode, DataBymedicalRecordJsonClone);
      // 2 取出表单数据 并转换成暂存在本地的数据
      let params = {
        id: DictionaryDataJson.id,
        expertsId: DictionaryDataJson.expertsId,
        type: DictionaryDataJson.type,
        processNode: processNode,
        tenantId: DictionaryDataJson.tenantId,
        consultationCaseInfoDto: {
          customerId: DictionaryDataJson.consultationCaseInfoDto.customerId,
          customerName: DictionaryDataJson.consultationCaseInfoDto.customerName,
          fileNumber: DictionaryDataJson.consultationCaseInfoDto.fileNumber,
          sex: DictionaryDataJson.consultationCaseInfoDto.sex,
          age: DictionaryDataJson.consultationCaseInfoDto.age,
          caseName: DictionaryDataJson.consultationCaseInfoDto.caseName ? DictionaryDataJson.consultationCaseInfoDto.caseName : '正畸专科病例',
          orthodonticCaseDictDtoList: orthodonticCaseDictDtoList,
        }
      }
      const DataByEditOrthodonticCaseInfo = yield call(editOrthodonticCaseInfo, params);
      return DataByEditOrthodonticCaseInfo;
    },

    // 保存数据并触发保存接口
    * saveDataByMedicalRecordJson({payload}, {call, put, select}) {
      // 1 取出数据
      const {
        processNode,
        formDataArr,
        isSubmit,
        age, // 年龄
        sex, // 性别
        firstQuestion, // 专家会诊-提问-描述问题
      } = payload || {};
      let DataBymedicalRecordJson = yield select(state => state.CreationOrthodontics.DataBymedicalRecordJson);
      let DictionaryDataJson = yield select(state => state.CreationOrthodontics.DictionaryData);
      let CreationOrthodontics = yield select(state => state.CreationOrthodontics);

      let {
        consultationCaseInfoDto, // 用户信息
        id: consultationId,       // :consultationId 8dad6e1a68714dc7b1f908902ad2d4f4
        customerId,              // 客户id
        createUserId,            // 创建人id
        tenantId,                // 租户id
        orderCaseTemplate,       // "orderCaseTemplate": 0, -- 订单病例模板 1通用病例 2正畸病例
      } = DictionaryDataJson || {};
      let DataBymedicalRecordJsonClone = cloneDeep(DataBymedicalRecordJson);


      // 2 取出表单数据 并转换成暂存在本地的数据
      if (Array.isArray(DataBymedicalRecordJsonClone) && Array.isArray(formDataArr)) {
        formDataArr.map((item) => {
          const {id, inputContent} = item || {};
          DataBymedicalRecordJsonClone = DataBymedicalRecordJsonClone.map((item) => {
            return updateInputContentById(item, id, inputContent)
          })
        })
        let orthodonticCaseDictDtoList = getFormDataByStep(processNode, DataBymedicalRecordJsonClone);
        // [请求接口保存病例数据] 新建或编辑正畸方案病例信息
        /**
         *   "expertsId": 0,           --会诊医生ID
         *   "id": "",                    --订单会诊ID（新增没有不传）
         *[新增]   "isFinish": 0,                --节点是否完成（参照正畸病例审核）
         *[新增]   "orderCaseTemplate": 0,       -- 订单病例模板 1. 通用病例  2正畸病例
         *   "processNode": 0,             --流程节点正畸审核[1基本信息、2检查及分析、3问题清单及诊断、4治疗方案、5影像资料、6提交病例、7病例被查看、8审核驳回、9审核通过、10会诊结束]
         *[新增]   "status": 0,
         *   "type": 0,                   --会诊类型(1图文、2视频)
         *    "attachmentCount": 0,
         *     "caseName": "",            --病例名称："会诊病例"
         *     "caseType": 0,            -- 病例类型 ：1
         *     "chiefComplaint": "",           --主诉
         */
        let params = {
          id: DictionaryDataJson.id,
          expertsId: DictionaryDataJson.expertsId,
          type: DictionaryDataJson.type,
          processNode: `${parseInt(processNode)}`,
          tenantId: DictionaryDataJson.tenantId,
          isFinish: DictionaryDataJson.isFinish,
          orderCaseTemplate: DictionaryDataJson.orderCaseTemplate,
          status: DictionaryDataJson.status,
          consultationCaseInfoDto: {
            customerId: DictionaryDataJson.consultationCaseInfoDto.customerId,
            customerName: DictionaryDataJson.consultationCaseInfoDto.customerName,
            fileNumber: DictionaryDataJson.consultationCaseInfoDto.fileNumber,
            templateType: DictionaryDataJson.orderCaseTemplate == 2 ? 2 : 1, // 模板类型(1会诊、2正畸)
            sex: !!sex && orderCaseTemplate == 2 ? sex : DictionaryDataJson.consultationCaseInfoDto.sex,
            age: !!age && orderCaseTemplate == 2 ? age : DictionaryDataJson.consultationCaseInfoDto.age,
            firstQuestion: firstQuestion && orderCaseTemplate == 2 ? firstQuestion : DictionaryDataJson.consultationCaseInfoDto.firstQuestion,
            caseName: DictionaryDataJson.consultationCaseInfoDto.caseName,
            orthodonticCaseDictDtoList: orthodonticCaseDictDtoList,
          }
        }

        // [请求接口保存病例数据] 编辑正畸方案病例信息
        let DataByEditOrthodonticCaseInfo = null;
        if (orderCaseTemplate == 2) {
          DataByEditOrthodonticCaseInfo = yield call(editConsultationInfo, params);
        } else {
          DataByEditOrthodonticCaseInfo = yield call(editOrthodonticCaseInfo, params);
        }

        const {code, content, msg} = DataByEditOrthodonticCaseInfo || {};
        if (code == 200) {
          // TODO 需要删除的提示
          // message.success('正畸病例暂存成功');
          // 保存成功后从接口刷新表单数据
          if (isSubmit) {
            yield put({
              type: 'getDictionaryData',
              payload: {
                orthodonticConsultationId: orderCaseTemplate == 2 ? consultationId : null,
                consultationId: consultationId,
                tenantId: tenantId,
                customerId: customerId,
                type: processNode,
              }
            });
          }
        } else {
          message.error(msg ? msg : '保存失败');
        }

        // 完成数据的记录后将数据保存到DataBymedicalRecordJson中
        if (isSubmit) {
          yield put({
            type: 'setState',
            payload: {DataBymedicalRecordJson: DataBymedicalRecordJsonClone}
          })
        }
      }
      return DataBymedicalRecordJsonClone
    }
  },
  reducers: {
    // 更新状态值数据
    setState(state, {payload}) {
      return {
        ...state,
        ...payload
      }
    },
    // 清空数据
    clean(state, {payload}) {
      return {
        ...state,
        ...initializedState,
      }
    }
  },
}
