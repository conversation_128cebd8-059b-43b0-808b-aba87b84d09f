import React, {useState, useEffect, useRef} from 'react';
import { history,connect } from 'umi';
import styles from "./index.less";
import {Input, message, Upload,Spin} from "antd";
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import {stringify} from "qs";
import {cloneDeep} from "lodash";
import {getOperatingEnv} from "@/utils/utils";
import classNames from "classnames";
import docxIcon from '@/assets/Consultation/H5/docx_icon.png'
import xlsxIcon from '@/assets/Consultation/H5/xlsx_icon.png'
import zipIcon from '@/assets/Consultation/H5/zip_icon.png'
import pptxIcon from '@/assets/Consultation/H5/pptx_icon.png'
import pdfIcon from '@/assets/Consultation/H5/pdf_icon.png'
import stlIcon from '@/assets/Consultation/H5/stl_icon.png'

// 其他附件
const OtherAccessories: React.FC = (props) => {
  const { fieldContent,fileContent,onUploadComplete, } = props || {}
  // 登录用户信息
  const UerInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
  const [ uploadLoading, setUploadLoading] = useState(false)            // 上传影响资料loading
  const [ fileList, setFileList ] = useState([])                       // 上传文件列表

  // 上传图片headers
  const getHeaders=() =>{
    // 获取环境类型，1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用   5 FRIDAY app中使用
    const env = getOperatingEnv()

    // FRIDAY APP中本地存储值：access_token、client、phone、user_name、wxuserId、userInfo
    return {// token
      access_token:localStorage.getItem('access_token') || '',
      username: env == 5 ? localStorage.getItem('user_name') : localStorage.getItem('vxOpenIdCipherText') ? localStorage.getItem('vxOpenIdCipherText')  : UerInfo?.phone,
      client: env == 5 ? localStorage.getItem('client') : 'WX',
      type: env === '1' ? '' : '1', // h5 传1
    }
  }

  useEffect(()=>{
    if (!!Array.isArray(fileContent)) {
      setFileList(fileContent)
    }
  },[fileContent])

  useEffect(()=>{
    // 上传完成
    onUploadComplete(fileList)
  },[fileList.length])

  // 上传文件校验规则，附件
  const beforeUploadFile = (file) => {
    // 目前服务端限制最大不超过30MB
    const isSize = file.size / 1024 / 1024 < 30
    if (!isSize) {
      message.error('超过30M限制，不允许上传~')
      return false
    }

    const { name: fileName } = file || {}
    // 后缀名
    const suffix = fileName.substring(fileName.lastIndexOf('.')+1).toLowerCase()
    // .pdf,.doc,.docx,.xlsx,.xls,.ppt,.pptx,.zip,.stl
    const isSuffixByJpgOrPng = (
      suffix == 'pdf' || suffix == 'doc' || suffix == 'docx'
      || suffix == 'xlsx' || suffix == 'xls' || suffix == 'png'
      || suffix == 'ppt' || suffix == 'pptx' || suffix == 'zip' || suffix == 'stl'
    )
    if (!isSuffixByJpgOrPng) {
      message.error('该文件类型不允许上传~')
      return false
    }
    setUploadLoading(true)
    return true
  }

  /**
   * 文件大小处理
   * @param val
   * @returns
   */
  const sizeFun = (val:number)=>{
    let result = Math.ceil(val / 1024);
    return result.toFixed(1);
  }

  const uploadOnChangeFile = (info) => {
    if (info.file.status === 'uploading') {

    }

    // 状态不为uploading时，代表上传事件结束
    if (info.file.status != 'uploading') {
      setUploadLoading(false)
    }

    // 判断当前上传已被拦截
    if (info && !info.file.status) {
      return
    }

    // 上传结束
    if (info && info.file.status === 'error') {
      message.error('上传失败')
      return
    }

    if (info && info.file.status === 'done') {
      if(info && info.file.response && info.file.response.code != 200) {
        message.error(info.file.response.msg ? info.file.response.msg : '上传失败')
        return
      }
    }

    if(info.file.response && info.file.response.code == 200 && info.file.response.content) {
      const file = info.file
      const content = file.response.content
      // 获取文件名和后缀
      const suffix = file.name.substring(file.name.lastIndexOf('.')+1).toLowerCase()
      const name = file.name.substring(0, file.name.lastIndexOf('.'))

      if (Array.isArray(fileList)) {
        const fileListClone = cloneDeep(fileList)
        fileListClone.push({
          fieldContent:fieldContent,
          type: 1,  // 资料类型(0星球影像、1其他资料、2全景片、3侧位片、4正面像、5侧面像、6正面咬合像、7正面咬合45度像、8左侧咬合像、9右侧咬合像、10上牙弓像、11下牙弓像)
          fileSize: file.size,
          fileName: name,
          fileSuffix: suffix,
          fileUrl: content.fileUrl,
          fileUrlShow: content.fileUrlView,
        })
        setFileList(fileListClone)
      }
    }
  }

  /**
   * 获取文件格式icon
   * @param suffix   文件后缀
   * @returns
   */
  const annexFormatFun = (suffix: string): any => {

    switch (suffix) {
      case 'docx':
        return <img src={docxIcon} alt={suffix} />
      case 'doc':
        return <img src={docxIcon} alt={suffix} />
      case 'xlsx':
        return <img src={xlsxIcon} alt={suffix} />
      case 'xls':
        return <img src={xlsxIcon} alt={suffix} />
      case 'zip':
        return <img src={zipIcon} alt={suffix} />
      case 'pptx':
        return <img src={pptxIcon} alt={suffix} />
      case 'ppt':
        return <img src={pptxIcon} alt={suffix} />
      case 'pdf':
        return <img src={pdfIcon} alt={suffix} />
      case 'stl':
        return <img src={stlIcon} alt={suffix} />
      default:
        return ''
    }
  }


  return (
    <div className={styles.OtherAccessories_Warp}>
      <div className={styles.OtherAccessories_btn_Warp}>
        <Spin spinning={!!uploadLoading}>
          <Upload
            headers={getHeaders()}
            accept=".pdf,.doc,.docx,.xlsx,.xls,.ppt,.pptx,.zip,.stl"
            action={`/api/server/base/uploadFile?${stringify({fileType: 22, userId: UerInfo?.friUserId})}`}
            onChange={uploadOnChangeFile}
            beforeUpload={beforeUploadFile}
            showUploadList={false}
          >
            <div className={styles.OtherAccessories_btn}>
              上传附件
            </div>
          </Upload>
        </Spin>
        <div className={styles.OtherAccessories_text}>支持ppt word zip pdf</div>
      </div>



      {(Array.isArray(fileList) && fileList.length != 0) &&
        <div className={styles.OtherAccessories_List_text}>
          {Array.isArray(fileList) && fileList.map((item,index)=>{
            return (
              <div key={index}>
                <div className={styles.OtherAccessories_List_Item}>
                  <div className={styles.OtherAccessories_List_Item_left}>
                    <div className={classNames({
                      [styles.OtherAccessories_List_type_Icon]:true,

                    })}>
                      {annexFormatFun(item.fileSuffix)}
                    </div>
                    <div className={styles.OtherAccessories_List_file_info}>
                      <div className={styles.OtherAccessories_List_file_info}>
                        <div className={styles.OtherAccessories_List_Item_name}>{item.fileName}.{item.fileSuffix}</div>
                        <div className={styles.OtherAccessories_List_Item_size}>{sizeFun(item.fileSize)}kb</div>
                      </div>
                    </div>
                  </div>
                  <div className={styles.OtherAccessories_List_Item_right}>
                    <div
                      className={styles.OtherAccessories_List_Item_delete}
                      onClick={()=>{
                        const fileListClone = cloneDeep(fileList)
                        fileListClone.splice(index, 1)
                        setFileList(fileListClone)
                      }}
                    >删除</div>
                  </div>
                </div>
                {
                  fileList.length - 1 != index &&
                  <div className={styles.OtherAccessories_List_Item_Line}></div>
                }
              </div>
            )
          })}
        </div>
      }
    </div>
  )
}

export default connect(({ CreationOrthodontics, loading }: any) => ({
  CreationOrthodontics, loading
}))(OtherAccessories)
