import React, { useCallback } from 'react';
import styles from './index.less'

const PictureInPictureButton = ({ isMobile, dispatch, props, resetTimer, playerStateData, changeUrlParams, openOrCloseH5SmallWindow }) => {

  const handleFullScreenToggle = useCallback((e) => {
    e.stopPropagation();
    resetTimer();
    const element = document.documentElement;
    openOrCloseH5SmallWindow();
  }, [isMobile, dispatch, props, resetTimer, playerStateData, changeUrlParams]);

  return (
    <div
      onClick={handleFullScreenToggle} className={styles.HorizontalLiveRoom_Btn_Warp}>
      <div className={styles.HorizontalLiveRoom_PictureInPicture_btn}></div>
      <div className={styles.text}>小窗播放</div>
    </div>
  );
};

export default PictureInPictureButton;
