/**
 * @Description: 输入密码弹窗
 */
import React, { useState, useEffect, useRef } from 'react';
import { history, connect } from 'umi'
import { useThrottle, useDebounce } from '@/utils/utils'
import { Input, Spin } from 'antd'
import { Popup } from 'antd-mobile';
import styles from './index.less';

interface PropsType {
  visible: boolean,          // 弹窗是否显示
  onCancel: () => void,      // 关闭弹窗
  onClickLeaveMeeting: () => void,   // 点击离开会议
  onClickEndMeeting: () => void,   // 点击结束会议
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {
    visible,
    loading,
    PlanetChatRoom,
    checkSpacePassword, // 校验空间密码-密码校验成功后-进入中房间
    addStarSpaceApplyAdmission, // 点击 密码弹窗中的 无密码申请入会
  } = props
  const {
    SpaceInfo,
  } = PlanetChatRoom || {}
  const {
    isNeedPwd, // 是否需要密码 需要输入密码 0：不需要 1需要
    isNoPasswordApply, // 是否允许申请无密码进入会议
  } = SpaceInfo || {}

  const inputRefs = useRef([]);  // 密码输入框ref

  const [pwdArray, setPwdArray] = useState([null,null,null,null]); // 空间密码校验

  // 密码全部输入完成 进行密码校验-密码校验成功后-进入中房间
  useEffect(() => {
    pwdArrayByFunc(pwdArray)
  },[pwdArray])

  // 密码校验
  const pwdArrayByFunc = useDebounce((pwdArray)=>{
    let pwdArrayData = pwdArray.filter((item)=>{ return !!item && item != '' })
    if (pwdArrayData.length == 4) {
      checkSpacePassword(pwdArray.join(''))
    }
  },1000)

  // 密码输入框自动聚焦
  const handleInput = useDebounce((index, value) => {
    // 使用正则表达式检查输入是否为数字
    if (!/^\d*$/.test(value) || !value) {
      // 非数字则清空输入
      inputRefs.current[index].input.value = null;
      const newArray = pwdArray.map((item, i) => (i === index ? null : item));
      setPwdArray(newArray);
    } else {
      const newValue = value ? value[0] : null; // 只取输入的第一个字符
      if (inputRefs && inputRefs.current && inputRefs.current[index] && inputRefs.current[index].input) {
        inputRefs.current[index].input.value = newValue;
        const newArray = pwdArray.map((item, i) => (i === index ? newValue : item));
        setPwdArray(newArray);

        // 自动焦点切换到下一个 input
        if (newValue && index < inputRefs.current.length - 1) {
          inputRefs.current[index + 1].focus();
        }
      }
    }
  },40);

  const handleDelete = (index, event) => {
    if (event.keyCode === 8  && index > 0) {
      // 删除键被按下，并且当前输入框为空，且不是第一个输入框
      event.preventDefault();
      inputRefs.current[index - 1].focus();
      const newArray = pwdArray.map((item, i) => (i === index ? null : item));
      setPwdArray(newArray);
    }
  };

  return (
    <Popup
      visible={isNeedPwd == 1}
      onMaskClick={props.onCancel}
      className={styles.popup_container}
      bodyStyle={{ height: '70vh' }}
      destroyOnClose
    >
      <div className={styles.container}>
        {/* 头部 */}
        <div className={styles.header_line} onClick={props.onCancel}>
          <div className={styles.header_line_bar}></div>
        </div>

        <div className={styles.header_title}>会议密码</div>

        <div className={styles.content}>
          <div className={styles.contentBox}>
            <div className={styles.pwdTitle}><span className={styles.stress}>*</span>会议密码</div>
          </div>

          <div className={styles.PwdFlex}>
            {pwdArray.map((item, index) => (
              <React.Fragment key={index}>
                <Input
                  type="number"
                  maxLength={1}
                  value={pwdArray[index]}
                  onInput={(e)=>{
                    let value = !!e.target.value ? e.target.value.trim() : null;
                    handleInput(index, value)
                  }}
                  onKeyDown={(e) => handleDelete(index, e)}
                  className={styles.PwdInput}
                  ref={(el) => (inputRefs.current[index] = el)}
                />
                {
                  index == pwdArray.length - 1 ? null : <div className={styles.line_pwd}></div>
                }
              </React.Fragment>
            ))}
          </div>

          {isNoPasswordApply == 1 &&
          <div
            className={styles.allowApplications_box}
            onClick={addStarSpaceApplyAdmission}
          >
            <div className={styles.allowApplications_box_warp}>
              <Spin className={styles.spin_box} spinning={!!loading.effects['PlanetChatRoom/updateStarSpaceApplyAdmission']}></Spin>
              <div className={styles.allowApplications_box_text}>
                无密码？向主持人申请进入会议
              </div>
            </div>
          </div>
          }
        </div>
      </div>
    </Popup>
  )
}
export default connect(({ PlanetChatRoom, loading }: any) => ({PlanetChatRoom, loading}))(Index)
