import React, { useState, useEffect } from 'react';
import { connect, history } from 'umi';
import { Form, Input, Spin, message, Button } from 'antd';
import { EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';
import { Toast } from 'antd-mobile';
import styles from './index.less';
import { debounce } from 'lodash';
import loginIcon from '@/assets/GlobalImg/logo.png';
const loginBg = 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/logoBg.png';
import goBackPc from '@/assets/GlobalImg/pc_goback.png';
import {
  getForgetPasswordMsgCode,
  resetPassword, settingResetPassword,
  updatePassword
} from "@/services/login/login";
import NavBar from '@/components/NavBar';
import { getOperatingEnv, getAES, goToHomePage } from '@/utils/utils';


const Index: React.FC = (props: any) => {
  const {dispatch} = props;
  const [form] = Form.useForm();
  const [countdown, setCountdown] = useState(0); // 验证码倒计时状态
  const [loading, setLoading] = useState(false);  // loading
  const [loadingBySendCode, setLoadingBySendCode] = useState(false); // 添加发送验证码loading
  const [userInfo, setUserInfo] = useState(localStorage.getItem('userInfo')!=undefined?JSON.parse(localStorage.getItem('userInfo')):{});// 获取本地存储的个人信息
  const [passwordVisible1, setPasswordVisible1] = useState(false); // 显示隐藏密码按钮
  const [passwordVisible2, setPasswordVisible2] = useState(false); // 显示隐藏确认密码按钮
  const {query = {}} = history?.location;
  const {inputPhone,from,exist,} = query;
  const isPhoneVisible = /^1[3-9]\d{9}$/.test(inputPhone);

  const [pageType, setPageType] = useState(); // 1pc 2 移动端
  // ① 判定当前页面视口是否小于750 如果小于750则为移动端
  let updateType = () => {
    // let clientWidth = document.documentElement.clientWidth;
    let env = getOperatingEnv() // 获取当前操作环境 1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用
    let type = env == 4 ? 1 : 2;
    setPageType(type);
  };
  updateType = debounce(updateType, 100);
  window.addEventListener('resize', updateType);
  // 进入页面判定是否存在token
  useEffect(() => {
    // ① 判定当前页面视口是否小于750 如果小于750则为移动端
    updateType();
  }, []);

  // 回显原表单数据
  useEffect(() => {
    let loginDataByform = sessionStorage.getItem('registDataByform')
    if(!!loginDataByform&&from=='login') {
      // 回显保存的临时表单数据
      setDataBySessionStorage();
    }
  }, []);

  // 回显原表单数据
  const setDataBySessionStorage=()=>{
    let loginDataByform = sessionStorage.getItem('registDataByform')
    if(!!loginDataByform){
      loginDataByform = JSON.parse(loginDataByform);
      const {
        phone:phoneByfrom,
      } = loginDataByform || {}
      form && form.setFieldsValue({
        phone:phoneByfrom,
      })
      let loginDataByformArraykeys = Object.keys(loginDataByform)
      let filterByKeys = loginDataByformArraykeys.filter((item)=>{return !!loginDataByform[item]})
      form && form.validateFields(filterByKeys)
      sessionStorage.removeItem('registDataByform');
    }
  }

  // 发送验证码并开始倒计时
  const sendCode = async () => {
    const phone = form.getFieldValue('phone');
    if (!phone) {
      pageType == 1 ? message.error('请输入手机号') : Toast.show({content: '请输入手机号'})
      return;
    }
    if(!(/^1[3456789]\d{9}$/.test(phone))) {
      pageType == 1 ? message.error('请输入正确手机号') : Toast.show({content: '请输入正确手机号'})
      return;
    }

    setLoadingBySendCode(true); // 开启发送验证码loading
    try {
      // 发送验证码请求
      const res = await getForgetPasswordMsgCode({
        phone: phone,
      });

      if (res.code === 200 && res.content) {
        setLoadingBySendCode(false); // 关闭发送验证码loading
        pageType == 1 ? message.success('验证码发送成功') : Toast.show({content: '验证码发送成功'})

        // 开始倒计时 60s
        let time = 60;
        setCountdown(time);
        const timer = setInterval(() => {
          time--;
          setCountdown(time);
          if (time === 0) {
            clearInterval(timer);
          }
        }, 1000);

      } else {
        setLoadingBySendCode(false); // 关闭发送验证码loading
        pageType == 1 ? message.error(res.msg) : Toast.show({content: res.msg})
      }
    } catch (error) {
      setLoadingBySendCode(false); // 关闭发送验证码loading
      pageType == 1 ? message.error('验证码发送失败') : Toast.show({content: '验证码发送失败'})
    }
  }

  // form提交新密码
  const onFinish = debounce(async (values) => {
    const {
      phone,     // 手机号
      username,  // 在设置中的忘记密码是手机号
      phoneCode, // 验证码
      newPassword,  // 新密码
      oldPassword,  // 旧密码
    } = values;

    // 加密下密码
    const AgetES = getAES(newPassword, 'arrail-dentail&2', 'arrail-dentail&3');

    //  判断是否设置了密码，设置了需要传旧密码且不需要密码以及手机号
    const params = exist=='false'?{
      phone: phone,
      phoneCode: phoneCode,
      newPassword: AgetES,
    }: {
      newPassword: AgetES,
      oldPassword:getAES(oldPassword, 'arrail-dentail&2', 'arrail-dentail&3'),
    };
    // 开启loading
    setLoading(true);

    // 设置&修改&忘记密码请求
    const res = (exist=='true'&&from=='login')?await settingResetPassword({
      phone:username,
      phoneCode:phoneCode,
      password: AgetES
    }):from=='login'?await resetPassword({
      phone:phone,
      phoneCode:phoneCode,
      password: AgetES
    }):await updatePassword(params);

    // 设置&修改&忘记密码成功
    if (res.code === 200&& res.content) {
      setLoading(false) // 关闭loading
      pageType == 1 ? message.success('已成功修改密码，请返回登录页重新登录!') : Toast.show({content: '已成功修改密码，请返回登录页重新登录!'});
      form.resetFields();
      localStorage.removeItem('access_token');
      localStorage.removeItem('userInfo');
      // 重定向到指定页面
      const { redirect } = history.location.query;
      if (!!redirect) {
        window.location.replace(redirect);
      } else {
        // 去登录页面
        if(from=='Setting'){
          history.replace('/User/login?from=Setting&Tab=2')
        }else{
          history.replace('/User/login?Tab=2')
        }
      }
    } else {
      setLoading(false) // 关闭登录loading
      pageType == 1 ? message.error(res.msg) : Toast.show({content: res.msg})
    }
  },1000)



  // 渲染验证码倒计时
  const renderCountdown = () => {
    if (countdown > 0) {
      return `${countdown}`;
    }
    return '获取验证码';
  };

  // pc端返回首页事件
  const pcGoHomeFn = () => {
    history.push('/home')
  }

  return pageType ? (
    <div className={pageType == 1 ? styles.pc_forgetpwd_wrap : styles.forgetpwd_wrap}>
      <div className={styles.pc_gohome} onClick={pcGoHomeFn}><img src={goBackPc} alt=""/>返回首页</div>
      <div className={styles.forgetpwd_bg_wrap}>
        <NavBar title={from=='Setting'?'设置登录密码':'找回密码'} className={styles.forgetpwdNavBar}></NavBar>
        <div className={styles.forgetpwd_bg_box}><img src={loginBg} alt="" /></div>
        <div className={styles.forgetpwd_content_title}>
          <div className={styles.forgetpwd_img}><img src={loginIcon} alt="" /></div>
          <div className={styles.forgetpwd_text}>{from=='Setting'?'设置登录密码':'找回密码'}</div>
        </div>
        <Spin spinning={!!loading}>
          <div className={styles.forgetpwd_form_wrap}>
            <Form form={form} onFinish={onFinish}>
              {
                  ((from=='Setting'&&exist!=undefined&&exist=="false")||from=='login')&&<div className={styles.forgetpwd_form_input}>
                    <Form.Item
                        label=""
                        name="phone"
                        initialValue={isPhoneVisible ? inputPhone : userInfo?userInfo.phone:null}
                        rules={[
                          { required: true, message: '请输入手机号' },
                          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的联系电话' }
                        ]}
                    >
                      <Input bordered={false} autoComplete="off" placeholder='请输入手机号' />
                    </Form.Item>
                  </div>
              }
              {
                (from=='Setting'&&exist!=undefined&&exist=="true")&&<div className={styles.forgetpwd_form_input}>
                    <Form.Item
                        label=""
                        name="username"
                        initialValue={exist!=undefined&&exist?userInfo.name:userInfo.phone}
                    >
                      <div>{exist!=undefined&&exist?`用户名：${userInfo.name}`:`手机号：${userInfo.phone}`}</div>
                    </Form.Item>
                  </div>
              }
              {
                  (from=='Setting'&&exist!=undefined&&exist=="true")&&<div className={styles.forgetpwd_form_input}>
                    <Form.Item
                        label=""
                        name="oldPassword"
                        rules={[
                          { required: true, message: '请输入旧密码' },
                          { pattern: /^[a-zA-Z0-9\s~`!@#$%^&*()_+={}\[\]|\\:;"'<>,.?/\-]{6,20}$/, message: '请输入6-20位密码，可支持字母、数字、基本符号' },
                        ]}
                    >
                      <Input autoComplete="off" bordered={false} placeholder='请输入旧密码' />
                    </Form.Item>
                  </div>
              }
              {
                ((from=='Setting'&&exist!=undefined&&exist=="false")||from=='login')&&<div className={styles.forgetpwd_form_input2}>
                    <Form.Item
                        label=""
                        name="phoneCode"
                        className={styles.phone_code}
                        rules={[
                          { required: true, message: '请输入验证码' }
                        ]}
                    >
                      <Input bordered={false} autoComplete="off" maxLength={6} placeholder='请输入验证码' />
                    </Form.Item>
                    <Spin spinning={!!loadingBySendCode}>
                      <div className={styles.sendCode} onClick={()=>{
                        if (countdown > 0) { return; }
                        sendCode()
                      }}>
                        {renderCountdown()}
                      </div>
                    </Spin>
                  </div>
              }
              <div className={from=='login'?styles.forgetpwd_form_input3:styles.forgetpwd_form_input3}>
                <Form.Item
                    label=""
                    name="newPassword"
                    rules={[
                      { required: true, message: '请输入6-20位密码' },
                      { pattern: /^[a-zA-Z0-9\s~`!@#$%^&*()_+={}\[\]|\\:;"'<>,.?/\-]{6,20}$/, message: '请输入6-20位密码，可支持字母、数字、基本符号' },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          if (from=='login'&&(getFieldValue('phone') ==null||getFieldValue('phoneCode') ==null||getFieldValue('phone') ==''||getFieldValue('phoneCode') =='')) {
                            return Promise.reject(new Error('请先输入手机号和验证码'));
                          }
                          return Promise.resolve();
                        },
                      }),
                    ]}
                >
                  <Input.Password iconRender={passwordVisible => (passwordVisible ? <EyeTwoTone style={{fontSize: '18px'}} /> : <EyeInvisibleOutlined style={{fontSize: '18px'}} />)} visibilityToggle={{ visible: passwordVisible1, onVisibleChange: setPasswordVisible1 }} autoComplete="off" bordered={false} placeholder='请设置6-20位密码' />
                </Form.Item>
              </div>
              <div className={styles.forgetpwd_form_input4}>
                <Form.Item
                    label=""
                    name="checkNewPassword"
                    rules={[
                      { required: true, message: '请再次输入密码' },
                      { pattern: /^[a-zA-Z0-9\s~`!@#$%^&*()_+={}\[\]|\\:;"'<>,.?/\-]{6,20}$/, message: '请输入6-20位密码，可支持字母、数字、基本符号' },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          if (getFieldValue('checkNewPassword')!=getFieldValue('newPassword')) {
                            return Promise.reject(new Error('两次密码输入不一致，请重新输入'));
                          }
                          return Promise.resolve();
                        },
                      }),
                    ]}
                >
                  <Input.Password iconRender={passwordVisible => (passwordVisible ? <EyeTwoTone style={{fontSize: '18px'}} /> : <EyeInvisibleOutlined style={{fontSize: '18px'}} />)} visibilityToggle={{ visible: passwordVisible2, onVisibleChange: setPasswordVisible2 }} autoComplete="off" bordered={false} placeholder='请再次输入密码' />
                </Form.Item>
              </div>
              {
                (from=='Setting'&&exist!=undefined&&exist=="true")&&<div className={styles.forget_password} onClick={()=>{
                  form.resetFields();
                  history.replace('/User/forgetPassword?from=SLogin&exist=true')
                }}>忘记旧密码?</div>
              }
              <Button className={styles.forgetpwd_Btn} htmlType='submit' loading={loading}>
                确认
              </Button>
            </Form>
          </div>
        </Spin>
      </div>
    </div>
  ) : null
}
export default connect(({ activity, loading }: any) => ({activity, loading}))(Index)
