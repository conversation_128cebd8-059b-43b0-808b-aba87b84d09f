.popup_container {
  :global {
    .adm-popup-body {
      border-radius: 12px 12px 0 0;
    }
  }
  .header {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 28px;
    .line {
      width: 48px;
      height: 4px;
      background: #d0d4d7;
      border-radius: 4px;
    }
  }
}

.titleInfo {
  width: 100%;

  .titleWarp {
    position: relative;
    // margin-top: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    padding: 0 16px;

    .titleBackIcon {
      position: absolute;
      top: 0;
      left: 16px;

      img {
        width: 12px;
        height: auto;
      }
    }

    .titleText {
      color: #000000;
      font-weight: 500;
      font-size: 17px;
      line-height: 24px;
    }
  }
}

.content {
  width: 100%;
  padding: 16px 16px 0;

  .input_box {
    position: relative;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    width: 100%;
    height: 40px;
    padding: 0 12px;
    background: #f5f5f5;
    border-radius: 23px;

    .search_icon {
      flex-shrink: 0;
      flex-shrink: 0;
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }

    :global {
      .adm-input {
        flex: 1;
      }

      .adm-input-element {
        color: #000;
        font-weight: 400;
        font-size: 14px;

        &::placeholder {
          color: #cccccc;
        }
      }
    }
  }
}
.selectByBrand {
  display: flex;
  width: 100%;
  height: 41px;
  margin-top: 10px;
  padding-right: 16px;
  padding-left: 16px;
  background: #ffffff;
  border-bottom: 1px solid #dddddd;
  border-radius: 0px 0px 0px 0px;
}

.selectByBrandItem {
  // width: 32px;
  height: 41px;
  margin-right: 15px;
  color: #000000;
  font-size: 16px;
  font-style: normal;
  line-height: 41px;
  text-align: center;
  text-transform: none;
  cursor: pointer;
  user-select: none;
}

.selectByBrandItem_active {
  position: relative;
  font-weight: 600;
  display: flex;
  flex-direction: column;
  align-items: center;
  .active_line {
    position: absolute;
    bottom: -1px;
    width: 12px;
    height: 3px;
    background: #000000;
    border-radius: 6px 6px 6px 6px;
    display: block;
  }
}

.selectByOption {
  // border-bottom: 1px solid #DDDDDD;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 53px;
  padding-right: 14px;
  padding-left: 14px;
  background: #ffffff;
  border-radius: 0px 0px 0px 0px;

  .selectByLeft {
    display: flex;
    .selectByAreaIcon {
      display: inline-block;
      width: 18px;
      height: 18px;
      margin-right: 8px;
      background: url('../../../../assets/PlanetChatRoom/selectGuest_area_icon.png');
      background-size: 18px 18px;
    }
    .selectByClinicIcon {
      display: inline-block;
      width: 18px;
      height: 18px;
      margin-right: 8px;
      background: url('../../../../assets/PlanetChatRoom/selectGuest_clinic_icon.png');
      background-size: 18px 18px;
    }
    .selectByAreaText {
      color: #222222;
      font-weight: 400;
      font-size: 15px;
      font-style: normal;
      line-height: 15px;
      text-align: left;
      text-transform: none;
    }
  }
}

.selectByLine {
  width: calc(100% - 20px);
  height: 1px;
  margin-left: 20px;
  background: #dddddd;
}

.title_warp {
  width: 100%;
  height: 44px;
  padding-right: 14px;
  padding-left: 14px;
  line-height: 44px;
  background: #f5f6f8;
  border-radius: 0px 0px 0px 0px;
}

.list_box {
  height: calc(70vh - 155px);
  padding-top: 15px;
  padding-right: 15px;
  padding-bottom: 120px;
  padding-left: 15px;
  overflow-y: auto;
}

.list_item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 16px;

  .list_item_info_wrap {
    display: flex;
    align-items: center;

    .selectGuest_select {
      width: 22px;
      height: 22px;
      margin-right: 9px;
    }

    .selectGuest_select_user {
      display: inline-block;
      width: 22px;
      height: 22px;
      background: url('../../../../assets/PlanetChatRoom/selectGuest_select_user.png');
      background-size: 22px 22px;
    }

    .selectGuest_Unselect_user {
      display: inline-block;
      width: 22px;
      height: 22px;
      background: url('../../../../assets/PlanetChatRoom/selectGuest_Unselect_user.png');
      background-size: 22px 22px;
    }

    .list_item_img {
      width: 40px;
      height: 40px;
      margin-right: 8px;
      border-radius: 50%;

      .no_comment_head {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        color: #fff;
        font-weight: 500;
        font-size: 14px;
        white-space: nowrap;
        border-radius: 50%;
      }

      img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
      }
    }

    .list_item_info {
      .nameWarp {
        display: flex;
      }
      .bizUser {
        width: 32px;
        height: 21px;
        margin-left: 11px;
        color: #06a777;
        font-weight: 400;
        font-size: 12px;
        font-style: normal;
        line-height: 21px;
        text-align: center;
        text-transform: none;
        background: #eefff9;
        border-radius: 4px 0px 4px 0px;
      }

      .name {
        margin-bottom: 4px;
        color: #000000;
        font-weight: 500;
        font-size: 15px;
        line-height: 21px;
        word-break: break-all;

        .highlight_name {
          color: #00d78b;
        }
      }

      .phone {
        color: #999999;
        font-weight: 400;
        font-size: 13px;
        line-height: 15px;
      }
    }
  }

  .active_select {
    flex-shrink: 0;
    padding: 6px 8px;
    color: #999999;
    font-weight: 400;
    font-size: 12px;
    line-height: 17px;
    background: #f5f5f5;
    border-radius: 18px;
  }

  .init_select {
    flex-shrink: 0;
    padding: 6px 12px;
    color: #0095ff;
    font-weight: 400;
    font-size: 12px;
    line-height: 17px;
    background: #e6f4ff;
    border-radius: 18px;
  }
}

.nodata {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 4px;
  color: #333333;
  font-weight: 400;
  font-size: 15px;
  font-family: PingFang SC;
  line-height: 17px;

  img {
    width: 150px;
    height: 113px;
  }

  .empty_title {
    margin-bottom: 8px;
    color: #333;
    font-size: 15px;
    line-height: 21px;
  }
  .empty_msg {
    color: #999;
    font-size: 14px;
    line-height: 20px;
  }
}

.btn_wrap {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
}

.tip {
  display: flex;
  align-items: center;
  width: 100%;
  height: 30px;
  padding-right: 10px;
  /* justify-content: center; */
  padding-left: 10px;
  font-style: normal;
  text-align: left;
  text-transform: none;
  background: #ebf5ff;
  border-radius: 0px 0px 0px 0px;

  .tip_box {
    width: 100%;
    overflow: hidden; /* 确保超出容器的文本被裁剪 */
    color: #666666;
    font-weight: 400;
    font-size: 10px;
    line-height: 12px;
    white-space: nowrap; /* 确保文本在一行内显示 */
    text-overflow: ellipsis; /* 使用省略号表示文本超出 */
  }
}

.btn_box {
  display: flex;
  width: 100%;
  padding: 16px;
  background: #fff;

  .btn_wrap_left {
    display: flex;
    align-items: center;
    width: calc(100% - 80px);
    height: 50px;
    overflow-y: auto;
    scrollbar-width: 0;

    .item {
      width: 40px;
      height: 40px;
      margin-right: 8px;
      border-radius: 50%;
    }

    .no_comment_head {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      color: #fff;
      font-weight: 500;
      font-size: 14px;
      white-space: nowrap;
      border-radius: 50%;
    }

    img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
    }
  }
}

.btn_wrap_right {
  display: flex;
  justify-content: flex-end;
  width: 80px;
  height: 44px;

  .btn_submit {
    width: 74px;
    height: 44px;
    color: #ffffff;
    font-weight: 400;
    font-size: 16px;
    font-style: normal;
    line-height: 44px;
    text-align: center;
    text-transform: none;
    background: #0095ff;
    border-radius: 22px 22px 22px 22px;
  }

  .btn_submit:active {
    background: #0180da;
  }
}

.box_area {
  width: 100%;
  height: calc(90vh - 238px);
  margin-top: 10px;
  overflow-x: auto;
}

.box_user {
  width: 100%;
  height: calc(90vh - 238px);
  margin-top: 10px;
  padding-right: 15px;
  padding-left: 15px;
  overflow-x: auto;
}
