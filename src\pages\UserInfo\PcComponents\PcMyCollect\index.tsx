/**
 * @Description: pc端-个人中心-收藏tab页
 */
import React, { useState } from 'react';
import { history, connect } from 'umi';
import styles from './index.less';
import classNames from 'classnames';
import { Spin } from 'antd';
import PcMyCollectSpace from './PcMyCollectSpace';
import PcMyCollectMeeting from './PcMyCollectMeeting';
import PcMyCollectCase from './PcMyCollectCase';
import {stringify} from "qs"


const tabLists = [
  { id: 1, val: '直播' },
  { id: 3, val: '会议' },
  { id: 2, val: '病例' },
]

const Index: React.FC = (props: any) => {
  const { loading, dispatch, pcAccount } = props;
  const { query } = history.location

  const [tabType, setTabType] = useState(query.subTabKey || pcAccount?.subTabState || 1);

  const onClickTabFun = (tabKey:any) => {
    history.replace(`${history.location.pathname}?${stringify({
      ...history.location.query,
      subTabKey: tabKey,
    })}`)

    setTabType(tabKey)
    dispatch({
      type: 'pcAccount/save',
      payload: {
        subTabState: tabKey
      }
    })
  }

  const getCollectListLoading = !!loading.effects['userInfoStore/getCollectList']; // 获取收藏列表loading
  const getStarSpaceCollectLoading = !!loading.effects['userInfoStore/getStarSpaceCollect']; // 获取收藏直播or会议列表loading

  return <>
    <div className={styles.content}>
      <div className={styles.tab_wrap}>
        {
          tabLists.map(item => {
            return <div key={item.id} className={classNames({[styles.tab_init]: true, [styles.tab_active]: tabType == item.id })} onClick={() => { onClickTabFun(item.id) }}>{item.val}</div>
          })
        }
      </div>
      <Spin wrapperClassName={styles.tab_content} spinning={getCollectListLoading || getStarSpaceCollectLoading}>
        {/* 直播 */}
        {tabType == 1 && <PcMyCollectSpace starSpaceType={1} />}

        {/* 会议 */}
        {tabType == 3 && <PcMyCollectMeeting starSpaceType={2}  />}

        {/* 病例 */}
        {tabType == 2 && <PcMyCollectCase />}
      </Spin>
    </div>
  </>
}
export default connect(({ userInfoStore, pcAccount, loading }: any) => ({ userInfoStore, pcAccount, loading }))(Index)
