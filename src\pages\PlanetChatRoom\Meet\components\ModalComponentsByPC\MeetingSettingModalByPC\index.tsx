/**
 * @Description: 会议设置弹窗（PC端）
 */
import React, { useState, useEffect } from 'react';
import { history, connect } from 'umi'
import { Drawer, Switch } from 'antd';
import styles from './index.less';

interface PropsType {
  visible: boolean,          // 弹窗是否显示
  onCancel: () => void,      // 关闭弹窗
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {
    visible,
    PlanetChatRoom,
    onCancel, // 关闭弹窗
    onChangeIsSpectatorUse, // 不允许非参会人进入开关修改
    onChangeHandUpType, // 允许申请发言开关修改
    onChangeIsShowPassword, // 展示会议密码开关修改
  } = props

  const {
    SpaceInfo, // 直播间信息
  } = PlanetChatRoom || {}

  const {
    isSpectatorUse, // 是否允许非参会人进入 1 是 0否(页面中1为关0为开)
    handUpType, // 举手类型    0:开启 1:关闭
    isShowPassword, // 是否展示密码 1是 0否
    password, // 直播间密码
  } = SpaceInfo || {}

  const [loadingSwitch, setLoadingSwitch] = useState(null) // 开关loading状态，null 不loading，1，2，3分别表示三个开关

  // 不允许非参会人进入开关修改
  const onChangeIsSpectatorUseFun = async (checked) => {
    setLoadingSwitch(1)
    await onChangeIsSpectatorUse(checked)
    setLoadingSwitch(null)
  }

  // 允许申请发言开关修改
  const onChangeHandUpTypeFun = async (checked) => {
    setLoadingSwitch(2)
    await onChangeHandUpType(checked)
    setLoadingSwitch(null)
  }

  // 展示会议密码开关修改
  const onChangeIsShowPasswordFun = async (checked) => {
    setLoadingSwitch(3)
    await onChangeIsShowPassword(checked)
    setLoadingSwitch(null)
  }

  return (
    <Drawer
      open={visible}
      className={styles.drawer}
      placement="right"
      onClose={onCancel}
      destroyOnClose
      closable={false}
      maskStyle={{background: 'rgba(0,0,0,0)'}}
      width={248}
    >
      <div className={styles.header}>更多设置</div>
      <div className={styles.action_item}>
        <div className={styles.action_item_label}>不允许非参会人进入</div>
        <Switch checked={isSpectatorUse == 0} onChange={onChangeIsSpectatorUseFun} loading={loadingSwitch == 1} />
      </div>
      <div className={styles.action_item}>
        <div className={styles.action_item_label}>允许申请发言</div>
        <Switch checked={handUpType == 1} onChange={onChangeHandUpTypeFun} loading={loadingSwitch == 2} />
      </div>
      <div className={styles.action_item}>
        <div className={styles.action_item_label}>展示会议密码</div>
        <Switch checked={isShowPassword == 1} disabled={!password} onChange={onChangeIsShowPasswordFun} loading={loadingSwitch == 3} />
      </div>
    </Drawer>
  )
}
export default connect(({ loading, PlanetChatRoom }: any) => ({ loading, PlanetChatRoom }))(Index)
