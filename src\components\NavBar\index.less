
.nav_bar_wrap {
  position: fixed;
  z-index: 990;
  left: 0;
  right: 0;
  top: 0;
  width: 100%;
  height: 44px;
  background: #fff;
  font-size: 17px;
  font-weight: 500;
  color: #000;
  &.nav_bar_box_pc {
    max-width: 750px;
    margin: 0 auto;
  }
  &.bordered {
    border-bottom: 1px solid #ddd;
  }
}

.nav_bar {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  .nav_bar_title {
    max-width: 180px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .nav_bar_icon {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 36px;
    background: url("../../assets/GlobalImg/go_back.png") no-repeat 16px center;
    background-size: 12px 24px;
    &.icon_white {
      background: url("../../assets/GlobalImg/go_back_white.png") no-repeat 16px center;
      background-size: 12px 24px;
    }
  }
  .nav_bar_icon.share_page {
    background: url("../../assets/GlobalImg/home_back.png") no-repeat 12px center;
    background-size: 24px 24px;
    &.icon_white {
      background: url("../../assets/GlobalImg/home_back_white.png") no-repeat 12px center;
      background-size: 24px 24px;
    }
  }
  .left {
    position: absolute;
    left: 36px;
    top: 50%;
    transform: translateY(-50%);
  }
  .right {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);   // 会影响子元素position样式
  }
}

