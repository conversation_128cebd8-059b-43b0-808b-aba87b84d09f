/**
 * @Description: 查看完整服务流程按钮及弹窗，移动端
 */
import React, { useState } from 'react'
import classNames from 'classnames'
import { QuestionCircleOutlined, CheckOutlined } from '@ant-design/icons'
import styles from './index.less'

// 完整服务流程弹窗
import CompleteProcessModal from '@/components/ConsultationSteps/CompleteProcessModal'

interface PropsType {
}

const Index: React.FC<PropsType> = (props: any) => {
  const [completeProcessVisible, setCompleteProcessVisible] = useState(false)

  // 打开完整服务流程弹窗
  const onClickCompleteProcessBtn= () => {
    setCompleteProcessVisible(true)
  }

  // 关闭完整服务流程弹窗
  const completeProcessModalHide = () => {
    setCompleteProcessVisible(false)
  }

  return (
    <>
      <div className={styles.text_btn_wrap} onClick={onClickCompleteProcessBtn}>
        <QuestionCircleOutlined/>
        <span>查看完整服务流程</span>
      </div>

      {/* 完整服务流程弹窗 */}
      <CompleteProcessModal
        visible={completeProcessVisible}
        onCancel={completeProcessModalHide}
      />
    </>
  )
}

export default Index
