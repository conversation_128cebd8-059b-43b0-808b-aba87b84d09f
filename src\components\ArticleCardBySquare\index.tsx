/**
 * @Description: 广场推荐页-文章卡片组件（H5）
 */
import React, { useEffect, useRef, useState } from 'react'
import { history } from 'umi'
import { stringify } from 'qs'
import { useInView } from 'react-intersection-observer'  // 判断元素是否可见插件（作用：用户停留在某一内容超过3秒，展示评论输入框）
import { message } from 'antd'
import { ImageViewer } from 'antd-mobile'
import styles from './index.less'

// 图片图标
import arrow_icon from '@/assets/Consultation/H5/arrow_icon.png' // 右箭头

import UserCardByImageText from '@/components/UserCardByImageText' // 用户卡片信息公共组件
import ListComments from '@/components/ListComments' // 点赞/评论

interface PropsType {
  style?: object,                                 // 样式
  isMyPages?: any,  // 是否是从主页过来的
  item?:any,        // 文章详情数据
  disabledClick?: boolean,        // 是否禁用点击事件
  pageType: any, // 从哪个页面过来的标识，推荐首页过来传 1:场推荐 2:话题页面 3:我的主页草稿箱 4:国王详情 5:搜索页面
  refreshDataById: any,
}

const article_default_cover_img_1 = 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/article_default_cover_img_1.png' // 文章默认封面

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {
    style = {},
    isMyPages,
    item,
    disabledClick,
    pageType,
    refreshDataById,
    onClickItem,
    isShowMoreOperate = false, // 是否展示点点点更多操作
  } = props

  // 元素是否可见的配置
  const { ref, inView } = useInView({
    threshold: 0.6,  // 何时触发回调的阈值
  })
  const timer = useRef(null); // 当前内容可见并停留3秒，展示评论框定时器
  const refImageViewer = useRef(null) // 查看大图ref

  const [showComments , setShowComments] = useState(false); // 是否展示评论框
  const [imageViewerVisible, setImageViewerVisible] = useState(false) // 图片查看器

  let {
    createDate,       //: [创建时间] : "2024-01-09 14:53:47"
    createUserId,     //: [创建人id] : 60
    expertsInfo,      //: [专家信息] : null
    forwardDescribe,  //: [转发描述] : null
    gdp,              //: [页面GDP] : 210
    headUrlShow,      //: [用户头像] : null
    id,               //: [主键ID] : 57
    imageTextContent, //: [文章、帖子内容] : null
    imageTitle,       //: [标题] :null
    imageType,        //: [图文类型：1.文章 2.帖子 3.外链 4.空间] : 2
    isExperts,        //: [是否是专家：0:否，1:是] : 0
    isFocus,          //: [0未关注 1已关注] : 0
    isForward,        //: [是否转发：1.转发 0，非转发] : null
    isSpotLike,       //: [是否点赞 1是 0否] : 0
    kingdomId,        //: [关联王国ID] : 1
    kingdomName,      //: [关联王国名称] : "数字化讨论"
    outerChain,       //: [外链地址] : null
    spaceId,          //: [空间ID] :null
    spaceStatus,      //: [空间状态: 1直播中、2预约中、3弹幕轰炸中] : null
    spotLikeCount,    //: [点赞数量] : 0
    spotLikeUserList, //: [点赞用户信息，最多3条] : []
    textImgList,      //: [关联的图片] : null
    topicInfoList,    //: [关联的话题信息] : null
    userName,         //: [用户名称] : "志君"
    status,           // 状态：1.审核通过（已发布） 0.未审核 2.审核未通过 3.草稿
    operateDateDescs,
    videoUrl, // 文章第1个视频的url
  } = item || {};

  // 滚动监听事件，改变导航栏样式
  useEffect(() => {
    window.addEventListener('popstate', browserBack)

    return () => {
      ImageViewer.clear()
      window.removeEventListener('popstate', browserBack)
    }
  }, [])

  useEffect(() => {
    // 是推荐首页,则展示评论框
    if (pageType == '1') {
      clearTimeout(timer.current);
      // 判断当前内容是否可见,并停留3秒时,展示评论框
      if (inView) {
        timer.current = setTimeout(() => {
          console.log('显示')
          setShowComments(true);
        }, 3000)
      } else {
        setShowComments(false);
      }
    }
  }, [inView]);

  // 点击卡片内容事件
  const onClickItemFn = (e) => {
    e.stopPropagation()
    e.preventDefault()
    if (onClickItem) {
      onClickItem(id)
      return
    }
    // 转发页禁止点击
    if (disabledClick) {
      return
    }
    // 是从我的主页过来的，并且为草稿，需要跳转编辑页
    if (isMyPages && status == 3) {
      return history.push(`/CreateGraphicsText/CreateArticle?${stringify({id: id})}`);
    }
    if (isMyPages && status == 2) {
      message.warning('审核未通过')
      history.push(`/CreateGraphicsText/CreateArticle?id=${id}`);
      return
    }
    if (status == 0) {
      message.warning('审核中')
      return
    }
    history.push(`/CreateGraphicsText/ArticleDetails?id=${id}`)
  }

  // 点击视频
  const onClickVideo = (e) => {
    e.stopPropagation()
  }

  // 点击图片查看大图
  const onClickImg = (e, index) => {
    e.stopPropagation()
    if (imageViewerVisible) {
      return
    }
    // 为了在手机上手势返回时，不返回上一页，而是关闭弹窗
    window.history.pushState(null, null, document.URL)
    setImageViewerVisible(true)
    refImageViewer?.current?.swipeTo(index)
  }

  // 查看大图，关闭
  const imageViewerOnClose = () => {
    if (!imageViewerVisible) {
      return
    }
    window.history.go(-1)
    setImageViewerVisible(false)
  }

  // 浏览器返回事件
  const browserBack = () => {
    setImageViewerVisible(false)
  }

  // 关注或取消关注回调，0 取消关注，1 关注
  const handleFollowAndCheck = (isFocus2) => {
    refreshDataById(id)
  }

  // 删除或下架回调
  const handleDeleteOrLow = () => {
    refreshDataById(id)
  }

  return (
    <div className={styles.article_wrap} style={style} ref={ref}>
      {/* 用户信息 */}
      <UserCardByImageText
        headUrlShow={headUrlShow}
        userName={userName}
        createUserId={createUserId}
        isExperts={isExperts}
        operateDateDescs={operateDateDescs}
        isFocus={isFocus}
        expertsInfo={expertsInfo}
        handleFollowAndCheck={handleFollowAndCheck}
        isShowMoreOperate={isShowMoreOperate}
        id={id}
        imageType={imageType}
        status={status}
        handleDeleteOrLow={handleDeleteOrLow}
        style={{marginBottom: 16}}
      />
      {/* 文章内容 */}
      <div className={styles.article_content} onClick={onClickItemFn}>
        <div className={styles.article_title}>{imageTitle}</div>
        <div className={styles.article_user}>作者：{userName}</div>
        <div className={styles.article_img_wrap} style={{height: textImgList && textImgList.length == 3 ? 72 : 160}}>
          {
            textImgList && textImgList.length > 0 ?
              textImgList.map((item, index) => {
                return (
                  <div key={index} className={styles.article_img} onClick={(e) => onClickImg(e, index)}>
                    <img src={item.imageUrlShow} width={'100%'} height={'100%'} alt=""/>
                  </div>
                )
              }) : videoUrl ?
              <>
                <video
                  src={videoUrl}
                  controls
                  onClick={onClickVideo}
                  poster={videoUrl+`?x-oss-process=video/snapshot,t_0,f_jpg,w_630,h_320,m_fast,ar_auto`}
                ></video>
              </>
              :
              <div className={styles.article_img} onClick={(e) => onClickImg(e, 0)}>
                <img src={article_default_cover_img_1} width={'100%'} height={'100%'} alt=""/>
              </div>
          }
        </div>
        {/* 按钮 */}
        <div className={styles.article_btn_wrap}>
          <div className={styles.article_btn}>
            <span>查看文章详情</span>
            <img src={arrow_icon} width={18} height={18} alt=""/>
          </div>
        </div>
      </div>
      {/* 评论组件 */}
      <ListComments
        showComments={showComments}
        pageType={pageType}
        commentJupm={onClickItemFn}
        item={item}
      />

      {/* 图片查看器 */}
      <ImageViewer.Multi
        ref={refImageViewer}
        images={textImgList && textImgList.length > 0 ? textImgList.map(item => item.imageUrlShow) : [article_default_cover_img_1]}
        visible={imageViewerVisible}
        defaultIndex={0}
        onClose={imageViewerOnClose}
        getContainer={document.body}
      />
    </div>
  )
}

export default Index
