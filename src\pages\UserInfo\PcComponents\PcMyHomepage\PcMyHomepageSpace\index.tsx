import NoDataRender from '@/components/NoDataRender';
import React, { useEffect, useRef, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroller';
import { connect } from 'umi';
import {Modal, Checkbox, Button} from 'antd';
import styles from './index.less';
import SpaceCard from '@/componentsByPc/SpaceCard';
import { throttle } from 'lodash';
import noScreenIcon from '@/assets/Expert/no_screen_icon.png'   // 无筛选图标
import screenIcon from "@/assets/Expert/screen_icon.png";
import MeetingCardInfoByPC from "@/components/MeetingCardInfoByPC";

const initState = {
  total: 0,
  listDate: [],
}

const spaceJoinTypeList = [
  {
    id: 1,
    name: '我主持的直播',
  },
  {
    id: 2,
    name: '我预约的直播',
  },
  {
    id: 3,
    name: '历史记录',
  }
]


const spaceStatusList = [
  {
    id: null,
    name: '全部',
  },
  {
    id: 2,
    name: '预约中',
  },
  {
    id: 1,
    name: '进行中',
  },
  {
    id: 3,
    name: '已结束',
  }
]

const PcMyHomepageSpace: React.FC<any> = (props) => {
  const { dispatch, starSpaceType } = props;
  // 个人中心空间列表筛选条件
  let myHomeSpaceFilter = JSON.parse(sessionStorage.getItem('myHomeSpaceFilter'))|| {};
  let initStatePage = {
    pageNum: 1,
    hasMore: true,  // 加载更多
    loadMore: false,
    spaceJoinType:starSpaceType==2?null:myHomeSpaceFilter?.spaceJoinType||1,  // 空间角色类型 1:作为主持 2:预约的 3:作为嘉宾
    spaceStatus:myHomeSpaceFilter?.spaceStatus||null, // 空间状态 1:预约中 2:进行中 3:已结束
  }

  const scrollParentRef = useRef<HTMLDivElement | null>(null);
  const [state, setState] = useState(initState)             // 列表数据
  const [statePage, setStatePage] = useState(initStatePage)  // 当前分页
  const { total, listDate } = state
  const { pageNum, hasMore, loadMore, spaceJoinType, spaceStatus } = statePage || {}

  useEffect(() => {
    sessionStorage.getItem('myHomeSpaceFilter')&&sessionStorage.removeItem('myHomeSpaceFilter');
    getList(1)
  }, [starSpaceType, spaceJoinType, spaceStatus])

  // 获取空间列表数据
  const getList = (page:any, size:number) => {
    // 如果是第一页，滚动条回到顶部
    if(page=== 1){
      document.getElementById('tab_content_listBody')?.scrollTo(0, 0);
    }
    const UerInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    dispatch({
      type: "expertAdvice/getStarSpaceListBySearchUserId",
      payload: {
        pageNum: page,
        pageSize: size || 30,
        searchUserId: UerInfo?.friUserId, // 检索用户id
        starSpaceType,  // 空间类型 1:直播 2:会议
        ...(spaceJoinType ? { spaceJoinType } : {}),   // 空间角色类型 1:作为主持 2:预约的 3:作为嘉宾
        ...(spaceStatus ? { spaceStatus } : {}),   // 空间状态 1:预约中 2:进行中 3:已结束
      },
    }).then((res: any) => {
      const { code, content } = res || {};
      const { pageNum, total, resultList } = content || {};

      if (res && code == 200) {
        let data = pageNum == 1 ? [] : listDate;
        data = data.concat(resultList || []);
        const hasMore = data.length !== total;

        if (Array.isArray(data) && data.length == 0) {
          setState({
            ...state,
            listDate: [],
            total: 0,
          })
          return
        }
        setState({
          ...state,
          listDate: [...data],
          total,
        })
        setStatePage({
          ...statePage,
          loadMore: false,
          hasMore,
          pageNum: page && size ? statePage.pageNum : pageNum,
        })
      }
    }).catch((err) => {
      console.log(err)
    });
  }

  // 滚动加载分页
  let handleInfiniteOnLoad = () => {
    if (listDate.length > total - 1) {
      setStatePage({
        ...statePage,
        loadMore: false,
        hasMore: false
      })
      return;
    }
    const pages = pageNum + 1;
    setStatePage({
      ...statePage,
      loadMore: true,
    })
    getList(pages)
  }

  handleInfiniteOnLoad = throttle(handleInfiniteOnLoad, 100);

  // 下架成功后，刷新数据
  const refreshFn = () => {
    getList(1, listDate.length)
  }

  return (
    <div className={styles.tab_content_list} id={'tab_content_list'}>
      <div className={styles.tab_spaceRoleType_list}>{
        spaceJoinTypeList.map((item, index) => {
          return <span key={index} onClick={() => {
            setStatePage({
              ...statePage,
              spaceStatus: null, // 直播状态 0:全部 1:预约中 2:进行中 3:已结束
              spaceJoinType: item.id,
            })
          }} className={item.id == spaceJoinType ? styles.spaceRoleTypeActive : ''}>{item.name}</span>
        })}</div>
      <div className={styles.tab_spaceStatus_list}>{
        spaceStatusList.map((item, index) => {
          return <span key={index} onClick={() => {
            let valueArr;
            if(item.id == spaceStatus||(spaceStatus&&spaceStatus.includes(item.id))){
              if(spaceStatus){
                valueArr =spaceStatus.split(',').filter(i=>i!=item.id).join(',');
              }
            }else{
              if (item.id) {
                const ids = spaceStatus?spaceStatus.split(','):[];
                if (ids.includes(item.id)) {
                  valueArr = spaceStatus;
                } else {
                  valueArr = ids.concat(item.id).join(',');
                  // if (valueArr.split(',').length == 3) {
                  //   valueArr = null;
                  // }
                }
              } else {
                valueArr = null;
              }
            }
            setStatePage({
              ...statePage,
              spaceStatus: valueArr,
            })
          }} className={(item.id == spaceStatus||(spaceStatus&&spaceStatus.includes(item.id))) ? styles.spaceStatusActive : ''}>{item.name}</span>
        })
      }
      </div>
      {/*<div className={styles.tab_space_title}>共 {listDate && listDate.length} 条内容</div>*/}
      <div className={styles.tab_content_listBody} id={'tab_content_listBody'} ref={(ref) => (scrollParentRef.current = ref)}>
        <InfiniteScroll
          loadMore={handleInfiniteOnLoad}
          threshold={50}
          pageStart={1}
          initialLoad={false}
          hasMore={!loadMore && hasMore}
          useWindow={false}
          getScrollParent={() => scrollParentRef.current}
          className={styles.scroll_box}
        >
          <div className={styles.space_wrap}>
            {listDate && listDate.length ? (
              listDate.map((item: any, ind) => (
                <div className={styles.space_list} key={item?.id}>
                  <SpaceCard spaceData={item} myHomeSpaceFilter={statePage} whereFrom={2} isMyPage={true} refreshFn={refreshFn}/>
                </div>
              ))
            ) : (
              <div className={styles.no_data_wrap}><NoDataRender className={styles.noDataStyle}/></div>
            )}
          </div>
        </InfiniteScroll>
      </div>
    </div>
  );
};

export default connect(({expertAdvice, loading}: any) => ({expertAdvice, loading}))(
  PcMyHomepageSpace,
);
