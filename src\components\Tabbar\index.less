.tabbar_container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background: #fff;
  z-index: 799;
  border-top: 1px solid #EEEEEE;
  // padding-bottom: constant(safe-area-inset-bottom);//兼容 IOS<11.2
  // padding-bottom: env(safe-area-inset-bottom);//兼容 IOS>11.2
  padding-bottom: 12px;
  &.tabbar_container_pc {
    max-width: 750px;
    margin: 0 auto;
  }
  .box {
    display: flex;
    flex-wrap: nowrap;
    height: 49px;
    .tabbar_item {
      flex: 1;
      padding-top: 3px;
      display: flex;
      align-items: center;
      flex-direction: column;
      .tabbar_item_icon {
        width: 24px;
        height: 24px;
        margin-bottom: 2px;
        &.icon_1 {
          background: url("../../assets/GlobalImg/tabbar_1.png") no-repeat center;
          background-size: 100%;
        }
        &.icon_2 {
          background: url("../../assets/GlobalImg/tabbar_2.png") no-repeat center;
          background-size: 100%;
        }
        &.icon_3 {
          background: url("../../assets/GlobalImg/tabbar_3.png") no-repeat center;
          background-size: 100%;
        }
      }
      .tabbar_item_text {
        font-size: 12px;
        line-height: 17px;
        color: #AAAAAA;
      }
      &.checked {
        .tabbar_item_icon {
          &.icon_1 {
            background: url("../../assets/GlobalImg/tabbar_1_checked.png") no-repeat center;
            background-size: 100%;
          }
          &.icon_2 {
            background: url("../../assets/GlobalImg/tabbar_2_checked.png") no-repeat center;
            background-size: 100%;
          }
          &.icon_3 {
            background: url("../../assets/GlobalImg/tabbar_3_checked.png") no-repeat center;
            background-size: 100%;
          }
        }
        .tabbar_item_text {
          color: #000;
        }
      }
    }
  }
}

