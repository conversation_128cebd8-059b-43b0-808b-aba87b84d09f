/**
 * @Description: 选择可见企业/品牌用户
 */
import React, { useState, useEffect, useCallback } from 'react';
import { connect } from 'umi';
import styles from './index.less';
import GoBackIcon from '@/assets/GlobalImg/go_back.png'; // 返回图片
import { Spin } from 'antd';
import { Toast } from 'antd-mobile';
import classNames from 'classnames';
import topArrow from '@/assets/GlobalImg/top_arrow.png';
import botArrow from '@/assets/GlobalImg/bot_arrow.png';
import sosoIcon from '@/assets/GlobalImg/soso.png';
import checkIcon from '@/assets/GlobalImg/check_icon.png'; // 勾选小图标
import NoDataRender from '@/components/NoDataRender';


export type ListDateItem = {
  tenantId: number;
  name: string;
  // 是否全部
  isAllUser: any;
  // 区域源数据，从接口获取
  areaList: { id: number; name: string, isChecked: boolean }[];
  // 机构源数据，从接口获取
  orgList: { id: number; name: string, isChecked: boolean }[];
};

type PropTypes = {
  userInfoStore: {
    enterpriseUserData: ListDateItem[];
    enterpriseUserTab: number;
  };
  [key: string]: any;
};

const Index: React.FC<PropTypes> = (props) => {
  const { goBack, userInfoStore, dispatch, loading } = props || {};
  const { enterpriseUserTab, enterpriseUserSelectData } = userInfoStore || {};
  const [list, setList] = useState<any>([]); // 初始化数据
  const [selectList, setSelectList] =useState([]); // 选中的数据
  const [regionIsShow, setRegionIsShow] = useState(true); // 选择区域收起/展开
  const [institutionIsShow, setInstitutionIsShow] = useState(true); // 选择机构收起/展开


  useEffect(() => {
    setRegionIsShow(true);
    setInstitutionIsShow(true);
  }, []);

  useEffect(() => {
    getBizGroupByTenantId();
  }, [])

  // 获取企业品牌机构
  const getBizGroupByTenantId = useCallback(() => {
    dispatch({
      type: 'userInfoStore/getBizGroupByTenantId',
      payload: {
        spaceId: '', // 空间ID，编辑必传
      }
    }).then((res:any) => {
      const {code, content} = res || {};
      if(res && code == 200) {
        setList(content);

        // 找到选中的数据并赋值state
        let options = content.map(({tenantId, isAllUser, areaList, orgList}) => ({
          tenantId, // 企业id
          isAllUser, // 是否选中所有人 0 未选中 1 选中
          areaList: areaList.filter(it => it.isChecked).map(val => val.id), // 区域
          orgList: orgList.filter(it => it.isChecked).map(val => val.id), // 机构
        }))
        console.log(enterpriseUserSelectData, '？？？')
        setSelectList(enterpriseUserSelectData.length && enterpriseUserSelectData || options)
      }
    }).catch((err:any) => console.log(err))
  }, [dispatch])


  /**
   * 选择区域（多选）
   * @param item 当前选择的区域id
   * @param ind  tab标签页的索引
   *  */
  const selectRegionFn = useCallback(
    (item, ind) => {
      setSelectList((prev: any) =>
        prev.map((it:any, index:number) => {
          if (index === ind) {
            if(it.areaList.find(it => it === item)) {
              return {
                ...it,
                isAllUser: 0,
                areaList: it.areaList.filter((area:any) => area !== item)
              }
            } else {
              return {
                ...it,
                isAllUser: 0,
                areaList: [...it.areaList, item]
              }
            }
          }
          return it;
        }),
      );
    },
    [dispatch, selectList],
  );

  /**
   * 选择机构（多选）
   * @param item 当前选择的机构id
   * @param ind  tab标签页的索引
   *  */
  const selectInstitution = useCallback(
    (item, ind) => {
      setSelectList((prev: any) =>
        prev.map((it:any, index:number) => {
          if (index === ind) {
            if(it.orgList.find(it => it === item)) {
              return {
                ...it,
                isAllUser: 0,
                orgList: it.orgList.filter((org:any) => org !== item)
              }
            } else {
              return {
                ...it,
                isAllUser: 0,
                orgList: [...it.orgList, item]
              }
            }
          }
          return it;
        }),
      );
    },
    [dispatch],
  );

  /**
   * 所有员工点击事件
   * @param ind 当前操作的tab标签页索引
   * 通过index找到当前操作的数据，设置所有员工项为选中，并清空选择的地区、机构
   * 所有员工与【机构/区域】互斥
   *  */
  const allStaffClickFn = useCallback(
    (ind: number) => {
      setSelectList((prev: any) =>
        prev.map((it:any, index:number) => {
          if (index === ind) {
            return {
              ...it,
              isAllUser: 1,
              areaList: [],
              orgList: [],
            }
          }
          return it;
        }),
      );
    },
    [dispatch],
  );

  // 确定按钮
  const submitFn = () => {
    // 判断是否有选中所有人的数据，有，则算一个分组，无，则展示所有人
    let arr = selectList.filter(v => !v.isAllUser);
    let sum = 0;
    // 统计选中了多少个分组
    if(arr && arr.length) {
      selectList.forEach(item => {
        if(item?.isAllUser) {
            sum += 1;
        } else {
          sum += Number(item?.areaList?.length + item?.orgList?.length);
        }
      })
    }
    // 未选择时提示
    if (selectList.length == 0) {
      Toast.show('请选择企业/品牌用户')
      return
    }

    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        createModalVisible: false,
        spectatorType: 2, // 观众类型 0默认所有人 1指定王国成员可见 2指定企业品牌用户可见
        enterpriseText: sum ? `企业/品牌用户可见，已选${sum}个分组` :  `企业/品牌用户可见，已选所有成员`,
        enterpriseUserSelectData: selectList, // 选择后的企业用户数据
        selectedKingdomAudience: [], // 选择王国成员
        allViewersState: false, // 所有人员
        enterpriseUserTab: 0, // tab切换
      }
    })
    // goBack(1);
  };

  // 搜索机构
  const goSearOrg = useCallback((item) => {
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        enterpriseUserData: item, // 企业用户
        enterpriseUserSelectData: selectList, // 选择后的企业用户数据
      }
    })
    goBack(13);
  },[selectList])

  // 返回按钮
  const headGoBack = () => {
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        // enterpriseUserTab: 0, // tab切换
        createModalVisible:false, // 关闭弹框
      }
    })
    // goBack(7)
  }

  const getBizGroupByTenantIdLoading = !!loading.effects['userInfoStore/getBizGroupByTenantId']; // loading

  return (
    <div className={styles.container}>
      <div className={styles.title_box}>
        <div className={styles.title_btn} onClick={headGoBack}>
          <img src={GoBackIcon} width={12} height={24} alt="" />
        </div>
        <div className={styles.title}>选择可见企业/品牌用户</div>
      </div>
      <Spin spinning={getBizGroupByTenantIdLoading} >
        {
          list && list.length ?
          <div className={styles.wrap}>
            <div className={styles.tabs_box}>
              {list &&
                list.map((item, ind) => {
                  return (
                    <div
                      key={item.tenantId}
                      className={classNames({
                        [styles.tabs_item]: true,
                        [styles.checked]: enterpriseUserTab == ind,
                      })}
                      onClick={() => {
                        setRegionIsShow(true);
                        setInstitutionIsShow(true);
                        dispatch({
                          type: 'userInfoStore/setTaskListState',
                          payload: {
                            enterpriseUserTab: ind,
                          },
                        });
                      }}
                    >
                      {item.name}
                    </div>
                  );
                })}
            </div>
            {list &&
              list.map((item, ind) => (
                <div
                  className={classNames({
                    [styles.content]: true,
                    [styles.contentShow]: enterpriseUserTab == ind,
                  })}
                  key={item.tenantId}
                >
                  <div className={styles.all_staff} onClick={() => allStaffClickFn(ind)}>
                    <div>所有员工</div>
                    {selectList && selectList[ind] && selectList[ind].isAllUser ? (
                      <img className={styles.right_check} src={checkIcon} alt="" />
                    ) : null}
                  </div>
                  <div className={styles.region_wrap}>
                    <div className={styles.region_top_wrap}>
                      <div className={styles.region_content_left}>选择区域</div>
                      <div
                        className={styles.region_content_right}
                        onClick={() => setRegionIsShow(!regionIsShow)}
                      >
                        <img className={styles.arrow} src={regionIsShow ? topArrow : botArrow} alt="" />
                      </div>
                    </div>
                    {regionIsShow ? (
                      <div className={styles.region_bot_wrap}>
                        {item.areaList &&
                          item.areaList.map((region:any) => {
                            return (
                              <div
                                key={region.id}
                                className={classNames({
                                  [styles.init_style]: true,
                                  [styles.checked_style]: selectList && selectList[ind] && selectList[ind].areaList.find(it => it === region.id)
                                })}
                                onClick={() => selectRegionFn(region.id, ind)}
                              >
                                {region.name}
                              </div>
                            );
                          })}
                      </div>
                    ) : null}
                  </div>
                  <div className={styles.institution_wrap}>
                    <div className={styles.institution_top_wrap}>
                      <div className={styles.institution_content_left}>
                        选择机构
                        <img
                          className={styles.arrow_soso}
                          src={sosoIcon}
                          alt=""
                          onClick={() => goSearOrg(item)}
                        />
                      </div>
                      <div
                        className={styles.institution_content_right}
                        onClick={() => setInstitutionIsShow(!institutionIsShow)}
                      >
                        <img
                          className={styles.arrow}
                          src={institutionIsShow ? topArrow : botArrow}
                          alt=""
                        />
                      </div>
                    </div>
                    {institutionIsShow ? (
                      <div className={styles.institution_bot_wrap}>
                        {item.orgList &&
                          item.orgList.map((institution, i) => (
                            <div
                              key={institution.id}
                              className={classNames({
                                [styles.init_style]: true,
                                [styles.checked_style]: selectList && selectList[ind] && selectList[ind].orgList.find(it => it === institution.id)
                              })}
                              onClick={() => selectInstitution(institution.id, ind, i)}
                            >
                              {institution.name}
                            </div>
                          ))}
                      </div>
                    ) : null}
                  </div>
                </div>
              ))}
          </div> :
          <div className={styles.no_data_wrap_box}><NoDataRender className={styles.noDataStyle} /></div>
        }
      </Spin>
      <div className={styles.fixed_box}>
        <div className={styles.btn_box}>
          <div className={styles.btn} onClick={submitFn}>
            确定
          </div>
        </div>
      </div>
    </div>
  );
};

export default connect(({ userInfoStore, loading }: any) => ({ userInfoStore, loading }))(Index);
