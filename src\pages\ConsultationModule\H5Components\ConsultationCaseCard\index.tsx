/**
 * @Description: 移动端/PC端指导病例卡片信息
 */
import React, { useState, useEffect, useMemo } from 'react'
import { history } from 'umi'
import classNames from 'classnames'
import { stringify } from 'qs'
import { message } from 'antd'
import { getOperatingEnv, getArrailUrl } from '@/utils/utils'
import styles from './index.less'

// 移动端，病例详情弹窗，非模板
import CaseDetailsModal from '../CaseDetailsModal'
// 移动端，病例详情弹窗，模板
import CaseDetailsByTemplateModal from '../CaseDetailsByTemplateModal'

// PC版，病例详情弹窗，非模板
import CaseDetailsModalByPc from '../../PcComponents/CaseDetailsModal'
// PC版，病例详情弹窗，模板
import CaseDetailsByTemplateModalByPc from '../../PcComponents/CaseDetailsByTemplateModal'

interface PropsType {
  isShowBtn?: boolean,                 // 是否展示编辑按钮，true 展示，false 不展示
  isDoctor?: number,                   // 是否为医生，1 是医生（隐藏头部），0 不是医生
  caseData: any,                       // 病例数据dto
  consultationId: string,              // 指导ID
  onClickEditBtn?: any,                      // 点击编辑回调事件（目前在创建指导第4步传了）
  orderCaseTemplate: any,                       // 1 通用模板，2 正畸模板
}
const Index: React.FC<PropsType> = (props: any) => {
  // 是否嵌套在5i5ya的iframe中
  const isInIframe = (self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya')

  const {
    isShowBtn = true,
    isDoctor = 0,
    caseData = {},
    pageFrom = '', // ConsultationDetails 表示来自指导详情页
    consultationId,
    orderCaseTemplate, // 1通用模板，2正畸模板
    tenantId, // 租户id
  } = props
  const [caseDetailsVisible, setCaseDetailsVisible] = useState(false)                        // 病例详情弹窗
  const [caseDetailsByTemplateVisible, setCaseDetailsByTemplateVisible] = useState(false)    // 使用模板的病例详情弹窗
  const [caseDescriptionArr, setCaseDescriptionArr] = useState([])


  // iframe 嵌入到我爱我牙   不显示编辑
  const isShowEditBtn = useMemo(() => {
    return isShowBtn && !tenantId;
  }, [isShowBtn, tenantId]);

  useEffect(() => {
    if (caseData.id && caseData.isTemplate == 1) {
      const arr = []
      if (caseData.sex) {
        arr.push(caseData.sex)
      }
      if (caseData.age) {
        arr.push(caseData.age)
      }
      if (caseData.chiefComplaint) {
        arr.push(caseData.chiefComplaint)
      }
      if (caseData.presentDisease) {
        arr.push(caseData.presentDisease)
      }
      if (caseData.previousHistory) {
        arr.push(caseData.previousHistory)
      }
      if (caseData.wholeHealth) {
        arr.push(caseData.wholeHealth)
      }
      if (caseData.checkUp) {
        arr.push(caseData.checkUp)
      }
      if (caseData.diagnosis) {
        arr.push(caseData.diagnosis)
      }
      setCaseDescriptionArr(arr)
    }
  }, [caseData.id])

  // 查看病例详情
  const lookCaseDetails = () => {
    if (orderCaseTemplate == 1) {
      if (caseData.isTemplate == 1) {
        setCaseDetailsByTemplateVisible(true)
      } else if (caseData.isTemplate == 0) {
        setCaseDetailsVisible(true)
      }
    } else {
      if (getOperatingEnv() == '4') {
        // 在5i5ya的iframe中
        if (isInIframe) {
          const postData = {
            dataType: 'pathname',       // 页面地址onchange事件
            pathnameByChild: `/ConsultationModule/OrthodonticCasesDetail/${caseData.consultationId}`,  // 路由信息
            searchByChild: `?${stringify({
              type: 2,                // 表示图文、视频会诊-正畸病例详情
            })}`,  // 路由信息
          }
          console.log('子级发送数据：', postData, getArrailUrl())
          window.parent.postMessage(postData, getArrailUrl())
          return
        }
        history.push(`/ConsultationModule/OrthodonticCasesDetail/${caseData.consultationId}?type=2`)
      } else {
        history.push(`/ConsultationModule/H5OrthodonticCasesDetail/${caseData.consultationId}?type=2`)
      }
    }
  }

  // 病例详情弹窗关闭
  const caseDetailsModalHide = () => {
    setCaseDetailsByTemplateVisible(false)
    setCaseDetailsVisible(false)
  }

  // 编辑
  const caseEdit = (e) => {
    e.stopPropagation()
    e.preventDefault()

    // 创建指导第4步里点编辑走这
    if (props.onClickEditBtn) {
      props.onClickEditBtn()
      return
    }

    if (orderCaseTemplate == 1) {
      // 在5i5ya的iframe中
      if (isInIframe && getOperatingEnv() == '4') {
        const postData = {
          dataType: 'pathname',       // 页面地址onchange事件
          pathnameByChild: '/ConsultationModule/StartConsultation/Step3',  // 路由信息
          searchByChild: `?${stringify({
            consultationId: consultationId,                // 指导ID
            pageFrom,
          })}`,  // 路由信息
        }
        console.log('子级发送数据：', postData, getArrailUrl())
        window.parent.postMessage(postData, getArrailUrl())
        return
      }

      history.push({
        pathname: '/ConsultationModule/StartConsultation/Step3',
        query: {
          consultationId: consultationId,                // 指导ID
          pageFrom,
        }
      })
    } else if (orderCaseTemplate == 2) {
      // 在5i5ya的iframe中
      if (isInIframe) {
        const postData = {
          dataType: 'pathname',       // 页面地址onchange事件
          pathnameByChild: '/CreationOrthodontics/Step1',  // 路由信息
          searchByChild: `?${stringify({
            orthodonticConsultationId: consultationId,                // 指导ID
            pageFrom,
          })}`,  // 路由信息
        }
        console.log('子级发送数据：', postData, getArrailUrl())
        window.parent.postMessage(postData, getArrailUrl())
        return
      }

      history.push({
        pathname: '/CreationOrthodontics/Step1',
        query: {
          orthodonticConsultationId: consultationId,                // 指导ID
          pageFrom,
        }
      })
    }
  }

  // 诊断展示（正畸模板）
  const diagnosisFun = (orthodonticCaseDictDtoList) => {
    if (Array.isArray(orthodonticCaseDictDtoList) && orthodonticCaseDictDtoList.length) {
      const result = orthodonticCaseDictDtoList.find(item => item.id === 4);
      const { subsetList } = result || {}
      if (Array.isArray(subsetList) && subsetList.length) {
        return (
          <>
            {
              subsetList.map((item: any, index: number) => {
                if (item.id != 223) {
                  return (
                    <>
                      <span key={index}>{index ? '、' : null}{item.dictName}</span>
                      {
                        item.subsetList.map((val: any, idx: number) => {
                          if (val.id != 215 && val.operateType == 1 && val.isCheck == 1) {
                            return (<span key={idx}>{val.dictName}</span>)
                          }

                        })
                      }
                    </>
                  )
                }
              })
            }
          </>
        )
      }
    }
  }

  // 影像展示（正畸模板）
  const imageDisplayFun = (orthodonticCaseDictDtoList) => {
    if (Array.isArray(orthodonticCaseDictDtoList) && orthodonticCaseDictDtoList.length) {
      const result = orthodonticCaseDictDtoList.find(item => item.id === 7);
      const { subsetList } = result || {}
      if (Array.isArray(subsetList) && subsetList.length) {
        return (
          <div className={styles.case_img}>
            {
              subsetList.map((item, index) => {
                if (!item.fileUrlShow) {
                  return
                }
                // 最多展示9张
                if (index > 8) {
                  return null
                }
                return <div key={index} className={styles.img} style={{ backgroundImage: `url(${item.fileUrlShow})` }}></div>
              })
            }
          </div>
        )
      }
    }
  }

  return (
    <div className={classNames(styles.wrap, {
      [styles.wrap_pc]: getOperatingEnv() == '4',
    })}>
      {
        isDoctor != 1 &&
        <div className={styles.header}>
          <div className={styles.header_title}>病例信息</div>
          {
            isShowEditBtn && !(getOperatingEnv() != '4' && orderCaseTemplate == 2) && <div className={styles.header_btn} onClick={caseEdit}>编辑</div>
          }
        </div>
      }
      <div className={styles.content}  onClick={lookCaseDetails}>
        <div className={styles.case_title}>{caseData.caseName}</div>
        {/*
          caseData.depSubjectDictsStrList && caseData.depSubjectDictsStrList.length > 0 &&
          <div className={styles.case_course}>
            {
              caseData.depSubjectDictsStrList.map((item, index) => <div key={index}>{item}</div>)
            }
          </div>
        */}
        {
          orderCaseTemplate == 2 ?
            <div className={styles.case_desc}>
              <div className={styles.desc_title}>病例描述：</div>
              <div className={styles.desc}>{diagnosisFun(caseData.orthodonticCaseDictDtoList)}</div>
            </div>
            : caseData.isTemplate == 1 ?
            <div className={styles.case_desc}>
              <div className={styles.desc_title}>病例描述：</div>
              <div className={styles.desc}>{caseDescriptionArr.join('、')}</div>
            </div>
            : caseData.isTemplate == 0 ?
              <div className={styles.case_desc}>
                <div className={styles.desc_title}>病例描述：</div>
                <div className={styles.desc}>{caseData.noTemplateDescription}</div>
              </div>
              : null
        }

        {/* 影像 */}
        {/* 通用模板 */
          orderCaseTemplate == 1 && caseData.consultationCaseMediaDtoList && caseData.consultationCaseMediaDtoList.length > 0 &&
          <div className={styles.case_img}>
            {
              caseData.consultationCaseMediaDtoList.map((item, index) => {
                if (item.type == 0) {
                  return <div key={index} className={styles.img} style={{backgroundImage: `url(${item.fileUrlShow})`}}></div>
                }
                return null
              })
            }
          </div>
        }
        {/* 正畸模板 */
          orderCaseTemplate == 2 && imageDisplayFun(caseData.orthodonticCaseDictDtoList)
        }

        {
          caseData.isTemplate == 1 && caseData.treatmentPlanList && caseData.treatmentPlanList.length > 0 &&
          <div className={styles.case_content}>
            治疗方案：
            {
              caseData.treatmentPlanList.join('；')
            }
          </div>
        }

      </div>

      {
        getOperatingEnv() == '4' ?
          <>
            <CaseDetailsModalByPc
              visible={caseDetailsVisible}
              caseData={caseData}
              onCancel={caseDetailsModalHide}
            />
            <CaseDetailsByTemplateModalByPc
              visible={caseDetailsByTemplateVisible}
              caseData={caseData}
              onCancel={caseDetailsModalHide}
            />
          </>
          :
          <>
            <CaseDetailsModal
              visible={caseDetailsVisible}
              caseData={caseData}
              onCancel={caseDetailsModalHide}
            />
            <CaseDetailsByTemplateModal
              visible={caseDetailsByTemplateVisible}
              caseData={caseData}
              onCancel={caseDetailsModalHide}
            />
          </>
      }



    </div>
  )
}
export default Index
