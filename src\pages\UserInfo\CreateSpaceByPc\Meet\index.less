.container {
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  height: 100vh;
  background: #eef3f9;
}

.inner_container {
  flex: 1;
  width: 100%;
  padding-bottom: 16px;
  overflow-y: auto;
}
.wrap {
  width: 816px;
  margin: 0 auto;
  .header {
    display: flex;
    align-items: center;
    height: 62px;
    margin: 16px 0;
    padding: 17px 24px;
    background: #fff;
    border-radius: 8px;
    .header_title {
      color: #000000;
      font-weight: 600;
      font-size: 20px;
      .header_title_icon {
        width: 24px;
        height: 24px;
        margin-right: 10px;
        vertical-align: sub;
        cursor: pointer;
      }
    }
  }
}

.content {
  padding: 40px 40px 40px;
  background: #ffffff;
  border-radius: 8px;

  .form_item_name {
    display: flex;
    width: 100%;

    .form_box_img {
      width: 169px;
      height: 96px;
      margin-right: 24px;
      // background: #7ec1ff;
      border-radius: 4px 4px 4px 4px;

      .form_box_img_box {
        position: relative;
        width: 100%;
        height: 100%;
        cursor: pointer;
        user-select: none;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
        .space_form_box_btn {
          position: absolute;
          top: 50%;
          left: 50%;
          z-index: 1;
          width: 60px;
          height: 24px;
          color: #ffffff;
          font-weight: 400;
          font-size: 11px;
          line-height: 24px;
          text-align: center;
          text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.5);
          background: rgba(0, 0, 0, 0.3);
          border-radius: 30px 30px 30px 30px;
          transform: translate(-50%, -50%);
        }
      }
    }
  }

  .form_input {
    flex: 1;
    height: 54px;
    border-bottom: 1px solid #dddddd;

    :global {
      .ant-input::placeholder {
        color: #aaaaaa;
        font-weight: 600;
        font-size: 22px;
      }
      .ant-input {
        padding: 0;
        font-size: 22px;
      }
    }
  }
  .form_text_area {
    margin-top: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #dddddd;
    :global {
      .ant-input::placeholder {
        color: #999999;
        font-weight: 400;
        font-size: 14px;
      }
      .ant-input {
        padding: 0;
      }
    }
  }

  .from_wrap {
    margin-top: 36px;
    .from_content {
      display: flex;
      :global {
        .ant-select-item {
          padding: 0 16px;
        }
        .ant-select-dropdown {
          padding: 0;
        }
        .ant-select-item-option-selected:not(.ant-select-item-option-disabled),
        .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
          background: none;
        }
      }
      .from_title {
        width: 100px;
        padding-right: 15px;
        color: #000000;
        font-weight: 400;
        font-size: 14px;
        line-height: 34px;
        text-align: right;
      }

      .from_radio {
        flex: 1;
        :global {
          .ant-form-item {
            margin-bottom: 12px;
          }
          .ant-radio-wrapper {
            margin-right: 25px;
          }
        }
      }


      .upload_banner_courseware {
        // position: absolute;
        width: 120px;
        height: 120px;
        cursor: pointer;
        background: #F8F8F8;
        overflow: hidden;
        border: #F8F8F8 1px solid;
        cursor: pointer;
        text-align: center;
        line-height: 120px;

        .init_upload_img {
          width: 32px;
          height: 32px;
        }
      }

      .init_upload_tip {
        margin-top: 8px;
        font-weight: 400;
        font-size: 9px;
        color: #999999;
        line-height: 14px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

    }

    .start_date_time_wrap {
      // margin-left: 100px;
      display: flex;
    }
  }

  .from_desc {
    position: relative;
    flex: 1;

    :global {
      .ant-select-item {
        padding: 0 16px;
      }
      .ant-select-dropdown {
        padding: 0;
      }
      .ant-select-item-option-selected:not(.ant-select-item-option-disabled),
      .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
        background: none;
      }
    }

    .modal_wrap {
      opacity: 0;
    }

    :global {
      .ant-cascader-menu {
        width: 212px;
      }
      .ant-cascader-menu-item:hover {
        color: #0095ff;
        background: none;
      }
      .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled),
      .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled):hover {
        color: #0095ff;
        font-weight: 500;
        background: none;
      }

      .ant-select-selection-placeholder {
        color: #aaaaaa;
        font-weight: 400;
        font-size: 14px;
      }

      .ant-modal-wrap {
        z-index: 888;
      }

      .ant-modal-mask {
        z-index: 888;
        opacity: 0 !important;
      }

      .ant-modal {
        width: 0 !important;
      }
    }
    .init_text {
      color: #aaaaaa;
      font-weight: 400;
      font-size: 14px;
      line-height: 16px;
    }
    .audience_input,
    .audience_input_focus {
      position: relative;
      display: inline-block;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      width: 400px;
      // width: 100%;
      min-width: 0;
      height: 32px;
      margin: 0;
      padding: 4px 11px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 14px;
      // padding: 0;
      font-variant: tabular-nums;
      line-height: 1.5715;
      list-style: none;
      background-color: #fff;
      background-image: none;
      border: 1px solid #d9d9d9;
      border-radius: 2px;
      transition: all 0.3s;
      font-feature-settings: 'tnum', 'tnum';
    }

    .init_select_icon {
      position: absolute;
      right: 8px;
    }
    .audience_input:hover {
      border-color: var(--ant-primary-5);
      border-right-width: 1px;
    }
    .audience_input_focus {
      border-color: var(--ant-primary-color-hover);
      border-right-width: 1px;
      outline: 0;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      box-shadow: 0 0 0 2px var(--ant-primary-color-outline);
    }
    .audience_input_disabled {
      color: rgba(0, 0, 0, 0.25);
      background: #f5f5f5;
      border: 1px solid #d9d9d9;
      cursor: not-allowed;
    }
    .select_wrap {
      position: absolute;
      top: 34px;
      left: 0;
      z-index: 999;
      display: flex;
      // width: 542px;
      height: 190px;
      background: #ffffff;
      border: 1px solid #dcdfe6;
      border-radius: 4px 4px 4px 4px;
      box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.1);
      opacity: 1;
    }
    .right_arrow_icon {
      width: 14px;
      height: 14px;
    }
    .select_first,
    .select_two {
      width: 180px;
      height: 190px;
      margin: 0;
      padding: 12px 10px 6px 20px;

      .select_first_li,
      .select_two_li {
        height: 34px;
        color: #606266;
        font-weight: 400;
        font-size: 14px;
        cursor: pointer;
        .select_first_span,
        .select_two_span {
          display: inline-block;
          width: 135px;
          color: #606266;
          font-weight: 400;
          font-size: 14px;
          &.selected {
            color: #0095ff;
            font-weight: 500;
          }
        }
      }
    }
    .select_two {
      border-left: 1px solid #dcdfe6;
    }
    .select_three {
      width: 200px;
      padding: 12px 6px 12px 12px;
      overflow-y: auto;
      border-left: 1px solid #dcdfe6;
      :global {
        .ant-checkbox-wrapper + .ant-checkbox-wrapper {
          margin-left: 0;
        }
        .ant-checkbox + span {
          display: flex;
        }
      }
      .select_three_title {
        margin: 6px 0;
        color: #999999;
        font-weight: 400;
        font-size: 12px;
      }
      .three_content {
        width: 100%;
        height: 160px;
        overflow-y: scroll;

        :global {
          .ant-checkbox-wrapper {
            width: 100%;
            margin-bottom: 12px;
          }
          .ant-checkbox-wrapper:last-child {
            margin-bottom: 0;
          }
        }
      }
      .select_three_content {
        padding-left: 8px;
        :global {
          .ant-checkbox-wrapper {
            margin-bottom: 12px;
          }
          .ant-checkbox-wrapper:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  .space_password_wrap {
    width: 400px;
    margin-bottom: 15px;
    margin-left: 100px;
  }

  // 上传封面
  .upload_wrap {
    margin-bottom: 24px;
    padding-left: 110px;
    .tips {
      margin-top: 8px;
      color: #999999;
      font-weight: 400;
      font-size: 12px;
    }
    :global {
      .ant-upload-select-picture-card {
        width: 200px;
        height: 112px;
        margin: 0;
        background: #f8f8f8;
        border: 0;
      }
      .ant-form-item {
        margin-bottom: 8px;
      }
    }
    .upload_box {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 200px;
      height: 112px;
      overflow: hidden;

      :global {
        .ant-spin-container {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 200px;
          height: 112px;
        }
      }

      .edit_btn_box {
        position: absolute;
        bottom: 8px;
        left: 50%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 117px;
        height: 23px;
        padding: 0 27px;
        background: rgba(0, 0, 0, 0.5);
        transform: translateX(-50%);

        div {
          color: #ffffff;
          font-weight: 400;
          font-size: 11px;
          line-height: 13px;
          cursor: pointer;
        }
      }

      .upload_img {
        width: 100%;
        height: 100%;
      }
      .init_upload_img {
        width: 32px !important;
        height: 32px !important;
        margin: 0 auto;
      }
    }
  }

  // 上传空间广告
  .upload_banner_wrap {
    margin-bottom: 24px;
    padding-left: 110px;
    .tips {
      margin-top: 8px;
      color: #999999;
      font-weight: 400;
      font-size: 12px;
    }
    :global {
      .ant-upload-select-picture-card {
        width: 350px;
        height: 53px;
        margin: 0;
        background: #f8f8f8;
        border: 0;
      }
      .ant-form-item {
        margin-bottom: 8px;
      }
    }

    :global {
      .ant-spin-container {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 350px;
        height: 53px;
      }
    }

    .edit_btn_box {
      position: absolute;
      top: 17px;
      left: 50%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 117px;
      height: 23px;
      padding: 0 27px;
      background: rgba(0, 0, 0, 0.5);
      transform: translateX(-50%);

      div {
        color: #ffffff;
        font-weight: 400;
        font-size: 11px;
        line-height: 13px;
        cursor: pointer;
      }
    }

    .upload_banner_box {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 350px;
      height: 53px;

      .upload_banner_img {
        width: 100%;
        height: 100%;
      }

      .init_upload_banner_img {
        width: 32px !important;
        height: 32px !important;
        margin: 0 auto;
      }
    }
  }

  // 上传空间视频
  .upload_video_wrap {
    margin-bottom: 24px;
    padding-left: 110px;

    .form_error_message {
      color: var(--ant-error-color);
      font-size: 14px;
      line-height: 1.5715;
    }

    .tips {
      margin-top: 8px;
      color: #999999;
      font-weight: 400;
      font-size: 12px;
    }

    .upload_video_content {
      position: relative;
      width: 120px;
      height: 120px;
      overflow: hidden;
      background: #f8f8f8;

      .edit_btn_box {
        position: absolute;
        bottom: 0;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 23px;
        background: rgba(0, 0, 0, 0.5);

        div {
          color: #ffffff;
          font-weight: 400;
          font-size: 11px;
          line-height: 13px;
          cursor: pointer;
        }
      }

      .uploading_icon_content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 120px;
        height: 120px;
        background: #f8f8f8;

        .init_upload_img {
          width: 32px;
          height: 32px;
        }

        .uploading_icon_text {
          margin-top: 8px;
          color: #999999;
          font-weight: 400;
          font-size: 14px;
          line-height: 16px;
        }

        .video_name_text {
          display: flex;
          margin-top: 8px;
          color: #999999;
          font-weight: 400;
          font-size: 14px;
          line-height: 16px;

          .uploading_name_text {
            display: block;
            max-width: 70px;
            overflow: hidden;
            white-space: nowrap;
          }
        }
      }

      .uploading_init_icon_content {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 120px;
        height: 120px;
        .init_upload_img {
          width: 32px;
          height: 32px;
        }
      }

      .upload_video_input {
        position: absolute;
        top: 0;
        left: 0;
        display: block !important;
        width: 120px;
        height: 120px;
        cursor: pointer;
        opacity: 0;
      }

      .edit_btn_input {
        position: relative;

        .upload_video_input {
          position: absolute;
          top: -5px;
          left: 0;
          display: block !important;
          width: 22px;
          height: 23px;
          opacity: 0;
        }
      }
    }
  }
}

// 主持人\嘉宾下拉样式
.select_dropdown_content {
  padding-top: 16px;
  overflow: hidden;
  border-radius: 4px;
  .dropdown_content_input {
    position: relative;
    height: 36px;
    margin: 0 16px 12px;
    padding: 0 12px 0 25px;
    background: #f5f6f8;
    border-radius: 4px;
    & > img {
      position: absolute;
      top: 10px;
      left: 12px;
      width: 16px;
      height: 16px;
    }
    :global {
      .ant-input {
        line-height: 30px;
      }
      .ant-input::placeholder {
        color: #999999;
      }
    }
  }
  .dropdown_content_title {
    padding: 0 16px;
    color: #999;
    font-size: 12px;
    line-height: 17px;
  }
}
.select_option_content {
  position: relative;
  display: flex;
  align-items: center;
  height: 48px;
  padding-right: 16px;
  color: #666;
  font-size: 14px;
  border-bottom: 1px solid #f5f6f8;
  cursor: pointer;
  &:hover {
    .option_content_name {
      color: #0095ff;
    }
    .option_content_phone {
      color: #0095ff;
    }
  }
  .option_content_name {
    max-width: 300px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .option_content_phone {
    display: block;
    width: 120px;
    padding: 0 12px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .option_content_img {
    position: absolute;
    top: 50%;
    right: 11px;
    display: none;
    width: 24px;
    height: 24px;
    transform: translateY(-50%);
    & > img {
      width: 100%;
      height: 100%;
    }
  }
}

.content {
  :global(.ant-select-item-option-selected) {
    .select_option_content {
      .option_content_name,
      .option_content_phone {
        color: #0095ff;
      }
      .option_content_img {
        display: block;
      }
    }
  }
}

.from_content {
  :global {
    .ant-select-item-option-state {
      display: none;
    }
    .ant-select-item-option-disabled.ant-select-item-option-selected {
      background: none;
    }
  }
}

.space_password_wrap {
  :global {
    .ant-select-item-option-state {
      display: none;
    }
    .ant-select-item-option-disabled.ant-select-item-option-selected {
      background: none;
    }
  }
}

.footer {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  width: 100%;
  height: 80px;
  background: #ffffff;
  border-top: 1px solid #dddddd;
  .footer_content {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 816px;
    margin: 0 auto;
    text-align: right;

    :global {
      .ant-btn-primary {
        background: #0095ff;
        border-radius: 4px;
      }
    }

    .appointment_start_wrap {
      position: relative;
      cursor: pointer;

      .picker_input {
        position: absolute;
        top: 0;
        left: 0;
        opacity: 0;
      }
    }
  }
}

// 关联王国下拉框
.custom_dropdown_render {
  :global {
    ::-webkit-scrollbar {
      width: 7px;
      height: 7px;
    }
    ::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border: 2px solid #fff;
      border-radius: 10px;
      -webkit-box-shadow: inset 0 0 0 5px rgba(0, 0, 0, 0.2);
    }
    ::-webkit-scrollbar-track {
      background: #fff;
      // -webkit-box-shadow: inset 0 0 0 5px rgba(0,0,0,0.2);
      border-radius: 0px;
    }
  }
  .select_dropdown_container {
    max-height: 256px;
    padding-bottom: 16px;
    overflow-y: auto;
    .select_dropdown_title {
      padding: 12px 16px 4px;
      color: #666;
      font-size: 14px;
    }
    .kingdom_item {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #f5f6f8;
      cursor: pointer;
      &:hover {
        background: #f5f5f5;
      }
      &.selected {
        background: #e6f7ff;
      }
      & > i {
        flex-shrink: 0;
        width: 40px;
        height: 40px;
        margin-right: 8px;
        color: #fff;
        font-size: 12px;
        font-style: normal;
        line-height: 40px;
        white-space: nowrap;
        text-align: center;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
      }
      .kingdom_item_details {
        flex: 1;
        .kingdom_name {
          margin-bottom: 2px;
          color: #000;
          font-weight: 500;
          font-size: 15px;
          line-height: 21px;
        }
        .kingdom_info_box {
          display: flex;
          align-items: center;
          color: #999;
          font-size: 12px;
          line-height: 17px;
          & > span + span {
            margin-left: 4px;
          }
        }
      }
    }
  }
}

.title_item_box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 500px;
  margin-bottom: 20px;
  cursor: pointer;
  user-select: none;

  .title_item {
    color: #000000;
    font-weight: 500;
    font-size: 16px;
    font-style: normal;
    line-height: 16px;
  }

  .title_item_btn {
    height: 13px;
    color: #999999;
    font-weight: 700;
    font-size: 13px;
    line-height: 13px;
    border-radius: 0px 0px 0px 0px;
  }
}

.from_box_warp_hidden {
  display: none;
}

.space_allowApplications {
  position: relative;
  top: 1px;
  left: 3px;
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url('~@/assets/PlanetChatRoom/CreateSpace_allowApplications_icon.png');
  background-size: 100% 100%;
  cursor: pointer;
  user-select: none;
}

.space_box {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .space_title {
    color: #000000;
    font-weight: 400;
    font-size: 15px;
    line-height: 15px;
  }
  .space_open_btn {
  }
}

.space_password_wrap_formItem {
  margin-bottom: 0;
}

.guestIdList {
  display: flex;
  align-items: center;
  width: 400px;
  height: 34px;
  border: 1px solid #dddddd;
  border-radius: 3px;
  cursor: pointer;

  .guestIdListLeft {
    display: flex;
    flex: 1;
    align-items: center;
    height: 100%;
    padding-left: 10px;
    overflow-y: auto;
    scrollbar-height: none;
  }
  .guestIdListLeft::-webkit-scrollbar {
    height: 0;
  }

  .guestIcon {
    display: inline-block;
    width: 18px;
    height: 18px;
    padding-right: 12px;
    padding-left: 12px;
    background: url('~@/assets/Payment/CreateSpaceByPc_selectGuest.png');
    background-repeat: no-repeat;
    background-size: 18px 18px;
  }

  .guestplaceholder {
    color: #aaaaaa;
    font-weight: 400;
    font-size: 14px;
    font-style: normal;
    line-height: 16px;
    text-align: left;
    text-transform: none;
  }
}

.from_switch {
  width: 500px;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: space-between;
  .from_switch_left {
    color: #000;
    font-size: 14px;
    line-height: 34px;
    text-align: right;
  }
  .from_switch_right {
  }
}
