/**
 * @Description: 会议详情-点击结束按钮的弹窗
 */
import React from 'react';
import { history, connect } from 'umi'
import { Popup } from 'antd-mobile';
import styles from './index.less';

interface PropsType {
  visible: boolean,          // 弹窗是否显示
  onCancel: () => void,      // 关闭弹窗
  onClickLeaveMeeting: () => void,   // 点击离开会议
  onClickEndMeeting: () => void,   // 点击结束会议
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {
    visible,
    PlanetChatRoom,
    onCancel, // 关闭弹窗
    onClickLeaveMeeting, // 点击离开会议
    onClickEndMeeting, // 点击结束会议
  } = props

  const {
    membersListInTheMeeting, // 空间在线成员列表
  } = PlanetChatRoom || {}

  // 是否显示离开会议按钮，规则：离开前需在参会人列表中指定1人为主持人，过滤掉自己后，还有参会人，则显示
  // const isCanLeave = membersListInTheMeeting && membersListInTheMeeting.length > 0 && membersListInTheMeeting.filter(item => item.isSelf != 1 && item.meetingType == 2).length > 0

  return (
    <Popup
      visible={visible}
      onMaskClick={onCancel}
      className={styles.popup_container}
      bodyStyle={{ height: '188PX' }}
      destroyOnClose
    >
      <div className={styles.container}>
        {/* 头部 */}
        <div className={styles.header_line} onClick={onCancel}>
          <div className={styles.header_line_bar}></div>
        </div>

        {/* 按钮 */}
        <div className={styles.content}>
          <div className={styles.btn} onClick={onClickLeaveMeeting}>离开会议</div>
          <div className={styles.btn} style={{color: '#E03333'}} onClick={onClickEndMeeting}>结束会议</div>
        </div>
      </div>
    </Popup>
  )
}
export default connect(({ PlanetChatRoom, loading }: any) => ({PlanetChatRoom, loading}))(Index)
