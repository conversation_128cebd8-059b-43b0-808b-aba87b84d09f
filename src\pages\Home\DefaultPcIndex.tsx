/**
 * @Description: 管理端组件配置生成的页面，PC端页面   -- 默认首页  不是 分享生成的首页
 */
import React, { useState, useEffect } from 'react'
import { history, connect } from 'umi'
import classNames from 'classnames'
import { useThrottle } from '@/utils/utils'
import { Spin, message } from 'antd'
import styles from './DefaultPcIndex.less'

import PcHeader from '@/componentsByPc/PcHeader'                       // header组件
import SquareQRcodeModal from '@/pages/Home/Components/SquareQRcodeModal'      // 点击逛广场的二维码弹窗


const Index: React.FC = (props: any) => {
  const { dispatch, loading } = props

  const [homeMenuList, setHomeMenuList] = useState([])                 // 菜单数据集合
  const [searchKeyState, setSearchKeyState] = useState('')             // 搜索关键词
  const [squareQRcodeVisible, setSquareQRcodeVisible] = useState(false)// 二维码弹窗

  useEffect(() => {
    getHomeMenuList()
  }, [])

  // 获取WEB首页菜单
  const getHomeMenuList = () => {
    dispatch({
      type: 'activity/getHomeMenuList',
      payload: {}
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200) {
        setHomeMenuList(content || [])
      } else if (msg) {
        message.error(msg)
      }
    })
  }

  // 头部输入框，输入事件
  const inputOnChange = (e) => {
    setSearchKeyState(e.target.value)
  }

  // 头部输入框，按下回车事件
  let inputOnSearch = () => {
    history.push(`/Home/SearchResult?searchKey=${searchKeyState}`)
  }
  inputOnSearch = useThrottle(inputOnSearch, 300)

  // 点击菜单跳转，jumpType 1 内部跳转，2 外部跳转，3 弹窗
  const goToUrl = (item) => {
    if (item.jumpType == 1) {
      history.push(item.jumpInternalLink)
    } else if (item.jumpType == 2) {
      if (item.jumpLink) {
        window.open(item.jumpLink)
      } else {
        message.error('未配置地址')
      }
    } else if (item.jumpType == 3) {
      setSquareQRcodeVisible(true)
    }
  }

  // 二维码弹窗，关闭
  const squareQRcodeModalHide = () => {
    setSquareQRcodeVisible(false)
  }

  // loading
  const loadingGetHomeMenuList = !!loading.effects['activity/getHomeMenuList']

  return (
    <Spin spinning={loadingGetHomeMenuList}>
      <div className={styles.container}>
        <PcHeader
          isShowInput={true}
          inputChangeFn={inputOnChange}
          inputOnSearch={inputOnSearch}
        />
        <div className={styles.content_box}>
          <div className={styles.content}>
            {
              homeMenuList.map(item => {
                return (
                  <div key={item.id} className={styles.item_box} onClick={() => goToUrl(item)}>
                    <div className={classNames(styles.item_title, {
                      [styles.icon_lock]: item.name == '临床实操',
                    })}>{item.name}</div>
                    <i className={styles.small_circle} style={{background: `${item.color}`}}></i>
                    <i className={styles.big_circle} style={{background: `${item.color}`}}></i>
                    <i className={classNames(styles.icon, styles[item.iconPath])}></i>
                  </div>
                )
              })
            }
          </div>
        </div>

        {/* 二维码弹窗 */}
        <SquareQRcodeModal
          visible={squareQRcodeVisible}
          onCancel={squareQRcodeModalHide}
        />
      </div>
    </Spin>
  )
}

export default connect(({ activity, loading }: any) => ({activity, loading}))(Index)
