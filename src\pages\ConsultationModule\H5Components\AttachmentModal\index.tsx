/**
 * @Description: 移动端附件发送邮件弹窗
 * @author: 赵斐
 */
import React, { useEffect, useState } from 'react';
import { Popup } from 'antd-mobile'
import { Spin } from 'antd'

import styles from './index.less'
import closeIcon from '@/assets/Consultation/H5/close_icon.png'
// 附件列表
import AttachmentList from '@/pages/ConsultationModule/H5Components/AttachmentList'

interface PropsType {
  visible: boolean,       // 附件发送邮件弹窗状态
  dataSource: any,         // 附件数据
  onCancel: () => void,   // 取消附件发送邮件弹窗回调
  sendEMail: (value: string) => void,     // 发送邮件方法
  loading: boolean,        // 接口loading
}

const initState = {
  value: "",      // 邮箱输入内容
  errorValue: "", // 错误提示文案
}
const Index: React.FC<PropsType> = (props: PropsType) => {
  const { visible, dataSource, onCancel, sendEMail, loading } = props;
  const [state, setState] = useState(initState)
  const {
    value,
    errorValue,
  } = state


  useEffect(() => {
    if (Array.isArray(dataSource) && dataSource.length) {
      setState({
        value: dataSource[0].email || '',
        errorValue: ""
      })
    }
  }, [])

  // 点击确认并关闭弹窗
  const onConfirm = () => {
    if (!value) {
      setState({
        ...state,
        errorValue: "请输入邮箱"
      })
      return
    }
    if (errorValue) return
    sendEMail(value)
  }

  // 获取输入框数据
  const onChangeInput = (e: any) => {
    let email = e.target.value
    if (/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(email)) {
      setState({
        value: email,
        errorValue: ""
      })
    } else {
      setState({
        value: email,
        errorValue: "邮箱格式错误"
      })
    }
  }

  return (
    <div>
      <Popup
        className={styles.wrap}
        visible={visible}
        onMaskClick={() => { onCancel() }}
        bodyStyle={/iPhone/.test(navigator.userAgent) ? { height: '440px' } : { height: '406px' }}   // 436
      >
        <p className={styles.horizontal_line} onClick={() => { onCancel() }}></p>
        <div className={styles.header}>
          <span className={styles.header_title}>填写邮箱地址</span>
          <span className={styles.header_close} onClick={() => { onCancel() }}>
            <img src={closeIcon} alt="X" />
          </span>
        </div>
        <div className={styles.wrap_input}>
          <p className={styles.input_left}>
            <span className={styles.input_star}>*</span>
            <span className={styles.input_title}>邮箱地址</span>
          </p>
          <p className={styles.input_right}>
            <input className={styles.input} type="text" value={state.value} onChange={onChangeInput} placeholder='请输入邮箱' />
            {errorValue ? <span className={styles.input_error}>{errorValue}</span> : null}
          </p>
        </div>
        <p className={styles.annex_num}>共{dataSource.length}个文件</p>
        <div className={styles.annex_content}>
          <AttachmentList isOpenAnnexStatus={false} dataSource={dataSource} />
        </div>
        <div className={styles.footer} style={/iPhone/.test(navigator.userAgent) ? { paddingBottom: '25px' } : {}}>
          <Spin spinning={loading}>
            <p className={styles.footer_btn} onClick={() => { onConfirm() }}>确认发送</p>
          </Spin>
        </div>
      </Popup>
    </div>
  )
}
export default Index
