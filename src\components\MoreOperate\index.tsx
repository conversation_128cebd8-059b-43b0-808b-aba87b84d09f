/**
 * @Description: 文章、帖子、外链更多操作弹框（分享、删除、编辑、取消）
 */
import React, { useState } from 'react';
import { createPortal } from 'react-dom'
import { history, connect } from 'umi';
import classNames from 'classnames'
import { getOperatingEnv, getShareUrl, WxAppIdByPublicAccount, isIOS, shareWeChatInApp, shareInApp } from '@/utils/utils'
import { Typography } from 'antd'
import { ActionSheet, Popup, Toast, Mask } from 'antd-mobile';
import styles from './index.less';

// 删除提示弹窗
import CommonConfirmModal from '@/components/CommonConfirmModal';

import shareWXIcon from '@/assets/Case/share_wx.png';
import firdayIcon from '@/assets/firday.png';
import roundDotIcon from '@/assets/UserInfo/roundDot.png';
import friendsCircleIcon from '@/assets/GlobalImg/friendsCircle.png';
import enterprise_wechat from '@/assets/GlobalImg/enterprise_wechat.png'
import copy_link from '@/assets/GlobalImg/copy_link.png'

interface PropsType {
  id: number,   // 图文ID
  imageType: number,   // 图文类型：1.文章 2.帖子 3.外链 4.空间
  status: number,   // 状态：1.审核通过（已发布） 0.未审核 2.审核未通过 3.草稿
  handleDeleteOrLow: () => {},   // 删除或下架成功回调
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { dispatch, id, imageType, status } = props

  const [actionSheetVisible, setActionSheetVisible] = useState(false); // 操作弹框
  const [sharePopupVisible, setSharePopupVisible] = useState(false); // 分享弹框
  const [shareTipsVisible, setShareTipsVisible] = useState(false); // 分享提示弹窗
  const [commonConfirmVisible, setCommonConfirmVisible] = useState(false); // 删除二次提示弹框

  // 微信分享配置
  const onShareAppMessage = (shareUrl) => {
    const url = window.location.href
    const shareUrl2 = getShareUrl(shareUrl)

    return dispatch({
      type: 'userInfoStore/getJsapiTicket',
      payload: {
        currentUrl: url,                                   // 页面url
        appId: WxAppIdByPublicAccount,                     // 公众号appId
      },
    }).then(res => {
      if (res && res.code == 200) {
        wx.config({
          debug: false,
          appId: res.content.appId,
          timestamp: res.content.timestamp,
          nonceStr: res.content.nonceStr,
          signature: res.content.signature,
          jsApiList: [
            'updateAppMessageShareData',
            'updateTimelineShareData',
            'onMenuShareTimeline',
            'onMenuShareAppMessage',
            'onMenuShareQQ',
            'onMenuShareWeibo',
            'onMenuShareQZone',
          ],
        })
        wx.ready(() => {
          const shareDate = {
            title: '【FRIDAY医生星球】牙医都来这里学习和交流！',
            desc: '空间聊天室、在线课程、全国病例库、视频问专家',
            link: shareUrl2,
            imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/DigitalHealthShareImg.png',
          };
          console.log(shareDate)
          wx.updateAppMessageShareData(shareDate);
          wx.updateTimelineShareData(shareDate);
          wx.onMenuShareTimeline(shareDate);
          wx.onMenuShareAppMessage(shareDate);
          wx.onMenuShareQQ(shareDate);
          wx.onMenuShareWeibo(shareDate);
          wx.onMenuShareQZone(shareDate);
        })
      } else {
        // Toast.show('请求微信配置失败～！')
      }
    })
  }

  // 点击三个点，打开操作弹窗
  const actionSheetShow = () => {
    setActionSheetVisible(true)
  }

  // 关闭操作弹窗
  const actionSheetHide = () => {
    setActionSheetVisible(false)
  }

  // 打开分享弹窗
  const sharePopupShow = () => {
    actionSheetHide()
    setSharePopupVisible(true)
  }

  // 关闭分享弹窗
  const sharePopupHide = () => {
    setSharePopupVisible(false)
  }

  // 点击转发
  const onClickForward = () => {
    setSharePopupVisible(false)
    history.push(`/CreateGraphicsText/CreateForward?forwardId=${id}`)
  }

  // 获取分享链接地址
  const getTheShareUrl = () => {
    let url = window.location.origin
    if (imageType == 1) {
      url += `/CreateGraphicsText/ArticleDetails?id=${id}`
    } else if (imageType == 2) {
      url += `/CreateGraphicsText/PostDetails?id=${id}`
    }  else if (imageType == 3) {
      url += `/CreateGraphicsText/ExternalLinksDetails?id=${id}`
    } else if (imageType == 4) {
      // 空间没有更多操作按钮
    }

    const shareUrl = getShareUrl(url)
    return shareUrl
  }

  // 点击微信、朋友圈
  const onClickWeChat = (scene) => {
    if (getOperatingEnv() == '2' || getOperatingEnv() == '7') {
      shareLinkWeChatH5()
    } else {
      shareLinkWeChat(scene)
    }
  }

  // 微信H5环境分享
  const shareLinkWeChatH5 = async () => {
    const toastControl = Toast.show({
      icon: 'loading',
      content: '',
      duration: 0,
    })
    const url = getTheShareUrl()
    await onShareAppMessage(url)
    toastControl.close()

    sharePopupHide()
    setShareTipsVisible(true)
  }

  // APP环境微信分享
  const shareLinkWeChat = (scene) => {
    sharePopupHide()
    const url = getTheShareUrl()
    shareWeChatInApp({
      type: 'link',        // type取link（默认）或image
      url: url,          // url为网页链接或图片链接，必传
      thumbnail: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/DigitalHealthShareImg.png', // 缩略图链接
      title: '【FRIDAY医生星球】牙医都来这里学习和交流！',            // 标题
      description: '空间聊天室、在线课程、全国病例库、视频问专家',      // 描述
      scene: scene,         // scene取session（默认）或timeline
    }).then(res => {})
  }

  // APP环境系统分享
  const onClickSystem = () => {
    sharePopupHide()
    const url = getTheShareUrl()
    shareInApp({
      type: 'link',        // type取link（默认）或image
      url: url,
    }).then(res => {})
  }

  // 复制成功
  const onCopy = () => {
    Toast.show('复制成功')
    sharePopupHide()
  }

  // 分享提示弹窗，关闭
  const shareTipsHide = () => {
    setShareTipsVisible(false)
  }

  // 点击编辑
  const onClickEdit = () => {
    setActionSheetVisible(false)
    if (imageType == 1) {
      history.push(`/CreateGraphicsText/CreateArticle?id=${id}`)
    } else if (imageType == 2) {
      history.push(`/CreateGraphicsText/CreatePost?id=${id}`)
    } else if (imageType == 3) {
      history.push(`/CreateGraphicsText/CreateExternalLinks?id=${id}`)
    } else if (imageType == 4) {
      // 空间没有更多操作按钮
    }
  }

  // 点击删除，打开二次确认弹窗
  const commonConfirmModalShow = () => {
    actionSheetHide()
    setCommonConfirmVisible(true)
  }

  // 二次确认弹窗，关闭
  const commonConfirmModalHide = () => {
    setCommonConfirmVisible(false)
  }

  // 确认删除、下架
  const handleConfirmOk = () => {
    setCommonConfirmVisible(false)
    if (status == 3) {
      deleteImgTextInfo()
    } else {
      lowUpFrameImgTextInfo()
    }
  }

  // 删除
  const deleteImgTextInfo = () => {
    Toast.show({
      icon: 'loading',
      content: '',
      duration: 0,
    })
    dispatch({
      type: 'graphicsText/deleteImgTextInfo',
      payload: {
        imageTextId: id,
      }
    }).then(res => {
      Toast.clear()
      const { code, content, msg } = res
      if (code == 200 && content) {
        Toast.show('删除成功')
        props.handleDeleteOrLow && props.handleDeleteOrLow()
      } else {
        Toast.show(msg || '数据加载失败')
      }
    }).catch();
  }

  // 下架
  const lowUpFrameImgTextInfo = () => {
    Toast.show({
      icon: 'loading',
      content: '',
      duration: 0,
    })
    dispatch({
      type: 'graphicsText/lowUpFrameImgTextInfo',
      payload: {
        imageTextId: id,
        lowUpFrame: 0,   // 文章上下或下架: 1.上架 0，下架  默认操作下架
      }
    }).then(res => {
      Toast.clear()
      const { code, content, msg } = res
      if (code == 200 && content) {
        Toast.show('下架成功')
        props.handleDeleteOrLow && props.handleDeleteOrLow()
      } else {
        Toast.show(msg || '数据加载失败')
      }
    }).catch();
  }

  // 更多操作
  const actions = [
    { text: '分享', key: 'share', disabled: status != 1, onClick: sharePopupShow },
    { text: '编辑', key: 'edit', disabled: status == 0, onClick: onClickEdit },
    { text: status == 3 ? '删除' : '下架', key: 'delete', danger: true, onClick: commonConfirmModalShow },
    { text: '取消', key: 'cancel', onClick: actionSheetHide },
  ]

  return <>
    {/* 三个... */}
    <div className={styles.more_box} onClick={actionSheetShow}>
      <img src={roundDotIcon} width={24} height={24} alt="" />
    </div>

    {/* 更多操作（分享/删除/编辑） */}
    <ActionSheet
      className={styles.wrap}
      visible={actionSheetVisible}
      actions={actions}
      onClose={actionSheetHide}
    />

    {/* 分享弹框 */}
    <Popup
      visible={sharePopupVisible}
      onMaskClick={sharePopupHide}
      className={styles.popup_container}
      bodyStyle={{ height: '233px' }}
      destroyOnClose
    >
      <div className={styles.list_box}>
        <div className={styles.list_item} onClick={onClickForward}>
          <div className={styles.item_img}>
            <img src={firdayIcon} alt="" width={18} height={18} />
          </div>
          <div>转发</div>
        </div>
        {/* 微信浏览器环境 or iosAPP环境 */}
        {
          ((getOperatingEnv() == '2' || getOperatingEnv() == '7') || ((getOperatingEnv() == '5' || getOperatingEnv() == '6') && isIOS())) ?
            <>
              <div className={styles.list_item} onClick={() => onClickWeChat('session')}>
                <div className={styles.item_img}>
                  <img src={shareWXIcon} alt="" width={24} height={24} />
                </div>
                <div>微信</div>
              </div>
              <div className={styles.list_item} onClick={() => onClickWeChat('timeline')}>
                <div className={styles.item_img}>
                  <img src={friendsCircleIcon} alt="" width={24} height={24} />
                </div>
                <div>朋友圈</div>
              </div>
            </>
            : null
        }

        {/* 系统分享 */}
        {
          (getOperatingEnv() == '5' || getOperatingEnv() == '6') &&
          <div className={styles.list_item} onClick={onClickSystem}>
            <div className={styles.item_img}>
              <img src={enterprise_wechat} alt="" width={24} height={24} />
            </div>
            <div>企业微信</div>
          </div>
        }

        {/* 复制链接 */}
        <Typography.Paragraph copyable={{
          text: getTheShareUrl(),
          icon: [
            <div className={styles.list_item}>
              <div className={styles.item_img}>
                <img src={copy_link} alt="" width={24} height={24} />
              </div>
              <div>复制链接</div>
            </div>,
            <div className={styles.list_item}>
              <div className={styles.item_img}>
                <img src={copy_link} alt="" width={24} height={24} />
              </div>
              <div>复制链接</div>
            </div>
          ],
          tooltips: [false, false],
          onCopy: onCopy,
        }}></Typography.Paragraph>
      </div>
      <div className={styles.list_btn} onClick={sharePopupHide}>取消</div>
    </Popup>

    {/* 确定删除提示弹窗 */}
    <CommonConfirmModal
      isVisible={commonConfirmVisible}
      title={status == 3 ? '确定删除' : '确定下架'}
      text={status == 3 ? '内容删除后将无法恢复，请慎重考虑': '内容下架后将无法恢复，请慎重考虑'}
      onSubmit={handleConfirmOk}
      onCancel={commonConfirmModalHide}
    />

    {/* 分享提示弹窗 */}
    <Mask style={{'--z-index': 1000}} opacity={0.7} visible={shareTipsVisible} onMaskClick={shareTipsHide} getContainer={document.body}/>
    {
      shareTipsVisible && createPortal((
        <div className={classNames(styles.fixed_share_box, {[styles.fixed_share_box_show]: shareTipsVisible})}>
          <i className={styles.icon1}></i>
          <div className={styles.message_box}>
            <div>点击右上角</div>
            <div>发送到 微信好友 或者 分享到朋友圈</div>
          </div>
          <div className={styles.icon_box}>
            <i className={styles.icon2}></i>
            <i className={styles.icon3}></i>
          </div>
        </div>
      ), document.body)
    }

  </>
}
export default connect(({ loading }: any) => ({ loading }))(Index)
