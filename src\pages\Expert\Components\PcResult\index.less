.container {
  background: #EEF3F9;
  height: 100vh;
}

.wrap {
  width: 1228px;
  margin: 0 auto;

  .wrap_header {
    margin: 16px 0;
  }

  .content {
    padding: 16px 24px 0 24px;
    background: #FFFFFF;
    height: calc(100vh - 220px);
    overflow: auto;
    overflow-x: hidden;
    .table_title {
      display: flex;
      align-items: center;
      height: 54px;
      background: #F8F9FA;
      border-radius: 4px;

      &>div {
        font-size: 14px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #4A627E;
      }

      div:nth-child(1) {
        width: 112px;
        text-align: center;
      }

      div:nth-child(2) {
        width: 96px;
      }

      div:nth-child(3) {
        width: 96px;
      }

      div:nth-child(4) {
        width: 112px;
      }

      div:nth-child(5) {
        width: 222px;
      }

      div:nth-child(6) {
        flex: 1;
      }

      div:nth-child(7) {
        width: 109px;
      }
    }

    .content_list {
      

      .content_detail {
        display: flex;
        align-items: center;
        height: 80px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.15);

        &>div {
          font-size: 14px;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: 400;
          color: #071E3F;
        }

        div:nth-child(1) {
          width: 112px;
          text-align: center;

          &>img {
            width: 48px;
            height: 48px;
            border-radius: 50%;
          }

          .no_doctor_pic {
            display: inline-block;
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 500;
            color: #fff;
            padding: 2px 4px;
          }
        }

        div:nth-child(2) {
          width: 96px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        div:nth-child(3) {
          width: 96px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        div:nth-child(4) {
          width: 112px;

          &>span {
            padding: 2px 4px;
            background: #EEFFF9;
            border-radius: 2px 2px 2px 2px;
            opacity: 1;
            border: 1px solid #B0EAD9;
            font-size: 12px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: #06A777;
          }
        }

        div:nth-child(5) {
          width: 222px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        div:nth-child(6) {
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        div:nth-child(7) {
          width: 109px;
          font-size: 14px;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: 400;
          color: #0095FF;
          cursor: pointer;
        }
      }
    }
  }
}