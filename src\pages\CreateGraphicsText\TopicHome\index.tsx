import React, { useEffect, useRef, useState } from 'react'
import { connect, history } from 'umi'
import { Helmet } from 'react-helmet'
import { statusRecord, gdpFormat, useThrottle } from '@/utils/utils';
import { message, Spin } from 'antd';
import { InfiniteScroll, PullToRefresh, Toast } from 'antd-mobile';
import styles from './index.less'
import CreateKingdomOrSpace from '@/pages/UserInfo/CreateKingdomOrSpace';
import NavBar from '@/components/NavBar';
import PostCard from '@/components/PostCard';
import LikeCard from '@/components/LikeCard';
import ForwardCard from '@/components/ForwardCard';
import ArticleCardBySquare from '@/components/ArticleCardBySquare';
import ExternalLinkCard from '@/components/ExternalLinkCard';
import SpaceCardBySquare from '@/components/SpaceCardBySquare';
import LoadingException from '@/components/LoadingException';

const Index: React.FC = (props: any) => {
  const { dispatch } = props || {};
  const { query } = history.location
  const {
    topicId, // 话题ID
  } = query
  const initialState = {
    dataList: [],                  // 数据list
    total: 0,                      // 总条数
    hasMore: false,                // 是否还有更多
  }
  const initialFilterState = {
    page: 1,
    size: 30,
  }
  const [topicDetailsState, setTopicDetailsState] = useState({}) // 话题详情
  const [state, setState] = useState(initialState) // 数据list
  const [filterState, setFilterState] = useState(initialFilterState)
  const [loadingGetTopicList, setLoadingGetTopicList] = useState(false)  // loading
  const [loadingInfiniteScroll, setLoadingInfiniteScroll] = useState(false)

  useEffect(() => {
    if (topicId) {
      getTopicListThrottle(1, filterState.size, false, topicId)
    }
  },[topicId])

  useEffect(() => {
    getTopicListThrottle();
  }, [filterState.page]);

  // 分页获取内容
  const getTopicList = async (page, size, isLocalUpdate = false, newTopicId) => {
    setLoadingInfiniteScroll(true)
    if (!isLocalUpdate) {
      setLoadingGetTopicList(true)
    }
    const pageResult = page || filterState.page
    const sizeResult = size || filterState.size
    await dispatch({
      type: 'recommended/getTopicList',
      payload: {
        pageNum: pageResult,
        pageSize: sizeResult,
        topicId: newTopicId || topicId,  // 话题Id
      }
    }).then(res => {
      setLoadingInfiniteScroll(false)
      setLoadingGetTopicList(false)
      const { code, content, msg } = res
      const { squareRecommendPage } = content
      if (code == 200) {
        const resultList = squareRecommendPage.resultList || []
        setState({
          ...state,
          dataList: pageResult == 1 ? resultList : state.dataList.concat(resultList),
          total: content.total || 0,
          hasMore: true,
        })
        setTopicDetailsState({
          ...content,
          squareRecommendPage: null,
        })
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch(err => {})
  }
  const getTopicListThrottle = useThrottle(getTopicList, 300)

  // 滚动加载
  const loadMore = async () => {
    console.log('loadMore')
    if (state.dataList.length >= state.total) {
      await setState({
        ...state,
        hasMore: false,
      })
      return
    }
    await setLoadingInfiniteScroll(true)
    await setFilterState({
      ...filterState,
      page: filterState.page + 1,
    })
  }

  // 下拉刷新
  const updateListData = async () => {
    if (filterState.page != 1) {
      setFilterState({
        ...filterState,
        page: 1,
      })
    } else {
      getTopicListThrottle(1, filterState.size)
    }
  }

  // 删除或下架操作后刷新数据
  const refreshDataById = (id) => {
    getTopicListThrottle(1, state.dataList.length, true)
  }

  // 参与讨论
  const topicDiscuss = () => {
    checkSuperAccount()
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        createModalVisible: true,
        topicHomeTopicId: topicId,   // 话题id
        topicHomeTopicName: topicDetailsState.topicName || null,   // 话题名称
      }
    })
  }

  // 是否超级账号
  const checkSuperAccount = () => {
    dispatch({
      type: 'userInfoStore/checkSuperAccount',
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200) {
        dispatch({
          type: 'userInfoStore/setTaskListState',
          payload: {
           isSuperAccount: content,
          }
        })
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch(() => {})
  }

  return <>
    <Helmet>
      <title>{`#${topicDetailsState.topicName || '' }#`}</title>
      <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, shrink-to-fit=no" />
    </Helmet>
    <NavBar title={`#${topicDetailsState.topicName || '' }#`} bordered/>
    <div className={styles.wrap_padding}/>
    <Spin spinning={!!loadingGetTopicList}>
      <PullToRefresh
        onRefresh={updateListData}
        renderText={status => {
          return <div>{statusRecord[status]}</div>
        }}
      >
        <div className={styles.wrap}>
          <div className={styles.head_wrap}>
            <div className={styles.left_box}>
              <div className={styles.title}>#{topicDetailsState.topicName || '' }#</div>
              <div className={styles.text_info}>
                <span style={{marginRight: 8}}>讨论：{topicDetailsState.topicPv}</span>
                <span>阅读：{gdpFormat(topicDetailsState.topicGdp)}GDP</span>
              </div>
            </div>
            {
              topicDetailsState.topicName && topicDetailsState.topicStatus == 1 ?
                <div className={styles.right_box} onClick={topicDiscuss}>参与讨论</div>
                : null
            }
          </div>
          <div className={styles.list_wrap}>
            {
              state.dataList.map(item => {
                if (item.isForward == 1) {
                  return (
                    <ForwardCard
                      key={item.id}
                      pageType={2}
                      item={item}
                    />
                  )
                } if (item.isForward == 2) {
                  return (
                    <LikeCard
                      key={item.id}
                      item={item}
                    />
                  )
                } else if (item.imageType == 1) {
                  return (
                    <ArticleCardBySquare
                      key={item.id}
                      pageType={2}
                      item={item}
                      refreshDataById={refreshDataById}
                      isShowMoreOperate={true}
                    />
                  )
                } else if(item.imageType == 2) {
                  return (
                    <PostCard
                      key={item.id}
                      pageType={2}
                      item={item}
                      refreshDataById={refreshDataById}
                      isShowMoreOperate={true}
                    />
                  )
                } else if (item.imageType == 3) {
                  return (
                    <ExternalLinkCard
                      key={item.id}
                      item={item}
                      refreshDataById={refreshDataById}
                      isShowMoreOperate={true}
                    />
                  )
                } else if (item.imageType == 4) {
                  return (
                    <SpaceCardBySquare
                      key={item.id}
                      pageType={2}
                      item={item}
                      refreshDataById={refreshDataById}
                      isShowMoreOperate={false}
                    />
                  )
                }
            })}
          </div>
          {
            state.dataList.length == 0 ?
              <LoadingException
                exceptionStyle={{ paddingTop: 50, paddingBottom: 32 }}
                interfaceStatus={2}
                retryFun={()=>{}}
                resetStatusFun={()=>{}}
              />
              :
              <InfiniteScroll
                loadMore={loadMore}
                hasMore={!loadingInfiniteScroll && state.hasMore}
                threshold={30}
              />
          }
        </div>
      </PullToRefresh>
    </Spin>
    <CreateKingdomOrSpace />
  </>
}

export default connect(({userInfoStore, loading }: any) => ({userInfoStore, loading}))(Index)
