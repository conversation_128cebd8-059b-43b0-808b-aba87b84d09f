.modal {
  :global {
    .ant-modal-header {
      padding: 12px 24px;
    }
    .ant-modal-title {
      font-size: 16px;
      color: #303133;
      font-weight: 500;
      line-height: 26px;
    }
    .ant-modal-body {
      padding: 0;
    }
  }
}
.container {
  height: 70vh;
  padding: 20px 24px;
  overflow-y: auto;
}

.item {
  display: flex;
  flex-wrap: nowrap;
  padding-bottom: 10px;
  overflow: hidden;
  .label {
    width: 75px;
    min-width: 75px;
    white-space: nowrap;
    font-size: 15px;
    color: #666;
    line-height: 21px;
    margin-bottom: 6px;
  }
  .text_value {
    flex: 1;
    font-size: 15px;
    color: #000;
    line-height: 21px;
    word-break: break-all;
    white-space: pre-wrap;
  }
  .tag_value {
    display: flex;
    flex-wrap: wrap;
    .tag {
      height: 24px;
      line-height: 24px;
      border-radius: 2px;
      background: #EDF9FF;
      padding: 0 4px;
      font-size: 14px;
      color: #0095FF;
      margin-right: 6px;
      margin-bottom: 6px;
    }
  }
}
.item.img_item {
  display: block;
  .label {
    margin-bottom: 16px;
  }
  .img_value {
    display: flex;
    flex-wrap: wrap;
    .img {
      width: 198px;
      height: 198px;
      margin-right: 16px;
      margin-bottom: 16px;
      border-radius: 6px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      &:nth-child(3n) {
        margin-right: 0;
      }
    }
  }
}
