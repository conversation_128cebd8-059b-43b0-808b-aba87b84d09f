/**
 * @Description: 实名认证成功提示弹窗（PC）
 */
import React, { useState, useEffect } from 'react'
import { Modal, Button } from 'antd'
import styles from './index.less'

// 图标图片
import successIcon from '@/assets/GlobalImg/success.png' // 成功icon

interface PropsType {
  visible: boolean,                              // true，false
  onCancel: any,                                 // 关闭弹窗
}

const Index: React.FC<PropsType> = (props: any) => {
  const {
    visible,
    onCancel, // 关闭弹窗
  } = props

  return (
    <Modal
      title="提示"
      className={styles.modal}
      visible={visible}
      onCancel={onCancel}
      width={474}
      footer={null}
      destroyOnClose
    >
      <div className={styles.img_wrap}>
        <img src={successIcon} width={72} height={72} alt=""/>
      </div>
      <div className={styles.title}>认证成功！</div>
      <div className={styles.btn_wrap}>
        <Button type="primary" className={styles.btn} onClick={onCancel}>返回个人中心</Button>
      </div>
    </Modal>
  )
}

export default Index
