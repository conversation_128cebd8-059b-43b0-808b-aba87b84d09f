/**
 * @Description: 服务协议弹窗
 */
import React from 'react'
import { Modal } from 'antd'
import styles from './index.less'

// 隐私政策图片列表
const agreementImgList = [
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/ConsultationServiceAgreement/agreement_1.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/ConsultationServiceAgreement/agreement_2.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/ConsultationServiceAgreement/agreement_3.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/ConsultationServiceAgreement/agreement_4.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/ConsultationServiceAgreement/agreement_5.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/ConsultationServiceAgreement/agreement_6.png',
]

interface PropsType {
  visible: boolean,                              // true，false
  onCancel: any,                                 // 关闭弹窗
}

const Index: React.FC<PropsType> = (props: any) => {
  const { visible } = props
  return (
    <Modal
      title="用户服务付费协议"
      className={styles.modal}
      visible={visible}
      onCancel={props.onCancel}
      width={1100}
      footer={null}
    >
      <div className={styles.agreement_box}>
        {
          agreementImgList && agreementImgList.map((item, index) => {
            return (
              <div key={index} className={styles.agreement_img_item}>
                <img src={item} alt="" />
              </div>
            )
          })
        }
      </div>
    </Modal>
  )
}

export default Index
