/**
 * 专家是（本人）主页-会议列表
 */
import React, { useState, useEffect, useRef } from 'react';
import styles from './index.less';
import { history, connect } from 'umi';
import classNames from 'classnames';
import {Mask, Toast, Checkbox, SwipeAction} from 'antd-mobile';
import MeetingCard from '@/components/MeetingCardInfo';            // 会议卡片组件
import { getOperatingEnv, getDAesString } from '@/utils/utils';
import MoreOperate from './MoreOperate'; // 更多操作弹框
import PosterModal from '@/pages/Poster/PosterModal'                  // 海报弹框
import FilterIcon from "@/assets/Case/filter_icon.png";
import FilterActiveIcon from '@/assets/GlobalImg/PcMyConsultationList_Filter.png';
import {Typography} from "antd";

const meetingSpaceStatusList = [
  {
    id: 1,
    name: '我参与的会议',
  },
  {
    id: 2,
    name: '历史记录',
  },
]

const isBizList = [
  {
    id: 1,
    name: '企业会议',
  },
  {
    id: 0,
    name: '非企业会议',
  }
]

const isRecordingMeetingList = [
  {
    id: 0,
    name: '全部',
  },
  {
    id: 1,
    name: '已录制会议',
  }
]


const Index: React.FC = (props: any) => {
  const { dispatch, spaceDataSource, activitySpace, starSpaceType } = props|| {};
  const posterModalRef = useRef(null);
  // 我的空间列表筛选条件
  let myHomeSpaceFilter = sessionStorage.getItem('myHomeSpaceFilter')!='undefined'?JSON.parse(sessionStorage.getItem('myHomeSpaceFilter')):{};
  let { friUserId } = JSON.parse(localStorage.getItem('userInfo') || '{}');
  let initSpaceFilter = {
    spaceJoinType: null, // 非会议类型的筛选
    spaceStatus: null, // 非会议类型的筛选
    isBiz: myHomeSpaceFilter?.isBiz||null, // 会议类型 1:企业空间 2:非企业空间
    isRecordingMeeting: myHomeSpaceFilter?.isRecordingMeeting||null, // 会议录制 1:已录制，0/不传:全部
    meetingSpaceStatus: myHomeSpaceFilter?.meetingSpaceStatus||1, // 会议空间状态 1:我参与的会议 2:历史记录
  }
  const isFirstRender = useRef(true);  // 判断是否是初次加载
  const [spaceItem, setSpaceItem] = useState(null); // 当前空间数据
  const [visible, setVisible] = useState(false); // 筛选弹框
  const [spaceFilter, setSpaceFilter] = useState(initSpaceFilter)       // 我的空间列表筛选条件
  const [isFilterActive, setIsFilterActive] = useState((initSpaceFilter.spaceStatus!=null||initSpaceFilter.isBiz!=null||initSpaceFilter.isRecordingMeeting!=null)?true:false); // 筛选按钮是否激活
  const [selectBizList, setSelectBizList] = useState(initSpaceFilter.isBiz!=null&&initSpaceFilter?.isBiz.split(',').map(Number)||[])  // 选中的空间分类
  const [selectRecordingMeeting, setSelectRecordingMeeting] = useState(initSpaceFilter.isRecordingMeeting!=null&&initSpaceFilter?.isRecordingMeeting||[])  // 选中的会议录制
  const [moreVisible, setMoreVisible] = useState(false); // 更多操作弹框
  const { isBiz, isRecordingMeeting, meetingSpaceStatus } = spaceFilter || {};


  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
    } else {
      props.mySpaceChooseFilter(null,null,isBiz,isRecordingMeeting,meetingSpaceStatus);
    }
  }, [meetingSpaceStatus])

  // 筛选事件
  const screenBtnFn = () => {
    setVisible(true);
    // 解决出现弹框后,可以滚动页面问题
    document.getElementById('wrap').style.position = 'fixed'
    document.getElementById('wrap').style.overflow = 'hidden';
  }

  // 筛选弹框-取消事件
  const calcelBtnFn = () => {
    setVisible(false);
    // 关闭弹框,重置滚动
    document.getElementById('wrap').style.position = 'unset'
    document.getElementById('wrap').style.overflow = 'auto';
  }

  // 更多操作
  const moreOperateBtn = (item: React.SetStateAction<null>) => {
    setSpaceItem(item)
    setMoreVisible(true)
  }

  // 刷新页面
  const refreshFn = () => {
    props.mySpaceMoreOperateClose(null,null,isBiz, isRecordingMeeting, meetingSpaceStatus);
  }

  // 选中空间分类
  const onSelectBizChange = (val) => {
    setSpaceFilter({
      ...spaceFilter,
      isBiz: (val.length==2||val.length==0) ? null : val[0].toString()
    })
    setSelectBizList(val)
  }
  // 选中全部空间分类
  const onSelectAllBizChange = (checked) => {
    setSelectBizList(checked ? [0,1] : [])
    setSpaceFilter({
      ...spaceFilter,
      isBiz: null
    })
  }

  // 选中会议录制
  const onSelectRecordingMeetingChange = (val) => {
    setSpaceFilter({
      ...spaceFilter,
      isRecordingMeeting:val.length==0?null: val[val.length-1].toString()
    })
    setSelectRecordingMeeting(val.length==0?[]:[val[val.length-1]])
  }

  // 更新会议筛选条件
  const onOkFilter = () => {
    calcelBtnFn();
    (isBiz||isRecordingMeeting)?setIsFilterActive(true):setIsFilterActive(false);
    props.mySpaceChooseFilter(null,null,isBiz,isRecordingMeeting,meetingSpaceStatus);
  }

  // SwipeAction组件参数
  const getRightActions = (item) => {
    return [
      {
        key: 'delete',
        text: '删除',
        color: 'danger',
        onClick: () => deleteMeetingFromListFn(item),
      },
    ]
  }

  // 从列表中删除会议
  const deleteMeetingFromListFn = (item) => {
    Toast.show({icon: 'loading', maskClickable: false})
    dispatch({
      type: 'userInfoStore/deleteSpaceFromList',
      payload: {
        spaceId: item.id, // 会议id
      }
    }).then(res => {
      const { code } = res || {};
      if(res && code == 200) {
        Toast.show({content: '从列表删除成功'})
        refreshFn();
      } else {
        return Toast.show({content: res.msg})
      }
    }).catch(err => {
      console.log(err)
    })
  }
  // 更多操作下拉弹框关闭按钮
  const closeMoreOperateFn = () => {
    setMoreVisible(false)
  }

  // 海报
  const posterBtn = (item: any) => {
    posterModalRef && posterModalRef.current.init(1, item)
  }

  // 分享操作
  const shareBtn = (item: any) => {
    posterModalRef && posterModalRef.current.init(2, item)
  }

  // 复制链接
  const onCopy = () => {
    Toast.show('复制链接成功!')
  }

  return <>
    <div className={styles.top_wrap}>
      <div className={styles.tab_spaceRoleType_list}>
        {
          meetingSpaceStatusList.map((item: { id: any; name: React.ReactNode; }) => {
            return <span key={item.id} onClick={(e) => {
              e.stopPropagation();
              setSpaceFilter({
                ...spaceFilter,
                spaceStatus:null,  // 清空非会议类型的筛选
                spaceJoinType: null, //  清空非会议类型的筛选
                isBiz: null, // 会议类型 1:企业空间 2:非企业空间
                isRecordingMeeting: null, // 是否是录播会议 1:是 2:否
                meetingSpaceStatus: item.id, // 会议状态 1:我参与的会议 2:历史记录
              })
              setSelectBizList([]);
              setSelectRecordingMeeting([]);
              setIsFilterActive(false);
            }} className={item.id == meetingSpaceStatus ? styles.spaceRoleTypeActive : ''}>{item.name}</span>
          })
        }
      </div>
      <div className={isFilterActive?styles.screen_btn_active:''} style={{marginRight: '16px', paddingTop: '10px'}} onClick={screenBtnFn}>筛选<img src={isFilterActive?FilterActiveIcon:FilterIcon} style={{verticalAlign: 'baseline'}} width={12} height={12}/>
      </div>
      <Mask visible={visible} onMaskClick={calcelBtnFn} className={styles.mask_box}>
        <div className={styles.screen_wrap}>
          <div className={styles.screen_container}>
            <div className={styles.screen_box}>
              <div className={styles.isBizTitle}>会议分类</div>
              <div className={styles.isBizSelectBox}>
                <Checkbox
                  indeterminate={selectBizList.length == 1}
                  onChange={onSelectAllBizChange}
                  checked={selectBizList.length == 2}
                  style={{
                    '--icon-size': '14px',
                    '--font-size': '14px',
                    '--gap': '6px',
                  }}
                >全部</Checkbox>
                <Checkbox.Group value={selectBizList} style={{width: '100%'}} onChange={(val) => {
                  onSelectBizChange(val)
                }}>
                  {isBizList.map((item, idx) => {
                    return <div key={idx} className={styles.screen_child}>
                      <Checkbox
                        value={item.id}
                        style={{
                          '--icon-size': '14px',
                          '--font-size': '14px',
                          '--gap': '6px',
                        }}
                      >{item.name}</Checkbox>
                    </div>
                  })
                  }
                </Checkbox.Group>
              </div>
            </div>
            <div className={styles.screen_box}>
              <div className={styles.isBizTitle}>会议录制</div>
              <div className={styles.isBizSelectBox}>
                <Checkbox.Group value={selectRecordingMeeting} style={{width: '100%'}} onChange={(val) => {
                  onSelectRecordingMeetingChange(val)
                }}>
                  {isRecordingMeetingList.map((item, idx) => {
                    return <div key={idx} className={styles.screen_child}>
                      <Checkbox
                        value={item.id}
                        style={{
                          '--icon-size': '14px',
                          '--font-size': '14px',
                          '--gap': '6px',
                        }}
                      >{item.name}</Checkbox>
                    </div>
                  })
                  }
                </Checkbox.Group>
              </div>
            </div>
          </div>
          <div className={styles.screen_wrap_footer}>
            <div className={styles.screen_wrap_footer_close} onClick={calcelBtnFn}>取消</div>
            <div className={styles.screen_wrap_footer_confirm} onClick={onOkFilter}>确认</div>
          </div>
        </div>
      </Mask>
    </div>
    <div className={styles.meeting_container}>
      {
        spaceDataSource.length > 0 && spaceDataSource.map((item: any, index: any) => {
          return <div key={`${index}`} className={classNames(styles.meetingList_item, {
            [styles.meetingList_item_dayFirst]: meetingSpaceStatus == 1 && index > 0 && item.appointmentStartDateDescs != spaceDataSource[index - 1].appointmentStartDateDescs,
          })}>
            {meetingSpaceStatus == 1 && <div>
              {
                ((index > 0 && item.appointmentStartDateDescs != spaceDataSource[index - 1].appointmentStartDateDescs) || index == 0) &&
                <h3>{item.appointmentStartDateDescs}</h3>
              }
              <h4>{item.appointmentStartMinTime}-{item.appointmentStartMaxTime}</h4>
            </div>}
            {meetingSpaceStatus == 1 ?
              <MeetingCard myHomeSpaceFilter={spaceFilter} item={item} style={{padding: '0 0 8px'}}/> :
              <SwipeAction rightActions={getRightActions(item)}><MeetingCard myHomeSpaceFilter={spaceFilter}
                                                                             style={{padding: '0 0 12px',borderBottom: index!=spaceDataSource.length-1?'1px solid #E1E4E7':'none'}} item={item} /></SwipeAction>}
            {
              meetingSpaceStatus == 1 && <div className={classNames(styles.item_myMeeting_box,{[styles.meetingList_item_dayLast]:meetingSpaceStatus == 1&&index<=spaceDataSource.length-2&&item.appointmentStartDateDescs!=spaceDataSource[index+1].appointmentStartDateDescs||index==spaceDataSource.length-1})}>
                <span className={styles.item_moreOperate_btn} onClick={() => moreOperateBtn(item)}>更多操作</span>
                <span>
                   <span className={styles.item_poster_btn} onClick={() => posterBtn(item)}>生成海报</span>
                  {
                    (getOperatingEnv() == '2' || getOperatingEnv() == '7' || getOperatingEnv() == '5' || getOperatingEnv() == '6') ?
                      <span className={styles.item_share_btn} onClick={() => shareBtn(item)}>分享</span>
                      :
                      <span className={styles.item_copy_btn}>
                        <Typography.Paragraph copyable={{
                          text: `${window.location.origin}/PlanetChatRoom/Meet/${item.id}?shareUserId=${friUserId}&isShare=1` + (item.password ? `&pwd=${getDAesString(item.password,'arrail-dentail&2', 'arrail-dentail&3')}` : ''),
                          icon: [<div>复制链接</div>, <div>复制链接</div>],
                          tooltips: ['', ''],
                          onCopy: onCopy,
                        }}></Typography.Paragraph>
                      </span>
                  }
                </span>
              </div>
            }
          </div>
        })
      }
    </div>
    <MoreOperate meetingItem={spaceItem} visible={moreVisible} close={closeMoreOperateFn} refreshFn={refreshFn} />
    <PosterModal ref={posterModalRef}/>
  </>
}
export default connect(({expertAdvice, userInfoStore, loading}: any) => ({expertAdvice, userInfoStore, loading}))(Index)
