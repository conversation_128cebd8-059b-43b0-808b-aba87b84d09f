/**
 * @Description: 专家搜索页面
 * @author: 赵斐
 */
import React, { useEffect, useState, lazy, Suspense } from 'react';
import { connect, history } from 'umi';
import classNames from 'classnames';
import { getOperatingEnv } from '@/utils/utils'
import { Toast } from 'antd-mobile'
import { Spin } from 'antd'
import styles from './index.less'
// 搜索组件
// import SearchPage from '@/components/SearchPage';
const SearchPage = lazy(() => import('@/components/SearchPage'))
// 导航组件
import NavBar from '@/components/NavBar'

// 初始值设置
const initState = {
  dataSource: []      // 历史关键词
}

const Index: React.FC = (props: any) => {
  const { dispatch, loading } = props;
  const [state, setState] = useState(initState)
  const [value, setValue] = useState("")   // 搜索值
  const {
    dataSource,
  } = state

  useEffect(() => {
    getWordList()
  }, [])

  // 搜索历史展示
  const getWordList = () => {
    dispatch({
      type: "expertAdvice/getWordList"
    }).then((res: any) => {
      console.log(res)
      const { code, content, msg } = res;
      if (code == 200) {
        setState({
          ...state,
          dataSource: content
        })
      } else {
        Toast.show({
          content: msg,
        })
      }
    }).catch((err: String) => {
      console.log(err)
    })
  }

  /**
   * 保存搜索数据
   * @param v    输入框值
   */
  const saveInputFun = (v: string) => {
    // localStorage.setItem("searchValue", v)
    setValue(v)
  }
  /**
   * 跳转
   * @param type  1 跳转专家列表首页 2 跳转专家列表搜索结果页面
   */
  const jumpFun = (type: number) => {
    if (type == 1) {
      history.replace("/Expert/ExpertAdvice")
    } else {
      dispatch({
        type: "expertAdvice/save",
        payload: {
          searchValue: value
        }
      })
      history.replace(`/Expert/ExpertResult?whereStatus=999`)
    }
  }

  /**
 * 点击历史关键词跳转
 * @param value
 */
  const historyClickFn = (value: string) => {
    dispatch({
      type: "expertAdvice/save",
      payload: {
        searchValue: value
      }
    })
    history.replace(`/Expert/ExpertResult?whereStatus=999`)
  }
  /**
   * 点击热门搜索关键词跳转
   * @param value
   */
  const popularDataClickFn = (value: string) => {
    dispatch({
      type: "expertAdvice/save",
      payload: {
        searchValue: value
      }
    })
    history.replace(`/Expert/ExpertResult`)
  }

  const load = !!loading.effects['expertAdvice/getWordList']  // 搜索历史关键词loading

  return (
    <Spin spinning={load}>
      <div className={styles.header}>
        <NavBar title={"搜索"} className={styles.header_nav} />
      </div>
      <div className={classNames(styles.content, {
        [styles.content_pc]: getOperatingEnv() == 4,
      })}>
        <Suspense fallback={<div></div>}>
          <SearchPage
            isHistoryStatus={1}
            inputPlaceholder="输入专家姓名、医院"
            inputChangeFn={(v: string) => { saveInputFun(v) }}
            cancelBtnFn={() => { jumpFun(1) }}
            historyData={dataSource}
            historyClickFn={(val: string) => { historyClickFn(val) }}
            popularDataClickFn={(val: string) => { popularDataClickFn(val) }}
            onPressEnterFun={() => { jumpFun(2) }}
          />
        </Suspense>
      </div>
    </Spin>

  )
}
export default connect(({ expertAdvice, loading }: any) => ({ expertAdvice, loading }))(Index)
