/**
 * @Description: 专家列表首页医生能力等级提示弹窗
 * @author: 赵斐
 */
import { Mask } from 'antd-mobile';
import styles from './index.less';

const Index = (props:any) => {
  const { 
    visible,    // 提示弹窗状态
    cancelFn,   // 关闭弹窗方法    
  } = props || {};

  return (
    <Mask className={styles.tips_modal_mask} visible={visible}>
      <div className={styles.tips_modal_container}>
        <div className={styles.tips_modal}>
          <span className={styles.tips_modal_title}>医生能力等级</span>
          <span className={styles.tips_modal_desc}>医生能力等级由高到低分为四个等级，四级代表医生在该学科造诣相对较高</span>
        </div>
        <div className={styles.tips_modal_btn} onClick={()=>{cancelFn()}}>我知道了</div>
      </div>
    </Mask>
  )
}
export default Index;