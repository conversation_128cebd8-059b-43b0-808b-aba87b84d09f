.classifyGuide_container {
  position: relative;
  padding: 0;
  .classifyGuide_item_box {
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: baseline;
    overflow-x: auto;
    padding: 0 12px 16px;
    .classifyGuide_item {
      position: relative;
      font-size: 16px;
      line-height: 23px;
      margin-right: 16px;
      color: #666;
      white-space: nowrap;
      font-weight: 400;
      &:last-child {
        margin-right: 0;
      }
      &.checked {
        color: #000;
        font-weight: 500;
        &::after {
          content: "";
          display: block;
          position: absolute;
          bottom: -5px;
          left: 50%;
          transform: translateX(-50%);
          width: 12px;
          height: 3px;
          border-radius: 2px;
          background: #000;
        }
      }
    }
  }
}
