/**
 * @Description: 移动端-回到首页组件
 */
import React from 'react';
import {connect, history } from 'umi'
import { getOperatingEnv } from '@/utils/utils'
import goBackHomeIcon from '@/assets/GlobalImg/goBackHomeIcon.png';  // 回到首页图标

import styles from './index.less'

interface IProps {
  style?: object; // 样式
}

const GoBackHome: React.FC<IProps> = (props:IProps) => {
  const { style } = props;
  // 回到首页
  const goBackHome = () => {
    history.push('/Square')
  }

  return (
    <>
      {
        (getOperatingEnv() !=4) && <img src={goBackHomeIcon} style={style?style:{}} className={styles.goBackHomeIcon} onClick={goBackHome}/>
      }
    </>
  );
};

export default connect(({loading}: any) => ({loading}))(GoBackHome)
