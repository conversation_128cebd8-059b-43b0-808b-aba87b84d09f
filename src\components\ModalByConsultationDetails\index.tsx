import React, { useState,useEffect } from 'react';
import { history,connect } from 'umi';
import styles from "./index.less";
import {getOperatingEnv, goConsultationDetail, StatusRuleByConsultation} from "@/utils/utils";
import {
  getConsultationOrderInfo,
  submitConsultationPictureOrderPay as submitConsultationPictureOrderPayAction,
  submitConsultationVideoOrderPay as submitConsultationVideoOrderPayAction,     // 视频指导
} from "@/services/consultation/ConsultationList";
import Orderheader from "./Orderheader";
import ExpertCard from "./ExpertCard";
import {Modal, Spin, message, Checkbox, Button} from "antd";
import PayModal from "@/pages/ConsultationModule/PcComponents/PayModal";
import AgreementModal from "@/pages/ConsultationModule/StartConsultation/ComponentsPC/AgreementModal";

interface PropsType {
  visibleAndId: any,   // 是否展示订单详情弹窗和订单ID
  onCancel: any,       // 关闭弹窗
}
const ModalByConsultationDetails: React.FC<PropsType> = (props: PropsType) => {
  const { visibleAndId:id } = props;
  const [ consultationOrderInfo,setConsultationOrderInfo ] =  useState(null); // 指导订单详情
  const [ isConsentAgreement,setIsConsentAgreement ] =  useState(null);       // 是否同意协议
  const [ payVisible,setPayVisible ] =  useState(null);                       // 是否展示订单支付弹窗
  const [ isOpenPayModal,setIsOpenPayModal ] =  useState(null);               // 是否打开了支付弹窗 如果开启过支付弹窗关闭订单弹窗后需要刷新页面状态
  const [ agreementVisible,setAgreementVisible ] =  useState(null);           // 是否展示协议弹窗
  const userInfo = JSON.parse(localStorage.getItem('userInfo'));
  const { friUserId:idByUserInfo, name} = userInfo || {}

  const [ loading,setLoading ] =  useState(null);
  const statusText = ['已取消','未支付(待评估)', '待支付', '已支付']
  const {
    id:idByOrderInfo, // : "7c4df30622a0489ca978674374da4248",//指导订单ID
    orderNumber,      // : "2023100811415964635",//订单号
    expertsId,        // : 29,//指导医生ID
    expertsName,      // : null,
    status,           // : 1,//支付状态(0已取消、1未支付(待评估)、2去支付(已评估)、3已支付)
    type,             // : 2,//指导类型(1图文、2视频)
    duration,         // : null,//指导时长
    freeTimes,        // : null,//免费次数
    amount,           // : 0.10,//账单金额
    usableFreeTimes,  // : 1,//剩余免费次数
    unitPrice,        // : 1.00,//基础单价(图文是/次，视频是/30min)
    vipUnitPrice,     // : 1.00,//会员单价(图文是/次，视频是/30min)
    isFinish,         // : 0,
    h5BaseUserDto,    // : 专家信息
    createUserId,     // 创建人id
  } = consultationOrderInfo || {};
  const userInfoData = JSON.parse(localStorage.getItem('userInfo'));
  const { friUserId:userId } = userInfoData || {}  // 获取用户

  // 微信浏览器使用
  let OperatingEnv = getOperatingEnv();

  useEffect(() => {
    if(id) {
      getConsultationOrderInfoByPage();
    }else {
      // 关闭弹窗时清空页面数据
      setPayVisible(null);
      setIsOpenPayModal(null);
      setIsConsentAgreement(null);
      setConsultationOrderInfo(null);
    }
  },[id])


  // 查询指导订单详情
  const getConsultationOrderInfoByPage = async () => {
    setLoading(true);
    let getConsultationOrderInfoByData = await getConsultationOrderInfo({
      consultationId:id, // [string] 是 指导订单ID
      type:'2'  //  // [string] 是 (1:运营端订单详情, 2:H5/WEB端视频订单详情)
    })
    setLoading(false);
    const {
      code,
      content,
      msg,
    } = getConsultationOrderInfoByData || {};
    if(code == 200) {
      setConsultationOrderInfo(content);
    }else {
      message.error(msg ? msg : '指导订单详情查询失败');
      setConsultationOrderInfo(null);
    }
  }

  // 展示支付价格
  const showPaymentPrice = ()=>{
    // type == 1 ? vipUnitPrice || 0 :  amount || 0
    // 图文指导
    if(type == 1) {
      // 是否已支付
      if(status == 3) {
        // 已支付
        // 已支付是否使用免费次数
        if (freeTimes && freeTimes != 0) {
          // 图文指导-已支付-使用免费次数
          return amount;
        }else {
          // 图文指导-已支付-未使用免费次数
          return vipUnitPrice;
        }
      }else {
        // 图文指导-未支付
        return vipUnitPrice;
      }
    }else {
      // 视频指导
      return amount;
    }
  }

  // 核销免费次数
  const writeOffFreeTimes = async () => {
    if(type == 1) {
      // 判定当前指导类型 图文指导
      let dataBySubmitConsultationPictureOrderPayAction = await submitConsultationPictureOrderPayAction({
        wxUserId: idByUserInfo,
        userName: name,
        id: idByOrderInfo,                 //          指导订单ID
        payMethod: 1,                  // [int]    是 图文 1提交指导 2立即支付
      })
      const { code, content,msg} = dataBySubmitConsultationPictureOrderPayAction || {}
      if (code == 200) {
        // 核销成功
        message.success('核销成功');
        props.onCancel(true);
      }else {
        message.error(msg ? msg : '核销失败');
      }
    }else {
      // 视频指导结算
      let dataBySubmitConsultationVideoOrderPayAction = await submitConsultationVideoOrderPayAction({
        wxUserId: idByUserInfo,
        userName: name,
        id: idByOrderInfo,                 //          指导订单ID
        payMethod: 1,                  // [int]    是 图文 1提交指导 2立即支付
      })
      const { code, content, msg} = dataBySubmitConsultationVideoOrderPayAction || {}
      if (code == 200) {
        // 核销成功
        message.success('核销成功');
        props.onCancel(true);
      }else {
        message.error(msg ? msg : '核销失败');
      }
    }
  }

  return (
    <>
      <Modal
        title={`订单详情${statusText[status] ? `-${statusText[status]}` : ''}`}
        width={702}
        open={!payVisible && !!props.visibleAndId}
        footer={null}
        className={styles.ModalByConsultationDetails}
        onCancel={()=>{props.onCancel(isOpenPayModal)}}
      >
        <Spin spinning={!!loading}>
        <div className={styles.Mobile_Wrap}>
          {/* 指导订单信息 */}
          {consultationOrderInfo &&
            <div className={styles.OrderheaderWarp}>
              <Orderheader
                consultationType={type}
                data={consultationOrderInfo}
              />
            </div>
          }

          {/* 指导医生信息 */}
          {h5BaseUserDto &&
            <div className={styles.ExpertCardWarp}>
              <ExpertCard
                expertData={h5BaseUserDto}
              />
            </div>
          }


          {/* 指导详情 */}
          <div className={styles.Mobile_info_content}>
            {[
              {
                title: type == 1 ?  `图文指导` : '视频指导',
                content:
                  <div>
                    <span>¥</span>
                    <span>{vipUnitPrice ? vipUnitPrice : 0}</span>
                    <span>/次</span>
                  </div>
              },
              {
                title: '状态',
                content: <div>
                  <span onClick={()=>{
                    // 查验当前所在页面是否是指导详情页面
                    if(history.location.pathname.indexOf('/ConsultationModule/ConsultationDetails') > -1) {
                      // 当前页面是指导详情页面
                      props.onCancel(isOpenPayModal)
                    }else {
                      // 跳转到指导详情页面
                      goConsultationDetail(consultationOrderInfo)
                    }
                  }} className={styles.blueText}> 查看指导详情 </span>
                  <span> {StatusRuleByConsultation(consultationOrderInfo) ? StatusRuleByConsultation(consultationOrderInfo) : '-'} </span>
                </div>
              },
              {
                title: '指导时长',
                content: <div> <span>{ duration ? duration : 0 }</span><span>min</span> </div>
              },
              {
                title: '免费指导',
                content:  <div> <span>扣除 { freeTimes ? freeTimes : 0 } 次</span> <span>(30min/次)</span> </div>
              },
              {
                title: '免费指导剩余',
                content: <div> <span>{usableFreeTimes ? usableFreeTimes : 0}</span><span>次</span> </div>
              },
              {
                title: '现金支付',
                content: <div> <span>¥</span><span>{showPaymentPrice() ? showPaymentPrice() : 0}</span> </div>
              },
            ].map((item,index)=>{
              const { title,content } = item || {};
              return (
                <div key={index} style={{
                  marginBottom: index == 5 ? 0 : 12,
                }} className={styles.Mobile_info_content_item}>
                  <div className={styles.Mobile_info_content_item_left}>{ title }</div>
                  <div className={styles.Mobile_info_content_item_right}>{ content }</div>
                </div>
              )
            })}
          </div>
        </div>
          { createUserId == userId &&
            status == 2 &&
            <div className={styles.OnlinePaymentWarp}>
              <Checkbox
                value={!!isConsentAgreement}
                onChange={(e) => {
                  setIsConsentAgreement(e.target.checked);
                }}
              >
                我已阅读并同意
              </Checkbox>
              <span

                onClick={(e)=>{
                  e.stopPropagation();
                  console.log('color: #0095FF :: ',e);
                  setAgreementVisible(true);
                }}
                style={{
                  color: '#0095FF',
                  cursor: 'pointer',
                  userSelect: 'none',
                }}
              >《FRIDAY服务协议》</span>
              <Button
                onClick={()=> {
                  console.log('123123 :: ',amount,freeTimes);
                  if((!amount || (amount && amount == 0)) && (freeTimes && freeTimes != 0)) {
                    // 使用了免费次数
                    writeOffFreeTimes()
                  }else {
                    // 未使用免费次数
                    setPayVisible(true);
                    setIsOpenPayModal(true);
                  }
                }}
                className={styles.pay_btn}
                type="primary"
                disabled={!isConsentAgreement}
              >{showPaymentPrice() ? '确认支付' : '确认提交'}</Button>
            </div>
          }
        </Spin>
      </Modal>

      <AgreementModal
        visible={agreementVisible}
        onCancel={()=>{setAgreementVisible(false)}}
      ></AgreementModal>

      {payVisible &&
        <PayModal
          visible={payVisible}
          consultationId={idByOrderInfo}
          consultationType={type}
          onCancel={(isPayResultModal)=>{
            setPayVisible(false)
            getConsultationOrderInfoByPage();
            if (isPayResultModal){
              props.onCancel(isOpenPayModal)
            }
          }} // 关闭支付弹窗
          // payType={payType}
        />
      }
    </>
  )
}
export default connect(({ ConsultationList, loading }: any) => ({
  ConsultationList, loading
}))(ModalByConsultationDetails)
