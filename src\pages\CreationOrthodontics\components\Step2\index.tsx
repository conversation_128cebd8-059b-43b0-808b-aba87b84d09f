import React, {useState, useEffect, useRef} from 'react';
import { history,connect } from 'umi';
import { getArrailUrl } from '@/utils/utils'
import styles from "./index.less";
import {Input, Form, message} from "antd";
const { TextArea } = Input;
import ToothBit from "@/components/ToothBit/ToothBit";
import UploadByImage from "@/pages/CreationOrthodontics/components/UploadByImage";
import {
  setFormValues,
  updateFormInputContentById,
  getItemsByOperateType,
  scrollToTop
} from "@/pages/CreationOrthodontics/components/CreationFormUtils";
import { getToothBitInfoByToothPosition } from "@/utils/ToothSelect.js";
const { location } = history || {}
const { query } = location || {}
import _ from "lodash";
import {stringify} from "qs";

const Step2: React.FC = (props) => {
  // 是否嵌套在5i5ya的iframe中
  const isInIframe = (self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya')

  const [ form ] = Form.useForm();
  const { CreationOrthodontics } = props || {}
  const { medicalRecordJson,DataBymedicalRecordJson,DictionaryData } = CreationOrthodontics || {}  // 正畸病例字典结构
  const { consultationCaseInfoDto } = DictionaryData || {};
  const {
    checkIsEdit, // /** * 检查已编辑标识（1已编辑，0未编辑） */
  } = consultationCaseInfoDto || {}
  const {
    id:consultationId,       // :consultationId 8dad6e1a68714dc7b1f908902ad2d4f4
    customerId,              // 客户id
    createUserId,            // 创建人id
    tenantId,                // 租户id
    orderCaseTemplate,      // "orderCaseTemplate": 0, -- 订单病例模板 1通用病例 2正畸病例
  } = DictionaryData || {}

  // 检查项
  const checkJson5  = medicalRecordJson && medicalRecordJson.find((item)=>{return item.dictCode == 5})
  // 诊断分析
  const checkJson6  = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item)=>{return item.dictCode == '6'})
  // 模型分析
  const itemByModelAnalysis = checkJson6 && checkJson6.subsetList && checkJson6.subsetList.find((item)=>{return item.dictCode == '1'})
  // "侧位片分析"
  const itemByLateralFilmAnalysis = checkJson6 && checkJson6.subsetList && checkJson6.subsetList.find((item)=>{return item.dictCode == '2'})
  // "全景片分析" Panoramic Film Analysis
  const itemByPanoramicFilmAnalysis = checkJson6 && checkJson6.subsetList && checkJson6.subsetList.find((item)=>{return item.dictCode == '3'})
  let  operateTypeThreeItems = getItemsByOperateType(checkJson5, 3);

  useEffect(()=>{scrollToTop()},[]);


  useEffect(() => {
    echoFormValue()
    // updateFormInputContentById(DataBymedicalRecordJson,form)
  },[DataBymedicalRecordJson])

  // 回显
  const echoFormValue = ()=>{
    let ArrByForm = []
      .concat(itemByModelAnalysis.subsetList)
      .concat(itemByLateralFilmAnalysis.subsetList)
      .concat(itemByPanoramicFilmAnalysis.subsetList)

    if (Array.isArray(ArrByForm) && ArrByForm.length > 0) {
      setFormValues(ArrByForm,form)
    }
  }


  const onFormLayoutChange = _.debounce(() => {
    if (form) {
      let value = form.getFieldsValue()
      onFinish(value,false);
    }
  },3000);


  const renderMainItems = (checkJson5) => {
    if (checkJson5 && Array.isArray(checkJson5.subsetList)) {
      return checkJson5.subsetList.map((item1,index1)=>{
        return (
          <div key={index1} className={styles.pattern_Span}>
            <div key={index1} className={styles.pattern_title}>
              {item1.sortNum}、{item1.dictName}
            </div>
            <div className={styles.pattern_content}>
              <div className={styles.pattern_content_item_Warp}>
                {item1 && item1.subsetList && Array.isArray(item1.subsetList) && item1.subsetList.map((item2,index2)=>{
                  return (
                    <>
                      <div key={index2} className={styles.pattern_content_item}>
                        <div className={styles.pattern_content_lable}>{item2.dictCode}.{item2.dictName}：</div>
                        <div className={styles.pattern_content_value}>
                          {item2 && item2.operateType == 0 && item2.subsetList && Array.isArray(item2.subsetList) && item2.subsetList.map((item3,index3)=>{
                            // console.log('item31213 :: ',item3);
                            if (item3 && item3.isCheck == 1 && item3.operateType == 1) {
                              return <span key={index3} style={{marginRight:'3px'}}>{item3.dictName}</span>
                            } else {
                              if (item3 && Array.isArray(item3.subsetList)) {
                                return item3.subsetList.map((item4,index4)=>{
                                  // console.log('item4item4item4 : ',item4);
                                  if (item4 && item4.isCheck == 1 && item4.operateType == 1) {
                                    // console.log('item4.subsetList::: 123213 ',item4.subsetList);
                                    if (Array.isArray(item4.subsetList)) {
                                      // console.log('item4.subsetList::: 123213 ',item4.subsetList);
                                    }
                                    return <span key={index4} style={{marginRight:'3px'}}>{item3.dictName}:{item4.dictName}</span>
                                  }
                                })
                              }
                            }
                          })}

                          {item2 && item2.operateType == 2 &&
                            <div>{item2.inputContent}</div>
                          }
                        </div>
                      </div>
                    </>
                  )
                })}
              </div>
            </div>
            { (item1 && item1.dictCode == 2) &&
              <div className={styles.pattern_content}>
                <div className={styles.pattern_content_item_Warp}>
                  {operateTypeThreeItems && Array.isArray(operateTypeThreeItems) && operateTypeThreeItems.map((item,index)=>{
                    return (
                      <div key={index} className={styles.pattern_content_item}>
                        <ToothBit
                          ToothBefore={`${item.dictName}：`}
                          ToothInfo={getToothBitInfoByToothPosition(item.toothPosition)}
                          onClickToothBit={()=>{}}
                        />
                      </div>
                    )
                  })}
                </div>
              </div>
            }
          </div>
        )
      })
    }
  }

  const onFinish =_.debounce((value,isSubmitLoading,callBack) => {
    const { dispatch } = props || {}
    const { errorFields } = value || {}
    if (Array.isArray(errorFields) && errorFields.length > 0) {
      callBack && callBack();
      return
    }

    let formDataArr = Object.keys(value).map((key) => {
      let inputContent = value[key];
      if (inputContent && inputContent.length > 200) {
        inputContent = inputContent.substring(0, 200);
      }
      return { id: key,  inputContent: inputContent }
    })
    dispatch({
      type: 'CreationOrthodontics/saveDataByMedicalRecordJson',
      payload: {
        processNode:2,         // 检查及分析
        formDataArr:formDataArr,
        isSubmit:isSubmitLoading,
      }
    }).then(()=>{
      callBack && callBack();
    }).catch(()=>{
      callBack && callBack();
    })
  },500)


  return (
    <div className={styles.page_info}>
      <div className={styles.warp_content}>
        <div className={styles.title_box}>
          <div>检查及分析</div>
        </div>

        {/* Title-检查 */}
        <div className={styles.Item_Title}>
          <div>
            <span className={styles.Item_Span_dot}>*</span>
            <span className={styles.Item_Span_text}>检查</span>
          </div>
          <div onClick={()=>{
            const { consultationId, tenantId, customerId} = query || {}
            let value = form.getFieldsValue()
            onFinish(value,true,()=>{
              // 在5i5ya的iframe中
              if (isInIframe) {
                const postData = {
                  dataType: 'pathname',       // 页面地址onchange事件
                  pathnameByChild: '/CreationOrthodontics/OrthodonticChecklist',  // 路由信息
                  searchByChild: `?${stringify({
                    consultationId,
                    tenantId,
                    customerId
                  })}`,  // 路由信息
                }
                console.log('子级发送数据：', postData, getArrailUrl())
                window.parent.postMessage(postData, getArrailUrl())
                return
              }

              history.replace(`/CreationOrthodontics/OrthodonticChecklist?${stringify(history.location.query)}`)
            });
          }} className={styles.edit_flex}>
            <i className={styles.edit_icon}></i>
            <span className={styles.edit_text}>编辑</span>
          </div>
        </div>

        {/* 检查项未编辑-当前数据为空 */}
        {checkIsEdit == 1 ?
          <div className={styles.pattern_Span_Warp}>
            { renderMainItems(checkJson5) }
          </div>
          :
          <div className={styles.item_box_werp}>
            <div className={styles.item_box}></div>
            <div className={styles.item_span}>暂未填写检查单，请进入编辑～</div>
          </div>
        }
      </div>

      <Form
        form={form}
        initialValues={{}}
        onValuesChange={onFormLayoutChange}
        onFinish={(value)=>{ onFinish(value,true,()=>{
          // 在5i5ya的iframe中
          if (isInIframe) {
            let postData = {
              dataType: 'pathname',       // 页面地址onchange事件
              pathnameByChild: '/CreationOrthodontics/Step3',  // 路由信息
              searchByChild: `?${stringify(history.location.query)}`,  // 路由信息
            }
            console.log('子级发送数据：', postData, getArrailUrl())
            window.parent.postMessage(postData, getArrailUrl())
            return
          }

          history.replace(`/CreationOrthodontics/Step3?${stringify(history.location.query)}`)
        }) }}
      >
        {/* Title-诊断分析 */}
        <div className={styles.Item_Title}>
          <div>
            <span className={styles.Item_Span_dot}>*</span>
            <span className={styles.Item_Span_text}>诊断分析</span>
          </div>
        </div>

        <div className={styles.pattern_Span}>
          <div className={styles.pattern_title}> 1、模型分析</div>
          {itemByModelAnalysis &&
            [...itemByModelAnalysis.subsetList].map((itemBy1,index)=>{
              const {dictName,subsetList } = itemBy1 || {};
              return (
                <div key={itemBy1.id} className={styles.Item_pattern}>
                  <div className={styles.Item_title_lable}>{dictName}：</div>
                  <div className={styles.Item_title_value}>
                    <div className={styles.Item_title_value_content_Warp}>
                      {/* 当前是否一级未input */}
                      {itemBy1.operateType == 2 && <div  style={ itemBy1.dictCode == 4 ? {width:'100%'} : {}}>
                          {/* itemBy1.dictCode == 4 代表 Spee曲线 */}
                          <div  style={ itemBy1.dictCode == 4 ? {width:'100%'} : {}} className={styles.Item_title_value_content}>
                            <div style={ itemBy1.dictCode == 4 ? {width:'100%'} : { width:'74px' }}>

                              <Form.Item
                                label={null}
                                name={itemBy1.id}
                                rules={[
                                  { required: true, message: `请输入${itemBy1.dictName}!` },
                                  { max: 200, message: `${itemBy1.dictName}最多输入200字符` }
                                ]}
                              >
                                <Input autoComplete="off" placeholder={`请输入${itemBy1.dictName}...`}/>
                              </Form.Item>
                            </div>
                            { itemBy1.unit &&  <div style={{marginLeft:'8px'}} className={styles.Item_title_span_rigth}>{itemBy1.unit}</div> }
                          </div>
                        </div>
                      }
                    </div>
                    <div className={styles.Item_title_value_content_Warp}>
                      {/* 二级项目 */}
                      {Array.isArray(subsetList) && subsetList.map((itemBy2)=>{
                        return (
                          <Form.Item
                            key={itemBy2.id}
                          >
                            <div  style={ itemBy2.type == 'oneLine' ? {width:'100%'} : {}} className={styles.Item_title_value_content}>
                              { itemBy2.dictName && <div style={{marginRight:'8px'}}>{itemBy2.dictName}</div> }
                              <div style={ itemBy2.type == 'oneLine' ? {width:'100%'} : { width:'74px' }}>
                                <Form.Item
                                  key={itemBy2.id}
                                  label={null}
                                  name={itemBy2.id}
                                  noStyle={true}
                                  rules={[
                                    { required: true, message: `请输入${itemBy2.dictName}!` },
                                    { max: 200, message: `${itemBy2.dictName}最多输入200字符` }
                                  ]}
                                >
                                  <Input autoComplete="off" placeholder={`请输入`} />
                                </Form.Item>
                              </div>
                              { itemBy2.unit &&  <div style={{marginLeft:'8px'}} className={styles.Item_title_span_rigth}>
                                {itemBy2.unit}
                              </div> }
                            </div>
                          </Form.Item>
                        )
                      })}
                    </div>
                  </div>

                </div>
              )
            })
          }
        </div>

        {/* 2、侧位片分析 */}
        <div className={styles.pattern_Span}>
          <div className={styles.pattern_title}> 2、侧位片分析</div>
          { itemByLateralFilmAnalysis &&
            [
              ...itemByLateralFilmAnalysis.subsetList
            ].map((itemBy1)=>{
              const {dictName,subsetList } = itemBy1 || {};
              if(itemBy1.operateType == 2) {
                return (
                  <div key={itemBy1.id} className={styles.Item_pattern}>
                    <div className={styles.Item_title_lable}>{dictName}：</div>
                    <div className={styles.Item_title_value}>
                      <div style={{width:'100%', marginBottom:'16px' }}>
                        <Form.Item
                          label={null}
                          name={`${itemBy1.id}`}
                          rules={[
                            { required: true, message: `请输入${dictName}...` },
                            { max: 200, message: `${itemBy1.dictName}最多输入200字符` }
                          ]}
                        >
                          <TextArea  placeholder={`请输入${dictName}...`} style={{ height: 80 }} />
                        </Form.Item>
                      </div>
                    </div>
                  </div>
                )
              }else if(itemBy1.operateType == 4){
                /*return (
                  <div className={styles.Item_pattern}>
                    <div className={styles.Item_title_lable}></div>
                    <div className={styles.Item_title_value}>
                      <div>
                        <Form.Item
                          label={null}
                          name={`${itemBy1.id}`}
                          rules={[
                            { required: true, message: '请输入内容!!' }
                          ]}
                        >
                          <UploadByImage></UploadByImage>
                        </Form.Item>
                      </div>
                    </div>
                  </div>
                )*/
              }
            })
          }
        </div>

        {/* 3、全景片分析 */}
        <div className={styles.pattern_Span}>
          <div className={styles.pattern_title}> 3、全景片分析</div>
          {itemByPanoramicFilmAnalysis &&
            [
              ...itemByPanoramicFilmAnalysis.subsetList
            ].map((itemBy1)=>{
              if (!itemBy1) { return }
              const {dictName,subsetList } = itemBy1 || {};
              if(itemBy1.operateType == 2) {
                return (
                  <div key={itemBy1.id} className={styles.Item_pattern}>
                    <div className={styles.Item_title_lable}>{dictName}：</div>
                    <div className={styles.Item_title_value}>
                      <div style={{width:'100%', marginBottom:'16px' }}>
                        <Form.Item
                          label={null}
                          name={itemBy1.id}
                          rules={[
                            { required: true, message: `请输入${dictName}!` },
                            { max: 200, message: `${itemBy1.dictName}最多输入200字符` }
                          ]}
                        >
                          <TextArea placeholder={`请输入${dictName}...`}  style={{ height: 80 }} />
                        </Form.Item>
                      </div>
                    </div>
                  </div>
                )
              }else if(itemBy1.operateType == 4){
                /*return (
                  <div className={styles.Item_pattern}>
                    <div className={styles.Item_title_lable}></div>
                    <div className={styles.Item_title_value}>
                      <div>
                        <Form.Item
                          label={null}
                          name={`${itemBy1.id}`}
                          rules={[
                            { required: true, message: '请输入内容!!' }
                          ]}
                        >
                          <UploadByImage></UploadByImage>
                        </Form.Item>
                      </div>
                    </div>
                  </div>
                )*/
              }
            })
          }
        </div>
      </Form>

        {/* 上一步下一步 */}
        <div className={styles.submitWarp}>
          <div className={styles.submitBox}>
            <div onClick={()=>{
              if (form) {
                let value = form.getFieldsValue()
                onFinish(value,false,()=>{
                    // 在5i5ya的iframe中
                    if (isInIframe) {
                      const postData = {
                        dataType: 'goBack',       // 页面地址onchange事件
                      }
                      console.log('子级发送数据：', postData, getArrailUrl())
                      window.parent.postMessage(postData, getArrailUrl())
                    }else {
                      history.replace(`/CreationOrthodontics/Step1?${stringify(history.location.query)}`)
                    }
                });
              }
            }} className={styles.submit_btn_Cancel}>上一步</div>
            <div
              className={styles.submit_btn_Enter}
              onClick={()=>{
                if (checkIsEdit != 1) { message.warning('请编辑检查项！'); return }
                if (form) {
                  form.submit()
                }
              }}
            >下一步</div>
          </div>
        </div>
      </div>
  )
}

export default connect(({ CreationOrthodontics,pcAccount, loading }: any) => ({
  CreationOrthodontics,pcAccount, loading
}))(Step2)
