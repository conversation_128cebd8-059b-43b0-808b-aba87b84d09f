.screen_wrap{
  padding: 15px 0 0 12px;
  background: #fff;
}
.screen_container{
  height: 368px;
  overflow-y: auto;
  padding-bottom: 16px;
}
.screen_wrap_content {
  display: flex;
  flex-wrap: wrap;
  .screen_child_wrap{
    width: 33.3%;
    padding-right: 12px;
  }
}
.screen_wrap_content_case {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.screen_wrap_title ,.screen_wrap_title_one{
  font-size: 14px;
  font-weight: 400;
  color: #999;
}
.screen_wrap_title_one{
  margin-top: 15px;
}
.screen_child_word ,.screen_check_child_word {
  width: 100%;
  margin-top: 12px;
  display: inline-block;
  height: 32px;
  background: #F5F5F5;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 400;
  color: #666666;
  text-align: center;
  line-height: 32px;
}
.screen_check_child_word{
  background: #EDF9FF;
  color: #0095FF;
}
.screen_cases_child_wrap{
  width: 50%;
  padding-right: 12px;
}
.screen_check_cases_word,.screen_cases_word{
  margin-top: 12px;
  width:100%;
  height: 32px;
  background: #F5F5F5;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 400;
  color: #666666;
  text-align: center;
  line-height: 32px;
}

.screen_check_cases_word{
  background: #EDF9FF;
  color: #0095FF;
}

.screen_wrap_footer{
  width: 100%;
  height: 72px;
  padding-right: 12px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  .screen_wrap_footer_close ,.screen_wrap_footer_confirm{
    flex: 1;
    height: 40px;
    border-radius: 22px;
    font-size: 15px;
    font-weight: 500;
    text-align: center;
    line-height: 40px;
  }
  .screen_wrap_footer_close{
    margin-right: 15px;
    background: #EDF9FF;
    color: #0095FF;
  }
  .screen_wrap_footer_confirm{
    background: #0095FF;
    color: #FFFFFF;
  }
}
.screen_picker{
  display: inline-block;
  width: 170px;
  height: 32px;
  background: #f5f5f5;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 400;
  color: #999999;
  text-align: center;
  line-height: 32px;

  .select_date {
    color: #666;
  }
}
.screen_picker_line{
  width: 6px;
  border-top: 1px solid #CCCCCC;
  margin: 0 3px;
}
.screen_wrap_content_picker{
  display: flex;
  justify-content: space-between;
  padding-right: 12px;
  margin-top: 12px;
  align-items: center;
}