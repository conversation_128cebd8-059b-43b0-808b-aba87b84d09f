import fullscreen from "fullscreen.js";
import {getOperatingEnv} from "@/utils/utils";
/**
 * 从 window.location.href 中获取指定key的value
 * @param {*} key 要获取的 key
 * @returns window.location.href 中指定key对应的value
 * @example
 * const value = getUrlParam(key);
 */
export function getUrlParam(key) {
  const url = window.location.href.replace(/^[^?]*\?/, '');
  const regexp = new RegExp(`(^|&)${key}=([^&#]*)(&|$|)`, 'i');
  const paramMatch = url.match(regexp);

  return paramMatch ? paramMatch[2] : null;
}

/**
 * 从 window.location.href 获取search参数
 * @param {*} key 要获取的 key
 * @returns window.location.href 中指定key对应的value
 * @example
 * const value = getUrlParam(key);
 */
export function getUrlParamObj() {
  const urlParamObj = {};
  if (location.search) {
    const paramUrl = location.search.slice(1);
    paramUrl.split('&').forEach((item) => {
      const [key, value] = item.split('=');
      urlParamObj[key] = value;
    });
  }
  return urlParamObj;
}

/**
 * 单词首字母大写
 * @param {String}} str
 */
export function upperFirstLetter(str) {
  return str.split(' ')
    .map(item => item.slice(0, 1).toUpperCase() + item.slice(1))
    .join(' ');
}

// 数据是否是undefined
export function isUndefined(data) {
  return typeof data === 'undefined';
}

/**
 * 功能: 获取浏览器语言, 默认返回 'zh_CN'（中文）
 */
export function getLanguage() {
  let language = localStorage.getItem('language');
  const lang = navigator.language || navigator.userLanguage; // 常规浏览器语言和IE浏览器
  language = language || lang;
  language = language.replace(/-/, '_').toLowerCase();

  if (language === 'zh_cn' || language === 'zh') {
    language = 'zh_CN';
  } else if (language === 'zh_tw' || language === 'zh_hk') {
    language = 'zh_TW';
  } else {
    language = 'en_US';
  }

  return language || 'zh_CN';
};

/**
 * 功能: 获取 cookie 中特定 key 的值
 * @param {string} cname 就是 cookie 中对应的键
 *
 * ```javascript
 *     getCookie('language');  // 返回 cookie 中的设置的语言
 * ```
 */
export function getCookie(cname) {
  if (!cname) {
    return '';
  }
  const value = `; ${document.cookie}`;
  const parts = value.split('; ').find(str => str.includes(`${cname}=`)) || '';
  return parts.replace(`${cname}=`, '');
}

/**
 * 获取环境信息: 环境默认语言, 是否为移动端设备
 */
export function collectionCurrentInfo(req) {
  let currentEnvInfo = {};
  currentEnvInfo = {
    ...currentEnvInfo,
    isMobile: isMobile(req),
    // lang: getUrlParam('lang') || getCookie('lang') || getLanguage() || 'zh',
    lang: req && req.headers['accept-language'].split(';')[0].split(',')[0], // 'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
  };
  return currentEnvInfo;
}
/**
 * 判断当前环境是否为移动端设备
 */
export function isMobile(req) {
  let userAgent;
  if (req) { // if you are on the server and you get a 'req' property from your context
    userAgent = req.headers['user-agent']; // get the user-agent from the headers
  } else {
    // if you are on the client you can access the navigator from the window object
    userAgent = typeof navigator !== 'undefined' && navigator && navigator.userAgent;
  }
  const isMobile = Boolean(userAgent.match(/Android|BlackBerry|iPhone|iPad|iPod|Opera Mini|IEMobile|WPDesktop/i));

  return isMobile;
}

/**
 * 将 dom 元素全屏
 * @param {dom} element dom元素
 * @example
 * setFullscreen(document.documentElement) // 整个页面进入全屏
 * setFullscreen(document.getElementById("id")) // 某个元素进入全屏
 */
export function setFullscreen(element) {
  if (element.requestFullscreen) {
    element.requestFullscreen();
  } else if (element.mozRequestFullScreen) {
    element.mozRequestFullScreen();
  } else if (element.msRequestFullscreen) {
    element.msRequestFullscreen();
  } else if (element.webkitRequestFullscreen) {
    element.webkitRequestFullScreen();
  }
};

/**
 * 退出全屏
 * @example
 * exitFullscreen();
 */
export function exitFullscreen() {
  if (document.exitFullscreen) {
    document.exitFullscreen();
  } else if (document.msExitFullscreen) {
    document.msExitFullscreen();
  } else if (document.mozCancelFullScreen) {
    document.mozCancelFullScreen();
  } else if (document.webkitExitFullscreen) {
    document.webkitExitFullscreen();
  }
};

/**
 * mimetype 支持检查
 */
export function getSupportedMimeTypes() {
  const possibleTypes = [
    'video/webm;codecs=vp9,opus',
    'video/webm;codecs=vp8,opus',
    'video/webm;codecs=h264,opus',
    'video/mp4;codecs=h264,aac',
  ];
  return possibleTypes.filter(mimeType => MediaRecorder.isTypeSupported(mimeType));
}

/**
 * 上报 TAM 数据
 */
export function uploadToTAM(eventString, sdkAppId) {
  window && window.aegis && window.aegis.reportEvent({
    name: eventString.split('#')[0] || '',
    ext1: eventString,
    ext2: 'webrtcSamplesSite', // webrtc samples-site 为 100
    ext3: sdkAppId,
  });
}
// https://doc.weixin.qq.com/sheet/e3_AJQAlgbNACk3JTdvUgfSKGQ6PYbJ5?scode=AJEAIQdfAAoO04FJEHAMEAfQZdACo
// 进房成功、失败上报到 TAM
export const joinRoomSuccessUpload = sdkAppId => uploadToTAM('joinRoom-success', sdkAppId);
export const joinRoomFailedUpload = (sdkAppId, errorMsg) => {
  console.warn(errorMsg);
  uploadToTAM(`joinRoom-failed#error: ${errorMsg}`, sdkAppId);
};
// 推流成功、失败上报到 TAM
export const publishSuccessUpload = sdkAppId => uploadToTAM('publish-success', sdkAppId);
export const publishFailedUpload = (sdkAppId, errorMsg) => {
  uploadToTAM(`publish-failed#error: ${errorMsg}`, sdkAppId);
};
// 初始化流成功、失败上报到 TAM
export const initLocalStreamSuccessUpload = sdkAppId => uploadToTAM('initLocalStream-success', sdkAppId);
export const initLocalStreamFailedUpload = (sdkAppId, errorMsg) => {
  uploadToTAM(`initLocalStream-failed#error: ${errorMsg}`, sdkAppId);
};
// 使用美颜操作上报到 TAM
export const beautyOperationUpload = (sdkAppId, type) => {
  uploadToTAM(`beautyOperation-success#type: ${type}`, sdkAppId);
};
// 流录制成功、失败上报到 TAM
export const recordStreamSuccessUpload = sdkAppId => uploadToTAM('recordStream-success', sdkAppId);
export const recordStreamFailedUpload = (sdkAppId, errorMsg) => {
  uploadToTAM(`recordStream-failed#error: ${errorMsg}`, sdkAppId);
};
// 开启水印上报到 TAM
export const openWaterMarkUpload = sdkAppId => uploadToTAM('openWaterMark-success', sdkAppId);
// 使用语音识别上报到 TAM
export const startAsrUpload = sdkAppId => uploadToTAM('startAsr-success', sdkAppId);
// 推流 CDN 成功、失败上报到 TAM
export const publishCdnSuccessUpload = sdkAppId => uploadToTAM('publishCdn-success', sdkAppId);
export const publishCdnFailedUpload = sdkAppId => uploadToTAM('publishCdn-failed', sdkAppId);

// 判定当前手机是Iphone 并且包含刘海屏或者灵动岛
export const isIphoneIslandSeries  = () => {
  const isLandscape = window.matchMedia("(orientation: landscape)").matches;

  let userAgent = typeof navigator !== 'undefined' && navigator && navigator.userAgent;

  if(!userAgent){
    message.warning('userAgent 未获取');
    console.log('userAgent 未获取: ',userAgent);
    return false;
  }

  const isIPhone = /iPhone/.test(userAgent + '');
  if (!isIPhone) {
    return false;
  }

  let env = getOperatingEnv()
  if (!fullscreen.is() && env != 2) {
    return false;
  }

  if (!isLandscape) {
    return false;
  }

  // 使用CSS媒体查询判断是否有灵动岛和遮挡
  const dummyElement = document.createElement('div');
  dummyElement.style.cssText = 'position:fixed; inset:0; padding: env(safe-area-inset-top, 0) env(safe-area-inset-right, 0) env(safe-area-inset-bottom, 0) env(safe-area-inset-left, 0);';
  document.body.appendChild(dummyElement);

  // 获取计算后的样式
  const computedStyle = window.getComputedStyle(dummyElement);
  const paddingTop = parseInt(computedStyle.paddingTop, 10);
  const paddingBottom = parseInt(computedStyle.paddingBottom, 10);

  // 判断是否有灵动岛和遮挡
  let hasNotch = paddingTop > 0 || paddingBottom > 0;

  // 移除临时元素
  document.body.removeChild(dummyElement);

  if (!hasNotch && isLandscape) {
    if (userAgent.includes("iPhone X") || userAgent.includes("iPhone XR") ||
      userAgent.includes("iPhone XS") || userAgent.includes("iPhone XS Max") ||
      userAgent.includes("iPhone 11") || userAgent.includes("iPhone 12") ||
      userAgent.includes("iPhone 13") || userAgent.includes("iPhone 14")
    ) {
      hasNotch = true; // 设备包含刘海屏或者灵动岛
    }
  }

  return hasNotch;
}


// 保存当前是否开启分享课件



// 保存 静音/关闭摄像头/是否开启分享课件,状态到本地
// params value: {id:1, muteAudio: false, muteVideo: false, screenShare: false}
export const saveLocalStateByStream = (value) => {
  if (!value) { return }
  // 默认空间状态
  let DefaultLocalStateBySpatialObj = {
    mutedAudio: false,
    mutedVideo: false,
    openTiw:false,
  };
  // 获取本地已存储的数据 根据直播间id 获取本地存储的状态
  let LocalStateArrBySpatial = localStorage.getItem('LocalStateArrBySpatial');
  if (!!LocalStateArrBySpatial && !!JSON.parse(LocalStateArrBySpatial) && Array.isArray(JSON.parse(LocalStateArrBySpatial))) {
    let LocalStateArrBySpatialObj = JSON.parse(LocalStateArrBySpatial);
    let findByLocalStateArrBySpatial = LocalStateArrBySpatialObj.find((item, index) => {
      if (item.id === value.id) {
        LocalStateArrBySpatialObj[index] = {
          ...item,
          ...value,
        };
        return true;
      }
    })
    if (!findByLocalStateArrBySpatial) {
      LocalStateArrBySpatialObj.push({
        ...DefaultLocalStateBySpatialObj,
        ...value
      });
    }
    let StrByLocalStateArrBySpatialObj  =JSON.stringify(LocalStateArrBySpatialObj);
    localStorage.setItem('LocalStateArrBySpatial', StrByLocalStateArrBySpatialObj);
  }else {
    let LocalStateArrBySpatialObj = [];
    LocalStateArrBySpatialObj.push({
     ...DefaultLocalStateBySpatialObj,
     ...value
    });
    let StrByLocalStateArrBySpatialObj  =JSON.stringify(LocalStateArrBySpatialObj);
    localStorage.setItem('LocalStateArrBySpatial', StrByLocalStateArrBySpatialObj);
  }
}

// 获取本地存储的 静音/关闭摄像头/状态
export const getLocalStateByStream = (id) => {
  let LocalStateArrBySpatial = localStorage.getItem('LocalStateArrBySpatial');
  if (!!LocalStateArrBySpatial && !!JSON.parse(LocalStateArrBySpatial) && Array.isArray(JSON.parse(LocalStateArrBySpatial))) {
    let LocalStateArrBySpatialObj = JSON.parse(LocalStateArrBySpatial);
    let findByLocalStateArrBySpatial = LocalStateArrBySpatialObj.find((item, index) => {
      if (item.id == id) {
        return true;
      }
    })
    return findByLocalStateArrBySpatial;
  }else {
    return null;
  }
}

// 删除指定空间存储的 静音/关闭摄像头/状态数据
export const clearLocalStateByStreamById = (id) => {
  let LocalStateArrBySpatial = localStorage.getItem('LocalStateArrBySpatial');
  if (!!LocalStateArrBySpatial && !!JSON.parse(LocalStateArrBySpatial) && Array.isArray(JSON.parse(LocalStateArrBySpatial))) {
    let LocalStateArrBySpatialObj = JSON.parse(LocalStateArrBySpatial);
    let findByLocalStateArrBySpatial = LocalStateArrBySpatialObj.find((item, index) => {
      if (item.id === id) {
        LocalStateArrBySpatialObj.splice(index, 1);
        return true;
      }
    })
    let StrByLocalStateArrBySpatialObj  =JSON.stringify(LocalStateArrBySpatialObj);
    localStorage.setItem('LocalStateArrBySpatial', StrByLocalStateArrBySpatialObj);
  }
}


// 判定当前设备环境是否低于ios15 默认高于
export const isLowIos15 = () => {
  const iOSVersionRegex = /iPhone OS (\d+)/;
  const versionMatch = window.navigator.userAgent.match(iOSVersionRegex);
  let iosVersion = versionMatch ? parseInt(versionMatch[1], 10) : null;
  return iosVersion ? iosVersion && iosVersion < 12 : false;
}

// 发送鼓掌/送花消息合并相同msgGroupId(组id) 保留最后一条消息的msgGroupCount(组内消息数量)
export const processMessageArray = (messageArray) => {
  const msgGroupCountMap = {}; // 用于记录每个 msgGroupId 的 msgGroupCount 数据

  // 统计每个 msgGroupId 的 msgGroupCount 数据
  for (const message of messageArray) {
    const { msgGroupId, msgGroupCount } = message;
    if (msgGroupId) {
      msgGroupCountMap[msgGroupId] = msgGroupCount;
    }
  }

  // 去重 msgGroupId 并保留最后一个 msgGroupId 的 msgGroupCount 数据
  const processedArray = [];
  const addedMsgGroupIds = new Set(); // 用于记录已经添加过的 msgGroupId

  for (const message of messageArray) {
    const { msgGroupId } = message;
    if (!msgGroupId){
      processedArray.push(message);
    }else {
      if (msgGroupId && !addedMsgGroupIds.has(msgGroupId)) {
        addedMsgGroupIds.add(msgGroupId);
        const msgGroupCount = msgGroupCountMap[msgGroupId];
        const processedMessage = {...message, msgGroupCount};
        processedArray.push(processedMessage);
      }
    }
  }

  return processedArray;
}

// 获取userInfo
export const getUserInfoData = () => {
  // JSON.parse(localStorage.getItem('userInfo'))
  let userInfo = localStorage.getItem('userInfo');
  if (!userInfo) {
    return null;
  }
  userInfo = JSON.parse(userInfo);
  return userInfo;
}

// 获取远端流中屏幕分享流
export const getShareRemoteStreamConfig = (SpaceInfo,remoteStreamConfigList) => {
  // 远端流中屏幕分享流
  const shareRemoteStreamConfig = SpaceInfo && Array.isArray(remoteStreamConfigList) && remoteStreamConfigList.find(item => item.userID.indexOf('share') !== -1 && item.hasVideo);
  return shareRemoteStreamConfig;
}

// 获取远端流中的主持人流
export const getHostRemoteStreamConfig = (SpaceInfo,hostUserInfo,remoteStreamConfigList) => {
  // 远端流中主持人流
  const hostRemoteStreamConfig = SpaceInfo &&  Array.isArray(remoteStreamConfigList) && remoteStreamConfigList.find(item => item.userID == hostUserInfo?.imUserId);
  return hostRemoteStreamConfig;
}

// 获取远端流中摄像头流
// const userCameraRemoteStreamList = SpaceInfo && Array.isArray(remoteStreamConfigList) && remoteStreamConfigList.filter(item => item.userID.indexOf('share') == -1 && (item.hasVideo || item.hasAudio)); // 远端流中摄像头流 只要又画面或者有声音
export const getUserCameraRemoteStreamList = (SpaceInfo,remoteStreamConfigList) => {
  // 远端流中摄像头流 只要又画面或者有声音
  const userCameraRemoteStreamList = SpaceInfo && Array.isArray(remoteStreamConfigList) && remoteStreamConfigList.filter(item => item.userID.indexOf('share') == -1 && (item.hasVideo || item.hasAudio));
  return userCameraRemoteStreamList;
}

// isModeMatrix矩阵模式 去除主持人画面流 和连麦嘉宾流
/**
 *  const isModeMatrixCameraRemoteStreamList = SpaceInfo && Array.isArray(userCameraRemoteStreamList) && userCameraRemoteStreamList
 *     ?.filter(item => item.userID != hostUserInfo.imUserId)
 *     ?.filter(item => Array.isArray(handUpList) && handUpList.length != 0 ? handUpList.find((value)=>{
 *       return value.imUserId != item.userID
 *     }) : true);
 */
export const getIsModeMatrixCameraRemoteStreamList = (SpaceInfo,hostUserInfo,handUpList,userCameraRemoteStreamList) => {
  // 远端流中摄像头流 只要又画面或者有声音
  let isModeMatrixCameraRemoteStreamList = SpaceInfo && Array.isArray(userCameraRemoteStreamList) && userCameraRemoteStreamList
    ?.filter(item => item.userID != hostUserInfo?.imUserId)
    ?.filter(item => Array.isArray(handUpList) && handUpList.length > 0 ? !(handUpList.find((value)=>{
      return value.imUserId == item.userID // value. value.imUserId != item.userID
    })) : true);
  return isModeMatrixCameraRemoteStreamList;
}
// 获取连麦人画面信息
//  const handUpRemoteStreamList = SpaceInfo && Array.isArray(userCameraRemoteStreamList) && userCameraRemoteStreamList.filter(item => Array.isArray(handUpList) && handUpList.find((value)=>{ return value.imUserId == item.userID}));
export const getHandUpRemoteStreamList = (SpaceInfo,handUpList,userCameraRemoteStreamList) => {
  // 远端流中摄像头流 只要又画面或者有声音
  const handUpRemoteStreamList = SpaceInfo && Array.isArray(userCameraRemoteStreamList) && userCameraRemoteStreamList.filter(item => Array.isArray(handUpList) && handUpList.find((value)=>{ return (value.statusType == 1 && value.imUserId == item.userID)}));
  return handUpRemoteStreamList;
}

// 排序流画面
/**
 *  let cameralistArr = []
 *     // .concat(shareRemoteStreamConfig ? [shareRemoteStreamConfig] : [])                                 // 屏幕分享流保持第一位
 *     .concat(hostRemoteStreamConfig ? [hostRemoteStreamConfig] : [])                                      // 主持人保持第一位                       // 主持人保持第一位
 *     .concat(localStreamConfig ? [localStreamConfig] : [])                                                // 本人流保持第二位
 *     .concat(Array.isArray(handUpRemoteStreamList) ? handUpRemoteStreamList : [])                         // 连麦人的画面放在最后三位
 *     .concat(Array.isArray(isModeMatrixCameraRemoteStreamList)? isModeMatrixCameraRemoteStreamList : []); // 嘉宾画面和连麦人保持第四位
 *   cameralistArr = cameralistArr.filter((item) => { if(!!item && !!item.stream){ return item }});   // 去除空值 或无直播流的用户
 */
export const getCameralistArr = (hostRemoteStreamConfig,localStreamConfig,handUpRemoteStreamList,isModeMatrixCameraRemoteStreamList) => {
  let cameralistArr = []
    // .concat(shareRemoteStreamConfig ? [shareRemoteStreamConfig] : [])                                 // 屏幕分享流保持第一位
    .concat(hostRemoteStreamConfig ? [hostRemoteStreamConfig] : [])                                      // 主持人保持第一位
    .concat(localStreamConfig && localStreamConfig.hasVideo ? [localStreamConfig] : [])                                                // 本人流保持第二位
    .concat(Array.isArray(handUpRemoteStreamList) ? handUpRemoteStreamList : [])                         // 连麦人的画面放在最后三位
    .concat(Array.isArray(isModeMatrixCameraRemoteStreamList)? isModeMatrixCameraRemoteStreamList : []); // 嘉宾画面和连麦人保持第四位
  cameralistArr = cameralistArr.filter((item) => { if(!!item && !!item.stream){ return item }});   // 去除空值 或无直播流的用户
  return cameralistArr;
}

// 获取当前发言人
export const getCurrentUserInfoVolumeList = ({userCameraRemoteStreamList,localStreamConfig,currentLiveUserList,handUpList})=> {
  let currentUserInfoVolumeList = []
  // 获取流中当前发言人
  let userStreamList = Array.isArray(userCameraRemoteStreamList) && localStreamConfig ? [...userCameraRemoteStreamList,localStreamConfig] : [];
  // !showStream.mutedAudio && showStream.audioVolume > 0
  let AudioByUserStreamList = userStreamList.filter((item) => {
    return (item.hasAudio && !item.mutedAudio) || !item.hasAudio
  })
  let currentVolumeList = userStreamList.filter((item)=>{
    return item && !item.mutedAudio && item.audioVolume > 0
  })
  currentVolumeList.map((item)=>{
    if (Array.isArray(currentLiveUserList)) {
      let findByCurrentLiveUserList = currentLiveUserList.find((itemByCurrentLiveUserList)=>{
        return itemByCurrentLiveUserList.imUserId == item.userID
      })
      if (findByCurrentLiveUserList) {
        currentUserInfoVolumeList.push({...findByCurrentLiveUserList,audioVolume:item.audioVolume})
      }
    }
    if (Array.isArray(handUpList)) {
      let findByHandUpList = handUpList.find((itemByCurrentLiveUserList)=>{
        return itemByCurrentLiveUserList.imUserId == item.userID
      })
      if (findByHandUpList) {
        currentUserInfoVolumeList.push({...findByHandUpList,audioVolume:item.audioVolume})
      }
    }
  })
  return currentUserInfoVolumeList;
}

// 判定当前是否有摄像头画面
export const getIsNotHasVideo = ({localStreamConfig,userCameraRemoteStreamList}) => {
  let isHasVideoByLocalStreamConfig = (localStreamConfig && localStreamConfig.mutedVideo) || (localStreamConfig && !localStreamConfig.hasVideo)
  let isHasVideoByUserCameraRemoteStreamList = Array.isArray(userCameraRemoteStreamList) && userCameraRemoteStreamList.filter((item)=>{
    return (!!item.hasVideo && !item.mutedVideo)
  })

  let isNotHasVideo =
    isHasVideoByLocalStreamConfig
    && isHasVideoByLocalStreamConfig
    && isHasVideoByUserCameraRemoteStreamList
    && isHasVideoByUserCameraRemoteStreamList.length == 0;

  return isNotHasVideo;
}
