.page_Warp {
  background: #EEF3F9;
  padding-top: 10px;
}

.page_content_Warp {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #EEF3F9;
}

.page_content {
  width: 1228px;
  background: #EEF3F9;
}

.nav_title {
  width: 1228px;
  height: 84px;
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
  opacity: 1;
  font-size: 24px;
  font-weight: 600;
  color: #000000;
  line-height: 24px;
  display: flex;
  align-items: center;
  padding-left: 24px;
  padding-right: 24px;
  margin-bottom: 16px;
}

.backspace {
  width: 16px;
  height: 16px;
  background: url('../../../../assets/CreationOrthodontics/OrthodonticChecklist_backspace.png');
  background-size: 16px 16px;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  margin-right: 16px;
  cursor: pointer;
  user-select: none;
}

.form_warp {
  width: 1228px;
  // height: 1841px;
  background: #FFFFFF;
  border-radius: 8px 8px 0px 0px;
  opacity: 1;
  padding-top: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.form_title {
  font-size: 20px;
  font-weight: 500;
  color: #000000;
  line-height: 20px;
  text-align: center;
  margin-bottom: 20px;
}

.title_span {
  font-size: 14px;
  font-weight: 500;
  color: #000000;
  line-height: 14px;
  margin-bottom: 12px;
}

.content_warp {
  width: 873px;
}

.content_span {

}

.item_span {
  display: flex;
  padding-left: 33px;

  .item_lable {
    width: 124px;
    margin-right: 8px;
    line-height: 25px;
    text-align: right;
    color: #000000;
  }
  .item_value {
    width: calc(100% - 120px);
    flex-wrap: wrap;
  }
  .item_value_content {
    display: flex;
    flex-wrap: wrap;
    :global {
      .ant-form-item {
        margin-bottom: 10px;
        width: 100%;
      }
    }

    .item_value_content_select_warp {
      display: flex;
      flex-direction: column;
      margin-bottom: 18px;
    }
    .item_value_content_select {
      display: flex;
      flex-wrap: wrap;
    }
  }

  .Item_input {
    margin-bottom: 18px;
    width: 100%;
  }

  .checkBox_Warp {
    display: flex;
    justify-content: space-between;
    // margin-bottom: 18px;
  }

  .checkBox {
    width: 72px;
    height: 24px;
    background: #fff;
    border: 1px solid #DCDFE6;
    border-radius: 16px 16px 16px 16px;
    opacity: 1;
    font-size: 14px;
    font-weight: 400;
    color: #000000;
    line-height: 22px;
    margin-right: 24px;
    text-align: center;
    cursor: pointer;
    user-select: none;
    margin-bottom: 5px;
  }

  .ToothBitWarp {
    margin-right: 30px;
    margin-bottom: 10px;
  }

  .active {
    border: 1px solid #409EFF;
    background: #409EFF;
    color: #FFFFFF;
  }
}

.submitWarp {
  display: flex;
  justify-content: space-around;
  margin-top: 48px;
  padding-bottom: 53px;
  .submitBox {
    display: flex;
    .submit_btn_Cancel {
      width: 92px;
      height: 36px;
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      border: 1px solid #0095FF;
      font-size: 14px;
      font-weight: 400;
      color: #0095FF;
      line-height: 36px;
      user-select: none;
      cursor: pointer;
      text-align: center;
      margin-right: 30px;
    }
    .submit_btn_Enter {
      width: 92px;
      height: 36px;
      background: #0095FF;
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      font-size: 14px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 36px;
      text-align: center;
      user-select: none;
      cursor: pointer;
    }
  }
}

.select_tooth{
  min-width:1073px;
  :global{
    .ant-btn:focus {
      border-color: #D9D9D9;
    }
  }
}

.Item_title_value_content {
    display: flex;
    font-size: 14px;
    font-weight: 400;
    color: #000000;
    line-height: 26px;
    flex-direction: row;
    align-items: center;
    margin-right: 40px;
    margin-bottom: 15px;

}

.itemByLv3Id20 {
  display:flex;

  .RadioBox {
    display:flex;
  }
}

