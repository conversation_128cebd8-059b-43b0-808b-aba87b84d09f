/**
 * @Description: PC 指导头部信息卡片
 * @author: 赵斐
 */
import React, { useState } from 'react';
import { history } from 'umi';
import { getArrailUrl } from '@/utils/utils'
import { Typography, message } from 'antd';
import arrowIcon from '@/assets/Consultation/H5/arrow_icon.png'
import copyIcon from '@/assets/Consultation/H5/copy_icon.png'
import calendarIcon from '@/assets/Consultation/H5/calendar_icon.png'
// 添加客服小忆微信弹窗（PC）
import CustomerServiceWeChatModal from '../CustomerServiceWeChatModal'
import styles from './index.less'

interface PropsType {
  data: any,              // 展示数据
  consultationId: any,    // 指导ID
  consultationType: any,  // 指导类型
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  // 是否嵌套在5i5ya的iframe中
  const isInIframe = (self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya')

  const { data, consultationId, consultationType } = props;
  const [state, setState] = useState(false)  // 添加客服小忆微信弹窗状态
  const {
    videoAppointment,   // 预约时间对象
    thisUserIsExperts,   // 当前人是否是专家：0:否，1:是
    defaultAssistantUrl, // 二维码地址
    status,              // 支付状态(0已取消、1未支付(待评估)、2去支付(已评估)、3已支付)
  } = data || {}
  const {
    spaceId,    // 关联的空间ID
    dateStr,    // 日期字符串
    weekStr,    // 星期字符串
    timeStr,    // 时间字符串
    password,   // 空间密码
  } = videoAppointment || {}

  // 去创建直播
  const goToCreateSpace = () => {
    if (thisUserIsExperts == 1) {
      // 在5i5ya的iframe中
      if (isInIframe) {
        const postData = {
          dataType: 'pathname',       // 页面地址onchange事件
          pathnameByChild: '/UserInfo/CreateSpaceByPc/Live',  // 路由信息
          searchByChild: `?id=${spaceId}&consultationId=${consultationId}`,  // 路由信息
        }
        console.log('子级发送数据：', postData, getArrailUrl())
        window.parent.postMessage(postData, getArrailUrl())
        return
      }

      history.push(`/UserInfo/CreateSpaceByPc/Live?id=${spaceId}&consultationId=${consultationId}`)
    } else {
      setState(true)
    }
  }
  // 关闭添加客服小忆微信、预约指导二维码弹窗
  const onClickWeChatModalHide = () => {
    setState(false)
  }
  // 复制提示文案
  const onCopy = () => {
    message.success('复制成功')
  }

  // 进入视频会议
  const enterSpaceId = () => {
    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'pathname',       // 页面地址onchange事件
        pathnameByChild: `/PlanetChatRoom/Live/${spaceId}`,  // 路由信息
        searchByChild: ``,  // 路由信息
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
      return
    }

    history.push(`/PlanetChatRoom/Live/${spaceId}`)
  }

  return (
    <div className={styles.wrap}>
      <div className={styles.header}>
        <span className={styles.header_title}>预约时间</span>
        <span className={styles.header_title_tips}>该期间请保持手机通畅</span>
        {
          password && <>
            <span className={styles.header_psd}>密码：{password}</span>
            <Typography.Paragraph copyable={{
              text: password,
              icon: [<span className={styles.header_copy}><img src={copyIcon} alt="icon" />复制</span>, <span className={styles.header_copy}><img src={copyIcon} alt="icon" />复制</span>],
              tooltips: [false, false],
              onCopy: onCopy,
            }}></Typography.Paragraph>
          </>
        }

        {
          status != 0 ? <>
            <span className={styles.header_status} onClick={() => { enterSpaceId() }}>进入视频会议</span>
            <span className={styles.header_icon}><img src={arrowIcon} alt="icon" /></span>
          </> : null
        }

      </div>

      <div className={styles.content}>
        <div className={styles.reservation}>
          <p className={styles.reservation_left}>
            <span>{spaceId ? `${dateStr}${weekStr ? ',' : null}${weekStr}${timeStr ? ',' : null}${timeStr}` : "暂未预约时间"}</span>
          </p>
          {
            status == 1 ? <p className={styles.reservation_right}>
              <span><img src={calendarIcon} alt="" /></span>
              <span onClick={goToCreateSpace}>我要改约</span>
            </p> : null
          }

        </div>
      </div>
      {
        state && <CustomerServiceWeChatModal type={thisUserIsExperts == 1 ? 1 : 2} visible={state} url={defaultAssistantUrl} consultationId={consultationId} consultationType={consultationType} onCancel={onClickWeChatModalHide} />
      }
    </div>
  )
}
export default Index
