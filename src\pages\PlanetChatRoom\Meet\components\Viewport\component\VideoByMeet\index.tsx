import React, {useRef, useState,} from 'react';
import {formatTime, getDAesString, useDebounce} from '@/utils/utils';
import {Modal} from 'antd-mobile';
import {licenseUrl} from '@/app/config';
import {connect, history} from 'umi';
import styles from './index.less';
import classNames from 'classnames';
import TCPlayer from 'tcplayer.js';
import './video-js.min.css';
import 'tcplayer.js/dist/tcplayer.min.css';
import _ from 'lodash';
import dayjs from 'dayjs';
import {Slider} from 'antd';

type propsType = {
  global: any;
};

const Index: React.FC<propsType> = (props) => {
  const {dispatch, PlanetChatRoom} = props || {};

  const {SpaceInfo, playerInfo, isNotLogin, isHorizontalLive} = PlanetChatRoom || {};

  const {
    status: statusBySpaceInfo,
    videoList,
    videoSecond,
    spaceCoverUrlShow,
    isNeedPwd,
    id: spaceId,
  } = SpaceInfo || {};

  const [playerStateData, setPlayerStateData] = useState(null);
  const [videoPlayer, setVideoPlayer] = useState(null);
  const [isShowTrySeeTip, setIsShowTrySeeTip] = useState(false);

  const playerRefByHorizontalLiveRoom = useRef(null);
  const videoContentRefByHorizontalLiveRoom = useRef(null);

  let handlePlayerStateChange = (state, prevState) => {
    setPlayerStateData(state);
    getSpaceBulletScreen(state);
    findItemDanmuByCurrentTime(state);
  };

  let getSpaceBulletScreen = _.throttle((state) => {
    const {recordStartTime} = videoList[0] || {};
    const {currentTime} = state || {};
    let currentTimeByDayJS = dayjs(recordStartTime, 'YYYY-MM-DD HH:mm:ss')
      .add(_.floor(currentTime, 0), 'second')
      .format('YYYY-MM-DD HH:mm:ss');
    getSpaceBulletScreenByModel(currentTimeByDayJS);
  }, 60000);

  let findItemDanmuByCurrentTime = _.throttle(async (state) => {
    const {recordStartTime} = videoList[0] || {};
    const {currentTime} = state || {};
    let currentTimeByDayJS = dayjs(recordStartTime, 'YYYY-MM-DD HH:mm:ss')
      .add(_.floor(currentTime, 0), 'second')
      .format('YYYY-MM-DD HH:mm:ss');
    let Danmulist = await dispatch({type: 'PlanetChatRoom/getSpaceGroupMsgByMsgSeqByState'});
    let findItemByDanmulist =
      Danmulist &&
      Danmulist.find((item) => {
        return item.msgDataTime == currentTimeByDayJS;
      });
    if (findItemByDanmulist) {
    }
  }, 1000);

  const getSpaceBulletScreenByModel = async (currentTimeByDayJS) => {
    const dataByGetSpaceGroupMsg = await dispatch({
      type: 'PlanetChatRoom/getSpaceBulletScreen',
      payload: {
        spaceId: spaceId,
        eventTime: currentTimeByDayJS,
        // msgSeq:msgSeq,
      },
    });
  };

  const autoPlay = async (videoPlayer) => {
    if (!!videoPlayer && isNeedPwd == 0) {
      await videoPlayer.muted(true);
      await setTimeout(() => {
      }, 100);
      await videoPlayer.play();
      await setTimeout(() => {
      }, 100);
      await videoPlayer.muted(false);
    }
  };

  React.useEffect(() => {
    if (statusBySpaceInfo != 3 && !(Array.isArray(videoList) && videoList[0])) {
      return;
    }
    if (!playerRefByHorizontalLiveRoom.current) {
      return;
    }
    const {location} = history || {};
    const {query} = location || {};
    let {lastTime} = query || {};

    let currentTimeByVideoSecond = lastTime != 0 ? lastTime : null || videoSecond || 0;

    const videoJsNode = playerRefByHorizontalLiveRoom.current;
    const videoContentNode = videoContentRefByHorizontalLiveRoom.current;
    if (videoJsNode && videoJsNode.id) {
      setTimeout(() => {
        let sources =
          Array.isArray(videoList) &&
          videoList.map((item) => {
            const {vodPathUrl} = item || {};
            if (!vodPathUrl) {
              return;
            }
            let vodByDAes = getDAesString(vodPathUrl, 'arrail-dentail&2', 'arrail-dentail&3');
            return {src: vodByDAes};
          });

        if (!videoJsNode.id) {
          return;
        }
        try {
          let TCPlayerObj = TCPlayer(videoJsNode, {
            sources: sources,
            licenseUrl: licenseUrl,
          });
          if (!TCPlayerObj) {
            return;
          }
          TCPlayerObj.ready(async () => {
            setVideoPlayer(TCPlayerObj);
            await setTimeout(() => {
            }, 1000);
            await setPlayerStateData({
              currentTime: TCPlayerObj.currentTime(),
              duration: TCPlayerObj.duration()
                ? TCPlayerObj.duration()
                : videoList[0].recordDuration,
              paused: TCPlayerObj && TCPlayerObj.paused && TCPlayerObj.paused(),
            });
          });

          TCPlayerObj.on('timeupdate', (value) => {
            let state = {
              currentTime: TCPlayerObj?.currentTime(),
              duration: TCPlayerObj?.duration(),
              paused: TCPlayerObj?.paused(),
            };
            handlePlayerStateChange(state);
            if (isNotLogin) {
              if (TCPlayerObj?.currentTime() >= 600) {
                setIsShowTrySeeTip(true);
                TCPlayerObj?.pause();
              } else {
                setIsShowTrySeeTip(false);
              }
            }
          });

          TCPlayerObj.on('error', (value) => {
          });

          TCPlayerObj.one('canplay', (value) => {
            setTimeout(async () => {
              if (!!playerInfo && TCPlayerObj) {
                await setTimeout(() => {
                }, 1000);
                await TCPlayerObj.currentTime(
                  playerInfo.currentTime ? playerInfo.currentTime : currentTimeByVideoSecond,
                );
                if (!!TCPlayerObj.paused()) {
                  await TCPlayerObj.pause();
                } else {
                  await autoPlay(TCPlayerObj);
                }
              } else {
                TCPlayerObj.currentTime(currentTimeByVideoSecond);
                await autoPlay(TCPlayerObj);
              }
              await props.changeUrlParams({lastTime: null});
            }, 100);
          });

          TCPlayerObj.on('volumechange', (value) => {
            if (TCPlayerObj.muted()) {
              TCPlayerObj.muted(false);
            }
          });

          TCPlayerObj.on('blocked', async (value) => {
            Modal.alert({
              content: '播放已被浏览器暂停,点击继续播放。',
              onConfirm: (e) => {
                TCPlayerObj.play();
              },
            });
          });
        } catch (error) {
          console.log(error);
        }
      }, 1000);
    }
  }, [videoList]);

  let onChangeBySlider = useDebounce((value) => {
    const {recordStartTime} = videoList[0] || {};
    videoPlayer?.currentTime(value);
    let currentTimeByDayJS = dayjs(recordStartTime, 'YYYY-MM-DD HH:mm:ss')
      .add(_.floor(value, 0), 'second')
      .format('YYYY-MM-DD HH:mm:ss');
    getSpaceBulletScreenByModel(currentTimeByDayJS);
  }, 100);

  return (
    <>
      <div
        className={classNames({
          [styles.video_warp_Box_silderWarp]: true,
        })}
      >
        <div
          className={classNames({
            [styles.video_warp_Box]: true,
          })}
        >
          <div
            // style={{ height: '100%' }}
            className={classNames({
              [styles.video]: true,
              [styles.video_warp]: !isHorizontalLive,
              [styles.video_warp_HorizontalLiveRoom]: !!isHorizontalLive,
            })}
          >
            <video
              ref={playerRefByHorizontalLiveRoom}
              id="player-container-id-HorizontalLiveRoom"
              // className="video-js vjs-default-skin"
              autoPlay={isNeedPwd == 0 ? true : false}
              style={{height: '100%'}}
              className={classNames(styles.videoJs, 'video-react-video')}
              playsInline={true}
              x5-video-player-type="h5-page"
              x5-video-player-fullscreen="true"
              poster={
                playerStateData && playerStateData.currentTime && playerStateData.currentTime != 0
                  ? null
                  : spaceCoverUrlShow
                    ? spaceCoverUrlShow
                    : 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/PlanetChatRoom_HorizontalLiveRoom_Live_bg.png'
              }
            />
          </div>
        </div>
        <div className={styles.video_ModeratorControl_VerticalLiveRoom}>
          {!!playerStateData?.paused && (
            <div
              className={styles.playBtn}
              onClick={() => {
                videoPlayer?.play();
              }}
            ></div>
          )}
          {!playerStateData?.paused && (
            <div
              className={styles.PauseBtn}
              onClick={() => {
                videoPlayer?.pause();
              }}
            ></div>
          )}
          <div className={styles.video_Progress_bar_warp}>
            <div className={styles.video_Progress_bar}>
              <Slider
                value={_.floor(playerStateData?.currentTime, 0)}
                tooltip={{open: false}}
                min={0}
                max={_.floor(playerStateData?.duration, 0)}
                onChange={onChangeBySlider}
              />
            </div>
            <div className={styles.time_Progress}>
              <span>
                {playerStateData?.currentTime
                  ? formatTime(_.floor(playerStateData?.currentTime))
                  : '00:00:00'}
              </span>
              <span>/</span>
              <span>
                {playerStateData?.duration
                  ? formatTime(_.floor(playerStateData?.duration))
                  : '00:00:00'}
              </span>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
export default connect(({ global, PlanetChatRoom, loading }: any) => ({
  global,
  PlanetChatRoom,
  loading,
}))(Index);
