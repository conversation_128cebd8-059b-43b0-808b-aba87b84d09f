/**
 * @Description: 创建视频指导提交成功弹窗
 */
import React, { useState } from 'react';
import { history, connect } from 'umi'
import { Spin } from 'antd'
import { Modal, Checkbox } from 'antd-mobile';
import styles from './index.less';
import { useDebounce } from "@/utils/utils";

import successIcon from '@/assets/GlobalImg/success.png' // 成功icon

interface PropsType {
  visible: boolean,                  // 弹窗是否显示
  onCancel?: () => void,           // 点击按钮的回调
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {
    visible = false,
    onCancel,
  } = props

  return (
    <Modal
      visible={visible}
      className={styles.modal}
      destroyOnClose={true}
      closeOnMaskClick
      onClose={onCancel}
      getContainer={() => document.body}
      content={
        <div>
          <div className={styles.header}>
            <img src={successIcon} width={24} height={24} style={{flexShrink: 0}} alt=""/>
            <div className={styles.title}>提交成功！</div>
          </div>

          <div className={styles.text}>1个工作日内将有专属客服与您联系。具体费用根据实际指导时长计算，将优先使用免费时长</div>

          {/* 按钮 */}
          <div className={styles.btn} onClick={onCancel}>我知道了</div>
        </div>
      }
    />
  )
}
export default connect(({ loading }: any) => ({ loading }))(Index)
