
import request from "@/utils/request";
import {getOperatingEnv} from "@/utils/utils";
import {stringify} from "qs";

/**
 /server/maMemberOrder/getOrderPlan
 获取订单方案
 * wxUserId   [string]  是  用户ID
 * memberType [string]  是  会员类型,1个人版 2企业版
 */
export async function getOrderPlan(params: {}, options?: { [key: string]: any }) {
  return request(`/api/server/maMemberOrder/getOrderPlan`, {
    method: 'GET',
    headers:{
      // 1：微信浏览器 2：移动端浏览器 3：PC浏览器
      // type: getOperatingEnv() == 2 || getOperatingEnv() == 3 || getOperatingEnv() == 4 ? 1 : '',
    },
    params: {
      ...params,
    },
  });
}


/**
 * /server/maMemberOrder/createOrder
 * 提交创建订单
 * orderOrganizationName [string]  是  开通会员填写的企业全称    展开
 * orderOrganizationTel  [string]  是  开通会员填写的联系电话    展开
 * planId   [number]  是  订单方案ID    展开
 * wxUserId [number]  是  当前用户ID
 */
export async function createOrder(params: {}, options?: { [key: string]: any }) {
  return request(`/api/server/maMemberOrder/createOrder`, {
    method: 'POST',
    headers:{
      // 1：微信浏览器 2：移动端浏览器 3：PC浏览器
      // type: getOperatingEnv() == 2 || getOperatingEnv() == 3 || getOperatingEnv() == 4? 1 : "",
    },
    data: {
      ...params,
    },
  });
}
/**
 * /server/maMemberOrder/confirmOrder
 * 订单确认页
 * */
export async function confirmOrder(params: {}, options?: { [key: string]: any }) {
  return request(`/api/server/maMemberOrder/confirmOrder?${stringify(params)}`, {
    method: 'GET',
    headers:{
      // 1：微信浏览器 2：移动端浏览器 3：PC浏览器
      // type: getOperatingEnv() == 2 || getOperatingEnv() == 3 || getOperatingEnv() == 4? 1 : "",
    },
    data: {
      ...params,
    },
  });
}


/**
 * /server/maMemberOrder/payOrder
 * 订单支付

 * orderId [number]  是  订单ID    展开
 * payType [number]  是  付款方式 1微信小程序 2微信H5 3微信二维码 4对公账户    展开
 * publicPayImgUrl [string]    对公账户方式必传，上传付款回执的图片路径
 * wxOpenid [string]    微信小程序-用户openid，微信小程序支付时必传
 * wxType [string]    场景类型，H5支付时必传，示例值：iOS, Android, Wap
 * wxUserId [number]  是  当前用户ID
 */
export async function  payOrder(params: {},options: {}) {
  return request(`/api/server/maMemberOrder/payOrder`, {
    method: 'POST',
    headers: {
      type: getOperatingEnv() == 1 ? "" : 1
    },
    data: {
      ...params,
    },
  });
}


/**
 * /server/wxMpAuth/getWxMpOpenId
 * 微信网页获取公众号下的用户openId
 * appId 公众号ID
 * code 微信授权code
 */
export async function getWxMpOpenId(params: {},options: {}) {
  console.log('getWxMpOpenId21312 :: ',params);
  return request(`/api/server/wxMpAuth/getWxMpOpenId`, {
    method: 'GET',
    headers: {type: getOperatingEnv() == 1 ? "" : 1},
    params: params,
  });
}

/**
 * /server/wxMpAuth/getJsapiTicket
 * 获取微信公众号下JSAPI权限config信息
 * */
export async function getJsapiTicket(params: {},options: {}) {
  return request(`/api/server/wxMpAuth/getJsapiTicket`, {
    method: 'GET',
    headers: { type: getOperatingEnv() == 1 ? "" : 1 },
    params: params,
  });
}

/*
* 订单是否支付，返回true，falseGET
/server/maMemberOrder/getOrderIsPay?orderId=
* */
export async function getOrderIsPay(params: {},options: {}) {
  return request(`/api/server/maMemberOrder/getOrderIsPay`, {
    method: 'GET',
    headers: { type: getOperatingEnv() == 1 ? "" : 1 },
    params: params,
  });
}

// 获取会员权益banner数据
export async function getDentalAssistantBanner(params: {},options: {}) {
  return request(`https://yapi.jwsmed.com/mock/216/FridayWeb/DentalAssistantBanner`, {
    method: 'GET',
  });
}

// 获取会员权益价值模块数据
export async function getDentalAssistantInterestList(params: {},options: {}) {
  return request(`https://yapi.jwsmed.com/mock/216/FridayWeb/DentalAssistantInterestList`, {
    method: 'GET',
  });
}

// 获取会员权益列表
export async function getMemberBenefitsList(params: {},options: {}) {
  return request(`https://yapi.jwsmed.com/mock/216/FridayWeb/newMemberBenefits`, {
    method: 'GET',
  });
}
