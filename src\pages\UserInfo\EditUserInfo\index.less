.Information_wrap{
  width: 100%;
  height: 100vh;
  background: #F5F6F8;
  position: relative;
  padding-top: 52px;
  box-sizing: border-box;
  overflow: hidden;

  .Information_info_box_chever {
    background: #FFFFFF;
    padding-left: 16px;
    box-sizing: border-box;

    .Information_info_content {
      padding: 20px 0;
      padding-right: 16px;
      border-bottom: 1px solid #E1E4E7;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;

      &:nth-last-child(1) {
        border-bottom: 0;
      }

      .Informatio_info_box_Item_Left {
        font-size: 16px;
        font-weight: 400;
        color: #000;
        line-height: 22px;
        flex-shrink: 0;
      }

      .Informatio_info_box_Item_Right {
        font-size: 16px;
        font-weight: 400;
        color: #666;
        line-height: 22px;
        display: flex;
        align-items: center;
        position: relative;

        .edit_head_picture {
          position: absolute;
          top: 0;
          left: 0;
          height: 46px;
          width: 46px;
          opacity: 0;

          :global {
            .ant-upload.ant-upload-select-picture-card {
              height: 46px;
              width: 46px;
              border-radius: 50%;
              display: block!important;
            }
          }
        }

        .item_HeadPicture {
          width: 46px;
          height: 46px;
          border-radius: 50%;
          background: url("../../../assets/GlobalImg/default_head_picture.png") no-repeat;
          background-size: cover;
          position: relative;

          img {
            width: 100%;
            height: 46px;
            border-radius: 50%;
          }
        }

        .edit_right_arrow {
          width: 16px;
          height: 16px;
          margin-left: 4px;

          img {
            width: 100%;
            height: auto;
            position: relative;
            top: -2px;
          }
        }
      }
    }

    .Information_info_content_edit {
      height: 62px;
      line-height: 62px;
      padding-right: 16px;
      border-bottom: 1px solid #E1E4E7;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &:nth-last-child(1) {
        border-bottom: 0;
      }

      :global {
        .ant-form-item {
          margin-bottom: 0;
        }
        .ant-input {
          text-align: right;
          font-size: 16px;
          font-weight: 400;
          color: #666666;
          padding: 4px 0;

          &::-webkit-input-placeholder{
            color: #ccc;
          }
        }
        .ant-form-item-explain-error {
          text-align: right;
        }
      }

      .Informatio_info_box_Item_Left {
        font-size: 16px;
        font-weight: 400;
        color: #000;
        line-height: 19px;
        flex-shrink: 0;
      }

      .Informatio_info_box_Item_Right {
        font-size: 16px;
        font-weight: 400;
        color: #666666;
        line-height: 19px;
        display: flex;
        height: 100%;
        align-items: center;

        .edit_right_arrow {
          width: 16px;
          height: 16px;
          margin-left: 4px;
        
          img {
            width: 100%;
            height: auto;
            position: relative;
            top: -2px;
          }
        }
      }
    }
  }

  .Informatio_btn_edit_account {
    width: calc(100% - 32px);
    text-align: center;
    margin-left: 16px;
    padding: 10px 0;
    background: #0095FF;
    border-radius: 20px;
    line-height: 19px;
    font-size: 16px;
    font-weight: 400;
    color: #FFFFFF;
    text-align: center;
    position: fixed;
    bottom: 47px;
    user-select: none;
  }

  .Informatio_edit_Wrap {
    width: 100%;
    padding: 0 16px;
    position: fixed;
    bottom: 47px;
    left: 0;
    user-select: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
  
    .Informatio_edit_cancel {
      width: 40%;
      height: 40px;
      flex-shrink: 0;
      background: #EDF9FF;
      border-radius: 20px;
      font-size: 16px;
      font-weight: 400;
      color: #0095FF;
      margin-right: 16px;
      text-align: center;
      border: none;
      letter-spacing: -2px;
    }
  
    .Informatio_edit_save {
      width: 60%;
      height: 40px;
      background: #0095FF;
      border-radius: 20px;
      font-size: 16px;
      font-weight: 400;
      color: #FFFFFF;
      text-align: center;
      border: none;
      letter-spacing: -2px;
    }
  }
}

@media screen and (max-height: 500px) {
  .Informatio_edit_Wrap {
    display: none;

    .Informatio_edit_cancel {
      display: none;
    }

    .Informatio_edit_save {
      display: none;
    }
  }
}