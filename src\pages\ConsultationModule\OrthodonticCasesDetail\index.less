.container {
  background: #EEF3F9;
  height: 100vh;
}

.wrap {
  width: 1228px;
  margin: 0 auto;
}

// 导航
.header_wrap {
  padding: 16px 0;
  .header {
    border-radius: 6px;
    height: 84px;
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;

    .header_title {
      font-size: 24px;
      font-weight: 600;
      color: #000000;

      .header_title_icon {
        width: 24px;
        height: 24px;
        margin-right: 16px;
        cursor: pointer;
      }
    }
  }
}


.content {
  display: flex;
  flex-wrap: nowrap;
  background: #FFFFFF;
  border-radius: 8px 8px 0px 0px;
  height: calc(100vh - 249px);
  overflow: auto;
  padding-top: 32px;
  &.content_in_iframe {
    height: calc(100vh - 132px);
  }

  .content_left {
    width: 178px;
    flex-shrink: 0;
    padding-left: 12px;

    :global {
      .ant-anchor-ink-ball {
        width: 1px;
        height: 24px;
        border-radius: 0;
        margin-top: -10.5px;
        border: 2px solid #0095FF;
      }

      .ant-anchor-link{
        padding: 5px 0 4px 16px;
      }

      .ant-anchor-link-active>.ant-anchor-link-title {
        color: #0095FF;
      }
    }

    .left {
      position: fixed;
      bottom: calc(100vh - 425px);

      .fast_nav {
        font-size: 16px;
        font-weight: 600;
        color: #000;
        line-height: 19px;
        margin-bottom: 10px;
      }

    }
  }

  .content_right {
    flex: 1;
    padding-right: 160px;
    .title {
      margin-bottom: 0;
      height: 45px;
      padding: 10px 0 9px 20px;
      background: #F8FAFD;
      border-radius: 3px 3px 3px 3px;
      font-size: 14px;
      font-weight: 500;
      color: #000;
      line-height: 26px;
    }
    .detail {
      padding-top: 24px;
      .detail_title {
        font-size: 14px;
        font-weight: 500;
        color: #000;
        line-height: 26px;
        margin-bottom: 8px;
      }
      .details_wrap {
        display: flex;
        flex-wrap: wrap;
        padding-left: 32px;
        padding-bottom: 20px;
        column-gap: 32px;
        row-gap: 8px;
        .detail_item {
          display: flex;
          flex-wrap: nowrap;
          &.detail_item_100 {
            width: 100%;
            margin-right: 0;
          }
          .label {
            font-size: 14px;
            color: #666;
            line-height: 26px;
            flex-shrink: 0;
            white-space: nowrap;
          }
          .value {
            flex: 1;
            font-size: 14px;
            color: #000;
            line-height: 26px;
            word-break: break-all;
          }
        }
      }
    }
    // 基本信息
    .basic_information {
      padding-bottom: 12px;
      .info_wrap {
        display: flex;
        flex-wrap: nowrap;
        .info_item {
          flex: 1;
        }
      }
      .info_item {
        display: flex;
        flex-wrap: nowrap;
        margin-bottom: 12px;
        .label {
          font-size: 14px;
          color: #666;
          line-height: 22px;
          width: 100px;
          flex-shrink: 0;
          text-align: right;
          white-space: nowrap;
        }
        .value {
          flex: 1;
          font-size: 14px;
          color: #000;
          line-height: 22px;
          word-break: break-all;
        }
      }
    }

    // 检查
    .inspect {
      .detail_item {
        .value {
          & > span::after {
            content: "、";
          }
          & > span:last-child::after {
            display: none;
          }
        }
      }
      .detail_item.detail_item_tooth {
        width: 100%;
        margin-right: 0;
        display: flex;
        flex-wrap: wrap;
        .detail_item_wrap {
          display: flex;
          flex-wrap: nowrap;
          align-items: center;
          margin-right: 40px;
          margin-bottom: 8px;
        }
      }
    }

    // 诊断分析 和 问题清单及诊断
    .diagnosis, .issues_list {
      .detail_item {
        .value {
          & > span::after {
            content: "；";
          }
          & > span:last-child::after {
            display: none;
          }
        }
      }
    }

    // 影像资料
    .image_data {
      padding-bottom: 24px;
      .detail_wrap_image {
        display: flex;
        flex-wrap: wrap;
        padding-bottom: 20px;
        column-gap: 32px;
        row-gap: 16px;
        .detail_item_image {
          .image_label {
            font-size: 14px;
            color: #666;
            line-height: 20px;
            margin-top: 8px;
            text-align: center;
            white-space: nowrap;
          }
        }
      }
      .detail_wrap_annex {
        padding: 8px 16px;
        background: #F8F8F8;
        border-radius: 3px;
        .detail_item_annex {
          display: flex;
          flex-wrap: nowrap;
          align-items: center;
          padding: 8px 0;
          & + .detail_item_annex {
            border-top: 1px solid #D9D9D9;
          }
          .annex_icon {
            flex-shrink: 0;
            margin-right: 16px;
          }
          .annex_btn {
            flex-shrink: 0;
            cursor: pointer;
            font-size: 14px;
            color: #0095FF;
          }
          .annex_content {
            flex: 1;
            .annex_name {
              font-size: 14px;
              color: #000;
              line-height: 20px;
            }
            .annex_size {
              font-size: 12px;
              color: #999;
              line-height: 16px;
            }
          }
        }
      }
    }

  }
}

.submitWarp {
  display: flex;
  justify-content: space-around;
  margin-top: 48px;
  padding-bottom: 53px;
  .submitBox {
    display: flex;
    .submit_btn_Cancel {
      width: 92px;
      height: 36px;
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      border: 1px solid #0095FF;
      font-size: 14px;
      font-weight: 400;
      color: #0095FF;
      line-height: 34px;
      user-select: none;
      cursor: pointer;
      text-align: center;
      margin-right: 30px;
    }
    .submit_btn_Cancel:active {
      opacity: 0.8;
    }
    .submit_btn_Enter {
      width: 92px;
      height: 36px;
      background: #0095FF;
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      font-size: 14px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 34px;
      text-align: center;
      user-select: none;
      cursor: pointer;
    }
    .submit_btn_Enter:active {
      opacity: 0.8;
    }
  }
}
