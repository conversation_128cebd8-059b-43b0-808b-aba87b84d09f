.modal_container {
  position: fixed;
  z-index: 999;
}
.mask {
  position: fixed;
  z-index: 899;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.3);
  animation-duration: 0.3s;
  animation-play-state: paused;
}
.modal_content {
  position: fixed;
  z-index: 999;
  bottom: 0;
  left: 0;
  width: 100%;
  padding-bottom: 64px;
  background: #fff;
  animation-duration: 0.3s;
  animation-play-state: paused;
  border-radius: 16px 16px 0 0;
  color: #333333;
  .title_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 56px;
    border-bottom: 1px solid #F5F5F5;
    padding: 0 12px 0 20px;
    .title {
      font-size: 18px;
      font-family: PingFang SC;
      font-weight: 500;
    }
    .icon {
      font-size: 20px;
      padding: 0 4px;
    }
  }
  .content_box {
    padding: 8px 16px 40px;
    :global {
      .ant-input[disabled] {
        color: rgba(0,0,0,0.85);
        background: #fff;
      }
    }
  }
  .input_box {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;

    .label {
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 500;
      width: 73px;
      min-width: 73px;
      white-space: nowrap;
      height: 54px;
      line-height: 54px;
      text-align: right;
      .remark {
        color: #EF441F;
      }
    }
    .value {
      flex: 1;
      :global {
        .ant-input {
          width: 100%;
          border: 0;
          border-radius: 0;
          border-bottom: 1px solid #F5F5F5;
          height: 54px;
          outline: 0;
          box-shadow: none;
        }
      }
    }
  }
  .btn_box {
    padding: 12px 16px;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    .btn {
      height: 40px;
      border-radius: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #FFFFFF;
      background: linear-gradient(270deg, #8966FF 0%, #6BA6FF 100%);
    }
  }
}
@keyframes show1 {
  0%{
    opacity: 0;
  }
  100%{
    opacity: 1;
  }
}
@keyframes hide1 {
  0%{
    opacity: 1;
  }
  100%{
    opacity: 0;
  }
}

@keyframes show2 {
  0%{
    transform: translateY(100%);
  }
  100%{
    transform: translateY(0%);
  }
}
@keyframes hide2 {
  0%{
    transform: translateY(0%);
  }
  100%{
    transform: translateY(100%);
  }
}

