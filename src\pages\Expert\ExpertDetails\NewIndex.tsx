/**
* @Description: 专家详情PC、H5入口
* @author: 赵斐
*/
import React, { useState, useEffect, lazy, Suspense } from 'react'
import { getOperatingEnv, useDebounce } from '@/utils/utils'
import { connect,KeepAlive } from 'umi';
import {history} from "@@/core/history";
//  PC 专家详情
//  import PcIndex from './PcIndex'
const PcIndex = lazy(() => import('./PcIndex'))
//  H5 专家详情
//  import MobileIndex from './index'
const MobileIndex = lazy(() => import('./index'))
const Index: React.FC = () => {

  const { query } =  history.location || {}
  const { id } = query || {}   // 专家（用户）ID

  // 1 PC，2 移动端
  const [pageType, setPageType] = useState(null)
  const UerInfo = JSON.parse(localStorage.getItem('userInfo')) || {};

  // ① 判定当前页面视口是否小于750 如果小于750则为移动端
  let updateType = () => {
    // 获取当前操作环境 1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用
    const env = getOperatingEnv()
    const type = env == '4' ? 1 : 2
    setPageType(type)
  }
  updateType = useDebounce(updateType, 100)

  useEffect(() => {
    // ① 判定当前页面视口是否小于750 如果小于750则为移动端
    updateType()
    window.addEventListener('resize', updateType, {passive: true});
    return () => {
      window.removeEventListener('resize', updateType)
    }
  }, [])

  return (
    <Suspense fallback={<div></div>}>
      {
        pageType == 1 ? <PcIndex />
          : pageType == 2 ?
            <div>
              <KeepAlive
                name="ExpertDetails"
                saveScrollPosition="screen"        //自动保存共享屏幕容器的滚动位置
                id={
                  id                                  // 优先根据页面中传入的专家id建立缓存
                  || UerInfo?.friUserId               // 根据userId建立缓存
                }  // 根据参数去缓存，如果参数不同就缓存多份，如果参数相同就使用同一个缓存。这样解决了传参改变时，页面不刷新的问题
                when={() => {  // 根据路由的前进和后退状态去判断页面是否需要缓存，前进时缓存，后退时不缓存（卸载）。 when中的代码是在页面离开（卸载）时触发的。
                  // true卸载时缓存，false卸载时不缓存
                  // 当前我的列表点击去编辑会议或者空间则缓存当前列表
                  return (
                    history.action == 'PUSH'
                    && (
                      (history.location.pathname.indexOf('/CreateSpace') > -1)
                      || (history.location.pathname.indexOf('/PlanetChatRoom') > -1)
                      || (history.location.pathname.indexOf('/Poster') > -1)
                      || (history.location.pathname.indexOf('/Case/CaseDetails') > -1)
                      || (history.location.pathname.indexOf('/CreateGraphicsText/PostDetails') > -1)
                      || (history.location.pathname.indexOf('/CreateGraphicsText/ArticleDetails') > -1)
                      || (history.location.pathname.indexOf('/CreateGraphicsText/ExternalLinksDetails') > -1)
                      || (history.location.pathname.indexOf('/CreateGraphicsText/CreatePost') > -1)
                      || (history.location.pathname.indexOf('/CreateGraphicsText/CreateArticle') > -1)
                      || (history.location.pathname.indexOf('/CreateGraphicsText/CreateExternalLinks') > -1)
                      || (history.location.pathname.indexOf('/CreateGraphicsText/CreateForward') > -1)
                    )
                  );
                }}
              >
                <MobileIndex />
              </KeepAlive>
            </div>
          : null
      }
    </Suspense>
  )
}

export default Index
