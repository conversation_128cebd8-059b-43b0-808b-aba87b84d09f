/**
 * 预约订阅公众号弹窗
 */

/**
 * @Description: 预约空间卡片
 */
import React, {useEffect, useState} from 'react';
import { history, connect } from 'umi'
import classNames from 'classnames'
import {getOperatingEnv, goToHomePage, WxAppIdByPublicAccount} from '@/utils/utils'
import {Button, message, Modal, Spin} from 'antd';
import styles from './ModalWXsubscribe.less'
import {getJsapiTicket} from "@/services/userInfo";
import {Toast} from "antd-mobile";
import {getCallback, getSubscribeUrl, getTemplateList} from "@/services/planetChatRoom";

interface PropsType {
  open: any,
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {
    dispatch,
    open,
    spaceInfo,
    clearParams,
  } = props
  const pathname = history.location.pathname

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [template, setTemplate] = useState(null); // 微信订阅私有模板列表
  const [loading, setLoading] = useState(null); // 微信订阅私有模板列表loading

  useEffect(()=>{
    if(open) {
      showModal();
    }
  },[open])

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    setIsModalOpen(false);
    clearParams && clearParams();
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    clearParams && clearParams();
  };

  // 点击订阅公众号
  const onClickByWXsubscribe = () => {
    const {
      id, // 空间id
      wxUserId, // 用户id
    } = spaceInfo || {};
  }

  useEffect(async ()=>{
    // 微信config配置
    if(!!isModalOpen) {
      await getTemplateListFunc();
      await wxConfig();
    }
  },[isModalOpen])

  // 获取预约空间订阅公众号弹窗中的 getTemplateList获取私有模板列表
  const getTemplateListFunc = async()=> {
    const {
      id, // 空间id
      wxUserId, // 用户id
    } = spaceInfo || {};
    await setLoading(true);
    let resByGetTemplateList = await getTemplateList({
      wxUserId:wxUserId,
      spaceId:id,
    })
    await setLoading(false);
    const { code,content } = resByGetTemplateList || {}
    if (code == 200) {
      let templates = ['TenvU22BA1jCp4YHfYEpRuESXYReQyDuhs4vbdWA99I']
      let templateStr = templates.join(',')
      setTemplate(templateStr)
    }
  }

  const getSubscribeUrlFunc = async ()=>{
    const {
      id, // 空间id
      wxUserId, // 用户id
    } = spaceInfo || {};
    let  resBygetSubscribeUrl = await getSubscribeUrl({
      spaceId:id,
    });
    const { code,content } = resBygetSubscribeUrl || {}
    if (code == 200 && content) {
      window.location.href = content
    }else {
      message.warning('获取订阅地址失败!')
    }
  }


  // 微信config配置
  const wxConfig = async()=> {
    const jsapiTicketContent = await getJsapiTicket({
      appId: WxAppIdByPublicAccount,
      currentUrl: window.location.href.split('#')[0],
    })
    const {code, content} = jsapiTicketContent || {}
    if (code == 200) {
      wx.config({
        debug: false,
        appId: content.appId,
        timestamp: content.timestamp,
        nonceStr: content.nonceStr,
        signature: content.signature,
        jsApiList: [],
        openTagList:['wx-open-subscribe'],
      })
      wx.ready(() => {
        let subscribeBtn = document.getElementById('subscribe-btn');
        subscribeBtn?.addEventListener('success', function (e) {
          console.log('subscribe-btn-success', e);
          /**
           * [TEMPLATE_ID]是动态的键，即模版id，值包括：
           * 'accept'、'reject'、'cancel'、'filter'
           * 表示用户同意订阅该条id对应的模版消息，
           * 'reject'表示用户拒绝订阅该条id对应的模版消息，
           * 'cancel'表示用户取消订阅该条id对应的模版消息，
           * 'filter'表示该模版应该标题同名被后台过滤。
           * 例如：{ errMsg: "subscribe:ok", subscribeDetails:
           * "{"TenvU22BA1jCp4YHfYEpRuESXYReQyDuhs4vbdWA99I":"{\"status\":\"accept\"}"}" +
           *   ""表示用户同意订阅TenvU22BA1jCp4YHfYEpRuESXYReQyDuhs4vbdWA99I这条消息。
           *   {"status":"accept"}"
           */
          let jsonBysubscribeDetails = e.detail && e.detail.subscribeDetails ? JSON.parse(e.detail.subscribeDetails) : {};
          console.log('subscribe-btn-subscribeDetails :: ',jsonBysubscribeDetails);
          message.success('订阅公众号通知完成!')
          // handleCancel();
        });
        subscribeBtn?.addEventListener('error',function (e) {
          message.error('订阅公众号通知失败!')
          console.log('subscribe-btn-fail', e.detail);
          // handleCancel();
        });
      })
    }else {
      Toast.show(jsapiTicketContent && jsapiTicketContent.msg ?  jsapiTicketContent.msg : '获取微信配置失败!')
    }
  }




  return (
    <div className={styles.warp_ModalWXsubscribe}>
      <Modal
        title={false}
        footer={false}
        className={styles.Modal_ModalWXsubscribe}
        style={{
          width:'85vw'
        }}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <Spin spinning={!!loading}>
          <div>
            <div className={styles.ModalWXsubscribeContent}>
              <div onClick={()=>{
                getSubscribeUrlFunc();
              }} className={styles.ModalWXsubscribe_Icon}></div>
              <div className={styles.ModalWXsubscribe_title}>
                预约成功
              </div>
              {getOperatingEnv() == 2 &&
                <div className={styles.ModalWXsubscribe_title_desc}>
                  订阅通知，课程开始前我们会给您发送消息通知
                </div>
              }
            </div>

            {/*<div className={styles.btn_ModalWXsubscribe_warp}>
              <wx-open-subscribe template={template} id="subscribe-btn">
                <script type="text/wxtag-template">
                  <button id={'subscribe-btn'} style={{
                    width: '160px',
                    height: '33px',
                    background: "#0095FF",
                    borderRadius: "20px 20px 20px 20px",
                    fontWeight: "400",
                    fontSize: "15px",
                    color: "#FFFFFF",
                    lineHeight: "30px",
                    textAlign: "center",
                    fontStyle: "normal",
                    border: "0px",
                  }}>
                    订阅公众号通知
                  </button>
                </script>
              </wx-open-subscribe>
            </div>*/}

            {getOperatingEnv() == 2 &&
              <div className={styles.btn_ModalWXsubscribe_warp}>
                <button
                  onClick={()=>{ getSubscribeUrlFunc(); }}
                  style={{
                  width: '160px',
                  height: '33px',
                  background: "#0095FF",
                  borderRadius: "20px 20px 20px 20px",
                  fontWeight: "400",
                  fontSize: "15px",
                  color: "#FFFFFF",
                  lineHeight: "30px",
                  textAlign: "center",
                  fontStyle: "normal",
                  border: "0px",
                }}>
                  订阅公众号通知
                </button>
              </div>
            }
          </div>
        </Spin>
      </Modal>
    </div>
  )
}

export default connect(({ activity, loading }: any) => ({activity, loading}))(Index)
