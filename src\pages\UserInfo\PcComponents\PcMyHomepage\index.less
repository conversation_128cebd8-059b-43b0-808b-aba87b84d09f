.content {
  width: 100%;
  height: calc(100vh - 310px);
  overflow: hidden;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}
.tab_wrap {
  width: 100%;
  height: 43px;
  background: #FFFFFF;
  border-radius: 8px;
  padding: 16px 20px 0;
  margin-bottom: 12px;
  display: flex;

  .tab_init {
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    line-height: 16px;
    margin-right: 22px;
    height: 27px;
    cursor: pointer;
    display: flex;

    &.tab_active {
      font-size: 14px;
      font-weight: 600;
      color: #000000;
      line-height: 16px;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 12px;
        height: 3px;
        background: #000000;
        border-radius: 6px 6px 6px 6px;
      }
    }
  }

  .lines {
    width: 1px;
    height: 16px;
    background: #D9D9D9;
    margin-right: 22px;
  }
}

.tab_content {
  width: 100%;
  height: 100%;

  :global {
    .ant-spin-container {
      width: 100%;
      height: 100%;
    }
  }
}

.noDataStyle {
  width: 100%!important;
  justify-content: inherit;
}

// div::-webkit-scrollbar {
//   width: 4px;
// }
// div::-webkit-scrollbar-thumb {
//   border-radius: 10px;
//   -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
//   opacity: 0.2;
//   // background: ;
// }
// div::-webkit-scrollbar-track {
//   -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
//   border-radius: 0;
//   // background: fade(@primary-color, 30%);
// }
