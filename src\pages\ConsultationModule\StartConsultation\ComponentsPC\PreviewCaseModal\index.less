.modal {
  :global {
    .ant-modal-header {
      border-bottom: 0;
    }
    .ant-modal-body {
      padding: 24px 0 58px;
    }
  }
}
.preview_img_wrap {
  width: 788px;
  height: auto;
  margin: 0 auto;
  position: relative;
  :global {
    .ant-carousel .slick-slider {
      position: relative;
    }
    .ant-carousel .slick-dots-bottom {
      bottom: -30px;
    }
    .ant-carousel .slick-dots li {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background: rgba(0,0,0,0.2);
    }
    .ant-carousel .slick-dots li button {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background: rgba(0,0,0,0.2);
    }
    .ant-carousel .slick-dots li.slick-active button {
      background: #0095FF;
    }
  }
  .arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    cursor: pointer;
  }
  .arrow_left {
    left: -62px;
  }
  .arrow_right {
    right: -62px;
  }
}
