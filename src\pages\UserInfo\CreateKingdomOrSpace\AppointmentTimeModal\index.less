
.titleInfo {
  width: 100%;
  margin-bottom: 26px;

  .titleWarp {
    display: flex;
    align-items: center;
    padding: 0 16px;
    box-sizing: border-box;
    justify-content: center;
    position: relative;

    .titleBackIcon {
      position: absolute;
      top: 0;
      left: 16px;

      img {
        width: 12px;
        height: auto;
      }
    }

    .titleText {
      font-size: 17px;
      font-weight: 500;
      color: #000000;
      line-height: 24px;
    }
  }
}

.data_content_box {
  font-size: 20px;
  font-weight: 500;
  color: #000000;
  line-height: 28px;
  padding-left: 16px;
  box-sizing: border-box;

  span {
    color: #0095FF;
    margin-right: 4px;
  }
}

.PickerViewDataByWarp {
  padding: 16px 16px;
  height: 250px;
  box-sizing: border-box;
  overflow: hidden;

  :global {
    .adm-picker-view {
      justify-content: center;
    }
    .adm-picker-view-column:nth-child(1){
      width: 220px;
      flex: none;
    }
    .adm-picker-view-column:nth-child(2){
      width: 45px;
      flex: none;
    }
    .adm-picker-view-column:nth-child(3){
      width: 45px;
      flex: none;
    }
  }
}

.btnAppointmentStartWarp {
  padding: 16px;

  .btnAppointmentStart {
    width: 100%;
    height: 40px;
    text-align: center;
    background: #0095FF;
    border-radius: 20px;
    font-size: 16px;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 40px;
  }
}