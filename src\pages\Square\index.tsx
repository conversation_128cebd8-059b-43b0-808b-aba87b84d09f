/**
 * 广场主页
 */
import React, {  useRef, useState } from 'react'
import { history,connect, KeepAlive } from 'umi'
import classNames from 'classnames'
import { getOperatingEnv, backInApp, getIsFirstPageInApp, goToHomePage } from '@/utils/utils'
import { message } from 'antd'
import styles from './index.less'

// 图片icon
import aircraft from '@/assets/GlobalImg/aircraft.png' // 新建按钮icon

import Tabbar from '@/components/Tabbar' // tabbar底部内容
import CollectList from './Components/CollectList' // 收藏列表
import KingdomList from './Components/KingdomList' // 王国列表
import MeetingList from './Components/MeetingList' // 会议列表
import RecommendList from './Components/RecommendList' // 推荐列表
import MobileIndex from '@/pages/Home/MobileIndex' // 直播tab
import CreateKingdomOrSpace from '../UserInfo/CreateKingdomOrSpace'; // 创建空间/王国弹框组件

// tab列表数据
const tabList = [
  // {id: 1, text: '首页'},
  {id: 2, text: '广场'},
  // {id: 3, text: '帖子'},
  {id: 4, text: '直播'},
  {id: 5, text: '会议'},
  {id: 6, text: '王国'},
]

const Index: React.FC = (props: any) => {
  const env = getOperatingEnv() // 4 PC，5 FRIDAY APP，6 佳沃思APP
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  const { pathname, query } = history.location;
  const {
    meetingSpace,
    openid,       // 用户openid
    template_id,  // 模板id
    action,       // 1_1 请求带入原样返回
    scene,        //  } = query
    reserved,     // 1.2 请求带入原样返回
  } = query
  const { dispatch } = props

  const [tabKey, setTabKey] = useState(meetingSpace === null ? 5 : (query.tabKey || 2)); // tab页签表示，2 广场，4 直播，5 会议，6 王国

  const containerRef = useRef<HTMLDivElement|null>(null); // 外层div的ref

  // 点击首页tab
  const onClickHomeTab = () => {
    goToHomePage(dispatch, 'replace')
  }

  // tab切换事件
  const onClickTab = (value: any) => {
    if (containerRef?.current) {
      containerRef.current.scrollTop = 0
    }
    // 更新地址栏参数
    history.replace({
      pathname,
      query: {
        ...query,
        ...(meetingSpace == null ? {meetingSpace: ''} : {}), // 保留原参数
        tabKey: value, // tab页签表示，2 广场，4 直播，5 会议，6 王国
      }
    })
    // 保存tab值
    setTabKey(value)
  }

  // 点击搜索icon
  const onClickSearchIcon = () => {
    history.push('/Square/Search')
    // ios键盘聚焦
    document.getElementById('input_ios_focus') && document.getElementById('input_ios_focus').focus()
  }

  // 点击新建按钮
  const onClickCreateBtn = async () => {
    await dispatch({ type: 'userInfoStore/clean' });
    // 没登录跳转登录页
    if (!localStorage.getItem('access_token')) {
      history.push({
        pathname: '/User/login',
        query: {
          redirectByPush: window.location.href,
        }
      })
      return
    }
    // 是否是超级账号
    await checkSuperAccount();
    // 打开创建弹窗
    await dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        createModalVisible: true
      }
    });
  }

  // 是否为超级账号
  const checkSuperAccount = () => {
    dispatch({
      type: 'userInfoStore/checkSuperAccount',
    }).then(res => {
      const { code, content, msg } = res
      if(code == 200) {
        dispatch({
          type: 'userInfoStore/setTaskListState',
          payload: {
           isSuperAccount: content,
          }
        })
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch(err => {
      console.log(err)
    })
  }

  return (
    <>
      {/* 顶部导航栏 */}
      <div className={classNames(styles.fixed_nav_bar, {
        [styles.pc]: env == '4', // PC环境
        [styles.recommend]: tabKey == 2, // 推荐tab
      })}>
        <div className={classNames(styles.left, {
          [styles.in_jws]: env == '6', // 佳沃思APP环境
        })}>
          {/* app中返回按钮 */}
          {
            getIsFirstPageInApp() && <i className={styles.back_icon} onClick={backInApp}></i>
          }

          {/* tab页签 */}
          <div className={styles.tabs_item} onClick={onClickHomeTab}>首页</div>
          {
            tabList.map((item, ind) => {
              return (
                <div
                  key={ind}
                  className={classNames(styles.tabs_item, {
                    [styles.checked]: tabKey == item.id,
                  })}
                  onClick={() => onClickTab(item.id)}
                >
                  {item.text}
                </div>
              )
            })
          }
        </div>
        {/* 右侧搜索icon */}
        <div className={styles.right}>
          <i className={styles.icon_search} onClick={onClickSearchIcon}></i>
        </div>
      </div>

      {/* 新建按钮 */}
      <div className={styles.add_btn} onClick={onClickCreateBtn}>
        <img src={aircraft} width={28} height={28} alt=""/>
      </div>

      <KeepAlive
        name={'Square'}
        saveScrollPosition="screen" //自动保存滚动位置
        id={
          UserInfo?.friUserId ||
          history.location.pathname }  // 根据参数去缓存，如果参数不同就缓存多份，如果参数相同就使用同一个缓存。这样解决了传参改变时，页面不刷新的问题
        when={() => {  // 缓存条件
          return (
            (
              tabKey == 2 ||
              tabKey == 4 ||
              tabKey == 5
            )
            && history.action == 'PUSH'
            && !(history.location.pathname.indexOf('/CreateGraphicsText/CreatePost') > -1) // 进入发布帖子不缓存
            && !(history.location.pathname.indexOf('/CreateGraphicsText/CreateArticle') > -1) // 进入发布文章不缓存
            && !(history.location.pathname.indexOf('/CreateGraphicsText/CreateExternalLinks') > -1) // 进入发布外链不缓存
            && !(history.location.pathname.indexOf('/CreateSpace') > -1) // 进入创建直播或会议不缓存
          );
        }}
      >
        <div className={classNames(styles.container, {
          [styles.collect_container]: tabKey == 1,
          [styles.recommend_container]: tabKey == 2,
          [styles.space_container]: tabKey == 4||tabKey == 5,
          [styles.kingdom_container]: tabKey == 6,
          [styles.in_app_container]: env == '5',
        })}
        ref={containerRef}
        >
          <div className={styles.content}>
            {/*灰条*/
              tabKey != 5 && tabKey != 1 &&
              <div className={styles.gray_bar}></div>
            }

            {tabKey == 1 ? <CollectList/> : null}
            {tabKey == 2 ? <RecommendList onClickCreateBtn={onClickCreateBtn} /> : null}
            {tabKey == 4 ? <MobileIndex pageSource="square"/> : null}
            {tabKey == 5 ? <MeetingList/> : null}
            {tabKey == 6 ? <KingdomList /> : null}
          </div>

          {
            env == '5' ? null : <Tabbar/>
          }
        </div>

        {/* 创建弹窗组件 */}
        <CreateKingdomOrSpace />
      </KeepAlive>
    </>
  )
}

export default connect(({ square, loading, userInfoStore }: any) => ({ square, loading, userInfoStore }))(Index)
