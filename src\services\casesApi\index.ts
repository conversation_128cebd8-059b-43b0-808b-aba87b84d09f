
import request from "@/utils/request";
import {stringify} from "qs";
/**
 * 分页获取优秀病历数据     张志军
 * @param {Array}  achievementList        病历成就,多选
 * @param {Array}  depSubjectDictList     科室字典,多选
 * @param {number} difficultLevelDictList 难度等级
 * @param {string} endDate                结束日期
 * @param {string} startDate              开始日期
 * @param {number} pageNum                页码
 * @param {number} pageSize               条数
 * @param {string} searchKey              检索词
 * @param {string} vxOpenIdCipherText     openId
 * @returns
 */
export const getExcellentCaseList = (data:any) =>
  request(`/api/server/h5ExcellentCase/getExcellentCaseList`,{
    method:'POST',
    data
});
/**
 * 优秀病历 - 根据微信openID获取搜索关键字（查询指定15行数据,最近3个月）    张志军
 * @returns
 */

export const getCasesWordList = (data:any) =>
  request(`/api/server/h5ExcellentCase/getWordList`,{
    method:'GET',
    data
});

/**
 * 优秀病历 - 获取字典项数据    张志军
 * @returns
 */

export const getExcellentCaseDict = (data:any) =>
  request(`/api/server/h5ExcellentCase/getExcellentCaseDict`,{
    method:'GET',
    data
});

/**
 * 优秀病历 - 获取优秀病历信息    张志军
 * @param {String}  wxUserId            当前登录用户ID，没有登录不用传
 * @param {String}  excellentCaseId     优秀病历ID
 * @returns
 */
export const getExcellentCaseInfo = (data:any) =>
  request(`/api/server/h5ExcellentCase/getExcellentCaseInfo?${stringify(data)}`,{
    method:'GET',
    data
});

/**
 * 优秀病历 - 通过病历ID获取评论    张志军
 * @param {String}  wxUserId            当前登录用户ID，没有登录不用传
 * @param {String}  excellentCaseId     优秀病历ID
 * @returns
 */
export const getMaExcellentCaseCommentsList = (data:any) =>
  request(`/api/server/h5ExcellentCase/getMaExcellentCaseCommentsList?${stringify(data)}`,{
    method:'GET',
    data
});
/**
 * 收藏、取消收藏优秀病历     张志军
 * @param {string} excellentCaseId        病历案列ID
 * @param {string} isCollect              0取消收藏 1收藏
 * @param {string} wxUserId               微信登录用户ID
 * @returns
 */
export const isCollect = (data:any) =>
  request(`/api/server/h5ExcellentCase//isCollect`,{
    method:'POST',
    data
});
/**
 * 小程序评论或回复     张志军
 * @param {string} excellentCaseId        病历案列ID
 * @param {string} excellentCaseType      病历类型 0病历评论
 * @param {string} commentsContent        评论/回复内容
 * @param {string} commentsUserId         评论/回复用户ID
 * @param {string} commentsSuperId        评论/回复上级ID
 * @param {string} commentsSuperUserId    回复上级用户ID
 * @param {string} commentsType           评论类型 0评论 1回复
 * @returns
 */
export const saveCaseComments = (data:any) =>
  request(`/api/server/h5ExcellentCase/saveCaseComments`,{
    method:'POST',
    data
});
/**
 * 图片/文本模式切换    张志军
 * @param {string} isShow               1图片模式 2文本模式
 * @param {string} vxOpenIdCipherText   openId密文
 * @param {string} wxUserId             微信登录用户ID
 * @returns
 */
export const imgShowOrHide = (data:any)=>
  request(`/api/server/h5ExcellentCase/imgShowOrHide`,{
    method:'POST',
    data
});

/**
 * 优秀病历 - 获取优秀病历信息    张志军
 * @param {String}  wxUserId            当前登录绑定医生星球用户ID
 * @param {String}  excellentCaseId     优秀病历ID
 * @returns
 */
export const PcGetExcellentCaseInfo = (data:any) =>
  request(`/api/web-server/webExcellentCase/getExcellentCaseInfo?${stringify(data)}`,{
    method:'GET',
    data
});