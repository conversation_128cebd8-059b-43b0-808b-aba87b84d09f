import React, { useEffect, useState, useRef } from 'react'
import { connect, history } from 'umi'
import classNames from 'classnames'
import { Helmet } from 'react-helmet'
import DanmuJs from 'danmu.js'
import { userTokenInvalid } from '@/utils/request'
import {getIsIniPhoneAndWeixin, gdpFormat, getOperatingEnv} from '@/utils/utils'
import { Spin, message } from 'antd'
import { Toast } from 'antd-mobile'
import styles from './index.less'
import gdp_icon from '@/assets/GlobalImg/gdp.png' // gpd
import NoDataRender from '@/components/NoDataRender'
import NavBar from '@/components/NavBar'
import CommentList from '@/pages/CreateGraphicsText/ComponentsH5/CommentList'
import CommentFooter from '@/pages/CreateGraphicsText/ComponentsH5/CommentFooter'
import KingdomCard from '@/pages/CreateGraphicsText/ComponentsH5/KingdomCard'
import UserCardByImageText from '@/components/UserCardByImageText'
import UserCardByNavBar from '@/pages/CreateGraphicsText/ComponentsH5/UserCardByNavBar'
import CoverImageOrVideo from '@/components/SpaceList/CoverImageOrVideo'
import { saveCommentsOrReply } from '@/services/recommended' // 提交评论方法
import space_status_icon1 from "@/assets/GlobalImg/space1.png"
import space_status_icon2 from "@/assets/GlobalImg/space2.png"

const Index: React.FC = (props: any) => {
  const { id } = history.location.query;         // 图文ID
  const { dispatch } = props;
  const refContainer = useRef(null)
  const refUserCard = useRef(null)
  const commentListRef = useRef(null);
  const [detailsState, setDetailsState] = useState<any>({})                  // 详情数据
  const [navBarToggle, setNavBarToggle] = useState(false)                      // 导航状态
  const [loadingImgTextInfoById, setLoadingImgTextInfoById] = useState(false)  // loading
  const [pageLoadStatus, setPageLoadStatus] = useState(null)                   // 页面状态

  let {
    imageTitle,                        // 标题
    kingdomInfo,                       // 关联王国信息
    createUserId,                      // 创建人id
    userName,                          // 用户名称
    headUrlShow,                       // 头像
    isExperts,                         // 是否是专家
    operateDateDescs,                  // 创建时间
    gdp,                               // GDP
    isFocus,                           // 是否关注
    expertsInfo,                       // 专家信息
    status,                            // 状态
    textImgList,                       // 封面图
    spaceId,                           // 空间ID
    starSpaceType,                     // 空间类型
    isTemplateCover,                     // 是否是封面
    spaceStatus, // 空间状态
    space, // 空间信息
  } = detailsState;
  space = space || {}

  // 滚动事件
  useEffect(() => {
    document.getElementById('container') && document.getElementById('container').addEventListener('scroll', handleScroll)

    return () => {
      document.getElementById('container') && document.getElementById('container').removeEventListener('scroll', handleScroll)
    }
  }, [])

  useEffect(() => {
    imgTextInfoById()
  }, [])

  useEffect(() => {
    if (detailsState.id) {
      initializationByDanmu()
    }

  }, [detailsState])

  // 初始化弹幕
  const initializationByDanmu = () => {
    if (space.msgList && space.msgList.length > 0) {
      new DanmuJs({
        channelSize: 28,             // 轨道大小
        container: document.getElementById(`vs${detailsState.id}`), // 弹幕容器
        player: document.getElementById(`ms${detailsState.id}`),    // 播放器容器
        area: {
          start: 0,                  // 区域顶部到播放器顶部所占播放器高度的比例
          end: 1,                    // 区域底部到播放器顶部所占播放器高度的比例
        },
        mouseControl: false,         // 打开鼠标控制
        mouseControlPause: false,    // 鼠标触摸暂停
        chaseEffect: true,           // 开启滚动弹幕追逐效果
        comments: space.msgList && space.msgList.length > 0 ? space.msgList.map((item, index) => ({
          moveV: 100,
          id: index,
          txt: item,
          style: {
            color: '#FFFFFF',
            fontSize: '11px',
            borderRadius: '20px',
            padding: '0 6px',
            margin: '8px 0',
            height: '19px',
            lingHeight: '19px',
            backgroundColor: 'rgba(0,0,0,0.2)',
          },
        })) : []
      })
    }
  }

  // 滚动事件回调
  const handleScroll = () => {
    const scrollTop = refContainer.current.scrollTop + 44
    const offset = refUserCard.current.offsetHeight + refUserCard.current.offsetTop
    if (scrollTop >= offset) {
      setNavBarToggle(true)
    } else {
      setNavBarToggle(false)
    }
  }

  // 编辑图文获取原数据
  const imgTextInfoById = (isLocalUpdate = false) => {
    if (!isLocalUpdate) {
      setLoadingImgTextInfoById(true)
    }
    dispatch({
      type: 'graphicsText/imgTextInfoById',
      payload: {
        imageTextId: id,                         // 图文ID
      }
    }).then(res => {
      setLoadingImgTextInfoById(false)
      const { code, content, msg } = res
      if (code == 200 && content) {
        setDetailsState(content)
        setPageLoadStatus(1)
      } else {
        setPageLoadStatus(2)
        Toast.show(msg || '数据加载失败')
      }
    }).catch(err => {
      setPageLoadStatus(2)
    })
  }

  // 关注或取消关注回调
  const handleFollowAndCheck = () => {
    imgTextInfoById(true)
  }

  // 提交评论
  const onPost = async ({ commentInputInfo,  value}) => {
    if(commentInputInfo && value) {
      let params = {
        imageTextId: commentInputInfo.id,                     //  number 非必须  上级图文主键ID
        imageTextType: commentInputInfo.imageType,            //	number 非必须  上级图文类型
        commentsContent: value,                               //	string 非必须  上级评论/回复内容
        commentsType: 0,                                      // 评论类型
      }
      const data = await saveCommentsOrReply(params);
      const { code, content } = data || {}
      if (code == 200) {
        message.success('评论成功!')
        commentListRef?.current?.commentsList();
      }else {
        message.error('评论失败!')
      }
    }
  }

  // 点击空间封面
  const onClickCoverImg = (e) => {
    e.stopPropagation()
    e.preventDefault()
    if (starSpaceType == 2) {
      history.push(`/PlanetChatRoom/Meet/${spaceId}`)
    } else {
      history.push(`/PlanetChatRoom/Live/${spaceId}`)
    }
  }

  // 更新评论数据
  const updateCommentFooter = (commentsCount) => {
    setDetailsState(prevState => {
      return {
        ...prevState,
        commentsCount
      }
    })
  }

  return (
    <Spin spinning={loadingImgTextInfoById} wrapperClassName={styles.spin}>
      <Helmet>
        <title>{starSpaceType == 1 ? '直播详情' : starSpaceType == 2 ? '会议详情' : ''}</title>
        <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, shrink-to-fit=no" />
      </Helmet>
      {
        navBarToggle ?
          <UserCardByNavBar
            headUrlShow={headUrlShow}
            userName={userName}
            createUserId={createUserId}
            isExperts={isExperts}
            operateDateDescs={operateDateDescs}
            isFocus={isFocus}
            expertsInfo={expertsInfo}
            handleFollowAndCheck={handleFollowAndCheck}
            id={id}
            imageType={4}
            status={status}
          />
          :
          <NavBar
            title={starSpaceType == 1 ? '直播详情' : starSpaceType == 2 ? '会议详情' : ''}
            bordered
            // RightRender={
            //   () => UserInfo.friUserId == createUserId ? <MoreOperate id={id} imageType={1} status={status} handleDeleteOrLow={goBack}/>
            //     : null
            // }
          />
      }
      <div id="container" className={styles.container} ref={refContainer} style={getIsIniPhoneAndWeixin() ? { paddingBottom: '86px' } : {}}>
        {
          pageLoadStatus == 1 ?
            <>
              <div ref={refUserCard} className={styles.user_card_wrap}>
                <UserCardByImageText
                  headUrlShow={headUrlShow}
                  userName={userName}
                  createUserId={createUserId}
                  isExperts={isExperts}
                  operateDateDescs={operateDateDescs}
                  isFocus={isFocus}
                  expertsInfo={expertsInfo}
                  handleFollowAndCheck={handleFollowAndCheck}
                  isShowMoreOperate={false}
                />
              </div>
              <div className={styles.image_text_content_wrap}>
                <div className={styles.title}>
                  {starSpaceType == 2 ? '发布了一条会议，快来参与聊天吧~' : '发布了一场直播，快来参与聊天吧~'}
                </div>
                <div className={styles.cover_img} onClick={onClickCoverImg}>
                  <CoverImageOrVideo
                    data={space}
                    spaceCoverUrlShow={textImgList && textImgList[0] && textImgList[0].imageUrlShow}
                  />
                  {
                    isTemplateCover == 1 && !(space.vodPathUrl && getOperatingEnv() != '2') &&
                    <div className={styles.title_in_cover_image}>{imageTitle}</div>
                  }
                  {
                    (spaceStatus == 1 || spaceStatus == 2) &&
                    <div className={styles.status_box}>
                      <img src={spaceStatus == 1 ? space_status_icon1 : space_status_icon2} width={12} height={12} alt=""/>
                      <span>{spaceStatus == 1 ? '进行中' : spaceStatus == 2 ? '预约中' : ''}</span>
                    </div>
                  }
                  <span className={styles.gdp}>{gdpFormat(space.gdp)}GDP | {gdpFormat(space.pv)}观看</span>
                  <div id={`vs${detailsState.id}`} className={styles.danmu_box}>
                    <div id={`ms${detailsState.id}`}></div>
                  </div>
                </div>
              </div>
              {
                kingdomInfo &&
                <div className={styles.kingdom_wrap}>
                  <KingdomCard kingdomInfo={kingdomInfo}/>
                </div>
              }
              <div className={styles.gdp_wrap}>
                <img src={gdp_icon} width={14} height={14} alt=""/>
                <span>{gdpFormat(gdp)}GDP</span>
              </div>
              <CommentList
                onRef={commentListRef}
                imageTextId={id}
                updateCommentFooter={updateCommentFooter}
              />
              <CommentFooter
                imageTextId={id}
                imgTextInfo={detailsState}
                onPost={onPost}
              />
            </>
            : pageLoadStatus == 2 ?
            <div className={styles.no_data_wrap}>
              <NoDataRender style={{marginTop: 0}}/>
            </div>
            : null
        }
      </div>
    </Spin>
  )
}

export default connect(({ loading }: any) => ({ loading }))(Index)

