/**
 * @Description: pc端-个人中心
 */
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { connect, history } from 'umi';
import styles from './PcIndex.less';
import { Popover, Spin, message, Select } from 'antd';
import classNames from 'classnames';
import { QuestionCircleFilled } from '@ant-design/icons';
import {priceFormat, processNames, randomColor} from '@/utils/utils';
import PcHeader from '@/componentsByPc/PcHeader';                       // 头部组件
import PcEditUserInfoModal from './PcComponents/PcEditUserInfoModal';   // 编辑信息弹框
import PcMyHomepage from './PcComponents/PcMyHomepage';                 // 主页模块
import PcMyFollow from './PcComponents/PcMyFollow';                     // 我的关注模块
import PcMyCollect from './PcComponents/PcMyCollect';                   // 我的收藏模块
import PcMySetting from './PcComponents/PcMySetting';                   // 设置模块
import PcMyConsultationList from './PcComponents/PcMyConsultationList'; // 我的指导模块
import PcCropperHeaderImgModal from './PcComponents/PcCropperHeaderImgModal'; // 裁剪头像弹框
import pcTab1Icon from '@/assets/UserInfo/pc_user_1.png';
import pcTab2Icon from '@/assets/UserInfo/pc_user_2.png';
import pcTab3Icon from '@/assets/UserInfo/pc_user_3.png';
import pcTab4Icon from '@/assets/UserInfo/pc_user_4.png';
import pcTab5Icon from '@/assets/UserInfo/pc_user_5.png';
import pcTabActive1Icon from '@/assets/UserInfo/pc_user_active_1.png';
import pcTabActive2Icon from '@/assets/UserInfo/pc_user_active_2.png';
import pcTabActive3Icon from '@/assets/UserInfo/pc_user_active_3.png';
import pcTabActive4Icon from '@/assets/UserInfo/pc_user_active_4.png';
import pcTabActive5Icon from '@/assets/UserInfo/pc_user_active_5.png';
import pcEImg from '@/assets/UserInfo/pc_e.png'; // 鹅图片
import PcEArrow from '@/assets/GlobalImg/e_arrow.png';
import certified_icon from '@/assets/GlobalImg/certified_icon.png'
import e_arrow from '@/assets/GlobalImg/e_arrow.png'
import not_certified_icon from '@/assets/GlobalImg/not_certified_icon.png'
import right_arrow from '@/assets/GlobalImg/right_arrow.png'
import {stringify} from "qs"
import {Toast} from "antd-mobile"; // 右箭头
import dayjs from 'dayjs';
const editIcon = 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/edit.png'; // 编辑小图标
const ordinaryIcon = 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/ordinary.png'; // 普通用户图标
const doctorIcon = 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/doctor.png'; // 医生会员图标
const enterpriseIcon = 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/enterprise.png'; // 医生会员图标
const xiaoyiImg = 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/xiaoyi.png'; // 小忆人图片

// tab数据列表
const navigationList = [
  {id: 1, text: '主页', icon: pcTab1Icon, activeIcon: pcTabActive1Icon},
  {id: 6, text: '我的病例', icon: pcTab4Icon, activeIcon: pcTabActive4Icon},
  {id: 2, text: '我的课程', icon: pcTab2Icon, activeIcon: pcTabActive2Icon},
  {id: 3, text: '我的关注', icon: pcTab3Icon, activeIcon: pcTabActive3Icon},
  {id: 4, text: '我的收藏', icon: pcTab4Icon, activeIcon: pcTabActive4Icon},
  {id: 5, text: '设置', icon: pcTab5Icon, activeIcon: pcTabActive5Icon},
]

const Index: React.FC = (props: any) => {
  const { global, dispatch, loading, pcAccount } = props;
  const { query } = history.location
  const [isShowQRCode, setIsShowQRCode] = useState(true); // 是否展示二维码
  const [editAccountOpen, setEditAccountOpen] = useState(false); // 是否打开编辑账户弹框
  const [userInfo, setUserInfo] = useState<any>(null); // 用户信息数据
  const [vipList, setVipList] = useState([]); // vip数据
  const [codeImg, setCodeImg] = useState(''); // 二维码图片
  const [tabKey, setTabKey] = useState(query.tabKey || pcAccount.tabState || 1); // tab初始化
  const [expertAdviceData, setExpertAdviceData] = useState<any>({}); // 获取专家个人详细信息
  const [isOverflow, setIsOverflow] = useState(false); // 专家信息是否展示全部
  const [waistcoatList, setWaistcoatList] = useState([]); // 马甲List
  const [friUserToScholarship,setFriUserToScholarship] = useState(false); // 获取学习金余额
  const introduceRef = useRef<any>(null);
  const UerInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');

  // 是否嵌套在5i5ya的iframe中
  const isInIframe = (self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya')

  useEffect(() => {
    initialization()
    getExpertsInfo()
    initGetMemberIconList()
  }, [])

  // 初始化获取用户信息
	const initialization = async ()=>{
    await dispatch({
      type: 'userInfoStore/getUserInfo',
      payload: {
        wxUserId: UerInfo && UerInfo.friUserId || '',
      }
    }).then((res:any) => {
      const { code, content } = res || {};
      if(code == 200) {
        const { name, nickName } = content;
        localStorage.setItem('userInfo', JSON.stringify({
          ...UerInfo,
          name: name,
          nickName: nickName,
          headUrl: content && content.headUrlShow
        }))
        // 初始化账号马甲列表
        UerInfo?.vestUcStarIdentityList?.length>0&&getUpdateWaistcoatList(UerInfo.vestUcStarIdentityList)
        setUserInfo(content);
        // 判定是否有学习金 如果有继续调用获取学习金余额
        // isHasScholarship = 1 是否有学习金 1有 0无
        if (content && content.isHasScholarship == 1) {
          // 获取学习金数据
          getFriUserToScholarshipByfunc();
        }
      } else {
        setUserInfo(null);
      }
    }).catch((err:any)=>{
      // message.error(err.msg);
    })
	}

  // 获取学习金数据 fri-user-to-scholarship  获取用户个人主页学习金数据
  const getFriUserToScholarshipByfunc = () => {
    dispatch({
      type: 'userInfoStore/getFriUserToScholarship',
      payload: {
        wxUserId: UerInfo && UerInfo.friUserId || '',
      }
    }).then(res => {
      const { code, content } = res || {};
      if (code == 200 && content) {
        setFriUserToScholarship(content);
      }
    }).catch(err=>{
      Toast.show({content: err.msg});
    })
  }

  // 获取会员图标列表
  const initGetMemberIconList = async () => {
    await dispatch({
      type: 'userInfoStore/getMemberIconList',
      payload: {
        wxUserId: UerInfo && UerInfo.friUserId || '',
      }
    }).then((res:any) => {
      const { code, content } = res || {};
      if(code == 200) {
        setVipList(content && content.memberIconList);
        setCodeImg(content && content.defaultAssistantUrl);
      }
    }).catch((err:any)=>{
      message.error(err.msg);
    })
  }

  // 获取专家详情
  const getExpertsInfo = () => {
    dispatch({
      type: "expertAdvice/getExpertsInfo",
      payload: {
        expertsUserId: UerInfo && UerInfo.friUserId
      }
    }).then((res: any) => {
      const { code, content } = res || {}
      if (code == 200) {
        setExpertAdviceData(content)
      }
    }).catch((err: String) => {
      console.log(err)
    })
  }

  // vip会员权益图标跳转
  const jumpUrl = (item:any) => {
    const { pcJumpLink, jumpLink } = item || {};
    if(jumpLink) {// 外部链接-跳转小鹅通/鹅直播
      return window.open(jumpLink)
    }
    if(pcJumpLink) { // 跳转内部链接
      return history.push(pcJumpLink)
    }
  }

  // 会员开通按钮
  const vipOpenBtn = () => {
    history.push('/Payment/MemberBenefitsPage')
  }

  // 打开编辑账户
  const editAccountHandle = useCallback(() => {
    setEditAccountOpen(true);
  },[])

  // 编辑信息后关闭弹框并更新数据
  const editUpdateFn = (val?:any) => {
    setEditAccountOpen(false);
    val ? initialization() : null;
  }

  // tab切换（主页、收藏、关注、设置、课程）
  const switchTabHandle = (id:number) => {
    history.replace(`${history.location.pathname}?${stringify({
      ...history.location.query,
      tabKey: id,
      subTabKey: '',
    })}`)
    setTabKey(id);
    dispatch({
      type: 'pcAccount/save',
      payload: {
        tabState: id,
        subTabState: null,
        threeTabState: 1,     // 三级tab状态
      }
    })
    // 当在PC我的页面中切换到其他标签时，清空我的病例-筛选项控制参数
    if(id != 6) { dispatch({ type: 'pcAccount/cleanByPcMyConsultationList' }) }
  }

  // 我的课程跳转小鹅通地址
  const jumpXiaoETongUrl = () => {
    window.open('https://apptmq2ocpm6396.h5.xiaoeknow.com/p/t/v1/ecommerce/pay_record/record')
  }

  const { id, name, memberType, nickName, focusCount, fansCount, personGdpCount, status, headUrlShow, isHasSelfAuth } = userInfo || {};
  const {depSubjectDictName, abilityLevelDictName, postTitleDictName, intro, isExperts } = expertAdviceData || {};
  const getMemberIconListLoading = !!loading.effects['userInfoStore/getMemberIconList']; // loading
  const getUserInfoLoading = !!loading.effects['userInfoStore/getUserInfo']; // loading

  // 根据tabKey渲染各个组件
  const RenderComponent = () => {
    switch(Number(tabKey)) {
      case 1:
        return <PcMyHomepage isExperts={isExperts}/>
      case 2:
        return <div className={styles.e_wrap}><div className={styles.e_box}><img className={styles.e_img} src={pcEImg} alt="" /><span className={styles.e_text} onClick={jumpXiaoETongUrl}>即将跳转小鹅通<img src={PcEArrow} alt="" /></span></div></div>
      case 3:
        return <PcMyFollow/>
      case 4:
        return <PcMyCollect/>
      case 5:
        return <PcMySetting/>
      case 6:
        return <PcMyConsultationList/>
    }
  }

  // 判断专家信息总高度是否大于40（2行高度）则省略并滑过展示全部内容
  useEffect(() => {
    if (introduceRef?.current?.offsetHeight > 40) {
      setIsOverflow(true);
    }else {
      setIsOverflow(false);
    }
  }, [intro])

  // 获取马甲列表
  const getUpdateWaistcoatList = (List) => {
    const mainInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    let listArr = [];
    List.map((item:any)=>{
      if(item.friUserId!= mainInfo?.friUserId){
        listArr.push({
          label: `${item.serialNumber}-${item?.nickName?item.nickName:item.name}`,
          value:item.friUserId
        })
      }
    })
    setWaistcoatList(listArr)
  }

  // 设置切换马甲号
  const setSwitchWaistcoatAccount = (vestUserId) => {
    dispatch({
      type: 'userInfoStore/setSwitchWaistcoatAccount',
      payload: {
        vestUserId
      }
    }).then(res => {
      const { code, content } = res || {};
      if(code == 200) {
        dispatch({
          type: 'userInfoStore/getSwitchWaistcoatToken',
          payload: {
            vestToUcKey:content
          }
        }).then(res => {
          const { code, content } = res || {};
          if(code == 200) {
            localStorage.setItem('access_token', content.access_token);
            localStorage.setItem('user_name', content.username)
            localStorage.setItem('vxOpenIdCipherText', content.username)
            updateUserInfo(vestUserId, content.username)
          }
        }).catch(err=>{
          message.error(err.msg);
        })
      }
    }).catch(err=>{
      message.error(err.msg);
    })
  }

  // 更新当前账号信息
  const  updateUserInfo = (vestUserId,userName) => {
    dispatch({
      type: 'userInfoStore/getUserInfo',
      payload: {
        wxUserId:vestUserId,
        userId:vestUserId,
        userName
      }
    }).then(res => {
      const { code, content } = res || {};
      if(code == 200) {
        const currentInfo = UerInfo?.vestUcStarIdentityList.filter(obj => obj.friUserId === vestUserId)[0];
        localStorage.setItem('userInfo', JSON.stringify({
          ...UerInfo,
          ...currentInfo,
          id:vestUserId,
          headUrl:content.headUrlShow,
          vestUcStarIdentityList:content?.vestUcStarIdentityList
        }))
        getUpdateWaistcoatList(content?.vestUcStarIdentityList)
        setUserInfo(content);
        window.location.reload();
        message.success('切换马甲成功!');
      }
    }).catch(err=>{
      setUserInfo(null);
      message.error(err.msg);
    })
  }




  // 点击已认证/未认证按钮
  const onClickCertifiedBtn = () => {
    // isHasSelfAuth 1 已认证，0 未认证
    if (isHasSelfAuth == 1) {
      history.push('/UserInfo/Certification/InformationDetails')
    } else {
      history.push('/UserInfo/Certification/SelectRoles')
    }
  }

  return <>
    {
      isInIframe ?
        <div className={styles.pc_wrap}>
          <div className={styles.pc_content} style={{height: '100%'}}>
            <div className={styles.pc_left_content}>{RenderComponent()}</div>
          </div>
        </div>
        :
        <div className={styles.pc_wrap}>
          {/* 顶部导航栏 */}
          <PcHeader/>
          <Spin spinning={getMemberIconListLoading || getUserInfoLoading}>
            {/* 用户信息 */}
            <div className={styles.pc_head_wrap}>
              {/* 头像 */}
              <div className={styles.head_sculpture}>
                {
                  userInfo && headUrlShow ? <img onClick={() => {
                      if (isHasSelfAuth == 1) {
                        history.push('/UserInfo/Certification/InformationDetails')
                      } else {
                        history.push('/UserInfo/Certification/SelectRoles')
                      }
                    }} src={headUrlShow} alt="" /> :
                    <div onClick={() => {
                      if (isHasSelfAuth == 1) {
                        history.push('/UserInfo/Certification/InformationDetails')
                      } else {
                        history.push('/UserInfo/Certification/SelectRoles')
                      }
                    }} className={styles.head_sculpture_name} style={{background: userInfo && name || nickName ? randomColor(id) : "none"}}>{processNames(nickName || name)}</div>
                }
              </div>

              <div className={styles.pc_user_info}>
                {/* 姓名等信息 */}
                <div className={styles.pc_top_info_wrap}>
                  <div className={styles.pc_top_info}>
                    <div className={styles.name}>{nickName || name}</div>
                    {postTitleDictName && <div className={styles.doctor}>{postTitleDictName}</div>}
                    {depSubjectDictName || abilityLevelDictName ? <div className={styles.tips}>{depSubjectDictName}{abilityLevelDictName ? "·" : null}{abilityLevelDictName}</div> : ''}
                    <div className={styles.identity}>
                      {
                        memberType != null && memberType == 0 ? <img src={ordinaryIcon} alt="" /> :
                          memberType == 1 ? <img src={doctorIcon} alt="" /> :
                            memberType == 2 ? <img src={enterpriseIcon} alt="" /> : null
                      }
                    </div>
                    {
                      waistcoatList && waistcoatList.length > 0 && <div className={styles.switch_waistcoatAccount}>
                        <Select
                          placeholder="切换马甲"
                          style={{width: 150}}
                          options={waistcoatList}
                          onChange={(value)=>setSwitchWaistcoatAccount(value)}
                        />
                      </div>
                    }
                    <div className={styles.edit_user_info} onClick={editAccountHandle}>
                      <img src={editIcon} width={16} height={16} alt=''/>
                      <span>编辑账户</span>
                    </div>
                  </div>

                  {/* 认证信息 */}
                  <div className={classNames(styles.certified, {
                    [styles.not_certified]: isHasSelfAuth == 0,
                  })} onClick={onClickCertifiedBtn}>
                    <img src={isHasSelfAuth == 1 ? certified_icon : not_certified_icon} width={14} height={14} alt=""/>
                    <span>{isHasSelfAuth == 1 ? '已认证' : '未认证'}</span>
                    <img src={isHasSelfAuth == 1 ? e_arrow : right_arrow} width={12} height={12} alt=""/>
                  </div>
                </div>
                {/* 医生介绍信息 */}
                {
                  intro ? <div className={styles.pc_bottom_introduce}>
                    {
                      <Popover overlayClassName={styles.pc_bottom_introduce_popover} content={intro} open={isOverflow ? undefined : false}>
                        <div className={styles.brief_introduction_hide}><span className={styles.text} ref={introduceRef} dangerouslySetInnerHTML={{ __html: intro}} /></div>
                      </Popover>
                    }
                  </div> : ''
                }
              </div>
            </div>
            {/* 个人中心一级tab签 */}
            <div className={styles.pc_navigation}>
              <div className={styles.pc_tab_wrap}>
                <ul className={styles.pc_tab_list}>
                  {
                    navigationList.map(item => {
                      return <li key={item.id} className={classNames({[styles.pc_tab_item]: true, [styles.pc_tab_active_item]: tabKey == item.id})} onClick={() => switchTabHandle(item.id)}>
                        <img src={tabKey == item.id ? item.activeIcon : item.icon} alt="" />{item.text}
                      </li>
                    })
                  }
                </ul>
              </div>
              <div className={styles.pc_data_wrap}>
                <div className={styles.pc_data_list}>
                  <div className={styles.pc_data_title}>关注</div>
                  <div className={styles.pc_data_text}>{focusCount || 0}</div>
                </div>
                <div className={styles.pc_data_list}>
                  <div className={styles.pc_data_title}>粉丝</div>
                  <div className={styles.pc_data_text}>{fansCount || 0}</div>
                </div>
                <div className={styles.pc_data_list}>
                  <div className={styles.pc_data_title} id={'gdp_title'}>GDP<Popover placement="bottomRight" getPopupContainer={() => document.getElementById('gdp_title')} content={<div dangerouslySetInnerHTML={{ __html: global.gdpExplain!=null&&global.gdpExplain[0].gdpContent}} style={{width: '200px'}}></div>}><QuestionCircleFilled  style={{fontSize: 12,color: '#999',marginLeft: 5}} /></Popover></div>
                  <div className={styles.pc_data_text}>{personGdpCount || 0}</div>
                </div>
              </div>
            </div>
            {/* 页签内容 */}
            <div className={styles.pc_content}>
              <div className={styles.pc_left_content}>{RenderComponent()}</div>
              {/* 设置模块 - 不需要右侧样式 */}
              {
                (tabKey == 5 || tabKey == 6) ? '' :
                  <div className={styles.pc_right_content}>
                    <div className={styles.vip_member_box}>
                      <div className={styles.vip_title}>权益中心</div>
                      {/*
                        设置权益中心中的-余额卡片
                        学习金余额展示组件 userInfo.isHasScholarship
                        isHasScholarship = 1 是否有学习金 1有 0无
                       */}
                      {
                        userInfo
                        && userInfo?.isHasScholarship == 1
                        && friUserToScholarship
                        &&
                        <div className={styles.BalanceStudyFundsBoxWarp}>
                          <div className={styles.BalanceStudyFundsBox}>
                            <div className={styles.BalanceStudyFundsLeft}>
                              <div className={styles.BalanceStudyFundsTitle}> 学习金余额 </div>
                              <div className={styles.BalanceStudyFundsNum}> {priceFormat(friUserToScholarship.currentNum)} </div>
                              <div className={styles.BalanceStudyFundsDate}> 有效期至{dayjs(friUserToScholarship.validDate,'YYYY-MM-DD').format('YYYY年MM月DD日')} </div>
                              <div className={styles.StudyBonusBg}></div>
                            </div>
                            <div className={styles.BalanceStudyFundsRight}>
                              <div
                                onClick={()=>{ history.push('/StudyBonus/list'); }}
                                className={styles.DetailBox}>
                                <div>查看详情</div>
                                <i className={styles.DetailBox_icon}></i>
                              </div>
                            </div>
                          </div>
                        </div>
                      }

                      <div className={styles.vip_list}>
                        {
                          vipList && vipList.map((item, ind) => {
                            return <div key={ind} className={styles.vip_list_item} onClick={()=> jumpUrl(item)}>
                              <div className={styles.vip_list_item_icon}><img src={`https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/${item.iconPath}`}/></div>
                              <div className={styles.vip_list_item_content}>
                                <div className={styles.vip_list_item_text}>{item.name}</div>
                                {item.remark ? <div className={styles.vip_list_item_tips}>{item.remark}</div> : null}
                              </div>
                            </div>
                          })
                        }
                      </div>
                      <div className={styles.vip_button}>
                        <div className={styles.vip_button_style} onClick={vipOpenBtn}>
                          {
                            !userInfo || memberType == 0 ? <span className={styles.vip_button_text}>开通会员&nbsp;畅享内容</span> :
                              memberType == 1 ? <span className={styles.vip_button_text}>升级企业会员&nbsp;解锁新权益</span> :
                                memberType == 2 ? <span className={styles.vip_button_text}>续费企业会员&nbsp;解锁智能设备</span> : null
                          }
                        </div>
                      </div>
                    </div>
                    <div className={styles.character_Img}>
                      <div className={styles.QR_code_wrap} style={{display: isShowQRCode ? 'block' : 'none'}}>
                        <div className={styles.QR_code_box}>
                          <img className={styles.QR_code_img} src={codeImg}/>
                          <div className={styles.QR_code_text}>有问题,请找小忆</div>
                        </div>
                      </div>
                      <img className={styles.people_img} src={xiaoyiImg} onClick={()=>{setIsShowQRCode(!isShowQRCode)}} />
                    </div>
                  </div>
              }
            </div>
          </Spin>
          <PcEditUserInfoModal userInfo={userInfo} open={editAccountOpen} updataInitData={(val)=>editUpdateFn(val)}/>
        </div>
    }
  </>
}
export default connect(({global, userInfoStore,pcAccount, expertAdvice, loading }: any) => ({global, userInfoStore,pcAccount, expertAdvice, loading}))(Index)
