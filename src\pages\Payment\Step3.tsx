import React, { useState,useEffect } from 'react';
import { history,connect } from 'umi';
import styles from './Step3.less';
import { debounce } from 'lodash'
import Navigation from "@/pages/Payment/components/PC/Navigation";
import classNames from "classnames";
import { getOrderIsPay } from "@/services/payment";
import { getOperatingEnv, goToHomePage } from "@/utils/utils";
import {Spin} from "antd";
import BreadcrumbByPayment from "@/pages/Payment/components/PC/BreadcrumbByPayment";
import {Toast} from "antd-mobile";


const Step3: React.FC = (props) => {
  const { dispatch } = props
  const [loading, setLoading] = useState(false);
  const [pageType, setPageType] = useState(null); // 1 PC端  2移动端
  const [payState, setPayState] = useState(null); // 0:无此订单 1:支付成功  2:支付失败 3:等待中
  const [memberTypeCode, setMemberTypeCode] = useState(null); // 区分企业版本还是个人版本 1个人 2企业
  let envByPage = getOperatingEnv()

  // 当前页面视口是否小于750
  let updateType = () => {
    let env = getOperatingEnv() // 获取当前操作环境 1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用
    let type = env == 4 ? 1 : 2;
    setPageType(type);
  };
  updateType = debounce(updateType, 100);
  window.addEventListener('resize', updateType);

  useEffect(() => {
    updateType();
    initializeData(); // 初始化数据根据订单号获取支付状态
  }, []);

  // 根据订单号获支付状态
  const initializeData = async ()=>{
    let {match: { params: { orderId } },location} = props
    // 删除订单号
    sessionStorage.removeItem('orderId');
    setLoading(true)
    const DataByOrder = await getOrderIsPay({ orderId:orderId })
    setLoading(false)
    if(DataByOrder && DataByOrder.code === 200){
      const {
        payFlag, // : true已完成 false未完成
        payType,  // : 0 无此订单  1微信小程序 2微信H5 3微信二维码 4对公账户 5微信内浏览器
        memberTypeCode, // 区分企业版本还是个人版
      } = DataByOrder?.content || {}
      setMemberTypeCode(memberTypeCode);
      if(payType == 4) {
        // 1:支付成功  2:支付失败 3:对公转账审核中
        setPayState(3)
      }else if(payType != 0){
        // 1:支付成功  2:支付失败
        setPayState(payFlag? 1 : 2)
      }else {
        setPayState(0) // 查询不到此订单
      }
    }
  }


  // 点击返回首页
  const goHome= async ()=>{
    const env = getOperatingEnv() //  1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用
    goToHomePage(dispatch, 'push')
  }
  // 重新支付
  const goPay = async ()=>{
    let {match: { params: { orderId } },location} = props
    history.replace(`/Payment/Step2/${orderId}?random=${Math.random()}`)
  }


  return (
    <div>
      {pageType == 1 &&  // 当前
        <Spin spinning={!!loading}>
          <div className={styles.page_warp}>
            <div className={styles.page_content}>
              {/* ---面包屑--- */}
              <BreadcrumbByPayment items={memberTypeCode ? ['FRIDAY解决方案', memberTypeCode == 2? '企业版' : '个人版', '支付结果'] : ['FRIDAY解决方案', '支付结果']}/>
              {/* ---title导航条--- */}
              <Navigation activeStep={3} />  {/* activeStep: 1表示当前处于第1个导航项, 1:信息填写并提交,2:支付方式,3:支付结果 */}
              {/* ---支付结果--- */}
              <div style={{marginTop:'30px',marginBottom:'20px'}}>
                <div className={styles.Payment_result}>
                  <div className={classNames({
                    [styles.Payment_result_icon]:true,  // 支付成功
                    [styles.Payment_result_icon_Success]:payState == 1,  // 支付成功
                    [styles.Payment_result_icon_fail]:payState == 2 || payState == 0,  // 支付失败
                    [styles.Payment_result_icon_audit]:payState == 3,  // 支付审核中
                  })}></div>

                  <div className={styles.Payment_result_title}>
                    {payState === 1 && '支付成功'}
                    {payState === 2 && '很遗憾,支付失败'}
                    {payState === 3 && '审核中'}
                    {payState === 0 && '查询不到此订单'}
                  </div>
                  <div className={styles.Payment_result_desc}>
                    {payState === 1 && '您已完成支付，点击返回体验权益~'}
                    {payState === 2 && '您未完成支付，点击重新支付页面可重新支付支付，点击返回将无法获得权益'}
                    {payState === 3 && '您的订单正在审核中，预计1-2天内完成审核，请耐心等待'}
                    {payState === 0 && '查询不到此订单'}
                  </div>
                  <div className={styles.Payment_result_btn}>
                    <div onClick={goHome} className={styles.Payment_result_btn_repay}>返回</div>
                    {payState == 2 &&
                      <div onClick={goPay} className={styles.Payment_result_btn_back}>重新支付</div>
                    }
                  </div>
                </div>

              </div>
            </div>
          </div>
        </Spin>
      }

      {/* ------------移动端布局------------ */}
      <div>
        {pageType == 2 &&  // 当前
          <Spin spinning={!!loading}>
            <div className={styles.Mobile_Wrap}>
              {/*{
                (envByPage != 2) &&
                <div className={styles.Mobile_title_statusbar}></div>
              }
              {
                (envByPage != 2) &&
                <div className={styles.Mobile_title_Wrap}>
                  <div className={styles.Mobile_title}>支付结果</div>
                </div>
              }*/}


              {/* ---填写信息--- */}
              <div className={styles.Mobile_content}>
                <div className={styles.Mobile_content_title}>
                  <div className={classNames(
                    {
                      [styles.Mobile_content_title_status_icon]:true,
                      [styles.Mobile_content_title_status_icon_Success]:payState == 1,
                      [styles.Mobile_content_title_status_icon_fail]:payState == 2 || payState == 0,
                      [styles.Mobile_content_title_status_icon_audit]:payState == 3,
                    }
                  )}></div>
                  <div className={styles.Mobile_content_title_status_text}>
                    {payState === 1 && '支付成功'}
                    {payState === 2 && '支付失败'}
                    {payState === 3 && '审核中'}
                    {payState === 0 && '查询不到此订单'}
                  </div>
                </div>
                <div className={styles.Mobile_content_title_status_desc}>
                  {payState === 1 && '您已完成支付，客服人员会在24小时内联系您，请您耐心等待，点击返回我的页面查看点亮权益体验新权益~'}
                  {payState === 2 && '您未完成支付，点击重新支付页面可重新支付，点击返回将无法获得权益'}
                  {payState === 3 && '您的订单正在审核中，预计1-2天内完成审核，请耐心等待'}
                  {payState === 0 && '查询不到此订单'}
                </div>
                <div className={styles.Mobile_content_btn_wrap}>
                  <div onClick={goHome} className={styles.Mobile_content_btn}>
                    返回首页
                  </div>
                  {payState == 2 &&
                    <div onClick={goPay} className={styles.Mobile_content_repay}>
                      重新支付
                    </div>
                  }
                </div>
              </div>
            </div>
          </Spin>
        }
      </div>
    </div>
  );
};

export default connect(({  loading }: any) => ({
   loading
}))(Step3)
