/**
 * @Description: PC端，发起指导页第2步，选择病例模版
 */
import React, { useEffect, useState } from 'react'
import { history, connect } from 'umi'
import classNames from 'classnames'
import { stringify } from 'qs'
import { getArrailUrl } from '@/utils/utils'
import { Button, message, Modal, Spin } from 'antd'
import { Toast } from 'antd-mobile'
import { QuestionCircleOutlined, InfoCircleOutlined } from '@ant-design/icons'
import styles from './Step2.less'

// 模板预览图片
import preview_7 from '@/assets/Consultation/preview_7.png'
import preview_8 from '@/assets/Consultation/preview_8.png'

import PcHeader from '@/componentsByPc/PcHeader' // 顶部导航栏
import PreviewCaseModal from '../ComponentsPC/PreviewCaseModal' // 预览病例弹窗
import StartConsultationSteps from '@/pages/ConsultationModule/StartConsultation/ComponentsPC/StartConsultationSteps' // 完整服务流程按钮及弹窗
import TipsModal from '../ComponentsPC/TipsModal' // 切换模版提示弹窗、PC和H5账号不一致提示弹窗

const Index: React.FC = (props) => {
  const isInIframe = (self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya') // 是否嵌套在5i5ya的iframe中
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}') // 登录用户信息
  const { loading, dispatch } = props
  const { query, pathname } = history.location
  const {
    expertsUserId, // 专家ID
    consultationType, // 指导类型，1 图文，2 视频
    consultationId, // 指导ID
    copyUserId, // H5复制链接携带的用户ID
  } = query

  const initialModalState = {
    previewCaseModalVisible: false, // 预览弹窗
    templateType: 1, // 1 通用模板，2 正畸模板
    changeToCommonTipsModalVisible: false, // 切换到通用模版提示
    changeToOrthodonticTipsModalVisible: false, // 切换到正畸模版提示
    orderCaseTemplate: null, // 1 通用模板，2 正畸模板
    accountErrorTipsModalVisible: false, // H5与PC登录账号不一致提示弹窗
  }
  const [modalState, setModalState] = useState(initialModalState)
  const [state, setState] = useState({})
  const [loadingSelectTemplate, setLoadingSelectTemplate] = useState(null) // 选择模板loading
  const [loadingChangeTemplate, setLoadingChangeTemplate] = useState(false) // 切换模板loading

  useEffect(() => {
    // 未登录跳转登录页
    if (!localStorage.getItem('access_token')) {
      onClickLogin()
      return
    }
    if (copyUserId && copyUserId != UserInfo.friUserId) {
      // 您当前在PC端登录的账号与移动端不一致，请重新登录
      setModalState({
        ...modalState,
        accountErrorTipsModalVisible: true, // H5与PC登录账号不一致提示弹窗
      })
      return
    }
    if (consultationId) {
      getConsultationAndCaseInfo()
    }
  }, [])

  // 查询指导和病例详情
  const getConsultationAndCaseInfo = () => {
    dispatch({
      type: 'consultation/getConsultationAndCaseInfo',
      payload: {
        consultationId: consultationId,  // 指导ID
        type: 1,                         // (1:图文支付/视频提交, 2:其它通用详情)
      }
    }).then(res => {
      const { code, content, msg } = res
      // 除专家和用户之外的第三人查看，提示
      if (code == 422) {
        Modal.warning({
          content: msg,
          onOk: () => {
            goBack()
          }
        })
        return
      }
      // 专家也不能看
      if (content && content.createUserId != UserInfo.friUserId) {
        Modal.warning({
          content: '对不起，您无权限查看该数据！',
          onOk: () => {
            goBack()
          }
        })
        return
      }

      // 流程节点
      // 图文[1选择指导方式、2描述病例问题、3支付指导费用、4病例资料被查看、5问题被回复并对话、6结束指导交易成功];
      // 视频[1选择指导方式、2描述病例问题、3提交指导单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、7结束指导、8确认并支付指导费用、9交易成功]
      if (content && content.processNode > 3) {
        Modal.warning({
          content: '链接已失效，可能因为指导订单已经提交成功，请前往我的指导查看订单',
          onOk: () => {
            goBack()
          }
        })
        return
      }
      if (code == 200 && content) {
        // 在5i5ya的iframe中
        if (isInIframe) {
          const postData = {
            dataType: 'pathname',       // 页面地址onchange事件
            pathnameByChild: pathname,  // 路由信息
            searchByChild: `?${stringify({
              ...query,
              expertsUserId: content.expertsId, // 专家ID
              consultationType: content.type, // 指导类型，1图文，2视频
            })}`,  // 路由信息
          }
          console.log('子级发送数据：', postData, getArrailUrl())
          window.parent.postMessage(postData, getArrailUrl())
        }
        history.replace({
          pathname,
          query: {
            ...query,
            expertsUserId: content.expertsId, // 专家ID
            consultationType: content.type, // 指导类型，1图文，2视频
          },
        })

        setState(content)
      } else {
        message.error(msg || '查询指导和病例详情失败')
      }
    }).catch(err => {})
  }

  // 点击预览按钮
  const onClickPreviewBtn = (type) => {
    setModalState({
      ...modalState,
      previewCaseModalVisible: true,              // 预览弹窗
      templateType: type,                  // 1 通用模板，2 正畸模板
    })
  }

  // 关闭预览弹窗
  const previewCaseModalClose = () => {
    setModalState({
      ...modalState,
      previewCaseModalVisible: false,              // 预览弹窗
      templateType: 1,                  // 1 通用模板，2 正畸模板
    })
  }

  // 上一步
  const goToPrev = () => {
    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'pathname',       // 页面地址onchange事件
        pathnameByChild: `/ConsultationModule/StartConsultation/Step1`,  // 路由信息
        searchByChild: `?${stringify({
          ...query,
        })}`,  // 路由信息
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
      return
    }
    history.replace({
      pathname: '/ConsultationModule/StartConsultation/Step1',
      query: {
        ...query,
      }
    })
  }

  // 选择模板类型，1 通用模版，2 正畸模版
  const onClickSelectTemplate = (orderCaseTemplate) => {
    if (consultationId) {
      goToNextHaveId(orderCaseTemplate)
    } else {
      goToNextNoId(orderCaseTemplate)
    }
  }

  // 有指导ID的下一步，1 通用模版，2 正畸模版
  const goToNextHaveId = (orderCaseTemplate) => {
    // 触发切换模版提示
    if (state.orderCaseTemplate != orderCaseTemplate) {
      setModalState({
        ...modalState,
        changeToCommonTipsModalVisible: orderCaseTemplate == 1, // 切换通用模板提示弹窗
        changeToOrthodonticTipsModalVisible: orderCaseTemplate == 2, // 切换正畸模版提示弹窗
        orderCaseTemplate, // 1通用，2正畸
      })
      return
    }

    // 走到这，表示模版未变更，进入下一步
    if (orderCaseTemplate == 1) {
      // 在5i5ya的iframe中
      if (isInIframe) {
        const postData = {
          dataType: 'pathname',       // 页面地址onchange事件
          pathnameByChild: `/ConsultationModule/StartConsultation/Step3`,  // 路由信息
          searchByChild: `?${stringify({
            ...query,
            orderCaseTemplate, // 订单病例模板 1. 通用病例 2正畸病例
          })}`,  // 路由信息
        }
        console.log('子级发送数据：', postData, getArrailUrl())
        window.parent.postMessage(postData, getArrailUrl())
        return
      }
      history.replace({
        pathname: '/ConsultationModule/StartConsultation/Step3',
        query: {
          ...query,
          orderCaseTemplate, // 订单病例模板 1. 通用病例 2正畸病例
        }
      })
    } else if (orderCaseTemplate == 2) {
      // 在5i5ya的iframe中
      if (isInIframe) {
        const postData = {
          dataType: 'pathname',       // 页面地址onchange事件
          pathnameByChild: `/CreationOrthodontics/Step1`,  // 路由信息
          searchByChild: `?${stringify({
            orthodonticConsultationId: consultationId, // 指导ID
          })}`,  // 路由信息
        }
        console.log('子级发送数据：', postData, getArrailUrl())
        window.parent.postMessage(postData, getArrailUrl())
        return
      }
      history.replace(`/CreationOrthodontics/Step1?orthodonticConsultationId=${consultationId}`)
    }
  }

  // 没有指导ID的下一步，1 通用模版，2 正畸模版
  const goToNextNoId = async (orderCaseTemplate) => {
    setLoadingSelectTemplate(orderCaseTemplate)
    const res = await editConsultationInfo(orderCaseTemplate)
    setLoadingSelectTemplate(null)
    const { code, content, msg } = res || {}
    if (code == 200 && content) {
      const { consultationId: consultationIdByRequest } = content
      if (orderCaseTemplate == 1) {
        // 在5i5ya的iframe中
        if (isInIframe) {
          const postData = {
            dataType: 'pathname',       // 页面地址onchange事件
            pathnameByChild: `/ConsultationModule/StartConsultation/Step3`,  // 路由信息
            searchByChild: `?${stringify({
              ...query,
              orderCaseTemplate, // 订单病例模板 1. 通用病例 2正畸病例
              consultationId: consultationIdByRequest, // 指导ID
            })}`,  // 路由信息
          }
          console.log('子级发送数据：', postData, getArrailUrl())
          window.parent.postMessage(postData, getArrailUrl())
          return
        }
        history.replace({
          pathname: '/ConsultationModule/StartConsultation/Step3',
          query: {
            ...query,
            orderCaseTemplate, // 订单病例模板 1. 通用病例 2正畸病例
            consultationId: consultationIdByRequest, // 指导ID
          }
        })
      } else if (orderCaseTemplate == 2) {
        // 在5i5ya的iframe中
        if (isInIframe) {
          const postData = {
            dataType: 'pathname',       // 页面地址onchange事件
            pathnameByChild: `/CreationOrthodontics/Step1`,  // 路由信息
            searchByChild: `?${stringify({
              orthodonticConsultationId: consultationId, // 指导ID
            })}`,  // 路由信息
          }
          console.log('子级发送数据：', postData, getArrailUrl())
          window.parent.postMessage(postData, getArrailUrl())
          return
        }
        history.replace(`/CreationOrthodontics/Step1?orthodonticConsultationId=${consultationIdByRequest}`)
      }
    } else {
      message.error(msg || '数据加载失败')
    }
  }

  // 编辑指导订单信息，生成指导ID，1 通用模版，2 正畸模版
  const editConsultationInfo = (orderCaseTemplate) => {
    return dispatch({
      type: 'consultation/editConsultationInfo',
      payload: {
        postParams: {
          id: null,
          type: consultationType, // 指导类型，1 图文，2 视频
          expertsId: expertsUserId, // 指导医生ID
          orderCaseTemplate: orderCaseTemplate, // 订单病例模板 1. 通用病例  2正畸病例
          processNode: 2, // 图文[1选择指导方式、2描述病例问题、3支付指导费用、4病例资料被查看、5问题被回复并对话、6结束指导交易成功];
          // 视频[1选择指导方式、2描述病例问题、3提交指导单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、7结束指导、8确认并支付指导费用、9交易成功]
        },
      }
    }).then(res => {
      return res
    }).catch(err => {
      return null
    })
  }

  // 关闭切换模版提示弹窗
  const changeToCommonTipsModalClose = () => {
    setModalState({
      ...modalState,
      changeToCommonTipsModalVisible: false, // 切换通用病例提示弹窗
      orderCaseTemplate: null, // 1 通用模板，2正畸模版
    })
  }

  // 关闭切换模版提示弹窗
  const changeToOrthodonticTipsModalClose = () => {
    setModalState({
      ...modalState,
      changeToOrthodonticTipsModalVisible: false, // 切换正畸病例提示弹窗
      orderCaseTemplate: null, // 1 通用模板，2正畸模版
    })
  }

  // 确认切换模版
  const handleOkOnChangeTemplate = async () => {
    setLoadingChangeTemplate(true)
    const res1 = await switchOrderCaseTemplate()
    const { code: code1, content: content1, msg: msg1 } = res1 || {}
    if (code1 == 200 && content1) {

    } else {
      setLoadingChangeTemplate(false)
      message.error(msg1 || '数据加载失败')
      return
    }

    // 生成新的指导ID
    const res = await editConsultationInfo(modalState.orderCaseTemplate)
    setLoadingChangeTemplate(false)
    const { code, content, msg } = res || {}
    if (code == 200) {
      const { consultationId: consultationIdByRequest } = content
      if (modalState.orderCaseTemplate == 1) {
        // 在5i5ya的iframe中
        if (isInIframe) {
          const postData = {
            dataType: 'pathname',       // 页面地址onchange事件
            pathnameByChild: `/ConsultationModule/StartConsultation/Step3`,  // 路由信息
            searchByChild: `?${stringify({
              ...query,
              orderCaseTemplate: modalState.orderCaseTemplate, // 订单病例模板 1. 通用病例 2正畸病例
              consultationId: consultationIdByRequest, // 指导ID
            })}`,  // 路由信息
          }
          console.log('子级发送数据：', postData, getArrailUrl())
          window.parent.postMessage(postData, getArrailUrl())
          return
        }
        history.replace({
          pathname: '/ConsultationModule/StartConsultation/Step3',
          query: {
            ...query,
            orderCaseTemplate: modalState.orderCaseTemplate, // 订单病例模板 1. 通用病例 2正畸病例
            consultationId: consultationIdByRequest, // 指导ID
          }
        })
      } else if (modalState.orderCaseTemplate == 2) {
        // 在5i5ya的iframe中
        if (isInIframe) {
          const postData = {
            dataType: 'pathname',       // 页面地址onchange事件
            pathnameByChild: `/CreationOrthodontics/Step1`,  // 路由信息
            searchByChild: `?${stringify({
              orthodonticConsultationId: consultationIdByRequest, // 指导ID
            })}`,  // 路由信息
          }
          console.log('子级发送数据：', postData, getArrailUrl())
          window.parent.postMessage(postData, getArrailUrl())
          return
        }
        history.replace(`/CreationOrthodontics/Step1?orthodonticConsultationId=${consultationIdByRequest}`)
      }
    } else {
      message.error(msg || '数据加载失败')
    }
  }

  // 病例会诊切换模板
  const switchOrderCaseTemplate = () => {
    return dispatch({
      type: 'consultation/switchOrderCaseTemplate',
      payload: {
        getParams: {
          consultationId: consultationId, // 指导ID
        }
      }
    }).then(res => {
      return res
    }).catch(err => {
      return null
    })
  }

  // H5与PC登录账号不一致提示弹窗，点击立即登录；或未登录进行登录
  const onClickLogin = () => {
    if (isInIframe) {
      const postData = {
        dataType: 'logout',       // 登出
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
      return
    }
    history.push({
      pathname: '/User/login',
      query: {
        redirectByPush: window.location.href,
      }
    })
  }

  // H5与PC登录账号不一致提示弹窗关闭
  const accountErrorTipsModalClose = () => {
    setModalState({
      ...modalState,
      accountErrorTipsModalVisible: false,
    })
    goBack()
  }

  // 返回
  const goBack = () => {
    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'goBack',       // 页面地址onchange事件
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
      return
    }

    if (copyUserId || history.length <= 2) {
      history.replace('/')
    } else {
      history.goBack()
    }
  }

  // loading
  const getConsultationAndCaseInfoLoading = !!loading.effects['consultation/getConsultationAndCaseInfo']

  return (
    <>
      <div className={styles.container}>
        {/* iframe中隐藏header */}
        {
          isInIframe ? null : <PcHeader/>
        }

        <div className={styles.content}>
          <div className={styles.content_inner}>
            {/* 导航栏 */}
            <div className={styles.header}>
              <div className={styles.header_icon} onClick={goBack}></div>
              <div className={styles.header_title}>发起专家指导</div>
            </div>

            <div className={styles.box}>
              {/* 标题 */}
              <StartConsultationSteps title="选择病例信息模板"/>

              <Spin spinning={getConsultationAndCaseInfoLoading}>
                {/* tip提示 */}
                <div className={styles.warning_message_wrap}>
                  <div className={styles.warning_message}>
                    <InfoCircleOutlined />
                    <span>如您需要提交正畸等病例，推荐使用正畸专科模板。如病例信息不全，可能影响咨询效果</span>
                  </div>
                </div>

                {/* 选择模版 */}
                <div className={styles.block_wrap}>
                  <div className={styles.block}>
                    <div className={styles.block_title}>通用模版</div>
                    <div className={styles.block_info_wrap}>
                      <p>提供基础的病例信息输入</p>
                      <p>支持上传附件</p>
                    </div>
                    <div className={styles.block_img_wrap}>
                      <img src={preview_7} width={192} height={135} alt=""/>
                      <div className={styles.img_mask}></div>
                      <Button className={styles.img_btn} type="primary" ghost size="small" onClick={() => onClickPreviewBtn(1)}>预览</Button>
                    </div>
                    <div className={styles.block_tips_wrap}></div>
                    <div className={styles.block_btn_wrap}>
                      <Button className={styles.block_btn} type="primary" onClick={() => onClickSelectTemplate(1)} loading={loadingSelectTemplate == 1}>选择</Button>
                    </div>
                  </div>

                  <div className={styles.block}>
                    <div className={styles.block_title}>正畸专科模板</div>
                    <div className={styles.block_info_wrap}>
                      <p>专业的正畸病例模板</p>
                      <p>全方位上传患者病例信息</p>
                      <p>完整的信息填写，专家指导更快</p>
                    </div>
                    <div className={styles.block_img_wrap}>
                      <img src={preview_8} width={192} height={135} alt=""/>
                      <div className={styles.img_mask}></div>
                      <Button className={styles.img_btn} type="primary" ghost onClick={() => onClickPreviewBtn(2)}>预览</Button>
                    </div>
                    <div className={styles.block_tips_wrap}>
                      <p className={styles.tips_title}>请您提前准备以下资料以便精准咨询：</p>
                      <p>1、影像资料</p>
                      <p>全景片、侧位片、正面像、侧面像、正面咬合像、正面咬合45度像、左侧咬合像、右侧咬合像、上牙弓像、下牙弓像等</p>
                      <p>2、检查及分析</p>
                      <p>包括口外检查、口内检查、颞颌关节检查，模型分析、侧位片分析、全景片分析</p>
                    </div>
                    <div className={styles.block_btn_wrap}>
                      <Button className={styles.block_btn} type="primary" onClick={() => onClickSelectTemplate(2)} loading={loadingSelectTemplate == 2}>选择</Button>
                    </div>
                  </div>
                </div>

                {/* 按钮 */}
                <div className={styles.page_btn_wrap}>
                  <Button className={styles.page_btn} type="primary" ghost onClick={goToPrev}>上一步</Button>
                </div>
              </Spin>


            </div>
          </div>
        </div>

      </div>

      {/* 预览病例弹窗 */}
      <PreviewCaseModal
        visible={modalState.previewCaseModalVisible}
        templateType={modalState.templateType}
        onCancel={previewCaseModalClose}
      />

      {/* 切换模版提示弹窗-切换到通用 */}
      <TipsModal
        visible={modalState.changeToCommonTipsModalVisible}
        title={'已为您保存正畸病例草稿，是否确认切换为通用模板?'}
        text={'如确认切换，我们将不保留原草稿内容。'}
        leftBtnText={'取消'}
        rightBtnText={'确认切换'}
        onClickLeftBtn={changeToCommonTipsModalClose}
        onClickRightBtn={handleOkOnChangeTemplate}
        loading={loadingChangeTemplate}
      />

      {/* 切换模版提示弹窗-切换到正畸 */}
      <TipsModal
        visible={modalState.changeToOrthodonticTipsModalVisible}
        title={'已为您保存通用病例草稿，是否确认切换为正畸模板?'}
        text={'如确认切换，我们将不保留原草稿内容。'}
        leftBtnText={'取消'}
        rightBtnText={'确认切换'}
        onClickLeftBtn={changeToOrthodonticTipsModalClose}
        onClickRightBtn={handleOkOnChangeTemplate}
        loading={loadingChangeTemplate}
      />

      {/* H5与PC登录账号不一致提示弹窗 */}
      <TipsModal
        visible={modalState.accountErrorTipsModalVisible}
        title={'您当前在PC端登录的账号与移动端不一致，请重新登录'}
        leftBtnText={'取消'}
        rightBtnText={'重新登录'}
        onClickLeftBtn={accountErrorTipsModalClose}
        onClickRightBtn={onClickLogin}
      />
    </>
  )
}

export default connect(({ consultation, loading }: any) => ({ consultation, loading }))(Index)
