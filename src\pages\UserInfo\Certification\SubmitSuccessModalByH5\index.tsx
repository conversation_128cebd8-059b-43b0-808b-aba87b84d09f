/**
 * @Description: 实名认证成功弹窗
 */
import React, { useState } from 'react';
import { history, connect } from 'umi'
import { Spin } from 'antd'
import { Modal } from 'antd-mobile';
import styles from './index.less';

import successIcon from '@/assets/GlobalImg/success.png'
import {getOperatingEnv} from "@/utils/utils" // 成功icon

interface PropsType {
  visible: boolean,                  // 弹窗是否显示
  onCancel?: () => void,           // 点击按钮的回调
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {
    visible = false,
    onCancel,
  } = props

  return (
    <Modal
      visible={visible}
      className={styles.modal}
      destroyOnClose={true}
      closeOnMaskClick
      onClose={onCancel}
      getContainer={() => document.body}
      content={
        <div>
          <div className={styles.header}>
            <img src={successIcon} width={24} height={24} style={{flexShrink: 0}} alt=""/>
            <div className={styles.title}>认证成功</div>
          </div>

          {/* 按钮 */}
          <div className={styles.btn_wrap}>
            <div className={styles.btn} onClick={onCancel}>返回个人中心</div>
          </div>
        </div>
      }
    />
  )
}
export default connect(({ loading }: any) => ({ loading }))(Index)
