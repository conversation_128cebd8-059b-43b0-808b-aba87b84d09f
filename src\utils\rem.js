const baseSize = 16;

function setRem() {
  // 当前页面宽度相对于 1920宽的缩放比例，可根据自己需要修改。
  let scale = null;
  /*if (document.documentElement.clientWidth > 750) {
    scale = document.documentElement.clientWidth / 1440;
  } else {*/
  // }
  if (document.documentElement.clientWidth > 375) {
    scale = document.documentElement.clientWidth / 375
  }else {
    scale = document.documentElement.clientWidth / 375;
  }
  document.documentElement.style.fontSize =
    baseSize * Math.min(scale, 2) + 'px';
}
// 初始化
// setRem();
// 改变窗口大小时重新设置 rem
window.onresize = function() {
  // setRem();
};
