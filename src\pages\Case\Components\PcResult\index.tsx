/**
 * @Description: 优秀病例列表首页
 * @author: 赵斐
 */
import React, { useState, useEffect, useRef } from 'react';
import { connect, history } from 'umi';
import InfiniteScroll from 'react-infinite-scroller';
import { Spin, message } from 'antd'
import { throttle } from 'lodash';
import styles from './index.less';
// 公共导航组件
import PcHeader from '@/componentsByPc/PcHeader'
// pc-优秀病例列表头部
import Header from './Header';
// 列表卡片展示组件
import CaseCard from '@/componentsByPc/CaseCard';
// 筛选组件
import ScreenModal from './ScreenModal'
// 数据加载异常
import LoadingException from '@/components/LoadingException'

const initStatePage = {
  pageNum: 1,
  hasMore: true,  // 加载更多
  loadMore: false,
}
const Index: React.FC = (props: any) => {
  const { dispatch, loading, cases } = props;   // searchValues PC端首页跳转带的搜索值
  const { searchValue , screenHighlight } = cases;
  const initState = {
    total: 0,
    dataSource: [],
    searchValue: searchValue,
  }
  const listRef = useRef<any>(null);
  const [state, setState] = useState(initState)        // 列表接口所需参数及数据

  const [interfaceStatus, setInterfaceStatus] = useState(3); // 1 数据加载失败 2 暂无数据 3 筛选暂无数据
  const [tabData, setTabData] = useState<any>(null)    // 筛选数据
  const [statePage, setStatePage] = useState(initStatePage)  // 当前分页
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [ screenHeaderHighlight ,setScreenHeaderHighlight] = useState(screenHighlight)  // 筛选高亮

  // 是否嵌套在5i5ya的iframe中
  const isInIframe = (self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya')

  // 筛选弹窗状态
  const {
    total,
    dataSource,
  } = state
  const {
    pageNum,
    hasMore,
    loadMore,
  } = statePage || {}
  useEffect(() => {
    getExcellentCaseDict()
    // console.log(searchValues,"1111")
    // if(searchValues){
    //   console.log("走没走")
    //   dispatch({
    //     type: "cases/save",
    //     payload: {
    //       searchValue:searchValues
    //     }
    //   })

    // }
    getExcellentCaseList(1,searchValue, cases)
  }, [])

  // 获取字典
  const getExcellentCaseDict = () => {
    dispatch({
      type: "cases/getExcellentCaseDict",
      payload: {}
    }).then((res: any) => {
      console.log(res)
      const { content, code } = res || {}
      if (code == 200) {
        const { depSubject, difficultLevel, achievement } = content || {}
        difficultLevel.unshift({ code: 0, name: "全部", iconName: null, children: null })
        depSubject.unshift({ code: 0, name: "全部", iconName: "", children: null })
        achievement.unshift({ code: 0, name: "全部", iconName: "", children: null })
        setTabData(content)
      }
    }).catch((err: any) => {
      console.log(err)
    })
  }

  // 获取优秀列表数据
  const getExcellentCaseList = (pageNum: number = 1, searchKey: string = "", screenObj: any = {}) => {
    const { checkDepSubject, checkAbilityLevel ,checkAchievement , startDate , endDate } = screenObj || {}
    dispatch({
      type: "cases/getExcellentCaseList",
      payload: {
        pageNum,
        pageSize: 30,
        searchKey: searchKey,          // 搜索关键字
        achievementList: checkAchievement[0] == 0?[]:checkAchievement,
        depSubjectDictList: checkDepSubject[0] == 0?[]:checkDepSubject,
        difficultLevelDictList: checkAbilityLevel[0] == 0?[]:checkAbilityLevel,
        startDate,
        endDate,
      }
    }).then((res: any) => {
      if (res && res.code == 200) {
        const { content } = res || {};
        const { total, resultList } = content || {};
        let data = pageNum == 1 ? [] : dataSource;
        data = data.concat(resultList);
        const hasMore = data.length !== total;
        if (checkAchievement.length || checkDepSubject.length || checkAbilityLevel.length || startDate) {
          setScreenHeaderHighlight(true)
          setInterfaceStatus(3)
        } else {
          setScreenHeaderHighlight(false)
          setInterfaceStatus(3)
        }
        if (Array.isArray(data) && data.length == 0) {
          setState({
            ...state,
            dataSource: [],
            total: 0,
          })
          return
        }
        setState({
          ...state,
          dataSource: [...data],
          total,

        })
        setStatePage({
          ...statePage,
          loadMore: false,
          hasMore,
          pageNum,
        })
      } else {
        setInterfaceStatus(1)
      }
    }).catch((err: String) => {
      console.log(err)
    })
  }

  /**
   * 刷新数据
   * @param pageNum    当前页
   * @param searchKey  搜索值
   * @param screenObj  筛选值
   */
  const refreshInterface = (pageNum: number, searchKey: string, screenObj: any) => {

    listRef.current.scrollTop = 0
    setStatePage({
      ...statePage,
      loadMore: true,
      hasMore: false
    })
    getExcellentCaseList(pageNum, searchKey, screenObj)
  }

  // 滚动加载分页
  let handleInfiniteOnLoad = () => {
    if (dataSource.length > total - 1) {
      message.warning('已加载完毕');
      setStatePage({
        ...statePage,
        loadMore: false,
        hasMore: false
      })
      return;
    }
    const pages = pageNum + 1;
    setStatePage({
      ...statePage,
      loadMore: true,
    })
    getExcellentCaseList(pages, searchValue, cases)
  }
  handleInfiniteOnLoad = throttle(handleInfiniteOnLoad, 100);

  // 打开筛选弹窗
  const showScreenModal = () => {
    setIsModalOpen(true);
  };

  // 关闭筛选弹窗
  const handleScreenCancel = () => {
    setIsModalOpen(false);
  };
  const load = !!loading.effects['cases/getExcellentCaseList'] ||   // 优秀病例列表接口loading
    !!loading.effects['cases/getExcellentCaseDict']       // 学科字典接口loading
  return (
    <Spin spinning={load}>
      <div className={styles.container}>
        {/* iframe中隐藏header */}
        {isInIframe ? null : <PcHeader />}

        <div className={styles.wrap}>
          <div className={styles.wrap_header}>
            <Header screenHeaderHighlight={screenHeaderHighlight} showScreenModal={showScreenModal} refreshInterface={refreshInterface} />
          </div>
          <div className={styles.content} ref={listRef} style={{height: 'calc(100vh - 135px)'}}>
            {
              dataSource.length ?
                <InfiniteScroll
                  id="qwer"
                  initialLoad={false}
                  pageStart={0}
                  loadMore={handleInfiniteOnLoad}
                  hasMore={!loadMore && hasMore}
                  useWindow={false}
                  threshold={50}
                >
                  <div className={styles.tab_case}>

                    {
                      dataSource.map((item, ind) => <div key={ind} className={styles.tab_case_list}><CaseCard caseData={item} /></div>)
                    }
                  </div>

                </InfiniteScroll> : <div>
                  <LoadingException exceptionStyle={{ paddingTop: 220 }} interfaceStatus={interfaceStatus} />
                </div>
            }
          </div>
        </div>
      </div>
      {
        isModalOpen && <ScreenModal dataSource={tabData} isModalOpen={isModalOpen} refreshInterface={refreshInterface} handleScreenCancel={handleScreenCancel}/>
      }
    </Spin>
  )
}
export default connect(({ cases, loading }: any) => ({ cases, loading }))(Index)
