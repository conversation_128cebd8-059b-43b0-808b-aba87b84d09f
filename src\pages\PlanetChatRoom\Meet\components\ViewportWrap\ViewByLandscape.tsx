/**
 * @Description: 横屏样式
 */
import React, {useEffect, useImperativeHandle, useRef, useState,} from 'react';
import {connect, history} from 'umi';
import classNames from 'classnames';
import {formatTimeBySeconds, getOperatingEnv, useDebounce} from '@/utils/utils';
import {Input, Mask} from 'antd-mobile';
import {DownFill} from 'antd-mobile-icons';
import '../../../components/video-js.min.css';
import 'tcplayer.js/dist/tcplayer.min.css';
import styles from './ViewByLandscape.less';

// 图片图标
import SpatialDetail_Return_icon_btn from '@/assets/PlanetChatRoom/SpatialDetail_Return_icon_btn.png';
import inviteIcon from '@/assets/PlanetChatRoom/invite_icon.png';
import gdp_icon from '@/assets/PlanetChatRoom/gdp_icon.png';
import password_lock_icon from '@/assets/PlanetChatRoom/password_lock_icon.png';
import SpatialDetail_SoundOff_btn from '@/assets/PlanetChatRoom/SpatialDetail_SoundOff_btn.png'; // 麦克风
import SpatialDetail_SoundOff_btn_Forbidden from '@/assets/PlanetChatRoom/SpatialDetail_SoundOff_btn_Forbidden.png'; // 麦克风关闭状态
import SpatialDetail_Camera_btn from '@/assets/PlanetChatRoom/SpatialDetail_Camera_btn.png'; // 摄像头
import SpatialDetail_Forbidden from '@/assets/PlanetChatRoom/SpatialDetail_Forbidden.png'; // 摄像头关闭状态
import share_ppt_icon from '@/assets/PlanetChatRoom/share_ppt_icon.png'; // 分享课件
import HorizontalLiveRoom_shared_screen_active_WhiteBoardButton
  from '@/assets/PlanetChatRoom/HorizontalLiveRoom_shared_screen_active_WhiteBoardButton.png'; // 分享课件中
import manage_members_icon from '@/assets/PlanetChatRoom/manage_members_icon.png'; // 管理成员
import manage_members_lock_icon from '@/assets/PlanetChatRoom/manage_members_lock_icon.png'; // 管理成员锁定状态
import SpatialDetail_record_btn_luzhi from '@/assets/PlanetChatRoom/SpatialDetail_record_btn_luzhi.png'; // 录制
import SpatialDetail_record_btn_luzhi_StartRecording
  from '@/assets/PlanetChatRoom/SpatialDetail_record_btn_luzhi_StartRecording.png'; // 录制中
import more_seeting_icon from '@/assets/PlanetChatRoom/more_seeting_icon.png'; // 更多设置
import im_input_open from '@/assets/PlanetChatRoom/im_input_open.png'; // 评论区打开状态
import im_input_close from '@/assets/PlanetChatRoom/im_input_close.png'; // 评论区关闭状态
import HorizontalLiveRoom_title_clockin_Icon from '@/assets/PlanetChatRoom/HorizontalLiveRoom_title_clockin_Icon.png'; // 打卡
import HorizontalLiveRoom_title_collect_Icon from '@/assets/PlanetChatRoom/HorizontalLiveRoom_title_collect_Icon.png'; // 收藏              // 打开评论区
import call_icon1 from '@/assets/PlanetChatRoom/call_icon1.png'; // 打call，1
import apply_speak_status_icon from '@/assets/PlanetChatRoom/apply_speak_status_icon.png'; // 申请连麦状态小手
import mike_spaking_icon from '@/assets/PlanetChatRoom/mike_spaking_icon.png'; // 讲话中麦克风按钮
import SpatialDetail_PictureInPicture_btn from '@/assets/PlanetChatRoom/SpatialDetail_PictureInPicture_btn.png'; // 小窗播放按钮
import SpatialDetail_ClockIn_btn_complete from '@/assets/PlanetChatRoom/SpatialDetail_ClockIn_btn_complete.png'; // 已打卡状态
import SpatialDetail_Collect_btn_Open from '@/assets/PlanetChatRoom/SpatialDetail_Collect_btn_Open.png'; // 已收藏状态
import CallBox from '@/components/CallBox'; // 打call组件
import {BULLET_SCREEN, HAND_UP, NO_PW_APPLY, SEND_CALL, SIGN_IN,} from '@/app/config';
import {
  getCurrentUserInfoVolumeList,
  getHandUpRemoteStreamList,
  getUserCameraRemoteStreamList,
} from '@/utils/utilsByTRTC';

type propsType = {
  global: any; // 项目共享modals
  onRefByViewportWrap: any; // 暴露给父组件的方法
  sendMessageByIm: any; // 发送im实时通信消息
  localStreamConfig: any; // 本地流配置
  remoteStreamConfigList: any; // 远端流配置列表
  RTC: any; // RTC实例
  shareRTC: any; // 分享RTC实例
  isJoined: boolean; // 是否加入房间
  isPublished: boolean; // 是否发布流
  handleJoin: any; // onClick进入房间
  handleLeave: any; // onClick离开房间
  onChange: any; // 改变直播状态 分享屏幕
  spaceId: any; // 直播间id
  openCloseHandUp: any; // 打开/关闭连麦列表
  liveRecord: any; // 打卡录制视频
  getGuestList: any; // 获取嘉宾列表
  getSpaceInfo: any; // 获取直播间信息
  onClickLianMai: any; // 点击申请连麦
  changeUrlParams: any; // 改变url参数
  elapsedTime: any; // 正在录播时长
  shareOnClick: any; // 分享
  onClickBack: any; // 返回按钮
  isHorizontalLive: any; // 是否全屏
};

let timer1 = null; // 打call左边横幅的定时器
let timer2 = null; // 键盘弹出的定时器

const Index: React.FC<propsType> = (props) => {
  // 暴露给父组件的方法
  useImperativeHandle(props.viewRef, () => {
    return {
      resetCommentInput: resetCommentInput,
    };
  });

  // props传递过来的数据
  const {
    dispatch,
    PlanetChatRoom,
    meetingDurationTime,
    elapsedTime,
    onClickBackIcon,
    onClickInviteIcon,
    onClickNavBarTitle,
    onClickEndBtn,
    onScrollCommentList,
    commentListIsInBottomNow,
    onEnterPressCommentInput,
    onClickSignInBtn,
    onClickCollectBtn,
    onClickCallBtn,
    isJoined,
    isCommentAndDanmuOpen,
    handUpStatusTypeEnter,
    onClickDanmuIcon,
    isPublished,
    localStreamConfig,
    onClickApplySpeakBtn,
    onClickMikeBtn,
    onClickCameraBtn,
    onClickShareFileBtn,
    onClickMembersBtn,
    onClickRecordBtn,
    onClickMoreSettingBtn,
    onClickShowSmallWindowBtn,
    onClickTopMessageNotify,
    onClickHandUpBtn,
    onClickForceMikeOffBtn,
    onClickEndMikeOffBtn,
    onClickShareFileNextIcon,
    onClickShareFilePrevIcon,
    onClickShareFileListBtn,
    isHiddenControlArea,
    remoteStreamConfigList,
    isNotVideoOrScreen,
  } = props;

  // model中的数据
  const {
    SpaceInfo,
    currentUserType,
    msgListBySENDAPPLAUSE,
    isShowTopMessageNotify,
    topMessageNotifyType,
    applyAdmissionList,
    handUpList,
    isOpenTEduBoard,
    sendCallData,
    currentLiveUserList,
  } = PlanetChatRoom || {};

  const {
    name: nameBySpaceInfo,
    status: statusBySpaceInfo,
    isSignIn,
    isCollect,
    handUpType,
    recordType,
    handUpStatusType,
    pv,
    gdp,
    password,
    isSpectatorUse,
    isShowPassword,
    isHavCourseware,
  } = SpaceInfo || {};

  const {fromBrowser} = history.location.query || {}; // 路由参数，从浏览器点击app中打开进入app时，fromBrowser=1
  const inputRef = useRef(null); // 弹幕数据框ref
  const commentListRef = useRef(null); // 聊天区评论list ref
  const [isShowCommentInput, setIsShowCommentInput] = useState(false); // 是否显示真实的输入框
  let [currentUserInfoVolumeStrList, setCurrentUserInfoVolumeStrList] = useState(null); // 当前发言人展示
  let setCurrentUserInfoVolumeStrListDeb = useDebounce(setCurrentUserInfoVolumeStrList, 2000);
  // 远端流中摄像头流
  const userCameraRemoteStreamList = getUserCameraRemoteStreamList(
    SpaceInfo,
    remoteStreamConfigList,
  );
  // 获取连麦人画面信息
  const handUpRemoteStreamList = getHandUpRemoteStreamList(
    SpaceInfo,
    handUpList,
    userCameraRemoteStreamList,
  );
  // 获取流中当前发言人
  let userStreamList =
    Array.isArray(userCameraRemoteStreamList) && localStreamConfig
      ? [...userCameraRemoteStreamList, localStreamConfig]
      : [];
  // !showStream.mutedAudio && showStream.audioVolume > 0
  let AudioByUserStreamList = userStreamList.filter((item) => {
    return (item.hasAudio && !item.mutedAudio) || !item.hasAudio;
  });
  // 获取当前发言人
  let currentUserInfoVolumeList = getCurrentUserInfoVolumeList({
    userCameraRemoteStreamList,
    localStreamConfig,
    currentLiveUserList,
    handUpList,
  });

  // 展示当前发言人
  useEffect(() => {
    if (Array.isArray(currentUserInfoVolumeList) && currentUserInfoVolumeList.length != 0) {
      let resCurrentUserInfoVolumeList = currentUserInfoVolumeList.map((item) => {
        return item.name;
      });
      setCurrentUserInfoVolumeStrList(resCurrentUserInfoVolumeList);
    } else {
      setCurrentUserInfoVolumeStrListDeb(null);
    }
  }, [Array.isArray(currentUserInfoVolumeList) && currentUserInfoVolumeList.length]);

  // 评论列表变化，将消息滚动到底部
  useEffect(() => {
    if (
      isCommentAndDanmuOpen &&
      commentListIsInBottomNow &&
      msgListBySENDAPPLAUSE &&
      msgListBySENDAPPLAUSE.length > 0
    ) {
      commentListScrollToBottom();
    }
  }, [msgListBySENDAPPLAUSE]);

  // 打开弹幕时，聊天区滚动到底部
  useEffect(() => {
    if (isCommentAndDanmuOpen) {
      commentListScrollToBottom();
    }
  }, [isCommentAndDanmuOpen]);

  useEffect(() => {
    clearTimeout(timer1);
    timer1 = setTimeout(() => {
      dispatch({
        type: 'PlanetChatRoom/setState',
        payload: {
          sendCallData: null,
        },
      });
    }, 3000);
  }, [sendCallData]);

  // 滚动到底部
  const commentListScrollToBottom = () => {
    if (commentListRef && commentListRef.current) {
      const height = commentListRef.current.scrollHeight;
      commentListRef.current.scrollTop = height;
    }
  };

  // 重置输入框
  const resetCommentInput = () => {
    // inputRef.current.clear()
    // inputRef.current.blur()
  };

  // 点击顶部返回箭头
  const onClickBackIconFun = (e) => {
    // 点击后阻止事件冒泡,防止触发父元素的点击事件
    e.stopPropagation();
    onClickBackIcon();
  };

  // 点击顶部邀请图标
  const onClickInviteIconFun = (e) => {
    // 点击后阻止事件冒泡,防止触发父元素的点击事件
    e.stopPropagation();
    onClickInviteIcon();
  };

  // 点击顶部空间标题
  const onClickNavBarTitleFun = (e) => {
    // 点击后阻止事件冒泡,防止触发父元素的点击事件
    e.stopPropagation();
    onClickNavBarTitle();
  };

  // 点击顶部结束按钮
  const onClickEndBtnFun = (e) => {
    // 点击后阻止事件冒泡,防止触发父元素的点击事件
    e.stopPropagation();
    onClickEndBtn();
  };

  // 点击弹幕和评论开关icon
  const onClickDanmuIconFun = (e) => {
    // 点击后阻止事件冒泡,防止触发父元素的点击事件
    e.stopPropagation();
    onClickDanmuIcon();
  };

  // 点击打卡按钮
  const onClickSignInBtnFun = (e) => {
    // 点击后阻止事件冒泡,防止触发父元素的点击事件
    e.stopPropagation();
    onClickSignInBtn();
  };

  // 点击收藏按钮
  const onClickCollectBtnFun = (e) => {
    // 点击后阻止事件冒泡,防止触发父元素的点击事件
    e.stopPropagation();
    onClickCollectBtn();
  };

  // 点击申请发言按钮
  const onClickApplySpeakBtnFun = (e) => {
    // 点击后阻止事件冒泡,防止触发父元素的点击事件
    e.stopPropagation();
    onClickApplySpeakBtn();
  };

  // 点击底部麦克风按钮
  const onClickMikeBtnFun = (e) => {
    // 点击后阻止事件冒泡,防止触发父元素的点击事件
    e.stopPropagation();
    onClickMikeBtn();
  };

  // 点击底部摄像头按钮
  const onClickCameraBtnFun = (e) => {
    // 点击后阻止事件冒泡,防止触发父元素的点击事件
    e.stopPropagation();
    onClickCameraBtn();
  };

  // 点击底部分享课件按钮
  const onClickShareFileBtnFun = (e) => {
    // 点击后阻止事件冒泡,防止触发父元素的点击事件
    e.stopPropagation();
    onClickShareFileBtn();
  };

  // 点击底部成员按钮
  const onClickMembersBtnFun = (e) => {
    // 点击后阻止事件冒泡,防止触发父元素的点击事件
    e.stopPropagation();
    onClickMembersBtn();
  };

  // 点击底部录制按钮
  const onClickRecordBtnFun = (e) => {
    // 点击后阻止事件冒泡,防止触发父元素的点击事件
    e.stopPropagation();
    onClickRecordBtn();
  };

  // 点击底部更多按钮
  const onClickMoreSettingBtnFun = (e) => {
    // 点击后阻止事件冒泡,防止触发父元素的点击事件
    e.stopPropagation();
    onClickMoreSettingBtn();
  };

  // 点击底部小窗播放按钮
  const onClickShowSmallWindowBtnFun = (e) => {
    // 点击后阻止事件冒泡,防止触发父元素的点击事件
    e.stopPropagation();
    onClickShowSmallWindowBtn();
  };

  // 点击顶部消息通知
  const onClickTopMessageNotifyFun = (e) => {
    // 点击后阻止事件冒泡,防止触发父元素的点击事件
    e.stopPropagation();
    onClickTopMessageNotify();
  };

  // 点击当前有人正在申请发言时出现的小手图标（主持人）
  const onClickHandUpBtnFun = (e) => {
    // 点击后阻止事件冒泡,防止触发父元素的点击事件
    e.stopPropagation();
    onClickHandUpBtn();
  };

  // 主持人点击强制下麦悬浮按钮
  const onClickForceMikeOffBtnFun = (e, item) => {
    // 点击后阻止事件冒泡,防止触发父元素的点击事件
    e.stopPropagation();
    onClickForceMikeOffBtn(item);
  };

  // 观众自己点击结束连麦悬浮按钮
  const onClickEndMikeOffBtnFun = (e) => {
    // 点击后阻止事件冒泡,防止触发父元素的点击事件
    e.stopPropagation();
    onClickEndMikeOffBtn();
  };

  // 点击分享课件时的悬浮控制器，下一页icon
  const onClickShareFileNextIconFun = (e) => {
    // 点击后阻止事件冒泡,防止触发父元素的点击事件
    e.stopPropagation();
    onClickShareFileNextIcon();
  };

  // 点击分享课件时的悬浮控制器，上一页icon
  const onClickShareFilePrevIconFun = (e) => {
    // 点击后阻止事件冒泡,防止触发父元素的点击事件
    e.stopPropagation();
    onClickShareFilePrevIcon();
  };

  // 点击分享课件时的悬浮控制器，课件列表按钮
  const onClickShareFileListBtnFun = (e) => {
    // 点击后阻止事件冒泡,防止触发父元素的点击事件
    e.stopPropagation();
    onClickShareFileListBtn();
  };

  // 关闭蒙层
  const onMaskClick = () => {
    setIsShowCommentInput(false);
  };

  // 点击假的输入框
  const onClickInputBox = () => {
    setIsShowCommentInput(true);
  };

  // 输入框按下回车
  const onEnterPressFun = (e) => {
    const value = e.target.value;
    onEnterPressCommentInput(value);
    setIsShowCommentInput(false);
  };

  // 点击输入框发送按钮
  const onClickSendFun = () => {
    const value = (inputRef && inputRef.current?.nativeElement.value) || '';
    onEnterPressCommentInput(value);
    setIsShowCommentInput(false);
  };

  return (
    <>
      {/* 标题导航栏 */}
      {!isHiddenControlArea && (
        <div className={styles.header_wrap}>
          <div className={styles.header_left}>
            {/* 返回icon */}
            {
              /* FRIDAY app */
              fromBrowser == '1' && getOperatingEnv() == '5' ? (
                <span style={{width: 24, height: 24, marginRight: 6}}></span>
              ) : (
                <img
                  src={SpatialDetail_Return_icon_btn}
                  width={24}
                  height={24}
                  onClick={onClickBackIconFun}
                  style={{marginRight: 6, padding: '0 6px'}}
                  alt=""
                />
              )
            }
            {/* 邀请icon */}
            <img
              src={inviteIcon}
              width={24}
              height={24}
              onClick={onClickInviteIconFun}
              style={{marginRight: 16}}
              alt=""
            />
            <div className={styles.meeting_info_wrap}>
              {/* 会议标题，点击出详情弹窗 */}
              <div className={styles.meeting_name_wrap} onClick={onClickNavBarTitleFun}>
                <div className={styles.meeting_name}>{nameBySpaceInfo}</div>
                <DownFill color="#fff" fontSize={8}/>
              </div>
              <div className={styles.meeting_other_wrap}>
                <img src={gdp_icon} width={12} height={12} style={{marginRight: 3}} alt=""/>
                <span className={styles.gdp}>
                  {gdp}GDP｜{pv}人观看
                </span>
                {/* 会议已进行时间 */}
                <span className={styles.time}>
                  {statusBySpaceInfo != 3 && formatTimeBySeconds(meetingDurationTime)}
                </span>
              </div>
            </div>
          </div>
          <div className={styles.header_right}>
            {/* 密码 */}
            <div className={styles.meeting_password_wrap}>
              {isJoined && password && (isShowPassword == 1 || currentUserType == 1) && (
                <>
                  <img
                    src={password_lock_icon}
                    width={12}
                    height={12}
                    style={{marginRight: 2}}
                    alt=""
                  />
                  <span>{password}</span>
                </>
              )}
            </div>
            {/* 结束按钮 */}
            {isJoined && statusBySpaceInfo != 3 && (
              <div className={styles.meeting_end_btn} onClick={onClickEndBtnFun}>
                {currentUserType == 1 ? '结束' : '离开'}
              </div>
            )}
          </div>
        </div>
      )}

      {/* 圆圈背景图形 */}
      <div className={styles.bg_circle_wrap}></div>

      {/* 聊天区 */}
      {isCommentAndDanmuOpen && (
        <div
          className={classNames(styles.im_message_list_wrap, {
            [styles.in_black]: isNotVideoOrScreen, // 在深色背景里
            [styles.in_white]: !isNotVideoOrScreen, // 在浅色背景里
          })}
          ref={commentListRef}
          onScroll={onScrollCommentList}
        >
          {msgListBySENDAPPLAUSE &&
            msgListBySENDAPPLAUSE.length > 0 &&
            msgListBySENDAPPLAUSE.map((item) => {
              if (
                item.msgType != SIGN_IN &&
                item.msgType != BULLET_SCREEN &&
                item.msgType != SEND_CALL
              ) {
                return null;
              }

              return (
                <div className={styles.im_message_item}>
                  {/* 去掉大V icon
                    item.isExperts == 1 && <img src={im_user_icon1} width={12} height={12} style={{flexShrink: 0, marginTop: 3}} alt=""/>
                  */}
                  <div>
                    {item.name}：
                    {item.msgType == SIGN_IN ? (
                      '已打卡'
                    ) : item.msgType == BULLET_SCREEN ? (
                      item.bs
                    ) : item.msgType == SEND_CALL ? (
                      <>
                        助力
                        <img src={call_icon1} width={16} height={16} alt=""/>×
                        {item.msgGroupCount ? item.msgGroupCount : item.cnt}
                      </>
                    ) : (
                      ''
                    )}
                  </div>
                </div>
              );
            })}
        </div>
      )}

      {/* 聊天输入框（假输入框）和右侧按钮 */}
      {!isShowCommentInput && (
        <div
          className={classNames(styles.bottom_wrap, {
            [styles.in_black]: isNotVideoOrScreen, // 在深色背景里
            [styles.in_white]: !isNotVideoOrScreen, // 在浅色背景里
          })}
        >
          {/* 输入框 */}
          <div className={styles.bottom_input_wrap}>
            <img
              src={isCommentAndDanmuOpen ? im_input_open : im_input_close}
              width={18}
              height={18}
              onClick={onClickDanmuIconFun}
              style={{padding: 2, marginTop: 2}}
              alt=""
            />
            <div className={styles.input_box} onClick={onClickInputBox}>
              说点什么...
            </div>
          </div>
          <div className={styles.bottom_btn_wrap}>
            {/* 打卡 */}
            <div className={styles.bottom_btn_item} onClick={onClickSignInBtnFun}>
              <img
                src={
                  isSignIn || currentUserType == 1
                    ? SpatialDetail_ClockIn_btn_complete
                    : HorizontalLiveRoom_title_clockin_Icon
                }
                width={18}
                height={18}
                alt=""
              />
            </div>
            {/* 收藏 */}
            <div className={styles.bottom_btn_item} onClick={onClickCollectBtnFun}>
              <img
                src={
                  isCollect ? SpatialDetail_Collect_btn_Open : HorizontalLiveRoom_title_collect_Icon
                }
                width={18}
                height={18}
                alt=""
              />
            </div>
            {/* 打call */}
            {/*<div className={styles.bottom_call_btn} onClick={onClickCallBtnFun}>*/}
            {/*  <img src={call_icon1} width={34} height={34} alt=""/>*/}
            {/*</div>*/}

            {/* 打call组件 */}
            <CallBox
              onClickCallBtn={onClickCallBtn}
              BoxStyle={{top: -143}}
              BtnStyle={{}}
              TextStyle={{}}
            />
          </div>
        </div>
      )}

      {/* 输入框蒙层 */}
      <Mask
        visible={isShowCommentInput}
        opacity={0}
        getContainer={document.body}
        onMaskClick={onMaskClick}
        style={{
          '--z-index': 930,
        }}
      />

      {/* 真实的输入框 */}
      {isShowCommentInput && (
        <div className={styles.real_input_wrap}>
          <Input
            ref={inputRef}
            placeholder="说点什么..."
            autoFocus
            onEnterPress={onEnterPressFun}
            // onBlur={() => {
            //   // if (isIOS()) { return null; }
            //   setTimeout(() => {
            //     let DerailWarp = document.querySelector('#warp_content');
            //     DerailWarp.style.height = `100%`;
            //   }, 300);
            //   if (timer2) {
            //     clearTimeout(timer2);
            //   }
            // }}
            // onFocus={() => {
            //   // 当弹出软键盘后将弹幕区域高度去除用于弹出软键盘后
            //   let DerailWarp = document.querySelector('#warp_content')
            //   let windowHeight = DerailWarp.clientHeight; // 原始高度
            //   // document.documentElement.scrollTop = 0;
            //   // DerailWarp.scrollTop = 0;
            //   if (isIOS()) {
            //     // alert(11)
            //     // ios 高度
            //     timer2 = setTimeout(() => {
            //       if (DerailWarp && DerailWarp.clientHeight) {
            //         const originHeight = document.documentElement.clientHeight; // document高度
            //         const newHeight = window.visualViewport.height; // 视口高度
            //         // 获取键盘高度-keyboardHeight
            //         const keyboardHeight = originHeight - newHeight;
            //         let windowHeightBykeyboardOpen = windowHeight - keyboardHeight;
            //         // message.info(`windowHeightBykeyboardOpen:${windowHeightBykeyboardOpen}, keyboardHeight:${keyboardHeight}`);
            //         if (keyboardHeight > 0) {
            //
            //           DerailWarp.style.height = `${windowHeightBykeyboardOpen}px`;
            //           DerailWarp.scrollTo({ top: `0px` });
            //           document.documentElement.scrollTop = 0;
            //           window.scrollTop = 0
            //         }
            //       }
            //     },500);
            //   } else {
            //     // android 当键盘弹出时视口高度会变小
            //     timer2 = setTimeout(() => {
            //       if (DerailWarp && DerailWarp.clientHeight) {
            //         if (window.innerHeight != windowHeight) {
            //           DerailWarp.style.height = `${window.innerHeight}px`;
            //           DerailWarp.scrollTo({ top: `0px` });
            //           document.documentElement.scrollTop = 0;
            //           window.scrollTop = 0
            //         }
            //       }
            //     }, 500);
            //   }
            // }}
          />
          <div className={styles.real_input_btn} onClick={onClickSendFun}>
            发送
          </div>
        </div>
      )}

      {/* 底部操作按钮区 */}
      {isJoined && !isHiddenControlArea && (!!localStreamConfig || currentUserType == 3) && (
        <div className={styles.footer_btn_wrap}>
          {/*身份 currentUserType    1 主持人，2 参会人，3 观众 */}
          {/*举手类型 handUpType    0:开启 1:关闭*/}
          {/* 申请连麦 handUpStatusType 申请连麦状态类型：0申请连麦中 1接受连麦中，默认null*/}

          {/* 申请发言按钮（观众） */}
          {currentUserType == 3 && isJoined && !isPublished && handUpStatusType != 1 && (
            <div
              className={styles.footer_btn_item_wrap}
              onClick={onClickApplySpeakBtnFun}
              style={{opacity: handUpType == 1 ? 1 : 0.5}}
            >
              <img src={apply_speak_status_icon} width={20} height={20} alt=""/>
              {handUpType != 1 && <div>不允许申请发言</div>}
              {handUpType == 1 && (
                <div>
                  {handUpStatusType == null && '申请发言'}
                  {handUpStatusType == 0 && !handUpStatusTypeEnter && '等待中...'}
                  {handUpStatusType == 0 && handUpStatusTypeEnter && '取消发言'}
                </div>
              )}
            </div>
          )}

          {/* 麦克风静音、解除静音按钮（主持人、参会人、观众） */}
          {(currentUserType == 1 ||
              currentUserType == 2 ||
              (currentUserType == 3 && handUpStatusType == 1 && isPublished)) &&
            isJoined && (
              <div className={styles.footer_btn_item_wrap} onClick={onClickMikeBtnFun}>
                <img
                  src={
                    localStreamConfig && !!localStreamConfig.mutedAudio
                      ? SpatialDetail_SoundOff_btn_Forbidden
                      : SpatialDetail_SoundOff_btn
                  }
                  width={20}
                  height={20}
                  alt=""
                />
                <div>
                  {localStreamConfig && !!localStreamConfig.mutedAudio ? '解除静音' : '静音'}
                </div>
              </div>
            )}

          {(currentUserType == 1 ||
              currentUserType == 2 ||
              (currentUserType == 3 && handUpStatusType == 1 && isPublished)) &&
            isJoined && (
              <>
                {/* 摄像头开启、关闭按钮（主持人、参会人） */}
                <div className={styles.footer_btn_item_wrap} onClick={onClickCameraBtnFun}>
                  <img
                    src={
                      localStreamConfig && !!localStreamConfig.mutedVideo
                        ? SpatialDetail_Forbidden
                        : SpatialDetail_Camera_btn
                    }
                    width={20}
                    height={20}
                    alt=""
                  />
                  <div>
                    {localStreamConfig && !!localStreamConfig.mutedVideo ? '开启视频' : '关闭视频'}
                  </div>
                </div>
              </>
            )}

          {(currentUserType == 1 || currentUserType == 2) && isJoined && (
            <>
              {/* 分享课件按钮（主持人、参会人） */}
              {isHavCourseware == 1 && (
                <div className={styles.footer_btn_item_wrap} onClick={onClickShareFileBtnFun}>
                  <img
                    src={
                      !!isOpenTEduBoard
                        ? HorizontalLiveRoom_shared_screen_active_WhiteBoardButton
                        : share_ppt_icon
                    }
                    width={20}
                    height={20}
                    alt=""
                  />
                  <div>{!!isOpenTEduBoard ? '停止分享' : '分享课件'}</div>
                </div>
              )}
            </>
          )}

          {/* 管理成员/成员按钮（主持人、参会人、观众） */}
          <div className={styles.footer_btn_item_wrap} onClick={onClickMembersBtnFun}>
            <img
              src={isSpectatorUse == 0 ? manage_members_lock_icon : manage_members_icon}
              width={20}
              height={20}
              alt=""
            />
            <div>{currentUserType == 1 && '管理'}成员</div>
            {currentUserType == 1 &&
            applyAdmissionList &&
            applyAdmissionList.filter((item) => item.isAgree == 0).length > 0 ? (
              <div className={styles.badge}>
                {applyAdmissionList.filter((item) => item.isAgree == 0).length}
              </div>
            ) : null}
          </div>

          {/* 录制、申请录制按钮（主持人、参会人、观众） */}
          <div className={styles.footer_btn_item_wrap} onClick={onClickRecordBtnFun}>
            <img
              src={
                recordType == 1
                  ? SpatialDetail_record_btn_luzhi_StartRecording
                  : SpatialDetail_record_btn_luzhi
              }
              width={20}
              height={20}
              alt=""
            />
            <div>
              {recordType == 1
                ? formatTimeBySeconds(elapsedTime)
                : `${currentUserType != 1 ? '申请' : ''}录制`}
            </div>
          </div>

          {/* 更多设置按钮（主持人） */}
          {currentUserType == 1 && (
            <div className={styles.footer_btn_item_wrap} onClick={onClickMoreSettingBtnFun}>
              <img src={more_seeting_icon} width={20} height={20} alt=""/>
              <div>更多</div>
            </div>
          )}

          {/* 小窗播放（参会人、观众） */}
          {(currentUserType == 2 || currentUserType == 3) && (
            <div className={styles.footer_btn_item_wrap} onClick={onClickShowSmallWindowBtnFun}>
              <img src={SpatialDetail_PictureInPicture_btn} width={20} height={20} alt=""/>
              <div>小窗播放</div>
            </div>
          )}
        </div>
      )}

      {/* 顶部消息通知-有申请进入和申请发言时提示（主持人） */}
      {isShowTopMessageNotify && (
        <div
          className={classNames(styles.message_notify_wrap, {
            [styles.in_black]: isNotVideoOrScreen, // 在深色背景里
            [styles.in_white]: !isNotVideoOrScreen, // 在浅色背景里
          })}
        >
          <div className={styles.message_content} onClick={onClickTopMessageNotifyFun}>
            {topMessageNotifyType == HAND_UP && (
              <>
                当前已有
                <span>
                  {(handUpList && handUpList.filter((item) => item.statusType == 0).length) || 0}
                </span>
                位观众在申请发言
              </>
            )}
            {topMessageNotifyType == NO_PW_APPLY && (
              <>
                当前等候室已有
                <span>
                  {(applyAdmissionList &&
                      applyAdmissionList.filter((item) => item.isAgree == 0).length) ||
                    0}
                </span>
                人在等候
              </>
            )}
          </div>
        </div>
      )}

      {/* 申请连麦小手-点击查看申请列表（主持人） */}
      {currentUserType == 1 &&
        handUpList &&
        handUpList.filter((item) => item.statusType == 0).length > 0 && (
          <div
            className={classNames(styles.apply_speak_status_btn, {
              [styles.in_black]: isNotVideoOrScreen, // 在深色背景里
              [styles.in_white]: !isNotVideoOrScreen, // 在浅色背景里
            })}
            onClick={onClickHandUpBtnFun}
          >
            <img src={apply_speak_status_icon} width={20} height={20} alt=""/>
            <span className={styles.apply_speak_count}>
              {handUpList.filter((item) => item.statusType == 0).length}
            </span>
          </div>
        )}

      {/* 主持人视角，强制下麦悬浮按钮 */}
      {currentUserType == 1 &&
        handUpList &&
        handUpList.length > 0 &&
        handUpList.find((item) => item.statusType == 1) && (
          <div className={styles.forced_mute_btn_wrap}>
            <div className={styles.user_name}>
              {handUpList.find((item) => item.statusType == 1).name}
            </div>

            {Array.isArray(handUpRemoteStreamList) &&
              handUpRemoteStreamList.length > 0 &&
              handUpRemoteStreamList[0] && (
                <>
                  {/* 静音 */}
                  {!!handUpRemoteStreamList[0].mutedAudio ? (
                    <img
                      src={SpatialDetail_SoundOff_btn_Forbidden}
                      width={12}
                      height={12}
                      style={{marginRight: 6}}
                      alt=""
                    />
                  ) : (
                    <>
                      {/* 正在发言 */}
                      {!!handUpRemoteStreamList[0].audioVolume > 0 ? (
                        <img
                          src={mike_spaking_icon}
                          width={12}
                          height={12}
                          style={{marginRight: 6}}
                          alt=""
                        />
                      ) : (
                        <img
                          src={SpatialDetail_SoundOff_btn}
                          width={12}
                          height={12}
                          style={{marginRight: 6}}
                          alt=""
                        />
                      )}
                    </>
                  )}
                </>
              )}
            <div
              className={styles.mute_btn}
              onClick={(e) =>
                onClickForceMikeOffBtnFun(
                  e,
                  handUpList.find((item) => item.statusType == 1),
                )
              }
            >
              强制下麦
            </div>
          </div>
        )}

      {/* 观众视角，结束发言悬浮按钮 */}
      {currentUserType == 3 && handUpStatusType == 1 && isJoined && isPublished && (
        <div className={styles.forced_mute_btn_wrap}>
          <div className={styles.user_name}>
            {handUpList.find((item) => item.statusType == 1)?.name}
          </div>
          {localStreamConfig && (
            <>
              {/* 静音 */}
              {!!localStreamConfig.mutedAudio ? (
                <img
                  src={SpatialDetail_SoundOff_btn_Forbidden}
                  width={12}
                  height={12}
                  style={{marginRight: 6}}
                  alt=""
                />
              ) : (
                <>
                  {/* 正在发言 */}
                  {!!localStreamConfig.audioVolume > 0 ? (
                    <img
                      src={mike_spaking_icon}
                      width={12}
                      height={12}
                      style={{marginRight: 6}}
                      alt=""
                    />
                  ) : (
                    <img
                      src={SpatialDetail_SoundOff_btn}
                      width={12}
                      height={12}
                      style={{marginRight: 6}}
                      alt=""
                    />
                  )}
                </>
              )}
            </>
          )}
          <div className={styles.mute_btn} onClick={onClickEndMikeOffBtnFun}>
            结束发言
          </div>
        </div>
      )}

      {/* 打call连击消息 */}
      {sendCallData && (
        <div className={styles.call_group_msg_wrap}>
          {sendCallData.name}助力
          <img src={call_icon1} width={20} height={20} alt=""/>x{sendCallData.msgGroupCount}
        </div>
      )}

      {/* 正在讲话悬浮信息 */}
      {AudioByUserStreamList.length > 0 && statusBySpaceInfo == 1 && (
        <div className={styles.now_speaking_user_wrap}>
          {Array.isArray(currentUserInfoVolumeStrList) &&
          currentUserInfoVolumeStrList.length > 0 ? (
            <img src={mike_spaking_icon} width={12} height={12} style={{flexShrink: 0}} alt=""/>
          ) : (
            <img
              src={SpatialDetail_SoundOff_btn}
              width={12}
              height={12}
              style={{flexShrink: 0}}
              alt=""
            />
          )}
          <div className={styles.separator_line}></div>
          <div className={styles.speaking_user_content}>
            正在讲话：
            {Array.isArray(currentUserInfoVolumeStrList) &&
              currentUserInfoVolumeStrList.map((item, index) => {
                return (
                  <div className={styles.speaking_user_item} key={index}>
                    {item}
                  </div>
                );
              })}
          </div>
        </div>
      )}
    </>
  );
};

export default connect(({global, PlanetChatRoom, loading}: any) => ({
  global,
  PlanetChatRoom,
  loading,
}))(Index);
