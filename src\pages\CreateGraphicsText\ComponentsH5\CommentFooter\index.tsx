import React, { useState, useEffect ,useRef ,useCallback} from 'react';
import { connect, history } from 'umi';
import classNames from 'classnames'
import { getIsIniPhoneAndWeixin } from '@/utils/utils'
import { TextArea, Mask, Toast } from 'antd-mobile'
import styles from './index.less';
import {stringify} from "qs";
import {message} from "antd";
import Emoji from "@/pages/CreateGraphicsText/Quill/ToolbarMobile/Emoji";


interface PropsType {
  imageTextId:any,  // 图文id
  imgTextInfo:any,  // 图文详情
  onPost: any,  // 回调
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  let {
    imageTextId,
    imgTextInfo,
    dispatch
  } = props || {}
  imgTextInfo = imgTextInfo || {};  // 图文详情
  const {
    id,                       // 0 number, 非必须, 主键ID
    imageType,                // 0 number, 非必须, 图文类型
    isForward,                // null number, 非必须, 是否转发
    forwardSquareRecommendDto,// null, 非必须, 转发图文信息
    isSpotLike,               // null number, 非必须, 是否点赞
    forwardCount,             // null number, 非必须, 转发数量
  } = imgTextInfo || {};

  const [ likeStatus, setLikeStatus ] = useState(isSpotLike == 1);
  const [ spotLikeCount,setSpotLikeCount ] = useState(isSpotLike == 1 ? imgTextInfo.spotLikeOtherCount + 1 : imgTextInfo.spotLikeOtherCount);
  const [ commentsCount,setCommentsCount ] = useState(imgTextInfo.commentsCount);
  const [ isShowCommentInput, setIsShowCommentInput] = useState(false)
  const [ textAreaValue,setTextAreaValue ] = useState(null)
  const [ isShowEmoji,setIsShowEmoji ] = useState(null)

  useEffect(() => {
    if(imageTextId){
      setSpotLikeCount(isSpotLike == 1 ? imgTextInfo.spotLikeOtherCount + 1 : imgTextInfo.spotLikeOtherCount);
      setCommentsCount(imgTextInfo.commentsCount);
      setLikeStatus(isSpotLike == 1)
    }
  },[imageTextId,imgTextInfo])

  useEffect(()=>{
    if(!isShowCommentInput){ setIsShowEmoji(false); }
  },[isShowCommentInput])


  // 点击评论
  const postComment = (item) => {
    // 没登录跳转登录页
    if (!localStorage.getItem('access_token')) {
      history.push({
        pathname: '/User/login',
        query: {
          redirectByPush: window.location.href,
        }
      })
      return
    }
    /* 设置评论输入框*/
    setTextAreaValue(null);
    setIsShowCommentInput(true)
  }

  // 点击转发
  const goToUrl = () => {
    // 没登录跳转登录页
    if (!localStorage.getItem('access_token')) {
      history.push({
        pathname: '/User/login',
        query: {
          redirectByPush: window.location.href,
        }
      })
      return
    }
    if(isForward == 1 && !forwardSquareRecommendDto){
      message.warning('原内容已下架,无法转发!')
      return;
    }
    // 跳转到转发页面
    history.push(`/CreateGraphicsText/CreateForward?${stringify({ forwardId: imageTextId, imageType: imageType })}`)
  }

  // 输入框
  const textAreaOnChange = (value) => {
    if(!(/^\s*$/.test(value))){
      setTextAreaValue(value)
    }else{
      setTextAreaValue(null);
      // Toast.show('请输入非空的评论内容')
    }
  }

  // 关闭输入框
  const closeMask = () => {
    setIsShowCommentInput(false)
  }

  // 发布
  const post = () => {
    if(!(/^\s*$/.test(textAreaValue))&&textAreaValue!=null&&textAreaValue.length>0){
      props.onPost({ commentInputInfo:imgTextInfo, value:textAreaValue});
      setTextAreaValue(null);
      setIsShowEmoji(null);
      setCommentsCount(commentsCount + 1);
    }else {
      Toast.show('请输入非空的评论内容')
    }
    // setIsShowCommentInput(false)
  }

  // 点赞事件
  const likeClickFn = useCallback(async () => {
    // 没登录跳转登录页
    if (!localStorage.getItem('access_token')) {
      history.push({
        pathname: '/User/login',
        query: {
          redirectByPush: window.location.href,
        }
      })
      return
    }
    await setLikeStatus(!likeStatus)
    let newspotLikeCount = !likeStatus? spotLikeCount + 1 : spotLikeCount - 1
    await setSpotLikeCount(newspotLikeCount)
    let DataByImageTextLikeOrCancel = await dispatch({
      type: 'recommended/imageTextLikeOrCancel',
      payload: {
        imageTextId: id,           // ID
        status: !likeStatus? 1 : 0, // 状态
      }
    })
  }, [likeStatus,spotLikeCount])

  const itemOnClick = (value,item) => {
    let textAreaValueStr = textAreaValue || '';
    setTextAreaValue(textAreaValueStr + item);
  }

  return(
    <div className={styles.fixed_footer_wrap} style={getIsIniPhoneAndWeixin() ? { paddingBottom: '34px' } : {}}>
      <Mask
        visible={isShowCommentInput}
        opacity={0}
        getContainer={document.body}
        onMaskClick={closeMask}
        style={{
          '--z-index': 997
        }}
      />
      {
        isShowCommentInput ?
          <div>
            <div className={styles.input_wrap}>
              <div className={styles.textarea_wrap}>
                <TextArea
                  value={textAreaValue}
                  placeholder="友善评论..."
                  autoSize={{maxRows: 6, minRows: 1}}
                  rows={1}
                  autoFocus
                  onChange={textAreaOnChange}
                />
                <i
                  className={styles.textarea_Icon}
                  onClick={()=>{ setIsShowEmoji(!isShowEmoji) }}
                ></i>
              </div>
              <div className={styles.btn_wrap}>
                {/*<i></i>*/}
                <span onClick={post}>发布</span>
              </div>
            </div>
            {!!isShowEmoji &&
              <Emoji itemOnClick={itemOnClick}/>
            }
          </div>
          :
          <div className={styles.footer_ul}>
            <div className={styles.footer_li} onClick={likeClickFn}>
              <i className={classNames({
                [styles.icon_like]:true,
                [styles.icon_like_active]:likeStatus
              })}></i>
              <span>{spotLikeCount}</span>
            </div>
            <div className={styles.footer_li} onClick={postComment}>
              <i className={styles.icon_comment}></i>
              <span>{commentsCount}</span>
            </div>
            <div className={styles.footer_li} onClick={goToUrl}>
              <i className={styles.icon_forward}></i>
              <span>{forwardCount}</span>
            </div>
          </div>
      }

    </div>
  )
}

export default connect(({ graphicsText,recommended, loading }: any) => ({ graphicsText, loading }))(Index)
