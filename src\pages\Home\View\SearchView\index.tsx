/**
 * @Description: 搜索组件
 */
import React  from 'react'
import { history } from 'umi'
import styles from './index.less'

interface PropsType {
}

const Index: React.FC<PropsType> = (props: PropsType) => {

  // 跳转搜索页
  const goToUrl = (e) => {
    e.stopPropagation()
    e.preventDefault()

    history.push('/Home/Search')

    // 解决ios中搜索页input框不能自动聚焦问题
    document.getElementById('input_ios_focus') && document.getElementById('input_ios_focus').focus()
  }

  return (
    <div className={styles.search_container}>
      <div className={styles.box} onClick={goToUrl}>
        <i></i>
        <div className={styles.right_box}>搜索直播、会议、王国、用户、病例</div>
      </div>
    </div>
  )
}

export default Index
