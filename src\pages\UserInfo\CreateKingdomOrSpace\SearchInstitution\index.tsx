/**
 * @Description: 搜索机构
 */
import React, { useState, useEffect, useCallback } from 'react';
import { connect } from 'umi';
import { Input } from 'antd-mobile';
import styles from './index.less';
import GoBackIcon from '@/assets/GlobalImg/go_back.png';
import SearchIcon from '@/assets/GlobalImg/search.png';
import noDataImg from '@/assets/GlobalImg/no_data.png';
import { Spin } from 'antd';
import type { ListDateItem } from '../SelectEnterprise';
import classNames from 'classnames';

type PropTypes = {
  userInfoStore: {
    enterpriseUserData: ListDateItem[];
    enterpriseUserTab: number;
  };
  [key: string]: any;
};

const Index: React.FC<PropTypes> = (props) => {
  const { userInfoStore, dispatch, goBack } = props;
  const { enterpriseUserData, enterpriseUserTab, enterpriseUserSelectData } = userInfoStore || {};
  const [itemList, setItemList] = useState<ListDateItem['orgList']>([]); // 列表展示数据
  const [searchString, setSearchString] = useState(''); // 输入框输入的值
  const [selectedList, setSelectedList] = useState([]); // 是否选中

  useEffect(() => {
    const currentList = enterpriseUserSelectData.find(it => it.tenantId === enterpriseUserData.tenantId); // store当前企业选中的机构数据
    setSelectedList(currentList.orgList);
  }, [enterpriseUserSelectData])

  // 初始化，从对应的数据源拿到未筛选数据
  useEffect(() => {
    // 把store里面的选中项，复制一份到当前页，如果确定，存入store，如果取消，则放弃修改
    setItemList(enterpriseUserData?.orgList);
  }, []);

  /** 搜索筛选，对源数据进行过滤 */
  const searchInputChange = (value: string) => {
    setSearchString(value);
  };

  // 选中的机构
  const selectBtnFn = useCallback((id) => {
    const isSelect = selectedList.find(i => i === id);
    let selectArr = [];
    if(isSelect) {
      selectArr = selectedList.filter(i => i !== id);
    } else {
      selectArr = [...selectedList, id];
    }
    setSelectedList(selectArr);

  }, [selectedList]);

  // 确定
  const confirmHandle = useCallback(() => {
    console.log(selectedList, 'selectedListselectedListselectedList')
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        enterpriseUserSelectData: enterpriseUserSelectData.map((data, index) => {
          if (data.tenantId === enterpriseUserData.tenantId) {
            return {
              ...data,
              isAllUser: 0, // 重置所有员工-不勾选
              orgList: selectedList // 搜索后勾选中的机构
            };
          }
          return { ...data };
        }),
      },
    });
    // 跳转选择企业/品牌页
    goBack(12);
  }, [dispatch, enterpriseUserData, enterpriseUserTab, goBack, selectedList]);

  let list: ListDateItem = [];
  if (searchString) {
    list = itemList.filter((item) => item.name.indexOf(searchString) > -1);
  }

  return (
    <div className={styles.container}>
      <div className={styles.title_box}>
        <div
          className={styles.title_btn}
          onClick={() => {
            goBack(12);
          }}
        >
          <img src={GoBackIcon} width={12} height={24} alt="" />
        </div>
        <div className={styles.title}>搜索机构</div>
      </div>

      <div className={styles.search_box}>
        <div className={styles.search_content}>
          <div className={styles.search_icon}>
            <img src={SearchIcon} width={20} height={20} style={{ display: 'block' }} alt="" />
          </div>
          <div className={styles.search_input}>
            <Input
              placeholder="搜索机构"
              clearable
              value={searchString}
              onChange={searchInputChange}
            />
          </div>
        </div>
      </div>
      <Spin spinning={false}>
        <div className={styles.data_box}>
          {list && list.length > 0 ? (
            <div className={styles.list_box}>
              {list.map((item) => {
                return (
                  <div
                    key={item.id}
                    className={classNames({
                      [styles.init_style]: true,
                      [styles.checked_style]: selectedList && selectedList.find(t => t === item.id),
                    })}
                    onClick={() => selectBtnFn(item.id)}
                  >
                    {item.name}
                  </div>
                );
              })}
            </div>
          ) : (
            <div className={styles.nodata}>
              < img src={noDataImg} alt="" />
              <div className={styles.empty_title}>暂无该搜索结果</div>
              <div className={styles.empty_msg}>请试试其他搜索关键词</div>
            </div>
          )}
        </div>
      </Spin>
      <div className={styles.btn_wrap}>
        <div
          className={styles.cancelBtn}
          onClick={() => {
            goBack(12);
          }}
        >
          取消
        </div>
        <div className={styles.submitBtn} onClick={confirmHandle}>
          确定
        </div>
      </div>
    </div>
  );
};

export default connect(({ userInfoStore, loading }: any) => ({ userInfoStore, loading }))(Index);
