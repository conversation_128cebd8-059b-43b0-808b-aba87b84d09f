import React, { useState, useEffect } from 'react'
import { connect, history } from 'umi'
import { gdpFormat, processNames, randomColor, getHeaders, useThrottle } from '@/utils/utils';
import { Spin, Form, Input, Radio, Upload, Button, message, Typography, Select } from 'antd'
import { CloseOutlined } from '@ant-design/icons'
import styles from './index.less';
import PcHeader from '@/componentsByPc/PcHeader'
import PreviewModal from '@/pages/CreateGraphicsText/ComponentsPC/PreviewModal';
import NoDataRender from '@/components/NoDataRender'
import linkBgIcon from '@/assets/GlobalImg/link_bg.png';
import uploadImgIcon from '@/assets/GlobalImg/add.png';
import classNames from 'classnames'
import { stringify } from 'qs'

let timer = null                       // 暂存
let pageDidMount = false

const Index: React.FC = (props: any) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')  // 当前用户所有信息
  const { id } = history.location.query
  const { dispatch, loading } = props
  const [form] = Form.useForm()
  const [createAndJoinKingdomDataSource, setCreateAndJoinKingdomDataSource] = useState([]) // 王国数据
  const initialModalState = {
    previewVisible: false,             // 预览弹窗是否显示
    previewModalType: 1,               // 弹窗类型
    bottomTipsBarVisible: true,        // 黄条是否显示
  }
  const initialState = {
    imageTitle: '',                    // 标题
    outerChain: '',                    // 地址
    outerChainPaperwork: '了解更多',       // 文案
    kingdomId: null,                   // 王国的ID
    textImgList: [],                   // 封面
    textImgListType: 1,                // 封面类型
  }
  const [modalState, setModalState] = useState(initialModalState)
  const [state, setState] = useState(initialState)
  const [uploadImageLoading, setUploadImageLoading] = useState(false)
  const [refreshPageState, setRefreshPageState] = useState(false)
  const [loadingSave, setLoadingSave] = useState(false)
  const [loadingSave2, setLoadingSave2] = useState(false)
  const [kingdomDropdownRenderVisible, setKingdomDropdownRenderVisible] = useState(false)

  // 实时保存
  useEffect(() => {
    if (!pageDidMount) {
      return
    }
    clearTimeout(timer)
    timer = setTimeout(() => {
      editImgTextInfo2()
    }, 2000)

  }, [state.imageTitle, state.outerChain, state.outerChainPaperwork, state.kingdomId, state.textImgList])

  useEffect(() => {
    // 获取王国数据
    getCreateAndJoinKingdomList()

    if (id) {
      // 编辑图文
      imgTextInfoUpdateId()
    }
    pageDidMount = true

    return () => {
      pageDidMount = false
      clearTimeout(timer)
    }
  }, [])

  // 获取王国数据
  const getCreateAndJoinKingdomList = () => {
    dispatch({
      type: 'userInfoStore/getCreateAndJoinKingdomList',
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200) {
        const arr = []
        if (content['1']) {
          arr.push({
            id: 1,
            name: '我创建的',
            children: content[1],
          })
        }
        if (content['2']) {
          arr.push({
            id: 2,
            name: '我加入的',
            children: content[2],
          })
        }
        setCreateAndJoinKingdomDataSource(arr);
      }
      if (code == 400) {
      }
    }).catch()
  }

  // 编辑图文
  const imgTextInfoUpdateId = () => {
    dispatch({
      type: 'graphicsText/imgTextInfoUpdateId',
      payload: {
        imageTextId: id,                         // 图文ID
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200 && content) {
        // 无权限
        if (content.createUserId != UserInfo.friUserId) {
          message.error('您没有编辑权限')
          setTimeout(() => {
            goBack()
          }, 1000)
          return
        }
        setState({
          ...state,
          imageTitle: content.imageTitle || '',                                // 标题
          outerChain: content.outerChain || '',                                // 地址
          outerChainPaperwork: content.outerChainPaperwork || '了解更多',          // 文案
          kingdomId: content.kingdomId,                                        // 王国ID
          textImgList: content.textImgList || [],                              // 封面
          textImgListType: content.textImgList && content.textImgList.length > 0 ? 2 : 1,     // 封面类型
        })
        form.setFieldsValue({
          imageTitle: content.imageTitle || undefined,
          outerChain: content.outerChain || undefined,
        })
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch(err => {
    })
  }

  // 修改标题
  const onChangeImageTitle = (e) => {
    setState({
      ...state,
      imageTitle: e.target.value && e.target.value.trim() || '',
    })
  }

  // 修改地址
  const onChangeOuterChain = (e) => {
    setState({
      ...state,
      outerChain: e.target.value && e.target.value.trim() || '',
    })
  }

  // 修改封面
  const onChangeTextImgListType = (e) => {
    setState({
      ...state,
      outerChainPaperwork: '了解更多',
      textImgList: [],
      textImgListType: e.target.value,
    })
  }

  // 修改按钮文案
  const onChangeOuterChainPaperwork = (value) => {
    setState({
      ...state,
      outerChainPaperwork: value && value.trim() || '了解更多',
    })
  }

  // 上传校验
  const beforeUpload = (file) => {
    const isSize = file.size / 1024 / 1024 < 15
    if (!isSize) {
      message.error('超过15M限制，不允许上传~')
      return false
    }
    const { name: fileName } = file || {}
    const isJpgOrPng = file.type === 'image/jpg' || file.type === 'image/jpeg' ||  file.type === 'image/png' || file.type === 'image/gif'
    const suffix = fileName.substring(fileName.lastIndexOf('.')+1).toLowerCase()
    const isSuffixByJpgOrPng = (suffix === 'jpg' || suffix === 'jpeg' || suffix === 'png' || suffix === 'gif')
    if (!isJpgOrPng || !isSuffixByJpgOrPng) {
      message.error('只能上传JPG、JPEG、PNG、GIF格式的图片~')
      return false
    }
    return true
  };

  // 上传
  const onChangeByUpload = (info) => {
    if (info && !info.file.status) {
      return
    }

    if (info.file.status === 'uploading') {
      setUploadImageLoading(true)
    }

    if (info && info.file.status === 'error') {
      message.error('上传失败');
      setUploadImageLoading(false)
      return
    }

    if (info && info.file.status === 'done') {
      setUploadImageLoading(false)

      const res = info.file.response || {}
      const { code, content, msg } = res
      if (code == 200) {
        setState({
          ...state,
          textImgList: [{
            imageUrlShow: content && content.fileUrlView,
            isCover: 1,                                    // 1 是封面
          }]
        })
      } else {
        message.error(msg || '上传失败')
      }
    }
  }

  // 预览
  const previewModalShow = (previewModalType) => {
    setModalState({
      ...modalState,
      previewVisible: true,
      previewModalType,
    })
  }

  // 预览弹框关闭
  const previewModalHide = () => {
    setModalState({
      ...modalState,
      previewVisible: false,
      previewModalType: 1,
    })
  }

  // 选择王国
  const onKingdomDropdownVisibleChange = (visible) => {
    setKingdomDropdownRenderVisible(visible)
  }

  // 选择王国下拉框
  const dropdownRender = (originNode) => {
    // return originNode
    return (
      <div className={styles.select_dropdown_container}>
        {
          createAndJoinKingdomDataSource.length == 0 ? <NoDataRender style={{marginTop: 0}} text="当前无可关联王国~"/>
          : createAndJoinKingdomDataSource.map(item => {
              return (
                <div key={item.id}>
                  <div className={styles.select_dropdown_title}>{item.name}({item.children.length}个)</div>
                  {
                    item.children.map(itemChild => {
                      return (
                        <div key={itemChild.id} className={classNames(styles.kingdom_item, {
                          [styles.selected]: state.kingdomId == itemChild.id,
                        })} onClick={() => onChangeKingdomId(itemChild.id)}>
                          <i style={itemChild.kingdomCoverUrlShow || itemChild.kingImgUrlShow ? {backgroundImage: `url(${itemChild.kingdomCoverUrlShow || itemChild.kingImgUrlShow})`} : {background: randomColor(itemChild.wxUserId)}}>
                            {!itemChild.kingdomCoverUrlShow && !itemChild.kingImgUrlShow && processNames(itemChild.kingName)}
                          </i>
                          <div className={styles.kingdom_item_details}>
                            <div className={styles.kingdom_name}>{itemChild.name}</div>
                            <div className={styles.kingdom_info_box}>
                              <span>{gdpFormat(itemChild.gdp)}GDP</span>
                              <span>{gdpFormat(itemChild.nationalNum)}国民在交流</span>
                              <span>{itemChild.spaceNum}个热议空间</span>
                            </div>
                          </div>
                        </div>
                      )
                    })
                  }
                </div>
              )
            })
        }
      </div>
    )
  }

  // 选择王国
  const onChangeKingdomId = (kingdomId) => {
    setState({
      ...state,
      kingdomId,
    })
    setKingdomDropdownRenderVisible(false)
  }

  // 清空数据
  const onChangeKingdomIdByClear = (value, option) => {
    setState({
      ...state,
      kingdomId: null,       // 王国ID
    })
  }

  // 发布
  let submit = () => {
    form.validateFields().then(values => {
      console.log(values)
      console.log(state)
      editImgTextInfo(values)
    }).catch(err => {
      console.log(err)
      console.log(state)
    })
  }
  submit = useThrottle(submit, 500)

  // 提交
  const editImgTextInfo = (values) => {
    setLoadingSave(true)
    dispatch({
      type: 'graphicsText/editImgTextInfo',
      payload: {
        forwardDescribe: null,                                                 // 转发描述
        id: id || null,                                                        // 图文ID
        outerChain: values.outerChain && values.outerChain.trim() || null,     // 链接地址
        outerChainPaperwork: state.outerChainPaperwork,                        // 按钮文案
        imageTextId: null,                                                     // 图文ID  转发时存在
        imageTitle: values.imageTitle && values.imageTitle.trim() || null,     // 标题
        imageType: 3,                                                          // 类型
        textImgList: state.textImgList,                                        // 图片信息
        isForward: 0,                                                          // 是否转发
        kingdomId: state.kingdomId,                                            // 王国id
        saveType: 2,                                                           // 提交类型
      }
    }).then(res => {
      setLoadingSave(false)
      const { code, content, msg } = res
      if (code == 200 && (content || content == 0)) {
        message.success('提交成功~正在快马加鞭地审核')
        history.replace('/UserInfo')
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch(err => {})
  }

  const editImgTextInfo2 = () => {
    console.log(form.getFieldsValue())
    console.log(form.getFieldsError())
    if (loadingSave2) {
      return
    }
    const values = form.getFieldsValue()
    setLoadingSave2(true)
    dispatch({
      type: 'graphicsText/editImgTextInfo',
      payload: {
        forwardDescribe: null,                                                 // 转发描述
        id: id || null,                                                        // 图文ID
        outerChain: values.outerChain && values.outerChain.trim() || null ,    // 链接地址
        outerChainPaperwork: state.outerChainPaperwork,                        // 按钮文案
        imageTextId: null,                                                     // 图文ID  转发时存在
        imageTitle: values.imageTitle && values.imageTitle.trim() || null,     // 标题
        imageType: 3,                                                          // 类型
        textImgList: state.textImgList,                                        // 图片信息
        isForward: 0,                                                          // 是否转发
        kingdomId: state.kingdomId,                                            // 王国id
        saveType: 1,                                                           // 提交类型
      }
    }).then(res => {
      setLoadingSave2(false)
      const { code, content, msg } = res
      if (code == 200 && (content || content == 0)) {
        if (!id) {
          history.replace(`/CreateGraphicsText/CreateExternalLinks?id=${content}`)
          setRefreshPageState(!refreshPageState)
        }
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch(err => {})
  }

  // 隐藏黄条
  const bottomTipsBarHide = () => {
    setModalState({
      ...modalState,
      bottomTipsBarVisible: false,
    })
  }

  // 返回
  const goBack = () => {
    if (history.length > 1) {
      history.goBack()
    } else {
      history.replace('/')
    }
  }

  const loadingGetCreateAndJoinKingdomList = !!loading.effects['userInfoStore/getCreateAndJoinKingdomList']
  const loadingEditImgTextInfo = !!loading.effects['graphicsText/editImgTextInfo']
  return (
    <Spin spinning={loadingGetCreateAndJoinKingdomList} wrapperClassName={styles.spin}>
      <div className={styles.container}>
        <PcHeader />
        <div className={styles.content}>
          <div className={styles.content_inner}>
            <div className={styles.nav_bar}>
              <i onClick={goBack}></i>
              <span>发布外链</span>
            </div>
            <div className={styles.wrapper}>
              <Form form={form} autoComplete="off" component={false}>
                <div className={styles.title_box}>
                  <Form.Item
                    name="imageTitle"
                    rules={[
                      { required: true, whitespace: true, message: '请输入标题' },
                      { min: 2, max: 50, transform: (value) => value ? value.trim() : value, message: '请输入标题（2-50个字）' },
                      { pattern: /^[~\s!！@#$￥%^…&*()_—+{}：:‘’“”《》？"|<>?`\-=[\];',.。，；（）、a-zA-Z0-9\u4e00-\u9fa5/]+$/, message: '标题不能包含特殊字符' },
                    ]}
                  >
                    <Input.TextArea
                      placeholder="请输入标题（2-50个字）"
                      autoSize
                      size="large"
                      autoComplete="off"
                      maxLength={50}
                      onChange={onChangeImageTitle}
                    />
                  </Form.Item>
                </div>
                <div className={styles.form_input_text}>
                  <Form.Item
                    label="链接地址"
                    name="outerChain"
                    colon={false}
                    rules={[
                      { required: true, whitespace: true, message: '请输入链接地址' },
                      {  max: 200, transform: (value) => value ? value.trim() : value, message: '不能超过200字' },
                      { pattern: /^(http|https):\/\/[a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,}(\/\S*)?$/, message: '请输入正确的链接地址' },
                    ]}
                  >
                    <Input
                      placeholder='请输入链接地址'
                      style={{ width: 400 }}
                      autoComplete="off"
                      maxLength={200}
                      onChange={onChangeOuterChain}
                    />
                  </Form.Item>
                </div>
                <div className={styles.form_radio}>
                  <Form.Item label="封面设置" required={true} colon={false}>
                    <Radio.Group value={state.textImgListType} onChange={onChangeTextImgListType}>
                      <Radio value={1}>无封面</Radio>
                      <Radio value={2}>有封面</Radio>
                    </Radio.Group>
                  </Form.Item>
                </div>
                {
                  state.textImgListType == 1 ?
                    <div className={styles.init_img_wrap}>
                      <div className={styles.init_img_list}>
                        <div className={styles.init_bg}>
                          <img src={linkBgIcon} alt="" width={48} height={48} />
                        </div>
                        <div className={styles.init_info}>
                          <div className={styles.init_info_title}>{state.imageTitle}</div>
                          <Typography.Paragraph
                            editable={{
                              onChange: onChangeOuterChainPaperwork,
                              enterIcon: null,
                              maxLength: 8,
                            }}
                          >
                            {state.outerChainPaperwork}
                          </Typography.Paragraph>
                        </div>
                      </div>
                      <div className={styles.preview_btn} onClick={() => previewModalShow(1)}>预览</div>
                    </div>
                    :
                    <div className={styles.upload_wrap}>
                      <div className={styles.link_img_wrap}>
                        <Upload
                          headers={getHeaders()}
                          accept="image/*"
                          action={`/api/server/base/uploadFile?${stringify({ fileType: 23, userId: UserInfo?.friUserId })}`}
                          onChange={onChangeByUpload}
                          beforeUpload={beforeUpload}
                          showUploadList={false}
                        >
                          <Spin spinning={uploadImageLoading}>
                            <div className={styles.upload_box}>
                              {
                                state.textImgList[0] && state.textImgList[0].imageUrlShow ?
                                  <div className={styles.upload_img} style={{backgroundImage: `url(${state.textImgList[0].imageUrlShow})`}} />
                                  :
                                  <div className={styles.add_img}>
                                    <img className={styles.init_upload_img} src={uploadImgIcon} alt="" />
                                    <div>添加封面</div>
                                  </div>
                              }
                            </div>
                          </Spin>
                        </Upload>
                        <div className={styles.img_link_text}>
                          <Typography.Paragraph
                            editable={{
                              onChange: onChangeOuterChainPaperwork,
                              enterIcon: null,
                              maxLength: 8,
                            }}
                          >
                            {state.outerChainPaperwork}
                          </Typography.Paragraph>
                        </div>
                      </div>
                      <div className={styles.preview_btn} onClick={() => previewModalShow(2)}>预览</div>
                    </div>
                }
                <div className={styles.from_content} id="kingdomId">
                  <Form.Item label="关联王国" colon={false}>
                    <Select
                      getPopupContainer={() => document.getElementById('kingdomId')}
                      popupClassName={styles.custom_dropdown_render}
                      placeholder={createAndJoinKingdomDataSource.length == 0 ? '当前无可关联王国' : '不关联王国，默认只发布在个人主页'}
                      style={{width: 400}}
                      value={state.kingdomId}
                      listHeight={256}
                      fieldNames={{ label: 'name', value: 'id', options: 'children' }}
                      options={createAndJoinKingdomDataSource}
                      dropdownRender={dropdownRender}
                      open={kingdomDropdownRenderVisible}
                      onDropdownVisibleChange={onKingdomDropdownVisibleChange}
                      allowClear
                      onChange={onChangeKingdomIdByClear}
                    >
                    </Select>
                  </Form.Item>
                </div>
              </Form>
            </div>
          </div>
        </div>
        {
          modalState.bottomTipsBarVisible &&
          <div className={styles.bottom_tips_bar}>
            为了维护良好的社区氛围，每篇文章和外链都会经过审核，请耐心等待，我们将尽快审核并发布你的文章。
            <div className={styles.bar_btn_wrap} onClick={bottomTipsBarHide}>
              <CloseOutlined />
            </div>
          </div>
        }
        <div className={styles.footer}>
          <div className={styles.footer_content}>
            <div className={styles.footer_content_left}>
              <span>{loadingSave2 ? '保存中...' : '草稿将自动保存'}</span>
            </div>
            <div className={styles.footer_content_right}>
              <Button type="primary" size="large" onClick={submit} loading={loadingSave}>提交</Button>
            </div>
          </div>
        </div>
      </div>
      <PreviewModal
        visible={modalState.previewVisible}
        previewModalType={modalState.previewModalType}
        imageTitle={state.imageTitle}
        outerChainPaperwork={state.outerChainPaperwork}
        coverImageUrlShow={state.textImgList[0] && state.textImgList[0].imageUrlShow || ''}
        onCancel={previewModalHide}
      />
    </Spin>
  )
}
export default connect(({ graphicsText, loading }: any) => ({ graphicsText, loading }))(Index)
