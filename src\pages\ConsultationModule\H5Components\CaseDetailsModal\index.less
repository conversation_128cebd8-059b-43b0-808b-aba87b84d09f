.popup_container {
  :global {
    .adm-popup-body {
      border-radius: 16px 16px 0 0;
    }
  }
}
.header_line {
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  .header_line_bar {
    width: 48px;
    height: 4px;
    background: #D0D4D7;
    border-radius: 4px;
  }
}
.header_title {
  position: relative;
  font-size: 18px;
  line-height: 25px;
  text-align: center;
  font-weight: 500;
  margin-bottom: 16px;
  .close_icon {
    position: absolute;
    width: 32px;
    height: 32px;
    top: -3px;
    right: 16px;
    color: #000;
    display: flex;
    align-items: center;
    justify-content: center;
    :global {
      .anticon {
        font-size: 16px;
      }
    }
  }
}

.container {
  height: calc(100% - 70px);
  padding-bottom: 32px;
  overflow-y: auto;
  :global {
    .adm-mask {
      z-index: 9992;
    }
  }
}

.item {
  display: flex;
  flex-wrap: nowrap;
  padding: 0 12px;
  padding-bottom: 8px;
  overflow: hidden;
  .label {
    width: 66px;
    min-width: 66px;
    font-size: 13px;
    color: #666;
    line-height: 18px;
    margin-bottom: 6px;
    white-space: nowrap;
  }
  .text_value {
    flex: 1;
    font-size: 13px;
    color: #000;
    line-height: 18px;
    word-break: break-all;
    white-space: pre-wrap;
  }
  .tag_value {
    display: flex;
    flex-wrap: wrap;
    .tag {
      height: 21px;
      line-height: 21px;
      border-radius: 2px;
      background: #EDF9FF;
      padding: 0 4px;
      font-size: 12px;
      color: #0095FF;
      margin-right: 6px;
      margin-bottom: 6px;
    }
  }
}
.item.img_item {
  display: block;
  .label {
    margin-bottom: 16px;
  }
  .img_value {
    display: flex;
    flex-wrap: wrap;
    margin-right: -12px;
    max-width: 400px;
    .img {
      width: 108px;
      height: 110px;
      margin-right: 8px;
      margin-bottom: 8px;
      border-radius: 6px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      &:nth-child(3n) {
        margin-right: 0;
      }
    }
  }
}
