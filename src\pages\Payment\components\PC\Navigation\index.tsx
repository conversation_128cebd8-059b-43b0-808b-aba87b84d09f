/**
 * [PC][支付] 页面组件导航条组件
 * activeStep: 1表示当前处于第1个导航项, 1:信息填写并提交,2:支付方式,3:支付结果
 */
import React from "react";
import styles from "./index.less";
import classNames from "classnames";
import {connect} from "umi";


// 导航栏组件item
const NavItem: React.FC<{
  activeStep: number;
  stepNumber: number;
  text: string;
}> = ({ activeStep, stepNumber, text }) => {
  const isActive = activeStep === stepNumber; // 判断当前导航项是否处于活动状态
  return (
    <div className={styles.nav_item}>
      {/* 圆圈 */}
      <div className={classNames(styles.nav_item_circle, {
        [styles.nav_item_circle_active]: isActive,
        [styles.nav_item_circle_complete]: activeStep > stepNumber
      })}>
        {activeStep <= stepNumber && stepNumber}
      </div>
      {/* 文本 */}
      <div className={classNames(styles.nav_item_text, { [styles.nav_item_text_active]: activeStep >= stepNumber })}>
        {text}
      </div>
    </div>
  );
}
const Navigation: React.FC<{
  activeStep: number;
}> = ({ activeStep }) => {
  return (
    <div className={styles.nav_title_warp}>
      <div className={styles.nav_title}>
        <div className={styles.nav_line} /> {/* 线 */}
        <NavItem activeStep={activeStep} stepNumber={1} text="信息填写并提交" /> {/* 第1个导航项 */}
        <NavItem activeStep={activeStep} stepNumber={2} text="支付方式" />      {/* 第2个导航项 */}
        <NavItem activeStep={activeStep} stepNumber={3} text="支付结果" />      {/* 第3个导航项 */}
      </div>
    </div>
  );
}

export default connect(({ loading }: any) => ({
  loading
}))(Navigation)
