import React from 'react'
import classNames from 'classnames'
import styles from './index.less'

class Index extends React.Component {
  static defaultProps = {
    itemOnClick: () => {},
  }
  constructor(props) {
    super(props)
    this.state = {}
  }

  componentDidMount(): void {

  }

  // 点击字体设置
  formatOnChange = (value) => {
    console.log('点击字体设置', value)
    this.props.itemOnClick && this.props.itemOnClick(value)
  }

  render() {
    return (
      <div className={styles.editor_toolbar_container}>
        <div className={styles.toolbar_title}>文字格式</div>
        <div className={classNames(styles.font_toolbar, styles.font_toolbar1)}>
          <div className={classNames(styles.toolbar_item, styles.checked)} onClick={() => {this.formatOnChange('bold')}}>
            <i className={styles.icon_bold}></i>
          </div>
          <div className={styles.toolbar_item} onClick={() => {this.formatOnChange('italic')}}>
            <i className={styles.icon_italic}></i>
          </div>
          <div className={styles.toolbar_item} onClick={() => {this.formatOnChange('underline')}}>
            <i className={styles.icon_underline}></i>
          </div>
          <div className={styles.toolbar_item} onClick={() => {this.formatOnChange('strikethrough')}}>
            <i className={styles.icon_strikethrough}></i>
          </div>
        </div>
        <div className={styles.font_toolbar}>
          <div className={classNames(styles.toolbar_item, styles.checked)} onClick={() => {this.formatOnChange('small')}}>
            <span className={styles.small}>标准</span>
          </div>
          <div className={styles.toolbar_item} onClick={() => {this.formatOnChange('large')}}>
            <span className={styles.large}>大</span>
          </div>
          <div></div><div></div>
        </div>

        <div className={styles.toolbar_title}>文字颜色</div>
        <div className={styles.color_toolbar}>
          <div className={classNames(styles.toolbar_item, styles.checked)} onClick={() => {this.formatOnChange('color1')}}>
            <span className={styles.color_1}></span>
          </div>
          <div className={classNames(styles.toolbar_item, styles.checked)} onClick={() => {this.formatOnChange('color2')}}>
            <span className={styles.color_2}></span>
          </div>
          <div className={styles.toolbar_item} onClick={() => {this.formatOnChange('color3')}}>
            <span className={styles.color_3}></span>
          </div>
          <div className={styles.toolbar_item} onClick={() => {this.formatOnChange('color4')}}>
            <span className={styles.color_4}></span>
          </div>
          <div className={styles.toolbar_item} onClick={() => {this.formatOnChange('color5')}}>
            <span className={styles.color_5}></span>
          </div>
          <div className={styles.toolbar_item} onClick={() => {this.formatOnChange('color6')}}>
            <span className={styles.color_6}></span>
          </div>
          <div className={styles.toolbar_item} onClick={() => {this.formatOnChange('color7')}}>
            <span className={styles.color_7}></span>
          </div>
        </div>

        <div className={styles.toolbar_title}>对齐方式</div>
        <div className={classNames(styles.font_toolbar, styles.font_toolbar1)}>
          <div className={classNames(styles.toolbar_item, styles.checked)} onClick={() => {this.formatOnChange('justify')}}>
            <i className={styles.icon_justify}></i>
          </div>
          <div className={styles.toolbar_item} onClick={() => {this.formatOnChange('left')}}>
            <i className={styles.icon_left}></i>
          </div>
          <div className={styles.toolbar_item} onClick={() => {this.formatOnChange('center')}}>
            <i className={styles.icon_center}></i>
          </div>
          <div className={styles.toolbar_item} onClick={() => {this.formatOnChange('right')}}>
            <i className={styles.icon_right}></i>
          </div>
        </div>
      </div>
    )
  }
}

export default Index
