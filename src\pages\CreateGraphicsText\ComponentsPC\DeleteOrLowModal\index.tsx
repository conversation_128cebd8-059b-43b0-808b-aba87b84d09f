import React from 'react'
import { connect } from 'umi';
import { message, Modal } from 'antd';
import styles from './index.less'

interface PropsType {
  visible: boolean,                    // 弹窗是否显示
  deleteOrLowType: number,             // 类型
  deleteOrLowId: number,               // ID
  onCancel: () => {},
  handleDeleteOrLow: () => {},
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { loading, dispatch, visible, deleteOrLowType, deleteOrLowId } = props;

  // 确定
  const onOk = () => {
    if (deleteOrLowType == 1) {
      deleteImgTextInfo()
    } else {
      lowUpFrameImgTextInfo()
    }
  }

  // 删除
  const deleteImgTextInfo = () => {
    dispatch({
      type: 'graphicsText/deleteImgTextInfo',
      payload: {
        imageTextId: deleteOrLowId,    // 图文ID
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200 && content) {
        message.success('删除成功')
        props.handleDeleteOrLow()
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch();
  }

  // 下架
  const lowUpFrameImgTextInfo = () => {
    dispatch({
      type: 'graphicsText/lowUpFrameImgTextInfo',
      payload: {
        imageTextId: deleteOrLowId,    // 图文ID
        lowUpFrame: 0,                 // 下架
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200 && content) {
        message.success('下架成功')
        props.handleDeleteOrLow()
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch();
  }

  const loadingDeleteImgTextInfo = !!loading.effects['graphicsText/deleteImgTextInfo']
  const loadingLowUpFrameImgTextInfo = !!loading.effects['graphicsText/lowUpFrameImgTextInfo']
  return (
    <Modal
      open={visible}
      onOk={onOk}
      onCancel={props.onCancel}
      width={444}
      wrapClassName={styles.modal_wrap}
      confirmLoading={loadingDeleteImgTextInfo || loadingLowUpFrameImgTextInfo}
    >
      <div className={styles.title}>确定{deleteOrLowType == 1 ? '删除' : '下架'}此内容？</div>
      <div className={styles.text}>内容{deleteOrLowType == 1 ? '删除' : '下架'}后将无法恢复，请慎重考虑</div>
    </Modal>
  )
}

export default connect(({ loading }: any) => ({ loading }))(Index)

