import React, { useState, useEffect, useRef } from 'react';
import { connect } from 'umi';
import styles from "./OrthodonticChecklist.less";
import { getArrailUrl } from '@/utils/utils'
import {Form, message, Modal, Spin} from "antd";
import _ from "lodash";
import SelectToothMode from '@/components/SelectTooth/index.js';

import {
  getFieldDecoratorByitemByLv2,
  setFormValues,
} from "@/pages/CreationOrthodontics/components/CreationFormUtils";
import ToothSelectQuality from "@/components/ToothSelectQuality/index.jsx";
import {
  getToothBitInfo,
  getToothBitInfoByToothPosition,
  toothUtils} from "@/utils/ToothSelect";
import {history} from "@@/core/history";
import ToothSelect from "@/components/SelectTooth";
import {stringify} from "qs";

const defaultToothInfo = {
  leftTop: [],
  rigthTop: [],
  leftBottom: [],
  rigthBottom: [],
}

const OrthodonticChecklist: React.FC = (props) => {
  // 是否嵌套在5i5ya的iframe中
  const isInIframe = (self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya')

  const [ SelectToothModeVisible,setSelectToothModeVisible ] = useState(null);
  const [ toothRef,setToothRef ] = useState(null);
  const [ form ] = Form.useForm();
  const { CreationOrthodontics,loading } = props || {}
  const { location } = history || {}
  const { query } = location || {}
  const { medicalRecordJson,DictionaryData,DataBymedicalRecordJson } = CreationOrthodontics || {}  // 正畸病例字典结构
  const {
    id:consultationId,       // :consultationId 8dad6e1a68714dc7b1f908902ad2d4f4
    customerId,              // 客户id
    createUserId,            // 创建人id
    tenantId,                // 租户id
  } = DictionaryData || {}
  // 检查json
  const checkJson  = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item)=>{return item.dictCode == 5})


  useEffect(() => {
    if (!DataBymedicalRecordJson) {
      getDictionaryData();
    }
  },[])

  useEffect(() => {
    echoFormValue()
  },[!!DataBymedicalRecordJson])

  // 回显表单数据
  const echoFormValue = () => {
    if (checkJson) {
      const FormArr = [].concat(checkJson.subsetList);
      setFormValues(FormArr,form);
    }
  };

  const goBack = () => {
    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'goBack',       // 页面地址onchange事件
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
      return
    }

    /*if (modalStatus == 1) {
      history.replace('/')
      return
    }*/
    history.replace(`/CreationOrthodontics/Step2?${stringify(history.location.query)}`)
    // history.goBack()
  }

  // 获取字典数据
  const getDictionaryData=()=>{
    const { dispatch } = props;
    const { consultationId, tenantId, customerId} = query || {}
    dispatch({
      type: 'CreationOrthodontics/getDictionaryData',
      payload: {
        consultationId: consultationId,
        tenantId: tenantId,
        customerId: customerId,
        type: 0,
      }
    })
  }

  const onFormLayoutChange = _.debounce((value) => {
    let valuesByForm = form.getFieldsValue();
    onFinish(valuesByForm)
  },3000);

  const onClickToothBit = (item) => { setSelectToothModeVisible(item) }

  const onFinish = _.debounce((value,isSubmitLoading) => {
    const { errorFields } = value || {};
    if (Array.isArray(errorFields) && errorFields.length > 0) {
      message.error('请填写完整信息');
      return;
    }

    const { dispatch } = props || {}
    let formDataArr = Object.keys(value).map((key) => {
      let inputContent = value[key];
      // 判定当前是否是字符串
      if (typeof inputContent == 'string' && inputContent.length > 200) {
        inputContent = inputContent.substring(0, 200);
      }
      return { id: key,  inputContent: inputContent }
    })
    let formDataArrByID20 = formDataArr.find((item) => { return item.id == 20 })
    console.log('subsetListsubsetList123 :: ',formDataArrByID20);
    if (formDataArrByID20 && formDataArrByID20.inputContent && Array.isArray(formDataArrByID20.inputContent.subsetList)) {
      formDataArrByID20.inputContent.subsetList.map((item) => {
        if(!!formDataArrByID20.inputContent && !!formDataArrByID20.inputContent.subIsCheckValueItem){
          if(item.id == formDataArrByID20.inputContent.subIsCheckValueItem.id){
            formDataArr.push({ id: formDataArrByID20.inputContent.subIsCheckValue, inputContent: {...formDataArrByID20.inputContent.subIsCheckValueItem,isCheck:true}})
          }else {
            formDataArr.push({ id: item.id, inputContent: null})
          }
        }else {
          formDataArr.push({ id: item.id, inputContent: null})
        }
      })
    }
    /*if (Array.isArray(formDataArr) && formDataArrByID20 && formDataArrByID20.inputContent &&  formDataArrByID20.inputContent.subIsCheckValue) {
      formDataArr.push({ id: formDataArrByID20.inputContent.subIsCheckValue, inputContent: formDataArrByID20.inputContent.subIsCheckValueItem})
    }*/

    console.log('formDataArr111 :: ',formDataArr);
    dispatch({
      type: 'CreationOrthodontics/saveDataByMedicalRecordJson',
      payload: {
        processNode:2,         // 检查及分析
        formDataArr:formDataArr,
        isSubmit:isSubmitLoading,
      }
    }).then((value)=>{
      if(isSubmitLoading) { goBack() }
    })
  },500)

  return (
    <div className={styles.page_Warp}>
      <div className={styles.page_content_Warp}>
        <div className={styles.page_content}>
          <div className={styles.nav_title}>
            <i onClick={goBack} className={styles.backspace}></i> 检查及分析
          </div>

          <Spin spinning={!!loading.effects['CreationOrthodontics/getDictionaryData']}>
            <div className={styles.form_warp}>
              {/* 标题 */}
              <div className={styles.form_title}>
                <div>正畸初诊检查表</div>
              </div>
              {/* 内容
                 form,
                checkJson,
                onFinish,
                onFormLayoutChange,
                onClickToothBit,
                goBack,
                styles,
              */}
              {(!!checkJson && form) &&
                <>
                  <FromByOrthodonticChecklist
                    form={form}
                    checkJson={checkJson}
                    onFinish={onFinish}
                    onFormLayoutChange={onFormLayoutChange}
                    onClickToothBit={onClickToothBit}
                    goBack={goBack}
                    styles={styles}
                  />
                </>
              }
            </div>
          </Spin>
        </div>
      </div>

      <div>
        {!!SelectToothModeVisible &&
          <Modal
            title="选择牙位"
            open={SelectToothModeVisible}
            destroyOnClose={true}
            maskClosable={false}
            closable={false}
            onOk={()=>{
              let toothInfoStr = toothRef.getTooth();
              form.setFieldsValue({
                [SelectToothModeVisible.id]:!!toothInfoStr ? getToothBitInfoByToothPosition(toothInfoStr) : null
              })
              setSelectToothModeVisible(null)
            }}
            onCancel={()=>{ setSelectToothModeVisible(null) }}
            okText="保存"
            cancelText="取消"
            className={styles.select_tooth}
            width={1073}
          >
            <SelectToothMode
              toothPosition={(!!SelectToothModeVisible && !!form.getFieldValue(SelectToothModeVisible.id)) ? form.getFieldValue(SelectToothModeVisible.id).rawData : null}
              onRef={(ref) => setToothRef(ref)}></SelectToothMode>
          </Modal>
        }
      </div>
    </div>
  )
}


const FromByOrthodonticChecklist = React.FC = (props) => {
  const {
    form,
    checkJson,
    onFinish,
    onFormLayoutChange,
    onClickToothBit,
    goBack,
    styles,
  } = props || {}
  return (
    <Form
      form={form}
      initialValues={{}}
      onValuesChange={onFormLayoutChange}
      onFinish={(value)=>{onFinish(value,true)}}
      onFinishFailed={(value,errorFields, outOfDate )=>{ onFinish(value) }}
    >
      <div className={styles.content_warp}>
        {checkJson && Array.isArray(checkJson.subsetList) && checkJson.subsetList.map((itemByLv1) => {
          return (
            <div style={{marginBottom:'30px'}}>
              <div className={styles.title_span}>{itemByLv1.dictCode}、{itemByLv1.dictName}：</div>
              {itemByLv1 && Array.isArray(itemByLv1.subsetList) && itemByLv1.subsetList.map((itemByLv2) => {
                return getFieldDecoratorByitemByLv2({
                  item:itemByLv2,
                  form:form,
                  onClickToothBit:onClickToothBit,
                  onChange:()=>{onFormLayoutChange()},
                })
              })}
            </div>
          )
        })}
        <div className={styles.submitWarp}>
          <div className={styles.submitBox}>
            <div onClick={()=>{
              form && form.submit()
            }} className={styles.submit_btn_Cancel}>上一步</div>
            <div onClick={()=>{
              form && form.submit()
            }} className={styles.submit_btn_Enter}>提交</div>
          </div>
        </div>
      </div>
    </Form>
  )

}

export default connect(({ CreationOrthodontics,pcAccount, loading }: any) => ({
  CreationOrthodontics,pcAccount, loading
}))(OrthodonticChecklist)
