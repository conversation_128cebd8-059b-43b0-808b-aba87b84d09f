/**
 * @Description: PC端视频播放弹窗
 * @author: 赵斐
 */
import React, { useEffect, useRef, useState } from 'react';
import { Modal } from 'antd';
import TCPlayer from 'tcplayer.js';
import 'tcplayer.js/dist/tcplayer.min.css';
import styles from './index.less'

import { licenseUrl } from "@/app/config";

interface PropsType {
  dataSource: any,      // 视频数据
  visible: boolean,       // 视频弹窗状态
  onCancel: () => void,   // 取消回调
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { visible, onCancel, dataSource } = props;
  const {
    url,
    id
  } = dataSource || {}
  // let videoPlayer = useRef(null);
  let playerRef = useRef(null);
  const [videoPlayer, setVideoPlayer] = useState(null);
  // playerStateData 播放器状态数据
  const [playerStateData, setPlayerStateData] = useState(null)

  useEffect(() => {
    if (!!visible) {
      if (!videoPlayer && url) {
        // await setTimeout(() =>  {}, 1000);
        let videoJsNode = playerRef.current;
        if (videoJsNode && videoJsNode.id) {
          // 确认节点是否在异步执行的时候被销毁掉
          let element = document.getElementById(videoJsNode.id); // 通过ID获取节点
          if (!element) { return }
          // let vodByDAes = getDAesString(vodPathUrl,'arrail-dentail&2', 'arrail-dentail&3')
          let sources = [{ src: url }]
          let tcPlayerObj = TCPlayer(videoJsNode, {
            sources: sources,
            licenseUrl: licenseUrl,
            autoplay: true,
            loop: true,
            muted: true,
            preload: "meta"
          });
          console.log('tcPlayerObj 123123 :: ', tcPlayerObj);
          if (!tcPlayerObj) { return }
          tcPlayerObj.ready(() => {
            setVideoPlayer(tcPlayerObj);
            // tcPlayerObj.volume(0);
            // tcPlayerObj.play();
          })
          tcPlayerObj.on('error', (value) => {
            setPlayerStateData(null)
          });
          tcPlayerObj.on('blocked', (value) => {
            // message.error('自动播放被浏览器阻止');
            setPlayerStateData(null)
            // tcPlayerObj.volume(0);
            // tcPlayerObj.play();
          });
          tcPlayerObj.on('pause', (value) => {
            // setPlayerStateData(null)
          });

          tcPlayerObj.on('playing', (value) => {
            setPlayerStateData(true)
          });

          tcPlayerObj.on('progress', (value) => {

            setPlayerStateData(true)
          });
        }
      } else {
        // await setTimeout(()=>{},1000);
        let videoJsNode = playerRef.current;
        if (!videoJsNode) { return }
        let element = document.getElementById(videoJsNode.id); // 通过ID获取节点
        if (!element) { return }
        videoPlayer && playerStateData && videoPlayer.volume(0);
        videoPlayer && playerStateData && videoPlayer.play();
      }
    } else {
      if (!!videoPlayer) {
        videoPlayer && playerStateData && videoPlayer.pause();
      }
    }
  }, [visible])

  useEffect(() => {
    return () => {
      if (!!videoPlayer) {
        videoPlayer.dispose();
      }
    };
  }, [videoPlayer])

  // 关闭弹窗
  const handleCancel = () => {
    onCancel()
  };

  return (
    <div className={styles.wrap}>
      <Modal
        open={visible}
        onCancel={handleCancel}
        className={styles.modal_content}
        width={620}
        footer={null}
      >
        <div className={styles.modal_content}>
          <video
            key={`video_spaceList_id_${id}`}
            ref={playerRef}
            id={`video_spaceList_id_${id}`}
            className={styles.video}
            x-webkit-airplay="allow"
            playsInline={true}
          />
        </div>
      </Modal>
    </div>
  )
}
export default Index
