/**
 * @Description: PC端，发起指导页第4步
 */
import React, { useState, useEffect } from 'react'
import { history, connect } from 'umi'
import classNames from 'classnames'
import { stringify } from 'qs'
import {getArrailUrl, getOperatingEnv} from '@/utils/utils'
import { Checkbox, Button, message, Modal, Input, Spin } from 'antd'
import styles from './Step4.less'

import PcHeader from '@/componentsByPc/PcHeader' // 顶部导航栏
import ConsultationCaseCard from '../../H5Components/ConsultationCaseCard' // 病例卡片
import OtherInformation from '../../PcComponents/OtherInformation' // 文件列表
import ExpertCard from '../../H5Components/ExpertCard' // 专家卡片
import AgreementModal from '../ComponentsPC/AgreementModal' // 服务协议弹窗
import SubmitSuccessModal from '../ComponentsPC/SubmitSuccessModal' // 提交成功弹窗
import SelectPayMethodModal from '../ComponentsPC/SelectPayMethodModal' // 选择支付方式弹窗
import StartConsultationSteps from '@/pages/ConsultationModule/StartConsultation/ComponentsPC/StartConsultationSteps' // 完成服务流程按钮及弹窗
import PayModal from '../../PcComponents/PayModal' // 支付弹窗

const Index: React.FC = (props) => {
  const isInIframe = (self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya') // 是否嵌套在5i5ya的iframe中
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}') // 登录用户信息

  const { dispatch, loading } = props
  const { query, pathname } = history.location
  const {
    consultationId, // 指导ID
    consultationType, // 指导类型，1 图文，2 视频
    copyUserId, // H5复制链接携带的用户ID
  } = query

  const initialState = {}
  const initialFormState = {
    agreementIsChecked: false,                   // 协议勾选框
    firstQuestion: '', // 用户提问
    type: '', // 指导类型，1图文，2视频
  }
  const [state, setState] = useState(initialState) // 指导详情数据state
  const [formState, setFormState] = useState(initialFormState) // 表单state
  const [agreementVisible, setAgreementVisible] = useState(false)    // 用户协议弹窗
  const [submitSuccessModalVisible, setSubmitSuccessModalVisible] = useState(false) // 提交成功弹窗
  const [selectPayMethodModalVisible, setSelectPayMethodModalVisible] = useState(false) // 选择支付方式弹窗
  const [payModalVisible, setPayModalVisible] = useState(false) // 支付弹窗
  const [payType, setPayType] = useState(null) // 1 微信，2 支付宝，3免费次数

  useEffect(() => {
    if (consultationId) {
      getConsultationAndCaseInfo()
    } else {
      message.error('指导ID缺失')
    }
  }, [])

  // 查询指导和病例详情
  const getConsultationAndCaseInfo = () => {
    dispatch({
      type: 'consultation/getConsultationAndCaseInfo',
      payload: {
        consultationId: consultationId,                    // 指导ID
        type: 1,                                           // (1:图文支付/视频提交, 2:其它通用详情)
      }
    }).then(res => {
      const { code, content, msg } = res

      // 除专家和用户之外的第三人查看，提示
      if (code == 422) {
        Modal.warning({
          content: msg,
          onOk: () => {
            goBack()
          }
        })
        return
      }
      // 专家也不能看
      if (content && content.createUserId != UserInfo.friUserId) {
        Modal.warning({
          content: '对不起，您无权限查看该数据！',
          onOk: () => {
            goBack()
          }
        })
        return
      }

      // 流程节点
      // 图文[1选择指导方式、2描述病例问题、3支付指导费用、4病例资料被查看、5问题被回复并对话、6结束指导交易成功];
      // 视频[1选择指导方式、2描述病例问题、3提交指导单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、7结束指导、8确认并支付指导费用、9交易成功]
      if (content && content.processNode > 3) {
        Modal.warning({
          content: '链接已失效，可能因为指导订单已经提交成功，请前往我的指导查看订单',
          onOk: () => {
            goBack()
          }
        })
        return
      }

      if (code == 200 && content) {
        // 在5i5ya的iframe中
        if (isInIframe) {
          const postData = {
            dataType: 'pathname',       // 页面地址onchange事件
            pathnameByChild: pathname,  // 路由信息
            searchByChild: `?${stringify({
              ...query,
              expertsUserId: content.expertsId, // 专家ID
              consultationType: content.type, // 指导类型，1图文，2视频
              orderCaseTemplate: content.orderCaseTemplate, // 1通用模板，2正畸模板
            })}`,  // 路由信息
          }
          console.log('子级发送数据：', postData, getArrailUrl())
          window.parent.postMessage(postData, getArrailUrl())
        }
        // 更新一下地址
        history.replace({
          pathname,
          query: {
            ...query,
            expertsUserId: content.expertsId, // 专家ID
            consultationType: content.type, // 指导类型，1图文，2视频
            orderCaseTemplate: content.orderCaseTemplate, // 1通用模板，2正畸模板
          }
        })

        setState(content)
        setFormState({
          ...formState,
          firstQuestion: content.consultationCaseInfoDto && content.consultationCaseInfoDto.firstQuestion || '', // 用户提问
          type: content.type, // 指导类型，1图文，2视频
        })
      } else {
        message.error(msg || '查询指导和病例详情失败')
      }
    }).catch(err => {})
  }

  // 输入初始提问
  const onChangeFirstQuestion = (e) => {
    setFormState({
      ...formState,
      firstQuestion: e.target.value,
    })
  }

  // 修改指导类型
  const onChangeType = (e) => {
    setFormState({
      ...formState,
      type: e.target.value,
    })
  }

  // 协议勾选框修改
  const agreementOnChange = (value) => {
    setFormState({
      ...formState,
      agreementIsChecked: value.length > 0,
    })
  }

  // 服务协议
  const agreementModalShow = (e) => {
    // 避免选中勾选框
    e.stopPropagation()
    e.preventDefault()
    setAgreementVisible(true)
  }

  // 服务协议弹窗，关闭
  const agreementModalHide = () => {
    setAgreementVisible(false)
  }

  // 修改会诊用户提问且同步病历问题
  const updateCaseInfoQuestion = () => {
    return dispatch({
      type: 'consultation/updateCaseInfoQuestion',
      payload: {
        userName: UserInfo.name, // 用户名
        wxUserId: UserInfo?.friUserId, // 用户ID
        id: consultationId, // 指导ID
        firstQuestion: formState.firstQuestion.trim(), // 用户提问
        type: formState.type, // 指导类型，1图文，2视频
      }
    }).then(res => {
      return res
    }).catch(err => {
      return null
    })
  }

  // 提交成功弹窗关闭
  const submitSuccessModalClose = () => {
    setSubmitSuccessModalVisible(false)
    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'pathname',       // 页面地址onchange事件
        pathnameByChild: '/ConsultationModule/ConsultationDetails',  // 路由信息
        searchByChild: `?${stringify({
          consultationId,                      // 指导ID
          consultationType: formState.type, // 指导类型，1 图文，2 视频
        })}`,  // 路由信息
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
      return
    }
    history.replace({
      pathname: '/ConsultationModule/ConsultationDetails',
      query: {
        consultationId,                      // 指导ID
        consultationType: formState.type, // 指导类型，1 图文，2 视频
      }
    })
  }

  // 选择支付方式弹窗关闭
  const selectPayMethodModalClose = () => {
    setSelectPayMethodModalVisible(false)
  }

  // 提交
  const submit = async () => {
    if (!(formState.firstQuestion && formState.firstQuestion.trim())) {
      message.error('请输入提问问题')
      return
    }

    const res = await updateCaseInfoQuestion()
    const { code, content, msg } = res || {}
    if (code == 200 && content) {

    } else {
      message.error(msg || '数据加载失败')
      return
    }

    // 图文
    if (formState.type == 1) {
      setSelectPayMethodModalVisible(true)
    } else if (formState.type == 2) {
      // 视频
      editConsultationNodeAndStatus()
    }
  }

  // 图文指导
  const submitConsultationPictureOrderPay = async () => {
    await dispatch({
      type: 'consultation/submitConsultationPictureOrderPay',
      payload: {
        id: consultationId,                      // 订单id
        amount: 0,                               // 订单金额
        freeTimes: 1,                            // 免费次数
        payMethod: 1,                            // 图文 1提交指导 2立即支付
        operationPlatform: 2,                    // 操作平台 1H5 2PC
        // payType: null,                        // 1微信、2支付宝
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200) {
        // 在5i5ya的iframe中
        if (isInIframe) {
          const postData = {
            dataType: 'pathname',       // 页面地址onchange事件
            pathnameByChild: '/ConsultationModule/ConsultationDetails',  // 路由信息
            searchByChild: `?${stringify({
              consultationId,                      // 指导ID
              consultationType: formState.type,                    // 指导类型，1 图文，2 视频
            })}`,  // 路由信息
          }
          console.log('子级发送数据：', postData, getArrailUrl())
          window.parent.postMessage(postData, getArrailUrl())
          return
        }
        history.replace({
          pathname: '/ConsultationModule/ConsultationDetails',
          query: {
            consultationId,                      // 指导ID
            consultationType: formState.type, // 指导类型，1 图文，2 视频
          }
        })
      } else {
        message.error(msg || '提交失败')
      }
    }).catch(err => {})
  }

  // 视频指导
  const editConsultationNodeAndStatus = () => {
    dispatch({
      type: 'consultation/editConsultationNodeAndStatus',
      payload: {
        consultationId: consultationId,          // 指导ID
        type: 1,                                 // 1:先体验后付费, 2:图文或视频病例被查看, 3:图文问题被回复并对话, 4:图文结束指导交易成功, 5:视频预约视频会议, 6:视频沟通, 7:结束指导
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200) {
        setSubmitSuccessModalVisible(true)
      } else {
        message.error(msg || '提交失败')
      }
    }).catch(err => {})
  }

  // 确认支付
  const onClickPayBtn = async (checkedPayMethod) => {
    if (checkedPayMethod == 3) {
      await submitConsultationPictureOrderPay()
      setSelectPayMethodModalVisible(false)
    } else {
      setSelectPayMethodModalVisible(false)
      // 打开在线支付弹窗
      setPayModalVisible(true)
      setPayType(checkedPayMethod)
    }
  }

  // 支付弹窗关闭
  const payModalClose = (status) => {
    setPayModalVisible(false)
    // 表示有了支付结果，支付成功关闭弹窗默认去订单列表页
    if (status == true) {
      dispatch({
        type: 'pcAccount/save',
        payload: {
          tabState: 6,
          subTabState: 1,
        }
      })
      history.replace('/UserInfo')
    }
  }

  // 病例卡片点击编辑，返回上一步
  const onClickEditBtn = () => {
    if (state.orderCaseTemplate == 1) {
      // 在5i5ya的iframe中
      if (isInIframe) {
        const postData = {
          dataType: 'pathname',       // 页面地址onchange事件
          pathnameByChild: '/ConsultationModule/StartConsultation/Step3',  // 路由信息
          searchByChild: `?${stringify({
            ...query,
          })}`,  // 路由信息
        }
        console.log('子级发送数据：', postData, getArrailUrl())
        window.parent.postMessage(postData, getArrailUrl())
        return
      }
      history.replace({
        pathname: '/ConsultationModule/StartConsultation/Step3',
        query: {
          ...query,
        }
      })
    } else {
      // 在5i5ya的iframe中
      if (isInIframe) {
        const postData = {
          dataType: 'pathname',       // 页面地址onchange事件
          pathnameByChild: '/CreationOrthodontics/Step1',  // 路由信息
          searchByChild: `?${stringify({
            orthodonticConsultationId: consultationId, // 指导ID
          })}`,  // 路由信息
        }
        console.log('子级发送数据：', postData, getArrailUrl())
        window.parent.postMessage(postData, getArrailUrl())
        return
      }
      history.replace(`/CreationOrthodontics/Step1?orthodonticConsultationId=${consultationId}`)
    }
  }

  // 返回
  const goBack = () => {
    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'goBack',       // 页面地址onchange事件
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
      return
    }

    if (copyUserId || history.length <= 2) {
      history.replace('/')
    } else {
      history.goBack()
    }
  }

  // 附件展示
  const annexCaseFun = (id: number) => {
    const {
      consultationCaseInfoDto
    } = state || {}
    const {
      orthodonticCaseDictDtoList
    } = consultationCaseInfoDto || {}

    let arr = []
    if (Array.isArray(orthodonticCaseDictDtoList) && orthodonticCaseDictDtoList.length) {
      const result = orthodonticCaseDictDtoList.find(item => item.id === id);
      const { subsetList } = result || {}
      console.log(subsetList)
      if(Array.isArray(subsetList) && subsetList.length){
        if(Array.isArray(subsetList[0].subsetList) && subsetList[0].subsetList.length){
          arr = subsetList[0].subsetList
        }
      }
    }

    return arr
  }

  // loading
  const getConsultationAndCaseInfoLoading = !!loading.effects['consultation/getConsultationAndCaseInfo']
  const submitConsultationPictureOrderPayLoading = !!loading.effects['consultation/submitConsultationPictureOrderPay']
  const editConsultationNodeAndStatusLoading = !!loading.effects['consultation/editConsultationNodeAndStatus']
  const loadingUpdateCaseInfoQuestion = !!loading.effects['consultation/updateCaseInfoQuestion']

  return (
    <>
      <div className={styles.container}>
        {/* iframe中隐藏header */}
        {
          isInIframe ? null : <PcHeader/>
        }

        <div className={styles.content}>
          <div className={styles.content_inner}>
            <div className={styles.header}>
              <div className={styles.header_icon} onClick={goBack}></div>
              <div className={styles.header_title}>发起专家指导</div>
            </div>

            <div className={styles.box}>
              <StartConsultationSteps title="提交病例"/>

              <Spin spinning={getConsultationAndCaseInfoLoading}>
                <div className={styles.step_container}>
                  <div className={styles.step_content}>
                    <div className={styles.container_left}>
                      <div className={styles.case_scroll_wrap}>
                        {/* 病例卡片 */}
                        <ConsultationCaseCard
                          caseData={state.consultationCaseInfoDto}
                          consultationId={consultationId}
                          isShowBtn={state.createUserId == UserInfo.friUserId}
                          onClickEditBtn={onClickEditBtn}
                          orderCaseTemplate={state.orderCaseTemplate}
                        />

                        {/* 其他文件 */}
                        <OtherInformation
                          fileData={
                            state.orderCaseTemplate == 1 ? state.consultationCaseInfoDto && state.consultationCaseInfoDto.consultationCaseMediaDtoList
                              : annexCaseFun(9)
                          }
                        />
                      </div>
                    </div>
                    <div className={styles.container_right}>
                      <div className={styles.expert_card_wrap}>
                        {/* 专家卡片 */}
                        <ExpertCard
                          data={{
                            ...state,
                            thisUserIsExperts: 0,
                          }}
                        />
                      </div>

                      <div className={classNames(styles.block, styles.user_question)}>
                        <div className={styles.block_title}>用户提问</div>
                        <div className={styles.question_box}>
                          <Input.TextArea
                            className={styles.question_input}
                            placeholder="请输入针对以上病例您想要问的问题"
                            value={formState.firstQuestion}
                            onChange={onChangeFirstQuestion}
                            maxLength={200}
                            showCount
                            autoSize={{minRows: 3, maxRows: 3}}
                          />
                        </div>
                      </div>

                      <div className={styles.block} style={{height: 299}}>
                        <div className={styles.block_title}>专家指导明细</div>
                        <div className={styles.block_label}>指导类型</div>
                        <div className={styles.consultation_type}>
                          <Checkbox.Group value={formState.type ? [formState.type] : []}>
                            <Checkbox value={1} onChange={onChangeType}>图文指导</Checkbox>
                            <Checkbox value={2} onChange={onChangeType}>视频指导</Checkbox>
                          </Checkbox.Group>
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* 创建人可见，医生不可见 */}
                  {
                    state.createUserId == UserInfo.friUserId &&
                    <>
                      {
                        formState.type == 2 &&
                        <div className={styles.step_bottom}>提交订单1个工作日后,会有客服联系您~</div>
                      }

                      {/* 按钮 */}
                      <div className={styles.step_btn_box}>
                        <Checkbox.Group onChange={agreementOnChange} value={formState.agreementIsChecked ? [1] : []}>
                          <Checkbox value={1}>我已阅读并同意<span onClick={agreementModalShow} className={styles.highlight}>《FRIDAY服务协议》</span></Checkbox>
                        </Checkbox.Group>
                        <Button
                          className={styles.pay_btn}
                          type="primary"
                          disabled={!formState.agreementIsChecked}
                          onClick={submit}
                          loading={editConsultationNodeAndStatusLoading || loadingUpdateCaseInfoQuestion}
                        >提交</Button>
                      </div>
                    </>
                  }
                </div>
              </Spin>


            </div>
          </div>
        </div>

      </div>


      {/* 服务协议弹窗 */}
      <AgreementModal
        visible={agreementVisible}
        onCancel={agreementModalHide}
      />

      {/* 提交成功弹窗 */}
      <SubmitSuccessModal visible={submitSuccessModalVisible} onCancel={submitSuccessModalClose}/>
      {/* 选择支付方式弹窗 */}
      <SelectPayMethodModal
        visible={selectPayMethodModalVisible}
        usableFreeTimes={state.usableFreeTimes}
        vipUnitPrice={state.vipUnitPrice}
        onCancel={selectPayMethodModalClose}
        onClickPayBtn={onClickPayBtn}
        loading={submitConsultationPictureOrderPayLoading}
      />
      {/* 支付弹窗 */}
      {
        payModalVisible &&
        <PayModal
          visible={payModalVisible}
          consultationId={consultationId}
          consultationType={formState.type}
          payType={payType}
          onCancel={payModalClose}
        />
      }
    </>
  )
}

export default connect(({ consultation, loading }: any) => ({ consultation, loading }))(Index)
