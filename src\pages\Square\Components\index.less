// 收藏
.collect_content {
  padding-top: 40px;
}
.collect_tab_box {
  position: fixed;
  top: 54px;
  left: 0;
  right: 0;
  background: #fff;
  z-index: 990;
  width: 100%;
  height: 40px;
  padding-left: 12px;
  border-top: 1px solid #E9EEF2;
  border-bottom: 1px solid #E9EEF2;
  display: flex;
  &.pc {
    max-width: 750px;
    margin: 0 auto;
  }

  .tab_active {
    font-size: 14px;
    font-weight: 400;
    color: #0095FF;
    line-height: 40px;
    margin-right: 24px;
  }

  .tab_init {
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    line-height: 40px;
    margin-right: 24px;
  }
}

.gray_bar {
  height: 8px;
  background: #F5F6F8;
}

.collect_space_list_wrap {
  padding-top: 12px;
}

.collect_meeting_list_wrap {
  padding: 16px 12px 0;
  .collect_meeting_item_wrap {
    border-bottom: 1px solid #E1E4E7;
    margin-bottom: 16px;
  }
}

.squareSpaceList_box{
  background: #fff;
  min-height: calc(100vh - 125px);
}
.meetingList_box{
  position: relative;
  height: calc(100vh - 112px);
  .meetingList_tab{
    height: 40px;
    display: flex;
    align-items: center;
    padding-left: 12px;
    background: #fff;
    border-top: 1px solid #E9EEF2;
    border-bottom: 1px solid #E9EEF2;
    span{
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      line-height: 40px;
      margin-right: 24px;
    }
    .tab_active{
      color: #0095FF;
    }
  }
  .meetingList_content {
    height: calc(100vh - 112px);
    overflow-y: scroll;
    .meetingList_item{
      margin-bottom: 16px;
      padding:0 12px;
      h3{
        margin-bottom: 2px;
        font-size: 16px;
        font-weight: 600;
        line-height: 22px;
      }
      h4{
        font-size: 14px;
        line-height: 20px;
        margin-bottom: 4px;
      }
      .item_myMeeting_box{
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #fff;
        padding:0 12px 12px;
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
        .item_moreOperate_btn{
          color: #999999;
        }
        .item_poster_btn{
          display: inline-block;
          padding: 4px 16px;
          color: #0095FF;
          background: #E6F4FF;
          border-radius: 24px;
          margin-right: 8px;
        }
        .item_share_btn,.item_copy_btn{
          display: inline-block;
          padding: 4px 16px;
          color: #FFFFFF;
          background: #0095FF;
          border-radius: 24px;
        }
        .item_copy_btn{
          :global {
            .ant-typography {
              margin-bottom: 0;
            }
            .ant-typography-copy {
              color: #FFFFFF;
            }
          }
        }
      }
    }
    .meeting_nologin{
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-top: 126px;
      height: 50%;
      img{
        width: 150px;
        height: 113px;
      }
      p{
        font-size: 18px;
        font-weight: 900;
        margin-bottom:40px;
      }
      Button{
        width: 96px;
        height: 40px;
        background: #0095FF;
        border-radius: 20px 20px 20px 20px;
      }
    }
  }
}
.meetingList_box_bgWhite{
  background: #fff;
}
