import React, {useState,} from 'react';
import {connect} from 'umi';
import styles from './index.less';
import Stream from '@/componentsByTRTC/Stream';
import {
  getCameralistArr,
  getHandUpRemoteStreamList,
  getHostRemoteStreamConfig,
  getIsModeMatrixCameraRemoteStreamList,
  getShareRemoteStreamConfig,
  getUserCameraRemoteStreamList,
  getUserInfoData,
} from '@/utils/utilsByTRTC';

type propsType = {
  global: any;
};

const Index: React.FC<propsType> = (props) => {
  const {localStreamConfig, RTC, remoteStreamConfigList, PlanetChatRoom} = props || {};

  const {
    SpaceInfo,
    handUpList,
  } = PlanetChatRoom || {};

  const {
    hostUserInfo,
  } = SpaceInfo || {};

  const userInfoData = getUserInfoData();
  const shareRemoteStreamConfig = getShareRemoteStreamConfig(SpaceInfo, remoteStreamConfigList);

  const hostRemoteStreamConfig = getHostRemoteStreamConfig(
    SpaceInfo,
    hostUserInfo,
    remoteStreamConfigList,
  );

  const userCameraRemoteStreamList = getUserCameraRemoteStreamList(
    SpaceInfo,
    remoteStreamConfigList,
  );

  const isModeMatrixCameraRemoteStreamList = getIsModeMatrixCameraRemoteStreamList(
    SpaceInfo,
    hostUserInfo,
    handUpList,
    userCameraRemoteStreamList,
  );

  const handUpRemoteStreamList = getHandUpRemoteStreamList(
    SpaceInfo,
    handUpList,
    userCameraRemoteStreamList,
  );

  let cameralistArr = getCameralistArr(
    hostRemoteStreamConfig,
    localStreamConfig,
    handUpRemoteStreamList,
    isModeMatrixCameraRemoteStreamList,
  );

  const [randomNum, setRandomNum] = useState(Math.random());
  return (
    <>
      <div className={styles.StreamWarp}>
        {shareRemoteStreamConfig && shareRemoteStreamConfig.stream && (
          <Stream
            key={`${shareRemoteStreamConfig.stream.getUserId()}_${shareRemoteStreamConfig.stream.getType()}_${randomNum}`}
            stream={shareRemoteStreamConfig.stream}
            config={shareRemoteStreamConfig}
            init={(dom) => RTC && RTC.playStream(shareRemoteStreamConfig.stream, dom)}
          ></Stream>
        )}
      </div>
    </>
  );
};
export default connect(({ global, PlanetChatRoom, loading }: any) => ({
  global,
  PlanetChatRoom,
  loading,
}))(Index);
