.modal {
  :global {
    .ant-modal-header {
      padding: 16px;
    }
    .ant-modal-body {
      padding: 24px 16px 0;
    }
  }
}

.title {
  font-weight: 500;
  font-size: 14px;
  color: #000;
  line-height: 20px;
  margin-bottom: 12px;
}

.checkbox_wrap {
  margin-bottom: 56px;
  :global {
    .ant-checkbox-inner, .ant-checkbox-checked::after {
      border-radius: 50%;
    }
    .ant-checkbox-wrapper {
      color: #000;
    }
    .ant-checkbox-wrapper + .ant-checkbox-wrapper {
      margin-left: 40px;
    }
    .ant-checkbox-wrapper-checked {
      color: #0095FF;
    }
  }
  .option_text {
    margin-right: 40px;
    font-size: 14px;
    color: #999;
  }
}

.footer_wrap {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  padding: 8px 0 16px;
  .price_wrap {
    font-weight: 500;
    font-size: 14px;
    color: #000;
    flex-shrink: 0;
    white-space: nowrap;
    .price_value {
      color: #FF5F57;
    }
  }
  .btn_wrap {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    column-gap: 10px;
    :global {
      .ant-btn {
        border-radius: 4px;
      }
    }
  }
}

