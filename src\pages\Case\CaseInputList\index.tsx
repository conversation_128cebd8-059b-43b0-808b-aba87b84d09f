/**
 * @Description: 病例列表首页
 * @author: 赵斐
 */
import React, { useState, useEffect ,useRef} from 'react';
import { connect, history } from 'umi';
import { InfiniteScroll } from 'antd-mobile'
import { Spin } from 'antd'
import classNames from 'classnames';
import { getOperatingEnv } from '@/utils/utils'
import searchIcon from '@/assets/GlobalImg/search.png'; // 搜索小图标
import imageTextIcon from '@/assets/Case/image_text.png'; // 图文切换小图标
import styles from './index.less';
// 病例列表组件
import CaseList from '@/components/CaseList';
// 数据加载异常
import LoadingException from '@/components/LoadingException'
// 导航组件
import NavBar from '@/components/NavBar'

const initState = {
  total: 0,            // 总条数
  dataSource: [],      // 病例数据集合
}
const Index: React.FC = (props: any) => {
  const { dispatch, loading ,cases, } = props;
  const { imgShowOrHide } = cases
  const listRef = useRef<any>(null);
  const [state, setState] = useState(initState)                   // 专家列表数据
  const [interfaceStatus, setInterfaceStatus] = useState(2);      // 接口状态
  const [isShowImage, setIsShowImage] = useState(imgShowOrHide);  // 是否展示图片
  const [statePageNum, setStatePageNum] = useState(1)             // 当前分页

  const {
    total,
    dataSource,
  } = state

  useEffect(() => {
    getExcellentCaseList(1)
  }, [])

  /**
   * 获取优秀病例列表数据
   * @param pageNum 当前第几页
   */
  //
  const getExcellentCaseList = async (pageNum: number) => {
    await dispatch({
      type: "cases/getExcellentCaseList",
      payload: {
        pageNum,
        pageSize:30,
      }
    }).then((res: any) => {
      if (res && res.code == 200) {
        const { content } = res || {};
        const { total, resultList } = content || {};
        let data = pageNum == 1 ? [] : dataSource;
        data = data.concat(resultList);
        setStatePageNum(pageNum)
        if (Array.isArray(data) && data.length == 0) {
          setInterfaceStatus(2)
          return
        }
        setState({
          ...state,
          dataSource: [...data],
          total,
        })
      } else {
        setInterfaceStatus(1)
      }
    }).catch((err: String) => {
      console.log(err)
      setInterfaceStatus(1)
    })
  }

  /**
   * 图文模式
   * @param status  false、true
   */
  const onClickImgTextFun = (status: boolean) => {
    setIsShowImage(status)
    dispatch({
      type: "cases/imgShowOrHide",
      payload: {
        isShow: status ? 2 : 1,
      }
    })
    dispatch({
      type: "cases/save",
      payload: {
        imgShowOrHide: status,
      }
    })
  }

  // 加载更多数据
  let loadMore = async () => {
    await getExcellentCaseList(statePageNum + 1)
  }

  // 接口调用异常重新调用列表接口
  const retryFun = () => {
    getExcellentCaseList(1)
  }

  // 重置接口异常状态
  const resetStatusFun = ()=>{
    setInterfaceStatus(2)
  }

  // 跳转病例搜索页
  const goToUrl = () => {
    history.push('/Case/CaseSearch')

    // 解决ios中搜索页input框不能自动聚焦问题
    document.getElementById('input_ios_focus') && document.getElementById('input_ios_focus').focus()
  }

  const load = !!loading.effects['cases/getExcellentCaseList'] ||   // 病例列表接口loading
               !!loading.effects['cases/imgShowOrHide']             // 图文模式记录接口loading
  return (
    <Spin spinning={load}>
      <div className={styles.wrap}>
        <div>
          <NavBar title={"查病例"}/>
        </div>
        <div className={classNames(styles.input_wrap, {
          [styles.input_wrap_pc]: getOperatingEnv() == 4,
        })}>
          <div className={styles.input_box} onClick={goToUrl}><img src={searchIcon} alt="" />搜病例相关内容</div>
          <div className={classNames({ [styles.img_mode_box]: true, [styles.img_mode_box_active]: isShowImage })} onClick={() => { onClickImgTextFun(!isShowImage) }}><img src={imageTextIcon} alt="" />图文模式</div>
        </div>
        <div className={styles.expert_result_box} ref={listRef}>
          {
            Array.isArray(dataSource) && dataSource.length ? <CaseList componentData={{ dataList: dataSource }} isShowImage={isShowImage} style={{padding: '16px 12px 4px'}} /> :
              <LoadingException exceptionStyle={{ paddingTop: 110 }} interfaceStatus={interfaceStatus} retryFun={retryFun} resetStatusFun={resetStatusFun}/>
          }
          {total>0 && <InfiniteScroll loadMore={loadMore} hasMore={total > dataSource.length} threshold={100} />}
        </div>
      </div>
    </Spin>
  )
}
export default connect(({ cases, loading }: any) => ({ cases, loading }))(Index)


