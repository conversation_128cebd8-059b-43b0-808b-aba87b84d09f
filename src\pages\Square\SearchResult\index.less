//.pc_container {
//  background: #f1f2f4;
//}
//.pc_content {
//  max-width: 750px;
//  margin: 0 auto;
//  background: #fff;
//}
.container {
  height: 100vh;
  padding-top: 96px;
  padding-bottom: 20px;
  overflow-y: auto;
}
.header_box {
  position: fixed;
  z-index: 999;
  top: 0px;
  left: 0;
  right: 0;
  background: #fff;

  .header_box_content {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    padding-left: 10px;
  }

  &.header_box_pc {
    max-width: 750px;
    margin: 0 auto;
  }
  .tabs_box {
    display: flex;
    justify-content: space-between;
    padding: 0 8px 0 4px;
    border-bottom: 1px solid #E9EEF2;
    height: 40px;
    line-height: 39px;
    font-size: 14px;
    color: #666;
    .tabs_data_box {
      display: flex;
      flex-wrap: nowrap;
      white-space: nowrap;
      overflow-x: auto;
      & > div {
        padding: 0 5px;
      }
      .checked {
        color: #0095FF;
      }
    }
    .tabs_btn_box {
      display: flex;
      align-items: center;
      flex-wrap: nowrap;
      white-space: nowrap;
      & > img {
        margin-left: 2px;
        margin-top: -3px;
      }
      &.checked {
        color: #0095FF;
      }
    }
  }
  .filter_box {
    display: none;
    position: absolute;
    top: 96px;
    left: 0;
    right: 0;
    background: #fff;
    padding: 12px 6px 16px;
    max-height: calc(100vh - 96px);
    overflow-y: auto;
    &.filter_box_show {
      display: block;
    }
    .filter_title {
      font-size: 14px;
      color: #999;
      line-height: 20px;
      margin-bottom: 12px;
      padding: 4px 6px 0;
    }
    .filter_content {
      display: flex;
      flex-wrap: wrap;
      position: relative;
      .filter_item_box {
        padding: 0 6px;
        width: 33.33%;
        margin-bottom: 16px;
      }
      .filter_item {
        text-align: center;
        height: 32px;
        line-height: 32px;
        font-size: 14px;
        color: #666;
        background: #F5F5F5;
        border-radius: 16px;
        &.checked {
          background: #EDF9FF;
          color: #0095FF;
        }
      }
      &.filter_content_long {
        .filter_item_box {
          width: 50%;
        }
        .filter_item_separator {
          position: absolute;
          top: 16px;
          left: 50%;
          transform: translateX(-50%);
          width: 6px;
          height: 1px;
          background: #CCC;
        }
      }
      &.filter_content_time {
        .filter_item {
          color: #ccc;
          &.value {
            color: #999;
          }
        }
      }
    }
    .filter_btn_box {
      display: flex;
      text-align: center;
      line-height: 40px;
      font-size: 15px;
      font-weight: 500;
      margin-top: 24px;
      & > div {
        flex: 1;
        height: 40px;
        border-radius: 22px;
      }
      .filter_btn_cancel {
        background: #EDF9FF;
        color: #0095FF;
        margin-right: 16px;
      }
      .filter_btn_ok {
        background: #0095FF;
        color: #fff;
      }
    }
  }
}
.space {
  width: 100%;
  height: 8px;
  background: #F5F6F8;
}
.result_box {
  padding: 12px 12px 0;
  margin-bottom: 12px;
  .title_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    .title {
      line-height: 25px;
      font-size: 16px;
      font-weight: 600;
      color: #000;
      position: relative;
      &::after {
        content: "";
        display: block;
        position: absolute;
        left: 0;
        right: 0;
        bottom: -3px;
        height: 10px;
        border-radius: 10px;
        background: linear-gradient(90deg, #0095FF 0%, rgba(255,255,255,0) 100%);
      }
    }
    .title_btn_box {
      display: flex;
      align-items: center;
      font-size: 13px;
      color: #999;
    }
  }

  // 帖子下子tab
  .child_title_box {
    span{
      display: inline-block;
      font-size: 14px;
      color: #666;
      line-height: 14px;
      padding-bottom: 12px;
      margin-right: 12px;
      &.checked {
        color: #0095ff;
      }
    }
  }

  // 帖子、文章、外链wrap
  .result_wrap {
    background: #F5F6F8;
    margin: 0 -12px;
  }

  // 直播wrap
  .item_box {
    display: flex;
    flex-wrap: nowrap;
    margin-bottom: 12px;
    .left {
      position: relative;
      width: 36.75%;
      min-width: 36.75%;
      height: 73px;
      border-radius: 4px;
      margin-right: 8px;
      overflow: hidden;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      background-image: url("https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/PlanetChatRoom_HorizontalLiveRoom_Live_bg.png");
      .left_status_box {
        position: absolute;
        z-index: 666;
        top: 8px;
        right: 8px;
        padding: 0 4px;
        height: 18px;
        line-height: 18px;
        border-radius: 15px;
        background: linear-gradient(135deg, #4183EA 0%, #003AAF 100%);
        display: flex;
        align-items: center;
        flex-wrap: nowrap;
        white-space: nowrap;
        color: #fff;
        font-size: 10px;
        & > img {
          margin-right: 2px;
        }
      }
      // 封面中的标题
      .title_in_cover_image {
        position: absolute;
        z-index: 600;
        width: 56%;
        top: 14px;
        left: 0;
        padding-left: 8px;
        font-size: 12px;
        line-height: 16px;
        color: #fff;
        font-weight: 500;
        word-break: break-all;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2; /* 指定显示行数 */
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .right {
      flex: 1;
      overflow: hidden;
      .space_name {
        font-size: 14px;
        color: #000;
        font-weight: 600;
        line-height: 20px;
        margin-bottom: 7px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2; /* 指定显示三行 */
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
      }
      .space_intro {
        font-size: 11px;
        font-weight: 400;
        color: #999999;
        line-height: 13px;
        margin-bottom: 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-top: 4px;
      }
      .space_info_style {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .user_box {
        display: flex;
        align-items: center;
        margin-right: 8px;
        .avatar {
          position: relative;
          width: 23px;
          height: 23px;
          border-radius: 50%;
          margin-right: 8px;
          background-repeat: no-repeat;
          background-position: center;
          background-size: cover;
          font-size: 10px;
          font-weight: 500;
          color: #fff;
          text-align: center;
          line-height: 24px;
          white-space: nowrap;
          min-width: 23px;
          & > img {
            position: absolute;
            top: -3px;
            right: -3px;
          }
        }
        .name {
          font-size: 13px;
          color: #666;
          line-height: 18px;
          max-width: 70px;
          min-width: 50px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .gdp {
        font-size: 11px;
        color: #999;
        line-height: 15px;
        flex-shrink: 0;
      }
    }
  }

  // 会议wrap
  .meeting_item_wrap {
    margin-bottom: 16px;
    border-bottom: 1px solid #E1E4E7;
  }

  .item_empty_box {
    padding-top: 15px;
    text-align: center;
    padding-bottom: 4px;
    & > img {
      display: block;
      margin: 0 auto 4px;
    }
    .empty_title {
      font-size: 15px;
      color: #333;
      line-height: 21px;
      margin-bottom: 8px;
    }
    .empty_msg {
      font-size: 14px;
      color: #999;
      line-height: 20px;
    }
  }
}

.nav_bar_icon {
  width: 12px;
  height: 24px;
  background: url("../../../assets/GlobalImg/go_back.png")  no-repeat;
  background-size: 12px 24px;
}
