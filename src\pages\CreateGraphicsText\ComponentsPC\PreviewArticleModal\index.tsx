import React, { useEffect } from 'react'
import dayjs from 'dayjs'
import { processNames, randomColor, useThrottle } from '@/utils/utils'
import { Button, Modal } from 'antd'
import { PlusOutlined, CheckCircleOutlined } from '@ant-design/icons'
import styles from './index.less'


interface PropsType {
  visible: boolean,                    // 弹窗是否显示
  previewArticleModalType: number,     // 弹窗类型
  articleLength: number,               // 字数
  imageTitle: string,                  // 标题
  imageTextContent: string,            // 内容
  handlePublish: () => {},
  onCancel: () => {},
  loadingSave: boolean,
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')        // 用户信息

  const {
    visible,
    previewArticleModalType,
    articleLength,
    imageTitle,
    imageTextContent,
    loadingSave,
  } = props

  // 确定
  let onClickOk = () => {
    props.handlePublish()
  }
  onClickOk = useThrottle(onClickOk, 500)

  return (
    <Modal
      visible={visible}
      className={styles.modal}
      onCancel={props.onCancel}
      destroyOnClose
      footer={null}
      width="100%"
    >
      <div className={styles.container}>
        <div className={styles.content}>
          <div className={styles.iphone_box}>
            <div className={styles.header}>
              <i></i>
            </div>
            <div className={styles.middle}>
              <div className={styles.article_title}>{imageTitle || '标题'}</div>
              <div className={styles.article_top}>
                <div className={styles.article_top_user}>
                  <i style={UserInfo.headUrl ? {backgroundImage: `url(${UserInfo.headUrl})`} : {backgroundColor: `${randomColor(UserInfo.friUserId)}`}}>
                    {UserInfo.headUrl ? '' : processNames(UserInfo.name)}
                  </i>
                  <div>
                    <div className={styles.user_name}>{UserInfo.name}</div>
                    <div className={styles.date}>{dayjs().format('MM-DD')}</div>
                  </div>
                </div>
                <div className={styles.article_top_btn}>
                  <PlusOutlined />
                  <span>关注</span>
                </div>
              </div>
              <div className={styles.article_details}>
                <div className="ql-editor" dangerouslySetInnerHTML={{__html: imageTextContent}}></div>
              </div>
            </div>
            <div className={styles.bottom}>
              <div className={styles.bottom_toolbar}>
                <div className={styles.toolbar_item}>
                  <i className={styles.icon_like}></i>
                  <span>10</span>
                </div>
                <div className={styles.toolbar_item}>
                  <i className={styles.icon_comment}></i>
                  <span>20</span>
                </div>
                <div className={styles.toolbar_item}>
                  <i className={styles.icon_forward}></i>
                  <span>转发</span>
                </div>
              </div>
              <div className={styles.bottom_gray_bar}></div>
            </div>
          </div>
          <div className={styles.tips}>仅支持预览</div>
        </div>
        {
          previewArticleModalType == 2 &&
          <div className={styles.footer}>
            <div className={styles.footer_content}>
              <div className={styles.footer_content_left}>
                <CheckCircleOutlined />
                <span>草稿已保存</span>
                <span>共{articleLength}字</span>
              </div>
              <div className={styles.footer_content_right}>
                <Button size="large" onClick={props.onCancel}>返回编辑</Button>
                <Button type="primary" size="large" onClick={onClickOk} loading={loadingSave}>确认提交</Button>
              </div>
            </div>
          </div>
        }

      </div>
    </Modal>
  )
}

export default Index
