import React from 'react'
import styles from './Emoji.less'
// 表情包相关数据
import { emojiMap, emojiName } from '@/emoticon'
import { emojiUrl } from '@/utils/utils'

class Emoji extends React.Component {
  static defaultProps = {
    emojiOnClick: () => {},
  }

  constructor(props) {
    super(props)
    this.state = {}
  }

  componentDidMount(): void {

  }

  // 点击表情
  emojiOnClick = (emojiFileName) => {
    this.props.emojiOnClick(emojiFileName)
  }

  render() {
    return (
      <div className={styles.emoji_container}>
        <div className={styles.emoji_title}>全部表情</div>
        <div className={styles.emoji_content}>
          {
            emojiName.map(item => (
              <div key={item} className={styles.emoji_item} onClick={() => this.emojiOnClick(emojiMap[item])}>
                <img src={`${emojiUrl}${emojiMap[item]}`} width={32} height={32} alt={item} title={item}/>
              </div>
            ))
          }
        </div>
      </div>
    )
  }
}

export default Emoji
