/**
 * @Description: PC端，删除提示弹框
 */
import React, { useState, useRef } from 'react'
import {Modal} from 'antd';
import styles from './index.less'

/**
 * isOpen         开关
 * handleOk       确定事件
 * handleCancel   取消事件
 */
const Index: React.FC = (props: any) => {
  const {isOpen, handleOk, handleCancel} = props;

  return (
    <Modal title='' open={isOpen} onOk={handleOk} onCancel={handleCancel} width={444} wrapClassName={styles.modle_wrap}>
      <div className={styles.title}>确定删除此内容？</div>
      <div className={styles.text}>内容删除后将无法恢复，请慎重考虑</div>
    </Modal>
  )
}

export default Index

