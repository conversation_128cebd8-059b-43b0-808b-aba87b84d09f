.spin {
  height: 100%;
  & > :global(.ant-spin-container) {
    height: 100%;
  }
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #eef3f9;
}

.content {
  flex: 1;
  padding: 16px 0;
  overflow-y: auto;
}

.content_inner {
  width: 816px;
  min-height: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
}

.nav_bar {
  flex-shrink: 0;
  width: 100%;
  display: flex;
  align-items: center;
  background: #fff;
  padding: 15px 20px;
  margin-bottom: 16px;
  border-radius: 8px;
  i {
    display: inline-block;
    width: 32px;
    height: 32px;
    background: url("../../../../../assets/GlobalImg/pc_goback.png") no-repeat center;
    background-size: 24px 24px;
    margin-right: 4px;
    cursor: pointer;
  }
  span {
    font-size: 20px;
    color: #000;
    line-height: 32px;
  }
}

.wrap {
  flex: 1;
  width: 100%;
  background: #fff;
  border-radius: 8px;
  padding: 24px 40px;
  .content_title {
    font-size: 22px;
    color: #000;
    font-weight: 600;
    line-height: 31px;
    margin-bottom: 4px;
  }

  .content_tips {
    font-size: 14px;
    color: #999;
    line-height: 20px;
    margin-bottom: 40px;
  }

  .info_item_wrap {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    column-gap: 44px;
    padding-right: 16px;
    margin-bottom: 24px;
    font-size: 14px;
    color: #000;
    line-height: 20px;
    .info_item_label {
      flex-shrink: 0;
      width: 64px;
      white-space: nowrap;
      text-align: right;
    }
    .info_item_value {
      word-break: break-all;
    }
    .info_item_img_wrap {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #0095FF;
      cursor: pointer;
    }
  }
}

.footer {
  background: #fff;
  border-top: 1px solid #ddd;
  height: 80px;
  flex-shrink: 0;
  .footer_content {
    width: 816px;
    height: 100%;
    margin: 0 auto;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .footer_content_right {
      :global {
        .ant-btn {
          height: 38px;
          padding: 0 20px;
          border-radius: 4px;
        }
      }
    }
  }
}
