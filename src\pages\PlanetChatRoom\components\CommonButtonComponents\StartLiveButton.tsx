// 开始直播组件
import React from 'react';
import { PlayCircleOutlined } from '@ant-design/icons';
import styles from './index.less';  // 引入自定义样式

const StartLiveButton = ({ isLive, isJoined, currentUserType, resetTimer, getSpaceInfo, dispatch }) => {
  const handleStartLiveClick = async (e) => {
    // 阻止事件冒泡
    e.stopPropagation();
    resetTimer();

    let dataBySpaceInfo = await getSpaceInfo();
    const { code, content } = dataBySpaceInfo || {};

    if (code === 200 && content && content.status === 2 && !content.videoList) {
      // 打开马上开始提示弹窗
      dispatch({
        type: 'PlanetChatRoom/setState',
        payload: { ModalVisibleByStartLive: true }
      });
    } else {
      // 处理其他情况
    }
  };

  return (
    <div onClick={handleStartLiveClick} className={styles.startLiveButton}>
      <PlayCircleOutlined className={styles.PlayCircleOutlined_live}/>
    </div>
  );
};

export default StartLiveButton;
