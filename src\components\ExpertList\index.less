.expert_content{
  background: #fff;
  padding: 16px 0 0 12px;
  .expert_doctor{
    display: flex;
    flex-wrap: nowrap;
    margin-bottom: 16px;
    cursor: pointer;
    .doctor_img{
      padding-bottom: 11px;
      box-sizing: border-box;
      margin-right: 12px;
      width: 56px;
      .doctor_pic{
        background: #fff;
        width: 100%;
        height: auto;
        border-radius: 50%;
        aspect-ratio: 1;
      }
      .no_doctor_pic{
        width: 56px;
        height: 56px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        font-weight: 500;
        color: #fff;
      }
    }
    .doctor_detail{
      flex: 1;
      overflow: hidden;
      border-bottom: 1px solid #E1E4E7;
      padding-bottom: 11px;
      padding-right: 12px;
      box-sizing: border-box;
      display: flex;
      flex-wrap: nowrap;
      .doctor_detail_content {
        flex: 1;
        overflow: hidden;
      }
      .doctor_detail_head{
        display: flex;
        align-items: center;
        font-weight: 400;
        margin-bottom: 6px;

        .doctor_detail_title{
          font-size: 16px;
          font-weight: 500;
          color: #000;
          line-height: 19px;
          //max-width: 100px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .doctor_vertical{
          margin: 0 8px;
          height: 12px;
          display: inline-block;
          border-left: 1px solid #999;
          position: relative;
          top: 1px;
        }
        .doctor_detail_attending{
          font-size: 13px;
          font-weight: 400;
          color: #666;
          line-height: 15px;
          white-space: nowrap;
        }
        .doctor_detail_major{
          background: #EEFFF9;
          border-radius: 2px;
          font-size: 12px;
          color: #06A777;
          padding: 1px 4px;
          margin-left: 8px;
          border: 1px solid #B0EAD9;
          white-space: nowrap;
        }
        .doctor_detail_major_highlight{
          z-index: 10020;
          padding: 4px 6px 6px;
          border-radius: 6px;
          background: #fff;
          margin-left: 8px;
          white-space: nowrap;
          .major_highlight{
            background: #EEFFF9;
            border-radius: 2px;
            font-size: 12px;
            color: #06A777;
            padding: 1px 4px;
            border: 1px solid #B0EAD9;
          }
        }
      }
      .organization_name{
        font-size: 13px;
        font-weight: 400;
        color: #666;
        line-height: 15px;
        margin-bottom: 6px;
      }

      .doctor_detail_desc {
        font-size: 13px;
        font-weight: 400;
        color: #666;
        line-height: 15px;
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        word-break: break-all;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        margin-bottom: 8px;
      }

      .ask_experts {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 6px;

        span {
          background: linear-gradient(270deg, #EBF5FF 0%, #EBFAFF 100%);
          border-radius: 25px;
          font-size: 13px;
          font-weight: 500;
          color: #0095FF;
          padding: 3px 14px;
          display: block;
        }
      }
    }

    // 关注按钮
    .doctor_follow_btn_box {
      padding: 14px 0 0 0;
      .follow_btn {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        padding: 0 8px;
        height: 28px;
        border-radius: 14px;
        background: #EDF9FF;
        color: #0095FF;
        .follow_btn_icon {
          display: block;
          width: 16px;
          height: 16px;
          background: url("../../assets/GlobalImg/follow.png") no-repeat center;
          background-size: 100% 100%;
        }
        .follow_btn_text {
          font-size: 14px;
          line-height: 20px;
          white-space: nowrap;
        }
        &.checked {
          background: #F5F5F5;
          color: #999;
        }
      }
    }
  }
}
