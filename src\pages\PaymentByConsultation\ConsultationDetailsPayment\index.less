.Mobile_Wrap {
  width: 100%;
  min-width: 285px;
  height: 100vh;
  background: #F5F6F8;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  // padding-left: 12px;
  // padding-right: 12px;

  .Mobile_title_statusbar {
    width: 100%;
    height: 44px;
  }

  .Mobile_title_Wrap {
    width: 100%;
    height: 44px;
    margin-bottom: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: transparent;

    .Mobile_title {
      font-size: 17px;
      font-weight: 500;
      color: #000000;
    }
  }

  .Mobile_box_info {
    width: 100%;
    min-height: 147px;
    background: #FFFFFF;
    border-radius: 0px 0px 0px 0px;
    opacity: 1;
    .Mobile_box_title {}
  }

  .Warp_box {
    width: 100%;
  }

  .box_payment {
    width: 100%;
    margin-top: 40px;
    margin-bottom: 45px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: column;
  }

  .box_text {
    font-size: 13px;
    font-weight: 400;
    color: #898989;
    line-height: 13px;
    margin-bottom: 10px;
    cursor: pointer;
    user-select: none;
  }

  .downArrow_icon_ConsultationDetailsPayment {
    width: 12px;
    height: 12px;
    background: url('../../../assets/Consultation/Pc/downArrow_icon_ConsultationDetailsPayment.png') no-repeat;
    background-size: 12px 12px;
    display: inline-block;
    margin-left: 4px;
    vertical-align: top;
  }

  .box_select_modePayment {
    padding-left: 16px;
    padding-right: 16px;
  }

  .box_title {
    font-size: 13px;
    font-weight: 400;
    color: #000000;
    line-height: 13px;
    margin-bottom: 13px;
  }

  .box_select_modePayment_warp {
    width: 100%;

    .box_select_modePayment_item {
      width: 100%;
      height: 74px;
      background: #FFFFFF;
      border-radius: 8px 8px 8px 8px;
      opacity: 1;
      margin-bottom:10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-left: 16px;
      padding-right: 16px;
    }
    .payWarp {

      .pay_title {
        display: flex;
        align-items: center;
        margin-bottom: 4px;

        .pay_Icon_wechatPay {
          width: 24px;
          height: 24px;
          background: url('../../../assets/Consultation/Pc/wechatPay_Icon_ConsultationDetailsPayment.png') no-repeat;
          background-size: 24px 24px;
          margin-right: 10px;
        }

        .pay_Icon_alipay {
          width: 24px;
          height: 24px;
          background: url('../../../assets/Consultation/Pc/alipay_Icon_ConsultationDetailsPayment.png') no-repeat;
          background-size: 24px 24px;
          margin-right: 10px;
        }


        .pay_text {
          font-size: 15px;
          font-weight: 500;
          color: #191919;
          line-height: 15px;
        }
      }

      .pay_desc {
        margin-left: 34px;
        font-size: 12px;
        font-weight: 400;
        color: #898989;
        line-height: 12px;
      }
    }

    .select_warp {
      .select_icon {
        width: 20px;
        height: 20px;
        background: url('../../../assets/Consultation/Pc/unselected_Icon_ConsultationDetailsPayment.png') no-repeat;
        background-size: 20px 20px;
      }
      .select_icon_active {
        width: 20px;
        height: 20px;
        background: url('../../../assets/Consultation/Pc/selected_Icon_ConsultationDetailsPayment.png') no-repeat;
        background-size: 20px 20px;
      }
    }

    .box_select_modePayment_item_active {
      background: rgba(0, 157, 255, 0.56);
    }
  }



  .box_text_price {
    margin-bottom: 10px;
    .box_text_unit {
      font-size: 20px;
      font-weight: 500;
      color: #191919;
      line-height: 20px;
      margin-left: 4px;
    }
    .box_text_num {
      font-size: 32px;
      font-weight: bold;
      color: #191919;
      line-height: 32px;
    }
  }




  .Mobile_box_bottom_warp {
    width: 100%;
    height: calc(58px + 34px + 20px);
    // background: #FFFFFF;
    border-radius: 0px 0px 0px 0px;
    opacity: 1;
    // border-top: 1px solid #EEEEEE;
    position: absolute;
    bottom: 0;
    padding-left: 16px;
    padding-right: 16px;
    padding-top: 24px;
    display: flex;
    justify-content: space-between;

    .Mobile_pay {
      width: 100%;
      height: 40px;
      background: #0095FF;
      border-radius: 20px 20px 20px 20px;
      opacity: 1;
      font-size: 16px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 40px;
      text-align: center;
      cursor: pointer;
      user-select: none;
    }
    .Mobile_pay:active {
      background: rgba(0, 149, 255, 0.73);
    }
  }

}
