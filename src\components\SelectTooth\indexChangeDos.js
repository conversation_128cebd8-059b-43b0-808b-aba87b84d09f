import React,{PureComponent, Component} from 'react';
import styles from './index.less';
import { Select, Icon, Tabs, Checkbox, Button, Divider, Tag, Modal, Radio, Form, Input } from 'antd';
import TagSelect from '@/components/TagSelect';
import PropTypes from 'prop-types';
const Option = Select.Option;
const TabPane = Tabs.TabPane;
const TeethType = '1'; //乳牙 Type
const ToothType = '2'; //恒牙 Type
const ToothDirectionByIndex = {
  'leftTop':1,
  'rigthTop':2,
  'leftBottom':3,
  'rigthBottom':4,
}

//空数据基本 存储格式
let ToothInfo = {
    leftTop: [],
    rigthTop: [],
    leftBottom: [],
    rigthBottom: [],
}

export default class SelectTooth extends Component{
  static propTypes = {
    defaultType:PropTypes.string,
    defaultToothInfo:PropTypes.object,
    onChangeTooth:PropTypes.func,
    onChangeType:PropTypes.func
  };
  static defaultProps = {
    defaultType:'1',
    defaultToothInfo:null,
    onChangeTooth:()=>{},
    onChangeType:()=>{}
  };

  constructor(props){
    let defaultToothInfo = {
      leftTop: [],
      rigthTop: [],
      leftBottom: [],
      rigthBottom: [],
    }
    super(props)
    this.state = {
      type:this.props.defaultType,              // 1乳牙  2恒牙
      toothContent:this.props.defaultToothInfo || defaultToothInfo // 基本存储格式
    }
  }

  /**
   * 当父组件传给子组件的`props`有改变回调
   * @param nextProps
   */
  componentWillReceiveProps(nextProps){
    if (nextProps.defaultToothInfo && nextProps.defaultType) {
      console.log('defaultToothInfo :: ',nextProps.defaultToothInfo);
      this.setState({
        //type:this.props.defaultType,
        toothContent: nextProps.defaultToothInfo
      })
    }
  }

  componentDidMount() {
   this.onRef && this.onRef(this);
  }

  /**
   * 清空 牙位信息 提供为父组件重置state中的值
   */
  clearData(){
    console.log('qqweqw');
    this.setState({
      type:'1',
      toothContent:{
        leftTop: [],
        rigthTop: [],
        leftBottom: [],
        rigthBottom: [],
      },
    })
    ToothInfo = {
      leftTop: [],
      rigthTop: [],
      leftBottom: [],
      rigthBottom: [],
    }
  }

  componentWillUnmount(){
    this.clearData()
  }

  /**
   * 恒牙 点击事件监听
   * @returns {XML}
   */
  ToothLeftTop=(value)=>{this.onChangeToothState(ToothType,'leftTop',value);}
  ToothRigthTop=(value)=>{this.onChangeToothState(ToothType,'rigthTop',value);}
  ToothLeftBottom=(value)=>{this.onChangeToothState(ToothType,'leftBottom',value);}
  ToothRigthBottom=(value)=>{this.onChangeToothState(ToothType,'rigthBottom',value);}
  /**
   * 乳牙 点击事件监听
   * @returns {XML}
   */
  TeethLeftTop=(value)=>{this.onChangeToothState(TeethType,'leftTop',value);}
  TeethRigthTop=(value)=>{this.onChangeToothState(TeethType,'rigthTop',value);}
  TeethLeftBottom=(value)=>{this.onChangeToothState(TeethType,'leftBottom',value);}
  TeethRigthBottom=(value)=>{this.onChangeToothState(TeethType,'rigthBottom',value);}
  /**
   * 乳牙恒牙切换
   * @param activeKey
   * @constructor
   */
  TabsOnChange=(activeKey)=>{
    this.onChangeToothState(activeKey+"",null,null);
  }


  /**
   * 当选中牙位位置 改变记录牙位位置的值
   * @param type
   * @param ToothDirection
   * @param value
   */
  onChangeToothState=(type,ToothDirection,value)=>{
    console.log('onChangeToothState');
    //当切换乳牙恒牙 改变并清空原有存储
    if(this.state.type != type){
      let toothContent = Object.assign(ToothInfo)
      if(ToothDirection) {
        toothContent[ToothDirection] = value
      }
      this.setState({
        type:type,
        /*toothContent:{
          ...toothContent
        }*/
      },()=>{
        this.props.onChangeType(this.state.type)
      })
    }else {
      //未切换乳牙恒牙选项 则继续存储存储位置
      let toothContent = Object.assign(this.state.toothContent)
      let oldToothArr = toothContent[ToothDirection] || []

    //获取新添加的牙位
      let differenceAddSet = [...value].filter(x => !new Set([...oldToothArr]).has(x));
      //获取新删除的牙位
      let differenceRemoveSet = [...oldToothArr].filter(x => !new Set([...value]).has(x));
      toothContent[ToothDirection] = value,
      this.setState({
        toothContent:{
          ...toothContent,
        }
      },()=>{
        //当前操作的牙位
        let current = {
          toothIndex: differenceAddSet.length != 0 ? differenceAddSet[0] : differenceRemoveSet[0],  // 当前操作牙位的位置
          type:type,                                    // 当前操作的牙位是乳牙还是恒牙
          direction:ToothDirection,                     // 当前操作牙位的方向
          isAdd:differenceAddSet.length != 0,           // 当前是添加操作还是删除操作
          directionValue:ToothDirectionByIndex[ToothDirection]
        }
        console.log('onChangeTooth 111122233',current);
        this.props.onChangeTooth(this.state,current)
      })
    }
  }

  render(){
    return (
        <div className={ styles.DiagnosisModalTab }>
          <Tabs defaultActiveKey={this.state.type} type="card" onChange={this.TabsOnChange}>
            <TabPane tab="乳牙" key={TeethType} closable={false}>
              <div className={ styles.ToothPosition }>
                <table className={ styles.Toothcenter }>
                  <tbody>
                  <tr>
                    <td>
                      <TagSelect
                        className={ styles.ToothTopRow }
                        hideCheckAll={true}
                        onChange={this.TeethLeftTop}
                        value={this.state.type == TeethType && this.state.toothContent && this.state.toothContent.leftTop ? this.state.toothContent.leftTop : []}
                      >
                        <TagSelect.Option value="E">E</TagSelect.Option>
                        <TagSelect.Option value="D">D</TagSelect.Option>
                        <TagSelect.Option value="C">C</TagSelect.Option>
                        <TagSelect.Option value="B">B</TagSelect.Option>
                        <TagSelect.Option value="A">A</TagSelect.Option>
                      </TagSelect>
                    </td>
                    <td>
                      <TagSelect
                        className={ styles.ToothTopRow }
                        hideCheckAll={true} style={{ marginLeft: 6 }}
                        onChange={this.TeethRigthTop}
                        value={this.state.type == TeethType && this.state.toothContent && this.state.toothContent.rigthTop ? this.state.toothContent.rigthTop : []}
                      >
                        <TagSelect.Option value="A">A</TagSelect.Option>
                        <TagSelect.Option value="B">B</TagSelect.Option>
                        <TagSelect.Option value="C">C</TagSelect.Option>
                        <TagSelect.Option value="D">D</TagSelect.Option>
                        <TagSelect.Option value="E">E</TagSelect.Option>
                      </TagSelect>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <TagSelect
                        hideCheckAll={true}
                        className={ styles.ToothBottomRow }
                        onChange={this.TeethLeftBottom}
                        value={this.state.type == TeethType &&  this.state.toothContent && this.state.toothContent.leftBottom ? this.state.toothContent.leftBottom : []}
                      >
                        <TagSelect.Option value="E">E</TagSelect.Option>
                        <TagSelect.Option value="D">D</TagSelect.Option>
                        <TagSelect.Option value="C">C</TagSelect.Option>
                        <TagSelect.Option value="B">B</TagSelect.Option>
                        <TagSelect.Option value="A">A</TagSelect.Option>
                      </TagSelect>
                    </td>
                    <td>
                      <TagSelect
                        className={ styles.ToothBottomRow }
                        hideCheckAll={true} style={{ marginLeft: 6 }}
                        onChange={this.TeethRigthBottom}
                        value={this.state.type == TeethType && this.state.toothContent && this.state.toothContent.rigthBottom ? this.state.toothContent.rigthBottom : []}
                      >
                        <TagSelect.Option value="A">A</TagSelect.Option>
                        <TagSelect.Option value="B">B</TagSelect.Option>
                        <TagSelect.Option value="C">C</TagSelect.Option>
                        <TagSelect.Option value="D">D</TagSelect.Option>
                        <TagSelect.Option value="E">E</TagSelect.Option>
                      </TagSelect>
                    </td>
                  </tr>
                  </tbody>
                </table>
              </div>
            </TabPane>
            <TabPane tab="恒牙" key={ToothType} closable={false}>
              <div className={ styles.ToothPosition }>
                <table className={ styles.Toothcenter }>
                  <tbody>
                  <tr>
                    <td>
                      <TagSelect
                        className={ styles.ToothTopRow }
                        hideCheckAll={true}
                        onChange={this.ToothLeftTop}
                        value={this.state.type == ToothType && this.state.toothContent && this.state.toothContent.leftTop ? this.state.toothContent.leftTop : []}
                      >
                        <TagSelect.Option value='18'>18</TagSelect.Option>
                        <TagSelect.Option value='17'>17</TagSelect.Option>
                        <TagSelect.Option value='16'>16</TagSelect.Option>
                        <TagSelect.Option value='15'>15</TagSelect.Option>
                        <TagSelect.Option value='14'>14</TagSelect.Option>
                        <TagSelect.Option value='13'>13</TagSelect.Option>
                        <TagSelect.Option value='12'>12</TagSelect.Option>
                        <TagSelect.Option value='11'>11</TagSelect.Option>
                      </TagSelect>
                    </td>
                    <td>
                      <TagSelect
                        className={ styles.ToothTopRow }
                        hideCheckAll={true}
                        onChange={this.ToothRigthTop}
                        style={{ marginLeft: 6 }}
                        value={this.state.type == ToothType && this.state.toothContent && this.state.toothContent.rigthTop ? this.state.toothContent.rigthTop : []}
                      >
                        <TagSelect.Option value='21'>21</TagSelect.Option>
                        <TagSelect.Option value='22'>22</TagSelect.Option>
                        <TagSelect.Option value='23'>23</TagSelect.Option>
                        <TagSelect.Option value='24'>24</TagSelect.Option>
                        <TagSelect.Option value='25'>25</TagSelect.Option>
                        <TagSelect.Option value='26'>26</TagSelect.Option>
                        <TagSelect.Option value='27'>27</TagSelect.Option>
                        <TagSelect.Option value='28'>28</TagSelect.Option>
                      </TagSelect>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <TagSelect
                        hideCheckAll={true}
                        className={ styles.ToothBottomRow }
                        onChange={this.ToothLeftBottom}
                        value={this.state.type == ToothType && this.state.toothContent && this.state.toothContent.leftBottom ? this.state.toothContent.leftBottom : []}
                      >
                        <TagSelect.Option value='48'>48</TagSelect.Option>
                        <TagSelect.Option value='47'>47</TagSelect.Option>
                        <TagSelect.Option value='46'>46</TagSelect.Option>
                        <TagSelect.Option value='45'>45</TagSelect.Option>
                        <TagSelect.Option value='44'>44</TagSelect.Option>
                        <TagSelect.Option value='43'>43</TagSelect.Option>
                        <TagSelect.Option value='42'>42</TagSelect.Option>
                        <TagSelect.Option value='41'>41</TagSelect.Option>
                      </TagSelect>
                    </td>
                    <td>
                      <TagSelect
                        className={ styles.ToothBottomRow }
                        hideCheckAll={true}
                        onChange={this.ToothRigthBottom}
                        style={{ marginLeft: 6 }}
                        value={this.state.type == ToothType && this.state.toothContent && this.state.toothContent.rigthBottom ? this.state.toothContent.rigthBottom : []}
                      >
                        <TagSelect.Option value='31'>31</TagSelect.Option>
                        <TagSelect.Option value='32'>32</TagSelect.Option>
                        <TagSelect.Option value='33'>33</TagSelect.Option>
                        <TagSelect.Option value='34'>34</TagSelect.Option>
                        <TagSelect.Option value='35'>35</TagSelect.Option>
                        <TagSelect.Option value='36'>36</TagSelect.Option>
                        <TagSelect.Option value='37'>37</TagSelect.Option>
                        <TagSelect.Option value='38'>38</TagSelect.Option>
                      </TagSelect>
                    </td>
                  </tr>
                  </tbody>
                </table>
              </div>
            </TabPane>
          </Tabs>
        </div>
    )
  }
}
