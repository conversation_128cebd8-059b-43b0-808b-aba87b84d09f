import { Quill } from 'react-quill'
const BlockEmbed = Quill.import('blots/block/embed')

class VideoBlot extends BlockEmbed {
  static create(value) {
    let node = super.create();
    node.setAttribute('contenteditable', false)
    node.setAttribute('id', value.code)
    node.setAttribute('data-type', 'video')
    node.setAttribute('data-src', value.src)
    node.setAttribute('data-name', value.name)
    this.buildContentNode(node, value)
    return node;
  }

  static buildContentNode(node, value) {
    const tempNode = document.createElement('div')
    tempNode.classList.add('video_wrap')
    const tempIconNode = document.createElement('i')
    tempIconNode.classList.add('video_icon')
    tempNode.appendChild(tempIconNode)

    const tempChildNode = document.createElement('div')
    tempChildNode.classList.add('video_text')
    const childSpanNode1 = document.createElement('span')
    childSpanNode1.classList.add('video_text_name')
    childSpanNode1.innerText = value.name.substring(0, value.name.lastIndexOf('.'))
    tempChildNode.appendChild(childSpanNode1)

    const childSpanNode2 = document.createElement('span')
    childSpanNode2.classList.add('video_text_suffix')
    childSpanNode2.innerText = value.name.substring(value.name.lastIndexOf('.'))
    tempChildNode.appendChild(childSpanNode2)
    tempNode.appendChild(tempChildNode)

    // 回显内容
    const contentNode = document.createElement('div')
    contentNode.classList.add('video_content_wrap')
    const videoNode = document.createElement('video')
    videoNode.setAttribute('src', value.src)
    videoNode.setAttribute('poster', value.src +`?x-oss-process=video/snapshot,t_0,f_jpg,w_630,h_320,m_fast,ar_auto`)
    videoNode.setAttribute('controls', '')
    contentNode.appendChild(videoNode)
    node.appendChild(tempNode)
    node.appendChild(contentNode)
  }

  static value(node: Element) {
    return {
      code: node.id,
      src: node.dataset.src,
      name: node.dataset.name,
    }
  }

  format(name: string, value: string) {

  }

}

VideoBlot.blotName = 'video';                              // 格式名
VideoBlot.tagName = 'div';                                 // dom标签
VideoBlot.className = 'quill_video_format_wrap';           // dom类名

export default VideoBlot
