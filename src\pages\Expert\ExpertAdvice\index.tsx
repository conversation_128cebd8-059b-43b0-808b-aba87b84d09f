/**
 * @Description: 专家列表首页
 * @author: 赵斐
 */
import React, { useState, useEffect, useRef } from 'react';
import styles from './index.less';
import { connect, history } from 'umi';
import classNames from 'classnames';
import { getOperatingEnv } from '@/utils/utils'
import { InfiniteScroll } from 'antd-mobile'
import { Spin } from 'antd'
import TipsModal from './Components/TipsModal';
import ExpertList from '@/components/ExpertList'; // 专家列表
import searchIcon from '@/assets/GlobalImg/search.png'; // 搜索小图标
// 数据加载异常
import LoadingException from '@/components/LoadingException'
// 导航组件
import NavBar from '@/components/NavBar'

const initState = {
  total: 0,         // 总条数
  dataSource: [],   // 专家列表数据
}

const Index: React.FC = (props: any) => {
  const { dispatch, loading } = props;
  const listRef = useRef<any>(null);
  const [interfaceStatus, setInterfaceStatus] = useState(2);      // 接口状态
  const [tipsModalStatus, setTipsModalStatus] = useState(false);  // 能力等级提示弹框
  const [tabData, setTabData] = useState<any>(null)               // 学科分类数据
  const [state, setState] = useState(initState)                   // 专家列表数据
  const [statePageNum, setStatePageNum] = useState(1)             // 当前分页

  const {
    total,
    dataSource,
  } = state

  // 专家列表首页提示弹窗
  const tipsModalCancelFn = () => {
    setTipsModalStatus(false);
    localStorage.setItem("firstEntryStatus", "1")
  }

  useEffect(() => {
    listRef.current.scrollTop = 0
    if (!localStorage.getItem("firstEntryStatus")) {
      setTipsModalStatus(true)
    }
    getFilterDict()
    getExpertsList(1)
  }, [])

  // 按学科找专家字典数据
  const getFilterDict = () => {
    dispatch({
      type: "expertAdvice/getFilterDict",
      payload: {}
    }).then((res: any) => {
      if (res && res.code == 200) {
        const { content } = res || {}
        const { depSubject } = content || {};
        if (Array.isArray(depSubject) && depSubject.length) {
          setTabData(depSubject)
        }
      } else {
        setInterfaceStatus(1)
      }
    }).catch((err: String) => {
      setInterfaceStatus(1)
      console.log(err)
    })
  }

  /**
   * 获取专家列表数据
   * @param pageNum   当前第几页、默认第一页
   */
  const getExpertsList = async (pageNum: number = 1) => {
    await dispatch({
      type: "expertAdvice/getExpertsList",
      payload: {
        pageNum,
        pageSize: 30,
      }
    }).then((res: any) => {
      if (res && res.code == 200) {
        setStatePageNum(pageNum)
        const { content } = res || {};
        const { total, resultList } = content || {};
        let data = pageNum == 1 ? [] : dataSource;
        data = data.concat(resultList);
        if (Array.isArray(data) && data.length == 0) {
          setInterfaceStatus(2)
          return
        }
        setState({
          ...state,
          dataSource: [...data],
          total,
        })
      } else {
        setInterfaceStatus(1)
      }
    }).catch((err: String) => {
      console.log(err)
      setInterfaceStatus(1)
    })
  }

  // 加载更多数据
  let loadMore = async () => {
    await getExpertsList(statePageNum + 1)
  }

  /**
  * 点击tab跳转专家结果列表页面
  * @param code  学科类型
  */
  const onClickJumpExpertList = (code: number) => {
    history.push(`/Expert/ExpertResult?whereStatus=${code}`)
  }

  // 数据加载异常
  const retryFun = () => {
    listRef.current.scrollTop = 0
    getFilterDict()
    getExpertsList(1)
  }

  // 重置接口异常状态
  const resetStatusFun = () => {
    setInterfaceStatus(0)
  }

  // 跳转专家搜索页
  const goToUrl = () => {
    history.push('/Expert/ExpertSearch')

    // 解决ios中搜索页input框不能自动聚焦问题
    document.getElementById('input_ios_focus') && document.getElementById('input_ios_focus').focus()
  }

  const load = !!loading.effects['expertAdvice/getExpertsList'] ||   // 专家列表接口loading
               !!loading.effects['expertAdvice/getFilterDict']       // 专家字典接口loading
  return (
    <Spin spinning={load}>
      <div className={styles.wrap} style={{ overflow: tipsModalStatus ? 'hidden' : 'auto' }}>
        <div>
          <NavBar />
        </div>
        <div className={classNames(styles.input_wrap, {
          [styles.input_wrap_pc]: getOperatingEnv() == 4,
        })}>
          <div className={styles.input_box} onClick={goToUrl}><img src={searchIcon} alt="" />搜索专家姓名、医院</div>
        </div>
        <div className={styles.discipline_wrap}>
          <div className={styles.discipline_title}>按学科<span>找专家</span></div>
          <div className={styles.discipline_list}>
            {
              Array.isArray(tabData) && tabData.length ? <>
                {
                  tabData && tabData.map(item => {
                    return <div key={item.code} className={styles.discipline_list_item} onClick={() => { onClickJumpExpertList(item.code) }}>
                      <img src={require(`../../../assets/Expert/${item.iconName}.png`)} alt="tab" />
                      <span>{item.name}</span>
                    </div>
                  })
                }
              </> : null
            }
          </div>
        </div>
        <div className={styles.expert_list_wrap}>
          <span className={styles.expert_list_title}>全部</span>
          <div className={styles.expert_lists_item} ref={listRef}>
            {
              Array.isArray(dataSource) && dataSource.length ?
                <ExpertList
                  dataSource={dataSource}
                  tipsModalStatus={tipsModalStatus}
                /> : <LoadingException exceptionStyle={{ paddingTop: 50 }} interfaceStatus={interfaceStatus} retryFun={retryFun} resetStatusFun={resetStatusFun} />
            }

            {total>0 && <InfiniteScroll loadMore={loadMore} hasMore={total > dataSource.length} threshold={100} /> }

          </div>
        </div>
        {
          tipsModalStatus ?
            <TipsModal visible={tipsModalStatus} cancelFn={tipsModalCancelFn} /> : null
        }
      </div>
    </Spin>
  )
}
export default connect(({ expertAdvice, loading }: any) => ({ expertAdvice, loading }))(Index)
