import React, {useEffect, useState, useRef, createRef, useCallback} from 'react'
import {connect, history} from 'umi'
import classNames from 'classnames'
import { getIsIniPhoneAndWeixin } from '@/utils/utils'
import { Input, InfiniteScroll, TextArea, Radio, Popup, Button } from 'antd-mobile'
import { PlusOutlined, CloseCircleFilled, CloseOutlined } from '@ant-design/icons'
import Follow from '@/components/Follow'                   // 关注按钮
import CommentInput from './CommentInput'
import {commentsDetailList,saveCommentsOrReply} from "@/services/recommended";
import {message,Spin} from "antd";
import {values} from "lodash";
import {parseText} from "@/utils/im-index";                  // 弹窗中的输入框
import styles from './index.less'
import Avatar from '@/components/Avatar'; // 用户头像组件

const Index: React.FC = (props: any) => {
  const { visible,dispatch,recommended } = props;
  const { dataByGetCommentList:commnetList } = recommended || {};
  const [ DataBycommentsDetailList,setDataByCommentsDetailList ] = useState(null)
  const [ loading, setLoading ] = useState(null);
  const [ commentInfo, setCommentInfo ] = useState(visible)
  const {
    commentsContent,          //: [评论/回复内容]
    commentsUserName,         //: [评论/回复用户名称]:"志君"
    createDate,               //: [日期]
    createUserId,             //: 60
    headUrlShow,              //: [头像展示路径]:null
    id,                       //: [主键]:11
    imageTextId,              //: [图文主键ID]:55
    likeFlag,                 //: [当前用户评论点赞标志]: false
    likeNum,                  //: [点赞数量]:null
  } = commentInfo || {}

  const ref = useRef(null)
  useEffect(async () => {
    setCommentInfo(visible)
  }, [visible])


  useEffect(()=>{
    if (!!commentInfo) { getCommentsDetailList(); }
  },[commentInfo])

  const getCommentsDetailList = async ()=>{
    await setLoading(true);
    const data = await commentsDetailList({
      imageTextId:imageTextId,
      commentsId:id,
    })
    await setLoading(false);
    const { code, content } = data || {}
    if (code == 200 && Array.isArray(content)) {
      setDataByCommentsDetailList(content);
    }else {
      setDataByCommentsDetailList(null);
    }
  }

  const post = async (data)=>{
    const {
      commentInputInfo, // 评论数据
      value,
    } = data || {}

    if(commentInputInfo && value) {
      let params = null;
      if(commentInputInfo.commentsType == 2){
        // 回复引用回复类型参数
        params = {
          imageTextId: commentInputInfo.imageTextId,            //  number 非必须  ID
          imageTextType: commentInputInfo.imageTextType,        //	number 非必须  类型
          commentsContent: value,                               //	string 非必须  回复内容
          commentsType: 2,	                                    //  number 非必须  评论类型
          commentsSuperId: commentInputInfo.commentsSuperId,    //	number 非必须  评论ID
          commentsReplyUserId: commentInputInfo.commentsUserId, // 	number 非必须  回复用户ID
          commentsReplyId:commentInputInfo.id,                  //  number 非必须  回复ID
        }
      } else if(commentInputInfo.commentsType == 1) {
        // 引用回复类型参数
        params = {
          imageTextId: commentInputInfo.imageTextId,
          imageTextType: commentInputInfo.imageTextType,
          commentsContent: value,
          commentsType: 2,
          commentsSuperId: commentInputInfo.commentsSuperId,
          commentsReplyUserId: commentInputInfo.commentsUserId,
          commentsReplyId:commentInputInfo.id,
        }
      }else if (commentInputInfo.commentsType == 0) {
        // 引用回复类型参数
        params = {
          imageTextId: commentInputInfo.imageTextId,
          imageTextType: commentInputInfo.imageTextType,
          commentsContent: value,
          commentsType: 1,
          commentsSuperId: commentInputInfo.id,
        }
      }
      await setLoading(true);
      const data = await saveCommentsOrReply(params)
      await setLoading(false);
      const { code, content } = data || {}
      if (code == 200) {
        await getCommentsDetailList();
      }else {
        message.error('评论失败!')
      }
    }
  }

  // 点赞
  const collect = useCallback(async () => {
    await setCommentInfo({
      ...commentInfo,
      likeFlag: !likeFlag,
      likeNum: !likeFlag ? likeNum + 1 : likeNum - 1,
    })
    let commnetListByList = commnetList.map((item,index)=>{
      let likeNumByCommnetList = !commentInfo.likeFlag ? item.likeNum + 1 : item.likeNum - 1;
      return item.id == commentInfo.id ? {
        ...item,
        likeFlag:!commentInfo.likeFlag,
        likeNum: likeNumByCommnetList == 0 ? null : likeNumByCommnetList
      } : item
    })
    await dispatch({
      type:'recommended/save',
      payload: {
        dataByGetCommentList:commnetListByList
      }
    })
    let DataByImageTextLikeOrCancel = await dispatch({
      type: 'recommended/commentsLike',
      payload: {
        imageTextId: imageTextId,           // 图文ID
        commentsId: id,
        status: !likeFlag? 1 : 0, // 状态
      }
    })

  }, [likeFlag,likeNum])


  const collectByComment = async (value) => {
    if(!value){ return }
    let newDataBycommentsDetailList = DataBycommentsDetailList.map((item,index)=>{
      let likeNum_CommentsDetailList = !value.likeFlag ? item.likeNum + 1 : item.likeNum - 1;
      return item.id == value.id ? {
        ...item,
        likeFlag:!value.likeFlag,
        likeNum: likeNum_CommentsDetailList == 0 ? null : likeNum_CommentsDetailList
      } : item
    })
    await setDataByCommentsDetailList(newDataBycommentsDetailList);
    let DataByImageTextLikeOrCancel = await dispatch({
      type: 'recommended/commentsLike',
      payload: {
        imageTextId: value.imageTextId,           // 图文ID
        commentsId: value.id,
        status: !value.likeFlag ? 1 : 0, // 状态
      }
    })
  }

  // 发表评论
  const postComment = (item) => {
    ref?.current?.postComment(item)
  }

  return (
    <>
      <Popup
        className={styles.popup_container}
        visible={!!visible}
        onMaskClick={props.onCancel}
        bodyStyle={getIsIniPhoneAndWeixin() ? { paddingBottom: '34px' } : {}}
        destroyOnClose
      >
        <div className={styles.header} onClick={props.onCancel}>
          <span className={styles.gray_bar}></span>
        </div>
        <div className={styles.title_box}>
          <CloseOutlined onClick={props.onCancel}/>
          {Array.isArray(DataBycommentsDetailList) ? DataBycommentsDetailList.length : 0}条回复
        </div>
        <div className={styles.content}>
          <div className={classNames(styles.comment_item, styles.first)}>
            <div className={styles.comment_header}>
              <Avatar
                userInfo={{
                  userId:createUserId,
                  name:commentsUserName,
                  headUrlShow:headUrlShow,
                }}
                size={36}
              />
              <div className={styles.right}>
                <div className={styles.right_content}>
                  <div className={styles.user_box}>
                    <span className={styles.user_name}>{commentsUserName}</span>
                    <span className={styles.user_role}>楼主</span>
                  </div>
                  <div className={styles.time}>{createDate}</div>
                </div>
                <div className={styles.right_follow}>
                  <Follow/>
                </div>
              </div>
            </div>
            <div
              className={styles.comment_content}
              dangerouslySetInnerHTML={commentsContent ? { __html: parseText(commentsContent) } : null}
            ></div>
            <div className={styles.comment_btn_box}>
              <div className={styles.comment_btn_wrap}>
                <i
                  className={classNames({
                    [styles.icon_like]:true,
                    [styles.like_active]:likeFlag,
                  })}
                  onClick={collect}>
                </i>
                <span>{likeNum}</span>
              </div>
              <div className={styles.comment_btn_wrap}>
                <i className={styles.icon_comment}></i>
                <span>
                  { Array.isArray(DataBycommentsDetailList) && DataBycommentsDetailList.length }
                </span>
              </div>
            </div>
          </div>
          <div className={styles.divider}></div>

          <div className={styles.comment_title}>全部回复</div>

          <Spin spinning={loading}>
          {Array.isArray(DataBycommentsDetailList) &&
            DataBycommentsDetailList.map((item, index) => {
              return (
                <div className={styles.comment_item} key={index}>
                  <div className={styles.comment_header}>
                    <Avatar
                      userInfo={{
                        userId:item.createUserId,
                        name:item.commentsUserName,
                        headUrlShow:item.headUrlShow,
                      }}
                      size={36}
                    />
                    <div className={styles.right}>
                      <div className={styles.right_content}>
                        <div className={styles.user_box}>
                          <span className={styles.user_name}>{item.commentsUserName}</span>
                          {(createUserId == item.createUserId) &&
                            <span className={styles.user_role}>楼主</span>
                          }
                        </div>
                        <div className={styles.time}>{item.createDate}</div>
                      </div>

                      <div className={styles.like}>
                        <i
                          onClick={()=>{collectByComment(item)}}
                          className={classNames({
                            [styles.icon_like]:true,
                            [styles.like_active]:item.likeFlag,
                          })}
                        >
                        </i>
                        <span>{item.likeNum}</span>
                      </div>
                    </div>
                  </div>
                  {item.citeImageTextCommentsDto &&
                    <div className={styles.comment_quote}>
                      <span>
                      {`@${item.citeImageTextCommentsDto.commentsUserName}：`}
                      </span>
                      <span
                        dangerouslySetInnerHTML={{ __html: parseText(item.citeImageTextCommentsDto.commentsContent) }}
                      ></span>
                    </div>
                  }
                  <div
                    className={styles.comment_content}
                    dangerouslySetInnerHTML={{ __html: parseText(item.commentsContent) }}
                  ></div>
                  <div className={styles.comment_reply_box} onClick={()=>{postComment(item)}}>
                    <div className={styles.reply_btn}>
                      <span>回复</span>
                      <i></i>
                    </div>
                  </div>
                </div>
              )
            })
          }
          </Spin>
        </div>
        <CommentInput
          ref={ref}
          post={post}
          onClick={()=>{ postComment(visible); }}
        />

      </Popup>
    </>
  )
}

export default connect(({ userInfoStore,recommended, loading }: any) => ({
  userInfoStore,
  recommended,
  loading
}))(Index);
