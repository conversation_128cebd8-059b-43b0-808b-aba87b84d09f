.warp_content {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient( 180deg, #040831 0%, #151D4D 52%, #0C1533 100%);
  overflow: hidden;

  .content {
    height: 100%;
    position: relative;
    z-index: 10;
  }

  // 弹幕
  .video_box_warp {
    position: absolute;
    z-index: 20;
    width: 100%;
    color: #fff;
    pointer-events:none;
  }

  // PC端
  &.pc {
    padding-top: 60PX;
    padding-bottom: 62PX;

    // 弹幕
    .video_box_warp {
      top: 110PX;
      bottom: 290PX;
      pointer-events:none;
    }
  }

  // H5竖屏
  &.mobile_portrait {
    padding-top: 51PX;
    padding-bottom: 52PX;

    // 弹幕
    .video_box_warp {
      top: 100PX;
      bottom: 290PX;
      pointer-events:none;
    }
  }

  // H5横屏
  &.mobile_landscape {
    padding-top: 0;
    padding-bottom: 0;

    // 弹幕
    .video_box_warp {
      top: 60PX;
      bottom: 62PX;
      pointer-events:none;
    }
  }

  // H5 非直播情况下
  &.mobile_video {
    padding-bottom: 0PX;
  }
}


