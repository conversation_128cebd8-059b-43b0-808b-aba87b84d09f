.Mobile_Wrap {
  width: 100%;
  min-width: 285px;
  // height: 100vh;
  background: #FAFAFA;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  // padding-left: 12px;
  // padding-right: 12px;

  .Mobile_title_Wrap {
    width: 100%;
    height: 44px;
    margin-bottom: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #fff;

    .Mobile_title {
      font-size: 17px;
      font-weight: 500;
      color: #000000;
    }
  }


  .tab_title_Warp {
    width: 100%;
    padding-top: 16px;
    padding-bottom: 16px;
    padding-left: 16px;
    padding-right: 16px;
    display: flex;

    .tab_title_item {
      font-size: 14px;
      font-weight: 500;
      color: #666666;
      padding-right: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 4px;
    }

    .tab_title_item_active {
      color: #0095FF;
    }

    .tab_title_line {
      width: 12px;
      height: 3px;
      background: transparent;
      border-radius: 6px 6px 6px 6px;
      opacity: 1;
    }

    .tab_title_line_active {
      background: #0095FF;
    }
  }

  .Warp_Mobile_Content_box {
    width: 100%;
    height: calc(100vh - 61px - 44px);
    overflow-y: auto;
    padding-bottom: 30px;
  }

  .Mobile_Content_box {
    padding-left: 16px;
    padding-right: 16px;
    margin-bottom: 8px;

    .Mobile_Content_box_noDate {
      width: 100%;
      height: 80vh;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .Mobile_Content_box_noDate_icon {
      width: 226px;
      height: 122px;
      background: url('../../../assets/GlobalImg/NoDataAvailable_ConsultationList_icon.png');
      background-size: 100% 100%;
      margin-bottom:10px;
    }

    .Mobile_Content_box_noDate_text {
      text-align: center;
      font-size: 16px;
      font-weight: 400;
      color: #999999;
      line-height: 16px;
    }

    .Mobile_Content_box_item {
      width: 100%;
      min-height: 198px;
      background: #FFFFFF;
      border-radius: 8px 8px 8px 8px;
      opacity: 1;
      padding-top: 16px;
      padding-bottom: 18px;
      padding-left: 12px;
      padding-right: 12px;

      .Mobile_Content_box_item_title_warp {
        width: 100%;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .Mobile_Content_box_item_title_box {
          display: flex;
          align-items: center;

          .Mobile_Content_box_item_title_icon {
            width: 20px;
            height: 20px;
            background: #EDF9FF;
            border-radius: 4px 4px 4px 4px;
            opacity: 1;
            margin-right: 8px;
          }
          .GraphicConsultation_Icon {
            width: 20px;
            height: 20px;
            background: url('../../../assets/GlobalImg/GraphicConsultation_Icon.png');
            background-size: 20px 20px;
            display: inline-block;
            margin-right: 8px;
          }

          .VideoConsultation_Icon {
            width: 20px;
            height: 20px;
            background: url('../../../assets/GlobalImg/VideoConsultation_Icon.png');
            background-size: 20px 20px;
            display: inline-block;
            margin-right: 8px;
          }

          .Orthodontics_Icon {
            width: 20px;
            height: 20px;
            background: url('../../../assets/GlobalImg/Orthodontics_Icon.png');
            background-size: 20px 20px;
            display: inline-block;
            margin-right: 8px;
          }


          .Mobile_Content_box_item_title {
            font-size: 14px;
            font-weight: 500;
            color: #000000;
            line-height: 14px;
          }

        }

        .Mobile_Content_box_item_title_status {
          font-size: 14px;
          font-weight: 500;
          color: #888888;
        }
      }

      .item_box_content {
        display: flex;
        margin-bottom: 12px;

        .item_box_content_img {
          width: 60px;
          height: 60px;
          background: #D9D9D9;
          border-radius: 8px 8px 8px 8px;
          opacity: 1;
          margin-right: 8px;
          overflow: hidden;
        }

        .item_box_content_img_box {
          height: 100%;
          width: 100%;
          object-fit: cover;
        }

        .item_box_content_right {
          width: calc(100% - 70px);

          .item_box_content_right_box {
            font-size: 13px;
            font-weight: 400;
            color: #999999;
            line-height: 13px;
            display: flex;
            margin-bottom: 9px;


            .item_box_content_right_box_title {
              // width:63px;
              margin-right: 6px;
              text-align: left;
            }

            .item_box_content_right_box_content {
              flex: 1;
              // width: calc(100% - 62px);
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }

      .item_time {
        display: flex;
        justify-content: space-between;
        .item_tiem_text {
          font-size: 13px;
          font-weight: 400;
          color: #999999;
        }
        .item_total {
          display: flex;
          .item_text_spen {
            color: #999999;
          }
        }
      }


      .btn_box_warp {
        margin-top: 10px;
        display: flex;
        justify-content: flex-end;
      }

      .btn_box {
        width: 84px;
        height: 30px;
        border-radius: 34px 34px 34px 34px;
        opacity: 1;
        border: 1px solid #0095FF;
        font-size: 13px;
        font-weight: 400;
        color: #0095FF;
        line-height: 30px;
        text-align: center;
      }

      .btn_box_payment {
        width: 84px;
        height: 30px;
        border-radius: 34px 34px 34px 34px;
        opacity: 1;
        border: 1px solid #0095FF;
        font-size: 13px;
        font-weight: 400;
        color: #fff;
        line-height: 30px;
        text-align: center;
        background: #0095FF;
      }
    }
  }
}


.copyWarp {
  display: flex;
  align-items: center;
}

.copyText {
  font-size: 12px;
  font-weight: 400;
  line-height: 12px;
  color: #666666;
}
