import React, { useState, useEffect, useRef, useCallback } from 'react';
import { history, connect, useRouteMatch,useAliveController } from 'umi';

import styles from './index.less';
import {Form, message, Spin, Upload, UploadFile} from 'antd';
import { stringify } from 'qs';
import classNames from 'classnames';
import NavBar from '@/components/NavBar';
import { getOperatingEnv, processNames, randomColor } from '@/utils/utils';
import { Input, Switch, Toast, TextArea } from 'antd-mobile';
import { UploadChangeParam } from 'antd/lib/upload';
import CreateKingdomOrSpace from '@/pages/UserInfo/CreateKingdomOrSpace';

import goBackIcon from '@/assets/GlobalImg/go_back.png'; // 返回按钮小图标
import rightArrowIcon from '@/assets/GlobalImg/right_arrow.png'; // 右箭头小图标
import frameIcon from '@/assets/GlobalImg/frame.png';
import SearchGuest from '@/pages/UserInfo/CreateKingdomOrSpace/SearchGuest';
import TcVod from 'vod-js-sdk-v6'; // 视频上传中图标
import { Helmet } from '@@/plugin-helmet/exports';
import { DownOutlined, UpOutlined, RightOutlined } from '@ant-design/icons';
import CropperImgModal from "@/pages/CreateSpace/CropperImgModal";
import request from "@/utils/request";
import {checkSpaceCoursewareTranscode, spaceCoursewareTranscode} from '@/services/planetChatRoom';
const reduceIcon =
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/reduce.png'; // 减员小图标
const addIcon = 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/add.png'; // 增员小图标
const redReduceIcon =
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/red_reduce.png'; // 红色删除小图标
const closeEyeIcon =
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/close_eye.png'; // 闭眼小图标
const openEyeIcon =
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/open_eye.png'; // 睁眼小图标
const uploadImg =
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/big_add.png'; // 上传图片图标

const index: React.FC = (props) => {
  // 获取地址栏id参数
  const { location } = history || {};
  const { query } = location || {};
  const { id,consultationId:consultationIdByQurey } = query || {};
  const { path } = useRouteMatch();
  const [form] = Form.useForm();
  const contentRef = useRef(null);
  const { userInfoStore, dispatch, goBack = () => {} } = props || {};
  const { drop,clear } = useAliveController();
  const {
    spaceTypeId,
    selectCreateType,
    spaceCoverUrl,
    spaceCoverUrlView,
    creatTabSelectDate,
    selectedKingdom,
    selectedCompere,
    selectedGuest,
    spaceName,
    isSuperAccount,
    isEditSpace,
    isPasswordSwitchChecked,
    isAutoRecord,
    allowApplications,
    spacePasswordText,
    spaceAdvertisingUrl,
    spaceFromEnter,
    spaceVideoUrl,
    spaceVideoName,
    createModalVisible,
    selectedKingdomAudience,
    enterpriseUserSelectData,
    spaceVideoId,
    spaceIntroduceVal,
    spectatorType,
    spaceCoursewares,
  } = userInfoStore || {}; // 从仓库中取值

  const {
    title,
    type,
    goBackType,
    isShowGoBack = false,
    isSelectKingDom = false,
  } = (creatTabSelectDate && creatTabSelectDate) || {};
  const [passType, setPassType] = useState(true); // 密码是否可见
  const [isSwitch, setIsSwitch] = useState(
    spacePasswordText || isPasswordSwitchChecked ? true : false,
  ); // 设置密码开关
  const [password, setPassword] = useState(spacePasswordText || ''); // 密码
  const [minusIconVisible, setMinusIconVisible] = useState(false); // 设置删除嘉宾开关
  const [spaceInputVal, setSpaceInputVal] = useState(spaceName || ''); // 空间名称
  const [loadingByUpload, setLoadingByUpload] = useState(false); // 空间封面上传loading
  const [loadingByAdverUpload, setLoadingByAdverUpload] = useState(false); // 空间广告上传loading
  const [uploadVideoLoading, setUploadVideoLoading] = useState(false); // 空间视频上传loading
  const [uploadProgress, setUploadProgress] = useState(0); // 视频上传进度
  const [createKingdomOrSpaceType, setCreateKingdomOrSpaceType] = useState(1); // 创建空间弹窗内容类型
  const [showLiveSettings, setShowLiveSettings] = useState(null); // 展示直播设置
  const [ checkTranscodeTimeoutId,setCheckTranscodeTimeoutId ] = useState(null);   // 转码状态setTime
  const kingdomInfoData = JSON.parse(localStorage.getItem('kingdomInfoData') || '{}'); // 从创建王国成功页保存，防止刷新后王国相关数据丢失
  const isSuperAccountLocal = JSON.parse(localStorage.getItem('isSuperAccount') || 'false'); // 是否为超级账号 true是 false不是（默认false）
  const UerInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  // 记录是否是在当前页面,返回和关联王国返回时，需要设置为false，为了解决上传视频过程中视频不取消问题
  let openModalKeyRef = useRef(true);
  let CreateSpaceText = '直播';

  // 编辑获取默认回显数据
  useEffect(() => {
    if(location?.pathname.indexOf("CreateSpace") != -1) {
      if (!!id) {
        getEditSpaceInfo(id);
      } else {
        getCreateSpaceInfo();
      }
    }
  }, [id]);

  // [获取编辑信息后]-初始化form表单
  useEffect(() => {
    let formObject: Record<string, any> = {
      spaceName: spaceName || '',
      spacePassword: spacePasswordText || '',
      spaceIntroduce: spaceIntroduceVal || '',
    };
    if (isEditSpace) {
      formObject = {
        spaceName: spaceName || '',
        spacePassword: spacePasswordText || '',
        spaceIntroduce: spaceIntroduceVal,
      };
    }
    form.setFieldsValue(formObject);
  }, [form, isEditSpace, spaceIntroduceVal, spaceName, spacePasswordText]);

  // 初始化直播
  const getCreateSpaceInfo = async () => {
    let resByGetSpaceCover = await dispatch({
      type: 'userInfoStore/getSpaceCover',
      payload: {
        spaceId: null, // 空间ID
        starSpaceType: 1, // 封面类型 1直播、2会议
      },
    });
    // 查验是否为超级账号
    let resByCheckSuperAccount = await dispatch({
      type: 'userInfoStore/checkSuperAccount',
    });
    const { code, content } = resByGetSpaceCover || {};
    if (code == 200 && content) {
      if (Array.isArray(content) && content.length > 0) {
        let isSelectByItem = content.find((item) => {
          return item.isSelect == 1;
        });
        let selectByItem = !!isSelectByItem ? isSelectByItem : content[0];
        let paramsBysetTaskListState = {
          spaceCoverUrl: selectByItem
            ? {
                fileUrlView: selectByItem.showSpaceCoverUrl,
                fileUrl: selectByItem.spaceCoverUrl,
              }
            : null, // 空间封面
          spaceCoverUrlView:
            selectByItem && selectByItem.showSpaceCoverUrl ? selectByItem.showSpaceCoverUrl : null, // 获取的空间封面
          isSuperAccount: resByCheckSuperAccount && resByCheckSuperAccount.content,
          starSpaceType: 1,
        };
        // 已有封面则无需再覆盖
        if (!!spaceCoverUrlView) {
          paramsBysetTaskListState = {
            isSuperAccount: resByCheckSuperAccount && resByCheckSuperAccount.content,
            starSpaceType: 1,
          };
        }
        // 已有封面则无需再覆盖
        if (!!spaceCoverUrlView) {
          dispatch({
            type: 'userInfoStore/setTaskListState',
            payload: paramsBysetTaskListState,
          });
        } else {
          dispatch({
            type: 'userInfoStore/setTaskListState',
            payload: paramsBysetTaskListState,
          });
        }
      }
    }
  };

  const goBackHandle = useCallback(() => {
    openModalKeyRef.current = false;
    dispatch({
      type: 'userInfoStore/clean',
      payload: {},
    });
  }, [dispatch]);

  const getEditSpaceInfo = async (spaceId) => {
    const userInfoData = JSON.parse(localStorage.getItem('userInfo') || '{}');
    const { friUserId: id } = userInfoData || {}; // 获取用户id

    let resByGetSpaceCover = await dispatch({
      type: 'userInfoStore/getSpaceCover',
      payload: {
        spaceId: null, // 空间ID
        starSpaceType: 1, // 封面类型 1直播、2会议
      },
    });
    let selectByItem = null;
    const { code: resByCode, content: resByContent } = resByGetSpaceCover || {};
    if (resByCode == 200 && resByContent) {
      if (Array.isArray(resByContent) && resByContent.length > 0) {
        let isSelectByItem = resByContent.find((item) => {
          return item.isSelect == 1;
        });
        selectByItem = !!isSelectByItem ? isSelectByItem : resByContent[0];
      }
    }

    let res = await dispatch({
      type: 'userInfoStore/getSpaceInfoByEdit',
      payload: {
        spaceId: spaceId, // 空间id
        wxUserId: id, // 当前登录人id
      },
    });
    const { code, content } = res || {};
    if (code == 200 && content) {
      let {
        starSpaceType,
        isAutoRecord,
        isNoPasswordApply,
        name,
        guestDataList,
        appointmentStartTime,
        intro,
        spaceCoverUrlShow:spaceCoverUrlShowByCon,
        // spaceCoverUrl,
        spaceAdvertisingUrlShow,
        spaceAdvertisingUrl,
        vodFileId,
        vodPathUrl,
        password,
        vodFileName,
        spectatorType,
        spectatorCount,
        spaceType,
        kingdomId,
        spaceCoverList,
        spaceCoursewares,
      } = content || {};
      const spaceCoveritem =
        Array.isArray(spaceCoverList) && spaceCoverList.length > 0 && spaceCoverList[0];
      let { spaceCoverUrl:spaceCoverUrlByItem } = spaceCoveritem || {};
      let spaceCoursewaresArr = [];
      Array.isArray(spaceCoursewares) && spaceCoursewares.map((item) => {
        spaceCoursewaresArr.push({
          ...item,
          code:item.coursewareName,
          src:item.coursewarePathView,
          fileUrlView:item.coursewarePathView,
          name:item.coursewareName,
          uploadPath:item.coursewarePath,
          percent:100,   // 上传进度
        });
      })
      /*
       * isSelect: 0
       * showSpaceCoverUrl: "https://s1-test.5i5ya.com/54b66e219c25e7621bbc3963fe9f49c8/660bb898/dmp/spaceCover/live_broadcast_01.png"
       * spaceCoverUrl: "dmp/spaceCover/live_broadcast_01.png"
       */
      // spaceCoverUrlShow = spaceCoverUrlShow ? spaceCoverUrlShow : selectByItem.showSpaceCoverUrl;
      // spaceCoverUrl = spaceCoverUrl ? spaceCoverUrl : selectByItem.spaceCoverUrl;

      let spaceCoverUrlShowByInit = null;
      let spaceCoverUrlByInit = null;

      if(spaceCoverUrl){
        spaceCoverUrlShowByInit = spaceCoverUrl ? spaceCoverUrl.fileUrlView : null;
        spaceCoverUrlByInit = spaceCoverUrl ? spaceCoverUrl.fileUrl : null;
      }else {
        spaceCoverUrlShowByInit = spaceCoverUrlShowByCon ? spaceCoverUrlShowByCon : selectByItem.showSpaceCoverUrl;
        spaceCoverUrlByInit = spaceCoverUrlByItem ? spaceCoverUrlByItem : selectByItem.spaceCoverUrl;
      }

      const spaceTypeName =
        spaceType == 1
          ? '专家讲课'
          : spaceType == 2
          ? '指导'
          : spaceType == 3
          ? '复杂病例讨论'
          : null; // 观众类型名称

      let enterpriseText = ''; // 观众已选-展示文案
      // 根据当前类型判断选中的是哪个观众
      if (spectatorType == 1 && !kingdomId) {
        enterpriseText = `王国成员可见，已选${spectatorCount}个王国`;
        kingdomFn(spaceId);
      } else if (spectatorType == 2) {
        // 获取企业用户接口数据
        getBizGroupByTenantId(spaceId, spectatorCount);
      }

      const options = {
        id: `编辑${starSpaceType == 1 ? '直播' : '会议'}`,
        type: 2, // 观众类型 0默认所有人 1指定王国成员可见 2指定企业品牌用户可见
        title: `编辑${starSpaceType == 1 ? '直播' : '会议'}`,
        goBackType: '', // 返回
        isShowGoBack: 1, // 是否有返回箭头 1没有
        isSelectKingDom: 1, // 是否可以选择王国 1没有
      };

      setSpaceInputVal(name);
      setPassword(password);
      setIsSwitch(password ? true : false); // 设置密码开关
      dispatch({
        type: 'userInfoStore/setTaskListState',
        payload: {
          isEditSpace: true, // 是否是编辑状态
          createModalVisible: false, // 打开弹框
          creatTabSelectDate: options, // 空间信息
          spaceName: name, // 空间名称
          spaceIntroduceVal: intro, // 空间介绍
          selectedGuest: guestDataList, // 嘉宾
          selectCreateType: starSpaceType == 1 ? 1 : 14,
          spaceCoverUrl:
            spaceCoverUrlShowByInit && spaceCoverUrlByInit
              ? { fileUrlView: spaceCoverUrlShowByInit, fileUrl: spaceCoverUrlByInit }
              : null, // 空间封面
          spaceCoverUrlView: spaceCoverUrlShowByInit ? spaceCoverUrlShowByInit : null, // 获取的空间封面
          spaceAdvertisingUrl:
            spaceAdvertisingUrlShow && spaceAdvertisingUrl
              ? { fileUrlView: spaceAdvertisingUrlShow, fileUrl: spaceAdvertisingUrl }
              : null, // 空间广告
          spaceVideoUrl: vodPathUrl, // 空间视频
          spaceVideoId: vodFileId, // 空间视频id
          spaceVideoName: vodFileName, // 视频名称
          spacePasswordText: password, // 空间密码
          appointmentStartTime, // 空间预约时间
          isAutoRecord: isAutoRecord == 1 ? true : false, // 是否自动录制
          allowApplications: isNoPasswordApply == 1 ? true : false, // 是否开启无密码申请：0不开启，1
          spaceFromEnter: {
            pageType: '1', // 1 我的主页、2 空间详情、3 王国详情
            tipText: '修改成功',
            spaceId: spaceId, // 空间id
            isCureentKingdom: kingdomId ? true : false, // 是否是当前指定王国
          },
          spaceTypeId: spaceType, // 空间类型 1专家讲课 2指导 3复杂病例讨论
          spaceTypeName, // 空间类型名称
          spectatorType, // 观众类型 0默认所有人 1指定王国成员可见 2指定企业品牌用户可见
          enterpriseText, // 选择可见企业/品牌用户，弹框名称
          selectedKingdomAudience: kingdomId ? [kingdomId] : [], // 指定当前王国所有成员，并将当前王国的id保存到store中
          starSpaceType: 1,
          spaceCoursewares:spaceCoursewaresArr,
        },
      });
    } else {
      message.error(res && res.msg ? res.msg : '获取直播信息失败');
      history.goBack();
    }
  };

  // 获取企业品牌机构
  const getBizGroupByTenantId = useCallback(
    (spaceId, spectatorCount) => {
      dispatch({
        type: 'userInfoStore/getBizGroupByTenantId',
        payload: {
          spaceId, // 空间ID，编辑必传
        },
      })
        .then((res: any) => {
          const { code, content } = res || {};
          if (res && code == 200) {
            // 找到选中的数据并赋值state
            let options = content.map(({ tenantId, isAllUser, areaList, orgList }) => ({
              tenantId,
              isAllUser,
              areaList: areaList.filter((it) => it.isChecked).map((val) => val.id),
              orgList: orgList.filter((it) => it.isChecked).map((val) => val.id),
            }));

            const isAll = options.filter((it) => it.isAllUser); // 过滤出isAllUser选中的数据，判断是否展示为所有人员

            let enterpriseText = '';

            // 如果所有人勾选的数据总长度 与 初始数据的总长度 一致，则展示 所有成员，否则展示 已选xx个分组
            if (isAll.length === content.length) {
              enterpriseText = `企业/品牌用户可见，已选所有成员`;
            } else {
              enterpriseText = `企业/品牌用户可见，已选${spectatorCount}个分组`;
            }

            dispatch({
              type: 'userInfoStore/setTaskListState',
              payload: {
                enterpriseUserSelectData: options || [], // 当前勾选中的数据
                enterpriseText, // 观众-展示文案
                allViewersState: false, // 所有人状态勾选取消
              },
            });
          }
        })
        .catch((err: any) => console.log(err));
    },
    [dispatch],
  );

  // 获取创建王国或加入王国相关数
  const kingdomFn = useCallback(
    (spaceId) => {
      dispatch({
        type: 'userInfoStore/getCreateAndJoinKingdomList',
        payload: {
          spaceId, // 编辑时候需要传id用来查询当前空间选中了哪些
        },
      })
        .then((res: any) => {
          const { code, content } = res || {};
          if (res && code == 200) {
            if (content) {
              const createArrList = content && content[1]; // 创建的王国数据
              const joinArrList = content && content[2];   // 加入的王国数据

              let listArr; // 总数据

              if (createArrList && joinArrList) {
                // 如果加入的和创建的数据都有时，则合并数据
                listArr = createArrList.concat(content[2]);
              }

              if (createArrList && !joinArrList) {
                // 如果只有创建的数据，没有加入的数据时，则赋值创建的数据
                listArr = createArrList;
              }

              if (!createArrList && joinArrList) {
                // 如果只有创建的数据，没有加入的数据时，则赋值创建的数据
                listArr = joinArrList;
              }

              const filterList = listArr.filter((it) => it); // 过滤筛选出不为null的数据

              dispatch({
                type: 'userInfoStore/setTaskListState',
                payload: {
                  selectedKingdomAudience: filterList
                    ?.filter((it) => it.isChecked)
                    .map((v) => v.id), // 观众-王国列表数据
                  allViewersState: false, // 所有人状态勾选取消
                },
              });
              return;
            }
          }
        })
        .catch((err: any) => console.log(err));
    },
    [dispatch],
  );

  // 上传直播课件
  const beforeCoursewareUpload = (file: { size?: any; type?: any }) => {
    const isSize = file.size / 1024 / 1024 < 100;

    if (!isSize) {
      message.warning('超过100M限制，不允许上传~');
      return false;
    }
    const { name: fileName } = file || {};
    // 添加对文件后缀名的限制
    const suffix = fileName.substring(fileName.lastIndexOf('.') + 1);
    let isBySuffix = ['pdf', 'ppt', 'pptx', 'doc', 'docx'].find((item)=>{return item === suffix});
    if (!isBySuffix) {
      message.warning('课件仅支持上传pdf、ppt、pptx、doc、docx格式的课件~');
      return false;
    }
    if (Array.isArray(spaceCoursewares) && spaceCoursewares.length >= 3) {
      message.warning('最多上传3个课件~');
      return false;
    }
    return true;
  }

  // 上传校验规则
  const beforeUpload = (file: { size?: any; type?: any }) => {
    const isSize = file.size / 1024 / 1024 < 15;
    if (!isSize) {
      Toast.show({ content: '超过15M限制，不允许上传~' });
      return false;
    }

    const { name: fileName } = file || {};
    // 添加对文件后缀名的限制
    const suffix = fileName.substring(fileName.lastIndexOf('.') + 1);
    const isJpgOrPng =
      file.type === 'image/jpg' ||
      file.type === 'image/jpeg' ||
      file.type === 'image/png' ||
      file.type === 'image/gif';
    // 文件后缀名可以大写,所以需要添加大写后缀名的判断
    const isSuffixByJpgOrPng =
      suffix === 'jpg' ||
      suffix === 'JPG' ||
      suffix === 'jpeg' ||
      suffix === 'JPEG' ||
      suffix === 'png' ||
      suffix === 'PNG' ||
      suffix === 'gif' ||
      suffix === 'GIF';
    if (!isJpgOrPng || !isSuffixByJpgOrPng) {
      Toast.show({ content: '只能上传JPG、JPEG、PNG、GIF 格式的图片~' });
      return false;
    }
    return isJpgOrPng;
  };

  // 上传图片headers
  const getHeaders = () => {
    // 获取环境类型，1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用   5 FRIDAY app中使用
    const env = getOperatingEnv();

    // FRIDAY APP中本地存储值：access_token、client、phone、user_name、wxuserId、userInfo
    return {
      // token
      access_token: localStorage.getItem('access_token') || '',
      username:
        env == 5
          ? localStorage.getItem('user_name')
          : localStorage.getItem('vxOpenIdCipherText')
          ? localStorage.getItem('vxOpenIdCipherText')
          : UerInfo?.phone,
      client: env == 5 ? localStorage.getItem('client') : 'WX',
      type: env === '1' ? '' : '1', // h5 传1
    };
  };

  // 赋值到仓库中保存 file文件
  const uploadSetFileState = (file: any, type: string) => {
    if (type == 'COVER') {
      // 封面
      dispatch({
        type: 'userInfoStore/setTaskListState',
        payload: {
          spaceCoverUrl: file,
        },
      });
    } else {
      // 广告
      dispatch({
        type: 'userInfoStore/setTaskListState',
        payload: {
          spaceAdvertisingUrl: file,
        },
      });
    }
  };

  // 上传事件
  const onChangeByUpload = (info: UploadChangeParam<UploadFile<any>>, type: string) => {
    let fileList;
    fileList = info && info.fileList;
    if (info.file.status === 'uploading') {
      if (type == 'COVER') {
        setLoadingByUpload(true);
      } else {
        setLoadingByAdverUpload(true);
      }
      return;
    }

    // 判断当前上传已被拦截
    if (info && !info.file.status) {
      fileList = null;
      return;
    }
    if (info && info.file.status === 'error') {
      if (type == 'COVER') {
        setLoadingByUpload(false);
      } else {
        setLoadingByAdverUpload(false);
      }
      Toast.show({ content: '上传失败' });
      fileList = null;
      return;
    }
    // 上传结束
    if (info && info.file.status === 'done') {
      if (type == 'COVER') {
        setLoadingByUpload(false);
      } else {
        setLoadingByAdverUpload(false);
      }
      if (info && info.file.response && info.file.response.code != 200) {
        Toast.show({ content: info.file.response.msg ? info.file.response.msg : '上传失败' });
        fileList = [];
        return;
      }
    }

    if (
      info.file.type === 'image/png' ||
      info.file.type === 'image/jpeg' ||
      info.file.type === 'image/gif'
    ) {
      if (info.file.response && info.file.response.code == 200 && info.file.response.content) {
        uploadSetFileState(info.file.response.content, type);
      }
    }
  };

  // 是否开启自动录制
  const onChangeIsAutoRecord = (val) => {
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: { isAutoRecord: val },
    });
  };

  // 设置密码失去焦点
  const passwordBlur = (e: { target: { value: any } }) => {
    // window.scrollTo(0, 0);
    const val = e.target.value;
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        spacePasswordText: val,
      },
    });
  };

  // 是否允许无密码可申请进入空间
  const onChangeIsAllowApplications = (val) => {
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: { allowApplications: val },
    });
  };

  // 设置密码开关
  const passwordSwitchChangeFn = (val: boolean | ((prevState: boolean) => boolean)) => {
    // 按钮关闭，清空仓库中保存的密码
    if (!val) {
      dispatch({
        type: 'userInfoStore/setTaskListState',
        payload: {
          spacePasswordText: null,
        },
      });
      form.setFieldValue('spacePassword', '');
      setPassword('');
    }
    setIsSwitch(val);
  };

  // 空间名称失去焦点事件
  const spaceInputBlurFn = (e: { target: { value: any } }) => {
    // window.scrollTo(0, 0);
    const val = e.target.value;

    if (!val || !val.trim()) {
      form.setFieldValue('spaceName', '');
      setSpaceInputVal('');
      return;
    }

    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        spaceName: val && val.trim(), // 空间名称
      },
    });
  };

  // 空间介绍失去焦点事件
  const spaceTextAreaBlurFn = (e: any) => {
    // window.scrollTo(0, 0);
    const val = e && e.target.value;
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        spaceIntroduceVal: val && val.trim(),
      },
    });
    form.setFieldValue('spaceIntroduce', val && val.trim());
  };

  // 获取视频文件名称，截取后缀名前面的部分内容，以便文件名字过长时，做隐藏处理
  const videoNameModify = () => {
    return spaceVideoName && spaceVideoName.substring(0, spaceVideoName.lastIndexOf('.'));
  };

  // 空间视频上传事件
  const videoFileChange = (e: any) => {
    if (!e.target.files || !e.target.files[0]) return;
    const file = e.target.files[0];
    const fileType = file.type == 'video/mp4'; // 视频格式
    const oversize = file.size / 1024 / 1024 / 1024 > 2; // 视频大小

    // 判断上传视频是否大于2G或者上传文件是否为mp4格式的，不是则提示
    if (oversize || !fileType) {
      Toast.show({
        duration: 2000,
        content: (
          <div>
            请上传格式为MP4，
            <br />
            大小2G以内的视频
          </div>
        ),
      });
      e.target.value = ''; // 解决同一个视频重复上传后，导致不再提示
      return;
    }

    // 重置状态
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        spaceVideoUrl: '', // 上传视频后返回的路径
        spaceVideoId: '', // 上传视频成功后返回的id
        spaceVideoName: '', // 视频文件名字
      },
    });

    // 上传签名
    const tcVod = new TcVod({
      getSignature: getSignature, // 获取上传签名的函数
    });
    setUploadProgress(0); // 重置进度
    setUploadVideoLoading(true); // 打开loading

    // 上传视频文件
    const uploader = tcVod.upload({
      mediaFile: file, // 媒体文件（视频或音频或图片），类型为 File
    });

    // 获取上传视频进度
    uploader.addListener('media_progress', (info) => {
      // 如果弹框关闭或者返回上一个页面时，关闭上传动作
      setUploadProgress(Math.round(info.percent * 100));
    });

    // 上传结束
    uploader
      .done()
      .then((result) => {
        setUploadVideoLoading(false);
        // 如果弹框关闭或返回上一个页面时，则静默，不赋值任何内容，放弃本次上传的东西
        dispatch({
          type: 'userInfoStore/setTaskListState',
          payload: {
            spaceVideoUrl: result.video.url,
            spaceVideoId: result.fileId,
            spaceVideoName: file.name,
          },
        });
        setUploadVideoLoading(false);
      })
      .catch((err) => {
        setUploadVideoLoading(false);
        // 如果弹框关闭或返回上一个页面时，则静默，不赋值任何内容，放弃本次上传的东西
        Toast.show({ content: err });
      });
    e.target.value = ''; // 解决同一个视频重复上传时不能上传问题
  };

  // 获取空间视频签名
  const getSignature: () => Promise<string> = () => {
    return new Promise((resolve, reject) => {
      dispatch({
        type: 'userInfoStore/getVodUploadSign',
      })
        .then((res) => {
          if (res && res.code === 200) {
            resolve(res.content);
          } else {
            reject();
          }
        })
        .catch(reject);
    });
  };

  // 点击关联王国点击返回
  const jumpHandle = () => {
    openModalKeyRef.current = false;
    // dispatch({
    //   type: 'userInfoStore/clean',
    //   payload: {
    //     spaceCoverUrl: null, // 空间封面路径
    //     spaceAdvertisingUrl: null, // 空间广告路径
    //     spacePasswordText: null, // 密码
    //     spaceName: null, // 空间名称
    //     selectedKingdom: null, // 已选中王国
    //     selectedCompere: null, // 已选择主持人
    //     selectedGuest: [], // 已选择嘉宾
    //     spaceVideoUrl: null, // 上传的空间视频地址
    //     spaceVideoId: null, // 视频id
    //     spaceVideoName: null, // 视频文件名
    //     spaceTypeId: null, // 空间类型 1专家讲课 2指导 3复杂病例讨论
    //     spaceTypeName: null, // 空间类型名称
    //     spectatorType: 0, // 观众类型 0默认所有人 1指定王国成员可见 2指定企业品牌用户可见
    //     allViewersState: true, // 选择观众默认所有人可见状态
    //     kingdomListArr: [], // 观众-王国列表数据
    //     selectedKingdomAudience: [], // 观众-选择可见王国
    //     enterpriseUserData: [], // 选择可见企业/品牌用户，当前企业下的机构数据
    //     enterpriseUserTab: 0, // 选择可见企业/品牌用户，页面tab
    //     enterpriseUserSelectData: [], // 选择可见企业/品牌用户，选择后的数据
    //     enterpriseText: null, // 选择可见企业/品牌用户，弹框名称
    //   }
    // })
    setCreateKingdomOrSpaceType(20);
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: { createModalVisible: true },
    });
  };

  // 删除已选择嘉宾
  const deleteGuest = useCallback(
    (item) => {
      dispatch({
        type: 'userInfoStore/setTaskListState',
        payload: {
          selectedGuest: selectedGuest.filter((it: { id: any }) => it.id !== item.id),
        },
      });
    },
    [dispatch, selectedGuest],
  );

  // 创建空间事件
  const submitFnByForm = () => {
    // if (uploadVideoLoading || loadingByUpload || loadingByAdverUpload) return;
    form
      .validateFields()
      .then((values) => {
        Toast.show({ icon: 'loading', maskClickable: false });

        const uploadingBySpaceCoursewares = Array.isArray(spaceCoursewares) && spaceCoursewares.filter((it) => it.status == 'uploading')
        if (Array.isArray(uploadingBySpaceCoursewares) && uploadingBySpaceCoursewares.length > 0) {
          message.warning('课件正在上传中,请稍后再试!');
          return;
        }
        // 编辑 进入
        if (isEditSpace) {
          editSubmit();
        } else {
          submitFn();
        }
      })
      .catch((err) => {
        console.log(err, '有没有捕获到哟');
      });
  };

  const editSubmit = () => {
    const { tipText, spaceId, pageType, consultationId } = spaceFromEnter || {};
    const guestIdList = selectedGuest.map((it: { id: any }) => it.id); // 嘉宾取id
    const idByspaceCoursewares = Array.isArray(spaceCoursewares) && spaceCoursewares.map((it: { spaceCoursewareId: any }) => it.spaceCoursewareId)
    dispatch({
      type: 'userInfoStore/editSpaceInfo',
      payload: {
        consultationId: pageType == '4' ? consultationId : undefined, // 指导id(创建视频指导时需必传)
        id: spaceId, // 空间ID
        name: spaceInputVal && spaceInputVal.trim(), // 空间名称
        password: password || null, // 密码
        // isAutoRecord: isAutoRecord ? 1 : 0, // 是否自动录制 是否开启自动录播 1:开启  0或其他关闭
        startStatus: 1, // 开始状态：1立刻、2预约
        appointmentStartTime: null, // 预约开始时间
        spaceCoverUrl: (spaceCoverUrl && spaceCoverUrl.fileUrl) || null, // 空间封面路径
        spaceCoverList: spaceCoverUrl
          ? [
            {
              spaceCoverUrl: spaceCoverUrl && spaceCoverUrl.fileUrl, // string 必须 空间封面路径
              isSelect: 1, // number 必须 是否选中封面
            },
          ]
          : [], // 空间封面列表
        spaceCoursewares:idByspaceCoursewares,
        spaceAdvertisingUrl: (spaceAdvertisingUrl && spaceAdvertisingUrl.fileUrl) || null, // 空间广告路径
        updateUserId: UerInfo && UerInfo.friUserId, // 操作人用户ID
        guestIdList: guestIdList, // 嘉宾id集合
        vodFileId: spaceVideoId, // 空间视频id
        intro: spaceIntroduceVal, // 空间介绍
        spectatorType: spectatorType, // 观众类型 0默认所有人 1指定王国成员可见 2指定企业品牌用户可见
        spaceType: spaceTypeId || null, // 空间类型 1专家讲课 2指导 3复杂病例讨论
        spectatorOfKingdomList: selectedKingdomAudience, // 指定王国成员时传参
        spectatorOfBizList: enterpriseUserSelectData || null, // 指定企业品牌用户时传参
        starSpaceType: 1, // 类型：1 直播，2 会议
      },
    })
      .then((res: { code: number; content: any; msg: any }) => {
        if (res && res.code == 200) {
          // 1. 关闭弹框
          // 2. Toast提示
          // 3. 刷新页面
          dispatch({
            type: 'userInfoStore/setTaskListState',
            payload: {
              createModalVisible: false,
            },
          });
          // Toast.show({content: tipText})
          // 跳转空间详情页
          clear().then(()=>{})
          history.replace(`/PlanetChatRoom/Live/${spaceId}`);
        } else {
          Toast.show({ content: res.msg || '数据加载失败' });
          return;
        }
      })
      .catch((err: any) => {
        console.log(err);
      });
  };

  const onChangeByCourseware = (info)=>{
    // 判断当前上传已被拦截
    if (info && !info.file.status) { return }
    if (info.file.status === 'uploading') {
      dispatch({
        type: 'userInfoStore/setTaskListState',
        payload: { spaceCoursewares:info.fileList },
      });
    }else if(info && info.file.status === 'error') {
      message.error('上传失败')
      let filterByFileList = info.fileList.filter((item)=>{
        return item.status != 'error'
      })
      dispatch({
        type: 'userInfoStore/setTaskListState',
        payload: { spaceCoursewares:[...filterByFileList] },
      });
      return
    }else if (info.file.status === 'done') {
      const res = info.file.response || {}
      if (Array.isArray(info.fileList)) {
        let filterByFileList = info.fileList.filter((item)=>{
          if(item.status != 'done') {
            return true
          }else if(!item.response) {
            return true
          }else {
            return item.response.spaceCoursewareId != res.spaceCoursewareId
          }
        })
        dispatch({
          type: 'userInfoStore/setTaskListState',
          payload: { spaceCoursewares:[...filterByFileList,{
              code: res.code,                       // 唯一值
              spaceCoursewareId: res.spaceCoursewareId,
              src: res.src,                     // 视频url
              name: res.name,                 // 文件原名字
              coursewareName: res.name,        // 源文件名称
              coursewarePathView: res.src,
              coursewarePath: res.uploadPath,
              fileUrlView: res.fileUrlView,
              uploadPath: res.uploadPath,
              percent: 100,   // 上传进度
              status: 'done',
            }] },
        });
      }
    }
  }

  // 自定义直播课件上传请求
  const coursewareFileRequest = ({onProgress,onError,onSuccess,data,filename,file,withCredentials,action,headers}) => {
    const fileNameOrigin = file.name                                         // 视频文件原名字
    let fileType = file.type                                                 // 视频文件type类型
    let fileSuffix = fileNameOrigin.substring(file.name.lastIndexOf('.')+1)  // 文件后缀名
    const { spaceId } = spaceFromEnter || {};
    // 请求接口获取上传课件携带签名的ossUrl
    request(`/api/server/base/oss-all-file-signature-upload?fileType=25&contentType=${fileType}&fileSuffix=${fileSuffix}`, {
      method: 'GET',
      headers,
      isNoNeedCommonParams: true,
    }).then(res => {
      const { code, content } = res;
      const url = content.signUrl;                   // 签名url
      const fileName = content.fileName;             // 生成的文件名
      const fileUrlView = content.fileUrlView;       // 文件的全url
      const uploadPath = content.uploadPath;         // 文件的oss地址

      // 使用签名url上传文件
      let request = new XMLHttpRequest()
      request.onreadystatechange = async () => {
        if (request.readyState == 4) { // 数据接收完毕,此时可以通过通过responseBody和responseText获取完整的回应数据
          if (request.status == 200) {} else {
            // 上传失败
            onError({
              code: fileName,                       // 唯一值
            })
          }
        }
      }

      request.upload.onprogress = async (e) => {
        if (e.lengthComputable) {
          setLoadingByUpload(true);
          const percent = (e.loaded / e.total * 100).toFixed(1)
          // onProgress({percent: Number(percent)})
          onProgress({percent: Number(percent / 2)})
          if (percent == 100) { // 完成上传'
            let resBySpaceCoursewareTranscode = await spaceCoursewareTranscode({
              coursewareName:fileNameOrigin,
              coursewarePath:uploadPath,
              spaceId:spaceId ? spaceId : null,
            })
            if (resBySpaceCoursewareTranscode && resBySpaceCoursewareTranscode.code == 200) {
              const { spaceCoursewareId } = resBySpaceCoursewareTranscode && resBySpaceCoursewareTranscode.content || {};
              onProgress({percent: Number(50)})
              // 课件转码调用成功后 轮询检查转码结果
              checkTranscodeStatus({
                file:file,
                uploadPath:uploadPath,
                spaceCoursewareId:spaceCoursewareId,
                fileName:fileName,
                fileUrlView:fileUrlView,
                fileNameOrigin:fileNameOrigin,
                onSuccess:onSuccess,   // 上传成功的回调
                onError:onError,       // 上传失败的回调
                onProgress:onProgress, // 上传进行中的回调
              });
            }else {
              setLoadingByUpload(false);
              // 开始转码失败
              message.error(resBySpaceCoursewareTranscode.msg ? resBySpaceCoursewareTranscode.msg : '课件文件格式不支持!');
            }
          }
        }
      }
      request.open('PUT', url);                                          // 使用PUT方式上传文件
      request.setRequestHeader('Content-type', fileType);                  // 添加文件类型
      request.setRequestHeader('Access-Control-Allow-Origin', "*");  // 允许跨域
      request.send(new File([file], fileName));                           // 上传文件
    })
  }

  // 轮询检查课件转码结果
  const checkTranscodeStatus = async ({
                                        file,
                                        uploadPath,
                                        spaceCoursewareId,
                                        fileName,
                                        fileUrlView,
                                        fileNameOrigin,
                                        onSuccess,
                                        onProgress,
                                        onError,
                                      }) => {
    let resBycheckSpaceCoursewareTranscode = await checkSpaceCoursewareTranscode({ coursewarePath: uploadPath });
    if (resBycheckSpaceCoursewareTranscode && resBycheckSpaceCoursewareTranscode.code == 200) {
      const {
        transcodeResult, // 转码是否成功 1成功，0正在转码，-1转码失败”
        progress,        // 转码进度 0-100 转码失败是-1
      } = resBycheckSpaceCoursewareTranscode &&  resBycheckSpaceCoursewareTranscode.content || {};
      // let filterBySpaceCoursewares = spaceCoursewares.filter((item)=>{ return item.spaceCoursewareId != spaceCoursewareId });
      if(progress == 100 || progress == -1){
        setLoadingByUpload(false);
      }else {
        setLoadingByUpload(true);
      }

      if (progress && progress != -1) {
        let percent = Number((progress / 2) + 50);
        onProgress({percent: percent})
      }
      if (transcodeResult == 1) {
        // 轮询结果转码成功
        if (checkTranscodeTimeoutId) {  clearTimeout(checkTranscodeTimeoutId); }
        message.success('课件转码成功!')
        onSuccess({
          code: fileName,                       // 唯一值
          src: fileUrlView,                     // 视频url
          name: fileNameOrigin,                 // 文件原名字
          fileUrlView:fileUrlView,
          uploadPath:uploadPath,
          spaceCoursewareId:spaceCoursewareId,
        })
        /**
         * coursewareName: "李放_个人周报2022年12月第2周.pptx"
         * coursewarePath: "dmp/space-courseware/7ef098d820bd459ebcbef347c169ecb9.pptx"
         * coursewarePathView: "https://s1-test.5i5ya.com/fd119d9ddf193398bbc36dc277b269f9/663c7d64/dmp/space-courseware/7ef098d820bd459ebcbef347c169ecb9.pptx"
         * spaceCoursewareId: 164
         * thumbnailResolution: ""
         * thumbnailUrl: ""
         * tiwTaskId: "gbuujnf5t4oklu7q7uoc"
         * transcodePage: 3
         * transcodeResolution: "960x540"
         * transcodeStatus: 1
         * transcodeTitle: "7ef098d820bd459ebcbef347c169ecb9.pptx"
         * transcodeUrl: "/dev/star/transcode/gbuujnf5t4oklu7q7uoc_tiw/h5/index.html"
         */
        let filterByspaceCoursewares = spaceCoursewares.filter((item) => {
          return item.lastModified != file.lastModified;
        })
        // 上传课件id
        await dispatch({
          type: 'userInfoStore/setTaskListState',
          payload: {
            spaceCoursewares:[...filterByspaceCoursewares, {
              code: fileName,                       // 唯一值
              spaceCoursewareId:spaceCoursewareId,
              src: fileUrlView,                     // 视频url
              name: fileNameOrigin,                 // 文件原名字
              coursewareName:fileNameOrigin,        // 源文件名称
              coursewarePathView:fileUrlView,
              coursewarePath:uploadPath,
              fileUrlView:fileUrlView,
              uploadPath:uploadPath,
              percent:100,   // 上传进度
              status:'done',
            }]
          },
        })

        // 转码成功
        return resBycheckSpaceCoursewareTranscode;
      } else if(transcodeResult == -1) {
        // 轮询结果转码失败
        if (checkTranscodeTimeoutId) {  clearTimeout(checkTranscodeTimeoutId); }
        message.error('课件转码失败,请更换课件!')
        onError({
          code: fileName,                       // 唯一值
        })
        return null;
      } else {
        // 轮询结果-课件正在转码中
        let checkTranscodeTimeoutId = setTimeout(() => {
          checkTranscodeStatus({
            file,
            uploadPath,
            spaceCoursewareId,
            fileName,
            fileUrlView,
            fileNameOrigin,
            onSuccess,
            onProgress,
            onError,
          });
        }, 3000);
        setCheckTranscodeTimeoutId(checkTranscodeTimeoutId);
      }
    }else {
      // 转码异常 接口报错
      if (checkTranscodeTimeoutId) {  clearTimeout(checkTranscodeTimeoutId); }
      message.error(resBycheckSpaceCoursewareTranscode.msg ? resBycheckSpaceCoursewareTranscode.msg : '课件转码失败,请更换课件!')
      return null;
    }
  };


  // 创建空间事件
  const submitFn = () => {
    const { tipText, pageType, consultationId } = spaceFromEnter || {};
    const guestIdList = selectedGuest.map((it: { id: any }) => it.id); // 嘉宾取id
    const idByspaceCoursewares = Array.isArray(spaceCoursewares) && spaceCoursewares.map((it: { spaceCoursewareId: any }) => it.spaceCoursewareId)
    dispatch({
      type: 'userInfoStore/addSpace',
      payload: {
        consultationId: pageType == '4' ? consultationId : undefined, // 指导id，从指导创建的必传
        name: spaceInputVal && spaceInputVal.trim(), // 空间名称
        wxUserId: (selectedCompere && selectedCompere.id) || (UerInfo && UerInfo.friUserId), // 主持人id
        hostName: (selectedCompere && selectedCompere.name) || (UerInfo && UerInfo.name), // 主持人名称
        kingdomId: (selectedKingdom && selectedKingdom.id) || null, // 王国id
        kingdomName: (selectedKingdom && selectedKingdom.name) || null, // 王国名称
        password: password || null, // 密码
        isAutoRecord: isAutoRecord ? 1 : 0, // 是否自动录制 是否开启自动录播 1:开启  0或其他关闭
        startStatus: 1, // 开始状态：1立刻、2预约
        appointmentStartTime: null, // 预约开始时间
        spaceCoverUrl: (spaceCoverUrl && spaceCoverUrl.fileUrl) || null, // 空间封面路径
        spaceCoverList: spaceCoverUrl
          ? [
              {
                spaceCoverUrl: spaceCoverUrl && spaceCoverUrl.fileUrl, // string 必须 空间封面路径
                isSelect: 1, // number 必须 是否选中封面
              },
            ]
          : [], // 空间封面列表
        spaceAdvertisingUrl: (spaceAdvertisingUrl && spaceAdvertisingUrl.fileUrl) || null, // 空间广告路径
        updateUserId: UerInfo && UerInfo.friUserId, // 操作人用户ID
        guestIdList: guestIdList, // 嘉宾id集合
        vodFileId: spaceVideoId, // 空间视频id
        intro: spaceIntroduceVal, // 空间介绍
        spectatorType: spectatorType, // 观众类型 0默认所有人 1指定王国成员可见 2指定企业品牌用户可见
        spaceType: spaceTypeId || null, // 空间类型 1专家讲课 2指导 3复杂病例讨论
        spectatorOfKingdomList: selectedKingdomAudience, // 指定王国成员时传参
        spectatorOfBizList: enterpriseUserSelectData || null, // 指定企业品牌用户时传参
        starSpaceType: 1, // 类型：1 直播，2 会议
        isNoPasswordApply: allowApplications ? 1 : 0, // 是否开启无密码申请：0不开启，1开启
        spaceCoursewares:idByspaceCoursewares, // 课件id集合
      },
    })
      .then((res: { code: number; content: any; msg: any }) => {
        if (res && res.code == 200) {
          if (pageType == '4') {
            // 返回指导详情
            history.goBack();
            return;
          }
          // 是在王国详情中创建的王国空间
          if (spaceFromEnter?.isJumpOrRefresh) {
            // 关闭弹框
            dispatch({
              type: 'userInfoStore/setTaskListState',
              payload: {
                createModalVisible: false,
              },
            });
            // 创建成功，直接跳转空间详情
            history.replace(`/PlanetChatRoom/Live/${res.content}`);
          } else {
            // 关闭弹框并跳转创建空间成功页
            dispatch({
              type: 'userInfoStore/setTaskListState',
              payload: {
                createModalVisible: false,
              },
            });
            // 判断如果是成功页，则使用replace，成功页只展示一次，不允许返回
            /* let historyJump = history.push
               if (path.endsWith('KingDomSuccessPage')) {
                  historyJump = history.replace
               }
            */
            let historyJump = history.replace;
            historyJump({
              pathname: '/UserInfo/SpaceSuccessPage',
              query: {
                id: res.content,
                starSpaceType: 1, // 创建直播
              },
            });
          }
        } else {
          Toast.show({ content: res.msg || '数据加载失败' });
          return;
        }
      })
      .catch((err: any) => {
        console.log(err);
      });
  };

  return (
    <div className={styles.page_Warp}>
      <Helmet>
        <title>{`${id ? '编辑' : '创建'}直播`}</title>
      </Helmet>
      <NavBar
        title={`${!!id ? '编辑' : '创建'}直播`}
        bordered={true}
        onBack={()=>{
          if (history.length > 1) {
            history.goBack()
          } else {
            history.replace('/')
          }
          dispatch({ type: 'userInfoStore/clean' })
        }}
      />
      <div className={styles.content_warp}>
        <Form form={form} validateTrigger="onBlur">
          <div className={styles.page_content} ref={contentRef}>
            {(!isEditSpace && isSuperAccount) || (isSuperAccountLocal && !isEditSpace) ? (
              <div className={styles.space_form_box}>
                <div className={styles.space_form_title}>主持人</div>
                <div
                  className={styles.space_form_content}
                  onClick={() => {
                    if (uploadVideoLoading || loadingByUpload || loadingByAdverUpload) {
                      return;
                    }
                    setCreateKingdomOrSpaceType(4);
                    dispatch({
                      type: 'userInfoStore/setTaskListState',
                      payload: { createModalVisible: true },
                    });
                  }}
                >
                  <div
                    className={classNames({
                      [styles.space_input_init_text]: true,
                      [styles.selected]: !!selectedCompere,
                    })}
                  >
                    {selectedCompere ? (
                      <div className={styles.host_content}>
                        {selectedCompere.headUrlShow ? (
                          <>
                            <img src={selectedCompere.headUrlShow} alt="" />
                            {selectedCompere?.name}
                          </>
                        ) : (
                          <>
                            <div
                              className={styles.no_comment_head}
                              style={{ background: randomColor(selectedCompere?.id) }}
                            >
                              {processNames(selectedCompere?.name)}
                            </div>
                            {selectedCompere?.name}
                          </>
                        )}
                      </div>
                    ) : (
                      '请选择主持人'
                    )}
                  </div>
                  <img className={styles.space_form_arrow} src={rightArrowIcon} alt="" />
                </div>
              </div>
            ) : null}

            {/* 直播名称 */}
            <div
              style={{ paddingBottom: '0px', borderBottom: 'none' }}
              className={styles.special_wrap}
            >
              <div style={{ marginBottom: '8px' }} className={styles.space_form_box}>
                <div className={styles.space_form_title}>
                  <span>*</span>
                  {CreateSpaceText}名称
                </div>
              </div>

              <div className={styles.space_form_box_spaceName}>
                <div
                  onClick={useCallback(() => {
                    setCreateKingdomOrSpaceType(15);
                    let spaceName = form.getFieldValue('spaceName');
                    dispatch({
                      type: 'userInfoStore/setTaskListState',
                      payload: {
                        createModalVisible: true,
                        spaceName: spaceName ? spaceName.trim() : null,
                      },
                    });
                  }, [spaceInputVal])}
                  className={styles.space_form_box_cover}
                >
                  {spaceCoverUrlView && <img src={spaceCoverUrlView} alt={''} />}
                  <div className={styles.space_form_box_btn}>更换</div>
                </div>
                <div className={styles.space_form_box_title}>
                  <Form.Item
                    label=""
                    name="spaceName"
                    rules={[
                      { required: true, message: `请输入${CreateSpaceText}名称` },
                      {
                        pattern:
                          /^[~\s!！@#$￥%^…&*()_—+{}：:‘’“”《》？"|<>?`\-=[\];',.。，；（）、a-zA-Z0-9\u4e00-\u9fa5/]+$/,
                        message: `${CreateSpaceText}名称不能包含特殊字符`,
                      },
                      { max: 50, message: '输入长度最大为50个字符!' },
                    ]}
                  >
                    <TextArea
                      type="text"
                      autoComplete="off"
                      placeholder={`为${CreateSpaceText}取一个讨论话题`}
                      style={{ '--text-align': 'left' }}
                      rows={3}
                      onChange={(val) => setSpaceInputVal(val)}
                      onBlur={spaceInputBlurFn}
                    />
                  </Form.Item>
                  {/*种植体集采下，民营诊所如何收费才能保证利润*/}
                </div>
              </div>
            </div>

            {/* 直播介绍 */}
            <div className={styles.space_introduction_wrap}>
              <div className={styles.space_introduction}>{CreateSpaceText}介绍</div>
              <Form.Item
                label=""
                name="spaceIntroduce"
                rules={[
                  {
                    pattern:
                      /^[~\s!！@#$￥%^…&*()_—+{}：:‘’“”《》？"|<>?`\-=[\];',.。，；（）、a-zA-Z0-9\u4e00-\u9fa5/]+$/,
                    message: `${CreateSpaceText}介绍不能包含特殊字符`,
                  },
                ]}
              >
                <TextArea
                  placeholder={`详细描述${CreateSpaceText}分享的话题内容`}
                  rows={4}
                  showCount
                  maxLength={100}
                  onBlur={spaceTextAreaBlurFn}
                  className={styles.space_introduction_input}
                />
              </Form.Item>
            </div>

            {/* 邀请嘉宾 */}
            <div className={styles.space_guest_wrap}>
              <div className={styles.space_guest_box}>
                <div className={styles.space_guest_title}>邀请嘉宾</div>
                <div className={styles.space_guest_illustrate}>最多可邀请15位嘉宾</div>
              </div>
              <div className={styles.space_guest_list_wrap}>
                {selectedGuest &&
                  selectedGuest.map(
                    (item: {
                      id: React.Key | null | undefined;
                      headUrlShow: string | undefined;
                      name:
                        | boolean
                        | React.ReactChild
                        | React.ReactFragment
                        | React.ReactPortal
                        | null
                        | undefined;
                    }) => (
                      <div className={styles.space_guest_item} key={item.id}>
                        <div className={styles.space_guest_img}>
                          {item.headUrlShow ? (
                            <img src={item.headUrlShow} alt="" />
                          ) : (
                            <div
                              className={styles.no_comment_head}
                              style={{ background: randomColor(item.id) }}
                            >
                              <span>{processNames(item?.name)}</span>
                            </div>
                          )}
                        </div>
                        <div className={styles.space_guest_name}>{item.name}</div>
                        <span
                          className={classNames({
                            [styles.space_guest_item_icon]: true,
                            [styles.show]: minusIconVisible,
                          })}
                          onClick={() => {
                            deleteGuest(item);
                          }}
                        >
                          <img src={redReduceIcon} alt="" />
                        </span>
                      </div>
                    ),
                  )}
                {/* 加嘉宾 */}
                {selectedGuest && selectedGuest.length < 15 ? (
                  <div
                    className={styles.space_guest_add}
                    onClick={() => {
                      if (uploadVideoLoading || loadingByUpload || loadingByAdverUpload) {
                        return;
                      }
                      setCreateKingdomOrSpaceType(5);
                      dispatch({
                        type: 'userInfoStore/setTaskListState',
                        payload: {
                          createModalVisible: true, // 打开弹框
                        },
                      });
                    }}
                  >
                    <img src={addIcon} alt="" />
                  </div>
                ) : (
                  ''
                )}
                {/* 减嘉宾 */}
                {selectedGuest && selectedGuest.length >= 1 ? (
                  <div
                    className={styles.space_guest_reduce}
                    onClick={() => {
                      setMinusIconVisible((visible) => !visible);
                    }}
                  >
                    <img src={reduceIcon} alt="" />
                  </div>
                ) : (
                  ''
                )}
              </div>
            </div>
          </div>

          <div
            style={{
              marginTop: '15px',
              paddingTop: '16px',
            }}
            className={styles.page_content}
          >
            <div className={styles.title_warp_box}>
              <div className={styles.title}> 直播设置 </div>
              <div
                onClick={() => {
                  setShowLiveSettings(!showLiveSettings);
                }}
                className={styles.title_item_btn}
              >
                {!showLiveSettings && <UpOutlined style={{ fontWeight: '700' }} />}
                {showLiveSettings && <DownOutlined style={{ fontWeight: '700' }} />}
              </div>
            </div>

            <div className={styles.from_warp}>
              <div
                className={classNames({
                  [styles.from_box_warp_hidden]: !!showLiveSettings,
                })}
              >
                <div style={{ paddingBottom: 0 }} className={styles.space_password_wrap}>
                  <div className={styles.space_password_box}>
                    <div className={styles.space_password_title}>{CreateSpaceText}密码</div>
                    <div className={styles.space_password_open_btn}>
                      <Switch
                        style={{
                          '--checked-color': '#0095FF',
                          '--height': '26px',
                          '--width': '48px',
                        }}
                        checked={isSwitch}
                        disabled={isEditSpace}
                        onChange={(val) => {
                          passwordSwitchChangeFn(val);
                          // 当关闭会议密码时, 同时取消无密码可申请进入会议开关
                          if (!val) {
                            onChangeIsAllowApplications(val);
                          }
                        }}
                      />
                    </div>
                  </div>
                  {isSwitch ? (
                    <div className={styles.password}>
                      <Form.Item
                        label=""
                        name="spacePassword"
                        rules={[
                          { required: !isEditSpace && isSwitch, message: '请输入4位数字密码' },
                          { pattern: /^[0-9]{4}$/, message: '请输入4位数字密码' },
                        ]}
                      >
                        <Input
                          className={styles.input}
                          placeholder="请输入4位数密码"
                          maxLength={4}
                          autoComplete="off"
                          type={passType ? 'text' : 'password'}
                          onChange={(val) => setPassword(val)}
                          onBlur={passwordBlur}
                          disabled={isEditSpace}
                        />
                      </Form.Item>
                      <div
                        className={styles.eye}
                        onClick={() => {
                          setPassType(!passType);
                        }}
                      >
                        {passType ? (
                          <img src={openEyeIcon} alt="" />
                        ) : (
                          <img src={closeEyeIcon} alt="" />
                        )}
                      </div>
                    </div>
                  ) : null}
                </div>

                <div className={styles.space_password_wrap}>
                  <div className={styles.space_password_box}>
                    <div className={styles.space_password_title}>屏幕录制自动打开</div>
                    <div className={styles.space_password_open_btn}>
                      <Switch
                        style={{
                          '--checked-color': '#0095FF',
                          '--height': '26px',
                          '--width': '48px',
                        }}
                        checked={isAutoRecord}
                        // disabled={isEditSpace}
                        onChange={onChangeIsAutoRecord}
                      />
                    </div>
                  </div>
                </div>

                <div className={styles.space_password_wrap_bottm_line}></div>

                <div className={styles.upload_wrap}>
                  <div className={styles.upload_img_wrap}>
                    <div>
                      <div className={styles.upload_img_title}>
                        {CreateSpaceText}广告{/*(建议尺寸:750*114)*/}
                      </div>
                      <div className={styles.upload_img_tip}>(建议尺寸:750*422)</div>
                    </div>
                    <Spin spinning={loadingByAdverUpload}>
                      <div className={styles.upload_img_content}>
                        <div className={styles.upload_box}>
                          {spaceAdvertisingUrl ? (
                            <img
                              className={styles.upload_img}
                              src={spaceAdvertisingUrl?.fileUrlView}
                              alt=""
                            />
                          ) : (
                            <div>
                              <img className={styles.init_upload_img} src={uploadImg} alt="" />
                            </div>
                          )}
                        </div>
                        <Upload
                          headers={getHeaders()}
                          accept="image/*"
                          action={`/api/server/base/uploadFile?${stringify({
                            fileType: 15,
                            userId: UerInfo?.friUserId,
                          })}`}
                          listType="picture-card"
                          className={styles.edit_head_picture}
                          onChange={(info) => onChangeByUpload(info, 'ADVERT')}
                          onRemove={() => {}}
                          beforeUpload={beforeUpload}
                          showUploadList={false}
                        />
                      </div>
                    </Spin>
                  </div>
                </div>

                <div className={styles.upload_wrap}>
                  {spaceFromEnter.pageType != '4' && (
                    <div className={styles.upload_video_wrap}>
                      <div>
                        <div className={styles.upload_video_title}>
                          {CreateSpaceText}视频{/*(支持MP4,大小2G以内)*/}
                        </div>
                        <div className={styles.upload_img_tip}>(支持MP4,大小2G以内)</div>
                      </div>
                      <div className={styles.video_content}>
                        {
                          // 视频上传完后展示的样式
                          spaceVideoUrl ? (
                            <div className={classNames(styles.upload_video_content)}>
                              <div className={styles.uploading_icon_content}>
                                <img src={frameIcon} className={styles.init_upload_img} alt="" />
                                <div className={styles.video_name_text}>
                                  <span className={styles.uploading_name_text}>
                                    {videoNameModify()}
                                  </span>
                                  .mp4
                                </div>
                              </div>
                              <input
                                type="file"
                                name="uploadVideoInput"
                                id="uploadVideoInput"
                                accept="video/mp4"
                                className={styles.upload_video_input}
                                onChange={videoFileChange}
                                disabled={uploadVideoLoading}
                              />
                            </div>
                          ) : (
                            // 未上传默认样式以及上传中的样式
                            <div className={classNames(styles.upload_video_content)}>
                              {uploadVideoLoading ? (
                                // 上传中
                                <div
                                  className={styles.uploading_icon_content}
                                  style={{ background: 'none' }}
                                >
                                  <img src={frameIcon} className={styles.init_upload_img} alt="" />
                                  <div className={styles.uploading_icon_text}>
                                    上传{uploadProgress}%...
                                  </div>
                                </div>
                              ) : (
                                // 未上传
                                <div className={styles.uploading_init_icon_content}>
                                  <img className={styles.init_upload_img} src={uploadImg} alt="" />
                                </div>
                              )}
                              <input
                                type="file"
                                name="uploadVideoInput"
                                id="uploadVideoInput"
                                accept="video/mp4"
                                className={styles.upload_video_input}
                                onChange={videoFileChange}
                                disabled={uploadVideoLoading}
                              />
                            </div>
                          )
                        }
                      </div>
                    </div>
                  )}
                </div>

                {/* 直播课件 */}
                <div className={styles.upload_wrap}>
                  {
                    spaceFromEnter.pageType != '4'
                    &&
                    (<div className={styles.upload_img_wrap}>
                      <div>
                        <div style={{marginBottom:'12px'}} className={styles.upload_img_title_Courseware}>
                          {CreateSpaceText}课件
                        </div>
                        <div className={styles.upload_img_tip}>
                          {CreateSpaceText}课件(支持上传PPT、Word、PDF，大小100m以内，最多上传3个课件部分ppt动效、fash动画不支持上传，建议您在上传前检查并移除这些元素，以确保最佳的展示效果。谢谢您的合作!)
                        </div>
                      </div>
                      <div className={styles.upload_img_Content_Courseware}>
                          <div className={styles.upload_img_Content_Courseware_Upload}>
                            <Upload
                              disabled={(!!isEditSpace && spaceVideoUrl)}
                              showUploadList={true}
                              accept={'.pdf,.ppt,.doc,.docx,.pptx'} // 限制上传文件类型
                              headers={getHeaders()}
                              beforeUpload={beforeCoursewareUpload}
                              className={styles.edit_head_picture}
                              customRequest={coursewareFileRequest}
                              fileList={spaceCoursewares}
                              onRemove={(info) => {
                                let filterByspaceCoursewares = spaceCoursewares.filter((item) => {
                                  return item.src != info.src;
                                });
                                dispatch({
                                  type: 'userInfoStore/setTaskListState',
                                  payload: { spaceCoursewares:filterByspaceCoursewares },
                                });
                                if (checkTranscodeTimeoutId) { clearTimeout(checkTranscodeTimeoutId) };
                              }}
                              onChange={onChangeByCourseware}
                            >
                              <div className={styles.upload_img_content}>
                                <div className={styles.upload_box}>
                                  <div>
                                    <img className={styles.init_upload_img} src={uploadImg} alt="" />
                                  </div>
                                </div>
                              </div>
                            </Upload>
                          </div>
                      </div>
                    </div>
                  )}
                </div>

                {!isEditSpace && !consultationIdByQurey ? (
                  <div
                    onClick={() => {
                      if (!isSelectKingDom) jumpHandle();
                    }}
                    className={styles.space_form_associatedKingdom}
                  >
                    <div className={styles.space_form_title}>
                      <div className={styles.associatedKingdom}>
                        <div className={styles.textIcon}></div>
                        <div className={styles.text}>选择关联王国</div>
                        <div className={styles.RightOutlined}>
                          <RightOutlined
                            style={{
                              color: '#000000',
                              fontSize: '10px',
                            }}
                          />
                        </div>
                        <div className={styles.line}></div>
                      </div>
                    </div>
                    <div className={styles.associatedKingdom_content}>
                      {(selectedKingdom?.name || kingdomInfoData?.name) && (
                        <div className={styles.kingdom_name_text_box}>
                          {selectedKingdom?.name || kingdomInfoData?.name}
                        </div>
                      )}
                    </div>
                  </div>
                ) : null}
              </div>
            </div>

            <div className={styles.box_btn_warp}>
              <div onClick={submitFnByForm} className={styles.box_btn_start_live}>
                马上开始直播
              </div>
              <div
                onClick={() => {
                  // 图片、视频在上传的时候不可点击，上传完后才可点击
                  if (uploadVideoLoading || loadingByUpload || loadingByAdverUpload) return;
                  form
                    .validateFields()
                    .then((res) => {
                      // 判定课件是否正在上传中
                      const uploadingBySpaceCoursewares = Array.isArray(spaceCoursewares) && spaceCoursewares.filter((it) => it.status == 'uploading')
                      if (Array.isArray(uploadingBySpaceCoursewares) && uploadingBySpaceCoursewares.length > 0) {
                        message.warning('课件正在上传中,请稍后再试!');
                        return;
                      }

                      // 判断观众是否为企业用户，并且空间类型是否为空
                      setCreateKingdomOrSpaceType(10); // 弹出预约弹窗

                      dispatch({
                        type: 'userInfoStore/setTaskListState',
                        payload: {
                          createModalVisible: true, // 打开弹框
                        },
                      });
                    })
                    .catch((err) => {
                      console.log(err);
                    });
                }}
                className={styles.box_btn_reservation}
              >
                预约开始
              </div>
            </div>
          </div>
        </Form>
      </div>

      <CreateKingdomOrSpace comeType={createKingdomOrSpaceType} />
      <CropperImgModal/>
    </div>
  );
};

export default connect(({ userInfoStore, loading }: any) => ({
  userInfoStore,
  loading,
}))(index);
