.image_text_container {
  position: relative;
  padding: 0 12px 4px;
  .item_box {
    width: 100%;
    margin-bottom: 12px;
    display: flex;
    flex-wrap: nowrap;
    .left {
      width: 31.9%;
      min-width: 31.9%;
      height: 78px;
      border-radius: 4px;
      overflow: hidden;
      & > img {
        width: 100%;
        height: 100%;
      }
    }
    .right {
      flex: 1;
      overflow: hidden;
      padding-left: 12px;
      position: relative;
      .title {
        font-size: 15px;
        color: #000;
        font-weight: 500;
        line-height: 20px;
        margin-bottom: 4px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        -webkit-line-clamp: 2;
        text-overflow: ellipsis;
        word-break: break-all;
      }
      .subtitle {
        font-size: 12px;
        color: #999;
        line-height: 17px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .price {
        font-size: 12px;
        color: #FF5F57;
        line-height: 17px;
        position: absolute;
        left: 12px;
        bottom: 0;
      }
    }
  }
}
