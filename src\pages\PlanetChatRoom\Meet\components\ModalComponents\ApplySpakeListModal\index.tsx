/**
 * @Description: 申请发言弹窗
 */
import React, { useState, useEffect, useRef } from 'react';
import { history, connect } from 'umi'
import classNames from 'classnames'
import { Spin } from 'antd'
import { Popup } from 'antd-mobile';
import styles from './index.less';

import SearchInput from '@/components/SearchInput' // 公共搜索框组件
import Avatar from '@/pages/PlanetChatRoom/components/Avatar' // 头像组件
import NoDataRender from '@/components/NoDataRender' // 暂无数据组件

// 图片图标
import mikeOpenIcon from '@/assets/GlobalImg/mike_open.png' // 开启状态麦克风

interface PropsType {
  visible: boolean,                // 弹窗是否显示
  onCancel: () => void,            // 关闭弹窗
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {
    visible,
    PlanetChatRoom,
    onClickStopSpeaking, // 点击停止发言
    onClickAgreeSpeaking, // 点击同意发言
  } = props

  const {
    handUpList, // 申请连麦列表
  } = PlanetChatRoom || {}

  const inputRef = useRef(null)    // input输入框的ref

  const [queryKey, setQueryKey] = useState('')             // 搜索关键词

  useEffect(() => {
    if (visible) {

    } else {
      // 清空数据
      cleanState()
    }
  }, [visible])

  // 关闭弹窗清空数据
  const cleanState = () => {
    setQueryKey('')
  }

  // 搜索框输入事件
  const inputChangeFn = (value) => {
    console.log('搜索框输入', value)
    setQueryKey(value.trim())
  }

  // 点击搜索框的取消按钮
  const cancelBtnFn = () => {
    console.log('点击搜索框的取消按钮')
    setQueryKey('')
  }

  return (
    <Popup
      visible={visible}
      onMaskClick={props.onCancel}
      className={styles.popup_container}
      bodyStyle={{ height: 'calc(100% - 44PX)' }}
      destroyOnClose
    >
      <Spin spinning={false} wrapperClassName={styles.spin}>
        <div className={styles.container}>
          {/* 头部 */}
          <div className={styles.header_line} onClick={props.onCancel}>
            <div className={styles.header_line_bar}></div>
          </div>

          {/* 标题 */}
          <div className={styles.header_title_wrap}>
            <div className={styles.title}>申请发言</div>
          </div>

          {/* 搜索框 */}
          <div className={styles.search_wrap}>
            <SearchInput
              inputPlaceholder="搜索成员"
              inputChangeFn={inputChangeFn}
              cancelBtnFn={cancelBtnFn}
              isHistoryStatus={2}
              onPressEnterFun={() => {}}
              ref={inputRef}
            />
          </div>

          {/* 用户列表 */}
          <div className={styles.user_list_wrap} id="user_list_wrap">
            {
              handUpList && handUpList.length > 0 ? handUpList.map(item => {
                if (queryKey && item.name && item.name.indexOf(queryKey) == -1) {
                  return null
                }
                return (
                  <div key={item.wxUserId} className={styles.user_item}>
                    <div className={styles.left}>
                      <div className={styles.avatar_wrap}>
                        <Avatar size={24} userInfo={item}/>
                      </div>
                      <div className={styles.user_name}>{item.name}</div>
                      {/* 发言中，statusType状态类型：0申请连麦 1接受连麦 */}
                      {
                        item.statusType == 1 &&
                        <img src={mikeOpenIcon} width={24} height={24} style={{padding: 4, flexShrink: 0}} alt=""/>
                      }
                    </div>
                    <div className={styles.right}>
                      {
                        item.statusType == 1 ? <span onClick={() => onClickStopSpeaking(item)}>停止发言</span>
                          : <span onClick={() => onClickAgreeSpeaking(item)}>同意发言</span>
                      }
                    </div>
                  </div>
                )
              }) : <NoDataRender style={{marginTop: 40}} text="暂无申请~"/>
            }
          </div>
        </div>
      </Spin>
    </Popup>
  )
}
export default connect(({ loading, PlanetChatRoom }: any) => ({ loading, PlanetChatRoom }))(Index)
