import React from 'react';
import { history } from 'umi';
import { processNames, randomColor } from '@/utils/utils'
import styles from './index.less';

interface PropsType {
  kingdomInfo: any;                // 王国信息
}
const Index: React.FC<PropsType> = (props: PropsType)  => {
  const { kingdomInfo } = props;

  const {
    id,                            // ID
    kingdomCoverUrlShow,           // 封面
    name,                          // 王国名
    nationalNum,                   // 人数
    spaceNum,                      // 空间数量
    meetingSpaceNum,               // 会议数量
    imgTextNum,                    // 图文数量
    kingImgUrlShow,                // 国王头像
    kingId,                        // 国王ID
    kingName,                      // 国王姓名
    userInfoDtoList,               // 国民数据
  } = kingdomInfo || {};

  // 点击王国
  const goToUrl = (e) => {
    e.stopPropagation()
    e.preventDefault()
    history.push(`/Kingdom/${id}`)
  }

  return (
    <div className={styles.kingdom_box} onClick={goToUrl}>
      <div className={styles.kingdom_left}>
        <div className={styles.kingdom_avatar} style={(kingdomCoverUrlShow||userInfoDtoList) ? {backgroundImage: `url(${kingdomCoverUrlShow||userInfoDtoList[0]?.headUrlShow})`} : {backgroundColor: `${randomColor(kingId)}`}}>
          {kingImgUrlShow ? '' : processNames(kingName)}
        </div>
        <div>
          <div className={styles.kingdom_name}>{name}</div>
          <div className={styles.kingdom_info}>{spaceNum || 0}热议直播·{meetingSpaceNum || 0}热议会议·{nationalNum}国民·{imgTextNum}文章</div>
        </div>
      </div>
      {
        userInfoDtoList && userInfoDtoList.length > 0 &&
        <div className={styles.kingdom_right}>
          {
            userInfoDtoList.map(item => {
              return (
                <div
                  key={item.userId}
                  className={styles.right_avatar}
                  style={item.headUrlShow ? {backgroundImage: `url(${item.headUrlShow})`} : {backgroundColor: `${randomColor(item.userId)}`}}
                >
                  {item.headUrlShow ? '' : processNames(item.userName)}
                </div>
              )
            })
          }
          <div className={styles.right_avatar_more}></div>
        </div>
      }
    </div>
  )
}
export default Index
