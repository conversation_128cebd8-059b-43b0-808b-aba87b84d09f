// @ts-ignore
/* eslint-disable */
import request from '@/utils/request'
import { getOperatingEnv } from '@/utils/utils'

/**
 * 根据页面ID查询页面详情（时均瑶）
 * @params id                          页面id
 * @params userId                      用户id
 */
export async function getPageInfo(params) {
  return request('/api/server/componentManagement/getPageInfo', {
    method: 'GET',
    params,
  })
}

/**
 * 提交报名（时均瑶）
 * @params activityId                  活动ID
 * @params userName                    姓名
 * @params phone                       手机号
 * @params orgName                     机构名称
 * @params applySource                 报名来源(1小程序，2H5)
 * @params pageId                      页面ID
 */
export async function submitApplyInfo(params) {
  return request('/api/server/activityManagement/submitApplyInfo', {
    method: 'POST',
    data: params,
    headers: {
      type: getOperatingEnv() == 1 ? "" : 1,
    },
  })
}

/**
 * 生成小程序码（时均瑶）
 * @params type                        1 病例，2 专家
 * @params dataJson                    需要保存的数据，（在小程序端再调接口查出来）
 */
export async function qrCodeGenerator(params) {
  return request('/api/user/base/qrCodeGenerator', {
    method: 'POST',
    data: params,
    responseType: 'blob',
    headers: {
      type: getOperatingEnv() == 1 ? "" : 1,
    },
  })
}

/**
 * 获取首页地址（时均瑶）
 */
export async function getHomePageLink(params) {
  return request('/api/server/componentManagement/getHomePageLink', {
    method: 'GET',
    params,
    headers: {
      type: getOperatingEnv() == 1 ? "" : 1,
    },
  })
}

/**
 * 根据用户ID获取搜索关键字（张志军）
 */
export async function getWordList(params) {
  return request('/api/server/h5Index/getWordList', {
    method: 'GET',
    params,
    headers: {
      type: getOperatingEnv() == 1 ? "" : 1,
    },
  })
}

/**
 * 加入或退出王国（时均瑶）
 * @params id                          王国ID
 * @params wxUserId                    用户ID
 * @params type                        类型(1加入，2退出)
 */
export async function joinOrQuitKingdom(params) {
  return request('/api/server/kingdom/joinOrQuitKingdom', {
    method: 'POST',
    data: params,
    headers: {
      type: getOperatingEnv() == 1 ? "" : 1,
    },
  })
}

/**
 * H5首页检索（张志军）
 * @params pageNum                     页码
 * @params pageSize                    页数
 * @params searchKey                   搜索关键字
 * @params type                        默认不传或传0，空间1 王国2 病例3 用户4
 * @params wxUserId                    当前登录用户ID
 * @params status                      空间状态：1直播中、2预约中、3弹幕轰炸中
 */
export async function homeIndexSearch(params) {
  return request('/api/server/h5Index/homeIndexSearch', {
    method: 'POST',
    data: params,
    headers: {
      type: getOperatingEnv() == 1 ? "" : 1,
    },
  })
}


/**
 * web端-首页检索（张志军）
 * @params pageNum                     页码
 * @params pageSize                    页数
 * @params searchKey                   搜索关键字
 * @params type                        默认不传，专家 1  病历2
 */
export async function webHomeIndexSearch(params) {
  return request('/api/server/webHomeIndex/homeIndexSearch', {
    method: 'POST',
    data: params,
    headers: {
      type: getOperatingEnv() == 1 ? "" : 1,
    },
  })
}

/**
 * 获取WEB首页菜单（时均瑶）
 */
export async function getHomeMenuList(params) {
  return request('/api/server/webBase/getHomeMenuList', {
    method: 'GET',
    params,
    headers: {
      type: getOperatingEnv() == 1 ? "" : 1,
    },
  })
}
