import {
  editConsultationInfo,
  editConsultationNodeAndStatus,
  getCaseAttachmentList,
  getChatGroupMsg,
  getConsultationAndCaseInfo,
  getConsultationOrthodonticCaseInfo,
  getConsultationProcessNode,
  getConsultationWay,
  getDepSubjectDict,
  getImInfoByUser,
  getOrthodonticCaseInfo,
  isFocusMp,
  saveConsultationInfo,
  sendConsultationCaseAttachmentEMail,
  submitConsultationPictureOrderPay,
  submitConsultationVideoOrderPay,
  switchOrderCaseTemplate,
  updateCaseInfoQuestion,
} from '@/services/consultation'
import {editOrthodonticCaseInfo} from "@/services/CreationOrthodontics";

export default {
  namespace: 'consultation',
  state: {
    checkedTab: 0,                                         // 搜索结果页，选中tab
    searchKey: '',                                         // 搜索关键词

    imLoading: false,      // IM上传图片、视频所需loading

    softKeyboard: false,   // 软键盘抬起
    uploadProgress: []
  },

  effects: {
    // 登录后获取该用户的IM秘钥信息
    * getImInfoByUser({payload}, {call}) {
      const response = yield call(getImInfoByUser, payload)
      return response
    },
    // 获取图文聊天消息，分页倒序
    * getChatGroupMsg({payload}, {call}) {
      const response = yield call(getChatGroupMsg, payload)
      return response
    },
    // 查询指导和病例详情
    * getConsultationAndCaseInfo({payload}, {call}) {
      const response = yield call(getConsultationAndCaseInfo, payload)
      return response
    },
    // 查询指导流程节点
    * getConsultationProcessNode({payload}, {call}) {
      const response = yield call(getConsultationProcessNode, payload)
      return response
    },
    // 修改指导订单节点和状态
    * editConsultationNodeAndStatus({payload}, {call}) {
      const response = yield call(editConsultationNodeAndStatus, payload)
      return response
    },
    // 查询病例附件列表
    * getCaseAttachmentList({payload}, {call}) {
      const response = yield call(getCaseAttachmentList, payload)
      return response
    },
    // 发送指导病例附件到邮箱
    * sendConsultationCaseAttachmentEMail({payload}, {call}) {
      const response = yield call(sendConsultationCaseAttachmentEMail, payload)
      return response
    },

    // 查询指导方式
    * getConsultationWay({payload}, {call}) {
      const response = yield call(getConsultationWay, payload)
      return response
    },

    // 查询学科列表
    * getDepSubjectDict({payload}, {call}) {
      const response = yield call(getDepSubjectDict, payload)
      return response
    },

    // 创建订单(选择学科后确定)
    * saveConsultationInfo({payload}, {call}) {
      const response = yield call(saveConsultationInfo, payload)
      return response
    },

    // 编辑指导订单信息
    * editConsultationInfo({payload}, {call}) {
      const response = yield call(editConsultationInfo, payload)
      return response
    },
    // 正畸病例
    * editOrthodonticCaseInfo({payload}, {call}) {
      const response = yield call(editOrthodonticCaseInfo, payload)
      return response
    },

    // 图文指导提交订单或去支付
    * submitConsultationPictureOrderPay({payload}, {call}) {
      const response = yield call(submitConsultationPictureOrderPay, payload)
      return response
    },

    // 视频指导提交订单或去支付
    * submitConsultationVideoOrderPay({payload}, {call}) {
      const response = yield call(submitConsultationVideoOrderPay, payload)
      return response
    },
    // 记录用户已关注公众号
    * isFocusMp({payload}, {call}) {
      const response = yield call(isFocusMp, payload)
      return response
    },
    // 获取正畸方案病例详情(无指导ID时获取基本信息字典和患者信息)
    * getOrthodonticCaseInfo({payload}, {call}) {
      const response = yield call(getOrthodonticCaseInfo, payload)
      return response
    },
    // 判定是专家会诊创建的正畸病例类型数据
    * getConsultationOrthodonticCaseInfo({payload}, {call}) {
      const response = yield call(getConsultationOrthodonticCaseInfo, payload)
      return response
    },

    // 切换病例模板
    * switchOrderCaseTemplate({payload}, {call}) {
      const response = yield call(switchOrderCaseTemplate, payload)
      return response
    },

    // 修改会诊用户提问且同步病历问题，修改会诊类型
    * updateCaseInfoQuestion({payload}, {call}) {
      const response = yield call(updateCaseInfoQuestion, payload)
      return response
    },

  },

  reducers: {
    // 保存数据
    save(state, {payload}) {
      return {
        ...state,
        ...payload,
      }
    },
    // IM上传图片、视频所需loading
    saveImloading(state, {payload}) {
      return {
        ...state,
        ...payload,
      }
    },
    // 上传进度集合
    uploadProgressLoading(state, {payload}) {
      return {
        ...state,
        ...payload,
      }
    },
    // 清空数据
    clean(state, {payload}) {
      return {
        checkedTab: 0,                                         // 搜索结果页，选中tab
        searchKey: '',                                         // 搜索关键词

        spaceStatusList: [],                                   // 空间状态：1直播中、2预约中、3弹幕轰炸中

        achievementList: [],                                   // 病例成就
        difficultLevelDictList: [],                            // 难度等级
        startDate: null,                                       // 开始日期
        endDate: null,                                         // 结束日期
        depSubjectDictList: [],                                // 学科/科室

        cityList: [],                                          // 城市
        abilityLevelDictList: [],                              // 能力等级
        postTitleDictList: [],                                 // 职级
      }
    },
  },


}
