import { useEffect } from 'react';

require('../../utils/ct4');

const Captcha = (props) => {
  const { onSuccess, onReady, onClose } = props;

  useEffect(() => {
    window.initAlicom4(
      {
        captchaId: '2990bbcbf386097dc9f0edd42f7c8a92',
        product: 'bind',
        protocol: 'https://',
      },
      (captchaObj) => {
        window.captchaObj = captchaObj;
        captchaObj
          .onReady(() => {
            if (onReady) {
              onReady(captchaObj);
            }
          })
          .onNextReady(() => {})
          .onBoxShow(() => {})
          .onError(() => {})
          .onClose(() => {
            if (onClose) {
              onClose();
            }
          })
          .onSuccess(() => {
            if (onSuccess) {
              const res = captchaObj.getValidate();
              onSuccess(res);
            }
          });
      },
    );
  }, []);

  return <div />;
};

export default Captcha;
