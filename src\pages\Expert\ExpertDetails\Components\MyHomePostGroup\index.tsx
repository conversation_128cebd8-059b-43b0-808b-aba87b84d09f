/**
 * @Description: PC-医生主页-帖子合集
 */
import React, { useState } from 'react';
import { connect } from 'umi';
import classNames from 'classnames'
import styles from './index.less';

import MyHomePost from '../MyHomePost';                    // 个人中心-医生主页-帖子
import MyHomeArticle from '../MyHomeArticle';              // 个人中心-医生主页-文章
import MyHomeLink from '../MyHomeLink';                    // 个人中心-医生主页-外链
import MyHomeDrafts from '../MyHomeDrafts';                // 个人中心-医生主页-草稿箱

// tab签数据
const tabsDataSource = [
  { id: 1, name: '帖子' },
  { id: 2, name: '文章' },
  { id: 3, name: '外链' },
  { id: 4, name: '草稿箱' },
]

interface PropsType {
  isMyPages: boolean,              // 是否是自己的主页
  expertsUserId: number,           // 专家ID
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { dispatch, isMyPages, expertsUserId, pcAttention } = props
  const [tab, setTab] = useState(pcAttention?.subTabState || 1)     // tab状态

  // 切换tab签
  const onChangeTabs = (id) => {
    setTab(id)
    dispatch({
      type: 'pcAttention/save',
      payload: {
        subTabState: id,       // 二级tab状态
      }
    })
  }

  return (
    <>
      <div className={styles.post_group_container}>
        {/* tab签 */}
        <div className={styles.tabs_wrap}>
          {
            tabsDataSource.map(item => {
              // 非本人不展示草稿箱
              if (!isMyPages && item.id == 4) {
                return null
              }
              return (
                <span
                  key={item.id}
                  className={classNames(styles.tabs_item, {
                    [styles.checked]: tab == item.id,
                  })}
                  onClick={() => onChangeTabs(item.id)}
                >{item.name}</span>
              )
            })
          }
        </div>
        {/* 列表 */}
        {
          tab == 1 ? <MyHomePost isMyPages={isMyPages} expertsUserId={expertsUserId} />
            : tab == 2 ? <MyHomeArticle isMyPages={isMyPages} expertsUserId={expertsUserId} />
            : tab == 3 ? <MyHomeLink isMyPages={isMyPages} expertsUserId={expertsUserId} />
              : tab == 4 ? <MyHomeDrafts isMyPages={isMyPages} expertsUserId={expertsUserId} />
                : null
        }
      </div>

    </>
  );
};

export default connect(({ pcAttention, loading }: any) => ({ pcAttention, loading }))(Index);
