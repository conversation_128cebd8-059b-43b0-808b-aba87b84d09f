// 展开或收起设置列表
// SetConfigButton.js
import React from 'react';
import styles from './index.less';  // 引入自定义样式

const SideListTypeForSettings = 'Settings'                  // 设置列表
const SideListTypeForApply = 'Apply'                          // 申请列表
const SideListTypeForDistinguished = 'Distinguished'    // 嘉宾列表
const SideListTypeForSignInList = 'SignInList'             // 打卡列表

const GuestListButton = ({
                           starSpaceType,  // 空间类型 1-直播 2-会议
                           isLive,
                           isJoined,
                           setIsShowApplyForLinkMicList,
                           isShowApplyForLinkMicList,
}) => {
  const handleSetConfigClick = (e) => {
    e.stopPropagation()
    e.preventDefault()
    setIsShowApplyForLinkMicList(SideListTypeForDistinguished == isShowApplyForLinkMicList ? null : SideListTypeForDistinguished)
  };

  return (
    <div
      onClick={handleSetConfigClick} className={styles.HorizontalLiveRoom_Btn_Warp}>
      <i className={styles.title_Icon_jiabin_Icon}></i>
      <div className={styles.text}>{starSpaceType  == 2 ? '成员' : '嘉宾'}</div>
    </div>
  );
};

export default GuestListButton;
