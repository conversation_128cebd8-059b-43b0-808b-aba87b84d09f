/**
 * @Description: 移动端指导头部信息卡片
 * @author: 赵斐
 */
import React from 'react';

import photoIcon from '@/assets/Consultation/H5/photo_icon.png'
import videoIcon from '@/assets/Consultation/H5/video_icon.png'
import dayjs from 'dayjs'
import styles from './index.less'

interface PropsType {
  consultationType: string,  // 1 图文指导  2视频指导
  data:any,                 // 展示数据
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { consultationType ,data} = props;
  const {
    processNode,        // 进度状态
    createDate,         // 创建时间
    createUserName,     // 创建人
    status,             // 取消指导状态
    isFinish,           //当前节点是否完成(1是、0否)
  } = data || {}

  /**
   * 指导状态展示
   * @param processNode 状态
   */
  const TextDisplayFun = (processNode: number) => {
    if (consultationType == '1') {
      let type = processNode == 5?1:processNode == 6 && isFinish == 0?2:processNode == 6 && isFinish == 1?3:null
      switch (type) {
        case 1:
          return "病例资料被查看"
        case 2:
          return "问题被回复并对话"
        case 3:
          return "结束指导交易成功"
      }
    } else if (consultationType == '2') {
      let type = processNode == 5?1:processNode == 6?2:processNode == 7?3:processNode == 8?4:processNode == 9 && isFinish == 0?5:processNode == 9 && isFinish == 1?6:null
      switch (type) {
        case 1:
          return "病例资料被查看"
        case 2:
          return "预约视频会议"
        case 3:
          return "视频沟通"
        case 4:
          return "结束指导"
        case 5:
          return "支付指导费用"
        case 6:
          return "交易成功"
      }
    }
  }

  return (
    <div className={styles.wrap}>
      <div className={styles.header}>
        <span className={styles.header_icon}>
          {
            consultationType == '1' ? <img src={photoIcon} alt="" /> : <img src={videoIcon} alt="" />
          }
        </span>
        <span className={styles.header_title}>{consultationType == '1' ? "图文指导" : "视频指导"}</span>
        {consultationType != '1' ? <span className={styles.header_title_tips}>请保持手机畅通</span> : null}
        {
          status !=0?<span className={styles.header_status}>{TextDisplayFun(processNode)}</span>:<span className={styles.header_close_status}>已取消</span>
        }

      </div>
      <div className={styles.content}>
        <p>创建人：{createUserName}</p>
        <p>创建时间：{createDate?dayjs(createDate).format('YYYY-MM-DD'):null}</p>
      </div>
    </div>
  )
}
export default Index
