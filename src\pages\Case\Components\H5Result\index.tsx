/**
 * @Description: 优秀病例搜索结果页面
 * @author: 赵斐
 */
import React, { useEffect, useState ,useRef } from 'react';
import styles from './index.less';
import { connect ,history } from 'umi';
import { InfiniteScroll, Mask } from 'antd-mobile'
import { Spin } from 'antd'
import imageTextIcon from '@/assets/Case/image_text.png'; // 图文切换小图标
import filterIcon from '@/assets/Case/filter_icon.png'; // 筛选小图标
// 搜索组件
import SearchInput from '@/components/SearchInput';
// 筛选抽屉组件
import CasesScreenModal from "./CasesScreenModal"
// 列表卡片展示组件
import CaseList from '@/components/CaseList';
// 数据加载异常
import LoadingException from '@/components/LoadingException'
// 导航组件
import NavBar from '@/components/NavBar'


const Index: React.FC = (props: any) => {
  const { dispatch, loading ,cases ,whereStatus } = props || {}; // whereStatus 首页跳转过来带筛选条件
  const initState = {
    total: 0,
    dataSource: [],
    searchValue: cases.searchValue,
  }
  const listRef = useRef<any>(null);
  const onRef = useRef(null);
  const { imgShowOrHide ,screenHighlight:Highlight} = cases || {}
  const [state, setState] = useState(initState)        // 列表接口所需参数及数据
  const [statePageNum, setStatePageNum] = useState(1)  // 当前分页
  const [isShowImage, setIsShowImage] = useState(imgShowOrHide); // 是否展示图片
  const [screenModalState, setScreenModalState] = useState(false);  // 筛选抽屉状态
  const [interfaceStatus, setInterfaceStatus] = useState(0); // 接口状态
  const [screenHighlight, setScreenHighlight] = useState(Highlight);  // 筛选文案高亮
  const {
    total,             // 总条数
    dataSource,        // 优秀病例数据
    searchValue,       // 搜索值

  } = state
  const {
    checkDepSubject,   // 选中学科
    checkAbilityLevel, // 选中能力等级
    checkAchievement,  // 选中病例成就
    startDate,         // 开始时间
    endDate,           // 结束时间
  } = cases
  useEffect(() => {
    const { searchValue } = cases
    let screenObj = {
      checkDepSubject: [],
      checkAbilityLevel: [],
      checkAchievement: [],
      startDate: null,
      endDate: null,
    }
    // 筛选条件
    if(whereStatus){
      screenObj = {
        checkDepSubject: [parseInt(whereStatus)],
        checkAbilityLevel: [],
        checkAchievement: [],
        startDate: null,
        endDate: null,
      }
      dispatch({
        type: 'cases/save',
        payload:{
          searchValue:'',         // 搜索条件
          screenHighlight:true,  // 清空高亮
          ...screenObj,
        }
      })
      setScreenHighlight(true)
    }
    if(screenHighlight){
      
      screenObj = {
        checkDepSubject,
        checkAbilityLevel,
        checkAchievement,
        startDate,
        endDate,
      }
    }
    getExcellentCaseList(1, searchValue,screenObj)
  }, [])

  // 获取优秀列表数据
  const getExcellentCaseList = async (current: number, searchKey: string = "",screenObj:any) => {
    const { checkDepSubject, checkAbilityLevel ,checkAchievement , startDate , endDate} = screenObj || {};
   await dispatch({
      type: "cases/getExcellentCaseList",
      payload: {
        pageNum: current,
        pageSize:30,
        searchKey: searchKey,          // 搜索关键字
        achievementList: checkAchievement,
        depSubjectDictList: checkDepSubject,
        difficultLevelDictList: checkAbilityLevel,
        startDate,
        endDate,
      }
    }).then((res: any) => {
      const { code ,content } = res || {};
      if (code == 200) {
        if(whereStatus){
          history.replace("/Case/CaseResult")
        }
        const { total, resultList } = content || {};
        if (Array.isArray(resultList) && resultList.length == 0) {
          setState({
            ...state,
            dataSource:[],
            total:0,
            searchValue:searchKey
          })
          setInterfaceStatus(0)
          return
        }
        let data = current == 1 ? [] : dataSource;
        data = data.concat(resultList);
        setState({
          ...state,
          dataSource: [...data],
          total,
          searchValue:searchKey
        })
        setStatePageNum(current)
      }else{
        setInterfaceStatus(1)
      }
    }).catch((err: String) => {
      console.log(err)
      setInterfaceStatus(1)
    })
  }

  // 加载更多数据
  let loadMore = async () => {
    await getExcellentCaseList(statePageNum + 1,searchValue,cases)
  }

  /**
   * 关闭筛选弹窗状态
   * @param type   1 确定  其他取消
   * @param obj    type 传1 并传筛选条件数据
   */
  const onClickHideScreenFun = (type: number, obj?: any) => {
    listRef.current.scrollTop = 0
    if (type == 1) {
      const { checkDepSubject ,checkAbilityLevel ,checkAchievement ,startDate ,endDate} = obj || {}
      dispatch({
        type: 'cases/save',
        payload:{
          searchValue:'',         // 搜索条件
          checkDepSubject: Array.isArray(checkDepSubject) && checkDepSubject.length?checkDepSubject:[],        // 选中学科
          checkAbilityLevel:Array.isArray(checkAbilityLevel) && checkAbilityLevel.length?checkAbilityLevel:[],      // 选中能力等级
          checkAchievement: Array.isArray(checkAchievement) && checkAchievement.length?checkAchievement:[],       // 选中病例成就
          startDate,              // 开始时间
          endDate,                // 结束时间
          screenHighlight:true,  // 清空高亮
        }
      })
      setScreenHighlight(true)
    } else {
      dispatch({
        type: 'cases/save',
        payload:{
          checkDepSubject: [],
          checkAbilityLevel: [],
          checkAchievement: [],
          startDate: null,
          endDate: null,
          screenHighlight:false,  // 筛选文案高亮
        }
      })
      setScreenHighlight(false)
    }
    setScreenModalState(false)
    getExcellentCaseList(1, searchValue,obj)

  }

  /**
   * 搜索病例相关内容
   * @param value 搜索值
   */
  const onChangeSearchFun = (value: string) => {
    setState({
      ...state,
      searchValue: value,
    })
    // if(value == ""){
    //   listRef.current.scrollTop = 0
    //   dispatch({
    //     type: 'cases/save',
    //     payload:{
    //       searchValue:"",         // 搜索条件
    //     }
    //   })
    //   getExcellentCaseList(1, "",cases)
    // }

  }

  // 点击完成进行搜索
  const onSearchEnterFun = () => {
    listRef.current.scrollTop = 0
    dispatch({
      type: 'cases/save',
      payload:{
        searchValue:searchValue,         // 搜索条件
      }
    })
    getExcellentCaseList(1, searchValue,cases)
  }

  // 清空搜索值
  const clearSearchFun = () => {
    listRef.current.scrollTop = 0
    if(checkDepSubject.length || checkAbilityLevel.length || checkAchievement.length || startDate){
      setScreenHighlight(false)
      onRef.current && onRef.current.clearScreenFun()
      dispatch({
        type: 'cases/save',
        payload:{
          searchValue:'',         // 搜索条件
          checkDepSubject: [],        // 选中学科
          checkAbilityLevel: [],      // 选中能力等级
          checkAchievement: [],       // 选中病例成就
          startDate: null,              // 开始时间
          endDate: null,                // 结束时间
          screenHighlight:false,  // 清空高亮
        }
      })
      getExcellentCaseList(1,"","")
    }else{
      dispatch({
        type: 'cases/save',
        payload:{
          searchValue:'',         // 搜索条件
        }
      })
      getExcellentCaseList(1,"",cases)
    }
    
  }
  /**
   * 点击图文模式
   * @param status  false、true
   */
  const onClickImgTextFun = (status: boolean) => {
    setIsShowImage(status)
    dispatch({
      type: "cases/imgShowOrHide",
      payload: {
        isShow: status ? 2 : 1,
      }
    })
    dispatch({
      type: "cases/save",
      payload: {
        imgShowOrHide: status,
      }
    })
  }
  // 接口调用异常重新调用列表接口
  const retryFun = () => {
    getExcellentCaseList(1, searchValue,cases)
  }

  // 重置接口异常状态
  const resetStatusFun = ()=>{
    setInterfaceStatus(0)
  }
  const load = !!loading.effects['cases/getExcellentCaseList'] ||   // 病例列表接口loading
    !!loading.effects['cases/imgShowOrHide']       // 图文模式记录接口loading
  return (
    <Spin spinning={load}>
      <div className={styles.wrap}>
        <div className={styles.header}>
          <NavBar title={"查病例"} className={styles.header_nav}/>
        </div>
        <SearchInput
          isHistoryStatus={2}
          inputPlaceholder={'搜索病例相关内容'}
          defaultInputValue={searchValue}
          inputChangeFn={onChangeSearchFun}
          cancelBtnFn={clearSearchFun}
          onPressEnterFun={onSearchEnterFun}
        />
        <div className={styles.screen_wrap}>
          <div className={styles.box}>
            <div className={isShowImage ? styles.img_switch_box : styles.img_init_box} onClick={() => { onClickImgTextFun(!isShowImage) }}><img src={imageTextIcon} alt="" />图文模式</div>
            <div className={styles.lines}></div>
            <div className={screenHighlight ? styles.screen_list_highlight_box : styles.screen_list_box} onClick={() => { setScreenModalState(true) }}>筛选<img src={filterIcon} alt="" /></div>
          </div>
          {
            screenModalState && <Mask visible={screenModalState} onMaskClick={() => setScreenModalState(false)} className={styles.mask_box}>
             <CasesScreenModal onRef={onRef} onClickHideScreenFun={onClickHideScreenFun} />
          </Mask>
          }
          
        </div>
        <div className={styles.case_list_content} ref={listRef}>
          {
            Array.isArray(dataSource) && dataSource.length ? <CaseList componentData={{ dataList: dataSource }} isShowImage={isShowImage} style={{padding: '16px 12px 4px'}} /> :
            <LoadingException exceptionStyle={{ paddingTop: 110 }} interfaceStatus={interfaceStatus} retryFun={retryFun} resetStatusFun={resetStatusFun}/>
          }
          {
            total>0 && <InfiniteScroll loadMore={loadMore} hasMore={total>dataSource.length} threshold={100} />
          }
          
        </div>
      </div>
    </Spin>
  )
}
export default connect(({ cases, loading }: any) => ({ cases, loading }))(Index)

