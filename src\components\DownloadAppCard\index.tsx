/**
 * @Description: 移动端-下载App卡片组件
 */
import React,{ useState } from 'react';
import dayjs from 'dayjs';
import {connect, history} from 'umi'
import { getOperatingEnv, isIOS } from '@/utils/utils'
import classNames from 'classnames'
import FDIcon from '@/assets/AppDownloadFDIcon.png';  // Friday logo 图标
import SearchIcon from '@/assets/AppDownloadSearchIcon.png'; // 搜索图标
import CloseIcon from '@/assets/AppDownloadCloseIcon.png';  // 关闭图标


import styles from './index.less'

interface IProps {
  title?: string;  // 标题
  description?: string;  // 描述
  icon?: string;  // 图标
  info?:object;  // 直播或会议的id，type 1:直播 2:会议
  style?: object;  // 样式
}

const DownloadAppCard: React.FC<IProps> = (props:IProps) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');  // 用户信息
  const fridayCardExpirationDate = localStorage.getItem('expirationDateFridayCard');  // 下载app卡片过期时间
  const { title, description, icon, info, style } = props;
  const [ showDownloadAppCard, setShowDownloadAppCard ] = useState(fridayCardExpirationDate?false:true)  // 是否显示下载app卡片

  // 点击下载app
  const downloadApp = () => {
    if(getOperatingEnv()==2){
      history.push(`/AppDownload?info=${encodeURIComponent(JSON.stringify(info))}`)
    }else{
      if (isIOS()) {
        history.push(`/AppDownload?info=${encodeURIComponent(JSON.stringify(info))}`)
      } else {
        let appUrl= info?.type==1?`${window.location.origin.replace('https', 'friday')}/PlanetChatRoom/Live/${info?.roomId}?fromBrowser=1&token=${localStorage.getItem('access_token')?localStorage.getItem('access_token'):''}&phone=${UserInfo?.phone?UserInfo?.phone:''}`:`${window.location.origin.replace('https', 'friday')}/PlanetChatRoom/Meet/${info?.roomId}?fromBrowser=1&token=${localStorage.getItem('access_token')?localStorage.getItem('access_token'):''}&phone=${UserInfo?.phone?UserInfo?.phone:''}`;
        const start = Date.now();
        let timeout;
        const checkOpen = () => {
          const elapsed = Date.now() - start;
          if (elapsed < 2000) {
            window.location.href ='https://jwsmedstatic.oss-cn-hangzhou.aliyuncs.com/fileByHand/app-release.apk'
          }
        };
        const handleVisibilityChange=()=> {
          if (document.visibilityState === 'hidden') {
            clearTimeout(timeout);
            document.removeEventListener('visibilitychange', handleVisibilityChange);
          }
        }
        timeout = setTimeout(checkOpen, 1500);
        if(navigator.userAgent.indexOf("Firefox") > -1){
          window.location.href = appUrl;
          timeout = setTimeout(() => {
            checkOpen();
            clearTimeout(timeout);
          }, 1500);
        }else{
          const iframe = document.createElement('iframe');
          iframe.style.display = 'none';
          iframe.src = appUrl;
          document.body.appendChild(iframe);
          timeout = setTimeout(() => {
            document.body.removeChild(iframe);
            clearTimeout(timeout);
          }, 1500);
        }

        window.addEventListener('blur', () => {
          clearTimeout(timeout);
        });
        document.addEventListener('visibilitychange', handleVisibilityChange);
      }
    }
  }

  // 关闭下载app卡片
  const closeDownloadAppCard = () => {
    const currentDate = dayjs();
    const expirationDate = currentDate.add(1, 'month');
    localStorage.setItem('expirationDateFridayCard', expirationDate.valueOf());
    setShowDownloadAppCard(false)
  }

  return (
    <div
      className={classNames(styles.downloadCard,{[styles.downloadCardHidden]:!showDownloadAppCard})}
      style={style?style:{}}
    >
      <div className={styles.downloadCardLeft}>
        <img src={icon?icon:FDIcon} alt="FRIDAY" />
        <div>
          <h3>{title?title:'FRIDAY周五牙医'}</h3>
          <p>{description?description:'牙医的学习交流社区'}</p>
        </div>
      </div>
      <div className={styles.downloadCardRight}>
        <img onClick={() => history.push('/Home/Search')} className={styles.searchIcon} src={SearchIcon} alt="FRIDAY Search"/>
        <span onClick={downloadApp}>下载APP</span>
        <img className={styles.closeIcon} onClick={closeDownloadAppCard} src={CloseIcon} alt="Close"/>
      </div>
    </div>
  );
};

export default connect(({loading}: any) => ({loading}))(DownloadAppCard)
