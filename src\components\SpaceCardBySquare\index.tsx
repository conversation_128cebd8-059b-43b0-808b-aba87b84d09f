/**
 * @Description: 广场推荐-空间卡片
 */
import React, { useEffect, useRef, useState } from 'react';
import { history } from 'umi';
import { stringify } from 'qs';
import DanmuJs from 'danmu.js'
import { useInView } from 'react-intersection-observer'; // 判断元素是否可见插件（作用：用户停留在某一内容超过3秒，展示评论输入框）
import { gdpFormat, getOperatingEnv } from '@/utils/utils';
import styles from './index.less';

// 图片icon
import blueAssociatedIcon from '@/assets/GlobalImg/blue_associated.png'; // 关联王国小图标
import space_status_icon1 from '@/assets/GlobalImg/space1.png' // 直播中
import space_status_icon2 from '@/assets/GlobalImg/space2.png' // 预约中

// 组件
import ListComments from '@/components/ListComments'; // 点赞/评论
import CoverImageOrVideo from '@/components/SpaceList/CoverImageOrVideo' // 空间封面视频自动播放组件
import UserCardByImageText from '@/components/UserCardByImageText' // 用户卡片信息公共组件

interface PropsType {
  pageType?: any; // 从哪个页面过来的标识，推荐首页过来传 1:场推荐 2:话题页面 3:我的主页草稿箱 4:国王详情 5:搜索页面
  item?: any; // 图文数据
  style?: any; // 样式
  onClickItem?: any; // 点击事件
  refreshDataById?: any; // 刷新页面方法
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const env = getOperatingEnv()
  const {
    pageType, // 从哪个页面过来的标识，推荐首页过来传 1:场推荐 2:话题页面 3:我的主页草稿箱 4:国王详情 5:搜索页面
    item, // 图文数据
    style = {}, // 样式
    onClickItem, // 点击事件
    refreshDataById, // 刷新页面方法
    isShowMoreOperate = false, // 是否展示点点点更多操作
  } = props

  const [showComments , setShowComments] = useState(false); // 是否展示评论框

  // 滚动元素可见配置
  const { ref, inView } = useInView({
    threshold: 0.6,
  });
  const timer = useRef(null); // 当前内容可见并停留3秒，展示评论框定时器

  const {
    createDate,       //: [创建时间] : "2024-01-09 14:53:47"
    createUserId,     //: [创建人id] : 60
    expertsInfo,      //: [专家信息] : null
    forwardDescribe,  //: [转发描述] : null
    gdp,              //: [页面GDP] : 210
    headUrlShow,      //: [用户头像] : null
    id,               //: [主键ID] : 57
    imageTextContent, //: [文章、帖子内容] : null
    imageTitle,       //: [标题] :null
    imageType,        //: [图文类型：1.文章 2.帖子 3.外链 4.空间] : 2
    isExperts,        //: [是否是专家：0:否，1:是] : 0
    isFocus,          //: [0未关注 1已关注] : 0
    isForward,        //: [是否转发：1.转发 0，非转发] : null
    isSpotLike,       //: [是否点赞 1是 0否] : 0
    kingdomId,        //: [关联王国ID] : 1
    kingdomName,      //: [关联王国名称] : "数字化讨论"
    outerChain,       //: [外链地址] : null
    spaceId,          //: [空间ID] :null
    spaceStatus,      //: [空间状态: 1直播中、2预约中、3弹幕轰炸中] : null
    spotLikeCount,    //: [点赞数量] : 0
    spotLikeUserList, //: [点赞用户信息，最多3条] : []
    textImgList,      //: [关联的图片] : null
    topicInfoList,    //: [关联的话题信息] : null
    userName,         //: [用户名称] : "志君"
    operateDateDescs, // 时间
    starSpaceType, // 类型：1 直播，2 会议
    isTemplateCover, // 是否是封面 1.是 0否
    space, // 空间信息
    status, // 状态：1.审核通过（已发布） 0.未审核 2.审核未通过 3.草稿
  } = item || {}

  const {
    msgList, // 弹幕list
    vodPathUrl, // 空间视频地址
    gdp: spaceGdp, // gdp
    pv, // 观看人数
  } = space || {}

  // 可见3秒后，出现评论输入框
  useEffect(() => {
    // 是推荐首页,则展示评论框
    if (pageType == '1') {
      clearTimeout(timer.current);
      // 判断当前内容是否可见,并停留3秒时,展示评论框
      if (inView) {
        timer.current = setTimeout(() => {
          console.log('显示')
          setShowComments(true);
        }, 3000)
      } else {
        setShowComments(false);
      }
    }
  }, [inView]);

  // 初始化弹幕
  useEffect(() => {
    if (item.id) {
      initializationByDanmu()
    }
  }, [item])

  // 初始化弹幕组件
  const initializationByDanmu = () => {
    if (msgList && msgList.length > 0) {
      new DanmuJs({
        channelSize: 28,             // 轨道大小
        container: document.getElementById(`vs${item.id}`), // 弹幕容器，该容器发生尺寸变化时会自动调整弹幕行为
        player: document.getElementById(`ms${item.id}`),    // 播放器容器（需要有这个，弹幕才能无限轮播）
        area: {
          // 弹幕显示区域
          start: 0,                  // 区域顶部到播放器顶部所占播放器高度的比例
          end: 1,                    // 区域底部到播放器顶部所占播放器高度的比例
        },
        mouseControl: false,         // 打开鼠标控制, 打开后可监听到 bullet_hover 事件。danmu.on('bullet_hover', function (data) {})
        mouseControlPause: false,    // 鼠标触摸暂停。mouseControl: true 生效
        chaseEffect: true,           // 开启滚动弹幕追逐效果, 默认为true
        comments: msgList && msgList.length > 0 ? msgList.map((item, index) => ({
          moveV: 100,                // 每秒移动距离，单位px
          id: index,                 // 弹幕id，需唯一
          txt: item,                 // 弹幕文字内容
          style: {
            color: '#FFFFFF',
            fontSize: '11px',
            borderRadius: '20px',
            padding: '0 6px',
            margin: '8px 0',
            height: '19px',
            lingHeight: '19px',
            backgroundColor: 'rgba(0,0,0,0.2)',
          },
        })) : []
      })
    }
  }

  // 点击发言内容，跳转到空间帖子详情
  const onClickItemFn = (e) => {
    e.stopPropagation()
    e.preventDefault()
    if (onClickItem) {
      onClickItem(id)
      return
    }
    history.push(`/CreateGraphicsText/SpaceDetails?${stringify({id: id})}`)
  }

  // 点击空间封面，跳转到空间详情
  const onClickCoverImg = (e) => {
    e.stopPropagation()
    e.preventDefault()
    if (starSpaceType == 2) {
      history.push(`/PlanetChatRoom/Meet/${spaceId}`)
    } else {
      history.push(`/PlanetChatRoom/Live/${spaceId}`)
    }
  }

  // 点击王国，跳转王国详情
  const onClickKingdom = () => {
    history.push(`/Kingdom/${kingdomId}`)
  }

  // 关注或取消关注回调，0 取消关注，1 关注
  const handleFollowAndCheck = (isFocus2) => {
    refreshDataById(id)
  }

  return (
    <div className={styles.space_wrap} style={style} ref={ref}>
      {/* 用户信息 */}
      <UserCardByImageText
        headUrlShow={headUrlShow} // 头像
        userName={userName} // 姓名
        createUserId={createUserId} // 用户ID
        isExperts={isExperts} // 是否是专家，1是 0否
        operateDateDescs={operateDateDescs} // 时间
        isFocus={isFocus} // 是否关注，1已关注，0未关注
        expertsInfo={expertsInfo} // 专家信息
        isShowMoreOperate={isShowMoreOperate} // 是否展示点点点更多操作，true展示，false不展示
        id={id} // 图文ID
        imageType={imageType} // 图文类型，
        status={status} // 图文类型：1.文章 2.帖子 3.外链 4.空间
        style={{marginBottom: 12}} // 样式
        handleFollowAndCheck={handleFollowAndCheck} // 关注回调
      />

      <div className={styles.space_tips_text} onClick={onClickItemFn}>
        {starSpaceType == 2 ? '发布了一条会议，快来参与聊天吧~' : '发布了一场直播，快来参与聊天吧~'}
      </div>

      {/* 空间信息 */}
      <div className={styles.cover_img} onClick={onClickCoverImg}>
        {/* 封面视频自动播放组件 */}
        <CoverImageOrVideo
          data={space}
          spaceCoverUrlShow={textImgList && textImgList[0] && textImgList[0].imageUrlShow}
        />
        {/* 封面中的标题，isTemplateCover 1是默认模版封面，微信浏览器环境不支持自动播放 */}
        {
          isTemplateCover == 1 && !(vodPathUrl && env != '2') &&
          <div className={styles.title_in_cover_image}>{imageTitle}</div>
        }
        {/* 空间状态: 1直播中、2预约中、3弹幕轰炸中 */}
        {
          (spaceStatus == 1 || spaceStatus == 2) &&
          <div className={styles.status_box}>
            <img src={spaceStatus == 1 ? space_status_icon1 : space_status_icon2} width={12} height={12} alt=""/>
            <span>{spaceStatus == 1 ? '进行中' : spaceStatus == 2 ? '预约中' : ''}</span>
          </div>
        }
        {/* 空间GDP */}
        <span className={styles.gdp}>{gdpFormat(spaceGdp)}GDP | {gdpFormat(pv)}观看</span>

        {/* 弹幕容器 */}
        <div id={`vs${item.id}`} className={styles.danmu_box}>
          <div id={`ms${item.id}`}></div>
        </div>
      </div>

      {/* 王国 */}
      {
        !!kingdomName &&
        <div className={styles.kingdom_wrap} onClick={onClickKingdom}>
          <img src={blueAssociatedIcon} width={14} height={14} alt="" />
          <span>{kingdomName}</span>
        </div>
      }

      {/* 评论 */}
      <ListComments
        showComments={showComments}
        pageType={pageType}
        commentJupm={onClickItemFn}
        item={item}
      />
    </div>
  )
};

export default Index;
