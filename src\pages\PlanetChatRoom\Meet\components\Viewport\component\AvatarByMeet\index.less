.AvatarByMeetWarp {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 80px;
  height: 100%;
  margin-top: 12px;
  margin-bottom: 12px;
}

.AvatarByMeetWarpBoxMargin_H5 {
  margin-right: 6px;
  margin-left: 6px;
}

.AvatarByMeetWarpBoxMargin_H5_H {
  margin-right: 12px;
  margin-left: 12px;
}

.AvatarByMeetWarpBoxMargin_PC {
  margin-right: 12px;
  margin-left: 12px;
}

.AvatarByMeetHostIcon {
  position: absolute;
  top: 41px;
  left: 17px;
  display: inline-block;
  width: 16px;
  height: 16px;
  background: #0095ff;
  background: url('~@/assets/PlanetChatRoom/im_user_icon2.png');
  background-size: 16px 16px;
  border-radius: 50%;
}

.AvatarByMeetBox {
  width: 52px;
  height: 52px;
  margin-bottom: 8px;
  overflow: hidden;
  border-radius: 50%;
}

.video_Title_box_left_avatar {
  width: 100%;
  height: 100%;
  margin-right: 8px;
  overflow: hidden;
  border-radius: 50%;
  opacity: 1;
}

.video_Title_box_left_avatar_img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  overflow: hidden;
  border-radius: 50%;
}

.head_sculpture_name {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #ffffff;
  font-weight: 500;
  font-size: 16px;
  white-space: nowrap;
}

.userInfoNameWarp {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;

  .userInfoNameBox {
    display: flex;
    align-items: center;
  }

  .userInfoName {
    width: 100%;
    margin-right: 1px;
    overflow: hidden;
    color: #ffffff;
    font-weight: 400;
    font-size: 12px;
    line-height: 14px;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .maikef {
    width: 12px;
    height: 12px;
  }
}

.HorizontalLiveRoom_camera_picture_mic_icon {
  display: inline-block;
  width: 15px;
  height: 12px;
  background: url('~@/assets/PlanetChatRoom/HorizontalLiveRoom_camera_picture_mic_icon.png')
    no-repeat;
  background-size: 12px 12px;
}

.HorizontalLiveRoom_camera_picture_forbidden_Lianmai {
  display: inline-block;
  width: 15px;
  height: 12px;
  background: url('~@/assets/PlanetChatRoom/SpatialDetail_MicrophoneOn_btn_Lianmai.png') no-repeat;
  background-size: 12px 12px;
}

.HorizontalLiveRoom_camera_picture_forbidden_mic_icon {
  display: inline-block;
  width: 15px;
  height: 12px;
  background: url('~@/assets/PlanetChatRoom/HorizontalLiveRoom_camera_picture_forbidden_mic_icon.png')
    no-repeat;
  background-size: 12px 12px;
}
