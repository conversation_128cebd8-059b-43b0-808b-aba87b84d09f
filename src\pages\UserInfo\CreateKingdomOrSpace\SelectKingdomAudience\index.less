.container {
  height: calc(70vh - 28px);
  position: relative;
}

.title_box {
  position: relative;
  .title {
    font-size: 17px;
    color: #000;
    font-weight: 500;
    line-height: 24px;
    text-align: center;
  }
  .title_btn {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    padding: 0 16px;
  }
}

.wrap {
  padding: 16px;
  box-sizing: border-box;

  .tabs_box {
    width: 100%;
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .tabs_item {
      font-size: 15px;
      font-weight: 500;
      color: #999999;
      line-height: 21px;
      margin-right: 16px;
    }

    .checked {
      font-size: 15px;
      font-weight: 600;
      color: #000000;
      line-height: 21px;
      position: relative;

      &::after {
        content: '';
        width: 12px;
        height: 3px;
        background: #000000;
        border-radius: 6px 6px 6px 6px;
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
      }
    }
  }

  .data_box {
    height: calc(70vh - 180px);
    overflow-y: auto;

    .data_item {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      margin-bottom: 16px;
      .avatar {
        width: 32px;
        height: 32px;
        border-radius: 2px;
        margin-right: 8px;

        .no_comment_head{
          width: 32px;
          height: 32px;
          border-radius: 2px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          font-weight: 500;
          color: #fff;
          white-space: nowrap;
        }

        img {
          width: 32px;
          height: 32px;
          border-radius: 2px;
        }
      }
      .data_name {
        flex: 1;
        font-size: 14px;
        color: #000;
        line-height: 20px;
        word-break: break-all;
      }
      .data_btn {
        height: 29px;
        line-height: 29px;
        text-align: center;
        padding: 0 8px;
        min-width: 48px;
        border-radius: 18px;
        font-size: 12px;
        color: #0095FF;
        background: #E6F4FF;
        white-space: nowrap;
        flex-shrink: 0;
        &.checked {
          color: #999;
          background: #F5F5F5;
        }
      }
    }
  }

  .nodata {
    font-size: 15px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #333333;
    line-height: 17px;
    margin-top: 50px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  
    .empty_title {
      font-size: 15px;
      color: #333;
      line-height: 21px;
      margin-bottom: 8px;
    }
    .empty_msg {
      font-size: 14px;
      color: #999;
      line-height: 20px;
    }
  
    img {
      width: 150px;
      height: 113px;
    }
  }  
}

.fixed_box {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding-bottom: constant(safe-area-inset-bottom);//兼容 IOS<11.2
  padding-bottom: env(safe-area-inset-bottom);//兼容 IOS>11.2
  .btn_box {
    padding: 0 16px 8px;
    .btn {
      height: 40px;
      line-height: 40px;
      background: #0095FF;
      border-radius: 20px;
      text-align: center;
      font-size: 16px;
      color: #fff;
    }
  }
}