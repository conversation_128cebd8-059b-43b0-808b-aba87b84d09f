
import request from "@/utils/request";
import {stringify} from "qs";
/**
 * 专家列表字典项     张志军
 * @returns
 */

export const getFilterDict = (data:any) =>
  request(`/api/server/h5Experts/getH5FilterDict`,{
    method:'GET',
    data
});
/**
 * 专家列表       -- 张志军
 * @param {string} pageNum
 * @param {string} pageSize
 * @param {string} abilityLevelDict
 * @param {string} city
 * @param {string} depSubjectDict
 * @param {string} postTitleDict
 * @param {string} searchKey
 * @param {string} vxOpenIdCipherText
 * @returns
 */
export const getExpertsList = (data:any) =>
  request(`/api/server/h5Experts/getExpertsList`,{
    method:'POST',
    data
});
/**
 * 根据微信openID获取搜索关键字（查询指定15行数据,最近3个月）   张志军
 * @returns
 */
export const getWordList = (data:any) =>
  request(`/api/server/h5Experts/getH5WordList`,{
      method:'GET',
      data
  });
/**
 * 获取专家详情信息   张志军
 * @param {string} wxUserId       当前微信用户ID，登录后必传
 * @param {string} expertsUserId  专家用户ID
 * @returns
 */
export const getExpertsInfo = (data:any) =>
  request(`/api/server/h5Experts/getH5ExpertsInfo?${stringify(data)}`,{
    method:'GET',
    data
  });

  /**
 * 通过专家用户ID，获取专家关联的优秀病历信息
 * @param {string} wxUserId
 * @param {string} expertsUserId
 */
export const getCaseInfoByExpertsUserId = (data:any) =>
request(`/api/server/h5ExcellentCase/getCaseInfoByExpertsUserId?${stringify(data)}`, {
  method:'GET',
  data
})

/**
 * 关注、取关 专家    张志军
 * @param {string} expertsUserId
 * @param {string} isFocus
 * @param {string} wxUserId
 * @returns
 */
export const followAndCheck = (data:any) =>
request(`/api/server/h5Experts/isH5Focus`,{
  method:'POST',
  data
});
/**
* 视频获取专家二维码    张志军
* @param {string} expertsUserId
* @param {string} wxUserId
* @returns
*/
export const getExpertsQrCode = (data:any) =>
request(`/api/server/h5Experts/getH5ExpertsQrCode?${stringify(data)}`,{
  method:'GET',
});
/**
* 获取检索用户的空间数据列表      张志军
* @param {string} pageNum       页码
* @param {string} pageSize      条数
* @param {string} searchUserId  检索用户id
* @param {string} wxUserId      当前用户ID
* @returns
*/
export const getStarSpaceListBySearchUserId = (data:any) =>
request(`/api/server/square/getStarSpaceListBySearchUserId?${stringify(data)}`,{
  method:'GET',
});

/**
 * 个人中心获取用户图文列表信息
 * personImageTextList
 * @param data
 * Query：page  size
 * Body:
 *  imageType  number  图文类型：1.文章 2.帖子 3.外链 4.空间，草稿传输空
 *  status     number[]  状态   item:number  状态：1.审核通过 0.未审核 2.审核未通过 3.草稿
 */
export const personImageTextList = (data:any) => {
  const { page,size } = data || {};
  return request(`/api/server/imageTextInfoPc/personImageTextList?${stringify({page,size})}`, {
    method: 'POST',
    data:data,
  });
}

