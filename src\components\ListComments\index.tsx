/**
 * 推荐列表评论/点赞/转发组件
 */
import React, { useCallback, useEffect, useState } from 'react';
import styles from './index.less';
import { Modal, TextArea, Mask } from 'antd-mobile';
import {connect, history} from 'umi';
import { processNames, randomColor } from '@/utils/utils';
import likeIcon from '@/assets/GlobalImg/like.png';
import likeActiveIcon from '@/assets/GlobalImg/like_active.png';
import commentIcon from '@/assets/GlobalImg/comment.png';
import forwardIcon from '@/assets/GlobalImg/forward.png';
import gdpIcon from '@/assets/GlobalImg/gdp.png';
import smileIcon from '@/assets/GlobalImg/smile.png';
import {stringify} from "qs";
import {message} from "antd";
import {saveCommentsOrReply} from "@/services/recommended";
import Emoji from "@/pages/CreateGraphicsText/Quill/ToolbarMobile/Emoji";
import {parseText} from "@/utils/im-index";

const Index: React.FC = (props:any) => {
  let { showComments=true, pageType=false ,item,recommended,dispatch } = props || {};

  item = item || {};
  let {
    createDate,       //: [创建时间] : "2024-01-09 14:53:47"
    createUserId,     //: [创建人id] : 60
    expertsInfo,      //: [专家信息] : null
    forwardDescribe,  //: [转发描述] : null
    gdp,              //: [页面GDP] : 210
    headUrlShow,      //: [用户头像] : null
    id,               //: [主键ID] : 57
    imageTextContent, //: [文章、帖子内容] : null
    imageTitle,       //: [标题] :null
    imageType,        //: [图文类型：1.文章 2.帖子 3.外链 4.空间] : 2
    isExperts,        //: [是否是专家：0:否，1:是] : 0
    isFocus,          //: [0未关注 1已关注] : 0
    isForward,        //: [是否转发：1.转发 0，非转发] : null
    isSpotLike,       //: [是否点赞 1是 0否] : 0
    kingdomId,        //: [关联王国ID] : 1
    kingdomName,      //: [关联王国名称] : "数字化讨论"
    outerChain,       //: [外链地址] : null
    spaceId,          //: [空间ID] :null
    spaceStatus,      //: [空间状态: 1直播中、2预约中、3弹幕轰炸中] : null
    // spotLikeCount,    //: [点赞数量] : 0
    // commentsCount,    //: [评论数量] : 0
    spotLikeUserList, //: [点赞用户信息，最多3条] : []
    textImgList,      //: [关联的图片] : null
    topicInfoList,    //: [关联的话题信息] : null
    userName,         //: [用户名称] : "志君"
    forwardCount,     //: [转发数量] : 0
    // spotLikeCount,
    spotLikeOtherCount,
    forwardSquareRecommendDto:forwardDto, // 被转发图文信息
  } = item || {};

  const [isShowComment, setIsShowComment] = useState(false); // 评论输入框是否展示
  const [textAreaVal, setTextAreaVal] = useState(''); // 输入框输入的内容
  const [releaseText, setReleaseText] = useState(''); // 评论展示
  const [likeStatus, setLikeStatus] = useState(isSpotLike == 1); // 点赞状态
  const userInfoData = JSON.parse(localStorage.getItem('userInfo') || '{}');
  const [ spotLikeCount,setSpotLikeCount ] = useState(isSpotLike == 1 ? item.spotLikeOtherCount + 1 : item.spotLikeOtherCount); // 点赞数量
  const [ commentsCount,setCommentsCount ] = useState(item.commentsCount); // 评论数量
  const [ isShowEmoji,setIsShowEmoji ] = useState(null)


  /*useEffect(()=>{
    setSpotLikeCount(item.spotLikeCount);
    setCommentsCount(item.commentsCount);
  },[item.spotLikeCount,item.commentsCount])*/

  // 阻止默认事件
  const preventDefaultHandler = (e) => {
    e.preventDefault()
  }

  // 添加评论
  const addComment = useCallback((e) => {
    e.stopPropagation()
    e.preventDefault()
    // 没登录跳转登录页
    if (!localStorage.getItem('access_token')) {
      history.push({
        pathname: '/User/login',
        query: {
          redirectByPush: window.location.href,
        }
      })
      return
    }
    setIsShowComment(true)
    // ios自动获取焦点兼容处理
    // document.getElementById('input_ios_focus') && document.getElementById('input_ios_focus').focus()

    // setTimeout(() => {
    //   // 自动获取焦点
    //   document.getElementById('textArea')?.focus();
    //
    //   // 添加禁止移动事件，若要删除当前事件， 需要配合hooks 中 useCallback方法使用
    //   document.getElementsByTagName('body')[0].addEventListener('touchmove', preventDefaultHandler, {passive: false})
    // }, 100)
  },[])

  // 输入框输入事件
  const onChangeInput = (val) => {
    setTextAreaVal(val)
  }

  // 转发
  const forwardClickFn = (e) => {
    e.stopPropagation()
    e.preventDefault()
    // 没登录跳转登录页
    if (!localStorage.getItem('access_token')) {
      history.push({
        pathname: '/User/login',
        query: {
          redirectByPush: window.location.href,
        }
      })
      return
    }else if(isForward  == 1 && !forwardDto){
      // 原内容已下架,无法转发
      message.warning('原内容已下架,无法转发!')
    }else{
      // 跳转到转发页面 转发id forwardId // 图文类型 imageType
      history.push(`/CreateGraphicsText/CreateForward?${stringify({ forwardId: id, imageType: imageType })}`)
    }
  }

  // 发布
  const onReleaseBtn = useCallback((e) => {
    e.stopPropagation()
    e.preventDefault()
    if(!!textAreaVal && textAreaVal.length > 0) {
      const val = textAreaVal.trim()
      setReleaseText(val)
      setIsShowComment(false)
      setTextAreaVal('')
      setCommentsCount(commentsCount + 1);
      saveCommentsOrReplyByFunc(val);
    }else {
      message.warning('请输入评论内容');
    }
    // 删除禁止滚动事件，需要配合hooks 中 useCallback方法使用
    // document.getElementsByTagName('body')[0].removeEventListener('touchmove', preventDefaultHandler, {passive: false})
  }, [textAreaVal,commentsCount])

  // 评论或回复评论或引用回复评论
  const saveCommentsOrReplyByFunc = async (val)=>{
    let params = {
      imageTextId: id,                     //  number 非必须  上级图文主键ID
      imageTextType: imageType,            //	number 非必须  上级图文类型：1.文章 2.帖子 3.外链 4.空间
      commentsContent: val,                               //	string 非必须  上级评论/回复内容
      commentsType: 0,
    }
    const data = await saveCommentsOrReply(params);
    const { code, content } = data || {}
    if (code == 200) {
      message.success('评论成功!')
    }else {
      message.error('评论失败!')
    }
  }

  // 关闭蒙层
  const closeMask = () => {
    console.log('关闭蒙层')
    setIsShowComment(false)
    setTextAreaVal('')
  }

  // 点赞事件
  const likeClickFn = useCallback(async (e) => {
    e.stopPropagation()
    e.preventDefault()
    // 没登录跳转登录页
    if (!localStorage.getItem('access_token')) {
      history.push({
        pathname: '/User/login',
        query: {
          redirectByPush: window.location.href,
        }
      })
      return
    }else{
      await setLikeStatus(!likeStatus)
      let newspotLikeCount = !likeStatus? spotLikeCount + 1 : spotLikeCount - 1
      await setSpotLikeCount(newspotLikeCount)
      // 去点赞
      let DataByImageTextLikeOrCancel = await dispatch({
        type: 'recommended/imageTextLikeOrCancel',
        payload: {
          imageTextId: id,           // 图文主键ID
          status: !likeStatus? 1 : 0, // 状态：1.正常 0.取消点赞
        }
      })
    }
  }, [likeStatus,spotLikeCount])


  // 关闭评论区时同时关闭表情框
  useEffect(()=>{
    if(!isShowComment){ setIsShowEmoji(false); }
  },[isShowComment])


  // 评论事件，点击跳转详情
  const commentClickFn = (e) => {
    e.stopPropagation()
    e.preventDefault()
    // 没登录跳转登录页
    if (!localStorage.getItem('access_token')) {
      history.push({
        pathname: '/User/login',
        query: {
          redirectByPush: window.location.href,
        }
      })
      return
    }else{
      props.commentJupm && props.commentJupm(e)
    }
  }

  const itemOnClick = (value,item) => {
    let textAreaValueStr = textAreaVal || '';
    setTextAreaVal(textAreaValueStr + item);
  }

  return <>
    <div className={styles.operation_area_content}>
      <div className={styles.operation_btn}>
        <div className={styles.operation_btn_icon} onClick={likeClickFn}>
          <img src={ likeStatus ? likeActiveIcon : likeIcon } width={20} height={20} alt="" />
          <span className={styles.operation_btn_num}>{spotLikeCount ? spotLikeCount : '0'}</span>
        </div>
        <div className={styles.operation_btn_icon} onClick={commentClickFn}>
          <img src={commentIcon} width={20} height={20} alt="" />
          <span className={styles.operation_btn_num}>{commentsCount ? commentsCount : '0'}</span>
        </div>
        <div className={styles.operation_btn_icon} onClick={forwardClickFn}>
          <img src={forwardIcon} width={20} height={20} alt="" />
          <span className={styles.operation_btn_num}>{forwardCount ? forwardCount : '0'}</span>
        </div>
      </div>
      <div className={styles.gdp}><img src={gdpIcon} alt="" width={14} height={14} style={{marginRight:'5px'}}/>{gdp} GDP</div>
    </div>
    {
      // 从推荐首页过来的标识
      pageType == 1 ?
      <>
        {
          (Array.isArray(spotLikeUserList) && spotLikeUserList.length > 1) &&
          <div className={styles.like_text_content_Warp}>
            <div className={styles.like_text_content}>
              {
                spotLikeUserList.map((itemChild, indexChild) => {
                  if (indexChild >= 3) { return null }
                  return (
                    <div key={indexChild} className={styles.right_avatar} style={itemChild.headUrlShow ? {backgroundImage: `url(${itemChild.headUrlShow})`} : {background: randomColor(itemChild.userId)}}>
                      {!itemChild.headUrlShow && processNames(itemChild.userName)}
                    </div>
                  )
                })
              }
            </div>
            <div className={styles.like_text}>
              {(isSpotLike == 1)  && <span>
                <span>{userInfoData.name}</span>
                和</span>
              }
              <span>其他{item.spotLikeOtherCount}位用户</span>点赞了
            </div>
          </div>
        }

        {
          releaseText ?
          <div className={styles.my_comments_content}>
            <span className={styles.my_comments_name}>{userInfoData.name}</span>
            <div
              className={styles.my_comments_text}
              dangerouslySetInnerHTML={{ __html: parseText(releaseText,18) }}
            ></div>
          </div> : null
        }
        {
          showComments && (isForward != 1 || (isForward == 1 && !!forwardDto)) ?
          <div className={styles.comments_input_content}>
            <div
              className={styles.left_picture}
              style={ userInfoData && userInfoData.headUrl ? {backgroundImage: `url(${userInfoData.headUrl})`} : {background: randomColor(item.friUserId)}}
              // style={{background: randomColor(userInfoData.friUserId)}}
            >  {userInfoData && userInfoData.headUrl ? null : processNames(userInfoData.name)}</div>
            <div className={styles.comments_input} onClick={addComment}>添加评论...</div>
          </div> : null
        }
      </> : null
    }


    {/* 输入框蒙层 */}
    <Mask
      visible={!!isShowComment}
      opacity={0}
      getContainer={document.body}
      onMaskClick={closeMask}
      style={{
        '--z-index': 997
      }}
    />
    {
      isShowComment ?
        <div className={styles.fixed_footer_wrap}>
          <div>
            <div className={styles.input_wrap}>
              <div className={styles.textarea_wrap}>
                <TextArea
                  value={textAreaVal}
                  placeholder="添加评论"
                  autoSize={{maxRows: 6, minRows: 1}}
                  rows={1}
                  autoFocus
                  onChange={onChangeInput}
                />
                <i
                  className={styles.textarea_Icon}
                  onClick={(e)=>{
                    e.stopPropagation()
                    e.preventDefault()
                    setIsShowEmoji(!isShowEmoji)
                  }}
                ></i>
              </div>
              <div className={styles.btn_wrap}>
                {/*<i></i>*/}
                <span onClick={onReleaseBtn}>发布</span>
              </div>
            </div>
            {!!isShowEmoji &&
              <Emoji itemOnClick={itemOnClick}/>
            }
          </div>
        </div>
        : null
    }

  </>
};

export default connect(({ userInfoStore,recommended, loading }: any) => ({
  userInfoStore,
  recommended,
  loading
}))(Index);
