/**
 * @Description: 选择空间类型
 */
import React, { useEffect, useState } from 'react';
import { connect } from 'umi';
import styles from './index.less';
import GoBackIcon from '@/assets/GlobalImg/go_back.png'; // 返回图片
import checkIcon from '@/assets/GlobalImg/check_icon.png'; // 勾选小图标
import cover1 from '@/assets/PlanetChatRoom/CreateSpace_cover_1.png'; // 预约封面1
import cover2 from '@/assets/PlanetChatRoom/CreateSpace_cover_2.png'; // 预约封面1
import cover3 from '@/assets/PlanetChatRoom/CreateSpace_cover_3.png'; // 预约封面1
import cover4 from '@/assets/PlanetChatRoom/CreateSpace_cover_4.png';
import classNames from 'classnames'; // 预约封面1
import { PlusOutlined } from '@ant-design/icons';
import { message, Spin, Upload } from 'antd';
import { stringify } from 'qs';
import { getOperatingEnv } from '@/utils/utils';
import { history } from '@@/core/history';

const Index: React.FC = (props: any) => {
  const { goBack, userInfoStore, dispatch } = props || {};
  const { spaceTypeId, spaceCoverUrlView, spaceCoverUrl, starSpaceType,spaceName } = userInfoStore || {};
  const [spaceCoverList, setSpaceCoverList] = useState([]); // 勾选中的id
  const [loadingByUpload, setLoadingByUpload] = useState(false); // 空间封面上传loading
  const [checkedId, setCheckedId] = useState(spaceTypeId); // 勾选中的id
  // 自定义上传-空间封面数据
  const [spaceCoverState, setSpaceCoverState] = useState({
    spaceCoverUrl: null,
    spaceCoverUrlView: null,
  });
  const UerInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');

  // 类型：1 直播，2 会议
  let starSpaceTypeText= starSpaceType == 2 ? '会议' : '直播'

  useEffect(() => {
    getSpaceCover();
  }, []);

  // 勾选事件
  const checkedFn = (item: any) => {
    setCheckedId(item);
    /*dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        spaceTypeId: item?.id, // 空间类型 1专家讲课 2指导 3复杂病例讨论
        spaceTypeName: item?.text // 空间选择的类型名称
      }
    })*/
    // 勾选完成后直接返回上一页, 并反显内容
    // goBack(1)
  };

  // 获取直播、会议封面信息
  const getSpaceCover = async () => {
    let resByGetSpaceCover = await dispatch({
      type: 'userInfoStore/getSpaceCover',
      payload: {
        spaceId: null, // 空间ID
        starSpaceType: starSpaceType, // 封面类型 1直播、2会议
      },
    });
    const { code, content } = resByGetSpaceCover || {};
    if (code == 200 && content) {
      if (Array.isArray(content)) {
        let isSelectByItem = content.find((item) => {
          return item.isSelect == 1;
        });
        setSpaceCoverList(content);
        setCheckedId(spaceCoverUrl && spaceCoverUrl.fileUrl ? spaceCoverUrl.fileUrl : null);
      } else {
        setSpaceCoverList([]);
      }
    }
  };

  // 上传空间封面、空间广告
  const onChangeByUpload = (info: any) => {
    let fileList;
    fileList = info && info.fileList;
    if (info.file.status === 'uploading') {
      setLoadingByUpload(true);
      return;
    }

    // 判断当前上传已被拦截
    if (info && !info.file.status) {
      setLoadingByUpload(false);
      fileList = null;
      return;
    }
    if (info && info.file.status === 'error') {
      setLoadingByUpload(false);
      message.error('上传失败');
      fileList = null;
      return;
    }
    // 上传结束
    if (info && info.file.status === 'done') {
      setLoadingByUpload(false);
      const res = info.file.response || {};
      const { code, content, msg } = res;
      if (code == 200 && content) {
        console.log('content123123 :: ', content);
        // 封面
        setSpaceCoverState({
          ...spaceCoverState,
          spaceCoverUrl: content.fileUrl,
          spaceCoverUrlView: content.fileUrlView,
        });
      } else {
        fileList = [];
        message.error(msg || '上传失败');
      }
    }
  };

  // 上传图片headers
  const getHeaders = () => {
    // 获取环境类型，1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用   5 FRIDAY app中使用
    const env = getOperatingEnv();
    // FRIDAY APP中本地存储值：access_token、client、phone、user_name、wxuserId、userInfo
    return {
      // token
      access_token: localStorage.getItem('access_token') || '',
      username:
        env == 5
          ? localStorage.getItem('user_name')
          : localStorage.getItem('vxOpenIdCipherText')
          ? localStorage.getItem('vxOpenIdCipherText')
          : UerInfo?.phone,
      client: env == 5 ? localStorage.getItem('client') : 'WX',
      type: env === '1' ? '' : '1', // h5 传1
    };
  };

  // 上传校验规则
  const beforeUpload = (file: { size?: any; type?: any }) => {
    const isSize = file.size / 1024 / 1024 < 15;
    if (!isSize) {
      message.error('超过15M限制，不允许上传~');
      return false;
    }

    const { name: fileName } = file || {};
    // 添加对文件后缀名的限制
    const suffix = fileName.substring(fileName.lastIndexOf('.') + 1);
    const isJpgOrPng =
      file.type === 'image/jpg' ||
      file.type === 'image/jpeg' ||
      file.type === 'image/png' ||
      file.type === 'image/gif';
    // 文件后缀名可以大写,所以需要添加大写后缀名的判断
    const isSuffixByJpgOrPng =
      suffix === 'jpg' ||
      suffix === 'JPG' ||
      suffix === 'jpeg' ||
      suffix === 'JPEG' ||
      suffix === 'png' ||
      suffix === 'PNG' ||
      suffix === 'gif' ||
      suffix === 'GIF';
    if (!isJpgOrPng || !isSuffixByJpgOrPng) {
      message.error('只能上传JPG、JPEG、PNG、GIF 格式的图片~');
      return false;
    }
    return isJpgOrPng;
  };

  return (
    <div className={styles.container}>
      <div className={styles.title_box}>
        <div className={styles.title_btn} onClick={() => goBack(1)}>
          <img src={GoBackIcon} width={12} height={24} alt="" />
        </div>
        <div className={styles.title}>请选择一个{starSpaceTypeText}封面</div>
      </div>
      <Spin spinning={!!props.loading.effects['userInfoStore/getSpaceCover']}>
        <div className={styles.wrap}>
          <div className={styles.warp_list}>
            {Array.isArray(spaceCoverList) &&
              spaceCoverList.map((item, index) => {
                const { isSelect, showSpaceCoverUrl, spaceCoverUrl } = item || {};
                return (
                  <div
                    key={index}
                    onClick={() => {
                      checkedFn(spaceCoverUrl);
                    }}
                    className={classNames({
                      [styles.warp_list_item]: true,
                      [styles.warp_list_item_select]: spaceCoverUrl == checkedId,
                    })}
                  >
                    {!!spaceName && <div className={styles.CoverTitle}>{spaceName}</div>}
                    <img src={showSpaceCoverUrl} alt="" />
                    {spaceCoverUrl == checkedId &&  <i className={styles.warp_list_item_select_icon}/>}
                  </div>
                );
              })}
            <Upload
              headers={getHeaders()}
              accept="image/*"
              action={`/api/server/base/uploadFile?${stringify({
                fileType: 14,
                userId: UerInfo?.friUserId,
              })}`}
              // listType="picture-card"
              // className={styles.edit_head_picture}
              onChange={(info) => onChangeByUpload(info)}
              onRemove={() => {}}
              beforeUpload={beforeUpload}
              showUploadList={false}
            >
              <Spin spinning={!!loadingByUpload}>
                <div
                  onClick={() => {
                    checkedFn('locality');
                  }}
                  className={classNames({
                    [styles.warp_list_item]: true,
                    [styles.warp_list_item_select]: 'locality' == checkedId,
                  })}
                >
                  {spaceCoverState.spaceCoverUrlView ? (
                    <div className={styles.warp_list_item_box_img_warp}>
                      <img src={spaceCoverState.spaceCoverUrlView} alt="" />
                    </div>
                  ) : (
                    <div className={styles.warp_list_item_box}>
                      <div className={styles.addiconBox}>
                        <div className={styles.addicon}>
                          <PlusOutlined />
                        </div>
                        <div>本地上传</div>
                      </div>
                    </div>
                  )}
                </div>
              </Spin>
            </Upload>
          </div>
        </div>
      </Spin>
      <div className={styles.enter_warp}>
        <div
          onClick={() => {
            // 使用选择的封面
            if (checkedId != 'locality') {
              let findByitem =
                Array.isArray(spaceCoverList) &&
                spaceCoverList.find((item, index) => {
                  return item.spaceCoverUrl == checkedId;
                });
              dispatch({
                type: 'userInfoStore/setTaskListState',
                payload: {
                  createModalVisible: false,
                  spaceCoverUrl: findByitem
                    ? {
                        fileUrlView: findByitem.showSpaceCoverUrl,
                        fileUrl: findByitem.spaceCoverUrl,
                      }
                    : null,
                  spaceCoverUrlView: findByitem && findByitem.showSpaceCoverUrl,
                },
              });
            } else {
              // 使用自定义封面进入裁剪页面
              if (spaceCoverState && spaceCoverState.spaceCoverUrlView) {
                dispatch({
                  type: 'userInfoStore/setTaskListState',
                  payload: {
                    createModalVisible: false,
                    spaceCoverUrl: spaceCoverState
                      ? {
                          fileUrlView: spaceCoverState.spaceCoverUrlView,
                          fileUrl: spaceCoverState.spaceCoverUrl,
                        }
                      : null,
                    spaceCoverUrlView: spaceCoverState.spaceCoverUrlView,
                  },
                });
                dispatch({
                  type: 'userInfoStore/setTaskListState',
                  payload: { openCropperImgModal: true },
                });
                return;
              }
            }
          }}
          className={styles.enter_btn}
        >
          确定
        </div>
      </div>
    </div>
  );
};

export default connect(({ userInfoStore, loading }: any) => ({ userInfoStore, loading }))(Index);
