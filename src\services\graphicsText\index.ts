import request from '@/utils/request'

// https://yapi.jwsmed.com/project/238/interface/api
/**
 * H5创建图文信息 赵志君
 * @params ss                    remark
 */
export async function editImgTextInfo(params) {
  return request('/api/server/imageTextInfoH5/editImgTextInfo', {
    method: 'POST',
    data: params,
  })
}

/**
 * 关联话题列表 赵志君
 * @params topicName                    话题搜索关键字
 */
export async function getRelationList(params) {
  return request('/api/server/h5ImageTopic/getRelationList', {
    method: 'GET',
    params,
  })
}

/**
 * 移动端获取王国列表 赵志君
 * @params kingdomName                    王国搜索关键字
 */
export async function getCreateKingdomList(params) {
  return request('/api/server/kingdom/getCreateKingdomList', {
    method: 'GET',
    params,
  })
}

/**
 * 图文内容视频上传获取OSS参数 赵志君
 * @params kingdomName                    王国搜索关键字
 */
export async function ossParameter(params) {
  return request('/api/server/base/ossParameter', {
    method: 'GET',
    params,
  })
}

/**
 * 编辑图文获取原数据 赵志君
 * @params imageTextId                    图文ID
 */
export async function imgTextInfoUpdateId(params) {
  return request('/api/server/imageTextInfoH5/imgTextInfoUpdateId', {
    method: 'GET',
    params,
  })
}

/**
 * 图文详情 赵志君
 * @params imageTextId                    图文ID
 */
export async function imgTextInfoById(params) {
  return request('/api/server/imageTextInfoH5/imgTextInfoById', {
    method: 'GET',
    params,
  })
}

/**
 * 个人中心获取用户图文列表信息 赵志君
 * @params ss                    remark
 */
export async function personImageTextList(params) {
  console.log(params)
  return request('/api/server/imageTextInfoPc/personImageTextList', {
    method: 'POST',
    data: params.postParams,
    params: params.getParams,
  })
}

/**
 * 草稿图文删除 赵志君
 * @params imageTextId                    图文ID
 */
export async function deleteImgTextInfo(params) {
  return request('/api/server/imageTextInfoPc/deleteImgTextInfo', {
    method: 'GET',
    params,
  })
}

/**
 * PC图文下架 赵志君
 * @params imageTextId                    图文ID
 */
export async function lowUpFrameImgTextInfo(params) {
  return request('/api/server/imageTextInfoH5/lowUpFrameImgTextInfo', {
    method: 'GET',
    params,
  })
}

/**
 * 图文内容视频上传获取OSS参数 曹萌萌
 * @params imageTextId                    图文ID
 */
export async function signaturecom(params) {
  return request('/api/server/base/oss/signaturecom', {
    method: 'GET',
    params,
  })
}

/**
 * 空间内部分享到广场 赵志君
 * @params spaceId                     空间ID
 */
export async function insideShareStarSpace(params) {
  return request('/server/space/inside_share_star_space', {
    method: 'GET',
    params,
  })
}
