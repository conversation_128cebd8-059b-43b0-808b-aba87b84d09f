.pc_wrap {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #EEF3F9;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.content_box {
  width: 1228px;
  max-width: 1228px;
  margin-top: 16px;
  height: calc(100vh - 100px);
}

.header_content{
  padding: 24px;
  width: 100%;
  height: 180px;
  border-radius: 6px;
  background: url('https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/Expert_pc_bg.png') no-repeat;
  background-size: 100% 100%;
  display: flex;

  .header_left{
    margin-right: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .head_sculpture {
      width: 72px;
      height: 72px;
      border-radius: 50%;
      border: 3px solid #FFFFFF;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 12px;

      img {
        width: 72px;
        height: 72px;
      }
    }
    .no_head_sculpture {
      width: 72px;
      height: 72px;
      border-radius: 50%;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      font-weight: 500;
      color: #FFFFFF;
      margin-bottom: 12px;
    }

    .header_follow{
      cursor: pointer;
      width: 60px;
      height: 28px;
      background: #EDF9FF;
      border-radius: 14px 14px 14px 14px;
      opacity: 1;
      display: flex;
      align-items: center;
      padding: 0 8px;
      .follow_icon{
        width: 16px;
        height: 16px;
      }
      .follow_bule{
        font-size: 14px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #0095FF;
      }
    }
    .header_not_follow{
      cursor: pointer;
      width: 88px;
      height: 28px;
      background: #F5F5F5;
      border-radius: 14px 14px 14px 14px;
      opacity: 1;
      display: flex;
      align-items: center;
      padding: 0 8px;
      .follow_not_icon{
        width: 16px;
        height: 16px;
      }
      .follow_not_bule{
        font-size: 14px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #CCCCCC;
      }
    }
  }
  .header_center{
    flex: 1;
    overflow: hidden;
    .header_name{
      margin-bottom: 8px;
      .name{
        font-size: 24px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #000000;
        line-height: 28px;
      }
      .rank{
        font-size: 14px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
        line-height: 16px;
        margin: 0 8px;
      }
      .grade{
        padding: 2px 4px;
        height: 21px;
        background: #EEFFF9;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        border: 1px solid #B0EAD9;
        font-size: 12px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #06A777;
      }
    }

    .header_clinic{
      font-size: 14px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 16px;
    }
    .header_gdp{
      margin: 4px 0 6px;
      .gdp{
        margin-right: 8px;
        font-size: 14px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
        line-height: 16px;
        p{
          line-height: 20px;
        }
        :global(.ql-align-center){
          text-align: center;
        }
      }
    }
    .header_desc{
      overflow: hidden;

      .brief_introduction_show {
        display: flex;
        flex-direction: column;
        font-size: 14px;
        font-weight: 400;
        color: #666666;
        line-height: 20px;

        .text {
          word-break: break-all;
        }
      }

      .brief_introduction_hide {
        display: block;
        font-size: 14px;
        font-weight: 400;
        color: #666666;
        line-height: 20px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        -webkit-line-clamp: 2;
        text-overflow: ellipsis;

        .text {
          word-break: break-all;
        }
      }
    }
  }
  .header_right{
    cursor: pointer;
    width: 30px;
    font-size: 15px;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #0095FF;
    flex-shrink: 0;
  }
}

.content {
  width: 100%;
  height: calc(100vh - 314px);
  overflow: hidden;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  margin-top: 16px;
}

.tab_wrap {
  width: 100%;
  height: 43px;
  background: #FFFFFF;
  border-radius: 8px;
  padding: 16px 20px 0;
  margin-bottom: 12px;
  display: flex;

  .tab_init {
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    line-height: 16px;
    margin-right: 16px;
    height: 27px;
    cursor: pointer;

    &.tab_active {
      font-size: 14px;
      font-weight: 600;
      color: #000000;
      line-height: 16px;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 12px;
        height: 3px;
        background: #000000;
        border-radius: 6px 6px 6px 6px;
      }
    }
  }

  .lines {
    width: 1px;
    height: 16px;
    background: #D9D9D9;
    margin-right: 22px;
  }
}

.tab_content {
  width: 100%;
  height: calc(100% - 55px);

  :global {
    .ant-spin-container {
      width: 100%;
      height: 100%;
    }
  }
}

.tab_content_list {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding: 20px 0;
  // display: flex;
  // flex-wrap: wrap;
  background: #fff;
  border-radius: 8px 8px 8px 8px;
  .tab_spaceRoleType_list{
    padding-left: 20px;
    border-bottom: 1px solid #E9EEF2;
    span{
      display: inline-block;
      font-size: 14px;
      color: #666666;
      line-height: 14px;
      padding-bottom: 12px;
      margin-right: 20px;
      cursor: pointer;
    }
    .spaceRoleTypeActive{
      color: #0095FF;
    }
  }
  .tab_spaceStatus_list{
    padding: 20px;
    position: relative;
    >span{
      display: inline-block;
      font-size: 14px;
      color: #666666;
      background: #F3F3F3;
      line-height: 14px;
      padding: 6px 12px;
      border-radius: 14px;
      margin-right: 16px;
      cursor: pointer;
    }
    .spaceStatusActive{
      color: #0095FF;
      background: #E1F1FE;
    }
    i{
      position: absolute;
      right:20px;
      top: 20px;
      font-style: normal;
      cursor: pointer;
      .screen_icon{
        width: 18px;
        height: 18px;
        margin-right: 4px;
        cursor: pointer;
        vertical-align:text-bottom;
      }
    }
    .screen_btnActive{
      color: #0095FF;
    }
  }
  .scroll_box {
    width: 100%;
    display: flex;
    flex-wrap: wrap;

    .no_data_wrap {
      width: 100%;
      display: flex;
      justify-content: center;
    }
  }

  .space_wrap {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 30px;
    padding: 0 10px;
    box-sizing: border-box;

    .space_list {
      width: 20%;
      margin-bottom: 16px;
      padding: 12px;
    }
  }
  .screen_modal{
    :global{
      .ant-modal-header,.ant-modal-body{
        padding: 15px;
      }
      .ant-modal-header{
        padding-bottom:10px;
        font-size: 18px;
        border-bottom:0;
      }
      .ant-modal-body{
        padding-top:5px;
      }
    }
    .screen_box{
      .isBizTitle{
        font-size: 14px;
        font-weight: 600;
        color:#000000;
      }
      .isBizSelectBox{
        margin-top:10px;
        :global{
          .ant-checkbox-group{
            width: auto!important;
            display: inline-block;
            >div{
              display: inline-block;
            }
          }
        }
      }
    }
    .footer{
      text-align: right;
      .btn_primary{
        background: #409EFF;
        border-radius: 4px;
        margin-left: 10px;
      }
    }
  }
  .meeting_wrap{
    width: 100%;
    display: flex;
    flex-direction: column;
    margin-bottom: 30px;
    padding: 0 20px;
    box-sizing: border-box;
    .meeting_list {
      position: relative;
      margin-bottom: 16px;
      padding: 0 0 16px;
      border-bottom: 1px solid #E1E4E7;
      h3 {
        font-size: 16px;
        color: #000;
        font-weight: 600;
        line-height: 22px;
        margin-bottom: 2px;
      }
      h4 {
        font-size: 14px;
        color: #000;
        font-weight: 500;
        line-height: 20px;
        margin-bottom: 8px;
      }
      .item_myMeeting_box{
        position: absolute;
        right: 0;
        bottom: 20px;
        span{
          display: inline-block;
          font-size: 14px;
          padding: 4px 10px;
          cursor: pointer;
        }
        .item_poster_btn{
          color:#000000;
          border:1px solid #CCCCCC;
          margin-right: 16px;
        }
        .item_copy_btn{
          background: #0095FF;
          border:1px solid #0095FF;
          :global {
            .ant-typography {
              margin-bottom: 0;
            }
            .ant-typography-copy {
              color: #FFFFFF;
            }
          }
        }
      }
    }
  }
}
.tab_content_case_box {
  height: 100%;
  overflow-y: auto;
  background: #fff;
}

.tab_content_case {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  // // justify-content: space-between;
  // align-items: flex-start;

  .tab_case_list {
    width: 33.3%;
    padding-right: 16px;
    margin-bottom: 16px;

    &:nth-child(3n) {
      padding-right: 0;
    }
  }
}

.noDataStyle {
  width: 100%!important;
  justify-content: inherit;
}

.ask_experts_btn {
  flex-shrink: 0;
  min-width: 221px;
  height: 40px;
  background: #0095FF;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  cursor: pointer;
  z-index: 700;

  img {
    width: 24px;
    height: 24px;
    margin-right: 4px;
  }

  .price_style_box {
    text-align: center;

    .title_style {
      font-size: 16px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 22px;
    }
  }
}

.qrcode_wrap {

  :global {
    .ant-modal-body {
      text-align: center;
    }
  }

  .expert_headline{
    font-size: 18px;
    font-weight: 500;
    color: #000000;
    line-height: 21px;
    margin-bottom: 20px;
    margin-top: 20px;
  }
  .expert_qr_code{
    width: 180px;
    height: 180px;
    margin: 0 auto 12px;
  }
}

// div::-webkit-scrollbar {
//   width: 4px;
// }
// div::-webkit-scrollbar-thumb {
//   border-radius: 10px;
//   -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
//   opacity: 0.2;
//   // background: ;
// }
// div::-webkit-scrollbar-track {
//   -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
//   border-radius: 0;
//   // background: fade(@primary-color, 30%);
// }

.header_desc_popover {
  :global{
    .ant-popover-inner{
      width: 1100px;
    }
    .ant-popover-inner-content {
      max-width: 1100px;
    }
  }
}

