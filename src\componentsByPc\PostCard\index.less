@import '~@/utils/imageText.less';
.wrap {
  padding: 20px 0 17px;
  border-bottom: 1px solid #ddd;
  .post_title {
    margin-bottom: 9px;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  .cover_img_box {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    margin-bottom: 12px;
    .cover_img_wrap {
      flex-shrink: 0;
      width: 117px;
      height: 90px;
      border-radius: 4px;
      overflow: hidden;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      border: 1px solid #ddd;
      .no_cover_wrap {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #666;
        :global {
          .anticon {
            font-size: 17px;
          }
        }
      }
    }
    .date {
      padding-top: 5px;
      font-size: 14px;
      color: #999;
      line-height: 20px;
    }
  }
  .status_wrap {
    display: flex;
    margin-bottom: 12px;
    .status {
      width: auto;
      height: 21px;
      line-height: 21px;
      padding: 0 8px;
      border-radius: 4px;
      font-size: 12px;
      &.status_0 {
        background: #FFF5E5;
        color: #EAA434;
      }
      &.status_1 {
        background: #F8F8F9;
        color: #999;
      }
      &.status_2 {
        background: rgba(249,101,91,.2);
        color: #dc3545;
      }
    }
  }
  .details_box {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: space-between;
    .details_data {
      font-size: 14px;
      color: #000;
      line-height: 20px;
    }
    .btn_wrap {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      font-size: 16px;
      color: #000;
      line-height: 22px;
      cursor: pointer;
      :global {
        .ant-typography {
          margin-bottom: 0;
        }
        .ant-typography-copy {
          color: #000;
        }
      }

      & > div + div {
        margin-left: 32px;
      }

      .btn_disabled {
        color: #999;
        cursor: not-allowed;
      }
    }
  }
}
