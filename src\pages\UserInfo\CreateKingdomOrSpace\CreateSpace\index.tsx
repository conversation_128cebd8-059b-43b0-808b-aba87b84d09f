/**
 * @Description: 创建开放空间/王国空间
 */
import React, { useState, useCallback, useEffect, useRef } from 'react';
import { connect, history, useRouteMatch } from 'umi';
import { Input, Switch, Toast, TextArea } from 'antd-mobile'
import styles from './index.less';
import goBackIcon from '@/assets/GlobalImg/go_back.png'; // 返回按钮小图标
import rightArrowIcon from '@/assets/GlobalImg/right_arrow.png'; // 右箭头小图标
import frameIcon from '@/assets/GlobalImg/frame.png'; // 视频上传中图标
const reduceIcon = 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/reduce.png'; // 减员小图标
const addIcon = 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/add.png'; // 增员小图标
const redReduceIcon = 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/red_reduce.png'; // 红色删除小图标
const closeEyeIcon = 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/close_eye.png'; // 闭眼小图标
const openEyeIcon = 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/open_eye.png'; // 睁眼小图标
const uploadImg = 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/big_add.png'; // 上传图片图标
import classNames from 'classnames';
import { Form, Spin, Upload, UploadFile } from 'antd';
import { stringify } from 'qs';
import { getOperatingEnv } from '@/utils/utils';
import { UploadChangeParam } from 'antd/lib/upload';
import { processNames, randomColor } from '@/utils/utils';
import TcVod from 'vod-js-sdk-v6';

const Index: React.FC = (props: any) => {
  const {path} = useRouteMatch();
  const { userInfoStore, dispatch, goBack } = props || {};
  const { selectCreateType,creatTabSelectDate, selectedKingdom, selectedCompere, selectedGuest, spaceName, isSuperAccount,isEditSpace, isPasswordSwitchChecked,isAutoRecord,allowApplications } = userInfoStore || {}; // 从仓库中取值
  const { title, type, goBackType, isShowGoBack=false, isSelectKingDom=false } = creatTabSelectDate && creatTabSelectDate || {};
  const { spaceCoverUrl, spaceAdvertisingUrl, spaceVideoUrl, spacePasswordText, createModalVisible, spaceVideoId, spaceVideoName, spaceIntroduceVal, spaceFromEnter } = userInfoStore || {};
  const { spaceTypeName, spaceTypeId, spectatorType, enterpriseText, selectedKingdomAudience, enterpriseUserSelectData } = userInfoStore || {};
  const [passType, setPassType] = useState(true); // 密码是否可见
  const [isSwitch, setIsSwitch] = useState((spacePasswordText || isPasswordSwitchChecked) ? true : false); // 设置密码开关
  const [password, setPassword] = useState(spacePasswordText || ''); // 密码
  const [minusIconVisible, setMinusIconVisible] = useState(false); // 设置删除嘉宾开关
  const [spaceInputVal, setSpaceInputVal] = useState(spaceName || ''); // 空间名称
  const [loadingByUpload, setLoadingByUpload] = useState(false);  // 空间封面上传loading
  const [loadingByAdverUpload, setLoadingByAdverUpload] = useState(false);  // 空间广告上传loading
  const [uploadVideoLoading, setUploadVideoLoading] = useState(false);  // 空间视频上传loading
  const [uploadProgress, setUploadProgress] = useState(0); // 视频上传进度
  const [form] = Form.useForm();
  const contentRef = useRef(null);
  const UerInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  const createModalVisibleRef = useRef(createModalVisible); // 弹框是否打开或关闭

  // 记录是否是在当前页面,返回和关联王国返回时，需要设置为false，为了解决上传视频过程中视频不取消问题
  let openModalKeyRef = useRef(true);
  let CreateSpaceText =  selectCreateType == 14 ? '会议' : '直播';
  let isMeeting = selectCreateType == 14 // 判定是否是创建会议

  // 弹框关闭后，重置表单项
  useEffect(() => {
    createModalVisibleRef.current = createModalVisible;
    if (!createModalVisible) {
      form.resetFields()
      setPassType(true)
      setIsSwitch(false)
      setPassword('')
      setSpaceInputVal('')
      setMinusIconVisible(false)
      setUploadVideoLoading(false)
      setUploadProgress(0)
      contentRef.current.scrollTop = 0
      openModalKeyRef.current = false;
      dispatch({
        type: 'userInfoStore/setTaskListState',
        payload: {
          isEditSpace: false
        }
      })
    }
  },[createModalVisible, dispatch, form])

  // 初始化form表单
  useEffect(() => {
    let formObject: Record<string,any> = {
      spaceName: spaceName || '',
      spacePassword: spacePasswordText || '',
      spaceIntroduce: spaceIntroduceVal || '',
    }
    if (isEditSpace) {
      formObject = {
        spaceName: spaceName || '',
        spacePassword: spacePasswordText || '',
        spaceIntroduce: spaceIntroduceVal,
      }
    }
    form.setFieldsValue(formObject)

  }, [form, isEditSpace, spaceIntroduceVal, spaceName, spacePasswordText])

  // 返回，清除数据
  const goBackHandle = useCallback(() => {
    openModalKeyRef.current = false;
    dispatch({
      type: 'userInfoStore/clean',
      payload: {
        spaceCoverUrl: null, // 空间封面路径
        spaceAdvertisingUrl: null, // 空间广告路径
        spacePasswordText: null, // 密码
        spaceName: null, // 空间名称
        selectedKingdom: null, // 已选中王国
        selectedCompere: null, // 已选择主持人
        selectedGuest: [], // 已选择嘉宾
        spaceVideoUrl: null, // 上传的空间视频地址
        spaceVideoId: null, // 视频id
        spaceVideoName: null, // 视频文件名
        spaceIntroduceVal: null, // 空间介绍
        allViewersState: true, // 选择观众默认所有人可见状态
        selectedKingdomAudience: [], // 观众-选择可见王国
        enterpriseUserData: [], // 选择可见企业/品牌用户，页面选择数据
        enterpriseUserTab: 0, // 选择可见企业/品牌用户，页面tab
        spaceTypeId: null, // 空间类型 1专家讲课 2指导 3复杂病例讨论
        spaceTypeName: null, // 空间类型名称
        spectatorType: 0, // 观众类型 0默认所有人 1指定王国成员可见 2指定企业品牌用户可见
        kingdomListArr: [], // 观众-王国列表数据
        enterpriseUserSelectData: [], // 选择可见企业/品牌用户，选择后的数据
        enterpriseText: null, // 选择可见企业/品牌用户，弹框名称
      }
    })
    goBack(goBackType); // 从哪儿来的返回到哪儿
  }, [dispatch, goBack, goBackType]);

  // 空间名称失去焦点事件
  const spaceInputBlurFn = (e: { target: { value: any; }; }) => {
    window.scrollTo(0,0)
    const val = e.target.value;

    if(!val || !val.trim()) {
      form.setFieldValue('spaceName', '')
      setSpaceInputVal('');
      return
    };

    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        spaceName: val && val.trim(), // 空间名称
      }
    })
  }

  // 点击关联王国点击返回
  const jumpHandle = () => {
    openModalKeyRef.current = false;
    // dispatch({
    //   type: 'userInfoStore/clean',
    //   payload: {
    //     spaceCoverUrl: null, // 空间封面路径
    //     spaceAdvertisingUrl: null, // 空间广告路径
    //     spacePasswordText: null, // 密码
    //     spaceName: null, // 空间名称
    //     selectedKingdom: null, // 已选中王国
    //     selectedCompere: null, // 已选择主持人
    //     selectedGuest: [], // 已选择嘉宾
    //     spaceVideoUrl: null, // 上传的空间视频地址
    //     spaceVideoId: null, // 视频id
    //     spaceVideoName: null, // 视频文件名
    //     spaceTypeId: null, // 空间类型 1专家讲课 2指导 3复杂病例讨论
    //     spaceTypeName: null, // 空间类型名称
    //     spectatorType: 0, // 观众类型 0默认所有人 1指定王国成员可见 2指定企业品牌用户可见
    //     allViewersState: true, // 选择观众默认所有人可见状态
    //     kingdomListArr: [], // 观众-王国列表数据
    //     selectedKingdomAudience: [], // 观众-选择可见王国
    //     enterpriseUserData: [], // 选择可见企业/品牌用户，当前企业下的机构数据
    //     enterpriseUserTab: 0, // 选择可见企业/品牌用户，页面tab
    //     enterpriseUserSelectData: [], // 选择可见企业/品牌用户，选择后的数据
    //     enterpriseText: null, // 选择可见企业/品牌用户，弹框名称
    //   }
    // })
    goBack(20); // 从哪儿来的返回到哪儿
  };

  // 删除已选择嘉宾
  const deleteGuest = useCallback((item) => {
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        selectedGuest: selectedGuest.filter((it: { id: any; }) => it.id !== item.id)
      }
    })
  }, [dispatch, selectedGuest]);

  // 马上开始
  const startBtnFn = () => {
    // 图片、视频在上传的时候不可点击，上传完后才可点击
    if(uploadVideoLoading || loadingByUpload || loadingByAdverUpload) return;
    form.validateFields().then(res => {
      Toast.show({icon: 'loading', maskClickable: false})
      // 编辑 进入
      if(isEditSpace) {
        editSubmit()
      } else {
        submitFn()
      }
    }).catch(err => {
      console.log(err, '有没有捕获到哟')
    })
  }

  // 编辑事件
  const editSubmit = () => {
    const { tipText, refreshFn, spaceId, pageType, consultationId } = spaceFromEnter || {};
    const guestIdList = selectedGuest.map((it: { id: any; }) => it.id); // 嘉宾取id

    dispatch({
      type: 'userInfoStore/editSpaceInfo',
      payload: {
        consultationId: pageType == '4' ? consultationId : undefined, // 指导id(创建视频指导时需必传)
        id: spaceId, // 空间ID
        name: spaceInputVal && spaceInputVal.trim(), // 空间名称
        password: password || null, // 密码
        // isAutoRecord: isAutoRecord ? 1 : 0, // 是否自动录制 是否开启自动录播 1:开启  0或其他关闭
        startStatus: 1, // 开始状态：1立刻、2预约
        appointmentStartTime: null, // 预约开始时间
        spaceCoverUrl: spaceCoverUrl && spaceCoverUrl.fileUrl || null, // 空间封面路径
        spaceAdvertisingUrl: spaceAdvertisingUrl && spaceAdvertisingUrl.fileUrl || null, // 空间广告路径
        updateUserId: UerInfo && UerInfo.friUserId, // 操作人用户ID
        guestIdList: guestIdList, // 嘉宾id集合
        vodFileId: spaceVideoId, // 空间视频id
        intro: spaceIntroduceVal, // 空间介绍
        spectatorType: spectatorType, // 观众类型 0默认所有人 1指定王国成员可见 2指定企业品牌用户可见
        spaceType: spaceTypeId || null, // 空间类型 1专家讲课 2指导 3复杂病例讨论
        spectatorOfKingdomList: selectedKingdomAudience, // 指定王国成员时传参
        spectatorOfBizList: enterpriseUserSelectData || null, // 指定企业品牌用户时传参
        starSpaceType: isMeeting ? 2 : 1,               // 类型：1 直播，2 会议
      }
    }).then((res: { code: number; content: any; msg: any}) => {
      if(res && res.code == 200) {
        // 1. 关闭弹框
        // 2. Toast提示
        // 3. 刷新页面
        dispatch({
          type: 'userInfoStore/setTaskListState',
          payload: {
            createModalVisible: false
          }
        });
        // Toast.show({content: tipText})
        if(pageType == 2 || pageType == 4) { // 说明是在空间详情中进行的编辑操作
          return refreshFn && refreshFn();
        } else {// 否则就跳转空间详情页
          if (isMeeting) {
            return history.push(`/PlanetChatRoom/Meet/${spaceId}`);
          } else {
            return history.push(`/PlanetChatRoom/Live/${spaceId}`);
          }
        }
      } else {
        Toast.show({content: res.msg || '数据加载失败'})
        return;
      }
    }).catch((err: any) => {
      console.log(err)
    })
  }

  // 创建空间事件
  const submitFn = () => {
    const { tipText, refreshFn, pageType, consultationId } = spaceFromEnter || {};
    const guestIdList = selectedGuest.map((it: { id: any; }) => it.id); // 嘉宾取id
    console.log(UerInfo, selectedKingdomAudience, '选择的王国成员么？？', spaceTypeId, `${CreateSpaceText}类型Id`,  enterpriseUserSelectData, '勾选的企业')
    dispatch({
      type: 'userInfoStore/addSpace',
      payload: {
        consultationId: pageType == '4' ? consultationId : undefined, // 指导id，从指导创建的必传
        name: spaceInputVal && spaceInputVal.trim(), // 空间名称
        wxUserId: selectedCompere && selectedCompere.id || UerInfo && UerInfo.friUserId, // 主持人id
        hostName: selectedCompere && selectedCompere.name || UerInfo && UerInfo.name, // 主持人名称
        kingdomId: selectedKingdom && selectedKingdom.id || null, // 王国id
        kingdomName: selectedKingdom && selectedKingdom.name || null, // 王国名称
        password: password || null, // 密码
        isAutoRecord: isAutoRecord ? 1 : 0, // 是否自动录制 是否开启自动录播 1:开启  0或其他关闭
        startStatus: 1, // 开始状态：1立刻、2预约
        appointmentStartTime: null, // 预约开始时间
        spaceCoverUrl: spaceCoverUrl && spaceCoverUrl.fileUrl || null, // 空间封面路径
        spaceAdvertisingUrl: spaceAdvertisingUrl && spaceAdvertisingUrl.fileUrl || null, // 空间广告路径
        updateUserId: UerInfo && UerInfo.friUserId, // 操作人用户ID
        guestIdList: guestIdList, // 嘉宾id集合
        vodFileId: spaceVideoId, // 空间视频id
        intro: spaceIntroduceVal, // 空间介绍
        spectatorType: spectatorType, // 观众类型 0默认所有人 1指定王国成员可见 2指定企业品牌用户可见
        spaceType: spaceTypeId || null, // 空间类型 1专家讲课 2指导 3复杂病例讨论
        spectatorOfKingdomList: selectedKingdomAudience, // 指定王国成员时传参
        spectatorOfBizList: enterpriseUserSelectData || null, // 指定企业品牌用户时传参
        starSpaceType: isMeeting ? 2 : 1,               // 类型：1 直播，2 会议
        isNoPasswordApply:  allowApplications ? 1 : 0,  // 是否开启无密码申请：0不开启，1开启
      }
    }).then((res: { code: number; content: any; msg: any }) => {
      if(res && res.code == 200) {
        if (pageType == '4') {
          // 关闭弹框
          dispatch({
            type: 'userInfoStore/setTaskListState',
            payload: {
              createModalVisible: false
            }
          });
          refreshFn && refreshFn();
          return
        }
        // 是在王国详情中创建的王国空间
        if(spaceFromEnter?.isJumpOrRefresh) {
          // 关闭弹框
          dispatch({
            type: 'userInfoStore/setTaskListState',
            payload: {
              createModalVisible: false
            }
          });
          // 创建成功，直接跳转空间详情
          if (isMeeting) {
            history.push(`/PlanetChatRoom/Meet/${res.content}`)
          } else {
            history.push(`/PlanetChatRoom/Live/${res.content}`)
          }
        } else {
          // 关闭弹框并跳转创建空间成功页
          dispatch({
            type: 'userInfoStore/setTaskListState',
            payload: {
              createModalVisible: false
            }
          });
          // 判断如果是成功页，则使用replace，成功页只展示一次，不允许返回
          let historyJump = history.push
          if (path.endsWith('KingDomSuccessPage')) {
            historyJump = history.replace
          }
          historyJump({
            pathname: '/UserInfo/SpaceSuccessPage',
            query: {
              id: res.content,
              starSpaceType: isMeeting ? 2 : 1,
            }
          });
        }
      } else {
        Toast.show({content: res.msg || '数据加载失败'})
        return;
      }
    }).catch((err: any) => {
      console.log(err)
    })
  }

  // 预约开始
  const appointmentStartBtnFn = () => {
    // 图片、视频在上传的时候不可点击，上传完后才可点击
    if(uploadVideoLoading || loadingByUpload || loadingByAdverUpload) return;
    form.validateFields().then(res => {
      // 判断观众是否为企业用户，并且空间类型是否为空
      if(spectatorType === 2) {
        if(spaceTypeName) {
          goBack(10)
        } else {
          Toast.show(`${CreateSpaceText}类型不能为空~`)
        }
        return;
      }

      goBack(10)
    }).catch(err => {
      console.log(err)
    })
  }

  // 上传图片headers
  const getHeaders=() =>{
    // 获取环境类型，1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用   5 FRIDAY app中使用
    const env = getOperatingEnv()

    // FRIDAY APP中本地存储值：access_token、client、phone、user_name、wxuserId、userInfo
    return {// token
      access_token:localStorage.getItem('access_token') || '',
      username: env == 5 ? localStorage.getItem('user_name') : localStorage.getItem('vxOpenIdCipherText') ? localStorage.getItem('vxOpenIdCipherText')  : UerInfo?.phone,
      client: env == 5 ? localStorage.getItem('client') : 'WX',
      type: env === '1' ? '' : '1', // h5 传1
    }
  }

  // 赋值到仓库中保存 file文件
  const uploadSetFileState = (file: any, type: string) => {
    if(type == 'COVER') { // 封面
      dispatch({
        type: 'userInfoStore/setTaskListState',
        payload: {
          spaceCoverUrl: file
        }
      })
    } else { // 广告
      dispatch({
        type: 'userInfoStore/setTaskListState',
        payload: {
          spaceAdvertisingUrl: file
        }
      })
    }
  }

  // 上传事件
	const onChangeByUpload = (info: UploadChangeParam<UploadFile<any>>, type: string)=>{
    let fileList;
    fileList = info && info.fileList;
    if (info.file.status === 'uploading') {
      if(type == 'COVER') {
        setLoadingByUpload(true);
      }else {
        setLoadingByAdverUpload(true);
      }
      return;
    }

    // 判断当前上传已被拦截
    if (info && !info.file.status) { fileList = null;return;}
    if (info && info.file.status === 'error') {
      if(type == 'COVER') {
        setLoadingByUpload(false);
      }else {
        setLoadingByAdverUpload(false);
      }
      Toast.show({content: '上传失败'});
      fileList = null;
      return
    }
    // 上传结束
    if (info && info.file.status === 'done') {
      if(type == 'COVER') {
        setLoadingByUpload(false);
      }else {
        setLoadingByAdverUpload(false);
      }
      if(info && info.file.response && info.file.response.code != 200) {
        Toast.show({content: info.file.response.msg ? info.file.response.msg : '上传失败'});
        fileList = [];
        return
      }
    }

    if (info.file.type === "image/png" || info.file.type === "image/jpeg" || info.file.type === "image/gif") {
      if(info.file.response && info.file.response.code== 200 && info.file.response.content) {
        uploadSetFileState(info.file.response.content, type)
      }
    }
  }

	// 上传校验规则
	const beforeUpload = (file: { size?: any; type?: any; }) => {
    const isSize = file.size / 1024 / 1024 < 15;
    if (!isSize) {
      Toast.show({content: '超过15M限制，不允许上传~'});
      return false;
    }

    const { name:fileName } = file || {}
    // 添加对文件后缀名的限制
    const suffix = fileName.substring(fileName.lastIndexOf('.')+1)
    const isJpgOrPng = file.type === 'image/jpg' || file.type === 'image/jpeg' ||  file.type === 'image/png' ||  file.type === 'image/gif';
    // 文件后缀名可以大写,所以需要添加大写后缀名的判断
    const isSuffixByJpgOrPng = (
      suffix === 'jpg'
      || suffix === 'JPG'
      || suffix === 'jpeg'
      || suffix === 'JPEG'
      || suffix === 'png'
      || suffix === 'PNG'
      || suffix === 'gif'
      || suffix === 'GIF'
    )
    if (!isJpgOrPng || !isSuffixByJpgOrPng) {
      Toast.show({content: '只能上传JPG、JPEG、PNG、GIF 格式的图片~'});
      return false;
    }
    return isJpgOrPng;
  };

  // 设置密码开关
  const passwordSwitchChangeFn = (val: boolean | ((prevState: boolean) => boolean)) => {
    // 按钮关闭，清空仓库中保存的密码
    if(!val) {
      dispatch({
        type: 'userInfoStore/setTaskListState',
        payload: {
          spacePasswordText: null
        }
      })
      form.setFieldValue('spacePassword', '')
      setPassword('')
    }
    setIsSwitch(val)
  }

  // 设置密码失去焦点
  const passwordBlur = (e: { target: { value: any; }; }) => {
    window.scrollTo(0,0)
    const val = e.target.value;
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        spacePasswordText: val
      }
    })
  }

  // 获取空间视频签名
  const getSignature: () => Promise<string> = () => {
    return new Promise((resolve,reject) => {
      dispatch({
        type: 'userInfoStore/getVodUploadSign'
      }).then(res => {
        if (res && res.code === 200) {
          resolve(res.content);
        } else {
          reject()
        }
      }).catch(reject)
    })
  }

  // 空间视频上传事件
  const videoFileChange = (e:any) => {
    if (!e.target.files || !e.target.files[0]) return;
    const file = e.target.files[0];
    const fileType = file.type == 'video/mp4'; // 视频格式
    const oversize = file.size / 1024 / 1024 / 1024 > 2; // 视频大小

    // 判断上传视频是否大于2G或者上传文件是否为mp4格式的，不是则提示
    if (oversize || !fileType) {
      Toast.show({duration: 2000, content: <div>请上传格式为MP4，<br/>大小2G以内的视频</div>});
      e.target.value = ''; // 解决同一个视频重复上传后，导致不再提示
      return;
    }

    // 重置状态
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        spaceVideoUrl: '', // 上传视频后返回的路径
        spaceVideoId: '', // 上传视频成功后返回的id
        spaceVideoName: '', // 视频文件名字
      }
    })

    // 上传签名
    const tcVod = new TcVod({
      getSignature: getSignature // 获取上传签名的函数
    })
    setUploadProgress(0); // 重置进度
    setUploadVideoLoading(true); // 打开loading

    // 上传视频文件
    const uploader = tcVod.upload({
      mediaFile: file, // 媒体文件（视频或音频或图片），类型为 File
    })

    // 获取上传视频进度
    uploader.addListener('media_progress', (info) => {
      // 如果弹框关闭或者返回上一个页面时，关闭上传动作
      if (!createModalVisibleRef.current || !openModalKeyRef.current) {
        uploader.cancel()
        return;
      }
      setUploadProgress(Math.round(info.percent * 100))
    })

    // 上传结束
    uploader.done().then((result) => {
      setUploadVideoLoading(false)
      // 如果弹框关闭或返回上一个页面时，则静默，不赋值任何内容，放弃本次上传的东西
      if (!createModalVisibleRef.current || !openModalKeyRef.current) {
        return;
      }
      dispatch({
        type: 'userInfoStore/setTaskListState',
        payload: {
          spaceVideoUrl: result.video.url,
          spaceVideoId: result.fileId,
          spaceVideoName: file.name,
        }
      })
      setUploadVideoLoading(false)
    }).catch((err) => {
      setUploadVideoLoading(false)
      // 如果弹框关闭或返回上一个页面时，则静默，不赋值任何内容，放弃本次上传的东西
      if (!createModalVisibleRef.current || openModalKeyRef.current) {
        return;
      }
      Toast.show({content: err})
    })
    e.target.value = ''; // 解决同一个视频重复上传时不能上传问题
  }

  // 获取视频文件名称，截取后缀名前面的部分内容，以便文件名字过长时，做隐藏处理
  const videoNameModify = ()=> {
    return spaceVideoName && spaceVideoName.substring(0, spaceVideoName.lastIndexOf('.'));
  }

  // 空间介绍失去焦点事件
  const spaceTextAreaBlurFn = (e: any) => {
    window.scrollTo(0,0)
    const val = e && e.target.value;
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        spaceIntroduceVal: val && val.trim()
      }
    })
    form.setFieldValue('spaceIntroduce', val && val.trim())
  }

  // 是否开启自动录制
  const onChangeIsAutoRecord = (val)=>{
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: { isAutoRecord: val }
    })
  }

  // 是否允许无密码可申请进入空间
  const onChangeIsAllowApplications = (val)=>{
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: { allowApplications: val }
    })
  }

  const kingdomInfoData = JSON.parse(localStorage.getItem('kingdomInfoData') || '{}'); // 从创建王国成功页保存，防止刷新后王国相关数据丢失
  const isSuperAccountLocal = JSON.parse(localStorage.getItem('isSuperAccount') || 'false'); // 是否为超级账号 true是 false不是（默认false）
  const { isCureentKingdom } = spaceFromEnter || {};
  return (
    <div className={styles.wrap}>
      <div className={styles.titleInfo}>
        <div className={styles.titleWarp}>
          {isShowGoBack ? '' : <div className={styles.titleBackIcon} onClick={goBackHandle}><img src={goBackIcon} alt="" /></div>}
          <div className={styles.titleText}>{title}</div>
        </div>
      </div>
      <Form form={form} validateTrigger="onBlur">
        <div className={styles.content} ref={contentRef}>
          <div className={styles.space_wrap}>
            {
              (!isEditSpace && isSuperAccount) || (isSuperAccountLocal && !isEditSpace) ?
              <div className={styles.space_form_box}>
                <div className={styles.space_form_title}>主持人</div>
                <div className={styles.space_form_content} onClick={() => {if(uploadVideoLoading || loadingByUpload || loadingByAdverUpload) return; goBack(4)}}>
                  <div className={classNames({
                      [styles.space_input_init_text]: true,
                      [styles.selected]: !!selectedCompere
                    })
                  }>
                    {
                      selectedCompere ?
                      <div className={styles.host_content}>
                        {
                          selectedCompere.headUrlShow ?
                          <><img src={selectedCompere.headUrlShow} alt="" />{selectedCompere?.name}</>:
                          <><div className={styles.no_comment_head} style={{background:randomColor(selectedCompere?.id)}}>{processNames(selectedCompere?.name)}</div>{selectedCompere?.name}</>
                        }
                      </div> :
                      '请选择主持人'
                    }
                  </div>
                  <img className={styles.space_form_arrow} src={rightArrowIcon} alt="" />
                </div>
              </div>
               : null
            }
            <div className={styles.special_wrap}>
              <div className={styles.space_form_box}>
                <div className={styles.space_form_title}><span>*</span>{CreateSpaceText}名称</div>
                <Form.Item
                  label=""
                  name="spaceName"
                  rules={[
                    {required: true, message: `请输入${CreateSpaceText}名称`},
                    {pattern: /^[~\s!！@#$￥%^…&*()_—+{}：:‘’“”《》？"|<>?`\-=[\];',.。，；（）、a-zA-Z0-9\u4e00-\u9fa5/]+$/, message: `${CreateSpaceText}名称不能包含特殊字符`},
                    {max: 50, message: '输入长度最大为50个字符!'}
                  ]}
                >
                  <Input
                    type="text" autoComplete="off" placeholder={`为${CreateSpaceText}取一个讨论话题`} style={{ '--text-align': 'right' }}
                    onChange={(val) => setSpaceInputVal(val)} onBlur={spaceInputBlurFn}
                  />
                </Form.Item>
              </div>
            </div>
            <div className={styles.space_introduction_wrap}>
              <div className={styles.space_introduction}>{CreateSpaceText}介绍</div>
              <Form.Item
                label=""
                name="spaceIntroduce"
                rules={[
                  {pattern: /^[~\s!！@#$￥%^…&*()_—+{}：:‘’“”《》？"|<>?`\-=[\];',.。，；（）、a-zA-Z0-9\u4e00-\u9fa5/]+$/, message: `${CreateSpaceText}介绍不能包含特殊字符`},
                ]}
              >
                <TextArea
                  placeholder={`详细描述${CreateSpaceText}分享的话题内容`}
                  rows={4}
                  showCount
                  maxLength={100}
                  onBlur={spaceTextAreaBlurFn}
                  className={styles.space_introduction_input}
                />
              </Form.Item>
            </div>
            {
              !isEditSpace ?
                <div className={styles.space_form_box}>
                  <div className={styles.space_form_title}>关联王国</div>
                  <div className={styles.space_form_content} onClick={()=>{if(!isSelectKingDom) jumpHandle() }}>
                    {
                      (selectedKingdom&&selectedKingdom!=null) || (kingdomInfoData&&Object.keys(kingdomInfoData).length != 0) ?
                        <div className={styles.space_form_item}>
                          {
                            selectedKingdom?.kingdomCoverUrlShow || selectedKingdom?.kingImgUrlShow || kingdomInfoData?.kingImgUrlShow ?
                              <img src={selectedKingdom?.kingdomCoverUrlShow || selectedKingdom?.kingImgUrlShow || kingdomInfoData?.kingImgUrlShow} alt="" /> :
                              <div className={styles.no_comment_head} style={{background:randomColor(selectedKingdom?.wxUserId || kingdomInfoData?.wxUserId)}}>{processNames(selectedKingdom?.kingName || kingdomInfoData?.kingName)}</div>
                          }
                          <span className={styles.kingdom_name_text}>{selectedKingdom?.name || kingdomInfoData?.name}</span>
                        </div> :
                        <div className={styles.space_input_init_text}>请选择关联王国</div>
                    }
                    <img className={styles.space_form_arrow} src={rightArrowIcon} alt="" />
                  </div>
                </div>
                : null
            }
            <div className={styles.space_guest_wrap}>
              <div className={styles.space_guest_box}>
                <div className={styles.space_guest_title}>邀请{isMeeting ? '参会人' : '嘉宾'}</div>
                <div className={styles.space_guest_illustrate}>最多可邀请15位{isMeeting ? '参会人' : '嘉宾'}</div>
              </div>
              <div className={styles.space_guest_list_wrap}>
                {
                  selectedGuest && selectedGuest.map((item: { id: React.Key | null | undefined; headUrlShow: string | undefined; name: boolean | React.ReactChild | React.ReactFragment | React.ReactPortal | null | undefined; }) =>
                    <div className={styles.space_guest_item} key={item.id}>
                      <div className={styles.space_guest_img}>
                        {
                          item.headUrlShow ?
                          <img src={item.headUrlShow} alt="" /> :
                          <div className={styles.no_comment_head} style={{background:randomColor(item.id)}}><span>{processNames(item?.name)}</span></div>
                        }
                      </div>
                      <div className={styles.space_guest_name}>{item.name}</div>
                      <span className={classNames({
                          [styles.space_guest_item_icon]: true,
                          [styles.show]: minusIconVisible})
                        }
                        onClick={() => deleteGuest(item)}
                      >
                        <img src={redReduceIcon} alt="" />
                      </span>
                    </div>)
                }
                {/* 加嘉宾 */}
                {selectedGuest && selectedGuest.length < 15 ? <div className={styles.space_guest_add} onClick={()=>{if(uploadVideoLoading || loadingByUpload || loadingByAdverUpload) return; goBack(5)}}><img src={addIcon} alt="" /></div> : ''}
                {/* 减嘉宾 */}
                {selectedGuest && selectedGuest.length >= 1 ? <div className={styles.space_guest_reduce} onClick={()=>{setMinusIconVisible(visible => !visible)}}><img src={reduceIcon} alt="" /></div> : ''}
              </div>
            </div>
            {/* 预约指导创建时，不展示观众字段 */}
            {
              spaceFromEnter.pageType != '4'  && isMeeting ?
              <div className={styles.space_audience}>
                <div className={styles.space_audience_title}>观众</div>
                {
                 /* type == 2 && isCureentKingdom ?
                  <div className={styles.space_audience_text}>
                    <div className={styles.space_input_init_text}>当前王国所有成员可见</div>
                  </div> :*/
                  <div className={styles.space_audience_text} onClick={() => {if(uploadVideoLoading || loadingByUpload || loadingByAdverUpload) return; goBack(7)}}>
                    {
                      enterpriseText ?
                      <div className={styles.space_form_item}>
                        {enterpriseText}
                      </div> :
                      <div className={styles.space_input_init_text}>默认所有人可见</div>
                    }
                    <img className={styles.space_form_arrow} src={rightArrowIcon} alt="" />
                  </div>
                }
              </div> : null
            }
            {/* 观众为企业用户时才展示该字段 */}
            {
              spectatorType === 2 ?
              <div className={styles.space_type}>
                <div className={styles.space_type_title}>{CreateSpaceText}类型</div>
                <div
                  className={styles.space_type_text}
                  onClick={() => {if(uploadVideoLoading || loadingByUpload || loadingByAdverUpload) return; goBack(8)}}>
                  {
                    spaceTypeName ?
                    <div className={styles.space_form_item}>{spaceTypeName}</div> :
                    <div className={styles.space_input_init_text}>请选择</div>
                  }
                  <img className={styles.space_form_arrow} src={rightArrowIcon} alt="" />
                </div>
              </div> : null
            }
          </div>
          <div className={styles.upload_wrap}>
            <div className={styles.upload_img_wrap}>
              <div className={styles.upload_img_title}>{CreateSpaceText}封面(建议尺寸:750*422)</div>
              <Spin spinning={loadingByUpload}>
                <div className={styles.upload_img_content}>
                  <div className={styles.upload_box}>
                    {spaceCoverUrl ? <img className={styles.upload_img} src={spaceCoverUrl?.fileUrlView} alt="" /> : <div><img className={styles.init_upload_img} src={uploadImg} alt="" /></div>}
                  </div>
                  <Upload
                    headers={getHeaders()}
                    accept="image/*"
                    action={`/api/server/base/uploadFile?${stringify({ fileType: 14, userId: UerInfo?.friUserId})}`}
                    listType="picture-card"
                    className={styles.edit_head_picture}
                    onChange={(info)=>onChangeByUpload(info, 'COVER')}
                    onRemove={()=>{}}
                    beforeUpload={beforeUpload}
                    showUploadList={false}
                   />
                </div>
              </Spin>
            </div>
            <div className={styles.upload_img_wrap}>
              <div className={styles.upload_img_title}>{CreateSpaceText}广告(建议尺寸:750*114)</div>
              <Spin spinning={loadingByAdverUpload}>
                <div className={styles.upload_img_content}>
                  <div className={styles.upload_box}>
                    {spaceAdvertisingUrl ? <img className={styles.upload_img} src={spaceAdvertisingUrl?.fileUrlView} alt="" /> : <div><img className={styles.init_upload_img} src={uploadImg} alt="" /></div>}
                  </div>
                  <Upload
                    headers={getHeaders()}
                    accept="image/*"
                    action={`/api/server/base/uploadFile?${stringify({ fileType: 15, userId: UerInfo?.friUserId})}`}
                    listType="picture-card"
                    className={styles.edit_head_picture}
                    onChange={(info)=>onChangeByUpload(info, 'ADVERT')}
                    onRemove={()=>{}}
                    beforeUpload={beforeUpload}
                    showUploadList={false}
                   />
                </div>
              </Spin>
            </div>
            {
              spaceFromEnter.pageType != '4' &&
              <div className={styles.upload_video_wrap}>
                <div className={styles.upload_video_title}>{CreateSpaceText}视频(支持MP4,大小2G以内)</div>
                <div className={styles.video_content}>
                  {
                    // 视频上传完后展示的样式
                    spaceVideoUrl ?
                      <div className={classNames(styles.upload_video_content)}>
                        <div className={styles.uploading_icon_content}>
                          <img src={frameIcon} className={styles.init_upload_img} alt=""/>
                          <div className={styles.video_name_text}><span
                            className={styles.uploading_name_text}>{videoNameModify()}</span>.mp4
                          </div>
                        </div>
                        <input type="file" name="uploadVideoInput" id="uploadVideoInput" accept='video/mp4'
                               className={styles.upload_video_input} onChange={videoFileChange}
                               disabled={uploadVideoLoading}
                        />
                      </div> :
                      // 未上传默认样式以及上传中的样式
                      <div className={classNames(styles.upload_video_content)}>
                        {
                          uploadVideoLoading ?
                            // 上传中
                            <div className={styles.uploading_icon_content} style={{background: 'none'}}>
                              <img src={frameIcon} className={styles.init_upload_img} alt=""/>
                              <div className={styles.uploading_icon_text}>上传{uploadProgress}%...</div>
                            </div> :
                            // 未上传
                            <div className={styles.uploading_init_icon_content}><img className={styles.init_upload_img}
                                                                                     src={uploadImg} alt=""/></div>
                        }
                        <input type="file" name="uploadVideoInput" id="uploadVideoInput" accept='video/mp4'
                               className={styles.upload_video_input} onChange={videoFileChange}
                               disabled={uploadVideoLoading}
                        />
                      </div>
                  }
                </div>
              </div>
            }

            <div
              style={{paddingBottom:0}}
              className={styles.space_password_wrap}
            >
              <div className={styles.space_password_box}>
                <div className={styles.space_password_title}>{CreateSpaceText}密码</div>
                <div className={styles.space_password_open_btn}>
                  <Switch
                    style={{
                      '--checked-color': '#0095FF',
                      '--height': '26px',
                      '--width': '48px',
                    }}
                    checked={isSwitch}
                    disabled={isEditSpace}
                    onChange={(val)=>{
                      passwordSwitchChangeFn(val)
                      // 当关闭会议密码时, 同时取消无密码可申请进入会议开关
                      if (!val) { onChangeIsAllowApplications(val) }
                    }}
                  />
                </div>
              </div>
              {
                isSwitch ?
                <div className={styles.password}>
                  <Form.Item
                    label=""
                    name="spacePassword"
                    rules={[
                      {required: !isEditSpace && isSwitch, message: '请输入4位数字密码'},
                      {pattern: /^[0-9]{4}$/, message: '请输入4位数字密码'},
                    ]}
                  >
                    <Input
                      className={styles.input}
                      placeholder='请输入4位数密码'
                      maxLength={4}
                      autoComplete='off'
                      type={passType ? 'text' : 'password'}
                      onChange={(val) => setPassword(val)}
                      onBlur={passwordBlur}
                      disabled={isEditSpace}
                    />
                  </Form.Item>
                  <div className={styles.eye} onClick={()=>{setPassType(!passType)}}>
                    {passType ? (
                      <img src={openEyeIcon} alt="" />
                    ) : (
                      <img src={closeEyeIcon} alt="" />
                    )}
                  </div>
                </div> : null
              }
            </div>

            {/*无密码需要申请,主持人批准后进入*/}
            {(!!isSwitch && !!isMeeting) &&
              <div
                style={{paddingTop:10}}
                className={classNames(
                {
                  [styles.space_password_wrap]:true,
                  [styles.space_password_wrap_bottm_line]:true,
                })
              }>
                <div className={styles.space_password_box}>
                  <div className={styles.space_password_title}>
                    无密码需要申请，主持人批准后进入
                    <i className={styles.space_allowApplications}></i>
                  </div>
                  <div className={styles.space_password_open_btn}>
                    <Switch
                      style={{
                        '--checked-color': '#0095FF',
                        '--height': '26px',
                        '--width': '48px',
                      }}
                      checked={allowApplications} // 是否允许无密码申请进入会议
                      disabled={isEditSpace}
                      onChange={onChangeIsAllowApplications}
                    />
                  </div>
                </div>
              </div>
            }

            <div className={styles.space_password_wrap}>
              <div className={styles.space_password_box}>
                <div className={styles.space_password_title}>屏幕录制自动打开</div>
                <div className={styles.space_password_open_btn}>
                  <Switch
                    style={{
                      '--checked-color': '#0095FF',
                      '--height': '26px',
                      '--width': '48px',
                    }}
                    checked={isAutoRecord}
                    disabled={isEditSpace}
                    onChange={onChangeIsAutoRecord}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className={styles.space_btn_wrap}>
            <div className={styles.space_start_now_btn} onClick={startBtnFn}>马上开始</div>
            <div className={styles.space_appointment_start_btn} onClick={appointmentStartBtnFn}>预约开始</div>
          </div>
        </div>
      </Form>
    </div>
  )
}
export default connect(({ userInfoStore, loading }: any) => ({ userInfoStore, loading}))(Index)
