.container {
  height: 100%;
  background: #EEF3F9;
  display: flex;
  flex-wrap: nowrap;
  flex-direction: column;
  align-items: center;
  .content {
    flex: 1;
    width: 100%;
    overflow-y: auto;
    .content_inner {
      max-width: 1228px;
      min-height: 100%;
      padding: 16px 0;
      margin: 0 auto;
      display: flex;
      flex-wrap: nowrap;
      flex-direction: column;
    }
  }
}

.header {
  width: 100%;
  flex-shrink: 0;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  height: 48px;
  .header_icon {
    width: 40px;
    height: 40px;
    background: url("../../../../assets/GlobalImg/pc_goback.png") no-repeat center;
    background-size: 20px 20px;
    margin-right: 8px;
    cursor: pointer;
  }
  .header_title {
    font-size: 18px;
    color: #000;
    font-weight: 600;
    line-height: 34px;
  }
}
.box {
  width: 100%;
  flex: 1;
  background: #fff;
  border-radius: 8px;
  padding-bottom: 16px;
}

.step_container {
  width: 956px;
  margin: 0 auto;
  padding-top: 40px;
}

.step_content {
  display: flex;
  flex-wrap: nowrap;
  column-gap: 12px;
  :global {
    .ant-checkbox-inner, .ant-checkbox-checked::after {
      border-radius: 50%;
    }
    .ant-checkbox-wrapper {
      color: #000;
    }
    .ant-checkbox-wrapper + .ant-checkbox-wrapper {
      margin-left: 22px;
    }
    .ant-checkbox-wrapper-checked {
      color: #0095FF;
    }
  }
}

.container_left {
  flex-shrink: 0;
  margin-right: 12px;
  border-radius: 3px;
  border: 1px solid #D9D9D9;
  overflow-y: auto;
  .case_scroll_wrap {
    width: 375px;
    min-width: 375px;
  }
}
.container_right {
  flex: 1;
  min-width: 569px;
  max-width: 569px;
  border-radius: 3px;
  .expert_card_wrap {
    border: 1px solid #D9D9D9;
    border-radius: 3px 3px 0 0;
    overflow: hidden;
    margin-bottom: 12px;
  }
  .block {
    border-radius: 3px;
    border: 1px solid #D9D9D9;
    padding-left: 16px;
    &.user_question {
      margin-bottom: 12px;
    }
    .block_title {
      font-size: 16px;
      color: #000;
      font-weight: 500;
      line-height: 22px;
      border-bottom: 1px solid #E1E4E7;
      padding-top: 16px;
      height: 50px;
    }
    .question_box {
      padding-bottom: 28px;
      :global {
        .ant-input {
          border: 0;
          outline: 0;
          box-shadow: none;
          padding: 10px 16px 10px 0;
          font-size: 14px;
          color: #666;
        }
        .ant-input-textarea-show-count::after {
          font-size: 12px;
          color: #999;
          line-height: 21px;
          padding-right: 16px;
        }
      }
    }

    .block_label {
      font-size: 14px;
      color: #000;
      font-weight: 500;
      padding-top: 16px;
      padding-bottom: 8px;
    }
    .consultation_type {

    }
  }
}
.step_bottom {
  background: #FFF7DA;
  border: 1px solid #EFD989;
  line-height: 33px;
  text-align: center;
  font-size: 12px;
  color: #8C772B;
  margin-top: 12px;
}
.step_btn_box {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 20px;
  .pay_btn {
    margin-left: 4px;
    border-radius: 4px;
  }
  .highlight {
    color: #0095FF;
  }
}
