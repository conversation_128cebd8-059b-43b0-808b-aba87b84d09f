.spin {
  height: 100%;
  :global {
    .ant-spin-container {
      height: 100%;
    }
  }
}
:global {
  .ka-wrapper, .ka-content {
    height: 100%;
  }
}
.nav_bar_left {
  font-size: 12px;
  color: #999;
}
.nav_bar_btn {
  display: block;
  width: 69px;
  height: 29px;
  background: #0095FF;
  font-size: 15px;
  color: #fff;
  line-height: 30px;
  border-radius: 15px;
  text-align: center;
  &.disabled {
    background: #8DCDF7;
    color: #E8F4FC;
  }
}
.container {
  padding: 44px 12px 0;
  height: calc(100% - 68px);
  overflow-y: auto;
}
.container.show_panel {
  height: calc(100% - 68px - 300px);
}

.title_input_box {
  padding: 16px 0 8px;
  :global {
    .adm-text-area {
      position: relative;
    }
    .adm-text-area-element {
      font-size: 22px;
      color: #000;
    }
  }
}




