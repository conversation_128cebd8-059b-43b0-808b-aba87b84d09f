.spin {
  height: 100%;
  :global {
    .ant-spin-container {
      height: 100%;
    }
  }
}
:global {
  .ka-wrapper, .ka-content {
    height: 100%;
  }
}
.nav_bar_right {
  display: flex;
  align-items: center;
  .nar_bar_link_btn {
    font-size: 15px;
    color: #0095FF;
    margin-right: 16px;
  }
  .nar_bar_btn {
    display: block;
    width: 54px;
    height: 29px;
    background: #0095FF;
    font-size: 15px;
    color: #fff;
    line-height: 30px;
    border-radius: 15px;
    text-align: center;
    &.disabled {
      background: #8DCDF7;
      color: #E8F4FC;
    }
  }
}

.container {
  padding: 44px 0 20px;
  height: calc(100% - 45px);
  overflow-y: auto;
}
.container.show_panel {
  height: calc(100% - 45px - 300px);
}

.select_kingdom_box {
  display: flex;
  flex-wrap: nowrap;
  padding: 16px 24px 16px 12px;
  border-bottom: 1px solid #f5f6f8;
  align-items: center;
  position: relative;
  .select_left {
    display: flex;
    flex-wrap: nowrap;
    flex-shrink: 0;
    overflow: hidden;
    align-items: center;
    column-gap: 4px;
    font-size: 14px;
    color: #000;
    & > i {
      width: 16px;
      height: 16px;
      &.select_left_icon_1 {
        background: url("../../../../assets/GlobalImg/associated.png") no-repeat center;
        background-size: 100% 100%;
      }
      &.select_left_icon_2 {
        background: url("../../../../assets/GlobalImg/black_arrow.png") no-repeat center;
        background-size: 100% 100%;
      }
    }
    & > span {
      height: 20px;
      line-height: 21px;
    }
    .divider {
      width: 0;
      height: 17px;
      border-left: 1px solid #d9d9d9;
    }
  }
  .select_right {
    margin-left: 8px;
    background: #F5F6F8;
    font-size: 13px;
    color: #000;
    line-height: 18px;
    padding: 2px 8px 2px 12px;
    border-radius: 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .select_right_default {
    font-size: 12px;
    color: #999;
    margin-left: 8px;
    padding-right: 8px;
    height: 17px;
    line-height: 19px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .change_page_btn {
    position: absolute;
    right: 8px;
    top: 15px;
    font-size: 16px;
    color: #0095FF;
  }

  .popover_container {
    padding: 6px 4px 6px 0;
    .popover_link {
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #000;
      margin-bottom: 16px;
      &:last-child {
        margin-bottom: 0;
      }
      img {
        margin-right: 8px;
      }
      span {
        height: 18px;
        line-height: 18px;
      }
    }
  }
}

.container .editor_box {
  margin-bottom: 20px;
  :global {
    .ql-editor {
      min-height: 100px !important;
      padding: 16px 12px !important;
    }
    .ql-editor.ql-blank::before {
      left: 12px;
    }
  }
}

.cover_img_box {
  display: flex;
  flex-wrap: wrap;
  column-gap: 4px;
  row-gap: 8px;
  padding-left: 12px;
  .cover_img_item {
    width: 114px;
    height: 114px;
    position: relative;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    :global {
      .anticon {
        position: absolute;
        top: 0;
        right: 0;
        color: #fff;
        font-size: 14px;
        padding: 3px;
      }
    }
    .cover_img_item_shadow {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 20px;
      background: linear-gradient(180deg, #000 0%, rgba(0,0,0,0) 100%);
      opacity: 0.4;
    }
    .cover_img_item_edit {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      background: rgba(0,0,0,0.5);
      color: #fff;
      text-align: center;
      height: 23px;
      line-height: 23px;
      font-size: 11px;
    }
  }
  .cover_img_btn {
    position: relative;
    width: 114px;
    height: 114px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #F8F8F8;
    color: #CBCBCB;
    font-size: 36px;
    :global {
      .ant-upload {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        z-index: 2;
        display: block !important;
      }
    }
  }
}

// 空间卡片
.space_wrap {
  margin: 0 16px;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #EBEBEB;
  display: flex;
  flex-wrap: nowrap;
  .left_cover_image {
    flex-shrink: 0;
    margin-right: 8px;
    width: 68px;
    height: 68px;
    border-radius: 4px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    line-height: 68px;
    color: #fff;
    font-size: 30px;
    text-align: center;
    white-space: nowrap;
    position: relative;
    .title_in_cover_image {
      position: absolute;
      z-index: 10;
      width: 100%;
      top: 14px;
      left: 0;
      padding-left: 8px;
      font-size: 12px;
      line-height: 16px;
      color: #fff;
      font-weight: 500;
      word-break: break-all;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2; /* 指定显示行数 */
      overflow: hidden;
      text-overflow: ellipsis;
      text-align: left;
    }
  }
  .right {
    flex: 1;
    overflow: hidden;
    .space_title {
      font-size: 13px;
      color: #000;
      font-weight: 500;
      line-height: 18px;
      word-break: break-all;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      text-overflow: ellipsis;
    }
    .space_introduce {
      font-size: 12px;
      color: #333;
      line-height: 16px;
      word-break: break-all;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      text-overflow: ellipsis;
    }
  }
}
