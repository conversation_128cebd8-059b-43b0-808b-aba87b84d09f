/**
 * @Description: H5-个人中心/医生主页-帖子合集
 */
import React, { useState } from 'react';
import { connect } from 'umi';
import classNames from 'classnames'
import styles from './index.less';

import H5Post from '../H5Post';                // 帖子
import H5Article from '../H5Article';          // 文章
import H5Link from '../H5Link'                 // 外链
import H5Drafts from '../H5Drafts';            // 草稿箱

// tab签数据
const tabsDataSource = [
  { id: 1, name: '帖子' },
  { id: 2, name: '文章' },
  { id: 3, name: '外链' },
  { id: 4, name: '草稿箱' },
]

interface PropsType {
  isMyPages: boolean,        // 是否是自己的主页
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { dispatch, isMyPages, expertAdvice } = props

  const [tab, setTab] = useState(expertAdvice.subTabKey || 1)    // tab状态

  // tab签切换
  const onChangeTabs = (id) => {
    setTab(id)
    dispatch({
      type: 'expertAdvice/save',
      payload: {
        subTabKey: id,       // 专家详情二级tab选中状态
      }
    })
  }

  return (
    <>
      <div className={styles.post_group_container}>
        {/* tab签 */}
        <div className={styles.tabs_wrap}>
          {
            tabsDataSource.map(item => {
              // 非本人不展示草稿箱
              if (!isMyPages && item.id == 4) {
                return null
              }
              return (
                <span
                  key={item.id}
                  className={classNames(styles.tabs_item, {
                    [styles.checked]: tab == item.id,
                  })}
                  onClick={() => onChangeTabs(item.id)}
                >{item.name}</span>
              )
            })
          }
        </div>
        {/* 列表 */}
        {
          tab == 1 ? <H5Post isMyPages={isMyPages} />
            : tab == 2 ? <H5Article isMyPages={isMyPages} />
            : tab == 3 ? <H5Link isMyPages={isMyPages} />
              : tab == 4 ? <H5Drafts isMyPages={isMyPages} />
                : null
        }
      </div>

    </>
  );
};

export default connect(({ expertAdvice, loading }: any) => ({ expertAdvice, loading }))(Index);
