/**
 * @Description: PC端，文章/外链卡片组件
 */
import React from 'react'
import classNames from 'classnames'
import { message, Typography } from 'antd'
import { PictureOutlined } from '@ant-design/icons'
import styles from './index.less'

interface PropsType {
  isMyPages: boolean,        // 是否是自己的页面
  data: any,                 // 数据
  onEdit: () => {},          // 编辑回调
  onLow: () => {},           // 下架回调
  onDelete: () => {},        // 删除回调
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { isMyPages, data } = props

  // 修改事件，状态：1.审核通过 0.未审核 2.审核未通过 3.草稿
  const onEdit = () => {
    if (data.status == 0) {
      return
    }
    props.onEdit(data.id, data.imageType)
  }

  // 下架
  const onLow = () => {
    props.onLow(data.id)
  }

  // 删除
  const onDelete = () => {
    props.onDelete(data.id)
  }

  // 复制空间链接
  const onCopy = () => {
    message.success('复制成功')
  }

  return (
    <div className={styles.wrap}>
      <div className={styles.left}>
        <div className={styles.cover_img_wrap} style={data.imageUrls && data.imageUrls[0] ? {backgroundImage: `url(${data.imageUrls[0]})`} : {}}>
          {
            data.imageUrls && data.imageUrls[0] ? null :
              <div className={styles.no_cover_wrap}>
                <PictureOutlined/>
                <div style={{marginTop: 5}}>无封面</div>
              </div>
          }
        </div>
        {/* 状态：1.审核通过 0.未审核 2.审核未通过 3.草稿 */}
        <div className={styles.details_wrap}>
          <div>
            <div className={styles.title}>{data.imageTitle || '--'}</div>
            <span className={classNames(styles.status, {
              [styles.status_0]: data.status == 0,
              [styles.status_1]: data.status == 1,
              [styles.status_2]: data.status == 2,
            })}>
              {
                data.status == 0 ? '审核中' :
                  data.status == 1 ? '已发布'
                    : data.status == 2 ? '审核不通过'
                    : ''
              }
            </span>
            {
              data.status == 2 ? <span className={styles.status_2_tip}>审核不通过：{data.auditingReason || '--'}</span> : null
            }
          </div>
          {
            data.status !=3 &&
            <div className={styles.details_data}>
              <span>点赞{data.spotLikeSize || 0}</span> · <span>评论{data.imageComments || 0}</span> · <span>浏览量{data.imagePv || 0}</span> · <span>转发{data.imageForward || 0}</span>
            </div>
          }
        </div>
      </div>
      <div className={styles.right}>
        <div className={styles.date}>{data.editDate}</div>
        {/* 是我的主页时有操作按钮 */}
        {
          isMyPages ?
          <div className={styles.btn_wrap}>
            {
              data.status == 1 &&
              <Typography.Paragraph copyable={{
                text: data.imageType == 1 ? `${window.location.origin}/CreateGraphicsText/ArticleDetails?id=${data.id}` : `${window.location.origin}/CreateGraphicsText/ExternalLinksDetails?id=${data.id}`,
                icon: [<div>复制链接</div>, <div>复制链接</div>],
                tooltips: ['', ''],
                onCopy: onCopy,
              }}></Typography.Paragraph>
            }

            <div className={classNames({
              [styles.btn_disabled]: data.status == 0,
            })} onClick={onEdit}>修改</div>

            {
              data.status != 3 &&
              <div onClick={onLow}>下架</div>
            }

            {
              data.status == 3 &&
              <div onClick={onDelete}>删除</div>
            }
          </div> : null
        }
      </div>
    </div>
  )
}

export default Index
