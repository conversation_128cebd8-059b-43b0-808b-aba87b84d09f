@import '../../../../utils/imageText.less';
.spin {
  height: 100%;
  :global {
    .ant-spin-container {
      height: 100%;
    }
  }
}

.container {
  padding-top: 52px;
  padding-bottom: 52px;
  height: 100%;
  overflow-y: auto;
}

.user_card_wrap {
  padding: 0 16px;
}

.image_text_content_wrap {
  padding: 20px 16px 12px;
}

.post_image_wrap {
  display: flex;
  flex-wrap: wrap;
  padding: 0 12px;
  margin-bottom: 4px;
  .image_item_wrap {
    width: 33.33%;
    padding: 0 4px;
    margin-bottom: 8px;
  }
  .image_item {
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    &::after {
      content: "";
      display: block;
      width: 100%;
      padding-top: 100%;
    }
  }
}

.kingdom_wrap{
  padding: 0 16px;
  margin-bottom: 11px;
}
.gdp_wrap {
  display: flex;
  align-items: center;
  padding: 0 16px;
  margin-bottom: 24px;
  & > i {
    width: 14px;
    height: 14px;
    margin-right: 4px;
    background: url("../../../../assets/GlobalImg/gdp.png") no-repeat center;
    background-size: 100% 100%;
  }
  & > span {
    font-size: 12px;
    color: #777;
    line-height: 18px;
  }
}
