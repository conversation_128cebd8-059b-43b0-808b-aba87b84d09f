/**
 * @Description: 创建王国、空间相关模块总弹窗
 */
import React, { useState, useEffect, useCallback } from 'react';
import { connect } from 'umi';
import { Input, Popup, Toast } from 'antd-mobile';
import styles from './index.less';
import goBackIcon from '@/assets/GlobalImg/go_back.png';
import searchIcon from '@/assets/GlobalImg/search.png';
import classNames from 'classnames';
import { processNames, randomColor } from '@/utils/utils';
import noDataImg from '@/assets/GlobalImg/no_data.png';
import { RightOutlined } from '@ant-design/icons';
import { Spin } from 'antd';
import { content } from 'html2canvas/dist/types/css/property-descriptors/content';
import { useDebounce } from "@/utils/utils";
const Index: React.FC = (props: any) => {
  const { userInfoStore, dispatch, comeType, loading } = props || {};
  const { VisibleBySelectGuest } = userInfoStore;
  const [userlist, setUserList] = useState<any[]>([]); // 嘉宾列表
  const [isHasData, setIsHasData] = useState(null); // 是否有数据
  const [thisSelectedGuest, setThisSelectedGuest] = useState<Record<string, any>[]>([]); // 勾选中的数据
  const [attendeeCfgBizUserList, setAttendeeCfgBizUserList] = useState<any[]>(); //
  const [currentType, setCurrentType] = useState(); // 判定当前弹窗类型 area选择区域 clinic选择诊所 user选择参会人
  const [bizList, setBizList] = useState(); // 区域列表
  const [orgList, setOrgList] = useState(); // 诊所列表
  const [biz, setBiz] = useState(); // 选择的区域
  const [org, setOrg] = useState(); // 选择的机构
  const [tenantId, setTenantId] = useState(); // 选择的品牌id
  const UerInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');

  useEffect(() => {
    if (VisibleBySelectGuest) {
      getListData();
      const { selectedGuest } = userInfoStore || {};
      if (Array.isArray(selectedGuest)) {
        setThisSelectedGuest(selectedGuest);
      }
    } else {
      // 清空弹窗中的缓存数据
      clearData();
    }
  }, [VisibleBySelectGuest]);

  const  updataByBizOrOrg = (id) => {
      getAttendeeSearchUserByBizGroupId(id);
  };

  // 根据区域、机构获取用户
  const getAttendeeSearchUserByBizGroupId = (bizGroupId) => {
    dispatch({
      type: 'userInfoStore/getAttendeeSearchUserByBizGroupId',
      payload: {
        bizGroupId: bizGroupId,
      },
    }).then((res) => {
      if (res && res.code == 200) {
        if (Array.isArray(res.content) && res.content.length > 0) {
          setUserList(res.content);
          setIsHasData(0);
        } else {
          setUserList([]);
          setIsHasData(1);
        }
      }
    });
  };

  // 选中处理
  const selectHandle = useCallback(
    (item) => {
      const isSelected = thisSelectedGuest.find((it) => it?.friUserId === item.friUserId);
      // 已经选择了15位，并且不是取消选择
      if (!isSelected && thisSelectedGuest.length >= 15)
        return Toast.show({ content: '最多能选15位哦~' });
      setThisSelectedGuest((guest) => {
        return isSelected
          ? guest.filter((it) => it.friUserId !== item.friUserId)
          : [...guest, item];
      });
    },
    [thisSelectedGuest],
  );

  // input 参会人嘉宾
  let changeInputFn = (val) => {
    if (!val || !val.trim()) {
      setCurrentType(null);
      setUserList([]);
      getListData(val);
    } else {
      setCurrentType('user');
      getListData(val);
    }
  };
  // 搜索参会人添加防抖
  changeInputFn = useDebounce(changeInputFn,300);

  // 获取参会人数据
  const getListData = useCallback(
    (val) => {
      dispatch({
        type: 'userInfoStore/attendeeSearchUserByQueryKey',
        payload: {
          queryKey: val && val.trim(),
        },
      }).then((res) => {
        if (res && res.code == 200) {
          const { userList, attendeeCfgBizUserList } = res.content || {};
          if (Array.isArray(attendeeCfgBizUserList)) {
            setAttendeeCfgBizUserList(attendeeCfgBizUserList);
            if (
              attendeeCfgBizUserList.length > 0 &&
              attendeeCfgBizUserList[0] &&
              attendeeCfgBizUserList[0].tenantId
            ) {
              setTenantId(attendeeCfgBizUserList[0].tenantId);
            }
            if (
              attendeeCfgBizUserList.length > 0 &&
              attendeeCfgBizUserList[0] &&
              Array.isArray(attendeeCfgBizUserList[0].bizList)
            ) {
              setBizList(attendeeCfgBizUserList[0].bizList);
            }
            if (
              attendeeCfgBizUserList.length > 0 &&
              attendeeCfgBizUserList[0] &&
              Array.isArray(attendeeCfgBizUserList[0].orgList)
            ) {
              setOrgList(attendeeCfgBizUserList[0].orgList);
            }
            if (
              Array.isArray(attendeeCfgBizUserList[0]?.userList) &&
              attendeeCfgBizUserList[0]?.userList.length > 0
            ) {
              setUserList(attendeeCfgBizUserList[0]?.userList);
              setIsHasData(0);
            } else {
              setUserList([]);
              setIsHasData(1);
            }
          }
          if (!!val) {
            if (Array.isArray(userList) && userList.length > 0) {
              setUserList(userList);
              setIsHasData(0);
            } else {
              setUserList([]);
              setIsHasData(1);
            }
          }
        }else {
          setUserList([]);
          setIsHasData(1);
        }
      });
    },
    [tenantId],
  );

  useEffect(() => {
    if (tenantId && Array.isArray(attendeeCfgBizUserList)) {
      let findByAttendeeCfgBizUserList = attendeeCfgBizUserList.find((item) => {
        return item.tenantId == tenantId;
      });

      setBizList(
        Array.isArray(findByAttendeeCfgBizUserList?.bizList)
          ? findByAttendeeCfgBizUserList.bizList
          : [],
      );
      setOrgList(
        Array.isArray(findByAttendeeCfgBizUserList?.orgList)
          ? findByAttendeeCfgBizUserList.orgList
          : [],
      );

      if (
        Array.isArray(findByAttendeeCfgBizUserList?.userList) &&
        findByAttendeeCfgBizUserList?.userList.length > 0
      ) {
        setUserList(
          Array.isArray(findByAttendeeCfgBizUserList?.userList)
            ? findByAttendeeCfgBizUserList?.userList
            : [],
        );
        setIsHasData(0);
      } else {
        setUserList([]);
        setIsHasData(1);
      }
    }
  }, [tenantId]);

  // 确定，保存到store，返回上层
  const confirmHandle = useCallback(() => {
    let confirmHandleList = thisSelectedGuest.map((item) => {
      return {
        ...item,
        id: item.friUserId,
      };
    });
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        VisibleBySelectGuest: false,
        selectedGuest: confirmHandleList,
      },
    });
  }, [dispatch, thisSelectedGuest]);

  // 清空弹窗缓存的数据
  const clearData = () => {
    setCurrentType(null);
    setBizList([]);
    setOrgList([]);
    setBiz(null);
    setOrg(null);
    setUserList([]);
    setIsHasData(null);
    setThisSelectedGuest(null);
    setAttendeeCfgBizUserList(null);
  };

  const closeModalBtnFn = () => {
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: { VisibleBySelectGuest: false },
    });
    clearData();
  };

  return (
    <Popup
      visible={VisibleBySelectGuest}
      onMaskClick={closeModalBtnFn}
      className={styles.popup_container}
      bodyStyle={{ height: '90vh' }}
      destroyOnClose
    >
      <div className={styles.header}>
        <div className={styles.line} onClick={closeModalBtnFn}></div>
      </div>
      <div>
        <div className={styles.titleInfo}>
          <div className={styles.titleWarp}>
            <div
              className={styles.titleBackIcon}
              onClick={() => {
                if (!!currentType) {
                  setUserList([]);
                  setCurrentType(null);
                } else {
                  dispatch({
                    type: 'userInfoStore/setTaskListState',
                    payload: { VisibleBySelectGuest: false },
                  });
                }
              }}
            >
              <img src={goBackIcon} alt="" />
            </div>
            <div className={styles.titleText}>参会人</div>
          </div>
        </div>
        <div className={styles.content}>
          <div className={styles.input_box}>
            <img className={styles.search_icon} src={searchIcon} alt="" />
            <Input placeholder="请输入内容" clearable onChange={changeInputFn} />
          </div>
          {/*<div className={styles.select_num}>已选择{thisSelectedGuest.length}/15</div>*/}
        </div>


        {!currentType && (
          <div className={styles.selectGuest_content}>
            {(UerInfo && UerInfo.isBizUser == 1) &&
              <>
                {/* 选择品牌 */}
                <div className={styles.selectByBrand}>
                  {Array.isArray(attendeeCfgBizUserList) &&
                    attendeeCfgBizUserList.map((item,index) => {
                      const { tenantName, tenantId: tenantIdByitem } = item || {};
                      return (
                        <div
                          key={index}
                          onClick={() => {
                            if (tenantIdByitem != tenantId) {
                              setTenantId(tenantIdByitem);
                              setBiz(null);
                              setOrg(null);
                              setUserList([]);
                            }
                          }}
                          className={classNames({
                            [styles.selectByBrandItem]: true,
                            [styles.selectByBrandItem_active]: tenantIdByitem == tenantId,
                          })}
                        >
                          {tenantName}
                          {tenantIdByitem == tenantId && <div className={styles.active_line}></div>}
                        </div>
                      );
                    })}
                </div>
                {/* 选择区域 */}
                <div
                  onClick={() => {
                    // area选择区域 clinic选择诊所 user选择参会人
                    setCurrentType('area');
                  }}
                  className={styles.selectByOption}
                >
                  <div className={styles.selectByLeft}>
                    <div className={styles.selectByAreaIcon}></div>
                    <div className={styles.selectByAreaText}>选择区域</div>
                  </div>
                  <div className={styles.selectByIcon}>
                    <RightOutlined style={{ fontSize: '15px', color: '#CCCCCC' }} />
                  </div>
                </div>
                <div className={styles.selectByLine}></div>
                {/* 选择机构 */}
                <div
                  onClick={() => {
                    // area选择区域 clinic选择诊所 user选择参会人
                    setCurrentType('clinic');
                  }}
                  className={styles.selectByOption}
                >
                  <div className={styles.selectByLeft}>
                    <div className={styles.selectByClinicIcon}></div>
                    <div className={styles.selectByAreaText}>选择机构</div>
                  </div>
                  <div className={styles.selectByIcon}>
                    <RightOutlined style={{ fontSize: '15px', color: '#CCCCCC' }} />
                  </div>
                </div>
                {/* 全部员工 */}
                <div className={styles.title_warp}>全部员工</div>
              </>
            }

            <div>
              <Spin
                spinning={
                  !!loading.effects['userInfoStore/attendeeSearchUserByQueryKey'] ||
                  !!loading.effects['userInfoStore/getAttendeeSearchUserByBizGroupId']
                }
              >
                {userlist && userlist.length > 0 ? (
                  <div className={styles.list_box}>
                    {userlist &&
                      userlist.length > 0 &&
                      userlist.map((item) => (
                        <div
                          onClick={() => selectHandle(item)}
                          className={styles.list_item}
                          key={item.friUserId}
                        >
                          <div className={styles.list_item_info_wrap}>
                            <div
                              className={classNames({
                                [styles.selectGuest_select]: true,
                                [styles.selectGuest_select_user]: !!thisSelectedGuest.find(
                                  (it) => it.friUserId === item.friUserId,
                                ),
                                [styles.selectGuest_Unselect_user]: !thisSelectedGuest.find(
                                  (it) => it.friUserId === item.friUserId,
                                ),
                              })}
                            ></div>
                            <div className={styles.list_item_img}>
                              {item.headUrlShow ? (
                                <img src={item.headUrlShow} alt="" />
                              ) : (
                                <div
                                  className={styles.no_comment_head}
                                  style={{ background: randomColor(item?.friUserId) }}
                                >
                                  {processNames(item?.name)}
                                </div>
                              )}
                            </div>
                            <div className={styles.list_item_info}>
                              <div className={styles.nameWarp}>
                                <div
                                  className={styles.name}
                                  dangerouslySetInnerHTML={{
                                    __html: item.highlightName ? item.highlightName : item.name,
                                  }}
                                ></div>
                                {item.isCfg == 1 && <div className={styles.bizUser}>企业</div>}
                              </div>
                              <div className={styles.phone}>{item.phone}</div>
                            </div>
                          </div>
                          {/*<div className={thisSelectedGuest.find(it => it.friUserId == item.friUserId)
                            ? styles.active_select : styles.init_select}
                               onClick={() => selectHandle(item)}
                          >
                            {thisSelectedGuest.find(it => it.friUserId == item.friUserId) ? '取消选择' : '选择'}
                          </div>*/}
                        </div>
                      ))}
                  </div>
                ) : null}
                {isHasData == 1 ? (
                  <div className={styles.nodata}>
                    <img src={noDataImg} alt="" />
                    <div className={styles.empty_title}>暂无该搜索结果</div>
                    <div className={styles.empty_msg}>请试试其他搜索关键词</div>
                  </div>
                ) : null}
              </Spin>
            </div>
          </div>
        )}

        {/* 选择区域 判定当前弹窗类型 area选择区域 clinic选择诊所 user选择参会人*/}
        {currentType == 'area' && (
          <div className={styles.box_area}>
            {Array.isArray(bizList) &&
              bizList.map((item) => {
                /*
                id: 256
                name: "华东区域"
                status: 1
                tenantId: "ba67e6cf30dc4f9c9c46adef188bbd04"
                type: 1
              */
                return (
                  <div>
                    <div
                      key={item.id}
                      onClick={() => {
                        setBiz(item.id);
                        setOrg(null);
                        setCurrentType('user');
                        updataByBizOrOrg(item.id)
                      }}
                      className={styles.selectByOption}
                    >
                      <div className={styles.selectByLeft}>
                        <div className={styles.selectByAreaText}>{item && item.name}</div>
                      </div>
                      <div className={styles.selectByIcon}>
                        <RightOutlined style={{ fontSize: '15px', color: '#CCCCCC' }} />
                      </div>
                    </div>
                    <div className={styles.selectByLine}></div>
                  </div>
                );
              })}
          </div>
        )}

        {/* 选择诊所 判定当前弹窗类型 area选择区域 clinic选择诊所 user选择参会人*/}
        {currentType == 'clinic' && (
          <div className={styles.box_area}>
            {Array.isArray(orgList) &&
              orgList.map((item) => {
                /*
                id: 1
                name: "瑞尔齿科深圳欢乐海岸诊所"
                status: 1
                tenantId: "ba67e6cf30dc4f9c9c46adef188bbd04"
                type: 2
              */
                return (
                  <div key={item.id}>
                    <div
                      onClick={() => {
                        setOrg(null);
                        setOrg(item.id);
                        setBiz(null);
                        setCurrentType('user');
                        updataByBizOrOrg(item.id)
                      }}
                      className={styles.selectByOption}
                    >
                      <div className={styles.selectByLeft}>
                        <div className={styles.selectByAreaText}>{item && item.name}</div>
                      </div>
                      <div className={styles.selectByIcon}>
                        <RightOutlined style={{ fontSize: '15px', color: '#CCCCCC' }} />
                      </div>
                    </div>
                    <div className={styles.selectByLine}></div>
                  </div>
                );
              })}
          </div>
        )}

        {/* 选择诊所 判定当前弹窗类型 area选择区域 clinic选择诊所 user选择参会人*/}
        {currentType == 'user' && (
          <div className={styles.box_user}>
            <Spin
              spinning={
                !!loading.effects['userInfoStore/attendeeSearchUserByQueryKey'] ||
                !!loading.effects['userInfoStore/getAttendeeSearchUserByBizGroupId']
              }
            >
              {userlist &&
                userlist.length > 0 &&
                userlist.map((item) => (
                  <div
                    onClick={() => selectHandle(item)}
                    className={styles.list_item}
                    key={item.friUserId}
                  >
                    <div className={styles.list_item_info_wrap}>
                      <div
                        className={classNames({
                          [styles.selectGuest_select]: true,
                          [styles.selectGuest_select_user]: !!thisSelectedGuest.find(
                            (it) => it.friUserId === item.friUserId,
                          ),
                          [styles.selectGuest_Unselect_user]: !thisSelectedGuest.find(
                            (it) => it.friUserId === item.friUserId,
                          ),
                        })}
                      ></div>
                      <div className={styles.list_item_img}>
                        {item.headUrlShow ? (
                          <img src={item.headUrlShow} alt="" />
                        ) : (
                          <div
                            className={styles.no_comment_head}
                            style={{ background: randomColor(item?.friUserId) }}
                          >
                            {processNames(item?.name)}
                          </div>
                        )}
                      </div>
                      <div className={styles.list_item_info}>
                        <div className={styles.nameWarp}>
                          <div
                            className={styles.name}
                            dangerouslySetInnerHTML={{
                              __html: item.highlightName ? item.highlightName : item.name,
                            }}
                          ></div>
                          {item.isCfg == 1 && <div className={styles.bizUser}>企业</div>}
                        </div>
                        <div className={styles.phone}>{item.phone}</div>
                      </div>
                    </div>
                    {/*<div className={thisSelectedGuest.find(it => it.friUserId === item.friUserId)
                      ? styles.active_select : styles.init_select}
                         onClick={() => selectHandle(item)}
                    >
                      {thisSelectedGuest.find(it => it.friUserId === item.friUserId) ? '取消选择' : '选择'}
                    </div>*/}
                  </div>
                ))}
              {isHasData == 1 ? (
                <div className={styles.nodata}>
                  <img src={noDataImg} alt="" />
                  <div className={styles.empty_title}>暂无该搜索结果</div>
                  <div className={styles.empty_msg}>请试试其他搜索关键词</div>
                </div>
              ) : null}
            </Spin>
          </div>
        )}
        <div className={styles.btn_wrap}>
          <div className={styles.tip}>
            <div className={styles.tip_box}>
              最多支持15个参会人自由发言或共享屏幕，其余参会人请发送邀请链接进入
            </div>
          </div>
          <div className={styles.btn_box}>
            <div className={styles.btn_wrap_left}>
              {Array.isArray(thisSelectedGuest) &&
                thisSelectedGuest.map((item) => {
                  return (
                    <div className={styles.item}>
                      {item.headUrlShow ? (
                        <img src={item.headUrlShow} alt="" />
                      ) : (
                        <div
                          className={styles.no_comment_head}
                          style={{ background: randomColor(item?.friUserId) }}
                        >
                          {processNames(item?.name)}
                        </div>
                      )}
                    </div>
                  );
                })}
            </div>
            <div className={styles.btn_wrap_right}>
              <div onClick={confirmHandle} className={styles.btn_submit}>
                确定
              </div>
            </div>
          </div>
        </div>
      </div>
    </Popup>
  );
};

export default connect(({ userInfoStore, loading }: any) => ({ userInfoStore, loading }))(Index);
