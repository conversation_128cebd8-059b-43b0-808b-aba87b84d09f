/**
 * @Description: 分享课件list弹窗
 */
import React, { useState } from 'react';
import { history, connect } from 'umi'
import { Modal } from 'antd-mobile';
import styles from './index.less';

// 图标图片
import ppt_icon from '@/assets/PlanetChatRoom/ppt_icon.png' // ppt图标
import word_icon from '@/assets/PlanetChatRoom/word_icon.png' // word文档图标
import pdf_icon from '@/assets/PlanetChatRoom/pdf_icon.png' // pdf图标

interface PropsType {
  visible: boolean,                // 弹窗是否显示
  onCancel: () => void,           // 点击取消的回调
  onOk: () => void,               // 点击确定的回调
}

// 课件数据
const fileListDataSource = [
  { id: 1, fileName: '啊手动滑稽阿是.ppt', fileType: 'ppt' },
  { id: 2, fileName: '啊萨达萨达.word', fileType: 'doc' },
  { id: 3, fileName: '而额外.pdf', fileType: 'pdf' },
]

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {
    visible,
    PlanetChatRoom,
    onCancel, // 点击取消的回调
    onOk, // 点击确定的回调
  } = props
  const {
    guestListInfo,
  } = PlanetChatRoom || {}

  const [checkedFileId, setCheckedFileId] = useState(null)   // 选中的文件ID

  // 选择文件
  const onClickFile = (fileId) => {
    setCheckedFileId(fileId)
  }

  // 返回icon
  const getIcon = (fileType) => {
    switch (fileType) {
      case 'ppt':
      case 'pptx':
        return ppt_icon
      case 'doc':
      case 'docx':
        return word_icon
      case 'pdf':
        return pdf_icon
      default:
        return word_icon
    }
  }

  return (
    <Modal
      visible={visible}
      className={styles.modal}
      destroyOnClose={true}
      closeOnMaskClick
      onClose={props.onCancel}
      getContainer={() => document.body}
      bodyStyle={{maxWidth: 400}}
      content={
        <div>
          {/* 标题 */}
          <div className={styles.header}>请选择您要分享的课件</div>

          {/* 课件list */}
          <div className={styles.file_list_wrap}>
            {
              fileListDataSource && fileListDataSource.length > 0 && fileListDataSource.map(item => {
                return (
                  <div key={item.id} className={styles.file_item_wrap} onClick={() => onClickFile(item.id)}>
                    <img src={getIcon(item.fileType)} width={29} height={35} style={{flexShrink: 0}} alt=""/>
                    <div className={styles.file_name}>{item.fileName}</div>
                  </div>
                )
              })
            }
          </div>

          {/* 按钮 */}
          <div className={styles.btn_wrap}>
            <div className={styles.btn_cancel} onClick={onCancel}>取消</div>
            <div className={styles.btn_ok} onClick={() => onOk(checkedFileId)}>确定</div>
          </div>
        </div>
      }
    />
  )
}
export default connect(({ PlanetChatRoom, loading }: any) => ({PlanetChatRoom, loading}))(Index)
