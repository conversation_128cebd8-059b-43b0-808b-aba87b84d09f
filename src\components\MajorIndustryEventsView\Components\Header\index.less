// 公共
.major_header {
  display: flex;
  overflow: auto;
  .month_item {
    display: inline-block;
    flex-shrink: 0;
    color: #666666;
    border-bottom: 4px solid transparent;
  }
  .active {
    border-bottom: 4px solid #5391EA;
    color: #000000;
    font-weight: 600;
  }
  
}

//手机端
.major_mobile_header {
  padding: 12px 12px 0;
  margin-bottom: 12px;
  .month_item {
    margin-right:16px;
    font-size: 18px;
    padding-bottom: 8px;
    
  } 
  .small_year {
    font-size: 14px;
  }
}

//pc端
.major_pc_header {
  padding: 12px 12px 0;
  margin-bottom: 16px;
  .month_item {
    margin-right:30px;
    font-size: 20px;
    padding-bottom: 14px;
    cursor: pointer;
    
  } 
  .small_year {
    font-size: 16px;
  }
}