.spin {
  height: 100%;
  :global {
    .ant-spin-container {
      height: 100%;
    }
  }
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #eef3f9;
}

.content_wrap {
  flex: 1;
  overflow-y: auto;
  margin-top: 16px;
  padding-bottom: 16px;
  .content_inner {
    width: 1212px;
    min-height: 100%;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
  }
}

.crumbs {
  font-size: 14px;
  color: rgba(0,0,0,0.45);
  line-height: 20px;
  margin-bottom: 15px;
  i{
    display: inline-block;
    position: relative;
    font-style: normal;
    margin-right: 8px;
    padding-left: 18px;
    color:#4292FF;
    cursor: pointer;
    &::before{
      content: '';
      width: 100%;
      height: 100%;
      position: absolute; /* 设置伪元素为绝对定位 */
      top: 0;
      left: -26px;
      background-image: url('../../../assets/GlobalImg/e_arrow.png');
      background-size: 20px;
      transform: rotate(180deg);
      background-repeat: no-repeat;
    }
  }
  span {
    color: #000;
  }
}

.headerContent {
  width: 100%;
  background: #fff;
  border-radius: 6px 6px 6px 6px;
  padding: 16px 24px;
  box-sizing: border-box;
  position: relative;
  margin-bottom: 16px;

  .headerBox {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    // justify-content: flex-start;

    .headerTitle {
      font-size: 20px;
      font-weight: 600;
      color: #000000;
      line-height: 28px;
      margin-right: 8px;
      flex-shrink: 0;
    }

    .lines {
      width: 1px;
      height: 9px;
      background: #D8D8D8;
      margin-right: 8px;
      position: relative;
      top: 2px;
    }

    .difficult1 {
      font-size: 12px;
      font-weight: 400;
      line-height: 14px;
      padding: 4px;
      background: #E6FBF3;
      color: #00D78B;
    }
    .difficult2 {
      font-size: 12px;
      font-weight: 400;
      line-height: 14px;
      padding: 4px;
      background: #FFF8EC;
      color: #E39D16;
    }
    .difficult3 {
      font-size: 12px;
      font-weight: 400;
      line-height: 14px;
      padding: 4px;
      background: #FFF4E9;
      color: #FF921F;
    }
    .difficult4 {
      font-size: 12px;
      font-weight: 400;
      line-height: 14px;
      padding: 4px;
      background: #FCE9E8;
      color: #FF5F57;
    }

    .headerLable {
      display: flex;
      margin-left: 8px;
      align-items: center;

      .headerBlueLable {
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #1A8CFF;
        line-height: 14px;

        span {
          padding: 2px 4px;
          box-sizing: border-box;
          background: #EBF5FF;
          border-radius: 2px 2px 2px 2px;
          margin-right: 6px;
        }
      }

      .headerYellowLable {
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #D3A221;
        line-height: 14px;
        padding: 2px 4px;
        box-sizing: border-box;
        background: #FFF7E2;
        border-radius: 2px 2px 2px 2px;
      }
    }
  }

  .headerDoctor {
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    line-height: 16px;
    margin-bottom: 8px;

    span {
      box-sizing: border-box;
      position: relative;
    }
  }

  .headerKeyWord {
    font-size: 14px;
    font-weight: 400;
    color: #666;
    line-height: 15px;
    display: flex;


    .keyWordText {
      flex-shrink: 0;
      position: relative;
      top: 2px;
    }

    .keyWordStyle {
      display: flex;
      flex-wrap: wrap;

      span {
        background: #F5F5F5;
        border-radius: 4px 4px 4px 4px;
        padding: 2px 4px;
        box-sizing: border-box;
        margin-right: 6px;
        margin-bottom: 6px;
      }
    }
  }

  .headerDifficulty {
    position: absolute;
    right: 24px;
    top: -5px;

    img {
      width: 40px;
      height: auto;
    }
  }

  .solutionStyle {
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    line-height: 22px;
    word-break: break-all;

    span {
      display: inline-block;
      width: 50px;
      height: auto;
      margin-right: 10px;
      position: relative;
      top: -2px;

      img {
        width: 100%;
        height: auto;
      }
    }
  }
}

.details_content {
  width: 100%;
  height: auto;
  background: #fff;
  border-radius: 8px;
  padding-bottom: 82px;
  margin-bottom: 16px;
  :global {
    .ant-affix {
      background: #eef3f9;
    }
    .ant-anchor-wrapper {
      width: 100%;
      padding-top: 24px;
      padding-bottom: 12px;
      margin-left: 0;
      padding-left: 0;
      border-bottom: 1px solid rgba(0,0,0,0.06);
      background: #fff;
      border-radius: 8px 8px 0 0;
    }
    .ant-anchor-ink {
      display: none;
    }
    .ant-anchor {
      display: flex;
      padding-left: 0;
      .ant-anchor-link {
        padding: 0 0 0 16px;
      }
      .ant-anchor-link-title {
        padding: 0 8px;
        height: 26px;
        line-height: 28px;
        color: rgba(0,0,0,045);
        border-radius: 2px;
      }
    }
    .ant-anchor-link-active {
      .ant-anchor-link-title-active {
        color: #4292FF;
        font-weight: 500;
        background: rgba(66,146,255,0.12);
      }
    }
  }
}

.box {
  width: 100%;

  .firstTitle {
    padding-top: 12px;
    width: 100px;
    font-size: 18px;
    font-weight: 500;
    color: #000;
    line-height: 25px;
    margin-bottom: 24px;
    margin-left: 28px;
  }


  .contentInfo {
    width: 100%;
    display: flex;
  }

  .content {
    width: 100%;

    .topContent {
      width: 100%;
      display: flex;
    }

    .contentTitle {
      width: 100px;
      text-align: right;
      font-size: 14px;
      font-weight: 400;
      color: #999999;
      line-height: 16px;
      margin-right: 16px;
      flex-shrink: 0;
    }

    .contentLists {
      flex: 1;
    }

    .imgsContent {
      display: flex;
      flex-wrap: wrap;

      .imgBox {

        .imgName {
          font-size: 14px;
          font-weight: 400;
          color: #999999;
          line-height: 24px;
          margin-bottom: 16px;

          .imgIconStyle {
            width: 16px;
            height: 16px;
            margin: 0;
            margin-right: 6px;
            position: relative;
            top: -1px;
          }
        }
      }

      img {
        width: 340px;
        height: auto;
        border-radius: 8px 8px 8px 8px;
        margin-right: 16px;
        margin-bottom: 12px;
      }
    }

    .imgContentText {
      font-size: 15px;
      font-weight: 400;
      color: #000;
      line-height: 18px;
      margin-bottom: 16px;
      white-space: pre-wrap;
      padding-right: 44px;
      box-sizing: border-box;
      word-break: break-all;
    }

    .contentText {
      font-size: 15px;
      font-weight: 400;
      color: #000;
      line-height: 18px;
      margin-bottom: 16px;
      padding-right: 44px;
      box-sizing: border-box;
      word-break: break-all;
    }

    .secondaryTitle {
      font-size: 16px;
      font-weight: 500;
      color: #000;
      line-height: 24px;
      margin-bottom: 16px;
      padding-right: 44px;
      box-sizing: border-box;
      word-break: break-all;
    }
  }

  .leftTitle {
    font-size: 15px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #000;
    line-height: 18px;
    margin-bottom: 20px;
    width: 100%;
    display: flex;
    padding-right: 44px;
    box-sizing: border-box;
    word-break: break-all;

    span {
      display: inline-block;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #999999;
      line-height: 16px;
      width: 100px;
      text-align: right;
      margin-right: 16px;
      flex-shrink: 0;
    }

    .rightText {
      flex: 1;
    }
  }
}

.vip_wrap {
  width: 100%;
  height: 310px;
  margin-bottom: 24px;
  position: relative;

  .vip_forbidden_wrap {
    width: 100%;
    height: 310px;
    overflow: hidden;
  }

  .vip_shade_wrap {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;

    .vip_shade_title {
      width: 100%;
      height: 56px;
      background: linear-gradient(180deg, rgba(255,255,255,0.9) 0%, #FFFFFF 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: 400;
      color: #BE7D1B;
      line-height: 24px;
      cursor: pointer;

      .vip_shade_icon {
        width: 16px;
        height: 16px;
        position: relative;
        margin-left: 4px;
      }
    }

    .vip_modal_content {
      width: 100%;
      height: auto;
      background: #fff;
      display: flex;
      justify-content: center;

      .vip_shade_content {
        width: 327px;
        height: 214px;
        background: #FFF9EA;
        border-radius: 8px;
        padding: 0 25px;

        .vip_shade_deblocking_title {
          width: 100%;
          height: 48px;
          display: flex;
          align-items: center;
          justify-content: center;

          .vip_shade_deblocking_text {
            font-size: 12px;
            font-weight: 400;
            color: #AD7D5A;
            line-height: 24px;
            margin: 0 8px;
          }

          .vip_shade_deblocking_icon {
            width: 44px;
            height: 4px;
          }
        }

        .vip_shade_deblocking_List {
          width: 100%;

          .vip_deblocking_content {
            width: 100%;
            display: flex;
            flex-wrap: wrap;

            .vip_deblocking_item {
              width: 50%;
              flex-shrink: 0;
              margin-bottom: 12px;
              display: flex;
              align-items: center;

              .vip_deblocking_text {
                font-size: 13px;
                font-weight: 400;
                color: #6B3B19;
                line-height: 24px;
              }

              .vip_deblocking_icon {
                width: 20px;
                height: 20px;
                margin-right: 8px;
              }
            }
          }
        }

        .vip_deblocking_btn_wrap {
          padding: 4px 4px 0px;

          .vip_button_style {
            width: 100%;
            height: 38px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: linear-gradient(158deg, #FDF0C2 0%, #F5D18A 100%);
            border-radius: 42px;
            cursor: pointer;

            .vip_button_text {
              font-size: 16px;
              font-weight: 500;
              line-height: 19px;
              background-image: -webkit-linear-gradient(-90deg, #E8975D 0%, #7D461E 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
        }
      }
    }
  }
}

.nologin_wrap {
  width: 100%;
  height: 150px;
  margin-bottom: 24px;
  position: relative;

  .vip_forbidden_wrap {
    width: 100%;
    height: 150px;
    overflow: hidden;
  }

  .vip_shade_wrap {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;

    .vip_shade_title {
      width: 100%;
      height: 50px;
      background: linear-gradient(180deg, rgba(255,255,255,0.92) 0%, #FFFFFF 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: 400;
      color: #BE7D1B;
      line-height: 24px;
      cursor: pointer;

      .vip_shade_icon {
        width: 16px;
        height: 16px;
        position: relative;
        margin-left: 4px;
      }
    }

    .vip_modal_content {
      width: 100%;
      height: 50px;
      background: #fff;
    }
  }
}

.right_content {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(440px, -50%);
  z-index: 101;

  .banner_window_wrap {
    width: 159px;
    height: 107px;
    position: relative;
    right: 16px;
    background: #fff;
    border-radius: 8px;

    .banner_window_img {
      width: 159px;
      height: 107px;
      cursor: pointer;
    }

    .banner_window_icon {
      position: absolute;
      right: 0;
      top: -10px;

      .banner_window_close {
        width: 20px;
        height: 20px;
        cursor: pointer;
      }
    }
  }

  .show_window_wrap {
    width: 100%;
    height: 100px;
    display: none;
    cursor: pointer;
    transform: translateX(135px);

    .show_window_icon {
      width: 22px;
      height: 100px;
    }
  }
}

// 评论区
.comment_wrap {
  background: #fff;
  border-radius: 6px;
  padding: 16px 24px 16px;
  .comment_title {
    font-size: 20px;
    color: #000;
    line-height: 28px;
    font-weight: 600;
    margin-bottom: 16px;
  }
  .post_comment_input, .reply_comment_input {
    margin-bottom: 8px;
    :global {
      .ant-input-textarea {
        position: relative;
      }
      .ant-input-textarea-show-count::after {
        color: #ccc;
        position: absolute;
        right: 20px;
        bottom: 4px;
        font-size: 12px;
      }
      .ant-input-textarea-show-count > .ant-input {
        resize: none;
        padding: 8px 12px 12px;
      }
    }
  }
  .post_comment_btn, .reply_comment_btn {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 24px;
    :global {
      .ant-btn {
        height: 36px;
        padding: 8px 31px;
      }
    }
  }
  .comment_list {
    .comment_item {
      display: flex;
      flex-wrap: nowrap;
      margin-bottom: 24px;
      .comment_item_right {
        flex: 1;
        .right_user_name {
          margin-bottom: 4px;
          font-size: 14px;
          color: #999;
          line-height: 20px;
        }
        .right_details_item {
          display: flex;
          flex-wrap: nowrap;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 8px;
          .right_details_item_btn {
            font-size: 14px;
            color: #0095FF;
            line-height: 20px;
            white-space: nowrap;
            flex-shrink: 0;
            margin-left: 30px;
            cursor: pointer;
            margin-top: 10px;
          }
          .right_details_item_comment {
            word-break: break-all;
            font-size: 16px;
            color: #333;
            line-height: 22px;
          }
          .right_details_item_reply {
            word-break: break-all;
            font-size: 15px;
            color: #999;
            line-height: 21px;
            padding: 7px 15px 6px 8px;
            border-radius: 4px;
            background: #FAFAFA;
            & > span {
              color: #0095FF;
            }
          }
        }
        .reply_comment_btn {
          margin-bottom: 0;
        }
      }
    }
  }
}
