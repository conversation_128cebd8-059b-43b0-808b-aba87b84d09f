/**
 * @Description: 创建王国成功页面
 */
import React from 'react'
import { history, connect } from 'umi'
import styles from './index.less'
import SuccessIcon from '@/assets/GlobalImg/success.png'
import CreateKingdomOrSpace from '../CreateKingdomOrSpace'
import NavBar from '@/components/NavBar'
import { Toast } from 'antd-mobile'

const Index: React.FC = (props: any) => {
  const { dispatch, userInfoStore } = props;
  const { selectedKing, isSuperAccount } = userInfoStore;

  const kingdomInfoData = JSON.parse(localStorage.getItem('kingdomInfoData') || '{}'); // 获取王国相关信息
  const isSuperAccountLocal = JSON.parse(localStorage.getItem('isSuperAccount') || 'false'); // 是否为超级账号 true是 false不是（默认false）
  const { kingName } = kingdomInfoData;

  // 创建王国空间
  const createSpaceBtnFn = () => {
    const { id } = history.location.query || {};
    console.log(id, 'id？？')
    // 核验是否为超级账号
    checkSuperAccount();



    const options = {
      id: '王国空间',
      type: 2,
      title: '创建直播',
      goBackType: '', // 返回（99创建王国空间）
      isShowGoBack: 1, // 是否有返回箭头 1没有
      isSelectKingDom: 1, // 是否可以选择王国 1没有
    }
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        createModalVisible: false,
        creatTabSelectDate: options,
        spaceFromEnter: {
          isCureentKingdom: true // 是否是当前指定王国
        },
        selectedKingdomAudience: [], // 指定当前王国所有成员，并将当前王国的id保存到store中
        selectedKingdom: kingdomInfoData ? {
          name: kingdomInfoData.name, // 王国名称
          id: kingdomInfoData.id, // 王国id
          kingImgUrlShow: kingdomInfoData.kingdomCoverUrlShow || kingdomInfoData.kingImgUrlShow, // 头像
          wxUserId: kingdomInfoData.wxUserId, // 国王id
          kingName: kingdomInfoData.kingName, // 国王
          comeType: 'kingdom', // 从哪来的标识
        } : null,  // 关联的国王信息
      }
    })

    // 前往创建直播页面 并已关联王国
    history.push(`/CreateSpace/Live`);
  }

  // 是否为超级账号
  const checkSuperAccount = () => {
    dispatch({
      type: 'userInfoStore/checkSuperAccount',
    }).then(res => {
      if(res && res.code == 200) {
        localStorage.setItem('isSuperAccount', res.content) // true是，false否
        dispatch({
          type: 'userInfoStore/setTaskListState',
          payload: {
           isSuperAccount: res.content
          }
        })
      } else {
        Toast.show({content: '数据加载失败'})
      }
    }).catch((err:any) => {
      console.log(err)
    })
  }

  // 进入王国
  const jumpKingdom = () => {
    const { id } = history.location.query || {};
    history.replace(`/Kingdom/${id}`)
  }

  return (
    <>
      <div className={styles.container}>
        <NavBar title={'创建成功'} ></NavBar>
        <div className={styles.icon_box}>
          <img src={SuccessIcon} width={72} height={72} alt=""/>
        </div>
        <div className={styles.title}>恭喜您成功{(isSuperAccount || isSuperAccountLocal) && (selectedKing || kingName) ? `为${selectedKing?.kingName || kingName}` : ''}创建王国!</div>
        <div className={styles.message}>小贴士：在王国内创建直播，可以为自己的王国积攒人气GDP，GDP越高，王国越强</div>
        <div className={styles.btn_box}>
          <div className={styles.btn_left} onClick={jumpKingdom}>进入王国</div>
          <div className={styles.btn_right} onClick={createSpaceBtnFn}>创建直播</div>
        </div>
      </div>
      <CreateKingdomOrSpace comeType={2} />
    </>
  )
}

export default connect(({ userInfoStore, loading }: any) => ({userInfoStore, loading}))(Index)
