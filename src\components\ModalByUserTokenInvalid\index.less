.warp_UserTokenInvalid {
  position: relative;
  z-index: 999;
}

.WarnModalWarp_NoRem {

  .Download_Documents {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
    color: #0095FF;
    user-select: none;
    cursor: pointer;
    font-size: 14PX;
  }

  :global {
    .adm-modal-content {
      padding: 0px 10PX 16PX;
    }

    .adm-modal-body:not(.adm-modal-with-image) {
      padding-top: 32PX;
    }
  }
  .WarnModal {
    .WarnModalTitle {
      display: flex;
      justify-content: center;

      .SpatialDetail_modal_warn_icon {
        width: 24PX;
        height: 24PX;
        background: url('../../assets/PlanetChatRoom/SpatialDetail_modal_warn_icon.png') no-repeat;
        background-size: contain;
        display: inline-block;
        position: relative;
        top: -2PX;
        margin-right: 4PX;
      }

      .SpatialDetail_modal_warn_title {
        max-width: 230PX;
        font-size: 17PX;
        font-weight: 500;
        color: #000000;
        line-height: 20PX;
        text-align: center;
      }

      .SpatialDetail_modal_title {
        margin-right: 15PX;
        font-size: 17PX;
        font-weight: 500;
        color: #000000;
        line-height: 20PX;
        text-align: center;
      }
    }

    .SpatialDetail_modal_desc {
      width: 100%;
      min-width: 230PX;
      font-size: 14PX;
      font-weight: 400;
      color: #666666;
      line-height: 16PX;
      margin-top: 10PX;
      text-align: center;
    }

    .WarnModalBtnWarp {
      display: flex;
      margin-top: 17PX;
      justify-content: center;
      align-items: center;

      .CancelBtn {
        width: 108PX;
        height: 37PX;
        background: #EDF9FF;
        border-radius: 20PX 20PX 20PX 20PX;
        opacity: 1;
        line-height: 37PX;
        text-align: center;
        font-size: 15PX;
        font-weight: 400;
        color: #0095FF;
        margin-right: 14PX;
        cursor: pointer;
        user-select: none;
      }

      .CancelBtn:active {
        background: #aeb7bc;
      }

      .EnterBtn {
        width: 108PX;
        height: 37PX;
        background: #0095FF;
        border-radius: 20PX 20PX 20PX 20PX;
        opacity: 1;
        line-height: 37PX;
        text-align: center;
        font-size: 15PX;
        font-weight: 400;
        color: #FFFFFF;
        cursor: pointer;
        user-select: none;
      }

      .EnterBtn:active {
        background: #0170c0;
      }
    }
  }
}
