@import '~@/utils/imageText.less';

.container {
  margin-bottom: 10px;
  padding: 16px 16px;
  background: #fff;
  .title {
    font-size: 15px;
    color: #222;
    font-weight: 500;
    line-height: 21px;
    word-break: break-all;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /* 指定显示2行 */
    overflow: hidden;
    :global(.username_in_title) {
      color: #0095FF;
      margin-right: 4px;
    }
  }
  .kingdom_box {
    display: inline-flex;
    width: auto;
    height: 19px;
    align-items: center;
    border-radius: 4px;
    background: #E4F0FC;
    padding: 0 4px;
    & > i {
      width: 14px;
      height: 14px;
      background: url("../../assets/GlobalImg/blue_associated.png") no-repeat center;
      background-size: 100% 100%;
      margin-right: 4px;
    }
    & > span {
      font-size: 11px;
      color: #0095FF;
    }
  }
  .article_user {
    display: flex;
    align-items: flex-start;
    .name {
      font-size: 12px;
      color: #000;
      line-height: 17px;
    }
    .icon {
      width: 14px;
      height: 14px;
      background: url("../../assets/GlobalImg/gdp.png") no-repeat center;
      background-size: 100% 100%;
      margin-right: 4px;
    }
    & > span {
      margin-right: 8px;
      font-size: 12px;
      color: #666;
      line-height: 17px;
    }
  }

}

.container.vertical {
  .title {
    margin-bottom: 8px;
  }
  .cover_img_box {
    display: flex;
    flex-wrap: nowrap;
    column-gap: 4px;
    margin-bottom: 8px;
    .cover_img_item {
      flex: 1;
      max-width: 33.3333%;
      height: 80px;
      //background-repeat: no-repeat;
      //background-position: center;
      //background-size: cover;
      &:first-child {
        border-radius: 4px 0 0 4px;
      }
      &:last-child {
        border-radius: 0 4px 4px 0;
      }
      img{
        width: 100%;
        height: 100%;
        border-radius: 4px;
        object-fit: cover;
      }
    }
  }
  .kingdom_box {
    margin-bottom: 8px;
  }
}

&.container.horizontal {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  .article_left {
    flex: 1;
    min-height: 86px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    .title {
      padding-right: 5px;
    }
  }
  .article_right {
    flex-shrink: 0;
    width: 122px;
    height: 86px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    border-radius: 4px;
    overflow: hidden;

    .article_img {
      width: 100%;
      height: 100%;
      border-radius: 4px;
      object-fit: cover;
    }
  }
}
