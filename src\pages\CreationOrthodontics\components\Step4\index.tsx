import React, {useState, useEffect, useRef} from 'react';
import { history,connect } from 'umi';
import styles from "./index.less";
import {Form, Input} from "antd";
const  { TextArea } = Input;
import {
  getFieldDecoratorByitemByLv2,
  scrollToTop,
  setFormValues
} from "@/pages/CreationOrthodontics/components/CreationFormUtils";
import {stringify} from "qs";
import {useDebounce, getArrailUrl} from "@/utils/utils";
const firstQuestionField = [
  {
    caseId: 'firstQuestion',
    dictCode: "10",
    dictId: 6,
    dictName: "提问",
    fileName: null,
    fileSize: null,
    fileSuffix: null,
    fileUrl: null,
    fileUrlShow: null,
    id: 'firstQuestion-id',
    inputContent: null,
    isCheck: null,
    isDel: 0,
    level: 1,
    operateType: 0,
    parentId: null,
    sortNum: 10,
    toothPosition: null,
    topId: 6,
    unit: null,
    subsetList: [
      {
        caseId: 'firstQuestion-sub',
        dictCode: "1",
        dictId: 'firstQuestion-sub-dictId',
        dictName: "描述问题",
        fileName: null,
        fileSize: null,
        fileSuffix: null,
        fileUrl: null,
        fileUrlShow: null,
        id: 'firstQuestion-sub-id',
        inputContent: "描述问题",
        isCheck: 0,
        isDel: 0,
        level: 2,
        operateType: 2,
        parentId: 6,
        sortNum: 1,
        subsetList: null,
        toothPosition: null,
        topId: 6,
        unit: null,
      }
    ]
  }
]

const Step4: React.FC = (props) => {
  // 是否嵌套在5i5ya的iframe中
  const isInIframe = (self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya')

  const [ form] = Form.useForm();
  const { CreationOrthodontics, dispatch } = props || {}
  const { medicalRecordJson,DictionaryData,DataBymedicalRecordJson } = CreationOrthodontics || {}  // 正畸病例字典结构
  const {
    id:consultationId,       // :consultationId 8dad6e1a68714dc7b1f908902ad2d4f4
    customerId,              // 客户id
    createUserId,            // 创建人id
    tenantId,                // 租户id
    orderCaseTemplate,       // "orderCaseTemplate": 0, -- 订单病例模板 1通用病例 2正畸病例
    type,             // "type": 0, --会诊类型(1图文、2视频,3审核)
  } = DictionaryData || {}

  let isNotRequired = orderCaseTemplate == 2 ? true : false;

  // 治疗目标
  const checkJson9  = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item)=>{return item.dictCode == '9'})

  // 治疗方案
  const checkJson10  = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item)=>{return item.dictCode == '10'})

  useEffect(()=>{scrollToTop()},[]);

  const onFormLayoutChange = useDebounce((value) => {
    let valuesByForm = form.getFieldsValue();
    onFinish(valuesByForm);
  },3000);

  const echoFormValue = ()=>{
    if (checkJson9 && checkJson10) {
      let FormArr = [].concat(checkJson9.subsetList).concat(checkJson10.subsetList)
      setFormValues(FormArr,form,isNotRequired)
    }
    const { consultationCaseInfoDto } = DictionaryData || {}
    form.setFieldsValue({
     'firstQuestion-sub-id': consultationCaseInfoDto && consultationCaseInfoDto.firstQuestion,
    })
  }

  useEffect(() => {},[])

  useEffect(() => {
    echoFormValue()
  },[DataBymedicalRecordJson])

  const onFinish = _.debounce((value,isSubmitLoading)=>{
    const { errorFields } = value || {}
    if (Array.isArray(errorFields) && errorFields.length > 0) { return }

    let formDataArr = Object.keys(value).map((key) => {
      let inputContent = value[key];
      // 判定当前是否是字符串
      if (typeof inputContent == 'string' && inputContent.length > 200) {
        inputContent = inputContent.substring(0, 200);
      }
      return { id: key,  inputContent: inputContent }
    })

    let findByformDataArr = formDataArr.find((item)=>{
      return item.id == 'firstQuestion-sub-id'
    })
    dispatch({
      type: 'CreationOrthodontics/saveDataByMedicalRecordJson',
      payload: {
        processNode:4,         // 治疗方案
        formDataArr:formDataArr,
        isSubmit:isSubmitLoading,
        firstQuestion: findByformDataArr && findByformDataArr.inputContent, // 专家会诊-提问-描述问题
      }
    }).then(()=>{
      if (isSubmitLoading) {
        // 在5i5ya的iframe中
        if (isInIframe) {
          let postData = {
            dataType: 'pathname',       // 页面地址onchange事件
            pathnameByChild: '/CreationOrthodontics/Step5',  // 路由信息
            searchByChild: `?${stringify(history.location.query)}`,  // 路由信息
          }
          console.log('子级发送数据：', postData, getArrailUrl())
          window.parent.postMessage(postData, getArrailUrl())
          return
        }

        history.replace(`/CreationOrthodontics/Step5?${stringify(history.location.query)}`)
      }
    })
  },500)



  // 问题清单
  // getFieldDecoratorBy
  const getFieldDecoratorByProblems = ()=>{
    return (
      <>
        {checkJson9 && checkJson10 && [checkJson9,checkJson10].map((itemByLv1) => {
          return (
            <div style={{marginBottom:'30px'}}>
              <div className={styles.title_span}>
                {!isNotRequired &&
                  <span style={{color:'red'}}>*</span>
                }
                {itemByLv1.dictName}：</div>
              {Array.isArray(itemByLv1.subsetList) && itemByLv1.subsetList.map((itemByLv2) => {
                return getFieldDecoratorByitemByLv2({
                  item:itemByLv2,
                  form:form,
                  onChange:onFormLayoutChange,
                  isNotRequired:isNotRequired,
                })
              })}
            </div>
          )
        })}

        {/* 提问-描述问题 */}
        {type != 3 && firstQuestionField.map((itemByLv1) => {
          return (
            <div style={{marginBottom:'30px'}}>
              <div className={styles.title_span}><span style={{color:'red'}}>*</span> {itemByLv1.dictName}：</div>
              {Array.isArray(itemByLv1.subsetList) && itemByLv1.subsetList.map((itemByLv2) => {
                return getFieldDecoratorByitemByLv2({
                  item:itemByLv2,
                  form:form,
                  onChange:onFormLayoutChange,
                })
              })}
            </div>
          )
        })}

      </>
    )
  }

  return (
    <div className={styles.page_info}>
      <div className={styles.warp_content}>
        {/* 标题 */}
        <div className={styles.title_box}>
          <div>治疗方案</div>
        </div>

        {/* 内容 */}
        <Form
          form={form}
          initialValues={{}}
          onValuesChange={onFormLayoutChange}
          onFinish={(value)=>{onFinish(value,true)}}
          onFinishFailed={onFinish}
        >
          <div className={styles.content_warp}>
            {getFieldDecoratorByProblems()}
          </div>
        </Form>

        {/* 上一步下一步 */}
        <div className={styles.submitWarp}>
          <div className={styles.submitBox}>
            <div
              onClick={()=>{
                if (form) {
                  let value = form.getFieldsValue()
                  onFinish(value,false);

                  // 在5i5ya的iframe中
                  if (isInIframe) {
                    const postData = {
                      dataType: 'goBack',       // 页面地址onchange事件
                    }
                    console.log('子级发送数据：', postData, getArrailUrl())
                    window.parent.postMessage(postData, getArrailUrl())
                    return
                  }
                  // history.goBack();
                  history.replace(`/CreationOrthodontics/Step3?${stringify(history.location.query)}`)
                }
              }} className={styles.submit_btn_Cancel}>上一步</div>
            <div
              onClick={()=>{
                if (form) {
                  form.submit()
                }
              }}
              className={styles.submit_btn_Enter}>下一步</div>
          </div>
        </div>

      </div>
    </div>
  )
}

export default connect(({ CreationOrthodontics,pcAccount, loading }: any) => ({
  CreationOrthodontics,pcAccount, loading
}))(Step4)
