import React from 'react'
import { history } from 'umi'
import classNames from 'classnames'
import { stringify } from 'qs'
import PropTypes from 'prop-types'
import { Upload } from 'antd'
import { Toast } from 'antd-mobile'
import { getHeaders } from '@/utils/utils'
import styles from './index.less'
import Emoji from './Emoji'
import toolbar_emoji_icon from '@/assets/GlobalImg/toolbar_emoji.png';
import toolbar_emoji_active_icon from '@/assets/GlobalImg/toolbar_emoji_active.png';
import toolbar_font_icon from '@/assets/GlobalImg/toolbar_font.png';
import toolbar_font_active_icon from '@/assets/GlobalImg/toolbar_font_active.png';

class Index extends React.Component {
  static propTypes = {
    quillRef: PropTypes.object,                            // 编辑器ref
    quillHistoryStack: PropTypes.object,                   // 编辑历史记录
    quillFormat: PropTypes.object,                         // 编辑格式
    toolbarPanelOnChange: PropTypes.func,                  // 工具栏面板打开或关闭
    createType: PropTypes.string,                          // 来自于哪个页面
    handleUploadImage: PropTypes.func,                     // （发帖子页专用）上传图片回调
    getUploadVideoOrImageLoading: PropTypes.func,          // 上传视频或图片时loading状态变化的回调
  }
  static defaultProps = {
    quillRef: {},
    quillHistoryStack: {},
    quillFormat: {},
    toolbarPanelOnChange: () => {},
    createType: 'createArticle',
    handleUploadImage: () => {},
    getUploadVideoOrImageLoading: () => {},
  }

  constructor(props) {
    super(props)
    this.state = {
      toolbarValue: null,              // 工具栏tab
    }
  }

  componentDidMount() {
    // 监听页面聚焦、失焦事件
    document.body.addEventListener('focusin', this.focusinHandler)
    // document.body.addEventListener('focusout', this.focusoutHandler)
  }

  componentWillUnmount(): void {
    document.body.removeEventListener('focusin', this.focusinHandler)
    // document.body.removeEventListener('focusout', this.focusoutHandler)
  }

  // 页面可输入元素聚焦事件
  focusinHandler = () => {
    console.log('focusinHandler')
    this.setState({
      toolbarValue: null,              // 工具栏tab
    })
    this.props.toolbarPanelOnChange(false)
  }

  // 页面可输入元素失焦事件
  // focusoutHandler = () => {
  //   console.log('focusoutHandler')
  // }

  // 点击底部工具栏
  toolbarOnChange = (value) => {
    if (value == this.state.toolbarValue) {
      this.setState({
        toolbarValue: null,
      })
      this.props.toolbarPanelOnChange(false)
    } else {
      this.setState({
        toolbarValue: value,
      })
      this.props.toolbarPanelOnChange(true)
    }
  }

  // 选择话题
  goToSelectTopic = () => {
    setTimeout(() => {
      history.push('/CreateGraphicsText/SelectTopic?selectTopicType=2')
    }, 150)
  }

  // 撤销
  undoFn = () => {
    if (!this.props.quillHistoryStack.undo || this.props.quillHistoryStack.undo.length == 0) {
      return
    }
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.undoFn) {
      this.props.quillRef.current.undoFn()
    }
  }

  // 重做
  redoFn = () => {
    if (!this.props.quillHistoryStack.redo || this.props.quillHistoryStack.redo.length == 0) {
      return
    }
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.redoFn) {
      this.props.quillRef.current.redoFn()
    }
  }

  // 设置字体大小
  fontSizeFn = (value) => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.fontSizeFn) {
      this.props.quillRef.current.fontSizeFn(value)
    }
  }

  // 加粗
  boldFn = () => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.boldFn) {
      this.props.quillRef.current.boldFn()
    }
  }

  // 倾斜
  italicFn = () => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.italicFn) {
      this.props.quillRef.current.italicFn()
    }
  }

  // 删除线
  strikethroughFn = () => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.strikethroughFn) {
      this.props.quillRef.current.strikethroughFn()
    }
  }

  // 下划线
  underlineFn = () => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.underlineFn) {
      this.props.quillRef.current.underlineFn()
    }
  }

  // 对齐方式
  alignFn = (value) => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.alignFn) {
      this.props.quillRef.current.alignFn(value)
    }
  }

  // 文字颜色
  fontColorFn = (value) => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.fontColorFn) {
      this.props.quillRef.current.fontColorFn(value)
    }
  }

  // 添加表情
  emojiFn = (emojiFileName) => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.emojiFn) {
      this.props.quillRef.current.emojiFn(emojiFileName, this.openEmojiPanel)
    }
  }

  // 添加话题
  topicFn = (value) => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.topicFn) {
      this.props.quillRef.current.topicFn(value)
    }
  }

  // 选择表情回调
  emojiOnClick = (emojiFileName) => {
    this.emojiFn(emojiFileName);
  }

  // 打开面板
  openEmojiPanel = () => {
    this.setState({
      toolbarValue: 2,
    })
    this.props.toolbarPanelOnChange(true)
  }

  // 添加图片
  imageFn = (value) => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.imageFn) {
      this.props.quillRef.current.imageFn(value)
    }
  }

  // 添加图片进度条
  imageProgressFn = (value) => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.imageProgressFn) {
      this.props.quillRef.current.imageProgressFn(value)
    }
  }

  // 图片进度条改变
  imageProgressChangeFn = (value) => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.imageProgressChangeFn) {
      this.props.quillRef.current.imageProgressChangeFn(value)
    }
  }

  // 删除进度条
  imageProgressDeleteFn = (value) => {
    if (this.props.quillRef && this.props.quillRef.current && this.props.quillRef.current.imageProgressDeleteFn) {
      this.props.quillRef.current.imageProgressDeleteFn(value)
    }
  }

  // 上传校验
  beforeUpload = (file) => {
    const isSize = file.size / 1024 / 1024 < 15
    if (!isSize) {
      Toast.show('超过15M限制，不允许上传~')
      return false
    }

    const { name: fileName } = file || {}
    const suffix = fileName.substring(fileName.lastIndexOf('.')+1).toLowerCase()
    const isJpgOrPng = file.type === 'image/jpg' || file.type === 'image/jpeg' ||  file.type === 'image/png' || file.type === 'image/gif'
    const isSuffixByJpgOrPng = (suffix === 'jpg' || suffix === 'jpeg' || suffix === 'png' || suffix === 'gif')
    if (!isJpgOrPng || !isSuffixByJpgOrPng) {
      Toast.show('只能上传JPG、JPEG、PNG、GIF格式的图片~')
      return false
    }
    this.props.getUploadVideoOrImageLoading(true)
    // 插入进度条
    this.imageProgressFn({
      code: file.uid,
      name: file.name,
      percent: 0,
    })
    return true
  }

  // 上传完成回调
  uploadOnChange = (info) => {
    if (info && !info.file.status) {
      return
    }

    if (info.file.status === 'uploading') {
      // 更新进度条
      this.imageProgressChangeFn({
        code: info.file.uid,
        name: info.file.name,
        percent: info.file.percent.toFixed(1) + '%',
      })
    }
    if (info && info.file.status === 'error') {
      Toast.show('上传失败')
      this.props.getUploadVideoOrImageLoading(false)
      // 删除进度条
      this.imageProgressDeleteFn({
        code: info.file.uid,
      })
      return
    }

    if (info && info.file.status === 'done') {
      this.props.getUploadVideoOrImageLoading(false)
      const res = info.file.response || {}
      const { code, content, msg } = res
      if (code == 200 && content) {
        if (this.props.createType == 'createPost') {
          this.props.handleUploadImage(content.fileUrlView)
        } else {
          this.imageFn({
            code: info.file.uid,
            src: content.fileUrlView,
          })
        }
      } else {
        Toast.show(msg || '上传失败')
        if (this.props.createType == 'createPost') {

        } else {
          // 删除进度条
          this.imageProgressDeleteFn({
            code: info.file.uid,
          })
        }
      }
    }
  }

  // 阻止默认事件
  onMouseDown = (e) => {
    e.preventDefault()
  }

  render() {
    const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    const { quillHistoryStack, quillFormat, createType } = this.props
    const { toolbarValue } = this.state

    return (
      <div id="fixed_toolbar_mobile" className={styles.fixed_content} >
        <div className={styles.toolbar}>
          <div className={styles.toolbar_left}>
            {
              (createType == 'createArticle') &&
              <div className={styles.upload_icon_wrap} onMouseDown={this.onMouseDown}>
                <i className={styles.icon_toolbar_image}></i>
                <Upload
                  headers={getHeaders()}
                  accept="image/*"
                  action={`/api/server/base/uploadFile?${stringify({ fileType: 23, userId: UserInfo?.friUserId})}`}
                  onChange={this.uploadOnChange}
                  beforeUpload={this.beforeUpload}
                  showUploadList={false}
                  // multiple={true}
                />
              </div>
            }
            <img src={toolbarValue == 2 ? toolbar_emoji_active_icon : toolbar_emoji_icon} width={36} height={36} style={{padding: 8}} alt="" onClick={() => this.toolbarOnChange(2)}/>
            <i className={styles.icon_toolbar_topic} onClick={this.goToSelectTopic}></i>
            {
              createType == 'createArticle' &&
              <img src={toolbarValue == 4 ? toolbar_font_active_icon : toolbar_font_icon} width={36} height={36} style={{padding: 8}} alt="" onClick={() => this.toolbarOnChange(4)}/>
            }
          </div>
          {
            createType == 'createArticle' &&
            <div className={styles.toolbar_right}>
              <span className={styles.gray_bar}></span>
              <i className={classNames(styles.icon_toolbar_undo, {
                [styles.disabled]: !quillHistoryStack.undo || quillHistoryStack.undo.length == 0,
              })} onClick={this.undoFn}></i>
              <i className={classNames(styles.icon_toolbar_redo, {
                [styles.disabled]: !quillHistoryStack.redo || quillHistoryStack.redo.length == 0,
              })} onClick={this.redoFn}></i>
            </div>
          }
        </div>
        {
          toolbarValue == 2 &&
          <Emoji itemOnClick={this.emojiOnClick}/>
        }
        {
          toolbarValue == 4 &&
          <div className={styles.editor_toolbar_container}>
            <div className={styles.toolbar_title}>文字格式</div>
            <div className={classNames(styles.font_toolbar, styles.font_toolbar1)}>
              <div className={classNames(styles.toolbar_item, {
                [styles.checked]: quillFormat.bold,
              })} onClick={this.boldFn}>
                <i className={styles.icon_bold}></i>
              </div>
              <div className={classNames(styles.toolbar_item, {
                [styles.checked]: quillFormat.italic,
              })} onClick={this.italicFn}>
                <i className={styles.icon_italic}></i>
              </div>
              <div className={classNames(styles.toolbar_item, {
                [styles.checked]: quillFormat.underline,
              })} onClick={this.underlineFn}>
                <i className={styles.icon_underline}></i>
              </div>
              <div className={classNames(styles.toolbar_item, {
                [styles.checked]: quillFormat.strike,
              })} onClick={this.strikethroughFn}>
                <i className={styles.icon_strikethrough}></i>
              </div>
            </div>
            <div className={styles.font_toolbar}>
              <div className={classNames(styles.toolbar_item, {
                [styles.checked]: !quillFormat.size || quillFormat.size == '16px',
              })} onClick={() => {this.fontSizeFn('16px')}}>
                <span className={styles.small}>标准</span>
              </div>
              <div className={classNames(styles.toolbar_item, {
                [styles.checked]: quillFormat.size == '20px',
              })} onClick={() => {this.fontSizeFn('20px')}}>
                <span className={styles.large}>大</span>
              </div>
              <div></div><div></div>
            </div>

            <div className={styles.toolbar_title}>文字颜色</div>
            <div className={styles.color_toolbar}>
              <div className={classNames(styles.toolbar_item, {
                [styles.checked]: !quillFormat.color || quillFormat.color == '#000000',
              })} onClick={() => {this.fontColorFn('#000000')}}>
                <span className={styles.color_1}></span>
              </div>
              <div className={classNames(styles.toolbar_item, {
                [styles.checked]: quillFormat.color == '#666666',
              })} onClick={() => {this.fontColorFn('#666666')}}>
                <span className={styles.color_2}></span>
              </div>
              <div className={classNames(styles.toolbar_item, {
                [styles.checked]: quillFormat.color == '#e03333',
              })} onClick={() => {this.fontColorFn('#e03333')}}>
                <span className={styles.color_3}></span>
              </div>
              <div className={classNames(styles.toolbar_item, {
                [styles.checked]: quillFormat.color == '#ee732e',
              })} onClick={() => {this.fontColorFn('#ee732e')}}>
                <span className={styles.color_4}></span>
              </div>
              <div className={classNames(styles.toolbar_item, {
                [styles.checked]: quillFormat.color == '#33bd62',
              })} onClick={() => {this.fontColorFn('#33bd62')}}>
                <span className={styles.color_5}></span>
              </div>
              <div className={classNames(styles.toolbar_item, {
                [styles.checked]: quillFormat.color == '#4481dd',
              })} onClick={() => {this.fontColorFn('#4481dd')}}>
                <span className={styles.color_6}></span>
              </div>
              <div className={classNames(styles.toolbar_item, {
                [styles.checked]: quillFormat.color == '#864ae9',
              })} onClick={() => {this.fontColorFn('#864ae9')}}>
                <span className={styles.color_7}></span>
              </div>
            </div>

            <div className={styles.toolbar_title}>对齐方式</div>
            <div className={classNames(styles.font_toolbar, styles.font_toolbar1)}>
              <div className={classNames(styles.toolbar_item, {
                [styles.checked]: quillFormat.align == 'justify',
              })} onClick={() => {this.alignFn('justify')}}>
                <i className={styles.icon_justify}></i>
              </div>
              <div className={classNames(styles.toolbar_item, {
                [styles.checked]: !quillFormat.align,
              })} onClick={() => {this.alignFn('')}}>
                <i className={styles.icon_left}></i>
              </div>
              <div className={classNames(styles.toolbar_item, {
                [styles.checked]: quillFormat.align == 'center',
              })} onClick={() => {this.alignFn('center')}}>
                <i className={styles.icon_center}></i>
              </div>
              <div className={classNames(styles.toolbar_item, {
                [styles.checked]: quillFormat.align == 'right',
              })} onClick={() => {this.alignFn('right')}}>
                <i className={styles.icon_right}></i>
              </div>
            </div>
          </div>
        }
      </div>
    )
  }
}

export default Index
