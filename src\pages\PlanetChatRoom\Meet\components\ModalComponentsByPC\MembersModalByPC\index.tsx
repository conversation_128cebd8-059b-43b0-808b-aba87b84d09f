/**
 * @Description: 管理成员弹窗，区分主持人和非主持人身份（PC端）
 */
import React, { useState, useEffect, useRef } from 'react';
import { history, connect, useRouteMatch } from 'umi'
import classNames from 'classnames'
import { Drawer, Switch, Input, Spin, Popover } from 'antd';
import { Popup, Toast } from 'antd-mobile';
import styles from './index.less';

import Avatar from '@/pages/PlanetChatRoom/components/Avatar'             // 头像组件
import NoDataRender from '@/components/NoDataRender' // 暂无数据组件

// 图片图标
import search_white_icon from '@/assets/PlanetChatRoom/search_white_icon.png'
import inviteIcon from '@/assets/GlobalImg/invite.png'               // 邀请
import cameraOpenIcon from '@/assets/GlobalImg/camera_open.png'      // 摄像头开启
import cameraCloseIcon from '@/assets/GlobalImg/camera_close.png'    // 摄像头关闭
import mikeOpenIcon from '@/assets/GlobalImg/mike_open.png'          // 麦克开启
import mikeCloseIcon from '@/assets/GlobalImg/mike_close.png'        // 麦克关闭
import more_seeting_icon from '@/assets/PlanetChatRoom/more_seeting_icon.png'            // 点点点

import {
  getCameralistArr,
  getHandUpRemoteStreamList,
  getHostRemoteStreamConfig,
  getIsModeMatrixCameraRemoteStreamList,
  getShareRemoteStreamConfig,
  getUserCameraRemoteStreamList,
  getUserInfoData
} from "@/utils/utilsByTRTC";

// 主持人身份的标签
const tabsByHost = [
  { id: 1, name: '会议中' },
  { id: 2, name: '申请进入' },
]

// 非主持人身份的标签
const tabsByNotHost = [
  { id: 1, name: '会议中' },
]

interface PropsType {
  visible: boolean,          // 弹窗是否显示
  onCancel: () => void,      // 关闭弹窗
  defaultTabKey: number, // 默认打开的tab
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const match = useRouteMatch()

  const {
    visible,
    dispatch,
    loading,
    PlanetChatRoom,
    localStreamConfig, // 本地流配置
    remoteStreamConfigList, // 远端流配置列表
    defaultTabKey, // 默认打开的tab
    onClickMembersCameraIcon, // 点击摄像头icon事件（主持人）
    onClickMembersMikeIcon, // 点击麦克风icon事件（主持人）
    onClickMembersRemoveBtn, // 点击移出会议事件（主持人）
    onClickMembersRefuseBtn, // 点击移除（主持人）
    onClickMembersAgreeBtn, // 点击准入（主持人）
    onClickMembersMuteAllBtn, // 点击全体静音（主持人）
    onClickMembersUnmuteAllBtn, // 点击解除全体静音（主持人）
    onClickMembersMuteOrUnmuteBtn, // 点击静音、解除静音（参会人）
  } = props

  const {
    SpaceInfo, // 直播间信息
    currentUserType, // 当前用户类型，1 主持人，2 参会人，3 观众
    membersListInTheMeeting, // 空间在线成员列表
    applyAdmissionList, // 申请进入会议成员
  } = PlanetChatRoom || {}

  // 空间信息
  const {
    wxUserId, // 微信用户id
    imUserId, // IM用户id
    hostUserInfo, // 主播信息
    name: nameBySpaceInfo, // 空间名称
    kingdomName, // 王国名称
    kingdomId, // 王国id
    status: statusBySpaceInfo, // 状态：1直播中、2预约中、3弹幕轰炸中
    spaceAdvertisingUrlShow, // 空间广告图
    isSignIn, // 是否打卡    0:未打卡 1:已打卡
    isCollect, // 是否收藏    0:未收藏 1:已收藏
    handUpList,             // 申请连麦列表
    isAppointment, // 是否预约    0:未预约 1:已预约
    appointmentStartTime, // 开始时间
    handUpType, // 举手类型    0:开启 1:关闭
    recordType, // 录制视频    1录制中
    handUpStatusType, // 申请连麦状态类型：0申请连麦 1接受连麦，默认null
    videoList,
    pv, // 页面访问量（观看量)
    gdp, // 页面gdp
    intro, // 空间介绍
    spaceCoverUrlShow, // 空间封面图
    videoSecond, // 之前播放到的视频时长
    password, // 直播间密码
    isNeedPwd,
    starSpaceType, // 类型：1直播，2会议
    liveStartTime, // 会议开始时间
    isSpectatorUse, // 是否允许非参会人进入 1 是 0否(页面中1为关0为开)
  } = SpaceInfo || {}

  // 远端流中摄像头流
  const userCameraRemoteStreamList = getUserCameraRemoteStreamList(SpaceInfo,remoteStreamConfigList); // 远端流中摄像头流 只要又画面或者有声音

  const [tabsResult, setTabsResult] = useState<any>([])    // 标签页数据
  const [currentTabKey, setCurrentTabKey] = useState(1)    // 当前选中的标签
  const [queryKey, setQueryKey] = useState('')             // 搜索关键词

  useEffect(() => {
    if (visible) {
      // 主持人展示2个标签页，非主持人展示1个
      if (currentUserType == 1) {
        setTabsResult(tabsByHost)
      } else {
        setTabsResult(tabsByNotHost)
      }
      setCurrentTabKey(defaultTabKey)
      getManageMembersInTheMeeting()
    } else {
      // 清空数据
      cleanState()
    }
  }, [visible])

  // 关闭弹窗清空数据
  const cleanState = () => {
    setTabsResult([])
    setCurrentTabKey(1)
    setQueryKey('')
  }

  // 空间在线成员列表
  const getManageMembersInTheMeeting = async () => {
    let res = await dispatch({
      type: 'PlanetChatRoom/getManageMembersInTheMeeting',
      payload: {
        spaceId: match?.params?.RoomId,
        sceneType: 2, // 场景类型，1：会议详情在线用户，2：管理成员在线用户
      }
    })
    return res
  }

  // 搜索框输入事件
  const onChanceInput = (e) => {
    console.log('搜索框输入', e.target.value)
    setQueryKey(e.target.value)
  }

  // 点击搜索框的取消按钮
  const onClickCancel = () => {
    console.log('点击搜索框的取消按钮')
    setQueryKey('')
  }

  // 切换标签页
  const onChangeTabs = (value) => {
    setCurrentTabKey(value)
    setQueryKey('')
  }

  // 根据角色值获取文案
  const getRoleText = (value) => {
    return value == 1 ? '主持人' : value == 2 ? '参会人' : '观众'
  }

  // 获取会议中用户dom
  const getUserListDom1 = () => {
    return membersListInTheMeeting && membersListInTheMeeting.length > 0 ? membersListInTheMeeting.map(item => {
      if (queryKey && item.name && item.name.indexOf(queryKey.trim()) == -1) {
        return null
      }
      let userStreamList = [...userCameraRemoteStreamList,localStreamConfig]
      let itemByStreamList = userStreamList.find(item1 => item1?.userID == item?.imUserId)
      const {
        mutedAudio,
        mutedVideo,
        hasVideo,
        hasAudio,
      } = itemByStreamList || {}
      return (
        <div key={item.wxUserId} className={styles.user_item_1}>
          <div className={styles.left}>
            <div className={styles.avatar_wrap}>
              <Avatar size={24} userInfo={item}/>
            </div>
            <div className={styles.user_name}>{item.name}</div>
            <div className={classNames(styles.user_role, {
              [styles.host]: item.meetingType == 1 || item.isSelf == 1,
              [styles.guest]: item.meetingType == 2,
              [styles.audience]: item.meetingType == 3,
            })}>{item.isSelf == 1 ? '我' : getRoleText(item.meetingType)}</div>
          </div>
          <div className={styles.right}>
            {/* meetingType 参会人类型：1主持人、2参会人、3观众 */}
            {/* 成员麦克和摄像头icon状态回显 */}
            {
              (item.meetingType == 1 || item.meetingType == 2) && !!itemByStreamList ?
                <>
                  <img
                    src={mutedVideo || !hasVideo  ? cameraCloseIcon : cameraOpenIcon}
                    width={24} height={24} style={{padding: 4, cursor: 'pointer'}} alt=""
                    onClick={() => onClickMembersCameraIcon({...item,mutedVideo:mutedVideo})}
                  />
                  <img
                    src={mutedAudio || !hasAudio ? mikeCloseIcon : mikeOpenIcon}
                    width={24} height={24} style={{padding: 4, cursor: 'pointer'}} alt=""
                    onClick={() => onClickMembersMikeIcon({...item,mutedAudio:mutedAudio})}
                  />
                </>
                : null
            }

            {/* 是支持人，显示更多操作按钮 */}
            {
              currentUserType == 1 && item.isSelf != 1 &&
              <Popover
                getPopupContainer={() => document.getElementById('user_list_wrap')}
                trigger="click"
                placement="bottomLeft"
                content={<div style={{cursor: 'pointer'}} onClick={() => onClickMembersRemoveBtn(item)}>移出会议</div>}
              >
                <img src={more_seeting_icon} width={24} height={24} style={{padding: 4, opacity: 0.6, cursor: 'pointer'}} alt=""/>
              </Popover>
            }
          </div>
        </div>
      )
    }) : <NoDataRender style={{marginTop: 40, color: '#fff'}} text="暂无成员~"/>
  }

  // 申请进入用户dom
  const getUserListDom2 = () => {
    return applyAdmissionList && applyAdmissionList.filter((item) => item.isAgree == 0).length > 0 ? applyAdmissionList.map(item => {
      // isAgree 是否同意：0申请中,1同意，2拒绝
      if (item.isAgree != 0) {
        return null;
      }
      if (queryKey && item.name && item.name.indexOf(queryKey.trim()) == -1) {
        return null
      }
      return (
        <div key={item.wxUserId} className={styles.user_item_2}>
          <div className={styles.left}>
            <div className={styles.avatar_wrap}>
              <Avatar size={24} userInfo={item}/>
            </div>
            <div className={styles.user_name}>{item.userName}</div>
          </div>
          <div className={styles.right}>
            <span style={{color: '#FF5F57'}} onClick={() => onClickMembersRefuseBtn(item)}>移除</span>
            <span onClick={() => onClickMembersAgreeBtn(item)}>准入</span>
          </div>
        </div>
      )
    }) : <NoDataRender style={{marginTop: 40, color: '#fff'}} text="暂无申请~"/>
  }

  // loading
  const loadingGetManageMembersInTheMeeting = !!loading.effects['PlanetChatRoom/getManageMembersInTheMeeting']

  return (
    <Drawer
      open={visible}
      className={styles.drawer}
      placement="right"
      onClose={props.onCancel}
      destroyOnClose
      closable={false}
      maskStyle={{background: 'rgba(0,0,0,0)'}}
      width={383}
    >
      <Spin wrapperClassName={styles.spin} spinning={loadingGetManageMembersInTheMeeting}>
        <div className={styles.container}>
          {/* 标题 */}
          <div className={styles.header_title_wrap}>
            <div className={styles.title}>{currentUserType == 1 ? '管理' : ''}成员</div>
            {
              currentUserType == 1 && isSpectatorUse == 0 && <div className={styles.text}>已开启不允许非参会人进入</div>
            }
          </div>

          {/* 搜索框 */}
          {
            currentUserType == 1 || currentUserType == 2 ?
              <div className={styles.search_wrap}>
                <Input
                  placeholder="搜索成员"
                  prefix={<img src={search_white_icon} width={20} height={20} alt="" />}
                  value={queryKey}
                  onChange={onChanceInput}
                  allowClear={true}
                />

                <span className={styles.search_cancel_btn} onClick={onClickCancel}>取消</span>
              </div>
              : null
          }

          {/* 标签页，判断是否是主持人，不同的身份有不同的标签 */}
          <div className={styles.tabs_wrap}>
            {
              tabsResult.map(item => {
                return (
                  <div key={item.id} className={classNames(styles.tabs_item, {
                    [styles.checked]: currentTabKey == item.id,
                  })} onClick={() => onChangeTabs(item.id)}>
                    {item.name}
                    {/* 红字数量 */}
                    {item.id == 2 && applyAdmissionList && applyAdmissionList.filter((item) => item.isAgree == 0).length > 0 && <div className={styles.badge}>{applyAdmissionList.filter((item) => item.isAgree == 0).length}</div>}
                    {/* 短横线 */}
                    {currentTabKey == item.id && <i className={styles.tabs_bar}></i>}
                  </div>
                )
              })
            }
          </div>

          {/* 用户列表 */}
          <div className={styles.user_list_wrap} id="user_list_wrap">
            {
              currentTabKey == 1 ? getUserListDom1()
                : getUserListDom2()
            }
          </div>

          {/* 按钮 */}
          {
            currentUserType == 1 ?
              <div className={styles.btn_wrap}>
                <div className={styles.btn_left} onClick={onClickMembersMuteAllBtn}>全体静音</div>
                <div className={styles.btn_right} onClick={onClickMembersUnmuteAllBtn}>解除全体静音</div>
              </div>
              : currentUserType == 2 ?
              <div className={styles.btn_wrap}>
                <div className={styles.btn_right} onClick={onClickMembersMuteOrUnmuteBtn}>
                  {localStreamConfig && !!localStreamConfig.mutedAudio ? '解除静音' : '静音'}
                </div>
              </div>
              : null
          }
        </div>
      </Spin>
    </Drawer>
  )
}
export default connect(({ loading, PlanetChatRoom }: any) => ({ loading, PlanetChatRoom }))(Index)
