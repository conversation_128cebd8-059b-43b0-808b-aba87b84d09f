import React, { useEffect, useState } from 'react'
import { history, connect } from 'umi'
import { randomColor , processNames, gdpFormat, useThrottle } from '@/utils/utils'
import { Spin } from 'antd'
import { Input } from 'antd-mobile'
import styles from './index.less'
import NoDataImage from '@/assets/GlobalImg/no_data.png'
import NavBar from '@/components/NavBar'

const Index: React.FC = (props: any) => {
  const { loading, dispatch } = props
  // 关联王国
  const [createAndJoinKingdomDataSource, setCreateAndJoinKingdomDataSource] = useState([])
  // 搜索关键词
  const [kingdomName, setKingdomName] = useState('')

  useEffect(() => {
    getCreateKingdomList()
  }, [])

  // 获取王国数据
  const getCreateKingdomList = () => {
    dispatch({
      type: 'graphicsText/getCreateKingdomList',
      payload: {
        kingdomName: kingdomName,                // 王国name
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200) {
        const arr = []
        if (content['1']) {
          arr.push({
            id: 1,
            name: '自己创建的',
            children: content[1],
          })
        }
        if (content['2']) {
          arr.push({
            id: 2,
            name: '我加入的王国',
            children: content[2],
          })
        }
        setCreateAndJoinKingdomDataSource(arr);
      } else if (code == 400) {
        setCreateAndJoinKingdomDataSource([])
      }
    }).catch(err => {})
  }

  // 输入搜索关键词
  const inputOnChange = (value) => {
    console.log('输入搜索关键词', value)
    setKingdomName(value)
  }

  // 搜索
  let inputOnSearch = () => {
    console.log('搜索')
    getCreateKingdomList()
  }
  inputOnSearch = useThrottle(inputOnSearch, 500)

  // 选择王国
  let selectItem = (item) => {
    dispatch({
      type: 'graphicsText/save',
      payload: {
        selectedKingdomId: item.id,
        selectedKingdomName: item.name,
      }
    })
    history.goBack()
  }
  selectItem = useThrottle(selectItem, 500)

  const loadingGetCreateKingdomList = !!loading.effects['graphicsText/getCreateKingdomList']

  return (
    <Spin spinning={loadingGetCreateKingdomList} wrapperClassName={styles.spin}>
      <NavBar
        title="选择王国"
        bordered
      />
      <div className={styles.container}>
        <div className={styles.header_box}>
          <div className={styles.header}>
            <i className={styles.header_icon}></i>
            <Input
              placeholder="输入关键字搜索王国"
              autoComplete="off"
              value={kingdomName}
              onChange={inputOnChange}
              onEnterPress={inputOnSearch}
            />
          </div>
        </div>
        {
          createAndJoinKingdomDataSource.length == 0 ?
            <div className={styles.item_empty_box}>
              <img src={NoDataImage} width={150} height={113} alt=""/>
              <div className={styles.empty_title}>暂无数据</div>
              <div className={styles.empty_msg}>请试试其他搜索关键词</div>
            </div>
            : createAndJoinKingdomDataSource.map(item => {
              return (
                <div key={item.id}>
                  <div className={styles.list_title}>{item.name}({item.children.length}个)</div>
                  <div className={styles.kingdom_box}>
                    {
                      item.children.map(itemChild => {
                        return (
                          <div key={itemChild.id} className={styles.kingdom_item} onClick={() => selectItem(itemChild)}>
                            <div
                              className={styles.kingdom_item_avatar}
                              style={itemChild.kingdomCoverUrlShow || itemChild.kingImgUrlShow ? {backgroundImage: `url(${itemChild.kingdomCoverUrlShow || itemChild.kingImgUrlShow})`} : {background: randomColor(itemChild.wxUserId)}}
                            >
                              {!itemChild.kingdomCoverUrlShow && !itemChild.kingImgUrlShow && processNames(itemChild.kingName)}
                            </div>
                            <div className={styles.kingdom_item_details}>
                              <div className={styles.kingdom_name}>{itemChild.name}</div>
                              <div className={styles.kingdom_info}>
                                <span>{gdpFormat(itemChild.gdp)}GDP</span>
                                <span>{gdpFormat(itemChild.nationalNum)}国民在交流</span>
                                <span>{itemChild.spaceNum}个热议空间</span>
                              </div>
                            </div>
                          </div>
                        )
                      })
                    }
                  </div>
                </div>
              )
            })
        }
      </div>
    </Spin>
  )
}

export default connect(({ graphicsText, loading }: any) => ({ graphicsText, loading }))(Index)
