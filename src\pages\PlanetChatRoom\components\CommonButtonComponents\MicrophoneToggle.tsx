// 开启/关闭麦克风
import React from 'react';
import classNames from 'classnames';
import styles from './index.less';  // 引入自定义样式

const MicrophoneToggle = ({ isLive, isJoined, isPublished, currentUserType, handUpStatusType, resetTimer, localStreamConfig, handleChangeByLocalStreamConfig }) => {
  return (
      <div onClick={(e)=>{
        // 点击后阻止事件冒泡,防止触发父元素的点击事件
        e.stopPropagation(); resetTimer();
        handleChangeByLocalStreamConfig('audio',null);
      }} className={styles.HorizontalLiveRoom_Btn_Warp}>
        <i className={classNames({
          [styles.SpatialDetail_SoundOff_btn]:true,
          [styles.SpatialDetail_SoundOff_btn_Forbidden]:localStreamConfig && !!localStreamConfig.mutedAudio,
        })}/>
        <div className={styles.text}>{localStreamConfig && !!localStreamConfig.mutedAudio ? '解除静音' : '静音'}</div>
      </div>
  );
};

export default MicrophoneToggle;
