/**
 * @Description: 创建空间成功页面
 */
import React, { useEffect, useState, useRef } from 'react'
import { history,connect } from 'umi'
import { getOperatingEnv } from '@/utils/utils'
import { Typography, Spin } from 'antd'
import { Toast } from 'antd-mobile'
import styles from './index.less'

import SuccessIcon from '@/assets/GlobalImg/success.png'
import NavBar from '@/components/NavBar'                       // 导航栏
import PosterModal from '@/pages/Poster/PosterModal'           // 海报（APP、H5）

const Index: React.FC = (props: any) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')   // 用户信息
  const { dispatch, loading } = props;
  // 创建空间类型  starSpaceType 1 直播，2 会议
  const { id, starSpaceType } = history.location.query || {}; // 空间id

  const posterModalRef = useRef(null)     // 海报弹窗ref
  const [spaceState, setSpaceState] = useState({})                        // 直播or会议数据


  useEffect(() => {
    localStorage.removeItem('kingdomInfoData')
    localStorage.removeItem('isSuperAccount')

    getSpacePosterInfo()
  }, [])

  // 获取空间海报信息
  const getSpacePosterInfo = () => {
    dispatch({
      type: 'userInfoStore/getSpacePosterInfo',
      payload: {
        spaceId: id,                   // 空间ID
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200) {
        setSpaceState(content || {})
      } else {
        Toast.show(msg || '数据加载失败')
      }
    }).catch(err => {
    })
  }

  // 生成海报
  const sharePoster = (type) => {
    posterModalRef && posterModalRef.current.init(type, spaceState, 'replace')
  }

  // 复制成功
  const onCopy = () => {
    Toast.show('复制链接成功!')
  }

  // loading
  const loadingGetSpacePosterInfo = !!loading.effects['userInfoStore/getSpacePosterInfo']

  return (
    <>
      <NavBar title={'创建成功'} ></NavBar>
      <Spin spinning={loadingGetSpacePosterInfo}>
        <div className={styles.container}>

          <div className={styles.icon_box}>
            <img src={SuccessIcon} width={72} height={72} alt=""/>
          </div>
          <div className={styles.title}>恭喜您成功创建{starSpaceType == 2 ? '会议' : '直播'}!</div>
          <div className={styles.message}>快分享给小伙伴们一起来观看吧~</div>
          <div className={styles.btn_box}>
            <div className={styles.btn_left} onClick={()=>{history.replace(`/PlanetChatRoom/${starSpaceType == '2' ? 'Meet' : 'Live'}/${id}`)}}>进入{starSpaceType == '2' ? '会议' : '直播'}</div>
            <div className={styles.btn_right} onClick={() => sharePoster(1)}>生成海报</div>
            {
              (getOperatingEnv() == '2' || getOperatingEnv() == '7' || getOperatingEnv() == '5' || getOperatingEnv() == '6') ?
                <div className={styles.btn_right} onClick={() => sharePoster(2)}>分享</div>
                :
                <div className={styles.btn_right}>
                  <Typography.Paragraph copyable={{
                    text: `${window.location.origin}/PlanetChatRoom/${starSpaceType == '2' ? 'Meet' : 'Live'}/${id}?shareUserId=${UserInfo?.friUserId}&isShare=1` + (starSpaceType == '2' && spaceState.password ? `&pwd=${spaceState.password}` : ''),
                    icon: [<div>复制链接</div>, <div>复制链接</div>],
                    tooltips: ['', ''],
                    onCopy: onCopy,
                  }}></Typography.Paragraph>
                </div>
            }
          </div>
        </div>
      </Spin>

      {/* 海报弹窗 */}
      <PosterModal ref={posterModalRef}/>
    </>
  )
}

export default connect(({ userInfoStore, loading }: any) => ({userInfoStore, loading}))(Index)
