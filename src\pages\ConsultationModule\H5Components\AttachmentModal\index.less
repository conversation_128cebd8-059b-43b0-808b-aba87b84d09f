.wrap {
  width: 100%;
  // height: 122px;
  background: #FFFFFF;

  .horizontal_line{
    width: 48px;
    height: 4px;
    background: #D0D4D7;
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
    margin: 12px auto;
  }
  .header{
    display: flex;
    padding: 0 16px;
    .header_title{
      font-size: 17px;
      font-weight: 500;
      color: #000000;
      flex: 1;
      text-align: center;
    }
    .header_close{
      width: 24px;
      height: 24px;
      display: block;
      &>img{
        width: 100%;
        height: 100%;
      }
    }
  }

  .wrap_input{
    height: 60px;
    display: flex;
    align-items: center;
    margin-left: 16px;
    padding: 24px 16px 16px 0;
    border-bottom: 1px solid #E1E4E7;
    &>p{
      margin-bottom: 0;
    }
    .input_left{
      font-size: 14px;
      font-weight: 500;
      .input_star{
        color: #FF5F57;
      }
      .input_title{
        color: #666666;
      }
    }
    .input_right{
      flex: 1;
      padding-left: 50px;
      position: relative;
      .input_error{
        position: absolute;
        right: 0;
        bottom: -12px;
        font-size: 14px;
        font-weight: 400;
        color: #FF5F57;
        text-align: right;
        display: block;
      }
      .input{
        border: 0;
        outline: none;
        width: 100%;
        height: 32px;
        font-size: 14px;
        font-weight: 400;
        color: #000000;
        text-align: right;
      }
    }
  }
  .annex_num{
    padding: 12px 0 0 24px;
    font-size: 12px;
    font-weight: 400;
    color: #666666;
    margin-bottom: 0;
  }
  .annex_content{
    padding: 0 16px;
    height: 200px;
    overflow: auto;
  }
  .footer{
    border-top: 1px solid #EEEEEE;
    // height: 58px;
    padding: 8px 16px;
    .footer_btn{
      // height: 40px;
      background: #0095FF;
      border-radius: 20px 20px 20px 20px;
      padding: 9px 0;
      text-align: center;
      font-size: 16px;
      font-weight: 400;
      color: #FFFFFF;
      margin-bottom: 0;
    }
  }
}