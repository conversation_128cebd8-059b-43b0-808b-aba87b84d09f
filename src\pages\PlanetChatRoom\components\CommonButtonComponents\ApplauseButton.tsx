/* 鼓掌按钮 */
import React from 'react';
import styles from './index.less';  // 引入自定义样式
import {
  SDKAPPID,       // TRTC 的 sdkappid
  EXPIRETIME,     // TRTC 签名过期时间，建议不要设置的过短
  SECRETKEY,      // TRTC 计算签名用的加密密钥
  audience,       // TRTC 角色类型-观众
  anchor,         // TRTC 角色类型-主播
  SDKAppIDByIm,   // 接入时需要将0替换为您的即时通信 IM 应用的 SDKAppID
  SIGN_IN,        // IM 自定义消息类型-签到
  BULLET_SCREEN,  // IM 自定义消息类型-弹幕
  SEND_FLOWERS,   // IM 自定义消息类型-送花
  SEND_APPLAUSE,  // IM 自定义消息类型-掌声
  HAND_UP,        // IM 自定义消息类型-举手连麦
  HAND_DOWN,      // IM 自定义消息类型-放弃连麦断开
  FORCED_END,
  UPDATA_STATE,      // IM 自定义消息类型-强制结束连麦
  licenseUrl,
} from '@/app/config';

const ApplauseButton = ({
                          resetTimer,
                          isNotLogin,
                          setModalVisibleByUserTokenInvalid,
                          sendMessageByIm }) => {
  const handleApplauseClick = (e) => {
    e.stopPropagation();
    resetTimer();

    if (isNotLogin) {
      setModalVisibleByUserTokenInvalid();
      return null;
    }
    sendMessageByIm({ dataType: SEND_APPLAUSE, description: '1' });
  };

  return (
    <div className={styles.HorizontalLiveRoom_Btn_Warp}>
      <div className={styles.HorizontalLiveRoom_send_clap_btn} onClick={handleApplauseClick}></div>
      <div className={styles.text}>鼓掌</div>
    </div>
  );
};

export default ApplauseButton;
