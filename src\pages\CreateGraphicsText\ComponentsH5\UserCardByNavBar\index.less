.nav_bar {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 666;
  width: 100%;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  background: #fff;
  padding-right: 12px;
  .back_icon {
    flex-shrink: 0;
    width: 36px;
    height: 44px;
    background: url("../../../../assets/GlobalImg/go_back.png") no-repeat 16px center;
    background-size: 12px 24px;
    margin-right: 12px;
  }
  .header {
    flex: 1;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    overflow: hidden;
    .header_left {
      display: flex;
      align-items: center;
      overflow: hidden;
      margin-right: 16px;
      .left_avatar {
        flex-shrink: 0;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        margin-right: 8px;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        line-height: 36px;
        color: #fff;
        font-size: 16px;
        text-align: center;
        white-space: nowrap;
      }

      .left_info_wrap {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        .user_info {
          display: flex;
          align-items: center;
          .user_name {
            font-size: 15px;
            color: #000;
            line-height: 21px;
            font-weight: 600;
          }
          .user_gray_bar {
            margin: 0 8px;
            height: 12px;
            width: 0;
            border-left: 1px solid #ccc;
          }
          .user_grade {
            font-size: 12px;
            color: #333;
            line-height: 17px;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .user_tag {
            margin-left: 8px;
            height: 18px;
            line-height: 18px;
            padding: 0 4px;
            font-size: 11px;
            color: #06A777;
            background: #EEFFF9;
            border-radius: 2px;
            border: 1px solid #B0EAD9;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        .date {
          font-size: 11px;
          color: #333;
          line-height: 15px;
        }
      }
    }
    .header_right {
      flex-shrink: 0;
    }
  }

  .right {
    //position: absolute;
    //right: 12px;
    //top: 50%;
    //transform: translateY(-50%);   // 会影响子元素position样式
    flex-shrink: 0;
  }
}
