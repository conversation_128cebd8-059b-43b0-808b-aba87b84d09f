import { processNames, randomColor } from '@/utils/utils';
import styles from './index.less';
import {history} from 'umi';

import avatarIcon from '@/assets/GlobalImg/default_head_picture.png';

const Index = (props:any) => {
  const {item} = props;
  const UerInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  return (
    <div className={styles.doctor_wrap} onClick={()=>{history.push(`/Expert/ExpertDetails?id=${item.expertsUserId}&shareUserId=${UerInfo?.friUserId}`)}}>
      <div className={styles.doctor_img}>
        {
          item.imagePhotoPathShow?<img className={styles.doctor_pic} src={item.imagePhotoPathShow?item.imagePhotoPathShow:avatarIcon}/>:
          <div className={styles.no_doctor_pic} style={{background:randomColor(item.expertsUserId)}}>{processNames(item.name)}</div>
        }
      </div>
      <div className={styles.doctor_detail}>
        <div className={styles.doctor_detail_head}>
          <span className={styles.doctor_detail_title}>{item.name}</span>
          {
            item.postTitleDictName?<div className={styles.doctor_vertical}></div>:null
          }
          {
            item.postTitleDictName? <span className={styles.doctor_detail_attending}>{item.postTitleDictName}</span>:null
          }
          {
            item.depSubjectDictName && item.abilityLevelDictName ? <span className={styles.doctor_detail_major}>{item.depSubjectDictName}{item.abilityLevelDictName?"·":null}{item.abilityLevelDictName}</span> : null
          }
        </div>
        {
          item.organizationName ? <div className={styles.organization_name}>{item.organizationName}</div> : null
        }
      </div>
    </div>
  )
}
export default Index
