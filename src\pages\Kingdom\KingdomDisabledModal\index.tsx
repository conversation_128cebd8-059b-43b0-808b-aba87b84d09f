/**
 * @Description: 王国违规被关闭提示弹窗
 */
import React from 'react'
import { history, connect } from 'umi'
import { Modal } from 'antd-mobile'
import styles from './index.less'
import WranningIcon from '@/assets/GlobalImg/wranning.png'

const Index: React.FC = (props: any) => {
  return (
    <Modal
      visible={props.visible}
      content={
        <div className={styles.container}>
          <div className={styles.title_box}>
            <img src={WranningIcon} width={24} height={24} alt=""/>
            <div className={styles.title}>该王国违反平台规范无法查看</div>
          </div>
          <div className={styles.btn_box}>
            <div className={styles.ok} onClick={()=>{history.goBack()}}>我已知晓</div>
          </div>
        </div>
      }
    >
    </Modal >
  )
}

export default connect(({ activity, loading }: any) => ({activity, loading}))(Index)
