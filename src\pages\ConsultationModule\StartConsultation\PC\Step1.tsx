/**
 * @Description: PC端，发起指导页第1步
 */
import React, { useEffect, useState } from 'react'
import { history, connect } from 'umi'
import classNames from 'classnames'
import { getArrailUrl } from '@/utils/utils'
import { Button, Spin, message } from 'antd'
import { Toast } from 'antd-mobile'
import { QuestionCircleOutlined, InfoCircleOutlined } from '@ant-design/icons'
import styles from './Step1.less'

import CompleteProcessModal from '@/componentsByPc/ConsultationSteps/CompleteProcessModal' // 完整服务流程弹窗
import PcHeader from '@/componentsByPc/PcHeader' // 顶部导航栏组件
import StartConsultationSteps from '@/pages/ConsultationModule/StartConsultation/ComponentsPC/StartConsultationSteps'
import {stringify} from "qs" // 完整服务流程按钮及弹窗

const Index: React.FC = (props) => {
  const isInIframe = (self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya') // 是否嵌套在5i5ya的iframe中
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}') // 登录用户信息

  const { loading, dispatch } = props
  const { query, pathname } = history.location
  const {
    expertsUserId, // 专家ID
    consultationId, // 指导ID
    copyUserId, // H5复制链接携带的用户ID
  } = query

  // 指导方式state
  const initialState = {
    // discount: '0.5',                          // 折扣(非会员无折扣NULL、个人7折、企业5折)
    // originalVideoTreatCosts: null,            // 原视频诊费(/30min)
    // videoTreatCosts: null,                    // 视频诊费(/30min)
    // originalPictureTreatCosts: null,          // 原图文诊费(/次)
    // pictureTreatCosts: null,                  // 图文诊费(/次)
    // freeTimes: '1',                           // 剩余免费次数
    // memberOrderId: '153'                      // 会员订单ID
    // memberTypeCode: 0,                        // 会员类型0非会员，1个人会员，2企业会员
  }
  // 弹窗state
  const initialModalState = {
    completeProcessVisible: false,               // 完整服务流程弹窗
    defaultConsultationType: 1,                  // 弹窗中默认展示tab的指导类型，1 图文，2 视频
  }
  const [state, setState] = useState(initialState)
  const [modalState, setModalState] = useState(initialModalState)

  useEffect(() => {
    // 查询指导方式
    getConsultationWay()
  }, [])

  // 查询指导方式
  const getConsultationWay = () => {
    dispatch({
      type: 'consultation/getConsultationWay',
      payload: {
        expertsUserId: expertsUserId,                      // 专家ID
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200) {
        setState(content || {})
      } else {
        message.error(msg || '查询指导方式失败')
      }
    }).catch(err => {})
  }

  // 打开完整服务流程弹窗
  const completeProcessModalShow = (type) => {
    setModalState({
      ...modalState,
      completeProcessVisible: true,
      defaultConsultationType: type,                       // 指导类型，1 图文，2 视频
    })
  }

  // 关闭完整服务流程弹窗
  const completeProcessModalHide = () => {
    setModalState({
      ...modalState,
      completeProcessVisible: false,
      defaultConsultationType: 1,                          // 指导类型，1 图文，2 视频
    })
  }

  // 选择指导类型，去下一步
  const goToNext = async (consultationType) => {
    if (consultationId) {
      const res = await updateCaseInfoQuestion(consultationType)
      const { code, content, msg } = res || {}
      if (code == 200 && content) {

      } else {
        message.error(msg || '数据加载失败')
        return
      }
    }
    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'pathname',       // 页面地址onchange事件
        pathnameByChild: `/ConsultationModule/StartConsultation/Step2`,  // 路由信息
        searchByChild: `?${stringify({
          ...query,
          consultationType: consultationType,            // 指导类型，1 图文，2 视频
        })}`,  // 路由信息
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
      return
    }
    history.replace({
      pathname: '/ConsultationModule/StartConsultation/Step2',
      query: {
        ...query,
        consultationType: consultationType,            // 指导类型，1 图文，2 视频
      }
    })
  }

  // 修改会诊用户提问且同步病历问题
  const updateCaseInfoQuestion = (consultationType) => {
    return dispatch({
      type: 'consultation/updateCaseInfoQuestion',
      payload: {
        wxUserId: UserInfo?.friUserId,
        id: consultationId, // 指导ID
        type: consultationType, // 1图文，2视频
      }
    }).then(res => {
      return res
    }).catch(err => {
      return null
    })
  }

  // 返回
  const goBack = () => {
    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'goBack',       // 页面地址onchange事件
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
      return
    }

    if (copyUserId || history.length <= 2) {
      history.replace('/')
    } else {
      history.goBack()
    }
  }

  // loading
  const getConsultationWayLoading = !!loading.effects['consultation/getConsultationWay']
  const getDepSubjectDictLoading = !!loading.effects['consultation/getDepSubjectDict']
  const getConsultationAndCaseInfoLoading = !!loading.effects['consultation/getConsultationAndCaseInfo']

  return (
    <>
      <div className={styles.container}>
        {/* iframe中隐藏header */}
        {
          isInIframe ? null : <PcHeader/>
        }

        <div className={styles.content}>
          <div className={styles.content_inner}>
            {/* 导航栏 */}
            <div className={styles.header}>
              <div className={styles.header_icon} onClick={goBack}></div>
              <div className={styles.header_title}>发起专家指导</div>
            </div>

            <div className={styles.box}>
              {/* 标题及弹窗 */}
              <StartConsultationSteps title="选择咨询方式"/>

              <Spin spinning={getConsultationWayLoading || getDepSubjectDictLoading || getConsultationAndCaseInfoLoading}>
                {/* 指导方式 */}
                <div className={styles.method}>
                  {/* 图文指导 */}
                  {
                    state.originalPictureTreatCosts ?
                      <div className={styles.block}>
                        {
                          state.memberTypeCode !=0 && <div className={styles.method_sign}>会员价</div>
                        }

                        <div className={classNames(styles.method_icon, styles.method_icon_1)}></div>
                        <div className={styles.method_title}>图文指导</div>
                        <div className={styles.method_text}>通过文字、图片向专家提问，可在2个工作日内得到专家的图文或语音回复</div>
                        <div className={styles.method_price1}>
                          <div className={styles.price}>¥{state.pictureTreatCosts}</div>
                          <div className={styles.unit}>/次</div>
                        </div>
                        <div className={styles.method_price2}>
                          {
                            state.originalPictureTreatCosts != state.pictureTreatCosts && `¥${state.originalPictureTreatCosts}/次`
                          }
                        </div>


                        <Button
                          className={styles.method_btn1}
                          type="primary"
                          onClick={() => goToNext(1)}
                        >选择</Button>
                        <div className={styles.method_btn2} onClick={() => completeProcessModalShow(1)}>
                          <QuestionCircleOutlined/>
                          查看完整服务流程
                        </div>
                      </div>
                      : null
                  }

                  {/* 视频指导 */}
                  {
                    state.originalVideoTreatCosts ?
                      <div className={styles.block}>
                        {
                          state.memberTypeCode !=0 && <div className={styles.method_sign}>会员价</div>
                        }
                        <div className={classNames(styles.method_icon, styles.method_icon_2)}></div>
                        <div className={styles.method_title}>视频指导</div>
                        <div className={styles.method_text}>通过视频方式沟通更清楚</div>
                        <div className={styles.method_price1}>
                          <div className={styles.price}>¥{state.videoTreatCosts}</div>
                          <div className={styles.unit}>/30min</div>
                        </div>
                        <div className={styles.method_price2}>
                          {
                            state.originalVideoTreatCosts != state.videoTreatCosts && `¥${state.originalVideoTreatCosts}/30min`
                          }
                        </div>
                        <Button
                          className={styles.method_btn1}
                          type="primary"
                          onClick={
                          () => goToNext(2)
                        }
                        >选择</Button>
                        <div className={styles.method_btn2} onClick={() => completeProcessModalShow(2)}>
                          <QuestionCircleOutlined/>
                          查看完整服务流程
                        </div>
                      </div>
                      : null
                  }
                </div>
                {/* 免费次数提示 */}
                {
                  state.freeTimes > 0 &&
                  <div className={styles.bottom}>
                    <InfoCircleOutlined/>
                    <div className={styles.bottom_text}>您还可使用免费指导<span className={styles.highlight}>{state.freeTimes}</span>次</div>
                  </div>
                }
              </Spin>
            </div>
          </div>
        </div>

      </div>
      {/* 完整服务流程弹窗 */}
      <CompleteProcessModal
        visible={modalState.completeProcessVisible}
        defaultConsultationType={modalState.defaultConsultationType}
        onCancel={completeProcessModalHide}
      />
    </>
  )
}

export default connect(({ consultation, loading }: any) => ({ consultation, loading }))(Index)
