/**
 * @Description: 移动端，发起指导，第3步创建病例
 */
import React, { useState, useEffect, useRef } from 'react'
import heic2any from 'heic2any';
import { history, connect } from 'umi'
import classNames from 'classnames'
import { cloneDeep } from 'lodash'
import { stringify } from 'qs'
import dayjs from 'dayjs'
import request from '@/utils/request'
import { getOperatingEnv, getFileType } from '@/utils/utils'
import { Upload, Typography, Spin, message } from 'antd'
import { TextArea, Toast, Modal } from 'antd-mobile'
import { RightOutlined, PlusOutlined, CloseCircleFilled } from '@ant-design/icons'
import styles from './Step3.less'

import Navbar from '@/components/NavBar'                             // 导航栏组件
import StartConsultationSteps from '@/pages/ConsultationModule/StartConsultation/ComponentsH5/StartConsultationSteps' // 查看完整服务流程按钮及弹窗

// 病例模板选项字典
const caseTemplateList = [
  {code: 1, name: '基本信息'},
  {code: 2, name: '检查及诊断'},
  {code: 3, name: '治疗方案'},
]

let isReset = true // 状态，判断ios中滚动是否归位
let timer = null // 暂存定时器
let pageDidMount = false // 页面是否已挂载，为了初次进入时不调暂存口

const Index: React.FC = (props) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}') // 用户信息
  const { dispatch, loading } = props
  const { query, pathname } = history.location
  const {
    consultationId, // 指导ID
    pageFrom = '', // ConsultationDetails 表示来自指导详情页
    consultationType, // 指导类型，1 图文，2 视频
    expertsUserId, // 专家ID
    orderCaseTemplate, // 1通用模板 ，2正畸模板
  } = query

  const inputRef = useRef(null) // 用户提问输入框ref

  // 指导信息state
  const initialFormState = {
    templateType: 1,                                       // 模板类型(1星球、2诊所)
    checkedCaseTemplateList: [],                           // 选中的病例模板选项[1,2,3]
    isTemplate: 0,                                         // 是否是模板(1是，0否)
    noTemplateDescription: '',                             // 无模板的描述
    age: '',                                               // 年龄
    sex: '',                                               // 性别
    chiefComplaint: '',                                    // 主诉
    presentDisease: '',                                    // 现病史
    previousHistory: '',                                   // 既往史
    wholeHealth: '',                                       // 全身健康情况
    checkUp: '',                                           // 检查
    diagnosis: '',                                         // 诊断
    treatmentPlan: '',                                     // 治疗方案
    firstQuestion: '',                                     // 初始提问
    consultationCaseMediaDtoList: [
      // {
      //   type: 0,   // 资料类型(0星球影像、1其他资料、2全景片、3侧位片、4正面像、5侧面像、6正面咬合像、7正面咬合45度像、8左侧咬合像、9右侧咬合像、10上牙弓像、11下牙弓像)
      //   fileUrl: '',  // 文件路径
      //   fileName: '',  // 文件名称
      //   fileSuffix: '',   // 文件后缀
      //   fileSize: '',   // 文件大小
      // }
    ],      // 病例媒体信息集合
    orderNumber: null,   // 订单号，FY+年月日时分秒+5位随机数，在前端生成是为了实现移动端创建新指导时点击复制链接的暂存功能
    processNode: 2,      // 流程节点(图文流程节点[1选择会诊方式、2描述病例问题、3支付会诊金、4病例资料被查看、5问题被回复并对话、6结束会诊交易成功];  视频流程节点[1选择会诊方式、2描述病例问题、3提交会诊单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、7结束会诊、8确认并支付会诊金、9交易成功])
  }
  const [formState, setFormState] = useState(initialFormState)
  const [isClickSubmit, setIsClickSubmit] = useState(false)            // 是否点击了提交按钮，用来判断是否显示表单检验红字的
  const [updateState, setUpdateState] = useState(false)                // 用来replace后，来触发页面state渲染更新的参数

  useEffect(() => {
    if (consultationId) {
      getConsultationAndCaseInfo()
    } else {
      // 生成订单号，作用：点击复制链接时，用携带订单号的链接在PC端打开，可以根据订单号查到会诊信息
      // const orderNumber = 'FY' + dayjs().format('YYYYMMDDHHmmss') + Math.random().toString().slice(2,7)
      // setFormState({
      //   ...formState,
      //   orderNumber, // 订单号，FY+年月日时分秒+5位随机数，在前端生成是为了实现移动端创建新指导时点击复制链接的暂存功能
      // })
      message.error('指导ID缺失')
    }

    // 实时保存逻辑
    pageDidMount = true
    return () => {
      pageDidMount = false
      clearTimeout(timer)
    }
  }, [])

  // 实时保存逻辑
  useEffect(() => {
    // 初次进入页面时不执行
    if (!pageDidMount) {
      return
    }
    console.log('暂存暂存暂存暂存1111111111')
    // 暂存
    clearTimeout(timer)
    timer = setTimeout(() => {
      editConsultationInfo()
    }, 2000)

  }, [formState])

  // 查询指导和病例详情
  const getConsultationAndCaseInfo = () => {
    dispatch({
      type: 'consultation/getConsultationAndCaseInfo',
      payload: {
        consultationId: consultationId,      // 指导ID
        type: 1,                              // 1 创建图文、视频指导，2 详情页
      }
    }).then(res => {
      const { code, content, msg } = res
      // 除专家和用户之外的第三人查看，提示
      if (code == 422) {
        Modal.alert({
          content: msg,
          onConfirm: () => {
            goBack()
          }
        })
        return
      }
      // 专家也不能看
      if (content && content.createUserId != UserInfo.friUserId) {
        Modal.alert({
          content: '对不起，您无权限查看该数据！',
          onConfirm: () => {
            goBack()
          }
        })
        return
      }
      // 流程节点
      // 图文[1选择指导方式、2描述病例问题、3支付指导费用、4病例资料被查看、5问题被回复并对话、6结束指导交易成功];
      // 视频[1选择指导方式、2描述病例问题、3提交指导单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、7结束指导、8确认并支付指导费用、9交易成功]
      if (content && content.processNode > 3 && pageFrom != 'ConsultationDetails') {
        Modal.alert({
          content: '链接已失效，可能因为指导订单已经提交成功，请前往我的指导查看订单',
          onConfirm: () => {
            goBack()
          }
        })
        return
      }

      if (code == 200 && content) {
        // 更新一下地址，避免有些参数没有
        history.replace({
          pathname,
          query: {
            ...query,
            consultationId: content.id, // 指导ID
            expertsUserId: content.expertsId, // 医生ID
            consultationType: content.type, // 指导类型，1图文，2视频
            orderCaseTemplate: content.orderCaseTemplate, // 1通用模板，2正畸模板
          }
        })

        // 病例信息
        const consultationCaseInfoDto = content.consultationCaseInfoDto || {}

        const checkedCaseTemplateListNew = []
        if (consultationCaseInfoDto && consultationCaseInfoDto.isTemplate == 1) {
          if (consultationCaseInfoDto.age || consultationCaseInfoDto.sex || consultationCaseInfoDto.chiefComplaint || consultationCaseInfoDto.presentDisease || consultationCaseInfoDto.previousHistory || consultationCaseInfoDto.wholeHealth) {
            checkedCaseTemplateListNew.push(1)
          }
          if (consultationCaseInfoDto.checkUp || consultationCaseInfoDto.diagnosis) {
            checkedCaseTemplateListNew.push(2)
          }
          if (consultationCaseInfoDto.treatmentPlanList && consultationCaseInfoDto.treatmentPlanList.length > 0) {
            checkedCaseTemplateListNew.push(3)
          }
        }
        setFormState({
          ...formState,
          checkedCaseTemplateList: checkedCaseTemplateListNew,       // 选中的病例模板选项[1,2,3]
          isTemplate: checkedCaseTemplateListNew.length > 0 ? 1 : 0,  // 是否是模板(1是，0否)
          noTemplateDescription: consultationCaseInfoDto.noTemplateDescription,       // 病例描述（无模板）
          age: consultationCaseInfoDto.age || '',                                     // 年龄
          sex: consultationCaseInfoDto.sex || '',                                     // 性别
          chiefComplaint: consultationCaseInfoDto.chiefComplaint || '',               // 主诉
          presentDisease: consultationCaseInfoDto.presentDisease || '',               // 现病史
          previousHistory: consultationCaseInfoDto.previousHistory || '',             // 既往史
          wholeHealth: consultationCaseInfoDto.wholeHealth || '',                     // 全身健康状况
          checkUp: consultationCaseInfoDto.checkUp || '',                             // 检查
          diagnosis: consultationCaseInfoDto.diagnosis || '',                         // 诊断
          treatmentPlan: consultationCaseInfoDto.treatmentPlanList && consultationCaseInfoDto.treatmentPlanList[0] || '', // 治疗计划
          firstQuestion: consultationCaseInfoDto.firstQuestion || '',                 // 初始提问
          consultationCaseMediaDtoList: consultationCaseInfoDto.consultationCaseMediaDtoList || [],    // 媒体信息
          orderNumber: content.orderNumber,                                       // 订单号
          processNode: content.processNode, // 图文[1选择指导方式、2描述病例问题、3支付指导费用、4病例资料被查看、5问题被回复并对话、6结束指导交易成功];
          // 视频[1选择指导方式、2描述病例问题、3提交指导单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、7结束指导、8确认并支付指导费用、9交易成功]
        })

      } else {
        Toast.show(msg || '查询指导和病例详情失败')
      }
    }).catch(err => {})
  }

  // 输入病例描述
  const caseDescriptionOnChange = (value) => {
    setFormState({
      ...formState,
      noTemplateDescription: value,
    })
  }

  // 使用病例模板
  const selectCaseTemplate = (value, checked) => {
    const checkedCaseTemplateListClone = cloneDeep(formState.checkedCaseTemplateList)
    if (checked) {
      checkedCaseTemplateListClone.push(value)
    } else {
      const index = checkedCaseTemplateListClone.indexOf(value)
      checkedCaseTemplateListClone.splice(index, 1)
    }
    setFormState({
      ...formState,
      checkedCaseTemplateList: checkedCaseTemplateListClone,                   // 选择的病例模板选项
      isTemplate: checkedCaseTemplateListClone.length > 0 ? 1 : 0,             // 是否使用了病例模板
      noTemplateDescription: '',                                               // 未使用模板时的病例描述
      age: value == 1 ? '' : formState.age,                                    // 年龄
      sex: value == 1 ? '' : formState.sex,                                    // 性别
      chiefComplaint: value == 1 ? '' : formState.chiefComplaint,              // 主诉
      presentDisease: value == 1 ? '' : formState.presentDisease,              // 现病史
      previousHistory: value == 1 ? '' : formState.previousHistory,            // 既往史
      wholeHealth: value == 1 ? '' : formState.wholeHealth,                    // 全身健康情况
      checkUp: value == 2 ? '' : formState.checkUp,                            // 检查
      diagnosis: value == 2 ? '' : formState.diagnosis,                        // 诊断
      treatmentPlan: value == 3 ? '' : formState.treatmentPlan,                // 治疗方案
    })
  }

  // 输入年龄
  const ageOnChange = (value) => {
    setFormState({
      ...formState,
      age: value,
    })
  }

  // 输入性别
  const sexOnChange = (value) => {
    setFormState({
      ...formState,
      sex: value,
    })
  }

  // 输入主诉
  const chiefComplaintOnChange = (value) => {
    setFormState({
      ...formState,
      chiefComplaint: value,
    })
  }

  // 输入现病史
  const presentDiseaseOnChange = (value) => {
    setFormState({
      ...formState,
      presentDisease: value,
    })
  }

  // 输入既往史
  const previousHistoryOnChange = (value) => {
    setFormState({
      ...formState,
      previousHistory: value,
    })
  }

  // 输入全身健康情况
  const wholeHealthOnChange = (value) => {
    setFormState({
      ...formState,
      wholeHealth: value,
    })
  }

  // 输入检查
  const checkUpOnChange = (value) => {
    setFormState({
      ...formState,
      checkUp: value,
    })
  }

  // 输入诊断
  const diagnosisOnChange = (value) => {
    setFormState({
      ...formState,
      diagnosis: value,
    })
  }

  // 输入治疗方案
  const treatmentPlanOnChange = (value) => {
    setFormState({
      ...formState,
      treatmentPlan: value,
    })
  }

  // 输入初始提问
  const firstQuestionOnChange = (value) => {
    setFormState({
      ...formState,
      firstQuestion: value,
    })
  }

  // 上传校验规则，图片
  const beforeUpload = (file) => {
    console.log('beforeUpload',file)
    const isSize = file.size / 1024 / 1024 < 15
    if (!isSize) {
      Toast.show({content: '超过15M限制，不允许上传~'})
      return false
    }

    const { name: fileName } = file || {}
    // 添加对文件后缀名的限制
    const suffix = fileName.substring(fileName.lastIndexOf('.')+1).toLowerCase()
    const isJpgOrPng = file.type === 'image/jpg' || file.type === 'image/jpeg' ||  file.type === 'image/png'
    const isSuffixByJpgOrPng = (
      suffix === 'jpg'
      || suffix === 'jpeg'
      || suffix === 'png'
    )
    if (!isJpgOrPng || !isSuffixByJpgOrPng) {
      Toast.show({content: '只能上传JPG、JPEG、PNG格式的图片~'})
      return false
    }

    return new Promise((resolve, reject) => {
      const fileReaderBuffer = new FileReader();
      fileReaderBuffer.onload = async () => {
        const type = getFileType(fileReaderBuffer);
        if (type === 'unknown') {
          reject()
          return;
        }
        if (type.includes('/heic')) {
          heic2any({ blob: file, toType: 'image/jpeg' }).then((blob) => {
            resolve(blob)
          }).catch((err) => {
            reject()
          });
        } else {
          resolve(file)
        }
      };
      fileReaderBuffer.readAsArrayBuffer(file);
    })
  }

  // 上传完成回调，图片
  const uploadOnChange = (info) => {
    console.log('uploadOnChange',info)
    if (info.file.status === 'uploading') {
      Toast.show({
        icon: 'loading',
        content: '',
        duration: 0,
      })
    }

    // 判断当前上传已被拦截
    if (info && !info.file.status) {
      Toast.clear()
      return
    }

    // 上传结束
    if (info && info.file.status === 'error') {
      Toast.clear()
      Toast.show('上传失败')
      return
    }

    if (info && info.file.status === 'done') {
      Toast.clear()
      const file = info.file
      const res = info.file.response || {}
      const { code, content, msg } = res
      if (code == 200 && content) {
        // 上传图片不能超过9张
        if (formState.consultationCaseMediaDtoList.filter(item => item.type == 0).length >= 9) {
          return
        }
        // 获取文件名和后缀
        const suffix = file.name.substring(file.name.lastIndexOf('.')+1)
        const name = file.name.substring(0, file.name.lastIndexOf('.'))
        const consultationCaseMediaDtoListClone = cloneDeep(formState.consultationCaseMediaDtoList)
        // 二重保险，就算少上传了图片也不展示重复的
        if (consultationCaseMediaDtoListClone.findIndex(item => item.fileUrlShow == content.fileUrlView) > -1) {
          return
        }
        consultationCaseMediaDtoListClone.push({
          type: 0,   // 资料类型(0星球影像、1其他资料、2全景片、3侧位片、4正面像、5侧面像、6正面咬合像、7正面咬合45度像、8左侧咬合像、9右侧咬合像、10上牙弓像、11下牙弓像)
          fileSize: file.size,           // 文件大小
          fileName: name,                // 文件名
          fileSuffix: suffix,            // 文件后缀
          fileUrl: content.fileUrl,      // 文件路径
          fileUrlShow: content.fileUrlView,
        })
        setFormState({
          ...formState,
          consultationCaseMediaDtoList: consultationCaseMediaDtoListClone,       // 媒体文件list
        })
      } else {
        Toast.show({content: msg || '上传失败'})
      }
    }
  }

  // 上传图片headers
  const getHeaders=() =>{
    // 获取环境类型，1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用   5 FRIDAY app中使用
    const env = getOperatingEnv()

    // FRIDAY APP中本地存储值：access_token、client、phone、user_name、wxuserId、userInfo
    return {// token
      access_token:localStorage.getItem('access_token') || '',
      username: env == '5' ? localStorage.getItem('user_name') : localStorage.getItem('vxOpenIdCipherText') ? localStorage.getItem('vxOpenIdCipherText')  : UserInfo?.phone,
      client: env == '5' ? localStorage.getItem('client') : 'WX',
      type: env == '1' ? '' : '1', // h5 传1
    }
  }

  // 删除图片
  const deleteImage = (index) => {
    const consultationCaseMediaDtoListClone = cloneDeep(formState.consultationCaseMediaDtoList)
    consultationCaseMediaDtoListClone.splice(index, 1)
    setFormState({
      ...formState,
      consultationCaseMediaDtoList: consultationCaseMediaDtoListClone,         // 媒体文件list
    })
  }

  // 复制成功回调
  const onCopy = () => {
    message.success('复制成功')
  }

  // 点击确定提交/下一步
  const submit = () => {
    // 页面输入框是否显示红字的状态
    setIsClickSubmit(true)
    // 校验影像资料
    if (formState.consultationCaseMediaDtoList.length == 0) {
      return
    }
    // 校验提问
    if (!formState.firstQuestion) {
      return
    }
    // 清除实时保存定时器
    clearTimeout(timer)
    // loading
    Toast.show({
      icon: 'loading',
      content: '',
      duration: 0,
    })
    let postParams = {
      id: consultationId,                              // 指导ID
      type: consultationType,                          // 指导类型，1 图文，2 视频
      expertsId: expertsUserId, // 指导医生ID
      orderCaseTemplate: orderCaseTemplate, // 订单病例模板 1. 通用病例  2正畸病例
      processNode: formState.processNode < 4 ? 3 : formState.processNode, // 图文[1选择指导方式、2描述病例问题、3支付指导费用、4病例资料被查看、5问题被回复并对话、6结束指导交易成功];
      // 视频[1选择指导方式、2描述病例问题、3提交指导单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、7结束指导、8确认并支付指导费用、9交易成功]
      consultationCaseInfoDto: {
        caseName: '指导病例',
        isTemplate: formState.isTemplate,                                  // 是否是模板(1是，0否)
        noTemplateDescription: formState.noTemplateDescription,            // 无模板的描述
        firstQuestion: formState.firstQuestion,                            // 初始提问
        consultationCaseMediaDtoList: formState.consultationCaseMediaDtoList,   // 病例媒体信息集合
        treatmentPlanList: formState.treatmentPlan ? [formState.treatmentPlan] : [],   // 治疗方案集合(兼容瑞尔数组)
        diagnosis: formState.diagnosis,                // 诊断
        checkUp: formState.checkUp,                    // 检查
        wholeHealth: formState.wholeHealth,            // 全身健康情况
        previousHistory: formState.previousHistory,    // 既往史
        presentDisease: formState.presentDisease,      // 现病史
        chiefComplaint: formState.chiefComplaint,      // 主诉
        sex: formState.sex,                            // 性别
        age: formState.age,                            // 年龄
        templateType: formState.templateType,          // 模板类型(1星球、2瑞尔)
      },
    }
    dispatch({
      type: 'consultation/editConsultationInfo',
      payload: {
        postParams,
      }
    }).then(res => {
      Toast.clear()
      const { code, content, msg } = res
      if (code == 200 && content) {
        // 表示从指导详情来，编辑完成后返回
        if (pageFrom == 'ConsultationDetails') {
          history.goBack()
          return
        }

        history.replace({
          pathname: '/ConsultationModule/StartConsultation/Step4',
          query: {
            ...query,
          }
        })
      } else {
        Toast.show(msg || '提交失败')
      }
    }).catch(err => {})
  }

  // 实时保存
  const editConsultationInfo = () => {
    let postParams = {
      id: consultationId,                              // 指导ID
      type: consultationType,                          // 指导类型，1 图文，2 视频
      expertsId: expertsUserId, // 指导医生ID
      orderCaseTemplate: orderCaseTemplate, // 订单病例模板 1. 通用病例  2正畸病例
      processNode: formState.processNode, // 图文[1选择指导方式、2描述病例问题、3支付指导费用、4病例资料被查看、5问题被回复并对话、6结束指导交易成功];
      // 视频[1选择指导方式、2描述病例问题、3提交指导单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、7结束指导、8确认并支付指导费用、9交易成功]
      consultationCaseInfoDto: {
        caseName: '指导病例',
        isTemplate: formState.isTemplate,                                  // 是否是模板(1是，0否)
        noTemplateDescription: formState.noTemplateDescription,            // 无模板的描述
        firstQuestion: formState.firstQuestion,                            // 初始提问
        consultationCaseMediaDtoList: formState.consultationCaseMediaDtoList,   // 病例媒体信息集合
        treatmentPlanList: formState.treatmentPlan ? [formState.treatmentPlan] : [],   // 治疗方案集合(兼容瑞尔数组)
        diagnosis: formState.diagnosis,                // 诊断
        checkUp: formState.checkUp,                    // 检查
        wholeHealth: formState.wholeHealth,            // 全身健康情况
        previousHistory: formState.previousHistory,    // 既往史
        presentDisease: formState.presentDisease,      // 现病史
        chiefComplaint: formState.chiefComplaint,      // 主诉
        sex: formState.sex,                            // 性别
        age: formState.age,                            // 年龄
        templateType: formState.templateType,          // 模板类型(1星球、2瑞尔)
      },
    }
    dispatch({
      type: 'consultation/editConsultationInfo',
      payload: {
        postParams,
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200) {

      } else {
        message.error(msg || '实时保存失败')
      }
    }).catch(err => {})
  }

  // 两个问题，1 ios部分机型，弹出软键盘后，拉动页面拉到底后，在拉会拉出一段空白。2 收起软键盘页面没有自动回落。
  // 解决空白为，需要禁止页面滚动，这个页面表单较多，使用体验较差，所以暂不处理。只解决问题2
  // 下面是解决ios部分机型，弹出键盘后，页面底部会拉出一段空白的问题（下面已注释的代码）
  useEffect(() => {
    // 添加ios监听事件，解决弹出键盘后，页面底部会拉出一段空白的问题
    const userAgent = window.navigator.userAgent
    if (/iPhone/.test(userAgent)) {
      document.body.addEventListener('focusin', focusinHandler)
      document.body.addEventListener('focusout', focusoutHandler)
    }

    // 移除监听事件
    return () => {
      if (/iPhone/.test(userAgent)) {
        document.body.removeEventListener('focusin', focusinHandler)
        document.body.removeEventListener('focusout', focusoutHandler)
      }
    }
  }, [])

  // useEffect(() => {
  //   // 添加ios监听事件，解决弹出键盘后，页面底部会拉出一段空白的问题
  //   const userAgent = window.navigator.userAgent
  //   if (/iPhone/.test(userAgent) && formState.isTemplate == 1) {
  //     // 阻止冒泡。病例模板的框里得允许滚动
  //     if (document.getElementById('case_template_box')) {
  //       document.getElementById('case_template_box').addEventListener('touchmove', stopPropagationHandler, {passive: false})
  //     }
  //   }
  // }, [formState.isTemplate])

  // 是否已经有病例模板的框中的表单聚焦了
  // const [isFocusInCaseTemplateBox, setIsFocusInCaseTemplateBox] = useState(true)

  // 阻止冒泡
  // const stopPropagationHandler = (e) => {
  //   e.stopPropagation()
  // }
  //
  // // 阻止默认事件
  // const preventDefaultHandler = (e) => {
  //   e.preventDefault()
  // }

  // ios，监听页面表单聚焦事件
  const focusinHandler = (e) => {
    const targetInputElement = e.target
    if (targetInputElement) {
      // 聚焦时键盘弹出，焦点在输入框之间切换时，会先触发上一个输入框的失焦事件，再触发下一个输入框的聚焦事件
      isReset = false
      // if (document.getElementById('case_template_box')) {
      //   if (!isFocusInCaseTemplateBox && document.getElementById('case_template_box').contains(targetInputElement)) {
      //     setIsFocusInCaseTemplateBox(true)
      //     targetInputElement.scrollIntoView({behavior: 'smooth'})
      //   }
      // } else {
      //   setIsFocusInCaseTemplateBox(false)
      //   targetInputElement.scrollIntoView({behavior: 'smooth'})
      // }
    }
    // 阻止默认事件，禁止滚动
    // document.getElementsByTagName('body')[0].addEventListener('touchmove', preventDefaultHandler, {passive: false})

  }

  // ios，监听页面表单失去焦点事件
  const focusoutHandler = (e) => {
    // 聚焦时键盘弹出，焦点在输入框之间切换时，会先触发上一个输入框的失焦事件，再触发下一个输入框的聚焦事件
    isReset = true
    setTimeout(() => {
      // 当焦点在弹出层的输入框之间切换时先不归位
      if (isReset) {
        window.scrollTo({ top: 120, behavior: "smooth" }) // 确定延时后没有聚焦下一元素，是由收起键盘引起的失焦，则强制让页面归位，解决软键盘收起后，拉上去的页面没有自动收回来的问题
        // 打开默认事件，允许滚动
        // document.getElementsByTagName('body')[0].removeEventListener('touchmove', preventDefaultHandler, {passive: false})
      }
    }, 30)
  }

  // loading
  const getConsultationAndCaseInfoLoading = !!loading.effects['consultation/getConsultationAndCaseInfo']

  // 自定义上传方法，为了解决iphone手机（8p），qq浏览器有上传图片重复bug
  const uploadCustomRequest = ({onProgress,onError,onSuccess,data,filename,file,withCredentials,action,headers}) => {
    // 重复原因是少调口了，比如选择3张图片，实际只调了2遍接口，真正传上去的是两张
    const formData = new FormData()
    formData.append('file', file)

    // 加alert就不会重复，所以加个定时器，不同时调接口
    if (/iPhone/.test(window.navigator.userAgent) && /QQBrowser/.test(window.navigator.userAgent)) {
      const random = Math.floor(Math.random()*1000) + 100
      setTimeout(() => {
        request(action, {
          method: 'POST',
          body: formData,
          headers: headers,
        }).then(res => {
          const { code } = res
          if (code == 200) {
            onSuccess(res)
          } else {
            onError(res)
          }
        })
      }, random)
    } else {
      request(action, {
        method: 'POST',
        body: formData,
        headers: headers,
      }).then(res => {
        const { code } = res
        if (code == 200) {
          onSuccess(res)
        } else {
          onError(res)
        }
      })
    }
  }

  // 返回
  const goBack = () => {
    if (history.length > 1) {
      history.goBack()
    } else {
      history.replace('/')
    }
  }

  return (
    <Spin spinning={getConsultationAndCaseInfoLoading}>
      <Navbar title={pageFrom == 'ConsultationDetails' ? '编辑病例' : '创建病例'}/>
      <div className={styles.container}>
        {/* 查看完整服务流程按钮及弹窗 */}
        <div className={styles.complete_process_wrap}>
          <StartConsultationSteps/>
        </div>

        <div className={classNames(styles.block, styles.block_padding)}>
          <div className={styles.form_label}>
            病例描述
          </div>
          {
            formState.checkedCaseTemplateList.length == 0 ?
              <div className={styles.form_content_textarea}>
                <TextArea
                  rows={4}
                  placeholder='请输入病例描述，或选择下方病例模板，指导结果更精准'
                  value={formState.noTemplateDescription}
                  onChange={caseDescriptionOnChange}
                  maxLength={200}
                />
              </div>
              :
              <div className={styles.form_content_box} id='case_template_box'>
                {
                  formState.checkedCaseTemplateList.indexOf(1) > -1 &&
                  <>
                    <div className={styles.child_item}>
                      <div className={styles.child_item_label}>患者年龄：</div>
                      <div className={styles.child_item_content}>
                        <TextArea
                          autoSize={true}
                          rows={1}
                          maxLength={200}
                          value={formState.age}
                          onChange={ageOnChange}
                        />
                      </div>
                    </div>
                    <div className={styles.child_item}>
                      <div className={styles.child_item_label}>性别：</div>
                      <div className={styles.child_item_content}>
                        <TextArea
                          autoSize={true}
                          rows={1}
                          maxLength={200}
                          value={formState.sex}
                          onChange={sexOnChange}
                        />
                      </div>
                    </div>
                    <div className={styles.child_item}>
                      <div className={styles.child_item_label}>主诉：</div>
                      <div className={styles.child_item_content}>
                        <TextArea
                          autoSize={true}
                          rows={1}
                          maxLength={200}
                          value={formState.chiefComplaint}
                          onChange={chiefComplaintOnChange}
                        />
                      </div>
                    </div>
                    <div className={styles.child_item}>
                      <div className={styles.child_item_label}>现病史：</div>
                      <div className={styles.child_item_content}>
                        <TextArea
                          autoSize={true}
                          rows={1}
                          maxLength={200}
                          value={formState.presentDisease}
                          onChange={presentDiseaseOnChange}/>
                      </div>
                    </div>
                    <div className={styles.child_item}>
                      <div className={styles.child_item_label}>既往史：</div>
                      <div className={styles.child_item_content}>
                        <TextArea
                          autoSize={true}
                          rows={1}
                          maxLength={200}
                          value={formState.previousHistory}
                          onChange={previousHistoryOnChange}/>
                      </div>
                    </div>
                    <div className={styles.child_item}>
                      <div className={styles.child_item_label}>全身健康情况：</div>
                      <div className={styles.child_item_content}>
                        <TextArea
                          autoSize={true}
                          rows={1}
                          maxLength={200}
                          value={formState.wholeHealth}
                          onChange={wholeHealthOnChange}/>
                      </div>
                    </div>
                  </>
                }
                {
                  formState.checkedCaseTemplateList.indexOf(2) > -1 &&
                  <>
                    <div className={styles.child_item}>
                      <div className={styles.child_item_label}>检查：</div>
                      <div className={styles.child_item_content}>
                        <TextArea
                          autoSize={true}
                          rows={1}
                          maxLength={200}
                          value={formState.checkUp}
                          onChange={checkUpOnChange}/>
                      </div>
                    </div>
                    <div className={styles.child_item}>
                      <div className={styles.child_item_label}>诊断：</div>
                      <div className={styles.child_item_content}>
                        <TextArea
                          autoSize={true}
                          rows={1}
                          maxLength={200}
                          value={formState.diagnosis}
                          onChange={diagnosisOnChange}/>
                      </div>
                    </div>
                  </>
                }
                {
                  formState.checkedCaseTemplateList.indexOf(3) > -1 &&
                  <>
                    <div className={styles.child_item}>
                      <div className={styles.child_item_label}>治疗方案：</div>
                      <div className={styles.child_item_content}>
                        <TextArea
                          autoSize={true}
                          rows={1}
                          value={formState.treatmentPlan}
                          onChange={treatmentPlanOnChange}
                          maxLength={200}
                        />
                      </div>
                    </div>
                  </>
                }

              </div>
          }

          {/* 模版选项 */}
          <div className={styles.textarea_template}>
            {
              caseTemplateList.map((item, index) => {
                const checked = formState.checkedCaseTemplateList.indexOf(item.code) > -1
                return (
                  <div
                    key={index}
                    className={classNames(styles.textarea_template_item, {
                      [styles.textarea_template_item_checked]: checked,
                    })}
                    onClick={() => selectCaseTemplate(item.code, !checked)}
                  >
                    <PlusOutlined/>{item.name}
                  </div>
                )
              })
            }
          </div>
        </div>

        <div className={styles.block}>
          <div className={styles.form_label}>
            <span className={styles.required_mark}>*</span>
            影像资料
            <span className={styles.notes}>(请至少上传1张影像)</span>
          </div>
          <div className={styles.form_content_upload}>
            {
              formState.consultationCaseMediaDtoList.map((item, index) => {
                if (item.type == 0) {
                  return (
                    <div key={index} className={styles.upload_item} style={{backgroundImage: `url(${item.fileUrlShow})`}}>
                      <div className={styles.upload_delete_btn} onClick={() => deleteImage(index)}><CloseCircleFilled/></div>
                    </div>
                  )
                }
                return null
              })
            }
            <div className={classNames(styles.upload_btn, {
              [styles.disabled]: formState.consultationCaseMediaDtoList.filter(item => item.type == 0).length >= 9
            })}>
              <PlusOutlined/>
              <Upload
                className={styles.edit_head_picture}
                headers={getHeaders()}
                accept="image/*"
                listType="picture-card"
                action={`/api/server/base/uploadFile?${stringify({ fileType: 18, userId: UserInfo?.friUserId})}`}
                onChange={uploadOnChange}
                beforeUpload={beforeUpload}
                showUploadList={false}
                multiple={true}
                customRequest={uploadCustomRequest}
              />
            </div>
          </div>

          {
            isClickSubmit && formState.consultationCaseMediaDtoList.filter(item => item.type == 0).length == 0 &&
            <div className={styles.form_error}>请上传影像资料</div>
          }
        </div>

        {
          pageFrom != 'ConsultationDetails' &&
          <div className={classNames(styles.block, styles.block_padding)}>
            <div className={styles.form_label}><span className={styles.required_mark}>*</span>提问</div>
            <div className={styles.form_content_textarea}>
              <TextArea
                ref={inputRef}
                rows={3}
                placeholder='请输入针对以上病例您想要问的问题'
                value={formState.firstQuestion}
                onChange={firstQuestionOnChange}
                maxLength={200}
              />
            </div>

            {
              isClickSubmit && !formState.firstQuestion &&
              <div className={styles.form_error}>请填写提问</div>
            }
          </div>
        }

        <div className={styles.remark_box}>
          如您有ppt、word、pdf、zip等文件上传，请复制以下链接，从电脑端打开
          <Typography.Paragraph copyable={{
            text: window.location.origin + '/ConsultationModule/StartConsultation/Step3?' + stringify({
              ...query,
              copyUserId: UserInfo.friUserId,
            }),
            icon: [<span>复制地址</span>, <span>复制地址</span>],
            tooltips: [false, false],
            onCopy: onCopy,
          }}></Typography.Paragraph>
        </div>

        <div className={styles.fixed_box}>
          <div className={styles.btn} onClick={submit}>
            {pageFrom != 'ConsultationDetails' ? '下一步' : '确定'}
          </div>
        </div>
      </div>
    </Spin>
  )
}

export default connect(({ consultation, loading }: any) => ({ consultation, loading }))(Index)
