.modal {
  :global {
    .ant-modal-body {
      padding: 0;
    }
  }
}

.header {
  font-size: 18PX;
  color: #303133;
  line-height: 26PX;
  word-break: break-all;
  padding: 15PX 55PX 10PX 16PX;
  margin-bottom: 8PX;
}

// 用户列表
.scroll_wrap {
  height: 216PX;
  overflow-y: auto;
  .item {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: space-between;
    padding: 8PX 16PX;
    cursor: pointer;
    &:hover {
      background: rgba(0, 0, 0, 0.04);
    }
    .item_left {
      flex: 1;
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      overflow: hidden;
      .avatar_wrap {
        width: 24PX;
        height: 24PX;
        margin-right: 12PX;
        flex-shrink: 0;
      }
      .user_name {
        flex: 1;
        font-size: 14PX;
        color: #000;
        line-height: 20PX;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    & > img {
      flex-shrink: 0;
    }
  }
}

// 按钮
.btn_wrap {
  padding: 8PX 16PX 16PX;
  display: flex;
  justify-content: flex-end;
}

