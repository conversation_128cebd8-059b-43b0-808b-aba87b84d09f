/**
 * @Description: 逛广场页面二维码弹窗
 */
import React from 'react'
import QRcode from 'qrcode.react'
import { Modal } from 'antd'
import styles from './index.less'

interface PropsType {
  visible: any,                                            // 弹窗显隐
  onCancel: any,                                           // 关闭弹窗回调
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  return (
    <Modal
      open={props.visible}
      title="逛广场"
      className={styles.modal}
      width={474}
      closable={true}
      destroyOnClose={true}
      footer={null}
      onCancel={props.onCancel}
    >
      <div className={styles.container}>
        <div className={styles.qrcode_box}>
          <div className={styles.qrcode}>
            <QRcode
              value={`${window.location.origin}/Square`}
              size={154}
            />
          </div>
        </div>

        <div className={styles.msg}>微信扫码打开体验广场功能~</div>
      </div>
    </Modal>
  )
}

export default Index
