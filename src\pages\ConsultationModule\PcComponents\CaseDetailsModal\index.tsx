/**
 * @Description: PC端，病例详情弹窗，非模板
 */
import React from 'react'
import classNames from 'classnames'
import { Modal, Image } from 'antd'
import styles from './index.less'

interface PropsType {
  visible: boolean,                    // true，false
  caseData: any,                       // 病例数据
  onCancel: any,                       // 关闭弹窗
}

const Index: React.FC<PropsType> = (props: any) => {
  const { visible, caseData = {} } = props

  return (
    <>
      <Modal
        title="病例详情"
        width={791}
        visible={visible}
        onCancel={props.onCancel}
        className={styles.modal}
        destroyOnClose
        footer={null}
      >

        <div className={styles.container}>
          <div className={styles.item}>
            <div className={styles.label}>病例名称：</div>
            <div className={styles.text_value}>{caseData.caseName}</div>
          </div>
          {/*
            caseData.depSubjectDictsStrList && caseData.depSubjectDictsStrList.length > 0 &&
            <div className={styles.item}>
              <div className={styles.label}>涉及学科：</div>
              <div className={styles.tag_value}>
                {
                  caseData.depSubjectDictsStrList.map((item, index) => <div key={index} className={styles.tag}>{item}</div>)
                }
              </div>
            </div>
          */}

          <div className={styles.item}>
            <div className={styles.label}>病例描述：</div>
            <div className={styles.text_value}>{caseData.noTemplateDescription}</div>
          </div>
          <div className={classNames(styles.item, styles.img_item)}>
            <div className={styles.label}>影像资料：</div>
            <div className={styles.img_value}>
              <Image.PreviewGroup>
                {
                  caseData.consultationCaseMediaDtoList && caseData.consultationCaseMediaDtoList.length > 0 &&
                  caseData.consultationCaseMediaDtoList.map((item, index) => {
                    if (item.type == 0) {
                      return <div key={index} className={styles.img}>
                        <Image width={198} height={198} src={item.fileUrlShow} />
                      </div>
                    }
                    return null
                  })
                }
              </Image.PreviewGroup>
            </div>
          </div>
        </div>
      </Modal>
    </>
  )
}

export default Index
