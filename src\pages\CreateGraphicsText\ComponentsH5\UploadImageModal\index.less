.popup_container {
  :global {
    .adm-popup-body {
      border-radius: 16px 16px 0 0;
      display: flex;
      flex-direction: column;
    }
  }
}
.header {
  flex-shrink: 0;
  width: 100%;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  .gray_bar {
    width: 48px;
    height: 4px;
    border-radius: 4px;
    background: #D0D4D7;
  }
}
.tabs_box {
  flex-shrink: 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  column-gap: 46px;
  position: relative;
  :global {
    .anticon {
      font-size: 16px;
      color: #000;
      position: absolute;
      left: 12px;
      top: 0;
      padding: 4px;
    }
  }
  .tabs_item {
    font-size: 16px;
    color: #000;
    height: 30px;
    position: relative;
    &.selected {
      font-weight: 500;
      &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        margin-left: -15px;
        width: 30px;
        height: 4px;
        border-radius: 3px;
        background: #0095FF;
      }
    }
  }
}
.content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 10px 0;
  .image_box {
    display: flex;
    flex-wrap: wrap;
    .image_item_wrap {
      width: 25%;
      padding: 0 2px;
      margin-bottom: 8px;
    }
    .image_item {
      position: relative;
      border-radius: 4px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;

      :global {
        .anticon {
          position: absolute;
          top: 0;
          right: 0;
          font-size: 20px;
          color: rgba(0, 0, 0, 0.5);
        }
      }
      &::after {
        content: "";
        display: block;
        width: 100%;
        padding-top: 100%;
      }
      &.selected {
        .mask {
          position: absolute;
          display: block;
          border-radius: 4px;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0,0,0,0.4);
          z-index: 1;
        }
        .selected_number {
          position: absolute;
          display: block;
          top: 4px;
          left: 4px;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: #0095FF;
          color: #fff;
          font-size: 12px;
          line-height: 20px;
          text-align: center;
          z-index: 2;
        }
      }
    }

    .upload_item {
      position: relative;
      background: #F8F8F8;
      border-radius: 4px;
      &::after {
        content: "";
        display: block;
        width: 100%;
        padding-top: 100%;
      }
      :global {
        .ant-upload {
          position: absolute;
          width: 100%;
          height: 100%;
          left: 0;
          top: 0;
          z-index: 2;
          display: block !important;
        }
      }
      .wrap {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        :global {
          .anticon {
            font-size: 24px;
            color: #CBCBCB;
            margin-bottom: 4px;
          }
        }
        & > span {
          font-size: 13px;
          color: #999;
          line-height: 18px;
        }
      }

    }
  }


}
.empty_wrap {
  width: 100%;
  display: flex;
  justify-content: center;
}

.bottom_btn_box {
  flex-shrink: 0;
  padding: 0 12px;
  .btn {
    text-align: center;
    height: 40px;
    line-height: 42px;
    font-size: 16px;
    color: #fff;
    background: #0095FF;
    border-radius: 20px;
  }
}
