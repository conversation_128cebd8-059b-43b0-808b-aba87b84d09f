/**
 * @Description: 创建王国、空间相关模块总弹窗
 */
import React, { useState, useEffect } from 'react'
import { connect } from 'umi'
import { Popup } from 'antd-mobile'
import styles from './index.less'
import CreateSelectType from './CreateSelectType';  // 选择创建王国/空间
import CreateKingdom from './CreateKingdom';        // 创建王国
import SearchKing from './SearchKing';              // 选择国王
import CreateSpace from './CreateSpace';            // 创建空间
import SearchHost from './SearchHost';              // 选择主持人
import SearchGuest from './SearchGuest';            // 选择嘉宾
import AppointmentTimeModal from './AppointmentTimeModal'; // 预约开始
import SelectAudience from './SelectAudience';      // 选择观众
import SelectSpaceType from './SelectSpaceType';    // 选择空间类型
import SelectKingdomAudience from './SelectKingdomAudience';  // 选择可见王国
import SelectEnterprise from './SelectEnterprise';  // 选择可见企业/品牌用户
import SearchInstitution from './SearchInstitution'; // 搜索机构
import ChooseKingdom from './ChooseKingdom';  // 选择关联王国
import SelectCover from './SelectCover';    // 选择会议封面
import SelectMeetExpectDuration from './SelectMeetExpectDuration';  // 选择会议预计时长
import SelectMeetingLevel from './SelectMeetingLevel';  // 选择会议级别

const Index: React.FC = (props: any) => {
  const { userInfoStore, dispatch, comeType } = props || {};
  const { createModalVisible } = userInfoStore;
  const [ openModalKey, setOpenModalKey ] = useState(99); // 打开的弹框

  useEffect(() => {
    // 判断是否是从王国成功页进来的，如果是传 2 直接打开创建王国空间
    setOpenModalKey(comeType ? comeType : 99);
  }, [comeType, createModalVisible])

  // 创建后将数据保存到仓库中
  const createBtnFn = (options) => {
    const { type } = options || {};
    setOpenModalKey(type)
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
       creatTabSelectDate: options
      }
    })
  }

  // 关闭弹框
  const closeModalBtnFn = () => {
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        createModalVisible: false,
        selectCreateType: 0
      }
    });

    // 判断是否是从创建成功页面打开的，如果是则保留国王和王国相关数据，不是则清空
    if(!comeType) {
      dispatch({
        type: 'userInfoStore/clean',
        payload: {
          selectedKing: null, // 已选择-国王
          selectedKingdom: null, // 已选择-王国
        }
      })
    }

    /*dispatch({
      type: 'userInfoStore/clean',
      payload: {
        createModalVisible: false, // 是否展示创建空间王国下拉弹框
        selectCreateType: 0, // 创建空间王国选中的tab值
        selectedGuest: [], // 已选择嘉宾
        creatTabSelectDate: {}, // 创建空间王国选择后的数据
        spaceIntroduceVal: null, // 空间介绍
        spaceCoverUrl: null, // 空间封面路径
        spaceAdvertisingUrl: null, // 空间广告路径
        spacePasswordText: null, // 密码
        spaceName: null, // 空间名称
        kingdomName: null, // 王国名称
        kingdomIntroduce: null, // 王国介绍
        kingdomCoverUrl: null, // 王国封面图路径
        selectedCompere: null, // 已选择-主持人
        spaceVideoId: null, // 空间视频id
        spaceVideoUrl: null, // 空间视频上传文件地址
        spaceVideoName: null, // 视频文件名
        appointmentStartTime: null, // 空间预约时间
        isEditSpace: false, // 是否编辑空间
        isEditKingdom: false, // 是否编辑王国
        spaceFromEnter: {}, // 从哪里进入的创建空间/编辑空间相关信息
        kingdomFromEnter: {}, // 从哪里进入的创建王国/编辑王国相关信息
        spaceTypeId: 0, // 空间类型
        spaceTypeName: null, // 空间类型名称
        allViewersState: true, // 选择观众默认所有人可见状态
        selectedKingdomAudience: [], // 观众-选择可见王国
        spectatorType: 0, // 观众类型 0默认所有人 1指定王国成员可见 2指定企业品牌用户可见
        kingdomListArr: [], // 观众-王国列表数据
        enterpriseUserData: [], // 选择可见企业/品牌用户，当前企业下的机构数据
        enterpriseUserTab: 0, // 选择可见企业/品牌用户，页面tab
        enterpriseUserSelectData: [], // 选择可见企业/品牌用户，选择后的数据
        enterpriseText: null, // 选择可见企业/品牌用户，弹框名称
        topicHomeTopicId: null,   // 话题主页打开创建弹窗时，保存话题ID，跳转创建文章和帖子要带过去
        topicHomeTopicName: null,   // 话题主页打开创建弹窗时，保存话题name，跳转创建文章和帖子要带过去
      }
    });*/
    // 判断是否是从王国成功页进来的，如果是传 2 直接打开创建王国空间 3 创建王国
    setOpenModalKey(comeType ? comeType : 99)
  }

  // 返回事件
  const goBack = (goBackUrl) => {
    setOpenModalKey(goBackUrl)
  }

  const goBackBySpace = ()=> {
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: { createModalVisible:false, }
    })
  }

  // 渲染模版内容
  const RenderComponent = () => {
    // 选择创建/王国空间
    if(openModalKey == 99){
      return <CreateSelectType createBtnFn={(options) => {createBtnFn(options)}} />;
    }
    /*// 创建空间(1开放空间，2王国空间)
    if(openModalKey == 1 || openModalKey == 2 || openModalKey == 14) {
      return <CreateSpace selectCreateType={userInfoStore.selectCreateType} goBack={(type)=>{goBack(type)}} />;
    }*/
    // 创建王国
    if(openModalKey == 3) {
      return <CreateKingdom goBack={(type)=>{goBack(type)}} />;
    }
    // 选择主持人(从1/2空间(主持人)-跳转-4选择主持人)
    if(openModalKey == 4) {
      return <SearchHost goBack={(type)=>{goBack(type)}} />;
    }
    // 邀请嘉宾(从1/2空间(嘉宾)-跳转-5邀请嘉宾)
    if(openModalKey == 5) {
      return <SearchGuest goBack={(type)=>{goBackBySpace()}} />
    }
    // 选择国王(从3王国(选择国王)-跳转-6选择国王)
    if(openModalKey == 6) {
      return <SearchKing goBack={(type)=>{goBack(type)}} />
    }

    // 选择观众(从1/2空间(观众)-跳转-7 选择观众)
    if(openModalKey == 7) {
      return <SelectAudience goBack={(type)=>{goBack(type)}} />
    }

    // 选择空间类型(从1/2空间(空间类型)-跳转-8 选择空间类型)
    if(openModalKey == 8) {
      return <SelectSpaceType goBack={(type)=>{goBack(type)}} />
    }

    // 预约开始(从1/2空间(预约开始)-跳转-10预约开始)
    if(openModalKey == 10) {
      return <AppointmentTimeModal goBack={(type)=>{goBack(type)}} />
    }

    // // 选择关联王国（从1/2创建空间（关联王国）-跳转-20关联王国）
    if(openModalKey == 20) {
      return <ChooseKingdom goBack={(type)=>{goBackBySpace()}} />
    }

    // 选择可见王国(从7观众-跳转-11 选择可见王国)
    if(openModalKey == 11) {
      return <SelectKingdomAudience goBack={(type)=>{goBack(type)}} />
    }

    // 选择可见企业/品牌用户(从7选择观众(企业/品牌用户)-跳转-12 选择可见企业/品牌用户)
    if(openModalKey == 12) {
      return <SelectEnterprise goBack={(type)=>{goBack(type)}} />
    }

    // 搜索机构(从12选择可见企业/品牌用户(选择机构)-跳转-13 搜索机构)
    if(openModalKey == 13) {
      return <SearchInstitution goBack={(type)=>{goBack(type)}} />
    }
    // 选择会议封面
    if(openModalKey == 15) {
      return <SelectCover goBack={(type)=>{goBackBySpace()}} />
    }

    // 选择会议时长
    if(openModalKey == 16) {
      return <SelectMeetExpectDuration goBack={(type)=>{goBackBySpace()}} />
    }

    // 选择会议级别
    if(openModalKey == 17) {
      return <SelectMeetingLevel goBack={(type)=>{goBackBySpace()}} />
    }

  }

  return (
    <Popup
      visible={createModalVisible}
      onMaskClick={closeModalBtnFn}
      className={styles.popup_container}
      bodyStyle={{ height:
          openModalKey == 10 ||
          openModalKey == 7 ||
          openModalKey == 15 ||
          openModalKey == 16
          ? '402px' : '70vh' }}
      destroyOnClose
    >
      <div className={styles.header}>
        <div className={styles.line} onClick={closeModalBtnFn}></div>
      </div>

      {RenderComponent()}
    </Popup>
  )
}

export default connect(({ userInfoStore, loading }: any) => ({userInfoStore, loading}))(Index)
