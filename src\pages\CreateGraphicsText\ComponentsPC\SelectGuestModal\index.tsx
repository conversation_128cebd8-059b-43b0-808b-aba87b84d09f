import React, { useState, useEffect, useCallback } from 'react';
import { connect, history } from 'umi';
import { Button, message, Modal, Spin, Upload, Select } from 'antd';
import styles from './index.less';
import searchIcon from '@/assets/GlobalImg/search.png';
import { Input } from 'antd-mobile';
import classNames from 'classnames';
import noDataImg from '@/assets/GlobalImg/no_data.png';
import {processNames, randomColor, useDebounce} from '@/utils/utils';

const Index: React.FC = (props: any) => {
  const { starSpaceType, dispatch, userInfoStore, loading } = props || {};
  const { VisibleBySelectGuest } = userInfoStore || {};
  const [userlist, setUserList] = useState<any[]>([]); // 嘉宾
  const [isHasData, setIsHasData] = useState(null); // 是否有数据
  const [thisSelectedGuest, setThisSelectedGuest] = useState<Record<string, any>[]>([]); // 勾选中的数据
  const [attendeeCfgBizUserList, setAttendeeCfgBizUserList] = useState<any[]>(); //
  const [bizList, setBizList] = useState(); // 区域列表
  const [orgList, setOrgList] = useState(); // 诊所列表
  const [biz, setBiz] = useState(); // 选择的区域
  const [org, setOrg] = useState(); // 选择的机构
  const [tenantId, setTenantId] = useState(); // 选择的品牌id
  const UerInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');


  // input 搜索嘉宾
  let changeInputFn = (val) => {
    if (!val || !val.trim()) {
      setUserList([]);
      getListData();
    } else {
      getListData(val);
    }
  };
  changeInputFn = useDebounce(changeInputFn,300);


  useEffect(() => {
    if (VisibleBySelectGuest) {
      getListData();
      const { selectedGuest } = userInfoStore || {};
      if (Array.isArray(selectedGuest)) {
        setThisSelectedGuest(selectedGuest);
      }
    } else {
      clearData();
    }
  }, [VisibleBySelectGuest]);

  // 清空数据
  const clearData = () => {
    setBizList([]);
    setOrgList([]);
    setBiz(null);
    setOrg(null);
    setUserList([]);
    setIsHasData(null);
    setThisSelectedGuest(null);
    setAttendeeCfgBizUserList(null);
  };

  useEffect(() => {
    if (biz || org) {
      getAttendeeSearchUserByBizGroupId(biz || org);
    }
  }, [biz, org]);

  // 根据区域、机构获取用户
  const getAttendeeSearchUserByBizGroupId = (bizGroupId) => {
    dispatch({
      type: 'userInfoStore/getAttendeeSearchUserByBizGroupId',
      payload: {
        bizGroupId: bizGroupId,
      },
    }).then((res) => {
      if (res && res.code == 200) {
        if (Array.isArray(res.content) && res.content.length > 0) {
          setUserList(res.content);
          setIsHasData(0);
        } else {
          setUserList([]);
          setIsHasData(1);
        }
      }else {
        setUserList([]);
        setIsHasData(1);
      }
    });
  };

  // 获取参会人数据
  const getListData = useCallback(
    (val) => {
      dispatch({
        type: 'userInfoStore/attendeeSearchUserByQueryKey',
        payload: {
          queryKey: val && val.trim(),
        },
      }).then((res) => {
        if (res && res.code == 200) {
          const { userList, attendeeCfgBizUserList } = res.content || {};
          if (Array.isArray(attendeeCfgBizUserList)) {
            setAttendeeCfgBizUserList(attendeeCfgBizUserList);
            if (
              attendeeCfgBizUserList.length > 0 &&
              attendeeCfgBizUserList[0] &&
              attendeeCfgBizUserList[0].tenantId
            ) {
              setTenantId(attendeeCfgBizUserList[0].tenantId);
            }
            if (
              attendeeCfgBizUserList.length > 0 &&
              attendeeCfgBizUserList[0] &&
              Array.isArray(attendeeCfgBizUserList[0].bizList)
            ) {
              setBizList(attendeeCfgBizUserList[0].bizList);
            }
            if (
              attendeeCfgBizUserList.length > 0 &&
              attendeeCfgBizUserList[0] &&
              Array.isArray(attendeeCfgBizUserList[0].orgList)
            ) {
              setOrgList(attendeeCfgBizUserList[0].orgList);
            }
            if (
              Array.isArray(attendeeCfgBizUserList[0]?.userList) &&
              attendeeCfgBizUserList[0]?.userList.length > 0
            ) {
              setUserList(attendeeCfgBizUserList[0]?.userList);
              setIsHasData(0);
            } else {
              setUserList([]);
              setIsHasData(1);
            }
          }

          if (!!val) {
            if (Array.isArray(userList) && userList.length > 0) {
              setUserList(userList);
              setIsHasData(0);
            } else {
              setUserList([]);
              setIsHasData(1);
            }
          }
        }else {
          setUserList([]);
          setIsHasData(1);
        }
      });
    },
    [tenantId],
  );

  useEffect(() => {
    if (tenantId && Array.isArray(attendeeCfgBizUserList)) {
      let findByAttendeeCfgBizUserList = attendeeCfgBizUserList.find((item) => {
        return item.tenantId == tenantId;
      });

      setBizList(
        Array.isArray(findByAttendeeCfgBizUserList?.bizList)
          ? findByAttendeeCfgBizUserList.bizList
          : [],
      );
      setOrgList(
        Array.isArray(findByAttendeeCfgBizUserList?.orgList)
          ? findByAttendeeCfgBizUserList.orgList
          : [],
      );

      if (
        Array.isArray(findByAttendeeCfgBizUserList?.userList) &&
        findByAttendeeCfgBizUserList?.userList.length > 0
      ) {
        setUserList(
          Array.isArray(findByAttendeeCfgBizUserList?.userList)
            ? findByAttendeeCfgBizUserList?.userList
            : [],
        );
        setIsHasData(0);
      } else {
        setUserList([]);
        setIsHasData(1);
      }
    }
  }, [tenantId]);

  // 选中处理
  const selectHandle = useCallback(
    (item) => {
      const isSelected = thisSelectedGuest.find((it) => it?.friUserId === item.friUserId);
      // 已经选择了15位
      if (!isSelected && thisSelectedGuest.length >= 15) return message.warning('最多能选15位哦~');
      setThisSelectedGuest((guest) => {
        return isSelected
          ? guest.filter((it) => it.friUserId !== item.friUserId)
          : [...guest, item];
      });
    },
    [thisSelectedGuest],
  );

  // 确定
  const confirmHandle = useCallback(() => {
    let confirmHandleList = thisSelectedGuest.map((item) => {
      return {
        ...item,
        id: item.friUserId,
      };
    });
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        VisibleBySelectGuest: false,
        selectedGuest: confirmHandleList,
      },
    });
    props.onConfirm && props.onConfirm(confirmHandleList);
  }, [dispatch, thisSelectedGuest]);

  const closeModalBtnFn = () => {
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: { VisibleBySelectGuest: false },
    });
    clearData();
  };

  // 机构列表
  let optionByClinic =
    Array.isArray(orgList) &&
    orgList.map((item) => {
      return {
        value: item.id,
        label: item.name,
      };
    });

  // 区域列表
  let optionByArea =
    Array.isArray(bizList) &&
    bizList.map((item) => {
      return {
        value: item.id,
        label: item.name,
      };
    });

  return (
    <Modal
      open={VisibleBySelectGuest}
      className={styles.modal}
      title={`选择参会人`}
      onCancel={closeModalBtnFn}
      destroyOnClose
      footer={null}
      width={640}
    >
      <div className={styles.content}>
        <div className={styles.input_box}>
          <img className={styles.search_icon} src={searchIcon} alt="" />
          <Input placeholder="输入关键字搜索" clearable onChange={changeInputFn} />
        </div>
        <div className={styles.selectGuest_content}>

        {(UerInfo && UerInfo.isBizUser == 1) && <>
          <div className={styles.selectByBrand}>
            {Array.isArray(attendeeCfgBizUserList) &&
              attendeeCfgBizUserList.map((item) => {
                const { tenantName, tenantId: tenantIdByitem } = item || {};
                return (
                  <div
                    onClick={() => {
                      if (tenantIdByitem != tenantId) {
                        setTenantId(tenantIdByitem);
                        setBiz(null);
                        setOrg(null);
                        setUserList([]);
                      }
                    }}
                    className={classNames({
                      [styles.selectByBrandItem]: true,
                      [styles.selectByBrandItem_active]: tenantIdByitem == tenantId,
                    })}
                  >
                    {tenantName}
                    {tenantIdByitem == tenantId && <div className={styles.active_line}></div>}
                  </div>
                );
              })}
          </div>
          <div className={styles.areaWarp}>
            <div className={styles.selectTitle}>选择区域</div>
            <div className={styles.selectBox}>
              <Select
                value={biz}
                placeholder={'请选择区域'}
                style={{ width: '100%' }}
                onChange={(value) => {
                  setBiz(value);
                  setOrg(null);
                }}
                options={optionByArea ? optionByArea : []}
              />
            </div>
          </div>
          <div className={styles.areaWarp}>
            <div className={styles.selectTitle}>选择机构</div>
            <div className={styles.selectBox}>
              <Select
                value={org}
                placeholder={'请选择机构'}
                style={{ width: '100%' }}
                onChange={(value) => {
                  console.log('value113123 :: ', value);
                  setBiz(null);
                  setOrg(value);
                }}
                options={optionByClinic ? optionByClinic : []}
              />
            </div>
          </div>
          </>
        }
          <div className={styles.GuestListWarp}>
            {(UerInfo && UerInfo.isBizUser == 1) &&
              <>
                <div className={styles.GuestListTitle}>全部员工</div>
              </>
            }
            <div className={styles.box_user}>
              <Spin
                spinning={
                  !!loading.effects['userInfoStore/attendeeSearchUserByQueryKey'] ||
                  !!loading.effects['userInfoStore/getAttendeeSearchUserByBizGroupId']
                }
              >
                {userlist &&
                  userlist.length > 0 &&
                  userlist.map((item) => (
                    <div
                      onClick={() => selectHandle(item)}
                      className={styles.list_item}
                      key={item.friUserId}
                    >
                      <div className={styles.list_item_info_wrap}>
                        <div
                          className={classNames({
                            [styles.selectGuest_select]: true,
                            [styles.selectGuest_select_user]: !!thisSelectedGuest.find(
                              (it) => it.friUserId === item.friUserId,
                            ),
                            [styles.selectGuest_Unselect_user]: !thisSelectedGuest.find(
                              (it) => it.friUserId === item.friUserId,
                            ),
                          })}
                        ></div>
                        <div className={styles.list_item_img}>
                          {item.headUrlShow ? (
                            <img src={item.headUrlShow} alt="" />
                          ) : (
                            <div
                              className={styles.no_comment_head}
                              style={{ background: randomColor(item?.friUserId) }}
                            >
                              {processNames(item?.name)}
                            </div>
                          )}
                        </div>
                        <div className={styles.list_item_info}>
                          <div className={styles.nameWarp}>
                            <div
                              className={styles.name}
                              dangerouslySetInnerHTML={{
                                __html: item.highlightName ? item.highlightName : item.name,
                              }}
                            ></div>
                            {item.isCfg == 1 && <div className={styles.bizUser}>企业</div>}
                          </div>
                          <div className={styles.phone}>{item.phone}</div>
                        </div>
                      </div>
                      {/*<div className={thisSelectedGuest.find(it => it.friUserId === item.friUserId)
                      ? styles.active_select : styles.init_select}
                         onClick={() => selectHandle(item)}
                    >
                      {thisSelectedGuest.find(it => it.friUserId === item.friUserId) ? '取消选择' : '选择'}
                    </div>*/}
                    </div>
                  ))}
                {isHasData == 1 ? (
                  <div className={styles.nodata}>
                    <img src={noDataImg} alt="" />
                    <div className={styles.empty_title}>暂无该搜索结果</div>
                    <div className={styles.empty_msg}>请试试其他搜索关键词</div>
                  </div>
                ) : null}
              </Spin>
            </div>
          </div>
        </div>
      </div>

      <div className={styles.btn_wrap}>
        <div className={styles.tip}>
          <div className={styles.tip_box}>
            最多支持15个参会人自由发言或共享屏幕，其余参会人请发送邀请链接进入
          </div>
        </div>
        <div className={styles.btn_box}>
          <div className={styles.btn_wrap_left}>
            {Array.isArray(thisSelectedGuest) &&
              thisSelectedGuest.map((item) => {
                return (
                  <div className={styles.item}>
                    {item.headUrlShow ? (
                      <img src={item.headUrlShow} alt="" />
                    ) : (
                      <div
                        className={styles.no_comment_head}
                        style={{ background: randomColor(item?.friUserId) }}
                      >
                        {processNames(item?.name)}
                      </div>
                    )}
                  </div>
                );
              })}
          </div>
          <div className={styles.btn_wrap_right}>
            <div onClick={confirmHandle} className={styles.btn_submit}>
              确定
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default connect(({ userInfoStore, loading }: any) => ({ userInfoStore, loading }))(Index);
