.modal {
  :global {
    .adm-center-popup-wrap {
      max-width: 320PX;
    }
    .adm-modal-body:not(.adm-modal-with-image) {
      padding-top: 32PX;
    }
    .adm-modal-content {
      padding: 0 16PX 16PX;
    }
  }
}

// 提示文案
.header {
  display: flex;
  flex-wrap: nowrap;
  justify-content: center;
  align-items: flex-start;
  column-gap: 8PX;
  margin-bottom: 12PX;
  .title {
    font-weight: 500;
    font-size: 17PX;
    color: #000;
    line-height: 24PX;
    text-align: center;
  }
}

.text_wrap {
  margin-bottom: 16PX;
  .text {
    font-size: 14PX;
    color: #666;
    line-height: 20PX;
    text-align: center;
    word-break: break-all;
  }
}

// 额外的勾选项
.checkbox_wrap {
  :global {
    .adm-checkbox-content {
      color: #666;
    }
    .adm-checkbox.adm-checkbox-checked .adm-checkbox-icon {
      border: 0;
      background: #0095FF;
    }
  }
}

// 按钮
.btn_wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  column-gap: 16PX;
  padding-top: 24PX;
  .left_btn, .right_btn {
    flex: 1;
    border-radius: 20PX;
    height: 40PX;
    line-height: 40PX;
    font-size: 16PX;
    text-align: center;
    cursor: pointer;
  }
  .left_btn {
    background: #EDF9FF;
    color: #0095FF;
  }
  .right_btn {
    background: #0095FF;
    color: #fff;
  }
}

// 帮助文档
.help_file {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 16PX;
  color: #0095FF;
  user-select: none;
  cursor: pointer;
  font-size: 14PX;
}
