.container{
  height: 100vh;
}
.header{
  padding-bottom: 44px;
}
.cases_details_container{
  background: #f5f6f8;
  position: relative;
}
.cases_details{
  // padding: 0 12px;
  position: relative;
  .cases_details_basic_info{
    border-top: 8px solid #f5f6f8;
    padding: 12px 12px 16px 12px;
    background: #fff;
    box-sizing: border-box;
    margin-bottom: 8px;

    .basic_info_title{
      font-size: 20px;
      font-weight: 600;
      color: #000000;
      line-height: 23px;
      margin-bottom: 8px;
    }
    .basic_info_sub{
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      .difficulty_one {
        font-size: 12px;
        font-weight: 400;
        color: #00D78B;
        line-height: 12px;
        padding: 4px;
        background: #E6FBF3;
        border-radius: 2px;
        margin-right: 6px;
        flex-shrink: 0;
        margin-bottom: 6px;
      }

      .difficulty_two {
        font-size: 12px;
        font-weight: 400;
        color: #E39D16;
        line-height: 12px;
        padding: 4px;
        background: #FFF8EC;
        border-radius: 2px;
        margin-right: 6px;
        flex-shrink: 0;
        margin-bottom: 6px;
      }

      .difficulty_three {
        font-size: 12px;
        font-weight: 400;
        color: #FF921F;
        line-height: 12px;
        padding: 4px;
        background: #FFF4E9;
        border-radius: 2px;
        margin-right: 6px;
        flex-shrink: 0;
        margin-bottom: 6px;
      }

      .difficulty_four {
        font-size: 12px;
        font-weight: 400;
        color: #FF5F57;
        line-height: 12px;
        padding: 4px;
        background: #FCE9E8;
        border-radius: 2px;
        margin-right: 6px;
        flex-shrink: 0;
        margin-bottom: 6px;
      }

      .sub_title {
        padding: 4px;
        background: #EDF9FF;
        border-radius: 2px;
        color: #0095FF;
        font-size: 12px;
        font-weight: 400;
        line-height: 12px;
        margin-right: 6px;
        display: inline-block;
        flex-shrink: 0;
        margin-bottom: 6px;
      }

      .achieve_title{
        padding: 4px;
        background: #FFF7E2;
        font-size: 12px;
        font-weight: 400;
        color: #D3A221;
        line-height: 12px;
        flex-shrink: 0;
        margin-bottom: 6px;
      }
    }
    .basic_info_indications{
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      line-height: 16px;
      margin-bottom: 8px;

      .basic_info_indications_title{
        margin-right: 8px;
        display: inline-block;
      }
    }
    .basic_info_word{
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      line-height: 16px;
      margin-bottom: 8px;
      display: flex;
      flex-wrap: wrap;

      .basic_info_word_title {
        position: relative;
        top: 2px;
        flex-shrink: 0;
      }

      .basic_info_word_desc{
        padding: 4px;
        background: #F5F5F5;
        border-radius: 2px;
        margin-right: 6px;
        display: inline-block;
        font-size: 14px;
        font-weight: 400;
        color: #666666;
        line-height: 14px;
        flex-shrink: 0;
        margin-bottom: 4px;
      }
    }
    .solution {
      background: #F5F5F5;
      border-radius: 6px;
      padding: 8px;
      margin-bottom: 8px;

      .solution_img {
        width: 57px;
        min-width: 57px;
        height: 20px;
        margin-right: 8px;
        position: relative;
        top: -2px;
      }

      .solution_text {
        font-size: 14px;
        font-weight: 400;
        color: #666666;
        line-height: 22px;
        word-break: break-all;
      }
    }
  }

  .cases_details_wrap{
    background: #FFFFFF;

    .tabsContainer {
      position: sticky;
      top: 43px;
      z-index: 100;
      background: #fff;

      :global {
        .adm-tabs-header {
          height: 55px;
          border-bottom: 0;
        }

        .adm-tabs-tab-list {
          padding: 16px 12px 0;
          font-size: 16px;
          font-weight: 400;
          color: #999999;
          line-height: 19px;
        }

        .adm-tabs-tab-active {
          font-size: 18px;
          font-weight: 600;
          color: #000000;
          line-height: 21px;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 17px;
            height: 4px;
            background: #000000;
            border-radius: 10px;
          }
        }

        .adm-tabs-tab-line {
          display: none;
        }

        .adm-tabs-tab-wrapper {
          padding: 0;
          margin-right: 16px;
        }
      }
    }

    .basic_info{
      padding: 0 12px 0;

      .basic_title{
        font-size: 15px;
        font-weight: 500;
        color: #091715;
        line-height: 24px;
        margin-right: 16px;
      }

      .basic_value{
        font-size: 15px;
        font-weight: 400;
        color: #000000;
        line-height: 24px;
      }
      .basic_info_age_sex{
        display: flex;
        margin-bottom: 16px;

        .basic_wrap{
          flex: 1;
        }
      }
      .basic_info_content_desc{
        font-size: 15px;
        font-weight: 400;
        color: #000000;
        line-height: 18px;
        margin-top: 8px;
        margin-bottom: 16px;
        word-break: break-all;
        white-space: pre-wrap;
      }

      .basic_header{
        margin-bottom: 16px;

        .basic_header_title{
          font-size: 18px;
          font-weight: 600;
          color: #091715;
          line-height: 21px;
        }

        .basic_header_back{
          width: 73px;
          height: 10px;
          background: linear-gradient(90deg, #0095FF 0%, rgba(255,255,255,0) 100%);
          border-radius: 10px 10px 10px 10px;
          margin-top: -5px;
        }
      }

      .basic_info_one,.basic_info_two,.basic_info_three ,.basic_info_four ,.basic_info_five{
        padding-top: 16px;
      }
    }
  }
  .cases_details_comment{
    padding: 16px 0 32px;
    background: #fff;
    margin-top: 8px;

    .cases_details_comment_title{
      font-size: 20px;
      font-weight: 600;
      color: #091715;
      line-height: 28px;
      padding-bottom: 16px;
    }
  }
}

.comment_publish_input {
  :global {
    .adm-popup-body {
      border-radius: 8px 8px 0 0;
      padding: 12px;
    }
    .adm-text-area-element {
      color: #000;
      font-size: 14px;

      &::placeholder {
        color: #ccc;
      }
    }
  }

  .comment_publish_input_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .comment_publish_input_left {
      font-size: 18px;
      font-weight: 600;
      color: #000000;
      line-height: 21px;
    }
    .comment_publish_input_right {
      width: 70px;
      height: 29px;
      background: #0095FF;
      border-radius: 20px;
      font-size: 15px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 29px;
      text-align: center;
    }
  }
  .comment_publish_textarea {
    display: flex;
  }
  .publish_input{
    flex: 1;
    border-radius: 8px;
    padding: 12px;
    height: 96px;
    font-size: 14px;
    font-weight: 400;
    background: #F5F5F5;
  }
}
.no_cases_content{
  padding: 50px 0;
  width: 100%;
  height: 100%;
  background: #FFFFFF;
  margin-bottom: 20px;
  text-align: center;
  .no_content_img{
    width: 150px;
    height: 113px;
  }
  .no_content_title{
    font-size: 15px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #333333;
    line-height: 17px;
    margin-top: 4px;
  }
  .no_content_tips{
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #999999;
    line-height: 17px;
    margin-top: 8px;
  }
  .no_content_btn{
    margin: 12px auto 0;
    width: 108px;
    padding: 6px 0;
    border-radius: 36px;
    border: 1px solid #EEEEEE;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #333333;
  }
}
.cases_details_header{
  height: 90px;
}
.comment_mask{
  width: 100%;
  height: 100vh;
  position: absolute;
  z-index: 9999;
  background: #000000;
  opacity: 0.6;
  left: 0;
  top: 0;
}
.comment_publish{
  background: #FFFFFF;
  opacity: 1;
  border-top: 2px solid #EEEEEE;
  padding: 10px 12px 10px 0;
  display: flex;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;

  .collection_wrap{
    position: relative;
    margin-left: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .collection_icon{
      width: 24px;
      height: 24px;
    }
    .collection_title{
      font-size: 13px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #666;
      line-height: 18px;
      display: block;
    }
    .collection_people{
      position: absolute;
      right: -20px;
      top: -5px;
      height: 14px;
      background: #E6F5FF;
      border-radius: 16px;
      line-height: 14px;
      padding: 0 5px;
      font-size: 10px;
      font-weight: 500;
      color: #0095FF;
    }
  }
  .share_wrap{
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 24px;
    margin-left: 24px;

    .wx_icon{
      width: 24px;
      height: 24px;
    }

    .wx_title{
      font-size: 13px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #666;
      line-height: 18px;
      display: block;
    }
  }
  .say_something{
    flex: 1;
    height: 40px;
    background: #F5F5F5;
    border-radius: 22px;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #999;
    line-height: 40px;
    padding-left: 16px;
  }
}

.right_content {
  position: fixed;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: 101;

  .banner_window_wrap {
    width: 159px;
    height: 107px;
    position: relative;
    right: 16px;
    background: #fff;
    border-radius: 8px;

    .banner_window_img {
      width: 159px;
      height: 107px;
    }

    .banner_window_icon {
      position: absolute;
      right: 0;
      top: -10px;

      .banner_window_close {
        width: 20px;
        height: 20px;
      }
    }
  }

  .show_window_wrap {
    width: 22px;
    height: 100px;
    display: none;

    .show_window_icon {
      width: 22px;
      height: 100px;
    }
  }
}

.vip_wrap {
  width: 100%;
  height: 310px;
  margin-bottom: 24px;
  position: relative;

  .vip_forbidden_wrap {
    width: 100%;
    height: 310px;
    overflow: hidden;
  }

  .vip_shade_wrap {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;

    .vip_shade_title {
      width: 100%;
      height: 56px;
      background: linear-gradient(180deg, rgba(255,255,255,0.9) 0%, #FFFFFF 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: 400;
      color: #BE7D1B;
      line-height: 24px;

      .vip_shade_icon {
        width: 16px;
        height: 16px;
        position: relative;
        margin-left: 4px;
      }
    }

    .vip_shade_content {
      width: 100%;
      height: 214px;
      background: #FFF9EA;
      border-radius: 8px;
      padding: 0 25px;

      .vip_shade_deblocking_title {
        width: 100%;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;

        .vip_shade_deblocking_text {
          font-size: 12px;
          font-weight: 400;
          color: #AD7D5A;
          line-height: 24px;
          margin: 0 8px;
        }

        .vip_shade_deblocking_icon {
          width: 44px;
          height: 4px;
        }
      }

      .vip_shade_deblocking_List {
        width: 100%;

        .vip_deblocking_content {
          width: 100%;
          display: flex;
          flex-wrap: wrap;

          .vip_deblocking_item {
            width: 50%;
            flex-shrink: 0;
            margin-bottom: 12px;
            display: flex;
            align-items: center;

            .vip_deblocking_text {
              font-size: 13px;
              font-weight: 400;
              color: #6B3B19;
              line-height: 24px;
            }

            .vip_deblocking_icon {
              width: 20px;
              height: 20px;
              margin-right: 8px;
            }
          }
        }
      }

      .vip_deblocking_btn_wrap {
        padding: 4px 4px 0px;

        .vip_button_style {
          width: 100%;
          height: 38px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          background: linear-gradient(158deg, #FDF0C2 0%, #F5D18A 100%);
          border-radius: 42px;

          .vip_button_text {
            font-size: 16px;
            font-weight: 500;
            line-height: 19px;
            background-image: -webkit-linear-gradient(-90deg, #E8975D 0%, #7D461E 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }
    }
  }
}

.unlogin_wrap {
  width: 100%;
  height: 150px;
  margin-bottom: 24px;
  position: relative;

  .vip_forbidden_wrap {
    width: 100%;
    height: 150px;
    overflow: hidden;
  }

  .vip_shade_wrap {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;

    .vip_shade_title {
      width: 100%;
      height: 50px;
      background: linear-gradient(180deg, rgba(255,255,255,0.92) 0%, #FFFFFF 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: 400;
      color: #BE7D1B;
      line-height: 24px;

      .vip_shade_icon {
        width: 16px;
        height: 16px;
        position: relative;
        margin-left: 4px;
      }
    }

    .vip_shade_content {
      width: 100%;
      height: 50px;
      background: #fff;
    }
  }
}

.seize_seat{
  margin: 0 -12px;
  height: 8px;
  background: #f5f6f8;
}
