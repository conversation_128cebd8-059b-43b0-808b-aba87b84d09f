import React, {useState, useEffect, useRef} from 'react';
import { history,connect } from 'umi';
import styles from "./index.less";
import { Input,Upload,message,Spin } from "antd";
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { getOperatingEnv } from '@/utils/utils'
import {stringify} from "qs";
import { cloneDeep } from 'lodash'


const UploadByImage: React.FC = (props) => {
  const { fieldContent,onUploadComplete,fileContent } = props || {}
  // 登录用户信息
  const UerInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
  const { dictName } =  fieldContent || {}
  const [loading, setLoading] = useState(false);
  const [uploadLoading, setUploadLoading] = useState(false)            // 上传影响资料loading

  const uploadButton = (
    <div>
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
    </div>
  );

  // 上传完成回调，图片
  const uploadOnChange = (info) => {
    if (info.file.status === 'uploading') {}

    // 状态不为uploading时，代表上传事件结束
    if (info.file.status != 'uploading') {
      setUploadLoading(false)
    }

    // 判断当前上传已被拦截
    if (info && !info.file.status) { return }

    // 上传结束
    if (info && info.file.status === 'error') {
      message.error('上传失败')
      return
    }

    if (info && info.file.status === 'done') {
      if(info && info.file.response && info.file.response.code != 200) {
        message.error(info.file.response.msg ? info.file.response.msg : '上传失败')
        return
      }
    }

    if (info.file.type == 'image/png' || info.file.type === 'image/jpeg') {
      if(info.file.response && info.file.response.code == 200 && info.file.response.content) {
        const file = info.file
        const content = file.response.content
        // 文件名和后缀名
        const suffix = file.name.substring(file.name.lastIndexOf('.')+1)
        const name = file.name.substring(0, file.name.lastIndexOf('.'))

        /*const consultationCaseMediaDtoListClone = cloneDeep(formState.consultationCaseMediaDtoList)
        consultationCaseMediaDtoListClone.push({
          type: 0,      // //资料类型(0星球影像、1其他资料、2全景片、3侧位片、4正面像、5侧面像、6正面咬合像、7正面咬合45度像、8左侧咬合像、9右侧咬合像、10上牙弓像、11下牙弓像)
          fileSize: file.size,    // 文件大小
          fileName: name,              // 文件名称
          fileSuffix: suffix,          // 文件后缀
          fileUrl: content.fileUrl,    // 文件路径
          fileUrlShow: content.fileUrlView,
        })*/
        let fieldContent = {
          fieldContent:fieldContent,
          type: 0, //资料类型(0星球影像、1其他资料、2全景片、3侧位片、4正面像、5侧面像、6正面咬合像、7正面咬合45度像、8左侧咬合像、9右侧咬合像、10上牙弓像、11下牙弓像)
          fileSize: file.size,    // 文件大小
          fileName: name,              // 文件名称
          fileSuffix: suffix,          // 文件后缀
          fileUrl: content.fileUrl,    // 文件路径
          fileUrlView: content.fileUrlView, // 文件回显路径
        }
        onUploadComplete && onUploadComplete(fieldContent)
      }
    }
  }

  // 上传校验规则，图片
  const beforeUpload = (file) => {
    const isSize = file.size / 1024 / 1024 < 15
    if (!isSize) {
      message.error('超过15M限制，不允许上传~')
      return false
    }

    const { name: fileName } = file || {}
    // 添加对文件后缀名的限制
    const suffix = fileName.substring(fileName.lastIndexOf('.')+1).toLowerCase()
    const isJpgOrPng = file.type === 'image/jpg' || file.type === 'image/jpeg' ||  file.type === 'image/png'
    const isSuffixByJpgOrPng = (
      suffix === 'jpg'
      || suffix === 'jpeg'
      || suffix === 'png'
    )
    if (!isJpgOrPng || !isSuffixByJpgOrPng) {
      message.error({content: '只能上传JPG、JPEG、PNG格式的图片~'})
      return false
    }
    setUploadLoading(true)
    return true
  }


  // 上传图片headers
  const getHeaders=() =>{
    // 获取环境类型，1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用   5 FRIDAY app中使用
    const env = getOperatingEnv()

    // FRIDAY APP中本地存储值：access_token、client、phone、user_name、wxuserId、userInfo
    return {// token
      access_token:localStorage.getItem('access_token') || '',
      username: env == 5 ? localStorage.getItem('user_name') : localStorage.getItem('vxOpenIdCipherText') ? localStorage.getItem('vxOpenIdCipherText')  : UerInfo?.phone,
      client: env == 5 ? localStorage.getItem('client') : 'WX',
      type: env === '1' ? '' : '1', // h5 传1
    }
  }

  return (
    <div className={styles.Upload_Warp}>
      <Spin spinning={!!uploadLoading}>
        <Upload
          headers={getHeaders()}
          accept="image/*"
          action={`/api/server/base/uploadFile?${stringify({fileType: 10, userId: UerInfo?.friUserId})}`}
          listType="picture-card"
          className="avatar-uploader"
          onChange={uploadOnChange}
          onRemove={()=>{}}
          beforeUpload={beforeUpload}
          showUploadList={false}
        >
          {!!fileContent ? <img className={styles.ImageUrlWarp} src={fileContent.fileUrlView} style={{ width: '100%' }} /> : uploadButton}
        </Upload>
      </Spin>
      <div className={styles.Upload_title}>
        { dictName }
      </div>
    </div>
  )
}

export default connect(({ CreationOrthodontics, loading }: any) => ({
  CreationOrthodontics, loading
}))(UploadByImage)
