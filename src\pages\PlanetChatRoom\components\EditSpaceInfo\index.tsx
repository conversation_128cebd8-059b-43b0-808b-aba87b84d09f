/**
 * @Description: 空间详情（本人的空间）有编辑按钮，可以对空间信息进行编辑
 */
import React, { useCallback } from 'react';
import {connect} from "umi";
import styles from './index.less';
import { Toast } from 'antd-mobile';
import editIcon from '@/assets/PlanetChatRoom/SpatialDetail_edit_btn.png'; // 编辑——小图标
import CreateKingdomOrSpace from '@/pages/UserInfo/CreateKingdomOrSpace';
import {message} from "antd";
import {history} from "@@/core/history";
import {stringify} from "qs"; // 编辑空间弹框

type propsType = {
  dispatch: any;
  spaceId: string;
  refreshFn: any;
}

const Index: React.FC<propsType> = (props) => {
  const {dispatch, spaceId, refreshFn} = props || {};

  // 获取当前编辑空间信息
  const editSpaceInfoFn = () => {
    const userInfoData = JSON.parse(localStorage.getItem('userInfo'));
    const { friUserId:id } = userInfoData || {}  // 获取用户id
    // 获取当前的空间基本信息
    dispatch({
      type: 'userInfoStore/getSpaceInfoByEdit',
      payload: {
        spaceId: spaceId, // 空间id
        wxUserId: id, // 当前登录人id
      }
    }).then((res: any) => {
      const { content, code } = res || {};
      if(res && code == 200) {
        const {
          name,
          starSpaceType,
          guestDataList,
          appointmentStartTime,
          intro,
          spaceCoverUrlShow,
          spaceCoverUrl,
          spaceAdvertisingUrlShow,
          spaceAdvertisingUrl,
          vodFileId,
          vodPathUrl,
          password,
          vodFileName,
          spectatorType,
          spectatorCount,
          spaceType,
          kingdomId,
          isAutoRecord,
          isNoPasswordApply,
          status,
          id
        } = content || {};

        if(status == 1) {
          message.warning('该内容正在进行中，不允许编辑~')
          return;
        }else {
          if (starSpaceType == 2) {
            history.push(`/CreateSpace/Meet?${stringify({
              id: id,
            })}`)
          }else {
            history.push(`/CreateSpace/Live?${stringify({
              id: id,
            })}`)
          }
        }

      } else {
        return Toast.show({content: res.msg})
      }
    }).catch((err: any) => {
      console.log(err)
    })
  }

  // 获取企业品牌机构
  const getBizGroupByTenantId = useCallback((spectatorCount) => {
    dispatch({
      type: 'userInfoStore/getBizGroupByTenantId',
      payload: {
        spaceId: spaceId, // 空间ID，编辑必传
      }
    }).then((res:any) => {
      const {code, content} = res || {};
      if(res && code == 200) {

        // 找到选中的数据并赋值state
        let options = content.map(({tenantId, isAllUser, areaList, orgList}) => ({
          tenantId,
          isAllUser,
          areaList: areaList.filter(it => it.isChecked).map(val => val.id),
          orgList: orgList.filter(it => it.isChecked).map(val => val.id),
        }))

        const isAll = options.filter(it => it.isAllUser); // 过滤出isAllUser选中的数据，判断是否展示为所有人员

        let enterpriseText = '';

        // 如果所有人勾选的数据总长度 与 初始数据的总长度 一致，则展示 所有成员，否则展示 已选xx个分组
        if(isAll.length === content.length) {
          enterpriseText = `企业/品牌用户可见，已选所有成员`
        } else {
          enterpriseText = `企业/品牌用户可见，已选${spectatorCount}个分组`
        }

        dispatch({
          type: 'userInfoStore/setTaskListState',
          payload: {
            enterpriseUserSelectData: options || [], // 当前勾选中的数据
            enterpriseText, // 观众-展示文案
            allViewersState: false, // 所有人状态勾选取消
          }
        })
      }
    }).catch((err:any) => console.log(err))
  }, [dispatch])


  // 获取创建王国或加入王国相关数
  const kingdomFn = useCallback(() => {
    dispatch({
      type: 'userInfoStore/getCreateAndJoinKingdomList',
      payload: {
        spaceId: spaceId, // 编辑时候需要传id用来查询当前空间选中了哪些
      }
    }).then((res:any) => {
      const {code, content} = res || {};
      if(res && code == 200) {
        if(content) {
          const createArrList = content && content[1]; // 创建的王国数据
          const joinArrList = content && content[2]; // 加入的王国数据

          let listArr; // 总数据

          if(createArrList && joinArrList) {
            // 如果加入的和创建的数据都有时，则合并数据
            listArr = createArrList.concat(content[2])
          }

          if(createArrList && !joinArrList) {
            // 如果只有创建的数据，没有加入的数据时，则赋值创建的数据
            listArr = createArrList;
          }

          if(!createArrList && joinArrList) {
            // 如果只有创建的数据，没有加入的数据时，则赋值创建的数据
            listArr = joinArrList;
          }

          const filterList = listArr.filter(it => it); // 过滤筛选出不为null的数据

          dispatch({
            type: 'userInfoStore/setTaskListState',
            payload: {
              selectedKingdomAudience: filterList?.filter(it => it.isChecked).map(v => v.id), // 观众-王国列表数据
              allViewersState: false, // 所有人状态勾选取消
            }
          })
          return;
        }
      }
    }).catch((err:any) => console.log(err))
  }, [dispatch])

  return <>
    <div
      className={styles.edit_box}
      onClick={()=>{ editSpaceInfoFn(); }
    }>
      <div className={styles.edit_img}><img src={editIcon} alt="" /></div>
      <div className={styles.edit_text}>编辑</div>
    </div>

    {/* comeType传2，打开空间编辑 */}
    <CreateKingdomOrSpace comeType={2} />
  </>
}
export default connect(({ userInfoStore, loading }: any) => ({ userInfoStore, loading}))(Index)
