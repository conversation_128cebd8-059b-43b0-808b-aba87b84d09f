.major_mobile_container {
  position: relative;
  background:#F5F6F8;
  padding-bottom: 12px;

}
.major_pc_container {
  position: relative;
}

// 查看更多按钮容器
.view_more_container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
  margin-top: 16px;
}

// 查看更多按钮样式
.view_more_button {
  background: transparent;
  color: #1890ff;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;

  &:hover {
    color: #40a9ff;
    background: rgba(24, 144, 255, 0.06);
  }

  &:active {
    color: #096dd9;
    background: rgba(24, 144, 255, 0.1);
  }
}