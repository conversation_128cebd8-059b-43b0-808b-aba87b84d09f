.major_mobile_container {
  position: relative;
  background:#F5F6F8;
  padding-bottom: 12px;

}
.major_pc_container {
  position: relative;
}

// 查看更多按钮容器
.view_more_container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
  margin-top: 16px;
}

// 查看更多按钮样式
.view_more_button {
  background: #1890ff;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;

  &:hover {
    background: #40a9ff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  }

  &:active {
    background: #096dd9;
    transform: translateY(0);
  }
}