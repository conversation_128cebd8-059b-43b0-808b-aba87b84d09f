.search_wrap {
  width: 100%;
  height: 100vh;
  background: #F5F6F8;
}

.search_content_wrap {
  width: 100%;
  min-height: 303px;
  background: #fff;
  padding: 12px;
  max-height: calc(100vh - 70px);
  overflow-y: auto;

  .header_box_content {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    padding-left: 10px;
  }

  .history_wrap {
    width: 100%;
    padding-bottom: 16px;

    .history_title {
      font-size: 15px;
      font-weight: 500;
      color: #000000;
      line-height: 21px;
      margin-bottom: 12px;
    }

    .history_content {
      display: flex;
      flex-wrap: wrap;

      .history_item {
        font-size: 14px;
        font-weight: 400;
        color: #666666;
        line-height: 20px;
        background: #F5F5F5;
        border-radius: 4px;
        padding: 4px 12px;
        margin-right: 12px;
        margin-bottom: 8px;
      }
    }
  }
}

.header_box_content {
  display: flex;
  width: 100%;
  align-items: center;
  padding-left: 10px;
  background: #fff;
}
.nav_bar_icon {
  flex-shrink: 0;
  width: 12px;
  height: 24px;
  background:  url("../../assets/GlobalImg/go_back.png") no-repeat;
  background-size: 12px 24px;
}
