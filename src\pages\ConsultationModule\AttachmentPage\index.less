.wrap {
  background: #F5F6F8;
  height: 100vh;
  position: relative;
  .toast_wrap{
    :global{
      .adm-toast-main-text{
        padding: 6px 16px;
        border-radius: 16px;
      }
      .adm-toast-main{
        background: rgba(0,0,0,0.3);
      }
    }
  }
  
}

.wrap_bottom {
  background: #F5F6F8;
  height: calc(100vh - 91px);
  position: relative;
}

.header {
  width: 100%;
  background: #FFFFFF;
  height: 44px;
  position: relative;
}

.content {
  margin-top: 8px;
  padding: 7px 16px 24px;
  background: #FFFFFF;

  .content_list {
    margin-top: 17px;
    display: flex;
    align-items: center;

    .annex_format {
      width: 27px;
      height: 30px;
      margin-right: 16px;
      &>img {
        width: 100%;
        height: 100%;
      }
    }

    .annex_content {
      flex: 1;

      .annex_name {
        display: block;
        font-size: 14px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #000000;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        padding-right: 16px;
        line-height: 16px;
      }

      .annex_size {
        display: block;
        font-size: 12px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
        line-height: 14px;
      }

    }

    .annex_look {
      width: 30px;
      font-size: 14px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #0095FF;
      text-align: right;
    }
  }

}

.tips {
  margin-top: 20px;
  padding: 0 56px;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #999999;
  line-height: 14px;
}

.footer {
  width: 100%;
  border-top: 1px solid #EEEEEE;
  background: #FFFFFF;
  box-shadow: 5px 10px 49px 1px rgba(53, 51, 97, 0.17);
  backdrop-filter: blur(8px);
  display: flex;
  position: fixed;
  bottom: 0;
  .footer_content{
    width: 100%;
    height: 58px;
    display: flex;
    align-items: center;
    padding: 0 16px 0 1px;
    &>p{
      margin-bottom: 0;
    }
    .copy{
      width: 50%;
      margin-left: 15px;
    }
    :global{
      div.ant-typography, .ant-typography p{
        margin-bottom: 0;
      }
      .ant-typography-copy{
        display: block !important;
      }
    }
    .copy_link{
      border-radius: 20px 20px 20px 20px;
      opacity: 1;
      border: 1px solid #0095FF;
      padding: 8px 0;
      font-size: 16px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #0095FF;
      // width: 50%;
      text-align: center;
      margin-bottom: 0;
    }
    .send_to_email{
      width: 50%;
      margin-left: 15px;
      background: #0095FF;
      border-radius: 20px 20px 20px 20px;
      font-size: 16px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #FFFFFF;
      padding: 9px 0;
      text-align: center;
    }
  }
  
}