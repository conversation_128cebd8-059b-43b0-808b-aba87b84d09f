.container{
  padding: 0 16px;
}
.container_title{
  font-size: 18px;
  font-weight: 500;
  color: #000000;
  line-height: 18px;
  margin-bottom: 16px;
}

.comment_item {
  margin-bottom: 16px;
  .comment_header {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    margin-bottom: 4px;
    .right {
      flex: 1;
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      justify-content: space-between;
      .right_content {
        flex: 1;
        .user_box {
          font-size: 13px;
          color: #000;
          font-weight: 500;
          height: 18px;
          line-height: 17px;
          margin-right: 8px;
        }
        .time {
          font-size: 10px;
          color: #666;
          height: 14px;
          line-height: 15px;
        }
      }
      .like {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        & > i {
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }
        .like {
          width: 16px;
          height: 16px;
          margin-right: 4px;
          background: url("../../../../assets/GlobalImg/like.png") no-repeat center;
          background-size: 100% 100%;
        }
        .like_active {
          width: 16px;
          height: 16px;
          margin-right: 4px;
          background: url("../../../../assets/GlobalImg/like_active.png") no-repeat center;
          background-size: 100% 100%;
        }

        & > span {
          font-size: 12px;
          color: #000;
          height: 16px;
          line-height: 17px;
        }
      }
    }
  }
  .comment_content {
    padding-left: 40px;
    font-size: 14px;
    color: #222;
    line-height: 20px;
    word-break: break-all;
    margin-bottom: 4px;
  }
  .comment_reply_box {
    display: flex;
    padding-left: 40px;
    .reply_btn {
      display: flex;
      align-items: center;
      flex-wrap: nowrap;
      height: 25px;
      padding: 0 4px 0 8px;
      border-radius: 30px;
      background: #F5F5F5;
      & > span {
        font-size: 12px;
        color: #666;
        height: 16px;
        line-height: 17px;
      }
      & > i {
        width: 16px;
        height: 16px;
        background: url("../../../../assets/GlobalImg/right_arrow_2.png") no-repeat center;
        background-size: 100% 100%;
      }
    }
  }
}
