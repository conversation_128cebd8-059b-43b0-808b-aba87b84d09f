import React from 'react';
import styles from './index.less';
import { connect } from 'umi';
import SearchInput from '../SearchInput';
import classNames from "classnames";
import {history} from "@@/core/history";

type PropsType = {
  isHistoryStatus:number;  // 1 历史搜索 2、列表页面搜索
  inputPlaceholder?: string; // 搜索框默认展示内容
  inputChangeFn?: any; // input事件
  cancelBtnFn?: any; // 取消按钮事件
  historyData?: any; // 历史搜索数据
  historyClickFn?: any; // 历史搜索点击事件
  isShowPopularSearch?: boolean; // 是否展示热门搜索(默认不展示)
  popularData?: any; // 热门搜索数据
  popularDataClickFn?: any; // 热门搜索点击事件
  onPressEnterFun?: any; // 点击回车
  TopicRanking?:any; // 热门话题模块
}


const Index: React.FC<PropsType> = (props: PropsType) => {
  const { TopicRanking, historyClickFn , popularDataClickFn ,inputPlaceholder, inputChangeFn, onPressEnterFun, cancelBtnFn ,historyData, isShowPopularSearch, popularData ,isHistoryStatus } = props || {};

  // 历史搜索——点击事件
  const historyClick = (item:string) => {
    historyClickFn && historyClickFn(item)
  }

  // 热门搜索——点击事件
  const popularDataClick = (item:string) => {
    popularDataClickFn && popularDataClickFn(item)
  }

  return (
    <div className={styles.search_wrap}>
      <div className={styles.header_box_content}>
        {/* 返回按钮 */}
        <i className={classNames(styles.nav_bar_icon)} onClick={cancelBtnFn}></i>
        <SearchInput
          inputPlaceholder={inputPlaceholder}
          inputChangeFn={inputChangeFn}
          cancelBtnFn={cancelBtnFn}
          isHistoryStatus={isHistoryStatus}
          onPressEnterFun={onPressEnterFun}
          isFocus={true}
        />
      </div>
      <div className={styles.search_content_wrap}>
        {
          historyData && historyData.length ?
          <div className={styles.history_wrap}>
            <div className={styles.history_title}>历史搜索</div>
            <div className={styles.history_content}>
              {
                historyData.map((item:String, index:number)=>{
                  return (
                    <div key={index} className={styles.history_item} onClick={() => {historyClick(item)}}>{item}</div>
                  )
                })
              }
            </div>
          </div> : null
        }
        {TopicRanking && TopicRanking()}
        {
          isShowPopularSearch && popularData && popularData.length ?
          <div className={styles.history_wrap}>
            <div className={styles.history_title}>热门搜索</div>
            <div className={styles.history_content}>
              {
                popularData.map((item, index)=>{
                  return (
                    <div key={index} className={styles.history_item} onClick={()=>{popularDataClick(item)}}>{item}</div>
                  )
                })
              }
            </div>
          </div> : null
        }
      </div>
    </div>
  )
}
export default connect(({ loading }: any) => ({ loading}))(Index)
