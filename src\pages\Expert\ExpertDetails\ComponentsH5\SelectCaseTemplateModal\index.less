.popup_container {
  :global {
    .adm-popup-body {
      border-radius: 16px 16px 0 0;
      padding-bottom: 40px;
    }
  }
}
.header_line {
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  .header_line_bar {
    width: 48px;
    height: 4px;
    background: #D0D4D7;
    border-radius: 4px;
  }
}
.header_title {
  position: relative;
  font-size: 18px;
  line-height: 25px;
  text-align: center;
  font-weight: 500;
  margin-bottom: 16px;
  .close_icon {
    position: absolute;
    width: 32px;
    height: 32px;
    top: -3px;
    right: 16px;
    color: #000;
    display: flex;
    align-items: center;
    justify-content: center;
    :global {
      .anticon {
        font-size: 16px;
      }
    }
  }
}

.complete_process_wrap {
  padding: 0 16px 16px;
}

.tips_message_wrap {
  background: #FFF7DA;
  margin: 0 16px 8px;
  padding: 4px 8px;
  display: flex;
  flex-wrap: nowrap;
  align-items: flex-start;
  column-gap: 6px;
  color: #8C772B;
  :global {
    .anticon {
      font-size: 12px;
      flex-shrink: 0;
      margin-top: 1px;
    }
  }
  .tips_message {
    flex: 1;
    word-break: break-all;
    font-size: 10px;
    line-height: 14px;
  }
}

.block {
  border: 1px solid #eee;
  border-radius: 2px;
  padding: 16px;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  column-gap: 16px;
  margin: 0 16px 8px;
  :global {
    .adm-popover-arrow {
      display: none;
    }
    .adm-popover-inner {
      width: 180px;
      max-width: 180px;
      word-break: break-all;
    }
  }
  p {
    margin-bottom: 0;
  }
  .block_right_wrap {
    flex-shrink: 0;
    font-size: 15px;
  }
  .block_left_wrap {
    flex: 1;
    word-break: break-all;
    .block_title_wrap {
      font-weight: 500;
      font-size: 16px;
      color: #000;
      line-height: 22px;
      margin-bottom: 4px;
      display: flex;
      column-gap: 6px;
      :global {
        .anticon {
          display: inline-flex;
          align-items: center;
          padding-top: 1px;
          font-size: 12px;
          color: #999;
        }
      }
    }
    .block_info_wrap {
      font-size: 12px;
      color: #999;
      line-height: 17px;
    }
    .block_extra_wrap {
      padding-top: 8px;
      font-size: 12px;
      color: #000;
      line-height: 17px;
      :global {
        .ant-typography {
          margin-bottom: 0;
        }
        .ant-typography-copy {
          margin-left: 0;
        }
      }
      .copy_icon_wrap {
        word-break: break-all;
        span {
          color: #0095FF;
        }
        img {
          margin-left: 6px;
        }
      }
    }
  }
  .block_tips_wrap {
    font-size: 12px;
    color: #666;
    line-height: 17px;
    word-break: break-all;
    .tips_title {
      font-weight: 500;
      margin-bottom: 7px;
    }
  }
}
