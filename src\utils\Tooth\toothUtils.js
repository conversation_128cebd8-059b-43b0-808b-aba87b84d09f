import React from "react";
import styles from "@/utils/Tooth/ToothUtils.less";// 样式
import { StringUtils } from "@/utils/Tooth/StringUtils";

export const toothUtils = {
  // 牙位选中
  selectTooth(tooth, toothSelect, key, index, value, suffix) {
    const key1 = suffix;
    let key2 = 0;
    if (suffix === 1) {
      key2 = 2;
    } else {
      key2 = 1;
    }
    const row = tooth[key][index];
    if (row) {
      toothSelect[key + key2][index] = false;
      toothSelect[key + key1][index] = true;
      tooth[key][index] = tooth[key][index].replaceAll(row[0], value);
    } else {
      toothSelect[key + key1][index] = true;
      tooth[key][index] = value;
    }
    return {
      tooth, toothSelect
    }
  },

  // 取消牙位选中
  noSelectTooth(tooth, toothSelect, key, index, value, suffix) {
    toothSelect[key + suffix][index] = false;
    tooth[key][index] = null;
    toothSelect[key + 3][index] = {};
    return { tooth, toothSelect };
  },

  // 取消牙面选择
  noSelectToothSurface(tooth, toothSelect, key, index, value) {
    tooth[key][index] = tooth[key][index].replaceAll(value, "");
    // console.log(tooth[key][index]);
    toothSelect[key + 3][index][value] = false;
    return { tooth, toothSelect };
  },

  // 牙面选择
  selectToothSurface(tooth, toothSelect, key, index, value) {
    toothSelect[key + 3][index][value] = true;

    if (StringUtils.isBlank(tooth[key][index])) {
      // console.log("判断1");
      tooth[key][index] = `${index} ${value}`;
      toothSelect[key + 1][index] = true;
    } else if (tooth[key][index].length === 1) {
      // console.log("判断2");
      tooth[key][index] = `${tooth[key][index]} ${value}`;
    } else {
      // console.log("判断3");
      tooth[key][index] = tooth[key][index] + value;
    }
    return { tooth, toothSelect };
  },

  // 牙位显示
  showTooth(e, toothPosition) {
    const arr = [];
    const toothArr = toothPosition ? toothPosition.split(";") : [];
    for (const t of toothArr) {
      if (t[0] === `${e}`) {
        arr.push(t.substring(1, t.length));
      }
    }
    if (e == 1 || e == 4) {
      return [].concat(arr)
        .sort((a, b) => toothSort[a[0]] < toothSort[b[0]] ? 1 : -1)
        .map((item, i) =>
          item.length === 1 ? item : item.trim().split(" ").map((d, ti) =>
            ti === 0 ? d : <span key={ti} className={styles.tooth}>{this.facingSort(d)}</span>
          )
        );
    }
    return [].concat(arr)
      .sort((a, b) => toothSort[a[0]] > toothSort[b[0]] ? 1 : -1)
      .map((item, i) =>
        item.length === 1 ? item : item.trim().split(" ").map((d, ti) =>
          ti === 0 ? d : <span key={ti} className={styles.tooth}>{this.facingSort(d)}</span>
        )
      );

    return arr;
  },

  // O牙面占位
  facingSort(facing) {
    if (StringUtils.isNotBlank(facing) && facing.indexOf('O') != -1) {
      // 替换
      facing = facing.replace('O', '');
      if (facing.length <= 1) {
        facing += 'O';
      } else if (facing.length > 1) {
        facing = `${facing.slice(0, 1)}O${facing.slice(1)}`;
      }
      return facing;
    } if (StringUtils.isNotBlank(facing) && facing.indexOf('I') != -1) {
      // 替换
      facing = facing.replace('I', '');
      if (facing.length <= 1) {
        facing += 'I';
      } else if (facing.length > 1) {
        facing = `${facing.slice(0, 1)}I${facing.slice(1)}`;
      }

      return facing;
    }
    return facing

  }
}
// 牙位取值顺序标记
export const toothSort = {
  "A": 1,
  "B": 2,
  "C": 3,
  "D": 4,
  "E": 5,
  "1": 1,
  "2": 2,
  "3": 3,
  "4": 4,
  "5": 5,
  "6": 6,
  "7": 7,
  "8": 8
};
// 牙位取值顺序标记
export const milkToothToPermanentTooth = {
  "A": "1",
  "B": "2",
  "C": "3",
  "D": "4",
  "E": "5",
  "1": "A",
  "2": "B",
  "3": "C",
  "4": "D",
  "5": "E",
  "6": "6",
  "7": "7",
  "8": "8"
};


/* 删除地址栏不需要参数
*/
export function ParamsEmpty(query, params) {
  Object.keys(query).forEach(key => {
    params.forEach(index => {
      if (key == index) {
        delete query[key]
      }
    })
  })
  return query
}



