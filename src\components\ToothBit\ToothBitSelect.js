import React , { Component } from 'react'
import { Badge, Icon } from 'antd';
import PropTypes from 'prop-types';
import classNames from 'classnames'

import styles from './ToothBitSelect.less'

/**
 *
   "halfToothDto": {
			"topRight": [{
				"toothId": 19,
				"halfArea": 2,
				"teethNumber": 7,
				"teethType": 2,
				"status": 1,
				"patientTherapyId": 2,
				"visitReferralId": null,
				"side": null
			}, {
				"toothId": 20,
				"halfArea": 2,
				"teethNumber": 6,
				"teethType": 2,
				"status": 1,
				"patientTherapyId": 2,
				"visitReferralId": null,
				"side": null
			}],
			"topLeft": null,
			"bottomLeft": null,
			"bottomRight": null
		}
 */

const ToothInfo = {
  topLeft:null,
  topRight:null,
  bottomLeft:null,
  bottomRight:null,
};

class ToothBit extends Component{

  static propTypes = {
    ToothBefore:PropTypes.string,
    ToothAfter:PropTypes.string,
    ToothInfo:PropTypes.object,
    onClickToothBit:PropTypes.func,
    onSelectBit:PropTypes.func,
    onSelectBitStatus:PropTypes.bool,
  };

  static defaultProps = {
    ToothBefore:null,
    ToothAfter:null,
    ToothInfo:ToothInfo,
    onClickToothBit:()=>{},
    onSelectBit:()=>{},
    onSelectBitStatus:false   // 是否可点击选中牙位位置
  };


  constructor(props) {
    super(props);
    this.state={
      ToothBefore:props.ToothBefore,
      ToothAfter:props.ToothAfter,
      ToothInfo:props.ToothInfo || ToothInfo ,
    }
  }
  componentDidMount() {

  }

  componentWillReceiveProps(nextProps) {
    this.setState({
      ToothBefore:nextProps.ToothBefore,
      ToothAfter:nextProps.ToothAfter,
      ToothInfo: nextProps.ToothInfo || ToothInfo
    })
  }

  onClickToothBit=()=>{
    this.props.onClickToothBit && this.props.onClickToothBit()
  }

  onSelectBit=({value,list,key})=>{
    if(!this.props.onSelectBitStatus){
      return
    }
    let { ToothInfo } = this.state;
    //value.status = value.status == 1 ? 0 : 1
    let bitValue = Object.assign(value)
    list = list.map((res,idx)=>{
      if (res.teethNumber == value.teethNumber) {
        res.status = res.status == 1 ? 0 : 1
      }
      return res
    })
    ToothInfo[key] = list
    this.setState({
      ToothInfo
    },()=>{
      this.props.onSelectBit && this.props.onSelectBit({
        ToothInfo,
        value:bitValue,
        list,
        key,
      })
    })
  }

  render() {
    const  { data,ToothInfo } = this.state;
    const Style1 = {
      color:'#fff',
    };
    const Style2 = {
      display: 'none'
    };

    return (
      <div onClick={this.onClickToothBit} className={styles.ToothBit}>
        <table border="0">
          <tbody>
          <tr>
            <td rowSpan="2" style={{ borderRightWidth: '0' ,borderBottomWidth: '0' ,paddingRight: '10px'}}>{this.state.ToothBefore}</td>
            <td style={{borderRight:'1px solid black'}}>
              {
                ToothInfo.topLeft && ToothInfo.topLeft.map((val,idx)=>{
                  return (
                    <div
                      onClick={(e)=>{
                        if(this.props.onSelectBitStatus){
                          e.stopPropagation();
                        }

                        this.onSelectBit({
                          value:val,
                          list:ToothInfo.topLeft,
                          key:'topLeft'
                        })
                      }}
                      className={classNames({
                          [styles.ToothBitS]:true,
                          [styles.teethNumberSelect]:val && val.status == 1,}
                      )}
                      key={`TopLeft${idx}`}>
                      <span className={styles.teethNumber} >{val.teethNumber}</span>
                      <div className={styles.ToothBitS0NE}>
                        {val.side &&  val.side.join(',')}
                      </div>
                    </div>)
                })
              }
              {
                !ToothInfo.topLeft && <div className={styles.ToothBitEmpty}/>
              }
            </td>
            <td>
              {
                ToothInfo.topRight && ToothInfo.topRight.map((val,idx)=>{
                    return (<div
                      onClick={(e) => {
                        if (this.props.onSelectBitStatus) {
                          e.stopPropagation();
                        }
                        this.onSelectBit({
                          value: val,
                          list: ToothInfo.topRight,
                          key: 'topRight'
                        })
                      }}
                      className={classNames({
                          [styles.ToothBitS]: true,
                          [styles.teethNumberSelect]: val && val.status == 1,
                        }
                      )}
                      key={`TopRight${idx}`}>
                      <span className={styles.teethNumber}>{val.teethNumber}</span>
                      <div className={styles.ToothBitS0NE}>
                        {val.side && val.side.join(',')}
                      </div>
                    </div>)
                })
              }
              {
                !ToothInfo.topRight  && <div className={styles.ToothBitEmpty}/>
              }
            </td>
            <td rowSpan="2" style={{ borderLeftWidth: '0' ,borderBottomWidth: '0' ,paddingLeft: '10px'}}>{this.state.ToothAfter}</td>
          </tr>
          <tr>
            <td style={{borderRight:'1px solid black'}}>
              {
                ToothInfo.bottomLeft && ToothInfo.bottomLeft.map((val,idx)=>{
                  return (<div
                    onClick={(e)=>{
                      if(this.props.onSelectBitStatus){
                        e.stopPropagation();
                      }
                      this.onSelectBit({
                          value:val,
                          list:ToothInfo.bottomLeft,
                          key:'bottomLeft'
                      })
                    }}
                    className={classNames({
                      [styles.ToothBitS]:true,
                      [styles.teethNumberSelect]:val && val.status == 1,}
                    )}
                    key={`BottomLeft${idx}`}>
                    <span className={styles.teethNumber} >{val.teethNumber}</span>
                    <div className={styles.ToothBitS0NE}>
                      {val.side &&  val.side.join(',')}
                    </div>
                  </div>)
                })
              }
              {
                !ToothInfo.bottomLeft  && <div className={styles.ToothBitEmpty}></div>
              }
            </td>
            <td>
              {
                ToothInfo.bottomRight && ToothInfo.bottomRight.map((val,idx)=>{
                  return (<div
                    onClick={(e)=>{
                      if(this.props.onSelectBitStatus){
                        e.stopPropagation();
                      }
                      this.onSelectBit({
                        value:val,
                        list:ToothInfo.bottomRight,
                        key:'bottomRight'
                      })
                    }}
                    className={classNames({
                      [styles.ToothBitS]:true,
                      [styles.teethNumberSelect]:val && val.status == 1,}
                    )}
                    key={`BottomRight${idx}`}>
                    <span className={styles.teethNumber} >{val.teethNumber}</span>
                    <div className={styles.ToothBitS0NE}>
                      {val.side && val.side.join(',')}
                    </div>
                  </div>)
                })
              }
              {
                !ToothInfo.bottomRight  && <div className={styles.ToothBitEmpty}></div>
              }
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    );
  }
}
export default ToothBit
