/**
 * @Description: PC-个人中心-我的病例页面
 */
import React, {useEffect, useRef, useState} from 'react';
import { history, connect } from 'umi';
import styles from './index.less';
import {debounce, throttle} from "lodash";
import {Table, Empty, Input, Modal, Checkbox, DatePicker, Button,Spin, Select } from 'antd'
const { RangePicker } = DatePicker;
import loadFail from '@/assets/GlobalImg/NoDataAvailable_ConsultationList_icon.png'
import ModalByConsultationDetails from '@/components/ModalByConsultationDetails'
import dayjs from "dayjs";
import classNames from "classnames";
import AgreementModal from '@/pages/ConsultationModule/StartConsultation/ComponentsPC/AgreementModal'
import {goConsultationDetail, StatusRuleByConsultation, useDebounce, getArrailUrl} from "@/utils/utils";
import {stringify} from "qs"


const initStatePage = {
  pageNum: 1,      // :1,
  pageSize:10,     // : 10,
  resultList:[],   // : [],
  total:0,         // : 0,
}

const statusText =  ['已取消', '待支付', '待支付', '已支付']
const secondQueryTypeArr = ['全部','草稿','进行中','已结束']
const Index: React.FC = (props: any) => {
  // 是否嵌套在5i5ya的iframe中
  const isInIframe = (self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya')

  const { query } = history.location
  const { loading, dispatch, pcAccount } = props;
  const scrollParentRef = useRef<HTMLDivElement | null>(null);
  const [ modelByVisible, setModelByVisible] = useState(false)                     // 是否打开指导订单详情弹窗
  const [ filterModelByVisible, setFilterModelByVisible] = useState(false)         // 是否打卡指导筛选弹窗
  const [ loadingResultListByState,setLoadingResultListByState ] = useState(null);
  // const [ _tableData, setTableData] = useState(initStatePage)           // 当前分页-列表数据
  // const [ _selectByModalByType, setSelectByModalByType] = useState('1')               // 筛选指导类型
  // const [ _selectByModalByStatus, setSelectByModalByStatus] = useState([])           // 筛选指导支付状态
  // const [ _selectByModalByProcessNode, setSelectByModalByProcessNode] = useState([]) // 筛选指导进度状态
  // const [ _orderNumber, setOrderNumber] = useState(null)                                // 筛选订单号
  // const [ _valueByRangePicker,setValueByRangePicker] = useState(null)                   // 筛选时间
  // const [ tabType,setTabType] = useState(1)                                                           // 当前tab 我发起的

  const userInfoData = JSON.parse(localStorage.getItem('userInfo'));
  const { friUserId:userId } = userInfoData || {}  // 获取用户

  // 页面进来初次加载
  const getInitialData = (params) => {
    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'pathname',       // 页面地址onchange事件
        pathnameByChild: '/UserInfo',  // 路由信息
        searchByChild: `?subTabKey=${params}&tabKey=6`,  // 路由信息
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
    }

    history.replace(`${history.location.pathname}?${stringify({
      ...history.location.query,
      tabKey: 6,
      subTabKey: params,
    })}`)
    // dispatch({ type: 'pcAccount/save',  payload: { tabState: 6, subTabState: params, } })
    dispatch({ type:'pcAccount/saveByPcMyConsultationList',  payload: { tabType:params, } })

    // 如果在5i5ya中，保存数据到父窗口model
    if (isInIframe) {
      // 发送登录成功的消息
      const postData = {
        dataType: 'saveData',       // 保存数据事件
        pageType: 'MyConsultation',
        saveDataObj: {
          tabType: params,
        }
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
    }
  }

  // 我发起的指导,和需要我指导的 的类型
  const setTabType = (params)=>{
    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'pathname',       // 页面地址onchange事件
        pathnameByChild: '/UserInfo',  // 路由信息
        searchByChild: `?subTabKey=${params}&tabKey=6`,  // 路由信息
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
    }

    history.replace(`${history.location.pathname}?${stringify({
      ...history.location.query,
      tabKey: 6,
      subTabKey: params,
    })}`)
    // dispatch({ type: 'pcAccount/save',  payload: { tabState: 6, subTabState: params, } })
    // dispatch({ type:'pcAccount/saveByPcMyConsultationList',  payload: { tabType:tabType, } })
    dispatch({
      type: "pcAccount/saveByPcMyConsultationList",
      payload: {
        tabType: params,
        secondQueryType:0, // 当选择一级标签后二级标签要重置
      }
    })

    // 如果在5i5ya中，保存数据到父窗口model
    if (isInIframe) {
      // 发送登录成功的消息
      const postData = {
        dataType: 'saveData',       // 保存数据事件
        pageType: 'MyConsultation',
        saveDataObj: {
          tabType: params,
          secondQueryType: 0, // 当选择一级标签后二级标签要重置
        }
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
    }
  }

  // 我的指导 二级查询类型(0全部、1草稿、2指导中、3指导结束)
  const setSecondQueryType = (params)=>{
    dispatch({
      type: "pcAccount/saveByPcMyConsultationList",
      payload: { secondQueryType: params }
    })

    // 如果在5i5ya中，保存数据到父窗口model
    if (isInIframe) {
      // 发送登录成功的消息
      const postData = {
        dataType: 'saveData',       // 保存数据事件
        pageType: 'MyConsultation',
        saveDataObj: {
          secondQueryType: params
        }
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
    }
  }

  // 指导当前分页和数据
  const setTableData = (params)=>{
    dispatch({
      type: "pcAccount/saveByPcMyConsultationList",
      payload: { tableData: params }
    })

    // 如果在5i5ya中，保存数据到父窗口model
    if (isInIframe) {
      // 发送登录成功的消息
      const postData = {
        dataType: 'saveData',       // 保存数据事件
        pageType: 'MyConsultation',
        saveDataObj: {
          tableData: {
            ...params,
            resultList: [],// 加上这个就数据太多了，置为空再发送数据
          },
        }
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
    }
  }
  // 选择指导类型
  const setSelectByModalByType = (params)=>{
    dispatch({
      type: "pcAccount/saveByPcMyConsultationList",
      payload: { selectByModalByType: params }
    })

    // 如果在5i5ya中，保存数据到父窗口model
    if (isInIframe) {
      // 发送登录成功的消息
      const postData = {
        dataType: 'saveData',       // 保存数据事件
        pageType: 'MyConsultation',
        saveDataObj: {
          selectByModalByType: params
        }
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
    }
  }

  // 选择指导支付状态
  const setSelectByModalByStatus = (params)=>{
    dispatch({
      type: "pcAccount/saveByPcMyConsultationList",
      payload: { selectByModalByStatus: params }
    })

    // 如果在5i5ya中，保存数据到父窗口model
    if (isInIframe) {
      // 发送登录成功的消息
      const postData = {
        dataType: 'saveData',       // 保存数据事件
        pageType: 'MyConsultation',
        saveDataObj: {
          selectByModalByStatus: params
        }
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
    }
  }

  // 选择指导进度状态
  const setSelectByModalByProcessNode = (params)=>{
    dispatch({
      type: "pcAccount/saveByPcMyConsultationList",
      payload: { selectByModalByProcessNode: params }
    })

    // 如果在5i5ya中，保存数据到父窗口model
    if (isInIframe) {
      // 发送登录成功的消息
      const postData = {
        dataType: 'saveData',       // 保存数据事件
        pageType: 'MyConsultation',
        saveDataObj: {
          selectByModalByProcessNode: params
        }
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
    }
  }

  // 指导订单号
  const setOrderNumber = (params)=>{
    dispatch({
      type: "pcAccount/saveByPcMyConsultationList",
      payload: { searchText: params }
    })

    // 如果在5i5ya中，保存数据到父窗口model
    if (isInIframe) {
      // 发送登录成功的消息
      const postData = {
        dataType: 'saveData',       // 保存数据事件
        pageType: 'MyConsultation',
        saveDataObj: {
          searchText: params
        }
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
    }
  }

  const setSearchType= (params)=>{
    dispatch({
      type: "pcAccount/saveByPcMyConsultationList",
      payload: { searchType: params }
    })

    // 如果在5i5ya中，保存数据到父窗口model
    if (isInIframe) {
      // 发送登录成功的消息
      const postData = {
        dataType: 'saveData',       // 保存数据事件
        pageType: 'MyConsultation',
        saveDataObj: {
          searchType: params
        }
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
    }
  }

  // 指导时间范围
  const setValueByRangePicker = (params)=>{
    dispatch({
      type: "pcAccount/saveByPcMyConsultationList",
      payload: { valueByRangePicker: params }
    })

    // 如果在5i5ya中，保存数据到父窗口model
    if (isInIframe) {
      // postMessage无法发送时间格式的信息，先处理一下
      const paramsFormat = []
      if (params && params[0]) {
        paramsFormat.push(dayjs(params[0]).format("YYYY-MM-DD"))
      }
      if (params && params[1]) {
        paramsFormat.push(dayjs(params[1]).format("YYYY-MM-DD"))
      }
      // 发送登录成功的消息
      const postData = {
        dataType: 'saveData',       // 保存数据事件
        pageType: 'MyConsultation',
        saveDataObj: {
          valueByRangePicker: paramsFormat
        }
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
    }
  }

  // 清空筛选条件
  const cleanByPcMyConsultationList = ()=>{
    dispatch({
      type: "pcAccount/cleanByPcMyConsultationList",
      payload: {}
    })

    // 如果在5i5ya中，保存数据到父窗口model
    if (isInIframe) {
      // 发送登录成功的消息
      const postData = {
        dataType: 'cleanData',       // 保存数据事件
        pageType: 'MyConsultation',
        saveDataObj: {

        }
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
    }
  }

  const { stateByPcMyConsultationList } = pcAccount || {}
  const {
    selectByModalByType, //:1,          // 我的指导-选择指导类型
    selectByModalByStatus, //:[],       // 我的指导-筛选指导支付状态
    selectByModalByProcessNode, //:[],  // 我的指导-筛选指导进度状态
    orderNumber, //:null,               // 我的指导-订单号
    searchText, //:null,                // 我的指导-搜索文本
    searchType,                         // 我的指导-搜索类型
    valueByRangePicker, //:null,        // 我的指导-时间范围
    tableData, // :initState,           // 我的指导-当前分页
    tabType, //:1,                      // 我的指导-当前tab
    secondQueryType,                    // 二级查询类型(0全部、1草稿、2指导中、3指导结束)
  } = stateByPcMyConsultationList || {} // 我的指导-筛选条件

  const { total, listDate,pageNum, pageSize } = tableData || {}
  // const { pageNum, hasMore, loadMore } = statePage || {}
  const ModalByType = [{label: '图文指导', value: '1'}, {label: '视频指导', value: '2'},{label: '正畸方案审核', value: '3'}] // 指导类型
  const ModalByStatus = [{label: '全部', value: '-1'},{label: '待支付', value: '1'}, {label: '已支付', value: '2'}] // 指导状态
  const tabLists = [
    { id: 1, val: '我发起的' },
    { id: 2, val: '需要我指导的' },
  ]

  const ModalByProcessNodeBy1 = [
    {label: '全部', value: '-1', isDisabled:false },
    {label: '选择指导方式', value: '1', isDisabled:true},
    {label: '描述病例问题', value: '2', isDisabled:true},
    {label: '支付指导费用', value: '3', isDisabled:true},
    {label: '病例资料被查看', value: '4'},
    {label: '问题被回复并对话', value: '5'},
    {label: '结束指导交易成功', value: '6'},
  ] // 图文指导状态


  const ModalByProcessNodeBy2 = [
    {label: '全部', value: '-1'},
    {label: '选择指导方式', value: '1', isDisabled:true},
    {label: '描述病例问题', value: '2', isDisabled:true},
    {label: '提交指导单', value: '3', isDisabled:true},
    {label: '病例资料被查看', value: '4'},
    {label: '预约视频会议', value: '5'},
    {label: '视频沟通', value: '6'},
    {label: '结束指导', value: '7'},
    {label: '支付指导费用', value: '8'},
    {label: '交易成功', value: '9'},
  ] // 视频指导状态

  // 正畸审核[1基本信息、2检查及分析、3问题清单及诊断、4治疗方案、5影像资料、6提交病例、7病例被查看、8审核驳回、9审核通过、10指导结束])
  const ModalByProcessNodeBy3  = [
    {label: '全部', value: '-1'},
    {label: '病例资料被查看', value: '7'},
    {label: '审核驳回', value: '8'},
    {label: '审核通过', value: '9'},
    {label: '结束指导', value: '10'},
  ]

  const columns = [
      {
        title: '订单号',
        dataIndex: 'orderNumber',
        key: 'orderNumber',
        width: 215,
        align: 'left',
        className: 'orderNumber',
        render: (text) => {
          return (
            <span style={{color: '#6A727D'}}>{text ? text : '-'}</span>
          )
        }
      },
      {
        title: '订单类型',
        dataIndex: 'orderType',
        key: 'orderType',
        width: 126,
        align: 'left',
        className: 'orderType',
        render: (text) => {
          // 订单类型(1付费指导[图文/视频]、2正畸方案审核)
          let typeArr = ['','付费指导','正畸方案审核']
          return (
            <span style={{color: '#6A727D'}}>{typeArr[text]}</span>
          )
        }
      },
      {
        title: '指导类型',
        dataIndex: 'type',
        key: 'type',
        width: 110,
        align: 'left',
        className: 'type',
        render: (text) => {
          let typeArr = ['','图文指导','视频指导','正畸方案审核']
          return (
            <span style={{color: '#6A727D'}}>{typeArr[text]}</span>
          )
        }
      },
      {
        title: '病历号',
        dataIndex: 'fileNumber',
        key: 'fileNumber',
        width: 130,
        align: 'left',
        className: 'fileNumber',
        render: (text) => {
          return (
            <span style={{color: '#6A727D'}}>{text ? text : '-'}</span>
          )
        }
      },
      {
        title: '患者姓名',
        dataIndex: 'customerName',
        key: 'customerName',
        width: 96,
        align: 'left',
        className: 'customerName',
        render: (text) => {
          return (
            <span style={{color: '#6A727D'}}>{text ? text : '-'}</span>
          )
        }
      },
      {
        title: '创建人',
        dataIndex: 'createUserName',
        key: 'createUserName',
        width: 79,
        align: 'center',
        className: 'createUserName',
        render: (text) => {
          return (
            <span style={{color: '#6A727D'}}>{text ? text : '-'}</span>
          )
        }
      },

    {
      title: '支付方式',
      dataIndex: 'payTypeStr',
      key: 'payTypeStr',
      width: 96,
      align: 'center',
      className: 'payTypeStr',
      render: (text) => {
        return (
          <span style={{color: '#6A727D'}}>{text ? text : '-'}</span>
        )
      }
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 96,
      align: 'center',
      className: 'amount',
      render: (text,value) => {
        let amoutByType = showPaymentPrice(value);
        return (
          <span style={{color: '#6A727D'}}>{!!amoutByType ? `${amoutByType}` : '-'}</span>
        )
      }
    },
    // {
    //   title: '单价',
    //   dataIndex: 'vipUnitPrice',
    //   key: 'vipUnitPrice',
    //   width: 120,
    //   align: 'center',
    //   className: 'vipUnitPrice',
    //   render: (text,value) => {
    //     if(value.type == 1 ){
    //       return ( <span style={{color: '#6A727D'}}>{text ? `¥${text}/次` : '-'}</span> )
    //     }else {
    //       return ( <span style={{color: '#6A727D'}}>{text ? `¥${text}/30min` : '-'}</span> )
    //     }
    //   }
    // },
    {
      title: '创建时间',
      dataIndex: 'createDate',
      key: 'createDate',
      width: 110,
      align: 'center',
      className: 'createDate',
      render: (text) => {
        return (
          <span style={{color: '#6A727D'}}>{text ? dayjs(text,'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD') : '-'}</span>
        )
      }
    },{
      title: '支付状态',
      dataIndex: 'status',
      key: 'status',
      width: 97,
      align: 'center',
      className: 'status',
      render: (text) => {
        return (
          <span style={{color: '#6A727D'}}>{
            statusText[text]
          }</span>
        )
      }
    },
    {
      title: '进度',
      dataIndex: 'processNode',
      key: 'processNode',
      width: 97,
      align: 'center',
      className: 'processNode',
      render: (text,value) => {
        let processNodeText= null;
        if(value.type == 1){
          processNodeText = ModalByProcessNodeBy1.find(item => item.value == text)
        }else if(value.type == 2){
          processNodeText = ModalByProcessNodeBy2.find(item => item.value == text)
        }else if(value.type == 3){
          processNodeText = ModalByProcessNodeBy3.find(item => item.value == text)
        }
        return (
          <span style={{color: '#6A727D'}}>{StatusRuleByConsultation(value) ? StatusRuleByConsultation(value) : '-'}</span>
        )
      }
    },

    {
      title: '指导专家',
      dataIndex: 'expertsName',
      key: 'expertsName',
      width: 97,
      align: 'center',
      className: 'expertsName',
      render: (text) => {
        return (
          <span style={{color: '#6A727D'}}>{text ? text : '-'}</span>
        )
      }
    },

    {
      title: '审核状态',
      dataIndex: 'latestAuditStatus',
      key: 'latestAuditStatus',
      width: 104,
      align: 'center',
      className: 'latestAuditStatus',
      render: (text) => {
        let latestAuditStatusArr = ['','审核通过','审核驳回']
        return (
          <span style={{color: '#6A727D'}}>{latestAuditStatusArr[text] ?latestAuditStatusArr[text] : '-' }</span>
        )
      }
    },

    {
      title: '审核意见',
      dataIndex: 'latestAuditOpinion',
      key: 'latestAuditOpinion',
      width: 160,
      align: 'center',
      className: 'latestAuditOpinion',
      render: (text) => {
        return (
          <span style={{color: '#6A727D'}}>{text ? text : '-'}</span>
        )
      }
    },

    {
      title: '操作',
      // dataIndex: 'operation',
      key: 'operation',
      width: 140,
      align: 'right',
      className: 'operation',
      fixed: 'right',
      render: (data) => {
        const { createUserId } = data || {};
        let detailsText = '详情';
        if ((data.type == 3 && data.processNode < 6) || (data.type != 3 && data.processNode < 4)) {
          detailsText = '编辑'
        }

        return (
          <div>
            {(data && data.type == 2 && data.status == 2 && createUserId == userId) &&
              <span
                className={styles.operation}
                style={{ color: '#0095FF' ,marginRight: '10px' }}
                onClick={()=>{
                  if (data.type == 1) {
                    goConsultationDetail(data)
                  } else {
                    // 点击去支付弹出订单弹窗
                    setModelByVisible(data.id)
                  }
                }}
              >
                {'去支付'}
              </span>
            }
            <span
              className={styles.operation}
              style={{ color: '#0095FF' }}
              onClick={()=>{
                // 跳转到指导详情页面
                goConsultationDetail(data)
              }}
            >
              {detailsText}
            </span>
          </div>
        )
      }
    },
  ]

  useEffect(() => {
    // getConsultationList();  // 获取指导订单列表
    const { subTabState,stateByPcMyConsultationList } = pcAccount || {};
    const { tabType } = stateByPcMyConsultationList || {};
    getInitialData(query.subTabKey ? query.subTabKey : tabType ? tabType : subTabState ? subTabState : 1);
    // 我的指导列表页面 预登录tim
    dispatch({ type: 'tim/getTim' }).then((res: any) => {})
  },[])

  // 展示支付价格
  const showPaymentPrice = (item)=>{
    const {
      type,
      status,
      freeTimes,
      amount,
      vipUnitPrice,
    } =  item || {}

    let amoutByType = '-';
    // 图文指导
    if(type == 1) {
      // 是否已支付
      if(status == 3) {
        // 已支付
        // 已支付是否使用免费次数
        if (freeTimes && freeTimes != 0) {
          // 图文指导-已支付-使用免费次数
          amoutByType = !!amount ? amount : `¥ 0`;
        }else {
          // 图文指导-已支付-未使用免费次数
          amoutByType =  vipUnitPrice ? `¥ ${vipUnitPrice}` : null;
        }
      }else {
        // 图文指导-未支付
        amoutByType =  vipUnitPrice ? `¥ ${vipUnitPrice}` : null;
      }
    }else if(type == 2){
      if(status == 3) {
        if (freeTimes && freeTimes != 0) {
          // 指导-视频指导
          amoutByType =  amount ? `¥ ${amount}` : null;
        } else {
          amoutByType =  amount ? `¥ ${amount}` : null;
        }
      }else if(status == 1) {
        amoutByType =  '待评估'
      }else {
        amoutByType =  amount ? `¥ ${amount}` : null;
      }
    }else {
      amoutByType =  null;
    }

    return amoutByType ? amoutByType : '-';
  }

  // 输入的订单号
  const onChangeByInput = (e)=>{
    let value = e.target.value;
    setOrderNumber(value ? value.trim() : null);
  }

  // 订单号改变后调用刷新列表方法
  useEffect(() => {
    // getConsultationListByOrderNumber(searchText);
  },[searchText])

  // 切换tab
  useEffect(()=>{
    // cleanByPcMyConsultationList();
    getConsultationListByOrderNumber();
    // dispatch({ type: 'pcAccount/save',  payload: { tabState: 6, subTabState: tabType, } })
    // dispatch({ type:'pcAccount/saveByPcMyConsultationList',  payload: { tabType:tabType, } })
  },[tabType])

  // 切换二级查询类型(0全部、1草稿、2指导中、3指导结束)
  useEffect(()=>{
    getConsultationListByOrderNumber();
  },[secondQueryType])

  const getConsultationListByOrderNumber = useDebounce((searchText)=>{
    console.log('searchText123123 :: ',searchText);
    if(searchText) {
      getConsultationList({pageNumByParams:1,pageSizeByParams:10});
    }else {
      getConsultationList();
    }
  },500)


  // 获取指导订单列表
  const getConsultationList = async (params)=>{
    const  { pageNumByParams,pageSizeByParams } = params || {}
    await setLoadingResultListByState(true);
    const userInfoData = JSON.parse(localStorage.getItem('userInfo'));
    let { friUserId:id } = userInfoData || {}  // 获取用户id

    // 指导类型(1图文、2视频、3正畸方案审核)
    let type = selectByModalByType ? selectByModalByType : null;
    // 指导状态(1待支付、2待指导、3指导中、4已完成、5已取消)
    let status = Array.isArray(selectByModalByStatus) && selectByModalByStatus[0] && selectByModalByStatus[0] != -1 ? selectByModalByStatus : [];
    // 流程节点(图文流程节点[1选择指导方式、2描述病例问题、3支付指导费用、4病例资料被查看、5问题被回复并对话、6结束指导交易成功];  视频流程节点[1选择指导方式、2描述病例问题、3提交指导单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、  7结束指导、8确认并支付指导费用、9交易成功])
    let processNode = Array.isArray(selectByModalByProcessNode) && selectByModalByProcessNode[0] && selectByModalByProcessNode[0] != -1 ? selectByModalByProcessNode : [];

    // 拼接参数
    let paramsByGetConsultationList = {
      wxUserId: id,           // 用户id
      friUserId: id,          // 用户id
      queryType: tabType,     // 查询类型(1我的指导、2我发起的指导)
      secondQueryType:secondQueryType,      // 二级查询类型(0全部、1草稿、2指导中、3指导结束)
      expertsId: id,          // 专家id
      // expertsId: tabType == TypesArr[1].type ? id : null,
      pageNum: pageNumByParams ? pageNumByParams : pageNum || 1,       // 页码
      pageSize: pageSizeByParams ? pageSizeByParams : pageSize || 10,  // 每页条数
      type:type,               // 指导类型
      statusList:status,           // 指导状态
      processNodeList:processNode, // 流程节点
      orderNumber: searchType == 1 ? searchText : null, // 搜索订单编号
      createUserName:searchType == 4 ?  searchText : null, // 创建人
      customerName:searchType == 3 ? searchText : null, // 患者名称
      fileNumber: searchType == 2 ? searchText : null,  // 患者病历号
      startDate:valueByRangePicker && valueByRangePicker[0] ? dayjs(valueByRangePicker[0]).format("YYYY-MM-DD") : null, // 开始时间
      endDate:valueByRangePicker && valueByRangePicker[1] ? dayjs(valueByRangePicker[1]).format("YYYY-MM-DD") : null, // 结束时间
    }

    let paramsOrderNumberByGetConsultationList = {
      wxUserId: id,           // 用户id
      friUserId: id,          // 用户id
      queryType: tabType,     // 查询类型(1我的指导、2我发起的指导)
      secondQueryType:secondQueryType,      // 二级查询类型(0全部、1草稿、2指导中、3指导结束)
      expertsId: id,          // 专家id
      // expertsId: tabType == TypesArr[1].type ? id : null,
      pageNum: pageNumByParams ? pageNumByParams : pageNum || 1,       // 页码
      pageSize: pageSizeByParams ? pageSizeByParams : pageSize || 10,  // 每页条数
      orderNumber: searchType == 1 ? searchText : null, // 搜索订单编号
      createUserName:searchType == 4 ?  searchText : null, // 创建人
      customerName:searchType == 3 ? searchText : null, // 患者名称
      fileNumber: searchType == 2 ? searchText : null,  // 患者病历号
      type:null,               // 指导类型
    }

    let getConsultationListByObj = await dispatch({
      type: 'ConsultationList/getConsultationList',
      payload: searchText ? paramsOrderNumberByGetConsultationList : paramsByGetConsultationList,
    })
    await setLoadingResultListByState(false);
    const {
      code,
      content,
    } = getConsultationListByObj || {};
    if(code == 200) {
      /*const {
        pageNum,    // :1,
        pageSize,   // : 10,
        resultList, // : [],
        total,      // : 0,
      } = content || {};*/
      setTableData(content);
    }else {
      setTableData({
        ...initStatePage,
        errorStatus: 'error',
      });
    }
  }


  return <>
      <div>
        <div className={styles.tab_wrap}>
          {
            tabLists.map(item => {
              return <div key={item.id} className={classNames({[styles.tab_init]: true, [styles.tab_active]: tabType == item.id })} onClick={() => {
                if(item.id == tabType) {return}
                  cleanByPcMyConsultationList();
                  setTabType(item.id);
              }}>{item.val}</div>
            })
          }
        </div>
        <div className={styles.warp_filter}>
          <div className={styles.Statusfilter_warp}>
            {secondQueryTypeArr.map((item,index)=>{
              if (tabType == 2 && index == 1) {
                return null;
              }else {
                return (
                  <div
                    key={index}
                    onClick={() => {
                      setSecondQueryType(index);
                    }}
                    className={classNames({
                      [styles.Statusfilter_box]: true,
                      [styles.Statusfilter_Activity]: index == secondQueryType,
                    })}>{item}</div>
                )
              }
            })}
          </div>

          <div className={styles.filter_box_box}>
            <div onClick={()=>{
              setFilterModelByVisible(true)
            }} className={styles.warp_filter_box}>
              <div className={styles.warp_filter_box_Icon}></div>
              <div className={styles.warp_filter_box_text}>筛选</div>
            </div>
            {/* 请输入订单号搜索框 */}
            <div className={styles.input_filter_search_box}>
              <div className={styles.warp_filter_select}>
                <Select
                  defaultValue={searchType || '3'}
                  style={{ width: 102 }}
                  onChange={(value)=>{ setSearchType(value) }}
                  options={[
                    { value: '1', label: '订单号' },
                    { value: '2', label: '病历号' },
                    { value: '3', label: '患者姓名' },
                    { value: '4', label: '创建人' },
                  ]}
                ></Select>
              </div>
              <div className={styles.warp_filter_input_warp}>
                <Input
                  value={searchText}
                  onChange={onChangeByInput}
                  onPressEnter={(e)=>{
                    console.log('e13123 :: ',e,searchText);
                    getConsultationListByOrderNumber(searchText);
                  }}
                  autoComplete="off"
                  placeholder={'请输入搜索内容'}
                ></Input>
                <div
                  onClick={()=>{ getConsultationList({pageNumByParams:1,pageSizeByParams:10}); }}
                  className={styles.SearchOrderNumber_Icon}
                ></div>
              </div>
            </div>
          </div>
        </div>
        <Spin spinning={!!loading.effects['ConsultationList/getConsultationList']}>
          <div className={styles.tab_content_list} ref={(ref) => (scrollParentRef.current = ref)}>
            {/*<InfiniteScroll
              loadMore={handleInfiniteOnLoad}
              threshold={50}
              pageStart={1}
              initialLoad={false}
              hasMore={!loadMore && hasMore}
              useWindow={false}
              getScrollParent={() => scrollParentRef.current}
              className={styles.scroll_box}
            >*/}
              <div className={styles.space_wrap}>
                <Table
                  scroll={{ y: isInIframe ? 'calc(100vh - 240px)' : 'calc(100vh - 500px)' }}
                  columns={columns}
                  dataSource={tableData && tableData.resultList}
                  key={record => record.id}
                  rowKey={record => record.id}
                  pagination={{
                    showSizeChanger: true,
                    showQuickJumper: false,
                    pageSizeOptions: ['10', '20'],
                    total: tableData && tableData.total,
                    pageSize: pageSize,
                    current: pageNum,
                    onChange: (page, pageSize) => {
                      getConsultationList({pageNumByParams:page,pageSizeByParams: pageSize})
                    }
                  }}
                  locale={{
                    emptyText: () => (
                      <div className={styles.empty}>
                        {
                          tableData && tableData.errorStatus === 'error' ?
                            <Empty image={loadFail} description="加载失败..."></Empty>
                            : tableData && tableData.errorStatus === 'nodata' ?
                              <Empty image={loadFail} description="暂无数据"></Empty>
                              : tableData && tableData.errorStatus === 'nodataFilter' ?
                                <Empty image={loadFail} description="搜索结果为空，换一个关键词试试吧～"></Empty>
                                : tableData && tableData.resultList && tableData.resultList.length === 0 ?
                                  <Empty image={loadFail} description="暂无数据"></Empty>
                                  : null
                        }
                      </div>
                    )
                  }}
                />
              </div>
              {/* </InfiniteScroll> */}
          </div>
        </Spin>

        <ModalByConsultationDetails
          visibleAndId={modelByVisible}         // 指导id,传入id,打开弹窗 销毁id关闭弹窗
          onCancel={(isOpenPayModal)=>{
            setModelByVisible(null);      // 点击关闭弹窗
            console.log('isOpenPayModal1231 :: ',isOpenPayModal);
            if(isOpenPayModal) {
              // 刷新列表状态
              getConsultationList();
            }
          }}
        />

        <Modal
          title="筛选"
          width={480}
          footer={null}
          open={filterModelByVisible}
          onOk={()=>{setFilterModelByVisible(false)}}
          onCancel={()=>{setFilterModelByVisible(false)}}
        >
         <div className={styles.ModalByFilter}>
           <div className={styles.fieldName}>指导类型</div>
           <div className={styles.fieldContent}>
             <Checkbox.Group value={[selectByModalByType]}>
               {ModalByType.map((item) => {
                   return <Checkbox key={item.value} onChange={(e)=> {
                     setSelectByModalByType(e.target.value)
                     setSelectByModalByProcessNode([])
                     setSelectByModalByStatus([])
                   }} value={item.value}>{item.label}</Checkbox>
               })}
             </Checkbox.Group>
           </div>
           {!!selectByModalByType &&
           <>
             <div className={styles.fieldName}>进度</div>
             <div className={styles.fieldContent}>
               <Checkbox.Group onChange={(e)=>{
                       const set1 = new Set(e);
                       const set2 = new Set(selectByModalByProcessNode);
                       // 找到 arr2 中多出来的元素
                       const extraInArr2 = [...set1].filter(item => !set2.has(item));
                       if (extraInArr2.length > 0 && extraInArr2[0]) {
                         if (extraInArr2[0] == '-1') {
                            setSelectByModalByProcessNode(['-1']);
                         }else {
                           e = e.filter((itemByCheckBoxValue) => itemByCheckBoxValue !== '-1');
                           setSelectByModalByProcessNode(e)
                         }
                       }else {
                         setSelectByModalByProcessNode(e)
                       }
                     }} value={selectByModalByProcessNode}>
                     {selectByModalByType == 1 &&
                       <>
                         {ModalByProcessNodeBy1.map((item) => {
                             if (!item.isDisabled) {
                               return <Checkbox
                                 key={item.value}
                                 onChange={(e)=>{
                                   // setSelectByModalByProcessNode([e.target.value])
                                 }}
                                 value={item.value}>
                                 {item.label}
                               </Checkbox>
                             }
                          })}
                       </>
                     }
                     {selectByModalByType == 2 &&
                       <>
                         {ModalByProcessNodeBy2.map((item) => {
                           if (!item.isDisabled) {
                             return <Checkbox
                               key={item.value}
                               onChange={(e) => {
                                 setSelectByModalByProcessNode([e.target.value])
                               }}
                               value={item.value}>
                               {item.label}
                             </Checkbox>
                           }
                         })}
                       </>
                     }
                     {selectByModalByType == 3 &&
                       <>
                         {ModalByProcessNodeBy3.map((item) => {
                           if (!item.isDisabled) {
                             return <Checkbox
                               key={item.value}
                               onChange={(e) => {
                                 setSelectByModalByProcessNode([e.target.value])
                               }}
                               value={item.value}>
                               {item.label}
                             </Checkbox>
                           }
                         })}
                       </>
                     }

                   </Checkbox.Group>
               </div>
           </>
          }
          {selectByModalByType != 3 &&
              <>
               <div className={styles.fieldName}>支付状态</div>
               <div className={styles.fieldContent}>
                 <Checkbox.Group value={selectByModalByStatus} onChange={(e)=>{
                   const set1 = new Set(e);
                   const set2 = new Set(selectByModalByStatus);
                   // 找到 arr2 中多出来的元素
                   const extraInArr2 = [...set1].filter(item => !set2.has(item));
                   if (extraInArr2.length > 0 && extraInArr2[0]) {
                     if (extraInArr2[0] == '-1') {
                       setSelectByModalByStatus(['-1']);
                     }else {
                       e = e.filter((itemByCheckBoxValue) => itemByCheckBoxValue !== '-1');
                       setSelectByModalByStatus(e)
                     }
                   }else {
                     setSelectByModalByStatus(e)
                   }
                 }}>
                   {ModalByStatus.map((item) => {
                     return <Checkbox
                       key={item.value}
                       onChange={(e)=>{
                         // setSelectByModalByStatus([e.target.value])
                       }}
                       value={item.value}>
                        {item.label}
                       </Checkbox>
                   })}
                 </Checkbox.Group>
               </div>
             </>
           }
           <div className={styles.fieldName}>创建时间</div>
           <div className={styles.fieldContent}>
             <RangePicker
               // showTime={{ format: 'YYYY-MM-DD' }}
               format="YYYY-MM-DD"
               allowClear={true}
               value={valueByRangePicker}
               onChange={(date:any, dateString:any)=>{
                 setValueByRangePicker(date)
               }}
             />
           </div>
           <div className={styles.footer}>
             <Button onClick={()=>{
               setFilterModelByVisible(false); // 关闭弹窗
               getConsultationList()
             }} style={{marginRight:'10px'}}> 取消 </Button>
             <Button onClick={()=>{
               getConsultationList({pageNumByParams:1,pageSizeByParams:pageSize})
               setFilterModelByVisible(false); // 关闭弹窗
             }} type="primary" > 确认 </Button>
           </div>
         </div>
        </Modal>
      </div>
  </>
}
export default connect(({tim, userInfoStore, pcAccount, loading }: any) => ({ tim,userInfoStore, pcAccount, loading }))(Index)
