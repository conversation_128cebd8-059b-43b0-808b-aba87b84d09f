import React, {useState, useEffect, useRef} from 'react';
import { history,connect } from 'umi';
import styles from "@/pages/PaymentByConsultation/MyConsultationList/index.less";
import {getOperatingEnv, goConsultationDetail, StatusRuleByConsultation} from "@/utils/utils";
import classNames from "classnames";
import { Spin,message } from "antd";
import NavBar from "@/components/NavBar";
import InfiniteScroll from 'react-infinite-scroller';
import {throttle} from "lodash";
import dayjs from "dayjs";
import {stringify} from "qs";

const initStatePage = {
  pageNum: 1,
  hasMore: true,  // 加载更多
  loadMore: false,
}

const initState = {
  total: 0,
  resultList: [],
}

const TypesArr = [
  { type: 1, text: '我发起的' },
  { type: 2, text: '需要我指导的' }
]
const statusText = ['已取消', '待支付', '待支付', '已支付']

const MyConsultationList: React.FC = (props) => {
  const { pathname, query } = props.location || {}
  const { tabType:tabTypeByQuery } = query || {}
  let findByTypesArr = TypesArr.find((item)=>{ return item.type == tabTypeByQuery })
  const [ tabType,setTabType ] =  useState(!!findByTypesArr ? tabTypeByQuery : TypesArr[0].type);
  const scrollParentRef = useRef<HTMLDivElement | null>(null);
  const [ state, setState] = useState(initState)                        // 列表数据
  const [ statePage, setStatePage] = useState(initStatePage)        // 当前分页
  const [ loadingResultListByState,setLoadingResultListByState ] = useState(null);
  const { dispatch } = props;
  const { total, listDate } = state
  const { pageNum, hasMore, loadMore } = statePage || {};
  const userInfo = JSON.parse(localStorage.getItem('userInfo'));
  const { friUserId:idByUserInfo, name} = userInfo || {}


    // 微信浏览器使用
  let OperatingEnv = getOperatingEnv();


  useEffect(() => {
    setState(initState)
    setStatePage(initStatePage)
    getConsultationList(1);  // 获取指导订单列表
    const {
      pathname:pathnameByLocation,
      query:queryByLocation,
    } = props.location || {}
    history.replace({
      pathname: pathnameByLocation,
      query: {
        ...queryByLocation,
        tabType: tabType,
      }
    })
  },[tabType])

  // 获取指导订单列表
  const getConsultationList = async (pageNum)=>{
    await setLoadingResultListByState(true);
    const userInfoData = JSON.parse(localStorage.getItem('userInfo'));
    const { friUserId:id } = userInfoData || {}  // 获取用户id

    let getConsultationListByObj = await dispatch({
      type: 'ConsultationList/getConsultationList',
      payload: {
        wxUserId:  id,
        friUserId: id,
        expertsId: id,
        secondQueryType:0,      // 二级查询类型(0全部、1草稿、2指导中、3指导结束)
        queryType:tabType,
        pageNum: pageNum ? pageNum : 1,
        pageSize:10,
      }
    })
    await setLoadingResultListByState(false);
    const {
      code,
      content,
    } = getConsultationListByObj || {};
    if(code == 200) {
      const {
        pageNum,    // :1,
        pageSize,   // : 10,
        resultList, // : [],
        total,      // : 0,
      } = content || {};
      let data = pageNum == 1 ? [] : listDate;
      data = Array.isArray(data)  ? data.concat(resultList || []) : [].concat(resultList || []);
      const hasMore = data.length !== total;

      if (Array.isArray(data) && data.length == 0) {
        setState({
          ...state,
          listDate: [],
          total: 0,
        })
        return
      }
      setState({
        ...state,
        listDate: [...data],
        total,
      })
      setStatePage({
        ...statePage,
        loadMore: false,
        hasMore,
        pageNum,
      })

    }
  }

  let handleInfiniteOnLoad = () => {
    if (Array.isArray(listDate) && listDate.length > total - 1) {
      setStatePage({
        ...statePage,
        loadMore: false,
        hasMore: false
      })
      return;
    }
    const pages = pageNum + 1;
    setStatePage({
      ...statePage,
      loadMore: true,
    })
    getConsultationList(pages)
  }

  handleInfiniteOnLoad = throttle(handleInfiniteOnLoad, 100);

  // 展示支付价格
  const showPaymentPrice = (item)=>{
    const {
      type,
      status,
      freeTimes,
      amount,
      vipUnitPrice,
    } =  item || {}

    // 图文指导
    if(type == 1) {
      // 是否已支付
      if(status == 3) {
        // 已支付
        // 已支付是否使用免费次数
        if (freeTimes && freeTimes != 0) {
          // 图文指导-已支付-使用免费次数
          return amount;
        }else {
          // 图文指导-已支付-未使用免费次数
          return vipUnitPrice;
        }
      }else {
        // 图文指导-未支付
        return vipUnitPrice;
      }
    }else {
      // 视频指导
      return amount;
    }
  }

  // 返回指导状态 是否以回复
  const getProcessNodeText=(item)=>{
    {/*processNode,  // : 2,//流程节点(
     图文流程节点[1选择指导方式、2描述病例问题、3支付指导费用、4病例资料被查看、5问题被回复并对话、6结束指导交易成功];
     视频流程节点[1选择指导方式、2描述病例问题、3提交指导单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、 7结束指导、8确认并支付指导费用、9交易成功])
    */}
    if (!item) { return '' }
    if (item.type == 1) {
      return item.processNode > 5 ? '已回复' : '待回复'
    }else {
      return item.processNode > 6 ? '已回复' : '待回复'
    }
  }

  return (
    <div className={styles.Mobile_Wrap}>
      {OperatingEnv != 1 &&  // 当前是小程序端
        <div className={styles.Mobile_title_statusbar}></div>
      }

      {OperatingEnv != 1 && OperatingEnv != 2 &&  // 当前是小程序端
        <div className={styles.Mobile_title_Wrap}>
          <NavBar title={'我的病例'}></NavBar>
        </div>
      }

      <Spin spinning={!!loadingResultListByState}>
        <div className={styles.tab_title_Warp}>
          {
            TypesArr.map((item,index)=>{
              const { type,text } = item || {};
              return (
                <div
                  key={index}
                  onClick={ ()=>{ setTabType(type); } }
                  className={classNames({
                    [styles.tab_title_item]: true,
                    [styles.tab_title_item_active]: type == tabType
                  })}>
                    <div>{ text }</div>
                    <div className={classNames({
                      [styles.tab_title_line]: true,
                      [styles.tab_title_line_active]: type == tabType
                    })}></div>
                </div>
              )
            })
          }
        </div>

        {(!Array.isArray(listDate) || listDate.length == 0) &&
          <div className={styles.Mobile_Content_box}>
            <div className={styles.Mobile_Content_box_noDate}>
              <div>
                <div className={styles.Mobile_Content_box_noDate_icon}></div>
                <div className={styles.Mobile_Content_box_noDate_text}>暂无数据</div>
              </div>
            </div>

          </div>
        }

        <div
          className={styles.Warp_Mobile_Content_box}
          ref={(ref) => (scrollParentRef.current = ref)}
        >
          <InfiniteScroll
            loadMore={handleInfiniteOnLoad}
            threshold={50}
            pageStart={1}
            initialLoad={false}
            hasMore={!loadMore && hasMore}
            useWindow={false}
            getScrollParent={() => scrollParentRef.current}
            className={styles.scroll_box}
          >

          {(Array.isArray(listDate) && listDate.length > 0) && listDate.map((item,index)=>{
            const {
              id,           // : "a2c99d2774a54ddf900bfcd23b2be533",//指导订单ID
              orderNumber,  // : "2023100813402475762",//订单号
              expertsId,    // : 29,//指导医生ID
              expertsName,  // : "zhang",//指导医生名称
              type,         // : 1,//指导类型(1图文、2视频)
              processNode,  // : 2,//流程节点(图文流程节点[1选择指导方式、2描述病例问题、3支付指导费用、4病例资料被查看、5问题被回复并对话、6结束指导交易成功];  视频流程节点[1选择指导方式、2描述病例问题、3提交指导单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、  7结束指导、8确认并支付指导费用、9交易成功])
              createDate,   // : "2023-10-08 13:40:25",//创建时间
              amount,       // : 0.10,//账单金额
              status,       // : 支付状态(0已取消、1未支付(待评估)、2去支付(已评估)、3已支付)
              vipUnitPrice, // : 0.10,//会员单价
              fileUrlShow,  // : "http://file.100dyi.com/consultation/2023100813402475762/2023100813402475762.png",//病例资料
              createUserId,
              customerId,
              tenantId,
              orderCaseTemplate, // 1通用模板，2正畸模版
            } = item || {};


            return (
              <div key={index} onClick={()=>goConsultationDetail(item)} className={styles.Mobile_Content_box}>
                <div className={styles.Mobile_Content_box_item}>
                  <div className={styles.Mobile_Content_box_item_title_warp}>
                    {/* type 指导类型(1图文、2视频)  */}
                    {type == 1 &&
                      <div className={styles.Mobile_Content_box_item_title_box}>
                        <div className={styles.GraphicConsultation_Icon}></div>
                        <div className={styles.Mobile_Content_box_item_title}>
                          图文指导
                        </div>
                      </div>
                    }
                    {type == 2 &&
                      <div className={styles.Mobile_Content_box_item_title_box}>
                        <div className={styles.VideoConsultation_Icon}></div>
                        <div className={styles.Mobile_Content_box_item_title}>
                          视频指导
                        </div>
                      </div>
                    }
                    {type == 3 &&
                      <div className={styles.Mobile_Content_box_item_title_box}>
                        <div className={styles.Orthodontics_Icon}></div>
                        <div className={styles.Mobile_Content_box_item_title}>
                          正畸方案审核
                        </div>
                      </div>
                    }
                    <div className={styles.Mobile_Content_box_item_title_status}>
                      {status ? statusText[status] : ''}
                    </div>
                  </div>

                  <div className={styles.item_box_content}>
                    <div className={styles.item_box_content_img}>
                      <img
                        className={styles.item_box_content_img_box}
                        src={fileUrlShow ? fileUrlShow : require('@/assets/GlobalImg/DefaultPicture_Icon.png')}
                      />
                    </div>
                    <div className={styles.item_box_content_right}>
                      <div className={styles.item_box_content_right_box}>
                        <div className={styles.item_box_content_right_box_title}>订单号: </div>
                        <div className={styles.item_box_content_right_box_content}>
                          {/*  orderNumber,  // : "2023100813402475762",//订单号  */}
                          { orderNumber }
                        </div>
                      </div>

                      <div className={styles.item_box_content_right_box}>
                        <div className={styles.item_box_content_right_box_title}>指导专家: </div>
                        <div className={styles.item_box_content_right_box_content}>
                          {/*  expertsName,  // : "zhang",//指导医生名称  */}
                          { expertsName }
                        </div>
                      </div>

                      <div className={styles.item_box_content_right_box}>
                        {/*processNode,  // : 2,//流程节点(
                          图文流程节点[1选择指导方式、2描述病例问题、3支付指导费用、4病例资料被查看、5问题被回复并对话、6结束指导交易成功];
                          视频流程节点[1选择指导方式、2描述病例问题、3提交指导单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、 7结束指导、8确认并支付指导费用、9交易成功])
                        */}
                        <div className={styles.item_box_content_right_box_title}>状态: </div>
                        <div className={styles.item_box_content_right_box_content}>{
                          /*getProcessNodeText(item)*/
                          StatusRuleByConsultation(item) ? StatusRuleByConsultation(item) : '-'
                        }</div>
                      </div>
                    </div>
                  </div>

                  <div className={styles.item_time}>
                    <div className={styles.item_tiem_text}>
                      {/*  createDate,   // : "2023-10-08 13:40:25",//创建时间  */}
                      { dayjs(createDate,'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD HH:mm') }
                    </div>
                    {type != 3 &&
                      <div className={styles.item_total}>
                        <div className={styles.item_text_spen}>合计: </div>
                        {showPaymentPrice(item) ?
                          <div className={styles.item_box_spline}>
                            <span>￥</span>
                            <span>
                             {showPaymentPrice(item)}
                            </span>
                          </div> :
                          <div className={styles.item_box_spline}>
                            <span> - </span>
                          </div>
                        }
                      </div>
                    }
                  </div>

                  <div  className={styles.btn_box_warp}>
                      {/*
                         图文、视频指导
                      */}
                      {type != 3 && !(processNode < 4 && orderCaseTemplate == 2) &&
                        <div
                          onClick={(e)=>{
                            e.stopPropagation();
                            goConsultationDetail(item)
                          }}
                          className={styles.btn_box}>
                          <div>{processNode < 4 ? '编辑' : '指导详情'}</div>
                        </div>
                      }

                      {(type == 2 && status == 2 && createUserId == idByUserInfo)  &&
                        <div
                          style={{ marginLeft: '10px' }}
                          onClick={(e)=>{
                            e.stopPropagation();
                            if(type == 1){
                              goConsultationDetail(item)
                            }else {
                              history.push({
                                pathname: `/PaymentByConsultation/MyConsultationDetails/${id}`,
                              })
                            }
                          }}
                          className={styles.btn_box_payment}>
                          <div>去支付</div>
                        </div>
                      }

                      {(type == 3 || (type != 3 && orderCaseTemplate == 2 && processNode < 4)) &&
                        <div className={styles.copyWarp}>
                          <div className={styles.copyText}>
                            复制成功后，去电脑端打开可编辑
                          </div>
                          <div
                            style={{ marginLeft: '10px' }}
                            className={styles.btn_box}
                            onClick={(e)=> {
                              e.stopPropagation();
                              let paramsByhistory = ''
                              if (type == 3) {
                                // 流程节点正畸审核[1基本信息、2检查及分析、3问题清单及诊断、4治疗方案、5影像资料、6提交病例、7病例被查看、8审核驳回、9审核通过、10指导结束])
                                if(processNode >= 6 ) {
                                  paramsByhistory = `${window.location.origin}/ConsultationModule/ConsultationDetails?${
                                    stringify({
                                      consultationId:id,
                                      consultationType: type,
                                      isGoback: '1',
                                    })
                                  }`
                                }else {
                                  paramsByhistory = `${window.location.origin}/CreationOrthodontics/Step1?${
                                    stringify({
                                      consultationId:id,
                                      customerId:customerId,
                                      tenantId:tenantId,
                                    })
                                  }`
                                }
                              } else {
                                // 图文或视频指导
                                if (processNode < 3) {
                                  paramsByhistory = `${window.location.origin}/CreationOrthodontics/Step1?${
                                    stringify({
                                      orthodonticConsultationId:id,
                                    })
                                  }`
                                } else {
                                  paramsByhistory = `${window.location.origin}/ConsultationModule/StartConsultation/Step4?${
                                    stringify({
                                      consultationId:id,
                                    })
                                  }`
                                }
                              }

                              var textToCopy = paramsByhistory; // 替换成你想要复制的链接或文本
                              var textArea = document.createElement('textarea');
                              textArea.value = textToCopy;

                              // 避免在移动设备上出现键盘弹出
                              textArea.style.position = 'fixed';
                              textArea.style.top = 0;
                              textArea.style.left = 0;
                              textArea.style.opacity = 0;

                              document.body.appendChild(textArea);
                              textArea.focus();
                              textArea.select();

                              try {
                                var successful = document.execCommand('copy');
                                var messagetext = successful ? '复制成功' : '复制失败';
                                message.success(messagetext);
                              } catch (err) {
                                console.error('无法复制', err);
                              }
                              document.body.removeChild(textArea);
                            }}
                          >
                            复制链接
                          </div>
                        </div>
                      }

                  </div>

                </div>
              </div>
            )
          })}
          </InfiniteScroll>
        </div>
      </Spin>
    </div>
  )
}

export default connect(({ ConsultationList,pcAccount, loading }: any) => ({
  ConsultationList,pcAccount, loading
}))(MyConsultationList)
