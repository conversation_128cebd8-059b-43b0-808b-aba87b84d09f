.fixed_content {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #fff;
  border-top: 1px solid #ddd;
  z-index: 990;
  .toolbar {
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    .toolbar_left {
      display: flex;
      align-items: center;
      column-gap: 16px;
    }
    .toolbar_right {
      display: flex;
      align-items: center;
      .gray_bar {
        margin-right: 8px;
        width: 0;
        height: 20px;
        border-left: 1px solid #d9d9d9;
      }
    }
    .upload_icon_wrap {
      position: relative;
      :global {
        .ant-upload {
          position: absolute;
          width: 100%;
          height: 100%;
          left: 0;
          top: 0;
          z-index: 2;
          display: block !important;
        }
      }
    }
    i {
      width: 36px;
      height: 36px;
      display: block;
      &.icon_toolbar_image {
        background: url("../../../../assets/GlobalImg/toolbar_image.png") no-repeat center;
        background-size: 20px 20px;
      }
      //&.icon_toolbar_emoji {
      //  background: url("../../../../assets/GlobalImg/toolbar_emoji.png") no-repeat center;
      //  background-size: 20px 20px;
      //}
      &.icon_toolbar_topic {
        background: url("../../../../assets/GlobalImg/toolbar_topic.png") no-repeat center;
        background-size: 20px 20px;
      }
      //&.icon_toolbar_font {
      //  background: url("../../../../assets/GlobalImg/toolbar_font.png") no-repeat center;
      //  background-size: 20px 20px;
      //}
      &.icon_toolbar_undo {
        background: url("../../../../assets/GlobalImg/toolbar_undo.png") no-repeat center;
        background-size: 20px 20px;
      }
      &.icon_toolbar_redo {
        background: url("../../../../assets/GlobalImg/toolbar_redo.png") no-repeat center;
        background-size: 20px 20px;
      }
    }
    i.active {
      //&.icon_toolbar_emoji {
      //  background: url("../../../../assets/GlobalImg/toolbar_emoji_active.png") no-repeat center;
      //  background-size: 20px 20px;
      //}
      //&.icon_toolbar_font {
      //  background: url("../../../../assets/GlobalImg/toolbar_font_active.png") no-repeat center;
      //  background-size: 20px 20px;
      //}
    }
    i.disabled {
      &.icon_toolbar_undo {
        background: url("../../../../assets/GlobalImg/toolbar_undo_disabled.png") no-repeat center;
        background-size: 20px 20px;
      }
      &.icon_toolbar_redo {
        background: url("../../../../assets/GlobalImg/toolbar_redo_disabled.png") no-repeat center;
        background-size: 20px 20px;
      }
    }
  }

  .editor_toolbar_container {
    background: #fff;
    padding: 12px 12px 0;
    height: 300px;
    overflow-y: auto;
    .toolbar_title {
      font-size: 11px;
      color: #666;
      line-height: 15px;
      margin-bottom: 8px;
    }
    .font_toolbar.font_toolbar1 {
      background: #F5F6F8;
      margin-bottom: 12px;
    }
    .font_toolbar {
      display: flex;
      flex-wrap: nowrap;
      column-gap: 12px;
      margin-bottom: 16px;
      border-radius: 4px;
      & > div {
        flex: 1;
      }
      .toolbar_item {
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        background: #F5F6F8;
        &.checked {
          background: #E8E8E8;
        }
        & > i {
          width: 24px;
          height: 24px;
          &.icon_bold {
            background: url("../../../../assets/Editor/bold.png") no-repeat center;
            background-size: 100% 100%;
          }
          &.icon_italic {
            background: url("../../../../assets/Editor/italic.png") no-repeat center;
            background-size: 100% 100%;
          }
          &.icon_underline {
            background: url("../../../../assets/Editor/underline.png") no-repeat center;
            background-size: 100% 100%;
          }
          &.icon_strikethrough {
            background: url("../../../../assets/Editor/strikethrough.png") no-repeat center;
            background-size: 100% 100%;
          }

          // 对齐方式
          &.icon_justify {
            background: url("../../../../assets/Editor/align_justify.png") no-repeat center;
            background-size: 100% 100%;
          }
          &.icon_left {
            background: url("../../../../assets/Editor/align_left.png") no-repeat center;
            background-size: 100% 100%;
          }
          &.icon_center {
            background: url("../../../../assets/Editor/align_center.png") no-repeat center;
            background-size: 100% 100%;
          }
          &.icon_right {
            background: url("../../../../assets/Editor/align_right.png") no-repeat center;
            background-size: 100% 100%;
          }
        }
        .small {
          font-size: 14px;
          color: #000;
        }
        .large {
          font-size: 16px;
          color: #000;
        }
      }
    }

    // 文字颜色
    .color_toolbar {
      background: #F5F6F8;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      column-gap: 12px;
      margin-bottom: 16px;
      .toolbar_item {
        flex: 1;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        &.checked {
          background: #E8E8E8;
        }
        & > span {
          width: 16px;
          height: 16px;
          border-radius: 50%;
          &.color_1 {
            background: #000;
          }
          &.color_2 {
            background: #666;
          }
          &.color_3 {
            background: #E03333;
          }
          &.color_4 {
            background: #EE732E;
          }
          &.color_5 {
            background: #33BD62;
          }
          &.color_6 {
            background: #4481DD;
          }
          &.color_7 {
            background: #864AE9;
          }
        }
      }
    }
  }

}
