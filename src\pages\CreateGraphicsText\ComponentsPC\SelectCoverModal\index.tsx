import React, {useEffect, useState} from 'react'
import {connect, history} from 'umi'
import {PlusOutlined} from "@ant-design/icons";
import {display} from "html2canvas/dist/types/css/property-descriptors/display";
import { getOperatingEnv } from '@/utils/utils';
import {stringify} from "qs";
import {Button, message, Modal, Spin, Upload} from 'antd'
import successImage from '@/assets/GlobalImg/success.png'
import classNames from "classnames";
import cover1 from "@/assets/PlanetChatRoom/CreateSpace_cover_1.png";
import cover2 from "@/assets/PlanetChatRoom/CreateSpace_cover_2.png";
import cover3 from "@/assets/PlanetChatRoom/CreateSpace_cover_3.png";
import styles from './index.less'

const Index: React.FC = (props: any) => {
  const { visible, spaceId, starSpaceType, onSelect,spaceCoverState:spaceCoverStateByProps,spaceName } = props
  const [ checkedId, setCheckedId ] = useState(1); // 勾选中的id
  const [ spaceCoverList, setSpaceCoverList ] = useState([]); // 勾选中的id
  const [ loadingByUpload, setLoadingByUpload] = useState(false); // loading
  // 自定义上传数据
  const [spaceCoverState, setSpaceCoverState] = useState({
    spaceCoverUrl: null,
    spaceCoverUrlView: null,
  })
  const { dispatch, userInfoStore, loading } = props || {};
  const UerInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  let starSpaceTypeText= starSpaceType == 2 ? '会议' : '直播'

  useEffect(() => {
    if (visible) {
      getSpaceCover();
    }else {
      setSpaceCoverState({
        spaceCoverUrl: null,
        spaceCoverUrlView: null,
      })
      setLoadingByUpload(false);
      setCheckedId(1);
      setSpaceCoverList([]);
    }
  },[visible])

  // 获取封面
  const getSpaceCover = async () => {
    let resByGetSpaceCover = await dispatch({
      type: 'userInfoStore/getSpaceCover',
      payload: {
        spaceId: spaceId,                            // ID
        starSpaceType: starSpaceType,                // 类型
      }
    })

    const { code, content } = resByGetSpaceCover || {};
    if (code == 200 && content) {
      if (Array.isArray(content)) {
        let isSelectByItem = content.find((item)=>{ return item.isSelect == 1 })
        let selectByfileUrl = spaceCoverStateByProps && spaceCoverStateByProps.spaceCoverUrl;
        setCheckedId(selectByfileUrl ? selectByfileUrl : null);
        setSpaceCoverList(content)
      }else {
        setSpaceCoverList([])
      }
    }
  }

  // 上传广告
  const onChangeByUpload = (info: any)=>{
    let fileList;
    fileList = info && info.fileList;
    if (info.file.status === 'uploading') {
      setLoadingByUpload(true);
      return;
    }
    if (info && !info.file.status) {
      setLoadingByUpload(false);
      fileList = null;
      return;
    }
    if (info && info.file.status === 'error') {
      setLoadingByUpload(false);
      message.error('上传失败');
      fileList = null;
      return
    }
    if (info && info.file.status === 'done') {
      setLoadingByUpload(false);
      const res = info.file.response || {}
      const { code, content, msg } = res
      if (code == 200 && content) {
        console.log('content123123 :: ',content);
        setSpaceCoverState({
          ...spaceCoverState,
          spaceCoverUrl: content.fileUrl,
          spaceCoverUrlView: content.fileUrlView,
        })
      } else {
        fileList = [];
        message.error(msg || '上传失败')
      }
    }
  }

  // 上传图片headers
  const getHeaders=() =>{
    const env = getOperatingEnv()
    return {// token
      access_token:localStorage.getItem('access_token') || '',
      username: env == 5 ? localStorage.getItem('user_name') : localStorage.getItem('vxOpenIdCipherText') ? localStorage.getItem('vxOpenIdCipherText')  : UerInfo?.phone,
      client: env == 5 ? localStorage.getItem('client') : 'WX',
      type: env === '1' ? '' : '1', // h5 传1
    }
  }

  // 上传校验
  const beforeUpload = (file: { size?: any; type?: any; }) => {
    const isSize = file.size / 1024 / 1024 < 15;
    if (!isSize) {
      message.error('超过15M限制，不允许上传~');
      return false;
    }

    const { name:fileName } = file || {}
    const suffix = fileName.substring(fileName.lastIndexOf('.')+1)
    const isJpgOrPng = file.type === 'image/jpg' || file.type === 'image/jpeg' ||  file.type === 'image/png' ||  file.type === 'image/gif';
    const isSuffixByJpgOrPng = (
      suffix === 'jpg'
      || suffix === 'JPG'
      || suffix === 'jpeg'
      || suffix === 'JPEG'
      || suffix === 'png'
      || suffix === 'PNG'
      || suffix === 'gif'
      || suffix === 'GIF'
    )
    if (!isJpgOrPng || !isSuffixByJpgOrPng) {
      message.error('只能上传JPG、JPEG、PNG、GIF 格式的图片~');
      return false;
    }
    return isJpgOrPng;
  };



  const checkedFn = (item: any) => {
    setCheckedId(item)
  }

  return (
    <Modal
      visible={visible}
      className={styles.modal}
      title={`选择${starSpaceTypeText}封面`}
      onCancel={props.onCancel}
      destroyOnClose
      footer={null}
      width={590}
    >
      <div className={styles.content}>
        <div className={styles.content_title}>
          请选择一个{starSpaceTypeText}封面
        </div>
        <Spin spinning={!!props.loading.effects['userInfoStore/getSpaceCover']}>
          <div className={styles.content_flex}>
            {Array.isArray(spaceCoverList) && spaceCoverList.map((item, index) => {
              const {
                isSelect,
                showSpaceCoverUrl,
                spaceCoverUrl
              } = item || {};
                return (
                  <div
                    onClick={()=>{checkedFn(spaceCoverUrl)}}
                    className={classNames({
                      [styles.warp_list_item]:true,
                      [styles.warp_list_item_select]:spaceCoverUrl == checkedId,
                    })}
                  >
                    {!!spaceName && <div className={styles.CoverTitle}>{spaceName}</div>}
                    <img src={showSpaceCoverUrl} alt=""/>
                    {spaceCoverUrl == checkedId &&  <i className={styles.warp_list_item_select_icon}/>}
                  </div>
                )
            })}

            <Upload
              headers={getHeaders()}
              accept="image/*"
              action={`/api/server/base/uploadFile?${stringify({ fileType: 14, userId: UerInfo?.friUserId})}`}
              // listType="picture-card"
              // className={styles.edit_head_picture}
              onChange={(info)=>onChangeByUpload(info)}
              onRemove={() => { }}
              beforeUpload={beforeUpload}
              showUploadList={false}
            >
              <Spin spinning={!!loadingByUpload}>
                <div
                  onClick={()=>{checkedFn('locality')}}
                  className={classNames({
                    [styles.warp_list_item]:true,
                    [styles.warp_list_item_select]:'locality' == checkedId,
                  })}>
                  {spaceCoverState.spaceCoverUrlView ?
                    <div className={styles.warp_list_item_box_img_warp}>
                      <img src={spaceCoverState.spaceCoverUrlView} alt=""/>
                    </div>
                    :
                    <div className={styles.warp_list_item_box}>
                      <div className={styles.addiconBox}>
                        <div className={styles.addicon}>
                          <PlusOutlined/>
                        </div>
                        <div>本地上传</div>
                      </div>
                    </div>
                  }
                </div>
              </Spin>
            </Upload>

          </div>
        </Spin>
      </div>
      <div className={styles.footer}>
        <Button
          onClick={()=>{props.onCancel()}}>
          取消
        </Button>
        <Button
          type="primary"
          onClick={()=>{
            if (checkedId != 'locality') {
              let showSpaceCoverObj = Array.isArray(spaceCoverList) && spaceCoverList.find((item, index) => {
                return item.spaceCoverUrl == checkedId
              })
              onSelect(showSpaceCoverObj);
            }else {
              onSelect({
                isSelect: 0,
                showSpaceCoverUrl: spaceCoverState.spaceCoverUrlView,
                spaceCoverUrl: spaceCoverState.spaceCoverUrl,
              });
            }
          }}>
          确定
        </Button>
      </div>
    </Modal>
  )
}

export default connect(({ userInfoStore, loading }: any) => ({ userInfoStore, loading }))(Index)

