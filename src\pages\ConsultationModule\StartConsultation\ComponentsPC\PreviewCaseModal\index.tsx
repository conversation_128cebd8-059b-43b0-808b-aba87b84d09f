/**
 * @Description: 病例模板预览弹窗
 */
import React, { useRef, useState, useEffect } from 'react'
import classNames from 'classnames'
import { Modal, Carousel } from 'antd'
import styles from './index.less'

import arrow_left from '@/assets/Consultation/arrow_left.png' // 左箭头
import arrow_right from '@/assets/Consultation/arrow_right.png' // 右箭头

// 模版图片
const dataSourceList = [
  { id: 1, imgSrc: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/consultation_preview_1.png', templateType: 1 },
  { id: 2, imgSrc: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/consultation_preview_2.png', templateType: 1 },
  { id: 3, imgSrc: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/consultation_preview_3.png', templateType: 2 },
  { id: 4, imgSrc: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/consultation_preview_4.png', templateType: 2 },
  { id: 5, imgSrc: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/consultation_preview_5.png', templateType: 2 },
  { id: 6, imgSrc: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/consultation_preview_6.png', templateType: 2 },
]

interface PropsType {
  visible: boolean,                              // true，false
  templateType: number,                               // 1 通用模板，2 正畸模板
  onCancel: any,                                 // 关闭弹窗
}

const Index: React.FC<PropsType> = (props: any) => {
  const { visible, templateType } = props
  const ref = useRef(null)

  const [sliderIndex, setSliderIndex] = useState(0) // 轮播图图片index

  useEffect(() => {
    if (!visible) {
      setSliderIndex(0)
    }
  }, [visible])

  // 下一张
  const sliderGoToNext = (e) => {
    e.preventDefault()
    e.stopPropagation()
    if (ref && ref.current) {
      ref.current.next()
    }
  }

  // 上一张
  const sliderGoToPrve = (e) => {
    e.preventDefault()
    e.stopPropagation()
    if (ref && ref.current) {
      ref.current.prev()
    }
  }

  // 切换后触发时间
  const afterChange = (index) => {
    setSliderIndex(index)
  }

  return (
    <Modal
      title={templateType == 1 ? '通用模板预览' : '正畸专科模板'}
      className={styles.modal}
      visible={visible}
      onCancel={props.onCancel}
      width={1100}
      footer={null}
      destroyOnClose
    >
      <div className={styles.preview_img_wrap}>
        {
          sliderIndex > 0 &&
          <div className={classNames(styles.arrow, styles.arrow_left)} onClick={sliderGoToPrve}>
            <img src={arrow_left} width={32} height={32} alt=""/>
          </div>
        }
        {
          sliderIndex < dataSourceList.filter(item => item.templateType == templateType).length - 1 &&
          <div className={classNames(styles.arrow, styles.arrow_right)} onClick={sliderGoToNext}>
            <img src={arrow_right} width={32} height={32} alt=""/>
          </div>
        }
        <Carousel
          ref={ref}
          draggable={true}
          slidesToShow={1}
          speed={300}
          infinite={false}
          swipeToSlide={true}   // 拖到哪，停在哪
          afterChange={afterChange}
        >
          {
            dataSourceList.map((item, index) => {
              if (item.templateType != templateType) {
                return null
              }
              return (
                <div key={index}>
                  <img src={item.imgSrc} width={788} height={528} alt=""/>
                </div>
              )
            })
          }
        </Carousel>
      </div>
    </Modal>
  )
}

export default Index
