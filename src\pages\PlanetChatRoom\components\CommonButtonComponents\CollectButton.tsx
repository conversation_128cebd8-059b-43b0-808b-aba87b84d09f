// CollectButton.js
import React from 'react';
import classNames from 'classnames';
import styles from './index.less';  // 引入自定义样式

const CollectButton = ({ isCollect, onClickByCollect }) => {
  return (
    <div className={styles.HorizontalLiveRoom_Btn_Warp} onClick={onClickByCollect}>
      <i
        className={classNames({
          [styles.title_Icon_shoucang_Icon]: true,
          [styles.title_Icon_shoucang_Icon_active]: isCollect === 1,
        })}
      ></i>
      <div
        className={classNames({
          [styles.text]:true,
          [styles.title_Icon_shoucang_Icon_text]: true,
        })}
      >
        {isCollect === 1 ? '已收藏' : '收藏'}
      </div>
    </div>
  );
};

export default CollectButton;
