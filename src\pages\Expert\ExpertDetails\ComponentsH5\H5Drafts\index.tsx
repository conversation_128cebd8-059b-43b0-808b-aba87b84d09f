/**
 * @Description: H5-医生主页/个人中心-草稿箱
 */
import React, { useEffect, useState } from 'react';
import { connect } from "umi";
import { InfiniteScroll, Mask, Toast, SwipeAction } from 'antd-mobile';
import styles from './index.less';

// 图标、icon
import botArrowIcon from '@/assets/GlobalImg/bot_arrow.png';

import ArticleCard from '@/components/ArticleCard';              // 文章卡片
import ExternalLinkCard from '@/components/ExternalLinkCard';    // 外链卡片
import PostCard from '@/components/PostCard';                    // 帖子卡片
import CommonConfirmModal from '@/components/CommonConfirmModal';// 删除提示弹窗
import NoDataRender from '@/components/NoDataRender'             // 暂无数据

// 筛选数据
const screenList = [
  {id: 'all', text: '全部'},
  {id: 1, text: '文章'},
  {id: 2, text: '帖子'},
  {id: 3, text: '外链'},
]

const Index = (props:any) => {
  const { dispatch } = props;

  // 页面state
  const initialState = {
    dataList: [],                  // 数据list
    total: 0,                      // 总条数
    hasMore: false,                // 是否还有更多数据
  }
  // 筛选数据
  const initialFilterState = {
    page: 1,                       // 页码
    size: 30,                      // 每页条数
    imageType: 'all',              // 图文类型：1.文章 2.帖子 3.外链 4.空间
  }
  // 弹窗数据
  const initialModalState = {
    filterVisible: false,          // 筛选弹窗
    imageTypeByTemp: 'all',        // 临时的，图文类型：1.文章 2.帖子 3.外链 4.空间
    commonConfirmVisible: false,   // 删除二次确认弹窗
    deleteImageTextId: null,       // 删除的图文的ID
  }

  const [state, setState] = useState(initialState)
  const [filterState, setFilterState] = useState(initialFilterState)
  const [modalState, setModalState] = useState(initialModalState)
  const [loadingPersonImageTextList, setLoadingPersonImageTextList] = useState(false)  // loading

  useEffect(() => {
    personImageTextListByFunc();
  },[filterState.page, filterState.imageType]);

  // 获取个人中心获取用户图文列表信息 [文章,帖子,外链,草稿]，page，size，删除完刷新数据时的传参
  const personImageTextListByFunc = async (page, size) => {
    setLoadingPersonImageTextList(true)
    const pageResult = page || filterState.page
    const sizeResult = size || filterState.size

    await dispatch({
      type: 'expertAdvice/personImageTextList',
      payload: {
        page: pageResult,
        size: sizeResult,
        imageType: filterState.imageType == 'all' ? null : filterState.imageType,  // 图文类型：1.文章 2.帖子 3.外链 4.空间
        status: [3],         // 状态：1.审核通过 0.未审核 2.审核未通过 3.草稿
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200) {
        const resultList = content.resultList || []
        setState({
          ...state,
          dataList: pageResult == 1 ? resultList : state.dataList.concat(resultList),
          total: content.total || 0,
          hasMore: true,
        })
      } else {
        Toast.show(msg || '数据加载失败')
      }
      setLoadingPersonImageTextList(false)
    }).catch(err => {
      setLoadingPersonImageTextList(false)
    })
  }

  // 筛选事件
  const filterModalShow = () => {
    setModalState({
      ...modalState,
      filterVisible: true,
    })
    // 解决出现弹框后,可以滚动页面问题
    document.getElementById('wrap').style.position = 'fixed'
    document.getElementById('wrap').style.overflow = 'hidden';
  }

  // 筛选弹框-取消事件
  const filterModalHide = () => {
    setModalState({
      ...modalState,
      filterVisible: false,
      imageTypeByTemp: filterState.imageType,
    })
    // 关闭弹框,重置滚动
    document.getElementById('wrap').style.position = 'unset'
    document.getElementById('wrap').style.overflow = 'auto';
  }

  // 筛选-确定事件
  const filterModalOnOk = () => {
    setFilterState({
      ...filterState,
      imageType: modalState.imageTypeByTemp,
      page: 1,
    })
    setModalState({
      ...modalState,
      filterVisible: false,
    })
    // 关闭弹框,重置滚动
    document.getElementById('wrap').style.position = 'unset'
    document.getElementById('wrap').style.overflow = 'auto';
  }

  // 点击删除，打开二次确认弹窗
  const commonConfirmModalShow = (imageTextId) => {
    console.log('点击删除', imageTextId)

    setModalState({
      ...modalState,
      commonConfirmVisible: true,
      deleteImageTextId: imageTextId,
    })
  }

  // 二次确认弹窗，关闭
  const commonConfirmModalHide = () => {
    setModalState({
      ...modalState,
      commonConfirmVisible: false,
      deleteImageTextId: null,
    })
  }

  // 确认删除
  const handleConfirmOk = () => {
    deleteImgTextInfo(modalState.deleteImageTextId)
    commonConfirmModalHide()
  }

  // 删除
  const deleteImgTextInfo = (imageTextId) => {
    const toastControl = Toast.show({
      icon: 'loading',
      content: '',
      duration: 0,
    })
    dispatch({
      type: 'graphicsText/deleteImgTextInfo',
      payload: {
        imageTextId,         // 删除的图文ID
      }
    }).then(res => {
      toastControl.close()
      const { code, content, msg } = res
      if (code == 200 && content) {
        Toast.show('删除成功')
        personImageTextListByFunc(1, state.dataList.length)
      } else {
        Toast.show(msg || '数据加载失败')
      }
    }).catch();
  }

  // 滚动加载分页
  const loadMore = async () => {
    console.log('loadMore')
    // 没有更多数据了
    if (state.dataList.length >= state.total) {
      await setState({
        ...state,
        hasMore: false,
      })
      return
    }

    // loading
    await setLoadingPersonImageTextList(true)
    await setFilterState({
      ...filterState,
      page: filterState.page + 1,  // 页码
    })
  }

  // SwipeAction组件参数
  const getRightActions = (item) => {
    return [
      {
        key: 'delete',
        text: '删除',
        color: 'danger',
        onClick: () => commonConfirmModalShow(item.id)
      },
    ]
  }

  return (
    <>
      <div className={styles.content}>
        {/* 总条数和筛选按钮 */}
        <div className={styles.top_wrap}>
          <div className={styles.total_num}>共 { state.total} 条内容</div>
          <div className={styles.screen_btn} onClick={filterModalShow}>筛选<img src={botArrowIcon} alt="" width={16} height={16} /></div>
          {/* 筛选弹窗 */}
          <Mask visible={modalState.filterVisible} onMaskClick={filterModalHide} className={styles.mask_box}>
            <div className={styles.screen_wrap}>
              <div className={styles.screen_container}>
                {
                  screenList.map((item: any, index) => {
                    return <div
                      className={item.id == modalState.imageTypeByTemp ? styles.screen_check_child_word : styles.screen_child_word}
                      key={index}
                      onClick={() => { setModalState({
                        ...modalState,
                        imageTypeByTemp: item.id,
                      }) }}
                    >{item.text}</div>
                  })
                }
              </div>
              <div className={styles.screen_wrap_footer}>
                <div className={styles.screen_wrap_footer_close} onClick={filterModalHide}>取消</div>
                <div className={styles.screen_wrap_footer_confirm} onClick={filterModalOnOk}>确认</div>
              </div>
            </div>
          </Mask>
        </div>

        {/* 数据list */}
        {
          state.dataList && state.dataList.length > 0 ?
          <div className={styles.bot_wrap}>
            {
              state.dataList.map((item:any, index:number) => {
                if(item.imageType == 1){ // 文章
                  return (
                    <SwipeAction key={index} rightActions={getRightActions(item)}>
                      <ArticleCard
                        isMyPages={true} // 是我的主页进行跳转的-点击卡片跳转到编辑页面
                        item={item}
                      />
                    </SwipeAction>
                  )
                }else if(item.imageType == 2){ // 帖子组件
                  return (
                    <SwipeAction key={index} rightActions={getRightActions(item)}>
                      <PostCard
                        isMyPages={true}
                        pageType='3'
                        item={item}
                        isShowMoreOperate={false} // 是否展示点点点更多操作
                      />
                    </SwipeAction>
                  )
                }else if(item.imageType == 3){ // 外链
                  return (
                    <SwipeAction key={index} rightActions={getRightActions(item)}>
                      <ExternalLinkCard
                        isMyPages={true} // 是我的主页进行跳转的-点击卡片跳转到编辑页面
                        item={item}
                        isShowMoreOperate={false} // 是否展示点点点更多操作
                      />
                    </SwipeAction>
                  )
                }else if(item.imageType == 4){ // 空间
                  return null
                }
              })
            }
            {/* 滚动加载组件 */}
            <InfiniteScroll
              loadMore={loadMore}
              hasMore={!loadingPersonImageTextList && state.hasMore}
              threshold={30}
            />
          </div>
          :
          <NoDataRender style={{marginTop: 50, paddingBottom: 70}}/>
        }
      </div>

      {/* 确定删除提示弹窗 */}
      <CommonConfirmModal
        isVisible={modalState.commonConfirmVisible}
        title={'确定删除'}
        text={'内容删除后将无法恢复，请慎重考虑'}
        onSubmit={handleConfirmOk}
        onCancel={commonConfirmModalHide}
      />
    </>
  )
}
export default connect(({ activity,userInfoStore, loading }: any) => ({activity,userInfoStore, loading}))(Index)

