.wrap {
  width: 100%;
  background: #fff;
  padding-left: 16px;
  padding-top: 4px;
  border-radius: 2px 2px 0 0;
  cursor: pointer;
  .header {
    display: flex;
    justify-content: space-between;
    line-height: 22px;
    padding: 12px 16px 12px 0;
    border-bottom: 1px solid #E1E4E7;

    .header_title {
      font-size: 16px;
      font-weight: 500;
      color: #000;
    }

    .header_btn {
      font-size: 14px;
      color: #0095FF;
      padding: 0 3px;
      cursor: pointer;
    }
  }

  .content {
    padding: 12px 16px 8px 0;

    .case_title {
      font-size: 16px;
      line-height: 22px;
      font-weight: 500;
      color: #000;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-bottom: 8px;
    }

    .case_course {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      line-height: 16px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .case_img {
      margin-right: -16px;
      max-width: 400px;
      height: 62px;
      margin: 8px 0;
      overflow: hidden;
      .img {
        float: left;
        width: 62px;
        height: 62px;
        margin-right: 8px;
        border-radius: 6px;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        .img_length{
          display: none;
        }
        &:nth-child(5) {
          margin-right: 0;
          position: relative;
          .img_length{
            display: block;
            width: 62px;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-25%,-50%);
            font-size: 14px;
            font-weight: 400;
            color: #FFFFFF;
          }
        }
      }
    }


    .case_content {
      margin-bottom: 8px;
      background: #F5F5F5;
      border-radius: 6px;
      padding: 8px;
      font-size: 14px;
      color: #666;
      line-height: 22px;
      word-break: break-all;
      &>p{
        margin-bottom: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        line-height: 22px;
      }
    }
  }
}
