{"name": "DigitalHealth-Business-Project", "version": "1.0.0", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 umi build", "build": "umi build", "deploy": "npm run build && npm run gh-pages", "dev": "npm run start:dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "umi g tmp", "lint": "umi g tmp && npm run lint:js && npm run lint:style && npm run lint:prettier && npm run tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"src/**/*\" --end-of-line auto", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "openapi": "umi open<PERSON>i", "playwright": "playwright install && playwright test", "prettier": "prettier -c --write \"src/**/*\"", "serve": "umi-serve", "start": "cross-env UMI_ENV=dev umi dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_ENV=dev umi dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev PORT=8000 umi dev", "start:virtual": "cross-env REACT_APP_ENV=pre MOCK=none UMI_ENV=dev umi dev", "start:pro": "cross-env REACT_APP_ENV=pro MOCK=none UMI_ENV=dev umi dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev umi dev", "start:no-ui": "cross-env UMI_UI=none UMI_ENV=dev umi dev", "test": "umi test", "test:component": "umi test ./src/components", "test:e2e": "node ./tests/run-tests.js", "tsc": "tsc --noEmit", "lhci": "npx @lhci/cli autorun --config=lighthouserc.js"}, "lint-staged": {"**/*.less": "stylelint --syntax less", "**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/icons": "^4.7.0", "@ant-design/pro-components": "1.1.1", "@ant-design/pro-layout": "^7.14.7", "@material-ui/core": "4.11.4", "@material-ui/icons": "4.11.2", "@material-ui/lab": "4.0.0-alpha.58", "@sentry/react": "^7.57.0", "@sentry/tracing": "^7.57.0", "@umijs/route-utils": "^2.0.0", "a18n": "^1.12.4", "alloyfinger": "^0.1.16", "antd": "^4.20.0", "antd-mobile": "^5.30.0", "classnames": "^2.3.0", "crypto-js": "^4.1.1", "danmu.js": "^1.1.9-1", "dayjs": "^1.11.9", "fullscreen.js": "^0.3.0", "gsap": "^3.12.5", "heic2any": "^0.0.4", "html2canvas": "^1.4.1", "is-mobile": "^3.0.0", "jquery": "^3.4.1", "js-audio-recorder": "^1.0.7", "lodash": "^4.17.0", "moment": "^2.29.0", "next": "10.2.0", "omit.js": "^2.0.2", "qrcode.react": "^3.1.0", "qs": "^6.11.2", "quill-emoji": "^0.2.0", "quill-image-drop-module": "^1.0.3", "rc-menu": "^9.1.0", "rc-util": "^5.16.0", "react": "^17.0.2", "react-cropper": "^2.3.3", "react-dev-inspector": "^1.7.0", "react-dom": "^17.0.2", "react-draggable": "^4.4.5", "react-helmet": "^6.1.0", "react-helmet-async": "^1.2.0", "react-html-parser": "^2.0.2", "react-i18next": "^11.8.15", "react-infinite-scroller": "^1.2.6", "react-intersection-observer": "^9.5.3", "react-quill": "^2.0.0", "rtc-ai-denoiser": "^1.1.3", "rtc-audio-mixer": "0.0.1", "rtc-beauty-plugin": "^0.0.7", "rtc-device-detector-react": "^1.0.6", "sass": "^1.32.12", "screenfull": "^6.0.2", "tcplayer.js": "^5.0.1", "ua-parser-js": "^1.0.2", "umi": "^3.5.0", "umi-plugin-keep-alive": "^0.0.1-beta.35", "umi-request": "^1.4.0", "vod-js-sdk-v6": "^1.6.2"}, "devDependencies": {"@ant-design/pro-cli": "^2.1.0", "@lhci/cli": "^0.10.0", "@playwright/test": "^1.17.0", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^12.1.5", "@testing-library/react-hooks": "^8.0.1", "@types/classnames": "^2.3.1", "@types/express": "^4.17.0", "@types/history": "^4.7.0", "@types/jest": "^26.0.0", "@types/lodash": "^4.14.0", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@types/react-helmet": "^6.1.0", "@umijs/fabric": "^2.11.1", "@umijs/plugin-blocks": "^2.2.0", "@umijs/plugin-esbuild": "^1.4.0", "@umijs/preset-ant-design-pro": "^1.3.0", "@umijs/preset-dumi": "^1.1.0", "@umijs/preset-react": "^2.1.0", "cross-env": "^7.0.0", "cross-port-killer": "^1.3.0", "detect-installer": "^1.0.0", "eslint": "^7.32.0", "gh-pages": "^3.2.0", "jsdom-global": "^3.0.0", "lint-staged": "^10.0.0", "mockjs": "^1.1.0", "postcss-pxtorem": "^5.1.1", "prettier": "^2.5.0", "puppeteer": "^20.8.3", "stylelint": "^13.0.0", "swagger-ui-dist": "^4.12.0", "typescript": "^4.5.0", "umi-serve": "^1.9.10"}, "engines": {"node": ">=12.0.0"}}