/**
 * @Description: PC端，发起指导页第3步
 */
import React, { useEffect, useState } from 'react'
import { history, connect } from 'umi'
import classNames from 'classnames'
import { cloneDeep } from 'lodash'
import { stringify } from 'qs'
import { getOperatingEnv, getArrailUrl } from '@/utils/utils'
import { Upload, Checkbox, Input, Button, message, Spin, Modal } from 'antd'
import { PlusOutlined, CloseCircleFilled } from '@ant-design/icons'
import styles from './Step3.less'

import PcHeader from '@/componentsByPc/PcHeader' // 顶部导航栏
import StartConsultationSteps from '@/pages/ConsultationModule/StartConsultation/ComponentsPC/StartConsultationSteps' // 完成服务流程按钮及弹窗
import TipsModal from '../ComponentsPC/TipsModal' // 返回上一页提示弹窗，PC和H5账号不一致提示弹窗

// 病例模板选项list
const caseTemplateList = [
  {code: 1, name: '基本信息'},
  {code: 2, name: '检查及诊断'},
  {code: 3, name: '治疗方案'},
]

let timer = null                       // 暂存定时器
let pageDidMount = false               // 页面是否已挂载，为了初次进入时不调暂存口

const Index: React.FC = (props) => {
  const isInIframe = (self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya') // 是否嵌套在5i5ya的iframe中
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}') // 登录用户信息

  const { dispatch, loading } = props
  const { query, pathname } = history.location
  const {
    consultationId, // 指导ID
    consultationType, // 指导类型，1 图文，2 视频
    pageFrom = '', // ConsultationDetails 表示来自指导详情页
    expertsUserId, // 专家ID
    copyUserId, // 1 复制地址的用户ID
    orderCaseTemplate, // 1通用模板 ，2正畸模板
  } = query

  // 指导订单信息state
  const initialFormState = {
    templateType: 1,                                       // 模板类型(1星球、2诊所)
    checkedCaseTemplateList: [],                           // 选中的病例模板选项[1,2,3]
    isTemplate: 0,                                         // 是否是模板(1是，0否)
    noTemplateDescription: '',                             // 无模板的描述
    age: '',                                               // 年龄
    sex: '',                                               // 性别
    chiefComplaint: '',                                    // 主诉
    presentDisease: '',                                    // 现病史
    previousHistory: '',                                   // 既往史
    wholeHealth: '',                                       // 全身健康情况
    checkUp: '',                                           // 检查
    diagnosis: '',                                         // 诊断
    treatmentPlan: '',                                     // 治疗方案
    firstQuestion: '',                                     // 初始提问
    consultationCaseMediaDtoList: [
      // {
      //   type: 0,   // 资料类型(0星球影像、1其他资料、2全景片、3侧位片、4正面像、5侧面像、6正面咬合像、7正面咬合45度像、8左侧咬合像、9右侧咬合像、10上牙弓像、11下牙弓像)
      //   fileUrl: '',  // 文件路径
      //   fileName: '',  // 文件名称
      //   fileSuffix: '',   // 文件后缀
      //   fileSize: '',   // 文件大小
      // }
    ],      // 病例媒体信息集合
    processNode: 2,      // 流程节点(图文流程节点[1选择会诊方式、2描述病例问题、3支付会诊金、4病例资料被查看、5问题被回复并对话、6结束会诊交易成功];  视频流程节点[1选择会诊方式、2描述病例问题、3提交会诊单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、7结束会诊、8确认并支付会诊金、9交易成功])
  }
  const [formState, setFormState] = useState(initialFormState)
  const [isClickSubmit, setIsClickSubmit] = useState(false)            // 是否点击了提交，用来显示表单校验红字的
  const [uploadLoading, setUploadLoading] = useState(false)            // 上传影像资料loading
  const [loadingSubmit, setLoadingSubmit] = useState(false)            // 点击下一步提交loading
  const [goBackTipsModalVisible, setGoBackTipsModalVisible] = useState(false) // 返回提示弹窗
  const [accountErrorTipsModalVisible, setAccountErrorTipsModalVisible] = useState(false) // H5与PC登录账号不一致提示弹窗

  useEffect(() => {
    // 未登录跳转登录页
    if (!localStorage.getItem('access_token')) {
      onClickLogin()
      return
    }
    if (copyUserId && copyUserId != UserInfo.friUserId) {
      // 您当前在PC端登录的账号与移动端不一致，请重新登录
      setAccountErrorTipsModalVisible(true)
      return
    }
    if (consultationId) {
      getConsultationAndCaseInfo()
    } else {
      message.error('指导ID缺失')
    }

    // 实时保存逻辑
    pageDidMount = true
    return () => {
      pageDidMount = false
      clearTimeout(timer)
    }
  }, [])

  // 实时保存逻辑
  useEffect(() => {
    // 初次进入页面时不执行
    if (!pageDidMount) {
      return
    }
    console.log('暂存暂存暂存暂存**********')
    // 暂存
    clearTimeout(timer)
    timer = setTimeout(() => {
      editConsultationInfo()
    }, 2000)

  }, [formState])

  // 查询指导和病例详情
  const getConsultationAndCaseInfo = () => {
    dispatch({
      type: 'consultation/getConsultationAndCaseInfo',
      payload: {
        consultationId: consultationId,                    // 指导ID
        type: 1,                                           // (1:图文支付/视频提交, 2:其它通用详情)
      }
    }).then(res => {
      const { code, content, msg } = res
      // 除专家和用户之外的第三人查看，提示
      if (code == 422) {
        Modal.warning({
          content: msg,
          onOk: () => {
            goBack()
          }
        })
        return
      }
      // 专家也不能看
      if (content && content.createUserId != UserInfo.friUserId) {
        Modal.warning({
          content: '对不起，您无权限查看该数据！',
          onOk: () => {
            goBack()
          }
        })
        return
      }

      // 流程节点
      // 图文[1选择指导方式、2描述病例问题、3支付指导费用、4病例资料被查看、5问题被回复并对话、6结束指导交易成功];
      // 视频[1选择指导方式、2描述病例问题、3提交指导单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、7结束指导、8确认并支付指导费用、9交易成功]
      if (content && content.processNode > 3 && pageFrom != 'ConsultationDetails') {
        Modal.warning({
          content: '链接已失效，可能因为指导订单已经提交成功，请前往我的指导查看订单',
          onOk: () => {
            goBack()
          }
        })
        return
      }

      if (code == 200 && content) {
        // 在5i5ya的iframe中
        if (isInIframe) {
          const postData = {
            dataType: 'pathname',       // 页面地址onchange事件
            pathnameByChild: pathname,  // 路由信息
            searchByChild: `?${stringify({
              ...query,
              consultationId: content.id, // 指导ID
              expertsUserId: content.expertsId, // 专家ID
              consultationType: content.type, // 指导类型，1图文，2视频
              orderCaseTemplate: content.orderCaseTemplate, // 1通用模版，2正畸模版
            })}`,  // 路由信息
          }
          console.log('子级发送数据：', postData, getArrailUrl())
          window.parent.postMessage(postData, getArrailUrl())
        }

        // 移动端复制过来的地址，consultationId是单号，更新一下
        history.replace({
          pathname,
          query: {
            ...query,
            consultationId: content.id, // 指导ID
            expertsUserId: content.expertsId, // 专家ID
            consultationType: content.type, // 指导类型，1图文，2视频
            orderCaseTemplate: content.orderCaseTemplate, // 1通用模板，2正畸模板
          }
        })

        // 病例信息
        const consultationCaseInfoDto = content.consultationCaseInfoDto || {}

        const checkedCaseTemplateListNew = []
        if (consultationCaseInfoDto && consultationCaseInfoDto.isTemplate == 1) {
          if (consultationCaseInfoDto.age || consultationCaseInfoDto.sex || consultationCaseInfoDto.chiefComplaint || consultationCaseInfoDto.presentDisease || consultationCaseInfoDto.previousHistory || consultationCaseInfoDto.wholeHealth) {
            checkedCaseTemplateListNew.push(1)
          }
          if (consultationCaseInfoDto.checkUp || consultationCaseInfoDto.diagnosis) {
            checkedCaseTemplateListNew.push(2)
          }
          if (consultationCaseInfoDto.treatmentPlanList && consultationCaseInfoDto.treatmentPlanList.length > 0) {
            checkedCaseTemplateListNew.push(3)
          }
        }
        setFormState({
          ...formState,
          checkedCaseTemplateList: checkedCaseTemplateListNew,       // 选中的病例模板选项[1,2,3]
          isTemplate: checkedCaseTemplateListNew.length > 0 ? 1 : 0,  // 是否是模板(1是，0否)
          noTemplateDescription: consultationCaseInfoDto.noTemplateDescription,   // 病例描述（非模板）
          age: consultationCaseInfoDto.age || '',                                 //年龄
          sex: consultationCaseInfoDto.sex || '',                                 // 性别
          chiefComplaint: consultationCaseInfoDto.chiefComplaint || '',           // 主诉
          presentDisease: consultationCaseInfoDto.presentDisease || '',           // 现病史
          previousHistory: consultationCaseInfoDto.previousHistory || '',         // 既往史
          wholeHealth: consultationCaseInfoDto.wholeHealth || '',                 // 全身健康情况
          checkUp: consultationCaseInfoDto.checkUp || '',                         // 检查
          diagnosis: consultationCaseInfoDto.diagnosis || '',                     // 诊断
          treatmentPlan: consultationCaseInfoDto.treatmentPlanList && consultationCaseInfoDto.treatmentPlanList[0] || '',  // 治疗方案
          firstQuestion: consultationCaseInfoDto.firstQuestion || '',             // 初次诊断
          consultationCaseMediaDtoList: consultationCaseInfoDto.consultationCaseMediaDtoList || [],   // 媒体文件
          processNode: content.processNode, // 图文[1选择指导方式、2描述病例问题、3支付指导费用、4病例资料被查看、5问题被回复并对话、6结束指导交易成功];
          // 视频[1选择指导方式、2描述病例问题、3提交指导单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、7结束指导、8确认并支付指导费用、9交易成功]
        })

      } else {
        message.error(msg || '查询指导和病例详情失败')
      }
    }).catch(err => {})
  }

  // 使用病例模板
  const selectCaseTemplate = (value, checked) => {
    const checkedCaseTemplateListClone = cloneDeep(formState.checkedCaseTemplateList)
    if (checked) {
      checkedCaseTemplateListClone.push(value)
    } else {
      const index = checkedCaseTemplateListClone.indexOf(value)
      checkedCaseTemplateListClone.splice(index, 1)
    }
    setFormState({
      ...formState,
      checkedCaseTemplateList: checkedCaseTemplateListClone, // 选择的模版list
      isTemplate: checkedCaseTemplateListClone.length > 0 ? 1 : 0, // 1 使用模版，0 不使用模版
      noTemplateDescription: '', // 不使用模版-描述
      age: value == 1 ? '' : formState.age,                                               // 年龄
      sex: value == 1 ? '' : formState.sex,                                               // 性别
      chiefComplaint: value == 1 ? '' : formState.chiefComplaint,                                    // 主诉
      presentDisease: value == 1 ? '' : formState.presentDisease,                                    // 现病史
      previousHistory: value == 1 ? '' : formState.previousHistory,                                   // 既往史
      wholeHealth: value == 1 ? '' : formState.wholeHealth,                                       // 全身健康情况
      checkUp: value == 2 ? '' : formState.checkUp,                                           // 检查
      diagnosis: value == 2 ? '' : formState.diagnosis,                                         // 诊断
      treatmentPlan: value == 3 ? '' : formState.treatmentPlan,                                     // 治疗方案
    })
  }

  // 输入病例描述
  const noTemplateDescriptionOnChange = (e) => {
    setFormState({
      ...formState,
      noTemplateDescription: e.target.value,
    })
  }

  // 输入年龄
  const ageOnChange = (e) => {
    setFormState({
      ...formState,
      age: e.target.value,
    })
  }

  // 输入性别
  const sexOnChange = (e) => {
    setFormState({
      ...formState,
      sex: e.target.value,
    })
  }

  // 输入主诉
  const chiefComplaintOnChange = (e) => {
    setFormState({
      ...formState,
      chiefComplaint: e.target.value,
    })
  }

  // 输入现病史
  const presentDiseaseOnChange = (e) => {
    setFormState({
      ...formState,
      presentDisease: e.target.value,
    })
  }

  // 输入既往史
  const previousHistoryOnChange = (e) => {
    setFormState({
      ...formState,
      previousHistory: e.target.value,
    })
  }

  // 输入全身健康情况
  const wholeHealthOnChange = (e) => {
    setFormState({
      ...formState,
      wholeHealth: e.target.value,
    })
  }

  // 输入检查
  const checkUpOnChange = (e) => {
    setFormState({
      ...formState,
      checkUp: e.target.value,
    })
  }

  // 输入诊断
  const diagnosisOnChange = (e) => {
    setFormState({
      ...formState,
      diagnosis: e.target.value,
    })
  }

  // 输入治疗方案
  const treatmentPlanOnChange = (e) => {
    setFormState({
      ...formState,
      treatmentPlan: e.target.value,
    })
  }

  // 输入初始提问
  const firstQuestionOnChange = (e) => {
    setFormState({
      ...formState,
      firstQuestion: e.target.value,
    })
  }

  // 上传图片headers
  const getHeaders = () => {
    // 获取环境类型，1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用   5 FRIDAY app中使用
    const env = getOperatingEnv()
    // FRIDAY APP中本地存储值：access_token、client、phone、user_name、wxuserId、userInfo
    return {// token
      access_token:localStorage.getItem('access_token') || '',
      username: env == 5 ? localStorage.getItem('user_name') : localStorage.getItem('vxOpenIdCipherText') ? localStorage.getItem('vxOpenIdCipherText')  : UserInfo?.phone,
      client: env == 5 ? localStorage.getItem('client') : 'WX',
      type: env === '1' ? '' : '1', // h5 传1
    }
  }

  // 上传校验规则，图片
  const beforeUpload = (file) => {
    const isSize = file.size / 1024 / 1024 < 15
    if (!isSize) {
      message.error('超过15M限制，不允许上传~')
      return false
    }

    const { name: fileName } = file || {}
    // 添加对文件后缀名的限制
    const suffix = fileName.substring(fileName.lastIndexOf('.')+1).toLowerCase()
    const isJpgOrPng = file.type === 'image/jpg' || file.type === 'image/jpeg' ||  file.type === 'image/png'
    const isSuffixByJpgOrPng = (
      suffix === 'jpg'
      || suffix === 'jpeg'
      || suffix === 'png'
    )
    if (!isJpgOrPng || !isSuffixByJpgOrPng) {
      message.error({content: '只能上传JPG、JPEG、PNG格式的图片~'})
      return false
    }
    setUploadLoading(true)
    return true
  }

  // 上传完成回调，图片
  const uploadOnChange = (info) => {
    if (info.file.status === 'uploading') {

    }

    // 状态不为uploading时，代表上传事件结束
    if (info.file.status != 'uploading') {
      setUploadLoading(false)
    }

    // 判断当前上传已被拦截
    if (info && !info.file.status) {
      return
    }

    // 上传结束
    if (info && info.file.status === 'error') {
      message.error('上传失败')
      return
    }

    if (info && info.file.status === 'done') {
      const res = info.file.response || {}
      const { code, content, msg } = res
      if (code == 200 && content) {
        const file = info.file
        // 文件名和后缀名
        const suffix = file.name.substring(file.name.lastIndexOf('.')+1)
        const name = file.name.substring(0, file.name.lastIndexOf('.'))

        const consultationCaseMediaDtoListClone = cloneDeep(formState.consultationCaseMediaDtoList)
        consultationCaseMediaDtoListClone.push({
          type: 0,      // //资料类型(0星球影像、1其他资料、2全景片、3侧位片、4正面像、5侧面像、6正面咬合像、7正面咬合45度像、8左侧咬合像、9右侧咬合像、10上牙弓像、11下牙弓像)
          fileSize: file.size,    // 文件大小
          fileName: name,              // 文件名称
          fileSuffix: suffix,          // 文件后缀
          fileUrl: content.fileUrl,    // 文件路径
          fileUrlShow: content.fileUrlView,
        })
        setFormState({
          ...formState,
          consultationCaseMediaDtoList: consultationCaseMediaDtoListClone,
        })
      } else {
        message.error(msg || '上传失败')
      }
    }
  }

  // 删除图片或者文件
  const deleteImage = (index) => {
    const consultationCaseMediaDtoListClone = cloneDeep(formState.consultationCaseMediaDtoList)
    consultationCaseMediaDtoListClone.splice(index, 1)
    setFormState({
      ...formState,
      consultationCaseMediaDtoList: consultationCaseMediaDtoListClone,
    })
  }

  // 上传文件校验规则，附件
  const beforeUploadFile = (file) => {
    // 目前服务端限制最大不超过30MB
    const isSize = file.size / 1024 / 1024 < 30
    if (!isSize) {
      message.error('超过30M限制，不允许上传~')
      return false
    }

    const { name: fileName } = file || {}
    // 后缀名
    const suffix = fileName.substring(fileName.lastIndexOf('.')+1).toLowerCase()
    // .pdf,.doc,.docx,.xlsx,.xls,.ppt,.pptx,.zip,.stl
    const isSuffixByJpgOrPng = (
      suffix == 'pdf' || suffix == 'doc' || suffix == 'docx'
      || suffix == 'xlsx' || suffix == 'xls' || suffix == 'png'
      || suffix == 'ppt' || suffix == 'pptx' || suffix == 'zip' || suffix == 'stl'
    )
    if (!isSuffixByJpgOrPng) {
      message.error('该文件类型不允许上传~')
      return false
    }
    setUploadLoading(true)
    return true
  }

  // 上传文件完成回调，附件
  const uploadOnChangeFile = (info) => {
    if (info.file.status === 'uploading') {

    }

    // 状态不为uploading时，代表上传事件结束
    if (info.file.status != 'uploading') {
      setUploadLoading(false)
    }

    // 判断当前上传已被拦截
    if (info && !info.file.status) {
      return
    }

    // 上传结束
    if (info && info.file.status === 'error') {
      message.error('上传失败')
      return
    }

    if (info && info.file.status === 'done') {
      const res = info.file.response || {}
      const { code, content, msg } = res
      if (code == 200 && content) {
        const file = info.file
        // 获取文件名和后缀
        const suffix = file.name.substring(file.name.lastIndexOf('.')+1).toLowerCase()
        const name = file.name.substring(0, file.name.lastIndexOf('.'))
        const consultationCaseMediaDtoListClone = cloneDeep(formState.consultationCaseMediaDtoList)
        consultationCaseMediaDtoListClone.push({
          type: 1,  // 资料类型(0星球影像、1其他资料、2全景片、3侧位片、4正面像、5侧面像、6正面咬合像、7正面咬合45度像、8左侧咬合像、9右侧咬合像、10上牙弓像、11下牙弓像)
          fileSize: file.size,
          fileName: name,
          fileSuffix: suffix,
          fileUrl: content.fileUrl,
          fileUrlShow: content.fileUrlView,
        })
        setFormState({
          ...formState,
          consultationCaseMediaDtoList: consultationCaseMediaDtoListClone,
        })
      } else {
        message.error(msg || '上传失败')
      }
    }
  }

  // 上一步
  const goToPrev = () => {
    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'pathname',       // 页面地址onchange事件
        pathnameByChild: '/ConsultationModule/StartConsultation/Step2',  // 路由信息
        searchByChild: `?${stringify({
          ...query,
        })}`,  // 路由信息
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
      return
    }
    history.replace({
      pathname: '/ConsultationModule/StartConsultation/Step2',
      query: {
        ...query,
      }
    })
  }

  // 下一步
  const goToNext = () => {
    // 修改状态，若有未输入的必填项，就会出现红字提示
    setIsClickSubmit(true)
    // 校验影像资料
    if (formState.consultationCaseMediaDtoList.length == 0) {
      return
    }
    // 校验影像资料-图片
    if (formState.consultationCaseMediaDtoList.filter(item => item.type == 0).length == 0) {
      return
    }
    // 校验提问
    if (!formState.firstQuestion) {
      return
    }
    submit()
  }

  // 提交
  const submit = () => {
    clearTimeout(timer)
    setLoadingSubmit(true)
    let postParams = {
      id: consultationId,                              // 指导ID
      type: consultationType,                          // 指导类型，1 图文，2 视频
      expertsId: expertsUserId, // 指导医生ID
      orderCaseTemplate: orderCaseTemplate, // 订单病例模板 1. 通用病例  2正畸病例
      processNode: formState.processNode < 4 ? 3 : formState.processNode, // 图文[1选择指导方式、2描述病例问题、3支付指导费用、4病例资料被查看、5问题被回复并对话、6结束指导交易成功];
      // 视频[1选择指导方式、2描述病例问题、3提交指导单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、7结束指导、8确认并支付指导费用、9交易成功]
      consultationCaseInfoDto: {
        caseName: '指导病例',
        isTemplate: formState.isTemplate,                                  // 是否是模板(1是，0否)
        noTemplateDescription: formState.noTemplateDescription,            // 无模板的描述
        firstQuestion: formState.firstQuestion,                            // 初始提问
        consultationCaseMediaDtoList: formState.consultationCaseMediaDtoList,   // 病例媒体信息集合
        treatmentPlanList: formState.treatmentPlan ? [formState.treatmentPlan] : [],   // 治疗方案集合(兼容瑞尔数组)
        diagnosis: formState.diagnosis,                // 诊断
        checkUp: formState.checkUp,                    // 检查
        wholeHealth: formState.wholeHealth,            // 全身健康情况
        previousHistory: formState.previousHistory,    // 既往史
        presentDisease: formState.presentDisease,      // 现病史
        chiefComplaint: formState.chiefComplaint,      // 主诉
        sex: formState.sex,                            // 性别
        age: formState.age,                            // 年龄
        templateType: formState.templateType,          // 模板类型(1星球、2瑞尔)
      },
    }
    dispatch({
      type: 'consultation/editConsultationInfo',
      payload: {
        postParams,
      }
    }).then(res => {
      setLoadingSubmit(false)
      const { code, content, msg } = res
      if (code == 200) {
        // 表示从指导详情来，编辑完成后返回
        if (pageFrom == 'ConsultationDetails') {
          // 在5i5ya的iframe中
          if (isInIframe) {
            const postData = {
              dataType: 'goBack',       // 页面地址onchange事件
            }
            console.log('子级发送数据：', postData, getArrailUrl())
            window.parent.postMessage(postData, getArrailUrl())
            return
          }

          // 复制地址打开页面的情况下
          if (copyUserId) {
            history.replace({
              pathname: '/ConsultationModule/ConsultationDetails',
              query: {
                consultationId,                      // 指导ID
                consultationType,                    // 指导类型，1 图文，2 视频
              }
            })
          } else {
            history.goBack()
          }
          return
        }

        // 在5i5ya的iframe中
        if (isInIframe) {
          const postData = {
            dataType: 'pathname',       // 页面地址onchange事件
            pathnameByChild: '/ConsultationModule/StartConsultation/Step4',  // 路由信息
            searchByChild: `?${stringify({
              ...query,
            })}`,  // 路由信息
          }
          console.log('子级发送数据：', postData, getArrailUrl())
          window.parent.postMessage(postData, getArrailUrl())
          return
        }
        // 来自创建指导/我的指导列表
        history.replace({
          pathname: '/ConsultationModule/StartConsultation/Step4',
          query: {
            ...query,
          }
        })
      } else {
        message.error(msg || '提交失败')
      }
    }).catch(err => {})
  }

  // 实时保存
  const editConsultationInfo = () => {
    let postParams = {
      id: consultationId,                              // 指导ID
      type: consultationType,                          // 指导类型，1 图文，2 视频
      expertsId: expertsUserId, // 指导医生ID
      orderCaseTemplate: orderCaseTemplate, // 订单病例模板 1. 通用病例  2正畸病例
      processNode: formState.processNode, // 图文[1选择指导方式、2描述病例问题、3支付指导费用、4病例资料被查看、5问题被回复并对话、6结束指导交易成功];
      // 视频[1选择指导方式、2描述病例问题、3提交指导单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、7结束指导、8确认并支付指导费用、9交易成功]
      consultationCaseInfoDto: {
        caseName: '指导病例',
        isTemplate: formState.isTemplate,                                  // 是否是模板(1是，0否)
        noTemplateDescription: formState.noTemplateDescription,            // 无模板的描述
        firstQuestion: formState.firstQuestion,                            // 初始提问
        consultationCaseMediaDtoList: formState.consultationCaseMediaDtoList,   // 病例媒体信息集合
        treatmentPlanList: formState.treatmentPlan ? [formState.treatmentPlan] : [],   // 治疗方案集合(兼容瑞尔数组)
        diagnosis: formState.diagnosis,                // 诊断
        checkUp: formState.checkUp,                    // 检查
        wholeHealth: formState.wholeHealth,            // 全身健康情况
        previousHistory: formState.previousHistory,    // 既往史
        presentDisease: formState.presentDisease,      // 现病史
        chiefComplaint: formState.chiefComplaint,      // 主诉
        sex: formState.sex,                            // 性别
        age: formState.age,                            // 年龄
        templateType: formState.templateType,          // 模板类型(1星球、2瑞尔)
      },
    }
    dispatch({
      type: 'consultation/editConsultationInfo',
      payload: {
        postParams,
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200) {

      } else {
        message.error(msg || '实时保存失败')
      }
    }).catch(err => {})
  }

  // 点击返回按钮
  const onClickBackBtn = () => {
    setGoBackTipsModalVisible(true)
  }

  // 返回提示弹窗关闭
  const goBackTipsModalClose = () => {
    setGoBackTipsModalVisible(false)
    goBack()
  }

  // H5与PC登录账号不一致提示弹窗，点击立即登录；或未登录进行登录
  const onClickLogin = () => {
    if (isInIframe) {
      const postData = {
        dataType: 'logout',       // 登出
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
      return
    }
    history.push({
      pathname: '/User/login',
      query: {
        redirectByPush: window.location.href,
      }
    })
  }

  // H5与PC登录账号不一致提示弹窗关闭
  const accountErrorTipsModalClose = () => {
    setAccountErrorTipsModalVisible(false)
    goBack()
  }

  // 返回
  const goBack = () => {
    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'goBack',       // 页面地址onchange事件
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
      return
    }

    if (copyUserId || history.length <= 2) {
      history.replace('/')
    } else {
      history.goBack()
    }
  }

  // loading
  const getConsultationAndCaseInfoLoading = !!loading.effects['consultation/getConsultationAndCaseInfo']

  return (
    <>
      <div className={styles.container}>
        {/* iframe中隐藏header */}
        {
          isInIframe ? null : <PcHeader/>
        }

        <div className={styles.content}>
          <div className={styles.content_inner}>
            <div className={styles.header}>
              <div className={styles.header_icon} onClick={onClickBackBtn}></div>
              <div className={styles.header_title}>发起专家指导</div>
            </div>

            <div className={styles.box}>
              <StartConsultationSteps title="描述病例问题"/>

              <Spin spinning={getConsultationAndCaseInfoLoading}>
                <div className={styles.form_wrap}>


                  <div className={styles.form_title}>病例描述</div>
                  <div className={styles.form_item_box}>
                    <div className={styles.item_label}>病例描述：</div>
                    <div className={styles.item_content}>
                      {
                        formState.isTemplate == 0 ?
                          <Input.TextArea
                            placeholder="请输入病例描述，或选择下方病例模板，指导结果更精准"
                            autoSize={{minRows: 3, maxRows: 3}}
                            value={formState.noTemplateDescription}
                            onChange={noTemplateDescriptionOnChange}
                            maxLength={200}
                          />
                          :
                          <div className={styles.description_content}>
                            {
                              formState.checkedCaseTemplateList.indexOf(1) > -1 &&
                              <>
                                <div className={styles.child_item}>
                                  <div className={styles.child_item_label}>患者年龄：</div>
                                  <div className={styles.child_item_content}>
                                    <Input
                                      value={formState.age}
                                      onChange={ageOnChange}
                                    />
                                  </div>
                                </div>
                                <div className={styles.child_item}>
                                  <div className={styles.child_item_label}>性别：</div>
                                  <div className={styles.child_item_content}>
                                    <Input
                                      value={formState.sex}
                                      onChange={sexOnChange}
                                    />
                                  </div>
                                </div>
                                <div className={styles.child_item}>
                                  <div className={styles.child_item_label}>主诉：</div>
                                  <div className={styles.child_item_content}>
                                    <Input
                                      value={formState.chiefComplaint}
                                      onChange={chiefComplaintOnChange}
                                    />
                                  </div>
                                </div>
                                <div className={styles.child_item}>
                                  <div className={styles.child_item_label}>现病史：</div>
                                  <div className={styles.child_item_content}>
                                    <Input
                                      value={formState.presentDisease}
                                      onChange={presentDiseaseOnChange}
                                    />
                                  </div>
                                </div>
                                <div className={styles.child_item}>
                                  <div className={styles.child_item_label}>既往史：</div>
                                  <div className={styles.child_item_content}>
                                    <Input
                                      value={formState.previousHistory}
                                      onChange={previousHistoryOnChange}
                                    />
                                  </div>
                                </div>
                                <div className={styles.child_item}>
                                  <div className={styles.child_item_label}>全身健康情况：</div>
                                  <div className={styles.child_item_content}>
                                    <Input
                                      value={formState.wholeHealth}
                                      onChange={wholeHealthOnChange}
                                    />
                                  </div>
                                </div>
                              </>
                            }
                            {
                              formState.checkedCaseTemplateList.indexOf(2) > -1 &&
                              <>
                                <div className={styles.child_item}>
                                  <div className={styles.child_item_label}>检查：</div>
                                  <div className={styles.child_item_content}>
                                    <Input
                                      value={formState.checkUp}
                                      onChange={checkUpOnChange}
                                    />
                                  </div>
                                </div>
                                <div className={styles.child_item}>
                                  <div className={styles.child_item_label}>诊断：</div>
                                  <div className={styles.child_item_content}>
                                    <Input
                                      value={formState.diagnosis}
                                      onChange={diagnosisOnChange}
                                    />
                                  </div>
                                </div>
                              </>
                            }
                            {
                              formState.checkedCaseTemplateList.indexOf(3) > -1 &&
                              <>
                                <div className={styles.child_item}>
                                  <div className={styles.child_item_label}>治疗方案：</div>
                                  <div className={styles.child_item_content}>
                                    <Input
                                      value={formState.treatmentPlan}
                                      onChange={treatmentPlanOnChange}
                                      maxLength={200}
                                    />
                                  </div>
                                </div>
                              </>
                            }

                          </div>
                      }
                    </div>
                  </div>
                  <div className={styles.case_description_template}>
                    {
                      caseTemplateList.map((item, index) => {
                        const checked = formState.checkedCaseTemplateList.indexOf(item.code) > -1
                        return (
                          <div
                            key={index}
                            className={classNames(styles.template_item, {
                              [styles.checked]: checked,
                            })}
                            onClick={() => selectCaseTemplate(item.code, !checked)}>
                            <PlusOutlined/>{item.name}
                          </div>
                        )
                      })
                    }
                  </div>

                  {/* 其他文件 */}
                  <div className={styles.form_title}>
                    <span className={styles.required_mark}>*</span>
                    影像资料
                  </div>
                  <Spin spinning={uploadLoading}>
                    <div className={styles.form_item_box}>
                      <div className={styles.item_label}>上传影像：</div>
                      <div className={classNames(styles.item_content, styles.image_content)}>
                        {
                          formState.consultationCaseMediaDtoList.map((item, index) => {
                            if (item.type == 0) {
                              return (
                                <div key={index} className={styles.image_item}
                                     style={{backgroundImage: `url(${item.fileUrlShow})`}}>
                                  <div className={styles.image_delete_btn} onClick={() => deleteImage(index)}><CloseCircleFilled/>
                                  </div>
                                </div>
                              )
                            }
                            return null
                          })
                        }

                        {
                          formState.consultationCaseMediaDtoList.filter(item => item.type == 0).length < 9 &&
                          <div className={styles.upload_box}>
                            <div className={styles.upload_btn}>
                              <PlusOutlined/>
                              <Upload
                                headers={getHeaders()}
                                accept="image/*"
                                listType="picture-card"
                                action={`/api/server/base/uploadFile?${stringify({fileType: 18, userId: UserInfo?.friUserId})}`}
                                onChange={uploadOnChange}
                                beforeUpload={beforeUpload}
                                showUploadList={false}
                              />
                            </div>
                            <div className={styles.upload_text}>请至少上传1张图片</div>
                          </div>
                        }
                      </div>
                    </div>
                    {
                      isClickSubmit && (formState.consultationCaseMediaDtoList.length == 0 || formState.consultationCaseMediaDtoList.filter(item => item.type == 0).length == 0) &&
                      <div className={styles.form_error_along}>请至少上传1张图片</div>
                    }
                    <div className={styles.form_item_box}>
                      <div className={styles.item_label}>其他资料：</div>
                      <div className={classNames(styles.item_content, styles.file_content)}>
                        <div className={styles.upload_box}>
                          {
                            formState.consultationCaseMediaDtoList.filter(item => item.type == 1).length < 5 &&
                            <div className={styles.upload_btn}>
                              上传附件
                              <Upload
                                headers={getHeaders()}
                                accept=".pdf,.doc,.docx,.xlsx,.xls,.ppt,.pptx,.zip,.stl"
                                listType="picture-card"
                                action={`/api/server/base/uploadFile?${stringify({fileType: 19, userId: UserInfo?.friUserId})}`}
                                onChange={uploadOnChangeFile}
                                beforeUpload={beforeUploadFile}
                                showUploadList={false}
                              />
                            </div>
                          }
                          <div className={styles.upload_text}>支持ppt、word、zip、pdf</div>
                        </div>
                      </div>
                    </div>
                    <div className={styles.case_file_list}>
                      {
                        formState.consultationCaseMediaDtoList.map((item, index) => {
                          if (item.type == 1) {
                            return (
                              <div key={index} className={styles.file_item}>
                                <div className={styles.file_item_info}>
                                  <div className={classNames(styles.info_icon, {
                                    [styles[item.fileSuffix]]: true,
                                  })}></div>
                                  <div>
                                    <div className={styles.info_name}>{item.fileName}.{item.fileSuffix}</div>
                                    <div className={styles.info_size}>{(item.fileSize / 1024).toFixed(1)}kb</div>
                                  </div>
                                </div>
                                <div className={styles.file_item_btn} onClick={() => deleteImage(index)}>删除</div>
                              </div>
                            )
                          }
                          return null
                        })
                      }
                    </div>
                  </Spin>



                  {
                    pageFrom != 'ConsultationDetails' &&
                    <>
                      <div className={styles.form_title}><span className={styles.required_mark}>*</span>提问</div>
                      <div className={styles.form_item_box}>
                        <div className={styles.item_label}>描述问题：</div>
                        <div className={styles.item_content}>
                          <Input.TextArea
                            placeholder="请输入针对以上病例您想要问的问题"
                            value={formState.firstQuestion}
                            onChange={firstQuestionOnChange}
                            maxLength={200}
                            autoSize={{minRows: 3, maxRows: 3}}
                          />
                        </div>
                      </div>
                      {
                        isClickSubmit && !formState.firstQuestion &&
                        <div className={styles.form_error_along}>请填写提问</div>
                      }
                    </>
                  }
                </div>

                <div className={styles.step_btn_box}>
                  {
                    pageFrom == 'ConsultationDetails' ?
                      <Button type="primary" className={styles.btn_next} onClick={goToNext} loading={loadingSubmit}>保存并提交</Button>
                      :
                      <>
                        <Button className={styles.btn_prev} onClick={goToPrev}>上一步</Button>
                        <Button type="primary" className={styles.btn_next}  onClick={goToNext} loading={loadingSubmit}>下一步</Button>
                      </>
                  }
                </div>
              </Spin>


            </div>
          </div>
        </div>

      </div>

      {/* 返回上一页提示弹窗 */}
      <TipsModal
        visible={goBackTipsModalVisible}
        title={'病例草稿已自动保存！您可在个人中心-我的病例内，'}
        text={'随时查阅或继续编辑'}
        rightBtnText={'我知道了'}
        onClickLeftBtn={goBackTipsModalClose}
        onClickRightBtn={goBackTipsModalClose}
      />

      {/* H5与PC登录账号不一致提示弹窗 */}
      <TipsModal
        visible={accountErrorTipsModalVisible}
        title={'您当前在PC端登录的账号与移动端不一致，请重新登录'}
        leftBtnText={'取消'}
        rightBtnText={'重新登录'}
        onClickLeftBtn={accountErrorTipsModalClose}
        onClickRightBtn={onClickLogin}
      />
    </>
  )
}

export default connect(({ consultation, loading }: any) => ({ consultation, loading }))(Index)
