/**
 * @Description: 王国详情
 */
import React, { useState, useEffect, useRef } from 'react';
import { history, connect } from 'umi';
import {Modal, Toast} from 'antd-mobile';
import classNames from 'classnames';
import { QuestionCircleFilled } from '@ant-design/icons';
import styles from './index.less';
import { randomColor, processNames, WxAppIdByPublicAccount, getShareUrl, getOperatingEnv } from '@/utils/utils';
import blueUpArrowIcon from '@/assets/Expert/blue_up_arrow.png'; // 向上箭头小图标
import blueDownArrowIcon from '@/assets/Expert/blue_down_arrow.png'; // 向下箭头小图标
import SpaceList from '@/components/SpaceList'; // 空间组件
import MeetingCard from '@/components/MeetingCardInfo';            // 会议卡片组件
import QuitKingdomModal from './QuitKingdomModal'; // 退出王国提示弹框
import KingdomDisabledModal from './KingdomDisabledModal'; // 王国违规被关闭提示弹窗
import noDataImg from '@/assets/GlobalImg/no_data.png'; // 暂无数据图标
import NavBar from '@/components/NavBar'; // 头部组件
import {Helmet} from "react-helmet"; // 用于添加动态title
import ReactHtmlParser  from 'react-html-parser';    // dom字符串转dom结构
import {Spin} from 'antd';
import CreateKingdomOrSpace from '../UserInfo/CreateKingdomOrSpace';
import ImageTextList from './ImageTextList';
import {stringify} from "qs"

const tabLists = [
  { id: 1, val: '直播' },
  // { id: 2, val: '会议' },
  { id: 3, val: '帖子' },
]

const Index: React.FC = (props: any) => {
  const { global, dispatch, loading, userInfoStore, kingdom } = props || {};
  const { createModalVisible } = userInfoStore || {};
  const { query } = history.location
  const [tabType, setTabType] = useState(query.tabKey || kingdom.tabKey);  // tab
  const [spaceDtoList, setSpaceDtoList] = useState([]); // 直播or会议列表数据
  const [kingdomData, setKingdomData] = useState<any>({}); // 王国相关数据
  const [isShowBtn, setIsShowBtn] = useState(false); // 收起、展开简介
  const [isDisable, setIsDisable] = useState(false); // 当前王国是否被禁用
  const [isExitVisible, setIsExitVisible] = useState(false); // 是否退出当前王国弹框状态
  const [comeType, setComeType] = useState(3); // 打开哪个下拉框(2王国空间、3 王国)
  const [linkDetailsVisible, setLinkDetailsVisible] = useState(false); // 外链了解更多打开弹框状态
  const [imageTextKingdomList,setImageTextKingdomList ] = useState(null); // 王国查询图文信息
  const introduceRef = useRef<any>(null);
  const handler = useRef<any>();

   // 解决ios手机在弹框弹出软键盘后，弹框底部内容滚动问题
   useEffect(() => {
    if (createModalVisible) {
      document.body.style.position = 'fixed'
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.position = 'unset'
      document.body.style.overflow = 'auto';
    }
  }, [createModalVisible]);

  useEffect(() => {
    getKingdomDetailData();
    // 是否是从分享进入的
    if (query.shareUserId) {
      shareUpdateByType(query.shareUserId)
    }
  }, [])

  useEffect(() => {
    // 获取王国下的直播or会议列表数据
    tabType!=3 && getKingdomSpaceList();
  }, [tabType])

  // 初始化获取王国详情列表数据
  const getKingdomDetailData = () => {
    let KingdomId = props?.match?.params?.KingdomId;
    dispatch({
      type: 'userInfoStore/getKingdomInfo',
      payload: {
        id: KingdomId
      }
    }).then(res => {
      if(res && res.code == 200) {
        setKingdomData(res.content);
        setIsDisable(res?.content?.isDisable); // 0启用，1禁用
      }
      // 当前王国违反平台规范无法查看
      if(res && res.code == 403) {
        setIsDisable(true)
      }
    })
  }

  // 获取王国详情下的直播or会议列表数据
  const getKingdomSpaceList = () => {
    dispatch({
      type: 'userInfoStore/getKingdomSpaceList',
      payload: {
        kingdomId: props?.match?.params?.KingdomId,
        starSpaceType:tabType
      }
    }).then(res => {
      if(res && res.code == 200) {
        setSpaceDtoList(res?.content);
      }
    })
  }


  useEffect(() => {
    if (kingdomData.id || kingdomData.id == 0) {
      onShareAppMessage()
    }
  }, [kingdomData])

  // 微信分享配置
  const onShareAppMessage = () => {
    const url = window.location.href
    const shareUrl = getShareUrl(url)
    console.log('shareUrl：', shareUrl)
    dispatch({
      type: 'userInfoStore/getJsapiTicket',
      payload: {
        currentUrl: url,                                   // 页面url
        appId:  WxAppIdByPublicAccount,                     // 公众号appId
      },
    }).then(res => {
      if (res && res.code == 200) {
        wx.config({
          debug: false,
          appId: res.content.appId,
          timestamp: res.content.timestamp,
          nonceStr: res.content.nonceStr,
          signature: res.content.signature,
          jsApiList: [
            'updateAppMessageShareData',
            'updateTimelineShareData',
            'onMenuShareTimeline',
            'onMenuShareAppMessage',
            'onMenuShareQQ',
            'onMenuShareWeibo',
            'onMenuShareQZone',
          ],
        })
        wx.ready(() => {
          const shareDate = {
            title: '【FRIDAY医生星球】牙医都来这里学习和交流！',
            desc: kingdomData.name,
            link: shareUrl,
            imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/DigitalHealthShareImg.png',
          };
          console.log(shareDate)
          wx.updateAppMessageShareData(shareDate);
          wx.updateTimelineShareData(shareDate);
          wx.onMenuShareTimeline(shareDate);
          wx.onMenuShareAppMessage(shareDate);
          wx.onMenuShareQQ(shareDate);
          wx.onMenuShareWeibo(shareDate);
          wx.onMenuShareQZone(shareDate);
        })
      } else {
        // Toast.show('请求微信配置失败～！')
      }
    })
  }


  // 分享操作更新gdp等数据
  const shareUpdateByType = (shareUserId) => {
    let KingdomId = props?.match?.params?.KingdomId;
    dispatch({
      type: 'userInfoStore/shareUpdateByType',
      payload: {
        id: KingdomId,                                     // 被分享ID(王国、空间、用户)
        shareId: shareUserId,                              // 分享人ID
        type: 2,                                           // 类型(2王国，3空间)
      }
    }).then(res => {

    }).catch(err => {})
  }

  // 加入王国/已加入（需要登录才能操作）
  const joinKingdomBtn = () => {
    // 没登录跳转登录页
    if (!localStorage.getItem('access_token')) {
      history.push({
        pathname: '/User/login',
        query: {
          redirectByPush: window.location.href,
        }
      })
      return
    }

    // 是否已加入(1是，0否)-已加入-退出
    if(kingdomData?.hasJoined == 1) {
      setIsExitVisible(true)
      return;
    } else {
      // 加入
      joinOrExitClick(1) // 类型(1加入，2退出)
    }
  }

  // 加入或退出王国
  const joinOrExitClick = (type) => {
    handler.current = Toast.show({icon: 'loading', duration: 0, maskClickable: false})
    dispatch({
      type: 'userInfoStore/joinOrQuitKingdom',
      payload: {
        id: kingdomData && kingdomData.id || null,
        type: type, // 1加入 2退出
      }
    }).then(res => {
      handler?.current.close()
      if(res && res.code == 200) {
        Toast.show({content: '加入王国成功！'})
        getKingdomDetailData()
      }
    })
  }

  // 退出王国-我再想想
  const cancelFn = () => {
    setIsExitVisible(false)
  }

  // 狠心退出
  const submitFn = () => {
    handler.current = Toast.show({icon: 'loading', duration: 0, maskClickable: false})
    dispatch({
      type: 'userInfoStore/joinOrQuitKingdom',
      payload: {
        id: kingdomData && kingdomData.id || null, // 王国id
        type: 2, // 1加入 2退出
      }
    }).then(res => {
      handler?.current.close()
      if(res && res.code == 200) {
        Toast.show({content: '退出王国成功'})
        setIsExitVisible(false)
        getKingdomDetailData()
      } else {
        Toast.show({content: '退出王国失败'})
      }
    })
  }

  // 刷新
  const refreshFn = () => {
    getKingdomDetailData()
    onShareAppMessage()
    dispatch({
      type: 'userInfoStore/clean',
      payload: {
        createModalVisible: false, // 是否展示创建空间王国下拉弹框
        selectCreateType: 0, // 创建空间王国选中的tab值
        selectedKingdom: null, // 已选择-王国
        selectedCompere: null, // 已选择主持人
        selectedGuest: [], // 已选择嘉宾
        creatTabSelectDate: {}, // 创建空间王国选择后的数据
        spaceCoverUrl: null, // 空间封面路径
        spaceAdvertisingUrl: null, // 空间广告路径
        spacePasswordText: null, // 密码
        spaceName: null, // 空间名称
        selectedKing: null, // 已选择-国王
        kingdomName: null, // 王国名称
        spaceIntroduceVal: null, // 空间介绍
        kingdomIntroduce: null, // 王国介绍
        kingdomCoverUrl: null, // 王国封面图路径
        appointmentStartTime: null, // 空间预约时间
        spaceVideoId: null, // 空间视频id
        spaceVideoUrl: null, // 空间视频上传文件地址
        spaceVideoName: null, // 空间视频文件名
        isEditSpace: false, // 是否编辑空间
        spaceFromEnter: {}, // 从哪里进入的创建空间/编辑空间(pageType: 1 我的主页、2 空间详情、3 王国详情, refreshFn: 刷新页面的函数, tipText: 弹框提示内容)
        kingdomFromEnter: {}, // 从哪里进入的王国(pageType: 1 我的主页、2 空间详情、3 王国详情, refreshFn: 刷新页面的函数, tipText: 弹框提示内容)
        spaceTypeId: null, // 空间类型 1专家讲课 2指导 3复杂病例讨论
        spaceTypeName: null, // 空间类型名称
        spectatorType: 0, // 观众类型 0默认所有人 1指定王国成员可见 2指定企业品牌用户可见
        allViewersState: true, // 选择观众默认所有人可见状态
        kingdomListArr: [], // 观众-王国列表数据
        selectedKingdomAudience: [], // 观众-选择可见王国
        enterpriseUserData: [], // 选择可见企业/品牌用户，当前企业下的机构数据
        enterpriseUserTab: 0, // 选择可见企业/品牌用户，页面tab
        enterpriseUserSelectData: [], // 选择可见企业/品牌用户，选择后的数据
        enterpriseText: null, // 选择可见企业/品牌用户，弹框名称
      }
    })
  }

  // 创建王国空间
  const createSpaceFn = () => {
    // 先调用接口查询是否为超级账号
    checkSuperAccount();
    const {name, id, kingdomCoverUrlShow, kingImgUrlShow, wxUserId, kingName} = kingdomData || {}; // 王国信息
    // setComeType(2); // 打开创建王国空间弹框
    const options = {
      id: '王国直播',
      type: 2,
      title: '创建王国直播',
      goBackType: '', // 返回（99创建王国空间）
      isShowGoBack: 1, // 是否有返回箭头 1没有
      isSelectKingDom: 1, // 是否可以选择王国 1没有
    }

    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        createModalVisible: false,
        creatTabSelectDate: options,
        selectedKingdom: { // 保存王国信息
          name: name, // 王国名称
          id: id, // 王国id
          kingImgUrlShow: kingdomCoverUrlShow || kingImgUrlShow, // 头像
          wxUserId: wxUserId, // 国王id
          kingName: kingName, // 国王
          comeType: 'kingdom', // 从哪来的标识
        },
        spaceFromEnter: {
          pageType: '3', // 1 我的主页、2 空间详情、3 王国详情
          refreshFn: refreshFn, // 刷新页面的方法
          tipText: '创建直播成功', // 创建成功提示信息
          isJumpOrRefresh: true, // 用于在王国创建空间后直接跳转空间详情或关闭弹框内容
          isCureentKingdom: true // 是否是当前指定王国
        },
        selectedKingdomAudience: id ? [id] : [], // 指定当前王国所有成员，并将当前王国的id保存到store中
      }
    })
    // 前往创建直播页面 并已关联王国
    history.push(`/CreateSpace/Live`);
  }

  // 是否为超级账号
  const checkSuperAccount = () => {
    dispatch({
      type: 'userInfoStore/checkSuperAccount',
    }).then((res:any) => {
      if(res && res.code == 200) {
        localStorage.setItem('isSuperAccount', res.content) // true是，false否
        dispatch({
          type: 'userInfoStore/setTaskListState',
          payload: {
           isSuperAccount: res.content
          }
        })
        return;
      } else {
        return Toast.show({content: res.msg || '数据加载失败'})
      }
    }).catch((err:any) => {
      console.log(err)
    })
  }

  // 编辑王国
  const editBtnFn = () => {
    let KingdomId = props?.match?.params?.KingdomId; // 王国id
    const userInfoStr = localStorage.getItem('userInfo')
    const userInfo = userInfoStr ? JSON.parse(userInfoStr) : {}
    Toast.show({icon: 'loading', maskClickable: false})
    dispatch({
      type: 'userInfoStore/getKingdomInfoByEdit',
      payload: {
        kingdomId: KingdomId, // 王国id
        wxUserId: userInfo.friUserId, // 用户id
      }
    }).then(res => {
      if(res && res.code == 200) {
        const { content } = res || {};
        const { id, name, descriptions, kingdomCoverUrl, kingdomCoverUrlShow } = content || {};
        Toast.clear()
        const options = {
          id: '王国',
          type: 3,
          title: '编辑王国',
          goBackType: '', // 返回（99创建王国空间）
          isShowGoBack: 1, // 是否有返回箭头 1没有
          isSelectKingDom: 1, // 是否可以选择王国 1没有
        }
        dispatch({
          type: 'userInfoStore/setTaskListState',
          payload: {
            createModalVisible: true,
            creatTabSelectDate: options,
            kingdomName: name, // 王国名称
            kingdomIntroduce: descriptions, // 王国介绍
            kingdomCoverUrl: kingdomCoverUrl && kingdomCoverUrlShow ? {fileUrlView: kingdomCoverUrlShow, fileUrl: kingdomCoverUrl} : null, // 封面图
            isEditKingdom: true, // 是否编辑王国
            kingdomFromEnter: {
              pageType: '3', // 3 王国详情
              refreshFn: refreshFn, // 刷新页面的方法
              tipText: '修改成功',
              KingdomId: id, // 王国id
            }
          }
        })

        setComeType(3); // 打开创建王国弹框
      } else {
        Toast.clear()
        Toast.show({content: res.msg || '数据加载失败'})
      }
    }).catch((err:any) => {
      Toast.clear()
      console.log(err)
    })
  }

  // tab切换
  const tabBtnFn = (id) => {
    if (tabType == id) {
      return
    }
    history.replace(`${history.location.pathname}?${stringify({
      ...query,
      tabKey: id,
    })}`)
    setTabType(id)
    // store中保存当前tab切换值，防止去详情返回后tab会重置
    dispatch({
      type: 'kingdom/save',
      payload: {
        tabKey: id,
        subTabKey: 1,
      }
    })
  }

  // 外链-了解更多-打开详情弹框
  const learnMoreBtnFn = () => {
    setLinkDetailsVisible(true) // 打开外链详情
  }

  // 外链详情关闭事件
  const onCancelModalFn = () => {
    setLinkDetailsVisible(false) // 关闭外链详情
  }

  const getKingdomInfoLoading = !!loading.effects['userInfoStore/getKingdomInfo']; // loading

  return <>
    <Helmet>
      <title>{kingdomData?.name ? kingdomData?.name : '王国详情'}</title>
      <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, shrink-to-fit=no" />
    </Helmet>
    <Spin spinning={getKingdomInfoLoading}>
      <div className={styles.container}>
        <div className={styles.bg_color}><NavBar title={kingdomData?.name} style={{background: '#FFF linear-gradient(135deg, #FFF 0%, #E4EEFC 100%)'}} ></NavBar></div>

        <div className={styles.info_box}>
          <div className={styles.avatar}>
            {kingdomData && kingdomData?.kingdomCoverUrlShow || kingdomData.kingImgUrlShow ?
              <img className={styles.head_img} src={kingdomData?.kingdomCoverUrlShow || kingdomData?.kingImgUrlShow} alt=''/> :
              <div className={styles.no_comment_head} style={{background:randomColor(kingdomData?.wxUserId)}}>{processNames(kingdomData?.kingName)}</div>
            }
          </div>
          <div className={styles.info_title}>{kingdomData && kingdomData.name}</div>
          <div className={styles.user}>国王·{kingdomData && kingdomData.kingName}</div>
          <div className={styles.details}>
            <div className={styles.details_item}>
              <span>国民</span>
              <span className={styles.number}>{kingdomData && kingdomData.nationalNum || 0}</span>
            </div>
            <div className={styles.details_item}>
              <span>热议直播</span>
              <span className={styles.number}>{kingdomData && kingdomData.spaceNum || 0}</span>
            </div>
            <div className={styles.details_item}>
              <span>热议会议</span>
              <span className={styles.number}>{kingdomData && kingdomData.meetingSpaceNum || 0}</span>
            </div>
            <div className={styles.details_item}>
              <span>GDP</span>
              <span className={styles.number}>{kingdomData && kingdomData.gdp || 0}</span>
              <QuestionCircleFilled onClick={() => {
                Modal.show({
                  bodyClassName: 'gdp_modal',
                  content: global.gdpExplain!=null&&ReactHtmlParser(global.gdpExplain[2].gdpContent)||'暂无GDP介绍',
                  closeOnMaskClick: true,
                })
              }} style={{fontSize: 12, color: '#999', marginLeft: 2, verticalAlign: 'baseline'}}/>
            </div>
          </div>

          <div className={styles.introduce_box}>
            <div className={styles.introduce_title}>介绍</div>
            {
              introduceRef?.current?.offsetHeight > 60 ?
                <div className={styles.introduce_btn_box}>
                {
                  !isShowBtn ?
                    <div className={styles.brief_introduction_btn} onClick={() => { setIsShowBtn(true) }}>展开简介<img src={blueDownArrowIcon} alt="" /></div> :
                    <div className={styles.brief_introduction_btn} onClick={() => { setIsShowBtn(false) }}>收起简介<img src={blueUpArrowIcon} alt="" /></div>
                }
              </div> : null
            }
          </div>
          <div className={isShowBtn ? styles.brief_introduction_show : styles.brief_introduction_hide}>
            <span className={styles.text} ref={introduceRef} dangerouslySetInnerHTML={{ __html: kingdomData && kingdomData?.descriptions }}></span>
          </div>
        </div>

        <div className={styles.space}></div>
        <div className={styles.content_wrap}>
          <div className={styles.tab_wrap}>
            {
              tabLists.map(item => {
                return <div key={item.id} className={classNames({ [styles.tab_init]: true, [styles.tab_active]: tabType == item.id })} onClick={() => tabBtnFn(item.id)}>{item.val}</div>
              })
            }
          </div>
          <div className={styles.tab_content_list}>
            {/* 直播 */}
            {
              tabType == 1 ?
                <>
                  {
                   spaceDtoList && spaceDtoList.length ?
                   <div>
                      <div style={{paddingLeft: '12px', fontSize: '12px', marginBottom: '20px'}}>共 {spaceDtoList && spaceDtoList.length} 条内容</div>
                      <SpaceList componentData={{dataList: spaceDtoList, config: {number: 1}}} />
                    </div> :
                    <div className={styles.none_data}>
                      <img src={noDataImg} alt="" />
                      暂无热议直播
                    </div>
                  }
                </> : null
            }
            {
              tabType == 2 ?
                <>
                  {
                    spaceDtoList && spaceDtoList.length ?
                      <div className={styles.meeting_list_wrap}>
                        <div style={{fontSize: '12px', marginBottom: '20px'}}>共 {spaceDtoList && spaceDtoList.length} 条内容</div>
                        {
                          spaceDtoList && spaceDtoList.map((item: any, index: number) => {
                            return (
                              <div key={index} className={styles.meeting_item_wrap}>
                                <MeetingCard key={index} item={item} style={{minHeight: 'auto', paddingBottom: 12}}/>
                              </div>
                            )
                          })
                        }
                      </div> :
                      <div className={styles.none_data}>
                        <img src={noDataImg} alt="" />
                        暂无热议会议
                      </div>
                  }
                </> : null
            }
            {/* 帖子3合1 */}
            {
              tabType == 3 ?
                <>
                  <ImageTextList
                    KingdomId={ props?.match?.params?.KingdomId }
                    tabType={tabType}
                  />
                </> : null
            }
          </div>
        </div>

        {/* 底部按钮 */}
        {// 自己创建的王国可以进行编辑和创建空间按钮
          kingdomData?.hasJoined == 2 ?
          <div className={classNames(styles.fixed_box, styles.my_kingdom_style, {
            [styles.fixed_box_pc]: getOperatingEnv() == 4
          })}>
            <div className={styles.edit_btn_style} onClick={editBtnFn}>编辑</div>
            <div className={styles.create_space_style} onClick={createSpaceFn}>创建直播</div>
          </div> :
          // 否则就展示已加入/未加入的按钮
          <div className={classNames(styles.fixed_box, {
            [styles.fixed_box_pc]: getOperatingEnv() == 4
          })}>
            <div className={styles.box}>
              <div className={kingdomData?.hasJoined ? styles.visible_btn : styles.btn} onClick={joinKingdomBtn}>{kingdomData && kingdomData?.hasJoined ? "已加入" : "加入王国"}</div>
            </div>
          </div>
        }

        <QuitKingdomModal visible={isExitVisible} onCancel={cancelFn} onSubmit={submitFn}/>

        <KingdomDisabledModal visible={isDisable}/>

        {/* comeType传2，打开空间编辑 3 编辑王国*/}
        <CreateKingdomOrSpace comeType={comeType} />

      </div>
    </Spin>
  </>
}

export default connect(({global, userInfoStore, loading, kingdom }: any) => ({global, userInfoStore, loading, kingdom}))(Index)
