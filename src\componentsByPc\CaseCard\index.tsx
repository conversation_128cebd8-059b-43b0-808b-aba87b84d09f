/**
 * @Description: PC端，病例卡片组件
 */
import React, { useState, useRef } from 'react'
import { history } from 'umi'
import classNames from 'classnames'
import { getArrailUrl } from '@/utils/utils'
import { Carousel } from 'antd'
import styles from './index.less'

const Index: React.FC = (props: any) => {
  // 是否嵌套在5i5ya的iframe中
  const isInIframe = (self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya')

  const [sliderIndex, setSliderIndex] = useState(0)
  const ref = useRef(null)

  const {
    caseData = {},
    whereFrom = null,      // 1 首页
  } = props

  // 点击病例跳转
  const goToUrl = (caseData) => {
    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'pathname',       // 页面地址onchange事件
        pathnameByChild: '/Case/CaseDetails',  // 路由信息
        searchByChild: `?excellentCaseId=${caseData.id}`,  // 路由信息
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
      return
    }
    history.push({
      pathname: '/Case/CaseDetails',
      query: {
        excellentCaseId: caseData.id,                          // 病例ID
      }
    })
  }

  // 下一张
  const sliderGoToNext = (e) => {
    e.preventDefault()
    e.stopPropagation()
    if (ref && ref.current) {
      ref.current.next()
    }
  }

  // 上一张
  const sliderGoToPrve = (e) => {
    e.preventDefault()
    e.stopPropagation()
    if (ref && ref.current) {
      ref.current.prev()
    }
  }

  // 切换后触发时间
  const afterChange = (index) => {
    setSliderIndex(index)
  }

  return (
    <div className={classNames(styles.case_container, {
      [styles.container_form_1]: whereFrom == 1,
    })} onClick={() => goToUrl(caseData)}>
      {/* 主题 */}
      {
        caseData.topicName &&
        <h5 className={styles.topic} dangerouslySetInnerHTML={{__html: caseData.topicName}}></h5>
      }

      {/* 难度等级和学科和病例成就 */}
      {
        ((caseData.depSubjectDictNameList && caseData.depSubjectDictNameList.length > 0) || caseData.achievementDictName) &&
        <div className={styles.subject_box}>
          {
            caseData.difficultLevelDict &&
            <span className={styles[`difficult${caseData.difficultLevelDict}`]}>难度{caseData.difficultLevelDictName}</span>
          }
          {
            caseData.depSubjectDictNameList && caseData.depSubjectDictNameList.length > 0 &&
            caseData.depSubjectDictNameList.map((itemChild, indexChild) =>
              <span key={indexChild}>{itemChild}</span>
            )
          }
          {
            caseData.achievementDictName &&
            <span className={styles.achievement}>{caseData.achievementDictName}</span>
          }
        </div>
      }

      {/* 主诊医师 */}
      {
        caseData.doctorUserList && caseData.doctorUserList.length > 0 &&
        <div className={styles.doctor_box}>
          <div className={styles.label}>主诊：</div>
          <div className={styles.value}>{caseData.doctorUserList.join('、')}</div>
        </div>
      }

      {/* 病例问题 */}
      {
        caseData.keyWordList && caseData.keyWordList.length > 0 &&
        <div className={styles.keywords_box}>
          <div className={styles.label}>病例问题：</div>
          <div className={styles.value_box}>
            {
              caseData.keyWordList.map((itemChild, indexChild) =>
                <span key={indexChild} dangerouslySetInnerHTML={{__html: itemChild}}></span>
              )
            }
          </div>
        </div>
      }

      {/* 封面图 */}
      {
        caseData.coverPathUrlShowList && caseData.coverPathUrlShowList.length > 0 &&
        <div className={styles.cover_img_box}>
          {
            caseData.coverPathUrlShowList.length > 3 &&
            <>
            {
              sliderIndex > 0 &&
              <div className={classNames(styles.arrow, styles.arrow_left)} onClick={sliderGoToPrve}>上一张</div>
            }
            {
              sliderIndex < caseData.coverPathUrlShowList.length - 3 &&
              <div className={classNames(styles.arrow, styles.arrow_right)} onClick={sliderGoToNext}>下一张</div>
            }
            </>
          }
          <Carousel
            ref={ref}
            draggable={true}
            slidesToShow={3}
            speed={300}
            infinite={false}
            dots={false}
            swipeToSlide={true}   // 拖到哪，停在哪
            afterChange={afterChange}
          >
            {
              caseData.coverPathUrlShowList.map((itemChild, indexChild) => (
                <div key={indexChild} className={styles.cover_img_block}>
                  <div className={styles.cover_img} style={{backgroundImage: `url(${itemChild})`}}>
                    {/*<img src={itemChild} alt=""/>*/}
                  </div>
                </div>
              ))
            }
          </Carousel>
        </div>
      }

      {/* 封面图（首页里的） */}
      {
        caseData.coverPathUrlList && caseData.coverPathUrlList.length > 0 &&
        <div className={styles.cover_img_box}>
          {
            caseData.coverPathUrlList.length > 3 &&
            <>
              {
                sliderIndex > 0 &&
                <div className={classNames(styles.arrow, styles.arrow_left)} onClick={sliderGoToPrve}>上一张</div>
              }
              {
                sliderIndex < caseData.coverPathUrlList.length - 3 &&
                <div className={classNames(styles.arrow, styles.arrow_right)} onClick={sliderGoToNext}>下一张</div>
              }
            </>
          }
          <Carousel
            ref={ref}
            draggable={true}
            slidesToShow={3}
            speed={300}
            infinite={false}
            dots={false}
            swipeToSlide={true}   // 拖到哪，停在哪
            afterChange={afterChange}
          >
            {
              caseData.coverPathUrlList.map((itemChild, indexChild) => (
                <div key={indexChild} className={styles.cover_img_block}>
                  <div className={styles.cover_img} style={{backgroundImage: `url(${itemChild.fileUrlView})`}}>
                    {/*<img src={itemChild.fileUrlView} alt=""/>*/}
                  </div>
                </div>
              ))
            }
          </Carousel>
        </div>
      }

      {/* 治疗方案 */}
      {
        caseData.solutionAll &&
        <div className={styles.solution_box}>
          <i className={styles.solution_icon}></i>
          <div className={styles.solution_text} dangerouslySetInnerHTML={{__html: caseData.solutionAll}}></div>
        </div>
      }

      {/* 评论 */}
      {/*<div className={styles.comments}>{caseData.commentsCount}评论 · {caseData.excellentCasePV}学习</div>*/}
    </div>
  )
}

export default Index
