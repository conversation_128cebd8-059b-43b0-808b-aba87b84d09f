/**
 * @Description: 移动端-在Frida App中打开按钮
 */
import React, { useRef, useEffect } from 'react';
import {connect, history } from 'umi'
import { Toast } from 'antd-mobile'
import styles from './index.less';
import { isIOS, getOperatingEnv, WxAppIdByPublicAccount } from '@/utils/utils';
import { getJsapiTicket } from "@/services/userInfo";  // 微信jsapiTicket


interface IProps {
  type?:number;  // 1:直播 2:会议
  info?:object;  // 直播或会议的id
  BoxStyle?: object;  // 按钮Box样式
  InnerText?: string;  // 按钮文字
  InnerStyle?: object;  // 按钮文字样式
}

const DownloadAppBtn: React.FC<IProps> = (props:IProps) => {
  const { type, info, BoxStyle, InnerText, InnerStyle } = props;
  // const launchBtn = useRef(null);
  useEffect(() => {
    // if(getOperatingEnv()==2){
    //   wxConfig()
    // }
  }, [])

  // 微信config配置
  const wxConfig = async()=> {
    const jsapiTicketContent = await getJsapiTicket({
      appId: WxAppIdByPublicAccount,
      currentUrl: window.location.href.split('#')[0],
    })
    const {code, content} = jsapiTicketContent || {}
    if (code == 200) {
      wx.config({
        debug: false,
        appId: content.appId,
        timestamp: content.timestamp,
        nonceStr: content.nonceStr,
        signature: content.signature,
        jsApiList: [],
        openTagList:['wx-open-launch-app'],
      })
      wx.ready(() => {
        let openAppbtn = document.getElementById('launch-btn');
        openAppbtn.addEventListener('launch', function (e) {
          console.log('success');
        });
        openAppbtn.addEventListener('error', function (e) {
          console.log('fail', e);
        });
      })
    }else {
      Toast.show(jsapiTicketContent && jsapiTicketContent.msg ?  jsapiTicketContent.msg : '获取微信配置失败!')
    }
  }

  // 打开APP
  const openFridayApp = () => {
    const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    const message={
      ...info,
      type:type,
    }
    // const iosUrlScheme = 'yourapp://';
    // const androidIntent = 'intent://yourapp#Intent;scheme=yourapp;package=com.example.yourapp;end';
    let appUrl= type==1?`${window.location.origin.replace('https', 'friday')}/PlanetChatRoom/Live/${info?.roomId}?fromBrowser=1&token=${localStorage.getItem('access_token')?localStorage.getItem('access_token'):''}&phone=${UserInfo?.phone?UserInfo?.phone:''}`:`${window.location.origin.replace('https', 'friday')}/PlanetChatRoom/Meet/${info?.roomId}?fromBrowser=1&token=${localStorage.getItem('access_token')?localStorage.getItem('access_token'):''}&phone=${UserInfo?.phone?UserInfo?.phone:''}`;
    const start = Date.now();
    let timeout;
    const checkOpen = () => {
      const elapsed = Date.now() - start;
      if (elapsed < 2000) {
        history.push(`/AppDownload?info=${encodeURIComponent(JSON.stringify(info))}`)
      }
    };
    const handleVisibilityChange=()=> {
      if (document.visibilityState === 'hidden') {
        clearTimeout(timeout);
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      }
    }

    timeout = setTimeout(checkOpen, 1500);
    if (isIOS()) {
      window.location.href = appUrl;
    } else {
      if(navigator.userAgent.indexOf("Firefox") > -1){
        window.location.href = appUrl;
        timeout = setTimeout(() => {
          checkOpen();
          clearTimeout(timeout);
        }, 1500);
      }else{
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.src = appUrl;
        document.body.appendChild(iframe);
        timeout = setTimeout(() => {
          document.body.removeChild(iframe);
          clearTimeout(timeout);
        }, 1500);
      }
    }
    window.addEventListener('blur', () => {
      clearTimeout(timeout);
    });
    document.addEventListener('visibilitychange', handleVisibilityChange);
  }

  return (
    <>
      {
        getOperatingEnv() == 3 && <div className={styles.app_download_btn} style={BoxStyle ? BoxStyle : {}}><span
          style={InnerStyle ? InnerStyle : {}} onClick={openFridayApp}>{InnerText ? InnerText : 'FRIDAY APP内打开'}</span>
        </div>
      }
      {/*{getOperatingEnv() == 2 && <div className={styles.app_download_btn}>*/}
      {/*  <wx-open-launch-app*/}
      {/*    ref={launchBtn}*/}
      {/*    id={'launch-btn'}*/}
      {/*    appid={WxAppIdByPublicFridayAppAccount}*/}
      {/*    extinfo={type==1?`${window.location.origin}/PlanetChatRoom/Live/${info?.roomId}`:`${window.location.origin}/PlanetChatRoom/Meet/${info?.roomId}`}*/}
      {/*  >*/}
      {/*    <script type="text/wxtag-template">*/}
      {/*      <button className="wxAppOpenBtn">{InnerText?InnerText:'FRIDAY APP内打开'}</button>*/}
      {/*    </script>*/}
      {/*  </wx-open-launch-app>*/}
      {/*</div>}*/}
    </>
  );
};

export default connect(({loading}: any) => ({loading}))(DownloadAppBtn)
