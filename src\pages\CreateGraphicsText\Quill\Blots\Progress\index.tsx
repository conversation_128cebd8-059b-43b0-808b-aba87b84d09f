import { Quill } from 'react-quill'
const BlockEmbed = Quill.import('blots/block/embed')

class Progress extends BlockEmbed {
  static create(value) {
    let node = super.create();
    node.setAttribute('contenteditable', false)
    node.setAttribute('id', value.code)
    node.setAttribute('data-type', 'progress')
    node.setAttribute('data-name', value.name)
    node.setAttribute('data-percent', value.percent)
    this.buildContentNode(node, value)
    return node;
  }

  static buildContentNode(node, value) {
    const wrapNode = document.createElement('div')
    wrapNode.classList.add('progress_wrap')
    const containerNode = document.createElement('div')
    containerNode.classList.add('progress_container')
    const contentNode = document.createElement('div')
    contentNode.classList.add('progress_content')
    const infoNode = document.createElement('div')
    infoNode.classList.add('progress_info')
    const spanNameNode = document.createElement('span')
    const spanPercentNode = document.createElement('span')
    spanPercentNode.classList.add('span_percent')
    const lineNode = document.createElement('div')
    lineNode.classList.add('progress_line')
    const lineInnerNode = document.createElement('div')
    lineInnerNode.classList.add('progress_line_inner')

    spanNameNode.innerText = value.name
    spanPercentNode.innerText = value.percent

    wrapNode.appendChild(containerNode)
    containerNode.appendChild(contentNode)
    contentNode.appendChild(infoNode)
    contentNode.appendChild(lineNode)
    infoNode.appendChild(spanNameNode)
    infoNode.appendChild(spanPercentNode)
    lineNode.appendChild(lineInnerNode)
    node.appendChild(wrapNode)

  }

  static value(node: Element) {
    return {
      code: node.id,
      name: node.dataset.name,
      percent: node.dataset.percent,
    }
  }

  format(name: string, value: string) {
    const spanNodes = this.domNode.getElementsByClassName('span_percent')
    const divNodes = this.domNode.getElementsByClassName('progress_line_inner')
    const spanPercentNode = spanNodes && spanNodes[0]
    const lineInnerDivNode = divNodes && divNodes[0]
    console.log('format-------', name, value)
    if (spanPercentNode && lineInnerDivNode && name == 'width') {
      spanPercentNode.innerText = value
      lineInnerDivNode.style.width = value
    }
  }

}

Progress.blotName = 'progress';                            // 格式名
Progress.tagName = 'div';                                  // dom标签
Progress.className = 'quill_progress_format_wrap';         // dom类名

export default Progress
