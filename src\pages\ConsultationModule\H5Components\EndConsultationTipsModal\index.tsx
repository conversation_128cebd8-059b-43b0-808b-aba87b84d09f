/**
 * @Description: 移动端结束指导提示弹窗
 * @author: 赵斐
 */
import React from 'react';
import { Modal } from 'antd-mobile'
import warnIcon from '@/assets/Consultation/H5/warn_icon.png'
import styles from './index.less'


interface PropsType {
  visible: boolean,                 // 结束指导提示弹窗状态
  onCancel: (k?:number) => void,    // 取消回调
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { visible ,onCancel } = props;

  // 点击确认并关闭弹窗
  const onConfirm = ()=>{
    onCancel(1)
  }

  // 弹窗展示内容
  const endConsultationTipsDOM = () => {
    return (
      <div className={styles.content}>
        <div className={styles.content_desc}>
          <span className={styles.tips_icon}><img src={warnIcon} alt="提示icon" /></span>
          结束后将无法继续会话，确定结束？
        </div>
        <div className={styles.footer}>
          <span className={styles.cancel} onClick={() => {onCancel()}}>取消</span>
          <span className={styles.confirm} onClick={() => {onConfirm()}}>确定</span>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.wrap}>
      <Modal
        bodyClassName={styles.modal}
        visible={visible}
        content={endConsultationTipsDOM()}
        closeOnMaskClick
        onClose={() => {onCancel()}}
      />
    </div>
  )
}
export default Index
