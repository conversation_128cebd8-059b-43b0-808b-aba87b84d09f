.modal {

}

// 表单
.form_item_wrap {
  display: flex;
  flex-wrap: nowrap;
  column-gap: 8px;
  .form_item_label {
    flex-shrink: 0;
    white-space: nowrap;
    text-align: right;
    width: 84px;
    padding-top: 4px;
  }

  // 上传文件
  .upload_wrap {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    column-gap: 8px;
    row-gap: 8px;
    .upload_item {
      position: relative;
      width: 104px;
      height: 104px;
      border-radius: 2px;
      padding: 8px;
      border: 1px solid #d9d9d9;
      .upload_item_mask {
        display: none;
        position: absolute;
        z-index: 10;
        left: 8px;
        top: 8px;
        right: 8px;
        bottom: 8px;
        background: rgba(0,0,0,0.5);
        justify-content: center;
        align-items: center;
        column-gap: 4px;
        color: #fff;
        font-size: 14px;
        cursor: pointer;
        :global {
          .anticon {
            color: #fff;
            font-size: 14px;
          }
        }
      }
      &:hover .upload_item_mask {
        display: flex;
      }
    }
  }
}

// 底部按钮
.btn_wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  column-gap: 24px;
  padding-top: 40px;
}
