.wrap {
  width: 100%;
  background: #f5f6f8;
  // padding-bottom: 50px;
}

.user_info_wrap {
  width: 100%;
  min-height: 38px;
  padding-top: 38px;
  background: #ffffff linear-gradient(135deg, #ffffff 0%, #e4eefc 100%);

  .user_info_box {
    width: 100%;
    padding: 16px 16px 21px 20px;
    background: #ffffff;
    border-radius: 16px 16px 0px 0px;

    .user_info_img {
      position: relative;
      display: flex;
      justify-content: flex-end;
      margin-bottom: 16px;

      .head_sculpture {
        position: absolute;
        top: -45px;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 72px;
        height: 72px;
        overflow: hidden;
        color: #ffffff;
        font-weight: 500;
        font-size: 24px;
        line-height: 34px;
        background: url('../../assets/GlobalImg/default_head_picture.png') no-repeat;
        background-size: cover;
        border: 3px solid #ffffff;
        border-radius: 50%;

        img {
          width: 72px;
          height: 72px;
        }

        .head_sculpture_name {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 72px;
          height: 72px;
          color: #ffffff;
          font-weight: 500;
          font-size: 24px;
        }
      }

      .edit_box {
        color: #999999;
        font-weight: 400;
        font-size: 13px;
        line-height: 18px;

        img {
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }
      }
    }

    .user_info_content {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .user_info_content_left {
        display: flex;
        flex-direction: column;

        .top_box {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          color: #000000;
          font-weight: 500;
          font-size: 24px;
          line-height: 34px;

          .user_name_text {
            max-width: 110px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .change_waistcoat {
            margin-left: 10px;
            color: #0095ff;
            font-size: 15px;
          }
          img {
            width: 60px;
            height: auto;
            margin-left: 12px;
          }
        }
        .bottom_box {
          display: flex;
          align-items: center;

          .info_data_item {
            margin-right: 16px;
            color: #999999;
            font-weight: 400;
            font-size: 11px;
            line-height: 13px;

            span {
              //line-height: 18px;
              margin-right: 3px;
              color: #000000;
              font-weight: 500;
              font-size: 15px;
            }
          }
        }
      }

      .user_info_content_right {
        display: inline-block;
        flex-shrink: 0;
        padding: 8px 12px;
        color: #000000;
        font-weight: 400;
        font-size: 13px;
        line-height: 18px;
        background: #ffffff;
        border: 1px solid #cccccc;
        border-radius: 20px;

        img {
          position: relative;
          top: -1px;
          width: 12px;
          height: 12px;
          margin-left: 4px;
        }
      }
    }
  }
}

.menu_wrap {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 5px;
  padding: 21px 16px;
  background: #fff;

  .menu_item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 3px;

    img {
      width: 24px;
      height: 24px;
      margin-bottom: 8px;
    }

    .little_title {
      color: #666666;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
    }
  }
}

.kingdom_box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  width: 100%;
  margin-top: 5px;
  padding: 24px 21px 21px 16px;
  background: #fff;

  .kingdom_content {
    display: flex;
    flex-direction: column;

    .kingdom_text {
      margin-bottom: 4px;
      color: #000000;
      font-weight: 500;
      font-size: 17px;
      line-height: 22px;
    }

    .kingdom_describe {
      color: #999999;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
    }
  }

  .kingdom_create {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 32px;
    color: #ffffff;
    font-weight: 400;
    font-size: 14px;
    background: #0095ff;
    border-radius: 16px;

    img {
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }
  }
}

.vip_member_box {
  width: 100%;
  height: auto;
  margin-top: 5px;
  padding: 24px;
  padding-bottom: 150px;
  background: #fff;
  // border-bottom: 5px solid #F5F6F8;

  .vip_title {
    margin-bottom: 4px;
    color: #000000;
    font-weight: 500;
    font-size: 17px;
    line-height: 24px;
  }

  .vip_list {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;

    .vip_list_item {
      display: flex;
      flex-direction: column;
      flex-shrink: 0;
      align-items: center;
      width: 25%;
      margin-top: 12px;
    }

    .vip_list_item_icon {
      width: 44px;
      height: 44px;
      margin-bottom: 3px;

      img {
        width: 44px;
        height: 44px;
      }
    }

    .vip_list_item_content {
      display: flex;
      flex-direction: column;
      align-items: center;

      .vip_list_item_text {
        color: #666666;
        font-weight: 400;
        font-size: 12px;
        font-family: PingFang SC;
        line-height: 17px;
        white-space: pre-wrap;
        text-align: center;
      }

      .vip_list_item_tips {
        color: #ff895f;
        font-weight: 400;
        font-size: 9px;
        line-height: 13px;
        white-space: pre-wrap;
        text-align: center;
      }
    }
  }

  .vip_button {
    width: calc(100% - 8px);
    margin-top: 16px;
    text-align: center;

    .vip_button_style {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      background: linear-gradient(158deg, #fdf0c2 0%, #f5d18a 100%);
      border-radius: 42px 42px 42px 42px;

      .vip_button_text {
        font-weight: 500;
        font-size: 16px;
        line-height: 38px;
        background-image: -webkit-linear-gradient(-90deg, #e8975d 0%, #7d461e 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
}

.character_Img {
  position: fixed;
  right: 12px;
  bottom: 0;
  display: flex;
  align-items: flex-end;
  // 加上安全距离，防止iOS没有操作栏时，定位掉下去
  margin-bottom: calc(50px + env(safe-area-inset-bottom));

  .QR_code_box {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 104px;
    height: 121px;
    margin-bottom: 60px;
    background: url('https://js.5i5ya.com/public/DigitalHealth/assets/white_box.png') no-repeat;
    background-size: cover;

    .QR_code_img {
      width: 80px;
      height: 80px;
      margin-top: 8px;
      margin-bottom: 4px;
    }

    .QR_code_text {
      color: #333333;
      font-weight: 400;
      font-size: 12px;
      font-family: PingFang SC;
      line-height: 14px;
      white-space: nowrap;
    }
  }

  .people_img {
    width: 61px;
    height: auto;
  }
}

// app中，底部菜单栏隐藏的情况
.in_app_wrap .vip_member_box {
  padding-bottom: 100px;
}

.in_app_wrap .character_Img {
  // 加上安全距离，防止iOS没有操作栏时，定位掉下去
  margin-bottom: env(safe-area-inset-bottom);
}

/* --- 学习金余额Start ---  */
.BalanceStudyFundsBox {
  position: relative;
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 107px;
  margin-top: 20px;
  padding: 14px;
  background: linear-gradient(96deg, #fff6ea 0%, #ffeedc 100%);
  border-radius: 8px 8px 8px 8px;

  .StudyBonusBg {
    position: absolute;
    right: 16px;
    bottom: 0px;
    width: 79px;
    height: 65px;
    background: url('~@/assets/GlobalImg/StudyBonusBg.png');
    background-size: 100% 100%;
  }

  .BalanceStudyFundsLeft {
    .BalanceStudyFundsTitle {
      margin-bottom: 6px;
      color: #000000;
      font-weight: 400;
      font-size: 12px;
    }

    .BalanceStudyFundsNum {
      color: #000000;
      font-weight: 500;
      font-size: 26px;
    }

    .BalanceStudyFundsDate {
      color: #666666;
      font-weight: 400;
      font-size: 12px;
    }
  }

  .BalanceStudyFundsRight {
    .DetailBox {
      display: flex;
      align-items: center;
      color: #0095ff;
      font-weight: 400;
      font-size: 12px;

      .DetailBox_icon {
        position: relative;
        top: -1px;
        width: 12px;
        height: 12px;
        margin-left: 4px;
        background: url('~@/assets/GlobalImg/BalanceStudyFund_DetailBox_icon.png');
        background-size: 100% 100%;
      }
    }
  }
}
/* 学习金余额End */

/* 企业员工和管理员*/
.administratorsModal {
  padding: 0 0 100px 16px;
  border-radius: 0px 0px 0px 0px;
  .administratorsModal_line {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 28px;
    width: 48px;
    margin: auto;
    span {
      width: 48px;
      height: 4px;
      background: #d0d4d7;
      border-radius: 4px 4px 4px 4px;
    }
  }
  .administratorsModal_header {
    color: #000000;
    font-weight: 500;
    line-height: 20px;
    .administratorsModal_header_img {
      width: 24px;
      height: 24px;
      display: block;
    }
    img {
      width: 12px;
    }
    .title {
      position: relative;
      left: 50%;
      margin-left: -44px;
      font-size: 17px;
    }
  }
  .administratorsModal_list {
    display: flex;
    flex-direction: column;
    color: #000000;
    font-weight: 500;
    font-size: 14px;
    // line-height: 48px;
    .administratorsModal_item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 48px;
      padding-right: 16px;
      // line-height: 48px;
      border-bottom: 1px solid #e1e4e7;
      .administratorsModal_item_rt {
        display: flex;
        align-items: center;
        .mangeIcon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 66px;
          height: 21px;
          margin-right: 8px;
          line-height: 21px;
          background: linear-gradient(90deg, #67c0ff 0%, #0095ff 100%);
          border-radius: 32px 32px 32px 32px;
          span {
            margin-left: 2px;
            color: #ffffff;
            font-weight: 400;
            font-size: 12px;
          }
          img {
            width: 12px;
            height: 12px;
          }
        }
      }

      img {
        width: 16px;
        height: 16px;
      }
    }
  }
}
.staffModal {
  position: fixed;
  top: 30%;
  left: 50%;
  box-sizing: border-box;
  width: 270px;
  margin-left: -135px;
  padding: 24px 20px;
  background: #ffffff;
  border-radius: 12px 12px 12px 12px;
  .title {
    color: #000000;
    font-weight: 500;
    font-size: 17px;
    line-height: 20px;
    text-align: center;
  }
  .content {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    margin-top: 18px;
    margin-bottom: 24px;
    padding: 12px;
    background: #f5f6f8;
    border-radius: 3px 3px 3px 3px;
    .companytitle {
      color: #000000;
      font-weight: 500;
      font-size: 16px;
      line-height: 19px;
    }
    .des {
      display: -webkit-box;
      margin-top: 6px;
      overflow: hidden;
      color: #999999;
      font-weight: 400;
      font-size: 13px;
      line-height: 15px;
      text-overflow: ellipsis;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
    }
  }
  .footer {
    display: flex;
    justify-content: space-between;
    color: #ffffff;
    font-weight: 400;
    font-size: 15px;
    .btn1 {
      height: 33px;
      padding: 0 24px;
      line-height: 33px;
      background: #ff5f57;
      border-radius: 20px 20px 20px 20px;
    }
    .btn2 {
      height: 33px;
      padding: 0 24px;
      line-height: 33px;
      background: #0095ff;
      border-radius: 20px 20px 20px 20px;
    }
  }
}

.staffDoubleModal {
  position: fixed;
  top: 30%;
  left: 50%;
  z-index: 1;
  box-sizing: border-box;
  width: 270px;
  margin-left: -135px;
  padding: 24px 20px;
  background: #ffffff;
  border-radius: 12px 12px 12px 12px;
  .tip_header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    color: #000000;
    font-weight: 500;
    font-size: 17px;
    line-height: 20px;
    img {
      width: 20px;
      margin-right: 8px;
    }
  }
  .tip_content {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
    color: #666666;
    font-weight: 400;
    font-size: 14px;
    line-height: 16px;
  }
  .tip_footer {
    display: flex;
    justify-content: space-between;
    font-weight: 400;
    font-size: 15px;
    span {
      width: 108px;
      height: 33px;
      // padding: 0 24px;
      line-height: 33px;
      text-align: center;
      border-radius: 20px 20px 20px 20px;
    }
    .tip_btn1 {
      color: #0095ff;
      background: #edf9ff;
    }
    .tip_btn2 {
      color: #fff;
      background: #0095ff;
    }
  }
}

.inviteImg_wrapper{
  display: flex;
  justify-content: center;
  align-items: center;
  .ant-spin-container {
    width: 290px;
    height: 534px;
  }
  img{
    width: 290px;
    height: 534px;
  }
}

