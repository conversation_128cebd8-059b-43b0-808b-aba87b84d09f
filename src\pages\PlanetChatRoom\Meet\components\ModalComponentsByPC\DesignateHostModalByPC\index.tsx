/**
 * @Description: 指定主持人弹窗（PC端）
 */
import React, { useState, useEffect } from 'react';
import { history, connect, useRouteMatch } from 'umi'
import { Modal, Button, Checkbox, message, Spin } from 'antd';
import styles from './index.less';

// 图标图片
import checkIcon3 from '@/assets/GlobalImg/check_icon_3.png'
import checkIcon4 from '@/assets/GlobalImg/check_icon_4.png'

import Avatar from '@/pages/PlanetChatRoom/components/Avatar' // 头像组件
import NoDataRender from '@/components/NoDataRender' // 暂无数据组件

interface PropsType {
  visible: boolean,          // 弹窗是否显示
  onCancel: () => void,      // 关闭弹窗
  designateHostModalOnOk: () => void, // 指定并离开
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const match = useRouteMatch()

  const {
    visible,
    dispatch,
    loading,
    PlanetChatRoom,
    onCancel, // 关闭弹窗
    designateHostModalOnOk, // 点击指定并离开回调
    onClickEndMeeting, // 点击结束会议的回调
  } = props

  const {
    membersListInTheMeeting, // 空间在线成员列表
  } = PlanetChatRoom || {}

  const [checkedUserId, setCheckedUserId] = useState(null)   // 选中的用户ID
  const [checkedImUserId, setCheckedImUserId] = useState(null) // 选中的用户imID

  useEffect(() => {
    if (visible) {
      getManageMembersInTheMeeting()
    } else {
      cleanState()
    }
  }, [])

  // 清空state
  const cleanState = () => {
    setCheckedUserId(null)
    setCheckedImUserId(null)
  }

  // 空间在线成员列表
  const getManageMembersInTheMeeting = async () => {
    let res = await dispatch({
      type: 'PlanetChatRoom/getManageMembersInTheMeeting',
      payload: {
        spaceId: match?.params?.RoomId,
        sceneType: 2, // 场景类型，1：会议详情在线用户，2：管理成员在线用户
      }
    })
    return res
  }

  // 选择用户
  const onClickUser = (wxUserId, imUserId) => {
    setCheckedUserId(wxUserId)
    setCheckedImUserId(imUserId)
  }

  // 点击指定并离开
  const onClickOk = () => {
    if (!checkedUserId) {
      message.error('请选择用户')
      return
    }
    designateHostModalOnOk(checkedUserId, checkedImUserId)
  }

  // loading
  const loadingGetManageMembersInTheMeeting = !!loading.effects['PlanetChatRoom/getManageMembersInTheMeeting']
  // 从在线成员中获取参会人
  const userListDataSource = membersListInTheMeeting && membersListInTheMeeting.length > 0 ? membersListInTheMeeting.filter(item => item.isSelf != 1 && item.meetingType == 2) : []

  return (
    <Modal
      visible={visible}
      onCancel={onCancel}
      className={styles.modal}
      destroyOnClose
      footer={null}
      width={474}
    >
      <Spin spinning={loadingGetManageMembersInTheMeeting}>
        <div className={styles.header}>请在离开会议前指定主持人</div>

        <div className={styles.scroll_wrap}>
          {
            userListDataSource.length > 0 ? userListDataSource.map(item => {
              return (
                <div key={item.wxUserId} className={styles.item} onClick={() => onClickUser(item.wxUserId, item.imUserId)}>
                  <div className={styles.item_left}>
                    <div className={styles.avatar_wrap}>
                      <Avatar userInfo={item} size={24}/>
                    </div>
                    <div className={styles.user_name}>{item.name}</div>
                  </div>

                  {/* 选中状态 */}
                  {
                    checkedUserId == item.wxUserId ? <img src={checkIcon4} width={16} height={16} alt=""/>
                      : <img src={checkIcon3} width={16} height={16} alt=""/>
                  }
                </div>
              )
            }) : <NoDataRender style={{marginTop: 40}} text="暂无参会人~"/>
          }
        </div>

        {/* 按钮 */}
        <div className={styles.btn_wrap}>
          {
            userListDataSource.length > 0 ?
              <Button type="primary" onClick={onClickOk}>指定并离开</Button>
              : <Button type="primary" onClick={onClickEndMeeting}>结束会议</Button>
          }
        </div>
      </Spin>
    </Modal>
  )
}
export default connect(({ PlanetChatRoom, loading }: any) => ({PlanetChatRoom, loading}))(Index)
