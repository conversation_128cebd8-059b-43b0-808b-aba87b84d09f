import React, { useRef, useState } from 'react'
import Cropper from 'react-cropper';             // 裁剪工具
import request from '@/utils/request'
import { useThrottle } from '@/utils/utils'
import { Button, Modal, message } from 'antd'
import 'cropperjs/dist/cropper.css';
import styles from './index.less'

interface PropsType {
  visible: boolean,                    // 弹窗是否显示
  imageUrlShow: string,                // 图片url
  handleCroppingImage: () => {},
  onCancel: () => {},
  uploadFileType?: number,
}

let scaleXNum = 1;
let scaleYNum = 1;

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { visible, imageUrlShow, uploadFileType = 23 } = props

  const cropperRef = useRef<HTMLImageElement>(null);                 // ref

  const [croppingLoading, setCroppingLoading] = useState(false);     // 图片loading

  // 旋转
  const rotateFn = () => {
    cropperRef.current?.cropper.rotate(45);
  }

  // 左右翻转
  const horizontalFlipFn = () => {
    if (scaleXNum === 1) {
      cropperRef.current?.cropper.scaleX(-1);
      scaleXNum = -1;
    } else {
      cropperRef.current?.cropper.scaleX(1);
      scaleXNum = 1;
    }
  }

  // 上下翻转
  const verticalFlipFn = () => {
    if (scaleYNum === 1) {
      cropperRef.current?.cropper.scaleY(-1);
      scaleYNum = -1;
    } else {
      cropperRef.current?.cropper.scaleY(1);
      scaleYNum = 1;
    }
  }

  // 重置
  const initImageFn = () => {
    cropperRef.current?.cropper.reset()
  }

  // 确定
  let onClickOk = () => {
    const cropUrl = cropperRef?.current?.cropper?.getCroppedCanvas().toDataURL();
    setCroppingLoading(true)
    uploadImg(cropUrl)
  }
  onClickOk = useThrottle(onClickOk, 500)

  // 上传图片
  const uploadImg = (cropUrl) => {
    const imgFile = dataURLtoFile(cropUrl, 'cropping_image')
    let formData = new FormData()
    formData.append('file',imgFile)
    request(`/api/server/base/uploadFile?fileType=${uploadFileType}`, {
      method: 'POST',
      body: formData,
    }).then(res => {
      setCroppingLoading(false)
      const { code, content, msg } = res
      if (code == 200 && content) {
        // 图文
        if (uploadFileType == 23) {
          props.handleCroppingImage(content.fileUrlView)
        } else {
          props.handleCroppingImage(content.fileUrl, content.fileUrlView)
        }
      } else {
        message.error(msg || '数据加载失败')
      }
    })
  }

  // base64转文件
  const dataURLtoFile = (dataurl, fileName) => {
    let arr = dataurl.split(','),
      mime = arr[0].match(/:(.*?);/)[1],
      bstr = atob(arr[1]),
      n = bstr.length,
      u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    const suffix = (mime == 'image/png' ? '.png' : '.jpg')
    return new File([u8arr], fileName + suffix, { type: mime });
  }

  return (
    <Modal
      open={visible}
      title="封面图编辑"
      className={styles.modal}
      onCancel={props.onCancel}
      maskClosable={false}
      destroyOnClose
      footer={null}
      width={1110}
    >
      <div className={styles.container}>
        <Cropper
          ref={cropperRef}
          style={{ height: 600 }}
          viewMode={1}
          initialAspectRatio={1}
          background={false}
          minCropBoxHeight={50}
          minCropBoxWidth={50}
          src={imageUrlShow}
        />
      </div>
      <div className={styles.footer}>
        <div className={styles.left}>
          <div className={styles.action_item} onClick={rotateFn}>
            <i className={styles.rotate_right}></i>
            <span>旋转</span>
          </div>
          <div className={styles.action_item} onClick={horizontalFlipFn}>
            <i className={styles.rotate_horizontal}></i>
            <span>左右翻转</span>
          </div>
          <div className={styles.action_item} onClick={verticalFlipFn}>
            <i className={styles.rotate_vertical}></i>
            <span>上下翻转</span>
          </div>
          <div className={styles.action_item} onClick={initImageFn}>
            <i className={styles.rotate_reset}></i>
            <span>恢复原图</span>
          </div>
        </div>
        <div>
          <Button size="large" onClick={props.onCancel}>取消</Button>
          <Button type="primary" size="large" onClick={onClickOk} loading={croppingLoading}>确定</Button>
        </div>
      </div>
    </Modal>
  )
}

export default Index
