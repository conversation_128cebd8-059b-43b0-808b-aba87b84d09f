/**
 * @Description: 关注、取消关注按钮
 */
import React, { useState } from 'react';
import { history, connect } from 'umi'
import { useThrottle } from '@/utils/utils'
import { Spin, message } from 'antd'
import { Modal, Toast } from 'antd-mobile';
import styles from './index.less';

// 图片icon
import blue_follow from '@/assets/GlobalImg/blue_follow.png'; // 关注小图标
import un_follow from '@/assets/GlobalImg/un_follow.png'; // 已关注状态

import CommonTipsModal from '@/pages/PlanetChatRoom/Meet/components/ModalComponents/CommonTipsModal' // 公共提示弹窗

interface PropsType {
  expertsUserId: number, // 专家ID
  isFocus: number, // 0未关注，1已关注
  handleFollowAndCheck: () => {}, // 关注或取消关注回调
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {
    loading,
    dispatch,
    expertsUserId, // 专家ID
    isFocus, // 0未关注，1已关注
    handleFollowAndCheck, // 关注或取消关注回调
  } = props
  const [visible, setVisible] = useState(false); // 取消关注提示弹框

  // 关注，0 取消关注，1 关注
  let onClickFollow = (isFocus2) => {
    // 没登录跳转登录页
    if (!localStorage.getItem('access_token')) {
      history.push({
        pathname: '/User/login',
        query: {
          redirectByPush: window.location.href,
        }
      })
      return
    }
    if (isFocus2 == 1) {
      followAndCheck(1)
    } else {
      setVisible(true)
    }
  }

  onClickFollow = useThrottle(onClickFollow, 500)

  // 狠心取消
  let onClickOk = () => {
    setVisible(false);
    followAndCheck(0)
  }

  onClickOk = useThrottle(onClickOk, 500)

  // 我再想想
  const onClickCancel = () => {
    setVisible(false);
  }

  // 关注、取消关注接口
  const followAndCheck = (isFocus2) => {
    dispatch({
      type: 'expertAdvice/followAndCheck',
      payload: {
        expertsUserId, // 专家Id
        isFocus: isFocus2, // 0 取消关注，1 关注
      }
    }).then(res => {
      const { code, msg } = res
      if (code == 200) {
        if (isFocus2 == 1) {
          message.success('已关注')
        } else {
          message.success('已取消关注')
        }
        handleFollowAndCheck(isFocus2)
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch(err => {})
  }

  // loading
  const loadingFollowAndCheck = !!loading.effects['expertAdvice/followAndCheck']

  return <>
    <Spin spinning={loadingFollowAndCheck}>
      {
        isFocus == 1 ?
          <div className={styles.cancel} onClick={() => onClickFollow(0)}>
            <img src={un_follow} alt="" width={12} height={12}/>
            已关注
          </div>
          : isFocus == 0 ?
          <div className={styles.ok} onClick={() => onClickFollow(1)}>
            <img src={blue_follow} alt="" width={12} height={12}/>
            关注
          </div>
          : null
      }
    </Spin>

    {/* 取消关注提示弹窗 */}
    <CommonTipsModal
      visible={visible}
      onClickLeftBtn={onClickOk} // 点击左边按钮
      onClickRightBtn={onClickCancel} // 点击右边按钮
      title={'取消关注'} // 标题
      text={'取消关注后将无法接收该用户动态'} // 提示文案
      text2={'(王国、空间、帖子、文章等的更新)'} // 需要另起一行的文案
      leftBtnText={'狠心取消'} // 左边按钮文案
      rightBtnText={'我再想想'} // 右边按钮文案
    />
  </>
};

export default connect(({ loading }: any) => ({ loading }))(Index)
