import React, { useState } from 'react';
import styles from './index.less';
import { history, connect } from 'umi';
import { Modal, message, Dropdown } from 'antd';

const Index: React.FC = (props: any) => {
  let { friUserId } = JSON.parse(localStorage.getItem('userInfo') || '{}');
  const { meetingItem, dispatch } = props || {};


  // 更多操作
  const actions = (meetingItem?.isDisable!=1||meetingItem?.status==1||meetingItem?.status==3)?[
    { label: '编辑', key: 'edit'},
    { label: '下架', key: 'offShelf'},
    { label: '删除', key: 'delete'},
  ]:[
    { label: '删除', key: 'delete'},
  ]

  // 非本人主持的取消以及从列表中删除
  const isDisableShare = [
    { label: '从列表中删除', key: 'deleteFromList' },
  ]


  // 编辑会议信息
  const editClickFn = () => {
    // 跳转会议编辑页
    history.push({
      pathname: `/UserInfo/CreateSpaceByPc/Meet`,
      query: {
        id: meetingItem && meetingItem.id,
      }
    })
  }

  // 下架会议确定按钮
  const offShelfOkFn = () => {
    return dispatch({
      type: 'userInfoStore/editSpaceInfo',
      payload: {
        id: meetingItem && meetingItem.id, // 会议id
        updateUserId: friUserId, // 操作人用户ID
        isDisable: 1, // 1是下架，下架时必传
      }
    }).then(res => {
      const { code, msg } = res || {};
      if(res && code == 200) {
        message.success('下架成功')
        props.refreshFn && props.refreshFn() // 刷新页面
      } else {
        message.error(msg || '数据加载失败')
      }
      return true
    }).catch(err => {
      console.log(err)
    })
  }

  // 删除会议确定按钮
  const deleteOkFn = () => {
    return dispatch({
      type: 'userInfoStore/editSpaceInfo',
      payload: {
        id: meetingItem && meetingItem.id, // 会议id
        updateUserId: friUserId, // 操作人用户ID
        isDel: 1, // 1是删除，删除时必传
      }
    }).then(res => {
      const { code, msg } = res || {};
      if(res && code == 200) {
        message.success('删除成功')
        props.refreshFn && props.refreshFn() // 刷新页面
      } else {
        message.error(msg || '数据加载失败')
      }
      return true
    }).catch(err => {
      console.log(err)
    })
  }

  // 从列表中删除
  const deleteFromListFn = () => {
    return dispatch({
      type: 'userInfoStore/deleteSpaceFromList',
      payload: {
        spaceId: meetingItem && meetingItem.id, // 会议id
      }
    }).then(res => {
      const { code, msg } = res || {};
      if(res && code == 200) {
        message.success('从列表删除成功')
        props.refreshFn && props.refreshFn() // 刷新页面
      } else {
        message.error(msg || '数据加载失败')
      }
      return true
    }).catch(err => {
      console.log(err)
    })
  }

  // 点击更多操作
  const onClickMoreOperate = ({key}) => {
    if (key == 'edit') {
      editClickFn()
    } else if (key == 'offShelf') {
      offShelfComfirmModalShow()
    } else if (key == 'delete') {
      deleteComfirmModalShow()
    } else if (key == 'deleteFromList') {
      deleteFromListComfirmModalShow()
    }
  }

  // 下架提示弹窗
  const offShelfComfirmModalShow = () => {
    Modal.confirm({
      title: '确定下架',
      content: `确定要下架这个会议吗？`,
      onOk() {
        return offShelfOkFn()
      },
      onCancel() {},
    });
  }

  // 删除提示弹窗
  const deleteComfirmModalShow = () => {
    Modal.confirm({
      title: '确定删除',
      content: `确定要删除这个会议吗？`,
      onOk() {
        return deleteOkFn()
      },
      onCancel() {},
    });
  }

  // 从列表中删除提示弹窗
  const deleteFromListComfirmModalShow = () => {
    Modal.confirm({
      title: '确定删除',
      content: `确定要从列表中删除这个会议吗？`,
      onOk() {
        return deleteFromListFn()
      },
      onCancel() {},
    });
  }

  return <>
    <Dropdown menu={{
      items: (meetingItem?.hostUserId==friUserId)?actions:isDisableShare,
      onClick: onClickMoreOperate,
    }} trigger={['click']}>
      <span className={styles.more_operate_btn}>更多操作</span>
    </Dropdown>
  </>
}
export default connect(({ square, userInfoStore, loading }: any) => ({ square, userInfoStore, loading }))(Index)
