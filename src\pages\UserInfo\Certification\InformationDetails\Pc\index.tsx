/**
 * @Description: 实名认证-认证信息（PC）
 */
import React, { useState, useEffect } from 'react';
import { connect, history } from 'umi';
import { Button, message, Image, Spin } from 'antd';
import styles from './index.less';

// 图片icon
import right_arrow_blue_1 from '@/assets/GlobalImg/right_arrow_blue_1.png' // 蓝色右箭头

import PcHeader from '@/componentsByPc/PcHeader' // 公共导航组件

const Index: React.FC = (props: any) => {
  const { loading, dispatch } = props;

  const [detailsState, setDetailsState] = useState({}) // 认证详情
  const [previewImageVisible, setPreviewImageVisible] = useState(false) // 查看认证证明弹窗

  useEffect(() => {
    getSelfAuthInfo()
  }, [])

  // 个人实名认证详情-星球-实名认证版本
  const getSelfAuthInfo = () => {
    dispatch({
      type: 'userInfoStore/getSelfAuthInfo',
      payload: {}
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200 && content) {
        setDetailsState(content || {})
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch(err => {})
  }

  // 点击修改认证信息按钮
  const onClickChangeCertificationBtn = () => {
    history.push('/UserInfo/Certification/SelectRoles')
  }

  // 点击查看证明按钮
  const onClickLookImgBtn = () => {
    setPreviewImageVisible(true)
  }

  // 返回
  const goBack = () => {
    if (history.length > 1) {
      history.goBack()
    } else {
      history.replace('/')
    }
  }

  // loading
  const loadingGetSelfAuthInfo = !!loading.effects['userInfoStore/getSelfAuthInfo']

  return (
    <Spin wrapperClassName={styles.spin} spinning={loadingGetSelfAuthInfo}>
      <div className={styles.container}>
        {/* 头部 */}
        <PcHeader />
        {/* 内容 */}
        <div className={styles.content}>
          <div className={styles.content_inner}>
            {/* 标题导航条 */}
            <div className={styles.nav_bar}>
              <i onClick={goBack}></i>
              <span>个人认证</span>
            </div>

            <div className={styles.wrap}>
              <h1 className={styles.content_title}>{detailsState.identityTypeVal}认证信息</h1>
              <p className={styles.content_tips}>所有信息仅用于资料认证，我们将严格保护您的隐私</p>
              <div className={styles.info_item_wrap}>
                <div className={styles.info_item_label}>真实姓名</div>
                <div className={styles.info_item_value}>{detailsState.realName}</div>
              </div>
              <div className={styles.info_item_wrap}>
                <div className={styles.info_item_label}>手机号</div>
                <div className={styles.info_item_value}>{detailsState.phone}</div>
              </div>
              <div className={styles.info_item_wrap}>
                <div className={styles.info_item_label}>单位/学校</div>
                <div className={styles.info_item_value}>{detailsState.unitWorkName}</div>
              </div>
              <div className={styles.info_item_wrap}>
                <div className={styles.info_item_label}>职务/专业</div>
                <div className={styles.info_item_value}>{detailsState.specialityName || '-'}</div>
              </div>
              <div className={styles.info_item_wrap}>
                <div className={styles.info_item_label}>认证证明</div>
                {
                  detailsState.credentialUrlViews && detailsState.credentialUrlViews.length > 0 ?
                    <div className={styles.info_item_img_wrap} onClick={onClickLookImgBtn}>
                      <span>查看图片</span>
                      <img src={right_arrow_blue_1} width={16} height={16} alt=""/>
                    </div>
                    :
                    <div className={styles.info_item_value}>-</div>
                }
              </div>
            </div>
          </div>
        </div>
        {/* 按钮 */}
        <div className={styles.footer}>
          <div className={styles.footer_content}>
            <div className={styles.footer_content_right}>
              <Button type="primary" size="large" onClick={onClickChangeCertificationBtn}>修改认证信息</Button>
            </div>
          </div>
        </div>
      </div>

      {/* 认证证明预览 */}
      {
        detailsState.credentialUrlViews && detailsState.credentialUrlViews.length > 0 &&
        <div style={{display: 'none'}}>
          <Image.PreviewGroup preview={{
            visible: previewImageVisible,
            onVisibleChange: value => {
              setPreviewImageVisible(value)
            }
          }}>
            {
              detailsState.credentialUrlViews.map(item => {
                return <Image src={item}/>
              })
            }
          </Image.PreviewGroup>
        </div>
      }
    </Spin>
  )
}

export default connect(({ userInfoStore, loading }: any) => ({ userInfoStore, loading}))(Index)
