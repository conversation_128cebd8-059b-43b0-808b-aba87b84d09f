.spin {
  height: 100%;
  :global {
    .ant-spin-container {
      height: 100%;
    }
  }
}
:global {
  .ka-wrapper, .ka-content {
    height: 100%;
  }
}
.nav_bar_right {
  display: flex;
  align-items: center;
  .nar_bar_link_btn {
    font-size: 15px;
    color: #0095FF;
    margin-right: 16px;
  }
  .nar_bar_btn {
    display: block;
    width: 54px;
    height: 29px;
    background: #0095FF;
    font-size: 15px;
    color: #fff;
    line-height: 30px;
    border-radius: 15px;
    text-align: center;
    &.disabled {
      background: #8DCDF7;
      color: #E8F4FC;
    }
  }
}
.container {
  padding: 44px 0 0;
  height: calc(100% - 45px);
  overflow-y: auto;
}

.bottom_tips_bar {
  position: relative;
  z-index: 991;
  width: 100%;
  background: #FFF7DA;
  border-top: 1px solid #EFD989;
  border-bottom: 1px solid #EFD989;
  padding: 7px 28px 6px 10px;
  line-height: 18px;
  word-break: break-all;
  font-size: 12px;
  margin-top: -1px;
  color: #8C772B;
  .bar_btn_wrap {
    position: absolute;
    width: 12px;
    height: 12px;
    right: 8px;
    top: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 9px;
  }
}

.select_kingdom_box {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  padding: 17px 12px;
  border-bottom: 1px solid #f5f6f8;
  .select_left {
    display: flex;
    flex-wrap: nowrap;
    flex-shrink: 0;
    overflow: hidden;
    align-items: center;
    column-gap: 4px;
    font-size: 14px;
    color: #000;
    & > i {
      width: 16px;
      height: 16px;
      &.select_left_icon_1 {
        background: url("../../../../../assets/GlobalImg/associated.png") no-repeat center;
        background-size: 100% 100%;
      }
      &.select_left_icon_2 {
        background: url("../../../../../assets/GlobalImg/black_arrow.png") no-repeat center;
        background-size: 100% 100%;
      }
    }
    & > span {
      height: 20px;
      line-height: 21px;
    }
    .divider {
      margin-left: 4px;
      width: 0;
      height: 16px;
      border-left: 1px solid #d9d9d9;
    }
  }
  .select_right {
    margin-left: 8px;
    background: #F5F6F8;
    font-size: 13px;
    color: #000;
    line-height: 18px;
    padding: 2px 12px;
    border-radius: 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .select_right_default {
    font-size: 12px;
    color: #999;
    margin-left: 8px;
    height: 17px;
    line-height: 19px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.form_content {
  padding: 0 12px;
  :global {
    .adm-list-item {
      padding-left: 0;
    }

    .adm-list-item-content {
      border-top: none;
      border-bottom: 1px solid #F5F6F8;
    }

    .adm-list-default .adm-list-body {
      border: none;
    }

    .adm-list-item-content-main {
      padding: 14px 0;
      box-sizing: border-box;
    }

    .adm-input-element::placeholder {
      font-size: 15px;
      color: #AAA;
    }

    .ant-form-item-explain-error {
      font-size: 12px;
      color: #FF5F57;
      line-height: 14px;
    }
    .adm-input-element {
      font-size: 15px;
      color: #000;
    }
  }
}

.text_img_list_type_wrap {
  padding: 16px 0 12px;

  :global {

    .adm-radio.adm-radio-checked .adm-radio-icon {
      background-color: #0095FF;
      border-color: #0095FF;
    }

    .adm-radio-icon {
      width: 16px;
      height: 16px;
    }

    .adm-radio-content {
      font-size: 15px;
      color: #000;
      line-height: 21px;
    }

    label {
      margin-right: 12px;
    }
  }

}

.init_img_list {
  width: 100%;
  height: 64px;
  border-radius: 4px;
  border: 1px solid #EBEBEB;
  padding: 8px 0 0 8px;
  display: flex;
  flex-wrap: nowrap;
  .init_bg {
    flex-shrink: 0;
  }

  .init_info {
    flex: 1;
    overflow: hidden;
    padding: 0 8px 8px 8px;
    .init_info_title {
      font-size: 13px;
      color: #666;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .init_info_btn {
      display: flex;
      align-items: center;
      font-size: 13px;
      color: #0095FF;
    }
  }
}

.link_img_wrap {
  :global {
    .ant-upload {
      display: block;
      width: 100%;
    }
  }
  & > span {
    display: block;
    width: 100%;
  }
  .upload_box {
    width: 100%;
    height: 142px;
    background: #F8F8F8;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px 4px 0 0;
    .upload_img {
      width: 100%;
      height: 100%;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
    }
    .add_img {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 13px;
      color: #999;
      line-height: 18px;
      .init_upload_img {
        width: 48px;
        height: 48px;
        margin-bottom: 8px;
      }
    }
  }
  .img_link_text {
    height: 34px;
    padding: 0 4px;
    background: #F5F6F8;
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #0095FF;
  }
}
