/**
 * @Description: 中转页，为了兼容旧的路由/PlanetChatRoom/RoomId，跳转到会议详情或直播详情
 */
import React, { useEffect, useState } from 'react'
import { history, connect } from 'umi'
import { Spin } from 'antd'

import CommonTipsModal from '@/pages/PlanetChatRoom/Meet/components/ModalComponents/CommonTipsModal' // 会议详情-公共提示弹窗

const Index: React.FC = (props) => {
  const { dispatch } = props
  const [commonTipsModalVisible, setCommonTipsModalVisible] = useState('')

  useEffect(() => {
    // 获取空间海报信息
    getSpacePosterInfo()
  }, [])

  // 获取空间海报信息
  const getSpacePosterInfo = () => {
    const RoomId = props?.match?.params?.RoomId;
    dispatch({
      type: 'userInfoStore/getSpacePosterInfo',
      payload: {
        spaceId: RoomId, // 空间ID
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200 && content) {
        // starSpaceType空间类型，1 直播，2 会议
        if (content.starSpaceType == 1) {
          history.push(`/PlanetChatRoom/Live/${RoomId}`)
        } else if (content.starSpaceType == 2) {
          history.push(`/PlanetChatRoom/Meet/${RoomId}`)
        }
      } else {
        setCommonTipsModalVisible(msg || '数据加载失败')
      }
    }).catch(err => {})
  }

  // 关闭弹窗
  const commonTipsModalClose = () => {
    if (history.length > 1) {
      history.goBack()
    } else {
      history.replace('/')
    }
  }

  return (
    <>
      <Spin spinning={true}>
        <div style={{width: '100%', height: '100vh', background: '#fff'}}></div>
      </Spin>
      {/* [空间提示弹窗] - 下架等状态提示 */}
      {
        !!commonTipsModalVisible &&
        <CommonTipsModal
          visible={!!commonTipsModalVisible}
          onClickLeftBtn={commonTipsModalClose} // 点击右边按钮
          onClickRightBtn={commonTipsModalClose} // 点击右边按钮
          title={commonTipsModalVisible} // 标题
          leftBtnText={'返回'} // 左边按钮文案
          rightBtnText={'我知道了'} // 右边按钮文案
        />
      }
    </>
  )
}

export default connect(({ loading }: any) => ({ loading }))(Index)
