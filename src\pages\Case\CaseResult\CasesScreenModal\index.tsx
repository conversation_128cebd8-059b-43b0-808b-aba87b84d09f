import { useState, useEffect, useImperativeHandle } from "react";
import { connect } from "umi";
import styles from './index.less';
import { Spin } from 'antd';
import { DatePicker, Toast } from 'antd-mobile'


// 筛选选中初始化数据
const initState = {
  checkDepSubject: [],     // 选中学科
  checkAbilityLevel: [],   // 选中能力等级
  checkAchievement: [],     // 选中病例成就
  startDate: null,           // 开始时间
  endDate: null,             // 结束时间
}

// 筛选数据
const initData = {
  depSubject: [],
  difficultLevel: [],
  achievement: [],
}
// 
// @ts-ignore
const Index = (props) => {
  const { dispatch, loading, onClickHideScreenFun, cases } = props;
  const [dataSource, setDataSource] = useState(initData);  // 筛选数据
  const [state, setState] = useState(initState)  // 筛选条件
  const { checkDepSubject, checkAbilityLevel, checkAchievement, startDate, endDate } = state || {}
  const {
    depSubject,
    difficultLevel,
    achievement,
  } = dataSource || {}

  const [startDateVisible, setStartDateVisible] = useState(false); // 开始日期弹框
  const [endDateVisible, setEndDateVisible] = useState(false); // 结束日期弹框
  useEffect(() => {
    getExcellentCaseDict()
  }, [])
  useEffect(() => {
    const { checkDepSubject, checkAbilityLevel, checkAchievement, startDate, endDate } = cases || {}
    setState({
      checkDepSubject,     // 选中学科
      checkAbilityLevel,   // 选中能力等级
      checkAchievement,     // 选中病例成就
      startDate,           // 开始时间
      endDate,             // 结束时间
    })
  }, [])

  // 暴露给父组件的方法
  useImperativeHandle(props.onRef, () => {
    return {
      clearScreenFun: clearScreenFun,
    }
  })

  // 清空筛选条件
  const clearScreenFun = () => {
    setState(initState)
  }

  // 获取字典
  const getExcellentCaseDict = () => {
    dispatch({
      type: "cases/getExcellentCaseDict",
      payload: {}
    }).then((res: any) => {
      console.log(res)
      const { content, code } = res || {}
      if (code == 200) {
        const { depSubject, difficultLevel, achievement } = content || {}
        difficultLevel.unshift({ code: 0, name: "全部", iconName: null, children: null })
        depSubject.unshift({ code: 0, name: "全部", iconName: "", children: null })
        achievement.unshift({ code: 0, name: "全部", iconName: "", children: null })
        setDataSource(content)
      } else {

      }
    }).catch((err: any) => {
    })
  }

  // 学科选中 
  const onDepSubjectFun = (val) => {
    let arr = checkDepSubject || [];
    if (val == 0) {
      arr = [0]
    } else {
      if (arr && arr.length) {
        let idx = arr.findIndex(v => v == val)
        let _idx = arr.findIndex(v => v == "0")
        if (idx > -1) {
          arr.splice(idx, 1)
        } else {
          if (_idx > -1) {
            arr.splice(_idx, 1)
          }
          arr.push(val)
        }
      } else {
        arr.push(val)
      }
    }
    setState({
      ...state,
      checkDepSubject: arr
    })
  }

  // 能力等级
  const onAbilityLevelFun = (val) => {
    let arr = checkAbilityLevel || [];
    if (val == 0) {
      arr = [0]
    } else {
      if (arr.length) {
        let idx = arr.findIndex(v => v == val)
        let _idx = arr.findIndex(v => v == "0")
        if (idx > -1) {
          arr.splice(idx, 1)
        } else {
          if (_idx > -1) {
            arr.splice(_idx, 1)
          }
          arr.push(val)
        }
      } else {
        arr.push(val)
      }
    }
    setState({
      ...state,
      checkAbilityLevel: arr
    })
  }

  // 病例成就选中
  const onAchievementFun = (val) => {
    let arr = checkAchievement || [];
    if (val == 0) {
      arr = [0]
    } else {
      if (arr.length) {
        let idx = arr.findIndex(v => v == val)
        let _idx = arr.findIndex(v => v == "0")
        if (idx > -1) {
          arr.splice(idx, 1)
        } else {
          if (_idx > -1) {
            arr.splice(_idx, 1)
          }
          arr.push(val)
        }
      } else {
        arr.push(val)
      }
    }
    setState({
      ...state,
      checkAchievement: arr
    })
  }

  // 选择开始时间
  const onDateStartChange = (val: any) => {
    let startYear = val.getFullYear();
    if (endDate && startYear > endDate) {
      Toast.show('开始日期大于结束日期～')
    } else {
      setState({
        ...state,
        startDate: startYear
      })
    }
  }
  // 选择开始时间
  const onDateEndChange = (val: any) => {
    let endYear = val.getFullYear();
    if (startDate && endYear < startDate) {
      Toast.show('结束日期小于开始日期～')
    } else {
      setState({
        ...state,
        endDate: endYear
      })
    }
  }

  // 点击确定
  const onOK = () => {
    if (!checkDepSubject.length && !checkAbilityLevel.length && !checkAchievement.length && !startDate && !endDate) {
      Toast.show('请选择筛选条件')
      return
    }
    if ((startDate && !endDate) || (!startDate && endDate)) {
      Toast.show('请选择日期区间～')
      return
    }
    onClickHideScreenFun(1, state)
  }

  // 点击取消关闭抽屉
  const onCloseFun = () => {
    setState({ ...initState })
    onClickHideScreenFun(2, initState)
  }
  const load = !!loading.effects['cases/getExcellentCaseDict']// 病例字典接口loading
  return (
    <Spin spinning={load}>
      <div className={styles.screen_wrap}>
        <div className={styles.screen_container}>
          <div className={styles.screen_wrap_title}>学科</div>
          <div className={styles.screen_wrap_content}>
            {
              Array.isArray(depSubject) && depSubject.length ? <>
                {
                  depSubject.map((item: any, index) => {
                    return (<div className={styles.screen_child_wrap} key={index}>
                      <div
                        className={checkDepSubject && checkDepSubject.length && checkDepSubject.findIndex(v => v == item.code) > -1 ? styles.screen_check_child_word : styles.screen_child_word}
                        onClick={() => { onDepSubjectFun(item.code) }}
                      >{item.name}</div>
                    </div>)
                  })
                }
              </> : null
            }
          </div>
          <div className={styles.screen_wrap_title_one}>难度等级</div>
          <div className={styles.screen_wrap_content}>
            {
              Array.isArray(difficultLevel) && difficultLevel.length ? <>
                {
                  difficultLevel.map((item: any, index) => {
                    return (
                      <div className={styles.screen_child_wrap} key={index}>
                        <div
                          className={checkAbilityLevel && checkAbilityLevel.length && checkAbilityLevel.findIndex(v => v == item.code) > -1 ? styles.screen_check_child_word : styles.screen_child_word}
                          onClick={() => { onAbilityLevelFun(item.code) }}
                        >{item.name}</div>
                      </div>
                    )

                  })
                }
              </> : null
            }
          </div>
          <div className={styles.screen_wrap_title_one}>日期</div>
          <div className={styles.screen_wrap_content_picker}>
            <div className={styles.screen_picker} onClick={() => { setStartDateVisible(true) }}>
              {startDate ? <span className={styles.select_date}>{startDate}年</span> : "开始日期"}
            </div>
            <DatePicker
              visible={startDateVisible}
              onClose={() => { setStartDateVisible(false) }}
              precision='year'
              onConfirm={val => { onDateStartChange(val) }}
            />
            <div className={styles.screen_picker_line}></div>
            <div className={styles.screen_picker} onClick={() => { setEndDateVisible(true) }}>
              {endDate ? <span className={styles.select_date}>{endDate}年</span> : "结束日期"}
            </div>
            <DatePicker
              visible={endDateVisible}
              onClose={() => {
                setEndDateVisible(false)
              }}
              precision='year'
              onConfirm={val => { onDateEndChange(val) }}
            />
          </div>
          <div className={styles.screen_wrap_title_one}>病例成就</div>
          <div className={styles.screen_wrap_content_case}>
            {
              Array.isArray(achievement) && achievement.length ? <>
                {
                  achievement.map((item: any, index) => {
                    return (
                      <div className={styles.screen_cases_child_wrap} key={index}>
<div
                      className={checkAchievement && checkAchievement.length && checkAchievement.findIndex(v => v == item.code) > -1 ? styles.screen_check_cases_word : styles.screen_cases_word}
                      onClick={() => { onAchievementFun(item.code) }}
                    >{item.name}</div>
                      </div>
                    )
                    
                  })
                }
              </> : null
            }

          </div>
        </div>
        <div className={styles.screen_wrap_footer}>
          <div className={styles.screen_wrap_footer_close} onClick={() => { onCloseFun() }}>取消</div>
          <div className={styles.screen_wrap_footer_confirm} onClick={() => { onOK() }}>确认</div>
        </div>
      </div>
    </Spin>
  )
}


export default connect(({ cases, loading }: any) => ({ cases, loading }))(Index)