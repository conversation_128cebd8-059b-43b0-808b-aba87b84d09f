import React, {useState,} from 'react';
import {
  getCameralistArr,
  getHandUpRemoteStreamList,
  getHostRemoteStreamConfig,
  getIsModeMatrixCameraRemoteStreamList,
  getShareRemoteStreamConfig,
  getUserCameraRemoteStreamList,
  getUserInfoData,
} from '@/utils/utilsByTRTC';
import {connect} from 'umi';
import styles from './index.less';
import Stream from '@/componentsByTRTC/Stream';

type propsType = {
  global: any;
  onRefByVerticalLiveRoom: any;
  sendMessageByIm: any;
  localStreamConfig: any;
  remoteStreamConfigList: any;
  RTC: any;
  shareRTC: any;
  isJoined: boolean;
  isPublished: boolean;
  handleJoin: any;
  handleLeave: any;
  onChange: any;
  spaceId: any;
  openCloseHandUp: any;
  liveRecord: any;
  getGuestList: any;
  getSpaceInfo: any;
  onClickLianMai: any;
  changeUrlParams: any;
  elapsedTime: any;
  shareOnClick: any;
  onClickBack: any;
  isHorizontalLive: any;
};

const Index: React.FC<propsType> = (props) => {
  const {localStreamConfig, RTC, remoteStreamConfigList, PlanetChatRoom, key} = props || {};

  const {SpaceInfo, handUpList} = PlanetChatRoom || {};

  const {hostUserInfo} = SpaceInfo || {};

  const [isShowCameraList, setIsShowCameraList] = useState(true);

  const userInfoData = getUserInfoData();

  const shareRemoteStreamConfig = getShareRemoteStreamConfig(SpaceInfo, remoteStreamConfigList);

  const hostRemoteStreamConfig = getHostRemoteStreamConfig(
    SpaceInfo,
    hostUserInfo,
    remoteStreamConfigList,
  );

  const userCameraRemoteStreamList = getUserCameraRemoteStreamList(
    SpaceInfo,
    remoteStreamConfigList,
  );

  const isModeMatrixCameraRemoteStreamList = getIsModeMatrixCameraRemoteStreamList(
    SpaceInfo,
    hostUserInfo,
    handUpList,
    userCameraRemoteStreamList,
  );

  const handUpRemoteStreamList = getHandUpRemoteStreamList(
    SpaceInfo,
    handUpList,
    userCameraRemoteStreamList,
  );

  let cameralistArr = getCameralistArr(
    hostRemoteStreamConfig,
    localStreamConfig,
    handUpRemoteStreamList,
    isModeMatrixCameraRemoteStreamList,
  );
  const [randomNum, setRandomNum] = useState(Math.random());
  return (
    <>
      <div className={styles.StreamSpanWarp}>
        {localStreamConfig && localStreamConfig.hasAudio && (
          <div className={styles.StreamSpan}>
            <Stream
              key={`${localStreamConfig.stream.getUserId()}_${localStreamConfig.stream.getType()}_${key}_StreamAudioList_${randomNum}`}
              stream={localStreamConfig.stream}
              config={localStreamConfig}
              init={(dom) => RTC && RTC.playStream(localStreamConfig.stream, dom)}
              // onChange={e => handleChangeByLocalStreamConfig(e)}
            ></Stream>
          </div>
        )}
        {Array.isArray(userCameraRemoteStreamList) &&
          userCameraRemoteStreamList.map((itemByStream) => {
            return (
              <div className={styles.StreamSpan}>
                <Stream
                  key={`${itemByStream.stream.getUserId()}_${itemByStream.stream.getType()}_${key}_StreamAudioList_list_${randomNum}`}
                  stream={itemByStream.stream}
                  config={itemByStream}
                  init={(dom) => RTC && RTC.playStream(itemByStream.stream, dom)}
                ></Stream>
              </div>
            );
          })}
      </div>
    </>
  );
};
export default connect(({ global, PlanetChatRoom, loading }: any) => ({
  global,
  PlanetChatRoom,
  loading,
}))(Index);
