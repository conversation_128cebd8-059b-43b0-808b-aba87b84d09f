import React from 'react';
import styles from './index.less'; // 导入组件样式
import {processNames, randomColor} from '@/utils/utils'; // 导入相关的实用函数
import _ from 'lodash';

const Avatar = ({userInfo, size, isPc}) => {
  let containerStyle = {
    background: userInfo && userInfo.wxUserId ? randomColor(userInfo.wxUserId) : 'none',
    // width:  size ? _.floor(size / 16,2) + 'rem' : '1.2rem',
    // height: size ? _.floor(size / 16,2) + 'rem' : '1.2rem',
  };
  if (isPc) {
    containerStyle = {
      background: userInfo && userInfo.wxUserId ? randomColor(userInfo.wxUserId) : 'none',
      width: '100%',
      height: '100%',
    };
  }
  return (
    <div style={containerStyle} className={styles.video_Title_box_left_avatar}>
      {!!userInfo && userInfo.imagePhotoPathShow && (
        <img
          className={styles.video_Title_box_left_avatar_img}
          src={userInfo.imagePhotoPathShow}
          alt=""
        />
      )}
      {!!userInfo && !userInfo.imagePhotoPathShow && userInfo.headUrlShow && (
        <img className={styles.video_Title_box_left_avatar_img} src={userInfo.headUrlShow} alt=""/>
      )}
      {userInfo && !userInfo.headUrlShow && !userInfo.imagePhotoPathShow && (
        <div
          className={styles.head_sculpture_name}
          style={{
            fontSize: _.floor(size / 1.7, 2),
            background: userInfo && userInfo.wxUserId ? randomColor(userInfo.wxUserId) : 'none',
          }}
        >
          {userInfo && processNames(userInfo.name ? userInfo.name : userInfo.userName)}
        </div>
      )}
    </div>
  );
};

export default Avatar;
