/**
 * @Description: 选择预约时间
 */
import { PickerView, Toast } from 'antd-mobile';
import { connect, history, useRouteMatch } from 'umi';
import styles from './index.less';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs'
import goBackIcon from '@/assets/GlobalImg/go_back.png'; // 返回按钮小图标

const durationColumns = [
  [
    { label: '30分钟', value: '30' },
    { label: '1小时', value: '60' },
    { label: '2小时', value: '120' },
    { label: '3小时', value: '180' },
  ]
]


const Index: React.FC = (props: any) => {
  const {path} = useRouteMatch();
  const { dispatch, goBack, userInfoStore } = props;
  const { expectDuration, selectCreateType, selectedKingdom, selectedCompere,appointmentStartTime, selectedGuest, spaceName, spaceCoverUrl, spaceAdvertisingUrl, spacePasswordText, spaceVideoId, spaceIntroduceVal, spaceFromEnter, isEditSpace,isAutoRecord,allowApplications } = userInfoStore || {}; // 从仓库中取值
  const { creatTabSelectDate, spaceTypeId, spectatorType,  selectedKingdomAudience, enterpriseUserSelectData,starSpaceType } = userInfoStore || {};
  const { type } = creatTabSelectDate && creatTabSelectDate || {};
  const [ duration, setDuration ] = useState<any>('30'); // 预约时间列表选择相关数据（月

  useEffect(() => {
    if(!!expectDuration){
      setDuration([expectDuration,0]);
    }
  },[expectDuration])

  // 预约确定按钮
  const submitbtn = () => {
    dispatch({
      type:'userInfoStore/setTaskListState',
      payload: {
        expectDuration: Array.isArray(duration) ? duration[0] : null,
        createModalVisible:false,
      }
    })
  }

  // 选择预约时长
  const selectDuration = (value) => {
    setDuration(value);
  }

  const { pageType } = spaceFromEnter || {};
  return (
    <>
      <div className={styles.titleInfo}>
        <div className={styles.titleWarp}>
          <div
            className={styles.titleBackIcon}
            onClick={()=>{
              dispatch({
                type: 'userInfoStore/setTaskListState',
                payload: { createModalVisible:false, }
              })
            }}>
            <img src={goBackIcon} alt="" />
          </div>
          <div className={styles.titleText}>选择会议时长</div>
        </div>
      </div>
      <div className={styles.PickerViewDataByWarp}>
        <PickerView
          columns={durationColumns}
          defaultValue={['60']}
          onChange={(value)=>{ selectDuration(value) }}
          value={duration}
          style={{
            '--item-font-size': '20px',
          }}
        />
      </div>

      <div className={styles.btnAppointmentStartWarp}>
        <div className={styles.btnAppointmentStart} onClick={submitbtn}>确认</div>
      </div>
    </>
  )
}
export default connect(({ userInfoStore, loading }: any) => ({ userInfoStore, loading }))(Index)
