.drawer {
  :global {
    .ant-drawer-body {
      padding: 0;
      background: #141414;
    }
    // 隐藏popover箭头
    .ant-popover.ant-popover-placement-bottomRight {
      padding-top: 0;
    }
    .ant-popover-placement-bottomRight .ant-popover-arrow {
      display: none;
    }
  }
}

.spin {
  height: 100%;
  & > :global(.ant-spin-container) {
    height: 100%;
  }
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header_title_wrap {
  width: 100%;
  flex-shrink: 0;
  display: flex;
  flex-wrap: nowrap;
  align-items: flex-end;
  column-gap: 8PX;
  padding: 24PX 24PX 13PX;
  .title {
    font-size: 15PX;
    color: #fff;
    font-weight: 500;
    line-height: 21PX;
  }
  .text {
    font-size: 14PX;
    color: #999;
    line-height: 20PX;
  }
}

// 搜索框
.search_wrap {
  width: 100%;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  column-gap: 16PX;
  padding: 3PX 24PX 24PX;
  :global {
    .ant-input-affix-wrapper {
      flex: 1;
      background: rgba(255,255,255,0.2);
      border-radius: 23PX;
      padding: 0 12PX;
      border: 0;
      outline: 0;
      box-shadow: none;
    }
    .ant-input {
      height: 40PX;
      background: transparent;
      font-size: 14PX;
      color: #fff;
    }
    .ant-input-clear-icon {
      color: #fff;
    }
  }
  .search_cancel_btn {
    flex-shrink: 0;
    cursor: pointer;
    font-size: 14PX;
    color: #fff;
  }
}

.tabs_wrap {
  width: 100%;
  flex-shrink: 0;
  display: flex;
  padding: 0 24PX 16PX;
  column-gap: 16PX;
  .tabs_item {
    font-size: 15PX;
    color: #999;
    font-weight: 500;
    height: 28PX;
    line-height: 21PX;
    position: relative;
    cursor: pointer;
    &.checked {
      color: #fff;
      font-weight: 600;
      .tabs_bar {
        display: block;
        position: absolute;
        width: 12PX;
        height: 3PX;
        border-radius: 6PX;
        background: #fff;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
      }
    }
    .badge {
      position: absolute;
      right: -8PX;
      top: -2PX;
      min-width: 12PX;
      height: 12PX;
      line-height: 10PX;
      border-radius: 6PX;
      padding: 1PX;
      text-align: center;
      background: #FF180D;
      font-size: 9PX;
      color: #fff;
    }
  }
}

// 用户列表
.user_list_wrap {
  position: relative;
  flex: 1;
  padding: 0 20PX 16PX 24PX;
  overflow-y: auto;
  // 会议中用户
  .user_item_1 {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    padding: 8PX 0;
    .left {
      flex: 1;
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      overflow: hidden;
      padding-right: 8PX;
      .avatar_wrap {
        width: 24PX;
        height: 24PX;
        flex-shrink: 0;
        margin-right: 12PX;
      }
      .user_name {
        font-size: 14PX;
        color: #fff;
        margin-right: 8PX;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .user_role {
        flex-shrink: 0;
        height: 16PX;
        line-height: 16PX;
        border-radius: 2PX;
        padding: 0 2PX;
        font-size: 10PX;
        min-width: 18PX;
        text-align: center;
        &.audience {
          background: #FFF4EA;
          color: #FF922E;
        }
        &.guest {
          background: #E6F9F1;
          color: #06A777;
        }
        &.host {
          background: #E6F4FF;
          color: #0095FF;
        }
      }
    }
    .right {
      flex-shrink: 0;
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      column-gap: 12PX;
    }
  }

  // 申请进入用户
  .user_item_2 {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    padding: 8PX 0;
    .left {
      flex: 1;
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      overflow: hidden;
      padding-right: 8PX;
      .avatar_wrap {
        width: 24PX;
        height: 24PX;
        flex-shrink: 0;
        margin-right: 12PX;
      }
      .user_name {
        font-size: 14PX;
        color: #fff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .right {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      column-gap: 24PX;
      padding-right: 4PX;
      & > span {
        font-size: 14PX;
        color: #0095FF;
        cursor: pointer;
      }
    }
  }
}

// 底部按钮
.btn_wrap {
  width: 100%;
  flex-shrink: 0;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  column-gap: 16PX;
  padding: 8PX 24PX 24PX;
  .btn_left, .btn_right {
    flex: 1;
    height: 40PX;
    line-height: 40PX;
    border-radius: 20PX;
    text-align: center;
    font-size: 16PX;
    cursor: pointer;
  }
  .btn_left {
    background: #EDF9FF;
    color: #0095FF;
  }
  .btn_right {
    background: #0095FF;
    color: #fff;
  }
}

