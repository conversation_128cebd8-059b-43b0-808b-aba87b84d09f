/**
 * @Description: 首页搜索结果页
 */
import React, { useState, useEffect, lazy, Suspense } from 'react'
import { getOperatingEnv, useDebounce } from '@/utils/utils'

// import PcIndex from './PcIndex'
// import MobileIndex from './MobileIndex'
const PcIndex = lazy(()=> import('./PcIndex'))
const MobileIndex = lazy(()=> import('./MobileIndex'))

const Index: React.FC = () => {

  // 1 PC，2 移动端
  const [pageType, setPageType] = useState(null)

  // ① 判定当前页面视口是否小于750 如果小于750则为移动端
  let updateType = () => {
    // 获取当前操作环境 1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用
    const env = getOperatingEnv()
    const type = env == 4 ? 1 : 2
    setPageType(type)
  }
  updateType = useDebounce(updateType, 100)

  useEffect(() => {
    // ① 判定当前页面视口是否小于750 如果小于750则为移动端
    updateType()

    window.addEventListener('resize', updateType, {passive: true})
    return () => {
      window.removeEventListener('resize', updateType)
    }
  }, [])

  return (
    <Suspense fallback={<div></div>}>
      {
        pageType == 1 ? <PcIndex/>
          : pageType == 2 ? <MobileIndex/>
          : null
      }
    </Suspense>
  )
}

export default Index
