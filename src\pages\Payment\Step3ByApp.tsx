import React, { useState,useEffect } from 'react';
import { history,connect } from 'umi';
import styles from './Step3.less';
import classNames from "classnames";
import { getOrderIsPay } from "@/services/payment";
import {getOperatingEnv} from "@/utils/utils";

const Step3ByApp: React.FC = (props) => {
  let envByPage = getOperatingEnv()
  let pollingId = null; // 支付状态计时器id
  const { match: { params: { orderId } }, location } = props

  useEffect(() => {
    initializeData()
  }, []);

  const initializeData = async ()=>{
    let { match: { params: { orderId } },location } = props
    let { query:query_location } = location || {}
    let { code:code_query, env: env_query} = query_location || {}
    let env =  getOperatingEnv()
    if (orderId && (env == 5 || env == 6)) {
      getOrderIsPayByCallBack()
    }  else {
      if (env_query == 5) {
        setTimeout(()=>{
          window.location.href = 'friday://'
        },1500)
      } else if (env_query == 6) {
        setTimeout(()=>{
          window.location.href = 'jwsmed://'
        },1500)
      }
    }
  }

  // [支付]PC扫码支付后没有支付回调需要接口轮询判定支付状态
  // 当支付二维码生成之后每5秒获取状态一次

  // 修改设置轮询器在getOrderIsPayByCallBack调用之后
  // 0到1分钟每10秒钟调用g一次getOrderIsPay
  // 1到2分钟每20秒调用一次getOrderIsPay
  // 2到5分钟每30秒调用一次getOrderIsPay
  // 五分钟以后每60秒调用一次getOrderIsPay

  const getOrderIsPayByCallBack = async (interval) => {
    const startTime = Date.now()
    const { match: { params: { orderId } }, location } = props
    if(!!pollingId){ clearInterval(pollingId);pollingId = null }
    // 定义轮询函数
    const polling = async () => {
      const dataByOrder = await getOrderIsPay({ orderId: orderId })

      if (dataByOrder && dataByOrder.code === 200) {
        const {
          payFlag, // : true已完成 false未完成
          payType  // : 0 无此订单  1微信小程序 2微信H5 3微信二维码 4对公账户 5其他浏览器
        } = dataByOrder?.content || {}

        if (payType == 3 && payFlag) {
          // 支付完成,跳转到支付完成页面
          goStep3();
          // 清除轮询定时器
          clearInterval(pollingId)
        }else if(payType == 2 && payFlag){
          // 支付完成,跳转到支付完成页面
          goStep3();
          // 清除轮询定时器
          clearInterval(pollingId)
        }
      }
    }
    // 开始轮询，每 5 秒钟调用一次 polling 函数
    pollingId = setInterval(polling, 5000)
  }

  // 支付完成携带订单id
  const goStep3 = () => {
    const { match: { params: { orderId } } } = props
    let env = getOperatingEnv() // 获取当前操作环境 1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用
    if (env == 1) {

    }else {
      // 支付完成,跳转到支付完成页面
      history.replace(`/Payment/Step3/${orderId}?random=${Math.random()}`)
    }
  }

  return (
    <div>
      <div>
        <div className={styles.Mobile_Wrap}>
          <div className={styles.Mobile_content}>
            <div className={styles.Mobile_content_title}>
              <div className={classNames({
                      [styles.Mobile_content_title_status_icon]:true,
                      [styles.Mobile_content_title_status_icon_Success]:true,
                }
                )}></div>
              <div className={styles.Mobile_content_title_status_text}>
                {envByPage == 5 ? "支付中" : '支付完成'}
              </div>
            </div>
            <div className={styles.Mobile_content_title_status_desc}>
              {envByPage == 5 ? "如您已完成支付,点击支付完成查验支付结果" : '您已支付完成,点击下方按钮返回App,查验支付结果'}
            </div>

            <div className={styles.Mobile_content_btn_wrap}>
              {envByPage == 5 ?
              <div onClick={()=>{
                history.replace(`/Payment/Step3/${orderId}?random=${Math.random()}`)
              }} className={styles.Mobile_content_btn}>
                支付完成
              </div>
                :
                <div onClick={()=>{
                  window.location.href = 'friday://'
                }} className={styles.Mobile_content_btn}>
                  返回App
                </div>
              }
            </div>
          </div>
        </div>
      </div>

    </div>
  );
};

export default connect(({  }: any) => ({}))(Step3ByApp)
