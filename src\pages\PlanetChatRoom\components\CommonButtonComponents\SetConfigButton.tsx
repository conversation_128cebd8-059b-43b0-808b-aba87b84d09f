// 展开或收起设置列表
// SetConfigButton.js
import React from 'react';
import styles from './index.less';  // 引入自定义样式

const SideListTypeForSettings = 'Settings'                  // 设置列表
const SideListTypeForApply = 'Apply'                          // 申请列表
const SideListTypeForDistinguished = 'Distinguished'    // 嘉宾列表
const SideListTypeForSignInList = 'SignInList'             // 打卡列表

const SetConfigButton = ({
                           isLive,
                           isJoined,
                           setIsShowApplyForLinkMicList,
                           isShowApplyForLinkMicList,
}) => {
  const handleSetConfigClick = (e) => {
    e.stopPropagation();
    e.preventDefault();
    setIsShowApplyForLinkMicList(SideListTypeForSettings == isShowApplyForLinkMicList? null : SideListTypeForSettings);
  };

  return (
    <div className={styles.HorizontalLiveRoom_Btn_Warp} onClick={handleSetConfigClick}>
      <i className={styles.title_Icon_setConfig_Icon}></i>
      <div className={styles.text}>设置</div>
    </div>
  );
};

export default SetConfigButton;
