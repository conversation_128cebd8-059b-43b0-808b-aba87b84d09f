@import (reference) '~antd/es/style/themes/index';

.page_warp {
  display: flex;
  justify-content: space-around;
}
.page_content {
  width: 1000px;
  // background: #3b434b;
}

// -------title导航条-------
.nav_title_warp {
  display: flex;
  justify-content: space-around;
  margin-top: 30px;
}

.nav_title {
  width: 604px;
  display: flex;
  justify-content: space-between;
  position: relative;

  .nav_item {
    text-align: center;
    width: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .nav_item_circle {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #3C89FD;
      border: 1px solid #3C89FD;
      z-index: 10;
      font-size: 16px;
      font-weight: bold;
    }
    .nav_item_circle_active {
      background-color: #3C89FD;
      color: #fff;
    }
    .nav_item_text {
      margin-top: 11px;
      font-size: 14px;
      font-weight: 400;
      color: #999999;
    }
    .nav_item_text_active {
      font-size: 14px;
      font-weight: 500;
      color: #333333;
    }
  }

  .nav_line {
    height: 1px;
    opacity: 1;
    background: #CCCCCC;
    width: calc(100% - 80px);
    position: absolute;
    left: 40px;
    top: 20px;
  }
}
// -------title导航条-------end

// -------填写信息-------start
.informationBox {
  width: 100%;
  min-height: 357px;
  background: #FAFAFA;
  padding-left: 30px;
  padding-right: 30px;
  padding-top:17px;
  padding-bottom: 30px;

  .informationBox_title {
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    margin-bottom:24px;
  }

  .informationBox_from_item {
    display: flex;
    margin-bottom: 15px;
    align-items: center;

    .informationBox_from_item_lable {
      width: 70px;
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      margin-right: 16px;
    }

    .informationBox_from_item_field {
      width: calc(100% - 86px);
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      :global {
        .ant-form-item {
          margin-bottom: 0;
        }
      }
    }

    .informationBox_from_item_field_input {
      :global {
        .ant-input {
          width: 350px;
        }
      }
    }

    .informationBox_from_item_field_Box {
      display: flex;
      align-items: center;

      .informationBox_from_item_field_value {
        margin-right:12px
      }

      .editIcon {
        width: 16px;
        height: 16px;
        background: url("../../assets/Payment/Payment_information_isEdit_icon.png");
        display: inline-block;
        background-size: 16px 16px;
        cursor: pointer;
        user-select: none;
      }
    }

    .informationBox_from_item_field_SingleOption_Wrap {
      display: flex;
      .informationBox_from_item_field_option {
        width: 250px;
        height: 50px;
        background: #FFFFFF;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        border: 1px solid #EEEEEE;
        margin-right: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        user-select: none;
      }
      .informationBox_from_item_field_option_active {
        border: 1px solid #3C89FD;
      }
      .informationBox_from_item_field_option_text {
        display: flex;
        align-items: center;

        .informationBox_from_item_field_option_text_num {
          font-size: 24px;
          font-weight: bold;
          color: #ED3232;
          margin-right: 5px;
        }
        .informationBox_from_item_field_option_text_unit {
          font-size: 14px;
          font-weight: 400;
          color: #666666;
        }
      }
    }

    .informationBox_from_item_field_price {
      display: flex;
      font-size: 14px;
      font-weight: 500;
      .informationBox_from_item_field_price_num {
        color: #ED3232;
      }
      .informationBox_from_item_field_price_unit {
        color: #666666;
      }
    }
  }

  .informationBox_from_submit_Btn {
    display: flex;
    justify-content: space-around;
    align-items: center;


    :global {
      .ant-btn {
        width: 134px;
        height: 36px;
        background: #3C89FD;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        color: #FFFFFF;
        font-size: 16px;
        border: none;
      }
    }
  }

  .isAgree_box {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin-bottom:6px;

    :global {
      .ant-form-item {
        margin-bottom: 0;
      }
    }

    .isAgree_box_checkBox_wrap {
      display: flex;
      :global {
        .ant-checkbox + span {
          padding-right: 0px;
          padding-left: 8px;
          user-select: none;
        }
      }
    }
    .isAgree_box_checkBox_agreement {
      font-size: 14px;
      font-weight: 400;
      color: #198CFF;
      cursor: pointer;
      user-select: none;
    }
  }
}
// -------填写信息-------end


// -------购买信息-------start
.PurchaseInformationBox {
  width: 100%;
  min-height: 178px;
  background: #FAFAFA;
  padding-left: 30px;
  padding-right: 30px;
  padding-top:17px;
  padding-bottom: 30px;

  .PurchaseInformationBox_title {
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    margin-bottom:12px;
  }

  .PurchaseInformationBox_content_item {
    width: 100%;
    display: flex;
    .PurchaseInformationBox_content_item_box {
      width: 50%;
      height: 36px;
      line-height: 36px;
      color: #666666;
      display: flex;

      .PurchaseInformationBox_content_item_box_lable {
        margin-right: 4px;
      }
    }
  }
  .text_red {
    color: #ED3232;
    font-weight: 500;
  }
}
// -------购买信息-------end


// -------支付方式-------start
.MethodPaymentWrap {
  margin-left: 32px;
  .MethodPaymentItem {
    display: flex;

    .MethodPaymentLable {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      margin-right: 16px;
      padding-top: 10px;
    }
    .MethodPaymentValue_option {
      display: flex;
      .MethodPaymentValueItem {
        width: 164px;
        height: 42px;
        background: #FFFFFF;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        border: 1px solid #EEEEEE;
        margin-right: 12px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        cursor: pointer;
        user-select: none;
      }
      .MethodPaymentValueItem:active {
        border: 1px solid #3C89FD;
      }
      .MethodPaymentValueItem_selected {
        border: 1px solid #3C89FD;
      }

      .MethodPaymentValueItemBox {
        display: flex;
        align-items: center;
      }

      .MethodPaymentValueItemIconWechatBay {
        width: 24px;
        height: 24px;
        background: url("../../assets/Payment/Payment_MethodPayment_WechatPay.png");
        background-size: 24px 24px;
        margin-right: 8px;
      }

      .MethodPaymentValueItemIconCorporateTransfer {
        width: 24px;
        height: 24px;
        background: url("../../assets/Payment/Payment_MethodPayment_CorporateTransfer.png");
        background-size: 24px 24px;
        margin-right: 8px;
      }

      .MethodPaymentValueItemText {
        font-size: 14px;
        font-weight: 400;
        color: #333333;
      }
    }

    .PaymentMethodPaymentPromptBox {
      display: flex;
      align-items: center;
      margin-top: 12px;
    }

    .PaymentMethodPaymentPromptIcon {
      width: 16px;
      height: 16px;
      background: url("../../assets/Payment/Payment_MethodPayment_Prompt.png");
      display: inline-block;
      background-size: 16px 16px;
      cursor: pointer;
      user-select: none;
      margin-right: 3px;
    }

    .PaymentMethodPaymentPromptText {
      font-size: 14px;
      font-weight: 400;
      color: #999999;
      display: inline-block;
    }
  }


  .WechatBay_Box {
    display: flex;
    margin-top: 28px;
    flex-direction: column;
    align-items: center;
    padding-bottom: 30px;

    .WechatBay_Box_Wrap {
      display: flex;
      align-items: center;
      margin-bottom: 23px;

      .WechatBay_Box_icon {
        width: 32px;
        height: 32px;
        background: url("../../assets/Payment/Payment_MethodPayment_WechatPay.png");
        background-size: 32px 32px;
        margin-right: 6px;
      }
      .WechatBay_title_Box {
        .WechatBay_title_text {
          font-size: 16px;
          font-weight: 400;
          color: #333333;
        }
        .WechatBay_title_Desc {
          font-size: 12px;
          font-weight: 400;
          color: #999999;
        }
      }
    }

    .WechatBay_Box_QRCode {
      background: #FFFFFF;
      border-radius: 0px 0px 0px 0px;
      opacity: 1;
      border: 1px solid #EEEEEE;
      padding-top: 20px;
      padding-left: 20px;
      padding-right: 20px;
      padding-bottom: 22px;
      display: flex;
      flex-direction: column;
      align-items: center;

      .WechatBay_Box_QRCode_img {
        width: 118px;
        height: 118px;
        background: #D9D9D9;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        margin-bottom: 16px;
      }

      .WechatBay_Box_QRCode_text {
        font-size: 12px;
        font-weight: 400;
        color: #666666;
      }

    }
  }

  .PaymentCorporateTransfer_wrap {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
  }

  .PaymentCorporateTransfer {
    width: 520px;
    margin-top: 24px;
    padding-top: 20px;
    padding-left: 24px;
    padding-right: 24px;
    padding-bottom: 24px;
    background: #FAFAFA;
    border-radius: 2px 2px 2px 2px;
    opacity: 1;

    .PaymentCorporateTransfer_item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .PaymentCorporateTransfer_item_lable {
        font-size: 14px;
        font-weight: 400;
        color: #666666;
        margin-right: 12px;
        text-align: right;
        width: 70px;
      }
      .PaymentCorporateTransfer_item_value {
        font-size: 14px;
        font-weight: 400;
        color: #666666;
        width: cale(100% - 70px - 12px);
      }
    }

    .PaymentCorporateTransfer_item_upload {
      display: flex;
      align-items: flex-start;
    }
  }



  .PaymentCorporateTransfer_submit_wrap {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    :global {
      .ant-btn {
        width: 134px;
        height: 36px;
        background: #198CFF;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        font-size: 14px;
        font-weight: 400;
        color: #FFFFFF;
      }
    }
  }
}




// 移动端布局
  .Mobile_Wrap {
    width: 100%;
    min-width: 285px;
    height: 100vh;
    background: linear-gradient(180deg, #EDE7FF 0%, #FFFFFF 100%, #FAFAFA 100%);
    border-radius: 0px 0px 0px 0px;
    opacity: 1;
    padding-left: 12px;
    padding-right: 12px;
  }
  // app中
  .in_app_Mobile_Wrap .Mobile_Content_box {
    margin-top: 12px;
  }

  .Mobile_title_statusbar {
    width: 100%;
    height: 44px;
  }

  .Mobile_title_Wrap {
    width: 100%;
    height: 44px;
    margin-bottom: 12px;
    display: flex;
    justify-content: center;
    align-items: center;

    .Mobile_title {
      font-size: 17px;
      font-weight: 500;
      color: #000000;
    }
  }


  .Mobile_Content_box {
    min-height: 480px;
    // height: calc(100% - 44px - 44px - 12px - 12px);
    padding-bottom: 128px;
    position: relative;


    .Mobile_submit_warp {
      width: calc(100%);
      position: absolute;
      bottom: 0px;
      padding-bottom: 30px;
      margin-top: 6px;


      .Mobile_submit_agree {
        display: flex;
        justify-content: center;
        margin-bottom: 16px;
        font-size: 12px;
        font-weight: 400;
        color: #999999;
        :global {
          .ant-checkbox + span {
            padding-right: 0px;
          }
        }
      }

      .Mobile_submit_agree_agreement {
        color:#A95CF5
      }


      .Mobile_submit_btn_wrap {
        .Mobile_submit_btn {
          width: 100%;
          height: 40px;
          background: linear-gradient(270deg, #8966FF 0%, #6BA6FF 100%);
          border-radius: 28px 28px 28px 28px;
          opacity: 1;
          font-size: 14px;
          font-weight: 400;
          color: #FFFFFF;
          line-height: 40px;
          text-align: center;
          cursor: pointer;
          user-select: none;
        }
        .Mobile_submit_btn:active {
          background: linear-gradient(270deg, #5a43a9 0%, #4a74b3 100%);
        }
      }
    }
  }

  .Mobile_box_Confirm_Order {
    // min-width: 351px;
    // height: 179px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    opacity: 1;
    padding-left: 14px;
    padding-right: 14px;
    padding-top: 18px;
    padding-bottom: 18px;
    .Mobile_box_Confirm_Order_item {
      display: flex;
      margin-bottom: 12px;

      .Mobile_box_Confirm_Order_title {
        // width: 56px;
        font-size: 14px;
        font-weight: 400;
        color: #AAAAAA;
        margin-right: 17px;
      }
      .Mobile_box_Confirm_Order_value {
        width: calc(100% - 60px - 17px);
        font-size: 14px;
        font-weight: 400;
        color: #333333;
      }
    }
    .Mobile_box_Confirm_Order_item_amount {
      display: flex;
      margin-top: 16px;
      justify-content: space-between;

      .Mobile_box_Confirm_Order_title {
        // width: 56px;
        font-size: 14px;
        font-weight: 400;
        color: #AAAAAA;
        margin-right: 17px;
      }
      .Mobile_box_Confirm_Order_value {
        //width: calc(100% - 56px - 17px);
        //font-size: 14px;
        //font-weight: 400;
        //color: #333333;
      }
    }

    .Mobile_box_Confirm_Line {
      width: 100%;
      border-bottom: #F5F5F5 dashed 2px;
    }

    .Mobile_box_Confirm_Order_amount {
      display: flex;
      align-items: flex-end;

      .Mobile_box_Confirm_Order_amount_unit {
        font-size: 12px;
        font-weight: 400;
        color: #EB4C4C;
      }
      .Mobile_box_Confirm_Order_amount_num {
        font-size: 24px;
        font-weight: 400;
        color: #EB4C4C;
        height: 33px;
      }
    }
  }

  .Mobile_box_Confirm_Order_item_option {
    margin-top: 24px;

    .Mobile_box_Confirm_Order_item_title {
      font-size: 16px;
      font-weight: 500;
      color: #000000;
      margin-bottom: 13px;
    }
    .Mobile_box_Confirm_Order_item_option_box {
      .Mobile_box_Confirm_Order_item_option_item {
        height: 56px;
        background: #FBFBFB;
        border-radius: 8px 8px 8px 8px;
        opacity: 1;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        padding-left: 18px;
        padding-right: 18px;
        justify-content: space-between;
        cursor: pointer;
        user-select: none;
        .Mobile_box_Confirm_Order_item_option_item_text {
          display: flex;
          align-items: center;
          .Mobile_box_Confirm_Order_item_option_item_icon_wxIcon {
            width: 24px;
            height: 22px;
            background: url("../../assets/Payment/Payment_MethodPayment_WechatPay.png") no-repeat;
            background-size: 24px 22px;
            opacity: 1;
            margin-right: 3px;
          }
          .Mobile_box_Confirm_Order_item_option_item_icon_CorporateTransfer {
            width: 24px;
            height: 24px;
            background: url("../../assets/Payment/Payment_MethodPayment_CorporateTransfer.png") no-repeat;
            background-size: 24px 24px;
            opacity: 1;
            margin-right: 3px;
          }
          .item_text {
            font-size: 14px;
            font-weight: 400;
            color: #333333;
          }
        }
        .Mobile_box_Confirm_Order_item_option_item_icon_check {
          width: 20px;
          height: 20px;
          background: url("../../assets/Payment/Payment_MethodPayment_icon_check.png") no-repeat;
          background-size: 20px 20px;
          opacity: 1;
          margin-right: 3px;
          display: inline-block;
          cursor: pointer;
          user-select: none;
        }
        .Mobile_box_Confirm_Order_item_option_item_icon_check_active {
          width: 20px;
          height: 20px;
          background: url("../../assets/Payment/Payment_MethodPayment_icon_check_Select.png") no-repeat;
          background-size: 20px 20px;
          opacity: 1;
          margin-right: 3px;
          display: inline-block;
          cursor: pointer;
          user-select: none;
        }
      }
    }
  }

  .Mobile_box_Confirm_Order_enterprise_form {
    background: #FBFBFB;
    border-radius: 8px 8px 8px 8px;
    opacity: 1;
    padding-left: 12px;
    padding-right: 12px;
    padding-top: 16px;
    padding-bottom: 16px;

    .Mobile_box_Confirm_Order_enterprise_form_item {
      display: flex;
      margin-bottom: 12px;
      justify-content: space-between;
      .Mobile_box_Confirm_Order_enterprise_form_item_lable {
        // width: 56px;
        height: 20px;
        font-size: 14px;
        font-weight: 400;
        color: #AAAAAA;
      }
      .Mobile_box_Confirm_Order_enterprise_form_item_value {
        width: calc(100% - 60px - 17px);
        text-align: right;
        font-size: 14px;
        font-weight: 400;
        color: #333333;
      }
    }
  }


.imgWarp {
  width: 104px;
  height: 104px;
  overflow: hidden;
}


.LocalUpload {
  width: 24px;
  height: 24px;
  background: url("../../assets/Payment/Payment_Local_upload.png");
  background-size: 24px 24px;
  display: inline-block;
}
