/**
 * @Description: 选择空间类型
 */
import React, { useState } from 'react'
import { connect } from 'umi'
import styles from './index.less'
import GoBackIcon from '@/assets/GlobalImg/go_back.png' // 返回图片
import checkIcon from '@/assets/GlobalImg/check_icon.png'; // 勾选小图标

const Index: React.FC = (props: any) => {
  const { goBack, userInfoStore, dispatch } = props || {};
  const { spaceTypeId } = userInfoStore || {};
  const [ checkedId, setCheckedId ] = useState(spaceTypeId); // 勾选中的id

  const listData = [
    {id: 1, text: '专家讲课'},
    {id: 2, text: '专家指导'},
    {id: 3, text: '复杂病例讨论'},
  ]

  // 勾选事件
  const checkedFn = (item: any) => {
    setCheckedId(item?.id)
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        spaceTypeId: item?.id, // 空间类型 1专家讲课 2指导 3复杂病例讨论
        spaceTypeName: item?.text, // 空间选择的类型名称
        createModalVisible: false, // 是否展示创建空间王国下拉弹框
      }
    })
  }

  return (
    <div className={styles.container}>
      <div className={styles.title_box}>
        <div className={styles.title_btn} onClick={()=>{
          dispatch({
            type: 'userInfoStore/setTaskListState',
            payload: { createModalVisible:false, }
          })
        }}>
          <img src={GoBackIcon} width={12} height={24} alt=""/>
        </div>
        <div className={styles.title}>选择会议类型</div>
      </div>
      <div className={styles.wrap}>
        {
          listData && listData.map(item => {
            return <div className={styles.list} key={item.id} onClick={() => checkedFn(item)}>
              <div className={styles.list_title}>{item.text}</div>
              {checkedId == item.id ? <img className={styles.right_check} src={checkIcon} alt="" /> : null}
            </div>
          })
        }
      </div>
    </div>
  )
}

export default connect(({ userInfoStore, loading }: any) => ({userInfoStore, loading}))(Index)
