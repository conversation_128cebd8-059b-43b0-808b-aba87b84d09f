import NoDataRender from '@/components/NoDataRender';
import React, { useEffect, useRef, useState } from 'react';
import classNames from 'classnames';
import InfiniteScroll from 'react-infinite-scroller';
import { connect } from 'umi';
import { getDAesString } from '@/utils/utils'
import {Modal, Checkbox, Button, Typography, message} from 'antd';
import styles from './index.less';
import { throttle } from 'lodash';
import noScreenIcon from '@/assets/Expert/no_screen_icon.png'   // 无筛选图标
import screenIcon from "@/assets/Expert/screen_icon.png";
import MeetingCardInfoByPC from "@/components/MeetingCardInfoByPC";  // PC端会议卡片信息
import PosterModalByPc from '@/pages/Poster/PosterModalByPc';   // PC端海报弹窗
import MoreOperateByPc from './MoreOperateByPc'                  // 更多操作下拉菜单

const initState = {
  total: 0,
  listDate: [],
}


const meetingSpaceStatusList = [
  {
    id: 1,
    name: '我参与的会议',
  },
  {
    id: 2,
    name: '历史记录',
  },
]

const isBizList = [
  {
    id: 1,
    name: '企业会议',
  },
  {
    id: 0,
    name: '非企业会议',
  }
]

const isRecordingMeetingList = [
  {
    id: 0,
    name: '全部',
  },
  {
    id: 1,
    name: '已录制会议',
  }
]
const PcMyHomepageSpace: React.FC<any> = (props) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  const { dispatch, starSpaceType } = props;
  // 个人中心空间列表筛选条件
  let myHomeSpaceFilter = JSON.parse(sessionStorage.getItem('myHomeSpaceFilter'))|| {};
  let initStatePage = {
    pageNum: 1,
    hasMore: true,  // 加载更多
    loadMore: false,
    spaceJoinType:starSpaceType==2?null:myHomeSpaceFilter?.spaceJoinType||1,  // 空间角色类型 1:作为主持 2:预约的 3:作为嘉宾
    spaceStatus:myHomeSpaceFilter?.spaceStatus||null, // 空间状态 1:预约中 2:进行中 3:已结束
    isBiz:myHomeSpaceFilter?.isBiz||null, // 空间类型 1:企业空间 2:非企业空间
    isRecordingMeeting: starSpaceType==1?null:myHomeSpaceFilter?.isRecordingMeeting||null, // 会议录制 1:已录制，0/不传:全部
    meetingSpaceStatus:starSpaceType==1?null:myHomeSpaceFilter?.meetingSpaceStatus||1, // 会议空间状态 1:预约中 2:进行中 3:已结束
  }

  const scrollParentRef = useRef<HTMLDivElement | null>(null);
  const [state, setState] = useState(initState)             // 列表数据
  const [statePage, setStatePage] = useState(initStatePage)  // 当前分页
  const [screenHeaderHighlight, setScreenHeaderHighlight] = useState((initStatePage.spaceStatus!=null||initStatePage.isBiz!=null||initStatePage.isRecordingMeeting!=null)?true:false)  // 筛选高亮
  const [isModalOpen, setIsModalOpen] = useState(false)  // 筛选弹窗状态
  const [selectBizList, setSelectBizList] = useState(initStatePage.isBiz!=null&&initStatePage?.isBiz.split(',').map(Number)||[])  // 选中的空间分类
  const [selectRecordingMeeting, setSelectRecordingMeeting] = useState(initStatePage.isRecordingMeeting!=null&&initStatePage?.isRecordingMeeting||[])  // 选中的会议录制
  const [visiblePosterModal, setVisiblePosterModal] = useState(false)  // 生成海报弹窗
  const [spaceId, setSpaceId]= useState(null)   //当前生成海报的会议id
  const { total, listDate } = state
  const { pageNum, hasMore, loadMore, spaceJoinType, spaceStatus, isBiz, isRecordingMeeting, meetingSpaceStatus } = statePage || {}

  useEffect(() => {
    sessionStorage.getItem('myHomeSpaceFilter')&&sessionStorage.removeItem('myHomeSpaceFilter');
    getList(1)
  }, [starSpaceType, spaceJoinType, spaceStatus, isBiz, isRecordingMeeting, meetingSpaceStatus])

  // 获取空间列表数据
  const getList = (page:any, size:number) => {
    // 如果是第一页，滚动条回到顶部
    if(page=== 1){
      document.getElementById('tab_content_listBody')?.scrollTo(0, 0);
    }
    dispatch({
      type: "expertAdvice/getStarSpaceListBySearchUserId",
      payload: {
        pageNum: page,
        pageSize: size || 30,
        searchUserId: UserInfo?.friUserId, // 检索用户id
        starSpaceType,  // 空间类型 1:直播 2:会议
        ...(spaceJoinType ? { spaceJoinType } : {}),   // 空间角色类型 1:作为主持 2:预约的 3:作为嘉宾
        ...(spaceStatus ? { spaceStatus } : {}),   // 空间状态 1:预约中 2:进行中 3:已结束
        ...(isBiz ? { isBiz } : {}), // 空间类型 1:企业空间 2:非企业空间
        ...(isRecordingMeeting ? { isRecordingMeeting } : {}), // 会议录制 1:已录制，0/不传:全部
        ...(meetingSpaceStatus ? { meetingSpaceStatus } : {}), // 会议空间状态 1:我参与的会议 2:历史
      },
    }).then((res: any) => {
      const { code, content } = res || {};
      const { pageNum, total, resultList } = content || {};

      if (res && code == 200) {
        let data = pageNum == 1 ? [] : listDate;
        data = data.concat(resultList || []);
        const hasMore = data.length !== total;

        if (Array.isArray(data) && data.length == 0) {
          setState({
            ...state,
            listDate: [],
            total: 0,
          })
          return
        }
        setState({
          ...state,
          listDate: [...data],
          total,
        })
        setStatePage({
          ...statePage,
          loadMore: false,
          hasMore,
          pageNum: page && size ? statePage.pageNum : pageNum,
        })
      }
    }).catch((err) => {
      console.log(err)
    });
  }

  // 滚动加载分页
  let handleInfiniteOnLoad = () => {
    if (listDate.length > total - 1) {
      setStatePage({
        ...statePage,
        loadMore: false,
        hasMore: false
      })
      return;
    }
    const pages = pageNum + 1;
    setStatePage({
      ...statePage,
      loadMore: true,
    })
    getList(pages)
  }

  handleInfiniteOnLoad = throttle(handleInfiniteOnLoad, 100);

  // 下架成功后，刷新数据
  const refreshFn = () => {
    getList(1, listDate.length)
  }

  // 打开筛选项弹窗
  const openFilter = () => {
    setIsModalOpen(true)
  }

  // 选中空间分类
  const onSelectBizChange = (val) => {
    setSelectBizList(val)
  }
  // 选中全部空间分类
  const onSelectAllBizChange = (e) => {
    setSelectBizList(e.target.checked ? [0,1] : [])
  }

  // 选中会议录制
  const onSelectRecordingMeetingChange = (val) => {
    setSelectRecordingMeeting(val.length==0?[]:[val[val.length-1]])
  }

  // 确认筛选
  const onOkFilter = () => {
    setIsModalOpen(false)
    if(selectBizList.length!=0||selectRecordingMeeting.length!=0){
      setScreenHeaderHighlight(true);
    }else{
      setScreenHeaderHighlight(false);
    }
    setStatePage({
      ...statePage,
      isBiz: (selectBizList.length==2||selectBizList.length==0)?null:selectBizList[0].toString(),
      isRecordingMeeting:selectRecordingMeeting.length==0?null: selectRecordingMeeting[0].toString()
    })
  }

  // 清空筛选条件
  const clearFilter = () => {
    setStatePage({
      ...statePage,
      isBiz: null,
      spaceStatus: null,
      isRecordingMeeting:null,
    })
    setScreenHeaderHighlight(false);
    setSelectBizList([]);
    setSelectRecordingMeeting([]);
    setIsModalOpen(false)
  }

  // 海报
  const posterBtn = (item: any) => {
    setVisiblePosterModal(true)
    setSpaceId(item.id)
  }

  // 复制链接
  const onCopy = () => {
    message.success('复制链接成功!')
  }

  return (
    <div className={styles.tab_content_list} id={'tab_content_list'}>
      <div className={styles.tab_spaceRoleType_list}>
        {
          meetingSpaceStatusList.map((item, index) => {
            return <span key={index} onClick={() => {
            setStatePage({
              ...statePage,
              isBiz: null, // 会议类型 1:企业空间 2:非企业空间
              isRecordingMeeting:null,
              meetingSpaceStatus:item.id,
            })
            setState(initState) // 重置列表数据
            setScreenHeaderHighlight(false);
            setSelectBizList([]);
            setSelectRecordingMeeting([]);
          }} className={item.id == meetingSpaceStatus ? styles.spaceRoleTypeActive : ''}>{item.name}</span>
      })}</div>
      <div className={styles.tab_spaceStatus_list}>
        <span style={{backgroundColor:'#fff'}}></span>
          <i onClick={() => openFilter()} className={screenHeaderHighlight ? styles.screen_btnActive : ''}><img
            className={styles.screen_icon} src={screenHeaderHighlight ? screenIcon : noScreenIcon}/>筛选</i>
      </div>
      {/*<div className={styles.tab_space_title}>共 {listDate && listDate.length} 条内容</div>*/}
      <div className={styles.tab_content_listBody} id={'tab_content_listBody'} ref={(ref) => (scrollParentRef.current = ref)}>
        <InfiniteScroll
          loadMore={handleInfiniteOnLoad}
          threshold={50}
          pageStart={1}
          initialLoad={false}
          hasMore={!loadMore && hasMore}
          useWindow={false}
          getScrollParent={() => scrollParentRef.current}
          className={styles.scroll_box}
        >
          <div className={styles.space_wrap}>
            {listDate && listDate.length ? (
              listDate.map((item: any, index:number) => (
                <div className={styles.space_list} key={item?.id}>
                  {meetingSpaceStatus == 1&& <div>
                    {
                      (index>0&&item.appointmentStartDateDescs!=listDate[index-1].appointmentStartDateDescs||index==0)&&<h3>{item.appointmentStartDateDescs}</h3>
                    }
                    <h4>{item.appointmentStartMinTime}-{item.appointmentStartMaxTime}</h4>
                  </div>}
                  <MeetingCardInfoByPC myHomeSpaceFilter={{isBiz, isRecordingMeeting ,meetingSpaceStatus}} item={item} key={item?.id}/>
                  {
                    meetingSpaceStatus == 1 && <div className={styles.item_myMeeting_box}>
                      <MoreOperateByPc meetingItem={item} refreshFn={refreshFn}/>
                      <span className={styles.item_poster_btn} onClick={() => posterBtn(item)}>分享海报</span>
                      <span className={styles.item_copy_btn}>
                        <Typography.Paragraph copyable={{
                        text: `${window.location.origin}/PlanetChatRoom/Meet/${item.id}?shareUserId=${UserInfo?.friUserId}&isShare=1` + (item.password ? `&pwd=${getDAesString(item.password,'arrail-dentail&2', 'arrail-dentail&3')}` : ''),
                        icon: [<div>复制链接</div>, <div>复制链接</div>],
                        tooltips: ['', ''],
                        onCopy: onCopy,
                      }}></Typography.Paragraph>
                      </span>
                    </div>
                  }
                </div>
              ))
            ) : (
              <div className={styles.no_data_wrap}><NoDataRender className={styles.noDataStyle}/></div>
            )}
          </div>
        </InfiniteScroll>
      </div>
      <Modal
        title="筛选"
        open={isModalOpen}
        footer={null}
        onCancel={() => {
          setIsModalOpen(false);
        }}
        className={styles.screen_modal}
        destroyOnClose={true}
        getContainer={() => document.getElementById('tab_content_list')}
      >
        <div className={styles.screen_box}>
          <div className={styles.isBizTitle}>会议分类</div>
          <div className={styles.isBizSelectBox}>
            <Checkbox
              indeterminate={selectBizList.length == 1}
              onChange={onSelectAllBizChange}
              checked={selectBizList.length == 2}
            >全部</Checkbox>
            <Checkbox.Group value={selectBizList} style={{width: '100%'}} onChange={(val) => {
              onSelectBizChange(val)
            }}>
              {isBizList.map((item, idx) => {
                return <div key={idx} className={styles.screen_child}>
                  <Checkbox value={item.id}>{item.name}</Checkbox>
                </div>
              })
              }
            </Checkbox.Group>
          </div>
        </div>
        <div className={styles.screen_box}>
          <div className={styles.isBizTitle} style={{marginTop: '10px'}}>会议录制</div>
          <div className={styles.isBizSelectBox}>
            <Checkbox.Group value={selectRecordingMeeting} style={{width: '100%'}} onChange={(val) => {
              onSelectRecordingMeetingChange(val)
            }}>
              {isRecordingMeetingList.map((item, idx) => {
                return <div key={idx} className={styles.screen_child}>
                  <Checkbox value={item.id}>{item.name}</Checkbox>
                </div>
              })
              }
            </Checkbox.Group>
          </div>
        </div>
        <div className={styles.footer}>
          <Button onClick={() => clearFilter()}>取消</Button>
          <Button onClick={() => onOkFilter()} type="primary" className={styles.btn_primary}>确认</Button>
        </div>
      </Modal>

      {/* 海报弹窗 */}
      <PosterModalByPc
        visible={visiblePosterModal}
        spaceId={spaceId}
        onCancel={() => {
          setVisiblePosterModal(false);
          setSpaceId(null);
        }}
      />
    </div>
  );
};

export default connect(({expertAdvice, loading}: any) => ({expertAdvice, loading}))(
  PcMyHomepageSpace,
);
