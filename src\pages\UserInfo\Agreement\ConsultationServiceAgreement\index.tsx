/**
 * 服务协议相关页面
 */
import React, { useEffect } from 'react';
import classNames from "classnames";
import styles from './index.less';
import NavBar from '@/components/NavBar';
import { getOperatingEnv } from '@/utils/utils';
import { connect, history } from 'umi';

// 隐私政策图片列表
const agreementImgList = [
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/ConsultationServiceAgreement/agreement_1.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/ConsultationServiceAgreement/agreement_2.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/ConsultationServiceAgreement/agreement_3.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/ConsultationServiceAgreement/agreement_4.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/ConsultationServiceAgreement/agreement_5.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/ConsultationServiceAgreement/agreement_6.png',
]


const ConsultationServiceAgreement: React.FC = (props: any) => {
  const { dispatch } = props;
  // 生成8位随机数
  const pageKeyByRendom = Math.random().toString(36).substr(2, 8);

  useEffect(() => {
    /*if (getOperatingEnv() === '4' ) {
      // 设置pc tab页为设置
      dispatch({
        type: 'pcAccount/save',
        payload: {
          tabState: 5,
          subTabState: null,
        }
      })
      history.replace('/UserInfo')
    }*/
  }, [dispatch]);

  return (
    <div className={classNames({
      [styles.agreementWarp]: true,
    })}>
      <NavBar title={'用户服务付费协议'}></NavBar>
      <div className={classNames(styles.agreementBox, {[styles.pc_agreementBox]: getOperatingEnv() === '4'})}>
        {Array.from({length: 6}).map((item, index) => {
          try {
            return (
              <img
                key={index}
                src={`https://static.jwsmed.com/public/DigitalHealth/Business/assets/ConsultationServiceAgreement/agreement_${index + 1}.png?pageKeyByRendom=${pageKeyByRendom}`}
                alt=""
                onError={(e) => { e.target.style.display = 'none'; }}
              />
            )
          }catch (e) {
            console.log(e)
          }
        })}
      </div>
    </div>
  )
}
export default connect()(ConsultationServiceAgreement)
