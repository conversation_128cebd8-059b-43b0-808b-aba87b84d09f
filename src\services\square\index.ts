
import request from "@/utils/request";
import {stringify} from "qs";
import {getOperatingEnv} from "@/utils/utils"

/**
 *  /server/square/getStarKingdomList
 *  分页获取王国数据
 */
export async function getStarKingdomList(params: {}, options?: { [key: string]: any }) {
  return request(`/api/server/square/getStarKingdomList`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

/**
 * /server/square/getStarSpaceList
 * 分页获取会议数据
 * pageNum   页码
 * pageSize  条数
 * wxUserId  微信用户ID
 */
export async function getStarSpaceList(params: {}, options?: { [key: string]: any }) {
  return request(`/api/server/square/getStarSpaceList`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

/**
 * 根据用户ID获取搜索关键字（张志军）
 */
export async function getWordList(params) {
  return request('/api/server/square/getWordList', {
    method: 'GET',
    params,
    headers: {
      type: getOperatingEnv() == 1 ? "" : 1,
    },
  })
}

/**
 * 广场检索（张志军）
 * @params pageNum                     页码
 * @params pageSize                    页数
 * @params searchKey                   搜索关键字
 * @params type                        默认不传或传0，空间1 王国2
 * @params wxUserId                    当前登录用户ID
 * @params status                      空间状态：1直播中、2预约中、3弹幕轰炸中
 */
export async function getSquareList(params) {
  return request('/api/server/square/getSquareList', {
    method: 'POST',
    data: params,
    headers: {
      type: getOperatingEnv() == 1 ? "" : 1,
    },
  })
}
