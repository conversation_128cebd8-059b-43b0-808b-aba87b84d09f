/**
 * @Description: 广场-推荐
 */
import React, { useState, useEffect, useRef } from 'react';
import { connect, history } from 'umi';
import classNames from 'classnames';
import { stringify } from 'qs';
import { cloneDeep } from 'lodash';
import { processNames, randomColor, statusRecord } from '@/utils/utils';
import { Spin, message } from 'antd';
import { InfiniteScroll, ProgressBar, Toast, PullToRefresh, Swiper } from 'antd-mobile'; // 滚动加载
import styles from './index.less';

// 图片icon
import topic_title from '@/assets/GlobalImg/topic_title.png'; // 话题icon
import icon1 from '@/assets/Square/icon1.png';
import icon2 from '@/assets/Square/icon2.png';
import more from '@/assets/Square/more.png';
import t1 from '@/assets/Square/t1.png';
import t2 from '@/assets/Square/t2.png';
import t3 from '@/assets/Square/t3.png';
import t4 from '@/assets/Square/t4.png';
import tmore from '@/assets/Square/tmore.png';

import SpaceCardBySquare from '@/components/SpaceCardBySquare'; // 空间卡片
import ExternalLinkCard from '@/components/ExternalLinkCard'; // 外链卡片
import PostCard from '@/components/PostCard'; // 帖子卡片
import ForwardCard from '@/components/ForwardCard'; // 转发卡片
import ArticleCardBySquare from '@/components/ArticleCardBySquare'; // 文章卡片
import LikeCard from '@/components/LikeCard'; // 点赞卡片

const adList = [
  {
    id: 1,
    title: '行业展会',
    icon: icon1,
    moreIcon: more,
    adLinkUrl: 'https://dhealth.friday.tech/home?aWQ9NTMmcj0yMDE4MTE1Ng==',
  },
  {
    id: 2,
    title: '直播课',
    icon: icon2,
    moreIcon: more,
    adLinkUrl: 'https://dhealth.friday.tech/home?aWQ9NTImcj0xNjc4MzM0MQ==',
  },
  {
    id: 3,
    title: '线下课',
    subTitle: '瑞尔集团内训实操课',
    icon: [t1, t2, t3, t4, tmore],
    adLinkUrl: 'https://www.friday.tech/PracticalOperation',
  },
];

const CollectList: React.FC = (props: any) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}'); // 用户信息
  const { query } = history.location;
  const {
    publish, // 展示发布进度条
    isAuditing, // 是否需要审核（发布完成展示不同的提示信息）
  } = query;

  const {
    dispatch,
    loading,
    recommended,
    onClickCreateBtn, // 点击创建按钮
  } = props;

  // 页面state
  const initialState = {
    dataList: [], // 数据list
    total: 0, // 总条数
    hasMore: false, // 是否还有更多数据
  };

  // 筛选数据
  const initialFilterState = {
    page: 1, // 页码
    size: 30, // 每页条数
  };

  const [percent, setPercent] = useState(0); // 进度值
  const [isPercent, setIsPercent] = useState(null); // 是否展示进度条
  const [latelyPublishUserList, setLatelyPublishUserList] = useState([]); // 最近三天有发布的用户
  const [squareShowTopicList, setSquareShowTopicList] = useState([]); // 广场页展示的话题(“大家都在聊”)
  const [waterfallManageDataList, setWaterfallManageDataList] = useState([]); // 运营后台设置的前3条数据
  const [state, setState] = useState(initialState); // 数据list
  const [filterState, setFilterState] = useState(initialFilterState); // 分页
  const [loadingGetWaterfallManageData, setLoadingGetWaterfallManageData] = useState(false); // 页面loading-获取前3条数据
  const [loadingGetList, setLoadingGetList] = useState(false); // 页面loading-获取图文list
  const [loadingInfiniteScroll, setLoadingInfiniteScroll] = useState(false); // 滚动加载loading

  useEffect(() => {
    getLatelyPublishUser(); // 最近三天有发布的用户
    getSquareShowTopic(); // 广场页展示的话题(“大家都在聊”)
    getWaterfallManageData(); // 运营后台设置的前3条数据
  }, []);

  useEffect(() => {
    getList(); // 推荐-分页获取内容
  }, [filterState.page]);

  useEffect(() => {
    if (publish == 1) {
      setIsPercent(true);
    }
  }, [publish]);

  useEffect(() => {
    if (!isPercent) {
      return;
    }
    const interval = setInterval(() => {
      if (percent < 100) {
        setPercent((prevPercent) => prevPercent + 20);
      } else {
        clearInterval(interval);
      }
    }, 200); // 0.2秒增加一次

    const interfaceCompletionTimeout = setTimeout(() => {
      setIsPercent(false);
      setPercent(0);
      if (isAuditing == 1) {
        message.success('提交成功~正在快马加鞭地审核');
      } else {
        message.success('发布成功');
      }

      // 删除url中的publish和isAuditing参数
      const queryClone = cloneDeep(query);
      delete queryClone.publish;
      delete queryClone.isAuditing;
      history.replace(`/Square?${stringify(queryClone)}`);
    }, 1000); // 1秒后完成进度条

    // 清除定时器
    return () => {
      clearInterval(interval);
      clearTimeout(interfaceCompletionTimeout);
    };
  }, [isPercent, percent]);

  // 分页获取内容
  const getLatelyPublishUser = () => {
    dispatch({
      type: 'recommended/getLatelyPublishUser',
      payload: {},
    })
      .then((res) => {
        const { code, content, msg } = res;
        if (code == 200) {
          setLatelyPublishUserList(content || []);
        } else {
          message.error(msg || '数据加载失败');
        }
      })
      .catch((err) => {});
  };

  // 推荐-分页获取内容
  const getSquareShowTopic = () => {
    dispatch({
      type: 'recommended/getSquareShowTopic',
      payload: {
        pageNum: 1,
        pageSize: 10000,
      },
    })
      .then((res) => {
        const { code, content, msg } = res;
        if (code == 200) {
          setSquareShowTopicList(content.resultList || []);
        } else {
          message.error(msg || '数据加载失败');
        }
      })
      .catch((err) => {});
  };

  // 运营后台设置的前3条数据
  const getWaterfallManageData = (isLocalUpdate = false) => {
    // 局部刷新不显示loading
    if (!isLocalUpdate) {
      setLoadingGetWaterfallManageData(true); // 页面loading-加载前3条数据
    }
    dispatch({
      type: 'recommended/getWaterfallManageData',
      payload: {},
    })
      .then((res) => {
        setLoadingGetWaterfallManageData(false);
        const { code, content, msg } = res;
        if (code == 200) {
          setWaterfallManageDataList(content || []);
        } else {
          message.error(msg || '数据加载失败');
        }
      })
      .catch((err) => {});
  };

  // 获取推荐列表
  const getList = (page, size, isLocalUpdate = false) => {
    setLoadingInfiniteScroll(true); // 滚动加载loading
    // 局部刷新不显示loading
    if (!isLocalUpdate) {
      setLoadingGetList(true); // 页面loading-加载图文数据
    }
    const pageResult = page || filterState.page;
    const sizeResult = size || filterState.size;

    dispatch({
      type: 'recommended/getList',
      payload: {
        pageNum: pageResult,
        pageSize: sizeResult,
      },
    })
      .then((res) => {
        setLoadingInfiniteScroll(false);
        setLoadingGetList(false);
        const { code, content, msg } = res;
        if (code == 200) {
          const resultList = content.resultList || [];
          setState({
            ...state,
            dataList: pageResult == 1 ? resultList : state.dataList.concat(resultList),
            total: content.total || 0,
            hasMore: true,
          });
        } else {
          message.error(msg || '数据加载失败');
        }
      })
      .catch((err) => {});
  };

  // 滚动加载分页
  const loadMore = async () => {
    console.log('loadMore');
    // 没有更多数据了
    if (state.dataList.length >= state.total) {
      await setState({
        ...state,
        hasMore: false,
      });
      return;
    }

    await setLoadingInfiniteScroll(true); // 滚动加载loading
    await setFilterState({
      ...filterState,
      page: filterState.page + 1, // 页码
    });
  };

  // 下拉刷新数据
  const updateListData = async () => {
    getLatelyPublishUser(); // 最近三天有发布的用户
    getSquareShowTopic(); // 广场页展示的话题(“大家都在聊”)
    getWaterfallManageData(); // 运营后台设置前3条信息
    if (filterState.page != 1) {
      setFilterState({
        ...filterState,
        page: 1,
      });
    } else {
      getList(1, filterState.size);
    }
  };

  // 删除或下架操作后刷新数据，true表示局部刷新，不显示页面loading
  const refreshDataById = (id) => {
    getWaterfallManageData(true);
    getList(1, state.dataList.length, true);
  };

  // 最近三天有发布的用户
  const onClickLatelyPublishUser = (item) => {
    const { isForward, imageType, imageTextId, spaceId } = item;
    if (isForward == 1) {
      history.push(`/CreateGraphicsText/ForwardDetails?id=${imageTextId}`);
      return;
    }
    // 跳转详情页
    if (imageType == 1) {
      history.push(`/CreateGraphicsText/ArticleDetails?id=${imageTextId}`);
    } else if (imageType == 2) {
      history.push(`/CreateGraphicsText/PostDetails?id=${imageTextId}`);
    } else if (imageType == 3) {
      history.push(`/CreateGraphicsText/ExternalLinksDetails?id=${imageTextId}`);
    } else if (imageType == 4) {
      history.push(`/CreateGraphicsText/SpaceDetails?id=${imageTextId}`);
    }
  };

  // 点击大家都在聊-某个话题
  const onClickTopicItem = (value) => {
    history.push(`/CreateGraphicsText/TopicHome?topicId=${value}`);
  };

  // loading
  const loadingGetLatelyPublishUser = !!loading.effects['recommended/getLatelyPublishUser'];
  const loadingGetSquareShowTopic = !!loading.effects['recommended/getSquareShowTopic'];

  return (
    <Spin
      spinning={
        loadingGetLatelyPublishUser ||
        loadingGetSquareShowTopic ||
        loadingGetWaterfallManageData ||
        loadingGetList
      }
    >
      {/* 广告位 */}
      <div className={styles.ad_wrapper}>
        {adList.map((item) => {
          if (item.id !== 3) {
            return (
              <a
                className={classNames(styles.ad_item, {
                  [styles.ad_item1]: item.id === 1,
                  [styles.ad_item2]: item.id === 2,
                })}
                key={item.id}
                href={item.adLinkUrl}
              >
                <img className={styles.icon} src={item.icon} />
                <div className={styles.title}>{item.title}</div>
                <div className={styles.more}>
                  点击查看 <img src={more} />
                </div>
              </a>
            );
          }
          return (
            <a
              className={classNames(styles.ad_item, styles.ad_item3)}
              key={item.id}
              href={item.adLinkUrl}
            >
              <div className={styles.title}>{item.title}</div>
              <div className={styles.subTitle}>{item.subTitle}</div>
              <div className={styles.teachers}>
                {item.icon.map(item => <img src={item} key={item}/>)}
              </div>
            </a>
          );
        })}
      </div>

      {!!isPercent && (
        <div className={styles.progress_bar_wrap}>
          <div className={styles.progress_bar_text}>
            <div
              className={styles.progress_bar_avatar}
              style={
                UserInfo.headUrl
                  ? { backgroundImage: `url(${UserInfo.headUrl})` }
                  : { background: randomColor(UserInfo.friUserId) }
              }
            >
              {UserInfo.headUrl ? null : processNames(UserInfo.name)}
            </div>
            <span>{percent == 100 ? '完成' : '发布中...'}</span>
          </div>
          <ProgressBar
            percent={percent}
            rounded={false}
            style={{
              '--track-width': '2px',
              '--fill-color': '#0095FF',
              '--track-color': '#E4F0FC',
            }}
          />
        </div>
      )}
      <PullToRefresh
        onRefresh={updateListData}
        renderText={(status) => {
          return <div>{statusRecord[status]}</div>;
        }}
      >
        <div className={styles.InfiniteScroll_Warp}>
          {squareShowTopicList.length > 0 && (
            <div className={styles.topic_wrap}>
              <div className={styles.topic_content}>
                <div className={styles.topic_title}>
                  <img src={topic_title} width={20} height={20} alt="" />
                  <span>大家都在聊</span>
                </div>
                {/* 轮播 */}
                <div className={styles.topic_swiper_wrap}>
                  <Swiper
                    direction="vertical"
                    allowTouchMove={false}
                    autoplay={true}
                    autoplayInterval={3000}
                    indicator={() => null}
                    loop={true}
                    style={
                      squareShowTopicList.length == 1
                        ? { '--height': '26px' }
                        : { '--height': '52px' }
                    }
                    slideSize={squareShowTopicList.length == 1 ? 100 : 50}
                  >
                    {squareShowTopicList.map((item) => {
                      return (
                        <Swiper.Item
                          key={item.topicId}
                          onClick={() => onClickTopicItem(item.topicId)}
                        >
                          <div className={styles.topic_item}>
                            <div className={styles.topic_item_name}># {item.topicName}</div>
                            <div className={styles.topic_item_number}>
                              {item.gdp}人围观·{item.relationTimes}个内容
                            </div>
                          </div>
                        </Swiper.Item>
                      );
                    })}
                  </Swiper>
                </div>
              </div>
            </div>
          )}

          <div className={styles.list_wrap}>
            {/* 运营端设置的前3条数据和其他数据的集合 */}
            {waterfallManageDataList.concat(state.dataList).map((item) => {
              if (item.isForward == 1) {
                return (
                  <ForwardCard
                    key={item.id}
                    pageType={1} // 从哪个页面过来的标识，推荐首页过来传 1:场推荐 2:话题页面 3:我的主页草稿箱 4:国王详情 5:搜索页面
                    item={item}
                  />
                );
              } else if (item.isForward == 2) {
                return (
                  <LikeCard
                    key={item.id}
                    item={item} // 图文数据
                  />
                );
              } else if (item.imageType == 1) {
                return (
                  <ArticleCardBySquare
                    key={item.id}
                    pageType={1} // 从哪个页面过来的标识，推荐首页过来传 1:场推荐 2:话题页面 3:我的主页草稿箱 4:国王详情 5:搜索页面
                    item={item} // 图文数据
                    refreshDataById={refreshDataById} // 刷新页面方法
                    isShowMoreOperate={true} // 是否展示点点点更多操作
                  />
                );
              } else if (item.imageType == 2) {
                return (
                  <PostCard
                    key={item.id}
                    pageType={1} // 从哪个页面过来的标识，推荐首页过来传 1:场推荐 2:话题页面 3:我的主页草稿箱 4:国王详情 5:搜索页面
                    item={item} // 图文数据
                    refreshDataById={refreshDataById} // 刷新页面方法
                    isShowMoreOperate={true} // 是否展示点点点更多操作
                  />
                );
              } else if (item.imageType == 3) {
                return (
                  <ExternalLinkCard
                    key={item.id}
                    item={item} // 图文数据
                    refreshDataById={refreshDataById} // 刷新页面方法
                    isShowMoreOperate={true} // 是否展示点点点更多操作
                  />
                );
              } else if (item.imageType == 4) {
                return (
                  <SpaceCardBySquare
                    key={item.id}
                    pageType={1} // 从哪个页面过来的标识，推荐首页过来传 1:场推荐 2:话题页面 3:我的主页草稿箱 4:国王详情 5:搜索页面
                    item={item} // 图文数据
                    refreshDataById={refreshDataById} // 刷新页面方法
                    isShowMoreOperate={false} // 是否展示点点点更多操作
                  />
                );
              }
            })}
          </div>
          {/* 滚动加载组件 */}
          <InfiniteScroll
            loadMore={loadMore}
            hasMore={!loadingInfiniteScroll && state.hasMore}
            threshold={30}
          />
        </div>
      </PullToRefresh>
    </Spin>
  );
};
export default connect(({ userInfoStore, recommended, loading }: any) => ({
  userInfoStore,
  recommended,
  loading,
}))(CollectList);
