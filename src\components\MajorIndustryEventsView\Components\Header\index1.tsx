//处理月份是动态的 根据数据返回的生成月份

import React, { useState, useEffect, useRef } from 'react';
import classNames from 'classnames';
import styles from './index.less';

const MonthTab = () => {
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
  const [selectedMonth, setSelectedMonth] = useState<string>();
  const monthListRef = useRef(null);

  // 获取月份名称
  const months = [
    '1月', '2月', '3月', '4月', '5月', '6月',
    '7月', '8月', '9月', '10月', '11月', '12月'
  ];

  // 处理年份/ 月份 -- 初始化
  useEffect(() => {
    const year = new Date().getFullYear();
    const month = new Date().getMonth() + 1 + '月';
    setCurrentYear(year);
    setSelectedMonth(`${month}/${year}`);
  }, []);

  // 滚动到选中的月份
  useEffect(() => {
    if (selectedMonth && monthListRef.current) {
      const selectedItem = monthListRef.current.querySelector(`.${styles.active}`);
      if (selectedItem) {
        const scrollContainer = monthListRef.current;
        const selectedItemOffset = selectedItem.offsetLeft;
        const scrollOffset = selectedItemOffset - 100; // 100px from the left
        scrollContainer.scrollTo({
          left: scrollOffset,
          behavior: 'smooth'
        });
      }
    }
  }, [selectedMonth]);
  

  // 选择月份  -- 更新和数据筛选
  const handleSelectedMonth = (date: string) => {
    setSelectedMonth(date)

    // todo根据选中的时间  发请求筛选数据
    
  }



  // 渲染月份Tab
  const renderMonthTabs = () => {
    // 渲染过去年的月份
    const pastYearNum = 1;
    const pastYearsMonths = Array.from({ length: pastYearNum }, (_v, i) => currentYear - pastYearNum + i).map(year => {
      return months.map(month => (
        <div
          key={year}
          className={classNames(styles.month_item, {
            [styles.active]: selectedMonth === `${month}/${year}`,
          })}
          onClick={() => handleSelectedMonth(`${month}/${year}`)}
        >
          {`${month}/`}
          <span className={styles.small_year}>{year}</span>
        </div>
      ));
    }).flat();

    // 当前年份的月份
    const currentYearMonths = months.map((month, index) => (
      <div
        key={`${month}/${currentYear}`}
        className={classNames(styles.month_item, {
          [styles.active]: selectedMonth === `${month}/${currentYear}`,
        })}
        onClick={() => handleSelectedMonth(`${month}/${currentYear}`)}
      >
        {month}
      </div>
    ));

    // 渲染未来1年的月份
    const futureYearsMonths = Array.from({ length: 1 }, (_v, i) => currentYear + i + 1).map(year => {
      return months.map(month => (
        <div
          key={year}
          className={classNames(styles.month_item, {
            [styles.active]: selectedMonth === `${month}/${year}`,
          })}
          onClick={() => handleSelectedMonth(`${month}/${year}`)}
        >
          {`${month}/`}
          <span className={styles.small_year}>{year}</span>
        </div>
      ));
    }).flat();

    // 合并数组并排序
    return (
      <div ref={monthListRef} className={styles.major_header}>
        {[...pastYearsMonths, ...currentYearMonths, ...futureYearsMonths]}
      </div>
    );
  };

  return (
    <div>
      {renderMonthTabs()}
    </div>
  );
};

export default MonthTab;