import React, { useEffect, useState, useImperativeHandle, useCallback, useRef } from 'react';
import { Modal } from 'antd-mobile';
import { connect, Helmet, history } from 'umi';
import styles from './index.less';
import {
  extractQueryStringFromURL,
  formatTime,
  formatTimeBySeconds,
  getDAesString,
  getOperatingEnv,
  openLoginInApp,
  processNames,
  randomColor,
  sendDanmuCreateItemDom,
  useDebounce,
} from '@/utils/utils';
import { LeftOutlined, PlayCircleOutlined, RightOutlined } from '@ant-design/icons';
import classNames from 'classnames';
import { message, Slider } from 'antd';
import { InfiniteScroll, Switch } from 'antd-mobile';
import Stream from '@/componentsByTRTC/Stream';
import DanmuJs from 'danmu.js';
import moment from 'moment';
import _ from 'lodash';
import Avatar from '../Avatar';
import '../video-js.min.css';
import TCPlayer from 'tcplayer.js';
import 'tcplayer.js/dist/tcplayer.min.css';
import AlloyFinger from 'alloyfinger';
import fullscreen from 'fullscreen.js';

import { BULLET_SCREEN, SEND_FLOWERS, SEND_APPLAUSE, UPDATA_STATE, licenseUrl } from '@/app/config';
import dayjs from 'dayjs';
import { isIphoneIslandSeries } from '@/utils/utilsByTRTC';
import ScreenShareButton from '@/pages/PlanetChatRoom/components/CommonButtonComponents/ScreenShareButton';
import WhiteBoardButton from '@/pages/PlanetChatRoom/components/CommonButtonComponents/WhiteBoardButton';
import RecordButton from '@/pages/PlanetChatRoom/components/CommonButtonComponents/RecordButton';
import ApplauseButton from '@/pages/PlanetChatRoom/components/CommonButtonComponents/ApplauseButton';
import FlowersButton from '@/pages/PlanetChatRoom/components/CommonButtonComponents/FlowersButton';
import ConnectButton from '@/pages/PlanetChatRoom/components/CommonButtonComponents/ConnectButton';
import ShareButton from '@/pages/PlanetChatRoom/components/CommonButtonComponents/ShareButton';
import SignInButton from '@/pages/PlanetChatRoom/components/CommonButtonComponents/SignInButton';
import CollectButton from '@/pages/PlanetChatRoom/components/CommonButtonComponents/CollectButton';
import CameraToggle from '@/pages/PlanetChatRoom/components/CommonButtonComponents/CameraToggle';
import MicrophoneToggle from '@/pages/PlanetChatRoom/components/CommonButtonComponents/MicrophoneToggle';
import StartLiveButton from '@/pages/PlanetChatRoom/components/CommonButtonComponents/StartLiveButton';
import FinishLiveButton from '@/pages/PlanetChatRoom/components/CommonButtonComponents/FinishLiveButton';
import SetConfigButton from '@/pages/PlanetChatRoom/components/CommonButtonComponents/SetConfigButton';
import GuestListButton from '@/pages/PlanetChatRoom/components/CommonButtonComponents/GuestListButton';
import LinkMicApplicationsToggle from '@/pages/PlanetChatRoom/components/CommonButtonComponents/LinkMicApplicationsToggle';
import PictureInPictureButton from '@/pages/PlanetChatRoom/components/CommonButtonComponents/PictureInPictureButton';
import FullScreenToggle from '@/pages/PlanetChatRoom/components/CommonButtonComponents/FullScreenToggle';
import ApplyConnectWheatButton from '@/pages/PlanetChatRoom/components/CommonButtonComponents/ApplyConnectWheatButton';
import LiveRoomMessageList from '@/pages/PlanetChatRoom/components/LiveRoomMessageList';
import { stringify } from 'qs';
import WhiteboardLiveRoom from '@/pages/PlanetChatRoom/components/WhiteboardLiveRoom';

// 侧边列表类型
// SideListType 侧边列表类型
const SideListTypeForSettings = 'Settings'; // 设置列表
const SideListTypeForApply = 'Apply'; // 申请列表
const SideListTypeForDistinguished = 'Distinguished'; // 嘉宾列表
const SideListTypeForSignInList = 'SignInList'; // 打卡列表

// 对远端流中的嘉宾流原数据进行排序,
function sortRemoteStreamConfig(data, speakers) {
  if (!Array.isArray(data)) {
    return null;
  }
  let dataCopy = [];
  data.map((item) => {
    dataCopy.push(item);
  });
  // 深拷贝
  return dataCopy.sort((a, b) => {
    // 如果a或b的userID在speakers数组中，并且其mutedAudio为false，那么将其排在前面
    if (speakers.includes(a.userID) && !a.mutedAudio && !b.mutedAudio) {
      return -1;
    }
    if (speakers.includes(b.userID) && !a.mutedAudio && !b.mutedAudio) {
      return 1;
    }
    // 如果a的mutedAudio大于0，那么a应该在b前面
    if (a.mutedAudio > 0 && b.mutedAudio <= 0) {
      return -1;
    }
    // 如果b的mutedAudio大于0，那么a应该在b后面
    if (a.mutedAudio <= 0 && b.mutedAudio > 0) {
      return 1;
    }
    // 如果两者的mutedAudio相同，那么根据audioVolume进行降序排序
    return b.audioVolume - a.audioVolume;
  });
}

type propsType = {
  onRefByHorizontalLiveRoom: any; // 暴露给父组件的方法
  sendMessageByIm: any; // 发送im实时通信消息
  localStreamConfig: any; // 本地流配置
  remoteStreamConfigList: any; // 远端流配置列表
  RTC: any; // RTC实例
  shareRTC: any; // 分享RTC实例
  isJoined: boolean; // 是否加入房间
  isPublished: boolean; // 是否发布流本地流
  handleJoin: any; // onClick进入房间
  handleLeave: any; // onClick离开房间
  onChange: any; // 改变直播状态 分享屏幕
  spaceId: any; // 直播间id
  openCloseHandUp: any; // 打开/关闭连麦列表
  liveRecord: any; // 打卡录制视频
  getGuestList: any; // 获取嘉宾列表
  getSpaceInfo: any; // 获取直播间信息
  onClickLianMai: any; // 点击申请连麦
  changeUrlParams: any; // 改变url参数
  operateHandUp: any; // 操作连麦
  shareOnClick: any; // 分享点击事件
  onClickBack: any; // 返回按钮
  isHorizontalLive: any; // 是否是全屏
};

const Index: React.FC<propsType> = (props) => {
  // isHiddenControlArea 是否隐藏控制区域
  const [isHiddenControlArea, setIsHiddenControlArea] = useState(false);
  // isShowApplyForLinkMicList 申请连麦列表 SideListType 侧边列表类型 [Settings]设置列表 [Apply]申请列表 [Distinguished]嘉宾列表
  const [isShowApplyForLinkMicList, setIsShowApplyForLinkMicList] = useState(false);
  // isShowCameraList 摄像头列表
  const [isShowCameraList, setIsShowCameraList] = useState(true);
  // VideoDuration 视频时长
  const [videoDuration, setVideoDuration] = useState(0);
  // RTCDanmu
  const [RTCDanmu, setRTCDanmu] = useState(null);
  // inputRef
  const inputRef = useRef(null);
  // player 播放器实例
  const [player, setPlayer] = useState(null);
  // playerStateData 播放器状态数据
  const [playerStateData, setPlayerStateData] = useState(null);
  // 5秒自动隐藏控制区域计时器
  const [timer, setTimer] = useState(null);
  // handUpStatusTypeEnter 确认下麦或确认取消连麦
  const [handUpStatusTypeEnter, setHandUpStatusTypeEnter] = useState(null);

  const wrapperRefByApplyConnectLiveListWarp = useRef(null);
  // 当前展示送花或者鼓掌  currentShowFlowersOrApplause 1:送花 2:鼓掌
  const [currentShowFlowersOrApplause, setCurrentShowFlowersOrApplause] = useState(2);
  // 当前Danmu输入框是否有焦点
  const [isDanmuInputFocus, setIsDanmuInputFocus] = useState(false);
  // 打卡列表 hasMore 是否还有更多
  const [hasMoreBySignInList, setHasMoreBySignInList] = useState(true);
  const [isIphone, setIsIphone] = useState(null);
  const [videoPlayer, setVideoPlayer] = useState(null);
  const [scale, setScale] = useState(1);
  // hasMoreByMsgList 打卡消息列表是否还有更多
  const [hasMoreByMsgList, setHasMoreByMsgList] = useState(true);
  // 是否展示试看提示
  const [isShowTrySeeTip, setIsShowTrySeeTip] = useState(false);
  // 保存白板对象
  const [teduBoard, setTeduBoard] = useState(null);
  // 嘉宾列表是 参会人 还是 申请进入会议成员
  const [guestListType, setGuestListType] = useState(1); // 嘉宾列表, 参会人 还是 申请进入会议成员
  // 记录列表中的发言人
  const [speakers, setSpeakers] = useState([]);

  const val = React.useRef();
  const {
    localStreamConfig,
    RTC,
    isJoined,
    isPublished,
    remoteStreamConfigList,
    PlanetChatRoom,
    dispatch,
    spaceId,
    openCloseHandUp,
    liveRecord,
    getGuestList,
    getSpaceInfo,
    changeUrlParams,
    operateHandUp,
    shareOnClick,
    elapsedTime,
    onClickBack,
    isHorizontalLive,
    tim,
  } = props || {};

  const {
    msgListBySENDAPPLAUSE,
    guestListInfo,
    SpaceInfo,
    signInList,
    signInObj,
    handUpList,
    currentUserType,
    isMobile,
    currentLiveUserList,
    lastSeq,
    playerInfo,
    HiddenDanmu,
    sendFlowersCount,
    sendApplauseCount,
    ModalVisibleBySpaceViolation,
    ModalVisibleBySpaceRemoved,
    ModalVisibleByCancelAppointment,
    ModalVisibleByClosedSpace,
    ModalVisibleByAcceptLienMai,
    ModalVisibleByAppointmentClosedSpace,
    ModalVisibleByStartLive,
    ModalVisibleByNoMicrophone,
    ModalVisibleByOrientationWrong,
    ModalVisibleByVerticalPageWrong,
    ModalVisibleByLeaveMicrophone,
    ModalVisibleByForceWheat,
    ModalVisibleByEndRecording,
    ModalVisibleByKickedOut,
    autoExpandGuestArea,
    isModeMatrix,
    isShowCommentArea,
    isNotLogin,
    currentWatchMode,
    applyAdmissionList,
    isOpenTEduBoard,
  } = PlanetChatRoom || {};

  const {
    wxUserId,
    hostUserInfo,
    name: nameBySpaceInfo,
    kingdomName,
    status: statusBySpaceInfo,
    isSignIn,
    isCollect,
    handUpType,
    recordType,
    handUpStatusType,
    videoList,
    pv,
    gdp,
    videoSecond,
    spaceCoverUrlShow,
    kingdomId,
    isNeedPwd,
    starSpaceType,
    isHavCourseware,
  } = SpaceInfo || {};
  let starSpaceTypeLianMaiText = starSpaceType == 2 ? '发言' : '连麦';

  const userInfoData = JSON.parse(localStorage.getItem('userInfo'));
  let shareRemoteStreamConfig = null;

  const shareHostRemoteStreamConfig =
    SpaceInfo &&
    Array.isArray(remoteStreamConfigList) &&
    remoteStreamConfigList.find((item) => item.userID.indexOf('share') !== -1 && item.hasVideo);

  const hostRemoteStreamConfig =
    SpaceInfo &&
    Array.isArray(remoteStreamConfigList) &&
    remoteStreamConfigList.find((item) => item.userID == hostUserInfo.imUserId);

  let isUserSpeak =
    SpaceInfo &&
    Array.isArray(remoteStreamConfigList) &&
    remoteStreamConfigList.find(
      (item) => item.userID.indexOf('share') == -1 && item.audioVolume > 0,
    );
  let userCameraRemoteStreamListSort =
    Array.isArray(userCameraRemoteStreamList) &&
    sortRemoteStreamConfig(userCameraRemoteStreamList, speakers);

  const tiwRemoteStreamConfig =
    SpaceInfo &&
    Array.isArray(remoteStreamConfigList) &&
    remoteStreamConfigList.find((item) => item.userID.indexOf('tiw') !== -1);
  shareRemoteStreamConfig = tiwRemoteStreamConfig
    ? tiwRemoteStreamConfig
    : shareHostRemoteStreamConfig;

  let userCameraRemoteStreamListNotShare =
    SpaceInfo &&
    Array.isArray(remoteStreamConfigList) &&
    remoteStreamConfigList.filter(
      (item) => item.userID.indexOf('share') == -1 && (item.hasVideo || item.hasAudio),
    );

  let userCameraRemoteStreamList =
    SpaceInfo &&
    Array.isArray(userCameraRemoteStreamListNotShare) &&
    userCameraRemoteStreamListNotShare.filter(
      (item) => item.userID.indexOf('tiw') == -1 && (item.hasVideo || item.hasAudio),
    );

  const isModeMatrixCameraRemoteStreamList =
    SpaceInfo &&
    userCameraRemoteStreamList
      .filter((item) => item.userID != hostUserInfo.imUserId)
      .filter((item) =>
        Array.isArray(handUpList) && handUpList.length != 0
          ? handUpList.find((value) => {
              return value.imUserId != item.userID;
            })
          : true,
      );

  const handUpRemoteStreamList =
    SpaceInfo &&
    userCameraRemoteStreamList.filter(
      (item) =>
        Array.isArray(handUpList) &&
        handUpList.find((value) => {
          return value.imUserId == item.userID;
        }),
    );

  let cameralistArr = []
    .concat(shareRemoteStreamConfig ? [shareRemoteStreamConfig] : []) // 屏幕分享流保持第一位
    .concat(hostRemoteStreamConfig ? [hostRemoteStreamConfig] : []) // 主持人保持第一位
    .concat(localStreamConfig ? [localStreamConfig] : []) // 本人流保持第二位
    .concat(Array.isArray(handUpRemoteStreamList) ? handUpRemoteStreamList : []) // 连麦人的画面放在最后三位
    .concat(
      Array.isArray(isModeMatrixCameraRemoteStreamList) ? isModeMatrixCameraRemoteStreamList : [],
    ); // 嘉宾画面和连麦人保持第四位
  cameralistArr = cameralistArr.filter((item) => {
    if (!!item && !!item.stream) {
      return item;
    }
  }); // 去除空值 或无直播流的用户

  const playerRefByHorizontalLiveRoom = useRef(null);
  const videoContentRefByHorizontalLiveRoom = useRef(null);
  // const videoPlayer = useRef(null);
  const UerInfo = JSON.parse(localStorage.getItem('userInfo')) || {};

  // 试看结束从跳转到登录页面
  const TrialEnds = () => {
    // token失效 判定当前环境 小程序端跳转小程序用户页面,非小程序跳转到登录页面
    // 5表示friday app环境，不跳转登录页，不做任何处理
    if (getOperatingEnv() == '1') {
      wx && wx.miniProgram.redirectTo({ url: '/pages/Home/index' });
    } else if (getOperatingEnv() == '5') {
      // APP跳转登录
      openLoginInApp();
    } else {
      const url = window.location.href.split('?')[0];
      const currentTime = parseInt(videoPlayer?.currentTime() || 0);
      history.replace(
        `/User/login?${stringify({
          redirect: `${url}?lastTime=${currentTime}`,
          random: Math.random(),
          isShare: 1,
        })}`,
      );
    }
  };

  // 记录发言人
  useEffect(() => {
    if (!!isUserSpeak) {
      setSpeakers([...speakers, isUserSpeak.userID]);
    }
  }, [isUserSpeak && isUserSpeak.userID]);

  useEffect(() => {
    setVideoDuration(getVideoDuration());
    initializationByDanmu();
    resetTimer();
    document.addEventListener('click', handleClick);
    // 补充每隔10秒钟自动切换 展示送花还是鼓掌
    /* const interval = setInterval(() => {
      setCurrentShowFlowersOrApplause((prevValue) => (prevValue === 1 ? 2 : 1));
    }, 10000); */

    const inputElement = inputRef.current;
    if (inputElement) {
      // 添加focus事件监听，使软键盘贴合输入框
      inputElement.addEventListener('focus', handleFocus);
      // 添加blur事件监听，恢复页面滚动位置
      inputElement.addEventListener('blur', handleBlur);
    }
    if (isHorizontalLive) {
      // setIsIphone(isIphoneIslandSeries())
    }
    return () => {
      window.onresize = null;
      // clearInterval(interval)
      if (timer) {
        clearTimeout(timer);
      }
      if (inputElement) {
        inputElement.removeEventListener('focus', handleFocus);
        inputElement.removeEventListener('blur', handleBlur);
      }
      if (!!val?.current?.currentTime) {
        dispatch({
          type: 'PlanetChatRoom/saveVideoTime',
          payload: {
            wxUserId: wxUserId,
            spaceId: spaceId,
            videoSecond: _.floor(val.current.currentTime, 0),
          },
        });
      }
    };
  }, []);

  useEffect(() => {
    handleBlur();
  }, [
    ModalVisibleBySpaceViolation, // 是否显示弹窗(空间违规下架弹窗)
    ModalVisibleBySpaceRemoved, // 是否显示弹窗(该空间已下架)
    ModalVisibleByCancelAppointment, // 是否显示弹窗(取消预约)
    ModalVisibleByClosedSpace, // 是否显示弹窗(关闭空间)
    ModalVisibleByAcceptLienMai, // 是否显示弹窗(接受连麦)
    ModalVisibleByAppointmentClosedSpace, // 是否显示弹窗预约状态下(关闭空间)
    ModalVisibleByStartLive, // 是否显示弹窗(马上开始直播)
    ModalVisibleByNoMicrophone, // 是否显示弹窗(主播或嘉宾无麦克风设备)
    ModalVisibleByOrientationWrong, // 是否显示弹窗(横屏切换)
    ModalVisibleByVerticalPageWrong, // 是否显示弹窗(当前页面是横向但页面是竖版页面)
    ModalVisibleByLeaveMicrophone, // 确认下麦
    ModalVisibleByForceWheat, // 强制下麦
    ModalVisibleByEndRecording, // 是否显示弹窗(结束录制)
    ModalVisibleByKickedOut, // 是否显示弹窗(多端登录被踢)
  ]);

  // 补充横屏模式弹幕窗口有焦点时不隐藏控制区域
  const handleFocus = () => {
    setIsDanmuInputFocus(true);
  };
  const handleBlur = () => {
    setIsDanmuInputFocus(false);
    handleClick();
  };

  useEffect(() => {
    if (!!hostRemoteStreamConfig?.stream && isNotLogin) {
      loginTimer();
    }
  }, [!!hostRemoteStreamConfig && !!hostRemoteStreamConfig?.stream]);

  // 登录计时
  const loginTimer = () => {
    // 如果用户未登录倒计时10分钟后,出现登录弹窗
    const countdownTime = 10 * 60 * 1000; // 10分钟
    // const countdownTime = 10000; // 10分钟
    // 设置倒计时
    setTimeout(() => {
      // 在倒计时结束后执行的操作，比如显示登录弹窗
      setIsShowTrySeeTip(true);
    }, countdownTime);
  };

  useEffect(() => {
    val.current = playerStateData;
  }, [playerStateData]);

  // 根据触摸点计算距离的函数示例
  const getDistance = (point1, point2) => {
    const x = point2.clientX - point1.clientX;
    const y = point2.clientY - point1.clientY;
    return Math.sqrt(x * x + y * y);
  };

  // 申请进入会议成员相关
  useEffect(() => {
    if (guestListType == 2) {
      getApplyAdmissionList();
    } else if (guestListType == 1) {
      getGuestList();
    }
  }, [guestListType]);

  // 拒绝或准入申请进入会议成员
  const updateStarSpaceApplyAdmission = async (params) => {
    const {
      wxUserId,
      isAgree, // 是否同意：0申请中,1同意，2拒绝
      refuseAdmittance, // 操作类型：1拒绝、2准入
    } = params || {};
    const res = await dispatch({
      type: 'PlanetChatRoom/updateStarSpaceApplyAdmission',
      payload: {
        applyAdmissionId: wxUserId, //	是 1 申请记录ID
        refuseAdmittance: refuseAdmittance, //	是 1 操作类型：1:拒绝、2:准入
      },
    });
    const { code, content } = res || {};
    if (code == 200 && content) {
      await getApplyAdmissionList();
      await props.sendMessageByIm({
        dataType: UPDATA_STATE,
        description: JSON.stringify({ imUserId: 'ALL' }),
      });
    }
  };

  // 获取申请进入会议成员列表
  const getApplyAdmissionList = async () => {
    if (!SpaceInfo?.id) {
      return;
    }
    let response = await dispatch({
      type: 'PlanetChatRoom/getApplyAdmissionList',
      payload: {
        spaceId: SpaceInfo.id,
      },
    });
    if (!response || (response && response.code != 200)) {
      if (response && response.status != 401) {
        message.error('获取成员列表信息失败');
      }
      return;
    }
  };

  const initializeAlloyFinger = (videoElement) => {
    let lastDistance = 0; // 上一次的手指距离
    let lastScale = 1; // 上一次的缩放比例
    let scale = 1; // 当前缩放比例
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // scale = 1.5;
    // setScale(scale);
    // videoElement.style.transform = `scale(${scale})`;

    const af = new AlloyFinger(videoElement, {
      multipointStart: (evt) => {
        evt.preventDefault();
        // 计算初始手指之间的距离
        lastDistance = getDistance(evt.touches[0], evt.touches[1]);
      },
      pinch: (evt) => {
        evt.preventDefault();
        // 计算当前手指之间的距离
        const currentDistance = getDistance(evt.touches[0], evt.touches[1]);
        // 计算缩放比例的变化
        const delta = currentDistance - lastDistance;
        const zoomFactor = delta / window.innerWidth; // 调整比例因子
        scale = lastScale + zoomFactor;

        // 对缩放比例进行限制
        if (scale < 1) {
          scale = 1;
        } else if (scale > 5) {
          scale = 5;
        }

        // 更新视频元素的缩放
        videoElement.style.transform = `scale(${scale})`;

        // 记录当前的缩放比例
        lastScale = scale;
        lastDistance = currentDistance;
      },
      multipointEnd: (evt) => {
        evt.preventDefault();
        // 缩放结束时，保存最终的缩放比例
        setScale(scale);
      },
      pressMove: (evt) => {
        evt.preventDefault();
        if (scale > 1) {
          const deltaX = evt.deltaX / 2;
          const deltaY = evt.deltaY / 2;
          const rect = videoElement.getBoundingClientRect();

          // 计算移动后的位置
          const newLeft = rect.left + deltaX;
          const newTop = rect.top + deltaY;
          const newRight = newLeft + rect.width;
          const newBottom = newTop + rect.height;

          // 检查边界 检查当前移动范围
          // const moveX = Math.max(Math.min(viewportWidth - rect.width, newLeft), 0);
          // const moveY = Math.max(Math.min(viewportHeight - rect.height, newTop), 0);

          const moveX = rect.left + deltaX;
          const moveY = rect.top + deltaY;

          videoElement.style.transform = `translate(${moveX}px, ${moveY}px) scale(${scale})`;
          // console.log('videoElement.style.transform12312 :: ',videoElement.style.transform);
        }
      },
    });
  };

  // Initialize the Video.js player
  React.useEffect(() => {
    if (statusBySpaceInfo != 3 && !(Array.isArray(videoList) && videoList[0])) {
      return;
    }
    if (!playerRefByHorizontalLiveRoom.current) {
      return;
    }
    const { location } = history || {};
    const { query } = location || {};
    let { lastTime } = query || {};

    let currentTimeByVideoSecond = lastTime != 0 ? lastTime : null || videoSecond || 0;
    // 初始化播放器
    const videoJsNode = playerRefByHorizontalLiveRoom.current;
    const videoContentNode = videoContentRefByHorizontalLiveRoom.current;
    if (videoJsNode && videoJsNode.id) {
      // [手势放大缩小功能]-在组件加载时初始化 AlloyFinger
      setTimeout(() => {
        let sources =
          Array.isArray(videoList) &&
          videoList.map((item) => {
            const { vodPathUrl } = item || {};
            if (!vodPathUrl) {
              return;
            }
            let vodByDAes = getDAesString(vodPathUrl, 'arrail-dentail&2', 'arrail-dentail&3');
            return { src: vodByDAes };
          });

        if (!videoJsNode.id) {
          return;
        }
        try {
          // 获取播放器实例
          let TCPlayerObj = TCPlayer(videoJsNode, {
            sources: sources,
            licenseUrl: licenseUrl,
          });
          if (!TCPlayerObj) {
            return;
          }
          TCPlayerObj.ready(async () => {
            setVideoPlayer(TCPlayerObj);
            await setTimeout(() => {}, 1000);
            await setPlayerStateData({
              currentTime: TCPlayerObj.currentTime(),
              duration: TCPlayerObj.duration()
                ? TCPlayerObj.duration()
                : videoList[0].recordDuration,
              paused: TCPlayerObj && TCPlayerObj.paused && TCPlayerObj.paused(),
            });
          });

          // 监听播放器状态和播放进度
          TCPlayerObj.on('timeupdate', (value) => {
            let state = {
              currentTime: TCPlayerObj?.currentTime(),
              duration: TCPlayerObj?.duration(),
              paused: TCPlayerObj?.paused(),
            };
            handlePlayerStateChange(state);
            //[判定登录] 未登录只可以试看10分钟
            if (isNotLogin) {
              if (TCPlayerObj?.currentTime() >= 600) {
                setIsShowTrySeeTip(true);
                TCPlayerObj?.pause();
              } else {
                setIsShowTrySeeTip(false);
              }
            }
          });

          TCPlayerObj.on('error', (value) => {
            // 修改报错文案
          });

          TCPlayerObj.one('canplay', (value) => {
            setTimeout(async () => {
              if (!!playerInfo && TCPlayerObj) {
                await setTimeout(() => {}, 1000);
                await TCPlayerObj.currentTime(
                  playerInfo.currentTime ? playerInfo.currentTime : currentTimeByVideoSecond,
                );
                // 监听播放器状态和播放进度
                if (!!TCPlayerObj.paused()) {
                  await TCPlayerObj.pause();
                } else {
                  await autoPlay(TCPlayerObj);
                }
              } else {
                TCPlayerObj.currentTime(currentTimeByVideoSecond);
                await autoPlay(TCPlayerObj);
              }
              await props.changeUrlParams({ lastTime: null });
            }, 100);
          });

          // 监听播放器静音状态
          TCPlayerObj.on('volumechange', (value) => {
            if (TCPlayerObj.muted()) {
              TCPlayerObj.muted(false); // 静音状态下自动播放
            }
          });

          TCPlayerObj.on('blocked', async (value) => {
            Modal.alert({
              content: '播放已被浏览器暂停,点击继续播放。',
              onConfirm: (e) => {
                TCPlayerObj.play();
              },
            });
          });
        } catch (error) {
          console.log(error);
        }
      }, 1000);
    }
  }, [videoList]);

  useEffect(() => {
    return () => {
      if (!!videoPlayer) {
        videoPlayer.dispose();
      }
    };
  }, [videoPlayer]);

  useEffect(() => {
    if (isNeedPwd == 0 && videoPlayer) {
      videoPlayer.play();
    }
  }, [isNeedPwd, videoPlayer]);

  const autoPlay = async (videoPlayer) => {
    if (!!videoPlayer && isNeedPwd == 0) {
      await videoPlayer.muted(true);
      await setTimeout(() => {}, 100);
      await videoPlayer.play();
      await setTimeout(() => {}, 100);
      await videoPlayer.muted(false);
    }
  };

  // 接收申请连麦转换身份
  useEffect(async () => {
    if (RTC && statusBySpaceInfo == 1 && currentUserType == 3) {
      setHandUpStatusTypeEnter(false);
    }
  }, [handUpStatusType]);

  const resetTimer = () => {
    clearTimeout(timer);
    setTimer(setTimeout(handleComplete, 10000));
  };
  const handleClick = useDebounce((e) => {
    // 判定当前是否是暂停
    if (playerStateData && playerStateData.paused) {
      setIsHiddenControlArea(false);
      return;
    }
    if (!!isHiddenControlArea) {
      setIsHiddenControlArea(false);
      resetTimer();
    } else {
      // 当打开控制区域时则隐藏
      setIsHiddenControlArea(true);
    }
  }, 500);
  const handleComplete = () => {
    if (isDanmuInputFocus) return;
    setIsHiddenControlArea(true);
  };

  // 关闭或开启弹幕 默认开启
  useEffect(() => {
    if (HiddenDanmu) {
      RTCDanmu && RTCDanmu.stop();
    } else {
      RTCDanmu && RTCDanmu.start();
    }
  }, [HiddenDanmu]);

  // 订阅video播放器的状态
  let handlePlayerStateChange = (state, prevState) => {
    setPlayerStateData(state);
    getSpaceBulletScreen(state); // 从获取的历史弹幕中 匹配时间发送弹幕
    findItemDanmuByCurrentTime(state); // 录播分时间获取弹幕消息 每1分钟请求一次
  };

  // 从获取的历史弹幕中 匹配时间发送弹幕
  let findItemDanmuByCurrentTime = _.throttle(async (state) => {
    const { recordStartTime } = videoList[0] || {};
    const { currentTime } = state || {};
    let currentTimeByDayJS = dayjs(recordStartTime, 'YYYY-MM-DD HH:mm:ss')
      .add(_.floor(currentTime, 0), 'second')
      .format('YYYY-MM-DD HH:mm:ss');
    let Danmulist = await dispatch({ type: 'PlanetChatRoom/getSpaceGroupMsgByMsgSeqByState' });
    let findItemByDanmulist =
      Danmulist &&
      Danmulist.find((item) => {
        return item.msgDataTime == currentTimeByDayJS;
      });
    if (findItemByDanmulist) {
      sendDanmu({
        text: findItemByDanmulist.bs,
        userInfoByDanmu: {
          id: findItemByDanmulist.wxUserId,
          name: findItemByDanmulist.name,
          currentUserType: findItemByDanmulist.currentUserType,
          headUrl: findItemByDanmulist.headUrlShow,
          imagePhotoPathShow: findItemByDanmulist.headUrlShow,
        },
      });
    }
  }, 1000);

  // 录播分时间获取弹幕消息 每1分钟请求一次
  let getSpaceBulletScreen = _.throttle((state) => {
    const { recordStartTime } = videoList[0] || {};
    const { currentTime } = state || {};
    let currentTimeByDayJS = dayjs(recordStartTime, 'YYYY-MM-DD HH:mm:ss')
      .add(_.floor(currentTime, 0), 'second')
      .format('YYYY-MM-DD HH:mm:ss');
    getSpaceBulletScreenByModel(currentTimeByDayJS);
  }, 60000);

  let onChangeBySlider = useDebounce((value) => {
    const { recordStartTime } = videoList[0] || {};
    videoPlayer?.currentTime(value);
    let currentTimeByDayJS = dayjs(recordStartTime, 'YYYY-MM-DD HH:mm:ss')
      .add(_.floor(value, 0), 'second')
      .format('YYYY-MM-DD HH:mm:ss');
    getSpaceBulletScreenByModel(currentTimeByDayJS);
  }, 100);

  const getSpaceBulletScreenByModel = async (currentTimeByDayJS) => {
    const dataByGetSpaceGroupMsg = await dispatch({
      type: 'PlanetChatRoom/getSpaceBulletScreen',
      payload: {
        spaceId: spaceId,
        eventTime: currentTimeByDayJS,
      },
    });
  };

  // 暴露给父组件的方法
  useImperativeHandle(props.onRefByHorizontalLiveRoom, () => {
    return {
      sendDanmu: sendDanmu,
    };
  });

  // 点击展示摄像头列表
  const onClickByHorizontalLiveRoom_camera_picture_Box = (e) => {
    // 点击后阻止事件冒泡,防止触发父元素的点击事件
    e.stopPropagation();
    resetTimer();
    setIsShowCameraList(!isShowCameraList);
  };

  // 当有人开启或关闭摄像头时或开启语音时自动展开嘉宾区 当无人开启摄像头时和关闭语音时自动收起嘉宾区
  const setIsShowCameraListByAutoExpandGuestArea = () => {
    if (autoExpandGuestArea == 1) {
      setIsShowCameraList(true);
      dispatch({ type: 'PlanetChatRoom/setState', payload: { autoExpandGuestArea: null } });
    } else if (autoExpandGuestArea == 2 && Array.isArray(userCameraRemoteStreamList)) {
      if (userCameraRemoteStreamList) {
        const allMutedAudioFalse = userCameraRemoteStreamList.every((item) => item.mutedAudio);
        const allMutedVideoFalse = userCameraRemoteStreamList.every((item) => item.mutedVideo);
        let allMutedBoom = allMutedAudioFalse && allMutedVideoFalse;
        if (allMutedBoom) {
          setIsShowCameraList(false);
        }
      }
      dispatch({ type: 'PlanetChatRoom/setState', payload: { autoExpandGuestArea: null } });
    }
  };
  // 是否自动展开嘉宾摄像头区域
  useEffect(setIsShowCameraListByAutoExpandGuestArea, [
    autoExpandGuestArea,
    userCameraRemoteStreamList,
  ]);

  // 获取video的获取视频时长
  const getVideoDuration = () => {
    let video = document.querySelector('video');
    if (video) {
      let duration = video.duration;
      let hour = parseInt(duration / 3600);
      let minute = parseInt((duration % 3600) / 60);
      let sec = parseInt(duration % 60);
      return video.duration;
    }
    return 0;
  };

  // 创建新Danmu text:弹幕内容, level:弹幕等级 1:普通弹幕 2:关注弹幕 3:高级弹幕
  const sendDanmu = ({ text, userInfoByDanmu }) => {
    // 弹幕Dom
    let elByDanmu = sendDanmuCreateItemDom({ text, userInfoByDanmu });
    let idByNewDanmu = moment().format('YYYYMMDDHHmmss');
    RTCDanmu &&
      RTCDanmu.sendComment({
        duration: 10000,
        start: 100,
        id: parseInt(`${idByNewDanmu}${Math.floor(Math.random() * 10000)}`),
        el: elByDanmu, //弹幕文字内容
        style: {
          fontSize: '20px',
        },
      });
  };

  // 初始化弹幕组件
  let initializationByDanmu = async () => {
    let playerByMS = document.getElementById('ms');

    let danmu = new DanmuJs({
      channelSize: 24, // 轨道大小
      container: document.querySelector('#vs'), //弹幕容器，该容器发生尺寸变化时会自动调整弹幕行为
      containerStyle: {
        //弹幕容器样式
        // zIndex: 1001
      },
      live: true,
      player: playerByMS, // 配合音视频元素（video或audio）同步使用时需提供该项
      isLive: true,
      // direction,
      comments: [], // 弹幕预存数组,配合音视频元素（video或audio）同步使用时需提供该项
      chaseEffect: false, // 开启滚动弹幕追逐效果, 默认为true
      mouseControl: true, // 打开鼠标控制, 打开后可监听到 bullet_hover 事件。danmu.on('bullet_hover', function (data) {})
      mouseControlPause: true, // 鼠标触摸暂停。mouseControl: true 生效
      // channelSize: 24,       // 轨道大小
      area: {
        // 弹幕显示区域
        start: 0, // 区域顶部到播放器顶部所占播放器高度的比例
        end: 1, // 区域底部到播放器顶部所占播放器高度的比例
      },
      dropStaleComments: true,
    });
    setRTCDanmu(danmu); // 存储danmu控制对象
  };

  const handleChangeByLocalStreamConfig = useCallback(
    (name, e) => {
      e && e.preventDefault();
      props.onChange && props.onChange({ name, stream: localStreamConfig.stream });
    },
    [localStreamConfig, props],
  );

  useEffect(() => {
    // msg_content将内容区域 滚动到最底部
    let liveRoom_msg_list = document.getElementById('liveRoom_msg_list');
    if (liveRoom_msg_list) {
      liveRoom_msg_list.scrollTop = liveRoom_msg_list.scrollHeight;
    }
    // 当前有新的鼓掌或送花 则展示鼓掌送花区域
    if (Array.isArray(msgListBySENDAPPLAUSE) && msgListBySENDAPPLAUSE.length > 0) {
      // 获取数组中的最后一位
      let lastItem = msgListBySENDAPPLAUSE[msgListBySENDAPPLAUSE.length - 1];
      const { msgType } = lastItem || {};
      if (msgType == SEND_FLOWERS) {
        // 送花
        setCurrentShowFlowersOrApplause(1);
      } else if (msgType == SEND_APPLAUSE) {
        // 鼓掌
        setCurrentShowFlowersOrApplause(2);
      }
    }
  }, [Array.isArray(msgListBySENDAPPLAUSE) && msgListBySENDAPPLAUSE.length]);

  useEffect(() => {
    // 当前有新的鼓掌或送花 则展示鼓掌送花区域,展示后3秒后隐藏
    if (currentShowFlowersOrApplause) {
      hideCurrentShowFlowersOrApplause();
    }
  }, [
    currentShowFlowersOrApplause,
    Array.isArray(msgListBySENDAPPLAUSE) && msgListBySENDAPPLAUSE.length,
  ]);

  const hideCurrentShowFlowersOrApplause = useDebounce(() => {
    setCurrentShowFlowersOrApplause(null);
  }, 3000);

  /* -----------按钮交互-------------- */
  // 点击收藏空间
  const onClickByCollect = async (e) => {
    //[判定登录] 判定是否已登录,未登录则弹出登录弹窗
    if (isNotLogin) {
      setModalVisibleByUserTokenInvalid();
      return null;
    }
    // 点击后阻止事件冒泡,防止触发父元素的点击事件
    e.stopPropagation();
    resetTimer();
    let wxuserId = UerInfo && UerInfo.friUserId;
    await dispatch({
      type: 'PlanetChatRoom/spaceCollect',
      payload: {
        spaceId: spaceId,
        wxUserId: wxuserId,
        collectType: isCollect == 1 ? 0 : 1, // 空间收藏 0:取消收藏 1:收藏
      },
    });
  };

  // 关注 取关 专家
  const isFocusByOnClick = async ({ expertsUserId, isFocus, type }) => {
    let wxuserId = UerInfo && UerInfo.friUserId;
    // 关注取消专家
    await dispatch({
      type: 'PlanetChatRoom/isFocus',
      payload: {
        wxUserId: wxuserId, // [string]	是	用户ID
        expertsUserId: expertsUserId, // [string]	是  被关注的人
        isFocus: isFocus, // [string]	是	1 关注 0取消关注
      },
    });
    // 取消关注专家
    if (type == 1) {
      await getGuestList();
    } else if (type == 2) {
      await getSpaceInfo();
    } else if (type == 3) {
      await getHandUpList();
    }
  };

  // 获取查看空间连麦列表
  const getHandUpList = async () => {
    let wxuserId = UerInfo && UerInfo.friUserId;
    await dispatch({
      type: 'PlanetChatRoom/getHandUpList',
      payload: {
        spaceId: spaceId,
        wxUserId: wxuserId,
        pageNum: 1,
        pageSize: 100,
      },
    });
  };

  // 补充点击styles.ApplyConnectLiveListWarp区域的内容,不隐藏列表,
  // 当点击styles.ApplyConnectLiveListWarp区域外区域的内容 则触发 setIsShowApplyForLinkMicList(false);
  // 隐藏设置列表
  const handleClickOutside = (event) => {
    if (
      wrapperRefByApplyConnectLiveListWarp.current &&
      !wrapperRefByApplyConnectLiveListWarp.current.contains(event.target)
    ) {
      if (!!isShowApplyForLinkMicList) {
        setIsShowApplyForLinkMicList(false);
      }
    }
  };

  // 展示去登录弹窗
  const setModalVisibleByUserTokenInvalid = () => {
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: { ModalVisibleByUserTokenInvalid: true },
    });
  };

  // 评论区域向上滚动加载更多
  const onScrollByLiveRoomMessageList = async (e) => {
    const { target } = e || {};
    const { scrollTop } = target || {};
    if (scrollTop == 0) {
      if (!hasMoreByMsgList) {
        return;
      }
      const dataByGetSignInList = await dispatch({
        type: 'PlanetChatRoom/getSpaceGroupMsg',
        payload: {
          spaceId: SpaceInfo.id,
          msgSeq: lastSeq,
          pageSize: 10,
        },
      });
      const { content } = dataByGetSignInList || {};
      const { resultList } = content || {};
      setHasMoreByMsgList(Array.isArray(resultList) && resultList.length > 0);
      target.scrollTop = 700;
    }
  };

  // 开启或关闭弹幕
  const handleToggleDanmuVisibility = useCallback(
    (e) => {
      // 防止事件冒泡，防止触发父元素的点击事件
      e.stopPropagation();
      // 重置计时器
      resetTimer();
      // 切换弹幕可见性状态
      dispatch({
        type: 'PlanetChatRoom/setState',
        payload: {
          HiddenDanmu: !HiddenDanmu,
        },
      });
    },
    [resetTimer, dispatch, HiddenDanmu],
  );
  // visibilitychange检测页面失去焦点事件
  const handleVisibilityChange = () => {
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        // 页面失去焦点
        openOrCloseH5SmallWindow();
      } else {
        // 页面获得焦点
        openOrCloseH5SmallWindow();
      }
    });
  };

  // 开启调用h5小窗口播放
  const openOrCloseH5SmallWindow = async () => {
    // const video = playerRefByHorizontalLiveRoom && playerRefByHorizontalLiveRoom.current;
    let videoWarpDom = document.getElementsByClassName('video-react-video')[0];
    let video = videoWarpDom.querySelector('video');
    if (video) {
      // console.log('video !== document.pictureInPictureElement : ',video,video !== document.pictureInPictureElement);
      try {
        if (video !== document.pictureInPictureElement) {
          // 将视频设置为 Picture-in-Picture 模式
          await video.requestPictureInPicture();
        } else {
          // 退出 Picture-in-Picture 模式
          await document.exitPictureInPicture();
        }
      } catch (error) {
        console.error('Error:', error);
      }
    } else {
    }
  };

  // video-竖屏控制进度条等组件
  const video_ModeratorControl = () => {
    {
      /* 回放video控制区域 */
    }
    return (
      <>
        {!props.PlanetChatRoom.isLive &&
          playerStateData &&
          playerStateData.duration != 0 &&
          Array.isArray(videoList) &&
          videoList.length > 0 &&
          !isHorizontalLive && (
            <div className={styles.video_ModeratorControl_VerticalLiveRoom}>
              {/*[按钮] 播放回放*/}
              {!!playerStateData.paused && (
                <div
                  className={styles.playBtn}
                  onClick={() => {
                    videoPlayer?.play();
                  }}
                ></div>
              )}
              {/*[按钮] 暂停回放 */}
              {!playerStateData.paused && (
                <div
                  className={styles.PauseBtn}
                  onClick={() => {
                    videoPlayer?.pause();
                  }}
                ></div>
              )}

              {/* 播放进度条 */}
              <div className={styles.video_Progress_bar_warp}>
                <div className={styles.video_Progress_bar}>
                  <Slider
                    value={_.floor(playerStateData.currentTime, 0)}
                    tooltip={{ open: false }}
                    min={0}
                    max={_.floor(playerStateData.duration, 0)}
                    onChange={onChangeBySlider}
                  />
                </div>
                <div className={styles.time_Progress}>
                  <span>
                    {playerStateData.currentTime
                      ? formatTime(_.floor(playerStateData.currentTime))
                      : '00:00:00'}
                  </span>
                  <span>/</span>
                  <span>
                    {playerStateData.duration
                      ? formatTime(_.floor(playerStateData.duration))
                      : '00:00:00'}
                  </span>
                </div>
              </div>
              {/*[按钮] 切换画中画*/}
              {!PlanetChatRoom.isLive && playerStateData && playerStateData.duration != 0 && (
                <div
                  className={styles.FullScreenBtn_p}
                  onClick={async () => {
                    // 点击切换画中画模式
                    openOrCloseH5SmallWindow();
                  }}
                ></div>
              )}

              {/*[按钮] 切换全屏按钮 */}
              <div
                onClick={async () => {
                  await dispatch({
                    type: 'PlanetChatRoom/setState',
                    payload: {
                      isHorizontalLive: true,
                      playerInfo: playerStateData,
                    },
                  });
                  await changeUrlParams({ isHorizontalLive: 1 });
                }}
                className={styles.FullScreenBtn}
              ></div>
            </div>
          )}
      </>
    );
  };

  // 主持人控制区域横版_PC版本
  const Video_control_HorizontalLiveRoom_PC = () => {
    if (isMobile) {
      return <></>;
    }
    if (PlanetChatRoom.isLive) {
      return (
        <>
          <div
            className={classNames({
              [styles.video_ModeratorControl]: true,
              [styles.video_ModeratorControl_PC]: true,
              [styles.isHiddenControlArea]: isHiddenControlArea,
            })}
          >
            <div
              className={classNames({
                [styles.video_control_btn_warp]: true,
                [styles.video_control_btn_warp_PC]: true,
              })}
            >
              <div style={{ flex: 'unset' }} className={styles.video_control_btn_left_PC}>
                {/* [按钮]结束直播 */}
                {props.PlanetChatRoom.isLive && isJoined && currentUserType == 1 && (
                  <div className={styles.btn_content_warp_mid_PC}>
                    <FinishLiveButton
                      isLive={PlanetChatRoom.isLive}
                      isJoined={isJoined}
                      currentUserType={currentUserType}
                      resetTimer={resetTimer}
                      getSpaceInfo={getSpaceInfo}
                      dispatch={dispatch}
                    />
                  </div>
                )}

                {/* [按钮]开始直播 */}
                {props.PlanetChatRoom.isLive && !isJoined && currentUserType == 1 && (
                  <div className={styles.btn_content_warp_mid_PC}>
                    <StartLiveButton
                      isLive={PlanetChatRoom.isLive}
                      isJoined={isJoined}
                      currentUserType={currentUserType}
                      resetTimer={resetTimer}
                      getSpaceInfo={getSpaceInfo}
                      dispatch={dispatch}
                    />
                  </div>
                )}

                {/*[按钮] 麦克风静音/取消静音-按钮  */}
                {props.PlanetChatRoom.isLive &&
                  isJoined &&
                  isPublished &&
                  (currentUserType == 1 ||
                    currentUserType == 2 ||
                    (currentUserType == 3 && handUpStatusType == 1)) && (
                    <div className={styles.btn_content_warp_mid_PC}>
                      <MicrophoneToggle
                        isLive={PlanetChatRoom.isLive}
                        isJoined={isJoined}
                        isPublished={isPublished}
                        currentUserType={currentUserType}
                        handUpStatusType={handUpStatusType}
                        resetTimer={resetTimer}
                        localStreamConfig={localStreamConfig}
                        handleChangeByLocalStreamConfig={handleChangeByLocalStreamConfig}
                      />
                    </div>
                  )}

                {/* 打开/关闭-摄像头 */}
                {props.PlanetChatRoom.isLive &&
                  isJoined &&
                  isPublished &&
                  (currentUserType == 1 ||
                    currentUserType == 2 ||
                    (currentUserType == 3 && handUpStatusType == 1)) && (
                    <div className={styles.btn_content_warp_mid_PC}>
                      <CameraToggle
                        isLive={PlanetChatRoom.isLive}
                        isJoined={isJoined}
                        isPublished={isPublished}
                        currentUserType={currentUserType}
                        handUpStatusType={handUpStatusType}
                        resetTimer={resetTimer}
                        localStreamConfig={localStreamConfig}
                        handleChangeByLocalStreamConfig={handleChangeByLocalStreamConfig}
                      />
                    </div>
                  )}
              </div>

              {/* PC中间区域控制 */}
              <div className={styles.video_control_btn_middle_PC}>
                {/* [按钮]PC_收藏本直播间 */}
                <div className={styles.btn_content_warp_mid_PC}>
                  <CollectButton isCollect={isCollect} onClickByCollect={onClickByCollect} />
                </div>

                {/* [按钮]直播间打卡 */}
                {/* 直播间预约状态无法打卡 */}
                {statusBySpaceInfo && (
                  // && statusBySpaceInfo != 2
                  // && currentUserType != 1 // 主持人不能给自己打卡
                  <div className={styles.btn_content_warp_mid_PC}>
                    <SignInButton
                      isShowApplyForLinkMicList={isShowApplyForLinkMicList}
                      resetTimer={resetTimer}
                      isNotLogin={isNotLogin}
                      setModalVisibleByUserTokenInvalid={setModalVisibleByUserTokenInvalid}
                      sendMessageByIm={props.sendMessageByIm}
                      dispatch={dispatch}
                      SpaceInfo={SpaceInfo}
                      spaceId={spaceId}
                      currentUserType={currentUserType}
                      isSignIn={isSignIn}
                      setIsShowApplyForLinkMicList={setIsShowApplyForLinkMicList}
                      setHasMoreBySignInList={setHasMoreBySignInList}
                      SideListTypeForSignInList={SideListTypeForSignInList}
                    />
                  </div>
                )}

                {/* [按钮]直播间分享，APP环境中分享展示出来 */}
                {(getOperatingEnv() == '2' ||
                  getOperatingEnv() == '7' ||
                  getOperatingEnv() == '5' ||
                  getOperatingEnv() == '6') && (
                  <>
                    <div className={styles.btn_content_warp_mid_PC}>
                      <ShareButton
                        resetTimer={resetTimer}
                        isNotLogin={isNotLogin}
                        setModalVisibleByUserTokenInvalid={setModalVisibleByUserTokenInvalid}
                        shareOnClick={shareOnClick}
                      />
                    </div>
                  </>
                )}

                {/*[按钮] 屏幕投屏按钮*/}
                {props.PlanetChatRoom.isLive &&
                  localStreamConfig &&
                  isJoined &&
                  !isMobile &&
                  (currentUserType == 1 || currentUserType == 2) && (
                    <div className={styles.btn_content_warp_mid_PC}>
                      <ScreenShareButton
                        isMobile={isMobile}
                        resetTimer={resetTimer}
                        isLive={props.PlanetChatRoom.isLive}
                        localStreamConfig={localStreamConfig}
                        isJoined={isJoined}
                        currentUserType={currentUserType}
                        handleChangeByLocalStreamConfig={handleChangeByLocalStreamConfig}
                        shareRemoteStreamConfig={shareRemoteStreamConfig}
                      />
                    </div>
                  )}

                {/*[按钮] 分享课件*/}
                {props.PlanetChatRoom.isLive &&
                  isHavCourseware == 1 &&
                  localStreamConfig &&
                  isJoined &&
                  (currentUserType == 1 || currentUserType == 2) && (
                    <div className={styles.btn_content_warp_mid_PC}>
                      <WhiteBoardButton
                        spaceId={spaceId}
                        isMobile={isMobile}
                        isLive={props.PlanetChatRoom.isLive}
                        localStreamConfig={localStreamConfig}
                        tiwRemoteStreamConfig={shareHostRemoteStreamConfig}
                        isJoined={isJoined}
                        currentUserType={currentUserType}
                        isOpenTEduBoard={isOpenTEduBoard}
                        resetTimer={resetTimer}
                        handleChangeByLocalStreamConfig={handleChangeByLocalStreamConfig}
                        shareHostRemoteStreamConfig={shareHostRemoteStreamConfig}
                      />
                    </div>
                  )}

                {/*[按钮] 录屏 */}
                {props.PlanetChatRoom.isLive &&
                  localStreamConfig &&
                  isJoined &&
                  currentUserType == 1 && (
                    <div className={styles.btn_content_warp_mid_PC}>
                      <RecordButton
                        isLive={props.PlanetChatRoom.isLive}
                        localStreamConfig={localStreamConfig}
                        isJoined={isJoined}
                        currentUserType={currentUserType}
                        resetTimer={resetTimer}
                        recordType={recordType}
                        dispatch={dispatch}
                        liveRecord={liveRecord}
                        elapsedTime={elapsedTime}
                      />
                    </div>
                  )}

                {/*[按钮] 鼓掌*/}
                <div className={styles.btn_content_warp_mid_PC}>
                  <ApplauseButton
                    resetTimer={resetTimer}
                    isNotLogin={isNotLogin}
                    setModalVisibleByUserTokenInvalid={setModalVisibleByUserTokenInvalid}
                    sendMessageByIm={props.sendMessageByIm}
                  />
                </div>

                {/*[按钮] 送花*/}
                <div className={styles.btn_content_warp_mid_PC}>
                  <FlowersButton
                    resetTimer={resetTimer}
                    isNotLogin={isNotLogin}
                    setModalVisibleByUserTokenInvalid={setModalVisibleByUserTokenInvalid}
                    sendMessageByIm={props.sendMessageByIm}
                  />
                </div>

                {/* [按钮] 开启连麦 */}
                {props.PlanetChatRoom.isLive && currentUserType == 1 && isJoined && (
                  <div className={styles.btn_content_warp_mid_PC}>
                    <ConnectButton
                      starSpaceType={starSpaceType}
                      resetTimer={resetTimer}
                      handUpType={handUpType}
                      openCloseHandUp={openCloseHandUp}
                      setContentListType={() => {}}
                    />
                  </div>
                )}

                {/* [按钮] 开启申请列表 */}
                {props.PlanetChatRoom.isLive && isJoined && currentUserType == 1 && (
                  // && isPublished
                  <div className={styles.btn_content_warp_mid_PC}>
                    <LinkMicApplicationsToggle
                      isShowApplyForLinkMicList={isShowApplyForLinkMicList}
                      resetTimer={resetTimer}
                      isLive={PlanetChatRoom.isLive}
                      isJoined={isJoined}
                      setIsShowApplyForLinkMicList={setIsShowApplyForLinkMicList}
                      getHandUpList={getHandUpList}
                      handUpList={handUpList}
                    />
                  </div>
                )}

                {/* [按钮] 打开嘉宾列表 */}
                {
                  <div className={styles.btn_content_warp_mid_PC}>
                    <GuestListButton
                      starSpaceType={starSpaceType}
                      isShowApplyForLinkMicList={isShowApplyForLinkMicList}
                      resetTimer={resetTimer}
                      isLive={PlanetChatRoom.isLive}
                      isJoined={isJoined}
                      setIsShowApplyForLinkMicList={setIsShowApplyForLinkMicList}
                    />
                  </div>
                }

                {/* [按钮] 展开或收起设置 */}
                {
                  <div className={styles.btn_content_warp_mid_PC}>
                    <SetConfigButton
                      isShowApplyForLinkMicList={isShowApplyForLinkMicList}
                      resetTimer={resetTimer}
                      isLive={PlanetChatRoom.isLive}
                      isJoined={isJoined}
                      setIsShowApplyForLinkMicList={setIsShowApplyForLinkMicList}
                    />
                  </div>
                }
              </div>
              <div className={styles.video_control_btn_right_PC}>
                {/*[按钮] 申请连麦*/}
                {props.PlanetChatRoom.isLive &&
                  isJoined &&
                  currentUserType == 3 &&
                  (handUpType == 1 || handUpStatusType != null) && (
                    <div style={{ marginLeft: '14px' }}>
                      <ApplyConnectWheatButton
                        starSpaceType={starSpaceType}
                        props={props}
                        isJoined={isJoined}
                        currentUserType={currentUserType}
                        handUpType={handUpType}
                        SpaceInfo={SpaceInfo}
                        resetTimer={resetTimer}
                        isNotLogin={isNotLogin}
                        setModalVisibleByUserTokenInvalid={setModalVisibleByUserTokenInvalid}
                      />
                    </div>
                  )}
              </div>
            </div>
          </div>
        </>
      );
    } else {
      // 非直播情况
      return (
        <div
          className={classNames({
            [styles.video_ModeratorControl]: true,
            [styles.isHiddenControlArea]: isHiddenControlArea,
          })}
        >
          {/* 回放导航条 */}
          {props.PlanetChatRoom.isLive ? (
            <div
              style={
                currentUserType == 1 && statusBySpaceInfo == 1 && currentWatchMode == 2
                  ? { height: '8px' }
                  : { height: '42px' }
              }
            ></div>
          ) : (
            <>
              {playerStateData && (
                <div className={styles.video_Progress_bar_warp}>
                  {!props.PlanetChatRoom.isLive &&
                    playerStateData &&
                    playerStateData.duration != 0 &&
                    !!playerStateData.paused && (
                      <div
                        onClick={(e) => {
                          // 点击后阻止事件冒泡,防止触发父元素的点击事件
                          e.stopPropagation();
                          resetTimer();
                          videoPlayer?.play();
                        }}
                        className={styles.playBtn}
                      ></div>
                    )}

                  {/* [按钮] 暂停回放 */}
                  {!props.PlanetChatRoom.isLive &&
                    playerStateData &&
                    playerStateData.duration != 0 &&
                    !playerStateData.paused && (
                      <div
                        onClick={(e) => {
                          // 点击后阻止事件冒泡,防止触发父元素的点击事件
                          e.stopPropagation();
                          resetTimer();
                          videoPlayer?.pause();
                        }}
                        className={styles.PauseBtn}
                      ></div>
                    )}

                  <div className={styles.video_Progress_bar_time_left}>
                    {playerStateData.currentTime
                      ? formatTime(_.floor(playerStateData.currentTime))
                      : '00:00:00'}
                  </div>
                  <div className={styles.video_Progress_bar}>
                    <Slider
                      value={_.floor(playerStateData.currentTime, 0)}
                      tooltip={{ open: false }}
                      min={0}
                      max={_.floor(playerStateData.duration, 0)}
                      onChange={onChangeBySlider}
                    />
                  </div>
                  <div className={styles.video_Progress_bar_time_right}>
                    {playerStateData.duration
                      ? formatTime(_.floor(playerStateData.duration))
                      : '00:00:00'}
                  </div>
                </div>
              )}
            </>
          )}
          {/* 控制功能区域 */}
          <div
            className={classNames({
              [styles.video_control_btn_warp]: true,
              [styles.video_control_btn_warp_PC]: true,
            })}
          >
            <div style={{ flex: 'unset' }} className={styles.video_control_btn_left_PC} />
            {/* PC中控制区域 */}
            <div className={styles.video_control_btn_middle_PC}>
              {/* [按钮]PC_收藏本直播间 */}
              <div className={styles.btn_content_warp_mid_PC}>
                <CollectButton isCollect={isCollect} onClickByCollect={onClickByCollect} />
              </div>
              {/* [按钮]直播间打卡 */}
              {/* 直播间预约状态无法打卡 */}
              {statusBySpaceInfo && (
                <div className={styles.btn_content_warp_mid_PC}>
                  <SignInButton
                    isShowApplyForLinkMicList={isShowApplyForLinkMicList}
                    resetTimer={resetTimer}
                    isNotLogin={isNotLogin}
                    setModalVisibleByUserTokenInvalid={setModalVisibleByUserTokenInvalid}
                    sendMessageByIm={props.sendMessageByIm}
                    dispatch={dispatch}
                    SpaceInfo={SpaceInfo}
                    spaceId={spaceId}
                    currentUserType={currentUserType}
                    isSignIn={isSignIn}
                    setIsShowApplyForLinkMicList={setIsShowApplyForLinkMicList}
                    setHasMoreBySignInList={setHasMoreBySignInList}
                    SideListTypeForSignInList={SideListTypeForSignInList}
                  />
                </div>
              )}

              {/* [按钮]直播间分享，APP环境中分享展示出来 */}
              {(getOperatingEnv() == '2' ||
                getOperatingEnv() == '7' ||
                getOperatingEnv() == '5' ||
                getOperatingEnv() == '6') && (
                <>
                  <div className={styles.btn_content_warp_mid_PC}>
                    <ShareButton
                      resetTimer={resetTimer}
                      isNotLogin={isNotLogin}
                      setModalVisibleByUserTokenInvalid={setModalVisibleByUserTokenInvalid}
                      shareOnClick={shareOnClick}
                    />
                  </div>
                </>
              )}

              {/*[按钮] 鼓掌*/}
              <div className={styles.btn_content_warp_mid_PC}>
                <ApplauseButton
                  resetTimer={resetTimer}
                  isNotLogin={isNotLogin}
                  setModalVisibleByUserTokenInvalid={setModalVisibleByUserTokenInvalid}
                  sendMessageByIm={props.sendMessageByIm}
                />
              </div>

              {/*[按钮] 送花*/}
              <div className={styles.btn_content_warp_mid_PC}>
                <FlowersButton
                  resetTimer={resetTimer}
                  isNotLogin={isNotLogin}
                  setModalVisibleByUserTokenInvalid={setModalVisibleByUserTokenInvalid}
                  sendMessageByIm={props.sendMessageByIm}
                />
              </div>

              {/* [按钮] 打开嘉宾列表 */}
              <div className={styles.btn_content_warp_mid_PC}>
                <GuestListButton
                  starSpaceType={starSpaceType}
                  isShowApplyForLinkMicList={isShowApplyForLinkMicList}
                  resetTimer={resetTimer}
                  isLive={PlanetChatRoom.isLive}
                  isJoined={isJoined}
                  setIsShowApplyForLinkMicList={setIsShowApplyForLinkMicList}
                />
              </div>

              {/* [按钮] 展开或收起设置 */}
              <div className={styles.btn_content_warp_mid_PC}>
                <SetConfigButton
                  isShowApplyForLinkMicList={isShowApplyForLinkMicList}
                  resetTimer={resetTimer}
                  isLive={PlanetChatRoom.isLive}
                  isJoined={isJoined}
                  setIsShowApplyForLinkMicList={setIsShowApplyForLinkMicList}
                />
              </div>
            </div>
            <div className={styles.video_control_btn_right_PC}>
              {/*[按钮] PC开启小窗口播放画中画*/}
              {!PlanetChatRoom.isLive && playerStateData && playerStateData.duration != 0 && (
                <div className={styles.btn_control_Warp}>
                  <PictureInPictureButton
                    isMobile={isMobile}
                    dispatch={dispatch}
                    props={props}
                    resetTimer={resetTimer}
                    playerStateData={playerStateData}
                    changeUrlParams={changeUrlParams}
                    openOrCloseH5SmallWindow={openOrCloseH5SmallWindow}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      );
    }
  };

  // Video-横版控制组件
  const Video_control_HorizontalLiveRoom = (e) => {
    if (!isHorizontalLive) {
      return <></>;
    }
    if (!isMobile) {
      return <></>;
    }
    return (
      <>
        <div
          className={classNames({
            [styles.video_ModeratorControl]: true,
            [styles.isHiddenControlArea]: isHiddenControlArea,
          })}
        >
          {/* 回放导航条 */}
          {props.PlanetChatRoom.isLive ? (
            <div
              style={
                currentUserType == 1 && statusBySpaceInfo == 1 && currentWatchMode == 2
                  ? { height: '8px' }
                  : { height: '42px' }
              }
            ></div>
          ) : (
            <>
              {playerStateData && (
                <div className={styles.video_Progress_bar_warp}>
                  <div className={styles.video_Progress_bar_time_left}>
                    {playerStateData.currentTime
                      ? formatTime(_.floor(playerStateData.currentTime))
                      : '00:00:00'}
                  </div>
                  <div className={styles.video_Progress_bar}>
                    <Slider
                      value={_.floor(playerStateData.currentTime, 0)}
                      tooltip={{ open: false }}
                      min={0}
                      max={_.floor(playerStateData.duration, 0)}
                      onChange={onChangeBySlider}
                    />
                  </div>
                  <div className={styles.video_Progress_bar_time_right}>
                    {playerStateData.duration
                      ? formatTime(_.floor(playerStateData.duration))
                      : '00:00:00'}
                  </div>
                </div>
              )}
            </>
          )}
          <div
            className={classNames({
              [styles.video_control_btn_warp]: true,
              [styles.video_control_btn_warp_currentWatchModeBy2]:
                currentUserType == 1 && statusBySpaceInfo == 1 && currentWatchMode == 2,
            })}
          >
            {/* TODO Video_control_btn_right */}
            {currentUserType == 1 && statusBySpaceInfo == 1 && currentWatchMode == 2 ? (
              returnVideo_control_btn_right()
            ) : (
              <div></div>
            )}
            <div className={styles.video_control_btn_left}>
              {/*[按钮] 播放回放*/}
              {!props.PlanetChatRoom.isLive &&
                playerStateData &&
                playerStateData.duration != 0 &&
                !!playerStateData.paused && (
                  <div
                    onClick={(e) => {
                      // 点击后阻止事件冒泡,防止触发父元素的点击事件
                      e.stopPropagation();
                      resetTimer();
                      videoPlayer?.play();
                    }}
                    className={styles.playBtn}
                  ></div>
                )}

              {/* [按钮] 暂停回放 */}
              {!props.PlanetChatRoom.isLive &&
                playerStateData &&
                playerStateData.duration != 0 &&
                !playerStateData.paused && (
                  <div
                    onClick={(e) => {
                      // 点击后阻止事件冒泡,防止触发父元素的点击事件
                      e.stopPropagation();
                      resetTimer();
                      videoPlayer?.pause();
                    }}
                    className={styles.PauseBtn}
                  ></div>
                )}

              {/* [按钮]结束直播 */}
              {props.PlanetChatRoom.isLive && isJoined && currentUserType == 1 && (
                <div className={styles.btn_control_Warp}>
                  <FinishLiveButton
                    isLive={PlanetChatRoom.isLive}
                    isJoined={isJoined}
                    currentUserType={currentUserType}
                    resetTimer={resetTimer}
                    getSpaceInfo={getSpaceInfo}
                    dispatch={dispatch}
                  />
                </div>
              )}

              {/* [按钮]开始直播 */}
              {props.PlanetChatRoom.isLive && !isJoined && currentUserType == 1 && (
                <div className={styles.btn_control_Warp}>
                  <StartLiveButton
                    isLive={PlanetChatRoom.isLive}
                    isJoined={isJoined}
                    currentUserType={currentUserType}
                    resetTimer={resetTimer}
                    getSpaceInfo={getSpaceInfo}
                    dispatch={dispatch}
                  />
                </div>
              )}

              {/*[按钮] 麦克风静音/取消静音-按钮  */}
              {props.PlanetChatRoom.isLive &&
                isJoined &&
                isPublished &&
                (currentUserType == 1 ||
                  currentUserType == 2 ||
                  (currentUserType == 3 && handUpStatusType == 1)) && (
                  <div className={styles.btn_control_Warp}>
                    <MicrophoneToggle
                      isLive={PlanetChatRoom.isLive}
                      isJoined={isJoined}
                      isPublished={isPublished}
                      currentUserType={currentUserType}
                      handUpStatusType={handUpStatusType}
                      resetTimer={resetTimer}
                      localStreamConfig={localStreamConfig}
                      handleChangeByLocalStreamConfig={handleChangeByLocalStreamConfig}
                    />
                  </div>
                )}

              {/* 打开/关闭-摄像头 */}
              {props.PlanetChatRoom.isLive &&
                isJoined &&
                isPublished &&
                (currentUserType == 1 ||
                  currentUserType == 2 ||
                  (currentUserType == 3 && handUpStatusType == 1)) && (
                  <div className={styles.btn_control_Warp}>
                    <CameraToggle
                      isLive={PlanetChatRoom.isLive}
                      isJoined={isJoined}
                      isPublished={isPublished}
                      currentUserType={currentUserType}
                      handUpStatusType={handUpStatusType}
                      resetTimer={resetTimer}
                      localStreamConfig={localStreamConfig}
                      handleChangeByLocalStreamConfig={handleChangeByLocalStreamConfig}
                    />
                  </div>
                )}

              {/* [input]发送弹幕 */}
              <div className={styles.inputWarp}>
                <input
                  ref={inputRef}
                  placeholder={'来发弹幕'}
                  className={styles.input}
                  type="text"
                  enterKeyHint="send"
                  onClick={(e) => {
                    e && e.stopPropagation();
                    resetTimer();
                  }}
                  onKeyDown={(value, e) => {
                    if (value.keyCode == 13 && value.target.value.trim().length > 0) {
                      //[判定登录] 判定是否已登录,未登录则弹出登录弹窗
                      if (isNotLogin) {
                        setModalVisibleByUserTokenInvalid();
                        return null;
                      }
                      const { currentTime } = playerStateData || {};
                      // 发送弹幕消息
                      props.sendMessageByIm &&
                        props.sendMessageByIm({
                          dataType: BULLET_SCREEN,
                          description: value.target.value,
                          relativeTime: currentTime ? _.floor(currentTime, 0) : null,
                        });
                      inputRef.current.value = '';
                      inputRef.current.blur();
                    }
                  }}
                />
                {/* [按钮]开启弹幕 */}
                <div
                  onClick={handleToggleDanmuVisibility}
                  className={classNames({
                    [styles.OpenDanmuBtn]: !HiddenDanmu,
                    [styles.OpenDanmuBtnHidden]: !!HiddenDanmu,
                  })}
                ></div>
              </div>
            </div>
            {/* TODO Video_control_btn_right*/}
            {currentUserType == 1 && statusBySpaceInfo == 1 && currentWatchMode == 2 ? (
              <div></div>
            ) : (
              returnVideo_control_btn_right()
            )}
          </div>
        </div>
      </>
    );
  };
  const returnVideo_control_btn_right = () => {
    return (
      <div
        className={classNames({
          [styles.video_control_btn_right]: true,
          [styles.video_control_btn_right_currenMode]:
            currentUserType == 1 && currentWatchMode == 2 && statusBySpaceInfo == 1,
        })}
      >
        {/* [按钮]屏幕分享 */}
        {props.PlanetChatRoom.isLive &&
          localStreamConfig &&
          isJoined &&
          !isMobile &&
          (currentUserType == 1 || currentUserType == 2) && (
            <div className={styles.btn_control_Warp}>
              <ScreenShareButton
                isMobile={isMobile}
                resetTimer={resetTimer}
                isLive={props.PlanetChatRoom.isLive}
                localStreamConfig={localStreamConfig}
                isJoined={isJoined}
                currentUserType={currentUserType}
                handleChangeByLocalStreamConfig={handleChangeByLocalStreamConfig}
                shareRemoteStreamConfig={shareRemoteStreamConfig}
              />
            </div>
          )}

        {/* [按钮] 分享课件 */}
        {props.PlanetChatRoom.isLive &&
          isHavCourseware == 1 &&
          localStreamConfig &&
          isJoined &&
          (currentUserType == 1 || currentUserType == 2) && (
            <div className={styles.btn_control_Warp}>
              <WhiteBoardButton
                isMobile={isMobile}
                isLive={props.PlanetChatRoom.isLive}
                localStreamConfig={localStreamConfig}
                isJoined={isJoined}
                currentUserType={currentUserType}
                isOpenTEduBoard={isOpenTEduBoard}
                resetTimer={resetTimer}
                onClick={() => {
                  dispatch({
                    type: 'PlanetChatRoom/setState',
                    payload: { isOpenTEduBoard: !isOpenTEduBoard },
                  });
                }}
              />
            </div>
          )}

        {/* [按钮]直播录制 */}
        {props.PlanetChatRoom.isLive && localStreamConfig && isJoined && currentUserType == 1 && (
          <div className={styles.btn_control_Warp}>
            <RecordButton
              isLive={props.PlanetChatRoom.isLive}
              localStreamConfig={localStreamConfig}
              isJoined={isJoined}
              currentUserType={currentUserType}
              resetTimer={resetTimer}
              recordType={recordType}
              dispatch={dispatch}
              liveRecord={liveRecord}
              elapsedTime={elapsedTime}
            />
          </div>
        )}
        {/* [按钮]自定义消息-鼓掌 */}
        <div className={styles.btn_control_Warp}>
          <ApplauseButton
            resetTimer={resetTimer}
            isNotLogin={isNotLogin}
            setModalVisibleByUserTokenInvalid={setModalVisibleByUserTokenInvalid}
            sendMessageByIm={props.sendMessageByIm}
          />
        </div>

        {/* [按钮]自定义消息-送花 */}
        <div className={styles.btn_control_Warp}>
          <FlowersButton
            resetTimer={resetTimer}
            isNotLogin={isNotLogin}
            setModalVisibleByUserTokenInvalid={setModalVisibleByUserTokenInvalid}
            sendMessageByIm={props.sendMessageByIm}
          />
        </div>

        {/*[按钮] 开启小窗口播放画中画*/}
        {!props.PlanetChatRoom.isLive && playerStateData && playerStateData.duration != 0 && (
          <div className={styles.btn_control_Warp}>
            <PictureInPictureButton
              isMobile={isMobile}
              dispatch={dispatch}
              props={props}
              resetTimer={resetTimer}
              playerStateData={playerStateData}
              changeUrlParams={changeUrlParams}
              openOrCloseH5SmallWindow={openOrCloseH5SmallWindow}
            />
          </div>
        )}

        {/* [按钮]退出横屏模式 */}
        <FullScreenToggle
          isMobile={isMobile}
          dispatch={dispatch}
          props={props}
          resetTimer={resetTimer}
          playerStateData={playerStateData}
          changeUrlParams={changeUrlParams}
        />

        {/* [按钮] 申请连麦 handUpStatusType 申请连麦状态类型：0申请连麦中 1接受连麦中，默认null*/}
        {props.PlanetChatRoom.isLive &&
          isJoined &&
          currentUserType == 3 &&
          (handUpType == 1 || handUpStatusType != null) && (
            <div style={{ marginLeft: '14px' }}>
              <ApplyConnectWheatButton
                props={props}
                isJoined={isJoined}
                currentUserType={currentUserType}
                handUpType={handUpType}
                SpaceInfo={SpaceInfo}
                resetTimer={resetTimer}
                isNotLogin={isNotLogin}
                setModalVisibleByUserTokenInvalid={setModalVisibleByUserTokenInvalid}
              />
            </div>
          )}
      </div>
    );
  };
  // Video-控制区域头部
  const VideoControlHeader = () => {
    return (
      <>
        {!!isHorizontalLive && (
          <div
            className={classNames({
              [styles.title_Warp]: true,
              [styles.isHiddenControlArea]: isHiddenControlArea,
            })}
          >
            <div className={styles.title_content}>
              <div className={styles.title}>
                <div className={styles.title_Icon}>
                  <div
                    onClick={(e) => {
                      // 点击后阻止事件冒泡,防止触发父元素的点击事件
                      e.stopPropagation();
                      resetTimer();
                      if (isMobile) {
                        dispatch({
                          type: 'PlanetChatRoom/setState',
                          payload: {
                            isHorizontalLive: false,
                            playerInfo: playerStateData,
                          },
                        });
                        props.changeUrlParams({ isHorizontalLive: null });
                      } else {
                        onClickBack();
                      }
                    }}
                    className={styles.title_Icon_box}
                  ></div>
                </div>
                <div className={styles.trtc_boxWarp}>
                  <div className={styles.trtc_box}>{nameBySpaceInfo}</div>
                  <div className={styles.trtc_desc}>
                    <div
                      style={{ marginRight: '2px', marginTop: '-2px' }}
                      className={styles.trtc_HorizontalLiveRoom_title_user_volume_Icon}
                    />
                    <div className={styles.trtc_flow_rate}>
                      <span>{pv}观看</span>
                      <span>·</span>
                      <span>{gdp}GDP</span>
                    </div>
                    {kingdomId && (
                      <div
                        style={{ marginRight: '5px' }}
                        className={styles.kingdomNameWarp}
                        onClick={(e) => {
                          // 点击后阻止事件冒泡,防止触发父元素的点击事件
                          e.stopPropagation();
                          resetTimer();
                          if (kingdomId) {
                            history.push(`/Kingdom/${kingdomId}`);
                          }
                        }}
                      >
                        <div className={styles.kingdomNameSpan}>{kingdomName}</div>
                        <div className={styles.video_Title_box_LeftBottom_icon} />
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* 在移动端横屏才展示到此 */}
              {isMobile && (
                <div className={styles.title_Icon_Collect}>
                  {/* [按钮]收藏本直播间 */}
                  <div className={styles.btn_control_Warp}>
                    <CollectButton isCollect={isCollect} onClickByCollect={onClickByCollect} />
                  </div>

                  {/* [按钮]直播间打卡 */}
                  {/* 直播间预约状态无法打卡 */}
                  {statusBySpaceInfo && (
                    <div className={styles.btn_control_Warp}>
                      <SignInButton
                        isShowApplyForLinkMicList={isShowApplyForLinkMicList}
                        isNotLogin={isNotLogin}
                        setModalVisibleByUserTokenInvalid={setModalVisibleByUserTokenInvalid}
                        sendMessageByIm={props.sendMessageByIm}
                        dispatch={dispatch}
                        SpaceInfo={SpaceInfo}
                        spaceId={spaceId}
                        currentUserType={currentUserType}
                        isSignIn={isSignIn}
                        setIsShowApplyForLinkMicList={setIsShowApplyForLinkMicList}
                        setHasMoreBySignInList={setHasMoreBySignInList}
                        SideListTypeForSignInList={SideListTypeForSignInList}
                      />
                    </div>
                  )}

                  {/* [按钮]直播间分享，APP环境中分享展示出来 */}
                  {(getOperatingEnv() == '2' ||
                    getOperatingEnv() == '7' ||
                    getOperatingEnv() == '5' ||
                    getOperatingEnv() == '6') && (
                    <div className={styles.btn_control_Warp}>
                      <ShareButton
                        isNotLogin={isNotLogin}
                        setModalVisibleByUserTokenInvalid={setModalVisibleByUserTokenInvalid}
                        shareOnClick={shareOnClick}
                      />
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* 在移动端横屏展示到此 */}
            {isMobile && (
              <div
                className={classNames({
                  [styles.setConfig_warp]: true,
                  [styles.setConfig_warp_margin_IsShowCameraList]:
                    currentWatchMode == 3 && isJoined && !!isShowCameraList,
                  [styles.setConfig_warp_margin_IsShowApplyForLinkMicList]:
                    currentWatchMode == 3 && !!isShowApplyForLinkMicList,
                })}
              >
                {/* [按钮]只有主持人和嘉宾 或 正在连麦的人员有 设置列表 */}
                {
                  <div className={styles.btn_control_Warp}>
                    <SetConfigButton
                      isShowApplyForLinkMicList={isShowApplyForLinkMicList}
                      isLive={PlanetChatRoom.isLive}
                      isJoined={isJoined}
                      setIsShowApplyForLinkMicList={setIsShowApplyForLinkMicList}
                    />
                  </div>
                }

                {/* [按钮] 打开连麦申请列表 */}
                {props.PlanetChatRoom.isLive && currentUserType == 1 && isJoined && (
                  <div className={styles.btn_control_Warp}>
                    <LinkMicApplicationsToggle
                      isShowApplyForLinkMicList={isShowApplyForLinkMicList}
                      isLive={PlanetChatRoom.isLive}
                      isJoined={isJoined}
                      setIsShowApplyForLinkMicList={setIsShowApplyForLinkMicList}
                      getHandUpList={getHandUpList}
                      handUpList={handUpList}
                    />
                  </div>
                )}

                {/* [按钮]打开嘉宾列表 */}
                <div className={styles.btn_control_Warp}>
                  <GuestListButton
                    starSpaceType={starSpaceType}
                    isShowApplyForLinkMicList={isShowApplyForLinkMicList}
                    isLive={PlanetChatRoom.isLive}
                    isJoined={isJoined}
                    setIsShowApplyForLinkMicList={setIsShowApplyForLinkMicList}
                  />
                </div>
              </div>
            )}

            {/* [按钮]关注user */}
            {hostUserInfo && hostUserInfo.isFocus == 0 && hostUserInfo.isSelf == 0 && (
              <div
                onClick={(e) => {
                  e.stopPropagation();
                  //[判定登录] 判定是否已登录,未登录则弹出登录弹窗
                  if (isNotLogin) {
                    setModalVisibleByUserTokenInvalid();
                    return null;
                  }
                  isFocusByOnClick({
                    expertsUserId: hostUserInfo.wxUserId,
                    isFocus: 1,
                    type: 2, // 关注后刷新空间详情
                  });
                }}
                className={styles.follow_btn}
              >
                <div className={styles.avatar}>
                  <Avatar userInfo={hostUserInfo} size={24}></Avatar>
                </div>
                <div>+关注</div>
              </div>
            )}
          </div>
        )}
      </>
    );
  };

  return (
    <>
      <Helmet>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, user-scalable=no, shrink-to-fit=no, viewport-fit=cover"
        />
      </Helmet>
      <div
        onClick={(e) => {
          handleClickOutside(e);
        }}
        className={classNames({
          [styles.warp_content]: true,
          [styles.warp_content_iphone]: currentWatchMode == 3 && isIphoneIslandSeries(),
        })}
      >
        <div
          style={{
            height: '100%',
          }}
          className={styles.content}
        >
          {/* 摄像头列表 */}
          {!!props.PlanetChatRoom.isLive && isJoined && !isModeMatrix && (
            <div
              className={classNames({
                [styles.HorizontalLiveRoom_camera_picture_Box_VerticalLiveRoom]: !isHorizontalLive,
                [styles.HorizontalLiveRoom_camera_picture_Box]: !!isHorizontalLive,
                [styles.isShowCameraList]: isShowCameraList,
              })}
            >
              {/* [按钮]打开摄像头列表 */}
              <div
                onClick={onClickByHorizontalLiveRoom_camera_picture_Box}
                className={classNames({
                  [styles.HorizontalLiveRoom_camera_picture_btn]: true,
                  [styles.HorizontalLiveRoom_camera_picture_take_back_btn]: isShowCameraList,
                })}
              />
              <div className={styles.HorizontalLiveRoom_camera_picture_camera_live}>
                {!localStreamConfig &&
                  (!Array.isArray(userCameraRemoteStreamList) ||
                    (Array.isArray(userCameraRemoteStreamList) &&
                      userCameraRemoteStreamList.filter((item) =>
                        !shareRemoteStreamConfig ? item.userID != hostUserInfo.imUserId : true,
                      ).length == 0)) && (
                    <div className={styles.noGuestsListWarp}>
                      <div className={styles.noGuestsListIcon}>
                        <i className={styles.noGuestsIcon} />
                        <div className={styles.noGuestsText}>暂无嘉宾进入</div>
                      </div>
                    </div>
                  )}
                {localStreamConfig && RTC?.isPublished && localStreamConfig.hasVideo && (
                  <>
                    {(currentUserType == 1 ? !!shareRemoteStreamConfig : true) ? (
                      <div
                        style={{ order: 1 }}
                        className={classNames({
                          [styles.HorizontalLiveRoom_camera_picture_camera_item]: true,
                          [styles.HorizontalLiveRoom_camera_picture_camera_item_hidden]:
                            localStreamConfig && localStreamConfig.mutedVideo,
                        })}
                      >
                        <div
                          className={classNames({
                            [styles.StreamWarp]: true,
                            [styles.StreamWarpHidden]:
                              localStreamConfig && localStreamConfig.mutedVideo,
                          })}
                        >
                          <Stream
                            stream={localStreamConfig.stream}
                            config={localStreamConfig}
                            init={(dom) => RTC.playStream(localStreamConfig.stream, dom)}
                            onChange={(e) => props.handleLocalChange(e)}
                          ></Stream>
                        </div>
                        {localStreamConfig && localStreamConfig.mutedVideo && (
                          <div className={styles.headUrlWarp}>
                            <div
                              style={{ background: wxUserId ? randomColor(wxUserId) : 'none' }}
                              className={styles.video_Title_box_left_avatar}
                            >
                              <Avatar userInfo={userInfoData} size={32} isPc={!isMobile}></Avatar>
                            </div>
                          </div>
                        )}
                        <div className={styles.HorizontalLiveRoom_camera_picture_camera_bottom_box}>
                          <div
                            className={
                              styles.HorizontalLiveRoom_camera_picture_camera_bottom_box_Name_Warp
                            }
                          >
                            <div>{'本人'}</div>
                            <div
                              className={classNames({
                                [styles.HorizontalLiveRoom_camera_picture_mic_icon]:
                                  !localStreamConfig.mutedAudio &&
                                  localStreamConfig.audioVolume == 0,
                                [styles.HorizontalLiveRoom_camera_picture_forbidden_mic_icon]:
                                  localStreamConfig.mutedAudio,
                                [styles.HorizontalLiveRoom_camera_picture_forbidden_Lianmai]:
                                  !localStreamConfig.mutedAudio &&
                                  localStreamConfig.audioVolume > 0,
                              })}
                            ></div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div
                        style={{ order: 1 }}
                        className={classNames({
                          [styles.HorizontalLiveRoom_camera_picture_camera_item]: true,
                          [styles.HorizontalLiveRoom_camera_picture_camera_item_hidden]: true,
                        })}
                      >
                        <div
                          className={classNames({
                            [styles.StreamWarp]: true,
                            [styles.StreamWarpHidden]: true,
                          })}
                        ></div>
                        <div className={styles.headUrlWarp}>
                          <div
                            style={{ background: wxUserId ? randomColor(wxUserId) : 'none' }}
                            className={styles.video_Title_box_left_avatar}
                          >
                            <Avatar userInfo={userInfoData} size={32} isPc={!isMobile}></Avatar>
                          </div>
                        </div>
                        <div className={styles.HorizontalLiveRoom_camera_picture_camera_bottom_box}>
                          <div
                            className={
                              styles.HorizontalLiveRoom_camera_picture_camera_bottom_box_Name_Warp
                            }
                          >
                            <div>{'本人'}</div>
                            <div
                              className={classNames({
                                [styles.HorizontalLiveRoom_camera_picture_mic_icon]:
                                  !localStreamConfig.mutedAudio &&
                                  localStreamConfig.audioVolume == 0,
                                [styles.HorizontalLiveRoom_camera_picture_forbidden_mic_icon]:
                                  localStreamConfig.mutedAudio,
                                [styles.HorizontalLiveRoom_camera_picture_forbidden_Lianmai]:
                                  !localStreamConfig.mutedAudio &&
                                  localStreamConfig.audioVolume > 0,
                              })}
                            ></div>
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                )}

                {Array.isArray(userCameraRemoteStreamList) &&
                  userCameraRemoteStreamList.map((userCameraRemoteStreamConfig, index) => {
                    if (
                      !userCameraRemoteStreamConfig ||
                      (!!shareRemoteStreamConfig ? false : isModeMatrix)
                    ) {
                      return;
                    }

                    // 列表区域内不能展示主持人流
                    if (
                      !shareRemoteStreamConfig &&
                      userCameraRemoteStreamConfig.userID == hostUserInfo.imUserId
                    ) {
                      return;
                    }

                    let findBycurrentLiveUser =
                      Array.isArray(currentLiveUserList) &&
                      currentLiveUserList.find((value) => {
                        return value.imUserId == userCameraRemoteStreamConfig.userID;
                      });
                    if (!findBycurrentLiveUser) {
                      findBycurrentLiveUser =
                        Array.isArray(handUpList) &&
                        handUpList.find((value) => {
                          return value.imUserId == userCameraRemoteStreamConfig.userID;
                        });
                    }

                    let order =
                      Array.isArray(userCameraRemoteStreamListSort) &&
                      userCameraRemoteStreamListSort.findIndex(
                        (itemByuserCameraRemoteStreamListSort) => {
                          return (
                            userCameraRemoteStreamConfig.userID ==
                            itemByuserCameraRemoteStreamListSort.userID
                          );
                        },
                      ) + 2;

                    return (
                      <div
                        key={index + 2}
                        style={{ order: order ? order : index + 2 }}
                        className={classNames({
                          [styles.HorizontalLiveRoom_camera_picture_camera_item]: true,
                          [styles.HorizontalLiveRoom_camera_picture_camera_item_hidden]:
                            userCameraRemoteStreamConfig && !userCameraRemoteStreamConfig.hasVideo,
                        })}
                      >
                        <div
                          className={classNames({
                            [styles.StreamWarp]: true,
                            [styles.StreamWarpHidden]:
                              userCameraRemoteStreamConfig &&
                              !userCameraRemoteStreamConfig.hasVideo,
                          })}
                        >
                          {userCameraRemoteStreamConfig && (
                            <Stream
                              key={index}
                              stream={userCameraRemoteStreamConfig.stream}
                              config={userCameraRemoteStreamConfig}
                              init={(dom) => {
                                let config = {
                                  objectFit: 'contain',
                                };
                                return RTC.playStream(
                                  userCameraRemoteStreamConfig.stream,
                                  dom,
                                  config,
                                );
                              }}
                              // onChange = {e => shareRemoteStreamConfig(e)}
                            ></Stream>
                          )}
                        </div>
                        {userCameraRemoteStreamConfig && !userCameraRemoteStreamConfig.hasVideo && (
                          <div className={styles.headUrlWarp}>
                            <div
                              style={{ background: wxUserId ? randomColor(wxUserId) : 'none' }}
                              className={styles.video_Title_box_left_avatar}
                            >
                              <Avatar
                                userInfo={findBycurrentLiveUser}
                                size={32}
                                isPc={!isMobile}
                              ></Avatar>
                            </div>
                          </div>
                        )}
                        <div
                          className={classNames({
                            [styles.HorizontalLiveRoom_camera_picture_camera_bottom_box]: true,
                            [styles.HorizontalLiveRoom_camera_picture_camera_bottom_box_lianmai]:
                              findBycurrentLiveUser?.statusType == 1,
                          })}
                        >
                          <div
                            className={
                              styles.HorizontalLiveRoom_camera_picture_camera_bottom_box_Name_Warp
                            }
                          >
                            <div
                              className={classNames({
                                [styles.HorizontalLiveRoom_camera_picture_camera_bottom_box_Name]:
                                  true,
                                [styles.HorizontalLiveRoom_camera_picture_camera_bottom_box_Name_lianmai]:
                                  findBycurrentLiveUser?.statusType == 1,
                              })}
                            >
                              {findBycurrentLiveUser && findBycurrentLiveUser.name}
                            </div>
                            <div
                              className={classNames({
                                [styles.HorizontalLiveRoom_camera_picture_mic_icon]:
                                  !userCameraRemoteStreamConfig.mutedAudio &&
                                  userCameraRemoteStreamConfig.audioVolume == 0,
                                [styles.HorizontalLiveRoom_camera_picture_forbidden_mic_icon]:
                                  userCameraRemoteStreamConfig.mutedAudio,
                                [styles.HorizontalLiveRoom_camera_picture_forbidden_Lianmai]:
                                  !userCameraRemoteStreamConfig.mutedAudio &&
                                  userCameraRemoteStreamConfig.audioVolume > 0,
                              })}
                            ></div>
                          </div>

                          {findBycurrentLiveUser &&
                            findBycurrentLiveUser.statusType == 1 &&
                            currentUserType == 1 &&
                            isHorizontalLive && (
                              <div
                                onClick={(e) => {
                                  // 点击后阻止事件冒泡,防止触发父元素的点击事件
                                  e.stopPropagation();
                                  resetTimer();
                                  const { wxUserId, imUserId } = findBycurrentLiveUser || {};
                                  // 1 接受连麦 2暂不同意 3下麦
                                  dispatch({
                                    type: 'PlanetChatRoom/setState',
                                    payload: {
                                      ModalVisibleByForceWheat: {
                                        statusType: 3,
                                        guestUserId: wxUserId,
                                        imUserId: imUserId,
                                      },
                                    },
                                  });
                                  // operateHandUp({statusType:3,guestUserId:wxUserId,imUserId:imUserId})
                                }}
                                className={classNames({
                                  [styles.forceXiaMai]: true,
                                })}
                              >
                                强制下麦
                              </div>
                            )}
                        </div>
                      </div>
                    );
                  })}
              </div>
            </div>
          )}

          {/* 送花数量 */}
          {currentShowFlowersOrApplause == 1 && (
            <div className={styles.HorizontalLiveRoom_send_flowers_quantity}>
              <div className={styles.HorizontalLiveRoom_send_flowers_quantity_left}>
                <div className={styles.HorizontalLiveRoom_send_flowers_content_icon}></div>
              </div>
              <div className={styles.HorizontalLiveRoom_send_flowers_quantity_rigth}>
                <div>花花</div>
                <div>{sendFlowersCount}</div>
              </div>
            </div>
          )}

          {/* currentShowFlowersOrApplause 1展示-鼓掌 */}
          {currentShowFlowersOrApplause == 2 && (
            <div className={styles.HorizontalLiveRoom_send_flowers_quantity}>
              <div className={styles.HorizontalLiveRoom_send_flowers_quantity_left}>
                <div className={styles.HorizontalLiveRoom_clapinteraction_icon}></div>
              </div>
              <div className={styles.HorizontalLiveRoom_send_flowers_quantity_rigth}>
                <div>鼓掌</div>
                <div>{sendApplauseCount}</div>
              </div>
            </div>
          )}

          {/* -横屏动态列表- */}
          {!!isShowCommentArea && isHorizontalLive && currentWatchMode != 2 && (
            <LiveRoomMessageList
              HiddenDanmu={HiddenDanmu}
              isMobile={isMobile}
              msgListBySENDAPPLAUSE={msgListBySENDAPPLAUSE}
              isNotLogin={isNotLogin}
              setModalVisibleByUserTokenInvalid={setModalVisibleByUserTokenInvalid}
              resetTimer={resetTimer}
              playerStateData={playerStateData}
              props={props}
              onScroll={onScrollByLiveRoomMessageList}
              handleToggleDanmuVisibility={handleToggleDanmuVisibility}
            />
          )}

          {/* 视频流显示区域 */}
          <div
            id={'videoContent'}
            className={styles.video_content}
            ref={videoContentRefByHorizontalLiveRoom}
          >
            {!!isShowTrySeeTip && (
              <div className={styles.tryPlay}>
                <div className={styles.tryPlayContent}>
                  <div className={styles.tryPlayContent_text}>10分钟试看已结束</div>
                  <div
                    className={classNames(styles.tryPlayContent_text, styles.tryPlayContent_dose)}
                  >
                    登录畅享全部精彩内容
                  </div>
                  <div
                    className={styles.tryPlayContent_btn}
                    onClick={async () => {
                      TrialEnds();
                    }}
                  >
                    立即登录
                  </div>
                </div>
              </div>
            )}
            {!!props.PlanetChatRoom.isOpenDanmu && (
              <div id={'vs'} className={styles.video_box_warp}>
                <div id={'ms'} className={styles.video_box}></div>
              </div>
            )}

            {isHiddenControlArea && isHorizontalLive && (
              <div style={{ top: '0px' }} className={styles.video_HorizontalLiveRoom}></div>
            )}

            {/* 大播放键 判定video是否暂停 */}
            {playerStateData && playerStateData.paused && (
              <div
                onClick={() => {
                  videoPlayer && videoPlayer.play();
                }}
                className={classNames({
                  [styles.video_HorizontalLiveRoom_goPlayBox]: true,
                  [styles.video_HorizontalLiveRoom_goPlay]: !!isHorizontalLive,
                  [styles.video_HorizontalLiveRoom_goPlay_isHorizontalLive]: !isHorizontalLive,
                })}
              >
                <div className={styles.video_Play_Icon}></div>
              </div>
            )}
            {/* --[TEduBoard] 开启互动白板--*/}
            {!!props.PlanetChatRoom.isLive &&
              !!isOpenTEduBoard &&
              statusBySpaceInfo == 1 && ( // // 状态：1直播中、2预约中、3弹幕轰炸中
                <WhiteboardLiveRoom
                  tim={tim}
                  shareHostRemoteStreamConfig={shareHostRemoteStreamConfig}
                />
              )}

            {/* 录播video */}
            {!props.PlanetChatRoom.isLive &&
              !isOpenTEduBoard &&
              Array.isArray(videoList) &&
              videoList.length > 0 &&
              !ModalVisibleByKickedOut && // 是否显示弹窗(被踢出)
              !ModalVisibleBySpaceViolation && // 是否显示弹窗(空间违规下架弹窗)
              !ModalVisibleBySpaceRemoved && ( // 是否显示弹窗(该空间已下架)
                <div style={{ height: '100%' }} className={styles.video}>
                  <video
                    ref={playerRefByHorizontalLiveRoom}
                    id="player-container-id-HorizontalLiveRoom"
                    autoPlay={isNeedPwd == 0 ? true : false}
                    style={{ height: '100%' }}
                    className={classNames(styles.videoJs, 'video-react-video')}
                    playsInline={true}
                    x5-video-player-type="h5-page"
                    x5-video-player-fullscreen="true"
                    poster={
                      playerStateData &&
                      playerStateData.currentTime &&
                      playerStateData.currentTime != 0
                        ? null
                        : spaceCoverUrlShow
                        ? spaceCoverUrlShow
                        : 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/PlanetChatRoom_HorizontalLiveRoom_Live_bg.png'
                    }
                  />
                </div>
              )}
            {/* 直播区间 */}
            {!shareRemoteStreamConfig &&
              ((currentUserType != 1 && !hostRemoteStreamConfig) ||
                (currentUserType == 1 && !localStreamConfig)) && (
                <div className={styles.Live_Video}>
                  {!!spaceCoverUrlShow ? (
                    <img
                      className={classNames({
                        [styles.spaceCoverUrlShow]: true,
                      })}
                      src={spaceCoverUrlShow}
                    ></img>
                  ) : (
                    <img
                      className={classNames({
                        [styles.spaceCoverUrlShow]: true,
                      })}
                      src={
                        'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/PlanetChatRoom_HorizontalLiveRoom_Live_bg.png'
                      }
                    />
                  )}
                </div>
              )}
            {/* 远端直播流区域 */}
            {!isModeMatrix &&
              !isShowTrySeeTip &&
              shareRemoteStreamConfig &&
              shareRemoteStreamConfig.stream && (
                <Stream
                  key={`${shareRemoteStreamConfig.stream.getUserId()}_${shareRemoteStreamConfig.stream.getType()}`}
                  stream={shareRemoteStreamConfig.stream}
                  config={shareRemoteStreamConfig}
                  init={(dom) => RTC.playStream(shareRemoteStreamConfig.stream, dom)}
                ></Stream>
              )}

            {!isModeMatrix && !isShowTrySeeTip && !shareRemoteStreamConfig && (
              <>
                {/* 本地直播流 */}
                {/* 判定当前直播空间是否是自己的 1主播 2嘉宾 3观众 */}
                {currentUserType == 1 ? (
                  <>
                    {localStreamConfig &&
                      (localStreamConfig.hasAudio || localStreamConfig.hasVideo) && (
                        <Stream
                          stream={localStreamConfig.stream}
                          config={localStreamConfig}
                          init={(dom) =>
                            RTC.playStream(localStreamConfig.stream, dom, { objectFit: 'cover' })
                          }
                        ></Stream>
                      )}
                  </>
                ) : (
                  <>
                    {hostRemoteStreamConfig &&
                      (hostRemoteStreamConfig.hasAudio || hostRemoteStreamConfig.hasVideo) && (
                        <Stream
                          stream={hostRemoteStreamConfig.stream}
                          config={hostRemoteStreamConfig}
                          init={(dom) =>
                            RTC.playStream(hostRemoteStreamConfig.stream, dom, {
                              objectFit: 'cover',
                            })
                          }
                        ></Stream>
                      )}
                  </>
                )}
              </>
            )}

            {/* 摄像头矩阵模式 */}
            {isModeMatrix && (
              <>
                <div className={styles.CameralistWarp}>
                  <div className={styles.Cameralist}>
                    {cameralistArr.map((itemConfig, index) => {
                      if (!itemConfig) {
                        return;
                      }
                      let findBycurrentLiveUser =
                        Array.isArray(currentLiveUserList) &&
                        currentLiveUserList.find((value) => {
                          return value.imUserId == itemConfig.userID;
                        });
                      if (!findBycurrentLiveUser) {
                        findBycurrentLiveUser =
                          Array.isArray(handUpList) &&
                          handUpList.find((value) => {
                            return value.imUserId == itemConfig.userID;
                          });
                      }

                      let width = 0;
                      let height = 0;
                      if (cameralistArr.length == 1) {
                        width = 100;
                      } else if (cameralistArr.length != 1 && cameralistArr.length <= 4) {
                        width = 50;
                      } else if (cameralistArr.length > 4 && cameralistArr.length <= 9) {
                        width = 33.33;
                      } else {
                        width = 25;
                      }

                      if (cameralistArr.length == 1) {
                        height = 100;
                      } else if (cameralistArr.length <= 6) {
                        height = 50;
                      } else if (cameralistArr.length <= 9) {
                        height = 33.33;
                      } else {
                        height = 25;
                      }

                      return (
                        <div
                          key={index}
                          style={{
                            width: `${width}%`,
                            height: `${height}%`,
                          }}
                          className={styles.CameraItem}
                        >
                          {itemConfig && itemConfig.userID.indexOf('share') != -1 ? (
                            <>
                              <div
                                key={`${itemConfig.stream.getUserId()}_${itemConfig.stream.getType()}`}
                                className={classNames({
                                  [styles.isModeMatrix_camera_picture_camera_item]: true,
                                  [styles.isModeMatrix_camera_picture_camera_item_hidden]:
                                    itemConfig && !itemConfig.hasVideo,
                                })}
                              >
                                <div
                                  className={classNames({
                                    [styles.StreamWarp]: true,
                                    [styles.StreamWarpHidden]: itemConfig && !itemConfig.hasVideo,
                                  })}
                                >
                                  <Stream
                                    key={`${itemConfig.stream.getUserId()}_${itemConfig.stream.getType()}_HorizontalLiveRoom`}
                                    stream={itemConfig.stream}
                                    config={itemConfig}
                                    init={(dom) => RTC.playStream(itemConfig.stream, dom)}
                                    // onChange={e => handleChangeByLocalStreamConfig(e)}
                                  ></Stream>
                                </div>
                              </div>
                            </>
                          ) : (
                            <>
                              {itemConfig &&
                                (itemConfig.hasAudio ||
                                  (itemConfig.streamType == 'local' && !itemConfig.mutedVideo)) && (
                                  <div
                                    key={index}
                                    className={classNames({
                                      [styles.isModeMatrix_camera_picture_camera_item]: true,
                                      [styles.isModeMatrix_camera_picture_camera_item_hidden]:
                                        itemConfig &&
                                        (!itemConfig.hasVideo ||
                                          (itemConfig.streamType == 'local' &&
                                            itemConfig.mutedVideo)),
                                    })}
                                  >
                                    <div
                                      className={classNames({
                                        [styles.StreamWarp]: true,
                                        [styles.StreamWarpHidden]:
                                          itemConfig &&
                                          (!itemConfig.hasVideo ||
                                            (itemConfig.streamType == 'local' &&
                                              itemConfig.mutedVideo)),
                                      })}
                                    >
                                      <Stream
                                        key={`${itemConfig.stream.getUserId()}_${itemConfig.stream.getType()}_HorizontalLiveRoom`}
                                        stream={itemConfig.stream}
                                        config={itemConfig}
                                        init={(dom) => RTC.playStream(itemConfig.stream, dom)}
                                        // onChange={e => handleChangeByLocalStreamConfig(e)}
                                      ></Stream>
                                    </div>
                                    {itemConfig &&
                                      (!itemConfig.hasVideo ||
                                        (itemConfig.streamType == 'local' &&
                                          itemConfig.mutedVideo)) && (
                                        <div className={styles.headUrlWarp}>
                                          <div
                                            style={{
                                              background: wxUserId ? randomColor(wxUserId) : 'none',
                                            }}
                                            className={styles.video_Title_box_left_avatar}
                                          >
                                            <Avatar
                                              userInfo={findBycurrentLiveUser}
                                              size={44}
                                              isPc={!isMobile}
                                            ></Avatar>
                                          </div>
                                        </div>
                                      )}

                                    <div
                                      className={classNames({
                                        [styles.HorizontalLiveRoom_camera_picture_camera_bottom_box]:
                                          true,
                                        [styles.HorizontalLiveRoom_camera_picture_camera_bottom_box_lianmai]:
                                          findBycurrentLiveUser &&
                                          findBycurrentLiveUser.statusType == 1,
                                      })}
                                    >
                                      <div
                                        className={
                                          styles.HorizontalLiveRoom_camera_picture_camera_bottom_box_Name_Warp
                                        }
                                      >
                                        <div
                                          className={classNames({
                                            [styles.HorizontalLiveRoom_camera_picture_camera_bottom_box_Name]:
                                              true,
                                            [styles.HorizontalLiveRoom_camera_picture_camera_bottom_box_Name_lianmai]:
                                              findBycurrentLiveUser &&
                                              findBycurrentLiveUser.statusType == 1,
                                          })}
                                        >
                                          {findBycurrentLiveUser && findBycurrentLiveUser.name}
                                        </div>
                                        <div
                                          className={classNames({
                                            [styles.HorizontalLiveRoom_camera_picture_mic_icon]:
                                              !itemConfig.mutedAudio && itemConfig.audioVolume == 0,
                                            [styles.HorizontalLiveRoom_camera_picture_forbidden_mic_icon]:
                                              itemConfig.mutedAudio,
                                            [styles.HorizontalLiveRoom_camera_picture_forbidden_Lianmai]:
                                              !itemConfig.mutedAudio && itemConfig.audioVolume > 0,
                                          })}
                                        ></div>
                                      </div>
                                    </div>
                                  </div>
                                )}
                            </>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              </>
            )}

            {/* 直播或录播控制区域-横版-PC版本 */}
            <>{Video_control_HorizontalLiveRoom_PC()}</>
            {/* 直播或录播的控制区域-移动版本 */}
            <>{Video_control_HorizontalLiveRoom()}</>
            {/* video-竖屏控制进度条等组件 */}
            <>{video_ModeratorControl()}</>
          </div>
          {/*控制区域头部*/}
          <>{VideoControlHeader()}</>

          {/* 申请连麦列表 */}
          <div
            onClick={(e) => {
              e && e.stopPropagation();
            }}
            ref={wrapperRefByApplyConnectLiveListWarp}
            className={classNames({
              [styles.ApplyConnectLiveListWarp]: true,
              [styles.isShowApplyForLinkMicList]: !isShowApplyForLinkMicList,
            })}
          >
            {((!(isShowApplyForLinkMicList == SideListTypeForDistinguished) &&
              starSpaceType == 2) ||
              starSpaceType == 1) && (
              <div className={styles.ApplyConnectLiveList_title}>
                {isShowApplyForLinkMicList == SideListTypeForSettings && '设置'}
                {isShowApplyForLinkMicList == SideListTypeForApply &&
                  `申请${starSpaceTypeLianMaiText}`}
                {isShowApplyForLinkMicList == SideListTypeForDistinguished && '参与嘉宾'}
                {isShowApplyForLinkMicList == SideListTypeForSignInList && '打卡详情'}
              </div>
            )}

            {!!(isShowApplyForLinkMicList == SideListTypeForDistinguished) &&
              starSpaceType == 2 && (
                <div className={styles.ApplyConnectLiveList_header}>
                  <div
                    onClick={() => {
                      setGuestListType(1);
                    }}
                    className={classNames({
                      [styles.header_tab_item]: true,
                      [styles.header_tab_item_active]: guestListType == 1,
                    })}
                  >
                    参会人
                  </div>

                  {currentUserType == 1 && (
                    <div
                      onClick={() => {
                        setGuestListType(2);
                      }}
                      className={classNames({
                        [styles.header_tab_item]: true,
                        [styles.header_tab_item_active]: guestListType == 2,
                      })}
                    >
                      申请进入会议成员
                      {Array.isArray(applyAdmissionList) &&
                        applyAdmissionList.filter((item) => item && item.isAgree == 0).length >
                          0 && (
                          <i className={styles.tab_item_num}>
                            {Array.isArray(applyAdmissionList) &&
                              applyAdmissionList.filter((item) => item && item.isAgree == 0).length}
                          </i>
                        )}
                    </div>
                  )}
                </div>
              )}

            {isShowApplyForLinkMicList == SideListTypeForApply && (
              <>
                {/*** TODO [暂无申请连麦] ***/}
                {(!handUpList || (Array.isArray(handUpList) && handUpList.length == 0)) && (
                  <div className={styles.ApplyConnectLiveList_noData}>
                    <div className={styles.ApplyConnectLiveList_noData_icon_warp}>
                      <div className={styles.ApplyConnectLiveList_noData_icon} />
                      <div className={styles.ApplyConnectLiveList_noData_text}>
                        暂无申请{starSpaceTypeLianMaiText}
                      </div>
                    </div>
                  </div>
                )}

                {/*** TODO 申请连麦列表 ***/}
                <div className={styles.ApplyConnectLiveList}>
                  {Array.isArray(handUpList) &&
                    handUpList.map((item, index) => {
                      let {
                        applyDate, // : "2023-06-26 17:49:08"
                        imUserId, // : "8555007f9059dd9fddf6190870706d82"
                        name, // : "张志军"
                        statusType, // : 0
                        wxUserId, // : 54
                      } = item || {};

                      return (
                        <div className={styles.ApplyConnectLiveItem}>
                          <div className={styles.ApplyConnectLiveLeft}>
                            <div className={styles.ApplyConnectLiveItem_avatar}>
                              <Avatar userInfo={item} size={24}></Avatar>
                            </div>
                            <div className={styles.ApplyConnectLiveItem_name}>{name}</div>
                          </div>
                          {/*  // 1 接受连麦 2暂不同意 3下麦 */}
                          {statusType == 1 && (
                            <div
                              onClick={() => {
                                dispatch({
                                  type: 'PlanetChatRoom/setState',
                                  payload: {
                                    ModalVisibleByForceWheat: {
                                      statusType: 3,
                                      guestUserId: wxUserId,
                                      imUserId: imUserId,
                                    },
                                  },
                                });
                                // operateHandUp({statusType:3,guestUserId:wxUserId,imUserId:imUserId})
                              }}
                              className={styles.ApplyConnectLiveList_force_Cancel_btn}
                            >
                              强制下麦
                            </div>
                          )}
                          {statusType == 0 && (
                            <div
                              onClick={() => {
                                operateHandUp({
                                  statusType: 1,
                                  guestUserId: wxUserId,
                                  imUserId: imUserId,
                                });
                              }}
                              className={styles.ApplyConnectLiveList_receive_btn}
                            >
                              接受
                            </div>
                          )}
                        </div>
                      );
                    })}
                </div>
              </>
            )}

            {/*** TODO 设置 ***/}
            {isShowApplyForLinkMicList == SideListTypeForSettings && (
              <div
                onClick={(event) => {
                  event.stopPropagation();
                }}
                className={styles.ApplySetList}
              >
                {props.PlanetChatRoom.isLive && isJoined && currentUserType == 1 && (
                  <div className={styles.ApplySetItem}>
                    <div className={styles.ApplySetItem_title}>允许{starSpaceTypeLianMaiText}</div>
                    <div>
                      <Switch
                        onChange={(value) => {
                          if (value) {
                            // 开启连麦
                            openCloseHandUp({ handUpType: 1 });
                          } else {
                            openCloseHandUp({ handUpType: 2 });
                          }
                        }}
                        checked={handUpType == 1}
                        disabled={!localStreamConfig}
                        style={{
                          '--checked-color': '#009DFF',
                          '--height': '23px',
                          '--width': '35px',
                        }}
                      />
                    </div>
                  </div>
                )}

                {props.PlanetChatRoom.isLive && isJoined && (
                  <div className={styles.ApplySetItem}>
                    <div className={styles.ApplySetItem_title}>嘉宾视频全部展示</div>
                    <div>
                      <Switch
                        onChange={(value) => {
                          dispatch({
                            type: 'PlanetChatRoom/setState',
                            payload: {
                              isModeMatrix: value,
                            },
                          });
                        }}
                        checked={isModeMatrix}
                        style={{
                          '--checked-color': '#009DFF',
                          '--height': '23px',
                          '--width': '35px',
                        }}
                      />
                    </div>
                  </div>
                )}

                {
                  <div className={styles.ApplySetItem}>
                    <div className={styles.ApplySetItem_title}>展示评论区</div>
                    <div>
                      <Switch
                        onChange={(value) => {
                          dispatch({
                            type: 'PlanetChatRoom/setState',
                            payload: {
                              isShowCommentArea: value,
                            },
                          });
                        }}
                        checked={isShowCommentArea}
                        style={{
                          '--checked-color': '#009DFF',
                          '--height': '23px',
                          '--width': '35px',
                        }}
                      />
                    </div>
                  </div>
                }
              </div>
            )}

            {/*TODO 打卡列表*/}
            {isShowApplyForLinkMicList == SideListTypeForSignInList && (
              <div className={styles.ApplyConnectLiveList}>
                {Array.isArray(signInList) &&
                  signInList.map((item, index) => {
                    const { name, createDate, phoneTail } = item || {};
                    return (
                      <div className={styles.signInItem}>
                        <div className={styles.signInItemLeft}>
                          <div className={styles.signInItemLeftItem_Avatar}>
                            <Avatar userInfo={item} size={24}></Avatar>
                          </div>
                          <div className={styles.signInItemLeftInfo}>
                            <div className={styles.signInItemLeftInfoName}>{name}</div>
                            <div className={styles.signInItemLeftInfoTime}>{createDate}</div>
                          </div>
                        </div>
                        <div className={styles.signInItemRight}>尾号{phoneTail}</div>
                      </div>
                    );
                  })}
                <InfiniteScroll
                  loadMore={async () => {
                    const { total, pageNum, pageSize } = signInObj || {};
                    const dataByGetSignInList = await dispatch({
                      type: 'PlanetChatRoom/getSignInList',
                      payload: {
                        spaceId: spaceId,
                        pageNum: pageNum ? pageNum + 1 : 1,
                        pageSize: pageSize,
                      },
                    });
                    const { content } = dataByGetSignInList || {};
                    const { resultList } = content || {};
                    setHasMoreBySignInList(Array.isArray(resultList) && resultList.length > 0);
                  }}
                  threshold={20}
                  hasMore={hasMoreBySignInList}
                />
              </div>
            )}

            {/*** TODO 参与嘉宾 ***/}
            {isShowApplyForLinkMicList == SideListTypeForDistinguished && (
              <div>
                {guestListType == 1 && (
                  <div className={styles.ApplyConnectLiveList}>
                    {Array.isArray(guestListInfo) &&
                      guestListInfo.map((item, index) => {
                        const {
                          name,
                          postTitleDictName,
                          isFocus,
                          isSelf,
                          wxUserId: wxUserIdByItem,
                        } = item || {};
                        return (
                          <div key={index} className={styles.ApplyConnectLiveItem}>
                            <div className={styles.ApplyConnectLiveLeft}>
                              <div className={styles.ApplyConnectLiveItem_avatar}>
                                <Avatar userInfo={item} size={24}></Avatar>
                              </div>
                              <div className={styles.ApplyConnectLiveItem_name}>{name}</div>
                            </div>
                            {isFocus == 1 && isSelf == 0 && (
                              <div
                                onClick={(e) => {
                                  e.stopPropagation();
                                  isFocusByOnClick({
                                    expertsUserId: wxUserIdByItem,
                                    isFocus: 0, // 取消关注
                                    type: 1, // 关注列表
                                  });
                                }}
                                className={styles.ApplyConnectLiveList_unfollow_btn}
                              >
                                取消关注
                              </div>
                            )}

                            {isFocus == 0 && isSelf == 0 && (
                              <div
                                className={styles.ApplyConnectLiveList_receive_btn}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  isFocusByOnClick({
                                    expertsUserId: wxUserIdByItem,
                                    isFocus: 1, // 去关注
                                    type: 1, // 关注列表
                                  });
                                }}
                              >
                                关注
                              </div>
                            )}
                          </div>
                        );
                      })}
                  </div>
                )}

                {guestListType == 2 && (
                  <div className={styles.ApplyConnectLiveList}>
                    {Array.isArray(applyAdmissionList) &&
                      applyAdmissionList.map((item, index) => {
                        const {
                          id,
                          userName,
                          isAgree, // 是否同意：0申请中,1同意，2拒绝
                          wxUserId: wxUserIdByItem,
                        } = item || {};
                        if (isAgree != 0) {
                          return <div></div>;
                        }
                        return (
                          <div key={index} className={styles.ApplyConnectLiveItem}>
                            <div className={styles.ApplyConnectLiveLeft}>
                              <div className={styles.ApplyConnectLiveItem_avatar}>
                                <Avatar userInfo={item} size={24}></Avatar>
                              </div>
                              <div className={styles.ApplyConnectLiveItem_name}>{userName}</div>
                            </div>
                            <div className={styles.GuestlistItemRigth_ApplicationMeeting}>
                              <div
                                className={styles.refuse}
                                onClick={() => {
                                  updateStarSpaceApplyAdmission({
                                    wxUserId: id,
                                    refuseAdmittance: 1, // 操作类型：1拒绝、2准入
                                  });
                                }}
                              >
                                拒绝
                              </div>
                              {isAgree == 0 && (
                                <div
                                  className={styles.allow}
                                  onClick={() => {
                                    updateStarSpaceApplyAdmission({
                                      wxUserId: id,
                                      refuseAdmittance: 2, // 操作类型：1拒绝、2准入
                                    });
                                  }}
                                >
                                  准入
                                </div>
                              )}
                            </div>
                          </div>
                        );
                      })}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default connect(({ PlanetChatRoom, loading }: any) => ({ PlanetChatRoom, loading }))(Index);
