.wrap {
  width: 100%;
  background: #FFFFFF;
  border-radius: 3px 3px 3px 3px;
  opacity: 1;
  border: 1px solid #D9D9D9;
  padding: 0 0 0 16px;
  margin-bottom: 12px;

  .header {
    height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #E1E4E7;
    padding-right: 19px;

    .header_title {
      font-size: 16px;
      font-weight: 500;
      color: #000000;
    }

    .header_status {
      font-size: 14px;
      font-weight: 400;
      color: #0095FF;
      flex: 1;
      text-align: right;
      cursor: pointer;
    }

  }

  .hide_content {
    height: 72px;
    overflow: hidden;
  }

  .detail {
    border-bottom: 1px solid #E1E4E7;
    padding: 16px 0;

    .title {
      &>p {
        margin-bottom: 0;
      }

      padding-right: 16px;
      display: flex;
      font-size: 13px;
      font-weight: 400;
      color: #666666;

      .title_left {
        flex: 1;

        span> {
          font-weight: 500;
          color: #000000;
        }
      }

      .title_right {
        display: inline-block;
        width: 130px;
        text-align: right;
      }
    }

    .desc {
      width: 340px;
      font-size: 13px;
      font-weight: 400;
      color: #666666;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .detail:last-child {
    border-bottom: 0;
  }
}