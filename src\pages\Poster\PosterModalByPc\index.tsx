/**
 * @Description: PC端，生成海报弹窗
 * 使用方法：
 * ① 引入：import PosterModalByPc from '@/pages/Poster/PosterModalByPc'
 * ② 使用：<PosterModal visible={visible} spaceId={spaceId} onCancel={onCancel} />
 *   参数说明：visible：弹窗是否显示；spaceId：空间ID；onCancel：取消的回调
 */
import React, { useEffect, useState } from 'react'
import { connect } from 'umi'
import classNames from 'classnames'
import html2canvas from 'html2canvas'
import { Button, Spin, Modal, Typography, message } from 'antd'
import styles from './index.less'

import PosterTemplateDom from '@/pages/Poster/Components/PosterTemplateDom'    // 海报dom

// 模板数据list
const templateDataSource = [
  { id: 1, outerBgColor1: '#CEF9FF', outerBgColor2: '#E1E5FF', textColor: '#000', bgColor1: '#95C4FF', bgColor2: '#4E41FF', imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/poster_bg_1.png' },
  { id: 2, outerBgColor1: '#FFE483', outerBgColor2: '#FFFCEA', textColor: '#000', bgColor1: '#FFDD47', bgColor2: '#FF5C00',  imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/poster_bg_2.png' },
  { id: 3, outerBgColor1: '#FFCDDA', outerBgColor2: '#FFF4F7', textColor: '#000', bgColor1: '#FFE2E2', bgColor2: '#FF48B6',  imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/poster_bg_3.png' },
  { id: 4, outerBgColor1: '#B3E794', outerBgColor2: '#F1FBE7', textColor: '#fff', bgColor1: '#CADE90', bgColor2: '#00B15C',  imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/poster_bg_4.png' },
  { id: 5, outerBgColor1: '#C9E5FF', outerBgColor2: '#EAFAFF', textColor: '#fff', bgColor1: '#86E4FF', bgColor2: '#0067FF',  imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/poster_bg_5.png' },
  { id: 6, outerBgColor1: '#9FF6DF', outerBgColor2: '#EAFFF5', textColor: '#fff', bgColor1: '#5AEDB8', bgColor2: '#039BB0',  imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/poster_bg_6.png' },
]

interface PropsType {
  dispatch: any,
  visible: boolean,       // 弹窗是否显示
  spaceId: number,        // 空间ID
  onCancel: any,          // 点击取消的回调
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  const { dispatch, visible, spaceId } = props

  const initialTemplateData = {
    id: 1,
    outerBgColor1: '#CEF9FF',     // 页面背景色
    outerBgColor2: '#E1E5FF',
    textColor: '#000',            // 文字颜色
    bgColor1: '#95C4FF',          // 主持人区域背景色
    bgColor2: '#4E41FF',
    imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/poster_bg_1.png',   // 背景图片
  }
  const initialOutBgColorData = {
    outerBgColor1: '#CEF9FF',     // 页面背景色
    outerBgColor2: '#E1E5FF',
  }

  const [imgUrl, setImgUrl] = useState(null)                                   // 海报图片url
  const [templateData, setTemplateData] = useState(initialTemplateData)        // 当前模板数据
  const [outBgColorData, setOutBgColorData] = useState(initialOutBgColorData)  // 当前页面背景色
  const [loadingCreatePoster, setLoadingCreatePoster] = useState(false)        // 创建空间loading
  const [spaceState, setSpaceState] = useState({})                             // 直播or会议数据

  useEffect(() => {
    if (visible) {
      // 获取空间海报信息
      getSpacePosterInfo()
    } else {
      setTimeout(() => {
        setImgUrl(null)
        setTemplateData(initialTemplateData)
        setOutBgColorData(initialOutBgColorData)
        setLoadingCreatePoster(false)
        setSpaceState({})
      }, 200)
    }
  }, [visible])

  useEffect(() => {
    if (spaceState.id || spaceState.id == 0) {
      // 触发生成图片
      setLoadingCreatePoster(true)
    }
  }, [spaceState])

  useEffect(() => {
    if (loadingCreatePoster) {
      // 添加定时器，延时生成图片避免图片文字错位
      beforeCreatePoster()
    }
  }, [loadingCreatePoster])

  // 获取空间海报信息
  const getSpacePosterInfo = () => {
    dispatch({
      type: 'userInfoStore/getSpacePosterInfo',
      payload: {
        spaceId: spaceId,                            // 空间ID
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200) {
        setSpaceState(content || {})
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch(err => {})
  }

  // 延迟加载，避免图片文字错位
  const beforeCreatePoster = () => {
    setTimeout(() => {
      createPoster()
    }, 1000)
  }

  // 生成海报
  const createPoster = () => {
    html2canvas(document.querySelector('#poster_dom'),{
      foreignObjectRendering: false,
      backgroundColor: 'transparent',
      removeContainer: true,
      useCORS: true,
      scale: window.devicePixelRatio * 2,
      // allowTaint:true,
      // proxy: 'https://dhealth-test.friday.tech/'
    }).then(canvas => {
      const url = canvas.toDataURL('image/png')
      setImgUrl(url)
      setLoadingCreatePoster(false)
      setOutBgColorData({
        ...outBgColorData,
        outerBgColor1: templateData.outerBgColor1,
        outerBgColor2: templateData.outerBgColor2,
      })
    })
  }

  // 保存海报
  const savePoster = () => {
    const obj = document.createElement('a');
    const elink = obj;
    elink.download = 'poster.png';
    elink.href = imgUrl
    elink.click();
    // URL.revokeObjectURL(elink.href); // 释放URL 对象
    obj.remove();
  }

  // 保存海报（只有链接的保存）
  // const savePoster = (e) => {
  //   console.log(e.target)
  //   const url = 'https://t7.baidu.com/it/u=**********,**********&fm=193&f=GIF'
  //   const name = 'test.jpg'
  //
  //   const xhr = new XMLHttpRequest()
  //   xhr.open('GET', url, true)
  //   xhr.responseType = 'blob'
  //   xhr.onload = function () {
  //     if (xhr.status === 200) {
  //       console.log(xhr.response)
  //       const obj = document.createElement('a');
  //       const elink = obj;
  //       elink.download = name;
  //       elink.href = URL.createObjectURL(xhr.response);
  //       elink.click();
  //       URL.revokeObjectURL(elink.href); // 释放URL 对象
  //       obj.remove();
  //       // window.navigator.msSaveOrOpenBlob(xhr.response, name)
  //     }
  //   }
  //   xhr.send()
  // }

  // 点击切换模板
  const onClickTemplate = (obj) => {
    if (loadingCreatePoster) {
      return
    }
    setTemplateData({
      ...obj,
    })
    setLoadingCreatePoster(true)
  }

  // 复制空间链接
  const onCopy = () => {
    message.success('复制成功')
  }

  return (
    <Modal
      open={visible}
      className={styles.modal}
      title="海报"
      onCancel={props.onCancel}
      // destroyOnClose
      forceRender
      footer={null}
      width={518}
    >

      <div className={styles.container}>
        <Spin spinning={loadingCreatePoster}>
          <div className={styles.content} style={{background: `linear-gradient(180deg, ${outBgColorData.outerBgColor1} 0%, ${outBgColorData.outerBgColor2} 100%)`}}>
            <img src={imgUrl} width={320} height={468} alt=""/>
          </div>
        </Spin>

        {/* 海报dom-用于生成图片 */}
        <PosterTemplateDom templateData={templateData} data={spaceState}/>

        {/* 模板 */}
        <div className={styles.bottom_wrap}>
          <p className={styles.bottom_title}>选择模板</p>
          <div className={styles.bottom_template_wrap}>
            {
              templateDataSource.map(item => {
                return (
                  <div
                    key={item.id}
                    className={classNames(styles.template_option, {
                      [styles.checked]: templateData.id == item.id,
                    })}
                    onClick={() => onClickTemplate(item)}
                  >
                    <img src={item.imgUrl} width={71} height={90} alt=""/>
                  </div>
                )
              })
            }
          </div>
        </div>
      </div>

      <div className={styles.footer}>
        <Typography.Paragraph copyable={{
          text: `${window.location.origin}/PlanetChatRoom/${spaceState.starSpaceType == 2 ? 'Meet' : 'Live'}/${spaceId}?shareUserId=${UserInfo?.friUserId}&isShare=1` + (spaceState.starSpaceType == 2 && spaceState.password ? `&pwd=${spaceState.password}` : ''),
          icon: [<Button>复制链接</Button>, <Button>复制链接</Button>],
          tooltips: ['', ''],
          onCopy: onCopy,
        }}></Typography.Paragraph>
        <Button type="primary" onClick={savePoster}>保存海报</Button>
      </div>
    </Modal>
  )
}

export default connect(({ loading }: any) => ({loading}))(Index)
