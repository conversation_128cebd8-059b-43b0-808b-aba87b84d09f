const initStatePage = {
  pageNum: 1,    // :1,
  pageSize: 10,   // : 10,
  resultList: [], // : [],
  total: 0,      // : 0,
  // hasMore: true,  // 加载更多
  // loadMore: false,
}
export default {
  namespace: 'pcAccount',
  state: {
    tabState: 1,                      // 一级tab状态
    subTabState: 1,                   // 二级tab状态
    threeTabState: 1,                 // 三级tab状态
    stateByPcMyConsultationList: {    // 我的指导 筛选项控制
      selectByModalByType: null,       // 我的指导-选择指导类型
      selectByModalByStatus: [],       // 我的指导-筛选指导支付状态
      selectByModalByProcessNode: [],  // 我的指导-筛选指导进度状态
      searchText: null,                // 我的指导-搜索文本
      searchType: '3',                   // 我的指导-搜索类型
      valueByRangePicker: null,        // 我的指导-时间范围
      tableData: initStatePage,        // 我的指导-当前分页
      tabType: 1,                      // 我的指导-当前tab类型
      secondQueryType: 0,              // 我的指导-当前二级tab类型 二级查询类型(0全部、1草稿、2指导中、3指导结束)
    }
  },

  effects: {},

  reducers: {
    // 保存数据
    save(state, {payload}) {
      return {
        ...state,
        ...payload,
      }
    },

    // 保存我的指导-筛选项控制
    saveByPcMyConsultationList(state, {payload}) {
      return {
        ...state,
        stateByPcMyConsultationList: {
          ...state.stateByPcMyConsultationList,
          ...payload,
        }
      }
    },

    // 清空数据
    clean(state, {payload}) {
      return {
        ...state,
        tabState: 1, // 一级tab状态
        subTabState: 1, // 二级tab状态
        threeTabState: 1, // 三级tab状态
      }
    },

    // 清空我的指导列表-筛选控制项数据
    cleanByPcMyConsultationList(state, {payload}) {
      return {
        ...state,
        stateByPcMyConsultationList: {
          ...state.stateByPcMyConsultationList,
          selectByModalByType: null,      // 我的指导-选择指导类型
          selectByModalByStatus: [],      // 我的指导-筛选指导支付状态
          selectByModalByProcessNode: [], // 我的指导-筛选指导进度状态
          searchText: null,              // 我的指导-订单号
          valueByRangePicker: null,       // 我的指导-时间范围
          tableData: initStatePage,       // 我的指导-当前分页
          tabType: 1,
        }
      }
    },
  },

  subscriptions: {
    setup({dispatch, history}) {
      return history.listen(({pathname, search}) => {
        if (
          pathname.indexOf('/PlanetChatRoom/') == -1 &&
          pathname.indexOf('/Case/CaseDetails') == -1 &&
          pathname.indexOf('/Expert/ExpertDetails') == -1 &&
          pathname.indexOf('/UserInfo') == -1 &&
          pathname.indexOf('/userInfo') == -1 &&
          pathname.indexOf('/ConsultationModule/ConsultationDetails') == -1 &&
          pathname.indexOf('/CreationOrthodontics/Step') == -1 &&
          pathname.indexOf('/ConsultationModule/StartConsultation') == -1 &&
          pathname.indexOf('/CreateGraphicsText/CreateArticle') == -1 &&
          pathname.indexOf('/CreateGraphicsText/CreateArticle/Step2') == -1 &&
          pathname.indexOf('/CreateGraphicsText/CreateExternalLinks') == -1 &&
          pathname.indexOf('/UserInfo/CreateSpaceByPc') == -1
        ) {
          dispatch({
            type: 'clean',
          })
        }
      })
    }
  }
}
