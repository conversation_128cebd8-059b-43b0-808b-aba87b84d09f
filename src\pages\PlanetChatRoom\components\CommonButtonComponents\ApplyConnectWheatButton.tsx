// 申请连麦按钮
import React, { useState, useCallback } from 'react';
import { message } from 'antd'; // Assuming you are using Ant Design for the message component
import classNames from 'classnames'; // Assuming you are using classNames for conditional styling
import styles from './index.less'; // Import your styles module

const ApplyConnectWheatButton = ({
                                   props,
                                   isJoined,
                                   currentUserType,
                                   handUpType,
                                   SpaceInfo,
                                   resetTimer,
                                   isNotLogin,
                                   setModalVisibleByUserTokenInvalid,
                                   starSpaceType,
                                 }) => {
  const [handUpStatusTypeEnter, setHandUpStatusTypeEnter] = useState(false);

  let starSpaceTypeLianMaiText = starSpaceType == 2 ? '发言' : '连麦'

  const handleClick = useCallback(
    (e) => {
      e.stopPropagation();
      resetTimer();

      if (isNotLogin) {
        setModalVisibleByUserTokenInvalid();
        return null;
      }

      if (SpaceInfo?.handUpStatusType === 0) {
        // 当前是申请连麦状态
        if (!handUpStatusTypeEnter) {
          // 第一次点击取消连麦 提示吐司
          setHandUpStatusTypeEnter(true);
          message.warning(`再次点击取消${starSpaceTypeLianMaiText}`);
        } else {
          // 第二次点击取消连麦
          props.onClickLianMai();
          setHandUpStatusTypeEnter(false);
        }
      } else if (SpaceInfo?.handUpStatusType === 1) {
        // 当前是连麦状态
        if (!handUpStatusTypeEnter) {
          // 第一次点连麦中 提示吐司
          setHandUpStatusTypeEnter(true);
          message.warning(`再次点击下麦`);
        } else {
          // 第二次点击下麦
          props.onClickLianMai();
          // setHandUpStatusTypeEnter(false);
        }
      } else {
        props.onClickLianMai();
        setHandUpStatusTypeEnter(false);
      }
    },
    [
      isNotLogin,
      setModalVisibleByUserTokenInvalid,
      handUpStatusTypeEnter,
      SpaceInfo,
      props,
      resetTimer,
    ]
  );

  return (
    <div
      onClick={handleClick}
      className={classNames({
        [styles.ApplyConnectWheat]: true,
        [styles.CancelApplyConnectWheat]:
        SpaceInfo?.handUpStatusType !== null && handUpStatusTypeEnter,
      })}
    >
      <div className={styles.lianmaiWarp}>
        <div
          className={classNames({
            [styles.SpatialDetaiLianmaiBtnIcon]: true,
            [styles.SpatialDetaiLianmaiBtnIconCancel]:
            SpaceInfo?.handUpStatusType === 1 && handUpStatusTypeEnter,
          })}
        ></div>
        <div>
          {SpaceInfo?.handUpStatusType === null && `申请${starSpaceTypeLianMaiText}`}
          {SpaceInfo?.handUpStatusType === 0 && !handUpStatusTypeEnter && '等待中...'}
          {SpaceInfo?.handUpStatusType === 0 && handUpStatusTypeEnter && `取消${starSpaceTypeLianMaiText}`}

          {SpaceInfo?.handUpStatusType === 1 && !handUpStatusTypeEnter && `${starSpaceTypeLianMaiText}中...`}
          {SpaceInfo?.handUpStatusType === 1 && handUpStatusTypeEnter && '下麦'}
        </div>
      </div>
    </div>
  );
};

export default ApplyConnectWheatButton;
