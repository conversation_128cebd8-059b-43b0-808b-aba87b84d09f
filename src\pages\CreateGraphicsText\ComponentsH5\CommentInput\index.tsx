import React, { useState, useEffect ,useRef } from 'react';
import { connect } from 'umi';
import { TextArea } from 'antd-mobile'
import styles from './index.less';

const Index: React.FC = (props: any) => {
  const inputDom = useRef(null)
  const [ inputValue ,setInputValue] = useState('')

  useEffect(()=>{

  },[])

  // 输入框输入
  const textAreaOnChange = () => {}



  return(
    <div className={styles.container}>
      <div className={styles.false_input_box}>
        <div className={styles.wrap}>
          <div className={styles.left}>
            <i></i>
            <span>友善评论...</span>
          </div>
          <i className={styles.right}></i>
        </div>
      </div>

      <div className={styles.true_input_box}>
        <div className={styles.textarea_box}>
          <TextArea
            placeholder="友善评论..."
            autoSize={{maxRows: 6, minRows: 1}}
            rows={1}
            onChange={textAreaOnChange}
          />
        </div>
        <div className={styles.btn_box}>
          <i></i>
          <span>发布</span>
        </div>
      </div>
    </div>
  )
}

export default connect(({ userInfoStore, loading }: any) => ({ userInfoStore, loading }))(Index)
