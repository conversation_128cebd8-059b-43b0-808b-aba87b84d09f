/**
 * @Description: 查看完整服务流程按钮及弹窗，PC端
 */
import React, { useState } from 'react'
import { connect } from 'umi'
import classNames from 'classnames'
import { QuestionCircleOutlined, CheckOutlined } from '@ant-design/icons'
import styles from './index.less'

// 完整服务流程弹窗
import CompleteProcessModal from '@/componentsByPc/ConsultationSteps/CompleteProcessModal'

interface PropsType {
  title: string, // 标题
}


const Index: React.FC<PropsType> = (props: any) => {
  const { title } = props
  const [completeProcessVisible, setCompleteProcessVisible] = useState(false)

  // 打开完整服务流程弹窗
  const completeProcessModalShow = () => {
    setCompleteProcessVisible(true)
  }

  // 关闭完整服务流程弹窗
  const completeProcessModalHide = () => {
    setCompleteProcessVisible(false)
  }

  return (
    <div className={styles.step_container}>
      <div className={styles.step_content}>
        {title}
      </div>
      <div className={styles.btn} onClick={completeProcessModalShow}>
        <QuestionCircleOutlined/>
        查看完整服务流程
      </div>

      {/* 完整服务流程弹窗 */}
      <CompleteProcessModal
        visible={completeProcessVisible}
        onCancel={completeProcessModalHide}
      />
    </div>
  )
}

export default connect(({ userInfoStore, loading }: any) => ({userInfoStore, loading}))(Index)
