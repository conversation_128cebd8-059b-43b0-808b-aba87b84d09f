.modal {
  width: 649px;
  position: relative;
  :global {
    .ant-modal-header {
      border: none;
      text-align: center;
      padding: 24px;
    }
    .ant-modal-title {
      font-size: 20px;
      font-weight: 500;
      color: #000;
      line-height: 28px;
    }
    .ant-modal-body {
      padding-top: 10px;
      padding-bottom: 40px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }

  .close {
    position: absolute;
    top: -82px;
    right: -235px;
    cursor: pointer;
  }
}

.preview_bg {
  width: 399px;
  height: 363px;
  background: url('../../../../assets/GlobalImg/preview_head.png') no-repeat;
  background-size: 100% 100%;
  padding: 184px 12px 0 12px;
  box-sizing: border-box;
}

.box {
  width: 100%;
  padding: 16px 12px;
  box-sizing: border-box;
  background: #fff;
  margin-bottom: 8px;
}

.header {
  display: flex;
  width: 100%;
  margin-bottom: 12px;

  .left_picture {
    width: 36px;
    min-width: 36px;
    height: 36px;
    border-radius: 50%;
    position: relative;
    margin-right: 8px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    font-size: 16px;
    font-weight: 500;
    color: #fff;
    text-align: center;
    line-height: 36px;
    white-space: nowrap;
  }

  .right_text {

    .name {
      font-size: 15px;
      font-weight: 600;
      color: #000;
      line-height: 21px;
    }

    .text {
      font-size: 11px;
      color: #333;
      line-height: 15px;
    }
  }
}

.content {
  width: 100%;

  .title {
    font-size: 14px;
    font-weight: 400;
    color: #000000;
    line-height: 20px;
    word-break: break-all;
    margin-bottom: 8px;
  }

  .no_picture_box {
    width: 100%;
    height: 64px;
    background: #FFFFFF;
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
    border: 1px solid #EBEBEB;
    display: flex;
    padding: 8px;
    box-sizing: border-box;

    .init_img {
      width: 48px;
      height: 48px;
      margin-right: 8px;
      flex-shrink: 0;

      img {
        width: 48px;
        height: 48px;
      }
    }

    .link_content {
      flex: 1;
      overflow: hidden;

      .link_title {
        font-size: 13px;
        font-weight: 400;
        color: #666666;
        line-height: 18px;
        word-break: break-all;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .link_more {
        font-size: 13px;
        font-weight: 400;
        color: #0095FF;
        line-height: 18px;

        img {
          width: 16px;
          height: 16px;
        }
      }
    }
  }
}

.preview_no_bg {
  width: 399px;
  height: 363px;
  background: url('../../../../assets/GlobalImg/preview_no_head.png') no-repeat;
  background-size: 100% 100%;
  padding: 79px 12px 0 12px;
  box-sizing: border-box;

  .picture_box {
    width: 100%;
    background: #F5F6F8;
    border-radius: 0px 0px 4px 4px;
    overflow: hidden;

    .img {
      width: 351px;
      height: 142px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
    }

    .link_more {
      padding: 8px 6px;
      font-size: 13px;
      font-weight: 400;
      color: #0095FF;
      line-height: 18px;

      img {
        margin-right: 2px;
        width: 16px;
        height: 16px;
      }
    }
  }
}
