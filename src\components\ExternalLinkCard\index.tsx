/**
 * @Description: 外链卡片组件
 */
import React from 'react'
import { history } from 'umi'
import { stringify } from 'qs';
import { message } from 'antd'
import { Toast } from 'antd-mobile'
import styles from './index.less'

// 图片icon
import blueAssociatedIcon from '@/assets/GlobalImg/blue_associated.png'; // 关联王国小图标
import linkBgIcon from '@/assets/GlobalImg/link_bg.png'; // 外链默认图
import linkIcon from '@/assets/GlobalImg/link.png'; // 外链小图标

import UserCardByImageText from '@/components/UserCardByImageText' // 用户卡片

interface PropsType {
  style?: any; // 样式
  isMyPages?: any;
  refreshDataById?: any;
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {
    style, isMyPages, item, refreshDataById,
    isShowMoreOperate = false, // 是否展示点点点更多操作
  } = props
  const {
    createDate,       //: [创建时间] : "2024-01-09 14:53:47"
    createUserId,     //: [创建人id] : 60
    expertsInfo,      //: [专家信息] : null
    forwardDescribe,  //: [转发描述] : null
    gdp,              //: [页面GDP] : 210
    headUrlShow,      //: [用户头像] : null
    id,               //: [主键ID] : 57
    imageTextContent, //: [文章、帖子内容] : null
    imageTitle,       //: [标题] :null
    imageType,        //: [图文类型：1.文章 2.帖子 3.外链 4.空间] : 2
    isExperts,        //: [是否是专家：0:否，1:是] : 0
    isFocus,          //: [0未关注 1已关注] : 0
    isForward,        //: [是否转发：1.转发 0，非转发] : null
    isSpotLike,       //: [是否点赞 1是 0否] : 0
    kingdomId,        //: [关联王国ID] : 1
    kingdomName,      //: [关联王国名称] : "数字化讨论"
    outerChain,       //: [外链地址] : null
    spaceId,          //: [空间ID] :null
    spaceStatus,      //: [空间状态: 1直播中、2预约中、3弹幕轰炸中] : null
    spotLikeCount,    //: [点赞数量] : 0
    spotLikeUserList, //: [点赞用户信息，最多3条] : []
    textImgList,      //: [关联的图片] : null
    topicInfoList,    //: [关联的话题信息] : null
    userName,         //: [用户名称] : "志君"
    status,           // 状态：1.审核通过（已发布） 0.未审核 2.审核未通过 3.草稿
    outerChainPaperwork, // 按钮文案
    operateDateDescs,
  } = item|| {}

  // 跳转王国详情
  const onClickKingdom = () => {
    if (window.location.pathname == `/Kingdom/${kingdomId}`) { return; }
    history.push(`/Kingdom/${kingdomId}`)
  }

  // 跳转了解更多（站内打开外链地址）
  const jumpClickFn = (e) => {
    e.preventDefault();
    e.stopPropagation()
    // 是从我的主页过来的，需要跳转编辑页
    if (isMyPages && status == 3) {
      return history.push(`/CreateGraphicsText/CreateExternalLinks?id=${id}`);
    }
    if (isMyPages && status == 2) {
      message.warning('审核未通过')
      history.push(`/CreateGraphicsText/CreateExternalLinks?id=${id}`);
      return
    }
    if (status == 0) {
      message.warning('审核中')
      return
    }
    history.push(`/CreateGraphicsText/ExternalLinksDetails?${stringify({id:id})}`)
  }

  // 关注或取消关注回调，0 取消关注，1 关注
  const handleFollowAndCheck = (isFocus2) => {
    if (refreshDataById) {
      refreshDataById(item.id)
    }
  }

  // 删除或下架回调
  const handleDeleteOrLow = () => {
    if (refreshDataById) {
      refreshDataById(item.id)
    }
  }

  return (
    <div className={styles.external_link_wrap} style={style}>
      {/* 用户信息 */}
      <UserCardByImageText
        headUrlShow={headUrlShow}
        userName={userName}
        createUserId={createUserId}
        isExperts={isExperts}
        operateDateDescs={operateDateDescs}
        isFocus={isFocus}
        expertsInfo={expertsInfo}
        handleFollowAndCheck={handleFollowAndCheck}
        isShowMoreOperate={isShowMoreOperate}
        id={id}
        imageType={imageType}
        status={status}
        handleDeleteOrLow={handleDeleteOrLow}
        style={{marginBottom: 12}}
      />

      <div className={styles.content} onClick={jumpClickFn}>
        <div className={styles.title} dangerouslySetInnerHTML={{__html: imageTitle}}></div>
        {/* textImgList.length > 0，有封面图 */}
        {
          textImgList && textImgList.length > 0 ?
            <div className={styles.picture_box}>
              <div className={styles.img}><img className={styles.img_box} src={textImgList[0].imageUrlShow} alt="" /></div>
              <div className={styles.link_more} onClick={jumpClickFn}><img src={linkIcon} alt="" />{outerChainPaperwork || '了解更多'}</div>
            </div>
            :
            <div className={styles.no_picture_box}>
              <div className={styles.init_img}><img src={linkBgIcon} alt="" /></div>
              <div className={styles.link_content}>
                <div className={styles.link_title} dangerouslySetInnerHTML={{__html: imageTitle}}></div>
                <div className={styles.link_more} onClick={jumpClickFn}><img src={linkIcon} alt="" />{outerChainPaperwork || '了解更多'}</div>
              </div>
            </div>
        }
      </div>

      {/* 王国 */}
      {
        !!kingdomName &&
        <div className={styles.kingdom_wrap} onClick={onClickKingdom}>
          <img src={blueAssociatedIcon} width={14} height={14} alt="" />
          <span>{kingdomName}</span>
        </div>
      }
    </div>
  )
}

export default Index
