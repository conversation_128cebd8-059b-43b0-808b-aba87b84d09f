/**
 * @Description: 查看凭证弹窗
 */
import React, { useState, useEffect } from 'react'
import { Image, Modal } from 'antd'
import { EyeOutlined } from '@ant-design/icons'
import styles from './index.less'

// 图片icon
import pdf_icon from '@/assets/PlanetChatRoom/WhiteboardLiveRoom_PDF_icon.png' // pdf

interface PropsType {
  visible: boolean,
  proofs: any, // 已上传凭证
  onCancel: any; // 关闭弹窗
  onClickPreviewPdfBtn: any; // 点击预览PDF
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {
    proofs,
    visible,
    onCancel,
    onClickPreviewPdfBtn,
  } = props

  const [proofsImgList, setProofsImgList] = useState([]) // 图片类型
  const [proofsPdfList, setProofsPdfList] = useState([]) // pdf类型

  useEffect(() => {
    if (visible) {
      if (proofs && proofs.length > 0) {
        const imgList = proofs.filter(item => item.proofUrlView.indexOf('.pdf') == -1)
        const pdfList = proofs.filter(item => item.proofUrlView.indexOf('.pdf') > -1)
        setProofsImgList(imgList)
        setProofsPdfList(pdfList)
      }
    } else {
      setProofsImgList([])
      setProofsPdfList([])
    }
  }, [visible])

  return (
    <Modal
      visible={visible}
      title="查看凭证"
      width={600}
      className={styles.modal}
      destroyOnClose={true}
      footer={null}
      onCancel={onCancel}
    >
      <div className={styles.form_item_wrap} style={{marginBottom: 24}}>
        <div className={styles.form_item_label}>凭证：</div>
        <div className={styles.upload_wrap}>
          {/* 图片类型 */}
          {
            proofsImgList.length > 0 &&
            <Image.PreviewGroup>
              {
                proofsImgList.map(item => {
                  return (
                    <div key={item.proofUrlView} className={styles.upload_item}>
                      <Image src={item.proofUrlView} style={{objectFit: 'contain'}} width={'100%'} height={'100%'}/>
                    </div>
                  )
                })
              }
            </Image.PreviewGroup>
          }
          {/* PDF类型 */}
          {
            proofsPdfList.length > 0 && proofsPdfList.map(item => {
              return (
                <div key={item.proofUrlView} className={styles.upload_item}>
                  <img src={pdf_icon} width={'100%'} height={'100%'} alt=""/>
                  <div className={styles.upload_item_mask} onClick={() => onClickPreviewPdfBtn(item.proofUrlView)}>
                    <EyeOutlined/>
                    <span>预览</span>
                  </div>
                </div>
              )
            })
          }
        </div>
      </div>
    </Modal>
  )
}

export default Index

