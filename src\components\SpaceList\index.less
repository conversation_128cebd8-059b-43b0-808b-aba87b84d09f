.space_container {
  position: relative;
  padding: 0 12px 4px;
  &.one {
    .item_box {
      .cover_img {
        border-radius: 12px 12px 0 0;
        height: 121px;
        .status_box {
          top: 12px;
        }
        .gdp {
          right: 0px;
        }
        .danmu_box {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 667;
        }
        .title_in_cover_image {
          font-size: 16px;
        }
      }
      .space_info_box{
        padding: 8px 12px 16px;
        background: #fff;
        border-radius: 0 0 12px 12px;
        //box-shadow: inset 0px 1px 1px 0px rgba(255,255,255,1), 0px 2px 6px 0px rgba(233,238,242,1);
        .title {
          font-size: 16px;
        }
      }
    }
  }
  &.two {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 0 6.5px 4px;
    .item_box {
      width: 50%;
      min-width: 50%;
      padding: 0 5.5px;
      .cover_img {
        border-radius: 4px;
        height: 96px;
        .status_box {
          top: 10px;
        }
        .danmu_box {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 667;
        }
        .title_in_cover_image {
          font-size: 14px;
          line-height: 21px;
        }
      }
      .space_info_box {
        .title {
          min-height: 42px;

          .title_text {
            font-size: 14px;
            line-height: 21px;
          }
        }
      }
    }
  }
  &.sixty {
    padding: 0 0 4px;
    :global {
      .adm-swiper-track {
        padding: 0 12px;
      }
      .adm-swiper-item {
        padding-right: 12px;
      }
      .adm-swiper-slide:last-child .adm-swiper-item {
        padding-right: 0;
      }
      .adm-swiper-indicator {
        display: none;
      }
    }
    .item_box {
      .cover_img {
        border-radius: 4px;
        height: 96px;
        .status_box {
          top: 10px;
        }
        .title_in_cover_image {
          font-size: 14px;
          line-height: 21px;
        }
      }
      .space_info_box {
        .title {
          min-height: 42px;

          .title_text {
            font-size: 14px;
            line-height: 21px;
          }
        }
      }
    }
  }
  .item_box {
    margin-bottom: 12px;
    .cover_img {
      position: relative;
      //background-repeat: no-repeat;
      //background-position: center;
      //background-size: cover;
      //background-image: url("https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/PlanetChatRoom_HorizontalLiveRoom_Live_bg.png");
      overflow: hidden;
      // 封面中的标题
      .title_in_cover_image {
        position: absolute;
        z-index: 600;
        width: 56%;
        top: 18px;
        left: 0;
        padding-left: 12px;
        color: #fff;
        font-weight: 500;
        word-break: break-all;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2; /* 指定显示行数 */
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .status_box {
        position: absolute;
        right: 8px;
        background: linear-gradient(135deg, #4183EA 0%, #003AAF 100%);
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: nowrap;
        font-size: 12px;
        color: #FFFFFF;
        padding: 0 8px;
        height: 21px;
        white-space: nowrap;
        z-index: 666;
        .status_icon {
          display: block;
          width: 12px;
          height: 12px;
          margin-right: 2px;
          background-repeat: no-repeat;
          background-position: center;
          &.icon1 {
            background: url("../../assets/GlobalImg/space1.png") no-repeat center;
            background-size: 100% 100%;
          }
          &.icon2 {
            background: url("../../assets/GlobalImg/space2.png") no-repeat center;
            background-size: 100% 100%;
          }
          &.icon3 {
            background: url("../../assets/GlobalImg/space3.png") no-repeat center;
            background-size: 100% 100%;
          }
        }
      }
      .gdp {
        position: absolute;
        bottom: 0;
        right: 0;
        color: #fff;
        font-size: 11px;
        line-height: 15px;
        z-index: 666;
        background: rgba(0,0,0,0.4);
        border-radius: 8px 0px 0px 0px;
        padding: 2px 6px;
      }
    }
    .space_info_box {
      padding-top: 8px;
      .title {
        color: #000000;
        font-weight: 500;
        word-break: break-all;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2; /* 指定显示三行 */
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .space_intro {
        font-size: 12px;
        font-weight: 400;
        color: #888888;
        line-height: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-top: 4px;
      }
      .footer {
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
        align-items: center;
        margin-top: 11px;
        .footer_left {
          flex: 1;
          display: flex;
          flex-wrap: nowrap;
          align-items: center;
          overflow: hidden;
          .left_avatar {
            width: 23px;
            min-width: 23px;
            height: 23px;
            border-radius: 50%;
            position: relative;
            margin-right: 8px;
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
            font-size: 10px;
            font-weight: 500;
            color: #fff;
            text-align: center;
            line-height: 24px;
            white-space: nowrap;
            & > i {
              position: absolute;
              width: 11px;
              height: 11px;
              top: -3px;
              right: -3px;
              background: url("../../assets/GlobalImg/crown.png") no-repeat center;
              background-size: 100%;
            }
          }
          .left_name {
            flex: 1;
            font-size: 13px;
            color: #666666;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        .footer_right {
          display: flex;
          flex-wrap: nowrap;
          align-items: center;
          .right_avatar {
            width: 23px;
            min-width: 23px;
            height: 23px;
            border: 1px solid #fff;
            border-radius: 50%;
            margin-right: -8px;
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
            font-size: 10px;
            font-weight: 500;
            color: #fff;
            text-align: center;
            line-height: 24px;
            white-space: nowrap;
            &:last-child {
              margin-right: 0;
            }
          }
          .avatar_more {
            width: 23px;
            height: 23px;
            border: 1px solid #fff;
            border-radius: 50%;
            background: #E6F4FF;
            color: #0095FF;
            line-height: 22px;
            text-align: center;
          }
        }
      }
    }
  }
}
