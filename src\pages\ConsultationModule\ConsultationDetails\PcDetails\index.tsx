/**
 * @Description: PC端专家指导详情
 * @author: 赵斐
 */
import React, { useEffect, useState, useReducer, useRef } from 'react';
import { connect, history } from 'umi';
import classNames from 'classnames'
import { Spin, message, Modal } from 'antd'
import { useThrottle, getArrailUrl } from '@/utils/utils'
import TIM from 'tim-js-sdk';
import InfiniteScroll from 'react-infinite-scroller';
import styles from './index.less'
import { ProcessingImData } from '@/utils/im-index'
// 公共导航组件
import PcHeader from '@/componentsByPc/PcHeader'
// 头部组件
import Header from '@/pages/ConsultationModule/PcComponents/Header'
// 进度条
import ConsultationSteps from '@/componentsByPc/ConsultationSteps'
// 其他资料展示组件
import OtherInformation from '@/pages/ConsultationModule/PcComponents/OtherInformation'
// 结束指导弹窗
import EndConsultationTipsModal from '@/pages/ConsultationModule/PcComponents/EndConsultationTipsModal'
// PC端指导用户提问卡片(视频指导使用)
import UserQuestions from '@/pages/ConsultationModule/PcComponents/UserQuestions'
// 病例信息（与H5共用一个）
import ConsultationCaseCard from '@/pages/ConsultationModule/H5Components/ConsultationCaseCard'
// PC端专家(用户)信息卡片
import UserInfoCard from '@/pages/ConsultationModule/PcComponents/UserInfoCard'
// 指导预约时间卡片
import AppointmentTimeCard from '@/pages/ConsultationModule/PcComponents/AppointmentTimeCard'
// 指导订单信息卡片（与H5共用一个）
import ConsultationOrderCard from '@/pages/ConsultationModule/H5Components/ConsultationOrderCard'
// 移动端指导会议记录信息卡片（与H5共用一个）
import ConferenceRecordCard from '@/pages/ConsultationModule/H5Components/ConferenceRecordCard'
// PC端聊天组件
import IMInlet from '@/pages/ConsultationModule/PcIMComponents/IMInlet'
// PC端聊天组件
import ChatInputBottomBox from '@/pages/ConsultationModule/PcIMComponents/ChatInputBottomBox'
// 我已关注公众号，不再提示（与H5共用一个）
import FollowOfficialAccountTips from '@/pages/ConsultationModule/H5Components/FollowOfficialAccountTips'
// 指导订单详情弹窗（含支付流程）
import ModalByConsultationDetails from '@/components/ModalByConsultationDetails'
// 客服小忆微信\预约会议弹窗（PC）
import CustomerServiceWeChatModal from '@/pages/ConsultationModule/PcComponents/CustomerServiceWeChatModal'
// 正畸审核病例卡片
import OrthodonticCaseCard from '@/pages/ConsultationModule/PcComponents/OrthodonticCaseCard'
// 正畸病例审核记录卡片
import AuditRecordsCard from '@/pages/ConsultationModule/PcComponents/AuditRecordsCard'
// 方案审核弹窗
import SchemeReviewModal from '@/pages/ConsultationModule/PcComponents/SchemeReviewModal'
// 方案审核提示弹窗
import PlanPromptModal from '@/pages/ConsultationModule/PcComponents/PlanPromptModal'

const initStatePage = {
  pageNum: "",
  hasMore: true,  // 加载更多
  loadMore: false,
  isScroll: false,   // 滚动分页控制页面数据定位
}
const initConsultationDataSource = {
  type: null,
  imGroupId: null,       // 群组ID
  expertsId: '',      // 指导专家ID
  createDate: null,     // 创建时间
  processNode: null,
  // 流程节点(图文流程节点[1选择指导方式、2描述病例问题、3支付指导费用、4病例资料被查看、5问题被回复并对话、6结束指导交易成功];
  // 视频流程节点[1选择指导方式、2描述病例问题、3提交指导单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、7结束指导、8确认并支付指导费用、9交易成功])
  createUserName: null, // 创建人
  createUserId: null,   // 创建人ID
  createUserHeadUrlShow: null,  // 用户头像
  h5BaseUserDto: null,  // 专家信息
  consultationCaseInfoDto: {
    consultationCaseMediaDtoList: [],   // 其他资料
    orthodonticCaseDictDtoList:[],  // 正畸病例信息
    firstQuestion: '',   // 初次提问
  },  // 病例相关信息
  videoAppointment: null,   // 最新预约的视频指导会议时间
  videoRecordList: [],    // 历史视频指导-会议记录
  thisUserIsExperts: null,  // 当前人是否是专家：0:否，1:是
  attachmentCount: null,    // 附件数量
  isFinish: null,     //当前节点是否完成(1是、0否)
  orderNumber: null,  // 指导订单
  status: null,        // 支付状态(0已取消、1未支付(待评估)、2去支付(已评估)、3已支付)
  amount: 0,       // 账单金额
  defaultAssistantUrl: null, //小忆二维码
  auditRecordList:[], // 审核记录
  tenantId:null,      // 租户ID
}
const initImList: any[] | undefined = []  // IM列表数据

const initPlanTips = {
  planTipsVisible:false,
  planInputValue:'',
  planType:0
}
const reducer = (state: any, action: { type: any; payload: any; }) => {
  switch (action.type) {
    case 'insert':
      return [...state, ...action.payload];
    case 'receive':
      return [...state, ...action.payload];
    case 'replace':
      return [...action.payload];
    case 'receiveHistory':
      return [...action.payload, ...state,];
    case 'clearData':
      return [];
    // default:
    //   throw new Error();
  }
};
const Index: React.FC = (props: any) => {
  // 是否嵌套在5i5ya的iframe中
  const isInIframe = (self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya')

  const { dispatch, loading, consultation } = props;
  const { uploadProgress } = consultation || {}
  const { location } = history || {}
  const { query } = location || {}
  const { consultationId, consultationType, modalStatus, isGoback } = query || {}   // 指导ID  isGoback=1 不展示返回按钮
  const chatListRef = useRef(null)
  const val = useRef();
  const [timObj, setTimObj] = useState(null)
  const [statePage, setStatePage] = useState(initStatePage)  // 当前分页
  const [pageScrollHeight, setPageScrollHeight] = useState(0)  // 页面滚动高度
  const [stateImList, setStateImList] = useReducer(reducer, initImList);  // IM聊天数据
  const [consultationDataSource, setConsultationDataSource] = useState(initConsultationDataSource)  // 指导详情相关数据

  const [isModalOpen, setIsModalOpen] = useState(false);    // 结束指导提示弹窗
  const [modelByVisible, setModelByVisible] = useState(null)  // (视频)订单详情状态(包含指导ID)
  // const [weChatModal, setWeChatModal] = useState(false)   // 预约会议二维码弹窗

  const [isShowMpQr, setIsShowMpQr] = useState(false)   // 公众号展示卡片状态
  const [messageReceived, setMessageReceived] = useState({
    receivedStatus: false,  // 监听接收消息
    data: []                // 接收消息
  })

  const [isPlanModalOpen,setIsPlanModalOpen] = useState(false); // 方案审核弹窗状态
  const [planTipsModal,setPlanTipsModal] = useState(initPlanTips); // 方案审核提示弹窗状态

  const userInfoStr = localStorage.getItem('userInfo')
  const userInfo = userInfoStr ? JSON.parse(userInfoStr) : {}
  const {
    pageNum,
    hasMore,
    loadMore,
    isScroll
  } = statePage || {}
  const {
    planTipsVisible,
    planInputValue,
    planType
  } = planTipsModal || {}

  //
  useEffect(() => {

    if (modalStatus == '1') {
      setModelByVisible(consultationId)
    }
    callInterfaceFun()
    return ()=>{
      dispatch({
        type:'consultation/uploadProgressLoading',
        payload:{
          uploadProgress:[]
        }
      })
    }
  }, [])

  // 初始调用页面接口
  const callInterfaceFun = async () => {
    await getConsultationAndCaseInfo()
    await dispatch({
      type: 'tim/getTim'
    }).then((res: any) => {
      if (!!res) {
        setTimObj(res)
        initializationByIm(res)
      } else { message.error("tim初始化失败!") }
    })
    await getChatGroupMsg("")
  }

  // 接收消息使用
  useEffect(() => {
    const { imGroupId } = consultationDataSource || {}
    const { receivedStatus, data } = messageReceived || {};
    let id = `GROUP${imGroupId}`
    if (receivedStatus) {
      if (data[0].conversationID == id) {
        console.log("调用刷新接口")
        refreshPageStatus(data, 2)
        setMessageReceived({
          receivedStatus: false,
          data: []
        })
      }
    }

  }, [messageReceived.receivedStatus])

  useEffect(() => {
    const { imGroupId } = consultationDataSource || {}
    if (imGroupId && timObj) {
      joinGroupByIm(timObj)
    }
  }, [consultationDataSource, timObj])

  useEffect(() => {
    if (Array.isArray(stateImList) && stateImList.length && !isScroll) {
      pageHeightFun(1)
    }
    if (isScroll) {
      pageHeightFun(2)
      setStatePage({
        ...statePage,
        isScroll: false
      })
    }
    val.current = stateImList;
  }, [stateImList])
  /**
   * 获取页面滚动高度
   * @param init  1 第一次进入页面滚动 2 进行滚动分页面
   */
  const pageHeightFun = (init: number) => {
    if (chatListRef.current) {
      if (init == 1) {
        chatListRef.current.scrollTop = chatListRef.current.scrollHeight;
        setPageScrollHeight(chatListRef.current.scrollHeight)
      }
      if (init == 2) {
        chatListRef.current.scrollTop = chatListRef.current.scrollHeight - pageScrollHeight
        setPageScrollHeight(chatListRef.current.scrollTop)
      }
    }

  }

  // 查询指导和病例详情
  const getConsultationAndCaseInfo = () => {
    dispatch({
      type: "consultation/getConsultationAndCaseInfo",
      payload: {
        consultationId,
        type: 2
      }
    }).then((res: any) => {
      if (res != undefined) {
        let { code, content, msg } = res || {}
        let { type , expertsId, processNode, isShowMpQr } = content || {}   // 群组Id
        if (code == 422) {
          Modal.warning({
            content: msg,
            onOk: () => {
              // 在5i5ya的iframe中
              if (isInIframe) {
                const postData = {
                  dataType: 'goBack',       // 页面地址onchange事件
                }
                console.log('子级发送数据：', postData, getArrailUrl())
                window.parent.postMessage(postData, getArrailUrl())
                return
              }

              console.log('Confirmed')
              history.replace('/')
            }
          })
          return
        }
        if (code == 200) {
          // 医生端图文\正畸指导修改状态（第一次进入）
          if ((type == 1 && processNode == 4  && expertsId == userInfo.friUserId) || (type == 3  && processNode == 6 && expertsId == userInfo.friUserId) ) {
            editConsultationNodeAndStatus(2)
          } else {
            setIsShowMpQr(isShowMpQr == 1) // 是否展示聊天公众号二维码，0不展示，1展示
            setConsultationDataSource(content)
          }
        } else {
          message.error(msg)
        }
      }
    })
  }

  // 初次获取聊天信息或者分页时获取聊天信息
  const getChatGroupMsg = (msgSeq: string) => {
    dispatch({
      type: "consultation/getChatGroupMsg",
      payload: {
        msgSeq,
        pageSize: 30,
        consultationId
      }
    }).then((res: any) => {
      if (res != undefined) {
        let { code, content, msg } = res || {}
        let { resultList, lastSeq } = content || {}
        if (code == 200) {
          if (Array.isArray(resultList) && resultList.length) {
            setStateImList({ type: 'receiveHistory', payload: resultList })
            setStatePage({
              ...statePage,
              pageNum: lastSeq
            })
          } else {
            setStatePage({
              ...statePage,
              loadMore: false,
              hasMore: false
            })
          }

        } else {
          message.error(msg)
        }
      }
    })
  }

  /**
 * 修改指导订单节点和状态
 * @param status 1:先体验后付费, 2:图文或视频\正畸病例被查看, 3:图文问题被回复并对话, 4:图文结束指导交易成功, 5:视频预约视频会议, 6:视频沟通, 7:结束指导
 *  9:正畸确认上级医生, 10:正畸审核驳回, 11:正畸审核通过, 12:正畸结束指导)
 * @param auditStatus   是 审核状态(1通过、2驳回)
 * @param auditOpinion  是 输入内容
 */
  const editConsultationNodeAndStatus = (status: number,auditStatus?:number,auditOpinion?:string) => {
    let params = {}
    if(status>9){
      params = {
        type: status,
        consultationId,
        expertsId:userInfo.friUserId,     // [string] 是 上级医生ID
        expertsName:userInfo.name,  // [string] 是 上级医生名称
        auditStatus,  // [string] 是 审核状态(1通过、2驳回)
        auditOpinion, //[string] 是 输入内容
      }
    }else{
      params = {
        type: status,
        consultationId,
      }
    }

    dispatch({
      type: "consultation/editConsultationNodeAndStatus",
      payload: {
        ...params,
      }
    }).then((res: any) => {
      if (res != undefined) {
        let { code, msg } = res || {}
        if (code == 200) {
          if (status == 2 || status == 4 || status == 7 || status == 8 || status == 10 || status == 11 || status == 12 ) {
            getConsultationAndCaseInfo()
          }
        } else {
          message.error(msg)
        }
      }
    })
  }

  // 初始化Im及时通信组件 实例名称定义为"tim"
  const initializationByIm = async (tim: any) => {
    // 监听事件, 关于收到的消息的事件
    tim.off(TIM.EVENT.MESSAGE_RECEIVED, onMessageReceived)
    tim.on(TIM.EVENT.MESSAGE_RECEIVED, onMessageReceived);
    // 监听被踢出事件
    tim.on(TIM.EVENT.KICKED_OUT, onKickedOut);
    // 网络状态发生改变
    tim.on(TIM.EVENT.NET_STATE_CHANGE, (event: any) => {
      /**
       * TIM.TYPES.NET_STATE_CONNECTED    - 已接入网络
       * TIM.TYPES.NET_STATE_CONNECTING   - 连接中。很可能遇到网络抖动，SDK 在重试。接入侧可根据此状态提示“当前网络不稳定”或“连接中”
       * TIM.TYPES.NET_STATE_DISCONNECTED - 未接入网络。接入侧可根据此状态提示“当前网络不可用”。SDK 仍会继续重试，若用户网络恢复，SDK 会自动同步消息
       */
      if (event.data.state == TIM.TYPES.NET_STATE_CONNECTING) {
        let showMessage = useThrottle(() => { message.warning('当前网络不稳定'); }, 500)
        showMessage();

      }
      if (event.data.state == TIM.TYPES.NET_STATE_DISCONNECTED) {
        let showMessage = useThrottle(() => { message.error('当前网络不可用!'); }, 500)
        showMessage();
      }
    });

    // SDK 进入 not ready 状态时触发，此时接入侧将无法使用
    // SDK 发送消息等功能。如果想恢复使用，接入侧需调用 login 接口，驱动 SDK 进入 ready 状态
    tim.on(TIM.EVENT.SDK_NOT_READY, () => {
      // setLoadingByPage(false); // 设置页面加载状态
      /*let promise = tim.login({
        userID: imUserId,
        userSig: userSig,
      });
      promise.then(function (imResponse) {
        if (imResponse.data.repeatLogin === true) {
          setLoadingByPage(false); // 设置页面加载状态
        } // 重复登录
        joinGroupByIm(tim); // 加入当前直播群组
      }).catch(function (imError) {
        setLoadingByPage(false); // 设置页面加载状态
        message.error('TIM登录失败!!');
        console.warn('login error:', imError); // Error information
      });*/
    });
  }
  /**
 * 加入群组
 * @param imGroupId   群组ID
 */
  const joinGroupByIm = async (tim) => {
    // let Groupid = getUrlParam('Groupid')
    const { imGroupId } = consultationDataSource || {}
    console.log(tim, imGroupId)
    let promise = tim.joinGroup({ groupID: imGroupId });
    promise.then(function (imResponse) {
      switch (imResponse.data.status) {
        case TIM.TYPES.JOIN_STATUS_WAIT_APPROVAL:                    // 等待管理员同意
          break;
        case TIM.TYPES.JOIN_STATUS_SUCCESS:                          // 加群成功
          console.log('加群成功 :: ', imResponse.data.group); // 加入的群组资料
          break;
        case TIM.TYPES.JOIN_STATUS_ALREADY_IN_GROUP:                // 已经在群中
          console.log('已经在群 :: ', imResponse.data.group); // 加入的群组资料
          break;
        default:
          break;
      }
    }).catch(function (imError) {
      if (imError.code == 10013) {
        // 用户已经是当前群成员
      } else {
        console.warn('joinGroup error:', imError); // 申请加群失败的相关信息
        message.error(imError.message);
      }
      /*if (imError.code == 10037) {
        message.error(imError.message);
      }*/
    });
  }

  // 监听被踢出事件 - 例如：多端登录被踢
  const onKickedOut = (event: any) => {
    console.log(event)
  }



  // 监听IM获取数据
  const onMessageReceived = (event: { data: any; }) => {
    const messageList = event.data;
    if (messageList.length) {
      if (messageList[0].type == TIM.TYPES.MSG_CUSTOM && messageList[0].payload.data == 'REFRESH_STATUS') {
        getConsultationAndCaseInfo()
      } else {
        setMessageReceived({
          receivedStatus: true,
          data: messageList
        })
        uploadLoading(false)
      }
    }
  }

  // 上传所需Loading
  const uploadLoading = (status: boolean) => {
    dispatch({
      type: "consultation/saveImloading",
      payload: {
        saveImloading: status,
      }
    })
  }
  /**
 * 点击发送更改页面数据、更改状态，刷新
 * @param messageList    当前发送或接收消息
 * @param type           1 发送  2 接收  3 覆盖现有数据
 * @param localType      1 IM发送 其他本地处理最新数据展示
 */
  const refreshPageStatus = (messageList: any[], type: number,localType?:number) => {
    if(localType == 1){
      let arr  = ProcessingImData(messageList)
      for(let i = 0; i < val.current.length; i++) {
        for(let j = 0; j < arr.length; j++) {
            if(val.current[i].randomNumber === arr[j].randomNumber && val.current[i].islocal==1) {
              val.current[i] = arr[j];
            }
        }
    }
      setStateImList({ type: type == 1 ? 'insert' : type == 2 ? 'receive' : type == 3?'replace':null, payload: val.current })
    }else{
      setStateImList({ type: type == 1 ? 'insert' : type == 2 ? 'receive' : null, payload:localType==2?messageList:ProcessingImData(messageList)  })
    }
  }


  // 打开结束指导弹窗
  const onClickEndConsultationShow = () => {
    setIsModalOpen(true)
  }

  /**
   * 关闭结束指导弹窗
   * @param type  1 刷新页面
   */
  const onClickEndConsultationHide = (type: number) => {
    setIsModalOpen(false)
    if (type == 1) {
      let str = consultationType == '1' ? 4 : consultationType == '2' ? 7 : consultationType == '3' ?12: ''
      editConsultationNodeAndStatus(str)
    }
  }

  // 打开方案审核弹窗
  const onClickPlanProceShow = ()=>{
    setIsPlanModalOpen(true)
  }

    /**
   * 关闭方案审核弹窗
   * @param type  1 审核通过 2 审核驳回
   */
    const onClickPlanProceHide = (type?: number,value?:string) => {
      setIsPlanModalOpen(false)
      if(type){
        setPlanTipsModal({
          planTipsVisible:true,
          planInputValue:value || '',
          planType:type,
        })
      }
    }
    /**
   * 关闭方案审核提示弹窗
   * @param type  1 审核通过\审核驳回
   */
    const onClickPlanTipsHide = (type?: number)=>{

      if(type != 1){
        setIsPlanModalOpen(true)
      }else{
        // 10:正畸审核驳回, 11:正畸审核通过,
        let str = planType == 1 ? 11 :10
        editConsultationNodeAndStatus(str,planType,planInputValue)
      }
      setPlanTipsModal({
        ...planTipsModal,
        planTipsVisible:false,
        planInputValue:type==1?'':planInputValue,
        planType:0,
      })

    }

  // 滚动加载分页
  let handleInfiniteOnLoad = () => {
    setStatePage({
      ...statePage,
      loadMore: true,
      isScroll: true
    })
    getChatGroupMsg(pageNum)
  }
  handleInfiniteOnLoad = useThrottle(handleInfiniteOnLoad, 100);

  // 跳转到创建空间
  const goToCreateSpace = () => {
    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'pathname',       // 页面地址onchange事件
        pathnameByChild: '/UserInfo/CreateSpaceByPc/Live',  // 路由信息
        searchByChild: `?consultationId=${consultationId}`,  // 路由信息
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
      return
    }

    history.push(`/UserInfo/CreateSpaceByPc/Live?consultationId=${consultationId}`)
  }
  // 关闭预约会议提示弹窗
  // const onClickWeChatModalHide = () => {
  //   setWeChatModal(false)
  // }

  // 记录用户已关注公众号(点击我已关注)
  const isFocusMp = () => {
    dispatch({
      type: "consultation/isFocusMp",
      payload: {
        wxUserId: userInfo.friUserId,
      }
    }).then((res: any) => {
      if (res != undefined) {
        let { code, msg } = res || {}
        if (code == 200) {
          setIsShowMpQr(false)
        } else {
          message.error(msg)
        }
      }
    })
  }

  // 从诊所跳转过来，隐藏返回按钮
  const refreshAddress = () => {
    if (modalStatus) {
      history.replace(`/ConsultationModule/ConsultationDetails?consultationId=${consultationId}&consultationType=${consultationType}&isGoback=1`)
    }
  }

  /**
   * 获取IM返回上传进度
   * @param obj
   */
  const UploadSchedule = (obj:any)=>{
    dispatch({
      type:'consultation/uploadProgressLoading',
      payload:{
        uploadProgress:[...uploadProgress,obj]
      }
    })
  }

    /**
   * 附件展示
   * @param id
   */
    const annexCaseFun = (id: number) => {
      const {
        consultationCaseInfoDto
      } = consultationDataSource || {}
      const {
        orthodonticCaseDictDtoList
      } = consultationCaseInfoDto || {}

      let arr = []
      if (Array.isArray(orthodonticCaseDictDtoList) && orthodonticCaseDictDtoList.length) {
        const result = orthodonticCaseDictDtoList.find(item => item.id === id);
        const { subsetList } = result || {}
        console.log(subsetList)
        if(Array.isArray(subsetList) && subsetList.length){
          if(Array.isArray(subsetList[0].subsetList) && subsetList[0].subsetList.length){
            arr = subsetList[0].subsetList
          }
        }
      }

      return arr
    }

  const {
    type,
    imGroupId,  // 群组ID
    createDate, // 创建时间
    createUserId,   // 创建人ID
    createUserName, // 创建人
    createUserHeadUrlShow,  // 用户头像
    h5BaseUserDto,   // 专家信息
    consultationCaseInfoDto,  // 病例相关信息
    videoAppointment,   // 最新预约的视频指导会议时间
    videoRecordList,    // 历史视频指导-会议记录
    thisUserIsExperts,  // 当前人是否是专家：0:否，1:是
    attachmentCount,    // 附件数量
    orderNumber,  // 指导订单
    isFinish,     //当前节点是否完成(1是、0否)
    status,        // 支付状态(0已取消、1未支付(待评估)、2去支付(已评估)、3已支付)
    amount,        // 账单金额
    defaultAssistantUrl,  // 二维码地址
    processNode,
    auditRecordList,  // 审核记录
    tenantId,          // 租户ID
    orderCaseTemplate, // 1通用模板，2正畸模板
  } = consultationDataSource || {}

  const load = !!loading.effects['consultation/getConsultationAndCaseInfo'] ||  // 指导详情
    !!loading.effects['consultation/getChatGroupMsg'] ||            // 聊天记录
    !!loading.effects['consultation/editConsultationNodeAndStatus'] ||  // 修改指导订单节点和状态
    !!loading.effects['consultation/getConsultationProcessNode']    // 查询指导流程节点
  return (
    <Spin spinning={load}>
      <div className={styles.container}>
        {/* iframe中隐藏header */}
        {
          isInIframe ? null : <PcHeader />
        }

        <div className={styles.wrap}>
          <div className={styles.wrap_header}>
            <Header isGoback={isGoback} consultationType={consultationType} headerStatus={status} processNode={processNode} isFinish={isFinish} />
          </div>
          <div className={classNames(styles.content, {
            [styles.content_in_iframe]: isInIframe,
          })}>

            {
              thisUserIsExperts != 1 && consultationType!='3' ? <div className={styles.progress_bar}>
                <ConsultationSteps type={consultationType == '1' ? 2 : 4} processNode={processNode ? processNode : 0} isFinish={isFinish} width={consultationType == '1' ? "656px" : "100%"} />
              </div> : null
            }
            <div className={styles.content_detail}>
              <div className={styles.content_left_wrap}>
                {/* 正畸病例审核记录 */}
                {
                  thisUserIsExperts != 1 && Array.isArray(auditRecordList) && auditRecordList.length ? <AuditRecordsCard data={auditRecordList}/>:null
                }

                <div className={styles.content_left}>
                  {/* 病例卡片 */}
                  {
                    consultationType != '3'?<ConsultationCaseCard isDoctor={thisUserIsExperts} isShowBtn={consultationType == '1' ? !(processNode == 6 && isFinish == 1) : processNode < 7} caseData={consultationCaseInfoDto} consultationId={consultationId} pageFrom={'ConsultationDetails'} orderCaseTemplate={orderCaseTemplate} tenantId={tenantId} />:
                    <OrthodonticCaseCard isDoctor={thisUserIsExperts} isShowBtn={processNode < 9} caseData={consultationCaseInfoDto} tenantId={tenantId} />
                  }
                  {/* 其他资料 */}
                  <OtherInformation
                    fileData={
                      consultationType == '3' ? annexCaseFun(9)
                      : orderCaseTemplate == 2 ? annexCaseFun(9)
                        : consultationCaseInfoDto && consultationCaseInfoDto.consultationCaseMediaDtoList
                    }
                    consultationType={consultationType}
                     />
                </div>
              </div>

              <div className={consultationType == "1" || consultationType == '3'? styles.graphic_content_right : styles.video_content_right}>
                {/* 专家或者用户信息展示 */}
                <div className={consultationType == "2" ? styles.user_info : {}}>
                  <UserInfoCard isOrderShow={true} data={{ h5BaseUserDto, orderNumber, createDate, createUserName, createUserId, createUserHeadUrlShow, thisUserIsExperts,consultationType }} />
                </div>
                {/* 指导头部信息卡片（改约）（视频指导展示） */}
                {
                  videoAppointment && consultationType == "2" && processNode && processNode >= 5 && <div className={styles.consultation_order}>
                    <AppointmentTimeCard
                      data={{ videoAppointment, thisUserIsExperts, defaultAssistantUrl, status }}
                      consultationId={consultationId}
                      consultationType={consultationType}
                    />
                  </div>
                }
                {/* 指导订单信息卡片（视频指导展示） */}
                {
                  consultationType == "2" && thisUserIsExperts != 1 && processNode == 9 && (status == 2 || status == 3) && <div className={styles.consultation_order}>
                    <ConsultationOrderCard isPc={true} data={{ orderNumber, status, amount, consultationId }} goPay={setModelByVisible} />
                  </div>
                }

                {/* 指导会议记录卡片（视频指导展示） */}
                {
                  consultationType == "2" && Array.isArray(videoRecordList) && videoRecordList.length ?
                    <div className={styles.consultation_order}>
                      <div style={{ marginTop: 8 }}>
                        <ConferenceRecordCard data={videoRecordList} />
                      </div>
                    </div>
                    : null
                }

                {
                  consultationType == "2" && <>
                    {/* 用户提问 */}
                    <div className={styles.user_questions}>
                      <UserQuestions questionData={consultationCaseInfoDto && consultationCaseInfoDto.firstQuestion} />
                    </div>
                    {
                      isShowMpQr && <div className={styles.video_content_tips}>
                        <FollowOfficialAccountTips isPc={true} onClickIsFocusMp={isFocusMp} />
                      </div>
                    }
                    {
                      Array.isArray(stateImList) && stateImList.length ? <IMInlet dataSource={stateImList} consultationType={consultationType || ''}/> : null
                    }
                  </>
                }

                {/* PC端聊天组件 */}
                {
                  consultationType == '1' || consultationType == '3'? <div
                    className={styles.content_im}
                    id='content'
                    ref={chatListRef}
                  >
                    <InfiniteScroll
                      id="qwer"
                      initialLoad={false}
                      pageStart={0}
                      isReverse
                      loadMore={handleInfiniteOnLoad}
                      hasMore={!loadMore && hasMore}
                      useWindow={false}
                      threshold={50}
                    >
                      {
                        Array.isArray(stateImList) && stateImList.length ? <IMInlet dataSource={stateImList} consultationType={consultationType || ''}/> : null
                      }
                      {
                        isShowMpQr && <div className={styles.graphic_content_tips}>
                          <FollowOfficialAccountTips isPc={true} onClickIsFocusMp={isFocusMp} />
                        </div>
                      }

                    </InfiniteScroll>
                  </div>:null
                }

                {/* PC聊天底部输入框组件 */}
                {
                  consultationType == '1' || consultationType == '3' ? <div className={styles.chatImageBottom}>
                    {
                      (processNode == 6 && isFinish == 1) || (consultationType == '3' && processNode==10) ? null : <ChatInputBottomBox
                        uploadLoading={uploadLoading}
                        imGroupId={imGroupId}
                        timObj={timObj}
                        refreshPage={refreshPageStatus}
                        isDoctor={thisUserIsExperts}
                        stateImList = {val.current}
                        UploadSchedule={UploadSchedule}
                      />
                    }
                    {/* 图文结束指导 */}
                    {consultationType == '1' && thisUserIsExperts == 1 && processNode == 6 && isFinish == 0 ? <p className={styles.end_consultation} onClick={() => { onClickEndConsultationShow() }}>结束指导</p> : null}
                    {/* 正畸 结束指导 */}
                    {consultationType == '3' && thisUserIsExperts == 1 && processNode == 9? <p className={styles.end_consultation} onClick={() => { onClickEndConsultationShow() }}>结束指导</p> : null}
                    {/* 方案审核 */}
                    {
                      thisUserIsExperts == 1 && consultationType=='3' && processNode < 9 ? <p className={styles.plan_process} onClick={() => { onClickPlanProceShow() }}>方案审核</p>:null
                    }
                  </div>:null
                }

              </div>

            </div>
            {/* 视频指导展示 */}
            {
              consultationType == '2' && status != 0 && <>
                {/* 结束指导 */}
                {
                  thisUserIsExperts != 1 && processNode == 9 && isFinish == 0 && status == 2 ? <p className={styles.pay} onClick={() => { setModelByVisible(consultationId) }}>支付指导费用</p> : null
                }
                {
                  thisUserIsExperts == 1 ? <div className={styles.doctor_footer} style={processNode == 7 && videoAppointment ? { right: '30%' } : { right: '22%' }}>
                    {
                      processNode >= 5 && processNode <= 7 && !videoAppointment && <span className={styles.reserved_conference} onClick={goToCreateSpace}>预约会议</span>
                    }
                    {
                      processNode == 7 ? <span className={styles.end_footer_consultation} onClick={() => { onClickEndConsultationShow() }}>结束指导</span> : null
                    }
                  </div> : null
                }
                {
                  thisUserIsExperts == 1 && processNode >= 5 && processNode <= 6 ? <div className={styles.cancel_consultation} onClick={() => { editConsultationNodeAndStatus(8) }}>取消指导</div> : null
                }
              </>
            }

          </div>
        </div>
        {
          <EndConsultationTipsModal visible={isModalOpen} onCancel={onClickEndConsultationHide} />
        }
        {/* 订单详情 */}
        {
          <ModalByConsultationDetails
            visibleAndId={modelByVisible}                         // 指导id,传入id,打开弹窗 销毁id关闭弹窗
            onCancel={() => {
              setModelByVisible(null)
              getConsultationAndCaseInfo()
              refreshAddress()
            }} // 点击关闭弹窗
          />
        }
        {/* 预约会议二维码弹窗 */}
        {/*
          weChatModal && <CustomerServiceWeChatModal visible={weChatModal} type={1} consultationId={consultationId} consultationType={consultationType} onCancel={onClickWeChatModalHide} />
        */}
        {/* 方案审核弹窗 */}
        {
          isPlanModalOpen && <SchemeReviewModal dataSource={consultationCaseInfoDto} visible={isPlanModalOpen} onCancel={onClickPlanProceHide} planInputValue={planInputValue}/>
        }
        {/* 方案审核提示弹窗 */}
        {
          planTipsVisible && <PlanPromptModal visible={planTipsVisible} auditStatus={planType} value={planInputValue} onCancel={onClickPlanTipsHide}/>
        }
      </div>
    </Spin>

  )
}
export default connect(({ consultation, loading }: any) => ({ consultation, loading }))(Index)
