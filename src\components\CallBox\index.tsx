/**
 * @Description: 打call组件
 */
import React, { useState } from 'react'
import { connect } from 'umi'
import styles from './index.less'
import call1 from '@/assets/GlobalImg/call1.png'                       // call组件
import call2 from '@/assets/GlobalImg/call2.png'                       // call组件
import call3 from '@/assets/GlobalImg/call3.png'                       // call组件
import call4 from '@/assets/GlobalImg/call4.png'                       // call组件


const paths = {
  callImgPath1: "M23.5,179.5 C49.9140625,157.590342 63.1210938,136.5018 63.1210938,116.234375 C63.1210938,85.8332369 11.3392468,77.5446796 3.50390625,55.0039063 C-1.08378194,41.8060064 -1.69726679,23.9593232 23.5,6.1953125 C25.8282956,4.55386987 33.2462643,2.83642196 45.7539062,1.04296875",
  callImgPath2:"M41.6547012,179.5 C11.3107531,155.345632 -1.89627817,131.81959 2.03360741,108.921875 C4.83002298,92.6283913 21.8796964,86.8845421 41.6547012,74.3984375 C46.9982976,71.0244456 54.4870952,63.3942373 64.1210937,51.5078125 C65.6895909,38.4940528 63.2625075,27.6151465 56.8398437,18.8710938 C50.41718,10.127041 37.937497,4.18433263 19.4007949,1.04296875"
};

let callCount = 0, timer = null, callImgList = [call1, call2, call3, call4];

interface PropsType {
  BoxStyle? :any;     // Box样式
  TextStyle? :any;      // 文字样式
  BtnStyle? :any;      // 按钮自定义样式
  onClickCallBtn? :any; // 点击按钮回调
}

const Index: React.FC = (props: PropsType) => {
  gsap.registerPlugin(MotionPathPlugin); // 注册插件
  const { BoxStyle,TextStyle, BtnStyle } = props;
  let [showCount, setShowCount] = useState(0);       // 打call数量

  const onCallFn = (e) => {
    // 点击后阻止事件冒泡,防止触发父元素的点击事件
    e.stopPropagation();
    props.onClickCallBtn&&props.onClickCallBtn()

    callCount = ++callCount;
    setShowCount(callCount);
    clearTimeout(timer);
    timer = setTimeout(() => {
      callCount = 0;
      setShowCount(0)
    }, 3 * 1000); // 3秒
    const container = document.getElementById('container');
    const image = document.createElement('img');
    image.src = callImgList[Math.floor(callCount % 4)];
    const path = Math.floor(callCount / 5) % 2 === 0 ? paths.callImgPath1 : paths.callImgPath2;
    container.appendChild(image);
    gsap.to(image, {
      duration: 1.5,
      motionPath: {
        path: path,
        align: "self",
        autoRotate: true,
        alignOrigin: [0.5, 0.5]
      },
      ease: 'linear',
      onComplete: () => {
        image.remove();
      }
    });
    image.addEventListener('animationend', () => {
      image.remove();
    });
  }
  return (
    <>
      <div className={styles.svgBox} id={'container'} style={BoxStyle?{...BoxStyle}:{}}>
        {
          showCount > 0 &&
          <span style={TextStyle?{...TextStyle}:{}}>X{`${showCount >= 99 ? '99' : showCount}`}</span>
        }
        <div className={styles.svgBtn} style={BtnStyle?{...BtnStyle}:{}} onClick={(e) =>onCallFn(e) }></div>
      </div>
    </>
  )
}

export default connect(({activity, loading}: any) => ({activity, loading}))(Index)
