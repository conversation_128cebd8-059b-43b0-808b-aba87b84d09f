import NoDataRender from '@/components/NoDataRender';
import { useEffect, useRef, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroller';
import { connect } from 'umi';
import styles from './index.less';
import { throttle } from 'lodash';
import CaseCard from '@/componentsByPc/CaseCard';

const initStatePage = {
  pageNum: 1,
  hasMore: true,  // 加载更多
  loadMore: false,
}
const initState = {
  total: 0,
  listDate: [],
}

const PcMyCollectCase: React.FC<any> = (props) => {
  const { dispatch } = props;
  const scrollParentRef = useRef<HTMLDivElement | null>(null);
  const [state, setState] = useState(initState)             // 列表数据
  const [statePage, setStatePage] = useState(initStatePage)  // 当前分页
  const { total, listDate } = state
  const { pageNum, hasMore, loadMore } = statePage || {}
  
  useEffect(() => {
    getInitData(1);
  }, []);

  const getInitData = (page:any) => {
    dispatch({
      type: 'userInfoStore/getCollectList',
      payload: {
        pageNum: page,
        pageSize: 30,
      }
    }).then((res: any) => {
      const { code, content } = res || {};
      const { pageNum, total, resultList } = content || {};
      if (res && code == 200) {
        let data = pageNum == 1 ? [] : listDate;
        data = data.concat(resultList || []);
        const hasMore = data.length !== total;

        if (Array.isArray(data) && data.length == 0) {
          setState({
            ...state,
            listDate: [],
            total: 0,
          })
          return
        }
        setState({
          ...state,
          listDate: [...data],
          total,
        })
        setStatePage({
          ...statePage,
          loadMore: false,
          hasMore,
          pageNum,
        })
      }
    }).catch((err) => {
      console.log(err)
    });
  }

  // 滚动加载分页
  let handleInfiniteOnLoad = () => {
    if (listDate.length > total - 1) {
      setStatePage({
        ...statePage,
        loadMore: false,
        hasMore: false
      })
      return;
    }
    const pages = pageNum + 1;
    setStatePage({
      ...statePage,
      loadMore: true,
    })
    getInitData(pages)
  }

  handleInfiniteOnLoad = throttle(handleInfiniteOnLoad, 100);

  return (
    <div className={styles.tab_content_list} ref={(ref) => (scrollParentRef.current = ref)}>
      <InfiniteScroll
        loadMore={handleInfiniteOnLoad}
        threshold={50}
        pageStart={1}
        initialLoad={false}
        hasMore={!loadMore && hasMore}
        useWindow={false}
        getScrollParent={() => scrollParentRef.current}
        className={styles.scroll_box}
      >
        <div className={styles.space_wrap}>
          {listDate && listDate.length ? (
            listDate.map((item: any, ind) => (
              <div className={styles.tab_case_list} key={ind}>
                <CaseCard key={ind} caseData={item} />
              </div>
            ))
          ) : (
            <div className={styles.no_data_wrap_box}><NoDataRender className={styles.noDataStyle} text="暂无收藏病例"/></div>
          )}
        </div>
      </InfiniteScroll>
    </div>
  );
};

export default connect(({ cases,userInfoStore, loading }: any) => ({ cases,userInfoStore, loading }))(
  PcMyCollectCase,
);
