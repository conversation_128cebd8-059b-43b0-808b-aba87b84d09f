/**
 * @Description: 移动端聊天页面入口
 * @author: 赵斐
 */
import React, { useState, useEffect } from 'react';
import { ImageViewer } from 'antd-mobile'

import styles from './index.less'
// 我的聊天信息
import MyTemplate from '../MyTemplate'
// 他人聊天信息
import OtherTemplates from '../OtherTemplates'
// 系统消息模板展示
import SystemMessageTemplate from '../SystemMessageTemplate'
// 播放视频弹窗
import PlayVideoModal from '@/pages/ConsultationModule/H5IMComponents/PlayVideoModal'

const initAudio = {
  audioId: 0,
  audioRef: null,
}
const initStateVideo = {
  id:0,
  url:""
}
const initStateImage = {
  imgUrl:'',
  imgShow:false
}
interface PropsType {
  dataSource: any,   // 聊天数据
  consultationType: string | string[],  // 指导类型
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { dataSource ,consultationType } = props

  const [playerModalOpen, setPlayerModalOpen] = useState(false)  // 打开视频弹窗
  const [stateVideo,setStateVideo] = useState(initStateVideo)   // 视频地址
  const [playAudioObj, setPlayAudioObj] = useState(initAudio);  // 语音播放（当前播放ID，Dom）

  const [imageVisible,setImageVisible] = useState(initStateImage)  // 查看图片state
  const userInfoStr = localStorage.getItem('userInfo')
  const userInfo = userInfoStr ? JSON.parse(userInfoStr) : {}

  let { friUserId } = userInfo || {}   // 用户Id

  const {
    audioRef,   // 语音播放载体dom对象
  } = playAudioObj || {}
  // 打开视频播放弹窗
  const onClickOpenVideoFun = (url:string,id:number) => {
    setPlayerModalOpen(true)
    setStateVideo({
      url,
      id
    })
  }
  // 关闭视频播放弹窗
  const onClickCloseVideoFun = () => {
    setPlayerModalOpen(false)
    setStateVideo(initStateVideo)
  }

  useEffect(() => {
    if (audioRef?.current) {
      audioRef.current.addEventListener('pause', pauseFunction);
      audioRef.current.addEventListener('ended', endFunction);
      audioRef.current.addEventListener('canplay', () => {
        // setCurrentTime(parseInt(`${audioRef.current.duration}`, 10));
      });
    }
    return () => {
      if (audioRef?.current) {
        audioRef.current.removeEventListener('pause', pauseFunction);
        audioRef.current.removeEventListener('ended', endFunction);
      }
      setImageVisible(initStateImage)
    };
  }, [audioRef]);
  /**
   * 点击播放语音
   * @param id  播放哪一条
   */
  const playAudioFun = async (id: number, val: any) => {
    if (val?.current) {
      console.log(val.current.paused)
      if (!val.current.paused) {
        await val.current.pause();
        await endFunction();
      } else {
        await val.current.play();
        await setPlayAudioObj({
          audioId: id,
          audioRef: val
        });
        // setAudioSource(audioRef.current);
      }
    }
  }

  // 暂停语音
  const pauseFunction = () => {
    setPlayAudioObj(initAudio);
  };

  // 语音结束
  const endFunction = async () => {
   audioRef.current.currentTime= 0
   await pauseFunction();
  };

  /**
   * 查看大图
   * @param imgUrl    图片路径
   * @param imgShow   弹窗状态
   */
  const onClickAmplifyImg = (imgUrl:string,imgShow:boolean)=>{
    setImageVisible({
      imgUrl,
      imgShow
    })
  }

  return (
    <div className={styles.im_inlet_container} id='modal_content'>
      {
        dataSource.map((item: any, index: number) => {
          return (
            <div key={index}>
              {
                item.wxUserId == friUserId && consultationType != '2'? <MyTemplate onClickAmplifyImg={onClickAmplifyImg} playAudioFun={playAudioFun} playAudioObj={playAudioObj} data={item} onClickOpenVideoFun={onClickOpenVideoFun} /> : null
              }
              {
                item.wxUserId != friUserId && item.msgType != 5 && consultationType != '2'? <OtherTemplates onClickAmplifyImg={onClickAmplifyImg} data={item}  playAudioFun={playAudioFun} playAudioObj={playAudioObj} onClickOpenVideoFun={onClickOpenVideoFun} /> : null
              }
              {
                !item.wxUserId && item.msgType == 5 ? <SystemMessageTemplate data={item.msgContent} /> : null
              }
            </div>
          )
        })
      }

      <ImageViewer
        image={imageVisible.imgUrl}
        visible={imageVisible.imgShow}
        onClose={() => {
          setImageVisible(initStateImage)
        }}
      />
      {
        // 播放视频弹窗
        playerModalOpen && <PlayVideoModal visible={playerModalOpen} dataSource={stateVideo}  onCancel={onClickCloseVideoFun} />
      }
    </div>
  )
}
export default Index
