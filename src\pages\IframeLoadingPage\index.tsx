/**
 * @Description: 5i5ya嵌套的iframe中转页
 */
import React, { useEffect } from 'react'
import { history, connect } from 'umi'
import moment from 'moment'
import { getArrailUrl } from '@/utils/utils'
import { Spin, message } from 'antd'
import styles from './index.less'

// 登录相关
import { getMsgCodeUserInfo, arrailToUcLogin } from '@/services/login/login'

const Index: React.FC = (props: any) => {
  const { dispatch } = props

  useEffect(()=>{
    window.addEventListener('message', onReceiveMessage)
    return () => {
      window.removeEventListener('message', onReceiveMessage)
    }
  }, [])

  // 接收到了postMessage的数据
  const onReceiveMessage = (e) => {
    console.log('子级接收到了数据：', e)
    const data = e && e.data || {}
    const { iframeWhereFrom, checkType, userKey, iframeChildSrc, tenantId, sentDataObj, pageType } = data || {}
    localStorage.setItem('iframeWhereFrom', iframeWhereFrom)
    localStorage.setItem('tenantId', tenantId)

    // 保存传过来的数据
    if (pageType == 'CaseResult') {
      dispatch({
        type: 'cases/save',
        payload: {
          ...sentDataObj,
        }
      })
    } else if (pageType == 'MyConsultation') {
      // 处理时间
      const valueByRangePickerFormat = sentDataObj.valueByRangePicker
      const valueByRangePickerMoment = []
      if (valueByRangePickerFormat && valueByRangePickerFormat[0]) {
        valueByRangePickerMoment.push(moment(valueByRangePickerFormat[0]))
      }
      if (valueByRangePickerFormat && valueByRangePickerFormat[1]) {
        valueByRangePickerMoment.push(moment(valueByRangePickerFormat[1]))
      }
      dispatch({
        type: 'pcAccount/saveByPcMyConsultationList',
        payload: {
          ...sentDataObj,
          valueByRangePicker: valueByRangePickerMoment,
        }
      })
    }
    // 判断是否需要自动登录
    if (checkType == 1) {
      if (userKey) {
        autoLogin(userKey, iframeChildSrc)
      } else {
        history.replace(iframeChildSrc)
      }
    } else {
      history.replace(iframeChildSrc)
    }
  }

  // 自动登录
  const autoLogin = async (userKey, iframeChildSrc) => {
    localStorage.removeItem('wxuserId')
    // 登录请求
    const res = await arrailToUcLogin({arrailToUcKey: userKey})
    const { code, content, msg } = res || {}
    // 登录成功
    if (code === 200) {
      // 保存token
      localStorage.setItem('access_token', content.access_token)
      // 获取用户信息接口时，header头里面需要传userName，值为手机号
      localStorage.setItem('userInfo', JSON.stringify({
        phone: content.username
      }))
      await getMsgCodeUserInfo_func(content.username, iframeChildSrc)
    } else {
      message.error(msg || '自动登录失败')
    }
  }

  const getMsgCodeUserInfo_func = async (phone, iframeChildSrc) => {
    try {
      const token_text = localStorage.getItem('access_token')
      const res = await getMsgCodeUserInfo({ token: token_text, username: phone })
      const { code, content } = res
      if (code === 200) {
        // 保存用户信息
        localStorage.setItem('userInfo', JSON.stringify({
          ...content,
          id: content.friUserId
        }))

        // 发送登录成功的消息
        const postData = {
          dataType: 'login',       // 登录成功事件
        }
        console.log('子级发送数据：', postData, getArrailUrl())
        window.parent.postMessage(postData, getArrailUrl())
        history.replace(iframeChildSrc)
      }
    } catch (error) {
      console.error('获取用户信息失败：', error)
    }
  }

  return (
    <Spin spinning={true} wrapperClassName={styles.spin}>
      <div style={{height: '100%'}}></div>
    </Spin>
  )
}

export default connect(({loading }: any) => ({ loading }))(Index)
