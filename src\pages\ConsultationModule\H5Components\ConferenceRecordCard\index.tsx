/**
 * @Description: 移动端会议记录卡片
 * @author: 赵斐
 */
import React from 'react';
import styles from './index.less'

interface PropsType {
  data:any,          // 会议记录数据
}
const Index: React.FC<PropsType> = (props: any) => {
  const { data } = props;
  return (
    <div className={styles.wrap}>
      <div className={styles.header}>
        <span className={styles.header_title}>会议记录</span>
      </div>
      <div className={styles.content}>
        {
          data.map((item:any, index:number) => {
            return (
              <div className={styles.content_detail} key={index}>
                <p>{item.dateStr}{item.weekStr?',':null}{item.weekStr}{item.timeStr?',':null}{item.timeStr}</p>
                <p>会议已结束</p>
              </div>
            )
          })
        }
      </div>
    </div>
  )
}
export default Index
