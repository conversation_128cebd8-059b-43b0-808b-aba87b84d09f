/**
 * @Description: 设置
 */
import React, { useState, useEffect } from 'react';
import { connect, history } from 'umi';
import classNames from 'classnames'
import { getOperatingEnv, backInApp, getIsFirstPageInApp, goToHomePage } from '@/utils/utils'
import { Modal, Toast } from 'antd-mobile'
import { message, Spin } from 'antd';
import styles from './index.less';

// 图片icon
import account_edit from '@/assets/UserInfo/account_edit.png'
import pc_goback from '@/assets/GlobalImg/pc_goback.png'
import certified_icon from '@/assets/GlobalImg/certified_icon.png'
import e_arrow from '@/assets/GlobalImg/e_arrow.png'
import not_certified_icon from '@/assets/GlobalImg/not_certified_icon.png'
import right_arrow from '@/assets/GlobalImg/right_arrow.png'
import privacy_icon from '@/assets/GlobalImg/privacy_icon.png'
import logoff_icon from '@/assets/GlobalImg/logoff_icon.png'
import password_icon from '@/assets/GlobalImg/password_icon.png'
import right_arrow_3 from '@/assets/GlobalImg/right_arrow_3.png'

import Avatar from '@/components/Avatar'; // 头像组件
import CommonTipsModal from '@/pages/PlanetChatRoom/Meet/components/ModalComponents/CommonTipsModal' // 公共提示弹窗

import { checkExistPassword } from '@/services/login/login';

const Index: React.FC = (props: any) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  const { loading, dispatch } = props;

  const [logoutModalVisible, setLogoutModalVisible] = useState(false); // 退出登录弹窗
  const [existPassword, setExistPassword] = useState(null); // 判断是否存在密码
  const [userInfoState, setUserInfoState] = useState({}) // 用户信息

  useEffect(() => {
    // 检查是否设置过密码
    checkPassword()
    // 获取用户信息
    getUserInfo()
  }, [])

  useEffect(() => {
    if (getOperatingEnv() === '4' ) {
      // 设置pc tab页为设置
      dispatch({
        type: 'pcAccount/save',
        payload: {
          tabState: 5,
          subTabState: null,
        }
      })
      history.replace(`/UserInfo?tabKey=5`)
    }
  }, [dispatch]);

  // 检查是否设置过密码
  const checkPassword = async () => {
    const res = await checkExistPassword();
    const { code, content, msg } = res || {}
    if (code == 200) {
      setExistPassword(content)
    } else {
      message.error(msg || '无法确认当前账户是否设置过密码，请稍后重试!')
    }
  }

  // 获取用户信息
  const getUserInfo = () => {
    dispatch({
      type: 'userInfoStore/getUserInfo',
      payload: {
        wxUserId: UserInfo && UserInfo.friUserId || '',
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200 && content) {
        setUserInfoState(content)
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch(err => {})
  }

  // 点击编辑账户
  const onClickEditAccountIcon = () => {
    // 未登录->前往登录页面
    if (!localStorage.getItem('access_token')) {
      history.push(`/User/login`)
    } else {
      // 去编辑信息页
      history.push('/userInfo/editUserInfo')
    }
  }

  // 点击已认证/未认证
  const onClickCertifiedBtn = () => {
    if (userInfoState.isHasSelfAuth == 1) {
      history.push('/UserInfo/Certification/InformationDetails')
    } else {
      history.push('/UserInfo/Certification/SelectRoles')
    }
  }

  // 点击隐私政策
  const onClickPrivacyPolicyBtn = () => {
    history.push('/UserInfo/Agreement')
  }

  // 点击账号注销
  const onClickLogOffBtn = () => {
    history.push('/userInfo/logout')
  }

  // 点击设置/修改密码
  const onClickSetPasswordBtn = () => {
    history.push(`/User/forgetPassword?from=Setting&exist=${existPassword}`)
  }

  // 点击退出登录
  const onClickLogoutBtn = () => {
    setLogoutModalVisible(true)
  }

  // 退出登录二次弹框取消按钮
  const onClickLeftBtn = () => {
    setLogoutModalVisible(false)
  }

  // 退出登录二次弹框确定按钮
  const onClickRightBtn = () => {
    setLogoutModalVisible(false)

    dispatch({
      type: 'userInfoStore/exitLogout',
      payload: {}
    }).then(res => {
      const { code, msg } = res
      if (code == 200) {
        localStorage.clear(); // 清空本地存储
        history.replace('/Square')
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch(err => {
      console.log(err)
    })
  }

  // 点击返回按钮
  const onClickBackIcon = () => {
    if (history.length > 2) {
      history.goBack()
    } else {
      history.replace('/')
    }
  }

  // loading
  const loadingGetUserInfo = !!loading.effects['userInfoStore/getUserInfo']

  return (
    <Spin wrapperClassName={styles.spin} spinning={loadingGetUserInfo}>
      {/* 导航栏 */}
      <div className={styles.header_wrap}>
        <div className={styles.header_left}>
          <img src={pc_goback} width={20} height={20} alt="" onClick={onClickBackIcon}/>
          <span>我的</span>
        </div>
        {/* 编辑账户icon */}
        <div onClick={onClickEditAccountIcon}>
          <img src={account_edit} width={34} height={34} alt=""/>
        </div>
      </div>
      {/* 内容 */}
      <div className={styles.container}>
        {/* 灰色背景层div */}
        <div className={styles.bg_wrap}>
          {/* 上半部分 */}
          <div className={styles.top_wrap}>
            <div className={styles.avatar_wrap}>
              <div className={styles.avatar}>
                <Avatar
                  size={72}
                  userInfo={{
                    name: userInfoState.name,
                    userId: userInfoState.id,
                    headUrlShow: userInfoState.headUrlShow,
                  }}
                  style={{marginRight: 0}}
                />
              </div>
            </div>
            <div className={styles.name_wrap}>{userInfoState.name}</div>
            <div className={styles.nickname_wrap}>昵称：{userInfoState.nickName || '-'}</div>
            <div className={styles.certified_wrap}>
              <div className={classNames(styles.certified, {
                [styles.not_certified]: userInfoState.isHasSelfAuth == 0,
              })} onClick={onClickCertifiedBtn}>
                <img src={userInfoState.isHasSelfAuth == 1 ? certified_icon : not_certified_icon} width={16} height={16} alt=""/>
                <span>{userInfoState.isHasSelfAuth == 1 ? '已认证' : '未认证'}</span>
                <img src={userInfoState.isHasSelfAuth == 1 ? e_arrow : right_arrow} width={8} height={8} alt=""/>
              </div>
            </div>
          </div>
          {/* 下半部分 */}
          <div className={styles.bottom_wrap}>
            <div className={styles.bottom_title}>设置</div>
            <div className={styles.setting_item_wrap} onClick={onClickPrivacyPolicyBtn}>
              <img src={privacy_icon} width={20} height={20} alt=""/>
              <div className={styles.setting_item_text}>隐私政策</div>
              <img src={right_arrow_3} width={16} height={16} alt=""/>
            </div>
            <div className={styles.setting_item_wrap} onClick={onClickLogOffBtn}>
              <img src={logoff_icon} width={20} height={20} alt=""/>
              <div className={styles.setting_item_text}>账户注销</div>
              <img src={right_arrow_3} width={16} height={16} alt=""/>
            </div>
            <div className={styles.setting_item_wrap} onClick={onClickSetPasswordBtn}>
              <img src={password_icon} width={20} height={20} alt=""/>
              <div className={styles.setting_item_text}>{existPassword ? '修改登录密码' : '设置登录密码'}</div>
              <img src={right_arrow_3} width={16} height={16} alt=""/>
            </div>
          </div>
          {/* 退出登录 */}
          <div className={styles.btn_wrap}>
            <div className={styles.btn} onClick={onClickLogoutBtn}>退出登录</div>
          </div>
        </div>
      </div>

      {/* 退出登录提示弹窗 */}
      <CommonTipsModal
        visible={logoutModalVisible}
        title={'确定退出登录?'}
        leftBtnText={'取消'}
        rightBtnText={'确定'}
        onClickLeftBtn={onClickLeftBtn}
        onClickRightBtn={onClickRightBtn}
      />
    </Spin>
  )
}

export default connect(({ userInfoStore, loading }: any) => ({ userInfoStore, loading}))(Index)
