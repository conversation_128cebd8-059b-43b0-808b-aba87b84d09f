.space_wrap {
  width: 100%;
  height: auto;
  background: #fff;
  padding: 16px;
  margin-bottom: 10px;
}

// 空间帖子文案
.space_tips_text {
  font-size: 14px;
  color: #000;
  line-height: 20px;
  margin-bottom: 8px;
}

// 空间封面
.cover_img {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  height: 121px;
  // 封面中的标题
  .title_in_cover_image {
    position: absolute;
    z-index: 600;
    width: 56%;
    top: 18px;
    left: 0;
    padding-left: 12px;
    font-size: 16px;
    color: #fff;
    font-weight: 500;
    word-break: break-all;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /* 指定显示行数 */
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .status_box {
    position: absolute;
    top: 12px;
    right: 8px;
    background: linear-gradient(135deg, #4183EA 0%, #003AAF 100%);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: nowrap;
    font-size: 12px;
    color: #FFF;
    padding: 0 8px;
    height: 21px;
    white-space: nowrap;
    z-index: 666;
  }
  .gdp {
    position: absolute;
    bottom: 0;
    right: 0;
    color: #fff;
    font-size: 11px;
    line-height: 15px;
    z-index: 666;
    background: rgba(0,0,0,0.4);
    border-radius: 8px 0 0 0;
    padding: 2px 6px;
  }
  .danmu_box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 667;
  }
}

// 王国
.kingdom_wrap {
  display: inline-flex;
  align-items: center;
  column-gap: 4px;
  height: 19px;
  padding: 0 4px;
  border-radius: 4px;
  background: #E4F0FC;
  font-size: 11px;
  color: #0095FF;
  margin-top: 8px;
}
