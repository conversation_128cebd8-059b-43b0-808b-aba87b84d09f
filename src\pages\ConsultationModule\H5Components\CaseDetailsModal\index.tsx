/**
 * @Description: 移动端，病例详情弹窗，非模板
 */
import React, { useEffect } from 'react'
import classNames from 'classnames'
import { Popup, ImageViewer } from 'antd-mobile'
import { CloseOutlined } from '@ant-design/icons'
import styles from './index.less'

interface PropsType {
  visible: boolean,                    // true，false
  caseData: any,                       // 病例数据
  onCancel: any,                       // 关闭弹窗
}

const Index: React.FC<PropsType> = (props: any) => {
  const { visible, caseData = {} } = props

  // 查看大图
  const previewBigImage = (index) => {
    ImageViewer.Multi.show({
      defaultIndex: index,
      images: caseData.consultationCaseMediaDtoList.filter(item => item.type == 0).map(item => item.fileUrlShow),
      getContainer: () => document.getElementById('container'),
    })
  }

  // 组件销毁时，关闭查看大图弹窗
  useEffect(() => {
    return () => {
      ImageViewer.clear()
    }
  },[])

  return (
    <Popup
      visible={visible}
      onMaskClick={props.onCancel}
      className={styles.popup_container}
      bodyStyle={{height: 'calc(100% - 1px)'}}
      style={{'--z-index': 9991}}
      destroyOnClose
    >
      <div className={styles.header_line} onClick={props.onCancel}>
        <div className={styles.header_line_bar}></div>
      </div>
      <div className={styles.header_title}>
        病例详情
        <div className={styles.close_icon} onClick={props.onCancel}>
          <CloseOutlined/>
        </div>
      </div>

      <div className={styles.container} id="container">
        <div className={styles.item}>
          <div className={styles.label}>病例名称：</div>
          <div className={styles.text_value}>{caseData.caseName}</div>
        </div>
        {/*
          caseData.depSubjectDictsStrList && caseData.depSubjectDictsStrList.length > 0 &&
          <div className={styles.item}>
            <div className={styles.label}>涉及学科：</div>
            <div className={styles.tag_value}>
              {
                caseData.depSubjectDictsStrList.map((item, index) => <div key={index} className={styles.tag}>{item}</div>)
              }
            </div>
          </div>
        */}
        <div className={styles.item}>
          <div className={styles.label}>病例描述：</div>
          <div className={styles.text_value}>{caseData.noTemplateDescription}</div>
        </div>
        <div className={classNames(styles.item, styles.img_item)}>
          <div className={styles.label}>影像资料：</div>
          <div className={styles.img_value}>
            {
              caseData.consultationCaseMediaDtoList && caseData.consultationCaseMediaDtoList.filter(item => item.type == 0).length > 0 &&
              caseData.consultationCaseMediaDtoList.filter(item => item.type == 0).map((item, index) => {
                return <div key={index} onClick={() => previewBigImage(index)} className={styles.img} style={{backgroundImage: `url(${item.fileUrlShow})`}}></div>
              })
            }
          </div>
        </div>
      </div>

    </Popup>
  )
}

export default Index
