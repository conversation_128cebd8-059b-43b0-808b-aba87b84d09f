/**
 * @Description: 底部导航栏组件
 */
import React from 'react'
import { history, connect } from 'umi'
import classNames from 'classnames'
import { getOperatingEnv, goToHomePage } from '@/utils/utils'
import { square_click } from '@/pages/Home/utils'
import styles from './index.less'

interface PropsType {
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { dispatch } = props
  const pathname = history.location.pathname

  // 页面跳转
  const goToUrl = (type) => {
    switch (type) {
      case 1:
        console.log('点击了首页-+---------------')
        goToHomePage(dispatch, 'replace')
        break
      case 2:
        history.replace('/Square')
        square_click()
        break
      case 3:
        history.replace('/UserInfo')
        break
    }
  }
  return (
    <div className={classNames(styles.tabbar_container, {
      [styles.tabbar_container_pc]: getOperatingEnv() == 4
    })}>
      <div className={styles.box}>
        <div className={classNames(styles.tabbar_item, {
          [styles.checked]: pathname.toLocaleLowerCase() == '/home',
        })} onClick={() => goToUrl(1)}>
          <div className={classNames(styles.tabbar_item_icon, styles.icon_1)}></div>
          <div className={styles.tabbar_item_text}>首页</div>
        </div>
        <div className={classNames(styles.tabbar_item, {
          [styles.checked]: pathname.toLocaleLowerCase() == '/square',
        })} onClick={() => goToUrl(2)}>
          <div className={classNames(styles.tabbar_item_icon, styles.icon_2)}></div>
          <div className={styles.tabbar_item_text}>广场</div>
        </div>
        <div className={classNames(styles.tabbar_item, {
          [styles.checked]: pathname.toLocaleLowerCase() == '/userinfo',
        })} onClick={() => goToUrl(3)}>
          <div className={classNames(styles.tabbar_item_icon, styles.icon_3)}></div>
          <div className={styles.tabbar_item_text}>我的</div>
        </div>
      </div>
    </div>
  )
}

export default connect(({ activity, loading }: any) => ({activity, loading}))(Index)
