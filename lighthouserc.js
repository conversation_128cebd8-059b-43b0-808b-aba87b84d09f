/*
* lighthouserc.js配置文件url为需要测试的页面域名地址，例如：https://dhealth-test.friday.tech
* budget.json为本地性能指标配置文件，请自行配置
* 生成报告命令: npm run lhci
* 报告生成上传查看地址:https://googlechrome.github.io/lighthouse/viewer
* */
module.exports = {
  "ci": {
    "collect": {
      "url":"页面域名地址",
      "settings": {
        "budgets": [
          {
            "path": "/budget.json"
          }
        ]
      }
    },
    "assert": {
      "assertions": {
        "performance-budget": "error"
      }
    },
    "upload": {
      "target": "filesystem",
      "outputDir": "./lighthouseOutput",
      "reportFilenamePattern": "%%HOSTNAME%%-%%PATHNAME%%-%%DATETIME%%.report.json"
    }
  }
}
