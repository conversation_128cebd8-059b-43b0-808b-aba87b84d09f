/**
 * @Description: 广场-收藏
 */
import React, { useState, useEffect } from 'react';
import { connect, history } from 'umi';
import classNames from 'classnames'
import { getOperatingEnv } from '@/utils/utils'
import { InfiniteScroll } from 'antd-mobile'; // 滚动加载
import { Spin } from 'antd';
import styles from './index.less';

import SpaceList from '@/components/SpaceList'; // 空间
import MeetingCard from '@/components/MeetingCardInfo';  // 会议卡片组件
import NotDataRender from '@/components/NoDataRender'; // 暂无数据
import noDataImg from '@/assets/GlobalImg/no_data.png'; // 无数据图片

export type DateItem = {
  id: number;
  wxUserId: number;
  wxUserName: string;
  kingImgUrl: string;
  kingImgUrlShow: string;
  name: string;
  descriptions: string;
  gdp: number;
  hasJoined: number;
  spaceNum: number;
};

// tab切换列表数据
const tabList = [
  {id: 1, text: '直播'},
  {id: 2, text: '会议'},
]

const initState: {
  pageNum: number;
  pageSize: number;
  total: number;
  collectList: DateItem[];
} = {
  pageNum: 1, // 当前页
  pageSize: 30, // 每页条数
  total: 0, // 总条数
  collectList: [], // 数据集合
};

const CollectList: React.FC = (props: any) => {
  const { dispatch, loading, tabState } = props;
  const {collectTabState} = tabState || {}; // 从仓库中取值
  const [tabType, setTabType] = useState(collectTabState || 1); // tab状态
  const [state, setState] = useState(initState); // 列表数据
  const [hasMore, setHasMore] = useState(true); // 更多数据状态
  const { pageNum, pageSize, total, collectList } = state;

  useEffect(()=>{
    if(!localStorage.getItem('access_token')) return;
    getCollectListFn()
  },[tabType])

  // 收藏空间
  const getCollectListFn = () => {
    return dispatch({
      type: 'userInfoStore/getStarSpaceCollect',
      payload: {
        pageNum,
        pageSize,
        starSpaceType:tabType==2?2:1
      },
    }).then((res) => {
      const { code, content } = res || {};
      if (res && code == 200) {
        const { total: responseTotal, resultList } = content || {};
        setState((prevState) => ({
          ...prevState,
          collectList: prevState.pageNum ==1?resultList:[...prevState.collectList, ...resultList],
          total: responseTotal,
          pageNum: prevState.pageNum + 1,
        }));
      } else {
        console.log('数据失败!');
      }
    });
  };

  // 渲染列表
  useEffect(() => {
    if (collectList.length >= total) {
      setHasMore(false);
    } else {
      setHasMore(true);
    }
  }, [collectList, total]);

  // 加载更多数据
  const loadMore = async () => {
    await getCollectListFn();
  };

  // tab切换事件
  const tabSwitchFn = (item: { id: any; text?: string; }) => {
    setTabType(item.id)
    setState(initState) // 重置数据
    // 保存到仓库中当前切换的tab值
    dispatch({
      type: 'tabState/save',
      payload: {
        collectTabState: item.id
      }
    })
  }

  // 去登录
  const goLogin = () => {
    return history.push({
      pathname: '/User/login',
      query: {
        redirect: window.location.href,
      }
    })
  }

  // 未登录
  const RenderNoLogin = () => {
    return <div style={{display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', paddingTop: '40px'}}>
      <div><img style={{width: '150px', height: '113px'}} src={noDataImg} alt="" /></div>
      <div style={{color: '#0095FF', fontSize: '14px'}} onClick={goLogin}>请先登录</div>
    </div>
  }

  const getStarSpaceCollectLoading = !!loading.effects['userInfoStore/getStarSpaceCollect']; // loading

  return (
    <Spin spinning={getStarSpaceCollectLoading}>
      {
        !localStorage.getItem('access_token') ? RenderNoLogin() :
          <div className={styles.collect_content}>
            <div className={classNames(styles.collect_tab_box, {
              [styles.pc]: getOperatingEnv() == 4,
            })}>
              {
                tabList.map((item) => {
                  return <div key={item.id} className={tabType === item.id ? styles.tab_active : styles.tab_init}
                              onClick={() => tabSwitchFn(item)}>{item.text}</div>
                })
              }
            </div>
            {/* 灰条 */}
            <div className={styles.gray_bar}></div>

            {/* 直播or会议数据 */}
            {collectList && collectList.length ? (
              <>
                {
                  tabType == 1 ?
                    <div className={styles.collect_space_list_wrap}>
                      <SpaceList starSpaceType={1} componentData={{dataList: collectList, config: {number: 2}}}/>
                    </div>
                    :
                    <div className={styles.collect_meeting_list_wrap}>
                      {
                        collectList.map((item, index) => {
                          return (
                            <div className={styles.collect_meeting_item_wrap} key={item?.id}>
                              <MeetingCard key={item?.id} item={item} style={{minHeight: 'auto', paddingBottom: 12}} />
                            </div>
                          )
                        })
                      }
                    </div>
                }
                <InfiniteScroll loadMore={loadMore} hasMore={hasMore} threshold={30}/>
              </>
            ) : (
              <NotDataRender text={`暂无收藏${tabType == 1 ? '直播' : '会议'}`}/>
            )}
          </div>
      }
    </Spin>
  );
};
export default connect(({userInfoStore, loading, tabState}: any) => ({
  userInfoStore,
  tabState,
  loading
}))(CollectList);
