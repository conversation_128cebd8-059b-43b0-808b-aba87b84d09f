.meeting_container {
  padding: 0 0 8px;
  background-color:#F5F6F8;
  .meetingList_item{
    padding:10px 12px 0;
    background-color: #FFFFFF;
    h3{
      margin-bottom: 2px;

      font-size: 16px;
      font-weight: 600;
      line-height: 22px;
    }
    h4{
     font-size: 14px;
      line-height: 20px;
      margin-bottom: 8px;
    }
    .item_myMeeting_box{
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #fff;
      padding:0 0 12px;
      border-bottom: 1px solid #E1E4E7;
      .item_moreOperate_btn{
        color: #999999;
      }
      .item_poster_btn{
        display: inline-block;
        padding: 4px 16px;
        color: #0095FF;
        background: #E6F4FF;
        border-radius: 24px;
        margin-right: 8px;
      }
      .item_share_btn,.item_copy_btn{
        display: inline-block;
        padding: 4px 16px;
        color: #FFFFFF;
        background: #0095FF;
        border-radius: 24px;
      }
      .item_copy_btn{
        :global {
          .ant-typography {
            margin-bottom: 0;
          }
          .ant-typography-copy {
            color: #FFFFFF;
          }
        }
      }
    }
    .meetingList_item_dayLast{
      border-bottom: none;
    }
  }
  .meetingList_item_dayFirst{
    margin-top: 8px;
  }
}
.top_wrap{
  display: flex;
  position: relative;
  justify-content: space-between;
  .tab_spaceRoleType_list{
    padding-left: 12px;
    span{
      display: inline-block;
      font-size: 14px;
      color: #666666;
      line-height: 14px;
      padding:12px 0;
      margin-right: 6px;
      cursor: pointer;
    }
    .spaceRoleTypeActive{
      color: #0095FF;
    }
  }
  .screen_btn_active{
    color: #0095FF;
  }
}
.mask_box {
  position: absolute;
  top: 100%;
  left: 0;
  height: 100vh;
  .screen_wrap{
    padding-left:12px;
    background: #fff;
    .screen_container{
      //height: 200px;
      //padding-bottom: 16px;
      .tab_spaceStatus_list{
        span{
          display: inline-block;
          font-size: 14px;
          color: #666666;
          background: #F5F5F5;
          line-height: 14px;
          padding: 6px 30px;
          border-radius: 25px;
          margin-right: 16px;
          margin-bottom: 8px;
        }
        .spaceStatusActive{
          color: #0095FF;
          background: #EDF9FF;
        }
      }
      .screen_box{
        .isBizSelectBox{
          margin-top:5px;
          >div{
            display: inline-block;
          }
          >*{
            margin-right: 20px;
          }
        }
      }
      .tab_spaceStatusTitle,.isBizTitle{
        height: 24px;
        line-height: 24px;
        color: #999999;
        font-size: 14px;
        margin-bottom: 5px;
      }
      :global{
        .adm-checkbox.adm-checkbox-checked .adm-checkbox-icon{
          border-color: #0095FF;
          background: #0095FF;
        }
      }
    }
    .screen_wrap_footer{
      width: 100%;
      height: 50px;
      padding-right: 12px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      .screen_wrap_footer_close ,.screen_wrap_footer_confirm{
        flex: 1;
        height: 34px;
        border-radius: 25px;
        font-size: 15px;
        font-weight: 500;
        text-align: center;
        line-height: 34px;
      }
      .screen_wrap_footer_close{
        margin-right: 15px;
        background: #EDF9FF;
        color: #0095FF;
      }
      .screen_wrap_footer_confirm{
        background: #0095FF;
        color: #FFFFFF;
      }
    }
  }
}
