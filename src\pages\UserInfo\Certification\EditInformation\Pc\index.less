.spin {
  height: 100%;
  & > :global(.ant-spin-container) {
    height: 100%;
  }
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #eef3f9;
}

.content {
  flex: 1;
  padding: 16px 0;
  overflow-y: auto;
}

.content_inner {
  width: 816px;
  min-height: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
}

.nav_bar {
  flex-shrink: 0;
  width: 100%;
  display: flex;
  align-items: center;
  background: #fff;
  padding: 15px 20px;
  margin-bottom: 16px;
  border-radius: 8px;
  i {
    display: inline-block;
    width: 32px;
    height: 32px;
    background: url("../../../../../assets/GlobalImg/pc_goback.png") no-repeat center;
    background-size: 24px 24px;
    margin-right: 4px;
    cursor: pointer;
  }
  span {
    font-size: 20px;
    color: #000;
    line-height: 32px;
  }
}

.wrap {
  flex: 1;
  width: 100%;
  background: #fff;
  border-radius: 8px;
  padding: 24px 40px;
  :global {
    .ant-form-item-label {
      width: 74px;
      text-align: right;
      white-space: nowrap;
      margin-right: 44px;
    }
    .ant-form-item-label > label::after {
      display: none;
    }
    .ant-form-item-label > label {
      height: 36px;
      font-size: 14px;
      color: #000;
    }
    .ant-input {
      height: 36px;
      border-radius: 4px;
      font-size: 14px;
      color: #000;
    }
    .ant-upload.ant-upload-select-picture-card {
      margin: 0;
      width: 120px;
      height: 120px;
      border: 0;
      border-radius: 0;
      background: #F8F8F8;
    }
  }
  .content_title {
    font-size: 22px;
    color: #000;
    font-weight: 600;
    line-height: 31px;
    margin-bottom: 4px;
  }

  .content_tips {
    font-size: 14px;
    color: #999;
    line-height: 20px;
    margin-bottom: 40px;
  }

  .form_upload_img {
    display: flex;
    flex-wrap: nowrap;
    .upload_img_title {
      width: 74px;
      text-align: right;
      white-space: nowrap;
      margin-right: 44px;
      font-size: 14px;
      color: #000;
    }
    .upload_img_list {
      display: flex;
      flex-wrap: wrap;
      column-gap: 8px;
      row-gap: 8px;
      margin-bottom: 8px;
      .upload_img_item {
        position: relative;
        img {
          object-fit: cover;
        }
        .delete_icon_wrap {
          position: absolute;
          top: -10px;
          right: -7px;
          z-index: 1;
          cursor: pointer;
        }
      }
    }
    .upload_img_tips {
      width: 383px;
      font-size: 12px;
      color: #999;
      line-height: 17px;
    }
  }

}

.footer {
  background: #fff;
  border-top: 1px solid #ddd;
  height: 80px;
  flex-shrink: 0;
  .footer_content {
    width: 816px;
    height: 100%;
    margin: 0 auto;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .footer_content_right {
      :global {
        .ant-btn + .ant-btn {
          margin-left: 16px;
        }
        .ant-btn {
          height: 38px;
          padding: 0 20px;
          border-radius: 4px;
        }
      }
    }
  }
}
