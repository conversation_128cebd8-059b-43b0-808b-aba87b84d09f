/**
 * 专家是（本人）主页-空间列表
 */
import React, { useState, useEffect, useRef, useCallback } from 'react';
import styles from './index.less';
import { history, connect } from 'umi';
import classNames from 'classnames';
import {Mask, Toast, Checkbox} from 'antd-mobile';
import { randomColor, processNames, gdpFormat, getOperatingEnv } from '@/utils/utils';
import CoverImageOrVideo from '@/components/SpaceList/CoverImageOrVideo';
import MoreOperate from './MoreOperate'; // 更多操作弹框
import roundDotIcon from '@/assets/UserInfo/roundDot.png';
import CreateKingdomOrSpace from '@/pages/UserInfo/CreateKingdomOrSpace';
import FilterIcon from "@/assets/Case/filter_icon.png";
import FilterActiveIcon from '@/assets/GlobalImg/PcMyConsultationList_Filter.png';
import {stringify} from "qs";
import {message} from "antd";

const spaceJoinTypeList = [
  {
    id: 1,
    name: '我主持的直播',
  },
  {
    id: 2,
    name: '我预约的直播',
  },
  {
    id: 3,
    name: '历史记录',
  }
]
const spaceStatusList = [
  {
    id: null,
    name: '全部',
  },
  {
    id: 2,
    name: '预约中',
  },
  {
    id: 1,
    name: '进行中',
  },
  {
    id: 3,
    name: '已结束',
  }
]


const Index: React.FC = (props: any) => {
  const { dispatch, spaceDataSource, activitySpace, starSpaceType } = props|| {};
  // 我的空间列表筛选条件
  let myHomeSpaceFilter = sessionStorage.getItem('myHomeSpaceFilter')!='undefined'?JSON.parse(sessionStorage.getItem('myHomeSpaceFilter')):{};
  let { friUserId } = JSON.parse(localStorage.getItem('userInfo') || '{}');
  let initSpaceFilter = {
    spaceJoinType: myHomeSpaceFilter?.spaceJoinType||1, // 直播角色类型 1:我主持的\我是嘉宾的，2:我预约的，3:直播历史
    spaceStatus: myHomeSpaceFilter?.spaceStatus||null, // 直播状态 0:全部 1:预约中 2:进行中 3:已结束
    isBiz: null, // 清空非直播类型的筛选
    meetingSpaceStatus: null // 清空非直播类型的筛选
  }
  const isFirstRender = useRef(true);  // 判断是否是初次加载
  const [spaceItem, setSpaceItem] = useState(null); // 当前空间数据
  const [visible, setVisible] = useState(false); // 筛选弹框
  const [spaceFilter, setSpaceFilter] = useState(initSpaceFilter)       // 我的空间列表筛选条件
  const [isFilterActive, setIsFilterActive] = useState((initSpaceFilter.spaceStatus!=null||initSpaceFilter.isBiz!=null)?true:false); // 筛选按钮是否激活
  const [moreVisible, setMoreVisible] = useState(false); // 更多操作弹框
  const { spaceJoinType,spaceStatus } = spaceFilter || {};


  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
    } else {
      props.mySpaceMoreOperateClose(spaceJoinType,spaceStatus);
    }
  }, [spaceJoinType])

  // 点击空间跳转到空间详情
  const goToUrl = (item: { id: any; }) => {
    sessionStorage.setItem('myHomeSpaceFilter', JSON.stringify(spaceFilter));
    // 传空间id
    history.push(`/PlanetChatRoom/Live/${item.id}`)
  }

  // 筛选事件
  const screenBtnFn = () => {
    setVisible(true);
    // 解决出现弹框后,可以滚动页面问题
    document.getElementById('wrap').style.position = 'fixed'
    document.getElementById('wrap').style.overflow = 'hidden';
  }

  // 筛选弹框-取消事件
  const calcelBtnFn = () => {
    setVisible(false);
    // 关闭弹框,重置滚动
    document.getElementById('wrap').style.position = 'unset'
    document.getElementById('wrap').style.overflow = 'auto';
  }

  // 更多操作
  const moreOperateBtn = (item: React.SetStateAction<null>) => {
    setSpaceItem(item)
    setMoreVisible(true)
  }

  // 更多操作下拉弹框关闭按钮
  const closeFn = () => {
    setMoreVisible(false)
  }

  // 编辑空间
  const editSpace = async(item: { id: any; }) => {
    //status,                   // 状态：1直播中、2预约中、3弹幕轰炸中
    const { id,status:statusByItem } = item;
    if(statusByItem == 1) {
      message.warning('该内容正在进行中，不允许编辑~')
      return;
    }else {
      history.push(`/CreateSpace/Live?${stringify({
        id: id,
      })}`)
    }
  }

  // 刷新页面
  const refreshFn = () => {
    props.mySpaceMoreOperateClose(spaceJoinType,spaceStatus);
  }

  // 更新空间筛选条件
  const onOkFilter = () => {
    calcelBtnFn();
    spaceStatus?setIsFilterActive(true):setIsFilterActive(false);
    props.mySpaceChooseFilter(spaceJoinType,spaceStatus);
  }

  return <>
    <div className={styles.top_wrap}>
      <div className={styles.tab_spaceRoleType_list}>
        {
          spaceJoinTypeList.map((item: { id: any; name: React.ReactNode; }) => {
            return <span key={item.id} onClick={(e) => {
              e.stopPropagation();
              setSpaceFilter({
                spaceStatus: null, // 直播状态 0:全部 1:预约中 2:进行中 3:已结束
                isBiz: null, // 清空非直播类型的筛选
                meetingSpaceStatus:null, // 清空非直播类型的筛选
                spaceJoinType: item.id,
              })
              setIsFilterActive(false);
            }} className={item.id == spaceJoinType ? styles.spaceRoleTypeActive : ''}>{item.name}</span>
          })}
      </div>
      <div className={isFilterActive?styles.screen_btn_active:''} style={{marginRight: '16px', paddingTop: '10px'}} onClick={screenBtnFn}>筛选<img src={isFilterActive?FilterActiveIcon:FilterIcon} style={{verticalAlign: 'baseline'}} width={12} height={12}/>
      </div>
      <Mask visible={visible} onMaskClick={calcelBtnFn} className={styles.mask_box}>
        <div className={styles.screen_wrap}>
          <div className={styles.screen_container}>
            <div className={styles.tab_spaceStatus_list}>
                <div className={styles.tab_spaceStatusTitle}>直播状态</div>
                <div className={styles.tab_spaceStatusBox}>
                  {
                    spaceStatusList.map((item, index) => {
                      return <span key={index} onClick={() => {
                        let valueArr;
                        if (item.id == spaceStatus || (spaceStatus && spaceStatus.includes(item.id))) {
                          if(spaceStatus){
                            valueArr =spaceStatus.split(',').filter(i=>i!=item.id).join(',');
                          }
                        } else {
                          if (item.id) {
                            const ids = spaceStatus ? spaceStatus.split(',') : [];
                            if (ids.includes(item.id)) {
                              valueArr = spaceStatus;
                            } else {
                              valueArr = ids.concat(item.id).join(',');
                              // if (valueArr.split(',').length == 3) {
                              //   valueArr = null;
                              // }
                            }
                          } else {
                            valueArr = null;
                          }
                        }
                        setSpaceFilter({
                          ...spaceFilter,
                          spaceStatus: valueArr,
                        })
                      }}
                        className={(item.id == spaceStatus || (spaceStatus && spaceStatus.includes(item.id))) ? styles.spaceStatusActive : ''}>{item.name}</span>
                    })
                  }
                </div>
              </div>
          </div>
          <div className={styles.screen_wrap_footer}>
            <div className={styles.screen_wrap_footer_close} onClick={calcelBtnFn}>取消</div>
            <div className={styles.screen_wrap_footer_confirm} onClick={onOkFilter}>确认</div>
          </div>
        </div>
      </Mask>
    </div>
    <div className={classNames(styles.space_container, styles.two)}>
      {
        spaceDataSource.map((item: any, index: any) => {
          return <div key={`${index}`} className={classNames(styles.item_box, 'item_box_BySpaceList')}>
            <div onClick={(event) => {
              event.stopPropagation();
              goToUrl(item)}}>
              <div className={styles.cover_img}>
                <CoverImageOrVideo
                  data={item}
                  isVideo={Array.isArray(activitySpace) && activitySpace.find(itemChild => itemChild == index)}
                  spaceCoverUrlShow={item.spaceCoverUrlShow}
                  onClick={() => { goToUrl(item) }}
                />
                {/* 封面中的标题 */}
                {
                  item.isTemplateCover==1&& !(item.vodPathUrl && getOperatingEnv() != '2') &&
                  <div className={styles.title_in_cover_image}>{item.name}</div>
                }
                {/* 企业标签 */}
                {item.isBiz ? <div className={styles.sign}>企业</div> : null}
                {/* 身份 */}
                {
                  (item.userIdentity == 1 || item.userIdentity == 2) &&
                  <span className={classNames(styles.user_role, {
                    [styles.role_host]: item.userIdentity == 1,
                    [styles.role_guest]: item.userIdentity == 2,
                  })}>{item.userIdentity == 1 ? '主持人' : '嘉宾'}</span>
                }
                {/* 状态 */}
                {
                  item.status && item.status == 3 || item.isDisable ? '' :
                    <div className={styles.status_box}>
                      <i className={classNames(styles.status_icon, {
                        [styles.icon1]: item.status == 1,
                        [styles.icon2]: item.status == 2,
                      })}></i>
                      <span>{item.status == 1 ? '进行中' : item.status == 2 ? '预约中' : ''}</span>
                    </div>
                }
                {/* GDP */}
                <span className={styles.gdp}>{gdpFormat(item.gdp)}GDP | {gdpFormat(item.pv)}观看</span>
                {item.isDisable ? <span className={styles.offshelf_style}>该内容已下架</span> : ''}
              </div>
              <div className={styles.space_info_box}>
                <div className={styles.space_top}>
                  <div className={styles.title}>{item.name}</div>
                  {item.intro ? <div className={styles.text}>{item.intro}</div> : ''}
                </div>
                <div className={styles.footer}>
                  <div className={styles.footer_left}>
                    <div className={styles.left_avatar}
                         style={item.hostImgUrlShow ? {backgroundImage: `url(${item.hostImgUrlShow})`} : {background: randomColor(item.wxUserId)}}>
                      {!item.hostImgUrlShow && processNames(item.hostName)}
                      {item?.isKing ? <i></i> : ''}
                    </div>
                    <div className={styles.left_name}>{item.hostName}</div>
                  </div>
                  <div className={styles.footer_right}>
                    {
                      item.guestDataList && item.guestDataList.length > 0 &&
                      item.guestDataList.map((itemChild: {
                        wxUserId: React.Key | null | undefined;
                        headUrlShow: any;
                        userName: string;
                      }, indexChild: number) => {
                        if (indexChild >= 3) {
                          return null
                        }
                        return (
                          <div key={itemChild.wxUserId} className={styles.right_avatar}
                               style={itemChild.headUrlShow ? {backgroundImage: `url(${itemChild.headUrlShow})`} : {background: randomColor(itemChild.wxUserId)}}>
                            {!itemChild.headUrlShow && processNames(itemChild.userName)}
                          </div>
                        )
                      })
                    }
                    {
                      item.guestDataList && item.guestDataList.length > 3 &&
                      <div className={styles.avatar_more}>···</div>
                    }
                  </div>
                </div>
              </div>
            </div>
            <div className={styles.edit_wrap}>
              <div className={styles.more_box} onClick={() => moreOperateBtn(item)}><img src={roundDotIcon} alt=""/></div>
              {(item.isDisable || (item.hostUserId != friUserId)||spaceJoinType==3) ? '' :
                <div className={styles.edit_box} onClick={() => editSpace(item)}>编辑</div>}
            </div>
          </div>
        })
      }
    </div>
    <MoreOperate spaceItem={spaceItem} visible={moreVisible} close={closeFn} refreshFn={refreshFn}
                 myHomeSpaceFilter={spaceFilter}/>
    {/* comeType传2，打开空间编辑 */}
    <CreateKingdomOrSpace comeType={2} starSpaceType={starSpaceType}/>
  </>
}
export default connect(({expertAdvice, userInfoStore, loading}: any) => ({expertAdvice, userInfoStore, loading}))(Index)
