export default {
  namespace: 'kingdom',
  state: {
    tabKey: 1,    // tab选中状态
    subTabKey: 1,    // 王国详情-二级tab选中状态
  },

  effects: {},

  reducers: {
    // 保存数据
    save(state: any, {payload}: any) {
      return {
        ...state,
        ...payload,
      }
    },
    // 清空数据
    clean(state: any, {payload}: any) {
      return {
        ...state,
        tabKey: 1,
        subTabKey: 1,    // 王国详情-二级tab选中状态
      }
    },
  },

  subscriptions: {
    setup({dispatch, history}) {
      return history.listen(({pathname, search}) => {
        if (
          pathname.indexOf('/PlanetChatRoom/') == -1 &&
          pathname.indexOf('/CreateGraphicsText/ArticleDetails') == -1 &&
          pathname.indexOf('/CreateGraphicsText/PostDetails') == -1 &&
          pathname.indexOf('/CreateGraphicsText/ExternalLinksDetails') == -1 &&
          pathname.indexOf('/Kingdom') == -1
        ) {
          dispatch({
            type: 'clean',
          })
        }
      })
    }
  }
};
