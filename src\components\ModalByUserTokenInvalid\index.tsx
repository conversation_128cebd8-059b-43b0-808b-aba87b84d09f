/**
 * @Description: 预约空间卡片
 */
import React, {useEffect, useState} from 'react';
import { history, connect } from 'umi'
import classNames from 'classnames'
import { getOperatingEnv, goToHomePage } from '@/utils/utils'
import {Button, message} from 'antd';
import {Modal, Popup} from "antd-mobile";
import styles from './index.less'
import ModalWXsubscribe from './ModalWXsubscribe'
import Avatar from '@/pages/PlanetChatRoom/components/Avatar';
import { getCurrentWXWorkApp } from '@/utils/utils'
import { liveAppointment } from '@/services/planetChatRoom'
import {userTokenInvalid} from "@/utils/request";

interface PropsType {
  open: any,
  ReservationId: any,  // 预约空间id
  title:any,  // 提示title
  isNotIcon: any, // 是否展示叹号 icon
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {
    dispatch,
    open,
    title,
    isNotIcon
  } = props

  const pathname = history.location.pathname
  const UerInfo = JSON.parse(localStorage.getItem('userInfo')) || {};
  const [ isNotLogin,setIsNotLogin ] = useState(!(UerInfo && UerInfo.friUserId));
  // 是否打开去登录弹窗
  const [ModalVisibleByUserTokenInvalid,setModalVisibleByUserTokenInvalid] = useState(false);

  useEffect(()=>{
    setModalVisibleByUserTokenInvalid(open)
  },[open])

  const showModal = () => {
    setModalVisibleByUserTokenInvalid(true);
  };

  const handleOk = () => {
    setModalVisibleByUserTokenInvalid(false);
  };

  const handleCancel = () => {
    setModalVisibleByUserTokenInvalid(false);
  };

  return (
    <>
      {/* 用户未登录提示弹窗 */}
      <div className={styles.warp_UserTokenInvalid}>
        <Modal
          visible={!!ModalVisibleByUserTokenInvalid}
          className={styles.WarnModalWarp_NoRem}
          closeOnAction
          onClose={() => {}}
          content={
            <div className={styles.WarnModal}>
              <div className={styles.WarnModalTitle}>
                {!isNotIcon &&
                  <i className={styles.SpatialDetail_modal_warn_icon}></i>
                }
                <div className={styles.SpatialDetail_modal_warn_title}>{!!title ? title : "立即登录，畅享精彩内容！"}</div>
              </div>
              <div className={styles.WarnModalBtnWarp}>
                <div
                  onClick={async ()=>{
                    handleCancel();
                  }}
                  className={styles.CancelBtn}>暂不登录</div>
                <div
                  onClick={async ()=>{ userTokenInvalid('/PlanetChatRoom') }}
                  className={styles.EnterBtn}>立即登录</div>
              </div>
            </div>
          }
        />
      </div>
    </>
  )
}

export default connect(({ activity, loading }: any) => ({activity, loading}))(Index)
