/**
 * @Description: 专家搜索结果页面rukou
 * @author: 赵斐
 */
import React, { useEffect, useState, lazy, Suspense } from 'react';
import { connect, history } from 'umi';
import { getOperatingEnv, } from '@/utils/utils'
import { debounce } from 'lodash';
// H5端专家搜索结果页面
const H5Result = lazy(() => import('../Components/H5Result'))
// import H5Result from '../Components/H5Result';
// PC端专家搜索结果页面
const PcResult = lazy(() => import('../Components/PcResult'))
// import PcResult from '../Components/PcResult';
const Index: React.FC = (props: any) => {
  const { location, dispatch } = props || {};

  const { query } = location || {}; // whereStatus = 999代表点击搜索跳转过来
  const { whereStatus, searchValue } = query || {}
  const [pageType, setPageType] = useState<any>(); // 1pc 2 移动端
  // ① 判定当前页面视口是否小于750 如果小于750则为移动端
  let updateType = () => {
    // let clientWidth = document.documentElement.clientWidth;
    let env = getOperatingEnv() // 获取当前操作环境 1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用
    let type = env == '4' ? 1 : 2;
    setPageType(type);
  };
  updateType = debounce(updateType, 100);
  // 进入页面判定是否存在token
  useEffect(() => {
    // ① 判定当前页面视口是否小于750 如果小于750则为移动端
    updateType();
    window.addEventListener('resize', updateType, { passive: true });
    if (getOperatingEnv() == "4") {
      if (searchValue || whereStatus) {
        dispatch({
          type: "expertAdvice/save",
          payload: {
            searchValue: searchValue ? searchValue : null,
            checkDepSubject: whereStatus != 999 ? parseInt(whereStatus) : null,  // 学科初始数据
          }
        })
      }
      history.replace('/Expert/ExpertResult')
    }
    return () => {
      window.removeEventListener('resize', updateType)
    }
  }, []);
  return (
    <Suspense fallback={<div></div>}>
      {
        pageType == 1 ?
          <PcResult />
          : pageType == 2 ? <H5Result whereStatus={whereStatus} /> : null
      }
    </Suspense>
  )
}
export default connect(({ expertAdvice, loading }: any) => ({ expertAdvice, loading }))(Index)
