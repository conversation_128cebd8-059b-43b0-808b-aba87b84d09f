.container {
  height: 100%;
  padding-top: 44px;
  padding-bottom: 190px;
}

.content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .img_wrap {
    position: relative;
    & > img {
      border-radius: 16px;
      max-width: 320px;
      max-height: calc(100vh - 190px - 44px - 36px - 36px);
    }
    .tips {
      position: absolute;
      bottom: -24px;
      left: 0;
      width: 100%;
      text-align: center;
      color: #666;
      font-size: 12px;
      height: 14px;
      line-height: 14px;
    }
  }

}

.bottom_wrap {
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 900;
  width: 100%;
  height: 190px;
  padding: 15px 0 0;
  background: #fff;
  .bottom_title {
    margin-bottom: 8px;
    font-size: 14px;
    color: #666;
    line-height: 20px;
    padding: 0 16px;
  }
  .bottom_template_wrap {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    column-gap: 12px;
    padding: 0 16px 12px;
    .template_option {
      width: auto;
      height: auto;
      border-radius: 6px;
      position: relative;
      & > img {
        object-fit: cover;
        border-radius: 6px;
      }
      &.checked::after {
        content: "";
        display: block;
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        border: 2px solid #0095FF;
        border-radius: 6px;
        background-image: url("../../assets/GlobalImg/check_icon_2.png");
        background-repeat: no-repeat;
        background-size: 24px 24px;
        background-position: 102% 103%;
      }
    }
  }
}
