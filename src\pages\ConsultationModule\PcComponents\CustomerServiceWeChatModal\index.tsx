/**
 * @Description: PC端添加客服小忆微信、预约会议二维码提示弹窗
 * @author: 赵斐
 */
import React from 'react';
import { Modal } from 'antd';
import QRcode from 'qrcode.react'
import styles from './index.less'
interface PropsType {
  type?: number,            // 1 预约会议  2 小忆改约
  visible: boolean,         // 添加客服小忆微信弹窗状态
  onCancel: () => void,     // 取消回调
  url?: string,             // 二维码地址
  consultationId?: any,      // 指导ID
  consultationType?: any,    // 指导类型
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { visible, onCancel, url, type, consultationId } = props;

  // 关闭弹窗
  const handleCancel = () => {
    onCancel()
  };

  return (
    <div className={styles.wrap} id='modal_customer'>
      <Modal
        getContainer={() => document.getElementById("modal_customer")}
        open={visible}
        title={type == 1 ? '预约会议' : "添加客服小忆微信"}
        onCancel={handleCancel}
        width={474}
        footer={null}
      >
        <div className={styles.content}>
          <div className={styles.qr_code}>
            {
              type == 1 ? <>
                <QRcode
                  value={`${window.location.origin}/ConsultationModule/ConsultationDetails?consultationId=${consultationId}&consultationType=2&isShare=1`}
                  size={180}
                />
                <p className={styles.word}>请扫码，在手机端预约指导</p>
              </>
                : <>
                  <img src={url} alt="code" />
                  <p className={styles.word}>联系客服小忆更改会议时间</p>
                </>
            }
          </div>
        </div>

      </Modal>
    </div>
  )
}
export default Index
