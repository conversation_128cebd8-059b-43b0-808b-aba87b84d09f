@import '../../../../utils/imageText.less';

.spin {
  height: 100%;
  :global {
    .ant-spin-container {
      height: 100%;
    }
  }
}

.container {
  padding-top: 52px;
  padding-bottom: 52px;
  height: 100%;
  overflow-y: auto;
}

.user_card_wrap {
  padding: 0 16px;
}

.image_text_content_wrap {
  padding: 20px 12px 12px;
}

.gdp_wrap {
  display: flex;
  align-items: center;
  padding: 0 12px;
  margin-bottom: 24px;
  & > i {
    width: 14px;
    height: 14px;
    margin-right: 4px;
    background: url("../../../../assets/GlobalImg/gdp.png") no-repeat center;
    background-size: 100% 100%;
  }
  & > span {
    font-size: 12px;
    color: #777;
    line-height: 18px;
  }
}

.forward_content {
  background: #f8f8f9;
  padding: 12px;
  margin-bottom: 12px;
}

.post_content {
  padding: 8px;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  border: 1px solid #EBEBEB;
  border-radius: 4px;
  overflow: hidden;
  :global {
    .ql-editor {
      font-size: 14px !important;
      color: #333333 !important;
      line-height: 20px !important;
    }
  }
  .init_img {
    width: 68px;
    height: 68px;
    border-radius: 4px;
    font-size: 30px;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    flex-shrink: 0;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
  }

  .text {
    flex: 1;
    font-size: 14px;
    color: #333333;
    line-height: 18px;
    word-break: break-all;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3; /* 指定显示三行 */
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.space_wrap {
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #EBEBEB;
  display: flex;
  flex-wrap: nowrap;
  .left_cover_image {
    flex-shrink: 0;
    margin-right: 8px;
    width: 68px;
    height: 68px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    line-height: 68px;
    color: #fff;
    font-size: 30px;
    text-align: center;
    white-space: nowrap;
    position: relative;
    .title_in_cover_image {
      position: absolute;
      z-index: 10;
      width: 100%;
      top: 14px;
      left: 0;
      padding-left: 8px;
      font-size: 12px;
      line-height: 16px;
      color: #fff;
      font-weight: 500;
      word-break: break-all;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2; /* 指定显示行数 */
      overflow: hidden;
      text-overflow: ellipsis;
      text-align: left;
    }
  }
  .right {
    flex: 1;
    overflow: hidden;
    .space_title {
      font-size: 13px;
      color: #000;
      font-weight: 500;
      line-height: 18px;
      word-break: break-all;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      text-overflow: ellipsis;
    }
    .space_introduce {
      font-size: 12px;
      color: #333;
      line-height: 16px;
      word-break: break-all;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      text-overflow: ellipsis;
    }
  }
}

.no_forward_content {
  margin-bottom: 12px;
  height: 34px;
  background: #F8F8F9;
  padding: 0 12px;
  line-height: 34px;
  font-size: 13px;
  color: #333;
}
