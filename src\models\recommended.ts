import {
  commentsLike,
  getCommentList,
  getHotList,
  getLatelyPublishUser,
  getList,
  getSquareShowTopic,
  getTopicList,
  getWaterfallManageData,
  imageTextLikeOrCancel,
  imgTextInfoById,
} from '@/services/recommended'

export default {
  namespace: 'recommended',
  state: {
    checkedTab: 0, // 搜索结果页，选中tab
    dataByGetCommentList:[], // 评论下的回复列表
  },

  effects: {
    // 获取最近三天有发布的用户推荐
    *getLatelyPublishUser({ payload }, { call }) {
      const response = yield call(getLatelyPublishUser, payload)
      return response
    },

    // 推荐-分页获取内容
    *getList({ payload }, { call }) {
      const response = yield call(getList, payload)
      return response
    },

    // 获取运营后台设置前3条信息
    *getWaterfallManageData({ payload }, { call }) {
      const response = yield call(getWaterfallManageData, payload)
      return response
    },

    // 评论下的回复列表
    *getCommentList({ payload }, { call,put }){
      const response = yield call(getCommentList, payload);
      const { code,content } = response || {};
      if (code == 200 && Array.isArray(content)) {
        yield put({ type: 'save', payload: { dataByGetCommentList: content }});
      }else {
        yield put({ type: 'save', payload: { dataByGetCommentList: [] }});
      }
      return response
    },

    // 获取图文详情
    *imgTextInfoById({ payload }, { call,put }){
      const response = yield call(imgTextInfoById, payload);
      return response;
    },

    // 图文点赞与取消点赞
    *imageTextLikeOrCancel({ payload }, { call,put }){
      const response = yield call(imageTextLikeOrCancel, payload);
      return response;
    },

    // 评论/回复点赞 server/imageTextCommentsH5/commentsLike
    *commentsLike({ payload }, { call,put }){
      const response = yield call(commentsLike, payload);
      return response;
    },
    // 热门话题排行榜
    *getHotList({ payload }, { call,put }){
      const response = yield call(getHotList, payload);
      return response;
    },
    // 话题-分页获取内容
    *getTopicList({ payload }, { call,put }){
      const response = yield call(getTopicList, payload);
      return response;
    },

    // 获取广场页展示的话题(“大家都在聊”)
    *getSquareShowTopic({ payload }, { call,put }){
      const response = yield call(getSquareShowTopic, payload);
      return response;
    },
  },

  reducers: {
    // 保存数据
    save(state, { payload }) {
      return {
        ...state,
        ...payload,
      }
    },
    // 清空数据
    clean(state, { payload }) {
      return {
        checkedTab: 0,                                         // 搜索结果页，选中tab
      }
    },
  },

  subscriptions: {
    setup({ dispatch, history }) {
      /*return history.listen(({ pathname, search }) => {
        if (
          pathname.indexOf('/Square') == -1
        ) {
          dispatch({
            type: 'clean',
          })
        }
      })*/
    }
  }
}
