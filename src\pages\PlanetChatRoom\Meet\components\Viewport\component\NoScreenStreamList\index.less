.StreamSpanWarp {
  display: flex;
  width: 0px;
  height: 0px;
  overflow: hidden;

  .StreamSpan {
    width: 0px;
    height: 0px;
    overflow: hidden;
  }
}

.swiperWarp {
  width: 100%;
  // height: calc(100% - 20px);
  :global {
    .adm-swiper {
      padding-top: 20px;
      padding-bottom: 20px;
    }
  }
}

.swiperWarpByindicatorhidden {
  :global {
    .adm-swiper-horizontal .adm-swiper-indicator {
      display: none;
    }
  }
}

.ViewContent {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: calc(100% - 8px);

  // padding: 10px;
  // border: 1px solid #fff;
  overflow: hidden;
}

.ViewLine {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.WaitNumBox {
  width: 100%;
  height: 24px;
  color: #a0a0a0;
  font-weight: 400;
  font-size: 12px;
  font-style: normal;
  line-height: 24px;
  text-align: center;
  text-transform: none;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px 2px 2px 2px;

  .num {
    color: #0095ff;
  }
}
