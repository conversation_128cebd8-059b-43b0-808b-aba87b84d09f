.modal_content{
  height: 500px;
  display: flex;
  align-items: center;
  background: #fff;
  padding-top: 52px;
  :global {
    .ant-modal-body {
      padding:0;
    }
    .ant-modal-content{
      background: transparent;
      box-shadow: none;
      width: 100%;
      
    }
    .ant-modal-close{
      top: -14px;
    }
    .ant-modal-close-x{
      width: 52px;
      height: 52px;
    }
    .video-js{
      width: 100%;
        height: 480px;
    }
  }
}
// .wrap {
//   width: 100%;
//   background: #FFFFFF;
//   position: relative;
//   :global {

//     .ant-modal-body {
//       padding: 0;
//     }
//   }


//   .modal_content {
//     height: 500px;
//     display: flex;
//     align-items: center;
//     .video {
//       width: 100%;
//       height: unset;
//       aspect-ratio: 16/9;
//       object-fit: contain;
//     }
//   }

// }