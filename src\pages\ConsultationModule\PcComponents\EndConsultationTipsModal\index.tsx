/**
 * @Description: PC 端结束指导提示弹窗
 * @author: 赵斐
 */
import React from 'react';
import { Modal } from 'antd';
import styles from './index.less'


interface PropsType {
  visible: boolean,       // 结束指导提示弹窗状态
  onCancel: (val?:number) => void,   // 取消回调
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { visible, onCancel } = props;

  // 点击确认并关闭弹窗
  const onConfirm = () => {
    onCancel(1)
  }

  // 关闭弹窗
  const handleCancel = () => {
    onCancel()
  };

  return (
    <div className={styles.wrap} id='modal'>
      <Modal
        getContainer={() => document.getElementById("modal")}
        open={visible}
        title="结束指导"
        onCancel={handleCancel}
        width={474}
        footer={null}
      >
        <div className={styles.content}>
          <p className={styles.word}>结束后将无法继续会话，确定结束？</p>
          <div className={styles.footer}>
            <span className={styles.cancel} onClick={() => { onCancel() }}>取消</span>
            <span className={styles.confirm} onClick={() => { onConfirm() }}>确定</span>
          </div>
        </div>
      </Modal>
    </div>
  )
}
export default Index
