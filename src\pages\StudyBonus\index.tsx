import React, {useState, useEffect, useRef, lazy, Suspense} from 'react';
import { history,connect } from 'umi';
import styles from "./index.less";
import {getOperatingEnv} from "@/utils/utils";
import {NavBar} from "antd-mobile";
import {Spin} from "antd";
import InfiniteScroll from 'react-infinite-scroller';

// H5 端学习金详情
const H5studyBonus = lazy(() => import('./H5studyBonus'));
// PC 端学习金详情
const PCstudyBonus = lazy(() => import('./PCstudyBonus'));


const index: React.FC = (props) => {
  const [pageType, setPageType] = useState<any>(); // 1pc 2 移动端
  // ① 判定当前页面视口是否小于750 如果小于750则为移动端
  let updateType = () => {
    // let clientWidth = document.documentElement.clientWidth;
    let env = getOperatingEnv() // 获取当前操作环境 1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用
    let type = env == '4' ? 1 : 2;
    setPageType(type);
  };
  // 进入页面判定是否存在token
  useEffect(() => {
    // ① 判定当前页面视口是否小于750 如果小于750则为移动端
    updateType();
    window.addEventListener('resize', updateType, { passive: true });
    return () => {
      window.removeEventListener('resize', updateType)
    }
  }, []);

  return (
    <Suspense fallback={<div></div>}>
      {
        pageType == 1 ?
          <PCstudyBonus />
          : pageType == 2 ? <H5studyBonus/> : null
      }
    </Suspense>
  )
}

export default connect(({ ConsultationList,pcAccount, loading }: any) => ({
  ConsultationList,pcAccount, loading
}))(index)
