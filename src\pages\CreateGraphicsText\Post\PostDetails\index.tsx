/**
 * @Description: 帖子详情
 */
import React, { useState, useEffect, useRef } from 'react';
import { connect, history, useAliveController } from 'umi';
import { getIsIniPhoneAndWeixin, gdpFormat } from '@/utils/utils'
import { message, Spin } from 'antd'
import { Toast, ImageViewer } from 'antd-mobile'
import styles from './index.less';

import NavBar from '@/components/NavBar'                                                 // 导航组件
import UserCardByImageText from '@/components/UserCardByImageText' // 用户卡片
import KingdomCard from '@/pages/CreateGraphicsText/ComponentsH5/KingdomCard'            // 王国卡片
import CommentList from '@/pages/CreateGraphicsText/ComponentsH5/CommentList'            // 评论内容
import CommentFooter from '@/pages/CreateGraphicsText/ComponentsH5/CommentFooter'        // 评论底部组件
import MoreOperate from '@/components/MoreOperate'                                       // 右上角点点点
import UserCardByNavBar from '@/pages/CreateGraphicsText/ComponentsH5/UserCardByNavBar'  // 导航栏样式的用户卡片

import { saveCommentsOrReply } from '@/services/recommended';        // 提交评论

const Index: React.FC = (props: any) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')   // 用户信息
  const { id } = history.location.query          // 图文ID
  const { dispatch } = props;

  const refContainer = useRef(null)              // ref，处理页面滚动时用户信息卡片置顶
  const refUserCard = useRef(null)               // ref
  const refImageViewer = useRef(null)            // 查看大图ref
  const commentListRef = useRef(null);           // 评论组件ref

  const [detailsState, setDetailsState] = useState<any>(null)                  // 详情数据
  const [navBarToggle, setNavBarToggle] = useState(false)                      // 切换显示导航的状态
  const [loadingImgTextInfoById, setLoadingImgTextInfoById] = useState(false)  // 页面查询数据loading
  const [imageViewerVisible, setImageViewerVisible] = useState(false)          // 图片查看器
  const { clear } = useAliveController()             // 页面缓存控制器

  // 详情
  const {
    // imageType,              // 图文类型：1.文章 2.帖子 3.外链 4.空间
    kingdomInfo,              // 关联王国信息，备注: 关联王国信息
    createUserId,             // 创建人id
    userName,                 // 用户名称
    headUrlShow,              // 头像/专家形象路径
    isExperts,                // 是否是专家：0:否，1:是
    operateDateDescs,         // 创建时间
    imageTextContent,         // 文章、帖子内容
    gdp,                      // 页面GDP
    isFocus,                  // 0未关注，1已关注
    expertsInfo,              // 专家信息
    status,                   // 状态：1.审核通过（已发布） 0.未审核 2.审核未通过 3.草稿
    textImgList,              // 帖子图片
  } = detailsState || {};

  // 浏览器返回事件
  const browserBack = () => {
    setImageViewerVisible(false)
  }

  // 滚动监听事件，改变导航栏样式
  useEffect(() => {
    window.addEventListener('popstate', browserBack)
    document.getElementById('container') && document.getElementById('container').addEventListener('scroll', handleScroll)

    return () => {
      ImageViewer.clear()
      window.removeEventListener('popstate', browserBack)
      document.getElementById('container') && document.getElementById('container').removeEventListener('scroll', handleScroll)
    }
  }, [])

  // 获取图文详情
  useEffect(() => {
    imgTextInfoById()
  }, [])

  // 滚动事件回调
  const handleScroll = () => {
    // console.log('handleScroll')
    // console.log(refContainer, refUserCard)
    // console.log(ref.current.offsetTop, ref.current.offsetParent.offsetTop)
    const scrollTop = refContainer.current.scrollTop + 44            // 44 是顶部导航栏的高度
    const offset = refUserCard.current.offsetHeight + refUserCard.current.offsetTop
    // 显示带用户信息的导航栏
    if (scrollTop >= offset) {
      setNavBarToggle(true)
    } else {
      setNavBarToggle(false)
    }
  }

  // 图文详情，isLocalUpdate 是否局部更新
  const imgTextInfoById = (isLocalUpdate = false) => {
    // 关注后刷新数据，不loading
    if (!isLocalUpdate) {
      setLoadingImgTextInfoById(true)
    }
    dispatch({
      type: 'graphicsText/imgTextInfoById',
      payload: {
        imageTextId: id,                         // 图文ID
      }
    }).then(res => {
      setLoadingImgTextInfoById(false)
      const { code, content, msg } = res
      if (code == 200) {
        setDetailsState(content)
      } else {
        Toast.show(msg || '数据加载失败')
      }
    }).catch(err => {})
  }

  // 关注或取消关注回调，刷新数据
  const handleFollowAndCheck = () => {
    imgTextInfoById(true)
  }

  // 点击图文内容，如果点击到话题就跳转到话题主页
  const onClickImageTextContent = (e) => {
    console.log(e.target,e.target.parentNode)
    // 点击话题
    if (e.target && e.target.dataset && e.target.dataset.type == 'topic') {
      history.push(`/CreateGraphicsText/TopicHome?topicId=${e.target.dataset.id}`)
      return
    }
    // 点击话题
    if (e.target && e.target.parentNode && e.target.parentNode.dataset && e.target.parentNode.dataset.type == 'topic') {
      history.push(`/CreateGraphicsText/TopicHome?topicId=${e.target.parentNode.dataset.id}`)
      return
    }
  }

  // 提交评论
  const onPost = async ({ commentInputInfo,  value}) => {
    if(commentInputInfo && value) {
      let params = {
        imageTextId: commentInputInfo.id,                     //  number 非必须  上级图文主键ID
        imageTextType: commentInputInfo.imageType,            //	number 非必须  上级图文类型：1.文章 2.帖子 3.外链 4.空间
        commentsContent: value,                               //	string 非必须  上级评论/回复内容
        commentsType: 0,                                      // 评论类型 0评论 1回复 2 引用回复
      }
      const data = await saveCommentsOrReply(params)
      const { code, content } = data || {}
      if (code == 200) {
        message.success('评论成功!')
        commentListRef?.current?.commentsList();
      }else {
        message.error('评论失败!')
      }
    }
  }

  // 查看大图，打开
  const previewBigImage = (index) => {
    if (imageViewerVisible) {
      return
    }
    // 为了在手机上手势返回时，不返回上一页，而是关闭弹窗
    window.history.pushState(null, null, document.URL)
    setImageViewerVisible(true)
    refImageViewer?.current?.swipeTo(index)
  }

  // 查看大图，关闭
  const imageViewerOnClose = () => {
    if (!imageViewerVisible) {
      return
    }
    window.history.go(-1)
    setImageViewerVisible(false)
  }

  // 返回
  const handleDeleteOrLow = async () => {
    // 清除我的主页的页面缓存
    await clear()

    if (history.length > 1) {
      history.goBack()
    } else {
      history.replace('/')
    }
  }

  // 关闭评论弹窗需更新评论底部数据
  const updateCommentFooter = (commentsCount) => {
    setDetailsState(prevState => {
      return {
        ...prevState,
        commentsCount
      }
    })
  }

  return (
    <Spin spinning={loadingImgTextInfoById} wrapperClassName={styles.spin}>
      {/* 滚动后导航条 */}
      {
        navBarToggle ?
          <UserCardByNavBar
            headUrlShow={headUrlShow}
            userName={userName}
            createUserId={createUserId}
            isExperts={isExperts}
            operateDateDescs={operateDateDescs}
            isFocus={isFocus}
            expertsInfo={expertsInfo}
            handleFollowAndCheck={handleFollowAndCheck}
            id={id}
            imageType={2}
            status={status}
            handleDeleteOrLow={handleDeleteOrLow}
          />
          :
          <NavBar
            title="帖子详情"
            bordered
            RightRender={
              () => UserInfo.friUserId == createUserId ?
                <MoreOperate id={id} imageType={2} status={status} handleDeleteOrLow={handleDeleteOrLow}/>
              : null
            }
          />
      }

      <div id="container" className={styles.container} ref={refContainer} style={getIsIniPhoneAndWeixin() ? { paddingBottom: '86px' } : {}}>

        {/* 用户卡片 */}
        <div ref={refUserCard} className={styles.user_card_wrap}>
          <UserCardByImageText
            headUrlShow={headUrlShow}
            userName={userName}
            createUserId={createUserId}
            isExperts={isExperts}
            operateDateDescs={operateDateDescs}
            isFocus={isFocus}
            expertsInfo={expertsInfo}
            handleFollowAndCheck={handleFollowAndCheck}
            isShowMoreOperate={false}
          />
        </div>

        {/* 图文详情 */}
        <div className={styles.image_text_content_wrap}>
          <div className="ql-editor" onClick={onClickImageTextContent} dangerouslySetInnerHTML={{__html: imageTextContent}}></div>
        </div>

        {/* 图片 */}
        {
          textImgList && textImgList.length > 0 &&
          <div className={styles.post_image_wrap}>
            {
              textImgList.map((item, index) => {
                return (
                  <div key={index} className={styles.image_item_wrap}>
                    <div className={styles.image_item} onClick={() => previewBigImage(index)} style={{backgroundImage: `url(${item.imageUrlShow})`}}></div>
                  </div>
                )
              })
            }
          </div>
        }
        {
          textImgList && textImgList.length > 0 &&
          <ImageViewer.Multi
            ref={refImageViewer}
            images={textImgList.map(item => item.imageUrlShow)}
            visible={imageViewerVisible}
            defaultIndex={0}
            onClose={imageViewerOnClose}
            getContainer={document.body}
          />
        }

        {/* 关联王国 */}
        {
          kingdomInfo &&
          <div className={styles.kingdom_wrap}>
            <KingdomCard kingdomInfo={kingdomInfo}/>
          </div>
        }

        {/* gdp */}
        <div className={styles.gdp_wrap}>
          <i></i>
          <span>{gdpFormat(gdp)}GDP</span>
        </div>

        {/* 评论list */}
        <CommentList
          onRef={commentListRef}
          imageTextId={id}
          updateCommentFooter={updateCommentFooter}
        />
      </div>

      {/* 评论底部内容 */}
      <CommentFooter
        imageTextId={id}
        imgTextInfo={detailsState}
        onPost={onPost}
      />
    </Spin>

  )
}

export default connect(({ userInfoStore, loading }: any) => ({ userInfoStore, loading }))(Index)
