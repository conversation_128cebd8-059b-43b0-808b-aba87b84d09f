/**
 * @Description: PC 端指导用户提问卡片
 * @author: 赵斐
 */
import React from 'react';
import styles from './index.less'

interface PropsType {
  questionData:string   // 问题
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { questionData } = props || {}

  return (
    <div className={styles.wrap}>
      <div className={styles.header}>
        <span className={styles.header_title}>用户提问</span>
      </div>
      <div className={styles.content}>
        <div className={styles.desc}>{questionData}</div>
      </div>
    </div>
  )
}
export default Index
