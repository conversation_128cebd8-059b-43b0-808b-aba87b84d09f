/**
 * @Description: h5端-我的
 */
import React, { useEffect, useState } from 'react';
import { connect, history } from 'umi';
import classNames from 'classnames';
import { QuestionCircleFilled, LeftOutlined } from '@ant-design/icons';
import styles from './index.less';
import { Spin, Badge } from 'antd';
import { To<PERSON>, Modal, Picker, Popup, Mask } from 'antd-mobile';
import ReactHtmlParser from 'react-html-parser'; // dom字符串转dom结构
import Tabbar from '@/components/Tabbar'; // 底部tab
import CreateKingdomOrSpace from './CreateKingdomOrSpace'; // 创建空间/王国弹框组件
import { randomColor, processNames, getOperatingEnv } from '@/utils/utils';
import dayjs from 'dayjs';

import {
  priceFormat, // 价格格式化
} from '@/utils/utils';
import rightArrowIcon from '@/assets/GlobalImg/right_arrow.png'; // 右箭头小图标
import createIcon from '@/assets/GlobalImg/create_icon.png'; // 创建空间王国小图标
import companyIcon from '@/assets/Enterprise/companyIcon.png'; // 我的企业图标
import rightIcon from '@/assets/Enterprise/right.png'; // 右侧
import leftIcon from '@/assets/Enterprise/left.png'; // 左侧
import tipIcon from '@/assets/Enterprise/tip.png'; // 提示
import mangeIcon from '@/assets/Enterprise/mangeIcon.png'; //管理员图标

const editIcon = 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/edit.png'; // 编辑小图标
const classIcon =
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/icon_class.png'; // 课程小图标
const followIcon =
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/icon_follow.png'; // 关注小图标
const collectIcon =
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/icon_collect.png'; // 收藏小图标
const setIcon =
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/icon_set.png'; // 设置小图标
const xiaoyiImg =
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/xiaoyi.png'; // 小忆人图片
const ordinaryIcon =
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/ordinary.png'; // 普通用户图标
const doctorIcon =
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/doctor.png'; // 医生会员图标
const enterpriseIcon =
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/enterprise.png'; // 医生会员图标
import consultationIcon from '@/assets/GlobalImg/consultation_icon.png'; // 指导图标
const dataList = [
  {
    id: 1,
    text: '我的课程',
    icon: classIcon,
    jumpExternalUrl: 'https://apptmq2ocpm6396.h5.xiaoeknow.com/p/t/v1/ecommerce/pay_record/record',
  },
  { id: 2, text: '我的关注', icon: followIcon, jumpUrl: '/UserInfo/Interest' },
  { id: 3, text: '我的收藏', icon: collectIcon, jumpUrl: '/UserInfo/Collect' },
  {
    id: 5,
    text: '我的病例',
    icon: consultationIcon,
    jumpUrl: '/PaymentByConsultation/MyConsultationList',
  },
  // {id: 4, text: '我的企业', icon: companyIcon, jumpUrl: '/UserInfo/Setting'}
];

const Index: React.FC = (props: any) => {
  const { global, dispatch, loading, userInfoStore } = props || {};
  const [isShowQRCode, setIsShowQRCode] = useState(true); // 是否展示二维码
  const [userInfo, setUserInfo] = useState<any>(null); // 用户信息数据
  const [vipList, setVipList] = useState([]); // vip数据
  const [codeImg, setCodeImg] = useState(''); // 二维码图片
  const [waistcoatVisible, setWaistcoatVisible] = useState(false); // 马甲弹窗是否显示
  const [waistcoatList, setWaistcoatList] = useState([]); // 马甲List
  const [friUserToScholarship, setFriUserToScholarship] = useState(false); // 获取学习金余额
  const [administratorsVisible, setAdministratorsVisible] = useState(false); // 企业管理员选择企业的弹窗
  const [staffVisible, setStaffVisible] = useState(false); // 企业员工的背景层弹窗
  const [staffFirstVisible, setStaffFirstVisible] = useState(true); //企业员工离职第一次默认显示弹窗
  const [staffDoubleVisible, setStaffDoubleVisible] = useState(false); //企业员工离职二次确认弹窗
  const [enterpriseInfo, setEnterpriseInfo] = useState(); // 企业管理员或者员工的信息
  const [selectedEnterprise, setSelectedEnterprise] = useState(); // 当前选择的员工或者管理员
  const [inviteVisible, setInviteVisible] = useState(false); // 邀请弹窗
  const [inviteImg, setInviteImg] = useState(''); // 邀请二维码图片
  const [inviteImgLoading, setInviteImgLoading] = useState(false); // 邀请二维码loading

  const token = localStorage.getItem('access_token');
  const { createModalVisible } = userInfoStore;
  const UerInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');

  // 解决ios手机在弹框弹出软键盘后，弹框底部内容滚动问题
  useEffect(() => {
    if (createModalVisible) {
      document.body.style.position = 'fixed';
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.position = 'unset';
      document.body.style.overflow = 'auto';
    }
  }, [createModalVisible]);

  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    initialization();
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    initGetMemberIconList();
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    getEnterpriseUserInfo();
  }, []);

  // 获取学习金数据 fri-user-to-scholarship  获取用户个人主页学习金数据
  const getFriUserToScholarshipByfunc = () => {
    dispatch({
      type: 'userInfoStore/getFriUserToScholarship',
      payload: {
        wxUserId: (UerInfo && UerInfo.friUserId) || '',
      },
    })
      .then((res) => {
        const { code, content } = res || {};
        if (code == 200 && content) {
          setFriUserToScholarship(content);
        }
      })
      .catch((err) => {
        Toast.show({ content: err.msg });
      });
  };
  // 初始化获取用户是否登录
  const initialization = async () => {
    if (!localStorage.getItem('access_token')) return;
    await dispatch({
      type: 'userInfoStore/getUserInfo',
      payload: {
        wxUserId: (UerInfo && UerInfo.friUserId) || '',
      },
    })
      .then((res) => {
        const { code, content } = res || {};
        if (code == 200) {
          localStorage.setItem(
            'userInfo',
            JSON.stringify({
              ...UerInfo,
              headUrl: content.headUrlShow,
            }),
          );
          // 初始化账号马甲列表
          UerInfo?.vestUcStarIdentityList?.length > 0 &&
            getUpdateWaistcoatList(UerInfo.vestUcStarIdentityList);
          setUserInfo(content);
          // 判定是否有学习金 如果有继续调用获取学习金余额
          // isHasScholarship = 1 是否有学习金 1有 0无
          if (content && content.isHasScholarship == 1) {
            // 获取学习金数据
            getFriUserToScholarshipByfunc();
          }
        } else {
          setUserInfo(null);
        }
      })
      .catch((err) => {
        Toast.show({ content: err.msg });
      });
  };

  // 获取会员图标列表
  const initGetMemberIconList = async () => {
    await dispatch({
      type: 'userInfoStore/getMemberIconList',
      payload: {
        wxUserId: (UerInfo && UerInfo.friUserId) || '',
      },
    })
      .then((res) => {
        const { code, content } = res || {};
        if (code == 200) {
          setVipList(content && content.memberIconList);
          setCodeImg(content && content.defaultAssistantUrl);
        }
      })
      .catch((err) => {
        Toast.show({ content: err.msg });
      });
  };

  // 我的课程、我的关注、我的收藏、设置点击跳转事件
  const menuBtnFn = (item: any) => {
    // 判断是否登录
    if (!token) {
      return history.push(`/User/login`);
    }

    // 内部跳转
    if (item && item.jumpUrl) {
      return history.push(item.jumpUrl);
    }

    // 跳转外部链接(我的课程)
    if (item && item.jumpExternalUrl) {
      return window.open(item.jumpExternalUrl);
    }
  };

  // 获取企业员工信息
  const getEnterpriseUserInfo = async () => {
    await dispatch({
      type: 'userInfoStore/getEnterpriseUserInfo',
      payload: {},
    })
      .then((res) => {
        const { code, content } = res || {};
        if (code == 200) {
          setEnterpriseInfo(content);
        }
      })
      .catch((err) => {
        Toast.show({ content: err.msg || '获取企业员工信息失败' });
      });
  };

  // 下浮弹窗处理中的 点击处理
  const handleEnterprise = (item) => {
    setSelectedEnterprise(item);
    if (item.roleType == 'BIZ_ADMIN') {
      // 进入详情页
      setAdministratorsVisible(false);
      const access_token = localStorage.getItem('access_token');
      const { phone, friUserId } = JSON.parse(localStorage.getItem('userInfo'));
      const { tenantId, name, roleType } = item;
      const paramsUrl = `?phone=${phone}&tenantId=${tenantId}&name=${name}&roleType=${roleType}&access_token=${access_token}&friUserId=${friUserId}`;
      window.location.href = 
      window.location.hostname !== 'dhealth-test.friday.tech' &&  window.location.hostname !== 'localhost'
        ? `https://doctor.friday.tech/enterprise/home${paramsUrl}` 
        : `https://doctortest.friday.tech/enterprise/home${paramsUrl}`;
    } else {
      // 离职弹窗
      setAdministratorsVisible(false);
      setStaffVisible(true);
    }
  };

  // 我的企业逻辑处理: 是否品牌管理员  若存在多品牌 至少有一个品牌是管理员则为true
  const handleCompany = () => {
    // 1.没有企业时，提示用户没有加入
    if(!enterpriseInfo || (enterpriseInfo && !enterpriseInfo?.bizList?.length) ) {
      Toast.show({ content:'您暂未加入任何企业'});
    }

    // 2.普通员工 / 企业管理员 只有一个时
    if (enterpriseInfo && enterpriseInfo?.bizList?.length == 1) {
      handleEnterprise(enterpriseInfo?.bizList[0]);
    }

    // 3.普通员工 / 企业管理员  有多个时  唤起下浮弹窗
    if (enterpriseInfo && enterpriseInfo?.bizList?.length > 1) {
      setAdministratorsVisible(true);
    }
  };

  // 确认离职
  const handleResign = async () => {
    await dispatch({
      type: 'userInfoStore/staffResign',
      payload: {
        tenantId: selectedEnterprise?.tenantId,
      },
    })
      .then((res) => {
        if (res.code == 200) {
          setStaffVisible(false);
          setStaffFirstVisible(true);
          setStaffDoubleVisible(false);
          getEnterpriseUserInfo();
          Toast.show('您已完成离职');
        }
      })
      .catch((err) => {
        Toast.show({ content: err.msg || '您办理的离职失败' });
      });
  };

  // 邀请成员
  const handleInvite = async() => {
    // setInviteVisible(true)
    // setInviteImgLoading(true)
    // setStaffVisible(false);
    // setStaffFirstVisible(false);
    // await dispatch({
    //   type: 'userInfoStore/getInviteImg',
    //   payload: {
    //     tenantId: selectedEnterprise?.tenantId,
    //   },
    // })
    //   .then((res) => {
    //     if (res.code == 200) {
    //       setInviteImg(res.content)
    //       setInviteImgLoading(false)
    //     }
    //   })
    //   .catch((err) => {
    //     setInviteImgLoading(false)
    //     Toast.show({ content: err.msg || '目前无法邀请成员' });
    //   });


    const access_token = localStorage.getItem('access_token');
    const { phone } = JSON.parse(localStorage.getItem('userInfo'));
    const { tenantId, name, roleType } = selectedEnterprise;
    const paramsUrl = `?phone=${phone}&tenantId=${tenantId}&name=${name}&roleType=${roleType}&access_token=${access_token}`;
    setStaffVisible(false);
    setStaffFirstVisible(false);
    window.location.href = 
      window.location.hostname !== 'dhealth-test.friday.tech' &&  window.location.hostname !== 'localhost'
        ? `https://doctor.friday.tech/enterprise/invite${paramsUrl}` 
        : `https://doctortest.friday.tech/enterprise/invite${paramsUrl}`;
  };

  // vip会员权益图标跳转
  const jumpUrl = (item: any) => {
    if (!token) {
      return history.push(`/User/login`);
    }
    const { webJumpLink, jumpLink } = item || {};
    if (jumpLink) {
      // 外部链接-跳转小鹅通/鹅直播
      return window.open(jumpLink);
    }
    if (webJumpLink) {
      // 跳转内部链接
      return history.push(webJumpLink);
    }
  };

  // 会员开通按钮
  const vipOpenBtn = () => {
    history.push('/Payment/MemberBenefitsPage');
  };

  // 创建空间/王国
  const createSpaceBtnFn = async () => {
    await dispatch({ type: 'userInfoStore/clean' });
    await localStorage.removeItem('kingdomInfoData');
    await localStorage.removeItem('isSuperAccount');
    if (!token) {
      return history.push(`/User/login`);
    } else {
      await checkSuperAccount();
      await dispatch({
        type: 'userInfoStore/setTaskListState',
        payload: {
          createModalVisible: true,
        },
      });
    }
  };

  // 是否为超级账号
  const checkSuperAccount = () => {
    dispatch({
      type: 'userInfoStore/checkSuperAccount',
    })
      .then((res) => {
        if (res && res.code == 200) {
          localStorage.setItem('isSuperAccount', res.content); // true是，false否
          dispatch({
            type: 'userInfoStore/setTaskListState',
            payload: {
              isSuperAccount: res.content,
            },
          });
        } else {
          Toast.show({ content: '数据加载失败' });
        }
      })
      .catch((err: any) => {
        console.log(err);
      });
  };

  // 去主页
  const goHomepage = () => {
    // 去主页
    return history.push(`/Expert/ExpertDetails?id=${UerInfo?.friUserId}`);
  };

  // 编辑信息
  const editInfo = () => {
    // 未登录->前往登录页面
    if (!token) {
      return history.push(`/User/login`);
    } else {
      // 去编辑信息页
      return history.push('/userInfo/editUserInfo');
    }
  };

  // 获取马甲列表
  const getUpdateWaistcoatList = (List) => {
    const mainInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    let listArr = [];
    List.map((item: any) => {
      if (item.friUserId != mainInfo?.friUserId) {
        listArr.push({
          label: `${item.serialNumber}-${item?.nickName ? item.nickName : item.name}`,
          value: item.friUserId,
        });
      }
    });
    setWaistcoatList(listArr);
  };

  // 设置切换马甲号
  const setSwitchWaistcoatAccount = (vestUserId) => {
    Toast.show({ icon: 'loading', maskClickable: false });
    dispatch({
      type: 'userInfoStore/setSwitchWaistcoatAccount',
      payload: {
        vestUserId,
      },
    })
      .then((res) => {
        const { code, content } = res || {};
        if (code == 200) {
          dispatch({
            type: 'userInfoStore/getSwitchWaistcoatToken',
            payload: {
              vestToUcKey: content,
            },
          })
            .then((res) => {
              const { code, content } = res || {};
              if (code == 200) {
                localStorage.setItem('access_token', content.access_token);
                localStorage.setItem('user_name', content.username);
                localStorage.setItem('vxOpenIdCipherText', content.username);
                updateUserInfo(vestUserId, content.username);
              }
            })
            .catch((err) => {
              Toast.show({ content: err.msg });
            });
        }
      })
      .catch((err) => {
        Toast.show({ content: err.msg });
      });
  };

  // 更新当前账号信息
  const updateUserInfo = (vestUserId, userName) => {
    dispatch({
      type: 'userInfoStore/getUserInfo',
      payload: {
        wxUserId: vestUserId,
        userId: vestUserId,
        userName,
      },
    })
      .then((res) => {
        const { code, content } = res || {};
        if (code == 200) {
          const currentInfo = UerInfo?.vestUcStarIdentityList.filter(
            (obj) => obj.friUserId === vestUserId,
          )[0];
          localStorage.setItem(
            'userInfo',
            JSON.stringify({
              ...UerInfo,
              ...currentInfo,
              id: vestUserId,
              headUrl: content.headUrlShow,
              vestUcStarIdentityList: content?.vestUcStarIdentityList,
            }),
          );
          getUpdateWaistcoatList(content?.vestUcStarIdentityList);
          setUserInfo(content);
          Toast.show('切换马甲成功!');
        }
      })
      .catch((err) => {
        setUserInfo(null);
        Toast.show({ content: err.msg });
      });
  };

  const goToCertification = () => {
    history.push('/UserInfo/Setting');
  };

  const {
    id,
    name,
    memberType,
    nickName,
    focusCount,
    fansCount,
    personGdpCount,
    status,
    headUrlShow,
  } = userInfo || {};
  const getMemberIconListLoading = !!loading.effects['userInfoStore/getMemberIconList']; // loading
  const getUserInfoLoading = !!loading.effects['userInfoStore/getUserInfo']; // loading
  return (
    <Spin spinning={getMemberIconListLoading || getUserInfoLoading}>
      <div
        className={classNames(styles.wrap, {
          [styles.in_app_wrap]: getOperatingEnv() == 5,
        })}
      >
        <div className={styles.user_info_wrap}>
          <div className={styles.user_info_box}>
            <div className={styles.user_info_img}>
              <div className={styles.head_sculpture}>
                {userInfo && headUrlShow ? (
                  <img onClick={goToCertification} src={headUrlShow} alt="" />
                ) : (
                  <div
                    className={styles.head_sculpture_name}
                    onClick={goToCertification}
                    style={{
                      background: (userInfo && name) || nickName ? randomColor(id) : 'none',
                    }}
                  >
                    {processNames(nickName || name)}
                  </div>
                )}
              </div>
              <div className={styles.edit_box} onClick={editInfo}>
                <img src={editIcon} alt="" />
                编辑账户
              </div>
            </div>
            <div>
              <div className={styles.user_info_content}>
                <div className={styles.user_info_content_left}>
                  <div className={styles.top_box}>
                    {userInfo && (name || nickName) ? (
                      <>
                        <span className={styles.user_name_text}>
                          {!!nickName ? nickName : name}
                        </span>
                        {memberType != null && memberType == 0 ? (
                          <img src={ordinaryIcon} alt="" />
                        ) : memberType == 1 ? (
                          <img src={doctorIcon} alt="" />
                        ) : memberType == 2 ? (
                          <img src={enterpriseIcon} alt="" />
                        ) : null}
                      </>
                    ) : (
                      <span
                        onClick={() => {
                          history.push(`/User/login`);
                        }}
                      >
                        您好，请先登录
                      </span>
                    )}
                    {(getOperatingEnv() != 5 || getOperatingEnv() != 6) &&
                      waistcoatList?.length > 0 && (
                        <span
                          className={styles.change_waistcoat}
                          onClick={() => {
                            setWaistcoatVisible(true);
                          }}
                        >
                          换马甲
                        </span>
                      )}
                  </div>
                  <div className={styles.bottom_box}>
                    <div className={styles.info_data_item}>
                      <span>{userInfo && focusCount != null ? focusCount : '***'}</span>关注
                    </div>
                    <div className={styles.info_data_item}>
                      <span>{userInfo && fansCount != null ? fansCount : '***'}</span>粉丝
                    </div>
                    <div className={styles.info_data_item}>
                      <span>{userInfo && personGdpCount != null ? personGdpCount : '***'}</span>GDP
                      <QuestionCircleFilled
                        onClick={() => {
                          Modal.show({
                            bodyClassName: 'gdp_modal',
                            content:
                              (global.gdpExplain != null &&
                                ReactHtmlParser(global.gdpExplain[0].gdpContent)) ||
                              '暂无GDP介绍',
                            closeOnMaskClick: true,
                          });
                        }}
                        style={{ fontSize: 12, color: '#999', marginLeft: 4 }}
                      />
                    </div>
                  </div>
                </div>
                {userInfo ? (
                  <div className={styles.user_info_content_right} onClick={goHomepage}>
                    我的主页
                    <img src={rightArrowIcon} alt="" />
                  </div>
                ) : null}
              </div>

              {/*
                学习金余额展示组件 userInfo.isHasScholarship
                isHasScholarship = 1 是否有学习金 1有 0无
              */}
              {userInfo && userInfo?.isHasScholarship == 1 && friUserToScholarship && (
                <div className={styles.BalanceStudyFundsBox}>
                  <div className={styles.BalanceStudyFundsLeft}>
                    <div className={styles.BalanceStudyFundsTitle}> 学习金余额 </div>
                    <div className={styles.BalanceStudyFundsNum}>
                      {' '}
                      {priceFormat(friUserToScholarship.currentNum)}{' '}
                    </div>
                    <div className={styles.BalanceStudyFundsDate}>
                      {' '}
                      有效期至
                      {dayjs(friUserToScholarship.validDate, 'YYYY-MM-DD').format(
                        'YYYY年MM月DD日',
                      )}{' '}
                    </div>
                    <div className={styles.StudyBonusBg}></div>
                  </div>
                  <div className={styles.BalanceStudyFundsRight}>
                    <div
                      onClick={() => {
                        history.push('/StudyBonus/list');
                      }}
                      className={styles.DetailBox}
                    >
                      <div>查看详情</div>
                      <i className={styles.DetailBox_icon}></i>
                    </div>
                  </div>
                </div>
              )}
              {/* 学习金余额展示组件-end */}
            </div>
          </div>
        </div>
        <div className={styles.menu_wrap}>
          {dataList.map((item) => {
            return (
              <div key={item.id} className={styles.menu_item} onClick={() => menuBtnFn(item)}>
                <img src={item.icon} alt="" />
                <span className={styles.little_title}>{item.text}</span>
              </div>
            );
          })}
          <div key="4" className={styles.menu_item} onClick={handleCompany}>
            <Badge count={enterpriseInfo?.waitAuditCount || 0} size="small">
              <img src={companyIcon} alt="" />
            </Badge>
            <span className={styles.little_title}>我的企业</span>
          </div>
        </div>
        <div className={styles.kingdom_box}>
          <div className={styles.kingdom_content}>
            <div className={styles.kingdom_text}>发布新内容</div>
            <div className={styles.kingdom_describe}>传播知识的领土/在线分享的渠道</div>
          </div>
          <div className={styles.kingdom_create} onClick={createSpaceBtnFn}>
            <img src={createIcon} alt="" />
            创建
          </div>
        </div>
        <div className={styles.vip_member_box}>
          <div className={styles.vip_title}>{userInfo ? '我的权益' : 'FRIDAY会员权益'}</div>
          <div className={styles.vip_list}>
            {vipList &&
              vipList.map((item, ind) => {
                return (
                  <div key={ind} className={styles.vip_list_item} onClick={() => jumpUrl(item)}>
                    <div className={styles.vip_list_item_icon}>
                      <img
                        src={`https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/${item.iconPath}`}
                        alt=""
                      />
                    </div>
                    <div className={styles.vip_list_item_content}>
                      <div className={styles.vip_list_item_text}>{item.name}</div>
                      {item.remark ? (
                        <div className={styles.vip_list_item_tips}>{item.remark}</div>
                      ) : null}
                    </div>
                  </div>
                );
              })}
          </div>
          <div className={styles.vip_button}>
            <div className={styles.vip_button_style} onClick={vipOpenBtn}>
              {!userInfo || memberType == 0 ? (
                <span className={styles.vip_button_text}>开通会员&nbsp;畅享内容</span>
              ) : memberType == 1 ? (
                <span className={styles.vip_button_text}>升级企业会员&nbsp;解锁新权益</span>
              ) : memberType == 2 ? (
                <span className={styles.vip_button_text}>续费企业会员&nbsp;解锁智能设备</span>
              ) : null}
            </div>
          </div>
        </div>
        <div className={styles.character_Img}>
          <div className={styles.QR_code_wrap} style={{ display: isShowQRCode ? 'block' : 'none' }}>
            <div className={styles.QR_code_box}>
              <img className={styles.QR_code_img} src={codeImg} alt="" />
              <div className={styles.QR_code_text}>有问题,请找小忆</div>
            </div>
          </div>
          <img
            className={styles.people_img}
            src={xiaoyiImg}
            onClick={() => {
              setIsShowQRCode(!isShowQRCode);
            }}
            alt=""
          />
        </div>

        {/* 底部菜单栏 */}
        {getOperatingEnv() == 5 ? null : <Tabbar />}

        {/* 创建空间弹窗 */}
        <CreateKingdomOrSpace />

        {/* 切换马甲弹窗*/}
        <Picker
          columns={[waistcoatList]}
          visible={waistcoatVisible}
          onCancel={() => {
            setWaistcoatVisible(false);
          }}
          onConfirm={(value) => {
            setSwitchWaistcoatAccount(value[0]);
            setWaistcoatVisible(false);
          }}
        />

        {/* 企业管理员 / 普通员工 有多个企业时 - 进行选择进入详情/ 出现弹窗 */}
        {administratorsVisible && (
          <Popup
            visible={administratorsVisible}
            onClose={() => {
              setAdministratorsVisible(false);
            }}
            onMaskClick={() => {
              setAdministratorsVisible(false);
            }}
            // bodyStyle={{ height: '40vh' }}
          >
            <div className={styles.administratorsModal}>
              <div className={styles.administratorsModal_line} onClick={() => setAdministratorsVisible(false)}>
                <span />
              </div>
              <div className={styles.administratorsModal_header}>
                <span
                  className={styles.administratorsModal_header_img}
                  onClick={() => setAdministratorsVisible(false)}
                >
                  <img src={leftIcon} />
                </span>
                <span className={styles.title}>选择企业</span>
              </div>
              <div className={styles.administratorsModal_list}>
                {enterpriseInfo &&
                  enterpriseInfo?.bizList.length > 0 &&
                  enterpriseInfo?.bizList?.map((item, index) => (
                    <div
                      className={styles.administratorsModal_item}
                      key={index}
                      onClick={() => handleEnterprise(item)}
                    >
                      <Badge count={item?.bizWaitAuditCount || 0} size="small">
                        {item.name}
                      </Badge>
                      <div className={styles.administratorsModal_item_rt}>
                        {item.roleType == 'BIZ_ADMIN' && (
                          <div className={styles.mangeIcon}>
                            <img src={mangeIcon} />
                            <span>管理员</span>
                          </div>
                        )}
                        <img src={rightIcon} />
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </Popup>
        )}

        {/* 企业员工 - 我的企业 - 处理弹窗 */}
        {staffVisible && (
          <Mask
            visible={staffVisible}
            onMaskClick={() => {
              setStaffVisible(false);
              setStaffFirstVisible(true);
              setStaffDoubleVisible(false);
            }}
          >
            {staffFirstVisible && (
              <div className={styles.staffModal}>
                <div className={styles.title}>我的企业</div>
                <div className={styles.content}>
                  <div className={styles.companytitle}> {selectedEnterprise?.name} </div>
                  {selectedEnterprise?.orgList?.length > 0 && (
                    <div className={styles.des}>
                      {selectedEnterprise?.orgList.map((item) => item.bizOrgName).join('、')}{' '}
                    </div>
                  )}
                </div>
                <div className={styles.footer}>
                  <span
                    className={styles.btn1}
                    onClick={() => {
                      setStaffDoubleVisible(true);
                      setStaffFirstVisible(false);
                    }}
                  >
                    我已离职
                  </span>
                  <span className={styles.btn2} onClick={handleInvite}>
                    邀请成员
                  </span>
                </div>
              </div>
            )}

            {staffDoubleVisible && (
              <div className={styles.staffDoubleModal}>
                <div className={styles.tip_header}>
                  <img src={tipIcon} alt="" />
                  <span>确认离职？</span>
                </div>
                <div className={styles.tip_content}>
                  <span>您是否确认已从该企业离开?</span>
                  <span>如离开企业，您将不再享受企业权益!</span>
                </div>
                <div className={styles.tip_footer}>
                  <span
                    className={styles.tip_btn1}
                    onClick={() => {
                      setStaffVisible(false);
                      setStaffFirstVisible(true);
                      setStaffDoubleVisible(false);
                    }}
                  >
                    取消
                  </span>
                  <span className={styles.tip_btn2} onClick={handleResign}>
                    确认离职
                  </span>
                </div>
              </div>
            )}
          </Mask>
        )}

        {/* 邀请弹窗 */}
        {/* {
          inviteVisible &&
           <Mask
           visible={inviteVisible}
           onMaskClick={() => {
            setInviteVisible(false);
            setInviteImg('')

            setStaffVisible(false);
            setStaffFirstVisible(true);

           }}
           className={styles.inviteImg_wrapper}
         >
          {

          }
            <Spin spinning={inviteImgLoading}>
              {inviteImg && <img src={inviteImg} />}
            </Spin>
          </Mask>
        } */}
      </div>
    </Spin>
  );
};
export default connect(({ global, userInfoStore, loading }: any) => ({
  global,
  userInfoStore,
  loading,
}))(Index);
