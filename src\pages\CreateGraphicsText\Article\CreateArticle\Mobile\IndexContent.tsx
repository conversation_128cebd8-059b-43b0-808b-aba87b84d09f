import React, { useEffect, useState, useRef } from 'react'
import { connect, history, useAliveController } from 'umi'
import classNames from 'classnames'
import { stringify } from 'qs'
import { cloneDeep } from 'lodash'
import { useThrottle, isIOS, getIsIniPhoneAndWeixin, getOperatingEnv } from '@/utils/utils'
import { Spin } from 'antd'
import { TextArea, Toast } from 'antd-mobile'
import styles from './index.less'

import NavBar from '@/components/NavBar'
import QuillDom from '@/pages/CreateGraphicsText/Quill'
import ToolbarMobile from '@/pages/CreateGraphicsText/Quill/ToolbarMobile'

let timer = null
let timer2 = null
let pageDidMount = false

let initialPageY = 0

const Index: React.FC = (props: any) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
  const { id, topicId: topicHomeTopicId, topicName: topicHomeTopicName } = history.location.query
  const { loading, graphicsText, dispatch } = props

  const quillRef = useRef(null)

  const [toolbarPanelVisible, setToolbarPanelVisible] = useState(false)
  const [quillHistoryStack, setQuillHistoryStack] = useState({})
  const [quillFormat, setQuillFormat] = useState({})

  const [imageTitle, setImageTitle] = useState('')
  const [textImgList, setTextImgList] = useState([])
  const [refreshPageState, setRefreshPageState] = useState(false)
  const [loadingSave2, setLoadingSave2] = useState(false)
  const [loadingUploadVideoOrImage, setLoadingUploadVideoOrImage] = useState(0)
  const { clear } = useAliveController()

  useEffect(() => {
    if (!pageDidMount) {
      return
    }
    console.log('暂存暂存暂存暂存1111111111', loadingUploadVideoOrImage)
    clearTimeout(timer)
    if (loadingUploadVideoOrImage) {
      return
    }
    timer = setTimeout(() => {
      editImgTextInfo2()
    }, 2000)
  }, [imageTitle])

  useEffect(() => {
    if (id) {
      imgTextInfoUpdateId()
      pageDidMount = true
    } else {
      pageDidMount = true
      if (topicHomeTopicId && topicHomeTopicName) {
        timer2 = setTimeout(() => {
          insertTopicByTopicHome()
        }, 100)
      }
    }
    // window.addEventListener('resize', onWindowResize)
    // window.addEventListener('focusin', focusinHandler)
    // window.addEventListener('focusout', focusoutHandler)
    // if (document.getElementById('content')) {
    //   document.getElementById('content').addEventListener('touchstart', touchstart, {passive: false})
    //   document.getElementById('content').addEventListener('touchmove', touchmove, {passive: false})
    // }

    const env = getOperatingEnv()
    if (isIOS() && window.visualViewport && env != '5' && env != '6') {
      window.addEventListener('focusin', focusinHandler, {passive: false})
      document.documentElement.addEventListener('touchstart', touchstart, {passive: false})
      document.documentElement.addEventListener('touchmove', touchmove, {passive: false})
      document.documentElement.addEventListener('touchend', touchend, {passive: false})
      window.visualViewport.addEventListener('resize', visualViewportResize)
    }

    clear()

    return () => {
      pageDidMount = false
      clearTimeout(timer)
      clearTimeout(timer2)
      initialPageY = 0
      if (isIOS() && window.visualViewport && env != '5' && env != '6') {
        window.removeEventListener('focusin', focusinHandler, {passive: false})
        document.documentElement.removeEventListener('touchstart', touchstart, {passive: false})
        document.documentElement.removeEventListener('touchmove', touchmove, {passive: false})
        document.documentElement.removeEventListener('touchend', touchend, {passive: false})
        window.visualViewport.removeEventListener('resize', visualViewportResize)
      }
    }
  }, [])

  const focusinHandler = () => {
    setTimeout(() => {
      document.documentElement.scrollTop = 0
    }, 0)
  }

  const visualViewportResize = () => {
    const dom = document.getElementById('content')
    const dom2 = document.getElementById('fixed_toolbar_mobile')
    const dom3 = document.querySelector('#quill .ql-editor')

    const originHeight = document.documentElement.clientHeight
    const newHeight = window.visualViewport.height;
    // const bb = document.body.clientHeight;
    // const dd = window.innerHeight
    // const ee = window.screen.height;

    const keyboardHeight = originHeight - newHeight

    document.documentElement.scrollTop = 0
    if (keyboardHeight == 0) {
      dom.style.height = ''
      // dom2.style.paddingBottom = getIsIniPhoneAndWeixin() ? '34px' : ''
      dom2.style.paddingBottom = ''
      dom3.style.minHeight = ''
    } else {
      dom.style.height = `calc(100% - 68px - ${keyboardHeight}px)`
      dom2.style.paddingBottom = `${keyboardHeight}px`
      dom3.style.minHeight = '1000px'
      if (quillRef.current && quillRef.current.quill) {
        quillRef.current.quill.scrollIntoView()
      }
    }
  }

  const touchstart = (e) => {
    console.log(111, 'touchstart', e)
    // e.stopPropagation()
    initialPageY = e.touches[0].pageY
  }

  const touchmove = (e) => {
    console.log(222, 'touchmove', e)
    const scrollDom = getScrollableElement(e.target)
    if (scrollDom) {
      if (scrollDom.scrollHeight - scrollDom.clientHeight - scrollDom.scrollTop < 10) {
        const pageY = e.touches[0].pageY
        if (pageY < initialPageY) {
          e.preventDefault()
        }
      } else if (scrollDom.scrollTop == 0) {
        const pageY = e.touches[0].pageY
        if (pageY > initialPageY) {
          e.preventDefault()
        }
      }
    } else {
      e.preventDefault()
    }
  }

  const touchend = (e) => {
    console.log(333, 'touchend', e)
    // e.stopPropagation()
    initialPageY = 0
  }

  const getScrollableElement = (el: HTMLElement | null) => {
    let current = el?.parentElement

    while (current) {
      if (current.clientHeight < current.scrollHeight) {
        return current
      }

      current = current.parentElement
    }

    return null
  }

  useEffect(() => {
    if (graphicsText.selectedTopicName) {
      insertTopic(graphicsText.selectedTopicId, graphicsText.selectedTopicName, graphicsText.selectTopicType)
    }
  }, [graphicsText.selectedTopicName])

  const imgTextInfoUpdateId = () => {
    dispatch({
      type: 'graphicsText/imgTextInfoUpdateId',
      payload: {
        imageTextId: id,
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200 && content) {
        if (content.createUserId != UserInfo.friUserId) {
          Toast.show('您没有编辑权限')
          setTimeout(() => {
            goBack()
          }, 1000)
          return
        }
        setImageTitle(content.imageTitle || '')
        setTextImgList(content.textImgList || [])
        if (content.contentJson) {
          const contentJsonObj = JSON.parse(content.contentJson)
          quillRef?.current?.quill.setContents(contentJsonObj)
        }
      } else {
        Toast.show(msg || '数据加载失败')
      }
    }).catch(err => {
    })
  }

  const insertTopicByTopicHome = () => {
    if (quillRef && quillRef.current && quillRef.current.quill && quillRef.current.quill.getSelection(true)) {
      quillRef.current.topicFn({topicId: topicHomeTopicId, topicName: `#${topicHomeTopicName}#`}, 2)
      clearTimeout(timer2)
    } else {
      timer2 = setTimeout(() => {
        insertTopicByTopicHome()
      }, 50)
    }
  }

  const titleOnChange = (value) => {
    console.log('输入标题', value)
    setImageTitle(value)
  }

  const onChangeImageTextContent = () => {
    console.log('输入正文内容触发回调，暂存', loadingUploadVideoOrImage)
    clearTimeout(timer)
    if (loadingUploadVideoOrImage) {
      return
    }
    timer = setTimeout(() => {
      editImgTextInfo2()
    }, 2000)
  }

  const insertTopic = (topicId, topicName, selectTopicType) => {
    dispatch({
      type: 'graphicsText/save',
      payload: {
        selectedTopicId: null,
        selectedTopicName: null,
        selectTopicType: 2,
      }
    })
    timer2 = setTimeout(() => {
      insertTopicTimeout(topicId, topicName, selectTopicType)
    }, 100)
  }

  const insertTopicTimeout = (topicId, topicName, selectTopicType) => {
    if (quillRef && quillRef.current && quillRef.current.quill && quillRef.current.quill.getSelection(true)) {
      quillRef.current.topicFn({topicId, topicName: `#${topicName}#`}, selectTopicType)
      clearTimeout(timer2)
    } else {
      timer2 = setTimeout(() => {
        insertTopicTimeout(topicId, topicName, selectTopicType)
      }, 50)
    }
  }

  const getQuillHistoryStack = (stack) => {
    setQuillHistoryStack(cloneDeep(stack))
  }

  const getQuillFormat = (format) => {
    setQuillFormat(cloneDeep(format))
  }

  const toolbarPanelOnChange = (visible) => {
    setToolbarPanelVisible(visible)
  }

  let beforeSubmit = (isDisabled) => {
    if (isDisabled) {
      return
    }
    submit()
  }

  beforeSubmit = useThrottle(beforeSubmit, 500)

  const submit = () => {
    const articleLength = quillRef?.current?.unprivilegedEditor?.getText().replace(/\n/g, '').length
    const imageTextContent = quillRef?.current?.unprivilegedEditor?.getHTML()
    const contentJson = JSON.stringify(quillRef?.current?.unprivilegedEditor?.getContents())
    if (!imageTitle || !imageTitle.trim()) {
      Toast.show('请输入标题(2-50个字)')
      return
    }
    if (imageTitle.trim().length > 50 || imageTitle.trim().length < 2) {
      Toast.show('请输入标题(2-50个字)')
      return
    }
    if (articleLength == 0) {
      Toast.show('请输入正文')
      return
    }
    if (articleLength > 5000) {
      Toast.show('正文字数不能超过5000字')
      return
    }
    clearTimeout(timer)
    const toastControl = Toast.show({
      icon: 'loading',
      content: '',
      duration: 0,
    })
    dispatch({
      type: 'graphicsText/editImgTextInfo',
      payload: {
        id: id || null,
        imageTextContent: imageTextContent,
        contentJson: contentJson,
        imageTitle: imageTitle.trim(),
        textImgList,
        imageType: 1,
        isForward: 0,
        saveType: 1,
      }
    }).then(res => {
      toastControl.close()
      const { code, content, msg } = res
      if (code == 200 && (content || content == 0)) {
        if (!id) {
          history.replace(`/CreateGraphicsText/CreateArticle?id=${content}`)
        }
        history.push(`/CreateGraphicsText/CreateArticle/Step2?id=${content}`)
      } else {
        Toast.show(msg || '数据加载失败')
      }
    }).catch(err => {})
  }

  const editImgTextInfo2 = () => {
    console.log('暂存暂存暂存暂存222222222')
    if (loadingSave2) {
      return
    }
    const articleLength = quillRef?.current?.unprivilegedEditor?.getText().replace(/\n/g, '').length
    const imageTextContent = quillRef?.current?.unprivilegedEditor?.getHTML()
    const contentJson = JSON.stringify(quillRef?.current?.unprivilegedEditor?.getContents())

    if (articleLength > 5000) {
      Toast.show('正文字数不能超过5000字，保存失败')
      return
    }

    setLoadingSave2(true)
    dispatch({
      type: 'graphicsText/editImgTextInfo',
      payload: {
        id: id || null,
        imageTextContent: imageTextContent,
        contentJson: contentJson,
        imageTitle: imageTitle && imageTitle.trim() || null,
        textImgList,
        imageType: 1,
        isForward: 0,
        saveType: 1,
      }
    }).then(res => {
      setLoadingSave2(false)
      const { code, content, msg } = res
      if (code == 200 && (content || content == 0)) {
        if (!id) {
          history.replace(`/CreateGraphicsText/CreateArticle?id=${content}`)
          setRefreshPageState(!refreshPageState)
        }
      } else {
        Toast.show(msg || '保存失败')
      }
    }).catch(err => {})
  }

  const getUploadVideoOrImageLoading = (isLoading) => {
    if (isLoading) {
      setLoadingUploadVideoOrImage(loadingUploadVideoOrImage + 1)
    } else {
      setLoadingUploadVideoOrImage(loadingUploadVideoOrImage - 1)
    }
  }

  const goBack = () => {
    if (history.length > 1) {
      history.goBack()
    } else {
      history.replace('/')
    }
  }

  const loadingImgTextInfoUpdateId = !!loading.effects['graphicsText/imgTextInfoUpdateId']

  return (
    <Spin spinning={loadingImgTextInfoUpdateId} wrapperClassName={styles.spin}>
      <NavBar
        title="发文章"
        LeftRender={() => (
          <div className={styles.nav_bar_left}>
            {
              id ? `${quillRef?.current?.unprivilegedEditor?.getText().replace(/\n/g, '').length}字/${loadingSave2 ? '保存中' : '草稿已保存'}`
                : '草稿将自动保存'
            }
          </div>
        )}
        RightRender={() => <span className={classNames(styles.nav_bar_btn, {
          [styles.disabled]: !imageTitle || quillRef?.current?.unprivilegedEditor?.getText().replace(/\n/g, '').length == 0,
        })} onClick={() => beforeSubmit(!imageTitle || quillRef?.current?.unprivilegedEditor?.getText().replace(/\n/g, '').length == 0)}>下一步</span>}
        bordered
      />

      <div className={classNames(styles.container, {[styles.show_panel]: toolbarPanelVisible})} id="content">
        <div className={styles.title_input_box}>
          <TextArea
            autoSize={true}
            rows={1}
            placeholder="请输入标题(2-50个字)"
            value={imageTitle}
            onChange={titleOnChange}
            maxLength={50}
          />
        </div>

        <div className={styles.editor_box}>
          <QuillDom
            deviceType="mobile"
            ref={quillRef}
            getQuillHistoryStack={getQuillHistoryStack}
            getQuillFormat={getQuillFormat}
            onChangeImageTextContent={onChangeImageTextContent}
          />
        </div>
      </div>

      <ToolbarMobile
        quillRef={quillRef}
        quillHistoryStack={quillHistoryStack}
        quillFormat={quillFormat}
        toolbarPanelOnChange={toolbarPanelOnChange}
        getUploadVideoOrImageLoading={getUploadVideoOrImageLoading}
      />
    </Spin>
  )
}

export default connect(({ graphicsText, loading }: any) => ({ graphicsText, loading }))(Index)
