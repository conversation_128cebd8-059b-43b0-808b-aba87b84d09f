/**
 * @Description: 搜索组件
 */
import React from 'react'
import { history } from 'umi'
import classNames from 'classnames'
import { page_search } from '../../utils/index'
import { backInApp, getIsFirstPageInApp, getOperatingEnv } from '@/utils/utils'
import styles from './index.less'

interface PropsType {
}

const Index: React.FC<PropsType> = (props: PropsType) => {

  // 跳转搜索页
  const goToUrl = (e) => {
    e.stopPropagation()
    e.preventDefault()
    history.push('/Home/Search')

    // 解决ios中搜索页input框不能自动聚焦问题
    document.getElementById('input_ios_focus') && document.getElementById('input_ios_focus').focus()

    // 首页，搜索词搜索次数排行，记录搜索词
    page_search('')
  }

  return (
    <div className={classNames(styles.search_container, {
      [styles.in_jws]: getOperatingEnv == '6'
    })}>
      {/* Friday APP中返回按钮 */}
      {
        getIsFirstPageInApp() && <i className={styles.back_icon} onClick={backInApp}></i>
      }
      <div className={styles.box} onClick={goToUrl}>搜索直播、会议、王国、课程、病例、医生</div>
      <div className={styles.text}>探索星球</div>
    </div>
  )
}

export default Index
