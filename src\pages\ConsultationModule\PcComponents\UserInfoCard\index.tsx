/**
 * @Description: PC端专家(用户)信息卡片
 * @author: 赵斐
 */
import React from 'react';
import { randomColor, processNames } from '@/utils/utils'
import styles from './index.less'
interface PropsType {
  isOrderShow?: boolean,  // 是否显示订单信息
  data: any,              // 用户数据
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { isOrderShow = false, data } = props
  const {
    h5BaseUserDto,      // 专家、用户信息
    orderNumber,        // 指导订单
    createDate,         // 创建时间
    thisUserIsExperts,  // 当前人是否是专家：0:否，1:是
    createUserId,       // 普通用户id
    createUserHeadUrlShow,       // 普通用户头像
    createUserName,     // 普通用户名称
    consultationType,   // 指导类型 1 图文2视频3正畸
  } = data || {}
  const {
    id,                  // 用户ID
    name,                // 姓名
    depSubjectDictName,  // 科室字典名称
    abilityLevelDictName,// 能力等级字典名称
    postTitleDictName,   // 职称字典名称
    organizationName,    // 机构名称
    headUrlShow,         // 头像展示路径
    isExperts,           // 是否是专家 1是 0 否
  } = h5BaseUserDto || {}
  return (
    <div className={styles.wrap}>
      {
        thisUserIsExperts == 1 ?<div className={styles.doctor_img}>
          {
            createUserHeadUrlShow ? <img className={styles.doctor_pic} src={createUserHeadUrlShow} alt='头像' /> :
              <div className={styles.no_doctor_pic} style={{ background: randomColor(createUserId) }}>{processNames(createUserName)}</div>
          }
        </div> : <div className={styles.doctor_img}>
          {
            headUrlShow ? <img className={styles.doctor_pic} src={headUrlShow} alt='头像' /> :
              <div className={styles.no_doctor_pic} style={{ background: randomColor(id) }}>{processNames(name)}</div>
          }
        </div>
      }

      <div className={styles.content_left}>
        {
          thisUserIsExperts == 0 ? <>
            {
              isExperts == 0 ? <div className={styles.name}>{name}</div>
                : <>
                  <p className={styles.doctor_content_top}>
                    <span className={styles.doctor_name}>{name}</span>
                    <span className={styles.doctor_rank}>{postTitleDictName}</span>
                    <span className={styles.doctor_grade}>{depSubjectDictName}·{abilityLevelDictName}</span>
                  </p>
                  <p className={styles.doctor_content_bottom}>{organizationName}</p>
                </>
            }
          </> : null
        }
        {
          thisUserIsExperts == 1 ? <div className={styles.name}>{createUserName}</div> : null
        }
      </div>
      {
        isOrderShow ? <div className={styles.content_right}>
          <p>{orderNumber}：订单号</p>
          <p>{createDate}：创建时间</p>
        </div> : null
      }

    </div>
  )
}
export default Index
