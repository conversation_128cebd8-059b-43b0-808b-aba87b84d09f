import React from 'react';
import { processNames, randomColor } from '@/utils/utils';
import { Button, Modal } from 'antd';
import linkBgIcon from '@/assets/GlobalImg/link_bg.png';
import linkIcon from '@/assets/GlobalImg/link.png';
import whiteCloseIcon from '@/assets/GlobalImg/white_close.png';
import styles from './index.less';

interface PropsType {
  visible: boolean;                    // 弹框是否显示
  previewModalType: number;            // 弹窗类型
  imageTitle: string;                  // 标题
  outerChainPaperwork: string;         // 内容
  coverImageUrlShow: string,           // 图片地址
  onCancel: () => {};
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')        // 用户信息
  const { visible, previewModalType, imageTitle, outerChainPaperwork, coverImageUrlShow } = props

  return (
    <Modal
      title="推荐流预览"
      open={visible}
      footer={null}
      closable={false}
      maskClosable={false}
      destroyOnClose={true}
      centered={true}
      className={styles.modal}
      onCancel={props.onCancel}
    >
      <div className={styles.close} onClick={props.onCancel}>
        <img src={whiteCloseIcon} alt="" width={32} height={32} />
      </div>
      {
        previewModalType === 1 ?
          <div className={styles.preview_bg}>
            <div className={styles.box}>
              <div className={styles.header}>
                <div
                  className={styles.left_picture}
                  style={UserInfo.headUrl ? {backgroundImage: `url(${UserInfo.headUrl})`} : {background: randomColor(UserInfo.friUserId)}}
                >
                  {UserInfo.headUrl ? '' : processNames(UserInfo.name)}
                </div>
                <div className={styles.right_text}>
                  <div className={styles.name}>{UserInfo.name}</div>
                  {/*<div className={styles.text}>广告</div>*/}
                </div>
              </div>
              <div className={styles.content}>
                <div className={styles.title}>{imageTitle}</div>
                <div className={styles.no_picture_box}>
                  <div className={styles.init_img}>
                    <img src={linkBgIcon} alt="" />
                  </div>
                  <div className={styles.link_content}>
                    <div className={styles.link_title}>{imageTitle}</div>
                    <div className={styles.link_more}>
                      <img src={linkIcon} alt="" />
                      {outerChainPaperwork}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          :
          <div className={styles.preview_no_bg}>
            <div className={styles.box}>
              <div className={styles.header}>
                <div
                  className={styles.left_picture}
                  style={UserInfo.headUrl ? {backgroundImage: `url(${UserInfo.headUrl})`} : {background: randomColor(UserInfo.friUserId)}}
                >
                  {UserInfo.headUrl ? '' : processNames(UserInfo.name)}
                </div>
                <div className={styles.right_text}>
                  <div className={styles.name}>{UserInfo.name}</div>
                  {/*<div className={styles.text}>广告</div>*/}
                </div>
              </div>
              <div className={styles.title}>{imageTitle}</div>
              <div className={styles.picture_box}>
                <div className={styles.img} style={coverImageUrlShow ? {backgroundImage: `url(${coverImageUrlShow})`} : {}}></div>
                <div className={styles.link_more}>
                  <img src={linkIcon} alt="" />
                  {outerChainPaperwork}
                </div>
              </div>
            </div>
          </div>
      }
      <Button type="primary" onClick={props.onCancel}>关闭预览</Button>
    </Modal>
  )
}
export default Index
