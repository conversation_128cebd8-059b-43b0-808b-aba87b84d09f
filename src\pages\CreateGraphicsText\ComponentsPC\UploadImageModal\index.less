.modal {
  :global {
    .ant-modal-body {
      padding: 0;
    }
    .ant-btn-lg {
      padding-left: 20px;
      padding-right: 20px;
      height: 38px;
    }
  }
}
.content {
  padding-left: 32px;
  :global {
    .ant-tabs {
      color: #000;
    }
    .ant-tabs-top > .ant-tabs-nav::before {
      display: none;
    }
    .ant-tabs-tab {
      font-size: 20px;
      padding: 21px 0 8px;
    }
    .ant-tabs-tab + .ant-tabs-tab {
      margin-left: 40px;
    }
    .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
      color: #000;
      text-shadow: none;
    }
    .ant-tabs-top > .ant-tabs-nav {
      margin-bottom: 0;
    }
    .ant-tabs-content {
      padding-top: 24px;
      padding-bottom: 0;
      padding-right: 24px;
      height: 598px;
      overflow-y: auto;
    }
  }
  .article_tab_title {
    font-size: 14px;
    color: #999;
    margin-bottom: 24px;
  }
  .article_img_box {
    display: flex;
    flex-wrap: wrap;
    column-gap: 20px;
    row-gap: 20px;
    padding-bottom: 20px;
    .article_img_item {
      position: relative;
      width: 140px;
      height: 140px;
      border-radius: 4px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      cursor: pointer;
      & > span {
        position: absolute;
        left: 12px;
        top: 12px;
        width: 30px;
        height: 30px;
        text-align: center;
        line-height: 32px;
        color: #fff;
        background: #0095FF;
        border-radius: 50%;
        display: none;
        z-index: 2;
      }
      &.selected::after {
        content: "";
        display: block;
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        right: 0;
        background: rgba(0,0,0,0.4);
        z-index: 1;
      }
      &.selected > span {
        display: block;
      }
    }
  }
  // 上传图片tab
  .upload_btn {
    display: flex;
    justify-content: center;
    padding-bottom: 20px;
    padding-top: 205px;
    &.not_empty {
      padding-top: 8px;
    }
  }
  .upload_text {
    font-size: 14px;
    color: #999;
    text-align: center;
  }
  .upload_list_title {
    padding-top: 28px;
    font-size: 14px;
    color: #666;
  }
  .upload_img_box {
    display: flex;
    flex-wrap: wrap;
    column-gap: 20px;
    row-gap: 20px;
    padding-top: 24px;
    padding-bottom: 20px;
    .upload_img_item {
      position: relative;
      width: 140px;
      height: 140px;
      border-radius: 4px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      :global {
        .anticon {
          color: rgba(0,0,0,0.5);
          font-size: 20px;
          position: absolute;
          top: -6px;
          right: -6px;
          z-index: 2;
          cursor: pointer;
          display: none;
        }
        //.ant-progress-line {
        //  position: relative;
        //  width: 103px;
        //  z-index: 2;
        //}
        //.ant-progress-inner {
        //  background: #fff;
        //}
      }
      &:hover {
        :global {
          .anticon {
            display: block;
          }
        }
      }
      //&::after {
      //  content: "";
      //  display: block;
      //  position: absolute;
      //  left: 0;
      //  top: 0;
      //  bottom: 0;
      //  right: 0;
      //  background: rgba(0,0,0,0.4);
      //  z-index: 1;
      //}
    }
  }
}
.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #ddd;
  padding: 21px 24px 21px 33px;
  :global {
    .ant-btn + .ant-btn {
      margin-left: 24px;
    }
  }
  .footer_left {
    font-size: 16px;
    color: #999;
  }
}
