.titleInfo {
  width: 100%;
  .lines {
    width: 48px;
    height: 4px;
    background: #D0D4D7;
    border-radius: 4px;
    margin: 0 auto;
    margin-top: 12px;
  }

  .titleWarp {
    display: flex;
    align-items: center;
    padding: 0 16px;
    box-sizing: border-box;
    justify-content: center;
    position: relative;

    .titleBackIcon {
      position: absolute;
      top: 0;
      left: 16px;

      img {
        width: 12px;
        height: auto;
      }
    }

    .titleText {
      font-size: 17px;
      font-weight: 500;
      color: #000000;
      line-height: 24px;
    }
  }
}

.tabs_box {
  width: 100%;
  display: flex;
  padding-top: 16px;
  padding-left: 16px;
  box-sizing: border-box;

  .tabs_item {
    font-size: 15px;
    font-weight: 500;
    color: #999999;
    line-height: 21px;
    margin-right: 16px;
    margin-bottom: 6px;
    &.checked {
      color: #000000;
      font-weight: 600;
      position: relative;

      &::after {
        content: '';
        width: 12px;
        height: 3px;
        background: #000000;
        border-radius: 6px 6px 6px 6px;
        position: absolute;
        left: 50%;
        top: 103%;
        transform: translateX(-50%);
      }
    }
  }
  .tabs_item + .tabs_item {
    margin-left: 8px;
  }
}

.list_wrap {
  width: 100%;
  height: calc(70vh - 180px);
  overflow-y: auto;
  padding: 16px;
}

.data_box {
  width: 100%;

  .data_item {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    margin-bottom: 16px;
    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 2px;
      margin-right: 8px;

      .no_comment_head{
        width: 32px;
        height: 32px;
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 500;
        color: #fff;
        white-space: nowrap;
      }

      img {
        width: 32px;
        height: 32px;
        border-radius: 2px;
      }
    }
    .data_name {
      flex: 1;
      font-size: 14px;
      color: #000;
      line-height: 20px;
      word-break: break-all;
    }
    .data_btn {
      height: 29px;
      line-height: 29px;
      text-align: center;
      padding: 0 8px;
      min-width: 48px;
      border-radius: 18px;
      font-size: 12px;
      color: #0095FF;
      background: #E6F4FF;
      white-space: nowrap;
      flex-shrink: 0;
      &.checked {
        color: #999;
        background: #F5F5F5;
      }
    }
  }
}

.fixed_box {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding-bottom: constant(safe-area-inset-bottom);//兼容 IOS<11.2
  padding-bottom: env(safe-area-inset-bottom);//兼容 IOS>11.2
  .btn_box {
    padding: 0 16px 8px;
    .btn {
      height: 40px;
      line-height: 40px;
      background: #0095FF;
      border-radius: 20px;
      text-align: center;
      font-size: 16px;
      color: #fff;
    }
  }
}
