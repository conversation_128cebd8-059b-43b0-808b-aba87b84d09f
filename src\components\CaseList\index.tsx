/**
 * @Description: 病例组件
 */
import React from 'react'
import { history } from 'umi'
import classNames from 'classnames'
import { case_click, group_click } from '@/pages/Home/utils'
import styles from './index.less'

interface PropsType {
  componentData: any,                                      // 组件数据，格式{ dataList: [] }
  isShowImage?: any,                                       // 是否展示图片，true 展示，false 不展示
  style?: any,                                             // 样式
  isHomePage?: any,                                         // 是否为首页，1是，0否
  moduleIndex?: any,                                        // 当前组件在所有病例组件中的索引
  isClassifyGuide?: any,                                   // 是否在分类导航组件中，1是
  classifyGuideTabIndex?: any,                             // 在分类导航组件中时，选中的tab标签
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { componentData, style = {}, isHomePage, moduleIndex, isClassifyGuide, classifyGuideTabIndex } = props
  const dataList = componentData.dataList || []

  // 点击病例跳转病例详情
  const goToUrl = (item, index) => {
    history.push({
      pathname: '/Case/CaseDetails',
      query: {
        excellentCaseId: item.id,                          // 病例ID
      }
    })

    // 首页友盟统计，isHomePage=1
    if (isHomePage != 1) {
      return
    }
    // 在分类导航组件中
    if (isClassifyGuide == 1) {
      // 首页，分类组件点击量
      group_click(moduleIndex, `第${moduleIndex}个分类组件，第${classifyGuideTabIndex}个标签，第${index + 1}个病例`)
      return
    }
    // 首页，病例点击量
    case_click(moduleIndex, index + 1)
  }

  return (
    <div className={styles.case_container} style={style}>
      {
        dataList.map((item, index) => (
          <div key={`${item.id}${index}`} className={styles.item_box} onClick={() => goToUrl(item, index)}>
            {/* 主题 */}
            {
              item.topicName &&
              <h5 className={styles.topic} dangerouslySetInnerHTML={{__html: item.topicName}}></h5>
            }

            {/* 难度等级和学科和病例成就 */}
            {
              ((item.depSubjectDictNameList && item.depSubjectDictNameList.length > 0) || item.achievementDictName) &&
              <div className={styles.subject_box}>
                {
                  item.difficultLevelDict &&
                  <span className={styles[`difficult${item.difficultLevelDict}`]}>难度{item.difficultLevelDictName}</span>
                }
                {
                  item.depSubjectDictNameList && item.depSubjectDictNameList.length > 0 &&
                  item.depSubjectDictNameList.map((itemChild, indexChild) =>
                    <span key={indexChild}>{itemChild}</span>
                  )
                }
                {
                  item.achievementDictName &&
                  <span className={styles.achievement}>{item.achievementDictName}</span>
                }
              </div>
            }

            {/* 主诊医师 */}
            {
              item.doctorUserList && item.doctorUserList.length > 0 &&
              <div className={styles.doctor_box}>
                <div className={styles.label}>主诊：</div>
                <div className={styles.value}>{item.doctorUserList.join('、')}</div>
              </div>
            }

            {/* 病例问题 */}
            {
              item.keyWordList && item.keyWordList.length > 0 &&
              <div className={styles.keywords_box}>
                <div className={styles.label}>病例问题：</div>
                <div className={styles.value_box}>
                  {
                    item.keyWordList.map((itemChild, indexChild) =>
                      <span key={indexChild} dangerouslySetInnerHTML={{__html: itemChild}}></span>
                    )
                  }
                </div>
              </div>
            }

            {/* 封面图（首页里的） */}
            {
              item.coverPathUrlList && item.coverPathUrlList.length > 0 &&
              <div className={classNames(styles.cover_img_box, {
                [styles.cover_img_line3]: item.coverPathUrlList.length > 6,
                [styles[`cover_img_length${item.coverPathUrlList.length}`]]: true,
              })}>
                {
                  item.coverPathUrlList.map((itemChild, indexChild) => (
                    <div key={itemChild.fileUrl} className={styles.cover_img} style={{backgroundImage: `url(${itemChild.fileUrlView})`}}>
                      {/*<img src={itemChild.fileUrlView} alt=""/>*/}
                    </div>
                  ))
                }
              </div>
            }

            {/* 封面图 */}
            {
              props.isShowImage && item.coverPathUrlShowList && item.coverPathUrlShowList.length > 0 &&
              <div className={classNames(styles.cover_img_box, {
                [styles.cover_img_line3]: item.coverPathUrlShowList.length > 6,
                [styles[`cover_img_length${item.coverPathUrlShowList.length}`]]: true,
              })}>
                {
                  item.coverPathUrlShowList.map((itemChild, indexChild) => (
                    <div key={indexChild} className={styles.cover_img} style={{backgroundImage: `url(${itemChild})`}}>
                      {/*<img src={itemChild} alt=""/>*/}
                    </div>
                  ))
                }
              </div>
            }

            {/* 治疗方案 */}
            {
              item.solutionAll &&
              <div className={styles.solution_box}>
                <i className={styles.solution_icon}></i>
                <div className={styles.solution_text} dangerouslySetInnerHTML={{__html: item.solutionAll}}></div>
              </div>
            }

            {/* 评论 */}
            <div className={styles.comments}>{item.commentsCount}评论 · {item.excellentCasePV}学习</div>
          </div>
        ))
      }
    </div>
  )
}

export default Index
