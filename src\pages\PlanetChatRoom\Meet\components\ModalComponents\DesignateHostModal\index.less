.modal {
  :global {
    .adm-modal-body:not(.adm-modal-with-image) {
      padding-top: 24PX;
    }
    .adm-modal-content {
      padding: 0;
    }
  }
}

// 标题
.header {
  font-size: 17PX;
  color: #000;
  font-weight: 500;
  line-height: 24PX;
  text-align: center;
}

// 用户列表
.scroll_wrap {
  height: 184PX;
  overflow-y: auto;
  padding: 16PX 0 8PX;
  .item {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: space-between;
    padding: 8PX 16PX;
    &:active {
      background: #eee;
    }
    .item_left {
      flex: 1;
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      overflow: hidden;
      .avatar_wrap {
        width: 24PX;
        height: 24PX;
        flex-shrink: 0;
        margin-right: 12PX;
      }
      .user_name {
        flex: 1;
        font-size: 14PX;
        color: #000;
        height: 24PX;
        line-height: 23PX;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    & > img {
      flex-shrink: 0;
    }
  }
}

// 按钮
.btn_wrap {
  padding: 8PX 20PX 16PX;
  .btn {
    border-radius: 20PX;
    height: 33PX;
    line-height: 33PX;
    background: #0095FF;
    color: #fff;
    text-align: center;
    font-size: 15PX;
  }
}
