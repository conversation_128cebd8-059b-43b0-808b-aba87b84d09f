@import "~react-quill/dist/quill.core.css";

.container {
  :global {
    // 编辑器自带样式修改
    .ql-container {
      font-size: 16px;
    }
    .ql-editor {
      line-height: 1.55;
      color: #000;
      ::selection {
        color: inherit;
        background-color: rgba(80,160,255,.4);
      }
    }
    .ql-editor.ql-blank::before {
      left: 0;
      font-style: initial;
      color: #aaa;
    }
    // 自定义内容的样式
    // 话题
    .quill_topic_format_wrap {
      color: #0095FF;
      white-space: nowrap;
    }
    // 表情
    .quill_emoji_format_wrap {
      width: 24px;
      height: 24px;
    }
    // 用户
    .quill_user_format_wrap {
      color: #0095FF;
      white-space: nowrap;
    }
  }
}
// PC端样式
.container.pc {
  :global {
    .ql-editor {
      line-height: 1.55;
      min-height: 444px;
      padding: 24px 0 !important;
    }
    // 图片格式的样式
    .quill_image_format_wrap {
      display: flex;
      justify-content: center;
      cursor: default;
      padding: 8px 0;
      img {
        width: auto;
        height: auto;
        max-width: 300px;
        -webkit-user-drag: none;
      }
    }
    // 视频格式的样式
    .quill_video_format_wrap {
      display: flex;
      justify-content: center;
      cursor: default;
      padding: 8px 0;
      .video_wrap {
        width: 300px;
        height: 172px;
        background: #f5f6f8;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .video_icon {
          width: 32px;
          height: 32px;
          background: url("../../../assets/GlobalImg/frame.png") no-repeat center;
          background-size: 100% 100%;
        }
        .video_text {
          font-size: 16px;
          color: #999;
          margin-top: 8px;
          user-select: none;
          width: 100%;
          display: flex;
          flex-wrap: nowrap;
          justify-content: center;
          overflow: hidden;
          padding: 0 12px;
          .video_text_name {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .video_text_suffix {
            flex-shrink: 0;
          }
        }
      }
      .video_content_wrap {
        width: 100%;
        display: none;
        video {
          width: 100%;
          height: auto;
        }
      }
      .video_progress {
        width: 100%;
        height: 20px;
        background: #f0f6ff;
        .video_progress_inner {
          width: 10%;
          height: 100%;
          background: #1B9EFB;
        }
      }
    }
    // 进度条样式
    .quill_progress_format_wrap {
      display: flex;
      justify-content: center;
      cursor: default;
      padding: 8px 0;
      .progress_wrap {
        width: 320px;
        height: 210px;
        background: #FCFCFC;
        border-radius: 4px;
        border: 1px solid #ddd;
        .progress_container {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 16px;
          .progress_content {
            width: 100%;
            .progress_icon {
              height: 20px;
              margin-bottom: 8px;
              background: url("../../../assets/GlobalImg/toolbar_image.png") no-repeat center;
              background-size: 20px 20px;
            }
            .progress_info {
              width: 100%;
              display: flex;
              justify-content: space-between;
              flex-wrap: nowrap;
              font-size: 14px;
              color: #000;
              & > span:first-child {
                flex: 1;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
              & > span:last-child {
                flex-shrink: 0;
                white-space: nowrap;
              }
            }
            .progress_line {
              width: 100%;
              height: 4px;
              border-radius: 8px;
              background: #D8D8D8;
              .progress_line_inner {
                width: 0;
                height: 100%;
                border-radius: 8px;
                background: #0095FF;
                transition: all .3s;
              }
            }
          }
        }
      }
    }
  }
}

// 移动端样式
.container.mobile {
  :global {
    .ql-editor {
      line-height: 1.55;
      min-height: 300px;
      padding: 16px 0 120px;
    }
    // 图片格式的样式
    .quill_image_format_wrap {
      padding: 8px 0;
      img {
        width: 100%;
        height: auto;
      }
    }
    // 视频格式的样式（移动端目前没加上传视频）
    .quill_video_format_wrap {
      padding: 8px 0;
      .video_wrap {
        height: 200px;
        background: #f5f6f8;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .video_icon {
          width: 32px;
          height: 32px;
          background: url("../../../assets/GlobalImg/frame.png") no-repeat center;
          background-size: 100% 100%;
        }
        .video_text {
          font-size: 16px;
          color: #999;
          margin-top: 8px;
          user-select: none;
          width: 100%;
          display: flex;
          flex-wrap: nowrap;
          justify-content: center;
          overflow: hidden;
          padding: 0 12px;
          .video_text_name {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .video_text_suffix {
            flex-shrink: 0;
          }
        }
      }
      .video_content_wrap {
        width: 100%;
        display: none;
        video {
          width: 100%;
          height: auto;
        }
      }
    }
    // 进度条样式
    .quill_progress_format_wrap {
      padding: 8px 0;
      .progress_wrap {
        height: 210px;
        background: #FCFCFC;
        border-radius: 4px;
        border: 1px solid #ddd;
        .progress_container {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 16px;
          .progress_content {
            width: 100%;
            .progress_icon {
              height: 20px;
              margin-bottom: 8px;
              background: url("../../../assets/GlobalImg/toolbar_image.png") no-repeat center;
              background-size: 20px 20px;
            }
            .progress_info {
              width: 100%;
              display: flex;
              justify-content: space-between;
              flex-wrap: nowrap;
              font-size: 14px;
              color: #000;
              & > span:first-child {
                flex: 1;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
              & > span:last-child {
                flex-shrink: 0;
                white-space: nowrap;
              }
            }
            .progress_line {
              width: 100%;
              height: 4px;
              border-radius: 8px;
              background: #D8D8D8;
              .progress_line_inner {
                width: 0;
                height: 100%;
                border-radius: 8px;
                background: #0095FF;
                transition: all .3s;
              }
            }
          }
        }
      }
    }
  }
}

.topic_popover_ref {
  position: absolute;
  width: 0;
  height: 0;
  left: 0;
  top: 0;
  transform: translate(0px, 0px);
  z-index: 980;
  .format_pop {
    :global {
      .ant-popover-inner-content {
        padding: 0;
      }
    }
  }
}
