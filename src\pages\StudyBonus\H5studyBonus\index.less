.Mobile_Wrap {
  width: 100%;
  min-width: 285px;
  // height: 100vh;
  background: #F5F6F8;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  // padding-left: 12px;
  // padding-right: 12px;

  .Mobile_title_Wrap {
    width: 100%;
    height: 44px;
    margin-bottom: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #fff;

    .Mobile_title {
      font-size: 17px;
      font-weight: 500;
      color: #000000;
    }
  }
}

.tab_title_Warp {
  width: 100%;
  height: 100%;
  .StudyBonusBox {
    width: 100%;
    height: 150px;
    background: linear-gradient( 135deg, #FFF7EA 0%, #FFEEDC 100%), #FFFFFF;
    border-radius: 0px 0px 0px 0px;
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding-left: 16px;
    padding-right: 16px;
    padding-bottom: 16px;
    position: relative;

    .BackWarp {
      position: absolute;
      width: 50px;
      height: 50px;
      // background: #a6a6a6;
      top: 0px;
      left: 0px;
    }

    .StudyBonusBg {
      width: 79px;
      height: 65px;
      background: url('~@/assets/GlobalImg/StudyBonusBg.png');
      background-size: 100% 100%;
      position: absolute;
      right: 16px;
      bottom: 0px;
    }
  }

  .StudyBonusBox_num {
    font-weight: 500;
    font-size: 26px;
    color: #000000;
    line-height: 26px;
    margin-bottom: 8px;
  }

  .StudyBonusBox_date {
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    line-height: 12px;
    margin-bottom: 16px;
  }

}

.title_name {
  font-weight: 500;
  font-size: 17px;
  color: #000000;
  line-height: 17px;
  font-style: normal;
  text-transform: none;
  // margin-left: 16px;
  margin-bottom: 15px;
}


.content_box {
  padding-left: 16px;
  padding-right: 16px;
  padding-top: 12px;
  padding-bottom: 40px;
  width: 100%;
  height: calc(100vh - 170px);
  overflow: auto;
  background: #F5F6F8;

  .item_content {
    width: 100%;
    min-height: 74px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    padding: 12px 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .item_title_box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
  }

  .item_title_box_felx {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .item_title {
    width: 60%;
    font-size: 14px;
    color: #000000;
    line-height: 16px;
    font-weight: 600;
    overflow: hidden;
    display: -webkit-box;
    // -webkit-line-clamp: 2;
    // -webkit-box-orient: vertical;
    // text-overflow: ellipsis;
    white-space: normal;
  }

  .item_title_num {
    font-size: 14px;
    color: #000000;
    line-height: 16px;
    font-weight: 600;
  }

  .item_time {
    font-weight: 400;
    font-size: 11px;
    color: #999999;
    line-height: 11px;
  }

  .btn_box {
    font-weight: 400;
    font-size: 14px;
    color: #0095FF;
    line-height: 14px;
    display: flex;
    align-items: center;
  }

  .btn_icon {
    width: 12px;
    height: 12px;
    background: url('~@/assets/GlobalImg/BalanceStudyFund_DetailBox_icon.png');
    background-size: 100% 100%;
    display: inline-block;
  }

}

.scroll_box {
  width: 100%;
}

.loadingByInfiniteScroll {
  width: 100%;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #F5F6F8;

  .loadingByInfiniteScrollContent {
    display: flex;
  }
}

.nav_bar_icon {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 36px;
  background: url("~@/assets/GlobalImg/go_back.png") no-repeat 16px center;
  background-size: 12px 24px;
  &.icon_white {
    background: url("~@/assets/GlobalImg/go_back_white.png") no-repeat 16px center;
    background-size: 12px 24px;
  }
}



.footer {
  padding: 16px;
  text-align: center;
}

.footerButton {
  font-size: var(--adm-font-size-4);
  color: #ffffff;
  line-height: 1;
  padding: 10px 16px;
  background-color: rgba(153, 153, 153, 0.4);
  border-radius: 100px;
  display: inline-block;
  cursor: pointer;
}


