/**
 * @Description: H5-病例详情
 * @author: 赵斐
 */
import React, { useEffect, useState, lazy, Suspense } from 'react';
import { connect, history } from "umi";
import styles from './index.less';
import classNames from 'classnames';
import { Ta<PERSON>, <PERSON>Area, Popup, Toast, Mask } from 'antd-mobile'
import { Spin } from 'antd'
import { WxAppIdByPublicAccount, getOperatingEnv, getShareUrl } from '@/utils/utils'
import wordArtImg from '@/assets/GlobalImg/word_art.png'; // 治疗方案图标
import lLinesIcon from '@/assets/Case/l_lines.png'; // vip会员左线小图标
import rLinesIcon from '@/assets/Case/r_lines.png'; // vip会员右线小图标
import yellowArrowIcon from '@/assets/Case/yellow_arrow.png'; // 黄色右箭头小图标
import vipIcon1 from '@/assets/Case/vip_1.png'; // vip会员小图标1
import vipIcon2 from '@/assets/Case/vip_2.png'; // vip会员小图标2
import vipIcon3 from '@/assets/Case/vip_3.png'; // vip会员小图标3
import vipIcon4 from '@/assets/Case/vip_4.png'; // vip会员小图标4
import vipIcon5 from '@/assets/Case/vip_5.png'; // vip会员小图标5
import vipIcon6 from '@/assets/Case/vip_6.png'; // vip会员小图标6
import noDataImg from '@/assets/GlobalImg/no_data.png'; // 无网络图片
import noCollectIcon from '@/assets/Case/no_collect.png'; // 未收藏小图标
import collectIcon from '@/assets/Case/collect.png'; // 收藏后小图标
import wxIcon from '@/assets/Case/share_wx.png'; // 分享小图标
import showIcon from '@/assets/Case/show_icon.png'; // 展开小图标
import closeIcon from '@/assets/Case/close_icon.png'; // 关闭小图标
// 公共组件(检查、计划、过程、结果、结论)
// import CasesDetailsContent from '../Components/CasesDetailsContent';
const CasesDetailsContent = lazy(() => import('../Components/CasesDetailsContent'))
// 数据加载异常
import LoadingException from '@/components/LoadingException'
// 评论列表数据
// import CommentList from '../Components/CommentList'
const CommentList = lazy(() => import('../Components/CommentList'))
import ShareModal from '../Components/ShareModal'    // 分享弹窗

// 导航组件
import NavBar from '@/components/NavBar'
// 开通会员列表展示数据

const vipDeblockingList = [
  { icon: vipIcon1, text: '会员病例免费看' },
  { icon: vipIcon2, text: '会员课程免费看' },
  { icon: vipIcon3, text: '研讨视频免费看' },
  { icon: vipIcon4, text: '付费课程折扣看' },
  { icon: vipIcon5, text: '参与病例研讨会' },
  { icon: vipIcon6, text: '参加集团年度峰会' },
]

const tabItems = [
  { key: '1', title: '基本信息' },
  { key: '2', title: '检查及诊断' },
  { key: '3', title: '计划' },
  { key: '4', title: '过程' },
  { key: '5', title: '结论' },
]
// 优秀病例初始化数据
const initState = {
  dataSource: null,           // 优秀病例详情数据
  interfaceTwoStatus: null,  // 优秀病历数据数据加载失败
}

// 评论\发布交互初始化数据
const initCommentData = {
  isCommentOpen: false,      // 评论弹窗状态
  commentValue: "",           // 评论内容
  commentsSuperId: "",       // 评论ID
  commentsSuperUserId: "",   // 回复上级用户ID
  commentsUserName: "",        // 回复用户 姓名
  pageScrollTop: 0,           // 打开弹窗滚动距离
}
const Index: React.FC = (props: any) => {
  const { dispatch, loading } = props;
  const { query } = history.location || {}
  const { excellentCaseId } = query || {}                             // 优秀病例ID
  const [state, setState] = useState<any>(initState)                  // 优秀病例相关数据
  const [casesCommentData, setCasesCommentData] = useState([])        // 获取评论信息
  const [activeKey, setActiveKey] = useState('1')                     // 楼层跳选中
  const [isShowBannerWindow, setIsShowBannerWindow] = useState(true); // 广告浮窗状态
  const [interfaceStatus, setInterfaceStatus] = useState(0);          // 接口状态  0 暂无数据 1 数据加载失败
  const [commentData, setCommentData] = useState(initCommentData);    // 评论\发布交互所需数据
  const [shareModalVisible, setShareModalVisible] = useState(false)    // 分享弹窗是否显示
  const {
    dataSource
  } = state;

  const {
    topicName,              // 主题
    difficultLevelDict,     // 难度等级
    depSubjectDictNameList, // 学科字典(多值)
    achievementDictName,    // 病历成就名称
    doctorUserList,         // 主诊医生(多个)
    keyWordList,            // 关键词
    sex,                    // 性别 1男 2女
    age,                    // 年龄
    chiefComplaint,         // 主诉
    presentDisease,         // 现病史
    previousHistory,        // 既往史
    wholeHealth,            // 全身健康情况
    checkList,              // 检查
    diagnosisList,          // 诊断
    treatmentPlanList,      // 治疗计划
    treatmentProcessList,   // 治疗过程
    summaryDiscussList,     // 总结与讨论
    collectCount,           // 收藏数量
    isCollect,              // 是否收藏 0未收藏 1已收藏
    solution,               // 治疗方案
    isAllLook,              // 是否可以全文查看 1可以 0不可以
    caseLinkTypeDict,       // 小鹅通资源类型
    floatImgShowUrl,        // 病例浮窗图片展示链接
    floatPathUrl,           // 病例浮窗链接路径
  } = dataSource || {};
  const {
    isCommentOpen,      // 评论弹窗状态
    commentValue,           // 评论内容
    commentsSuperId,       // 评论ID
    commentsSuperUserId,   // 回复上级用户ID
    commentsUserName,        // 回复用户 姓名
    pageScrollTop,
  } = commentData || {}

  useEffect(() => {
    window.scrollTo({ top: 0 })
    window.addEventListener('scroll', handleScroll)
    getExcellentCaseInfo()
    getMaExcellentCaseCommentsList()
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  useEffect(() => {
    if (dataSource) {
      onShareAppMessage()
    }
  }, [dataSource])

  useEffect(() => {
    if (!isCommentOpen) {
      window.scrollTo({ top: pageScrollTop })
    }
  }, [isCommentOpen]);

  // 获取优秀病例详情数据
  const getExcellentCaseInfo = () => {
    dispatch({
      type: "cases/getExcellentCaseInfo",
      payload: {
        excellentCaseId
      }
    }).then((res: any) => {
      let { code, content } = res || {};
      if (code == 200) {
        if (content) {
          setState({
            ...state,
            dataSource: content,
          })
        } else {
          setInterfaceStatus(2)
        }
      } else {
        setInterfaceStatus(1)
      }
    }).catch((err: string) => {
      console.log(err);

    })
  }

  // 获取评论信息数据
  const getMaExcellentCaseCommentsList = () => {
    dispatch({
      type: "cases/getMaExcellentCaseCommentsList",
      payload: {
        excellentCaseId
      }
    }).then((res: any) => {
      let { code, content } = res || {};
      if (code == 200) {
        if (Array.isArray(content) && content.length == 0) {
          setInterfaceStatus(2)
          return
        }
        setCasesCommentData(content)
      } else {
        setInterfaceStatus(1)
      }
    }).catch((err: string) => {
      setInterfaceStatus(1)
    })
  }

  //  开通vip-跳转vip会员宣传页
  const jumpVipLeaflets = () => {
    history.push("/Payment/MemberBenefitsPage")
  }

  /**
   * 回复评论
   * @param e                     e事件
   * @param commentsSuperId       评论ID
   * @param commentsSuperUserId   回复上级用户ID
   * @param commentsUserName      回复用户 姓名
   */
  const onClickReplyFun = (e: any, commentsSuperId: string, commentsSuperUserId: string, commentsUserName: string) => {
    e.stopPropagation()

    // 没登录跳转登录页
    if (!localStorage.getItem('access_token')) {
      history.push({
        pathname: '/User/login',
        query: {
          redirectByPush: window.location.href,
        }
      })
      return
    }


    setCommentData({
      ...commentData,
      isCommentOpen: true,    // 评论弹窗打开
      commentsSuperId,       // 评论ID
      commentsSuperUserId,   // 回复上级用户ID
      commentsUserName,        // 回复用户 姓名
    })
  }


  // 点击发布
  const onReleaseConfirm = () => {
    const UerInfo = JSON.parse(localStorage.getItem('userInfo')) || {};
    let commentsUserId = UerInfo && UerInfo.friUserId;
    let params = {};
    if (commentsSuperId) {
      params = {
        excellentCaseId,            // 病历案列ID
        excellentCaseType: 0,       // 病历类型 0病历评论
        commentsContent: commentValue.trim(),         // 评论/回复内容
        commentsUserId: commentsUserId,         // 评论/回复用户ID
        commentsSuperId,            // 评论/回复上级ID
        commentsSuperUserId,         // 回复上级用户ID
        commentsType: 1,              // 评论类型 0评论 1回复
      }
    } else {
      params = {
        excellentCaseId,            // 病历案列ID
        excellentCaseType: 0,       // 病历类型 0病历评论
        commentsContent: commentValue.trim(),         // 评论/回复内容
        commentsUserId: commentsUserId,         // 评论/回复用户ID
        commentsType: 0,              // 评论类型 0评论 1回复
      }
    }
    if (!commentValue.trim()) {
      Toast.show({
        content: '请正确输入评论信息～',
        duration: 2000
      })
      return
    }
    if (!(/^[~\s!！@#$￥%^…&*()_—+{}：:‘’“”《》？"|<>?`\-=[\];',.。，；（）、a-zA-Z0-9\u4e00-\u9fa5/]+$/.test(commentValue.trim()))) {
      Toast.show({
        content: '请正确输入评论信息～',
        duration: 2000
      })
      setCommentData({
        ...commentData,
        isCommentOpen: true,     // 评论弹窗状态
      })
      return
    }
    if (commentValue.length > 500) {
      Toast.show({
        content: '评论内容过长～',
        duration: 2000
      })
      return
    }
    dispatch({
      type: "cases/saveCaseComments",
      payload: {
        ...params
      }
    }).then((res: any) => {
      let { code } = res || {};
      if (code == 200) {
        setCommentData(initCommentData)
        getMaExcellentCaseCommentsList()
      }
    }).catch((err: string) => {
      console.log(err);
    })
  }

  /**
   * 评论输入内容
   * @param val
   */
  const onChangeInput = (val: string) => {
    setCommentData({
      ...commentData,
      commentValue: val,     // 评论内容
    })

  }

  // 评论输入框失去焦点
  const onBindBlur = () => {
    if (!commentValue.trim()) {
      return
    }
    setCommentData({
      ...commentData,
      isCommentOpen: false,     // 评论弹窗状态
    })
  }

  // 评论弹窗，点击蒙层关闭
  const onMaskClick = () => {
    setCommentData({
      ...commentData,
      isCommentOpen: false,     // 评论弹窗状态
    })
  }



  // 点击评论（需要登录才能操作）
  const onSayClickSomethehing = () => {
    // 没登录跳转登录页
    if (!localStorage.getItem('access_token')) {
      history.push({
        pathname: '/User/login',
        query: {
          redirectByPush: window.location.href,
        }
      })
      return
    }

    setCommentData({
      ...commentData,
      isCommentOpen: true,     // 评论弹窗状态
      pageScrollTop: document.documentElement.scrollTop || document.body.scrollTop
    })
  }

  // 分享弹窗打开
  const shareModalShow = () => {
    setShareModalVisible(true)
  }

  // 关闭分享弹窗
  const shareModalHide = () => {
    setShareModalVisible(false)
  }

  // 分享病例
  const onShareAppMessage = () => {
    const url = window.location.href
    const shareUrl = getShareUrl(url)
    console.log('shareUrl：', shareUrl)
    dispatch({
      type: 'userInfoStore/getJsapiTicket',
      payload: {
        currentUrl: url,
        appId: WxAppIdByPublicAccount,
      },
    }).then((res: any) => {
      if (res && res.code == 200) {
        wx.config({
          debug: false,
          appId: res.content.appId,
          timestamp: res.content.timestamp,
          nonceStr: res.content.nonceStr,
          signature: res.content.signature,
          jsApiList: [
            'updateAppMessageShareData',
            'updateTimelineShareData',
            'onMenuShareTimeline',
            'onMenuShareAppMessage',
            'onMenuShareQQ',
            'onMenuShareWeibo',
            'onMenuShareQZone',
          ],
        })
        wx.ready(() => {
          const shareDate = {
            title: '【FRIDAY医生星球】牙医都来这里学习和交流！',
            desc: `${topicName}`,
            link: shareUrl,
            imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/DigitalHealthShareImg.png',
          };
          console.log(shareDate)
          wx.updateAppMessageShareData(shareDate);
          wx.updateTimelineShareData(shareDate);
          wx.onMenuShareTimeline(shareDate);
          wx.onMenuShareAppMessage(shareDate);
          wx.onMenuShareQQ(shareDate);
          wx.onMenuShareWeibo(shareDate);
          wx.onMenuShareQZone(shareDate);
        })
      } else {
        // Toast.show('请求微信配置失败～！')
      }
    })
  }

  /**
   * 根据浮窗链接跳转
   * @param url   地址
   */
  const jumpExternalLinks = (url: string) => {
    window.open(url)
  }

  /**
   * 收藏（需要登录才能操作）
   * @param status 0未收藏 1已收藏
   */
  const onClickCollection = (status: number) => {
    // 没登录跳转登录页
    if (!localStorage.getItem('access_token')) {
      history.push({
        pathname: '/User/login',
        query: {
          redirectByPush: window.location.href,
        }
      })
      return
    }
    dispatch({
      type: "cases/isCollect",
      payload: {
        excellentCaseId,
        isCollect: status == 1 ? 0 : 1
      }
    }).then((res: any) => {
      let { code } = res || {};
      if (code == 200) {
        getExcellentCaseInfo()
      }
    }).catch((err: string) => {
      console.log(err);

    })

  }

  // 未登录进行登录
  const goLogin = () => {
    history.push({
      pathname: '/User/login',
      query: {
        redirectByPush: window.location.href,
      }
    })
  }

  // 接口调用异常重新调用列表接口
  const retryFun = () => {
    getExcellentCaseInfo()
    getMaExcellentCaseCommentsList()
  }

  // 重置接口异常状态
  const resetStatusFun = () => {
    setInterfaceStatus(0)
  }

  /**
   * 点击导航定位
   * @param key   当前选中
   */
  const onClickTab = (key: string) => {
    setActiveKey(key)
    document.getElementById(`anchor-${key}`)?.scrollIntoView()
    window.scrollTo({
      top: window.scrollY - 90,
    })
  }

  // 滚动定位导航
  const handleScroll = () => {
    let currentKey = tabItems[0].key
    for (const item of tabItems) {
      const element = document.getElementById(`anchor-${item.key}`)
      if (!element) continue;
      const rect = element.getBoundingClientRect()
      if (rect.top <= 95) {
        currentKey = item.key
      } else {
        break;
      }
    }
    setActiveKey(currentKey)
  }

  // 治疗计划以下内容
  const RenderList = () => {
    // 是否登录
    if(localStorage.getItem('access_token')) {
      // 是否能查看全部内容
      if(isAllLook == 1) {
        return <>
          <div id={`anchor-3`} className={styles.basic_info_three}>
            <div className={styles.treatment_plan_wrap}>
              <div className={styles.basic_header}>
                <span className={styles.basic_header_title}>治疗计划</span>
                <div className={styles.basic_header_back}></div>
              </div>
              {
                Array.isArray(treatmentPlanList) && treatmentPlanList.length ? <>
                  <CasesDetailsContent dataSource={treatmentPlanList} currentType={2} />
                </> : null
              }
            </div>
          </div>

          <div id={`anchor-4`} className={styles.basic_info_four}>
            <div className={styles.treatment_plan_wrap}>
              <div className={styles.basic_header}>
                <span className={styles.basic_header_title}>治疗过程</span>
                <div className={styles.basic_header_back}></div>
              </div>
              {
                Array.isArray(treatmentProcessList) && treatmentProcessList.length ? <>
                  <CasesDetailsContent dataSource={treatmentProcessList} currentType={2} />
                </> : null
              }
            </div>
          </div>

          <div id={`anchor-5`} className={styles.basic_info_five}>
            <div className={styles.treatment_plan_wrap}>
              <div className={styles.basic_header}>
                <span className={styles.basic_header_title}>总结与讨论</span>
                <div className={styles.basic_header_back}></div>
              </div>
              {
                Array.isArray(summaryDiscussList) && summaryDiscussList.length ? <>
                  <CasesDetailsContent dataSource={summaryDiscussList} currentType={3} />
                </> : null
              }
            </div>
          </div>
          <div className={styles.seize_seat}></div>
          <div className={styles.cases_details_comment}>
            <div className={styles.cases_details_comment_title}>评论</div>
            {
              Array.isArray(casesCommentData) && casesCommentData.length ?
                <CommentList dataSource={casesCommentData} onClickReplyFun={onClickReplyFun} />
                : <div className={styles.home_no_content}>
                  <div className={styles.no_cases_content}>
                    <img className={styles.no_content_img} src={noDataImg} />
                    <div className={styles.no_content_title}>暂无评论信息</div>
                  </div>
                </div>
            }
          </div>
        </>
      } else {
        // 普通用户每月只能查看5篇，第6篇会进行拦截
        return <>
          <div className={styles.vip_wrap}>
            <div className={styles.vip_forbidden_wrap}>
              <div id={`anchor-3`} className={styles.basic_info_three}>
                <div className={styles.treatment_plan_wrap}>
                  <div className={styles.basic_header}>
                    <span className={styles.basic_header_title}>治疗计划</span>
                    <div className={styles.basic_header_back}></div>
                  </div>
                  {
                    Array.isArray(treatmentPlanList) && treatmentPlanList.length ? <>
                      <CasesDetailsContent dataSource={treatmentPlanList} currentType={2} />
                    </> : null
                  }
                </div>
              </div>
              {/* <div id={`anchor-4`} className={styles.basic_info_four} style={{display: 'none'}}></div>
      <div id={`anchor-5`} className={styles.basic_info_five} style={{display: 'none'}}></div> */}
            </div>
            <div className={styles.vip_shade_wrap}>
              <div className={styles.vip_shade_title} onClick={jumpVipLeaflets}>
                开通会员,查看完整内容
                <img className={styles.vip_shade_icon} src={yellowArrowIcon} />
              </div>
              <div style={{ background: '#fff' }}>
                <div className={styles.vip_shade_content}>
                  <div className={styles.vip_shade_deblocking_title}>
                    <img className={styles.vip_shade_deblocking_icon} src={lLinesIcon} />
                    <span className={styles.vip_shade_deblocking_text}>成为会员后,你将解锁</span>
                    <img className={styles.vip_shade_deblocking_icon} src={rLinesIcon} />
                  </div>
                  <div className={styles.vip_shade_deblocking_List}>
                    <div className={styles.vip_deblocking_content}>
                      {
                        vipDeblockingList.map((item, ind) => {
                          return <div key={ind} className={styles.vip_deblocking_item}>
                            <img className={styles.vip_deblocking_icon} src={item.icon} />
                            <span className={styles.vip_deblocking_text}>{item.text}</span>
                          </div>
                        })
                      }
                    </div>
                    <div className={styles.vip_deblocking_btn_wrap}>
                      <div className={styles.vip_button_style} onClick={jumpVipLeaflets}>
                        <span className={styles.vip_button_text}>开通会员&nbsp;畅享内容</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className={styles.seize_seat}></div>
          <div className={styles.cases_details_comment}>
            <div className={styles.cases_details_comment_title}>评论</div>
            {
              Array.isArray(casesCommentData) && casesCommentData.length ?
                <CommentList dataSource={casesCommentData} onClickReplyFun={onClickReplyFun} />
                : <div className={styles.home_no_content}>
                  <div className={styles.no_cases_content}>
                    <img className={styles.no_content_img} src={noDataImg} />
                    <div className={styles.no_content_title}>暂无评论信息</div>
                  </div>
                </div>
            }
          </div>
        </>
      }
    } else {
      // 未登录只能查看一半，并提示去登录
      return <>
        <div className={styles.unlogin_wrap}>
          <div className={styles.vip_forbidden_wrap}>
            <div id={`anchor-3`} className={styles.basic_info_three}>
              <div className={styles.treatment_plan_wrap}>
                <div className={styles.basic_header}>
                  <span className={styles.basic_header_title}>治疗计划</span>
                  <div className={styles.basic_header_back}></div>
                </div>
                {
                  Array.isArray(treatmentPlanList) && treatmentPlanList.length ? <>
                    <CasesDetailsContent dataSource={treatmentPlanList} currentType={2} />
                  </> : null
                }
              </div>
            </div>
            {/* <div id={`anchor-4`} className={styles.basic_info_four} style={{display: 'none'}}></div>
          <div id={`anchor-5`} className={styles.basic_info_five} style={{display: 'none'}}></div> */}
          </div>
          <div className={styles.vip_shade_wrap}>
            <div className={styles.vip_shade_title} onClick={goLogin}>
              立即登录，查看完整内容
              <img className={styles.vip_shade_icon} src={yellowArrowIcon} />
            </div>
            <div className={styles.vip_shade_content}></div>
          </div>
        </div>
        <div className={styles.seize_seat}></div>
        <div className={styles.cases_details_comment}>
          <div className={styles.cases_details_comment_title}>评论</div>
          {
            Array.isArray(casesCommentData) && casesCommentData.length ?
              <CommentList dataSource={casesCommentData} onClickReplyFun={onClickReplyFun} />
              : <div className={styles.home_no_content}>
                <div className={styles.no_cases_content}>
                  <img className={styles.no_content_img} src={noDataImg} />
                  <div className={styles.no_content_title}>暂无评论信息</div>
                </div>
              </div>
          }
        </div>
      </>
    }
  }

  const load = !!loading.effects['cases/getExcellentCaseInfo'] ||           // 病例详情接口loading
    !!loading.effects['cases/getMaExcellentCaseCommentsList']    // 评论列表接口loading
  !!loading.effects['cases/saveCaseComments']                  // 保存评论接口loading
  !!loading.effects['cases/isCollect']                         // 收藏接口loading

  return (
    <Suspense fallback={<div></div>}>
      <Spin spinning={load}>
        <div className={styles.header}>
          <NavBar title={"病例详情"} className={styles.nav_bar} />
        </div>
        <div className={styles.cases_details_container}>
          {
            interfaceStatus != 1 ?
              <div className={styles.cases_details}>
                <div className={styles.cases_details_basic_info} id="casesDetailsBasicInfo">
                  <div className={styles.basic_info_title}>{topicName}</div>
                  <div className={styles.basic_info_sub}>
                    {
                      difficultLevelDict == 1 ? <span className={styles.difficulty_one}>难度一级</span> :
                        difficultLevelDict == 2 ? <span className={styles.difficulty_two}>难度二级</span> :
                          difficultLevelDict == 3 ? <span className={styles.difficulty_three}>难度三级</span> :
                            difficultLevelDict == 4 ? <span className={styles.difficulty_four}>难度四级</span> : null
                    }
                    {
                      Array.isArray(depSubjectDictNameList) && depSubjectDictNameList.length ? <>
                        {
                          depSubjectDictNameList.map((val, index) => {
                            return <div key={index} className={styles.sub_title}>{val}</div>
                          })
                        }
                      </> : null
                    }
                    {achievementDictName ? <div className={styles.achieve_title}>{achievementDictName}</div> : null}
                  </div>
                  <div className={styles.basic_info_indications}>主诊：
                    {
                      Array.isArray(doctorUserList) && doctorUserList.length ? <>
                        {
                          doctorUserList.map((val, idx) => {
                            return <span key={idx} className={styles.basic_info_indications_title}>{idx != 0 ? "、" : null}{val}</span>
                          })
                        }
                      </> : null
                    }
                  </div>
                  {
                    Array.isArray(keyWordList) && keyWordList.length ?
                      <div className={styles.basic_info_word}>
                        <div className={styles.basic_info_word_title}>病例问题：</div>
                        {
                          keyWordList.map((val, idx) => {
                            return <div key={idx} className={styles.basic_info_word_desc}>{val}</div>
                          })
                        }
                      </div> : null
                  }
                  {/* 治疗方案 */}
                  {
                    !!solution &&
                    <div className={styles.solution}>
                      <img className={styles.solution_img} src={wordArtImg} />
                      <span className={styles.solution_text}>{solution}</span>
                    </div>
                  }
                </div>
                <div className={styles.cases_details_wrap}>
                  <div className={styles.tabsContainer}>
                    <Tabs
                      activeKey={activeKey}
                      onChange={key => { onClickTab(key) }}
                    >
                      {tabItems.map(item => (
                        <Tabs.Tab title={item.title} key={item.key} />
                      ))}
                    </Tabs>
                  </div>

                  <div className={styles.basic_info}>
                    <div id={`anchor-1`}>
                      <div className={classNames([styles.basic_header, styles.basic_info_one])}>
                        <span className={styles.basic_header_title}>基础信息</span>
                        <div className={styles.basic_header_back}></div>
                      </div>
                      {
                        age || sex ? <div className={styles.basic_info_age_sex}>
                          {
                            age ? <div className={styles.basic_wrap}>
                              <span className={styles.basic_title}>年龄</span>
                              <span className={styles.basic_value}>{age}</span>
                            </div> : null
                          }
                          {
                            sex ? <div className={styles.basic_wrap}>
                              <span className={styles.basic_title}>性别</span>
                              <span className={styles.basic_value}>{sex == 1 ? "男" : sex == 2 ? "女" : null}</span>
                            </div> : null
                          }
                        </div> : null
                      }

                      <div className={styles.basic_title}>主诉</div>
                      <div className={styles.basic_info_content_desc}>{chiefComplaint}</div>
                      {
                        presentDisease ? <>
                          <div className={styles.basic_title}>现病史</div>
                          <div className={styles.basic_info_content_desc}>{presentDisease}</div>
                        </> : null
                      }
                      {
                        previousHistory ? <>
                          <div className={styles.basic_title}>既往史</div>
                          <div className={styles.basic_info_content_desc}>{previousHistory}</div>
                        </> : null
                      }
                      {
                        wholeHealth ? <>
                          <div className={styles.basic_title}>全身情况</div>
                          <div className={styles.basic_info_content_desc} style={{ margin: 0 }}>{wholeHealth}</div>
                        </> : null
                      }
                    </div>
                    <div id={`anchor-2`} className={styles.basic_info_two}>
                      <div className={styles.inspect_diagnosis_wrap}>
                        <div className={styles.basic_header}>
                          <span className={styles.basic_header_title}>检查及诊断</span>
                          <div className={styles.basic_header_back}></div>
                        </div>
                        <div className={styles.basic_title}>检查</div>
                        {
                          Array.isArray(checkList) && checkList.length ? <>
                            <CasesDetailsContent dataSource={checkList} currentType={1} />
                          </> : null
                        }
                        <div className={styles.basic_title}>诊断</div>
                        {
                          Array.isArray(diagnosisList) && diagnosisList.length ? <>
                            <CasesDetailsContent dataSource={diagnosisList} currentType={1} />
                          </> : null
                        }
                      </div>
                    </div>

                    {RenderList()}
                  </div>
                </div>


              </div>
              : <LoadingException exceptionStyle={{ paddingTop: 110 }} interfaceStatus={interfaceStatus} retryFun={retryFun} resetStatusFun={resetStatusFun} />
          }
          {/* 底部内容 */}

          <div style={{ height: 65 }}></div>
          <div className={styles.comment_publish} id="commentPublish" style={{}}>
            <div className={styles.collection_wrap} onClick={() => { onClickCollection(isCollect) }}>
              <img className={styles.collection_icon} src={isCollect == 1 ? collectIcon : noCollectIcon} />
              <span className={styles.collection_title}>收藏</span>
              <div className={styles.collection_people}>{collectCount}人</div>
            </div>
            <div className={styles.share_wrap}>
              {
                // (getOperatingEnv() == '2' || getOperatingEnv() == '7' || getOperatingEnv() == '5' || getOperatingEnv() == '6') &&
                  <div onClick={shareModalShow}>
                    <img className={styles.wx_icon} src={wxIcon} />
                    <span className={styles.wx_title}>分享</span>
                  </div>
              }

            </div>
            <div className={styles.say_something} onClick={() => { onSayClickSomethehing() }}>说点啥吧...</div>
          </div>
          {/* 评论输入弹框 */}
          {
            isCommentOpen &&
            <Popup
              visible={isCommentOpen}
              className={styles.comment_publish_input}
              onMaskClick={() => {
                onMaskClick()
              }}
              onClose={() => {
                onBindBlur()
              }}
              bodyStyle={{ height: '170px' }}
            >
              <div className={styles.comment_publish_input_header}>
                <div className={styles.comment_publish_input_left}>发表评论</div>
                <div className={styles.comment_publish_input_right} onMouseDown={() => { onReleaseConfirm() }}>发布</div>
              </div>
              <div className={styles.comment_publish_textarea}>
                <TextArea
                  className={styles.publish_input}
                  value={commentValue}
                  rows={4}
                  autoFocus={isCommentOpen}
                  placeholder={commentsSuperId ? `回复@${commentsUserName}` : "发表评论,和大家一起讨论吧..."}
                  onChange={onChangeInput}
                  onBlur={onBindBlur}
                />
              </div>
            </Popup>
          }
          {/* 右侧浮窗 */}
          {
            floatImgShowUrl && floatPathUrl ?
              <div className={styles.right_content}>
                <div className={styles.banner_window_wrap} style={{ display: isShowBannerWindow ? 'block' : 'none' }}>
                  <img className={styles.banner_window_img} src={floatImgShowUrl} onClick={() => jumpExternalLinks(floatPathUrl)} />
                  <div className={styles.banner_window_icon} onClick={() => { setIsShowBannerWindow(false) }}>
                    <img className={styles.banner_window_close} src={closeIcon} />
                  </div>
                </div>
                <div className={styles.show_window_wrap} style={{ display: isShowBannerWindow ? 'none' : 'block' }} onClick={() => setIsShowBannerWindow(true)}>
                  <img className={styles.show_window_icon} src={showIcon} />
                </div>
              </div> : null
          }
        </div>
      </Spin>

      {/* 分享弹窗 */}
      <ShareModal
        visible={shareModalVisible}
        topicName={topicName}
        onCancel={shareModalHide}
      />
    </Suspense>
  )
}
export default connect(({ cases, userInfoStore, loading }: any) => ({ cases, userInfoStore, loading }))(Index)
