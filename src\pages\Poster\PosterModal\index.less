.popup {
  :global {
    .adm-popup-body {
      border-radius: 16px 16px 0 0;
      height: 313px;
    }
  }
}
.poster_image {
  position: fixed;
  z-index: 1001;
  left: 0;
  top: 0;
  width: 100%;
  height: calc(100% - 313px);
  display: flex;
  justify-content: center;
  align-items: center;
  img {
    border-radius: 16px;
    max-width: 320px;
    max-height: calc(100% - 36px - 36px);
  }
}

.header {
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  .line {
    width: 48px;
    height: 4px;
    border-radius: 4px;
    background: #D0D4D7;
  }
}

.bottom_wrap {
  width: 100%;
  background: #fff;

  .bottom_title {
    margin-bottom: 8px;
    font-size: 14px;
    color: #666;
    line-height: 20px;
    padding: 0 16px;
  }

  .bottom_template_wrap {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    column-gap: 12px;
    padding: 0 16px 12px;
    margin-bottom: 12px;
    .template_option {
      width: auto;
      height: auto;
      border-radius: 6px;
      position: relative;
      & > img {
        object-fit: cover;
        border-radius: 6px;
      }
      &.checked::after {
        content: "";
        display: block;
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        border: 2px solid #0095FF;
        border-radius: 6px;
        background-image: url("../../../assets/GlobalImg/check_icon_2.png");
        background-repeat: no-repeat;
        background-size: 24px 24px;
        background-position: 102% 103%;
      }
    }
  }
}

.bottom_btn_wrap {
  display: flex;
  flex-wrap: nowrap;
  padding: 0 16px 12px;
  overflow-x: auto;
  :global {
    div.ant-typography {
      margin-bottom: 0;
      margin-left: 25px;
    }
    .ant-typography-copy {
      margin-left: 0;
    }
  }
  .btn_item {
    margin-left: 25px;
    width: 48px;
    display: flex;
    flex-direction: column;
    align-items: center;
    &:first-child {
      margin-left: 0;
    }
    img {
      margin-bottom: 8px;
    }
    p {
      font-size: 10px;
      color: #666;
      margin-bottom: 0;
      line-height: 14px;
      white-space: nowrap;
    }
  }
}

// APP环境分享弹窗
.popup_share_by_app {
  :global {
    .adm-popup-body {
      border-radius: 16px 16px 0 0;
      min-height: 164px;
      padding-bottom: 34px;
    }
  }
}

// 微信环境
.fixed_share_box {
  position: fixed;
  z-index: 1020;
  right: 21px;
  top: 24px;
  .icon1 {
    position: relative;
    display: block;
    background: url("../../../assets/GlobalImg/arrow_up.png") no-repeat right center;
    background-size: 122px 108px;
    width: 100%;
    height: 108px;
  }
  .message_box {
    position: relative;
    font-size: 18px;
    font-weight: 400;
    line-height: 25px;
    color: #fff;
    white-space: nowrap;
    top: -18px;
    margin-bottom: 14px;
  }
  .icon_box {
    display: flex;
    align-items: center;
    justify-content: center;
    i {
      display: block;
      width: 48px;
      height: 48px;
      &.icon2 {
        background: url("../../../assets/GlobalImg/share_one.png") no-repeat center;
        background-size: 100% 100%;
        margin-right: 24px;
      }
      &.icon3 {
        background: url("../../../assets/GlobalImg/share_more.png") no-repeat center;
        background-size: 100% 100%;
      }
    }
  }
}

// 空间详情页分享内容样式
.in_space_details_wrap {
  padding: 0 16px 24px;
  .forward_btn_wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0 16px;
    .left {
      font-size: 14px;
      color: #666;
    }
    .right_btn {
      background: #0095FF;
      font-size: 15px;
      color: #fff;
      height: 29px;
      line-height: 29px;
      border-radius: 15px;
      padding: 0 12px;
    }
  }
  .post_btn {
    font-size: 16px;
    color: #aaa;
    padding-left: 6px;
    height: 132px;
    position: relative;
    &::before {
      content: "";
      display: block;
      position: absolute;
      left: 0;
      top: 6px;
      width: 2px;
      height: 16px;
      border-radius: 3px;
      background: #0095FF;
    }
  }
  .space_wrap {
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #EBEBEB;
    display: flex;
    flex-wrap: nowrap;
    .left_cover_image {
      flex-shrink: 0;
      margin-right: 8px;
      width: 68px;
      height: 68px;
      border-radius: 4px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      line-height: 68px;
      color: #fff;
      font-size: 30px;
      text-align: center;
      white-space: nowrap;
      position: relative;
      .title_in_cover_image {
        position: absolute;
        z-index: 10;
        width: 100%;
        top: 14px;
        left: 0;
        padding-left: 8px;
        font-size: 12px;
        line-height: 16px;
        color: #fff;
        font-weight: 500;
        word-break: break-all;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2; /* 指定显示行数 */
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: left;
      }
    }
    .right {
      flex: 1;
      overflow: hidden;
      .space_title {
        font-size: 13px;
        color: #000;
        font-weight: 500;
        line-height: 18px;
        word-break: break-all;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        text-overflow: ellipsis;
      }
      .space_introduce {
        font-size: 12px;
        color: #333;
        line-height: 16px;
        word-break: break-all;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        text-overflow: ellipsis;
      }
    }
  }
}
