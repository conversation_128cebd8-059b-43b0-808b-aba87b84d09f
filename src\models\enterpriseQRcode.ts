import { enterpriseQrBind } from '@/services/enterpriseQRcode';

export default {
  namespace: 'enterpriseQRcode',
  state: {
    enterpriseUserInfo: {}, //企业用户信息
  },

  effects: {
    *enterpriseQrBind({ payload }: any, { put, call }: any) {
      const response = yield call(enterpriseQrBind, payload);
      if (response.code === 200) {
        yield put({type: 'save', payload: {enterpriseUserInfo: response.content}});
      }
      return response;
    },
  },

  reducers: {
    save(state: any, {payload}: any) {
      console.log(state, payload)
      return {
        ...state,
        ...payload,
      }
    },
  },
};
