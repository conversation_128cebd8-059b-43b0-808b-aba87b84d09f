.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  .content_box {
    flex: 1;
    overflow-y: auto;
    background: #EEF3F9;
    .content {
      width: 1228px;
      margin: 0 auto;
      padding: 24px 0 0;
    }
  }
}

.header_box {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  :global {
    .ant-input-affix-wrapper {
      border-radius: 4px;
      border: 1px solid #CFD7DF;
    }
    .ant-input-affix-wrapper:focus, .ant-input-affix-wrapper-focused {
      border-color: #40a9ff;
    }
  }
  .back_icon {
    display: block;
    width: 24px;
    height: 24px;
    background: url("../../../assets/GlobalImg/pc_goback.png") no-repeat center;
    background-size: 100% 100%;
    cursor: pointer;
    margin-right: 16px;
  }
  .search_icon {
    width: 20px;
    height: 20px;
    background: url("../../../assets/GlobalImg/search_icon_3.png") no-repeat center;
    background-size: 100% 100%;
    cursor: pointer;
  }
}

.result_box {
  padding-bottom: 8px;
  .result_title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 25px;
    margin-bottom: 18px;
    .title_text {
      font-size: 18px;
      color: #000;
      font-weight: 600;
      position: relative;
      &::after {
        content: "";
        display: block;
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 100%;
        height: 10px;
        border-radius: 10px;
        background: linear-gradient(90deg, #0095FF 0%, rgba(255,255,255,0) 100%);
      }
    }
    .title_btn_box {
      font-size: 13px;
      color: #999;
      display: flex;
      align-items: center;
      cursor: pointer;
      .title_btn_icon {
        display: block;
        width: 16px;
        height: 16px;
        background: url("../../../assets/GlobalImg/right_arrow_2.png") no-repeat center;
        background-size: 100% 100%;
      }
    }
  }
  .result_content {
    display: flex;
    flex-wrap: wrap;
    .result_item {
      width: 399px;
      margin-bottom: 16px;
      margin-right: 15px;
      &:nth-child(3n) {
        margin-right: 0;
      }
    }
    .result_item_empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      .empty_title {
        font-size: 15px;
        color: #333;
        line-height: 21px;
        margin-bottom: 8px;
        margin-top: 4px;
      }
      .empty_text {
        font-size: 14px;
        color: #999;
        line-height: 20px;
      }
      &.result_item_empty_case {
        padding: 80px 0 189px;
      }
    }
  }
}
