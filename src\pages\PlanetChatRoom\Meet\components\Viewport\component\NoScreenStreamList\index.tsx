import React, {useState,} from 'react';
import {
  getCameralistArr,
  getHandUpRemoteStreamList,
  getHostRemoteStreamConfig,
  getIsModeMatrixCameraRemoteStreamList,
  getShareRemoteStreamConfig,
  getUserCameraRemoteStreamList,
  getUserInfoData,
} from '@/utils/utilsByTRTC';
import {connect} from 'umi';
import styles from './index.less';
import classNames from 'classnames';
import {Swiper} from 'antd-mobile';
import AvatarByMeet from '@/pages/PlanetChatRoom/Meet/components/Viewport/component/AvatarByMeet';
import StreamAudioList from '../StreamAudioList';

type propsType = {
  global: any;
  onRefByVerticalLiveRoom: any;
  sendMessageByIm: any;
  localStreamConfig: any;
  remoteStreamConfigList: any;
  RTC: any;
  shareRTC: any;
  isJoined: boolean;
  isPublished: boolean;
  handleJoin: any;
  handleLeave: any;
  onChange: any;
  spaceId: any;
  openCloseHandUp: any;
  liveRecord: any;
  getGuestList: any;
  getSpaceInfo: any;
  onClickLianMai: any;
  changeUrlParams: any;
  elapsedTime: any;
  shareOnClick: any;
  onClickBack: any;
  isHorizontalLive: any;
};

const Index: React.FC<propsType> = (props) => {
  const {
    localStreamConfig,
    RTC,
    remoteStreamConfigList,
    PlanetChatRoom,
  } = props || {};

  let {
    SpaceInfo,
    handUpList,
    currentLiveUserList,
    isHorizontalLive,
  } = PlanetChatRoom || {};

  const {
    wxUserId,
    imUserId,
    hostUserInfo,
    name: nameBySpaceInfo,
    status: statusBySpaceInfo,
    imagePhotoPathShow,
  } = SpaceInfo || {};

  const [isShowCameraList, setIsShowCameraList] = useState(true);

  const userInfoData = getUserInfoData();

  const shareRemoteStreamConfig = getShareRemoteStreamConfig(SpaceInfo, remoteStreamConfigList);

  const hostRemoteStreamConfig = getHostRemoteStreamConfig(
    SpaceInfo,
    hostUserInfo,
    remoteStreamConfigList,
  );

  let userCameraRemoteStreamList = getUserCameraRemoteStreamList(SpaceInfo, remoteStreamConfigList); // 远端流中摄像头流 只要又画面或者有声音

  const isModeMatrixCameraRemoteStreamList = getIsModeMatrixCameraRemoteStreamList(
    SpaceInfo,
    hostUserInfo,
    handUpList,
    userCameraRemoteStreamList,
  );

  const handUpRemoteStreamList = getHandUpRemoteStreamList(
    SpaceInfo,
    handUpList,
    userCameraRemoteStreamList,
  );

  let cameralistArr = getCameralistArr(
    hostRemoteStreamConfig,
    localStreamConfig,
    handUpRemoteStreamList,
    isModeMatrixCameraRemoteStreamList,
  );

  currentLiveUserList =
    Array.isArray(currentLiveUserList) &&
    currentLiveUserList.concat(Array.isArray(handUpList) ? handUpList : []);

  return (
    <>
      <div
        style={{
          height: !!isHorizontalLive ? '100%' : '60%',
        }}
        className={styles.ViewContent}
      >
        <StreamAudioList
          key={'NoScreenStreamList'}
          RTC={RTC}
          localStreamConfig={localStreamConfig}
          remoteStreamConfigList={remoteStreamConfigList}
        />

        <div
          className={classNames({
            [styles.swiperWarp]: true,
            [styles.swiperWarpByindicatorhidden]: true,
          })}
        >
          <Swiper style={{height: '100%'}}>
            <Swiper.Item key={1}>
              <div className={styles.ViewLine}>
                {localStreamConfig && localStreamConfig.hasAudio && (
                  <AvatarByMeet
                    userInfo={{
                      imUserId: imUserId,
                      imagePhotoPathShow: imagePhotoPathShow,
                      wxUserId: wxUserId,
                      ...userInfoData,
                    }}
                    itemByStream={localStreamConfig}
                  />
                )}
                {Array.isArray(userCameraRemoteStreamList) &&
                  userCameraRemoteStreamList.map((itemByStream) => {
                    let itemByMeeting =
                      Array.isArray(currentLiveUserList) &&
                      currentLiveUserList.find((itemByMembers) => {
                        return itemByMembers.imUserId == itemByStream.userID;
                      });
                    if (itemByMeeting) {
                      return <AvatarByMeet userInfo={itemByMeeting} itemByStream={itemByStream}/>;
                    }
                  })}
              </div>
            </Swiper.Item>
          </Swiper>
        </div>
      </div>
    </>
  );
};
export default connect(({ global, PlanetChatRoom, loading }: any) => ({
  global,
  PlanetChatRoom,
  loading,
}))(Index);
