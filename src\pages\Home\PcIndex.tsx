/**
 * @Description: 管理端组件配置生成的页面  分享的页面（行业大事件星球等）
 */
import React, { useState, useEffect, lazy, Suspense } from 'react'
import { history, connect } from 'umi'
import classNames from 'classnames'
import { parse } from 'qs'
import { getOperatingEnv } from '@/utils/utils'
import { getNewImageNumber } from './utils'
import { Spin } from 'antd'
import { Toast } from 'antd-mobile'
import styles from './PcIndex.less'
import {Helmet} from "react-helmet"; // 用于添加动态title

const DefaultPcIndex = lazy(() => import('./DefaultPcIndex'))                               // 默认首页

const ImageView = lazy(() => import('@/pages/Home/View/ImageView'))                        // 图片
const ImageTextView = lazy(() => import('@/pages/Home/View/ImageTextView'))                // 图文
const ActivityView = lazy(() => import('@/pages/Home/View/ActivityView'))                  // 活动
const SearchView = lazy(() => import('@/pages/Home/View/SearchView'))                      // 搜索框
const ClassifyGuideView = lazy(() => import('@/pages/Home/View/ClassifyGuideView'))        // 分类导航
const HomeSearchView = lazy(() => import('@/pages/Home/View/HomeSearchView'))              // 搜索框（首页中）

const SpaceView = lazy(() => import('@/components/SpaceList'))                             // 空间
const KingdomView = lazy(() => import('@/components/KingdomView'))                         // 王国
const CaseView = lazy(() => import('@/components/CaseList'))                               // 病例
const TabbarView = lazy(() => import('@/components/Tabbar'))                               // 底部菜单
const MajorIndustryEventsView = lazy(() => import('@/components/MajorIndustryEventsView')) // 行业大事件

// 判断是否是经过base64编码的数据
const isBase64 = (str) => {
  if (str === '' || str.trim() === '') {
    return false;
  }
  try {
    return btoa(atob(str)) == str || btoa(atob(str)) == `${str}=` || btoa(atob(str)) == `${str}==`;
  } catch (err) {
    return false;
  }
}

// 从地址栏参数中解析出pageId
const getRealPageId = (pageUrl = '') => {
  // pageUrl有值，则解析pageUrl（表示即非首页，也非直播页），否则表示找首页
  const pageSearch = pageUrl ? pageUrl.split('?')[1] : ''
  const pageSearchParse = parse(pageSearch)
  const keys = Object.keys(pageSearchParse)

  let pageId = null

  // 循环是为了兼容地址栏参数有多个的情况，比如/home?alhelper&aWQ9OSZyPTA2OTEzMzcy
  keys.forEach(item => {
    if (isBase64(item)) {
      // 每个都解析一遍，哪个能解析出值来取哪个
      const pageIdAtob = atob(item)  // 将ID字符串解码
      const urlParams = new URLSearchParams(pageIdAtob)
      if (urlParams.get('id')) {
        pageId = urlParams.get('id')
      }
    }
  })

  return pageId      // 最终的页面ID
}

const Index: React.FC = (props: any) => {
  const { dispatch, loading, pageSource } = props

  const initialState = {
    componentsResultList: [],                              // 组件的集合
    fixedComponentsResultList: [],                         // 组件的集合（固定位置的组件）
    isHomePage: 0,                                         // 是否是首页，1 是，0 否

    // 友盟统计用的数据
    allSpaceList: [],                                      // 所有空间组件集合
    allCaseList: [],                                       // 所有病例组件集合
    allKingdomList: [],                                    // 所有王国组件集合
    allImageList: [],                                      // 所有图片组件集合
    allImageTextList: [],                                  // 所有图文组件集合
    allClassifyGuideList: [],                              // 所有分类组件集合
    allMajorIndustryEventsList: [],                        // 所有行业大事件集合
  }
  const [state, setState] = useState(initialState)
  const [realPageId, setRealPageId] = useState(null)       // 最终的页面ID
  const [showDefaultIndex, setShowDefaultIndex] = useState(false)// 是否显示默认首页
  const [wxPageName, setWxPageName] = useState('')             // 行业大事件名称

  useEffect(() => {
    const operatingEnv = getOperatingEnv();
    if (!location.host.includes('dhealth.friday.tech') && operatingEnv == 6) {
      Toast.show({
        content: `'现在环境是'${operatingEnv == 6 ? 'JarvisApp' : '其他非JarvisApp环境'}`,
      })
    }

    // 获取页面ID，有ID查询数据，没有ID的话，显示默认首页
    const pageId = getRealPageId(window.location.href)
    if (pageId) {
      setRealPageId(pageId)
      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      getPageInfo(pageId)
    } else {
      setShowDefaultIndex(true)
    }
  }, [])

  // 根据页面ID查询页面详情
  const getPageInfo = (pageId) => {
    dispatch({
      type: 'activity/getPageInfo',
      payload: {
        id: pageId,                               // 页面id
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200 && content) {
        // 动态标题
        const { pageName } = content;
        setWxPageName(pageName)

        // 预设行业大事件的数据   
        const componentsResultList = content.dataList || []

        // 拆分普通组件和位置固定的组件
        const fixedComponentsResultList = []

        // 活动组件
        const index = componentsResultList.findIndex(item => item.type == 'activity')
        if (index > -1) {
          fixedComponentsResultList.push(componentsResultList.splice(index, 1)[0])
        }
        // 搜索组件
        const index2 = componentsResultList.findIndex(item => item.type == 'search')
        if (index2 > -1) {
          fixedComponentsResultList.push(componentsResultList.splice(index2, 1)[0])
        }
        // 底部导航栏组件
        const index3 = componentsResultList.findIndex(item => item.type == 'tabBar')
        if (index3 > -1) {
          fixedComponentsResultList.push(componentsResultList.splice(index3, 1)[0])
        }

        // 首页友盟埋点数据结构保存
        let allSpaceList = []                  // 全部空间组件的集合
        let allKingdomList = []                // 全部王国组件的集合
        let allCaseList = []                   // 全部病例组件的集合
        let allImageList = []                  // 全部图片组件的集合
        let allImageTextList = []              // 全部图文组件的集合
        let allClassifyGuideList = []          // 全部分类组件的集合
        let allMajorIndustryEventsList = []    // 全部行业大事件的集合

        // 如果是首页，才进行统计
        if (content.isHomePage == 1) {
          componentsResultList.forEach(item => {
            if (item.type == 'space' && item.dataList) {
              allSpaceList = allSpaceList.concat(item.dataList)

            } else if (item.type == 'kingdom' && item.dataList) {
              allKingdomList = allKingdomList.concat(item.dataList)

            } else if (item.type == 'case' && item.dataList) {
              allCaseList = allCaseList.concat(item.dataList)

            } else if (item.type == 'image' && item.dataList) {
              allImageList = allImageList.concat(item.dataList)

            } else if (item.type == 'imageText' && item.dataList) {
              allImageTextList = allImageTextList.concat(item.dataList)

            } else if (item.type == 'classifyGuide' && item.dataList) {
              allClassifyGuideList = allClassifyGuideList.concat(item.dataList)

            } else if (item.type == 'industryEvent' && item.dataList) {
              allMajorIndustryEventsList = allMajorIndustryEventsList.concat(item.dataList)

            }
          })
        }

        setState({
          ...state,
          componentsResultList,                          // 组件的集合
          fixedComponentsResultList,                     // 组件的集合（固定位置的组件）
          isHomePage: content.isHomePage,                // 是否是首页，1 是，0 否

          // 友盟统计用
          allSpaceList,                                  // 全部空间组件的集合
          allKingdomList,                                // 全部王国组件的集合
          allCaseList,                                   // 全部病例组件的集合
          allImageList,                                  // 全部图片组件的集合（一行一个的）
          allImageTextList,                              // 全部图文组件的集合
          allClassifyGuideList,                          // 全部分类组件的集合
          allMajorIndustryEventsList,                    // 全部行业大事件的集合
        })
      } else {
        Toast.show(msg || '数据加载失败')

        // 没有查到数据，默认首页
        setShowDefaultIndex(true)
      }
    })
  }


  // 根据type返回对应组件
  const getViewByType = (item) => {
    let moduleIndex = -1
    switch (item.type) {
      case 'image':
        // 在所有图片组件（一行1个）里的索引
        moduleIndex = state.allImageList.filter(itemChild => itemChild.config && getNewImageNumber(itemChild.config.number) == 11).findIndex(itemChild => itemChild.id == item.id)
        // 在所有图片组件（轮播）里的索引
        const moduleIndexBanner = state.allImageList.filter(itemChild => itemChild.config && getNewImageNumber(itemChild.config.number) == 50).findIndex(itemChild => itemChild.id == item.id)
        // 在所有图片组件（一行5个）里的索引
        const moduleIndexJinGang = state.allImageList.filter(itemChild => itemChild.config && getNewImageNumber(itemChild.config.number) == 15).findIndex(itemChild => itemChild.id == item.id)
        // 在所有图片组件（一行3个）里的索引
        const moduleIndexAD = state.allImageList.filter(itemChild => itemChild.config && getNewImageNumber(itemChild.config.number) == 13).findIndex(itemChild => itemChild.id == item.id)
        return <ImageView
          key={item.id}
          componentData={item}
          isHomePage={state.isHomePage}
          moduleIndex={moduleIndex > -1 ? (moduleIndex + 1) : 0}
          moduleIndexBanner={moduleIndexBanner > -1 ? (moduleIndexBanner + 1) : 0}
          moduleIndexJinGang={moduleIndexJinGang > -1 ? (moduleIndexJinGang + 1) : 0}
          moduleIndexAD={moduleIndexAD > -1 ? (moduleIndexAD + 1) : 0}
        />
      // case 'case':
      //   moduleIndex = state.allCaseList.findIndex(itemChild => itemChild.id == item.id)
      //   return <CaseView
      //     key={item.id}
      //     componentData={item}
      //     isHomePage={state.isHomePage}
      //     moduleIndex={moduleIndex > -1 ? (moduleIndex + 1) : 0}
      //   />
      // case 'space':
      //   moduleIndex = state.allSpaceList.findIndex(itemChild => itemChild.id == item.id)
      //   return <SpaceView
      //     key={item.id}
      //     componentData={item}
      //     isHomePage={state.isHomePage}
      //     moduleIndex={moduleIndex > -1 ? (moduleIndex + 1) : 0}
      //   />
      // case 'kingdom':
      //   moduleIndex = state.allKingdomList.findIndex(itemChild => itemChild.id == item.id)
      //   return <KingdomView
      //     key={item.id}
      //     componentData={item}
      //     getPageInfo={getPageInfo}
      //     isHomePage={state.isHomePage}
      //     moduleIndex={moduleIndex > -1 ? (moduleIndex + 1) : 0}
      //   />
      // case 'imageText':
      //   moduleIndex = state.allImageTextList.findIndex(itemChild => itemChild.id == item.id)
      //   return <ImageTextView
      //     key={item.id}
      //     componentData={item}
      //     isHomePage={state.isHomePage}
      //     moduleIndex={moduleIndex > -1 ? (moduleIndex + 1) : 0}
      //   />
      // case 'classifyGuide':
      //   moduleIndex = state.allClassifyGuideList.findIndex(itemChild => itemChild.id == item.id)
      //   return <ClassifyGuideView
      //     key={item.id}
      //     componentData={item}
      //     getPageInfo={getPageInfo}
      //     isHomePage={state.isHomePage}
      //     moduleIndex={moduleIndex > -1 ? (moduleIndex + 1) : 0}
      //   />
      case 'industryEvent':
        moduleIndex = state.allMajorIndustryEventsList.findIndex(itemChild => itemChild.id == item.id)
        return <MajorIndustryEventsView
          key={item.id}
          componentData={item}
          pcOrMobileMode = 'pc'
        />
      default:
        return null
    }
  }

  // 根据type返回对应组件，位置固定的组件
  const getFixedViewByType = (item) => {
    switch (item.type) {
      case 'activity':
        return (
          <ActivityView
            key={item.id}
            pageId={realPageId}
            componentData={item}
            signUpOnOk={getPageInfo}
          />
        )
      case 'search':
        return state.isHomePage == 1 ? <HomeSearchView key={item.id} /> : <SearchView key={item.id} />
      case 'tabBar':
        // [APP相关] 5表示在FRIDAY APP中，这时隐藏底部菜单栏
        return getOperatingEnv() == 5 ? null : <TabbarView key={item.id} />
      default:
        return null
    }
  }

  // loading
  const loadingGetPageInfo = !!loading.effects['activity/getPageInfo']

  return (
    <>
      {showDefaultIndex ?
        <DefaultPcIndex />
        :
        <>
          <Helmet>
          <title>{wxPageName ? wxPageName : '医生星球'}</title>
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, shrink-to-fit=no" />
          </Helmet>
        <Spin spinning={loadingGetPageInfo}>
          <div className={styles.container_pc}>
            <div className={styles.container_box}>
            <div className={classNames(styles.container, {
              [styles.search_container]: state.fixedComponentsResultList.findIndex(item => item.type == 'search') > -1 && state.isHomePage == 0,
              [styles.home_search_container]: state.fixedComponentsResultList.findIndex(item => item.type == 'search') > -1 && state.isHomePage == 1,
              [styles.tabbar_container]: state.fixedComponentsResultList.findIndex(item => item.type == 'tabBar') > -1,
              [styles.activity_container]: state.fixedComponentsResultList.findIndex(item => item.type == 'activity') > -1,
              [styles.in_app_container]: getOperatingEnv() == 5,
            })}>

              {/* 一般组件 */}
              {
                state.componentsResultList.map((item, index) => {
                  return (
                    <Suspense key={index} fallback={<div></div>}>
                      {getViewByType(item)}
                    </Suspense>
                  )
                })
              }

              {/* 位置固定的组件 */}
              {
                state.fixedComponentsResultList.map((item, index) => {
                  // 广场页，不显示固定在顶部的搜索和底部的tabbar
                  if (pageSource == 'square') {
                    return null
                  }
                  return (
                    <Suspense key={index} fallback={<div></div>}>
                      {getFixedViewByType(item)}
                    </Suspense>
                  )
                })
              }
            </div>
            </div>
            
          </div>
        </Spin>
        </>
        }
    </>

  )
}

export default connect(({ activity, loading }: any) => ({ activity, loading }))(Index)
