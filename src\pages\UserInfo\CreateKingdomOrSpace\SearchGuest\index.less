.titleInfo {
  width: 100%;

  .titleWarp {
    // margin-top: 12px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    box-sizing: border-box;
    justify-content: center;
    position: relative;

    .titleBackIcon {
      position: absolute;
      top: 0;
      left: 16px;

      img {
        width: 12px;
        height: auto;
      }
    }

    .titleText {
      font-size: 17px;
      font-weight: 500;
      color: #000000;
      line-height: 24px;
    }
  }
}

.content {
  width: 100%;
  padding: 16px 16px 0;

  .input_box {
    width: 100%;
    height: 40px;
    position: relative;
    display: flex;
    align-items: center;
    padding: 0 12px;
    box-sizing: border-box;
    background: #F5F5F5;
    border-radius: 23px;

    .search_icon {
      flex-shrink: 0;
      width: 20px;
      height: 20px;
      margin-right: 8px;
      flex-shrink: 0;
    }

    :global {
      .adm-input {
        flex: 1;
      }
      .adm-input-element {
        font-size: 14px;
        font-weight: 400;
        color: #000;

        &::placeholder {
          color: #CCCCCC;
        }
      }
    }
  }

  .select_num {
    margin-top: 12px;
    font-size: 13px;
    font-weight: 400;
    color: #999999;
    line-height: 15px;
    padding-bottom: 12px;
  }

  .list_box {
    height: calc(70vh - 155px);
    padding-bottom: 100px;
    overflow-y: auto;

    .list_item {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .list_item_info_wrap {
        display: flex;
        align-items: center;

        .selectGuest_select {
          width: 22px;
          height: 22px;
          margin-right: 9px;
        }

        .selectGuest_select_user {
          display: inline-block;
          width: 22px;
          height: 22px;
          background: url('../../../../assets/PlanetChatRoom/selectGuest_select_user.png');
          background-size: 22px 22px;
        }

        .selectGuest_Unselect_user {
          display: inline-block;
          width: 22px;
          height: 22px;
          background: url('../../../../assets/PlanetChatRoom/selectGuest_Unselect_user.png');
          background-size: 22px 22px;
        }

        .list_item_img {
          width: 44px;
          height: 44px;
          margin-right: 8px;
          border-radius: 50%;

          .no_comment_head{
            width: 44px;
            height: 44px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 500;
            color: #fff;
            white-space: nowrap;
          }

          img {
            width: 44px;
            height: 44px;
            border-radius: 50%;
          }
        }

        .list_item_info {
          .name {
            font-size: 15px;
            font-weight: 500;
            line-height: 21px;
            color: #000000;
            margin-bottom: 4px;
            word-break: break-all;

            .highlight_name {
              color: #00D78B;
            }
          }

          .phone {
            font-size: 13px;
            font-weight: 400;
            color: #999999;
            line-height: 15px;
          }
        }
      }

      .active_select {
        background: #F5F5F5;
        border-radius: 18px;
        padding: 6px 8px;
        font-size: 12px;
        font-weight: 400;
        color: #999999;
        line-height: 17px;
        flex-shrink: 0;
      }

      .init_select {
        padding: 6px 12px;
        background: #E6F4FF;
        border-radius: 18px;
        font-size: 12px;
        font-weight: 400;
        color: #0095FF;
        line-height: 17px;
        flex-shrink: 0;
      }
    }
  }

  .nodata {
    font-size: 15px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #333333;
    line-height: 17px;
    margin-top: 4px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    img {
      width: 150px;
      height: 113px;
    }

    .empty_title {
      font-size: 15px;
      color: #333;
      line-height: 21px;
      margin-bottom: 8px;
    }
    .empty_msg {
      font-size: 14px;
      color: #999;
      line-height: 20px;
    }
  }
}

/*.btn_wrap {
  width: 100%;
  display: flex;
  padding: 16px;
  background: #fff;
  position: fixed;
  bottom: 0;
  left: 0;

  .cancelBtn {
    flex: 1;
    margin-right: 8px;
    height: 40px;
    background: #EDF9FF;
    border-radius: 20px 20px 20px 20px;
    font-size: 16px;
    font-weight: 400;
    color: #0095FF;
    line-height: 40px;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .submitBtn {
    flex: 1;
    height: 40px;
    background: #0095FF;
    border-radius: 20px 20px 20px 20px;
    font-size: 16px;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 40px;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}*/


.btn_wrap {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
}


.tip {
  display: flex;
  align-items: center;
  width: 100%;
  height: 30px;
  padding-right: 10px;
  /* justify-content: center; */
  padding-left: 10px;
  font-style: normal;
  text-align: left;
  text-transform: none;
  background: #ebf5ff;
  border-radius: 0px 0px 0px 0px;

  .tip_box {
    width: 100%;
    overflow: hidden; /* 确保超出容器的文本被裁剪 */
    color: #666666;
    font-weight: 400;
    font-size: 10px;
    line-height: 12px;
    white-space: nowrap; /* 确保文本在一行内显示 */
    text-overflow: ellipsis; /* 使用省略号表示文本超出 */
  }
}


.btn_box {
  display: flex;
  width: 100%;
  padding: 16px;
  background: #fff;

  .btn_wrap_left {
    display: flex;
    align-items: center;
    width: calc(100% - 80px);
    height: 50px;
    overflow-y: auto;
    scrollbar-width: 0;

    .item {
      width: 40px;
      height: 40px;
      margin-right: 8px;
      border-radius: 50%;
    }

    .no_comment_head {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      color: #fff;
      font-weight: 500;
      font-size: 14px;
      white-space: nowrap;
      border-radius: 50%;
    }

    img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
    }
  }
}

.btn_wrap_right {
  display: flex;
  justify-content: flex-end;
  width: 80px;
  height: 44px;

  .btn_submit {
    width: 74px;
    height: 44px;
    color: #ffffff;
    font-weight: 400;
    font-size: 16px;
    font-style: normal;
    line-height: 44px;
    text-align: center;
    text-transform: none;
    background: #0095ff;
    border-radius: 22px 22px 22px 22px;
  }

  .btn_submit:active {
    background: #0180da;
  }
}



@media (max-height: 500px) {
  .btn_wrap {
    display: none;
  }
}
