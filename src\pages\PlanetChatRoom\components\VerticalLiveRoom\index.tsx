import React, {
  useState,
  useEffect,
  useImperativeHandle,
  useCallback,
  useRef,
} from 'react';
import { connect, history } from 'umi';
import styles from './index.less';
import DanmuJs from 'danmu.js';
import { message } from 'antd';
import classNames from 'classnames';
import ReactHtmlParser from 'react-html-parser';
import { QuestionCircleFilled } from '@ant-design/icons';
import EditSpaceInfo from '../EditSpaceInfo';
import DownloadAppBtn from  '@/components/DownloadAppBtn';
import GoBackHomeIcon from '@/components/GoBackHome';
import _ from 'lodash';
import Avatar from '../Avatar';
import dayjs from 'dayjs';
import '../video-js.min.css';
import 'tcplayer.js/dist/tcplayer.min.css';
import ModalWXsubscribe from '@/components/SpaceRoomReservationCard/ModalWXsubscribe'

import {
  SIGN_IN,
  BULLET_SCREEN,
  SEND_FLOWERS,
  SEND_APPLAUSE,
  UPDATA_STATE,
} from '@/app/config';

import {
  formatTimeBySeconds,
  useDebounce,
  isIOS,
} from '@/utils/utils';

import {Popup, InfiniteScroll, Switch, Modal } from 'antd-mobile';

const TypeMsgList = 1; // 动态消息
const TypeGuestlist = 2; // 嘉宾
const TypeLienApplicationList = 3; // 连麦申请列表

type propsType = {
  global: any; // 项目共享modals
  onRefByVerticalLiveRoom: any; // 暴露给父组件的方法
  sendMessageByIm: any; // 发送im实时通信消息
  localStreamConfig: any; // 本地流配置
  remoteStreamConfigList: any; // 远端流配置列表
  RTC: any; // RTC实例
  shareRTC: any; // 分享RTC实例
  isJoined: boolean; // 是否加入房间
  isPublished: boolean; // 是否发布流
  handleJoin: any; // onClick进入房间
  handleLeave: any; // onClick离开房间
  onChange: any; // 改变直播状态 分享屏幕
  spaceId: any; // 直播间id
  openCloseHandUp: any; // 打开/关闭连麦列表
  liveRecord: any; // 打卡录制视频
  getGuestList: any; // 获取嘉宾列表
  getSpaceInfo: any; // 获取直播间信息
  onClickLianMai: any; // 点击申请连麦
  changeUrlParams: any; // 改变url参数
  elapsedTime: any; // 正在录播时长
  shareOnClick: any; // 分享
  onClickBack: any; // 返回按钮
  isHorizontalLive: any; // 是否全屏
};
function reducer(state, action) {
  return action && action.data ? [...action.data] : [];
}

const Index: React.FC<propsType> = (props) => {
  const {
    global,
    localStreamConfig,
    RTC,
    isJoined,
    isPublished,
    remoteStreamConfigList,
    PlanetChatRoom,
    dispatch,
    openCloseHandUp,
    liveRecord,
    getGuestList,
    getSpaceInfo,
    changeUrlParams,
    operateHandUp,
    elapsedTime,
    shareOnClick,
    onClickBack,
    isHorizontalLive,
  } = props;

  const {
    msgListBySENDAPPLAUSE,
    guestListInfo,
    applyAdmissionList,
    SpaceInfo,
    signInList,
    signInObj,
    BookingList,
    BookingObj,
    handUpList,
    currentUserType,
    isMobile,
    isInitialized,
    lastSeq,
    newMySnedSeq,
    HiddenDanmu,
    autoExpandGuestArea,
    ModalVisibleByKickedOut,
    isModeMatrix,
    ModalVisibleBySpaceViolation,
    ModalVisibleBySpaceRemoved,
    isNotLogin,
    isOpenTEduBoard,
    currentWatchMode,
  } = PlanetChatRoom || {};

  let {
    wxUserId,
    hostUserInfo,
    name: nameBySpaceInfo,
    kingdomName,
    kingdomId,
    status: statusBySpaceInfo,
    spaceAdvertisingUrlShow,
    isSignIn,
    isCollect,
    isAppointment,
    isHavCourseware,
    appointmentStartTime,
    handUpType,
    recordType,
    handUpStatusType,
    videoList,
    pv,
    gdp,
    intro,
    password,
    starSpaceType,
  } = SpaceInfo || {};
  let starSpaceTypeLianMaiText = starSpaceType == 2 ? '发言' : '连麦';

  const videoJsOptions = {
    autoplay: false,
    controls: true,
    fluid: true,
  };

  // RTCDanmu
  const [RTCDanmu, setRTCDanmu] = useState(null);
  // 发送弹幕input
  const inputRef = useRef(null);
  // isShowCameraList 摄像头列表
  const [isShowCameraList, setIsShowCameraList] = useState(true);
  // contentListType 消息类型 [MsgList: 动态消息] [Guestlist: 嘉宾列表] [LienApplicationList: 连麦申请列表]
  const [contentListType, setContentListType] = useState(TypeMsgList);
  // 嘉宾列表是 参会人 还是 申请进入会议成员
  const [guestListType, setGuestListType] = useState(1); // 嘉宾列表, 参会人 还是 申请进入会议成员
  // 是否隐藏空间广告图
  const [isHiddenSpaceAdvertisingUrlShow, setIsHiddenSpaceAdvertisingUrlShow] = useState(false);
  // isOpenClockinPopup 是否打卡详情弹窗
  const [isOpenClockinPopup, setIsOpenClockinPopup] = useState(false);
  // isOpenLiveBookingPopup 是否预约详情弹窗
  const [isOpenLiveBookingPopup, setIsOpenLiveBookingPopup] = useState(false);
  // 打卡列表 hasMore 是否还有更多
  const [hasMoreBySignInList, setHasMoreBySignInList] = useState(true);
  // 预约详情列表 hasMore 是否还有更多
  const [hasMoreByLiveBookingList, setHasMoreByLiveBookingList] = useState(true);
  // hasMoreByMsgList 打卡消息列表是否还有更多
  const [hasMoreByMsgList, setHasMoreByMsgList] = useState(true);
  // MsgContentHeight 消息高度
  const [msgContentHeight, setMsgContentHeight] = useState(true);
  // playerStateData 播放器状态数据
  const [playerStateData, setPlayerStateData] = useState(null);
  // handUpStatusTypeEnter 确认下麦或确认取消连麦
  const [handUpStatusTypeEnter, setHandUpStatusTypeEnter] = useState(null);
  // 当前展示送花或者鼓掌  currentShowFlowersOrApplause 1:送花 2:鼓掌
  const [currentShowFlowersOrApplause, setCurrentShowFlowersOrApplause] = useState(1);
  // 是否弹出软键盘
  const [isShowKeyboard, setIsShowKeyboard] = useState(false);
  // 获取当前窗口高度
  const [windowHeight, setWindowHeight] = useState(window.innerHeight);
  // isHiddenControlArea 是否隐藏控制区域
  const [isHiddenControlArea, setIsHiddenControlArea] = useState(false);
  // 5秒自动隐藏控制区域计时器
  const [timer, setTimer] = useState(null);
  const [videoPlayer, setVideoPlayer] = useState(null);
  // isOpenSpatialIntroduction 是否展开空间介绍
  const [isOpenSpatialIntroduction, setIsOpenSpatialIntroduction] = useState(false);
  // 是否打开预约成功弹窗
  const [isOpenByModalWXsubscribe,setIsOpenByModalWXsubscribe] = useState(false);


  let PopupKeyboard = null;
  // 远端流中屏幕分享流
  let shareRemoteStreamConfig =
    SpaceInfo &&
    Array.isArray(remoteStreamConfigList) &&
    remoteStreamConfigList.find((item) => item.userID.indexOf('share') !== -1 && item.hasVideo);
  // 远端流中的主持人流
  const hostRemoteStreamConfig =
    SpaceInfo &&
    Array.isArray(remoteStreamConfigList) &&
    remoteStreamConfigList.find((item) => item.userID == hostUserInfo.imUserId);
  const userCameraRemoteStreamList =
    SpaceInfo &&
    Array.isArray(remoteStreamConfigList) &&
    remoteStreamConfigList.filter(
      (item) => item.userID.indexOf('share') == -1 && (item.hasVideo || item.hasAudio),
    ); // 远端流中摄像头流 只要又画面或者有声音

  // 远端流中的白板流
  const tiwRemoteStreamConfig = SpaceInfo && Array.isArray(remoteStreamConfigList) && remoteStreamConfigList.find(item => item.userID.indexOf('tiw') !== -1);
  const shareHostRemoteStreamConfig = SpaceInfo && Array.isArray(remoteStreamConfigList) && remoteStreamConfigList.find(item => item.userID.indexOf('share') !== -1 && item.hasVideo);
  shareRemoteStreamConfig = tiwRemoteStreamConfig ? tiwRemoteStreamConfig : shareHostRemoteStreamConfig;

  // isModeMatrix矩阵模式 去除主持人画面流 和连麦嘉宾流
  const isModeMatrixCameraRemoteStreamList =
    SpaceInfo &&
    userCameraRemoteStreamList
      .filter((item) => item.userID != hostUserInfo.imUserId)
      .filter((item) =>
        Array.isArray(handUpList) && handUpList.length != 0
          ? handUpList.find((value) => {
              return value.imUserId != item.userID;
            })
          : true,
      );

  const baseSize = 16;
  const val = React.useRef();
  const playerRef = useRef(null);
  // const videoPlayer = useRef(null);
  const UerInfo = JSON.parse(localStorage.getItem('userInfo')) || {};
  const userCameraRemoteStreamListRef = useRef(userCameraRemoteStreamList);

  useEffect(() => {
    userCameraRemoteStreamListRef.current = userCameraRemoteStreamList;
  }, [userCameraRemoteStreamList]);

  useEffect(() => {
    initializationByDanmu(); // 初始化弹幕
    resetTimer();
    // 补充每隔10秒钟自动切换 展示送花还是鼓掌
    const interval = setInterval(() => {
      setCurrentShowFlowersOrApplause((prevValue) => (prevValue === 1 ? 2 : 1));
    }, 10000);
    // 处理软键盘弹出时的样式问题
    const inputElement = inputRef.current;
    if (inputElement) {
      // 添加focus事件监听，使软键盘贴合输入框
      inputElement.addEventListener('focus', handleFocus);
      // 添加blur事件监听，恢复页面滚动位置
      inputElement.addEventListener('blur', handleBlur);
    }
    return () => {
      if (!!val?.current?.currentTime) {
        dispatch({
          type: 'PlanetChatRoom/saveVideoTime',
          payload: {
            wxUserId: wxUserId,
            spaceId: SpaceInfo.id,
            videoSecond: _.floor(val.current.currentTime, 0),
          },
        });
      }
      if (inputElement) {
        inputElement.removeEventListener('focus', handleFocus);
        inputElement.removeEventListener('blur', handleBlur);
      }
      clearInterval(interval);
    }; // 在组件卸载时清除定时器
  }, []);

  const resetTimer = () => {
    clearTimeout(timer);
    setTimer(setTimeout(handleComplete, 5000));
  };
  const handleClick = useDebounce((e) => {
    if (!!isHiddenControlArea) {
      setIsHiddenControlArea(false);
      resetTimer();
    } else {
      // 当打开控制区域时则隐藏
      setIsHiddenControlArea(true);
    }
  }, 500);
  const handleComplete = () => {
    setIsHiddenControlArea(true);
  };

  const preventDefaultByHandler = (e) => {
    e.preventDefault();
  };

  // 定义handleFocus事件处理函数
  const handleFocus = () => {
    const inputElement = inputRef.current;
    setIsShowKeyboard(true);
    if (inputElement) {
      // 获取当前页面滚动位置
      const scrollY = window.scrollY;
      // 计算输入框在页面中的位置
      const inputTop = inputElement.getBoundingClientRect().top;
      // 计算需要滚动的距离
      const scrollToY = scrollY + inputTop;

      setTimeout(() => {
        document
          .getElementsByTagName('body')[0]
          .addEventListener('touchmove', preventDefaultByHandler, { passive: false }); // 阻止默认事件
        // window.scrollTo({ top:  window.innerHeight, left: 0, behavior: 'smooth' });
        let userAgent = navigator.userAgent;
        if (/iPhone/.test(userAgent)) {
          const versionMatch = userAgent.match(/iPhone OS (\d+)/);
          if (versionMatch && parseInt(versionMatch[1]) > 15) {
            // console.log('当前设备为 iPhone，系统版本 > 15，执行的滚动距离');
            inputElement.scrollTo({
              top: (inputElement.scrollHeight - window.innerHeight - 100) / 2,
              behavior: 'smooth',
            });
          } else if (versionMatch && parseInt(versionMatch[1]) <= 15) {
            // console.log('当前设备为 iPhone，系统版本 <= 15， 执行的滚动距离');
            window.scrollTo({ top: window.innerHeight, left: 0, behavior: 'smooth' });
          }
        } else if (/Android/.test(userAgent)) {
          // console.log('当前设备为 Android，返回 3');
          window.scrollTo({ top: window.innerHeight, left: 0, behavior: 'smooth' });
        } else {
          window.scrollTo({ top: window.innerHeight, left: 0, behavior: 'smooth' });
        }
      }, 100);

      // inputElement.scrollTo({ top: ((inputElement.scrollHeight - window.innerHeight - 200) / 2), behavior: 'smooth' });
    }
  };

  // 定义handleBlur事件处理函数
  const handleBlur = () => {
    setIsShowKeyboard(false);
    document
      .getElementsByTagName('body')[0]
      .removeEventListener('touchmove', preventDefaultByHandler, { passive: false }); // 打开默认事件
    // 恢复页面滚动位置
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  useEffect(() => {
    val.current = playerStateData;
  }, [playerStateData]);

  useEffect(() => {
    if (isInitialized) {
      getMsgContentHight(); // 获取消息内容高度
    }
    window.addEventListener('resize', () => {
      if (!isShowKeyboard) {
        setTimeout(() => {
          getMsgContentHight(); // 获取消息内容高度
        }, 500);
      }
    });
  }, [isInitialized]);

  // 关闭或开启弹幕 默认开启
  useEffect(() => {
    if (HiddenDanmu) {
      RTCDanmu && RTCDanmu.stop();
    } else {
      RTCDanmu && RTCDanmu.start();
    }
  }, [HiddenDanmu]);

  // 接收申请连麦转换身份
  useEffect(async () => {
    if (RTC && statusBySpaceInfo == 1 && currentUserType == 3) {
      setHandUpStatusTypeEnter(false);
    }
  }, [handUpStatusType]);

  const setIsShowCameraListByAutoExpandGuestArea = () => {
    if (autoExpandGuestArea == 1) {
      setIsShowCameraList(true);
      dispatch({ type: 'PlanetChatRoom/setState', payload: { autoExpandGuestArea: null } });
    } else if (autoExpandGuestArea == 2 && Array.isArray(userCameraRemoteStreamList)) {
      if (userCameraRemoteStreamList) {
        const allMutedAudioFalse = userCameraRemoteStreamList.every((item) => item.mutedAudio);
        const allMutedVideoFalse = userCameraRemoteStreamList.every((item) => item.mutedVideo);
        // const anyHasVideoAndMutedAudioFalse = remoteStreamConfigList.some(item => !item.hasVideo && !item.mutedAudio);
        let allMutedBoom = allMutedAudioFalse && allMutedVideoFalse;
        if (allMutedBoom) {
          setIsShowCameraList(false);
        }
      }
      dispatch({ type: 'PlanetChatRoom/setState', payload: { autoExpandGuestArea: null } });
    }
  };

  // 是否自动展开嘉宾摄像头区域
  useEffect(setIsShowCameraListByAutoExpandGuestArea, [
    autoExpandGuestArea,
    userCameraRemoteStreamList,
  ]);

  useEffect(() => {
    // msg_content将内容区域 滚动到最底部
    setTimeout(() => {
      let msg_content = document.getElementById('msg_content');
      let msgItem_offsetHeight = document.getElementsByClassName(styles.msg_content_item)[0]
        ?.offsetHeight;
      if (msg_content) {
        msg_content.scrollTop = msg_content.scrollHeight;
      }
    }, 100);
  }, [newMySnedSeq]);

  useEffect(() => {
    setTimeout(() => {
      let msg_content = document.getElementById('msg_content');
      let msgItem_offsetHeight = document.getElementsByClassName(styles.msg_content_item)[0]
        ?.offsetHeight;
      if (msg_content) {
        msg_content.scrollTop = msg_content.scrollHeight;
      }
    }, 100);
  }, [
    Array.isArray(msgListBySENDAPPLAUSE) && msgListBySENDAPPLAUSE.length > 0
      ? msgListBySENDAPPLAUSE[msgListBySENDAPPLAUSE.length - 1].msgSeq
      : null,
  ]);

  useEffect(() => {
    // msg_content将内容区域 滚动到最底部
    setTimeout(() => {
      let msg_content = document.getElementById('msg_content');
      let msgItem_offsetHeight = document.getElementsByClassName(styles.msg_content_item)[0]
        ?.offsetHeight;
      if (msg_content) {
        msg_content.scrollTop = msg_content.scrollHeight;
      }
    }, 100);
  }, [msgContentHeight]);

  // 订阅video播放器的状态
  let handlePlayerStateChange = (state) => {
    setPlayerStateData(state);
    getSpaceBulletScreen(state); // 从获取的历史弹幕中 匹配时间发送弹幕
    findItemDanmuByCurrentTime(state); // 录播分时间获取弹幕消息 每1分钟请求一次
  };

  // 从获取的历史弹幕中 匹配时间发送弹幕
  let findItemDanmuByCurrentTime = _.throttle(async (state) => {
    const { recordStartTime } = videoList[0] || {};
    const { currentTime } = state || {};
    let currentTimeByDayJS = dayjs(recordStartTime, 'YYYY-MM-DD HH:mm:ss')
      .add(_.floor(currentTime, 0), 'second')
      .format('YYYY-MM-DD HH:mm:ss');
    let Danmulist = await dispatch({ type: 'PlanetChatRoom/getSpaceGroupMsgByMsgSeqByState' });
    let findItemByDanmulist =
      Danmulist &&
      Danmulist.find((item) => {
        return item.msgDataTime == currentTimeByDayJS;
      });
    if (findItemByDanmulist) {
      sendDanmu({
        text: findItemByDanmulist.bs,
        userInfoByDanmu: {
          id: findItemByDanmulist.wxUserId,
          name: findItemByDanmulist.name,
          currentUserType: findItemByDanmulist.currentUserType, // 当前用户类型 1:主播(可发言,有连麦列表权限,有开始直播,有结束直播) 2:嘉宾(可发言) 3:观众(无权限)
          headUrl: findItemByDanmulist.headUrlShow,
          imagePhotoPathShow: findItemByDanmulist.headUrlShow,
        },
      });
    }
  }, 1000);

  // 录播分时间获取弹幕消息 每1分钟请求一次
  let getSpaceBulletScreen = _.throttle((state) => {
    const { recordStartTime } = videoList[0] || {};
    const { currentTime } = state || {};
    let currentTimeByDayJS = dayjs(recordStartTime, 'YYYY-MM-DD HH:mm:ss')
      .add(_.floor(currentTime, 0), 'second')
      .format('YYYY-MM-DD HH:mm:ss');
    getSpaceBulletScreenByModel(currentTimeByDayJS);
  }, 60000);

  let onChangeBySlider = useDebounce((value) => {
    const { recordStartTime } = videoList[0] || {};
    videoPlayer?.currentTime(value);
    let currentTimeByDayJS = dayjs(recordStartTime, 'YYYY-MM-DD HH:mm:ss')
      .add(_.floor(value, 0), 'second')
      .format('YYYY-MM-DD HH:mm:ss');
    getSpaceBulletScreenByModel(currentTimeByDayJS);
  }, 100);

  const getSpaceBulletScreenByModel = async (currentTimeByDayJS) => {
    const dataByGetSpaceGroupMsg = await dispatch({
      type: 'PlanetChatRoom/getSpaceBulletScreen',
      payload: {
        spaceId: SpaceInfo.id,
        eventTime: currentTimeByDayJS,
        // msgSeq:msgSeq,
      },
    });
  };

  let getMsgContentScrollHeight = () => {
    let msg_content = document.getElementById('msg_content');
    return msg_content && msg_content.scrollHeight ? msg_content.scrollHeight - 100 : 0 - 100;
  };

  // 暴露给父组件的方法
  useImperativeHandle(props.onRefByVerticalLiveRoom, () => {
    return {
      sendDanmu: sendDanmu,
    };
  });

  // 初始化弹幕组件
  let initializationByDanmu = async () => {
    let playerByMS = document.getElementById('ms');

    let danmu = new DanmuJs({
      channelSize: 24, // 轨道大小
      container: document.querySelector('#vs'), //弹幕容器，该容器发生尺寸变化时会自动调整弹幕行为
      containerStyle: {
        //弹幕容器样式
        // zIndex: 1001
      },
      live: true,
      player: playerByMS, // 配合音视频元素（video或audio）同步使用时需提供该项
      isLive: true,
      // direction,
      comments: [], // 弹幕预存数组,配合音视频元素（video或audio）同步使用时需提供该项
      chaseEffect: false, // 开启滚动弹幕追逐效果, 默认为true
      mouseControl: true, // 打开鼠标控制, 打开后可监听到 bullet_hover 事件。danmu.on('bullet_hover', function (data) {})
      mouseControlPause: true, // 鼠标触摸暂停。mouseControl: true 生效
      // channelSize: 24,       // 轨道大小
      area: {
        // 弹幕显示区域
        start: 0, // 区域顶部到播放器顶部所占播放器高度的比例
        end: 1, // 区域底部到播放器顶部所占播放器高度的比例
      },
      dropStaleComments: true,
      needResizeObserver: true,
      // interval: 1500,
      // mouseControl: true,
      // mouseControlPause: false,
    });
    setRTCDanmu(danmu); // 存储danmu控制对象
  };

  const handleChangeByLocalStreamConfig = useCallback(
    (name, e) => {
      e && e.preventDefault();
      props.onChange && props.onChange({ name, stream: localStreamConfig.stream });
    },
    [localStreamConfig, props],
  );

  // 创建新Danmu text:弹幕内容, level:弹幕等级 1:普通弹幕 2:关注弹幕 3:高级弹幕
  const sendDanmu = ({ text, userInfoByDanmu }) => {
    // 由于修改竖屏视频流回显区域使用横屏组件 所以只需要横屏区域回显弹幕即可
    // 暂停使用竖屏区域注入弹幕功能
    // 普通弹幕
    // // currentUserType: 1主播 2嘉宾 3观众
    /*let elByDanmu = sendDanmuCreateItemDom({text,userInfoByDanmu})
    let idByNewDanmu = moment().format('YYHHmmss')
    RTCDanmu && RTCDanmu.sendComment({
      duration: 5000,
      start: 100,
      id:  parseInt(`${idByNewDanmu}${Math.floor(Math.random() * 10000)}`),
      el: elByDanmu, //弹幕文字内容
      style: {
        fontSize: '20px'
      }
    })*/
  };

  // 点击展示摄像头列表
  const onClickByHorizontalLiveRoom_camera_picture_Box = (e) => {
    e.stopPropagation();
    resetTimer();
    setIsShowCameraList(!isShowCameraList);
  };

  // 点击收藏空间
  const onClickByCollect = async () => {
    //[判定登录] 判定是否已登录,未登录则弹出登录弹窗
    if (isNotLogin) {
      setModalVisibleByUserTokenInvalid();
      return null;
    }
    let wxuserId = UerInfo && UerInfo.friUserId;
    await dispatch({
      type: 'PlanetChatRoom/spaceCollect',
      payload: {
        spaceId: SpaceInfo.id,
        wxUserId: wxuserId,
        collectType: isCollect == 1 ? 0 : 1, // 空间收藏 0:取消收藏 1:收藏
      },
    });
  };

  // 获取查看空间连麦列表
  const getHandUpList = async () => {
    let wxuserId = UerInfo && UerInfo.friUserId;
    await dispatch({
      type: 'PlanetChatRoom/getHandUpList',
      payload: {
        spaceId: SpaceInfo.id,
        wxUserId: wxuserId,
        pageNum: 1,
        pageSize: 100,
      },
    });
  };

  // 拒绝或准入申请进入会议成员
  const updateStarSpaceApplyAdmission = async (params) => {
    const {
      wxUserId,
      isAgree, // 是否同意：0申请中,1同意，2拒绝
      refuseAdmittance, // 操作类型：1拒绝、2准入
    } = params || {};
    const res = await dispatch({
      type: 'PlanetChatRoom/updateStarSpaceApplyAdmission',
      payload: {
        applyAdmissionId: wxUserId, //	是 1 申请记录ID
        refuseAdmittance: refuseAdmittance, //	是 1 操作类型：1:拒绝、2:准入
      },
    });
    const { code, content } = res || {};
    if (code == 200 && content) {
      await getApplyAdmissionList();
      await props.sendMessageByIm({
        dataType: UPDATA_STATE,
        description: JSON.stringify({ imUserId: 'ALL' }),
      });
    }
  };

  // 获取申请进入会议成员列表
  const getApplyAdmissionList = async () => {
    if (!SpaceInfo?.id) {
      return;
    }
    let response = await dispatch({
      type: 'PlanetChatRoom/getApplyAdmissionList',
      payload: {
        spaceId: SpaceInfo.id,
      },
    });
    if (!response || (response && response.code != 200)) {
      if (response && response.status != 401) {
        message.error('获取成员列表信息失败');
      }
      return;
    }
  };

  // 申请进入会议成员相关
  useEffect(() => {
    if (guestListType == 2) {
      getApplyAdmissionList();
    } else if (guestListType == 1) {
      getGuestList();
    }
  }, [guestListType]);

  // 关注 取关 专家
  // type: 1:关注后刷新嘉宾列表
  // type: 2:关注后刷新空间详情
  // type: 3:关注后刷新连麦列表
  const isFocusByOnClick = async ({ expertsUserId, isFocus, type }) => {
    //[判定登录] 判定是否已登录,未登录则弹出登录弹窗
    if (isNotLogin) {
      setModalVisibleByUserTokenInvalid();
      return null;
    }
    let wxuserId = UerInfo && UerInfo.friUserId;
    // 关注取消专家
    await dispatch({
      type: 'PlanetChatRoom/isFocus',
      payload: {
        wxUserId: wxuserId, // [string]	是 用户ID
        expertsUserId: expertsUserId, // [string]	是  被关注的人
        isFocus: isFocus, // [string]	是	1 关注 0取消关注
      },
    });
    // 取消关注专家
    if (type == 1) {
      await getGuestList();
    } else if (type == 2) {
      await getSpaceInfo();
    } else if (type == 3) {
      await getHandUpList();
    }
  };

  // 计算msg_content的高度
  const getMsgContentHight = useDebounce(() => {
    // 计算msg_content的高度
    let Hight_contentByNotBottom = document.getElementsByClassName(styles.contentByNotBottom)[0]
      ?.offsetHeight;
    // let Hight_contentByNotBottom = windowHeight

    let Hight_video_content = document.getElementsByClassName(styles.video_content)[0]
      ?.offsetHeight;
    let Hight_video_advertisement = document.getElementsByClassName(styles.video_advertisement)[0]
      ?.scrollHeight
      ? document.getElementsByClassName(styles.video_advertisement)[0]?.scrollHeight + 30
      : null;
    let Hight_LiveBookingWarp = document.getElementsByClassName(styles.LiveBookingWarp)[0]
      ?.scrollHeight
      ? document.getElementsByClassName(styles.LiveBookingWarp)[0]?.scrollHeight + 30
      : null;
    let Hight_video_TitleWarp = document.getElementsByClassName(styles.video_TitleWarp)[0]
      ?.scrollHeight;
    let Hight_video_TitleWarp_title = document.getElementsByClassName(
      styles.video_TitleWarp_title,
    )[0]?.scrollHeight;
    let Hight_msg_content_tab = document.getElementsByClassName(styles.msg_content_tab)[0]
      ?.scrollHeight;
    // let Hight_reminder_warp = document.getElementsByClassName(styles.reminder_warp)[0]?.offsetHeight

    // [212, 57, undefined, 64, 33, 21, 30]

    let ArrayByHight = [
      Hight_video_content,
      Hight_video_advertisement,
      Hight_LiveBookingWarp,
      Hight_video_TitleWarp,
      Hight_video_TitleWarp_title,
      Hight_msg_content_tab,
      // Hight_reminder_warp
    ];
    let accumulation = 0;
    ArrayByHight.map((item) => {
      if (item) {
        accumulation = accumulation + item;
      }
    });
    let msg_content_height = Hight_contentByNotBottom - accumulation;
    setMsgContentHeight(msg_content_height);
  }, 300);

  // 展示去登录弹窗
  const setModalVisibleByUserTokenInvalid = () => {
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: { ModalVisibleByUserTokenInvalid: true },
    });
  };

  return (
    <>
      <div className={styles.warp_content}>
        <ModalWXsubscribe
          open={!!isOpenByModalWXsubscribe}
          spaceInfo={SpaceInfo}
          clearParams={()=>{ setIsOpenByModalWXsubscribe(false); }}
        />
        <div
          style={{
            // minHeight:`${windowHeight}px`
            maxWidth: !!isHorizontalLive ? '100%' : '750px',
          }}
          className={styles.content}
        >
          <div
            className={classNames({
              [styles.contentByNotBottom]: !isHorizontalLive,
              [styles.contentByNotBottom_isHorizontal]: !!isHorizontalLive,
            })}
          >
            {/* 视频流显示区域 */}
            <div
              id={'videoContent'}
              onClick={handleClick}
              className={styles.video_content}
              /*style={{ height: !!isHorizontalLive ? `${getViewportHeightWithoutSafeArea()}px` : '57VW' }}*/
            >
              {/* 非全屏下才展示此竖屏下的返回按钮头部 */}
              {!isHorizontalLive && (
                <div
                  style={{ display: isHiddenControlArea ? 'none' : 'block' }}
                  className={styles.video_ReturnIcon_Warp}
                >
                  <div
                    onClick={(e) => {
                      // 点击后阻止事件冒泡,防止触发父元素的点击事件
                      e.stopPropagation();
                      resetTimer();
                      onClickBack();
                    }}
                    className={styles.ReturnIconBtn_warp}
                  >
                    <div className={styles.ReturnIconBtn}></div>
                  </div>
                </div>
              )}
              {/* 录播video */}
              {!ModalVisibleByKickedOut && // 是否显示弹窗(被踢出)
                !ModalVisibleBySpaceViolation && // 是否显示弹窗(空间违规下架弹窗)
                !ModalVisibleBySpaceRemoved && ( // 是否显示弹窗(该空间已下架)
                  <div className={styles.VideoWarp}>
                    {/* 去除原有video标签改用 横屏组件替代video标签 */}
                    {props.children}
                  </div>
                )}

              {/* 主持人控制区域 */}
              {props.PlanetChatRoom.isLive && !isHorizontalLive && (
                <div
                  style={{
                    display: isHiddenControlArea ? 'none' : 'flex',
                    justifyContent:
                      currentUserType == 3 && !isPublished ? 'flex-end' : 'space-between',
                  }}
                  className={styles.video_ModeratorControl}
                >
                  {props.PlanetChatRoom.isLive && currentUserType == 1 && (
                    <div
                      onClick={async (e) => {
                        // 点击后阻止事件冒泡,防止触发父元素的点击事件
                        e.stopPropagation();
                        resetTimer();
                        if (statusBySpaceInfo == 2) {
                          dispatch({
                            type: 'PlanetChatRoom/setState',
                            payload: {
                              ModalVisibleByAppointmentClosedSpace: true,
                            },
                          });
                        } else {
                          // 关闭空间
                          await dispatch({
                            type: 'PlanetChatRoom/setState',
                            payload: {
                              ModalVisibleByClosedSpace: true,
                            },
                          });
                        }
                      }}
                      className={styles.video_ModeratorControl_box}
                    >
                      <div className={styles.video_ModeratorControl_box_icon}>
                        <div className={styles.finish_live} />
                      </div>
                      <div className={styles.video_ModeratorControl_box_text_red}>关闭</div>
                    </div>
                  )}

                  {/* 麦克风静音/取消静音-按钮 */}
                  {props.PlanetChatRoom.isLive &&
                    isJoined &&
                    isPublished &&
                    (currentUserType == 1 ||
                      currentUserType == 2 ||
                      (currentUserType == 3 && handUpStatusType == 1)) && (
                      <div
                        onClick={(e) => {
                          // 点击后阻止事件冒泡,防止触发父元素的点击事件
                          e.stopPropagation();
                          resetTimer();
                          handleChangeByLocalStreamConfig('audio', null);
                        }}
                        className={styles.video_ModeratorControl_box}
                      >
                        <div className={styles.video_ModeratorControl_box_icon}>
                          <i
                            className={classNames({
                              [styles.SpatialDetail_SoundOff_btn]: true,
                              [styles.SpatialDetail_SoundOff_btn_Forbidden]:
                                localStreamConfig && !!localStreamConfig.mutedAudio,
                            })}
                          />
                        </div>
                        <div className={styles.video_ModeratorControl_box_text}>
                          {localStreamConfig && !!localStreamConfig.mutedAudio
                            ? '解除静音'
                            : '静音'}
                        </div>
                      </div>
                    )}

                  {/* 打开/关闭-摄像头 */}
                  {props.PlanetChatRoom.isLive &&
                    isJoined &&
                    isPublished &&
                    (currentUserType == 1 ||
                      currentUserType == 2 ||
                      (currentUserType == 3 && handUpStatusType == 1)) && (
                      <div
                        onClick={(e) => {
                          // 点击后阻止事件冒泡,防止触发父元素的点击事件
                          e.stopPropagation();
                          resetTimer();
                          handleChangeByLocalStreamConfig('video', null);
                        }}
                        className={styles.video_ModeratorControl_box}
                      >
                        <div className={styles.video_ModeratorControl_box_icon}>
                          <i
                            className={classNames({
                              [styles.SpatialDetail_Camera_btn]: true,
                              [styles.SpatialDetail_Camera_btn_off]:
                                localStreamConfig && !!localStreamConfig.mutedVideo,
                            })}
                          />
                        </div>
                        <div className={styles.video_ModeratorControl_box_text}>
                          {localStreamConfig && !!localStreamConfig.mutedVideo
                            ? '开启摄像头'
                            : '关闭摄像头'}
                        </div>
                      </div>
                    )}

                  {/* 屏幕分享按钮 */}
                  {props.PlanetChatRoom.isLive &&
                    // && !isMobile
                    isJoined &&
                    isPublished &&
                    !isMobile &&
                    (currentUserType == 1 || currentUserType == 2) &&
                    ((!!shareRemoteStreamConfig &&
                      localStreamConfig &&
                      !!localStreamConfig.shareDesk) ||
                      !shareRemoteStreamConfig) && (
                      <div
                        onClick={(e) => {
                          // 点击后阻止事件冒泡,防止触发父元素的点击事件
                          e.stopPropagation();
                          resetTimer();
                          if (isMobile) {
                            message.info('手机端暂不支持投屏，请在电脑端使用');
                          } else {
                            handleChangeByLocalStreamConfig('shareDesk', e);
                          }
                        }}
                        className={styles.video_ModeratorControl_box}
                      >
                        <div className={styles.video_ModeratorControl_box_icon}>
                          <i
                            className={classNames({
                              [styles.SpatialDetail_OpenScreen_btn]: true,
                              [styles.SpatialDetail_OpenScreen_btn_CloseScreen]:
                                localStreamConfig && !!localStreamConfig.shareDesk,
                            })}
                          />
                        </div>
                        <div
                          className={classNames({
                            [styles.video_ModeratorControl_box_text]: true,
                            [styles.video_ModeratorControl_box_text_Blue]:
                              localStreamConfig && !!localStreamConfig.shareDesk,
                          })}
                        >
                          {localStreamConfig && !!localStreamConfig.shareDesk ? '暂停投屏' : '投屏'}
                        </div>
                      </div>
                  )}

                  {/* 分享课件 */}
                  {props.PlanetChatRoom.isLive &&
                    // && !isMobile
                    isHavCourseware == 1 &&
                    isJoined &&
                    isPublished &&
                    (currentUserType == 1 || currentUserType == 2) &&
                    (
                      <div
                        onClick={async (e) =>  {
                          // 点击后阻止事件冒泡,防止触发父元素的点击事件
                          e.stopPropagation();
                          resetTimer();
                          if (!isOpenTEduBoard) {
                              /*
                                if(currentUserType == 1) {
                                  let res = await dispatch({
                                    type: 'PlanetChatRoom/stopWhiteboardPush',
                                    payload: {
                                      spaceId: SpaceInfo.id,
                                      tiwPushTaskId: null,
                                    }
                                  })
                                  if (res && res.code == 200) {
                                    dispatch({type: 'PlanetChatRoom/setState', payload: {isOpenTEduBoard: !isOpenTEduBoard}})
                                  }
                                }else {}
                              */
                            if(!!shareRemoteStreamConfig) {
                              message.warning('他人正在分享课件，此时无法发起课件分享');
                            }else {
                              dispatch({type: 'PlanetChatRoom/setState', payload: {isOpenTEduBoard: !isOpenTEduBoard}});
                            }
                          }else {
                            let res = await dispatch({
                              type: 'PlanetChatRoom/stopWhiteboardPush',
                              payload: { spaceId: SpaceInfo.id }
                            })
                            dispatch({type: 'PlanetChatRoom/setState', payload: {isOpenTEduBoard: !isOpenTEduBoard}})
                          }
                        }}
                        className={styles.video_ModeratorControl_box}
                      >
                        <div className={styles.video_ModeratorControl_box_icon}>
                          <i
                            className={classNames({
                              [styles.HorizontalLiveRoom_shared_screen_WhiteBoardButton]: true,
                              [styles.HorizontalLiveRoom_shared_screen_active_WhiteBoardButton_btn]:!!isOpenTEduBoard
                            })}
                          />
                        </div>
                        <div
                          className={classNames({
                            [styles.video_ModeratorControl_box_text]: true,
                            [styles.video_ModeratorControl_box_text_Blue]:!!isOpenTEduBoard
                          })}
                        >
                          {!!isOpenTEduBoard ? '停止分享' : '分享课件'}
                        </div>
                      </div>
                    )
                  }
                  {props.PlanetChatRoom.isLive &&
                    isJoined &&
                    isPublished &&
                    currentUserType == 1 && (
                      <div
                        onClick={(e) => {
                          // 点击后阻止事件冒泡,防止触发父元素的点击事件
                          e.stopPropagation();
                          resetTimer();
                          if (handUpType == 1) {
                            // 开启
                            openCloseHandUp({ handUpType: 2 });
                            // setContentListType(TypeMsgList);
                          } else {
                            openCloseHandUp({ handUpType: 1 });
                            setContentListType(TypeLienApplicationList);
                          }
                        }}
                        className={styles.video_ModeratorControl_box}
                      >
                        <div className={styles.video_ModeratorControl_box_icon}>
                          <i
                            className={classNames({
                              [styles.SpatialDetail_openLianMai_btn]: true,
                              [styles.SpatialDetail_SoundOff_btn_off_Connection]: handUpType == 1,
                            })}
                          />
                        </div>
                        <div
                          className={classNames({
                            [styles.video_ModeratorControl_box_text]: true,
                            [styles.video_ModeratorControl_box_text_Blue]: handUpType == 1,
                          })}
                        >
                          {handUpType == 1
                            ? `关闭${starSpaceTypeLianMaiText}`
                            : `开启${starSpaceTypeLianMaiText}`}
                        </div>
                      </div>
                    )}

                  {/* 录制视频 */}
                  {props.PlanetChatRoom.isLive &&
                    isJoined &&
                    isPublished &&
                    currentUserType == 1 && (
                      <div
                        onClick={(e) => {
                          // 点击后阻止事件冒泡,防止触发父元素的点击事件
                          e.stopPropagation();
                          resetTimer();
                          if (recordType == 1) {
                            // 开启
                            dispatch({
                              type: 'PlanetChatRoom/setState',
                              payload: { ModalVisibleByEndRecording: true },
                            });
                          } else {
                            liveRecord({ recordType: 1 });
                          }
                        }}
                        className={styles.video_ModeratorControl_box}
                      >
                        <div className={styles.video_ModeratorControl_box_icon}>
                          <i
                            className={classNames({
                              [styles.SpatialDetail_record_btn_luzhi]: true,
                              [styles.SpatialDetail_record_btn_luzhi_StartRecording]:
                                recordType == 1,
                            })}
                          />
                        </div>
                        <div
                          className={classNames({
                            [styles.video_ModeratorControl_box_text]: true,
                            [styles.video_ModeratorControl_box_text_red]: recordType == 1,
                          })}
                        >
                          {recordType == 1 ? formatTimeBySeconds(elapsedTime) : '录制'}
                        </div>
                      </div>
                    )}

                  {/* 全屏按钮 */}
                  {props.PlanetChatRoom.isLive &&
                    statusBySpaceInfo != 2 && ( // 状态：1直播中、2预约中、3弹幕轰炸中
                      <div
                        onClick={async (e) => {
                          // 点击后阻止事件冒泡,防止触发父元素的点击事件
                          e.stopPropagation();
                          resetTimer();
                          await dispatch({
                            type: 'PlanetChatRoom/setState',
                            payload: { isHorizontalLive: true },
                          });
                          await changeUrlParams({ isHorizontalLive: 1 });
                        }}
                        className={classNames(styles.video_ModeratorControl_box)}
                      >
                        <div className={styles.video_ModeratorControl_box_icon}>
                          <i
                            className={classNames({
                              [styles.SpatialDetail_Full_screen_btn]: true,
                            })}
                          />
                        </div>
                        <div
                          className={classNames({
                            [styles.video_ModeratorControl_box_text]: true,
                          })}
                        >
                          全屏
                        </div>
                      </div>
                    )}
                </div>
              )}
            </div>

            {/* ----- 视频外区域 ------  */}
            {!isHorizontalLive && (
              <div>
                {/* 广告区域 会议详情不展示广告*/}
                {!isHorizontalLive &&
                  !!spaceAdvertisingUrlShow &&
                  !isHiddenSpaceAdvertisingUrlShow &&
                  starSpaceType == 1 && (
                    <div className={styles.video_advertisement}>
                      <img
                        className={styles.video_advertisement_img}
                        src={spaceAdvertisingUrlShow}
                      />
                      <div
                        onClick={() => {
                          // 关闭广告区域
                          setIsHiddenSpaceAdvertisingUrlShow(true);
                          getMsgContentHight();
                        }}
                        className={styles.video_advertisement_close}
                      >
                        <i className={styles.video_advertisement_close_icon} />
                      </div>
                    </div>
                  )}

                {/* 直播预约 预约中状态2 */}
                {statusBySpaceInfo == 2 && (
                  <div className={styles.LiveBookingWarp}>
                    <div className={styles.LiveBookingLeft}>
                      <div className={styles.LiveBookingBox}>
                        <div className={styles.LiveBookingTitle}>
                          <div className={styles.LiveBookingAppmentTitle}>预约中...</div>
                          {/* 预约详情只能空间主持人查看   1主持人 2嘉宾 3观众*/}
                          {(currentUserType == 1 || currentUserType == 2) && (
                            <div
                              onClick={async () => {
                                setIsOpenLiveBookingPopup(true);
                                const dataByAppointmentList = await dispatch({
                                  type: 'PlanetChatRoom/getAppointmentList',
                                  payload: {
                                    spaceId: SpaceInfo.id,
                                    pageNum: 1,
                                    pageSize: 1,
                                  },
                                });
                                const { content } = dataByAppointmentList || {};
                                const { resultList } = content || {};
                                setHasMoreByLiveBookingList(
                                  Array.isArray(resultList) && resultList.length > 0,
                                );
                              }}
                              className={styles.LiveBookingAppmentInfoTitle}
                            >
                              预约详情
                              <i className={styles.LiveBookingAppmentInfoTitleIcon}></i>
                            </div>
                          )}
                        </div>
                        <div
                          className={classNames({
                            [styles.LiveBookingClosedSpace]: true,
                            // [styles.LiveBookingClosedSpaceMaxWidth]:currentUserType == 1,
                          })}
                        >
                          <span>开始时间：</span>
                          <span>{appointmentStartTime}</span>
                        </div>
                      </div>
                    </div>
                    <div className={styles.LiveBookingRight}>
                      {((currentUserType != 1 && isAppointment == 0) || isNotLogin) && (
                        <div
                          className={styles.LiveBookingBtn}
                          onClick={async () => {
                            //[判定登录] 判定是否已登录,未登录则弹出登录弹窗
                            if (isNotLogin) {
                              setModalVisibleByUserTokenInvalid();
                              return null;
                            }
                            // 点击预约直播间 预约成功后提示成功
                            const res = await dispatch({
                              type: 'PlanetChatRoom/liveAppointment',
                              payload: {
                                spaceId: SpaceInfo.id,
                                wxUserId: wxUserId,
                                appointmentType: 1, // 1预约 2取消预约
                                // wcAppId:
                              },
                            });
                            const { code, content } = res || {};
                            if (code == 200 && content) {
                              setIsOpenByModalWXsubscribe(true);
                              message.success('预约成功，开播前15分钟将通知您。');
                            }
                          }}
                        >
                          <i className={styles.SpatialDetai_StartLive_Icon}></i>抢先预约
                        </div>
                      )}
                      {currentUserType != 1 && isAppointment == 1 && (
                        <div
                          className={styles.CancelLiveBookingBtn}
                          onClick={() => {
                            dispatch({
                              type: 'PlanetChatRoom/setState',
                              payload: {
                                ModalVisibleByCancelAppointment: true,
                              },
                            });
                          }}
                        >
                          取消预约
                        </div>
                      )}

                      {currentUserType == 1 && (
                        <div
                          onClick={async () => {
                            //[判定登录] 判定是否已登录,未登录则弹出登录弹窗
                            if (isNotLogin) {
                              setModalVisibleByUserTokenInvalid();
                              return null;
                            }
                            let dataBySpaceInfo = await props.getSpaceInfo();
                            const { code, content } = dataBySpaceInfo || {};
                            if (
                              code == 200 &&
                              content &&
                              content.status == 2 &&
                              !content.videoList
                            ) {
                              // 打开马上开始提示弹窗
                              await dispatch({
                                type: 'PlanetChatRoom/setState',
                                payload: { ModalVisibleByStartLive: true },
                              });
                            } else {
                              // message.info('')
                            }
                          }}
                          className={styles.LiveBookingBtn}
                        >
                          <i className={styles.SpatialDetai_StartLive_Icon}></i>
                          马上开始
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* 标题区域 */}
                <div className={styles.video_TitleWarp}>
                  <div className={styles.video_Title_box_left}>
                    <div className={styles.AvatarByhostUserInfo}>
                      <Avatar userInfo={hostUserInfo} size={32} />
                    </div>
                    <div>
                      <div className={styles.video_Title_box_left_title}>
                        <div className={styles.video_Title_box_left_title_name_text}>
                          {hostUserInfo?.name}
                        </div>
                        {hostUserInfo && hostUserInfo.isFocus == 0 && hostUserInfo.isSelf == 0 && (
                          <div
                            onClick={() => {
                              //[判定登录] 判定是否已登录,未登录则弹出登录弹窗
                              if (isNotLogin) {
                                setModalVisibleByUserTokenInvalid();
                                return null;
                              }
                              isFocusByOnClick({
                                expertsUserId: hostUserInfo.wxUserId,
                                isFocus: 1,
                                type: 2, // 关注列表
                              });
                            }}
                            className={styles.video_Title_box_left_title_follow_btn}
                          >
                            关注
                          </div>
                        )}
                        {hostUserInfo && hostUserInfo.isFocus == 1 && hostUserInfo.isSelf == 0 && (
                          <div
                            onClick={() => {
                              isFocusByOnClick({
                                expertsUserId: hostUserInfo.wxUserId,
                                isFocus: 0,
                                type: 2, // 关注列表
                              });
                            }}
                            className={styles.video_Title_box_left_title_Unfollow_btn}
                          >
                            已关注
                          </div>
                        )}
                      </div>
                      <div className={styles.video_Title_box_left_title}>
                        {/* 类型：1直播，2会议 */}
                        {starSpaceType == 2 ? '会议' : '直播'}主持人
                      </div>
                    </div>
                  </div>
                  <div className={styles.video_Title_box_Right}>
                    <div className={styles.box_icon_warp} onClick={onClickByCollect}>
                      <div
                        className={classNames({
                          [styles.SpatialDetail_Collect_btn]: true,
                          [styles.SpatialDetail_Collect_btn_Open]: isCollect == 1,
                        })}
                      ></div>
                      <div
                        className={classNames({
                          [styles.box_icon_title]: true,
                          // [styles.box_icon_title_Blue]:isCollect == 1,
                        })}
                      >
                        {isCollect == 1 ? '已收藏' : '收藏'}
                      </div>
                    </div>

                    {/* 直播间预约状态无法打卡 */}
                    {statusBySpaceInfo && statusBySpaceInfo != 2 && (
                      <div
                        onClick={async () => {
                          //[判定登录] 判定是否已登录,未登录则弹出登录弹窗
                          if (isNotLogin) {
                            setModalVisibleByUserTokenInvalid();
                            return null;
                          }
                          if (isSignIn == 1 || currentUserType == 1) {
                            setIsOpenClockinPopup(true);
                            let dataBySignInList = await dispatch({
                              type: 'PlanetChatRoom/getSignInList',
                              payload: {
                                spaceId: SpaceInfo.id,
                                pageNum: 1,
                                pageSize: 10,
                              },
                            });
                            const { content } = dataBySignInList || {};
                            const { resultList } = content || {};
                            setHasMoreBySignInList(
                              Array.isArray(resultList) && resultList.length > 0,
                            );
                          } else {
                            props.sendMessageByIm({ dataType: SIGN_IN, description: '1' });
                            dispatch({
                              type: 'PlanetChatRoom/setState',
                              payload: {
                                SpaceInfo: {
                                  ...SpaceInfo,
                                  isSignIn: 1,
                                },
                              },
                            });
                          }
                        }}
                        className={styles.box_icon_warp}
                      >
                        <div
                          className={classNames({
                            [styles.SpatialDetail_ClockIn_btn]: true,
                            [styles.SpatialDetail_ClockIn_btn_complete]:
                              isSignIn == 1 || currentUserType == 1,
                          })}
                        ></div>
                        <div
                          className={classNames({
                            [styles.box_icon_title]: true,
                            [styles.box_icon_title_Blue]: isSignIn == 1 || currentUserType == 1,
                          })}
                        >
                          打卡
                        </div>
                      </div>
                    )}

                    {/*分享，APP环境中分享展示出来 */}
                    {
                      // (getOperatingEnv() == '2' || getOperatingEnv() == '7' || getOperatingEnv() == '5' || getOperatingEnv() == '6') &&
                      <div
                        onClick={() => {
                          //[判定登录] 判定是否已登录,未登录则弹出登录弹窗
                          if (isNotLogin) {
                            setModalVisibleByUserTokenInvalid();
                            return null;
                          }
                          shareOnClick();
                        }}
                        className={styles.box_icon_warp}
                      >
                        <div
                          className={classNames({
                            [styles.SpatialDetail_Share_btn]: true,
                          })}
                        ></div>
                        <div
                          className={classNames({
                            [styles.box_icon_title]: true,
                          })}
                        >
                          分享
                        </div>
                      </div>
                    }

                    {/*编辑*/}
                    {hostUserInfo?.wxUserId == UerInfo?.friUserId ? (
                      <EditSpaceInfo
                        spaceId={SpaceInfo?.id}
                        refreshFn={() => {
                          getSpaceInfo();
                          getHandUpList();
                          getGuestList();
                          window.scrollTo({ top: 0, behavior: 'smooth' });
                          props.sendMessageByIm({
                            dataType: UPDATA_STATE,
                            description: JSON.stringify({ imUserId: 'ALL' }),
                          });
                        }}
                      />
                    ) : null}
                  </div>
                </div>

                {/*标题区域*/}
                <div id={'SpatialTitle'} className={styles.video_TitleWarp_title}>
                  <div className={styles.video_Title_box_left}>
                    <div className={styles.video_Title_box_Lefttop}>
                      <div>{nameBySpaceInfo}</div>
                      {!!intro && intro.length > 0 && (
                        <div
                          onClick={() => {
                            setIsOpenSpatialIntroduction(!isOpenSpatialIntroduction);
                            getMsgContentHight();
                          }}
                          className={classNames({
                            [styles.Icon_ShowSpatialIntroduction]: true,
                            [styles.Icon_ShowSpatialIntroduction_Open]: isOpenSpatialIntroduction,
                          })}
                        ></div>
                      )}
                    </div>
                    <div className={styles.video_Title_box_LeftBottom}>
                      <div className={styles.left_content}>
                        <div className={styles.LiveViewPortControls_OnlinePopulation}>
                          <div className={styles.LiveViewPortControls_OnlinePopulation_icon} />
                          <div className={styles.LiveViewPortControls_OnlinePopulation_text}>
                            <span>{pv}观看</span>
                            <span> · </span>
                            <span>
                              {gdp}GDP
                              <QuestionCircleFilled
                                onClick={() => {
                                  Modal.show({
                                    bodyClassName: 'gdp_modal',
                                    content:
                                      (global.gdpExplain != null &&
                                        ReactHtmlParser(
                                          starSpaceType == 1
                                            ? global.gdpExplain[1].gdpContent
                                            : global.gdpExplain[3].gdpContent,
                                        )) ||
                                      '暂无GDP介绍',
                                    closeOnMaskClick: true,
                                  });
                                }}
                                style={{ fontSize: 12, color: '#999', marginLeft: 4 }}
                              />
                            </span>
                          </div>
                        </div>
                        {kingdomId && kingdomName && (
                          <div
                            className={styles.kingdomNameSpanWarp}
                            onClick={() => {
                              if (kingdomId) {
                                history.push(`/Kingdom/${kingdomId}`);
                              }
                            }}
                          >
                            <div className={styles.kingdomNameSpan}>{kingdomName}</div>
                            <div className={styles.video_Title_box_LeftBottom_icon} />
                          </div>
                        )}
                      </div>
                      <div className={styles.video_Title_box_Lefttop_nameBySpaceInfo}>
                        <div className={styles.video_Title_box_right}>
                          {/* 空间密码只能主持人查看   1主持人 2嘉宾 3观众*/}
                          {password && currentUserType == 1 && (
                            <div className={styles.video_Title_box_pwd}>
                              <i className={styles.SpatialDetai_video_pwd_Icon}></i>
                              {password}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    <div
                      className={classNames({
                        [styles.video_Title_box_LeftInfo]: isOpenSpatialIntroduction,
                        [styles.video_Title_box_LeftInfo_hidden]: !isOpenSpatialIntroduction,
                      })}
                    >
                      {intro}
                    </div>
                  </div>
                </div>

                {/* 自定义消息区域 */}
                <div className={styles.Warp_msg_content}>
                  <div className={styles.msg_content_tab_warp}>
                    <div className={styles.msg_content_tab}>
                      <div
                        onClick={() => {
                          // 点击动态
                          setContentListType(TypeMsgList);
                        }}
                        className={classNames({
                          [styles.msg_content_tab_item]: true,
                          [styles.msg_content_tab_item_active]: contentListType == TypeMsgList,
                        })}
                      >
                        <div>动态</div>
                        <div
                          className={classNames({
                            [styles.msg_content_tab_item_line]: contentListType == TypeMsgList,
                            [styles.msg_content_tab_item_line_placeholder]: true,
                          })}
                        ></div>
                      </div>

                      <div
                        onClick={() => {
                          // 点击嘉宾
                          setContentListType(TypeGuestlist);
                          setGuestListType(1);
                        }}
                        className={classNames({
                          [styles.msg_content_tab_item]: true,
                          [styles.msg_content_tab_item_active]: contentListType == TypeGuestlist,
                        })}
                      >
                        <div>{starSpaceType == 2 ? '成员' : '嘉宾'}</div>
                        <div
                          className={classNames({
                            [styles.msg_content_tab_item_line]: contentListType == TypeGuestlist,
                            [styles.msg_content_tab_item_line_placeholder]: true,
                          })}
                        ></div>
                      </div>

                      {props.PlanetChatRoom.isLive && isJoined && currentUserType == 1 && (
                        <div
                          onClick={() => {
                            // 点击连麦申请
                            setContentListType(TypeLienApplicationList);
                            getHandUpList();
                          }}
                          className={classNames({
                            [styles.msg_content_tab_item]: true,
                            [styles.msg_content_tab_item_active]:
                              contentListType == TypeLienApplicationList,
                          })}
                        >
                          <div>{starSpaceTypeLianMaiText}申请</div>
                          <div
                            className={classNames({
                              [styles.msg_content_tab_item_line]:
                                contentListType == TypeLienApplicationList,
                              [styles.msg_content_tab_item_line_placeholder]: true,
                            })}
                          ></div>

                          {/* 展示当前申请连麦人数 */}
                          {Array.isArray(handUpList) && handUpList.length > 0 && (
                            <i className={styles.HandUpListLength}>{handUpList.length}</i>
                          )}
                        </div>
                      )}
                    </div>

                    {/* 嘉宾视频全部展示按钮 */}
                    {contentListType == TypeGuestlist && statusBySpaceInfo == 1 && (
                      <div className={styles.Warp_msg_Al_guests}>
                        <span className={styles.Warp_msg_Al_guests_text}>嘉宾视频全部展示</span>
                        <Switch
                          onChange={(value) => {
                            dispatch({
                              type: 'PlanetChatRoom/setState',
                              payload: {
                                isModeMatrix: value,
                              },
                            });
                          }}
                          checked={isModeMatrix}
                          style={{
                            '--checked-color': '#009DFF',
                            '--height': '23px',
                            '--width': '35px',
                          }}
                        />
                      </div>
                    )}
                  </div>

                  {/*提醒*/}
                  {contentListType == TypeMsgList && (
                    <div
                      style={{ height: msgContentHeight }}
                      id={'msg_content'}
                      className={styles.msg_content}
                      onScroll={async (e) => {
                        const { target } = e || {};
                        const { scrollTop } = target || {};
                        if (scrollTop == 0) {
                          if (!hasMoreByMsgList) {
                            return;
                          }
                          const dataByGetSignInList = await dispatch({
                            type: 'PlanetChatRoom/getSpaceGroupMsg',
                            payload: {
                              spaceId: SpaceInfo.id,
                              msgSeq: lastSeq,
                              pageSize: 10,
                            },
                          });
                          const { content } = dataByGetSignInList || {};
                          const { resultList } = content || {};
                          setHasMoreByMsgList(Array.isArray(resultList) && resultList.length > 0);
                          target.scrollTop = 700;
                        }
                      }}
                    >
                      <div className={styles.reminder_warp}>
                        <div className={styles.reminder_icon} />
                        <div className={styles.reminder_text}>
                          医生星球禁止谈论政治、黄赌毒和辱骂等不文明行为
                        </div>
                      </div>

                      {Array.isArray(msgListBySENDAPPLAUSE) &&
                        msgListBySENDAPPLAUSE.map((item, idx) => {
                          const {
                            msgType,
                            name,
                            bs,
                            extension,
                            cnt,
                            msgGroupCount,
                            currentUserType: currentUserTypeByItem,
                          } = item || {};
                          const extensionObj = extension && JSON.parse(extension);
                          const currentUserTypeByDanmu =
                            extensionObj && extensionObj.currentUserType
                              ? extensionObj.currentUserType
                              : currentUserTypeByItem;

                          return (
                            <div key={idx} className={styles.msg_content_item}>
                              <div className={styles.msg_content_item_icon}>
                                <Avatar userInfo={item} size={24}></Avatar>
                              </div>
                              <div className={styles.msg_content_item_user_warp}>
                                <div className={styles.msg_content_item_user}>
                                  <div className={styles.msg_content_item_user_name}>{name}</div>
                                  <div>：</div>
                                </div>
                                {msgType == SEND_APPLAUSE && (
                                  <div className={styles.msg_content_item_msgContent}>
                                    <div>送掌声</div>
                                    <div
                                      className={styles.HorizontalLiveRoom_send_guzhang_icon}
                                    ></div>
                                    <div className={styles.HorizontalLiveRoom_send_message_icon}>
                                      ×{msgGroupCount ? msgGroupCount : cnt}
                                    </div>
                                  </div>
                                )}
                                {msgType == SEND_FLOWERS && (
                                  <div className={styles.msg_content_item_msgContent}>
                                    <div>送花花</div>
                                    <div
                                      className={
                                        styles.HorizontalLiveRoom_send_flowers_content_icon
                                      }
                                    ></div>
                                    <div className={styles.HorizontalLiveRoom_send_message_icon}>
                                      ×{msgGroupCount ? msgGroupCount : cnt}
                                    </div>
                                  </div>
                                )}
                                {msgType == SIGN_IN && (
                                  <div className={styles.msg_content_item_msgContent}>
                                    <div>已打卡</div>
                                  </div>
                                )}
                                {msgType == BULLET_SCREEN && (
                                  <div
                                    className={classNames({
                                      [styles.msg_content_item_msgContent]: true,
                                      [styles.msg_content_item_msgContent_emcee]:
                                        currentUserTypeByDanmu == 1, // 当前用户类型 1:主播(可发言,有连麦列表权限,有开始直播,有结束直播) 2:嘉宾(可发言) 3:观众(无权限)
                                    })}
                                  >
                                    <div>{bs}</div>
                                  </div>
                                )}
                              </div>
                            </div>
                          );
                        })}

                      {/* 打卡卡片 */}
                      {/*<div className={styles.PunchCard}>
                    <div className={styles.avatar_box}>
                      <img className={styles.avatar_img} src={defaultHeadIcon}/>
                    </div>
                    <div className={styles.PunchUserInfo}>
                      <div className={styles.PunchUserInfo_name}>瑞泰总院正畸朱主任</div>
                      <div className={styles.PunchUserInfo_stutes}>已打卡</div>
                    </div>
                  </div>*/}
                    </div>
                  )}

                  {/* 嘉宾列表 */}
                  {contentListType == TypeGuestlist && (
                    <div className={styles.Guestlist}>
                      {currentUserType == 1 && starSpaceType == 2 && (
                        <div className={styles.tabHeader}>
                          <div
                            onClick={() => {
                              setGuestListType(1);
                            }}
                            className={classNames({
                              [styles.tab_item]: true,
                              [styles.tab_item_active]: guestListType == 1,
                            })}
                          >
                            参会人
                          </div>
                          {/* 类型：1直播，2会议 */}

                          <div
                            onClick={() => {
                              setGuestListType(2);
                            }}
                            className={classNames({
                              [styles.tab_item]: true,
                              [styles.tab_item_active]: guestListType == 2,
                            })}
                          >
                            申请进入会议成员
                            {Array.isArray(applyAdmissionList) &&
                              applyAdmissionList.filter((item) => item && item.isAgree == 0)
                                .length > 0 && (
                                <i className={styles.tab_item_num}>
                                  {Array.isArray(applyAdmissionList) &&
                                    applyAdmissionList.filter((item) => item && item.isAgree == 0)
                                      .length}
                                </i>
                              )}
                          </div>
                        </div>
                      )}
                      {guestListType == 1 && (
                        <div
                          style={{
                            height: msgContentHeight,
                          }}
                          className={styles.GuestlistWarp}
                        >
                          {Array.isArray(guestListInfo) &&
                            guestListInfo.map((item, index) => {
                              const {
                                name,
                                headUrlShow,
                                postTitleDictName,
                                isFocus,
                                isSelf,
                                wxUserId: wxUserIdByItem,
                              } = item || {};
                              return (
                                <div key={index} className={styles.GuestlistItem}>
                                  <div className={styles.GuestlistItemLeft}>
                                    <div className={styles.GuestlistItemAvatar}>
                                      <Avatar userInfo={item} size={24}></Avatar>
                                    </div>
                                    <div className={styles.GuestlistItemName}>{name}</div>
                                  </div>
                                  <div className={styles.GuestlistItemRigth}>
                                    {isFocus == 1 && isSelf == 0 && (
                                      <div
                                        onClick={() => {
                                          isFocusByOnClick({
                                            expertsUserId: wxUserIdByItem,
                                            isFocus: 0, // 取消关注
                                            type: 1, // 关注列表
                                          });
                                        }}
                                        className={styles.UnfollowGuestlistBtn}
                                      >
                                        取消关注
                                      </div>
                                    )}
                                    {isFocus == 0 && isSelf == 0 && (
                                      <div
                                        className={styles.FollowGuestlistBtn}
                                        onClick={() => {
                                          isFocusByOnClick({
                                            expertsUserId: wxUserIdByItem,
                                            isFocus: 1, // 去关注
                                            type: 1, // 关注列表
                                          });
                                        }}
                                      >
                                        关注
                                      </div>
                                    )}
                                  </div>
                                </div>
                              );
                            })}
                        </div>
                      )}

                      {guestListType == 2 && (
                        <div
                          style={{
                            height: msgContentHeight,
                          }}
                          className={styles.GuestlistWarp}
                        >
                          {Array.isArray(applyAdmissionList) &&
                            applyAdmissionList.map((item, index) => {
                              const {
                                id,
                                userName,
                                isAgree, // 是否同意：0申请中,1同意，2拒绝
                                wxUserId: wxUserIdByItem,
                              } = item || {};
                              if (isAgree != 0) {
                                return <div></div>;
                              }
                              return (
                                <div key={index} className={styles.GuestlistItem}>
                                  <div className={styles.GuestlistItemLeft}>
                                    <div className={styles.GuestlistItemAvatar}>
                                      <Avatar userInfo={item} size={24}></Avatar>
                                    </div>
                                    <div className={styles.GuestlistItemName}>{userName}</div>
                                  </div>
                                  <div className={styles.GuestlistItemRigth_ApplicationMeeting}>
                                    <div
                                      className={styles.refuse}
                                      onClick={() => {
                                        updateStarSpaceApplyAdmission({
                                          wxUserId: id,
                                          refuseAdmittance: 1, // 操作类型：1拒绝、2准入
                                        });
                                      }}
                                    >
                                      拒绝
                                    </div>
                                    {isAgree == 0 && (
                                      <div
                                        className={styles.allow}
                                        onClick={() => {
                                          updateStarSpaceApplyAdmission({
                                            wxUserId: id,
                                            refuseAdmittance: 2, // 操作类型：1拒绝、2准入
                                          });
                                        }}
                                      >
                                        准入
                                      </div>
                                    )}
                                  </div>
                                </div>
                              );
                            })}
                        </div>
                      )}
                    </div>
                  )}

                  {/* 连麦申请列表 */}
                  {contentListType == TypeLienApplicationList && (
                    <div className={styles.GuestlistWarp}>
                      {(!handUpList || (Array.isArray(handUpList) && handUpList.length == 0)) && (
                        <div className={styles.SpatialDetail_wheat_connection_Warp}>
                          <div className={styles.SpatialDetail_wheat_connection}></div>
                          <div className={styles.SpatialDetail_wheat_connection_text}>
                            暂无{starSpaceTypeLianMaiText}申请
                          </div>
                        </div>
                      )}

                      {Array.isArray(handUpList) &&
                        handUpList.map((value, index) => {
                          let {
                            applyDate, // : "2023-06-26 17:49:08"
                            headUrlShow, // : null
                            imUserId, // : "8555007f9059dd9fddf6190870706d82"
                            name, // : "张志军"
                            statusType, // : 0
                            wxUserId, // : 54
                          } = value || {};

                          return (
                            <div key={index} className={styles.GuestlistItem}>
                              <div className={styles.GuestlistItemLeft}>
                                <div className={styles.GuestlistItemAvatar}>
                                  <Avatar userInfo={value} size={24}></Avatar>
                                </div>
                                <div className={styles.GuestlistItemName}>{name}</div>
                                {statusType == 1 && (
                                  <div className={styles.lianmaiIconWarp}>
                                    <div className={styles.lianmaiIcon}></div>
                                    <div className={styles.lianmaiText}>
                                      {starSpaceTypeLianMaiText}中...
                                    </div>
                                  </div>
                                )}
                              </div>
                              <div className={styles.GuestlistItemRigth}>
                                {statusType == 1 && (
                                  <div
                                    onClick={() => {
                                      // 1 接受连麦 2暂不同意 3下麦
                                      dispatch({
                                        type: 'PlanetChatRoom/setState',
                                        payload: {
                                          ModalVisibleByForceWheat: {
                                            statusType: 3,
                                            guestUserId: wxUserId,
                                            imUserId: imUserId,
                                          },
                                        },
                                      });
                                      // operateHandUp({statusType:3,guestUserId:wxUserId,imUserId:imUserId})
                                    }}
                                    className={styles.forceXiamai}
                                  >
                                    强制下麦
                                  </div>
                                )}
                                {statusType == 0 && (
                                  <div
                                    onClick={() => {
                                      // 1 接受连麦 2暂不同意 3下麦
                                      operateHandUp({
                                        statusType: 1,
                                        guestUserId: wxUserId,
                                        imUserId: imUserId,
                                      });
                                    }}
                                    className={styles.FollowGuestlistBtn}
                                  >
                                    接受
                                  </div>
                                )}
                              </div>
                            </div>
                          );
                        })}
                    </div>
                  )}

                  {/* 连麦卡片 */}
                  {/*<div className={styles.LianMaiCard}>
                <div className={styles.LianMaiCard_avatar_box}>
                  <img className={styles.avatar_img} src={defaultHeadIcon}/>
                </div>
                <div className={styles.LianMaiCard_UserInfo}>
                  <div className={styles.LianMaiCard_UserInfo_name}>瑞泰总院朱医生</div>
                  <div className={styles.LianMaiCard_UserInfo_stutes}>连麦中</div>
                </div>
              </div>*/}

                  {/*  */}
                  {/*<div className={styles.video_interaction}>
                <div className={styles.buttonContainer}>
                  <Button style={{marginRight:'20px'}}  id="join" variant="contained" color="primary" className={ isJoined ? styles.forbidden : ''} onClick={handleJoin}>加入</Button>
                  <Button style={{marginRight:'20px'}}  id="leave" variant="contained" color="primary" onClick={handleLeave}>离开</Button>
                  <Button style={{marginRight:'20px'}}  id="publish" variant="contained" color="primary" className={ isPublished ? styles.forbidden : '' } onClick={handlePublish}>发布</Button>
                  <Button id="unpublish" variant="contained" color="primary" onClick={handleUnPublish}>停止发布</Button>
                </div>
                <div className={styles.buttonContainer} style={{marginTop:'20px'}}>
                  <Button style={{marginRight:'20px'}}  id="join" variant="contained" color="primary" onClick={onClickBySendDanmu}>发弹幕</Button>
                  <Button style={{marginRight:'20px'}}  id="join" variant="contained" color="primary" onClick={onClickBySendDanmu}>连麦</Button>
                  <Button style={{marginRight:'20px'}}  id="join" variant="contained" color="primary" onClick={handleLocalChange('shareDesk')}>分享屏幕</Button>
                </div>
              </div>*/}
                </div>
                {/*举手类型    0:开启 1:关闭*/}
                {/* 申请连麦 handUpStatusType 申请连麦状态类型：0申请连麦中 1接受连麦中，默认null*/}
                {props.PlanetChatRoom.isLive &&
                  isJoined &&
                  currentUserType == 3 &&
                  (handUpType == 1 || handUpStatusType != null) && (
                    <div
                      onClick={() => {
                        if (isNotLogin) {
                          setModalVisibleByUserTokenInvalid();
                          return null;
                        }
                        if (SpaceInfo?.handUpStatusType == 0) {
                          // 当前是申请连麦状态
                          if (!handUpStatusTypeEnter) {
                            // 第一次点击取消连麦 提示吐司
                            setHandUpStatusTypeEnter(true);
                            message.warning(`再次点击取消${starSpaceTypeLianMaiText}`);
                          } else {
                            // 第二次点击取消连麦
                            props.onClickLianMai();
                            setHandUpStatusTypeEnter(false);
                          }
                        } else if (SpaceInfo?.handUpStatusType == 1) {
                          // 当前是连麦状态
                          if (!handUpStatusTypeEnter) {
                            // 第一次点连麦中 提示吐司
                            setHandUpStatusTypeEnter(true);
                            message.warning('再次点击下麦');
                          } else {
                            // 第二次点击下麦
                            props.onClickLianMai();
                            // setHandUpStatusTypeEnter(false)
                          }
                        } else {
                          props.onClickLianMai();
                          setHandUpStatusTypeEnter(false);
                        }
                      }}
                      className={classNames({
                        [styles.ApplyConnectWheat]: true,
                        [styles.CancelApplyConnectWheat]:
                          SpaceInfo?.handUpStatusType != null && handUpStatusTypeEnter,
                      })}
                    >
                      <div className={styles.lianmaiWarp}>
                        <div
                          className={classNames({
                            [styles.SpatialDetaiLianmaiBtnIcon]: true,
                            [styles.SpatialDetaiLianmaiBtnIconCancel]:
                              SpaceInfo?.handUpStatusType == 1 && handUpStatusTypeEnter,
                          })}
                        ></div>
                        <div>
                          {SpaceInfo?.handUpStatusType == null && `申请${starSpaceTypeLianMaiText}`}
                          {SpaceInfo?.handUpStatusType == 0 &&
                            !handUpStatusTypeEnter &&
                            '等待中...'}
                          {SpaceInfo?.handUpStatusType == 0 &&
                            handUpStatusTypeEnter &&
                            `取消${starSpaceTypeLianMaiText}`}

                          {SpaceInfo?.handUpStatusType == 1 &&
                            !handUpStatusTypeEnter &&
                            `${starSpaceTypeLianMaiText}中...`}
                          {SpaceInfo?.handUpStatusType == 1 && handUpStatusTypeEnter && '下麦'}
                        </div>
                      </div>
                    </div>
                  )}

                {/* 提交弹幕区域 */}
                <div
                  id={'sendBottem'}
                  className={classNames({
                    [styles.sendDanmuWarp]: true,
                  })}
                >
                  <div className={styles.sendDanmuBox}>
                    <div className={styles.sendDanmuBox_inputWarp}>
                      <input
                        ref={inputRef}
                        onKeyDown={(value) => {
                          if (value.keyCode == 13 && value.target.value.trim().length > 0) {
                            //[判定登录] 判定是否已登录,未登录则弹出登录弹窗
                            if (isNotLogin) {
                              setModalVisibleByUserTokenInvalid();
                              return null;
                            }
                            const { currentTime } = playerStateData || {};
                            // 发送弹幕消息
                            props.sendMessageByIm &&
                              props.sendMessageByIm({
                                dataType: BULLET_SCREEN,
                                description: value.target.value,
                                relativeTime: currentTime ? _.floor(currentTime, 0) : null,
                              });
                            inputRef.current.value = '';
                            inputRef.current.blur();
                          }
                        }}
                        placeholder={'来发弹幕'}
                        className={styles.sendDanmuBoxInput}
                        type="text"
                        enterKeyHint="send"
                        onBlur={() => {
                          // if (isIOS()) { return null; }
                          setTimeout(() => {
                            let DerailWarp = document.querySelector('#DerailWarp');
                            let sendBottem = document.querySelector('#sendBottem');
                            sendBottem.style.top = `unset`;
                            sendBottem.style.bottom = `0px`;
                            DerailWarp.style.height = `${window.innerHeight}px`;
                          }, 300);
                          if (PopupKeyboard) {
                            clearTimeout(PopupKeyboard);
                          }
                        }}
                        onFocus={() => {
                          // 当弹出软键盘后将弹幕区域高度去除用于弹出软键盘后
                          let DerailWarp = document.querySelector('#DerailWarp');
                          let sendBottem = document.querySelector('#sendBottem');
                          let windowHeight = parseInt(DerailWarp.style.height.replace('px', '')); // 原始高度
                          document.documentElement.scrollTop = 0;
                          DerailWarp.scrollTop = 0;

                          if (isIOS()) {
                            // ios 高度
                            PopupKeyboard = setTimeout(() => {
                              if (DerailWarp && DerailWarp.style.height) {
                                const originHeight = document.documentElement.clientHeight; // document高度
                                const newHeight = window.visualViewport.height; // 视口高度
                                // 获取键盘高度-keyboardHeight
                                const keyboardHeight = originHeight - newHeight;
                                let windowHeightBykeyboardOpen = windowHeight - keyboardHeight;
                                // message.info(`windowHeightBykeyboardOpen:${windowHeightBykeyboardOpen}, keyboardHeight:${keyboardHeight}`);
                                if (keyboardHeight > 0) {
                                  DerailWarp.style.height = `${windowHeightBykeyboardOpen}px`;
                                  DerailWarp.scrollTo({ top: `0px` });
                                  document.documentElement.scrollTop = 0;
                                }
                              }
                            },500);
                          } else {
                            // android 当键盘弹出时视口高度会变小
                            PopupKeyboard = setTimeout(() => {
                              if (DerailWarp && DerailWarp.style.height) {
                                if (window.innerHeight != windowHeight) {
                                  DerailWarp.style.height = `${window.innerHeight}px`;
                                  DerailWarp.scrollTo({ top: `0px` });
                                }
                              }
                            }, 500);
                          }
                        }}
                      />
                      <div className={styles.lineInput}></div>
                      <i
                        onClick={() => {
                          dispatch({
                            type: 'PlanetChatRoom/setState',
                            payload: {
                              HiddenDanmu: !HiddenDanmu,
                            },
                          });
                        }}
                        className={classNames({
                          [styles.ShowDanmuIcon]: !HiddenDanmu,
                          [styles.ShowDanmuIconHidden]: !!HiddenDanmu,
                        })}
                      ></i>
                    </div>
                  </div>
                  <div className={styles.sendDanmuBoxRight}>
                    {/* 鼓掌 */}
                    <div
                      onClick={() => {
                        //[判定登录] 判定是否已登录,未登录则弹出登录弹窗
                        if (isNotLogin) {
                          setModalVisibleByUserTokenInvalid();
                          return null;
                        }
                        props.sendMessageByIm({ dataType: SEND_APPLAUSE, description: '1' });
                      }}
                      className={styles.sendDanmuBoxGuzhang}
                    >
                      <i className={styles.SpatialDetail_clap_Btn}></i>
                    </div>
                    {/* 送花 */}
                    <div
                      onClick={() => {
                        //[判定登录] 判定是否已登录,未登录则弹出登录弹窗
                        if (isNotLogin) {
                          setModalVisibleByUserTokenInvalid();
                          return null;
                        }
                        props.sendMessageByIm({ dataType: SEND_FLOWERS, description: '1' });
                      }}
                      className={styles.sendDanmuBoxSonghua}
                    >
                      <i className={styles.SpatialDetail_flowers_Btn}></i>
                    </div>
                  </div>
                </div>
              </div>
            )}
            {/* ---- 视频外区域结束 -----*/}
          </div>
          {/*在Friday App内打开弹窗 */}
          <DownloadAppBtn BoxStyle={!!isHorizontalLive?null:{bottom:'80px'}} info={{roomId:SpaceInfo?.id}} type={1} />
          {
            currentWatchMode==1&&<GoBackHomeIcon />
          }
        </div>
      </div>

      {/* 打卡详情列表 */}
      <div className={styles.PopupWarp}>
        <Popup
          visible={isOpenClockinPopup}
          onMaskClick={() => {
            setIsOpenClockinPopup(false);
          }}
          bodyStyle={{
            borderTopLeftRadius: '12px',
            borderTopRightRadius: '12px',
            minHeight: '50vh',
            paddingLeft: '16px',
            paddingRight: '16px',
          }}
        >
          <div className={styles.PopupContent}>
            <div className={styles.lineWarp}>
              <div className={styles.line}></div>
            </div>
            <div className={styles.content}>
              <div className={styles.contentTitle}>打卡详情</div>
              <div className={styles.contentitemBox}>
                {signInList &&
                  Array.isArray(signInList) &&
                  signInList.map((item, index) => {
                    const {
                      createDate,
                      name,
                      phoneTail, // 手机尾号
                    } = item || {};

                    return (
                      <div key={index} className={styles.contentitemWarp}>
                        <div className={styles.contentitem}>
                          <div className={styles.contentitemAvatar}>
                            <Avatar userInfo={item} size={24}></Avatar>
                          </div>
                          <div className={styles.contentitemName}>{name}</div>
                        </div>
                        <div className={styles.contentPhone}>尾号{phoneTail}</div>
                        <div className={styles.contentTime}>
                          <span>{createDate}</span>
                        </div>
                      </div>
                    );
                  })}
                <InfiniteScroll
                  loadMore={async () => {
                    const { total, pageNum, pageSize } = signInObj || {};
                    const dataByGetSignInList = await dispatch({
                      type: 'PlanetChatRoom/getSignInList',
                      payload: {
                        spaceId: SpaceInfo.id,
                        pageNum: pageNum ? pageNum + 1 : 1,
                        pageSize: pageSize,
                      },
                    });
                    const { content } = dataByGetSignInList || {};
                    const { resultList } = content || {};
                    setHasMoreBySignInList(Array.isArray(resultList) && resultList.length > 0);
                  }}
                  threshold={20}
                  hasMore={hasMoreBySignInList}
                />
              </div>
            </div>
          </div>
          {/*<div onClick={()=>{setIsOpenClockinPopup(false)}} className={styles.btn_Off}>
            关闭
          </div>*/}
        </Popup>
      </div>

      {/* 预约详情列表 */}
      <div className={styles.PopupWarp}>
        <Popup
          visible={isOpenLiveBookingPopup}
          onMaskClick={() => {
            setIsOpenLiveBookingPopup(false);
          }}
          bodyStyle={{
            borderTopLeftRadius: '12px',
            borderTopRightRadius: '12px',
            minHeight: '50vh',
            paddingLeft: '16px',
            paddingRight: '16px',
          }}
        >
          <div className={styles.PopupContent}>
            <div className={styles.lineWarp}>
              <div className={styles.line}></div>
            </div>
            <div className={styles.content}>
              <div className={styles.contentTitle}>预约详情</div>
              <div className={styles.contentitemBox}>
                {BookingList &&
                  Array.isArray(BookingList) &&
                  BookingList.map((item, index) => {
                    const {
                      createDate,
                      name,
                      phoneTail, // 手机尾号
                    } = item || {};

                    return (
                      <div key={index} className={styles.contentitemWarp}>
                        <div className={styles.contentitem}>
                          <div className={styles.contentitemAvatar}>
                            <Avatar userInfo={item} size={24}></Avatar>
                          </div>
                          <div className={styles.contentitemName}>{name}</div>
                        </div>
                        <div className={styles.contentPhone}>尾号{phoneTail}</div>
                        <div className={styles.contentTime}>
                          <span>{createDate}</span>
                        </div>
                      </div>
                    );
                  })}

                <InfiniteScroll
                  loadMore={async () => {
                    const { total, pageNum, pageSize } = BookingObj || {};
                    const dataByGetAppointmentList = await dispatch({
                      type: 'PlanetChatRoom/getAppointmentList',
                      payload: {
                        spaceId: SpaceInfo.id,
                        pageNum: pageNum ? pageNum + 1 : 1,
                        pageSize: pageSize,
                      },
                    });
                    const { content } = dataByGetAppointmentList || {};
                    const { resultList } = content || {};
                    setHasMoreByLiveBookingList(Array.isArray(resultList) && resultList.length > 0);
                  }}
                  threshold={20}
                  hasMore={hasMoreByLiveBookingList}
                />
              </div>
            </div>
          </div>
          {/*<div onClick={()=>{setIsOpenLiveBookingPopup(false)}} className={styles.btn_Off}>
            关闭
          </div>*/}
        </Popup>
      </div>
    </>
  );
};
export default connect(({ global, PlanetChatRoom, loading }: any) => ({
  global,
  PlanetChatRoom,
  loading,
}))(Index);
