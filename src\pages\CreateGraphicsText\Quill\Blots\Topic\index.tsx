import { Quill } from 'react-quill'
const Embed = Quill.import('blots/embed')

class TopicBlot extends Embed {
  static create(value) {
    let node = super.create();
    node.setAttribute('data-id', value.topicId);
    node.setAttribute('data-type', 'topic');
    const text = document.createTextNode(value.topicName)
    node.appendChild(text)
    return node;
  }

  static value(domNode: Element) {
    return {
      topicId: domNode.dataset.id,
      topicName: domNode.innerText,
    }
  }

  static formats(domNode: Element) {
    return {
      blotsName: 'topic',
    }
  }

  format(name: string, value: string) {

  }
}

TopicBlot.blotName = 'topic';                              // 格式名
TopicBlot.tagName = 'span';                                // dom标签
TopicBlot.className = 'quill_topic_format_wrap';           // dom类名

export default TopicBlot
