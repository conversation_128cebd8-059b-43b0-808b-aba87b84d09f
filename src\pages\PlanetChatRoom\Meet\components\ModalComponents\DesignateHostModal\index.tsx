/**
 * @Description: 会议详情-指定主持人弹窗
 */
import React, { useState, useEffect } from 'react';
import { history, connect, useRouteMatch } from 'umi'
import { Spin } from 'antd';
import { Modal, Toast } from 'antd-mobile';
import styles from './index.less';

import Avatar from '@/pages/PlanetChatRoom/components/Avatar' // 头像组件
import NoDataRender from '@/components/NoDataRender' // 暂无数据组件

// 图片图标
import checkIcon3 from '@/assets/GlobalImg/check_icon_3.png' // 未选中图标
import checkIcon4 from '@/assets/GlobalImg/check_icon_4.png' // 选中图标

interface PropsType {
  visible: boolean,          // 弹窗是否显示
  onCancel: () => void,      // 关闭弹窗
  designateHostModalOnOk: () => void,      // 指定并离开
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const match = useRouteMatch()

  const {
    visible,
    dispatch,
    loading,
    PlanetChatRoom,
    designateHostModalOnOk, // 点击指定并离开回调
    onClickEndMeeting, // 点击结束会议的回调
  } = props

  const {
    membersListInTheMeeting, // 空间在线成员列表
  } = PlanetChatRoom || {}

  const [checkedUserId, setCheckedUserId] = useState(null) // 选中的用户ID
  const [checkedImUserId, setCheckedImUserId] = useState(null) // 选中的用户imID

  useEffect(() => {
    if (visible) {
      getManageMembersInTheMeeting()
    } else {
      cleanState()
    }
  }, [])

  // 清空state
  const cleanState = () => {
    setCheckedUserId(null)
    setCheckedImUserId(null)
  }

  // 空间在线成员列表
  const getManageMembersInTheMeeting = async () => {
    let res = await dispatch({
      type: 'PlanetChatRoom/getManageMembersInTheMeeting',
      payload: {
        spaceId: match?.params?.RoomId,
        sceneType: 2, // 场景类型，1：会议详情在线用户，2：管理成员在线用户
      }
    })
    return res
  }

  // 选择用户
  const onClickUser = (wxUserId, imUserId) => {
    setCheckedUserId(wxUserId)
    setCheckedImUserId(imUserId)
  }

  // 点击指定并离开
  const onClickOk = () => {
    if (!checkedUserId) {
      Toast.show('请选择用户')
      return
    }
    designateHostModalOnOk(checkedUserId, checkedImUserId)
  }

  // loading
  const loadingGetManageMembersInTheMeeting = !!loading.effects['PlanetChatRoom/getManageMembersInTheMeeting']
  // 从在线成员中获取参会人
  const userListDataSource = membersListInTheMeeting && membersListInTheMeeting.length > 0 ? membersListInTheMeeting.filter(item => item.isSelf != 1 && item.meetingType == 2) : []

  return (
    <Modal
      visible={visible}
      className={styles.modal}
      destroyOnClose={true}
      getContainer={() => document.body}
      closeOnMaskClick={true}
      onClose={props.onCancel}
      content={
        <Spin spinning={loadingGetManageMembersInTheMeeting}>
          <div className={styles.header}>请在离开会议前指定主持人</div>

          <div className={styles.scroll_wrap}>
            {
              userListDataSource.length > 0 ? userListDataSource.map(item => {
                return (
                  <div key={item.wxUserId} className={styles.item} onClick={() => onClickUser(item.wxUserId, item.imUserId)}>
                    <div className={styles.item_left}>
                      <div className={styles.avatar_wrap}>
                        <Avatar userInfo={item} size={24}/>
                      </div>
                      <div className={styles.user_name}>{item.name}</div>
                    </div>

                    {/* 选中状态 */}
                    {
                      checkedUserId == item.wxUserId ? <img src={checkIcon4} width={16} height={16} alt=""/>
                      : <img src={checkIcon3} width={16} height={16} alt=""/>
                    }
                  </div>
                )
              }) : <NoDataRender style={{marginTop: 8}} text="暂无参会人~"/>
            }
          </div>

          {/* 按钮 */}
          <div className={styles.btn_wrap}>
            {
              userListDataSource.length > 0 ?
                <div className={styles.btn} onClick={onClickOk}>指定并离开</div>
                : <div className={styles.btn} onClick={onClickEndMeeting}>结束会议</div>
            }
          </div>
        </Spin>
      }
    />
  )
}
export default connect(({ PlanetChatRoom, loading }: any) => ({PlanetChatRoom, loading}))(Index)
