.container {
  height: 100%;
  background: #EEF3F9;
  display: flex;
  flex-wrap: nowrap;
  flex-direction: column;
  align-items: center;
  .content {
    flex: 1;
    width: 100%;
    overflow-y: auto;
    .content_inner {
      max-width: 1228px;
      min-height: 100%;
      padding: 16px 0;
      margin: 0 auto;
      display: flex;
      flex-wrap: nowrap;
      flex-direction: column;
    }
  }
}

.header {
  width: 100%;
  flex-shrink: 0;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  height: 48px;
  .header_icon {
    width: 40px;
    height: 40px;
    background: url("../../../../assets/GlobalImg/pc_goback.png") no-repeat center;
    background-size: 20px 20px;
    margin-right: 8px;
    cursor: pointer;
  }
  .header_title {
    font-size: 18px;
    color: #000;
    font-weight: 600;
    line-height: 34px;
  }
}
.box {
  width: 100%;
  flex: 1;
  background: #fff;
  border-radius: 8px;
  padding-bottom: 16px;
}

.warning_message_wrap {
  padding: 8px 24px 43px;
  .warning_message {
    background: #FFF7DA;
    display: flex;
    justify-content: center;
    column-gap: 4px;
    height: 29px;
    line-height: 30px;
    font-size: 12px;
    color: #8C772B;
    :global {
      .anticon {
        display: inline-flex;
        align-items: center;
      }
    }
  }
}

.block_wrap {
  display: flex;
  justify-content: center;
  column-gap: 24px;
  margin-bottom: 29px;
  p {
    margin-bottom: 0;
  }
  .block {
    width: 300px;
    padding: 24px 16px;
    background: #F8F9FC;
    border-radius: 8px;
    .block_title {
      text-align: center;
      font-weight: 500;
      font-size: 16px;
      color: #000;
      line-height: 22px;
      margin-bottom: 12px;
    }
    .block_info_wrap {
      height: 51px;
      text-align: center;
      font-size: 12px;
      color: #666;
      line-height: 17px;
      margin-bottom: 24px;
    }
    .block_img_wrap {
      display: flex;
      justify-content: center;
      position: relative;
      border-radius: 2px;
      overflow: hidden;
      .img_mask {
        position: absolute;
        z-index: 1;
        width: 100%;
        height: 100%;
        background: linear-gradient( 180deg, rgba(248,249,252,0) 0%, #F8F9FC 60%);
      }
      .img_btn {
        position: absolute;
        z-index: 2;
        top: 45px;
        border-radius: 20px;
        height: 26px;
        padding: 0 11px;
        font-size: 13px;
      }
    }
    .block_tips_wrap {
      position: relative;
      z-index: 3;
      height: 143px;
      margin-top: -19px;
      margin-bottom: 24px;
      font-size: 12px;
      color: #666;
      line-height: 17px;
      word-break: break-all;
      .tips_title {
        font-weight: 500;
        margin-bottom: 7px;
      }
    }
    .block_btn_wrap {
      display: flex;
      justify-content: center;
      .block_btn {
        height: 28px;
        padding: 0 23px;
      }
    }
  }
}

.page_btn_wrap {
  display: flex;
  justify-content: center;
  .page_btn {
    height: 36px;
    padding: 0 31px;
  }
}
