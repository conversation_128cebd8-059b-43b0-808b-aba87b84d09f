import React, { useEffect, useState } from 'react'
import classNames from 'classnames'
import { cloneDeep } from 'lodash'
import { stringify } from 'qs'
import { getHeaders } from '@/utils/utils'
import { Button, Modal, Tabs, Upload, message } from 'antd'
import { DesktopOutlined, CloseCircleFilled } from '@ant-design/icons'
import styles from './index.less'

interface PropsType {
  visible: boolean,                    // 弹窗是否显示
  articleTextImgList: object,          // 图片list
  uploadImageMaxNumber: number,        // 上传数量
  handleUploadImage: () => {},
  onCancel: () => {},
}

let loadingUploadImage = 0

const Index: React.FC<PropsType> = (props: PropsType) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')        // 用户信息

  const {
    visible,
    articleTextImgList,
    uploadImageMaxNumber,
  } = props

  const [selectedTextImgList, setSelectedTextImgList] = useState([])   // 已选择图片
  const [refreshState, setRefreshState] = useState(false)              // 刷新数据

  useEffect(() => {

    return () => {
      loadingUploadImage = 0
    }
  }, [])

  // 切换标签
  const onChangeTabs = () => {
    setSelectedTextImgList([])
  }

  // 选择图片
  const selectArticleImage = (value, isSelected, index) => {
    if (isSelected && selectedTextImgList.length >= uploadImageMaxNumber) {
      return
    }
    const selectedTextImgListClone = cloneDeep(selectedTextImgList)
    if (isSelected) {
      selectedTextImgListClone.push(value)
    } else {
      selectedTextImgListClone.splice(index, 1)
    }
    setSelectedTextImgList(selectedTextImgListClone)
  }

  // 上传校验规则，图片
  const beforeUpload = (file) => {
    const isSize = file.size / 1024 / 1024 < 15
    if (!isSize) {
      message.error('超过15M限制，不允许上传~')
      return false
    }

    const { name: fileName } = file || {}
    const isJpgOrPng = file.type === 'image/jpg' || file.type === 'image/jpeg' ||  file.type === 'image/png' || file.type === 'image/gif'
    const suffix = fileName.substring(fileName.lastIndexOf('.')+1).toLowerCase()
    const isSuffixByJpgOrPng = (suffix === 'jpg' || suffix === 'jpeg' || suffix === 'png' || suffix === 'gif')
    if (!isJpgOrPng || !isSuffixByJpgOrPng) {
      message.error('只能上传JPG、JPEG、PNG、GIF格式的图片~')
      return false
    }

    if (selectedTextImgList.length >= uploadImageMaxNumber) {
      message.error(`最多上传${uploadImageMaxNumber}张`)
      return false
    }

    loadingUploadImage += 1
    setRefreshState(!refreshState)
    return true
  }

  // 上传完成
  const uploadOnChange = (info) => {
    if (info && !info.file.status) {
      return
    }

    if (info.file.status === 'uploading') {

    }

    // 上传结束
    if (info && info.file.status === 'error') {
      message.error('上传失败')
      loadingUploadImage -= 1
      setRefreshState(!refreshState)
      return
    }

    if (info && info.file.status === 'done') {
      loadingUploadImage -= 1
      setRefreshState(!refreshState)
      if (selectedTextImgList.length >= uploadImageMaxNumber) {
        return
      }
      const res = info.file.response || {}
      const { code, content, msg } = res
      if (code == 200 && content) {
        selectArticleImage(content.fileUrlView, true)
      } else {
        message.error(msg || '上传失败')
      }
    }

  }

  // 删除图片
  const deleteImage = (index) => {
    const selectedTextImgListClone = cloneDeep(selectedTextImgList)
    selectedTextImgListClone.splice(index, 1)
    setSelectedTextImgList(selectedTextImgListClone)
  }

  // 确定
  const ok = () => {
    props.handleUploadImage(selectedTextImgList)
  }

  return (
    <Modal
      visible={visible}
      className={styles.modal}
      onCancel={props.onCancel}
      destroyOnClose
      footer={null}
      width={1119}
    >
      <div className={styles.content}>
        <Tabs defaultActiveKey="1" onChange={onChangeTabs}>
          <Tabs.TabPane tab="正文图片" key="1">
            <div className={styles.article_tab_title}>封面图尺寸建议大于 672*474，不可小于 452*354</div>
            <div className={styles.article_img_box}>
              {
                articleTextImgList.map((item, index) => {
                  const selectedIndex = selectedTextImgList.findIndex(itemChild => itemChild == item)
                  return (
                    <div key={item} className={classNames(styles.article_img_item, {
                      [styles.selected]: selectedIndex > -1,
                    })} style={{backgroundImage: `url(${item})`}} onClick={() => selectArticleImage(item, selectedIndex == -1, selectedIndex)}>
                      {selectedIndex > -1 && <span>{selectedIndex + 1}</span>}
                    </div>
                  )
                })
              }
            </div>
          </Tabs.TabPane>
          <Tabs.TabPane tab="上传图片" key="2">
            <div className={classNames(styles.upload_btn, {
              [styles.not_empty]: selectedTextImgList.length > 0,
            })}>
              <Upload
                headers={getHeaders()}
                accept="image/*"
                action={`/api/server/base/uploadFile?${stringify({ fileType: 23, userId: UserInfo?.friUserId})}`}
                beforeUpload={beforeUpload}
                onChange={uploadOnChange}
                showUploadList={false}
                multiple={true}
              >
                <Button type="primary" size="large" icon={<DesktopOutlined />} loading={loadingUploadImage > 0}>本地上传</Button>
              </Upload>
            </div>
            {
              selectedTextImgList.length == 0 &&
              <div className={styles.upload_text}>支持jpg、jpeg、png等多种格式，单张图片最大支持15MB</div>
            }

            {
              selectedTextImgList.length > 0 &&
              <>
                <div className={styles.upload_list_title}>已上传 {selectedTextImgList.length} 张图片</div>

                <div className={styles.upload_img_box}>
                  {
                    selectedTextImgList.map((item, index) => {
                      return (
                        <div key={item} className={styles.upload_img_item} style={{backgroundImage: `url(${item})`}}>
                          <CloseCircleFilled onClick={() => deleteImage(index)}/>
                          {/*<Progress percent={50} showInfo={false}/>*/}
                        </div>
                      )
                    })
                  }
                </div>
              </>
            }
          </Tabs.TabPane>
        </Tabs>
      </div>
      <div className={styles.footer}>
        <div className={styles.footer_left}>最多可选择 {uploadImageMaxNumber} 张，已选择 {selectedTextImgList.length} 张</div>
        <div>
          <Button size="large" onClick={props.onCancel}>取消</Button>
          <Button type="primary" size="large" onClick={ok}>确定</Button>
        </div>
      </div>
    </Modal>
  )
}

export default Index
