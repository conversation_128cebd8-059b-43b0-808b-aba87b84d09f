.wrap {
  width: 100%;
  background: #FFFFFF;
  padding: 0 0 16px 16px;
  .header{
    height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #E1E4E7;
    
    .header_title{
      font-size: 16px;
      font-weight: 500;
      color: #000000;
    }
    .header_status{
      font-size: 14px;
      font-weight: 400;
      color: #0095FF;
      flex: 1;
      text-align: right;
      
    }
    .header_icon{
      display: inline-block;
      width: 16px;
      height: 16px;
      margin-right: 16px;
      &>img{
        width: 100%;
        height: 100%;
        vertical-align: super;
      }
    }
  }
  .content{
    padding-top: 4px;
    &>p{
      margin-bottom: 0;
      margin-top: 8px;
      font-size: 13px;
      font-weight: 400;
      color: #999999;
    }
  }
}