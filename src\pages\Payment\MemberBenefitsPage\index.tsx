import { Select, InputNumber , message, Button, Modal, } from 'antd';
import React, { useEffect, useState, useRef } from 'react';
import { connect } from 'umi';
import styles from './index.less';
import './public.css';
import classNames from 'classnames';
import { history } from 'umi';
import { getOperatingEnv, commCORS, unCommCORS, getFridayURL } from "@/utils/utils";
import {getPlan} from "@/services/login/login";
import { getDentalAssistantBanner, getDentalAssistantInterestList, getMemberBenefitsList} from "@/services/payment";
import { debounce } from 'lodash';
import { mockData } from "./data";

const { Option } = Select;
import NavBar from '@/components/NavBar'
import CustomerModal from "./CustomerModal";
import customerIcon from '@/assets/Payment/customer.png';

// 生成8位随机数
const pageKeyByRendom = Math.random().toString(36).substr(2, 8);
const WX_QR = `https://static.jwsmed.com/public/DigitalHealth/Business/assets/DentalAssistant/callAssistantIcon.png?pageKeyByRendom=${pageKeyByRendom}`;
const bArrow = `https://static.jwsmed.com/public/DigitalHealth/Business/assets/MemberBenefitsPage/b_arrow.png?pageKeyByRendom=${pageKeyByRendom}`;
const tArrow = `https://static.jwsmed.com/public/DigitalHealth/Business/assets/MemberBenefitsPage/t_arrow.png?pageKeyByRendom=${pageKeyByRendom}`;
const paymentIcon1 = `https://static.jwsmed.com/public/DigitalHealth/Business/assets/MemberBenefitsPage/payment_icon1.png?pageKeyByRendom=${pageKeyByRendom}`;
const paymentIcon2 = `https://static.jwsmed.com/public/DigitalHealth/Business/assets/MemberBenefitsPage/payment_icon2.png?pageKeyByRendom=${pageKeyByRendom}`;


// 获取当前操作环境 1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用
const fromFriday = window.location.hash.indexOf('FridayHome')>-1;  // 判断是否从官网打开

// 更新官网页面高度方法=>仅在官网容器中有应用到
const updateHeight = () => {
  // 判断当前是不是在官网环境中
  if(fromFriday) {
    setTimeout(() => {
      const env = getOperatingEnv()
      let clientWidth = document.documentElement.clientWidth;
      let type = clientWidth > 750 ? 1 : 2;
      let bodyHeight;
      if(env == 4 && type == 1){
        bodyHeight = document.getElementById('MemberBenefitDigital').clientHeight+100;
      }else{
        bodyHeight= document.getElementById('MemberBenefitDigital').clientHeight;
      }
      // console.info(document.documentElement.scrollHeight,'高度是？？？？')
      commCORS('MemberBenefitDigital', `bodyHeight=${bodyHeight}`);  // 通知官网当前页面的高度
    }, 1000);
  }
}

// （二维码——仅展示于PC端）
const Float: React.FC = () => {
  // 跳转至支付模块web8
  const goToMemberBenefitsWeb = () => {
    document.querySelector('#MemberBenefitsWeb').scrollIntoView({ behavior: 'smooth' });
    updateHeight();
  }
  const env = getOperatingEnv()
  // 小程序webview内嵌公众号网页，无法长按识别二维码 去除二维码的展示
  if(env == 4&&(!fromFriday)) {
    return (
      <div className={styles.Float_box} style={{display:'none'}}>
        <div onClick={()=>{goToMemberBenefitsWeb()}}><img width={42} src={WX_QR} alt=""/></div>
      </div>
    );
  }else {
    return null;
  }
};
// 立即购买
const goBuy = (value) => {
  if(value == 1) {
    if(fromFriday){
      commCORS('MemberBenefitDigital',`/Payment/Step1?vipType=1&${Math.random().toFixed(6)}`)
    }else {
      history.push('/Payment/Step1?vipType=1');
    }
  }else if(value == 2) {
    if(fromFriday){
      commCORS('MemberBenefitDigital',`/Payment/Step1?vipType=2&${Math.random().toFixed(6)}`)
    }else {
      history.push('/Payment/Step1?vipType=2');
    }
  }
}

// 支付按钮（仅用于h5端）
const PaymentBtn: React.FC = ({plan}) => {
  // 个人版价格方案
  const plan1 = Array.isArray(plan && plan['1'])? plan['1'] : [];
  // 企业版价格方案
  const plan2 = Array.isArray(plan && plan['3'])? plan['2'] : [];

  return (
    <div className={styles.PaymentBtn_box}>
      <div className={styles.PaymentBtn_person}>
        <div className={styles.PaymentBtn_price}>
          {plan1 && plan1.map((item, index) => {
            return `¥${item.planPrice}${item.planUnit}`
          }).join(' | ')}
        </div>
        <div className={styles.PaymentBtn_content}>
          <span className={styles.PaymentBtn_icon}><img src={paymentIcon1} alt="" /></span>
          <span className={styles.PaymentBtn_btn} onClick={()=>{goBuy("1")}}>立即购买</span>
        </div>
      </div>
      <div className={styles.PaymentBtn_enterprise}>
        <div className={styles.PaymentBtn_price}>
          {plan2 && plan2.map((item, index) => {
            return `¥${item.planPrice}${item.planUnit}`
          }).join(' | ')}
        </div>
        <div className={styles.PaymentBtn_content}>
          <span className={styles.PaymentBtn_icon}><img src={paymentIcon2} alt="" /></span>
          <span className={styles.PaymentBtn_btn} onClick={()=>{goBuy("2")}}>立即购买</span>
        </div>
      </div>
    </div>
  );
};


// 省钱计算器单项组件
const OptionGroup =({ defaultData, dropdownOptions, getNewData }) =>{
  const [data, setData] = useState(defaultData);
  useEffect(() => {
    getNewData(data)
  }, [data]);
  const handleAddData = () => {
    const existingValues = data.map((item) => item.dropdownValue);
    const availableOptions = dropdownOptions.filter(
      (option) => !existingValues.includes(option)
    );
    if(availableOptions.length === 0){
      message.error("没有可继续添加的项～！")
      return false;
    }
    const newDropdownValue = availableOptions[0];
    const newInputValue = 1;
    const newId = data.length + 1;
    setData([...data, { id: newId, dropdownValue: newDropdownValue, inputValue: newInputValue }]);
  };
  const handleDeleteData = (id) => {
    if (data.length === 1) {
      return;
    }
    setData(data.filter((item) => item.dropdownValue !== id));
  };
  const handleDropdownChange = (value, id) => {
    const existingValues = data.map((item) => item.dropdownValue);
    if (existingValues.includes(value)) {
      return;
    }
    setData(
      data.map((item) =>
        item.dropdownValue === value ? { ...item, dropdownValue: value } : item
      )
    );
  };
  const handleInputChange = (value, id) => {
    setData(
      data.map((item) =>
        item.dropdownValue === id ? { ...item, inputValue: value } : item
      )
    );
  };
  const existingValues = data.map((item) => item.dropdownValue);
  return (
    <div>
      {data.map((item,index) => (
        <div key={index} style={{ marginBottom: '10px' }}>
          <Select
            value={item.dropdownValue}
            onChange={(value) => handleDropdownChange(value)}
            style={{ width: 120, marginRight: '10px' }}
          >
            {dropdownOptions.map((option) => (
              <Option key={option} value={option} disabled={existingValues.includes(option)}>
                {option}
              </Option>
            ))}
          </Select>
          <InputNumber
            value={item.inputValue}
            min={1}
            max={9999}
            onChange={(value) => handleInputChange(value, item.dropdownValue)}
          />
          <Button onClick={() => handleDeleteData(item.dropdownValue)} style={{ marginLeft: '10px' }} disabled={data.length === 1}>
            Delete
          </Button>
        </div>
      ))}
      <Button onClick={handleAddData}>新增+</Button>
    </div>
  );
}

// 牙医助手banner
const  DentalAssistantBannerPage: React.FC = ({type, fromFriday}) => {
  const [dentalAssistantBanner, setDentalAssistantBanner] = useState({});
  useEffect(() => {
    getDentalAssistantBannerdata();
  },[])

  // 获取会员权益banner数据
  const getDentalAssistantBannerdata=()=>{
    getDentalAssistantBanner().then((res)=>{
      setDentalAssistantBanner(res)
      updateHeight();
    })
  }
  return (
    <div className={styles.dentalAssistantBanner} style={type==1?{backgroundImage: `url(${dentalAssistantBanner.PCimg})`}:{backgroundImage: `url(${dentalAssistantBanner.H5img})`}}>
      <h3>{dentalAssistantBanner.pageTitle}</h3>
      <h1>{dentalAssistantBanner.title}</h1>
      <h2>{dentalAssistantBanner.subhead}</h2>
      <p>{type==1?dentalAssistantBanner.descPC:dentalAssistantBanner.descH5}</p>
    </div>
  )
}

// 牙医助手权益
const DentalAssistantInterestPage: React.FC = ({type, fromFriday}) => {
  const [dentalAssistantInterestList, setDentalAssistantInterestList] = useState([]);
  useEffect(() => {
    getDentalAssistantInterestListData();
  },[])

  // 去Friday官网各权益页面
  const gToDetail=(linkUrl)=>{
    const FridayUrl = getFridayURL()
    if(fromFriday){
      commCORS('MemberBenefitDigital',`Interest=${linkUrl}&${Math.random().toFixed(6)}`)
    }else {
      window.open(`${FridayUrl}${linkUrl}`,'_blank')
    }
  }

  const getDentalAssistantInterestListData=()=>{
    getDentalAssistantInterestList().then((res)=>{
      setDentalAssistantInterestList(res)
      updateHeight();
    })
  }
  return (
    <div className={styles.dentalAssistantInterest}>
      {
        dentalAssistantInterestList.map((item, index) => {
          return (
            <div key={index} className={styles.dentalAssistantInterestItem} style={index % 2 === 1 ? { flexDirection: 'row-reverse' } : {}}>
              {type !== 1 ? <h3>{item.title}</h3> : null}
              <div className={styles.InterestItemPart1}>
                <img src={type == 1 ? item.PCimg : item.H5img} alt="" />
              </div>
              <div className={styles.InterestItemPart2}>
                {type == 1 ? (
                  <div>
                    <h3>{item.title}</h3>
                    {item.subtitle ? <h2>{item.subtitle}</h2> : null}
                  </div>
                ) : null}
                <p
                  className={styles.InterestItemSubhead}
                  style={item.minText && item.minText != '' ? { marginBottom: 0 } : null}
                >
                  {type == 1 ? item.subhead : item.subhead.replace(/\n/g, '，')}
                </p>
                {item.minText && item.minText != '' && <span>{item.minText}</span>}
                <i>{item.desc}</i>
                {item.linkUrl && (
                  <p
                    onClick={() => {
                      gToDetail(item.linkUrl);
                    }}
                    className={styles.InterestLink}
                  >
                    {item.linkName}
                    <i></i>
                  </p>
                )}
                <p className={styles.InterestLink}>
                  {item.markTextArr &&
                    item.markTextArr.length > 0 &&
                    item.markTextArr.map((markItem, indexnum) => {
                      return (
                        <a href={markItem.linkUrl} key={indexnum} target="blank">
                          {markItem.name}
                        </a>
                      );
                    })}
                </p>
              </div>
            </div>
          );
        })
      }
    </div>
  )
}

// Web8 合作品牌
const Web8: React.FC = ({plan, type, fromFriday}) => {
  const [isShowOneBtn, setIsShowOneBtn] = useState(false);
  const [isShowTwoBtn, setIsShowTwoBtn] = useState(false);
  const [isActiveTab, setIsActiveTab] = useState(0);
  const [memberBenefitsList, setMemberBenefitsList ]= useState([]); // 会员权益列表
  const [customerModalVisible, setCustomerModalVisible] = useState(false); //咨询弹窗

  // 个人版价格方案
  const plan1 = Array.isArray(plan && plan['1'])? plan['1'] : [];
  // 企业版价格方案
  const plan2 = Array.isArray(plan && plan['2'])? plan['2'] : [];
  // 尊享版价格方案
  const plan3 = Array.isArray(plan && plan['3'])? plan['3'] : [];

  const didMountRef = useRef(false);
  const tabContainerRef = useRef(null);
  const tabPanelContainerRef = useRef(null);

  useEffect(() => {
    getMemberBenefitsListdata();
  },[])

  // 如果是官网平台环境
  useEffect(() => {
    if(!didMountRef.current){
      didMountRef.current = true;
      return;
    }
    if(fromFriday&&didMountRef.current){
      const bodyHeight = type==1?document.getElementById('MemberBenefitDigital').clientHeight:document.getElementById('MemberBenefitDigital').clientHeight-50;
      commCORS('MemberBenefitDigital',`bodyHeight=${bodyHeight}`);
    }
  }, [isShowOneBtn, isShowTwoBtn]);


  // 复制功能
  const copyAction = str => {
    const createInput = document.createElement('input');
    createInput.value = str;
    document.body.appendChild(createInput);
    createInput.select();
    document.execCommand('Copy'); // document执行复制操作
    createInput.remove();
    message.success('复制成功');
  };

  // 获取会员权益清单列表
  const getMemberBenefitsListdata = () => {
    getMemberBenefitsList().then((res)=>{
      setMemberBenefitsList(res || mockData )
      updateHeight();
      const tabPanels = tabPanelContainerRef.current?.children;
      const intersectionObserver = new IntersectionObserver((entries) => {
        const intersectionRatios: number[] = [];
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            intersectionRatios.push(entry.intersectionRatio);
          }
        });
        if (intersectionRatios.length === tabPanels.length) {
          // 从intersectionRatios中获取最大数的index
          const maxIndex = intersectionRatios.indexOf(Math.max(...intersectionRatios));
          setIsActiveTab(maxIndex);
        }
      }, {
        root: tabPanelContainerRef.current, // 监听的根元素
        threshold: [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1],
      });
      // 监听tabPanelContainer的子元素
      if (tabPanels) {
        for (let i = 0; i < tabPanels.length; i++) {
          intersectionObserver.observe(tabPanels[i]);
        }
      }
    });
  }

  // 关闭弹窗
  const closeCustomerModal = () => {
    setCustomerModalVisible(false)
  }
  return (
    <div className={styles.Web8} id={'MemberBenefitsWeb'}>
      {
        type != 1 ? <div className={styles.web8_tab} ref={tabContainerRef}>
          {
            memberBenefitsList && memberBenefitsList.map((item, ind) => {
              return <div key={ind} className={classNames({
                [styles.web8_tab_init]: true,
                [styles.web8_tab_active]: ind == isActiveTab
              })} onClick={() => {
                if (ind === isActiveTab) return; // 如果点击的tab已经是当前选中的tab，则不执行任何操作
                // setIsActiveTab(ind)
                // 点击tab，滚动到对应的tabPanel
                tabPanelContainerRef.current?.scrollTo({left: tabPanelContainerRef.current?.children[ind].getBoundingClientRect().x, behavior: "smooth",});
              }}>{item.title}</div>
            })
          }
        </div> : null
      }
      <div className={styles.web8_content_wrap}>
        <div ref={tabPanelContainerRef} className={classNames({
          [styles.web8_content]: true,
        })}>
          {
            memberBenefitsList && memberBenefitsList.map((item, ind) => {
              return <div key={ind} className={ind==0?styles.web8_left_content:styles.web8_right_content}>
                <div className={ind==0?styles.web8_left_title:styles.web8_right_title}>{item.title}{item.subhead!=''&&<span>{item.subhead}</span>}</div>
                <div className={styles.web8_list_box}>
                  {item.content&&item.content.map((every,index) => {
                    return (
                      <div key={index} className={styles.web8_list_item}>
                        <h4>{every.title}</h4>
                        <div className={styles.web8_item_content}>
                          {
                            every.content&&every.content.map((itemByEvery,num) => {
                              return (
                                <div key={num}>
                                  <div className={styles.memberitem}>
                                    <div className={classNames({
                                      [styles.type1]:itemByEvery.type == 1,
                                      [styles.type2]:itemByEvery.type == 2,
                                      [styles.type3]:itemByEvery.type == 3,
                                      [styles.type4]:itemByEvery.type == 4,
                                      [styles.type5]:itemByEvery.type == 5,
                                      [styles.disabledType]:itemByEvery?.disabled == 1,
                                    })}
                                    >
                                      <i></i>{itemByEvery.title}
                                    </div>
                                    <div>
                                      <span  className={classNames({[styles.fs16]: itemByEvery?.unitSmall == 1})}>{itemByEvery.priceORtime}</span>
                                      {itemByEvery?.unitDiff == 1 ? <span dangerouslySetInnerHTML={{ __html: itemByEvery?.unitDiffText }}></span>: <span>{itemByEvery.unit}</span>}

                                    </div>
                                  </div>
                                  {itemByEvery.subhead &&itemByEvery.subhead!='' && <i>{itemByEvery.subhead}</i>}
                                </div>
                              )
                            })
                          }
                        </div>
                      </div>
                    );
                  })}
                </div>
                <div className={styles.web8_price_content}>
                  {
                    ind==0?plan1 && plan1.map((itemByPlan, index) => {
                      const {planPrice, planUnit} = itemByPlan || {}
                      return (
                        <div key={index} className={styles.web8_price_item}>
                          <span className={styles.unit}>¥</span>
                          <span className={styles.money}>{planPrice}</span>
                          {planUnit}
                        </div>
                      )
                    }):plan3 && plan3.map((itemByPlan,index) => {
                      const { planPrice,planUnit } = itemByPlan || {}
                      return (
                        <div key={index} className={styles.web8_price_item}>
                          <span className={styles.unit}>¥</span>
                          <span className={styles.money}>{planPrice}</span>
                          {planUnit}
                        </div>
                      )
                    })
                  }
                </div>
                <div className={styles.btn}>
                  <span onClick={() => {
                    // if(ind==0){
                    //   goBuy(1)
                    // }else{
                    //   goBuy(2)
                    // }
                    setCustomerModalVisible(true);
                  }}>{item.btnText}</span>
                </div>
              </div>
            })
          }
        </div>
        {customerModalVisible &&
          <div className={styles.web8_modal}>
            <div className={styles.consult_customer_title}>
              <div className={styles.ccicon} onClick={closeCustomerModal}>
                <span />
                <span />
              </div>
              <div className={styles.cctitle}> 微信扫码 小茹解疑</div>
            </div>

            <div className={styles.consult_customer_con}>
              <img src={customerIcon} />
            </div>
            </div>
        }

      </div>

      {/* 咨询客服弹窗 */}
      {customerModalVisible && <CustomerModal/>}


      {/*<div className={styles.web8_apply_member}>*/}
      {/*  <div className={styles.web8_member_title}>如何向老板申请购买FRIDAY会员？</div>*/}
      {/*  <div className={styles.web8_member_content}>*/}
      {/*    <div className={styles.web8_member_content_text}>*/}
      {/*      ______, 您好：*/}
      {/*      <br />*/}
      {/*      我是_部门的_一，这次找您是因为我们诊所很多医生喜欢使用的学习平台需要收费，希望您可以审批一下。这个产品叫FRIDAY,他们持续做了很提升医生医疗水平的学习内容，如：每周跟瑞尔大专家的病例研讨会、优秀病例在线学习借鉴，还可以在线一对一请教专家问题。最重要的是，成为FRIDAY企业会员后，我们以后采购高值耗材和设备都可以跟其他几百家诊所一起，享受联盟集采的优惠价格，现在大多数民营诊所都是在使用*/}
      {/*      FRIDAY*/}
      {/*    </div>*/}
      {/*    <div className={styles.web8_member_copy_btn} onClick={()=>copyAction('______, 您好：我是_部门的_一，这次找您是因为我们诊所很多医生喜欢使用的学习平台需要收费，希望您可以审批一下。这个产品叫FRIDAY,他们持续做了很提升医生医疗水平的学习内容，如：每周跟瑞尔大专家的病例研讨会、优秀病例在线学习借鉴，还可以在线一对一请教专家问题。最重要的是，成为FRIDAY企业会员后，我们以后采购高值耗材和设备都可以跟其他几百家诊所一起，享受联盟集采的优惠价格，现在大多数民营诊所都是在使用FRIDAY')}>点击复制</div>*/}
      {/*  </div>*/}
      {/*</div>*/}
      {/*<div className={styles.web8_FAQ_content}>*/}
      {/*  <div className={styles.FAQ_title}>产品相关答疑</div>*/}
      {/*  <div className={styles.FAQ_wrapper}>*/}
      {/*    <div*/}
      {/*      className={styles.FAQ_box}*/}
      {/*      onClick={() => {*/}
      {/*        setIsShowOneBtn(!isShowOneBtn);*/}
      {/*      }}*/}
      {/*    >*/}
      {/*      <div className={styles.FAQ_list}>*/}
      {/*        <span>是否可以开具发票</span>*/}
      {/*        <span className={styles.arrow}>*/}
      {/*          <img src={isShowOneBtn ? tArrow : bArrow} alt="" />*/}
      {/*        </span>*/}
      {/*      </div>*/}
      {/*      {isShowOneBtn ? (*/}
      {/*        <div className={styles.FAQ_text}>*/}
      {/*          开通企业版会员我们将提供普通电子发票和增值税专用发票，联系客服线下提供*/}
      {/*        </div>*/}
      {/*      ) : null}*/}
      {/*    </div>*/}
      {/*    <div*/}
      {/*      className={styles.FAQ_box}*/}
      {/*      onClick={() => {*/}
      {/*        setIsShowTwoBtn(!isShowTwoBtn);*/}
      {/*      }}*/}
      {/*    >*/}
      {/*      <div className={styles.FAQ_list}>*/}
      {/*        <span>对公转账的流程</span>*/}
      {/*        <span className={styles.arrow}>*/}
      {/*          <img src={isShowTwoBtn ? tArrow : bArrow} alt="" />*/}
      {/*        </span>*/}
      {/*      </div>*/}
      {/*      {isShowTwoBtn ? (*/}
      {/*        <div className={styles.FAQ_text}>*/}
      {/*          您好，对公支付购买FRIDAY会员有三步：1、进入选择需要购买的版本和方案；2、确定支付订单信息后，选择对公转账。3.转账成功后，请将付款回执丸上传在对公转账的信息中。在收到您提交的订单后，我们将在1-3个工作日客服将与您联系，确认支付结果，并为您开通服务*/}
      {/*        </div>*/}
      {/*      ) : null}*/}
      {/*    </div>*/}
      {/*  </div>*/}
      {/*</div>*/}
    </div>
  );
};

const MemberBenefitsPage: React.FC = () => {
  const [plan,setPlan ] = useState(null)          // 套餐内容
  const [type,setType ] = useState(null)          // 当前是移动端还是PC端 PC端是1 移动端是2

  const updateType = debounce(() => {
    const env = getOperatingEnv()
    let clientWidth = document.documentElement.clientWidth;
    let type = clientWidth > 750 ? 1 : 2;
    if(env == 4 && type == 1) {
      setType(1)
    }else {
      setType(2)
    }

  },500);
  window.addEventListener('resize', updateType);

  useEffect(() => {
    window.scrollTo({ top: 0 });
    updateType()
    initializeData()
    updateHeight()
    // 监听官网传过来的值变化
    window.addEventListener('hashchange', handleHashChange)
    return () => {
      if(fromFriday){
        // 离开页面断连通信
        unCommCORS('MemberBenefitDigital','commWithFriday');
        window.removeEventListener('hashchange', handleHashChange);
      }
      window.removeEventListener('resize', updateType);
    }
  }, []);

  // 初始化数据
  const initializeData = async () => {
    sessionStorage.removeItem('orderId')
    // 首先判定环境 如果当前环境是小程序端则从url中获取token和openid
    const env = getOperatingEnv()
    if(env == 1){
      // 当前环境是小程序端
      // 获取地址栏上的hash值
      const hash = window.location.hash;
      console.log(' window.location.hash :: ',window.location.hash);
      if(hash) {
        const encodedMessage = hash.slice(1);
        const decodedMessage = JSON.parse(decodeURIComponent(encodedMessage));
        const {
          access_token: access_tokenByMini,
          vxOpenIdCipherText: vxOpenIdCipherTextByMini,
        } = decodedMessage || {}
        // 保存从小程序来的token值和vxOpenIdCipherText
        localStorage.setItem('access_token', access_tokenByMini);
        localStorage.setItem('vxOpenIdCipherText', vxOpenIdCipherTextByMini);
      }
    }
    getPlanByPage()
  }

  // 获取订单方案价格
  const getPlanByPage = async() => {
    const dataByGetPlan = await getPlan()
    const {
      code,
      content,
      msg,
    } = dataByGetPlan || {}
    if(code == 200 && content){
      setPlan(content)
    }else {
      message.warning(msg?msg:'获取订单方案失败');
    }
  }

  useEffect(() => {
    updateHeight()
  },[type])

  // 官网传过来的值
  const handleHashChange = () => {
    // 点击官网牙医助手浮窗定位会员支付
    if(window.location.hash.indexOf('ScrollWeb8')>-1){
      document.querySelector('#MemberBenefitsWeb').scrollIntoView({ behavior: 'smooth' });
    }
  }
  return (
    <div id={'MemberBenefitDigital'} className={classNames({
      [styles.containerByPC]:type == 1,
      [styles.containerByMobile]:type == 2,
    })}>
      <Float />
      {/* 导航条 */}
      {
        type == 2 && <NavBar title="FRIDAY牙医助手简介"/>
      }
      {/*牙医助手banner*/}
      {
        type!=null&&<DentalAssistantBannerPage type={type} fromFriday={fromFriday} />
      }
      {/*牙医助手权益列表*/}
      {
        type!=null&&<DentalAssistantInterestPage type={type} fromFriday={fromFriday} />
      }
      {/* 8.套餐内容 */}
      {
        type!=null&&<Web8 plan={plan} type={type} fromFriday={fromFriday}/>
      }
      {/* 立即支付==>官网环境下不展示 用官网自己的 */}
      {/* {type!=null&&!fromFriday&&<PaymentBtn plan={plan}/>} */}

    </div>
  );
};

export default connect(({ login, loading }: any) => ({
  login,
  loading,
}))(MemberBenefitsPage);
