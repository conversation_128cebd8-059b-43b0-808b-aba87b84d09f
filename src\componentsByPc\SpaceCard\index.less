.space_container {
  background: #fff;
  cursor: pointer;
  .cover_img {
    position: relative;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    overflow: hidden;
    margin-bottom: 8px;
    background-image: url("https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/PlanetChatRoom_HorizontalLiveRoom_Live_bg.png");

    .sign {
      position: absolute;
      top: 0;
      left: 0;
      font-size: 12px;
      font-weight: 400;
      color: #06A777;
      line-height: 17px;
      background: #EEFFF9;
      border-radius: 0px 0px 4px 0px;
      padding: 2px 4px;
    }

    // 用户身份
    .user_role {
      position: absolute;
      bottom: 0;
      right: 0;
      display: block;
      padding: 0 4px;
      height: 21px;
      line-height: 22px;
      border-radius: 2px;
      z-index: 10;
      &.role_host {
        background: #FCE9E8;
        color: #FF5F57;
      }
      &.role_guest {
        background: #EDF9FF;
        color: #0095FF;
      }
    }

    .status_box {
      position: absolute;
      background: linear-gradient(135deg, #4183EA 0%, #003AAF 100%);
      border-radius: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-wrap: nowrap;
      font-size: 12px;
      color: #FFFFFF;
      padding: 0 8px;
      height: 21px;
      white-space: nowrap;
      z-index: 666;
      .status_icon {
        display: block;
        width: 12px;
        height: 12px;
        margin-right: 2px;
        background-repeat: no-repeat;
        background-position: center;
        &.icon1 {
          background: url("../../assets/GlobalImg/space1.png") no-repeat center;
          background-size: 100% 100%;
        }
        &.icon2 {
          background: url("../../assets/GlobalImg/space2.png") no-repeat center;
          background-size: 100% 100%;
        }
        &.icon3 {
          background: url("../../assets/GlobalImg/space3.png") no-repeat center;
          background-size: 100% 100%;
        }
      }
    }
    .gdp {
      position: absolute;
      color: #fff;
      left: 0;
      bottom: 0;
      background: rgba(0,0,0,0.4);
      border-radius: 0px 8px 0px 0px;
      padding: 2px 6px;

    }
    .offshelf_style {
      position: absolute;
      z-index: 666;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: #F5F5F5;
      border-radius: 17px;
      font-size: 13px;
      font-weight: 400;
      color: #000000;
      padding: 4px 12px;
      white-space: nowrap;
    }

    .operate_btn {
      display: flex;
      align-items: center;
      padding: 4px 12px;
      background: rgba(0,0,0,0.5);
      border-radius: 20px 20px 20px 20px;
      font-size: 13px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 15px;
      cursor: pointer;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 999;

      .lines {
        width: 1px;
        height: 16px;
        background: #D9D9D9;
        margin: 0 8px;
      }
    }
    .danmu_box {
      position: relative;
      height: 100%;
    }
    .title_in_cover_image {
      position: absolute;
      z-index: 600;
      width: 60%;
      top: 20px;
      left: 0;
      padding-left: 12px;
      font-size: 14px;
      line-height: 20px;
      color: #fff;
      font-weight: 600;
      word-break: break-all;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2; /* 指定显示行数 */
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .space_info_box {
    .title_text {
      word-break: break-all;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2; /* 指定显示行数 */
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .space_intro {
      font-size: 12px;
      font-weight: 400;
      color: #888888;
      line-height: 14px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-top: 4px;
    }
    .footer {
      display: flex;
      flex-wrap: nowrap;
      justify-content: space-between;
      align-items: center;
      margin-top: 11px;
      .footer_left {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        .left_avatar {
          border-radius: 50%;
          position: relative;
          background-repeat: no-repeat;
          background-position: center;
          background-size: cover;
          font-weight: 500;
          color: #fff;
          text-align: center;
          white-space: nowrap;
          & > i {
            position: absolute;
            width: 11px;
            height: 11px;
            top: -3px;
            right: -3px;
            background: url("../../assets/GlobalImg/crown.png") no-repeat center;
            background-size: 100%;
          }
        }
        .left_name {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .footer_right {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        .right_avatar {
          border: 1px solid #fff;
          border-radius: 50%;
          background-repeat: no-repeat;
          background-position: center;
          background-size: cover;
          font-weight: 500;
          color: #fff;
          text-align: center;
          white-space: nowrap;
          &:last-child {
            margin-right: 0;
          }
        }
        .avatar_more {
          border: 1px solid #fff;
          border-radius: 50%;
          background: #E6F4FF;
          color: #0095FF;
          text-align: center;
        }
      }
    }
  }

  // 个人中心页样式
  &.container_form_2 {
    .cover_img {
      height: 107px;
      border-radius: 4px;
      .status_box {
        top: 8px;
        right: 8px;
      }
      .gdp {
        font-size: 11px;
        line-height: 15px;
      }
    }
    .space_info_box {
      .title {
        font-size: 14px;
        line-height: 20px;
        min-height: 40px;
        color: #091715;
        font-weight: 600;
      }
      .footer {
        .footer_left {
          .left_avatar {
            width: 23px;
            min-width: 23px;
            height: 23px;
            margin-right: 8px;
            font-size: 10px;
            line-height: 24px;
          }
          .left_name {
            font-size: 13px;
            color: #4A627E;
          }
        }
        .footer_right {
          .right_avatar {
            width: 23px;
            min-width: 23px;
            height: 23px;
            margin-right: -7px;
            font-size: 10px;
            line-height: 24px;
          }
          .avatar_more {
            width: 23px;
            height: 23px;
            line-height: 22px;
          }
        }
      }
    }
  }

  // 以下样式暂时没用
  // 医生主页样式
  &.container_form_3 {
    .cover_img {
      height: 144px;
      border-radius: 8px;
      .status_box {
        top: 10px;
        right: 10px;
      }
      .gdp {
        font-size: 14px;
        line-height: 20px;
      }
    }
    .space_info_box {
      .title {
        font-size: 16px;
        line-height: 22px;
        min-height: 44px;
        color: #000;
      }
      .footer {
        .footer_left {
          .left_avatar {
            width: 32px;
            min-width: 32px;
            height: 32px;
            margin-right: 4px;
            font-size: 12px;
            line-height: 32px;
          }
          .left_name {
            font-size: 14px;
            color: #666;
          }
        }
        .footer_right {
          .right_avatar {
            width: 32px;
            min-width: 32px;
            height: 32px;
            margin-right: -12px;
            font-size: 12px;
            line-height: 32px;
          }
          .avatar_more {
            width: 32px;
            height: 32px;
            line-height: 31px;
          }
        }
      }
    }
  }

  // 首页样式
  &.container_number_1 {
    .cover_img {
      height: 264px;
      border-radius: 12px 12px 0 0;
      margin-bottom: 12px;
      .status_box {
        top: 10px;
        right: 10px;
      }
      .gdp {
        font-size: 11px;
        line-height: 15px;
      }
    }
    .space_info_box {
      padding: 0 16px 12px;
      //box-shadow: inset 0px 1px 1px 0px #FFFFFF, 0px 2px 6px 0px #EFF1F5;
      border-radius: 0px 0px 8px 8px;
      .title {
        font-size: 16px;
        line-height: 22px;
        font-weight: 500;
        color: #071E3F;
      }
      .footer {
        .footer_left {
          .left_avatar {
            width: 23px;
            min-width: 23px;
            height: 23px;
            margin-right: 8px;
            font-size: 10px;
            line-height: 24px;
          }
          .left_name {
            font-size: 13px;
            color: #4A627E;
          }
        }
        .footer_right {
          .right_avatar {
            width: 23px;
            min-width: 23px;
            height: 23px;
            margin-right: -7px;
            font-size: 10px;
            line-height: 24px;
          }
          .avatar_more {
            width: 23px;
            height: 23px;
            line-height: 22px;
          }
        }
      }
    }
  }

  &.container_number_2, &.container_number_6 {
    .cover_img {
      height: 219px;
      border-radius: 4px;
      margin-bottom: 16px;
      .status_box {
        top: 16px;
        right: 16px;
      }
      .gdp {
        font-size: 12px;
        line-height: 17px;
      }
    }
    .space_info_box {
      .title {
        font-size: 18px;
        line-height: 26px;
        min-height: 58px;
        font-weight: 500;
        color: #000;
        margin-bottom: 15px;
      }
      .footer {
        .footer_left {
          .left_avatar {
            width: 32px;
            min-width: 32px;
            height: 32px;
            margin-right: 9px;
            font-size: 12px;
            line-height: 32px;
          }
          .left_name {
            font-size: 16px;
            color: #666;
          }
        }
        .footer_right {
          .right_avatar {
            width: 32px;
            min-width: 32px;
            height: 32px;
            margin-right: -12px;
            font-size: 12px;
            line-height: 32px;
          }
          .avatar_more {
            width: 32px;
            height: 32px;
            line-height: 31px;
          }
        }
      }
    }
  }

  // 分类导航中样式
  &.container_isInClassifyGuide_number_2 {
    &.container_number_1 {
      .cover_img {
        height: 121px;
        border-radius: 12px 12px 0 0;
        margin-bottom: 0;
        .status_box {
          top: 12px;
          right: 8px;
        }
        .gdp {
          font-size: 11px;
          line-height: 15px;
        }
      }
      .space_info_box {
        padding: 8px 12px 16px;
        //box-shadow: inset 0px 1px 1px 0px #FFFFFF, 0px 2px 6px 0px #EFF1F5;
        border-radius: 0px 0px 12px 12px;
        .title {
          font-size: 16px;
          line-height: 22px;
          font-weight: 500;
          color: #000000;
        }
        .footer {
          .footer_left {
            .left_avatar {
              width: 23px;
              min-width: 23px;
              height: 23px;
              margin-right: 8px;
              font-size: 10px;
              line-height: 24px;
            }
            .left_name {
              font-size: 13px;
              color: #666666;
            }
          }
          .footer_right {
            .right_avatar {
              width: 23px;
              min-width: 23px;
              height: 23px;
              margin-right: -7px;
              font-size: 10px;
              line-height: 24px;
            }
            .avatar_more {
              width: 23px;
              height: 23px;
              line-height: 22px;
            }
          }
        }
      }
    }

    &.container_number_2, &.container_number_6 {
      .cover_img {
        height: 102px;
        border-radius: 4px;
        margin-bottom: 8px;
        .status_box {
          top: 10px;
          right: 10px;
        }
        .gdp {
          font-size: 11px;
          line-height: 15px;
        }
      }
      .space_info_box {
        .title {
          font-size: 14px;
          line-height: 20px;
          min-height: 40px;
          font-weight: 600;
          color: #071E3F;
          margin-bottom: 11px;
        }
        .footer {
          .footer_left {
            .left_avatar {
              width: 23px;
              min-width: 23px;
              height: 23px;
              margin-right: 8px;
              font-size: 10px;
              line-height: 24px;
            }
            .left_name {
              font-size: 13px;
              color: #4A627E;
            }
          }
          .footer_right {
            .right_avatar {
              width: 23px;
              min-width: 23px;
              height: 23px;
              margin-right: -7px;
              font-size: 10px;
              line-height: 24px;
            }
            .avatar_more {
              width: 23px;
              height: 23px;
              line-height: 22px;
            }
          }
        }
      }
    }
  }
}
