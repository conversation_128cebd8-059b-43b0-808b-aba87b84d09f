.container {
  flex-shrink: 0;
  width: 100%;
  background: #fff;
  position: relative;
  z-index: 998;
}
.false_input_box {
  padding: 8px 12px;
  border-top: 1px solid #ddd;
  .wrap {
    background: #F5F5F5;
    border-radius: 36px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 34px;
    padding: 0 16px;
    .left {
      display: flex;
      align-items: center;
      & > i {
        width: 16px;
        height: 16px;
        background: url("../../../../assets/GlobalImg/edit_icon.png") no-repeat center;
        background-size: 100% 100%;
      }
      & > span {
        font-size: 14px;
        color: #999;
        margin-left: 4px;
      }
    }
    .right {
      width: 20px;
      height: 20px;
      background: url("../../../../assets/GlobalImg/smile.png") no-repeat center;
      background-size: 100% 100%;
    }
  }
}

.true_input_box{
  padding: 8px 12px;
  display: flex;
  align-items: flex-end;
  flex-wrap: nowrap;
  box-shadow: 0 -3px 5px 0 rgba(0,0,0,0.1);
  .textarea_box {
    flex: 1;
    background: #F5F5F5;
    border-radius: 25px;
    display: flex;
    align-items: center;
    :global {
      .adm-text-area {
        position: relative;
      }
      .adm-text-area-element {
        font-size: 14px;
        color: #000;
        padding: 7px 16px;
      }
    }
    .textarea_Icon {
      width: 22px;
      height: 20px;
      background: url("../../../../assets/GlobalImg/smile.png") no-repeat;
      background-size: 100% 100%;
      margin-right: 8px;
      cursor: pointer;
      user-select: none;
    }
  }
  .btn_box{
    flex-shrink: 0;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    margin-left: 6px;
    & > i {
      width: 32px;
      height: 36px;
      background: url("../../../../assets/GlobalImg/smile.png") no-repeat center;
      background-size: 20px 20px;
    }
    & > span {
      font-size: 14px;
      color: #0095FF;
      font-weight: 500;
      margin-left: 8px;
      height: 36px;
      line-height: 37px;
    }
  }
}
