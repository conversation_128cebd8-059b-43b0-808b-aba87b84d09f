import React, {useEffect, useState} from 'react';
import classNames from 'classnames';
import styles from './index.less';
import TEduBoard from 'TEduBoard';
import TIM from 'tim-js-sdk';
import {WHITEBOARD_MSG} from '@/app/config';

import {Button, message, Modal, Spin, Upload} from 'antd';
import {connect} from 'umi';
import {LeftCircleOutlined, RightCircleOutlined, UnorderedListOutlined, UploadOutlined,} from '@ant-design/icons';
import StreamAudioList from '@/pages/PlanetChatRoom/Meet/components/Viewport/component/StreamAudioList';

type propsType = {
  onRefByHorizontalLiveRoom: any;
  sendMessageByIm: any;
  localStreamConfig: any;
  remoteStreamConfigList: any;
  RTC: any;
  shareRTC: any;
  isJoined: boolean;
  isPublished: boolean;
  handleJoin: any;
  handleLeave: any;
  onChange: any;
  spaceId: any;
  openCloseHandUp: any;
  liveRecord: any;
  getGuestList: any;
  getSpaceInfo: any;
  onClickLianMai: any;
  changeUrlParams: any;
  operateHandUp: any;
  shareOnClick: any;
  onClickBack: any;
  isHorizontalLive: any;
};

const elementOperationAuthority = {
  line: {
    disableRotate: false,
    disableMove: false,
    disableProportionScale: false,
    disableArbitraryScale: false,
  },
};

const WhiteboardLiveRoom: React.FC<propsType> = (props) => {
  const {
    localStreamConfig,
    RTC,
    remoteStreamConfigList,
    PlanetChatRoom,
    dispatch,
    tim,
    shareHostRemoteStreamConfig,
  } = props || {};

  const {
    SpaceInfo,
    currentUserType,
    isMobile,
    currentWatchMode,
    isOpenTEduBoard,
    TiwTaskID,
    userInfo,
  } = PlanetChatRoom || {};

  const {userSig, id, roomId, imGroupId, trtcAppId} = SpaceInfo || {};

  const [teduBoard, setTeduBoard] = useState(null);
  const [ModalVisibleBySharedCourseware, setModalVisibleBySharedCourseware] = useState(null);
  const [fileList, setFileList] = useState(null);
  const [currentFileByModal, setCurrentFileByModal] = useState(null);
  const [pageNum, setPageNum] = useState(0);
  const [initloading, setInitloading] = useState(null);

  useEffect(async () => {
    if (currentWatchMode && teduBoard) {
      setTimeout(() => {
        teduBoard && teduBoard.refresh();
        teduBoard && teduBoard.resize();
        // teduBoard && teduBoard.setScaleToolRatio(50);
        teduBoard && teduBoard.setScaleAnchor(0.5, 0.5);
      }, 1000);
    }
  }, [currentWatchMode, teduBoard]);

  useEffect(async () => {
    if (userSig && isOpenTEduBoard && currentUserType) {
      let dataByGetTiwImUser = await dispatch({
        type: 'PlanetChatRoom/getTiwImUser',
        payload: {spaceId: id},
      });
      const {code, content} = dataByGetTiwImUser || {};
      if (code == 200 && content) {
        await initTEduBoard({...content}); // 初始化白板
      }
    }
  }, [userSig, isOpenTEduBoard, currentUserType]);

  useEffect(() => {
    return () => {
      if (!!TiwTaskID) {
        stopWhiteboardPush(TiwTaskID);
        dispatch({type: 'PlanetChatRoom/setState', payload: {TiwTaskID: null}});
        destroyBoard();
      }
    };
  }, [isOpenTEduBoard, TiwTaskID]);

  const mountTeduBoardByTranscodeFileBySpace = async (TranscodeFileBySpace) => {
    if (TranscodeFileBySpace && TranscodeFileBySpace.length > 0) {
      if (!teduBoard) {
        return;
      }

      teduBoard && teduBoard.setDataSyncEnable(true); // 设置白板是否开启数据同步
      let fileInfoList = teduBoard.getFileInfoList();
      fileInfoList = fileInfoList ? fileInfoList : [];
      fileInfoList = fileInfoList.filter((item) => {
        // 去除默认的白板
        return item.fid != '#DEFAULT';
      });
      fileInfoList = [...fileInfoList];

      let ListByTranscodeFileBySpace = TranscodeFileBySpace.filter((item) => {
        return !fileInfoList.find((findByFileInfoList) => {
          return findByFileInfoList.downloadURL == item.transcodeUrl;
        });
      });

      let deleteByfileInfoList = fileInfoList.filter((item) => {
        return !TranscodeFileBySpace.find((itemByTranscodeFileBySpace) => {
          return itemByTranscodeFileBySpace.transcodeUrl == item.downloadURL;
        });
      });

      // 先移除被删除的历史课件
      if (Array.isArray(deleteByfileInfoList)) {
        deleteByfileInfoList.map((item) => {
          teduBoard && teduBoard.deleteFile(item.fid);
        });
      }

      if (Array.isArray(ListByTranscodeFileBySpace) && ListByTranscodeFileBySpace.length > 0) {
        ListByTranscodeFileBySpace.map((item, index) => {
          teduBoard &&
          teduBoard.addTranscodeFile({
            pages: item.transcodePage,
            url: item.transcodeUrl,
            title: item.fileTitle,
          });
        });
        await setTimeout(async () => {
          let currentFileFid = await teduBoard.getCurrentFile();
          let currentFileObj =
            (await Array.isArray(fileInfoList)) &&
            fileInfoList.length > 0 &&
            fileInfoList.find((item) => `${item.fid}` == `${currentFileFid}`);
          await setCurrentFileByModal(!!currentFileObj ? currentFileObj : null);
          await setModalVisibleBySharedCourseware(true); // 打开选择课件弹窗
          await setInitloading(false);
        }, 3000);
      } else {
        if (fileInfoList.length > 0) {
          let currentFileFid = teduBoard.getCurrentFile();
          let currentFileObj =
            Array.isArray(fileInfoList) &&
            fileInfoList.length > 0 &&
            fileInfoList.find((item) => `${item.fid}` == `${currentFileFid}`);
          setCurrentFileByModal(!!currentFileObj ? currentFileObj : null);
          setModalVisibleBySharedCourseware(true); // 打开选择课件弹窗
          await setInitloading(false);
        }
      }
    }
  };

  // 结束白板推流
  const stopWhiteboardPush = async (tiwPushTaskId) => {
    if (shareHostRemoteStreamConfig && shareHostRemoteStreamConfig.streamType == 'main') {
      let res = await dispatch({
        type: 'PlanetChatRoom/stopWhiteboardPush',
        payload: {
          spaceId: id,
          tiwPushTaskId: tiwPushTaskId,
        },
      });
      return res;
    } else {
      return null;
    }
  };

  // 通过空间ID，进入直播会议后获取转码文件
  const getTranscodeFileBySpace = async () => {
    await setInitloading(true);
    let res = await dispatch({
      type: 'PlanetChatRoom/getTranscodeFileBySpace',
      payload: {spaceId: id},
    });
    const {code, content} = res || {};
    if (code == 200 && content) {
      await mountTeduBoardByTranscodeFileBySpace(content);
      return res;
    } else {
      await setInitloading(false);
      return res;
    }
  };

  // 获取白板互动im推流机器人
  const getTiwImUser = async () => {
    // 获取白板推流机器人身份
    let dataByGetTiwImUser = await dispatch({
      type: 'PlanetChatRoom/getTiwImUser',
      payload: {spaceId: id},
    });
    const {code, content} = dataByGetTiwImUser || {};
    if (code == 200 && content) {
      const {imUserId: TiwImUserId, userSig: TiwUserSig} = content || {};
      // 开启白板推流
      let dataByStartWhiteboardPush = await dispatch({
        type: 'PlanetChatRoom/startWhiteboardPush',
        payload: {
          spaceId: id, // --空间ID
          tiwPushUserId: TiwImUserId, // --推流机器人imuserId
          tiwPushUserSig: TiwUserSig, // --推流机器人签名
        },
      });
      // console.log('dataByStartWhiteboardPush :: ',dataByStartWhiteboardPush);
    }
  };

  useEffect(() => {
    setPageNum(currentFileByModal && currentFileByModal.currentPageIndex + 1);
  }, [currentFileByModal]);

  /*initTEduBoard 初始化白板功能 */
  const initTEduBoard = ({imUserId, userSig}) => {
    if (!!TEduBoard) {
      // 初始化白板功能
      let teduBoardByinit = new TEduBoard({
        id: 'sketch',
        sdkAppId: trtcAppId,
        userId: imUserId,
        userSig: userSig,
        classId: roomId,
        config: {
          ratio: '16:9', // 画布宽高比
          boardContentFitMode: TEduBoard.TEduBoardContentFitMode.TEDU_BOARD_FILE_FIT_MODE_NONE,
          toolType: TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_MOUSE,
        },

        userConfig: {
          nickname: `${imUserId}`,
        },

        styleConfig: {
          brushThin: 50,
          selectBoxColor: '#ffffff',
          selectAnchorColor: '#ffffff',
          globalBackgroundColor: '#ffffff',
        },

        authConfig: {
          showCursorOnTouch: false,
          mathGraphEnable: false,
          formulaEnable: false,
          elementOperationAuthority: elementOperationAuthority,
          mouseToolBehavior: {
            whiteBoard: true,
            h5PPT: true,
            imgPPT: true,
          },
          drawEnable: true, // 是否允许涂鸦
        },
      });
      window.teduBoard = teduBoardByinit;
      setTeduBoard(teduBoardByinit);
    }
  };

  useEffect(() => {
    if (teduBoard) {
      initEvent();
    }
  }, [teduBoard]);

  const initEvent = () => {
    // 监听错误事件
    teduBoard.on(TEduBoard.EVENT.TEB_ERROR, (errorCode, errorMessage) => {
      console.log(
        '======================:  ',
        'TEB_ERROR',
        ' errorCode:',
        errorCode,
        ' errorMessage:',
        errorMessage,
      );
      let message = '';
      switch (errorCode) {
        case TEduBoard.TEduBoardErrorCode.TEDU_BOARD_ERROR_INIT:
          message = '初始化失败，请重试';
          break;
        case TEduBoard.TEduBoardErrorCode.TEDU_BOARD_ERROR_AUTH:
          message = '服务鉴权失败，请先购买服务';
          break;
        case TEduBoard.TEduBoardErrorCode.TEDU_BOARD_ERROR_LOAD:
          message = '白板加载失败，请重试';
          break;
        case TEduBoard.TEduBoardErrorCode.TEDU_BOARD_ERROR_HISTORYDATA:
          message = '同步历史数据失败，请重试';
          break;
        case TEduBoard.TEduBoardErrorCode.TEDU_BOARD_ERROR_RUNTIME:
          message = '白板运行错误，请检查sdkAppId，userId, userSig是否正确';
          break;
        case TEduBoard.TEduBoardErrorCode.TEDU_BOARD_ERROR_AUTH_TIMEOUT:
          message = '服务鉴权超时，请重试';
          break;
        case TEduBoard.TEduBoardErrorCode.TEDU_BOARD_MAX_BOARD_LIMITED:
          message = '单课堂内白板页数已经到达上限';
          break;
        case TEduBoard.TEduBoardErrorCode.TEDU_BOARD_SIGNATURE_EXPIRED:
          message = 'userSig过期了，请重新生成新的userSig，再重新初始化白板';
          break;
      }
    });

    teduBoard.on(TEduBoard.EVENT.TEB_WARNING, (warnCode, warnMessage) => {
      console.warn(
        '======================:  ',
        'TEB_WARNING',
        ' warnCode:',
        warnCode,
        ' warnMessage:',
        warnMessage,
      );
      let messageInfo = '';
      switch (warnCode) {
        case TEduBoard.TEduBoardWarningCode.TEDU_BOARD_WARNING_SYNC_DATA_PARSE_FAILED:
          messageInfo = '实时数据格式错误，请检查白板信令是否有进行二次包装';
          break;
        case TEduBoard.TEduBoardWarningCode.TEDU_BOARD_WARNING_H5PPT_ALREADY_EXISTS:
          messageInfo = '重复添加文件';
          break;
        case TEduBoard.TEduBoardWarningCode.TEDU_BOARD_WANNING_ILLEGAL_OPERATION:
          // messageInfo = "非法操作，请在历史数据完成回调后再调用sdk相关接口";
          (messageInfo = null), console.warn('非法操作，请在历史数据完成回调后再调用sdk相关接口');
          break;
        case TEduBoard.TEduBoardWarningCode.TEDU_BOARD_WARNING_H5FILE_ALREADY_EXISTS:
          messageInfo = '重复添加文件';
          break;
        case TEduBoard.TEduBoardWarningCode.TEDU_BOARD_WARNING_VIDEO_ALREADY_EXISTS:
          messageInfo = '重复添加文件';
          break;
        case TEduBoard.TEduBoardWarningCode.TEDU_BOARD_WARNING_IMAGESFILE_ALREADY_EXISTS:
          messageInfo = '重复添加文件';
          break;
        case TEduBoard.TEduBoardWarningCode.TEDU_BOARD_WARNING_GRAFFITI_LOST:
          messageInfo = '涂鸦丢失';
          break;
        case TEduBoard.TEduBoardWarningCode.TEDU_BOARD_WARNING_CUSTOM_GRAPH_URL_NON_EXISTS:
          messageInfo = '自定义图形url为空';
          break;
        case TEduBoard.TEduBoardWarningCode.TEDU_BOARD_WARNING_IMAGESFILE_TOO_LARGE:
          messageInfo = '图片组超大';
          break;
        case TEduBoard.TEduBoardWarningCode.TEDU_BOARD_WARNING_IMAGE_COURSEWARE_ALREADY_EXISTS:
          messageInfo = '重复添加文件';
          break;
        case TEduBoard.TEduBoardWarningCode.TEDU_BOARD_WARNING_IMAGE_MEDIA_BITRATE_TOO_LARGE:
          messageInfo =
            '多媒体资源码率大于2048kb/s，网络不好情况下容易造成卡顿，建议对视频码率进行压缩';
          break;
        case TEduBoard.TEduBoardWarningCode.TEDU_BOARD_WARNING_IMAGE_WATERMARK_ALREADY_EXISTS:
          messageInfo = '已经存在图片水印，不能重复添加';
          break;
        case TEduBoard.TEduBoardWarningCode.TEDU_BOARD_WARNING_FORMULA_LIB_NOT_LOADED:
          messageInfo = '数学公式库没有重新加载';
          break;
        case TEduBoard.TEduBoardWarningCode.TEDU_BOARD_WARNING_ILLEGAL_FORMULA_EXPRESSION:
          messageInfo = '非法的数学公式';
          break;
        case TEduBoard.TEduBoardWarningCode.TEDU_BOARD_WARNING_TEXT_WATERMARK_ALREADY_EXISTS:
          messageInfo = '已经存在文本水印，不能重复添加';
          break;

        case TEduBoard.TEduBoardWarningCode.TEDU_BOARD_WARNING_EXPORTIMPORT_FILTERRULE_ILLEGAL:
          messageInfo = '已经存在文本水印，不能重复添加';
          break;

        case TEduBoard.TEduBoardWarningCode.TEDU_BOARD_WARNING_ELEMENTTYPE_NOT_EXISTS:
          messageInfo = '元素类型不存在';
          break;

        case TEduBoard.TEduBoardWarningCode.TEDU_BOARD_WARNING_ELEMENTID_NOT_EXISTS:
          messageInfo = '元素ID不存在';
          break;
      }
      // this.showErrorTip(message);
      if (messageInfo) {
        message.error(messageInfo);
      }
      console.warn(messageInfo);
    });

    teduBoard.on(TEduBoard.EVENT.TEB_HISTROYDATA_SYNCCOMPLETED, async () => {
      console.log('======================:  ', 'TEB_HISTROYDATA_SYNCCOMPLETED', currentUserType);
      if (currentUserType == 1 || currentUserType == 2) {
        await setTimeout(() => {
          return null;
        }, 1000);
        await getTranscodeFileBySpace(); // 添加并挂载课件
        await setTimeout(() => {
          return null;
        }, 1000);
        await getTiwImUser(); // 获取白板互动im推流机器人 并开始推流
        await sendWhiteboardMessageStart(1);
      }
    });

    teduBoard.on(TEduBoard.EVENT.TEB_SYNCDATA, (data) => {
      // this.$EventBus.$emit("tiw-send-sync-data", data);
      // teduBoard.addSyncData(data);
      sendBoardRealtimeDataMessage(data);
    });

    teduBoard.on(TEduBoard.EVENT.TEB_SWITCHFILE, (fid) => {
      // this.setCurrentFile(this.teduBoard.getFileInfo(fid));
      let FileInfoList = teduBoard.getFileInfoList();
      let currentFileFid = this.teduBoard.getFileInfo(fid);
      let currentFileObj =
        Array.isArray(FileInfoList) &&
        FileInfoList.length > 0 &&
        FileInfoList.find((item) => `${item.fid}` == `${currentFileFid}`);
      this.setCurrentFile(this.teduBoard.getFileInfo(fid));
      setCurrentFileByModal(!!currentFileObj ? currentFileObj : null);
    });

    teduBoard.on(TEduBoard.EVENT.TEB_ADDFILE, (info) => {
      console.log('TEB_ADDFILE :: ', info);
    });

    teduBoard.on(TEduBoard.EVENT.TEB_GOTOBOARD, (boardId, fid) => {
      // this.setCurrentFile(this.teduBoard.getFileInfo(fid));
      const {currentPageIndex} = teduBoard.getFileInfo(fid);
      setPageNum(currentPageIndex ? currentPageIndex + 1 : 1);
    });

    teduBoard.on(TEduBoard.EVENT.TEB_ZOOM_DRAG_STATUS, ({boardId, scale}) => {
      // setCurrentFile(this.teduBoard.getFileInfo());
    });

    teduBoard.on(TEduBoard.EVENT.TEB_VIDEO_STATUS_CHANGED, (data) => {
      if (data.status === TEduBoard.TEduBoardVideoStatus.TEDU_BOARD_VIDEO_STATUS_ERROR) {
        message.error('视频播放/加载失败');
        console.error('视频播放/加载失败');
      }
    });

    teduBoard.on(TEduBoard.EVENT.TEB_GOTOSTEP, (currentStep, totalStep) => {
      console.log('======================:  ', 'TEB_GOTOSTEP', currentStep, totalStep);
    });

    teduBoard.on(TEduBoard.EVENT.TEB_TRANSCODEPROGRESS, (res) => {
      if (res.code) {
        message.error(`转码失败code:${res.code} message:${res.message}`);
      } else {
        const { status } = res;
        if (
          status === TEduBoard.TEduBoardTranscodeFileStatus.TEDU_BOARD_TRANSCODEFILE_STATUS_ERROR
        ) {
          // this.showErrorTip("转码失败");
          message.error(`转码失败`);
        } else if (
          status ===
          TEduBoard.TEduBoardTranscodeFileStatus.TEDU_BOARD_TRANSCODEFILE_STATUS_UPLOADING
        ) {
          // this.showTip(`上传中，当前进度:${parseInt(res.progress)}%`);
          message.error(`上传中，当前进度:${parseInt(res.progress)}%`);
        } else if (
          status === TEduBoard.TEduBoardTranscodeFileStatus.TEDU_BOARD_TRANSCODEFILE_STATUS_CREATED
        ) {
          // this.showTip("创建转码任务");
          message.error(`创建转码任务`);
        } else if (
          status === TEduBoard.TEduBoardTranscodeFileStatus.TEDU_BOARD_TRANSCODEFILE_STATUS_QUEUED
        ) {
          // this.showTip("正在排队等待转码");
          message.error(`正在排队等待转码`);
        } else if (
          status ===
          TEduBoard.TEduBoardTranscodeFileStatus.TEDU_BOARD_TRANSCODEFILE_STATUS_PROCESSING
        ) {
          // this.showTip(`转码中，当前进度:${res.progress}%`);
          message.error(`转码中，当前进度:${res.progress}%`);
        } else if (
          status === TEduBoard.TEduBoardTranscodeFileStatus.TEDU_BOARD_TRANSCODEFILE_STATUS_FINISHED
        ) {
          // this.showTip("转码完成");
          message.error(`转码完成`);
          const config = {
            url: res.resultUrl,
            title: res.title,
            pages: res.pages,
            resolution: res.resolution,
          };
          teduBoard.addTranscodeFile(config);
        }
      }
    });

    // 操作权限
    teduBoard.on(TEduBoard.EVENT.TEB_BOARD_PERMISSION_DENIED, () => {
      message.error(`无操作权限`);
    });

    // 调用importInLocalMode接口导入数据完成后的回调
    teduBoard.on(TEduBoard.EVENT.TEB_BOARD_IMPORTINLOCALMODE_COMPLETED, (code) => {
      if (code === 0) {
        message.success('导入数据成功');
      } else {
        message.error(`导入数据失败`);
      }
    });

    // 监听截图事件，image为截图内容的base64数据
    teduBoard.on(TEduBoard.EVENT.TEB_SNAPSHOT, ({image}) => {
      const downloadEl = document.createElement('a');
      const event = new MouseEvent('click');
      downloadEl.download = Date.now() + '.png';
      downloadEl.href = image;
      downloadEl.dispatchEvent(event);
    });

    // 收到PPT加载完成的事件
    teduBoard.on(TEduBoard.EVENT.TEB_H5PPT_STATUS_CHANGED, (status, data) => {
      if (status === TEduBoard.TEduBoardH5PPTStatus.TEDU_BOARD_H5_PPT_STATUS_LOADED) {
        // 获取指定课件的所有备注
        // fid	      string 文件ID
        // pageIndex	number 1:表示获取当前ppt所有备注；其他表示获取当前ppt索引页(从0开始)的备注
        // teduBoard.getPPTRemarks(fid, -1);
        let FileInfoList = teduBoard.getFileInfoList();
        let currentFileFid = teduBoard.getCurrentFile();
        let currentFileObj =
          Array.isArray(FileInfoList) &&
          FileInfoList.length > 0 &&
          FileInfoList.find((item) => `${item.fid}` == `${currentFileFid}`);

        setCurrentFileByModal(!!currentFileObj ? currentFileObj : null);
        // setModalVisibleBySharedCourseware(true);  // 打开选择课件弹窗
      }
    });

    // 白板刷新回调
    teduBoard.on(TEduBoard.EVENT.TEB_REFRESH, () => {
    });

    // 增加转码文件回调 TEB_ADDTRANSCODEFILE
    teduBoard.on(TEduBoard.EVENT.TEB_ADDTRANSCODEFILE, (info) => {
      /*let currentFileFid = teduBoard.getCurrentFile();
      let currentFileObj = Array.isArray(fileInfoList) && fileInfoList.length > 0 && fileInfoList.find(item => `${item.fid}` == `${currentFileFid}`);
      setCurrentFileByModal(!!currentFileObj ? currentFileObj : null);
      console.log('info123123 :: ',info,currentFileFid,currentFileObj);
      setModalVisibleBySharedCourseware(true);  // 打开选择课件弹窗*/
    });
  };

  // 销毁白板
  const destroyBoard = () => {
    if (!!teduBoard) {
      teduBoard.destroy();
      setModalVisibleBySharedCourseware(null);
      setFileList(null);
      setCurrentFileByModal(null);
      setPageNum(0);
      setTeduBoard(null);
      dispatch({
        type: 'PlanetChatRoom/setState',
        payload: {
          isOpenTEduBoard: false, // 是否开启白板模式
          TranscodeFileBySpace: null, // 通过空间ID，进入直播会议后获取转码文件
          TiwImUserId: null, // 获取白板ImUserId
          TiwUserSig: null, // 获取白板UserSig
          TiwTaskID: null, // 获取白板TaskID
        },
      });
    }
  };

  // 用于同步白板云端消息
  const sendBoardRealtimeDataMessage = (data) => {
    if (!tim) {
      return;
    }
    const groupId = String(imGroupId);

    const messageByTim = tim.createCustomMessage({
      to: groupId,
      conversationType: TIM.TYPES.CONV_GROUP,
      priority: TIM.TYPES.MSG_PRIORITY_HIGH, // 因为im消息有限频，白板消息的优先级调整为最高
      payload: {
        data: JSON.stringify(data),
        description: '',
        extension: 'TXWhiteBoardExt',
      },
    });
    let promise = tim.sendMessage(messageByTim);
    promise
      .then((imResponse) => {
        // console.log('哈哈哈哈123123123 :: ',imResponse);
      })
      .catch(function (imError) {
        // console.log('imError1233 :: ',imError);
      });

    /*const [error] = await Util.awaitWrap(this.tim.sendMessage(message));
     if (error) {
       this.sendMessageStatus[message.ID] = {
         resendCount: 0,
       }; // 重试次数
       this.resendBoardRealtimeDataMessage(message);
     }*/
  };

  // 开启白板并推流
  const sendWhiteboardMessageStart = (params) => {
    if (!tim) {
      return;
    }
    const groupId = String(imGroupId);
    const messageByTim = tim.createCustomMessage({
      to: groupId,
      conversationType: TIM.TYPES.CONV_GROUP,
      priority: TIM.TYPES.MSG_PRIORITY_NORMAL, // 因为im消息有限频，白板消息的优先级调整为最高
      payload: {
        data: WHITEBOARD_MSG,
        description: String(params),
        extension: JSON.stringify({
          ...userInfo,
          currentUserType: currentUserType,
        }),
      },
    });
    let promise = tim.sendMessage(messageByTim);
    promise
      .then(function (imResponse) {
        let {
          data: {message},
        } = imResponse || {};
        console.log('messageimResponseimResponse :: ', imResponse);
      })
      .catch(function (imError) {
        console.log('imErrorimErrorimError :: ', imError);
      });
  };

  useEffect(() => {
    if (ModalVisibleBySharedCourseware && teduBoard) {
      let FileInfoList = teduBoard.getFileInfoList();
      let currentFileFid = teduBoard.getCurrentFile();
      let currentFileObj =
        Array.isArray(FileInfoList) &&
        FileInfoList.length > 0 &&
        FileInfoList.find((item) => `${item.fid}` == `${currentFileFid}`);
      if (Array.isArray(teduBoard.getFileInfoList())) {
        let filterByFileInfoList = teduBoard.getFileInfoList().filter((item) => {
          // 去除默认的白板
          return item.fid != '#DEFAULT';
        });
        setFileList(filterByFileInfoList);
      }
      setCurrentFileByModal(!!currentFileObj ? currentFileObj : null);
    }
  }, [ModalVisibleBySharedCourseware]);

  return (
    <div className={styles.sketch_wrap}>
      {!!initloading && (
        <div className={styles.initloadingWarp}>
          <Spin size={36} style={{fontSize: '36px'}} spinning={!!initloading}></Spin>
        </div>
      )}
      <div id={'sketch'} style={{height: '100%'}}></div>
      {/* -控制面板- */}
      <div className={styles.Bottombar}>
        <div
          onClick={async () => {
            if (teduBoard) {
              await teduBoard.prevBoard();
              await setPageNum(pageNum <= 1 ? 1 : pageNum - 1);
            }
          }}
          className={styles.Bottom_btn}
        >
          <LeftCircleOutlined/>
        </div>
        {/*<div
              onClick={()=>{
                // teduBoard.addTranscodeFile({
                //   pages: 3,
                //   resolution: "960x1230",
                //   title: "JavaScript设计模式_带页码-1-20.pdf",
                //   url: "https://s.friday.tech/dev/star/transcode/0n7eh7jdicmhglsqffnc_tiw/picture/1.jpg"
                // })
                setModalVisibleBySharedCourseware(true);
              }}
              className={styles.Bottom_btn}>
              <CheckSquareOutlined />
            </div>*/}
        <div className={styles.currentPageNum}>
          <span> {pageNum ? pageNum : 1} </span>
          <span> / </span>
          <span>
            {' '}
            {currentFileByModal && currentFileByModal.pageCount
              ? currentFileByModal.pageCount
              : 0}{' '}
          </span>
        </div>

        <div
          onClick={async () => {
            if (teduBoard) {
              await teduBoard.nextBoard();
              if (currentFileByModal) {
                await setPageNum(
                  pageNum >= currentFileByModal.pageCount
                    ? currentFileByModal.pageCount
                    : pageNum + 1,
                );
              }
            }
          }}
          className={styles.Bottom_btn}
        >
          <RightCircleOutlined/>
        </div>

        <div className={styles.line}></div>

        <div
          className={styles.UnorderedListOutlinedWarp}
          onClick={() => {
            setModalVisibleBySharedCourseware(true);
          }}
        >
          <div className={styles.fileList}>
            <UnorderedListOutlined/>
          </div>
          <div>课件列表</div>
        </div>
      </div>

      {/* -转码弹窗- */}
      <Modal open={false} title={'上传课件转码'}>
        <div>
          <div>1. ppt默认转码为动态转码，即保留ppt中的动画效果。</div>
          <div>2. word，pdf只能转换为静态图片，ppt也可以设置转换为静态图片。</div>
        </div>
        <div>
          <Upload
            name={'file'}
            action={'https://run.mocky.io/v3/435e224c-44fb-4773-9faf-380c5e6a2188'}
            headers={{authorization: 'authorization-text'}}
            onChange={(info) => {
              if (info.file.status !== 'uploading') {
                console.log(info.file, info.fileList);
              }
              if (info.file.status === 'done') {
                message.success(`${info.file.name} file uploaded successfully`);
              } else if (info.file.status === 'error') {
                message.error(`${info.file.name} file upload failed.`);
              }
            }}
          >
            <Button icon={<UploadOutlined/>}>选择需要上传的课件</Button>
          </Upload>
        </div>
      </Modal>

      {/* 请选择您要分享的课件 */}
      <div className={styles.SharedCoursewareWarp}>
        <Modal
          open={ModalVisibleBySharedCourseware}
          title={'请选择您要分享的课件'}
          className={styles.SharedCourseware_Modal}
          width="530px"
          style={{
            top: isMobile ? '10%' : '20%',
          }}
          onOk={() => {
            teduBoard && teduBoard.switchFile(currentFileByModal && currentFileByModal.fid);
            setModalVisibleBySharedCourseware(false);
          }}
          onCancel={() => {
            setModalVisibleBySharedCourseware(false);
          }}
        >
          <div className={styles.SharedCourseware}>
            {Array.isArray(fileList) &&
              fileList.map((item) => {
                let fix = item.title.split('.').pop();
                // 截取文档的扩展名
                return (
                  <div
                    className={classNames({
                      [styles.coursewareItem]: true,
                      [styles.coursewareItemActive]:
                      `${item.fid}` == `${currentFileByModal && currentFileByModal.fid}`,
                    })}
                    onClick={() => {
                      setCurrentFileByModal(item);
                    }}
                  >
                    <div className={styles.img_item}>
                      <i
                        className={classNames({
                          [styles.ppt]: fix == 'ppt' || fix == 'pptx',
                          [styles.pdf]: fix == 'pdf',
                          [styles.word]: fix == 'doc' || fix == 'docx',
                        })}
                      ></i>
                    </div>
                    <div className={styles.img_title}>{item.title}</div>
                  </div>
                );
              })}
          </div>
        </Modal>
      </div>
      <div className={styles.StreamAudioListWarp}>
        <StreamAudioList
          key={'WhiteboardLiveRoom'}
          RTC={RTC} // RTC实例
          localStreamConfig={localStreamConfig} // 本地流配置
          remoteStreamConfigList={remoteStreamConfigList} // 远端流配置列表
        />
      </div>
    </div>
  );
};

export default connect(({PlanetChatRoom, loading}: any) => ({PlanetChatRoom, loading}))(
  WhiteboardLiveRoom,
);
