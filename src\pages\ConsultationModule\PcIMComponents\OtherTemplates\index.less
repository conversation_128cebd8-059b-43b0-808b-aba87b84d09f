.wrap {
  margin-top: 20px;
  padding: 0 54px 0 16px;
  display: inline-flex;

  .avatar {
    width: 31px;
    height: 31px;
    margin-right: 8px;

    .avatar_pic {
      display: inline-block;
      width: 31px;
      height: 31px;
      background: #eee;
      width: 100%;
      height: auto;
      border-radius: 50%;
      aspect-ratio: 1;
      border: 1px solid #FFFFFF;
      overflow: hidden;
    }

    .no_avatar_pic {
      width: 31px;
      height: 31px;
      border-radius: 50%;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 500;
      color: #fff;
      border: 1px solid #FFFFFF;
    }
  }

  // 文本样式
  .characters_content {
    flex: 1;
    background: #FFFFFF;
    border-radius: 4px 16px 16px 16px;
    padding: 12px;

    .desc {
      font-size: 13px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #000000;
      line-height: 15px;
      margin-bottom: 4px;
      max-width: 280px;
      word-break: break-all;
      white-space: pre-wrap;
      cursor: pointer;
    }

    .time {
      font-size: 11px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
      margin-bottom: 0;
    }

  }

  .voice_content {
    flex: 1;
    position: relative;
    .message_audio{
      position: absolute;
      width: 20px;
      z-index: -99;
    }
    .voice {
      background: #FFFFFF;
      border-radius: 4px 16px 16px 16px;
      padding: 9px 12px;
      display: inline-block;
    }

    .desc {
      margin-bottom: 4px;

      .second {
        font-size: 14px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #000000;
        vertical-align: middle;
      }

      .second_icon {
        width: 20px;
        height: 20px;
      }
    }

    .time {
      font-size: 11px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
      margin-bottom: 0;
    }
  }

  // 图片
  .picture_content {
    flex: 1;

    .picture {
      display: inline-block;
      border-radius: 6px;
      width: 120px;
      min-width: 120px;
      height: 120px;
      overflow: hidden;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      cursor: pointer;
    }
  }

  // 视频
  .video_content {
    flex: 1;

    .video {
      display: inline-block;
      border-radius: 6px;
      width: 120px;
      min-width: 120px;
      height: 120px;
      overflow: hidden;
      position: relative;

      &>img {
        width: 100%;
        height: 100%;
      }

      .play_icon {
        cursor: pointer;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        display: inline-block;
        width: 40px;
        height: 40px;

        &>img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}