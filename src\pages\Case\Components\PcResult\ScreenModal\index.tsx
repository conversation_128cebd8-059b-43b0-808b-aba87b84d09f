/**
 * @Description: PC-搜索列表筛选组件
 * @author: 赵斐
 */
import React, { useState, useEffect, useRef } from 'react';
import { connect ,history} from 'umi';
import dayjs from 'dayjs'
import { getArrailUrl } from '@/utils/utils'
import { Modal, Checkbox ,DatePicker ,Row ,Col ,Button } from 'antd';
const { RangePicker } = DatePicker;
import styles from './index.less';

const Index: React.FC = (props: any) => {
  // 是否嵌套在5i5ya的iframe中
  const isInIframe = (self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya')

  const { dispatch, cases ,isModalOpen ,dataSource ,handleScreenCancel ,refreshInterface } = props;
  const [state, setState] = useState(cases)
  const {
    depSubject,      // 所属学科
    difficultLevel,  // 难度等级
    achievement      // 病历成就
  } = dataSource || {}
  const {
    searchValue,       // 搜索值
    checkDepSubject,      // 选中学科
    checkAbilityLevel,  // 选中难度等级
    checkAchievement,     // 选中病例成就
    startDate,
    endDate
  } = state || {}

  /**
   * 处理选中数据
   * @param arr    选中数据
   * @param data   筛选数据集合
   * @returns
   */
  const removeLastZero = (arr: any[],data: any[]) => {
    if(arr[arr.length - 1] == 0){
      arr = [0]
    }else if(arr.length == data.length-1){
      arr = [0]
    }else{
      const index = arr.indexOf(0);
      if (index !== -1 && index !== arr.length - 1) {
        arr.splice(index, 1);
      }
    }
    return arr;
  };
  // 筛选学科
  const onChangeDepSubjectFun = (val:any[])=>{
    console.log(val)
    const newVal = removeLastZero(val,depSubject);
    console.log(newVal,"处理之后")
    setState({...state,checkDepSubject:newVal})

  }

  // 筛选难度等级
  const onChangeDifficultLevelFun = (val:any)=>{
    console.log(val)
    const newVal = removeLastZero(val,difficultLevel);
    setState({...state,checkAbilityLevel:newVal})
  }
  // 筛选病例成就等级
  const onChangeAchievementFun = (val:any)=>{
    console.log(val)
    const newVal = removeLastZero(val,achievement);
    setState({...state,checkAchievement:newVal})
  }
  // 获取日期
  const onChangeDateFun = (date:any, dateString:any) => {
    console.log(date, dateString);
    setState({
      ...state,
      startDate:dateString[0],
      endDate:dateString[1],
    })
  }

  // 点击确定关闭弹窗
  const handleOk = ()=>{
    dispatch({
      type: 'cases/save',
      payload: {
        checkDepSubject,
        checkAbilityLevel,
        checkAchievement,
        startDate,
        endDate,
        screenHighlight:true,
      }
    })
    refreshInterface(1,searchValue,state)
    handleScreenCancel()

    // 如果在5i5ya中，保存数据到父窗口model
    if (isInIframe) {
      // 发送登录成功的消息
      const postData = {
        dataType: 'saveData',       // 保存数据事件
        pageType: 'CaseResult',
        saveDataObj: {
          checkDepSubject,
          checkAbilityLevel,
          checkAchievement,
          startDate,
          endDate,
          screenHighlight:true,
        }
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
    }
  }
  /**
   * 点击取消关闭弹窗
   * @param type 1  清空筛选保存数据
   */
  const handleCancel = (type?:number)=>{
    let obj = {
      checkDepSubject: [],    // 选中学科
      checkAbilityLevel: [],  // 选中能力等级
      checkAchievement: [],   // 选中病例成就
      startDate: null,        // 开始时间
      endDate: null,          // 结束时间

      screenHighlight:false,  // 筛选文案高亮
    }
    if(type == 1){

      dispatch({
        type: 'cases/clean',
        payload: {
          ...obj,
        }
      })
      refreshInterface(1,searchValue,obj)

      // 如果在5i5ya中，保存数据到父窗口model
      if (isInIframe) {
        // 保存数据
        const postData = {
          dataType: 'saveData',       // 保存数据事件
          pageType: 'CaseResult',
          saveDataObj: {
            ...obj,
          }
        }
        console.log('子级发送数据：', postData, getArrailUrl())
        window.parent.postMessage(postData, getArrailUrl())
      }

    }else{
      // refreshInterface(1,searchValue,state)
    }
    handleScreenCancel()
  }

  return (
    <div className={styles.header}>
      <Modal
        title="筛选"
        open={isModalOpen}
        footer={null}
        onCancel={() => { handleCancel() }}
      >
        <div className={styles.content}>
        <div className={styles.title}>学科</div>
           <div className={styles.screen_content}>
            <Checkbox.Group style={{ width: '100%' }} onChange={(val)=>{onChangeDepSubjectFun(val)}} value={checkDepSubject}>
              {
                Array.isArray(depSubject) && depSubject.length?<>
                  {
                    depSubject.map((item,idx)=>{
                      return <div key={idx} className={styles.screen_child}>
                        <Checkbox value={item.code}>{item.name}</Checkbox>
                      </div>
                    })
                  }
                </>:null
              }
            </Checkbox.Group>
           </div>
           <div className={styles.title}>难度等级</div>
           <div className={styles.screen_content}>
            <Checkbox.Group style={{ width: '100%' }} onChange={(val)=>{onChangeDifficultLevelFun(val)}} value={checkAbilityLevel}>
              {
                Array.isArray(difficultLevel) && difficultLevel.length?<>
                  {
                    difficultLevel.map((item,idx)=>{
                      return <div key={idx} className={styles.screen_child}>
                        <Checkbox value={item.code}>{item.name}</Checkbox>
                      </div>
                    })
                  }
                </>:null
              }
            </Checkbox.Group>
           </div>
           <div className={styles.title}>日期</div>
           <div className={styles.screen_content_time}>
            <RangePicker
              picker="year"
              onChange={onChangeDateFun}
              format={"YYYY"}
              value={startDate && endDate && [dayjs(startDate, "YYYY"),dayjs(endDate, "YYYY")]}
            />
           </div>
           <div className={styles.title}>病例成就</div>
           <div className={styles.screen_content}>
            <Checkbox.Group style={{ width: '100%' }} onChange={(val)=>{onChangeAchievementFun(val)}} value={checkAchievement}>
            <Row>
              {
                Array.isArray(achievement) && achievement.length?<>
                  {
                    achievement.map((item,idx)=>{
                      return <Col span={12} key={idx} style={{marginBottom:10}}>
                        <Checkbox value={item.code}>{item.name}</Checkbox>
                      </Col>
                    })
                  }
                </>:null
              }
              </Row>
            </Checkbox.Group>
           </div>
        </div>
        <div className={styles.footer}>
            <Button onClick={() => { handleCancel(1) }} >取消</Button>
            <Button onClick={() => { handleOk() }} type="primary" className={styles.btn_primary}>确认</Button>
          </div>
      </Modal>
    </div>
  )
}
export default connect(({ cases, loading }: any) => ({ cases, loading }))(Index)
