import React from 'react'
import { connect } from 'umi'
import { throttle } from 'lodash'
import { Input, message, Spin } from 'antd'
import { SearchOutlined } from '@ant-design/icons'
import styles from './Topic.less'
import NoDataRender from '@/components/NoDataRender'

class Topic extends React.Component {
  static defaultProps = {
    itemOnClick: () => {},
  }

  constructor(props) {
    super(props)
    this.state = {
      topicName: '',           // 话题名称
      relationList: [],        // 话题数据
    }
    this.itemOnClick = throttle(this.itemOnClick, 500)
  }

  componentDidMount(): void {
    this.getRelationList()
  }

  // 获取话题列表
  getRelationList = () => {
    const { dispatch } = this.props
    const { topicName } = this.state
    dispatch({
      type: 'graphicsText/getRelationList',
      payload: {
        topicName,                     // 话题名称
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200) {
        this.setState({
          relationList: content || [],
        })
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch(err => {})
  }

  // 搜索框输入
  onChangeTopicName = (e) => {
    this.setState({
      topicName: e.target.value ? e.target.value.trim() : e.target.value
    }, () => {
      this.getRelationList()
    })
  }

  // 点击话题
  itemOnClick = (value) => {
    this.props.itemOnClick({
      ...value,
      topicName: `#${value.topicName}#`
    })
  }

  // 阻止默认事件
  onMouseDown = (e) => {
    e.preventDefault()
  }

  render() {
    const { loading } = this.props
    const { relationList } = this.state
    const loadingGetRelationList = !!loading.effects['graphicsText/getRelationList']
    return (
      <div className={styles.topic_container} onMouseDown={this.onMouseDown}>
        <div className={styles.topic_input_wrap}>
          <Input
            prefix={<SearchOutlined />}
            placeholder="输入关键字搜索话题"
            size="large"
            autoComplete="off"
            maxLength={30}
            onChange={this.onChangeTopicName}
          />
        </div>
        <Spin spinning={loadingGetRelationList}>
          <div className={styles.topic_list}>
            {
              relationList.length == 0 ?
                <NoDataRender style={{marginTop: 40}}/>
                :
                <>
                  <div className={styles.topic_title}>全部话题</div>
                  {
                    relationList.map(item => {
                      return <div className={styles.topic_item} onClick={() => this.itemOnClick(item)}>#{item.topicName}#</div>
                    })
                  }
                </>
            }
          </div>
        </Spin>

      </div>
    )
  }
}

export default connect(({ loading, graphicsText }) => ({loading, graphicsText}))(Topic)
