/**
 * @Description: PC端方案审核弹窗
 * @author: 赵斐
 */
import React, { useEffect, useState } from 'react';
import { Modal , Input , Button, message} from 'antd';
import styles from './index.less'

interface PropsType {
  planInputValue:string,   // 输入框数据回显
  visible: boolean,       // 结束指导提示弹窗状态
  onCancel: (type?: number,val?:string) => void,   // 取消回调
  dataSource:any,       // 病例数据
}
const { TextArea } = Input;

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { visible, dataSource , onCancel , planInputValue } = props;
  const [state, setState] = useState('')   // 审核原因
  const [plan,setPlan] = useState('')   // 方案展示
  const {
    orthodonticCaseDictDtoList,  // 病例详情字典
  } = dataSource || {}

  useEffect(()=>{
    setState(planInputValue)
    if (Array.isArray(orthodonticCaseDictDtoList) && orthodonticCaseDictDtoList.length) {
      const result = orthodonticCaseDictDtoList.find(item => item.id === 6);
      const { subsetList } = result || {}
      if(Array.isArray(subsetList) && subsetList.length){
        subsetList.map((item:any)=>{
          setPlan(item.inputContent)
        })
      }
    }


  },[])

  // 点击确认并关闭弹窗
  const onConfirm = () => {
    if(state){
      onCancel(1,state.trim())
    }else{
      message.error('请填写审核原因')
    }
  }

  // 关闭弹窗
  const handleCancel = () => {
    if(state){
      onCancel(2,state.trim())
    }else{
      message.error('请填写审核原因')
    }
  };
  //点击叉号 关闭弹窗
  const closeCancel = () => {
    onCancel()

  };

  /**
   * 输入框事件
   * @param value  输入值
   */
  const onChangeTextArea = (value:string)=>{
    setState(value)
  }

  return (
    <Modal
      title="方案审核"
      width={702}
      open={visible}
      onCancel={closeCancel}
      className={styles.modal}
      destroyOnClose
      footer={null}
    >
      <div className={styles.plan_content}>
        治疗方案：{plan}
      </div>
      <p className={styles.plan_title}>审核意见</p>
      <TextArea rows={8} placeholder="" maxLength={300} value={state} onChange={e => onChangeTextArea(e.target.value)} style={{resize:'none'}}/>
      <div className={styles.plan_footer}>
        <Button style={{marginRight:8}} onClick={()=>{handleCancel()}}>审核驳回</Button>
        <Button type='primary' onClick={()=>{onConfirm()}}>审核通过</Button>
      </div>
    </Modal>
  )
}
export default Index
