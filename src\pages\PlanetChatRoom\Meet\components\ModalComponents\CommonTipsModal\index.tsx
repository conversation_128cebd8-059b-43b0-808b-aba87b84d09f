/**
 * @Description: 会议详情-公共提示弹窗
 */
import React, { useState } from 'react';
import { history, connect } from 'umi'
import { Spin } from 'antd'
import { Modal, Checkbox } from 'antd-mobile';
import styles from './index.less';
import { useDebounce } from "@/utils/utils";

import SpatialDetail_modal_warn_icon from '@/assets/PlanetChatRoom/SpatialDetail_modal_warn_icon.png' // warning图标

interface PropsType {
  visible: boolean,                  // 弹窗是否显示
  isShowHelpFile: boolean,           // 是否展示帮助文案按钮
  title?: string,                    // 标题
  text?: string,                     // 文案
  text2?: string,                     // 需要另起一行的文案
  leftBtnText?: string,              // 左边按钮文案
  rightBtnText?: string,             // 右边按钮文案
  checkBoxText?: string,             // 额外勾选项文案
  onClickLeftBtn?: () => void,           // 点击左边按钮的回调
  onClickRightBtn?: () => void,          // 点击右边按钮的回调
  onClickHelpFileBtn?: () => void,       // 点击右边按钮的回调
  ModalLoading?: boolean,                // 弹窗是否处于加载状态
  isShowIcon: boolean, // 是否展示标题中的icon
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  // 不传leftBtnText，则只展示一个按钮
  const {
    loading,
    ModalLoading,
    visible = false,
    title = '', // 标题
    text = '', // 文案
    text2 = '', // 需要另起一行的文案
    leftBtnText = '', // 左边按钮文案
    rightBtnText = '我知道了', // 右边按钮文案
    checkBoxText = '', // 勾选框文案
    isShowHelpFile = false, // 是否展示帮助按钮
    onClickLeftBtn, // 点击左边按钮
    onClickRightBtn, // 点击右边按钮
    onClickHelpFileBtn, // 点击帮助文档
    isShowIcon = true, // 是否展示标题中的icon
  } = props

  const [isChecked, setIsChecked] = useState(false)

  // 勾选框变化
  const onChangeCheckbox = (value) => {
    setIsChecked(value)
  }

  // 关于权限的提示弹窗全部添加防抖处理
  let funcByonClickLeftBtn = ()=>{
    onClickLeftBtn({
      isChecked:isChecked,
    })
  }

  let funcByonClickRightBtn = ()=>{
    onClickRightBtn({
      isChecked:isChecked,
    })
  }
  funcByonClickLeftBtn = useDebounce(funcByonClickLeftBtn, 300)
  funcByonClickRightBtn = useDebounce(funcByonClickRightBtn, 300)

  return (
    <Modal
      visible={visible}
      className={styles.modal}
      destroyOnClose={true}
      getContainer={() => document.body}
      content={
        <Spin spinning={!!ModalLoading}>
          <div>
            {/* 提示信息标题 */}
            {
              title &&
              <div className={styles.header}>
                {
                  isShowIcon &&
                  <img src={SpatialDetail_modal_warn_icon} width={24} height={24} style={{flexShrink: 0}} alt=""/>
                }
                <div className={styles.title}>{title}</div>
              </div>
            }

            {/* 提示信息文案 */}
            {
              (text || text2) &&
              <div className={styles.text_wrap}>
                {
                  text &&
                  <div className={styles.text}>{text}</div>
                }
                {
                  text2 &&
                  <div className={styles.text}>{text2}</div>
                }
              </div>
            }

            {/* 额外勾选项 */}
            {
              checkBoxText &&
              <div className={styles.checkbox_wrap}>
                <Checkbox style={{
                  '--icon-size': '14PX',
                  '--font-size': '12PX',
                  '--gap': '4PX',
                }} checked={isChecked} onChange={onChangeCheckbox}>{checkBoxText}</Checkbox>
              </div>
            }

            {/* 按钮 */}
            <div className={styles.btn_wrap}>
              {/* 左边按钮 */}
              {
                leftBtnText &&
                <div
                  className={styles.left_btn}
                  onClick={funcByonClickLeftBtn}
                >{leftBtnText}</div>
              }
              {/* 右边按钮 */}
              <div className={styles.right_btn} onClick={funcByonClickRightBtn}>{rightBtnText}</div>
            </div>

            {
              isShowHelpFile &&
              <div className={styles.help_file} onClick={onClickHelpFileBtn}>
                <Spin style={{marginRight:'5px'}} spinning={!!loading.effects['PlanetChatRoom/downScreenShareHelpFile']}></Spin>
                帮助文档
              </div>
            }
          </div>
        </Spin>
      }
    />
  )
}
export default connect(({ loading }: any) => ({ loading }))(Index)
