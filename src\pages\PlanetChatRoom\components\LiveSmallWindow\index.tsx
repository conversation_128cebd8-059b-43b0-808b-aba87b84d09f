import React, {useEffect, useRef, useState,} from 'react';
import {connect, history} from 'umi';
import styles from './index.less';
import {message, Slider, Spin} from 'antd';
import classNames from 'classnames';
import _ from 'lodash';
import Draggable from 'react-draggable';
import TCPlayer from 'tcplayer.js';
import 'tcplayer.js/dist/tcplayer.min.css';
import '../video-js.min.css';
import {anchor, licenseUrl} from '@/app/config';
import Stream from '@/componentsByTRTC/Stream';
import {formatTime, getDAesString, processNames, randomColor} from '@/utils/utils';
import dynamic from 'next/dynamic';
import TRTC from 'trtc-js-sdk';
import DeviceDetector from '@/componentsByTRTC/DeviceDetector';
import {saveLocalStateByStream} from '@/utils/utilsByTRTC';

const DynamicRtc = dynamic(import('@/componentsByTRTC/BaseRTC'), {ssr: false});
const DynamicShareRtc = dynamic(import('@/componentsByTRTC/ShareRTC'), {ssr: false});
const DeviceDetectorByDetector = dynamic(import('@/componentsByTRTC/DeviceDetector/detector'), {
  ssr: false,
});
const baseSize = 16;

const TypeMsgList = 1;
const TypeGuestlist = 2;
const TypeLienApplicationList = 3;

type propsType = {
  localStreamConfig: any;
  remoteStreamConfigList: any;
  RTC: any;
  shareRTC: any;
  isJoined: boolean;
  onChange: any;
  spaceId: any;
  getGuestList: any;
  getSpaceInfo: any;
  changeUrlParams: any;
  elapsedTime: any;
  shareOnClick: any;
};
function reducer(state, action) {
  return action && action.data ? [...action.data] : [];
}
const Index: React.FC<propsType> = (props) => {
  const {PlanetChatRoom, dispatch, loading} = props;

  const {
    userInfo,
    screenShareUser,
    SpaceInfo,
    handUpList,
    currentUserType,
    isInitialized,
    currentLiveUserList,
    playerInfo,
    isShowLiveSmallWindow,
    isNotLogin,
  } = PlanetChatRoom || {};

  let {
    wxUserId,
    imUserId,
    status: statusBySpaceInfo,
    isCollect,
    handUpStatusType,
    videoList,

    trtcAppId,
    userSig,
    roomId,
    status,
    spaceCoverUrlShow,
    videoSecond,
    starSpaceType,
  } = SpaceInfo || {};

  const inputRefs = useRef([]);
  const video = true;
  const audio = true;
  const mode = 'rtc';

  const inputRef = useRef(null);

  const [isShowCameraList, setIsShowCameraList] = useState(false);

  const [contentListType, setContentListType] = useState(TypeMsgList);

  const [isHiddenSpaceAdvertisingUrlShow, setIsHiddenSpaceAdvertisingUrlShow] = useState(false);

  const [isOpenClockinPopup, setIsOpenClockinPopup] = useState(false);

  const [isOpenLiveBookingPopup, setIsOpenLiveBookingPopup] = useState(false);

  const [hasMoreBySignInList, setHasMoreBySignInList] = useState(true);

  const [hasMoreByLiveBookingList, setHasMoreByLiveBookingList] = useState(true);

  const [hasMoreByMsgList, setHasMoreByMsgList] = useState(true);

  const [msgContentHeight, setMsgContentHeight] = useState(true);

  const [playerStateData, setPlayerStateData] = useState(null);
  const [RTC, setRTC] = useState(null);
  const [shareRTC, setShareRTC] = useState(null);
  const [userID, setUserID] = useState('');
  const [roomID, setRoomID] = useState('');
  const [cameraID, setCameraID] = useState('');
  const [microphoneID, setMicrophoneID] = useState('');
  const [timObj, setTimObj] = useState(null);
  const [localStreamConfig, setLocalStreamConfig] = useState(null);
  const [remoteStreamConfigList, setRemoteStreamConfigList] = useState([]);
  const [isJoined, setIsJoined] = useState(false);
  const [isPublished, setIsPublished] = useState(false);
  const [userRole, setUserRole] = useState(anchor);
  const [mountFlag, setMountFlag] = useState(false);
  const [pwdArray, setPwdArray] = useState([null, null, null, null]);
  const [loadingByCheckDevices, setLoadingByCheckDevices] = useState(null);
  const [startTime, setStartTime] = useState(null);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [loadingByPage, setLoadingByPage] = useState(null);
  const [maskVisible, setMaskVisible] = useState(false);
  const [RoomId, setRoomId] = useState(28);
  const [videoPlayer, setVideoPlayer] = useState(null);
  const val = React.useRef();
  const playerRefVideo = useRef(null);
  // const videoPlayer = useRef(null);

  const shareRemoteStreamConfig =
    Array.isArray(remoteStreamConfigList) &&
    remoteStreamConfigList.find((item) => item.userID.indexOf('share') !== -1 && item.hasVideo);
  const userCameraRemoteStreamList =
    Array.isArray(remoteStreamConfigList) &&
    remoteStreamConfigList.filter((item) => item.userID.indexOf('share') == -1);
  const baseSize = 16;
  const UerInfo = JSON.parse(localStorage.getItem('userInfo')) || {};

  useEffect(async () => {
    await dispatch({type: 'PlanetChatRoom/clean'});
    await dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {isNotLogin: UerInfo && UerInfo.friUserId ? false : true},
    });
    await getSpaceInfo();
    await getMsgCodeUserInfo();
    await getGuestList();
    await getSpaceGroupMsg();
    await getSpaceBulletScreen();
    await getHandUpList();

    await dispatch({
      type: 'PlanetChatRoom/setCurrentUserType',
      payload: {},
    });
  }, []);

  useEffect(() => {
    return async () => {
      await message.destroy();

      await dispatch({
        type: 'PlanetChatRoom/closeSmallWindow',
      });

      if (isShowLiveSmallWindow && !!val?.current?.playerStateData?.currentTime) {
        await dispatch({
          type: 'PlanetChatRoom/saveVideoTime',
          payload: {
            wxUserId: wxUserId,
            spaceId: isShowLiveSmallWindow,
            videoSecond: _.floor(val.current.playerStateData.currentTime, 0),
          },
        });
      }
    };
  }, []);

  useEffect(() => {
    val.current = {...val.current, playerStateData: playerStateData};
  }, [playerStateData]);

  React.useEffect(() => {
    if (statusBySpaceInfo != 3 && !(Array.isArray(videoList) && videoList[0])) {
      return;
    }
    if (!playerRefVideo.current) {
      return;
    }
    let currentTimeByVideoSecond = videoSecond || 0;

    const videoJsNode = playerRefVideo.current;
    if (videoJsNode) {
      let sources =
        Array.isArray(videoList) &&
        videoList.map((item) => {
          const {vodPathUrl} = item || {};
          if (!vodPathUrl) {
            return;
          }
          let vodByDAes = getDAesString(vodPathUrl, 'arrail-dentail&2', 'arrail-dentail&3');
          return {src: vodByDAes};
        });

      let TCPlayerObj = TCPlayer(videoJsNode.id, {
        sources: sources,
        licenseUrl: licenseUrl,
      });

      if (!TCPlayerObj) {
        return;
      }
      TCPlayerObj.ready(async () => {
        setVideoPlayer(TCPlayerObj);
        await setTimeout(() => {
        }, 1000);
        await setPlayerStateData({
          currentTime: TCPlayerObj.currentTime(),
          duration: TCPlayerObj.duration() ? TCPlayerObj.duration() : videoList[0].recordDuration,
          paused: TCPlayerObj && TCPlayerObj.paused && TCPlayerObj.paused(),
        });
      });

      TCPlayerObj.on('timeupdate', (value) => {
        let state = {
          currentTime: TCPlayerObj.currentTime(),
          duration: TCPlayerObj.duration(),
          paused: TCPlayerObj.paused(),
        };
        handlePlayerStateChange(state);

        if (isNotLogin) {
          if (TCPlayerObj?.currentTime() >= 600) {
            TCPlayerObj?.pause();
          } else {
          }
        }
      });

      TCPlayerObj.on('error', (value) => {
        dispatch({
          type: 'PlanetChatRoom/closeSmallWindow',
        });
      });

      TCPlayerObj.one('canplay', (value) => {
        setTimeout(() => {
          if (!!playerInfo && TCPlayerObj) {
            setTimeout(() => {
              TCPlayerObj.currentTime(
                playerInfo.currentTime ? playerInfo.currentTime : currentTimeByVideoSecond,
              );
              if (!!TCPlayerObj.paused()) {
                TCPlayerObj.pause();
              } else {
                TCPlayerObj.play();
              }
            }, 1000);
          } else {
            TCPlayerObj.currentTime(currentTimeByVideoSecond);
          }
        }, 100);
      });
    }
  }, [videoList]);

  useEffect(() => {
    return () => {
      if (!!videoPlayer) {
        videoPlayer.dispose();
      }
    };
  }, [videoPlayer]);

  useEffect(() => {
    return async () => {
      if (RTC && (RTC.isPublished || RTC.isPublishing)) {
        await handleUnPublish();
      }

      if (RTC && RTC.isJoined) {
        await handleLeave();
      }
    };
  }, [RTC]);

  useEffect(() => {
    if (status == 1 && !loadingByCheckDevices && RTC) {
      handleJoin();
    }
  }, [loadingByCheckDevices]);

  useEffect(() => {
    if (status == 1 && RTC && !RTC.isJoined && currentUserType == 3 && handUpStatusType != 1) {
      handleJoin();
    }
  }, [RTC]);

  useEffect(() => {
    if (trtcAppId && roomId) {
      initializationByTRTC();
    }
  }, [isInitialized]);

  const initializationByTRTC = async () => {
    await setLoadingByCheckDevices(true);
    await setUserID(imUserId);
    await setRoomID(roomId);

    if (status != 3) {
      if (currentUserType != 3 || (currentUserType == 3 && handUpStatusType == 1)) {
        let CameraIDByCheckCamera,
          MicrophoneIDByCheckMicrophone = null;

        let checkCamera = await checkDevices('camera');
        let checkMicrophone = await checkDevices('microphone');

        if (checkCamera) {
          let CamerasDeviceList = await TRTC.getCameras();
          CameraIDByCheckCamera =
            (await Array.isArray(CamerasDeviceList)) &&
            CamerasDeviceList.length > 0 &&
            CamerasDeviceList[0].deviceId;
          await setCameraID(CameraIDByCheckCamera);
        }
        if (checkMicrophone) {
          let MicrophonesDeviceList = await TRTC.getMicrophones();
          MicrophoneIDByCheckMicrophone =
            (await Array.isArray(MicrophonesDeviceList)) &&
            MicrophonesDeviceList.length > 0 &&
            MicrophonesDeviceList[0].deviceId;
          await setMicrophoneID(MicrophoneIDByCheckMicrophone);
        }

        if (checkMicrophone) {
          await showDeviceDetector(CameraIDByCheckCamera, MicrophoneIDByCheckMicrophone);
        }
      }
      await setLoadingByCheckDevices(false);
      if (status == 1 && RTC && !RTC.isJoined) {
        await handleJoin();
      }
    } else {
      await setLoadingByCheckDevices(false);
    }
  };

  useEffect(() => {
    if (status == 1 && RTC && !RTC.isJoined) {
      handleJoin();
    }

    if (status == 3 && RTC && RTC.isJoined) {
      setIsJoined(false);
      setRTC(null);
    }
  }, [status]);

  const checkDevices = async (deviceType) => {
    let checkType = true;
    const res = await TRTC.checkSystemRequirements();
    if (!res) {
      checkType = false;
      message.error('您的浏览器环境不支持TRTC，请使用Chrome浏览器体验');
    }
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        audio: deviceType === 'microphone',
        video: deviceType === 'camera',
      });
      mediaStream.getTracks()[0].stop();
    } catch (error) {
      checkType = false;
      if (error.name === 'NotAllowedError') {
        alert(`请允许网页访问${deviceType === 'microphone' ? '麦克风' : '摄像头'}的权限！`);
      } else if (error.name === 'NotFoundError') {
        alert(`请检查${deviceType === 'microphone' ? '麦克风' : '摄像头'}设备连接是否正常！`);
      } else if (error.name === 'NotReadableError') {
        alert(
          `请检查${
            deviceType === 'microphone' ? '麦克风' : '摄像头'
          }设备是否被其它应用占用或未授权应用权限！`,
        );
      }
    }
    return checkType;
  };

  const showDeviceDetector = async (CameraIDByCheckCamera, MicrophoneIDByCheckMicrophone) => {
    if (
      !(CameraIDByCheckCamera && MicrophoneIDByCheckMicrophone) &&
      (currentUserType == 1 || currentUserType == 2) &&
      !!props.PlanetChatRoom.isLive
    ) {
      await message.warning('获取设备麦克风和摄像头权限失败,开启检查设备状态');
      (await DeviceDetector) && DeviceDetector.show();
    }
  };

  const handleLeave = async () => {
    shareRTC && shareRTC.isJoined && shareRTC.handleLeave();
    await RTC.handleLeave();
  };

  const handleJoin = async () => {
    await RTC?.handleJoin();
    if (
      currentUserType == 1 ||
      currentUserType == 2 ||
      (currentUserType == 3 && handUpStatusType == 1)
    ) {
      if (microphoneID) {
        await RTC?.handlePublish();
      } else {
        message.warning('请开启麦克风权限,否则无法推送直播流');
      }
    }
  };

  const handleUnPublish = async () => {
    await RTC.handleUnPublish();
  };

  const getSpaceInfo = async () => {
    let wxuserId = UerInfo && UerInfo.friUserId;

    let response = await dispatch({
      type: 'PlanetChatRoom/getSpaceInfo',
      payload: {
        spaceId: isShowLiveSmallWindow,
        wxUserId: wxuserId,
      },
    });
    if (response && response.code == 422) {
      message.warning(response.msg ? response.msg : '空间不存在!');
    } else if (!response || (response && response.code != 200)) {
      message.error('获取空间信息失败');
    }
    return response;
  };

  useEffect(() => {
    const {status} = SpaceInfo || {};
    if (currentUserType == 1 && status == 1) {
      val.current = null;
      val.current = {
        ...SpaceInfo,
        currentUserType: currentUserType,
      };
    }
  }, [SpaceInfo, currentUserType]);

  const getMsgCodeUserInfo = async () => {
    let wxuserId = UerInfo && UerInfo.friUserId;
    if (!!wxuserId) {
      let response = await dispatch({
        type: 'PlanetChatRoom/getMsgCodeUserInfo',
        payload: {},
      });
    }
  };

  const getSpaceGroupMsg = async (msgSeq) => {
    const dataByGetSpaceGroupMsg = await dispatch({
      type: 'PlanetChatRoom/getSpaceGroupMsg',
      payload: {
        spaceId: isShowLiveSmallWindow,
        msgSeq: msgSeq,
        pageSize: 10,
      },
    });
  };

  const getSpaceBulletScreen = async () => {
    const dataByGetSpaceGroupMsg = await dispatch({
      type: 'PlanetChatRoom/getSpaceBulletScreen',
      payload: {
        spaceId: isShowLiveSmallWindow,
        eventTime: null,
      },
    });
  };

  const getGuestList = async () => {
    let wxuserId = UerInfo && UerInfo.friUserId;
    let response = await dispatch({
      type: 'PlanetChatRoom/getGuestListInfo',
      payload: {
        spaceId: isShowLiveSmallWindow,
        wxUserId: wxuserId,
      },
    });
    if (!response || (response && response.code != 200)) {
      message.error('获取嘉宾列表信息失败');
      return;
    }
  };

  let handlePlayerStateChange = (state, prevState) => {
    setPlayerStateData(state);
  };

  const onClickByHorizontalLiveRoom_camera_picture_Box = () => {
    setIsShowCameraList(!isShowCameraList);
  };

  const onClickByCollect = async () => {
    let wxuserId = UerInfo && UerInfo.friUserId;
    await dispatch({
      type: 'PlanetChatRoom/spaceCollect',
      payload: {
        spaceId: isShowLiveSmallWindow,
        wxUserId: wxuserId,
        collectType: isCollect == 1 ? 0 : 1,
      },
    });
  };

  const getHandUpList = async () => {
    let wxuserId = UerInfo && UerInfo.friUserId;
    await dispatch({
      type: 'PlanetChatRoom/getHandUpList',
      payload: {
        spaceId: isShowLiveSmallWindow,
        wxUserId: wxuserId,
        pageNum: 1,
        pageSize: 100,
      },
    });
  };

  const setState = (type, value) => {
    switch (type) {
      case 'join':
        setIsJoined(value);
        break;
      case 'publish':
        setIsPublished(value);
        break;
      default:
        break;
    }
    if (type == 'publish' && value) {
      initSetLocalStreamConfig();
    }
  };

  const initSetLocalStreamConfig = () => {
    let LocalStateByStreamConfig = {
      mutedAudio: true,
      mutedVideo: true,
    };

    saveLocalStateByStream({
      id: isShowLiveSmallWindow,
      mutedAudio: true,
      mutedVideo: true,
    });

    if (localStreamConfig && LocalStateByStreamConfig) {
      let {mutedAudio, mutedVideo} = LocalStateByStreamConfig;
      if (mutedAudio) {
        RTC?.muteAudio();
      }
      if (mutedVideo) {
        RTC?.muteVideo();
      }
      if (mutedAudio || mutedVideo) {
        setLocalStreamConfig({
          ...localStreamConfig,
          mutedAudio: mutedAudio,
          mutedVideo: mutedVideo,
        });
      }
    }
  };

  const removeUser = (userID, streamType) => {
    if (streamType === 'local') {
      setLocalStreamConfig(null);
      setRemoteStreamConfigList([]);
    } else {
      setRemoteStreamConfigList((preList) =>
        preList.filter((streamConfig) => streamConfig.userID !== userID),
      );
    }
  };

  const addStream = (stream) => {
    const streamType = stream.getType();
    const userID = stream.getUserId();
    switch (streamType) {
      case 'local':
        setLocalStreamConfig({
          stream,
          streamType,
          userID,
          hasAudio: audio,
          hasVideo: video,
          mutedAudio: false,
          mutedVideo: false,
          shareDesk: false,
          audioVolume: 0,
          userInfo: JSON.stringify(userInfo),
        });
        break;
      default: {
        setRemoteStreamConfigList((preList) => {
          const newRemoteStreamConfigList =
            preList.length > 0
              ? preList.filter(
                (streamConfig) =>
                  !(streamConfig.userID === userID && streamConfig.streamType === streamType),
              )
              : [];
          newRemoteStreamConfigList.push({
            stream,
            streamType,
            userID,
            hasAudio: stream.hasAudio(),
            hasVideo: stream.hasVideo(),
            mutedAudio: false,
            mutedVideo: false,
            subscribedAudio: true,
            subscribedVideo: true,
            resumeFlag: false,
            audioVolume: 0,
          });
          return newRemoteStreamConfigList;
        });
        break;
      }
    }
  };

  const updateStream = (stream) => {
    if (stream.getType() === 'local') {
      setLocalStreamConfig({
        ...localStreamConfig,
        stream,
        hasAudio: stream.hasAudio(),
        hasVideo: stream.hasVideo(),
      });
    } else {
      setRemoteStreamConfigList((preList) =>
        preList.map((config) =>
          config.stream === stream
            ? {
              ...config,
              stream,
              hasAudio: stream.hasAudio(),
              hasVideo: stream.hasVideo(),
            }
            : config,
        ),
      );
    }
  };

  const updateStreamConfig = (userID, type, value) => {
    if (localStreamConfig && localStreamConfig.userID === userID) {
      const config = {};
      switch (type) {
        case 'audio-volume':
          if (localStreamConfig.audioVolume === value) {
            break;
          }
          config.audioVolume = value;
          break;
        case 'share-desk':
          config.shareDesk = value;
          break;
        case 'uplink-network-quality':
          // PromptDeviceNetworkState(value);
          config.uplinkNetworkQuality = value > 0 ? 6 - value : value;
          break;
        case 'downlink-network-quality':
          config.downlinkNetworkQuality = value > 0 ? 6 - value : value;
          break;
        default:
          break;
      }
      setLocalStreamConfig((prevConfig) => ({
        ...prevConfig,
        ...config,
      }));
      return;
    }

    const config = {};
    switch (type) {
      case 'mute-audio':
        config.mutedAudio = true;
        break;
      case 'unmute-audio':
        config.mutedAudio = false;
        break;
      case 'mute-video':
        config.mutedVideo = true;
        break;
      case 'unmute-video':
        config.mutedVideo = false;
        break;
      case 'resume-stream':
        config.resumeFlag = true;
        break;
      case 'audio-volume':
        if (config.audioVolume === value) {
          break;
        }
        config.audioVolume = value;
        break;
      default:
        break;
    }
    setRemoteStreamConfigList((preList) =>
      preList.map((item) => (item.userID === userID ? {...item, ...config} : item)),
    );
  };

  const removeStream = (stream) => {
    const streamType = stream.getType();
    const userID = stream.getUserId();
    switch (streamType) {
      case 'local':
        setLocalStreamConfig((prevConfig) => ({
          ...prevConfig,
          hasAudio: false,
          hasVideo: false,
        }));
        break;
      default: {
        setRemoteStreamConfigList((preList) =>
          preList.map((streamConfig) =>
            streamConfig.userID === userID && streamConfig.streamType === streamType
              ? {
                ...streamConfig,
                hasAudio: false,
                hasVideo: false,
                subscribedAudio: false,
                subscribedVideo: false,
              }
              : streamConfig,
          ),
        );
        break;
      }
    }
  };

  const userInfoData = JSON.parse(localStorage.getItem('userInfo'));
  return (
    <>
      {userID && roomID && trtcAppId && (
        <DynamicRtc
          onRef={(ref) => setRTC(ref)}
          SDKAPPID={trtcAppId}
          userSig={userSig}
          userID={imUserId}
          roomID={roomID}
          // useStringRoomID={useStringRoomID}
          cameraID={cameraID}
          microphoneID={microphoneID}
          audio={audio}
          video={video}
          mode={mode}
          setState={setState}
          // addUser={addUser}
          removeUser={removeUser}
          addStream={addStream}
          updateStream={updateStream}
          updateStreamConfig={updateStreamConfig}
          removeStream={removeStream}
          role={userRole}
        ></DynamicRtc>
      )}

      {localStreamConfig && (
        <DynamicShareRtc
          onRef={(ref) => setShareRTC(ref)}
          SDKAPPID={trtcAppId}
          userSig={(screenShareUser && screenShareUser.userSig) || userSig}
          userID={(screenShareUser && screenShareUser.imUserId) || imUserId}
          roomID={roomID}
          relatedUserID={(screenShareUser && screenShareUser.imUserId) || imUserId}
          // useStringRoomID={useStringRoomID}
          updateStreamConfig={updateStreamConfig}
        ></DynamicShareRtc>
      )}
      <Draggable
        // axis="x"
        // handle=".handle"
        cancel=".no-drag"
        defaultPosition={{x: 100, y: 100}}
        position={null}
        onMouseDown={async (e) => {
          // e.stopPropagation();
          // e.preventDefault()
        }}
        // grid={[25, 25]}
        // scale={1}
        onStart={() => {
        }}
        onDrag={() => {
        }}
        onStop={() => {
        }}
      >
        <div
          onClick={(event) => {
            event.stopPropagation();
            event.preventDefault();
          }}
          className={styles.warp_content}
        >
          <div className={styles.content}>
            <Spin
              spinning={
                !!loading.effects['PlanetChatRoom/getSpaceInfo'] ||
                !!loading.effects['PlanetChatRoom/getMsgCodeUserInfo'] ||
                !!loading.effects['PlanetChatRoom/getSpaceGroupMsg'] ||
                !!loading.effects['PlanetChatRoom/getSpaceBulletScreen'] ||
                !!loadingByCheckDevices
              }
            >
              <div className={styles.video_content}>
                {
                  <div className={styles.Stream_Content}>
                    <div className={styles.Stream_config_title}>
                      <div
                        onClick={async (event) => {
                          event.stopPropagation();
                          event.preventDefault();
                          await dispatch({
                            type: 'PlanetChatRoom/closeSmallWindow',
                          });
                        }}
                        className={classNames({
                          [styles.dom_closeIcon]: true,
                          'no-drag': true,
                        })}
                      ></div>
                      <div
                        onClick={async (event) => {
                          event.stopPropagation();
                          event.preventDefault();
                          let idByLisve = isShowLiveSmallWindow;
                          await dispatch({
                            type: 'PlanetChatRoom/closeSmallWindow',
                          });
                          await setTimeout(() => {
                            if (starSpaceType == 2) {
                              history.push(`/PlanetChatRoom/Meet/${idByLisve}`);
                            } else {
                              history.push(`/PlanetChatRoom/Live/${idByLisve}`);
                            }
                          }, 800);
                        }}
                        className={classNames(styles.dom_fullIcon, 'no-drag')}
                      ></div>
                    </div>
                  </div>
                }
                <div className={styles.Live_Video}>
                  {(!shareRemoteStreamConfig ||
                    (!!shareRemoteStreamConfig && !shareRemoteStreamConfig.stream)) && (
                    <div className={styles.default_Bg}>
                      {!!spaceCoverUrlShow && (
                        <img src={spaceCoverUrlShow} className={styles.spaceCoverUrlShow}></img>
                      )}
                    </div>
                  )}

                  {shareRemoteStreamConfig && (
                    <Stream
                      key={`${shareRemoteStreamConfig.stream.getUserId()}_${shareRemoteStreamConfig.stream.getType()}`}
                      stream={shareRemoteStreamConfig.stream}
                      config={shareRemoteStreamConfig}
                      init={(dom) => RTC.playStream(shareRemoteStreamConfig.stream, dom)}
                    ></Stream>
                  )}

                  {!!props.PlanetChatRoom.isLive && isJoined && (
                    <div
                      className={classNames({
                        [styles.HorizontalLiveRoom_camera_picture_Box]: true,
                        [styles.isShowCameraList]: isShowCameraList,
                      })}
                    >
                      <div
                        onClick={onClickByHorizontalLiveRoom_camera_picture_Box}
                        className={classNames({
                          [styles.HorizontalLiveRoom_camera_picture_btn]: true,
                          [styles.HorizontalLiveRoom_camera_picture_take_back_btn]:
                          isShowCameraList,
                          'no-drag': true,
                        })}
                      />
                      <div className={styles.HorizontalLiveRoom_camera_picture_camera_live}>
                        {localStreamConfig && RTC?.isPublished && (
                          <div
                            className={classNames({
                              [styles.HorizontalLiveRoom_camera_picture_camera_item]: true,
                              [styles.HorizontalLiveRoom_camera_picture_camera_item_hidden]:
                              localStreamConfig && localStreamConfig.mutedVideo,
                            })}
                          >
                            <div
                              className={classNames({
                                [styles.StreamWarp]: true,
                                [styles.StreamWarpHidden]:
                                localStreamConfig && localStreamConfig.mutedVideo,
                              })}
                            >
                              <Stream
                                stream={localStreamConfig.stream}
                                config={localStreamConfig}
                                init={(dom) => RTC.playStream(localStreamConfig.stream, dom)}
                                onChange={(e) => props.handleLocalChange(e)}
                              ></Stream>
                            </div>
                            {localStreamConfig && localStreamConfig.mutedVideo && (
                              <div className={styles.headUrlWarp}>
                                <div
                                  style={{background: wxUserId ? randomColor(wxUserId) : 'none'}}
                                  className={styles.video_Title_box_left_avatar}
                                >
                                  {userInfoData && userInfoData.headUrl ? (
                                    <img
                                      className={styles.video_Title_box_left_avatar_img}
                                      src={userInfoData.headUrl}
                                      alt=""
                                    />
                                  ) : (
                                    userInfoData && (
                                      <div
                                        className={styles.head_sculpture_name}
                                        style={{
                                          background: wxUserId ? randomColor(wxUserId) : 'none',
                                        }}
                                      >
                                        {processNames(userInfoData.name)}
                                      </div>
                                    )
                                  )}
                                </div>
                              </div>
                            )}

                            <div
                              className={styles.HorizontalLiveRoom_camera_picture_camera_bottom_box}
                            >
                              <div>{'本人'}</div>
                              <div
                                className={classNames({
                                  [styles.HorizontalLiveRoom_camera_picture_mic_icon]:
                                  !localStreamConfig.mutedAudio &&
                                  localStreamConfig.audioVolume == 0,
                                  [styles.HorizontalLiveRoom_camera_picture_forbidden_mic_icon]:
                                  localStreamConfig.mutedAudio,
                                  [styles.HorizontalLiveRoom_camera_picture_forbidden_Lianmai]:
                                  !localStreamConfig.mutedAudio &&
                                  localStreamConfig.audioVolume > 0,
                                })}
                              ></div>
                            </div>
                          </div>
                        )}

                        {Array.isArray(userCameraRemoteStreamList) &&
                          userCameraRemoteStreamList.map((userCameraRemoteStreamConfig, index) => {
                            if (!userCameraRemoteStreamConfig) {
                              return;
                            }
                            let findBycurrentLiveUser =
                              Array.isArray(currentLiveUserList) &&
                              currentLiveUserList.find((value) => {
                                return value.imUserId == userCameraRemoteStreamConfig.userID;
                              });
                            if (!findBycurrentLiveUser) {
                              findBycurrentLiveUser =
                                Array.isArray(handUpList) &&
                                handUpList.find((value) => {
                                  return value.imUserId == userCameraRemoteStreamConfig.userID;
                                });
                            }

                            return (
                              <div
                                key={index}
                                className={classNames({
                                  [styles.HorizontalLiveRoom_camera_picture_camera_item]: true,
                                  [styles.HorizontalLiveRoom_camera_picture_camera_item_hidden]:
                                  userCameraRemoteStreamConfig &&
                                  !userCameraRemoteStreamConfig.hasVideo,
                                })}
                              >
                                <div
                                  className={classNames({
                                    [styles.StreamWarp]: true,
                                    [styles.StreamWarpHidden]:
                                    userCameraRemoteStreamConfig &&
                                    !userCameraRemoteStreamConfig.hasVideo,
                                  })}
                                >
                                  <Stream
                                    stream={userCameraRemoteStreamConfig.stream}
                                    config={userCameraRemoteStreamConfig}
                                    init={(dom) =>
                                      RTC.playStream(userCameraRemoteStreamConfig.stream, dom)
                                    }
                                  ></Stream>
                                </div>
                                {userCameraRemoteStreamConfig &&
                                  !userCameraRemoteStreamConfig.hasVideo && (
                                    <div className={styles.headUrlWarp}>
                                      <div
                                        style={{
                                          background: wxUserId ? randomColor(wxUserId) : 'none',
                                        }}
                                        className={styles.video_Title_box_left_avatar}
                                      >
                                        {findBycurrentLiveUser &&
                                        findBycurrentLiveUser.headUrlShow ? (
                                          <img
                                            className={styles.video_Title_box_left_avatar_img}
                                            src={findBycurrentLiveUser.headUrlShow}
                                            alt=""
                                          />
                                        ) : (
                                          findBycurrentLiveUser && (
                                            <div
                                              className={styles.head_sculpture_name}
                                              style={{
                                                background: findBycurrentLiveUser.wxUserId
                                                  ? randomColor(findBycurrentLiveUser.wxUserId)
                                                  : 'none',
                                              }}
                                            >
                                              {processNames(findBycurrentLiveUser.name)}
                                            </div>
                                          )
                                        )}
                                      </div>
                                    </div>
                                  )}
                                <div
                                  className={
                                    styles.HorizontalLiveRoom_camera_picture_camera_bottom_box
                                  }
                                >
                                  <div
                                    className={
                                      styles.HorizontalLiveRoom_camera_picture_camera_bottom_box_Name
                                    }
                                  >
                                    {findBycurrentLiveUser && findBycurrentLiveUser.name}
                                  </div>
                                  <div
                                    className={classNames({
                                      [styles.HorizontalLiveRoom_camera_picture_mic_icon]:
                                      !userCameraRemoteStreamConfig.mutedAudio &&
                                      userCameraRemoteStreamConfig.audioVolume == 0,
                                      [styles.HorizontalLiveRoom_camera_picture_forbidden_mic_icon]:
                                      userCameraRemoteStreamConfig.mutedAudio,
                                      [styles.HorizontalLiveRoom_camera_picture_forbidden_Lianmai]:
                                      !userCameraRemoteStreamConfig.mutedAudio &&
                                      userCameraRemoteStreamConfig.audioVolume > 0,
                                    })}
                                  ></div>
                                </div>
                              </div>
                            );
                          })}
                      </div>
                    </div>
                  )}
                </div>

                {!props.PlanetChatRoom.isLive &&
                  Array.isArray(videoList) &&
                  videoList.length > 0 && (
                    <div className={styles.VideoWarp}>
                      <video
                        key={`player-container-id-LiveSmallWindow`}
                        ref={playerRefVideo}
                        id="player-container-id-LiveSmallWindow"
                        autoPlay={true}
                        className={styles.videoJsByTC}
                        playsInline={true}
                      ></video>
                    </div>
                  )}

                {!props.PlanetChatRoom.isLive &&
                  playerStateData &&
                  playerStateData.duration != 0 && (
                    <div className={styles.video_ModeratorControl}>
                      {!!playerStateData.paused && (
                        <div
                          className={classNames(styles.playBtn, 'no-drag')}
                          onClick={() => {
                            videoPlayer?.play();
                          }}
                        ></div>
                      )}
                      {!playerStateData.paused && (
                        <div
                          className={classNames(styles.PauseBtn, 'no-drag')}
                          onClick={() => {
                            videoPlayer?.pause();
                          }}
                        ></div>
                      )}

                      <div className={styles.video_Progress_bar_warp}>
                        <div className={classNames(styles.video_Progress_bar, 'no-drag')}>
                          <Slider
                            value={_.floor(playerStateData.currentTime, 0)}
                            tooltip={{open: false}}
                            min={0}
                            max={_.floor(playerStateData.duration, 0)}
                            onChange={(value) => {
                              videoPlayer?.currentTime(value);
                            }}
                          />
                        </div>
                        <div className={styles.time_Progress}>
                          <span>
                            {playerStateData.currentTime
                              ? formatTime(_.floor(playerStateData.currentTime))
                              : '00:00:00'}
                          </span>
                          <span>/</span>
                          <span>
                            {playerStateData.duration
                              ? formatTime(_.floor(playerStateData.duration))
                              : '00:00:00'}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
              </div>
            </Spin>
          </div>
        </div>
      </Draggable>
    </>
  );
};
export default connect(({PlanetChatRoom, loading}: any) => ({PlanetChatRoom, loading}))(Index);
