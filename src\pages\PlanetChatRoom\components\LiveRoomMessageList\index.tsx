import React, {useEffect, useState} from 'react';
import classNames from 'classnames';
import Avatar from '../Avatar';
import styles from './index.less';
import {BULLET_SCREEN, SEND_APPLAUSE, SEND_FLOWERS, SIGN_IN} from '@/app/config';
import _ from 'lodash';

const LiveRoomMessageList = ({
                               msgListBySENDAPPLAUSE,
                               isNotLogin,
                               setModalVisibleByUserTokenInvalid,
                               resetTimer,
                               playerStateData,
                               props,
                               isMobile,
                               onScroll,
                               handleToggleDanmuVisibility,
                               HiddenDanmu,
                             }) => {
  const inputRef = React.createRef();

  const [viewportHeight, setViewportHeight] = useState(window.innerHeight);

  useEffect(() => {
    // 进入后msg_content将内容区域 滚动到最底部
    let liveRoom_msg_list = document.getElementById('liveRoom_msg_list');
    if (liveRoom_msg_list) {
      liveRoom_msg_list.scrollTop = liveRoom_msg_list.scrollHeight;
    }
  }, []);

  useEffect(() => {
    // 更新视口高度
    const updateViewportHeight = () => {
      setViewportHeight(window.innerHeight);
    };

    // 初次挂载时添加事件监听器
    window.addEventListener('resize', updateViewportHeight);

    // 在组件卸载时移除事件监听器
    return () => {
      window.removeEventListener('resize', updateViewportHeight);
    };
  }, []); // 空依赖数组表示只在初次挂载时执行

  useEffect(() => {
    let liveRoom_msg_list = document.getElementById('liveRoom_msg_list');
    // 判断视口高度是否小于 350
    if (viewportHeight < 350) {
      // 在此调整 liveRoom_msg_list 的高度
      if (liveRoom_msg_list) {
        liveRoom_msg_list.style.height = '20vh';
      }
    } else if (viewportHeight >= 350) {
      liveRoom_msg_list.style.height = '26vh';
    }
  }, [viewportHeight]); // 在视口高度变化时执行

  return (
    <div
      className={classNames({
        [styles.liveRoom_msg_list_Warp]: true,
        [styles.liveRoom_msg_list_Warp_PC]: !isMobile,
      })}
    >
      <div id={'liveRoom_msg_list'} className={styles.liveRoom_msg_list} onScroll={onScroll}>
        {Array.isArray(msgListBySENDAPPLAUSE) &&
          msgListBySENDAPPLAUSE.map((item, index) => {
            const {
              msgType,
              name,
              extension,
              cnt,
              msgGroupCount,
              bs,
              currentUserType: currentUserTypeByItem,
            } = item || {};
            const extensionObj = extension && JSON.parse(extension);

            const currentUserTypeByDanmu =
              extensionObj && extensionObj.currentUserType
                ? extensionObj.currentUserType
                : currentUserTypeByItem;

            return (
              <div key={index} className={styles.avatar_msg_item}>
                <div className={styles.avatar_msg_avatar}>
                  <Avatar userInfo={item} size={24}></Avatar>
                </div>
                <div className={styles.text_msg}>
                  <div className={styles.avatar_msg_name}>
                    <div className={styles.avatar_msg_name_text}>{name}</div>
                    <div>：</div>
                  </div>
                  {msgType == SEND_APPLAUSE && (
                    <>
                      <div className={styles.avatar_msg_content}>送掌声</div>
                      <div className={styles.HorizontalLiveRoom_send_guzhang_icon}></div>
                      <div className={styles.HorizontalLiveRoom_send_message_icon}>
                        ×{msgGroupCount ? msgGroupCount : cnt}
                      </div>
                    </>
                  )}
                  {msgType == SEND_FLOWERS && (
                    <>
                      <div className={styles.avatar_msg_content}>送花花</div>
                      <div className={styles.HorizontalLiveRoom_send_flowers_content_icon}></div>
                      <div className={styles.HorizontalLiveRoom_send_message_icon}>
                        ×{msgGroupCount ? msgGroupCount : cnt}
                      </div>
                    </>
                  )}
                  {msgType == SIGN_IN && (
                    <div className={styles.msg_content_item_msgContent}>
                      <div>已打卡</div>
                    </div>
                  )}
                  {msgType == BULLET_SCREEN && (
                    <div
                      className={classNames({
                        [styles.avatar_msg_content]: true,
                        [styles.msg_content_item_msgContent_emcee]: currentUserTypeByDanmu == 1, // 当前用户类型 1:主播(可发言,有连麦列表权限,有开始直播,有结束直播) 2:嘉宾(可发言) 3:观众(无权限)
                      })}
                    >
                      <div>{bs}</div>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
      </div>
      {/* 只有PC模式下才展示动态下方的输入框 */}
      {!isMobile && (
        <div className={styles.inputWarpByPC}>
          <input
            ref={inputRef}
            placeholder={'来发弹幕'}
            className={styles.input}
            type="text"
            enterKeyHint="send"
            onClick={(e) => {
              e && e.stopPropagation();
              resetTimer();
            }}
            onKeyDown={(value, e) => {
              if (value.keyCode == 13 && value.target.value.trim().length > 0) {
                //[判定登录] 判定是否已登录,未登录则弹出登录弹窗
                if (isNotLogin) {
                  setModalVisibleByUserTokenInvalid();
                  return null;
                }
                const {currentTime} = playerStateData || {};
                // 发送弹幕消息
                props.sendMessageByIm &&
                props.sendMessageByIm({
                  dataType: BULLET_SCREEN,
                  description: value.target.value,
                  relativeTime: currentTime ? _.floor(currentTime, 0) : null,
                });
                inputRef.current.value = '';
                inputRef.current.blur();
              }
            }}
          />

          <div
            onClick={handleToggleDanmuVisibility}
            className={classNames({
              [styles.OpenDanmuBtn]: !HiddenDanmu,
              [styles.OpenDanmuBtnHidden]: !!HiddenDanmu,
            })}
          ></div>
        </div>
      )}
    </div>
  );
};

export default LiveRoomMessageList;
