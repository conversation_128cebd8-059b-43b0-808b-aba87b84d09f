.wrap{
  background: #FFF;
  height: 100vh;
  position: relative;
  padding-bottom: constant(safe-area-inset-bottom);//兼容 IOS<11.2
  padding-bottom: env(safe-area-inset-bottom);//兼容 IOS>11.2\
}

.header {
  width: 100%;
  background: #FFFFFF;
  height: 44px;
  position: relative;
}

.basic_info{
  padding: 0 12px 0;
  .basic_title{
    font-size: 15px;
    font-weight: 500;
    color: #091715;
    line-height: 24px;
    margin-right: 16px;
  }

  .basic_value{
    font-size: 15px;
    font-weight: 400;
    color: #000000;
    line-height: 24px;
  }
  .basic_info_age_sex{
    display: flex;
    margin-bottom: 16px;

    .basic_wrap{
      flex: 1;
    }
  }
  .basic_info_content_desc{
    font-size: 15px;
    font-weight: 400;
    color: #000000;
    line-height: 18px;
    margin-top: 8px;
    margin-bottom: 16px;
    word-break: break-all;
    white-space: pre-wrap;
  }

  .basic_header{
    margin-bottom: 16px;

    .basic_header_title{
      font-size: 18px;
      font-weight: 600;
      color: #091715;
      line-height: 21px;
    }

    .basic_header_back{
      width: 73px;
      height: 10px;
      background: linear-gradient(90deg, #0095FF 0%, rgba(255,255,255,0) 100%);
      border-radius: 10px 10px 10px 10px;
      margin-top: -5px;
    }
  }

  .basic_info_one,.basic_info_two,.basic_info_three ,.basic_info_four ,.basic_info_five{
    padding-top: 16px;
  }
}

.detail_title {
  font-size: 14px;
  font-weight: 500;
  color: #000;
  line-height: 26px;
  margin-bottom: 8px;
}

.details_wrap {
  display: flex;
  flex-wrap: wrap;
  padding-left: 32px;
  padding-bottom: 20px;
  column-gap: 32px;
  row-gap: 8px;
  word-break: break-word;
  .detail_item {
    display: flex;
    flex-wrap: nowrap;
    &.detail_item_100 {
      width: 100%;
      margin-right: 0;
    }
    .label {
      font-size: 14px;
      color: #666;
      line-height: 26px;
      flex-shrink: 0;
      white-space: nowrap;
    }
    .value {
      flex: 1;
      font-size: 14px;
      color: #000;
      line-height: 26px;
      word-break: break-all;
    }
  }
}

// 影像资料
.image_data {
  padding-bottom: 24px;
  .detail_wrap_image {
    display: flex;
    flex-wrap: wrap;
    padding-bottom: 20px;
    column-gap: 20px;
    row-gap: 16px;
    .detail_item_image {
      .image_label {
        font-size: 14px;
        color: #666;
        line-height: 20px;
        margin-top: 8px;
        text-align: center;
        white-space: nowrap;
      }
    }
  }
  .detail_wrap_annex {
    padding: 8px 16px;
    background: #F8F8F8;
    border-radius: 3px;
    .detail_item_annex {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      padding: 8px 0;
      & + .detail_item_annex {
        border-top: 1px solid #D9D9D9;
      }
      .annex_icon {
        flex-shrink: 0;
        margin-right: 16px;
      }
      .annex_btn {
        flex-shrink: 0;
        cursor: pointer;
        font-size: 14px;
        color: #0095FF;
      }
      .annex_content {
        flex: 1;
        .annex_name {
          font-size: 14px;
          color: #000;
          line-height: 20px;
        }
        .annex_size {
          font-size: 12px;
          color: #999;
          line-height: 16px;
        }
      }
    }
  }
}

// 检查
.inspect {
  .detail_item {
    .value {
      & > span::after {
        content: "、";
      }
      & > span:last-child::after {
        display: none;
      }
    }
  }
  .detail_item.detail_item_tooth {
    width: 100%;
    margin-right: 0;
    display: flex;
    flex-wrap: wrap;
    .detail_item_wrap {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      margin-right: 40px;
      margin-bottom: 8px;
    }
  }
}
