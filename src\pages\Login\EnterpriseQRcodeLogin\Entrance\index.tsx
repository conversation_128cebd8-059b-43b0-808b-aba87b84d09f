import { FC, useEffect } from 'react';
import { connect, history } from 'umi';
import { message, Spin } from 'antd';

import { getOperatingEnv, getPageQuery } from '@/utils/utils';
import { Toast } from 'antd-mobile';
interface IProps {
  dispatch: any;
}
const Index: FC<IProps> = ({ dispatch }) => {
  const location = window.location.href;

  useEffect(() => {
    const pageQuery = getPageQuery();

    if ('tenantId' in pageQuery && pageQuery.tenantId) {
      //存储tenantId
      const tenantId = pageQuery.tenantId as string;
      localStorage.setItem('tenantId', tenantId);

      // 管理员 微信公众号推送审批消息
      if ('approvalStatus' in pageQuery && pageQuery.approvalStatus) {
        const approvalStatus = pageQuery.approvalStatus as string;
        localStorage.setItem('approvalStatus', approvalStatus);
      } else {
        localStorage.setItem('approvalStatus', '');
      }

      // 获取当前操作环境 1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用   5 FRIDAY app中使用 6 Jarvis App内使用 7 企微浏览器使用
      if (getOperatingEnv() == 2) {
        // eslint-disable-next-line @typescript-eslint/no-use-before-define
        getData(tenantId, pageQuery?.approvalStatus);
      } else {
        message.warning('请在微信中使用哦~');
      }
    }
  }, [location]);

  const getData = (tenantId, approvalStatus = '') => {
    dispatch({
      type: 'enterpriseQRcode/enterpriseQrBind',
      payload: { tenantId },
    })
      .then((res) => {
        const { code, content, msg } = res;
        // 表示已经登录过了  根据角色状态跳转不同的页面
        if (code == 200) {
           /**
           * roleType: 角色类型  BIZ_ADMIN 品牌管理员 NORMAL普通员工
           * orgCheck: 是否确认机构  用户为管理员时 若是true则不需要进入引导流程
           * bindBiz: 是否绑定当前企业  用户为普通员工  若为true, 不进入引导流程
           * waitAudit: 是否待审核 用户为普通员工  若为true,着直接跳到引导流程最后一步
           * bizName: 当前企业名称
           */
          const { roleType, orgCheck, bindBiz, waitAudit, bizName } = content;
          const access_token = localStorage.getItem('access_token');
          const { phone } = JSON.parse(localStorage.getItem('userInfo') || '');
         
          const paramsUrl = `?roleType=${roleType}&orgCheck=${orgCheck}&bindBiz=${bindBiz}&waitAudit=${waitAudit}&name=${bizName}&access_token=${access_token}&phone=${phone}&tenantId=${tenantId}`;

          // 微信消息推送 进入到审批页面
          if (approvalStatus && approvalStatus == 'wait') {
            const approvalStatusParams = `?type=${approvalStatus}&tenantId=${tenantId}&access_token=${access_token}`
            window.location.href = 
            window.location.hostname !== 'dhealth-test.friday.tech' &&  window.location.hostname !== 'localhost'
            ? `https://doctor.friday.tech/enterprise/memberList${approvalStatusParams}`
            : `https://doctortest.friday.tech/enterprise/memberList${approvalStatusParams}`;
           
          } else {
            // 微信扫码流程
            if (roleType == 'BIZ_ADMIN') { // 管理员流程
              if (orgCheck) {
                // 已认证：去首页
                history.replace('/Square');
              } else {
                // 未认证：去管理员引导页
                window.location.href = 
                window.location.hostname !== 'dhealth-test.friday.tech' &&  window.location.hostname !== 'localhost'
                  ? `https://doctor.friday.tech/enterprise/welcome${paramsUrl}`
                  : `https://doctortest.friday.tech/enterprise/welcome${paramsUrl}`;
              }
            } else {
              // 普通用户流程

              if (bindBiz) {
                // 已认证：去首页
                history.replace('/Square');
              } else {
                if (waitAudit) {
                  // 未认证但是在审核阶段: 去认证最后一步
                  window.location.href =  
                  window.location.hostname !== 'dhealth-test.friday.tech' &&  window.location.hostname !== 'localhost'
                    ? `https://doctor.friday.tech/enterprise/submitted${paramsUrl}`
                    : `https://doctortest.friday.tech/enterprise/submitted${paramsUrl}`;
                } else {
                  //未认证且未审核：去普通员工引导页
                  window.location.href = 
                  window.location.hostname !== 'dhealth-test.friday.tech' &&  window.location.hostname !== 'localhost'
                    ? `https://doctor.friday.tech/enterprise/welcome${paramsUrl}`
                    : `https://doctortest.friday.tech/enterprise/welcome${paramsUrl}`;
                }
              }
            }
          }

          
        } else {
          Toast.show({ content: msg || '请重新登录'});
        }
      })
      .catch((err) => {});
  };

  return <Spin tip="请完成认证流程后使用..." />;
};

export default connect(({ enterpriseQRcode }: any) => ({
  enterpriseQRcode,
}))(Index);
