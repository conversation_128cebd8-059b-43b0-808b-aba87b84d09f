@import '../../../../utils/imageText.less';
:global(.ant-modal).modal {
  width: 100%;
  max-width: 100%;
  height: 100%;
  top: 0;
  padding-bottom: 0;
  :global {
    .ant-modal-content {
      background: transparent;
      box-shadow: none;
      height: 100%;
    }
    .ant-modal-body {
      padding: 0;
      height: 100%;
    }
    .ant-modal-close {
      color: #fff;
      top: 100px;
      right: 100px;
    }
    .ant-modal-close-x {
      font-size: 22px;
    }

    ::-webkit-scrollbar{
      width: 7px;
      height: 7px;
    }
    ::-webkit-scrollbar-thumb{
      border-radius:10px;
      border: 2px solid #fff;
      -webkit-box-shadow: inset 0 0 0 5px rgba(0,0,0,0.2);
      background: rgba(0,0,0,0.2);
    }
    ::-webkit-scrollbar-track{
      // -webkit-box-shadow: inset 0 0 0 5px rgba(0,0,0,0.2);
      border-radius:0px;
      background:#fff;
    }
  }
}
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.content {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .iphone_box {
    width: 339px;
    height: 640px;
    border-radius: 40px;
    border: 10px solid #BFBFBF;
    background: #fff;
    display: flex;
    flex-direction: column;
    .header {
      flex-shrink: 0;
      height: 56px;
      padding-left: 12px;
      padding-top: 22px;
      i {
        display: block;
        width: 12px;
        height: 24px;
        background: url("../../../../assets/GlobalImg/go_back.png") no-repeat center;
        background-size: 100% 100%;
      }
    }
    .middle {
      flex: 1;
      overflow-y: auto;
      padding: 8px 12px;
      .article_title {
        font-size: 20px;
        color: #000;
        line-height: 28px;
        margin-bottom: 12px;
      }
      .article_top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
        .article_top_user {
          display: flex;
          align-items: center;
          i {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
            margin-right: 8px;
            font-style: normal;
            line-height: 36px;
            color: #fff;
            font-size: 12px;
            text-align: center;
            white-space: nowrap;
          }
          .user_name {
            font-size: 15px;
            color: #000;
            line-height: 21px;
          }
          .date {
            font-size: 11px;
            color: #333;
            line-height: 15px;
          }
        }
        .article_top_btn {
          border-radius: 14px;
          border: 1px solid #0095FF;
          display: flex;
          align-items: center;
          font-size: 12px;
          color: #0095FF;
          padding: 2px 8px;
          cursor: default;
          user-select: none;
        }
      }
      .article_details {
      }
    }
    .bottom {
      flex-shrink: 0;
      height: 70px;
      border-top: 1px solid #ddd;
      .bottom_toolbar {
        height: 34px;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        .toolbar_item {
          display: flex;
          align-items: center;
          font-size: 14px;
          color: #000;
          i {
            width: 20px;
            height: 20px;
            margin-right: 4px;
            &.icon_like {
              background: url("../../../../assets/GlobalImg/like_active.png") no-repeat center;
              background-size: 100% 100%;
            }
            &.icon_comment {
              background: url("../../../../assets/GlobalImg/comment.png") no-repeat center;
              background-size: 100% 100%;
            }
            &.icon_forward {
              background: url("../../../../assets/GlobalImg/forward.png") no-repeat center;
              background-size: 100% 100%;
            }
          }
        }
      }
      .bottom_gray_bar {
        width: 114px;
        height: 4px;
        border-radius: 8px;
        background: #000;
        margin: 16px auto 0;
      }
    }
  }
  .tips {
    font-size: 16px;
    color: #fff;
    margin-top: 24px;
  }
}

.footer {
  background: #fff;
  border-top: 1px solid #ddd;
  height: 80px;
  flex-shrink: 0;
  .footer_content {
    width: 816px;
    height: 100%;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .footer_content_left {
      font-size: 16px;
      color: #999;
      & > span + span {
        margin-left: 4px;
      }
      & > span:last-child {
        margin-left: 16px;
      }
    }
    .footer_content_right {
      :global {
        .ant-btn + .ant-btn {
          margin-left: 24px;
        }
        .ant-btn {
          height: 38px;
          padding-left: 20px;
          padding-right: 20px;
        }
      }
    }
  }
}


