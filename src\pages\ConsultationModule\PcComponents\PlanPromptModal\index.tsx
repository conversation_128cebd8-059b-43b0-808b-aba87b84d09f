/**
 * @Description: PC端方案审核提示弹窗
 * @author: 赵斐
 */
import React from 'react';
import { Modal } from 'antd';
import styles from './index.less'


interface PropsType {
  value:string,           // 审核方案输入原因
  auditStatus:number,     // 显示审核文案 1 审核通过 2 审核驳回
  visible: boolean,       // 结束指导提示弹窗状态
  onCancel: (val?: number) => void,   // 取消回调
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { visible, onCancel ,auditStatus = 1 } = props;
  // 点击确认并关闭弹窗
  const onConfirm = () => {
    onCancel(1)
  }

  // 关闭弹窗
  const handleCancel = () => {
    onCancel()
  };

  return (
    <Modal
      title={auditStatus == 1?"审核通过":'审核驳回'}
      width={474}
      open={visible}
      onOk={onConfirm}
      onCancel={handleCancel}
      className={styles.modal}
      destroyOnClose
      okText={auditStatus == 1?"确定通过":'确定驳回'}
      cancelText="取消"
    >
      <div className={styles.plan_content}>{auditStatus == 1?"确定审核通过该方案吗？":'确定审核驳回该方案吗？'}</div>
    </Modal>
  )
}
export default Index
