import React from 'react'
import { history, KeepAlive } from 'umi'

import IndexContent from './IndexContent'

const Index: React.FC = () => {

  return (
    <KeepAlive
      saveScrollPosition="screen"
      id={history.location.pathname}
      when={() => {
        return history.location.pathname == '/CreateGraphicsText/SelectTopic' && history.action == 'PUSH';
      }}
    >
      <IndexContent/>
    </KeepAlive>
  )
}

export default Index
