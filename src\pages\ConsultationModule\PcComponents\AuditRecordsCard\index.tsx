/**
 * @Description: 正畸病例审核记录卡片
 * @author: 赵斐
 */
import React, { useState } from 'react';
import styles from './index.less'

interface PropsType {
  data: any,          // 展示数据
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { data } = props;
  const [ openState, setOpenState ] = useState(false)
  return (
    <div className={styles.wrap}>
      <div className={styles.header}>
        <span className={styles.header_title}>审核记录</span>
        <span className={styles.header_status} onClick={()=>{setOpenState(!openState)}}>{openState?'收起':'展开'}</span>
      </div>

      <div className={openState?null:styles.hide_content}>
        {
          data.map((item:any,idx:number)=>{
            return (
              <div className={styles.detail} key={idx}>
                <div className={styles.title}>
                  <p className={styles.title_left}>最新审核状态：<span>{item.auditStatus == 1?'审核通过':item.auditStatus == 2?'审核驳回':null}</span></p>
                  <p className={styles.title_right}>{item.createDate}</p>
                </div>
                <div className={styles.desc}>{item.auditStatus == 1?'通过原因':item.auditStatus == 2?'驳回原因':null}：{item.auditOpinion}</div>
              </div>
            )
          })
        }
        
      </div>
    </div>
  )
}
export default Index
