/**
 * @Description: 病例详情，PC端
 */
import React, { useState, useEffect } from 'react';
import { connect, history } from 'umi';
import { getArrailUrl } from '@/utils/utils'
import { Anchor, Spin, message, Input, Button } from 'antd';
import styles from './PcIndex.less';

import imgIcon from '@/assets/GlobalImg/imgIcon.png'; // 图片
import treatmentIcon from '@/assets/GlobalImg/word_art.png';   // 治疗方案ICON
import lLinesIcon from '@/assets/Case/l_lines.png'; // vip会员左线小图标
import rLinesIcon from '@/assets/Case/r_lines.png'; // vip会员右线小图标
import yellowArrowIcon from '@/assets/Case/yellow_arrow.png'; // 黄色右箭头小图标
import vipIcon1 from '@/assets/Case/vip_1.png'; // vip会员小图标1
import vipIcon2 from '@/assets/Case/vip_2.png'; // vip会员小图标2
import vipIcon3 from '@/assets/Case/vip_3.png'; // vip会员小图标3
import vipIcon4 from '@/assets/Case/vip_4.png'; // vip会员小图标4
import vipIcon5 from '@/assets/Case/vip_5.png'; // vip会员小图标5
import vipIcon6 from '@/assets/Case/vip_6.png'; // vip会员小图标6
import closeIcon from '@/assets/Case/close_icon.png'; // 关闭小图标
import showIcon from '@/assets/Case/show_icon.png'; // 展开小图标

import PcHeader from '@/componentsByPc/PcHeader';          // pc头部组件
import Avatar from '@/components/Avatar'                   // 评论中头像组件
import NoDataRender from '@/components/NoDataRender'       // 暂无数据组件

const { Link } = Anchor;
const vipDeblockingList = [
  { icon: vipIcon1, text: '会员病例免费看' },
  { icon: vipIcon2, text: '会员课程免费看' },
  { icon: vipIcon3, text: '研讨视频免费看' },
  { icon: vipIcon4, text: '付费课程折扣看' },
  { icon: vipIcon5, text: '参与病例研讨会' },
  { icon: vipIcon6, text: '参加集团年度峰会' },
]

const Index: React.FC = (props: any) => {
  // 是否嵌套在5i5ya的iframe中
  const isInIframe = (self != top)

  const { dispatch, loading } = props;
  const { query } = history.location || {}
  const { excellentCaseId } = query || {}    // 优秀病例ID
  const [isShowBannerWindow, setIsShowBannerWindow] = useState(true); // 是否展示小浮窗
  const [detailsList, setDetailsList] = useState<any>(); // 病例详情数据

  // 评论相关state
  const initialCommentState = {
    postComment: '',                             // 发表评论内容
    replyComment: '',                            // 回复评论内容
    commentsSuperId: null,                       // 评论/回复上级ID
    commentsSuperUserId: null,                   // 回复上级用户ID
    replyInputPlaceholder: '回复...',              // 回复输入框placeholder
    replyCommentId: null,                        // 回复的ID（判断"回复"按钮是否显示用）
  }
  const [commentState, setCommentState] = useState(initialCommentState)
  const [commentDataSource, setCommentDataSource] = useState([])               // 评论数据
  const [loadingPostComment, setLoadingPostComment] = useState(false)          // 发表评论loading
  const [loadingReplyComment, setLoadingReplyComment] = useState(false)        // 回复评论loading

  useEffect(() => {
    initData()
    getMaExcellentCaseCommentsList()
  }, [])

  // 获取优秀病例信息
  const initData = () => {
    dispatch({
      type: "cases/getExcellentCaseInfo",
      payload: {
        excellentCaseId: excellentCaseId // 优秀病历ID
      }
    }).then( res => {
      const { content, code } = res || {};
      if(res && code == 200) {
        setDetailsList(content)
      } else {
        message.error(res.msg)
      }
    })
  }

  // 获取评论信息数据
  const getMaExcellentCaseCommentsList = () => {
    dispatch({
      type: "cases/getMaExcellentCaseCommentsList",
      payload: {
        excellentCaseId,
      }
    }).then((res: any) => {
      const { code, content, msg } = res
      if (code == 200) {
        setCommentDataSource(content || [])
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch(err => {})
  }

  // 点击锚点后不记录历史
  const handleClick = (
    e: React.MouseEvent<HTMLElement>,
    link: {
      title: React.ReactNode;
      href: string;
    },
  ) => {
    e.preventDefault();
  };

  // 点击跳转会员权益页面
  const jumpVipLeaflets = () => {
    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'pathname',       // 页面地址onchange事件
        pathnameByChild: '/Payment/MemberBenefitsPage',  // 路由信息
        searchByChild: '',  // 路由信息
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
      return
    }
    history.push('/Payment/MemberBenefitsPage')
  }

  // 点击右侧浮窗打开小鹅通
  const jumpExternalLinks = (url) => {
    window.open(url)
  }

  // 未登录进行登录
  const goLogin = () => {
    if (isInIframe) {
      const postData = {
        dataType: 'logout',       // 登出
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
      return
    }
    history.push({
      pathname: '/User/login',
      query: {
        redirectByPush: window.location.href,
      }
    })
  }

  // 详情模块（诊断、治疗计划、治疗过程、总结与讨论）
  const RenderComponent = (data:any) => {
    return data && data.length && data.map((item, ind) => {
      return <div className={styles.wrapper} key={ind}>
            {item.titleName ? <div className={styles.secondaryTitle}>{item.titleName}</div> : ''}
            <div className={styles.imgsContent}>
              {item.imgContent && item.imgContent.map((val, i) => {
                return <div className={styles.imgBox} key={i}>
                  <img src={val.imgPathShowUrl} alt='' />
                  {
                    val.imgName ?
                    <div className={styles.imgName}>
                      <img src={imgIcon} className={styles.imgIconStyle} alt=''/>{val.imgName}
                    </div>
                    : ''
                  }
                </div>
              })}
            </div>
          {item.content ? <div className={styles.imgContentText}>{item.content}</div>: ''}
      </div>
    })
  }

  // 治疗计划以下内容
  const RenderList = () => {
    // 判断是否登录，未登录遮挡一半，并提示文案
    if(localStorage.getItem('access_token')) {
      // 已登录，判断是否可查看全部
      if(isAllLook == 1) {
        return <>
          <div id="TreatmentPlanning" className={styles.box}>
            <div className={styles.firstTitle}>治疗计划</div>
            <div className={styles.content}>
              <div className={styles.topContent}>
                <div className={styles.contentTitle}></div>
                <div className={styles.contentLists}>
                  {RenderComponent(treatmentPlanList)}
                </div>
              </div>
            </div>
          </div>
          <div id="TreatmentProcess" className={styles.box}>
            <div className={styles.firstTitle}>治疗过程</div>
            <div className={styles.content}>
              <div className={styles.topContent}>
                <div className={styles.contentTitle}>图像资料:</div>
                <div className={styles.contentLists}>
                  {RenderComponent(treatmentProcessList)}
                </div>
              </div>
            </div>
          </div>
          <div id="SummaryAndDiscussion" className={styles.box}>
            <div className={styles.firstTitle}>总结与讨论</div>
            <div className={styles.content}>
              <div className={styles.topContent}>
                <div className={styles.contentTitle}></div>
                <div className={styles.contentLists}>
                  {RenderComponent(summaryDiscussList)}
                </div>
              </div>
            </div>
          </div>
        </>
      } else {
        // 查看次数，超免费次数时，遮挡并提示文案
        return <>
          <div className={styles.vip_wrap}>
            <div className={styles.vip_forbidden_wrap}>
              <div id="TreatmentPlanning" className={styles.box}>
                <div className={styles.firstTitle}>治疗计划</div>
                <div className={styles.content}>
                  <div className={styles.topContent}>
                    <div className={styles.contentTitle}></div>
                    <div className={styles.contentLists}>
                      {RenderComponent(treatmentPlanList)}
                    </div>
                  </div>
                </div>
              </div>
              <div id="TreatmentProcess" className={styles.box}></div>
              <div id="SummaryAndDiscussion" className={styles.box}></div>
            </div>
            <div className={styles.vip_shade_wrap}>
              <div className={styles.vip_shade_title} onClick={jumpVipLeaflets}>
                开通会员,查看完整内容
                <img className={styles.vip_shade_icon} src={yellowArrowIcon} />
              </div>
              <div className={styles.vip_modal_content}>
                <div className={styles.vip_shade_content}>
                  <div className={styles.vip_shade_deblocking_title}>
                    <img className={styles.vip_shade_deblocking_icon} src={lLinesIcon} />
                    <span className={styles.vip_shade_deblocking_text}>成为会员后,你将解锁</span>
                    <img className={styles.vip_shade_deblocking_icon} src={rLinesIcon} />
                  </div>
                  <div className={styles.vip_shade_deblocking_List}>
                    <div className={styles.vip_deblocking_content}>
                      {
                        vipDeblockingList.map((item, ind) => {
                          return <div key={ind} className={styles.vip_deblocking_item}>
                            <img className={styles.vip_deblocking_icon} src={item.icon} />
                            <span className={styles.vip_deblocking_text}>{item.text}</span>
                          </div>
                        })
                      }
                    </div>
                    <div className={styles.vip_deblocking_btn_wrap}>
                      <div className={styles.vip_button_style} onClick={jumpVipLeaflets}>
                        <span className={styles.vip_button_text}>开通会员&nbsp;畅享内容</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </>
      }
    } else {
      return <>
        <div className={styles.nologin_wrap}>
          <div className={styles.vip_forbidden_wrap}>
            <div id="TreatmentPlanning" className={styles.box}>
              <div className={styles.firstTitle}>治疗计划</div>
              <div className={styles.content}>
                <div className={styles.topContent}>
                  <div className={styles.contentTitle}></div>
                  <div className={styles.contentLists}>
                    {RenderComponent(treatmentPlanList)}
                  </div>
                </div>
              </div>
            </div>
            <div id="TreatmentProcess" className={styles.box}></div>
            <div id="SummaryAndDiscussion" className={styles.box}></div>
          </div>
          <div className={styles.vip_shade_wrap}>
            <div className={styles.vip_shade_title} onClick={goLogin}>
              立即登录，查看完整内容
              <img className={styles.vip_shade_icon} src={yellowArrowIcon} />
            </div>
            <div className={styles.vip_modal_content}></div>
          </div>
        </div>
      </>
    }
  }

  // 发表评论输入框onChange
  const onChangePostComment = (e) => {
    console.log(e.target.value)
    setCommentState({
      ...commentState,
      postComment: e.target.value,     // 发表评论内容
    })
  }

  // 发表评论按钮点击
  const onClickPostComment = () => {
    // 没登录跳转登录页
    if (!localStorage.getItem('access_token')) {
      goLogin()
      return
    }
    if (!commentState.postComment.trim()) {
      message.error('请正确输入评论信息～')
      return
    }
    if (!(/^[~\s!！@#$￥%^…&*()_—+{}：:‘’“”《》？"|<>?`\-=[\];',.。，；（）、a-zA-Z0-9\u4e00-\u9fa5/]+$/.test(commentState.postComment.trim()))) {
      message.error('请正确输入评论信息～')
      return
    }
    const params = {
      commentsType: 0,                                     // 评论类型 0评论 1回复
      commentsContent: commentState.postComment.trim(),    // 评论/回复内容
    }
    setLoadingPostComment(true)                            // 发表评论loading
    saveCaseComments(params)
  }

  // 点击回复小按钮，展示回复输入框
  const onClickReply = ({commentsSuperId, commentsSuperUserId, commentsSuperUserName, replyCommentId}) => {
    // 没登录跳转登录页
    if (!localStorage.getItem('access_token')) {
      goLogin()
      return
    }
    setCommentState({
      ...commentState,
      commentsSuperId,                 // 评论/回复上级ID
      commentsSuperUserId,             // 回复上级用户ID
      replyInputPlaceholder: `回复@${commentsSuperUserName}...`,       // 回复评论输入框placeholder
      replyCommentId,                  // 回复评论ID
    })
  }

  // 回复评论输入框onChange
  const onChangeReplyComment = (e) => {
    console.log(e.target.value)
    setCommentState({
      ...commentState,
      replyComment: e.target.value,    // 回复评论内容
    })
  }

  // 回复评论按钮点击
  const onClickReplyComment = () => {
    if (!commentState.replyComment.trim()) {
      message.error('请正确输入评论信息～')
      return
    }
    if (!(/^[~\s!！@#$￥%^…&*()_—+{}：:‘’“”《》？"|<>?`\-=[\];',.。，；（）、a-zA-Z0-9\u4e00-\u9fa5/]+$/.test(commentState.replyComment.trim()))) {
      message.error('请正确输入评论信息～')
      return
    }
    const params = {
      commentsType: 1,                                               // 评论类型 0评论 1回复
      commentsContent: commentState.replyComment.trim(),             // 评论/回复内容
      commentsSuperId: commentState.commentsSuperId,                 // 评论/回复上级ID
      commentsSuperUserId: commentState.commentsSuperUserId,         // 回复上级用户ID
    }
    setLoadingReplyComment(true)
    saveCaseComments(params)
  }

  // 发布评论请求接口
  const saveCaseComments = (params) => {
    const UserInfo = JSON.parse(localStorage.getItem('userInfo')) || {}
    const commentsUserId = UserInfo?.friUserId;
    dispatch({
      type: "cases/saveCaseComments",
      payload: {
        excellentCaseId,                         // 病历案列ID
        excellentCaseType: 0,                    // 病历类型 0病历评论
        commentsUserId: commentsUserId,          // 评论/回复用户ID
        ...params,
      }
    }).then((res: any) => {
      setLoadingPostComment(false)
      setLoadingReplyComment(false)
      const { code, msg } = res
      if (code == 200) {
        setCommentState(initialCommentState)
        getMaExcellentCaseCommentsList()
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch(err => {
    })
  }

  // 返回列表页
  const goBack = () => {
    history.go(-1)
  }

  const getExcellentCaseInfo = !!loading.effects['cases/getExcellentCaseInfo'] // loading
  // const loadingGetMaExcellentCaseCommentsList = !!loading.effects['cases/getMaExcellentCaseCommentsList'] // loading
  const { id, topicName, doctorUserList, depSubjectDictNameList, keyWordList, achievementDictName, difficultLevelDict, difficultLevelDictName, age, sex, chiefComplaint, presentDisease, previousHistory, wholeHealth, solution, checkList, diagnosisList, treatmentPlanList, treatmentProcessList, summaryDiscussList, isAllLook, floatImgShowUrl, floatPathUrl } = detailsList || {}; // 病例信息

  return (
    <Spin spinning={getExcellentCaseInfo} wrapperClassName={styles.spin}>
      <div className={styles.container}>
        {/* iframe中隐藏header */}
        {isInIframe ? null : <PcHeader />}
        <div className={styles.content_wrap} id="case_details_content">
          <div className={styles.content_inner}>
            <div className={styles.crumbs}><i onClick={goBack}> 返回</i>全国病例 / <span>病例详情</span></div>
            <div className={styles.headerContent}>
              <div className={styles.headerBox}>
                <span className={styles.headerTitle}>{topicName || ''}</span>
                <div className={styles.lines}></div>
                <span className={styles[`difficult${difficultLevelDict}`]}>难度{difficultLevelDictName}</span>
                <div className={styles.headerLable}>
                  <div className={styles.headerBlueLable}>{depSubjectDictNameList ? depSubjectDictNameList.map((val, i) => {return <span key={i}>{val}</span>}) : ''}</div>
                  {achievementDictName ? <div className={styles.headerYellowLable}>{achievementDictName}</div> : ''}
                </div>
              </div>
              {
                doctorUserList && doctorUserList.length ?
                  <div className={styles.headerDoctor}>主诊：
                    {doctorUserList.map((item, ind) => {return <span key={ind}>{item}{ind == doctorUserList.length-1 ? '' : '、'}</span>})}
                  </div> : null
              }
              {
                keyWordList && keyWordList.length ?
                  <div className={styles.headerKeyWord}>
                    <span className={styles.keyWordText}>病例问题：</span>
                    <div className={styles.keyWordStyle}>
                      {keyWordList.map((item, ind)=>{return <span key={ind}>{item}</span>})}
                    </div>
                  </div> : null
              }
              { solution ?
                <div className={styles.solutionStyle}>
                  <span><img src={treatmentIcon} alt='' /></span>
                  {solution}
                </div> : null
              }
            </div>
            <div className={styles.details_content}>
              <Anchor
                getContainer={() => document.getElementById('case_details_content')}
                offsetTop={0}
                targetOffset={62}
                onClick={handleClick}
              >
                <Link href="#UserInfo" title="基本信息"></Link>
                <Link href="#InspectAndDiagnosis" title="检查与诊断"></Link>
                <Link href="#TreatmentPlanning" title="治疗计划"></Link>
                <Link href="#TreatmentProcess" title='治疗过程'></Link>
                <Link href="#SummaryAndDiscussion" title='总结与讨论'></Link>
              </Anchor>
              <div id="UserInfo" className={styles.box}>
                <div className={styles.firstTitle}>基本信息</div>
                {
                  age || sex ?
                    <div className={styles.contentInfo}>
                      {age ? <div className={styles.leftTitle} style={{marginRight: '100px', width: 'auto', whiteSpace: 'nowrap'}}><span>年龄:</span>{age}</div> : ''}
                      {sex ? <div className={styles.leftTitle} style={{width: 'auto', whiteSpace: 'nowrap'}}><span>性别:</span>{sex && sex == 1 ? '男' : sex == 2 ? '女' : '未知'}</div> : ''}
                    </div> : ''
                }
                {chiefComplaint ? <div className={styles.leftTitle}><span>主诉:</span><div className={styles.rightText}>{chiefComplaint}</div></div> : ''}
                {presentDisease ? <div className={styles.leftTitle}><span>现病史:</span><div className={styles.rightText}>{presentDisease}</div></div> : ''}
                {previousHistory ? <div className={styles.leftTitle}><span>既往史:</span><div className={styles.rightText}>{previousHistory}</div></div> : ''}
                {wholeHealth ? <div className={styles.leftTitle}><span>全身情况:</span><div className={styles.rightText}>{wholeHealth}</div></div> : ''}
              </div>
              <div id="InspectAndDiagnosis" className={styles.box}>
                <div className={styles.firstTitle}>检查及诊断</div>
                <div className={styles.content}>
                  <div className={styles.topContent}>
                    <div className={styles.contentTitle}>检查:</div>
                    <div className={styles.contentLists}>
                      {checkList && checkList.length && checkList.map((item:any, ind:any) => {
                        return <div className={styles.wrapper} key={ind}>
                          {item.titleName ? <div className={styles.secondaryTitle}>{item.titleName}</div> : ''}
                          {item.content ? <div className={styles.imgContentText}>{item.content}</div>: ''}
                          <div className={styles.imgsContent}>
                            {item.imgContent && item.imgContent.map((val:any, i:any) => {
                              return <div className={styles.imgBox} key={i}>
                                <img src={val.imgPathShowUrl} alt='' />
                                {
                                  val.imgName ?
                                    <div className={styles.imgName}>
                                      <img src={imgIcon} className={styles.imgIconStyle} alt=''/>{val.imgName}
                                    </div>
                                    : ''
                                }
                              </div>
                            })}
                          </div>
                        </div>
                      })}
                    </div>
                  </div>
                  <div className={styles.topContent}>
                    <div className={styles.contentTitle}>诊断:</div>
                    <div className={styles.contentLists}>
                      {RenderComponent(diagnosisList)}
                    </div>
                  </div>
                </div>
              </div>
              {RenderList()}
            </div>

            {/* 评论区 */}
            <div className={styles.comment_wrap}>
              <p className={styles.comment_title}>评论</p>
              {/* 发表评论输入框 */}
              <div className={styles.post_comment_input}>
                <Input.TextArea
                  placeholder="发表评论，和大家一起讨论吧..."
                  showCount
                  maxLength={800}
                  autoSize={{minRows: 3, maxRows: 3}}
                  value={commentState.postComment}
                  onChange={onChangePostComment}
                />
              </div>
              <div className={styles.post_comment_btn}>
                <Button type="primary" onClick={onClickPostComment} loading={loadingPostComment}>发表评论</Button>
              </div>

              {/* 评论list */}
              {
                commentDataSource && commentDataSource.length > 0 ?
                  <div className={styles.comment_list}>
                    {
                      commentDataSource.map(item => {
                        return (
                          <div key={item.id} className={styles.comment_item}>
                            <Avatar userInfo={{
                              userId: item.commentsUserId,
                              name: item.commentsUserName,
                              headUrlShow: item.commentsUserHeadUrl,
                            }} size={32}/>
                            <div className={styles.comment_item_right}>
                              <p className={styles.right_user_name}>{item.commentsUserName}</p>
                              {/* 评论内容及回复 */}
                              <div className={styles.right_details_item}>
                                <div className={styles.right_details_item_comment}>
                                  {item.commentsContent}
                                </div>
                                {
                                  commentState.replyCommentId == item.id ?
                                    <span style={{width: 28}}></span>
                                    :
                                    <span
                                      className={styles.right_details_item_btn}
                                      style={{marginTop: 0}}
                                      onClick={() => onClickReply({
                                        commentsSuperId: item.id,
                                        commentsSuperUserId: item.commentsUserId,
                                        commentsSuperUserName: item.commentsUserName,
                                        replyCommentId: item.id,
                                      })}
                                    >回复</span>
                                }

                              </div>
                              {
                                item.excellentCaseCommentsDtos && item.excellentCaseCommentsDtos.length > 0 &&
                                item.excellentCaseCommentsDtos.map(itemChild => {
                                  return (
                                    <div key={itemChild.id} className={styles.right_details_item}>
                                      <div className={styles.right_details_item_reply}>
                                        <span>{itemChild.commentsUserName} </span>回复<span> {itemChild.commentsSuperUserName}:</span>
                                        {itemChild.commentsContent}
                                      </div>

                                      {
                                        commentState.replyCommentId == itemChild.id ?
                                          <span style={{width: 28}}></span>
                                          :
                                          <span
                                            className={styles.right_details_item_btn}
                                            onClick={() => onClickReply({
                                              commentsSuperId: itemChild.commentsSuperId,
                                              commentsSuperUserId: itemChild.commentsUserId,
                                              commentsSuperUserName: itemChild.commentsUserName,
                                              replyCommentId: itemChild.id,
                                            })}
                                          >回复</span>
                                      }
                                    </div>
                                  )
                                })
                              }
                              {/* 回复评论输入框 */}
                              {
                                commentState.commentsSuperId == item.id &&
                                <>
                                  <div className={styles.reply_comment_input}>
                                    <Input.TextArea
                                      placeholder={commentState.replyInputPlaceholder}
                                      showCount
                                      maxLength={800}
                                      autoSize={{minRows: 3, maxRows: 3}}
                                      value={commentState.replyComment}
                                      onChange={onChangeReplyComment}
                                    />
                                  </div>
                                  <div className={styles.reply_comment_btn}>
                                    <Button type="primary" onClick={onClickReplyComment} loading={loadingReplyComment}>回复</Button>
                                  </div>
                                </>
                              }
                            </div>
                          </div>
                        )
                      })
                    }
                  </div>
                  :
                  <NoDataRender text="暂无评论信息" style={{padding: '100px 0', marginTop: 0}}/>
              }

            </div>

            {/* 右侧浮窗 */}
            {
              floatImgShowUrl && floatPathUrl ?
                <div className={styles.right_content}>
                  <div className={styles.banner_window_wrap} style={{ display: isShowBannerWindow ? 'block' : 'none' }}>
                    <img className={styles.banner_window_img} src={floatImgShowUrl} onClick={() => jumpExternalLinks(floatPathUrl)} />
                    <div className={styles.banner_window_icon} onClick={() => { setIsShowBannerWindow(false) }}>
                      <img className={styles.banner_window_close} src={closeIcon} />
                    </div>
                  </div>
                  <div className={styles.show_window_wrap} style={{ display: isShowBannerWindow ? 'none' : 'block' }} onClick={() => setIsShowBannerWindow(true)}>
                    <img className={styles.show_window_icon} src={showIcon} />
                  </div>
                </div> : null
            }
          </div>
        </div>

      </div>
    </Spin>
  )
}
export default connect(({ cases, loading }: any) => ({ cases, loading }))(Index)
