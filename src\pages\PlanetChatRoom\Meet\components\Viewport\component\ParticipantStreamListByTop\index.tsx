import React, {useState,} from 'react';
import {connect} from 'umi';
import styles from './index.less';
import PrimaryStream from '@/pages/PlanetChatRoom/Meet/components/Viewport/component/PrimaryStream';
import {
  getCameralistArr,
  getHandUpRemoteStreamList,
  getHostRemoteStreamConfig,
  getIsModeMatrixCameraRemoteStreamList,
  getShareRemoteStreamConfig,
  getUserCameraRemoteStreamList,
  getUserInfoData,
} from '@/utils/utilsByTRTC';

type propsType = {
  global: any;
};

const Index: React.FC<propsType> = (props) => {
  const {RTC, localStreamConfig, remoteStreamConfigList, PlanetChatRoom, dispatch} = props || {};

  let {
    SpaceInfo,
    handUpList,
    currentLiveUserList,
  } = PlanetChatRoom || {};

  const {
    hostUserInfo,
    name: nameBySpaceInfo,
    status: statusBySpaceInfo,
  } = SpaceInfo || {};

  const userInfoData = getUserInfoData();

  const shareRemoteStreamConfig = getShareRemoteStreamConfig(SpaceInfo, remoteStreamConfigList);

  const hostRemoteStreamConfig = getHostRemoteStreamConfig(
    SpaceInfo,
    hostUserInfo,
    remoteStreamConfigList,
  );

  const userCameraRemoteStreamList = getUserCameraRemoteStreamList(
    SpaceInfo,
    remoteStreamConfigList,
  ); // 远端流中摄像头流 只要又画面或者有声音

  const isModeMatrixCameraRemoteStreamList = getIsModeMatrixCameraRemoteStreamList(
    SpaceInfo,
    hostUserInfo,
    handUpList,
    userCameraRemoteStreamList,
  );

  const handUpRemoteStreamList = getHandUpRemoteStreamList(
    SpaceInfo,
    handUpList,
    userCameraRemoteStreamList,
  );

  let cameralistArr = getCameralistArr(
    hostRemoteStreamConfig,
    localStreamConfig,
    handUpRemoteStreamList,
    isModeMatrixCameraRemoteStreamList,
  );

  currentLiveUserList =
    Array.isArray(currentLiveUserList) &&
    currentLiveUserList.concat(Array.isArray(handUpList) ? handUpList : []);
  const [randomNum, setRandomNum] = useState(Math.random());
  return (
    <>
      <div className={styles.ParticipantStreamListByTopWarp}>
        <div className={styles.StreamWarp}>
          {Array.isArray(cameralistArr) &&
            cameralistArr.map((itemConfig) => {
              if (!itemConfig) {
                return;
              }
              let findBycurrentLiveUser =
                Array.isArray(currentLiveUserList) &&
                currentLiveUserList.find((value) => {
                  return value.imUserId == itemConfig.userID;
                });
              return (
                <div className={styles.StreamItem}>
                  <PrimaryStream
                    randomNum={`${randomNum}_ParticipantStreamListByTop`}
                    RTC={RTC}
                    showStream={itemConfig}
                    streamUser={findBycurrentLiveUser}
                  />
                </div>
              );
            })}
        </div>
      </div>
    </>
  );
};
export default connect(({ global, PlanetChatRoom, loading }: any) => ({
  global,
  PlanetChatRoom,
  loading,
}))(Index);
