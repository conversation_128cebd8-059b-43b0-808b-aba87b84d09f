import React, { useState, useEffect } from 'react';
import { connect, history } from 'umi';
import { Form, Input, Spin, Button, message, Tabs } from 'antd';
import { Toast } from 'antd-mobile';
import styles from './index.less';
import { debounce } from 'lodash';
import loginIcon from '@/assets/GlobalImg/logo.png';
const loginBg =
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/logoBg.png';
import {
  getWechatAuthInfo,
  getWechatRegisterKey,
  wechatLoginUserToken,
} from '@/services/common/api';
import { getMsgCodeUserInfo, getEnterpriseLoginMsgCode } from '@/services/login/login';
import NavBar from '@/components/NavBar'; // 头部返回
import { getOperatingEnv, getCurrentWechatApp } from '@/utils/utils';
import Captcha from '../../../../components/Captcha';

const Index: React.FC = (props: any) => {
  const { dispatch } = props;
  const [form] = Form.useForm();
  const [pageType, setPageType] = useState(); // 1pc 2 移动端
  const [pageLoading, setPageLoading] = useState(true); // 页面加载状态
  const [existAccount, setExistAccount] = useState(false); //已授权且已绑定账户
  const [wechatUserInfo, setWechatUserInfo] = useState({
    vxOpenIdCipherText: null,
    nickName: null,
    headImgUrl: null,
    unionIdCipherText: null,
    code: null,
    userKey: null,
    code_query: null,
  }); // 微信授权回来拿到的信息以及通过url拿到的code
  const [countdown, setCountdown] = useState(0); // 验证码倒计时状态
  const pageKeyByRendom = Math.random().toString(36).substr(2, 8); // pageKey 当前页面唯一标识 用于验证码登录 生成8位随机整数
  const [imgCodeUrl, setImgCodeUrl] = useState(''); // 图片验证码地址
  const [pageKey, setPageKey] = useState(pageKeyByRendom); // 页面pageKey
  const [loading, setLoading] = useState(false); // 添加登录loading
  const [loadingBySendCode, setLoadingBySendCode] = useState(false); // 添加发送验证码loading

  const [binded, setBinded] = useState(false);
  // const [inited, setInited] = useState(false);

  //  页面打开生成pageKey
  useEffect(() => {
    // ① 判定当前页面视口是否小于750 如果小于750则为移动端
    updateType();
    getWechatAuthUserInfo();
  }, []);

  // ① 判定当前页面视口是否小于750 如果小于750则为移动端
  let updateType = () => {
    // let clientWidth = document.documentElement.clientWidth;
    let env = getOperatingEnv(); // 获取当前操作环境 1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用
    let type = env == 4 ? 1 : 2;
    setPageType(type);
  };
  updateType = debounce(updateType, 100);
  window.addEventListener('resize', updateType);

  // 获取微信授权登录用户信息
  const getWechatAuthUserInfo = async () => {
    let { location } = props;
    let { query: query_location } = location || {};
    let { code: code_query } = query_location || {};
    if (!!code_query) {
      const wechatAuthInfo = await getWechatAuthInfo({
        wxAppId: getCurrentWechatApp().appid,
        code: code_query,
        platform: getOperatingEnv() == 2 ? 'wx' : null,
      });
      const { code, content } = wechatAuthInfo || {};
      if (code == 200) {
        if (content.code == 1) {
          setExistAccount(true);
          setWechatUserInfo({
            code_query,
            ...content,
          });
          wechatLoginToken(getCurrentWechatApp().appid, code_query, content.userKey);
        } else {
          // 没账号走注册流程
          setPageLoading(false);
          setWechatUserInfo({
            code_query,
            ...content,
          });
        }
      } else {
        message.warning('微信授权失败!');
        setPageLoading(false);
      }
    } else {
      setPageLoading(false);
      message.warning('微信授权失败!');
    }
  };

  // 发送验证码并开始倒计时
  const sendCode = async () => {
    const phone = form.getFieldValue(`phone`);
    if (!phone) {
      pageType == 1 ? message.error('请输入手机号') : Toast.show({ content: '请输入手机号' });
      return;
    }
    if (!/^1[3456789]\d{9}$/.test(phone)) {
      pageType == 1
        ? message.error('请输入正确手机号')
        : Toast.show({ content: '请输入正确手机号' });
      return;
    }
    window.captchaObj.showCaptcha();
    if (!binded) {
      setBinded(true);
      window.captchaObj.onSuccess(async () => {
        setLoadingBySendCode(true);
        const v = window.captchaObj.getValidate();
        const { pass_token, lot_number, gen_time, captcha_output } = v;
        try {
          // 发送验证码请求
          const res = await getEnterpriseLoginMsgCode({
            phone: phone,
            pageKey: pageKey,
            lotNumber: lot_number,
            passToken: pass_token,
            genTime: gen_time,
            captchaOutput: captcha_output,
          });

          if (res.code === 200 && res.content) {
            setLoadingBySendCode(false); // 关闭发送验证码loading
            pageType == 1
              ? message.success('验证码发送成功')
              : Toast.show({ content: '验证码发送成功' });
            // 开始倒计时 60s
            let time = 60;
            setCountdown(time);
            const timer = setInterval(() => {
              time--;
              setCountdown(time);
              if (time === 0) {
                clearInterval(timer);
              }
            }, 1000);
          } else {
            setLoadingBySendCode(false); // 关闭发送验证码loading
            pageType == 1 ? message.error(res.msg) : Toast.show({ content: res.msg });
          }
        } catch (error) {
          setLoadingBySendCode(false); // 关闭发送验证码loading
          pageType == 1
            ? message.error('验证码发送失败')
            : Toast.show({ content: '验证码发送失败' });
        }
      });
    }
  };

  // 渲染验证码倒计时
  const renderCountdown = () => {
    if (countdown > 0) {
      return `${countdown}`;
    }
    return '获取验证码';
  };

  // form成功登录按钮
  const onFinish = debounce(async (values) => {
    // 开启登录loading
    setLoading(true);
    const {
      phone, // 手机号
      phoneCode, // 验证码
    } = values;
    const { vxOpenIdCipherText, nickName, headImgUrl, unionIdCipherText, code_query } =
      wechatUserInfo; // 微信用户信息
    const params = {
      vxOpenIdCipherText,
      nickName,
      headImgUrl,
      unionIdCipherText,
      phone,
      phoneCode,
    };
    // 注册获取Key
    const res = await getWechatRegisterKey(params);
    // 登录成功
    if (res.code === 200) {
      // 注册成功后获取token
      wechatLoginToken(getCurrentWechatApp().appid, code_query, res.content.userKey);
    } else {
      setLoading(false); // 关闭授权loading
      pageType == 1 ? message.error(res.msg) : Toast.show({ content: res.msg });
    }
  }, 1000);

  // 自动登录获取token
  const wechatLoginToken = async (wxAppId, weChatcode, weChatToUcKey) => {
    const wxWorkAuthToken = await wechatLoginUserToken({
      wxAppId,
      code: weChatcode,
      weChatToUcKey,
    });
    const { code, content } = wxWorkAuthToken || {};
    if (code == 200) {
      setPageLoading(false);
      // 走自动登录逻辑
      wechatLoginUserInfo(content.username, content.access_token);
    } else {
      Toast.show(
        wxWorkAuthToken && wxWorkAuthToken.msg ? wxWorkAuthToken.msg : '微信授权获取token失效',
      );
      return;
    }
  };

  // 自动登录获取用户信息
  const wechatLoginUserInfo = async (mobile, token_text) => {
    let { location } = props;
    let env = getOperatingEnv();
    localStorage.setItem('access_token', token_text);
    localStorage.setItem(
      'userInfo',
      JSON.stringify({
        phone: mobile,
      }),
    );
    try {
      let userInfo = await getMsgCodeUserInfo({ token: token_text, username: mobile });
      if (userInfo?.code === 200) {
        setLoading(false); // 关闭登录loading
        // 保存用户信息
        localStorage.setItem(
          'userInfo',
          JSON.stringify({
            ...userInfo.content,
            id: userInfo?.content?.friUserId,
          }),
        );
        env == 4 ? message.success('登录成功') : Toast.show({ content: '登录成功' });

        // 核心步骤 跳到哪里！
        // history.replace('/Square');
        dispatch({
          type: 'enterpriseQRcode/enterpriseQrBind',
          payload: {
            tenantId: localStorage.getItem('tenantId') || '',
          },
        })
          .then((res) => {
            const { code, content, msg } = res;
            if (code == 200) {
              const { roleType, orgCheck, bindBiz, waitAudit, bizName } = content;
              const access_token = localStorage.getItem('access_token');
              const { phone } = JSON.parse(localStorage.getItem('userInfo') || '');
              const tenantId = localStorage.getItem('tenantId') || '';
              const approvalStatus = localStorage.getItem('approvalStatus') || '';
              const paramsUrl = `?roleType=${roleType}&orgCheck=${orgCheck}&bindBiz=${bindBiz}&waitAudit=${waitAudit}&name=${bizName}&access_token=${access_token}&phone=${phone}&tenantId=${tenantId}`;

              // 微信消息推送 进入到审批页面
              if (approvalStatus && approvalStatus == 'wait') {
                const approvalStatusParams = `?type=${approvalStatus}&tenantId=${tenantId}&access_token=${access_token}`;
                window.location.href =
                window.location.hostname !== 'dhealth-test.friday.tech' &&  window.location.hostname !== 'localhost'
                    ? `https://doctor.friday.tech/enterprise/memberList${approvalStatusParams}`
                    : `https://doctortest.friday.tech/enterprise/memberList${approvalStatusParams}`;
              } else {
                // 微信扫码流程
                if (roleType == 'BIZ_ADMIN') {
                  // 管理员流程
                  if (orgCheck) {
                    // 已认证：去首页
                    history.replace('/Square');
                  } else {
                    // 未认证：去管理员引导页
                    window.location.href = 
                    window.location.hostname !== 'dhealth-test.friday.tech' &&  window.location.hostname !== 'localhost'
                      ? `https://doctor.friday.tech/enterprise/welcome${paramsUrl}`
                      : `https://doctortest.friday.tech/enterprise/welcome${paramsUrl}`;
                  }
                } else {
                  // 普通用户流程

                  if (bindBiz) {
                    // 已认证：去首页
                    history.replace('/Square');
                  } else {
                    if (waitAudit) {
                      // 未认证但是在审核阶段: 去认证最后一步
                       window.location.href = 
                       window.location.hostname !== 'dhealth-test.friday.tech' &&  window.location.hostname !== 'localhost'
                         ? `https://doctor.friday.tech/enterprise/submitted${paramsUrl}`
                        : `https://doctortest.friday.tech/enterprise/submitted${paramsUrl}`;
                    } else {
                      //未认证且未审核：去普通员工引导页
                      window.location.href = 
                      window.location.hostname !== 'dhealth-test.friday.tech' &&  window.location.hostname !== 'localhost'
                        ? `https://doctor.friday.tech/enterprise/welcome${paramsUrl}`
                        : `https://doctortest.friday.tech/enterprise/welcome${paramsUrl}`;
                    }
                  }
                }
              }
            }
          })
          .catch((err) => {});

        // let redirectByPush = null;
        // if (
        //   location?.query?.state &&
        //   /redirectByPush=(.*)/.exec(location?.query?.state) &&
        //   /redirectByPush=(.*)/.exec(location?.query?.state)[1]
        // ) {
        //   redirectByPush = /redirectByPush=(.*)/.exec(location?.query?.state)[1];
        // }
        // if (!redirectByPush) {
        //   redirectByPush =
        //     location?.hash?.state &&
        //     /redirectByPush=(.*)/.exec(location?.hash) &&
        //     /redirectByPush=(.*)/.exec(location?.hash)[1];
        // }

        // // 跳转到原页面
        // if (!!redirectByPush) {
        //   window.location.replace(redirectByPush);
        // } else {
        //   // PC端
        //   if (env == 4) {
        //     history.replace('/home');
        //   } else {
        //     history.replace('/Square');
        //   }
        // }
      } else {
        setLoading(false); // 关闭登录loading
        message.error('获取用户信息失败：', userInfo?.msg);
      }
    } catch (error) {
      setLoading(false); // 关闭登录loading
      message.error('获取用户信息失败：', error);
    }
  };

  return pageType ? (
    <div className={pageType == 1 ? styles.pc_login_wrap : styles.login_wrap}>
      <div className={styles.login_bg_wrap}>
        <NavBar title={'授权成功'} className={styles.loginNavBar}></NavBar>
        <div className={styles.login_bg_box}>
          <img src={loginBg} alt="" />
        </div>
        <div className={styles.login_content_title}>
          <div className={styles.login_img}>
            <img src={loginIcon} alt="" />
          </div>
          <div className={styles.login_text}>欢迎登录FRIDAY数智化平台</div>
        </div>
        {existAccount ? (
          <div className={styles.login_exist_account}>微信授权成功，正在登录中，请稍后...</div>
        ) : !pageLoading && !existAccount ? (
          <Spin spinning={!!loading}>
            <div className={styles.login_form_wrap}>
              <Form form={form} onFinish={onFinish}>
                <div>
                  <div className={styles.login_form_input}>
                    <Form.Item
                      label=""
                      name="phone"
                      rules={[
                        { required: true, message: '请输入手机号' },
                        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' },
                      ]}
                    >
                      <Input bordered={false} autoComplete="off" placeholder="请输入手机号" />
                    </Form.Item>
                  </div>
                  <div className={styles.login_form_input2}>
                    <Form.Item
                      label=""
                      name="phoneCode"
                      className={styles.phone_code}
                      rules={[{ required: true, message: '请输入验证码' }]}
                    >
                      <Input
                        bordered={false}
                        autoComplete="off"
                        maxLength={6}
                        placeholder="请输入验证码"
                      />
                    </Form.Item>
                    <Captcha />
                    <Spin spinning={!!loadingBySendCode}>
                      <div
                        className={styles.sendCode}
                        onClick={() => {
                          if (countdown > 0) {
                            return;
                          }
                          sendCode();
                        }}
                      >
                        {renderCountdown()}
                      </div>
                    </Spin>
                  </div>
                </div>
                <Button className={styles.login_Btn} htmlType="submit" loading={loading}>
                  验证账号并登录
                </Button>
              </Form>
            </div>
          </Spin>
        ) : null}
      </div>
    </div>
  ) : null;
};
export default connect(({ activity, loading, enterpriseQRcode }: any) => ({
  activity,
  loading,
  enterpriseQRcode,
}))(Index);
