import React, { useState,useEffect } from 'react';
import { history,connect } from 'umi';
import styles from "./index.less";
import {getOperatingEnv, goConsultationDetail} from "@/utils/utils";
import NavBar from "@/components/NavBar";
import {
  getConsultationOrderInfo,
  submitConsultationPictureOrderPay as submitConsultationPictureOrderPayAction,
  submitConsultationVideoOrderPay as submitConsultationVideoOrderPayAction,
} from "@/services/consultation/ConsultationList";
import classNames from "classnames";

const CompleteOrder: React.FC = (props) => {
  const { dispatch,location } = props;
  let { match: { params: { id } } } = props
  let { query:query_location } = location || {}
  let { env: env_query} = query_location || {}
  const [ consultationOrderInfo,setConsultationOrderInfo ] =  useState(null);
  const {
    type,         // : 1,//指导类型(1图文、2视频)
    processNode,  // : 2,//流程节点(图文流程节点[1选择指导方式、2描述病例问题、3支付指导费用、4病例资料被查看、5问题被回复并对话、6结束指导交易成功];  视频流程节点[1选择指导方式、2描述病例问题、3提交指导单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、  7结束指导、8确认并支付指导费用、9交易成功])
    status, // : 1,//支付状态(0已取消、1未支付(待评估)、2去支付(已评估)、3已支付)
  } = consultationOrderInfo || {};

  // 微信浏览器使用
  let OperatingEnv = getOperatingEnv();

  useEffect(() => {
    let { match: { params: { orderId } },location } = props
    let { query:query_location } = location || {}
    let { code:code_query, env: env_query} = query_location || {}
    if(!!env_query && OperatingEnv != 5 && OperatingEnv != 6) {
      if (env_query == 5) {
        setTimeout(()=>{
          window.location.href = 'friday://'
        },1000)
      } else if (env_query == 6) {
        setTimeout(()=>{
          window.location.href = 'jwsmed://'
        },1000)
      }
    }else {
      // 查询订单详情
      getConsultationOrderInfoByPage();
    }
  },[])

  // 查询指导订单详情
  const getConsultationOrderInfoByPage = async () => {
    let getConsultationOrderInfoByData = await getConsultationOrderInfo({
      consultationId:id, // [string] 是 指导订单ID
      type:'2'  //  // [string] 是 (1:运营端订单详情, 2:H5/WEB端视频订单详情)
    })

    const {
      code,
      content,
    } = getConsultationOrderInfoByData || {};
    if(code == 200) {
      setConsultationOrderInfo(content);
    }else {
      setConsultationOrderInfo(null);
    }
  }
  return (
    <div className={styles.Mobile_Wrap}>
      {OperatingEnv != 1 &&  // 当前是小程序端
        <div className={styles.Mobile_title_statusbar}></div>
      }

      {OperatingEnv != 1 && OperatingEnv != 2 &&  // 当前是小程序端
        <div className={styles.Mobile_title_Wrap}>
          <NavBar title={'支付结果'}></NavBar>
        </div>
      }
      {(!!env_query && OperatingEnv != 5 && OperatingEnv != 6) ?
        <div className={styles.Warp_box}>
          <div className={styles.box_payment}>
            返回App,查看支付结果
          </div>
          <div className={styles.Warp_btn}>
            <div
              onClick={()=>{
                if (env_query == 5) {
                  window.location.href = 'friday://'
                  // setTimeout(()=>{
                  //   window.location.href = 'friday://'
                  // },1000)
                } else if (env_query == 6) {
                  window.location.href = 'jwsmed://'
                  // setTimeout(()=>{
                  //   window.location.href = 'jwsmed://'
                  // },1000)
                }
              }}
              className={styles.goBack_btn}>返回App</div>
          </div>
        </div>
        :
        <div className={styles.Warp_box}>
          {!!status &&
            <div className={styles.box_payment}>
              <div className={classNames({
                [styles.Payment_result_icon_Success]:status == 3,
                [styles.Payment_result_icon_fail]:status != 3,
              })}></div>
              <div className={styles.payment_title}>{status == 3 ? '支付成功' : '支付失败'}</div>
            </div>
          }
          <div className={styles.Warp_btn}>
            <div
              onClick={()=>{

                // 返回订单列表
                history.replace('/PaymentByConsultation/MyConsultationList?tabType=1')
              }}
              className={styles.goBack_btn}>返回订单</div>
            <div
              onClick={()=>{
                // 判定当前订单状态是否已支付如果已支付 跳转到指导详情
                // 如果未支付则返回订单列表
                goConsultationDetail({
                  isReplace:true,
                  ...consultationOrderInfo
                })
              }}
              className={styles.ViewDetails_btn}>查看指导详情</div>
          </div>
        </div>
      }
    </div>
  )
}
export default connect(({ login, loading }: any) => ({
  login, loading
}))(CompleteOrder)
