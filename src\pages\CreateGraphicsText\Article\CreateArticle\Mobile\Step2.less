.spin {
  height: 100%;
  :global {
    .ant-spin-container {
      height: 100%;
    }
  }
}
.nav_bar_right {
  display: flex;
  align-items: center;
  .nar_bar_link_btn {
    font-size: 15px;
    color: #0095FF;
    margin-right: 16px;
  }
  .nar_bar_btn {
    display: block;
    width: 54px;
    height: 29px;
    background: #0095FF;
    font-size: 15px;
    color: #fff;
    line-height: 30px;
    border-radius: 15px;
    text-align: center;
    &.disabled {
      background: #8DCDF7;
      color: #E8F4FC;
    }
  }
}

.container {
  padding-top: 44px;
}

.bottom_tips_bar {
  position: relative;
  z-index: 991;
  width: 100%;
  background: #FFF7DA;
  border-top: 1px solid #EFD989;
  border-bottom: 1px solid #EFD989;
  padding: 7px 28px 6px 10px;
  line-height: 18px;
  word-break: break-all;
  font-size: 12px;
  margin-top: -1px;
  color: #8C772B;
  .bar_btn_wrap {
    position: absolute;
    width: 12px;
    height: 12px;
    right: 8px;
    top: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 9px;
  }
}

.select_kingdom_box {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  padding: 17px 12px;
  border-bottom: 1px solid #f5f6f8;
  .select_left {
    display: flex;
    flex-wrap: nowrap;
    flex-shrink: 0;
    overflow: hidden;
    align-items: center;
    column-gap: 4px;
    font-size: 14px;
    color: #000;
    & > i {
      width: 16px;
      height: 16px;
      &.select_left_icon_1 {
        background: url("../../../../../assets/GlobalImg/associated.png") no-repeat center;
        background-size: 100% 100%;
      }
      &.select_left_icon_2 {
        background: url("../../../../../assets/GlobalImg/black_arrow.png") no-repeat center;
        background-size: 100% 100%;
      }
    }
    & > span {
      height: 20px;
      line-height: 21px;
    }
    .divider {
      margin-left: 4px;
      width: 0;
      height: 16px;
      border-left: 1px solid #d9d9d9;
    }
  }
  .select_right {
    margin-left: 8px;
    background: #F5F6F8;
    font-size: 13px;
    color: #000;
    line-height: 18px;
    padding: 2px 12px;
    border-radius: 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .select_right_default {
    font-size: 12px;
    color: #999;
    margin-left: 8px;
    height: 17px;
    line-height: 19px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.select_image_number_box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 54px;
  padding: 0 12px;
  border-bottom: 1px solid #f5f6f8;
  margin-bottom: 12px;
  :global {
    .adm-radio-content {
      font-size: 15px;
      color: #000;
    }
    .adm-radio + .adm-radio {
      margin-left: 16px;
    }
  }
  .left {
    font-size: 15px;
    color: #666;
  }
}

.preview_title {
  font-size: 12px;
  color: #666;
  line-height: 17px;
  padding: 0 12px;
  margin-bottom: 4px;
}

// 单图样式
.article_horizontal_box {
  background: #fff;
  margin-bottom: 24px;
  padding: 0 12px;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  .article_left {
    flex: 1;
    min-height: 86px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    .article_title {
      font-size: 15px;
      color: #222;
      font-weight: 500;
      line-height: 21px;
      word-break: break-all;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2; /* 指定显示2行 */
      overflow: hidden;
      padding-right: 5px;
    }
    .kingdom_box {
      display: inline-flex;
      width: auto;
      align-items: center;
      border-radius: 4px;
      background: #E4F0FC;
      padding: 0 4px;
      & > i {
        width: 14px;
        height: 14px;
        background: url("../../../../../assets/GlobalImg/blue_associated.png") no-repeat center;
        background-size: 100% 100%;
        margin-right: 4px;
      }
      & > span {
        font-size: 11px;
        color: #0095FF;
        height: 15px;
        line-height: 16px;
      }
    }
    .article_user {
      display: flex;
      align-items: center;
      .avatar {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        margin-right: 4px;
        line-height: 16px;
        color: #fff;
        font-size: 8px;
        text-align: center;
        white-space: nowrap;
      }
      .name {
        font-size: 12px;
        color: #000;
        line-height: 17px;
      }
      .icon {
        width: 14px;
        height: 14px;
        background: url("../../../../../assets/GlobalImg/gdp.png") no-repeat center;
        background-size: 100% 100%;
        margin-right: 4px;
      }
      & > span {
        margin-right: 8px;
        font-size: 12px;
        color: #666;
        line-height: 17px;
      }
    }
  }
  .article_right {
    position: relative;
    flex-shrink: 0;
    width: 122px;
    height: 86px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    border-radius: 4px;
    .right_btn_box {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 23px;
      background: rgba(0,0,0,0.5);
      border-radius: 0 0 4px 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      column-gap: 24px;
      font-size: 11px;
      color: #fff;
    }
  }
  .article_right_upload {
    flex-shrink: 0;
    width: 122px;
    height: 86px;
    background: #F8F8F8;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    :global {
      .anticon {
        font-size: 24px;
        color: #CBCBCB;
        margin-bottom: 4px;
      }
    }
    & > span {
      font-size: 13px;
      color: #999;
      line-height: 18px;
    }
  }
}

// 3图样式
.article_vertical_box {
  background: #fff;
  margin-bottom: 24px;
  padding: 0 12px;
  .article_title {
    font-size: 15px;
    color: #222;
    font-weight: 500;
    line-height: 21px;
    word-break: break-all;
    margin-bottom: 8px;
  }
  .cover_img_box {
    display: flex;
    flex-wrap: nowrap;
    column-gap: 4px;
    margin-bottom: 8px;
    .cover_img_item {
      position: relative;
      flex: 1;
      max-width: 33.3333%;
      height: 80px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      overflow: hidden;
      &:first-child {
        border-radius: 4px 0 0 4px;
      }
      &:last-child {
        border-radius: 0 4px 4px 0;
      }
      .cover_img_btn_box {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 23px;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        column-gap: 24px;
        font-size: 11px;
        color: #fff;
      }
    }
    .cover_img_upload {
      flex: 1;
      max-width: 33.3333%;
      height: 80px;
      background: #F8F8F8;
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      :global {
        .anticon {
          font-size: 24px;
          color: #CBCBCB;
          margin-bottom: 4px;
        }
      }
      & > span {
        font-size: 13px;
        color: #999;
        line-height: 18px;
      }
    }
  }
  .kingdom_box {
    display: inline-flex;
    width: auto;
    align-items: center;
    border-radius: 4px;
    background: #E4F0FC;
    padding: 0 4px;
    margin-bottom: 8px;
    & > i {
      width: 14px;
      height: 14px;
      background: url("../../../../../assets/GlobalImg/blue_associated.png") no-repeat center;
      background-size: 100% 100%;
      margin-right: 4px;
    }
    & > span {
      font-size: 11px;
      color: #0095FF;
      height: 15px;
      line-height: 16px;
    }
  }
  .article_user {
    display: flex;
    align-items: center;
    .avatar {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      margin-right: 4px;
      line-height: 16px;
      color: #fff;
      font-size: 8px;
      text-align: center;
      white-space: nowrap;
    }
    .name {
      font-size: 12px;
      color: #000;
      line-height: 17px;
    }
    .icon {
      width: 14px;
      height: 14px;
      background: url("../../../../../assets/GlobalImg/gdp.png") no-repeat center;
      background-size: 100% 100%;
      margin-right: 4px;
    }
    & > span {
      margin-right: 8px;
      font-size: 12px;
      color: #666;
      line-height: 17px;
    }
  }
}
