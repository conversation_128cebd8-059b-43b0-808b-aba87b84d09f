/**
 * @Description: 专家详情取消关注提示弹窗
 * @author: 赵斐
 */
import React from 'react'
import { Modal } from 'antd-mobile'
import styles from './index.less'
import WranningIcon from '@/assets/GlobalImg/wranning.png'

const Index: React.FC = (props: any) => {
  const { 
    visible,    // 提示弹窗状态 
    cancelFn,   // 关闭弹窗并取消关注
    noCancelFn  // 关闭弹窗
  } = props;
  return (
    <Modal
      visible={visible}
      content={
        <div className={styles.container}>
          <div className={styles.title_box}>
            <img src={WranningIcon} width={20} height={20} alt=""/>
            <div className={styles.title}>取消关注</div>
          </div>
          <div className={styles.message}>取消关注后将无法接收该用户动态(王国、空间的更新等)</div>
          <div className={styles.btn_box}>
            <div className={styles.cancel} onClick={()=>{cancelFn()}}>狠心取消</div>
            <div className={styles.ok} onClick={()=>noCancelFn()}>我再想想</div>
          </div>
        </div>
      }
    >
    </Modal>
  )
}

export default Index
