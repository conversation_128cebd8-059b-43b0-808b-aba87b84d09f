/**
 * @Description: 会议信息
 */
import React, { useEffect, useState } from 'react'
import { history } from 'umi';
import { Toast } from 'antd-mobile';
import styles from './index.less'
import classNames from "classnames";
import {getDAesString} from "@/utils/utils";

interface PropsType {
  item?: any; // 会议信息对象
  cardBorderType: any; // "rounded":圆角卡片 / "roundedTop":上半部分圆角卡片 / "none":无圆角卡片
  style?: any; // 自定义样式
  myHomeSpaceFilter?: any;
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  let {
    item,
    style,
    cardBorderType='none',
    myHomeSpaceFilter={},
  } = props;

  const {
    appointmentStartDateDescs, // : "明天"
    appointmentStartMaxTime, //: "06:30"
    appointmentStartMinTime, //: "06:00"
    appointmentStartNoYearDate, // : "3月28日"
    appointmentStartWeek, //: "周四"
    guestDataList, //: [{…}]
    hostName, //: "李老师"
    intro, //: "创建会议3月29日创建会议3月29日创建会议3月29日创建会议3月29日"
    isBiz, //: 0  isBiz: // 会议类型 1:企业空间 2:非企业空间
    name, //: "创建会议3月29日"
    originalName, //: 原始名
    password, //: "8ffccea925a22946977428aa35a7310e"
    spaceCoverUrlShow, //: "https://s1-test.5i5ya.com/994220c71617a7d8bd339488b8608d85/6603ba30/dmp/spaceCover/CreateSpace_cover_1.png"
    status, //: 2  状态：1直播中、2预约中、3弹幕轰炸中
    isDisable,  // 是否已下架 1是下架 0否
  } = item || {};

  let vodByPassword = password ? getDAesString(password,'arrail-dentail&2', 'arrail-dentail&3') : null;
  useEffect(()=>{

  },[]);

  // 去会议详情页
  const goToMeetingDetail = () => {
    if (isDisable == 1) {
      Toast.show({content: '该会议已下架~!'});
      return;
    }
    history.push(`/PlanetChatRoom/Meet/${item.id}`);
    sessionStorage.setItem('myHomeSpaceFilter', JSON.stringify(myHomeSpaceFilter));
  }

  return (
    <div style={style ? {...style} : {}}
         className={classNames({
           [styles.MeetingCardInfo_item]:true,
           [styles.MeetingCardInfo_rounded]:cardBorderType == 'rounded',
           [styles.MeetingCardInfo_roundedTop]:cardBorderType == 'roundedTop',
           [styles.MeetingCardInfo_none]:cardBorderType == 'none',
         })}
         onClick={goToMeetingDetail}
    >
      <div className={styles.MeetingCardInfo_title}>
        <div className={styles.title}  dangerouslySetInnerHTML={{__html:name}}></div>

        <div className={classNames({
          [styles.status]:true,
          [styles.toBegin]: status == 1,
          [styles.progress]: status == 2,
          [styles.Ended]: status == 3,
        })}>
          {status == 1 && '进行中'}
          {status == 2 && '待开始'}
          {status == 3 && '已结束'}
        </div>
      </div>

      <div className={styles.MeetingCardInfo_Content}>
        <div className={styles.left_img}>
          <img src={spaceCoverUrlShow} alt={''}/>
          {isBiz == 1 &&
            <div className={styles.img_enterprise}>
              企业
            </div>
          }

          {/* 封面中的标题 */}
          {
            item.isTemplateCover==1 &&
            <div className={styles.title_in_cover_image}>{originalName || name}</div>
          }
        </div>

        <div className={styles.right_content}>
          <div className={styles.date_item_box}>
            <div className={styles.date_item}>{appointmentStartNoYearDate}</div>
            <div className={styles.date_item}>{appointmentStartWeek}</div>
            <div className={styles.date_item}>{appointmentStartMinTime}-{appointmentStartMaxTime}</div>
          </div>
          <div className={styles.content_item}>
            <div className={styles.title}>发起人：</div>
            <div className={styles.content} dangerouslySetInnerHTML={{__html:hostName}}></div>
          </div>
          {Array.isArray(guestDataList) && guestDataList.length > 0 &&
            <div className={styles.content_item}>
              <div className={styles.title}>参会人：</div>
              <div className={styles.content}>{
                guestDataList.map((item:any,index:number) => (
                  <span key={index}> {item.userName} </span>
                ))
              }</div>
            </div>
          }
          {intro &&
            <div className={styles.content_item}>
              <div className={styles.title}>会议描述：</div>
              <div className={styles.content}>{intro}</div>
            </div>
          }
          {password &&
            <div className={styles.content_item}>
              <div className={styles.title}>会议密码：</div>
              <div className={styles.content}>{vodByPassword}</div>
            </div>
          }
        </div>
      </div>

    </div>
  )
}

export default Index
