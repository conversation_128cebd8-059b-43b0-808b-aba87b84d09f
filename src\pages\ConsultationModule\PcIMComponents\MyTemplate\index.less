.wrap {
  margin-top: 16px;
  padding: 0 16px 0 40px;
  display: flex;

  .avatar {
    width: 31px;
    height: 31px;
    margin-left: 8px;

    .avatar_pic {
      display: inline-block;
      width: 31px;
      height: 31px;
      background: #eee;
      width: 100%;
      height: auto;
      border-radius: 50%;
      aspect-ratio: 1;
      border: 1px solid #FFFFFF;
      overflow: hidden;
    }

    .no_avatar_pic {
      width: 31px;
      height: 31px;
      border-radius: 50%;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 500;
      color: #fff;
      border: 1px solid #FFFFFF;
    }
  }

  // 文本样式
  .characters_content {
    flex: 1;
    text-align: right;

    .characters {
      background: #0095FF;
      border-radius: 16px 4px 16px 16px;
      padding: 12px;
      display: inline-block;
      max-width: 280px;
      position: relative;
    }

    .desc {
      font-size: 13px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 15px;
      margin-bottom: 4px;
      word-break: break-all;
      text-align: left;
      white-space: pre-wrap;
    }

    .time {
      font-size: 11px;
      font-weight: 400;
      color: #FFFFFF;
      margin-bottom: 0;
      text-align: right;
    }

  }

  // 语音样式
  .voice_content {
    flex: 1;
    text-align: right;
    position: relative;
    .message_audio{
      position: absolute;
      width: 20px;
      z-index: -99;
    }
    .voice {
      background: #0095FF;
      border-radius: 16px 4px 16px 16px;
      padding: 9px 12px;
      display: inline-block;
    }

    .desc {
      margin-bottom: 4px;
      cursor: pointer;
      .second {
        font-size: 14px;
        font-weight: 400;
        color: #FFFFFF;
        vertical-align: middle;
      }

      .second_icon {
        width: 20px;
        height: 20px;
      }
    }

    .time {
      font-size: 11px;
      font-weight: 400;
      color: #FFFFFF;
      margin-bottom: 0;
      text-align: right;
    }
  }

  // 图片
  .picture_content {
    flex: 1;
    text-align: right;
    
    .picture {
      position: relative;
      display: inline-block;
      border-radius: 6px;
      width: 120px;
      min-width: 120px;
      height: 120px;
      overflow: hidden;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      cursor: pointer;
    }
  }

  // 视频
  .video_content {
    flex: 1;
    text-align: right;

    .video {
      position: relative;
      display: inline-block;
      border-radius: 6px;
      width: 120px;
      min-width: 120px;
      height: 120px;
      overflow: hidden;
      position: relative;

      .thumb_url {
        width: 100%;
        height: 100%;
      }

      .play_icon {
        cursor: pointer;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        display: inline-block;
        width: 40px;
        height: 40px;

        .pay_circle {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .characters_loading{
    position: absolute;
    left: -25px;
    top: 50%;
    transform: translateY(-25%);
    width: 25px;
    height: 25px;
  }
  .video_mask,.picture_mask{
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
  }
  .video_loading,.picture_loading{
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
    width: 60px;
    height: 60px;
  }
  .video_rate,.picture_rate{
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
    font-size: 12px;
    color: #FFFFFF;
  }
}