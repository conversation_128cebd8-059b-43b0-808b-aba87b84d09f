/**
 * @Description: 移动端聊天非本人展示模板
 * @author: 赵斐
 */
import React, { useRef } from 'react';
import { randomColor, processNames } from '@/utils/utils'
import playCircleIcon from '@/assets/Consultation/Pc/play_circle_icon.png'
import voiceLeftIcon from '@/assets/Consultation/H5/voice_left_icon.png'
import voiceLeftGif from '@/assets/Consultation/H5/voice_left_icon.gif'
import { parseText } from '@/utils/im-index'
import styles from './index.less'

interface PropsType {
  data: any,                    // 数据
  onClickOpenVideoFun: (val:string,id:number) => void,  // 点击视频播放
  playAudioFun: (k:number,v:any) => void,  // 点击语音播放
  onClickAmplifyImg: (url:string,visible:any) => void,  // 点击图片放大
  playAudioObj:any,    // 播放ID
}
const Index: React.FC<PropsType> = (props: PropsType) => {
  const audioRef = useRef<HTMLAudioElement>(null);

  const { data, onClickOpenVideoFun ,playAudioFun ,playAudioObj ,onClickAmplifyImg} = props;
  const {
    audioId    // 当前播放语音ID
  } = playAudioObj || {}
  const {
    wxUserId,       // 用户ID
    name,           // 用户名
    headUrlShow,    // 用户头像
    msgSeq,         // 消息序列号，用于标识唯一消息，值越小发送的越早
    msgDataTime,    // 消息时间
    msgType,        // 消息类型 1文本 2图片 3语音 4视频 5自定义
    msgContent,     // 消息内容/图片地址/语音地址/视频地址/
    mediaDuration,  // 媒体文件时长，语音有值
    thumbUrlShow,   // 缩略图url地址，用于展示图片和视频的缩略图片
  } = data || {}
  return (
    <div className={styles.wrap}>
      <div className={styles.avatar}>
        {
          headUrlShow ? <img className={styles.avatar_pic} src={headUrlShow} alt='头' /> :
            <div className={styles.no_avatar_pic} style={{ background: randomColor(wxUserId) }}>{processNames(name)}</div>
        }
      </div>
      {
        msgType == 1 ? <div className={styles.characters_content}>
          <p className={styles.desc} dangerouslySetInnerHTML={{__html: parseText(msgContent)}}></p>
          <p className={styles.time}>{msgDataTime}</p>
        </div> : null
      }
      {
        msgType == 3 ? <div className={styles.voice_content}>
          <div className={styles.voice} style={{ width: `${150 + mediaDuration * 2}px` }}>
            <p className={styles.desc} onClick={() => { playAudioFun(msgSeq ,audioRef) }}>
              <img className={styles.second_icon} src={msgSeq == audioId ? voiceLeftGif : voiceLeftIcon} alt="语音" />
              <span className={styles.second}>{mediaDuration}"</span>
            </p>
            <p className={styles.time}>{msgDataTime}</p>
          </div>
          <audio src={msgContent} ref={audioRef} controls className={styles.message_audio}></audio>
        </div> : null
      }
      {
        msgType == 2 ? <div className={styles.picture_content}>
          <div className={styles.picture} onClick={()=>{onClickAmplifyImg(msgContent,true)}} style={{ backgroundImage: `url(${msgContent})` }}></div>
        </div> : null
      }
      {
        msgType == 4 ? <div className={styles.video_content}>
          <div className={styles.video}>
            <img src={thumbUrlShow} alt="icon" />
            <span onClick={() => { onClickOpenVideoFun(msgContent,msgSeq) }} className={styles.play_icon}><img src={playCircleIcon} alt="icon" /></span>
          </div>
        </div> : null
      }
    </div>
  )
}
export default Index
