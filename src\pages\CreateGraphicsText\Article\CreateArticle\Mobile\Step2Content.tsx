import React, { useEffect, useState } from 'react'
import { connect, history, useAliveController } from 'umi'
import classNames from 'classnames'
import { cloneDeep } from 'lodash'
import dayjs from 'dayjs'
import { processNames, randomColor, useThrottle } from '@/utils/utils'
import { Spin } from 'antd'
import { Radio, Toast } from 'antd-mobile'
import { PlusOutlined, CloseOutlined } from '@ant-design/icons'
import styles from './Step2.less'

import NavBar from '@/components/NavBar'
import UploadImageModal from '@/pages/CreateGraphicsText/ComponentsH5/UploadImageModal'
import CropModal from '@/pages/CreateGraphicsText/ComponentsH5/CropModal'

const coverImageListEmpty = [
  { isEmpty: true, },
  { isEmpty: true, },
  { isEmpty: true, },
]

const Index: React.FC = (props: any) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
  const { id } = history.location.query
  const { loading, dispatch, graphicsText } = props
  const {
    selectedKingdomId,
    selectedKingdomName,
  } = graphicsText

  const initialModalState = {
    cropVisible: false,
    cropImageIndex: null,              // index
    cropImageUrlShow: null,            // 图片地址

    uploadImageVisible: false,
    uploadImageModalType: 1,           // 类型
    uploadImageReplaceIndex: 0,        // 图片索引
    uploadImageMaxNumber: 0,           // 上传数量
    articleTextImgList: [],            // 图片集合

    bottomTipsBarVisible: true,
  }

  const [detailsState, setDetailsState] = useState({})
  const [coverImageNumber, setCoverImageNumber] = useState(1)        // 图片数量
  const [modalState, setModalState] = useState(initialModalState)
  const [isHaveKingdomData, setIsHaveKingdomData] = useState(true)
  const { clear } = useAliveController()

  useEffect(() => {
    console.log('useEffect')
    imgTextInfoUpdateId()
    getCreateAndJoinKingdomList()
  }, [])

  // 编辑图文
  const imgTextInfoUpdateId = () => {
    dispatch({
      type: 'graphicsText/imgTextInfoUpdateId',
      payload: {
        imageTextId: id,                         // 图文主ID
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200 && content) {
        if (content.createUserId != UserInfo.friUserId) {
          Toast.show('您没有编辑权限')
          setTimeout(() => {
            goBack()
          }, 1000)
          return
        }
        // 保存数据
        setDetailsState(content)
        if (content.textImgList && content.textImgList.length > 1) {
          setCoverImageNumber(3)
        }
        dispatch({
          type: 'graphicsText/save',
          payload: {
            selectedKingdomId: content.kingdomId,
            selectedKingdomName: content.kingdomName,
          }
        })
      } else {
        Toast.show(msg || '数据加载失败')
      }
    }).catch(err => {})
  }

  // 获取创建王国
  const getCreateAndJoinKingdomList = () => {
    dispatch({
      type: 'userInfoStore/getCreateAndJoinKingdomList',
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200 && content) {
        if (content['1'] || content['2']) {
          setIsHaveKingdomData(true)
        } else {
          setIsHaveKingdomData(false)
        }
      } else {
        setIsHaveKingdomData(false)
      }
    }).catch(err => {
      setIsHaveKingdomData(false)
    })
  }

  const selectKingdom = () => {
    if (isHaveKingdomData) {
      history.push('/CreateGraphicsText/SelectKingdom')
    }
  }

  const onChangeCoverImageNumber = (value) => {
    setCoverImageNumber(value)

    if (value == 1 && detailsState.textImgList && detailsState.textImgList.length > 1) {
      let textImgListClone = cloneDeep(detailsState.textImgList)
      textImgListClone = textImgListClone.splice(0, 1)
      setDetailsState({
        ...detailsState,
        textImgList: textImgListClone,
      })
    }
  }

  // 上传图片
  const uploadImageModalShow = (uploadImageModalType, uploadImageReplaceIndex = 0) => {
    let quillContents = null
    if (detailsState.contentJson) {
      quillContents = JSON.parse(detailsState.contentJson)
    }
    const articleTextImgList = []
    if (quillContents && quillContents.ops) {
      quillContents.ops.forEach(item => {
        if (typeof item.insert == 'object' && item.insert.image) {
          articleTextImgList.push(item.insert.image.src)
        }
      })
    }

    const length = detailsState.textImgList ? detailsState.textImgList.length : 0
    setModalState({
      ...modalState,
      uploadImageVisible: true,
      uploadImageModalType,
      uploadImageReplaceIndex,
      uploadImageMaxNumber: uploadImageModalType == 1 ? coverImageNumber - length : 1,
      articleTextImgList,
    })
  }

  const uploadImageModalHide = () => {
    setModalState({
      ...modalState,
      uploadImageVisible: false,       // 上传图片弹窗是否显示
      uploadImageModalType: 1,         // 类型，1 上传，2 替换
      uploadImageReplaceIndex: 0,      // 替换图片时的索引
      uploadImageMaxNumber: 0,         // 图片最大上传数量
      articleTextImgList: [],          // 文中图文集合
    })
  }

  const handleUploadImage = (uploadTextImgList) => {
    let textImgListClone = cloneDeep(detailsState.textImgList || [])
    if (modalState.uploadImageModalType == 1) {
      uploadTextImgList.forEach(item => {
        textImgListClone.push({
          imageUrlShow: item,
          isCover: 1,
        })
      })
    } else {
      textImgListClone.splice(modalState.uploadImageReplaceIndex, 1, {
        imageUrlShow: uploadTextImgList[0],
        isCover: 1,
      })
    }

    setDetailsState({
      ...detailsState,
      textImgList: textImgListClone,
    })
    uploadImageModalHide()
  }

  const cropModalShow = (cropImageIndex, cropImageUrlShow) => {
    setModalState({
      ...modalState,
      cropVisible: true,
      cropImageIndex,
      cropImageUrlShow,
    })
  }

  const cropModalHide = () => {
    setModalState({
      ...modalState,
      cropVisible: false,
      cropImageIndex: null,
      cropImageUrlShow: null,
    })
  }

  const handleCroppingImage = (imageUrlShow) => {
    let textImgListClone = cloneDeep(detailsState.textImgList || [])
    textImgListClone.splice(modalState.cropImageIndex, 1, {
      imageUrlShow,
      isCover: 1,
    })
    setDetailsState({
      ...detailsState,
      textImgList: textImgListClone
    })
    cropModalHide()
  }

  let submitDraft = () => {
    console.log('存草稿')
    submit(1)
  }
  submitDraft = useThrottle(submitDraft, 500)

  let beforeSubmit = (isDisabled) => {
    submit(2)
  }
  beforeSubmit = useThrottle(beforeSubmit, 500)

  const submit = async (saveType) => {
    if (saveType == 2 && coverImageNumber == 3 && (!detailsState.textImgList || detailsState.textImgList.length < 3)) {
      Toast.show('请上传3张封面图')
      return
    }

    Toast.show({
      icon: 'loading',
      content: '',
      duration: 0,
    })
    await clear()
    dispatch({
      type: 'graphicsText/editImgTextInfo',
      payload: {
        forwardDescribe: null,
        id: id,
        imageType: 1,
        textImgList: detailsState.textImgList,
        isForward: 0,
        kingdomId: selectedKingdomId,
        saveType,
      }
    }).then(res => {
      Toast.clear()
      const { code, content, msg } = res
      if (code == 200 && (content || content == 0)) {
        if (saveType == 1) {
          Toast.show('保存成功')
        } else {
          history.replace('/Square?tabKey=2&publish=1&isAuditing=1')
        }
      } else {
        Toast.show(msg || '数据加载失败')
      }
    }).catch(err => {})
  }

  const bottomTipsBarHide = () => {
    setModalState({
      ...modalState,
      bottomTipsBarVisible: false,
    })
  }

  const goBack = () => {
    if (history.length > 1) {
      history.goBack()
    } else {
      history.replace('/')
    }
  }

  const loadingImgTextInfoById = !!loading.effects['graphicsText/imgTextInfoById']
  return (
    <Spin spinning={loadingImgTextInfoById} wrapperClassName={styles.spin}>
      <NavBar
        title="发文章"
        bordered
        RightRender={() => (
          <div className={styles.nav_bar_right}>
            <span className={styles.nar_bar_link_btn} onClick={submitDraft}>存草稿</span>
            <span className={classNames(styles.nar_bar_btn, {
              // [styles.disabled]: !detailsState.textImgList || detailsState.textImgList.length == 0,
            })} onClick={() => beforeSubmit(false)}>提交</span>
          </div>
        )}
      />

      <div className={styles.container}>
        {
          modalState.bottomTipsBarVisible &&
          <div className={styles.bottom_tips_bar}>
            为了维护良好的社区氛围，每篇文章和外链都会经过审核，请耐心等待，我们将尽快审核并发布你的文章。
            <div className={styles.bar_btn_wrap} onClick={bottomTipsBarHide}>
              <CloseOutlined />
            </div>
          </div>
        }

        <div className={styles.select_kingdom_box}>
          <div className={styles.select_left} onClick={selectKingdom}>
            <i className={styles.select_left_icon_1}></i>
            <span>选择关联王国</span>
            <i className={styles.select_left_icon_2}></i>
            <span className={styles.divider}></span>
          </div>
          {
            selectedKingdomName ?
              <div className={styles.select_right}>{selectedKingdomName}</div>
              :
              <div className={styles.select_right_default}>
                {
                  isHaveKingdomData ? '不关联王国，默认只发布在个人主页'
                    : '当前无可关联王国'
                }
              </div>
          }
        </div>

        <div className={styles.select_image_number_box}>
          <div className={styles.left}>封面设置</div>
          <div>
            <Radio.Group onChange={onChangeCoverImageNumber} value={coverImageNumber}>
              <Radio value={1}>单图</Radio>
              <Radio value={3}>3图</Radio>
            </Radio.Group>
          </div>
        </div>

        <div className={styles.preview_title}>预览</div>
        {
          coverImageNumber == 1 ?
            <div className={styles.article_horizontal_box}>
              <div className={styles.article_left}>
                <div className={styles.article_title}>{detailsState.imageTitle}</div>
                {
                  selectedKingdomName &&
                  <div className={styles.kingdom_box}>
                    <i></i>
                    <span>{selectedKingdomName}</span>
                  </div>
                }
                <div className={styles.article_user}>
                  <div className={styles.avatar} style={UserInfo.headUrl ? {backgroundImage: `url(${UserInfo.headUrl})`} : {backgroundColor: `${randomColor(UserInfo.friUserId)}`}}>
                    {UserInfo.headUrl ? '' : processNames(UserInfo.name)}
                  </div>
                  <span className={styles.name}>{UserInfo.name}</span>
                  <span>{dayjs().format('MM-DD')}</span>
                  {/*<i className={styles.icon}></i>*/}
                  {/*<span>1.3wGDP</span>*/}
                </div>
              </div>
              {
                detailsState.textImgList && detailsState.textImgList.length > 0 ?
                  <div className={styles.article_right} style={{backgroundImage: `url(${detailsState.textImgList[0].imageUrlShow})`}}>
                    <div className={styles.right_btn_box}>
                      <span onClick={() => {cropModalShow(0, detailsState.textImgList[0].imageUrlShow)}}>编辑</span>
                      <span onClick={() => uploadImageModalShow(2, 0)}>替换</span>
                    </div>
                  </div>
                  :
                  <div className={styles.article_right_upload} onClick={() => uploadImageModalShow(1)}>
                    <PlusOutlined />
                    <span>添加封面</span>
                  </div>
              }
            </div>
            :
            <div className={styles.article_vertical_box}>
              <div className={styles.article_title}>{detailsState.imageTitle}</div>

              <div className={styles.cover_img_box}>
                {
                  (detailsState.textImgList || []).concat(coverImageListEmpty).map((item, index) => {
                    if (index >= coverImageNumber) {
                      return null
                    }
                    if (item.isEmpty) {
                      return (
                        <div key={index} className={styles.cover_img_upload} onClick={() => uploadImageModalShow(1)}>
                          <PlusOutlined />
                          <span>添加封面</span>
                        </div>
                      )
                    }
                    return (
                      <div key={index} className={styles.cover_img_item} style={{backgroundImage: `url(${item.imageUrlShow})`}}>
                        <div className={styles.cover_img_btn_box}>
                          <span onClick={() => {cropModalShow(index, item.imageUrlShow)}}>编辑</span>
                          <span onClick={() => uploadImageModalShow(2, index)}>替换</span>
                        </div>
                      </div>
                    )
                  })
                }
              </div>
              {
                selectedKingdomName &&
                <div className={styles.kingdom_box}>
                  <i></i>
                  <span>{selectedKingdomName}</span>
                </div>
              }
              <div className={styles.article_user}>
                <div className={styles.avatar} style={UserInfo.headUrl ? {backgroundImage: `url(${UserInfo.headUrl})`} : {backgroundColor: `${randomColor(UserInfo.friUserId)}`}}>
                  {UserInfo.headUrl ? '' : processNames(UserInfo.name)}
                </div>
                <span className={styles.name}>{UserInfo.name}</span>
                <span>{dayjs().format('MM-DD')}</span>
                {/*<i className={styles.icon}></i>*/}
                {/*<span>1.3wGDP</span>*/}
              </div>
            </div>
        }
      </div>
      <UploadImageModal
        visible={modalState.uploadImageVisible}
        articleTextImgList={modalState.articleTextImgList}
        uploadImageMaxNumber={modalState.uploadImageMaxNumber}
        handleUploadImage={handleUploadImage}
        onCancel={uploadImageModalHide}
      />
      <CropModal
        visible={modalState.cropVisible}
        imageUrlShow={modalState.cropImageUrlShow}
        handleCroppingImage={handleCroppingImage}
        onCancel={cropModalHide}
      />
    </Spin>
  )
}

export default connect(({ graphicsText, loading }: any) => ({ graphicsText, loading }))(Index)
