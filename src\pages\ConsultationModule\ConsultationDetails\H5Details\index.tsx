/**
 * @Description: 移动端专家指导详情
 * @author: 赵斐
 */
import React, { useEffect, useReducer, useState, useRef } from 'react';
import { connect, history } from 'umi';
import { Toast, Modal } from 'antd-mobile';
import { Spin, message } from 'antd'
import InfiniteScroll from 'react-infinite-scroller';
import { useDebounce } from "@/utils/utils";
import styles from './index.less'
import { throttle } from 'lodash';
import TIM from 'tim-js-sdk';
import toppingIcon from '@/assets/Consultation/H5/topping_icon.png'
import endIcon from '@/assets/Consultation/H5/end_icon.png'
// @ts-ignore
import { ProcessingImData } from '@/utils/im-index'
// 导航组件
import NavBar from '@/components/NavBar'
// 进度条
import ConsultationSteps from '@/components/ConsultationSteps'
// 移动端专家信息卡片
import ExpertCard from '@/pages/ConsultationModule/H5Components/ExpertCard'
// 移动端指导头部信息卡片
import ConsultationHead from '@/pages/ConsultationModule/H5Components/ConsultationHead'
// 移动端指导病例信息卡片
import ConsultationCaseCard from '@/pages/ConsultationModule/H5Components/ConsultationCaseCard'
// 移动端指导预约信息卡片 （视频指导使用）
import AppointmentTimeCard from '@/pages/ConsultationModule/H5Components/AppointmentTimeCard'
// 移动端指导订单信息卡片 （视频指导使用）
import ConsultationOrderCard from '@/pages/ConsultationModule/H5Components/ConsultationOrderCard'
// 移动端指导会议记录信息卡片 （视频指导使用）
import ConferenceRecordCard from '@/pages/ConsultationModule/H5Components/ConferenceRecordCard'
// 指导用户提问卡片(视频指导使用)
import UserQuestions from '@/pages/ConsultationModule/PcComponents/UserQuestions'
// 移动端结束指导提示弹窗
import EndConsultationTipsModal from '@/pages/ConsultationModule/H5Components/EndConsultationTipsModal'
// 移动端指导附件数量展示
import AnnexNum from '@/pages/ConsultationModule/H5IMComponents/AnnexNum'
// 移动端聊天组件
import IMInlet from '@/pages/ConsultationModule/H5IMComponents/IMInlet'
// 移动端我已关注公众号，不再提示
import FollowOfficialAccountTips from '@/pages/ConsultationModule/H5Components/FollowOfficialAccountTips'
// 移动端聊天输入框组件
import ChatInputBottomBox from '@/pages/ConsultationModule/H5IMComponents/ChatInputBottomBox'
// 创建空间组件 （视频指导使用）
import CreateKingdomOrSpace from '@/pages/UserInfo/CreateKingdomOrSpace';
// 病例详情无模板弹窗组件（图文指导使用）
import CaseDetailsModal from '@/pages/ConsultationModule/H5Components/CaseDetailsModal'
// 病例详情有模板弹窗组件（图文指导使用）
import CaseDetailsByTemplateModal from '@/pages/ConsultationModule/H5Components/CaseDetailsByTemplateModal'

const initStatePage = {
  pageNum: "",
  hasMore: true,  // 加载更多
  loadMore: false,
  isScroll: false,   // 滚动分页控制页面数据定位
}
// 初始化底部聊天功能内容
const initChatState = {
  emojiVisible: false,     // 表情包相关内容展示状态
  otherVisible: false,     // 其他内容展示状态
}
const initConsultationDataSource = {
  type: null,                 // 指导类型 (1图文、2视频)
  imGroupId: null,            // 群组ID
  expertsId: null,            // 指导医生ID
  createDate: null,           // 创建时间
  createUserName: null, // 创建人
  createUserId: null,   // 创建人ID
  processNode: null,
  // 流程节点(图文流程节点[1选择指导方式、2描述病例问题、3支付指导费用、4病例资料被查看、5问题被回复并对话、6结束指导交易成功];
  // 视频流程节点[1选择指导方式、2描述病例问题、3提交指导单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、7结束指导、8确认并支付指导费用、9交易成功])
  createUserHeadUrlShow: null,  // 用户头像
  h5BaseUserDto: null,        // 专家信息
  consultationCaseInfoDto: {  // 病例相关信息
    attachmentCount: null,     // 附件数量
    caseName: null,            // 病例名称
    id: null,                  // 病例Id
    isTemplate: null,          // 1 有模板 0 无模板
    firstQuestion: null,      // 初次提问
  },
  videoAppointment: null,     // 最新预约的视频指导会议时间
  videoRecordList: [],      // 历史视频指导-会议记录
  thisUserIsExperts: null,    // 当前人是否是专家：0:否，1:是
  attachmentCount: null,      // 附件数量
  isFinish: null,             // 当前节点是否完成(1是、0否)
  amount: null,               // 指导金额
  defaultAssistantUrl: null,  // 小忆二维码
  duration: null,              // 指导时长
  orderNumber: null,           // 指导订单
  status: null,                // 支付状态(0已取消、1未支付(待评估)、2去支付(已评估)、3已支付)
}
const initImList: any[] | undefined = []  // IM列表数据
const reducer = (state: any, action: { type: any; payload: any; }) => {
  switch (action.type) {
    case 'insert':
      return [...state, ...action.payload];
    case 'receive':
      return [...state, ...action.payload];
    case 'replace':
      return [...action.payload];
    case 'receiveHistory':
      return [...action.payload, ...state,];
    case 'clearData':
      return [];
    // default:
    //   throw new Error();
  }
};
let sendStatus = false
const Index: React.FC = (props: any) => {
  const { dispatch, loading, userInfoStore, consultation } = props;
  const { saveImloading, softKeyboard ,uploadProgress } = consultation || {}
  const { location } = history || {}
  const { query } = location || {}
  const { consultationId, consultationType } = query || {}   // 指导ID
  const userInfoStr = localStorage.getItem('userInfo')
  const userInfo = userInfoStr ? JSON.parse(userInfoStr) : {}  // 获取本地用户信息
  const val = useRef();
  const chatListRef = useRef(null)   // 获取聊天ref
  const caseCardRef = useRef(null)   // 获取病例ref
  const inputRef = useRef(null)

  const [timObj, setTimObj] = useState(null) // IM对象
  const [statePage, setStatePage] = useState(initStatePage)  // 当前分页
  const [pageScrollHeight, setPageScrollHeight] = useState(0)  // 页面滚动高度

  const [chatState, setChatState] = useState(initChatState)
  const [endTipsVisible, setEndTipsVisible] = useState(false)    // 结束指导提示弹窗
  const [showCaseHeader, setShowCaseHeader] = useState(false)     // 病例头部显示
  const [stateImList, setStateImList] = useReducer(reducer, initImList);  // IM聊天数据
  const [consultationDataSource, setConsultationDataSource] = useState(initConsultationDataSource)  // 指导详情相关数据
  // 图文指导使用
  const [caseDetailsVisible, setCaseDetailsVisible] = useState(false)                     // 病例详情无模板弹窗状态
  const [caseDetailsByTemplateVisible, setCaseDetailsByTemplateVisible] = useState(false) // 病例详情有模板弹窗状态
  // 视频指导使用
  const { createModalVisible } = userInfoStore || {}   // 打开创建空间(指导)弹窗状态
  const [createKingdomOrSpaceType, setCreateKingdomOrSpaceType] = useState(1)  // 创建空间弹窗内容类型
  //
  const [isShowMpQr, setIsShowMpQr] = useState(false)   // 公众号展示卡片状态
  // 计算页面内容高度
  const [msgContentHeight, setMsgContentHeight] = useState(0)
  const [messageReceived, setMessageReceived] = useState({
    receivedStatus: false,  // 监听接收消息
    data: []                // 接收消息
  })
  const {
    emojiVisible,
    otherVisible,
  } = chatState
  const {
    pageNum,
    hasMore,
    loadMore,
    isScroll
  } = statePage || {}


  const {
    type,               // 指导类型(1图文、2视频)
    imGroupId,          // 群组ID
    createDate,         // 创建时间
    createUserId,   // 创建人ID
    createUserName, // 创建人
    processNode,
    createUserHeadUrlShow,  // 用户头像
    h5BaseUserDto,      // 专家信息
    consultationCaseInfoDto,  // 病例相关信息
    videoAppointment,   // 最新预约的视频指导会议时间
    thisUserIsExperts,  // 当前人是否是专家：0:否，1:是
    isFinish,           // 当前节点是否完成(1是、0否)
    expertsId,          // 指导医生ID
    videoRecordList,    // 历史视频指导-会议记录
    duration,           // 指导时长
    orderNumber,        // 指导订单
    status,             // 支付状态(0已取消、1未支付(待评估)、2去支付(已评估)、3已支付)
    amount,             // 账单金额

    defaultAssistantUrl,  // 二维码地址
    orderCaseTemplate, // 1通用模板，2正畸模板
  } = consultationDataSource || {}
  const {
    caseName,           // 病例名称
    attachmentCount,    // 附件数量
    isTemplate,         // 1 有模板 0 无模板
    firstQuestion,      // 初次提问
  } = consultationCaseInfoDto || {}


  // 接收消息使用
  useEffect(() => {


    if(consultationType == '3'){
      Modal.alert({
        content: '请在电脑端浏览器打开',
        onConfirm: () => {
          console.log('Confirmed')
          history.replace('/')
        }
      })
      return
    }
    const { imGroupId } = consultationDataSource || {}
    const { receivedStatus, data } = messageReceived || {};
    let id = `GROUP${imGroupId}`
    if (receivedStatus) {
      if (data[0].conversationID == id) {
        console.log("调用刷新接口")
        refreshPageStatus(data, 2)
        setMessageReceived({
          receivedStatus: false,
          data: []
        })
      }
    }

  }, [messageReceived.receivedStatus])

  useEffect(() => {
    // 用户信息卡片距离计算替换展示
    let dom = document.getElementById("content")
    dom ? dom.addEventListener('scroll', handleScroll) : null
    // 加载页面所有使用的接口
    callInterfaceFun()
    Toast.show({
      content: navigator,
      duration: 0,
      position: 'top',
    })

    return () => {
      dom ? dom.removeEventListener('scroll', handleScroll) : null
      window.scrollTo({ top: 0, behavior: "smooth" });
    }

  }, [])

  useEffect(() => {
    const { imGroupId, isFinish } = consultationDataSource || {}
    if (imGroupId && timObj && isFinish == 0) {
      joinGroupByIm(timObj)   // 加入群组
    }
    let userAgent = navigator.userAgent
    const versionMatch = userAgent.match(/iPhone OS (\d+)/);

    // 处理软键盘弹出时的样式问题
    const inputElement = inputRef.current;
    if (versionMatch && parseInt(versionMatch[1]) > 15) {
      document.body.addEventListener('focusin', handleFocus)
      document.body.addEventListener('focusout', handleBlur)
    } else {
      if (inputElement) {
        // 添加focus事件监听，使软键盘贴合输入框
        inputElement.addEventListener("focus", handleFocus);
        // 添加blur事件监听，恢复页面滚动位置
        inputElement.addEventListener("blur", handleBlur);
      }
    }

    getMsgContentHight()
    // 移除监听事件
    return () => {
      if (versionMatch && parseInt(versionMatch[1]) > 15) {
        document.body.removeEventListener('focusin', handleFocus)
        document.body.removeEventListener('focusout', handleBlur)
      } else {
        if (inputElement) {
          inputElement.removeEventListener("focus", handleFocus);
          inputElement.removeEventListener("blur", handleBlur);
        }
      }

    }
  }, [consultationDataSource, timObj])


  useEffect(() => {
    if (Array.isArray(stateImList) && stateImList.length && sendStatus) {
      pageHeightFun(1)
      sendStatus = false
    } else if (isScroll && showCaseHeader) {
      pageHeightFun(2)
      setStatePage({
        ...statePage,
        isScroll: false
      })
    } else {
      getMsgContentHight()
    }
    val.current = stateImList;
  }, [stateImList, showCaseHeader])

  useEffect(() => {
    getMsgContentHight()
  }, [emojiVisible, otherVisible, softKeyboard])

  /**
 * 获取页面滚动高度
 * @param init  1 第一次进入页面滚动 2 进行滚动分页面
 */
  const pageHeightFun = (init: number) => {
    if (chatListRef.current) {
      if (init == 1) {
        window.scrollTo({ top: chatListRef.current.scrollHeight, behavior: "smooth" });
        chatListRef.current.scrollTop = chatListRef.current.scrollHeight + 300;
        setPageScrollHeight(chatListRef.current.scrollTop)
      }
      if (init == 2) {
        chatListRef.current.scrollTop = chatListRef.current.scrollHeight - pageScrollHeight
        console.log(chatListRef.current.scrollHeight - pageScrollHeight, "下一次即将使用的滚动距离")
        setPageScrollHeight(chatListRef.current.scrollTop)
      }
    }
  }
  // 加载页面所有使用的接口
  const callInterfaceFun = async () => {
    await getConsultationAndCaseInfo()
    await dispatch({
      type: 'tim/getTim'
    }).then((res: any) => {
      if (res) {
        setTimObj(res)
        initializationByIm(res)
      } else { message.error("tim初始化失败!") }
    })
    await getChatGroupMsg('')
  }

  // 获取病例卡片滚动距离顶部距离
  const scrollToTop = (element: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    chatListRef.current.scrollTop = 0
    let userAgent = navigator.userAgent
    const versionMatch = userAgent.match(/iPhone OS (\d+)/);
    if (versionMatch && parseInt(versionMatch[1]) <= 15) {
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
    setShowCaseHeader(false)
  }
  // 获取页面滚动距离
  const handleScroll = () => {
    if (caseCardRef.current && chatListRef.current) {
      let hel = caseCardRef.current.offsetTop + caseCardRef.current.offsetHeight - 54
      console.log(hel, "病例高度")
      if (chatListRef.current.scrollTop > hel) {
        setShowCaseHeader(true)
      }
    }
  }

  // 查询指导和病例详情
  const getConsultationAndCaseInfo = () => {
    dispatch({
      type: "consultation/getConsultationAndCaseInfo",
      payload: {
        consultationId,
        type: 2
      }
    }).then((res: any) => {
      if (res != undefined) {
        let { code, content, msg } = res || {}
        let { expertsId, processNode, isShowMpQr } = content || {}   // 群组Id
        if (code == 422) {
          Modal.alert({
            content: msg,
            onConfirm: () => {
              console.log('Confirmed')
              history.replace('/')
            }
          })
          return
        }
        if (code == 200) {
          if (processNode == 4 && expertsId == userInfo.friUserId) {
            editConsultationNodeAndStatus(2)
          }
          setIsShowMpQr(isShowMpQr == 1) // 是否展示聊天公众号二维码，0不展示，1展示
          // 查询页面进度节点
          setConsultationDataSource(content)
        } else {
          Toast.show(msg)
        }
      }
    })
  }

  // 初次获取聊天信息或者分页时获取聊天信息
  const getChatGroupMsg = (msgSeq: string) => {
    dispatch({
      type: "consultation/getChatGroupMsg",
      payload: {
        msgSeq,
        pageSize: 50,
        consultationId
      }
    }).then((res: any) => {
      if (res != undefined) {
        let { code, content, msg } = res || {}
        let { resultList, lastSeq } = content || {}
        if (code == 200) {
          if (Array.isArray(resultList) && resultList.length) {
            setStateImList({ type: 'receiveHistory', payload: resultList })
            setStatePage({
              ...statePage,
              pageNum: lastSeq
            })
          } else {
            setStatePage({
              ...statePage,
              loadMore: false,
              hasMore: false
            })
          }
        } else {
          Toast.show(msg)
        }
      }
    })
  }

  /**
 * 修改指导订单节点和状态
 * @param status 1:先体验后付费, 2:图文或视频病例被查看, 3:图文问题被回复并对话, 4:图文结束指导交易成功, 5:视频预约视频会议, 6:视频沟通, 7:结束指导
 */
  const editConsultationNodeAndStatus = (status: number) => {
    dispatch({
      type: "consultation/editConsultationNodeAndStatus",
      payload: {
        // userId:"",
        type: status,
        consultationId,
      }
    }).then((res: any) => {
      if (res != undefined) {
        let { code, msg } = res || {}
        if (code == 200) {
          if (status == 2 || status == 4 || status == 7 || status == 8) {
            getConsultationAndCaseInfo()
          }
        } else {
          Toast.show(msg)
        }
      }
    })
  }

  /**
   * 加入群组
   * @param imGroupId   群组ID
   */
  const joinGroupByIm = async (tim: any) => {
    const { imGroupId } = consultationDataSource || {}
    console.log(tim, imGroupId)
    let promise = tim.joinGroup({ groupID: imGroupId });
    promise.then(function (imResponse: any) {
      switch (imResponse.data.status) {
        case TIM.TYPES.JOIN_STATUS_WAIT_APPROVAL:                    // 等待管理员同意
          break;
        case TIM.TYPES.JOIN_STATUS_SUCCESS:                          // 加群成功
          console.log('加群成功 :: ', imResponse.data.group); // 加入的群组资料
          break;
        case TIM.TYPES.JOIN_STATUS_ALREADY_IN_GROUP:                // 已经在群中
          console.log('已经在群 :: ', imResponse.data.group); // 加入的群组资料
          break;
        default:
          break;
      }
    }).catch(function (imError: any) {
      if (imError.code == 10013) {
        // 用户已经是当前群成员
      } else {
        console.warn('joinGroup error:', imError); // 申请加群失败的相关信息
        message.error(imError.message);
      }
    });
  }

  // 初始化Im及时通信组件 实例名称定义为"tim"
  const initializationByIm = async (tim: any) => {
    // if (!tim) { return }
    // 监听事件, 关于收到的消息的事件
    tim.off(TIM.EVENT.MESSAGE_RECEIVED, onMessageReceived)
    tim.on(TIM.EVENT.MESSAGE_RECEIVED, onMessageReceived);
    // 监听被踢出事件
    tim.on(TIM.EVENT.KICKED_OUT, onKickedOut);
    // 网络状态发生改变
    tim.on(TIM.EVENT.NET_STATE_CHANGE, (event: any) => {
      /*
        *  TIM.TYPES.NET_STATE_CONNECTED    - 已接入网络
        // TIM.TYPES.NET_STATE_CONNECTING   - 连接中。很可能遇到网络抖动，SDK 在重试。接入侧可根据此状态提示“当前网络不稳定”或“连接中”
        // TIM.TYPES.NET_STATE_DISCONNECTED - 未接入网络。接入侧可根据此状态提示“当前网络不可用”。SDK 仍会继续重试，若用户网络恢复，SDK 会自动同步消息
      */
      if (event.data.state == TIM.TYPES.NET_STATE_CONNECTING) {
        let showMessage = throttle(() => { message.warning('当前网络不稳定'); }, 500)
        showMessage();

      }
      if (event.data.state == TIM.TYPES.NET_STATE_DISCONNECTED) {
        let showMessage = throttle(() => { message.error('当前网络不可用!'); }, 500)
        showMessage();
      }
    });

    // SDK 进入 not ready 状态时触发，此时接入侧将无法使用
    // SDK 发送消息等功能。如果想恢复使用，接入侧需调用 login 接口，驱动 SDK 进入 ready 状态
    tim.on(TIM.EVENT.SDK_NOT_READY, () => {

    });
  }
  // 监听被踢出事件 - 例如：多端登录被踢
  const onKickedOut = (event: any) => {

  }

  // 监听IM获取数据
  const onMessageReceived = (event: { data: any; }) => {
    const messageList = event.data;
    if (messageList.length) {
      if (messageList[0].type == TIM.TYPES.MSG_CUSTOM && messageList[0].payload.data == 'REFRESH_STATUS') {
        getConsultationAndCaseInfo()
      } else {
        setMessageReceived({
          receivedStatus: true,
          data: messageList
        })
        uploadLoading(false)
      }
    }
  }

  // 上传所需Loading
  const uploadLoading = (status: boolean) => {
    dispatch({
      type: "consultation/saveImloading",
      payload: {
        saveImloading: status,
      }
    })
  }

  /**
   * 点击发送更改页面数据、更改状态，刷新
   * @param messageList    当前发送或接收消息
   * @param type           1 发送  2 接收
   * @param localType      1 IM发送 其他本地处理最新数据展示
   */
  const refreshPageStatus = (messageList: any[], type: number,localType:number) => {
    if (type == 1) {
      onClickOhterOrEmojiHide()
    }
    sendStatus = true
    if(localType == 1){
      let arr  = ProcessingImData(messageList)
     console.log(messageList,"Ssss")

      for(let i = 0; i < val.current.length; i++) {
        for(let j = 0; j < arr.length; j++) {
            if(val.current[i].randomNumber === arr[j].randomNumber && val.current[i].islocal==1) {
              val.current[i] = arr[j];
            }
        }
    }
      setStateImList({ type: type == 1 ? 'insert' : type == 2 ? 'receive' : type == 3?'replace':null, payload: val.current })
    }else{
      setStateImList({ type: type == 1 ? 'insert' : type == 2 ? 'receive' : null,payload:localType==2?messageList:ProcessingImData(messageList) })
    }
  }
  // 打开结束指导弹窗
  const onClickEndConsultationShow = () => {
    setEndTipsVisible(true)
  }

  /**
   * 关闭结束指导弹窗
   * @param type  1 刷新页面
   */
  const onClickEndConsultationHide = (type: number) => {
    setEndTipsVisible(false)
    if (type == 1) {
      if (consultationType == '1') {
        editConsultationNodeAndStatus(4)
      } else {
        editConsultationNodeAndStatus(7)
      }
    }
  }

  /**
   * 视频指导
   * 刷新页面(创建空间【指导】)使用调用
   */
  const refreshPage = async () => {
    await editConsultationNodeAndStatus(5)
    await getConsultationAndCaseInfo()
  }

  // （视频指导）预约会议
  const onClickReservedConference = () => {
    setCreateKingdomOrSpaceType(1)
    const options = {
      id: '创建直播',
      type: 1,
      title: '创建直播',
      goBackType: '', // 返回（99创建王国空间）
      isShowGoBack: 1, // 是否有返回箭头 1没有
      isSelectKingDom: 1, // 是否可以选择王国 1没有
    }

    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        createModalVisible: false, // 打开弹框
        creatTabSelectDate: options, // 创建空间的信息
        spaceName: `${consultationDataSource.h5BaseUserDto && consultationDataSource.h5BaseUserDto.name}和${consultationDataSource.createUserName}的指导`,
        isPasswordSwitchChecked: true,
        isEditSpace: false,
        spaceFromEnter: {
          pageType: '4', // 1 我的主页、2 空间详情、3 王国详情  4 指导详情
          refreshFn: refreshPage, // 刷新页面的方法
          tipText: '创建直播成功', // 创建成功提示信息
          // isJumpOrRefresh: true, // 用于在王国创建空间后直接跳转空间详情或关闭弹框内容
          consultationId,
        }
      }
    })

    history.push(`/CreateSpace/Live?consultationId=${consultationId}`)
  }

  // （视频指导）修改空间
  const editSpaceInfo = () => {
    setCreateKingdomOrSpaceType(10)
    const options = {
      id: '创建直播',
      type: 1,
      title: '修改创建直播',
      goBackType: '', // 返回（99创建王国空间）
      isShowGoBack: 1, // 是否有返回箭头 1没有
      isSelectKingDom: 1, // 是否可以选择王国 1没有
    }

    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        createModalVisible: true, // 打开弹框
        creatTabSelectDate: options, // 创建空间的信息
        spaceName: `${consultationDataSource.h5BaseUserDto && consultationDataSource.h5BaseUserDto.name}和${consultationDataSource.createUserName}的指导`,
        isPasswordSwitchChecked: true,
        isEditSpace: true,
        spaceFromEnter: {
          pageType: '4', // 1 我的主页、2 空间详情、3 王国详情  4 指导详情
          refreshFn: refreshPage, // 刷新页面的方法
          tipText: '改约成功', // 创建成功提示信息
          // isJumpOrRefresh: true, // 用于在王国创建空间后直接跳转空间详情或关闭弹框内容
          consultationId,
          spaceId: videoAppointment && videoAppointment.spaceId
        }
      }
    })
  }


  // 点击查看附件跳转方法（专家权限）
  const onLookAnnexJump = () => {
    const { consultationCaseInfoDto } = consultationDataSource || {}
    const { id } = consultationCaseInfoDto || {}
    history.push(`/ConsultationModule/AttachmentPage/${id}?consultationId=${consultationId}&consultationType=${consultationType}`)
  }

  /**
   * 跳转支付页面 （视频指导使用）
   * @param id 指导ID
   */
  const jumpPayPage = (id: number) => {
    history.push(`/PaymentByConsultation/MyConsultationDetails/${id}`)
  }

  // 打开emoji内容
  const onClickEmojiShow = () => {
    setChatState({
      ...chatState,
      emojiVisible: true,
      otherVisible: false
    })
  }
  // 关闭emoji内容
  const onClickEmojiHide = () => {
    setChatState({
      ...chatState,
      emojiVisible: false
    })
  }
  // 打开其它内容
  const onClickOhterShow = () => {
    setChatState({
      ...chatState,
      otherVisible: true,
      emojiVisible: false
    })

  }
  // 关闭其它内容
  const onClickOhterHide = () => {
    setChatState({
      ...chatState,
      otherVisible: false
    })
  }
  // 隐藏其他内容、emoji内容
  const onClickOhterOrEmojiHide = () => {
    if (otherVisible || emojiVisible) {
      setChatState({
        ...chatState,
        otherVisible: false,
        emojiVisible: false
      })
    }
    dispatch({
      type: 'consultation/save',
      payload: {
        softKeyboard: false
      }
    })
  }

  // 查看病例详情
  const lookCaseDetails = () => {
    // 通用模板
    if (orderCaseTemplate == 1) {
      if (isTemplate == 1) {
        setCaseDetailsByTemplateVisible(true)
      } else if (isTemplate == 0) {
        setCaseDetailsVisible(true)
      }
    } else {
      // 正畸模版
      history.push(`/ConsultationModule/H5OrthodonticCasesDetail/${consultationId}?type=2`)
    }
  }

  // 病例详情弹窗关闭
  const caseDetailsModalHide = () => {
    setCaseDetailsByTemplateVisible(false)
    setCaseDetailsVisible(false)
  }
  // 滚动加载分页
  let handleInfiniteOnLoad = () => {
    if (showCaseHeader) {
      setStatePage({
        ...statePage,
        loadMore: true,
        isScroll: true
      })
      getChatGroupMsg(pageNum)
    }
  }
  handleInfiniteOnLoad = throttle(handleInfiniteOnLoad, 100);

  // 记录用户已关注公众号(点击我已关注)
  const isFocusMp = () => {
    dispatch({
      type: "consultation/isFocusMp",
      payload: {
        wxUserId: userInfo.friUserId,
      }
    }).then((res: any) => {
      if (res != undefined) {
        let { code, msg } = res || {}
        if (code == 200) {
          setIsShowMpQr(false)
        } else {
          Toast.show(msg)
        }
      }
    })
  }

  // 页面获取焦点
  const handleFocus = () => {
    const inputElement = inputRef.current;
    if (inputElement) {
      setTimeout(() => {
        document.getElementsByTagName('body')[0].addEventListener('touchmove', preventDefaultByHandler, { passive: false }) // 阻止默认事件
        // window.scrollTo({ top:  window.innerHeight, left: 0, behavior: 'smooth' });
        let userAgent = navigator.userAgent
        if (/iPhone/.test(userAgent)) {
          const versionMatch = userAgent.match(/iPhone OS (\d+)/);
          if (versionMatch && parseInt(versionMatch[1]) > 15) {
            console.log("当前设备为 iPhone，系统版本 > 15，执行的滚动距离");
            inputElement.scrollTo({ top: (inputElement.scrollHeight - window.innerHeight - 100) / 2, behavior: 'smooth' });
          } else if (versionMatch && parseInt(versionMatch[1]) <= 15) {
            console.log("当前设备为 iPhone，系统版本 <= 15， 执行的滚动距离");
            window.scrollTo({ top: window.innerHeight + 50, left: 0, behavior: 'smooth' });
          }
        } else if (/Android/.test(userAgent)) {

          console.log("当前设备为 Android，返回 3");
          window.scrollTo({ top: window.innerHeight, left: 0, behavior: 'smooth' });
        } else {
          window.scrollTo({ top: window.innerHeight, left: 0, behavior: 'smooth' });
        }
      }, 100)
    }
  }
  // 页面失去焦点
  const handleBlur = () => {
    document.getElementsByTagName('body')[0].removeEventListener('touchmove', preventDefaultByHandler, { passive: false }) // 打开默认事件
    // 恢复页面滚动位置
    window.scrollTo({ top: 0, behavior: "smooth" });
  }
  // 获取焦点
  const onFocusInput = () => {
    onClickOhterOrEmojiHide()
    dispatch({
      type: 'consultation/save',
      payload: {
        softKeyboard: true
      }
    })

  }

  // 失去焦点
  const onBlurInput = () => {
    dispatch({
      type: 'consultation/save',
      payload: {
        softKeyboard: false
      }
    })
  }
  // 阻止默认事件
  const preventDefaultByHandler = (e) => {
    e.preventDefault()
  }

  // 计算msg_content的高度
  const getMsgContentHight = useDebounce(() => {

    // 页面高度
    let Hight_wrap = document.getElementsByClassName(styles.wrap)[0]?.offsetHeight
    // 导航高度
    let Hight_header = document.getElementsByClassName(styles.header)[0]?.offsetHeight ? document.getElementsByClassName(styles.header)[0]?.offsetHeight : null
    // 病例置顶卡片高度
    let Hight_case_header = document.getElementsByClassName(styles.case_header)[0]?.offsetHeight ? document.getElementsByClassName(styles.case_header)[0]?.offsetHeight : null
    // 视频指导初次提问卡片
    let Hight_user_questions = document.getElementsByClassName(styles.user_questions)[0]?.offsetHeight ? document.getElementsByClassName(styles.user_questions)[0]?.offsetHeight : null
    // 图文指导底部高度
    let Hight_chat_bottom = document.getElementsByClassName(styles.chat_bottom)[0]?.offsetHeight ? document.getElementsByClassName(styles.chat_bottom)[0]?.offsetHeight : null
    // 视频底部高度
    let Hight_footer = document.getElementsByClassName(styles.footer)[0]?.offsetHeight ? document.getElementsByClassName(styles.footer)[0]?.offsetHeight : null

    let ArrayByHight = [
      Hight_header,
      Hight_case_header,
      Hight_user_questions,
      Hight_chat_bottom,
      Hight_footer,
    ]

    let accumulation = 0
    ArrayByHight.map((item) => { if (item) { accumulation = accumulation + item } })
    let msg_content_height = Hight_wrap - accumulation
    setMsgContentHeight(msg_content_height)
    if ((showCaseHeader && chatListRef.current) || (emojiVisible || otherVisible || softKeyboard && chatListRef.current)) {
      chatListRef.current.scrollTop = chatListRef.current.scrollHeight;
      setPageScrollHeight(chatListRef.current.scrollHeight)
    }
  }, 300)

  /**
   * 获取IM返回上传进度
   * @param obj
   */
  const UploadSchedule = (obj:any)=>{
    dispatch({
      type:'consultation/uploadProgressLoading',
      payload:{
        uploadProgress:[...uploadProgress,obj]
      }
    })
  }


  // loading
  const load = !!loading.effects['consultation/getConsultationAndCaseInfo'] ||  // 指导详情
    !!loading.effects['consultation/getChatGroupMsg'] ||            // 聊天记录
    !!loading.effects['consultation/editConsultationNodeAndStatus'] ||  // 修改指导订单节点和状态
    !!loading.effects['consultation/getConsultationProcessNode'] ||   // 查询指导流程节点
    !!saveImloading
  return (
    <Spin spinning={load}>
      {/* <div className={otherVisible || emojiVisible || (processNode == 6 && isFinish == 1) ? styles.wrap_bottom : styles.wrap}> */}
      <div className={styles.wrap}>
        {consultationType == '1' && thisUserIsExperts == 1 && processNode == 6 && isFinish == 0 ?
          <div className={styles.end_consultation} onClick={() => { onClickEndConsultationShow() }}>
            <span className={styles.end_span}><img src={endIcon} /></span>
            <span style={{ verticalAlign: "middle" }}>结束指导</span>
          </div> : null
        }
        <div className={styles.header}>
          <NavBar title="指导单详情" className={styles.header_nav} />
        </div>
        {
          // （图文指导展示）
          showCaseHeader && consultationType == '1' ? <div className={styles.case_header}>
            <span className={styles.left}>{caseName}</span>
            <span className={styles.right} onClick={() => { lookCaseDetails() }}>病例详情</span>
          </div> : null
        }

        <div
          className={styles.content}
          style={{ height: msgContentHeight }}
          id='content'
          ref={chatListRef}
        >
          <InfiniteScroll
            id="qwer"
            initialLoad={false}
            pageStart={0}
            isReverse
            loadMore={handleInfiniteOnLoad}
            hasMore={!loadMore && hasMore}
            useWindow={false}
            threshold={50}
          >
            {
              !showCaseHeader && consultationType == '1' || consultationType == '2' ? <>
                {/* 用户展示(提示信息) */}
                {
                  thisUserIsExperts != 1 && processNode && processNode < 5 ? <div className={styles.content_tips}>
                    {consultationType == '1' ? '专家查看病例后，将会在24小时内回复，您也可以向专家发起提问' : '提交成功后，24小时内将有客服联系您'}
                  </div> : null
                }
                {
                  thisUserIsExperts != 1 && status != 0 ? <div className={styles.progress_bar}>
                    <ConsultationSteps type={consultationType == '1' ? 2 : 4} processNode={processNode} isFinish={isFinish} />
                  </div> : null
                }

                <div style={{ marginTop: 8 }} className={styles.consultation_head}>
                  <ConsultationHead consultationType={consultationType} data={{ processNode, createDate, createUserName, videoAppointment, thisUserIsExperts, status, isFinish }} />
                </div>
                {/* 指导预约信息 （专家端且视频指导显示）*/}
                {
                  consultationType == '2' && processNode && processNode >= 5 && videoAppointment ? <div style={{ marginTop: 8 }} className={styles.appointment_time_card}>
                    <AppointmentTimeCard
                      data={{ videoAppointment, thisUserIsExperts, status, defaultAssistantUrl }}
                      editSpaceInfo={editSpaceInfo}
                    />
                  </div> : null
                }
                {/* 指导订单信息 (用户且视频指导显示)*/}
                {
                  consultationType == '2' && thisUserIsExperts != 1 && processNode == 9 && (status == 2 || status == 3) ? <div className={styles.consultation_order_card} style={{ marginTop: 8 }}>
                    <ConsultationOrderCard data={{ orderNumber, status, amount, consultationId, thisUserIsExperts }} goPay={jumpPayPage} />
                  </div> : null
                }
                {/* 会议记录 */}
                {
                  consultationType == '2' && Array.isArray(videoRecordList) && videoRecordList.length ? <div className={styles.conference_record_card} style={{ marginTop: 8 }}>
                    <ConferenceRecordCard data={videoRecordList} />
                  </div> : null
                }
                {/* 专家\用户信息 */}
                <div className={styles.expert_card} style={{ marginTop: 8 }}>
                  <ExpertCard data={{ h5BaseUserDto, orderNumber, createDate, createUserName, createUserId, createUserHeadUrlShow, thisUserIsExperts }} />
                </div>
                {/* 病例内容展示 */}
                <div className={styles.consultation_case_card} style={{ marginTop: 8 }} ref={caseCardRef}>
                  <ConsultationCaseCard isDoctor={thisUserIsExperts} isShowBtn={consultationType == '1' ? !(processNode == 6 && isFinish == 1) : processNode < 7} caseData={consultationCaseInfoDto || {}} consultationId={consultationId} pageFrom={'ConsultationDetails'} orderCaseTemplate={orderCaseTemplate} />
                </div>
                {/* 视频指导初次提问卡片 */}
                <div className={styles.user_questions} style={{ marginTop: 8 }}>
                  <UserQuestions questionData={firstQuestion} />
                </div>
              </> : null
            }
            {/* 查看附件入口 */}
            {
              attachmentCount ? <div className={styles.annex_num} style={{ marginTop: 20, textAlign: "center" }}>
                <AnnexNum annexNumber={attachmentCount} onLookJump={onLookAnnexJump} />
              </div> : null
            }
            {/* 聊天组件 */}
            <div className={styles.chat_list}>
              {
                Array.isArray(stateImList) && stateImList.length ? <IMInlet dataSource={stateImList} consultationType={consultationType || ''} /> : null
              }
              {/* 关注公众号 */}
              {
                isShowMpQr && <FollowOfficialAccountTips onClickIsFocusMp={isFocusMp} />
              }
            </div>
          </InfiniteScroll>
        </div>
        {/* 聊天输入框组件(图文指导) */}
        {
          consultationType == '1' ? <>
            <div
              ref={inputRef}
              className={styles.chat_bottom}
              style={/iPhone/.test(navigator.userAgent) ? { paddingBottom: '34px' } : {}}
            >

              {
                processNode == 6 && isFinish == 1 ? null : <>
                  {
                    showCaseHeader ? <div className={styles.chat_topping_icon} onClick={(element) => { scrollToTop(element) }}>
                      <img src={toppingIcon} alt="icon" />
                    </div> : null
                  }
                  <ChatInputBottomBox
                    isDoctor={thisUserIsExperts}
                    imGroupId={imGroupId}
                    timObj={timObj}
                    emojiVisible={emojiVisible}
                    otherVisible={otherVisible}
                    onClickOhterShow={onClickOhterShow}
                    onClickOhterHide={onClickOhterHide}
                    onClickEmojiShow={onClickEmojiShow}
                    onClickEmojiHide={onClickEmojiHide}
                    refreshPage={refreshPageStatus}
                    uploadLoading={uploadLoading}
                    onBlurInput={onBlurInput}
                    onFocusInput={onFocusInput}
                    stateImList = {val.current}
                    UploadSchedule={UploadSchedule}
                  />
                </>

              }
            </div>

          </> : null
        }
        {/* 视频指导底部展示 */}
        {
          consultationType == '2' ? <div className={styles.footer} style={/iPhone/.test(navigator.userAgent) ? { paddingBottom: '34px' } : {}}>
            {
              thisUserIsExperts == 1 && status != 0 ?
                <div className={styles.footer_content}>
                  {
                    processNode >= 5 && processNode <= 7 && !videoAppointment ? <p className={styles.reserved_conference} onClick={() => { onClickReservedConference() }}>预约会议</p> : null
                  }
                  {
                    processNode == 7 ? <p className={styles.chat_end_consultation} style={processNode == 7 && videoAppointment ? { width: "100%" } : {}} onClick={() => { onClickEndConsultationShow() }}>结束指导</p> : null
                  }
                  {
                    processNode >= 5 && processNode <= 6 ? <p style={!videoAppointment ? { width: "50%" } : { width: "100%" }} className={styles.cancel_a_meeting} onClick={() => { editConsultationNodeAndStatus(8) }}>取消指导</p> : null
                  }
                </div> : null
            }
          </div> : null
        }

        {/* 图文指导展示 */}
        {
          showCaseHeader && consultationType == '1' && processNode == 6 && isFinish == 1 ? <div className={styles.topping_icon} onClick={(element) => { scrollToTop(element) }}>
            <img src={toppingIcon} alt="icon" />
          </div> : null
        }
        {
          otherVisible || emojiVisible || softKeyboard ? <div onClick={() => { onClickOhterOrEmojiHide() }} className={styles.chat_mask}></div> : null
        }

      </div>
      {/* 取消指导提示弹窗 */}
      {
        <EndConsultationTipsModal visible={endTipsVisible} onCancel={onClickEndConsultationHide} />
      }
      {/* 创建空间弹窗（预约会议）【视频指导展示】 */}
      {
        createModalVisible && <CreateKingdomOrSpace comeType={createKingdomOrSpaceType} />
      }
      {/* 无模板病例详情弹窗 */}
      <CaseDetailsModal
        visible={caseDetailsVisible}
        caseData={consultationCaseInfoDto}
        onCancel={caseDetailsModalHide}
      />
      {/* 有模板病例详情弹窗 */}
      <CaseDetailsByTemplateModal
        visible={caseDetailsByTemplateVisible}
        caseData={consultationCaseInfoDto}
        onCancel={caseDetailsModalHide}
      />
    </Spin>
  )
}
export default connect(({ tim, consultation, userInfoStore, loading }: any) => ({ tim, consultation, userInfoStore, loading }))(Index)
