/**
 * @Description: 分类导航组件
 */
import React, { useState } from 'react'
import classNames from 'classnames'
import { group_click } from '../../utils'
import styles from './index.less'

interface PropsType {
  componentData: any,                                      // 组件数据，格式：{ dataList: [], config: { number: xx } }
  getPageInfo: any,                                        // 刷新页面数据回调
  isHomePage?: any,                                         // 是否为首页，1是，0否
  moduleIndex?: any,                                        // 当前组件在所有图文组件中的索引
}

import ImageView from '@/pages/Home/View/ImageView'              // 图片
import SpaceView from '@/components/SpaceList'                   // 空间
import KingdomView from '@/components/KingdomView'               // 王国
import CaseView from '@/components/CaseList'                     // 病例
import ImageTextView from '@/pages/Home/View/ImageTextView'      // 图文

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { componentData, isHomePage, moduleIndex } = props
  // config: { number: 11/12/13/14/15/50/60 } (11~15)-10， 一行几个，50 轮播，60 横向滑动
  const { config, id } = componentData
  const dataList = componentData.dataList || []

  // 当前选中的页签
  const [checkedIndex, setCheckedIndex] = useState(0)

  // 切换分类
  const checkedIndexOnChange = (index) => {
    setCheckedIndex(index)

    // 首页友盟统计，isHomePage=1
    if (isHomePage != 1) {
      return
    }
    // 首页，分类组件点击量
    group_click(moduleIndex, `第${moduleIndex}个分类组件，第${index + 1}个标签`)
  }

  return (
    <div className={styles.classifyGuide_container}>
      {/* 页签 */}
      <div className={styles.classifyGuide_item_box}>
        {
          dataList.map((item, index) => {
            return (
              <div key={index} className={classNames(styles.classifyGuide_item, {
                [styles.checked]: index == checkedIndex,
              })} onClick={() => checkedIndexOnChange(index)}>{item.name}</div>
            )
          })
        }
      </div>

      {/* 内容 */}
      <div className={styles.classifyGuide_content_box}>
        {
          dataList[checkedIndex] &&
          <>
            {
              dataList[checkedIndex].classifyType == 'image' ?
                <ImageView
                  componentData={{dataList: dataList[checkedIndex].classifyGuideDataList, config}}
                  isHomePage={isHomePage}
                  isClassifyGuide={1}
                  moduleIndex={moduleIndex}
                  classifyGuideTabIndex={checkedIndex + 1}
                />
                : dataList[checkedIndex].classifyType == 'case' ?
                <CaseView
                  componentData={{dataList: dataList[checkedIndex].classifyGuideDataList}}
                  isHomePage={isHomePage}
                  isClassifyGuide={1}
                  moduleIndex={moduleIndex}
                  classifyGuideTabIndex={checkedIndex + 1}
                />
                : dataList[checkedIndex].classifyType == 'space' ?
                  <SpaceView
                    componentData={{id, dataList: dataList[checkedIndex].classifyGuideDataList, config}}
                    isHomePage={isHomePage}
                    isClassifyGuide={1}
                    moduleIndex={moduleIndex}
                    classifyGuideTabIndex={checkedIndex + 1}
                  />
                  : dataList[checkedIndex].classifyType == 'kingdom' ?
                    <KingdomView
                      componentData={{dataList: dataList[checkedIndex].classifyGuideDataList}}
                      getPageInfo={props.getPageInfo}
                      isHomePage={isHomePage}
                      isClassifyGuide={1}
                      moduleIndex={moduleIndex}
                      classifyGuideTabIndex={checkedIndex + 1}
                    />
                    : dataList[checkedIndex].classifyType == 'imageText' ?
                      <ImageTextView
                        componentData={{dataList: dataList[checkedIndex].classifyGuideDataList}}
                        isHomePage={isHomePage}
                        isClassifyGuide={1}
                        moduleIndex={moduleIndex}
                        classifyGuideTabIndex={checkedIndex + 1}
                      />
                      : null
            }
          </>
        }
      </div>
    </div>
  )
}

export default Index
