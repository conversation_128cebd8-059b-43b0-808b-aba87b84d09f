
:global {
  @import (less) "~react-quill/dist/quill.core.css";
  .ql-editor {
    padding: 0 !important;
    font-size: 16px !important;
    color: #000 !important;
    line-height: 1.55;
    ::selection {
      color: inherit;
      background-color: rgba(80,160,255,.4);
    }
  }
  // 自定义内容的样式
  .quill_topic_format_wrap {
    color: #0095FF;
    white-space: nowrap;
  }
  .quill_emoji_format_wrap {
    width: 24px;
    height: 24px;
  }
  .quill_user_format_wrap {
    color: #0095FF;
    white-space: nowrap;
  }
  // 图片格式的样式
  .quill_image_format_wrap {
    padding: 8px 0;
    img {
      width: 100%;
      height: auto;
    }
  }
  // 视频格式的样式
  .quill_video_format_wrap {
    padding: 8px 0;
    .video_wrap {
      display: none;
      height: 200px;
      background: #f5f6f8;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .video_icon {
        width: 32px;
        height: 32px;
        background: url("../assets/GlobalImg/frame.png") no-repeat center;
        background-size: 100% 100%;
      }
      .video_text {
        font-size: 16px;
        color: #999;
        margin-top: 8px;
        user-select: none;
        width: 100%;
        display: flex;
        flex-wrap: nowrap;
        justify-content: center;
        overflow: hidden;
        padding: 0 12px;
        .video_text_name {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .video_text_suffix {
          flex-shrink: 0;
        }
      }
    }
    .video_content_wrap {
      display: block;
      width: 100%;
      video {
        width: 100%;
        height: auto;
      }
    }
  }
  // 进度条
  .quill_progress_format_wrap {
    display: none;
  }
}
