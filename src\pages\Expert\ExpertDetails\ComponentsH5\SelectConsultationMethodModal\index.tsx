/**
 * @Description: 创建指导H5第1步，选择指导方式弹窗
 */
import React, { useState, useEffect } from 'react'
import { connect, history } from 'umi'
import classNames from 'classnames'
import { Popup, Toast } from 'antd-mobile'
import { RightOutlined, InfoCircleOutlined, CloseOutlined } from '@ant-design/icons'
import styles from './index.less'

// 查看完整服务流程按钮及弹窗
import StartConsultationSteps from '@/pages/ConsultationModule/StartConsultation/ComponentsH5/StartConsultationSteps'

interface PropsType {
  dispatch: any,
  visible: boolean,                              // true，false
  expertsUserId: any,                            // 专家ID
  onCancel: any,                                 // 关闭弹窗
  onClickConsultationMethod: any, // 点击指导方式回调
}

const Index: React.FC<PropsType> = (props: any) => {
  const {
    dispatch,
    visible,
    expertsUserId, // 专家ID
    onClickConsultationMethod, // 点击指导方式回调
  } = props

  const initialState = {
    // discount: '0.5',                          //折扣(非会员无折扣NULL、个人7折、企业5折)
    // originalVideoTreatCosts: null,            //原视频诊费(/30min)
    // videoTreatCosts: null,                    //视频诊费(/30min)
    // originalPictureTreatCosts: null,          //原图文诊费(/次)
    // pictureTreatCosts: null,                  //图文诊费(/次)
    // freeTimes: '1',                           //剩余免费次数
    // memberOrderId: '153'                      //会员订单ID
  }
  const [state, setState] = useState(initialState)

  useEffect(() => {
    if (!localStorage.getItem('access_token')) {
      return
    }
    getConsultationWay()
  }, [])

  // 查询指导方式
  const getConsultationWay = () => {
    dispatch({
      type: 'consultation/getConsultationWay',
      payload: {
        expertsUserId: expertsUserId,            // 专家ID
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200) {
        setState(content || {})
      } else {
        Toast.show(msg || '查询指导方式失败')
      }
    }).catch(err => {})
  }

  return (
    <Popup
      visible={visible}
      onMaskClick={props.onCancel}
      className={classNames(styles.popup_container, {
        [styles.in_iphone]: /iPhone/.test(window.navigator.userAgent),
      })}
      destroyOnClose
      bodyStyle={{minHeight:'60%'}}
    >
      <div className={styles.header_line} onClick={props.onCancel}>
        <div className={styles.header_line_bar}></div>
      </div>
      <div className={styles.header_title}>
        选择咨询方式
        <div className={styles.close_icon} onClick={props.onCancel}>
          <CloseOutlined/>
        </div>
      </div>

      {/* 点击查看完整服务流程按钮及弹窗 */}
      <div className={styles.complete_process_wrap}>
        <StartConsultationSteps/>
      </div>

      <div className={styles.content}>
        {
          state.originalPictureTreatCosts &&
          <div className={styles.block} onClick={() => onClickConsultationMethod(1)}>
            <div className={classNames(styles.block_icon, styles.icon1)}></div>
            <div className={styles.block_details}>
              <div className={styles.details_title_box}>
                <div className={styles.details_title}>图文指导</div>
                <div className={styles.details_price_1}>¥{state.pictureTreatCosts}</div>
                <div className={styles.details_price_unit}>/次</div>
                {
                  state.originalPictureTreatCosts != state.pictureTreatCosts &&
                  <div className={styles.details_price_2}>¥{state.originalPictureTreatCosts}/次</div>
                }
              </div>
              <div className={styles.details_text}>通过文字、图片向专家提问，可在2个工作日内得到专家的图文或语音回复</div>
            </div>
            <div className={styles.block_arrow}>
              <RightOutlined/>
            </div>
          </div>
        }


        {
          state.originalVideoTreatCosts &&
          <div className={styles.block} onClick={() => onClickConsultationMethod(2)}>
            <div className={classNames(styles.block_icon, styles.icon2)}></div>
            <div className={styles.block_details}>
              <div className={styles.details_title_box}>
                <div className={styles.details_title}>视频指导</div>
                <div className={styles.details_price_1}>¥{state.videoTreatCosts}</div>
                <div className={styles.details_price_unit}>/30min</div>
                {
                  state.originalVideoTreatCosts != state.videoTreatCosts &&
                  <div className={styles.details_price_2}>¥{state.originalVideoTreatCosts}/30min</div>
                }
              </div>
              <div className={styles.details_text}>通过视频方式沟通更清楚</div>
            </div>
            <div className={styles.block_arrow}>
              <RightOutlined/>
            </div>
          </div>
        }
      </div>

      <div className={styles.bottom_box}>
        {
          state.freeTimes > 0 &&
          <div className={styles.bottom}>
            <InfoCircleOutlined/>
            <div>您还可使用免费指导<span className={styles.highlight}>{state.freeTimes}</span>次</div>
          </div>
        }
      </div>

    </Popup>
  )
}

export default connect(({ consultation, loading }: any) => ({ consultation, loading }))(Index)
