// @ts-ignore
/* eslint-disable */
import request from '@/utils/request';
import { stringify, parse } from 'qs';

/** 发送验证码 POST /api/login/captcha */
export async function newRequest(
  params: {
    phone?: string;
  },
  options?: { [key: string]: any },
) {
  return request<API.login>('/xxxx', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}


/**
 * /fri-uc/login-before/get-pc-register-phone-code
 * 获取注册短信验证码
 */
export async function getRegisterMsgCode(params: {
  phone?: string;
}, options?: { [key: string]: any }) {
  return request(`/api/fri-uc/login-before/get-pc-register-phone-code`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}


/**
 * /fri-uc/login-before/get-pc-register-phone-code
 * 获取登录短信验证码
 */
export async function getLoginMsgCode(params: {
  phone?: string;
}, options?: { [key: string]: any }) {
  return request(`/api/fri-uc/fri-auth/get-login-phone-code`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

/*
* 微信第三方授权获取短信验证码
* phone: 手机号
* pageKey: 页面key
* */
export async function getWechatLoginMsgCode(params: {
  phone?: string;
  pageKey?: string;
}, options?: { [key: string]: any }) {
  return request(`/api/fri-uc/login-before/get-we-chat-perfect-phone-code`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

/*
* 企业用户 - 微信第三方授权获取短信验证码
* phone: 手机号
* pageKey: 页面key
* */
export async function getEnterpriseLoginMsgCode(params: {
  phone?: string;
  pageKey?: string;
}, options?: { [key: string]: any }) {
  return request(`/api/fri-uc/login-before/get-we-chat-perfect-sms-code`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}


/**
 * /fri-uc/fri-auth/get-rest-phone-code
 * 获取设置&找回密码的短信验证码
 */
export async function getForgetPasswordMsgCode(params: {
  phone?: string;
}, options?: { [key: string]: any }) {
  return request(`/api/fri-uc/fri-auth/get-rest-phone-code`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

/**
 * /fri-uc/fri-auth/get-rest-phone-code
 * 判断当前账号是否有密码
 */
export async function checkExistPassword(params: { }, options?: { [key: string]: any }) {
  return request(`/api/fri-uc/user/check-uc-having-password`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

/**
 * /fri-uc/fri-auth/reset-password
 * 未登录状态下找回密码
 */
export async function resetPassword(params: { }, options?: { [key: string]: any }) {
  return request(`/api/fri-uc/fri-auth/reset-password`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

/**
 * /fri-uc/user/update-password
 * 设置中找回密码
 */
export async function updatePassword(params: { }, options?: { [key: string]: any }) {
  return request(`/api/fri-uc/user/update-password`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

/**
 * /fri-uc/user/update-password
 * 设置中忘记密码
 */
export async function settingResetPassword(params: { }, options?: { [key: string]: any }) {
  return request(`/api/fri-uc/user/login-after-reset-password`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}


/**
 * /user/h5User/checkPhoneRegistered
 * 校验手机号是否已被注册
 */
export async function checkPhoneRegistered(params: {
  phone?: string;
}, options?: { [key: string]: any }) {
  return request(`/api/user/h5User/checkPhoneRegistered`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

/**
 * /fri-uc/login-before/pc-register
 * 注册
 */
export async function signUp(params: {
  phone?: string;
  phoneCode?: string;   // 短信验证码
  pageKey?: string;     // 页面key(8位)
  imgCode?: string;     // 图形验证码
  name?: string;        // 姓名
  infoSource?: string;  // 用户信息来源，1:小程序；2:运营；3:H5；4:WEB；5:org-web；6:org-app
}, options?: { [key: string]: any }) {
  return request(`/api/fri-uc/login-before/pc-register`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

/**
 * 瑞尔转星球子系统的注册
 * /fri-uc/login-before/arrail-to-uc-register
 */
export async function signUpByArrailToUc(params: {
  arrailToUcKey?: string; // 瑞尔转星球的key，是会在瑞尔的接口中获取的
  phone?: string;
  phoneCode?: string;   // 短信验证码
  pageKey?: string;     // 页面key(8位)
  imgCode?: string;     // 图形验证码
  name?: string;        // 姓名
  infoSource?: string;  // 用户信息来源，1:小程序；2:运营；3:H5；4:WEB；5:org-web；6:org-app
}, options?: { [key: string]: any }) {
  return request(`/api/fri-uc/login-before/arrail-to-uc-register`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}


/**
 * /fri-uc/fri-auth/login
 * 验证码or密码登录
 */
export async function msgCodeLogin(params: {
  username?: string;       // 手机号
  imgCode?: string;        // 图形验证码
  password?: string;   // 短信验证码or密码
  loginMode?: string;      // 登录方式，1:验证码登录；2:密码登录
  pageKey?: string;     // 页面key(8位)
  starUserId?: string;     // 星球用户ID saas跳转登录用
  code?: string;     // 加密code,saas跳转登录用
}, options?: { [key: string]: any }) {
  return request(`/api/fri-uc/fri-auth/login`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

/**
 * [微信小程序外使用]
 *  /user/h5User/getMsgCodeUserInfo
 *  登录获取用户信息
 */
export async function getMsgCodeUserInfo(params: {
  access_token?: string;     // 页面key(8位)
  username?: string;       // 手机号
}, options?: { [key: string]: any }) {
  return request(`/api/fri-uc/user/get-star-identity`, {
    method: 'GET',
    headers: {
      type:1 // 头里加个字段  type  (不传：小程序；1：H5)
    },
    params: {
      ...params,
    },
  });
}

/**
 * [微信小程序内使用]
 * /user/wxMaUser/getAccountInfo
 * 获取账户信息
 */
export async function getAccountInfo(params: {
  access_token?: string;     // 页面key(8位)
}, options?: { [key: string]: any }) {
  return request(`/api/user/wxMaUser/getAccountInfo`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

/**
 * [外使用]
 * 获取图形验证码
 * /user/base/imgCode
 * */
export async function imgCode(params: {
  access_token?: string;     // 页面key(8位)
}, options?: { [key: string]: any }) {
  return request(`/api/fri-uc/login-before/get-img-code`, {
    ...options,
    method: 'GET',
    headers: {
      type:1 // 头里加个字段  type  (不传：小程序；1：H5)
    },
    params,
  });
}

/**
 * /user/base/checkImgCode
 * 校验图形验证码=>废弃该接口，已调整为后端校验
 * */
// export async function checkImgCode(params: {
//   access_token?: string;     // 页面key(8位)
// }, options?: { [key: string]: any }) {
//   return request(`/api/fri-uc/login-before/get-img-code`, {
//     ...options,
//     method: 'GET',
//     headers: {
//       type:1 // 头里加个字段  type  (不传：小程序；1：H5)
//     },
//     params,
//   });
// }

// 会员宣传页面获取订单价格
export async function getPlan(params: {
  access_token?: string;     // 页面key(8位)
}, options?: { [key: string]: any }) {
  return request(`/api/server/h5OrderPlan/getPlan`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}


/**
 * 瑞尔转星球子系统获取token\
 * */
export async function arrailToUcLogin(params: {
  fridayToUcKey: string;     // SAAS接口中返回的唯一值
}, options?: { [key: string]: any }) {
  return request(`/api/fri-uc/fri-auth/arrail-to-uc-login`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

/**
 * Friday-SAAS转星球子系统获取token\
 * */
export async function fridayToUcLogin(params: {
  fridayToUcKey: string;     // SAAS接口中返回的唯一值
}, options?: { [key: string]: any }) {
  return request(`/api/fri-uc/fri-auth/friday-to-uc-login`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}
