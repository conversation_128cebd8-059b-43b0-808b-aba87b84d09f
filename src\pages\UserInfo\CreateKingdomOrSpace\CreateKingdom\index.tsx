/**
 * @Description: 创建王国
 */
import React, { useState, useEffect, useRef, useCallback } from 'react'
import { history,connect } from 'umi'
import classNames from 'classnames'
import { Input, TextArea, Toast, Modal } from 'antd-mobile'
import styles from './index.less'
const tipsIcon = 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/tips.png' // 提示小图标
const uploadImg = 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/big_add.png'; // 上传图片图标
import GoBackIcon from '@/assets/GlobalImg/go_back.png'
import RightArrowIcon from '@/assets/GlobalImg/right_arrow.png'
import { Form, Spin, Upload } from 'antd'
import { stringify } from 'qs';
import { getOperatingEnv, processNames, randomColor } from '@/utils/utils';
import CommonConfirmModal from '@/components/CommonConfirmModal';

const Index: React.FC = (props: any) => {
  const { userInfoStore, dispatch, goBack } = props || {};
  const { selectedKing, creatTabSelectDate, isSuperAccount, kingdomName, kingdomIntroduce, kingdomCoverUrl, kingdomFromEnter, isEditKingdom } = userInfoStore || {};
  const { title, isShowGoBack, goBackType } = creatTabSelectDate && creatTabSelectDate || {};
  const [isVisible, setIsVisible] = useState(false); // 王国名称是否被使用
  const [kingdomInputVal, setKingdomInputVal] = useState(kingdomName || ''); // 王国名称value值
  const [kingdomInputIntroduceVal, setKingdomInputIntroduceVal] = useState(kingdomIntroduce || ''); // 王国名称value值
  const [kingdomLoadingByUpload, setKingdomLoadingByUpload] = useState(false);  // 王国封面上传loading
  const [isTipsVisible, setIsTipsVisible] = useState(false); // 编辑-确定时，展示二次确定提示弹框
  const {pageType, KingdomId, tipText, refreshFn} = kingdomFromEnter || {};

  const handler = useRef<any>();
  const [form] = Form.useForm();
  const UerInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');

  const goBackHandle = useCallback(() => {
    // 返回后清空值
    dispatch({
      type: 'userInfoStore/clean',
      payload: {
        selectedKing: null, // 选中的国王
        kingdomName: null, // 王国名称
        kingdomIntroduce: null, // 王国介绍
        isEditKingdom: false, // 是否是编辑王国
        kingdomCoverUrl: null,  // 王国封面图
      }
    })
    goBack(goBackType);
  }, [dispatch, goBack, goBackType])

  useEffect(() => {
    // 是否是编辑王国状态
    if(!pageType && !isEditKingdom) {
      // 校验王国名称是否被占用,出提示内容
      checkKingdomName(kingdomInputVal)
    }
  }, [])

  // 王国名称失去焦点事件-调接口检验名称是否重复
  const kingdomBlurFn = (e: { target: { value: any; }; }) => {
    const val = e.target.value;
    if(!val || !val.trim()) {
      form.setFieldValue('kingdomName', '')
      setKingdomInputVal('');
      return
    };

    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        kingdomName: val && val.trim(), // 王国名称
      }
    })

    form.setFieldValue('kingdomName', val && val.trim())

    checkKingdomName(val);
  }

  // 校验王国名称是否重复
  const checkKingdomName = useCallback((val) => {
    dispatch({
      type: 'userInfoStore/checkKingdomName',
      payload: {
        name: val && val.trim(), // 王国名称
        id: KingdomId, // 王国Id-编辑时必传
      }
    }).then((res: any) => {
      if(res && res.code == 200) {
        return setIsVisible(res.content);
      } else {
        return Toast.show({content: res.msg || '数据加载失败'})
      }
    }).catch((err:any) => {
      console.log(err)
    })
  },[dispatch])

  // 王国介绍失去焦点事件
  const textAreaBlurFn = (e:any) => {
    const val = e.target.value;
    if(!val && !val.trim()) return;

    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        kingdomIntroduce: val && val.trim(), // 王国介绍
      }
    })
  }

  // 确认创建
  const submitCreateFn = () => {
    // 1. isVisible 为true时，说明王国名称重复，且出提示，不能点击
    // 2. kingdomLoadingByUpload 为true时，说明正在上传封面图，不能点击
    if(isVisible || kingdomLoadingByUpload) return;
    handler.current = Toast.show({icon: 'loading', duration: 0, maskClickable: false}) // 加loading

    // 判断是否选择国王，或者未选择国王，则国王为当前用户，头像取当前用户头像
    const headUrl = !selectedKing || (selectedKing?.id == UerInfo?.friUserId) ? UerInfo?.headUrl : '';
    const headUrlImg = headUrl && headUrl != 'null' ? headUrl : null

    form.validateFields().then((err) => {
      dispatch({
        type: 'userInfoStore/addKingdom',
        payload: {
          wxUserId: selectedKing && selectedKing.id || UerInfo?.friUserId, // 国王ID
          kingName: selectedKing && selectedKing.name || UerInfo?.name, // 国王名称
          name: kingdomInputVal && kingdomInputVal.trim() || null, // 王国名称
          descriptions: kingdomInputIntroduceVal || null, // 王国描述
          kingdomCoverUrl: kingdomCoverUrl && kingdomCoverUrl.fileUrl || null, // 王国封面图
          updateUserId: UerInfo?.friUserId, // 操作人用户ID
        }
      }).then((res:any) => {
        if(res && res.code == 200) {
          handler.current?.close()
          const options = { // 保存王国信息
            name: kingdomInputVal && kingdomInputVal.trim(), // 王国名称
            id: res.content, // 王国id
            kingImgUrlShow: kingdomCoverUrl && kingdomCoverUrl.fileUrlView  || selectedKing && selectedKing.headUrlShow || headUrlImg, // 头像
            wxUserId: selectedKing && selectedKing.id || UerInfo?.friUserId, // 国王id
            kingName: selectedKing && selectedKing.name || UerInfo?.name, // 国王
            comeType: 'CreateKingdom', // 从哪来的标识
          }
          // 关闭弹框并跳转创建空间成功页
          dispatch({
            type: 'userInfoStore/setTaskListState',
            payload: {
              createModalVisible: false, // 关闭弹框
              selectedKingdom: options
            }
          })
          localStorage.setItem('kingdomInfoData', JSON.stringify(options))
          // 跳转创建王国成功页
          history.push({
            pathname: '/UserInfo/KingDomSuccessPage',
            query: {
              id: res.content
            }
          });
        } else {
          return Toast.show({content: res.msg || '数据加载失败'})
        }
      }).catch((err:any) => {
        console.log(err)
      })
    }).catch(err => {
      handler.current?.close()
    })
  }

  // 上传图片headers
  const getHeaders=() =>{
    // 获取环境类型，1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用   5 FRIDAY app中使用
    const env = getOperatingEnv()

    // FRIDAY APP中本地存储值：access_token、client、phone、user_name、wxuserId、userInfo
    return {// token
      access_token:localStorage.getItem('access_token') || '',
      username: env == 5 ? localStorage.getItem('user_name') : localStorage.getItem('vxOpenIdCipherText') ? localStorage.getItem('vxOpenIdCipherText')  : UerInfo?.phone,
      client: env == 5 ? localStorage.getItem('client') : 'WX',
      type: env === '1' ? '' : '1', // h5 传1
    }
  }

  // 上传事件
	const onChangeByUpload = (info: any)=>{
    let fileList;
    fileList = info && info.fileList;
    if (info.file.status === 'uploading') {
      setKingdomLoadingByUpload(true)
    }

    // 判断当前上传已被拦截
    if (info && !info.file.status) { fileList = null;return;}
    if (info && info.file.status === 'error') {
      setKingdomLoadingByUpload(false)
      Toast.show({content: '上传失败'});
      fileList = null;
      return
    }
    // 上传结束
    if (info && info.file.status === 'done') {
      setKingdomLoadingByUpload(false)
      if(info && info.file.response && info.file.response.code != 200) {
        Toast.show({content: info.file.response.msg ? info.file.response.msg : '上传失败'});
        fileList = [];
        return
      }
    }

    if (info.file.type === "image/png" || info.file.type === "image/jpeg") {
      if(info.file.response && info.file.response.code== 200 && info.file.response.content) {
        dispatch({
          type: 'userInfoStore/setTaskListState',
          payload: {
            kingdomCoverUrl: info.file.response.content
          }
        })
      }
    }
  }

	// 上传校验规则
	const beforeUpload = (file: { size?: any; type?: any; }) => {
    const isSize = file.size / 1024 / 1024 < 15;
    if (!isSize) {
      Toast.show({content: '超过15M限制，不允许上传~'});
      return false;
    }

    const { name:fileName } = file || {}
    // 添加对文件后缀名的限制
    const suffix = fileName.substring(fileName.lastIndexOf('.')+1)
    const isJpgOrPng = file.type === 'image/jpg' || file.type === 'image/jpeg' ||  file.type === 'image/png';
    // 文件后缀名可以大写,所以需要添加大写后缀名的判断
    const isSuffixByJpgOrPng = (
      suffix === 'jpg'
      || suffix === 'JPG'
      || suffix === 'jpeg'
      || suffix === 'JPEG'
      || suffix === 'png'
      || suffix === 'PNG'
    )
    if (!isJpgOrPng || !isSuffixByJpgOrPng) {
      Toast.show({content: '只能上传JPG、JPEG、PNG格式的图片~'});
      return false;
    }
    return isJpgOrPng;
  };

  // 取消事件函数
  const cancelBtnFn = () => {
    // 关闭弹框并刷新页面
    const {refreshFn} = kingdomFromEnter || {};
    refreshFn && refreshFn();
  }

  // 确定事件
  const okBtnFn = () => {
    // 如果kingdomLoadingByUpload 为true, 说明正在上传封面图中，不能点击，上传完封面后才可以进行点击
    if(kingdomLoadingByUpload) return;
    setIsTipsVisible(true)
  }

  // 二次弹框-确定按钮
  const tipsOkFn = () => {
    Toast.show({icon: 'loading', maskClickable: false})
    dispatch({
      type: 'userInfoStore/editKingdomInfo',
      payload: {
        id: KingdomId, // 王国id
        wxUserId: UerInfo?.friUserId, // 当前登录的用户ID
        name: kingdomInputVal && kingdomInputVal.trim() || null, // 王国名称
        descriptions: kingdomInputIntroduceVal || null, // 王国描述
        kingdomCoverUrl: kingdomCoverUrl && kingdomCoverUrl.fileUrl || null, // 王国封面
        updateUserId: UerInfo?.friUserId,
      }
    }).then((res:any) => {
      if(res && res.code == 200) {
        // 关闭弹框并刷新页面
        dispatch({
          type: 'userInfoStore/setTaskListState',
          payload: {
            createModalVisible: false, // 关闭弹框
            isEditKingdom: false, // 重置编辑状态
          }
        })
        setIsTipsVisible(false); // 关闭当前提示弹框
        Toast.show({content: tipText}) // 提示信息
        refreshFn && refreshFn(); // 刷新页面
        return;
      } else {
        return Toast.show({content: res.msg || '数据加载失败'})
      }
    }).catch((err:any) => {
      console.log(err)
    })
  }

  // 二次弹框-取消按钮
  const tipsCancelFn = () => {
    setIsTipsVisible(false); // 关闭当前提示弹框
  }

  return (
    <div className={styles.container}>
      <div className={styles.title_box}>
        {/* 是否有返回箭头 */}
        {
          isShowGoBack ? '' :
          <div className={styles.title_btn} onClick={goBackHandle}>
            <img src={GoBackIcon} width={12} height={24} alt=""/>
          </div>
        }
        <div className={styles.title}>{title || '创建王国'}</div>
      </div>

      <Form form={form} validateTrigger="onBlur" className={styles.form_container}>
        <div className={styles.form_box}>
          {
            isSuperAccount && !isEditKingdom ?
            <div className={classNames(styles.form_item, styles.form_item_inline)}>
              <div className={styles.label}>国王</div>
              {/* 如果正在上传封面图中，不能点击，上传完成后才可以点击 */}
              <div className={styles.king_value_box} onClick={()=>{if(kingdomLoadingByUpload) return; goBack(6)}}>
                <div className={classNames({
                    [styles.user_name]: true,
                    [styles.user_name_selected]: !!selectedKing
                  })}
                >
                  {selectedKing ?
                    <div className={styles.select_wrap}>
                      { selectedKing.headUrlShow ?
                        <><img src={selectedKing.headUrlShow} alt="" />{selectedKing?.name}</> :
                        <><div className={styles.no_comment_head} style={{background:randomColor(selectedKing?.id)}}><span>{processNames(selectedKing?.name)}</span></div>{selectedKing?.name}</>
                      }
                    </div> :
                    '请选择国王'
                  }
                </div>
                <img src={RightArrowIcon} width={16} height={16} alt=""/>
              </div>
            </div>
            : null
          }

          <div className={classNames(styles.form_item_special, styles.form_item_inline)}>
            <div className={styles.kingdom_form_box}>
              <div className={styles.label}>
                <span className={styles.required_mark}>*</span>
                <span>王国名称</span>
              </div>
              <div className={styles.name_value_box}>
                <Form.Item
                  label=""
                  name="kingdomName"
                  initialValue={kingdomInputVal}
                  rules={[
                    {required: true, message: '请输入王国名称'},
                    {pattern: /^[~\s!！@#$￥%^…&*()_—+{}：:‘’“”《》？"|<>?`\-=[\];',.。，；（）、a-zA-Z0-9\u4e00-\u9fa5/]+$/, message: '王国名称不能包含特殊字符'},
                    {max: 30, message: '输入长度最大为30个字符!'}
                  ]}
                >
                  <Input
                    type="text"
                    autoComplete="off"
                    placeholder="请输入"
                    style={{ '--text-align': 'right' }}
                    onChange={(val) => {setIsVisible(false);setKingdomInputVal(val)}}
                    onBlur={kingdomBlurFn}
                  />
                </Form.Item>

              </div>
            </div>
            {isVisible ? <div className={styles.input_tips}><img src={tipsIcon} alt="" />已被抢先创建,请试试别的名字吧!</div> : null}
          </div>

          <div className={classNames(styles.form_item, styles.form_item_vertical)}>
            <div className={styles.label}>
              <span className={styles.required_mark}>*</span>
              <span>王国介绍</span>
            </div>
            <div className={styles.introduce_value_box}>
              <Form.Item
                label=""
                name="kingdomIntroduce"
                initialValue={kingdomInputIntroduceVal}
                rules={[
                  {required: true, message: '请输入'},
                  {pattern: /^[~\s!！@#$￥%^…&*()_—+{}：:‘’“”《》？"|<>?`\-=[\];',.。，；（）、a-zA-Z0-9\u4e00-\u9fa5/]+$/, message: '王国介绍不能包含特殊字符'},
                ]}
              >
                <TextArea
                  placeholder="可以写一些你希望王国内分享的话题方向，吸引志同道合的人成为国民，参与分享讨论，让王国更加壮大~"
                  rows={3}
                  showCount
                  maxLength={100}
                  onChange={val => {
                    setKingdomInputIntroduceVal(val)
                  }}
                  onBlur={textAreaBlurFn}
                />
              </Form.Item>
            </div>
          </div>

          <div className={styles.upload_kingdom_wrap}>
            <div className={styles.upload_img_title}>王国封面(建议尺寸:200*200)</div>
            <Spin spinning={kingdomLoadingByUpload}>
              <div className={styles.upload_img_content}>
                <div className={styles.upload_box}>
                  {kingdomCoverUrl ? <img className={styles.upload_img} src={kingdomCoverUrl?.fileUrlView} alt="" /> : <div><img className={styles.init_upload_img} src={uploadImg} alt="" /></div>}
                </div>
                <Upload
                  headers={getHeaders()}
                  accept="image/*"
                  action={`/api/server/base/uploadFile?${stringify({ fileType: 16, userId: UerInfo?.friUserId})}`}
                  listType="picture-card"
                  className={styles.edit_head_picture}
                  onChange={(info)=>onChangeByUpload(info)}
                  onRemove={()=>{}}
                  beforeUpload={beforeUpload}
                  showUploadList={false}
                  />
              </div>
            </Spin>
          </div>
        </div>
        <div className={styles.fixed_box}>
          {/* isEditKingdom 是否是编辑王国 */}
          {
            isEditKingdom ?
            <div className={styles.edit_btn_style}>
              <div className={styles.edit_cancel_btn} onClick={cancelBtnFn}>取消</div>
              <div className={styles.edit_ok_btn} onClick={okBtnFn}>确定</div>
            </div>
            :
            <div className={styles.btn_box}>
              <div className={styles.btn} onClick={submitCreateFn}>确认创建</div>
            </div>
          }
          {isEditKingdom ? <CommonConfirmModal isVisible={isTipsVisible} title={'确定修改?'} text={'修改后，王国信息立即更新'} onSubmit={tipsOkFn} onCancel={tipsCancelFn} /> : null}
        </div>
      </Form>
    </div>
  )
}

export default connect(({ userInfoStore, loading }: any) => ({userInfoStore, loading}))(Index)
