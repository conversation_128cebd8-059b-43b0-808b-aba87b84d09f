.space_container {
  position: relative;
  padding: 0 12px 4px;

  &.two {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 0 6.5px 4px;
    .item_box {
      width: 50%;
      min-width: 50%;
      padding: 0 5.5px;
      .cover_img {
        height: 96px;
        .status_box {
          top: 10px;
        }
        .title_in_cover_image {
          font-size: 14px;
          line-height: 21px;
        }
      }
      .space_info_box {
        .space_top {
          min-height: 36px;
        }
        .title {
          font-size: 14px;
          line-height: 21px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2; /* 指定显示三行 */
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }

  .item_box {
    margin-bottom: 16px;
    .cover_img {
      position: relative;
      //background-repeat: no-repeat;
      //background-position: center;
      //background-size: cover;
      //background-image: url("https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/PlanetChatRoom_HorizontalLiveRoom_Live_bg.png");
      overflow: hidden;

      .sign {
        position: absolute;
        top: 0;
        left: 0;
        font-size: 12px;
        font-weight: 400;
        color: #06A777;
        line-height: 17px;
        background: #EEFFF9;
        border-radius: 0px 0px 4px 0px;
        padding: 2px 4px;
      }

      // 用户身份
      .user_role {
        position: absolute;
        bottom: 0;
        left: 0;
        display: block;
        padding: 0 4px;
        height: 21px;
        line-height: 22px;
        border-radius: 2px;
        z-index: 10;
        &.role_host {
          background: #FCE9E8;
          color: #FF5F57;
        }
        &.role_guest {
          background: #EDF9FF;
          color: #0095FF;
        }
      }

      .status_box {
        position: absolute;
        right: 8px;
        background: linear-gradient(135deg, #4183EA 0%, #003AAF 100%);
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: nowrap;
        font-size: 12px;
        color: #FFFFFF;
        padding: 0 8px;
        height: 21px;
        white-space: nowrap;
        z-index: 666;
        .status_icon {
          display: block;
          width: 12px;
          height: 12px;
          margin-right: 2px;
          background-repeat: no-repeat;
          background-position: center;
          &.icon1 {
            background: url("../../../../../assets/GlobalImg/space1.png") no-repeat center;
            background-size: 100% 100%;
          }
          &.icon2 {
            background: url("../../../../../assets/GlobalImg/space2.png") no-repeat center;
            background-size: 100% 100%;
          }
          &.icon3 {
            background: url("../../../../../assets/GlobalImg/space3.png") no-repeat center;
            background-size: 100% 100%;
          }
        }
      }
      .gdp {
        position: absolute;
        bottom: 0;
        right: 0;
        color: #fff;
        font-size: 11px;
        line-height: 15px;
        z-index: 666;
        background: rgba(0,0,0,0.4);
        border-radius: 8px 0px 0px 0px;
        padding: 2px 6px;
      }

      .offshelf_style {
        position: absolute;
        z-index: 666;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #F5F5F5;
        border-radius: 17px;
        font-size: 13px;
        font-weight: 400;
        color: #000000;
        padding: 4px 12px;
        white-space: nowrap;
      }
      // 封面中的标题
      .title_in_cover_image {
        position: absolute;
        z-index: 600;
        width: 56%;
        top: 18px;
        left: 0;
        color: #fff;
        font-weight: 500;
        padding-left: 12px;
        word-break: break-all;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2; /* 指定显示行数 */
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .space_info_box {
      padding-top: 8px;
      .title {
        color: #000000;
        font-weight: 500;
        word-break: break-all;
      }

      .text {
        font-size: 11px;
        font-weight: 400;
        color: #888888;
        line-height: 13px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-top: 4px;
      }
      .footer {
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
        align-items: center;
        margin-top: 12px;
        .footer_left {
          flex: 1;
          display: flex;
          flex-wrap: nowrap;
          align-items: center;
          overflow: hidden;
          .left_avatar {
            width: 23px;
            min-width: 23px;
            height: 23px;
            border-radius: 50%;
            position: relative;
            margin-right: 8px;
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
            font-size: 10px;
            font-weight: 500;
            color: #fff;
            text-align: center;
            line-height: 24px;
            white-space: nowrap;
            & > i {
              position: absolute;
              width: 11px;
              height: 11px;
              top: -3px;
              right: -3px;
              background: url("../../../../../assets/GlobalImg/crown.png") no-repeat center;
              background-size: 100%;
            }
          }
          .left_name {
            flex: 1;
            font-size: 13px;
            color: #666666;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        .footer_right {
          display: flex;
          flex-wrap: nowrap;
          align-items: center;
          .right_avatar {
            width: 23px;
            min-width: 23px;
            height: 23px;
            border: 1px solid #fff;
            border-radius: 50%;
            margin-right: -8px;
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
            font-size: 10px;
            font-weight: 500;
            color: #fff;
            text-align: center;
            line-height: 24px;
            white-space: nowrap;
            &:last-child {
              margin-right: 0;
            }
          }
          .avatar_more {
            width: 23px;
            height: 23px;
            border: 1px solid #fff;
            border-radius: 50%;
            background: #E6F4FF;
            color: #0095FF;
            line-height: 22px;
            text-align: center;
          }
        }
      }
    }
  }
}
.top_wrap{
  display: flex;
  position: relative;
  justify-content: space-between;
  .tab_spaceRoleType_list{
    padding-left: 12px;
    span{
      display: inline-block;
      font-size: 14px;
      color: #666666;
      line-height: 14px;
      padding:12px 0 24px 0;
      margin-right: 6px;
      cursor: pointer;
    }
    .spaceRoleTypeActive{
      color: #0095FF;
    }
  }
  .screen_btn_active{
    color: #0095FF;
  }
}
.mask_box {
  position: absolute;
  top: 100%;
  left: 0;
  height: 100vh;
  .screen_wrap{
    padding-left:12px;
    background: #fff;
    .screen_container{
      //height: 200px;
      //padding-bottom: 16px;
      .tab_spaceStatus_list{
        span{
          display: inline-block;
          font-size: 14px;
          color: #666666;
          background: #F5F5F5;
          line-height: 14px;
          padding: 6px 30px;
          border-radius: 25px;
          margin-right: 16px;
          margin-bottom: 8px;
        }
        .spaceStatusActive{
          color: #0095FF;
          background: #EDF9FF;
        }
      }
      .screen_box{
        .isBizSelectBox{
          margin-top:5px;
          >div{
            display: inline-block;
          }
          >*{
            margin-right: 20px;
          }
        }
      }
      .tab_spaceStatusTitle,.isBizTitle{
        height: 24px;
        line-height: 24px;
        color: #999999;
        font-size: 14px;
        margin-bottom: 5px;
      }
      :global{
        .adm-checkbox.adm-checkbox-checked .adm-checkbox-icon{
          border-color: #0095FF;
          background: #0095FF;
        }
      }
    }
    .screen_wrap_footer{
      width: 100%;
      height: 50px;
      padding-right: 12px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      .screen_wrap_footer_close ,.screen_wrap_footer_confirm{
        flex: 1;
        height: 34px;
        border-radius: 25px;
        font-size: 15px;
        font-weight: 500;
        text-align: center;
        line-height: 34px;
      }
      .screen_wrap_footer_close{
        margin-right: 15px;
        background: #EDF9FF;
        color: #0095FF;
      }
      .screen_wrap_footer_confirm{
        background: #0095FF;
        color: #FFFFFF;
      }
    }
  }
}
.edit_wrap {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;

  .more_box {
    width: 24px;
    height: 24px;
    img {
      width: 24px;
      height: 24px;
    }
  }

  .edit_box {
    font-size: 13px;
    font-weight: 400;
    color: #000000;
    padding: 4px 12px;
    background: #F5F5F5;
    border-radius: 17px 17px 17px 17px;
  }
}
