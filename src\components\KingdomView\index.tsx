/**
 * @Description: 王国组件
 */
import React, { useState } from 'react'
import { history, connect } from 'umi'
import classNames from 'classnames'
import { randomColor , processNames, gdpFormat } from '@/utils/utils'
import { kingdom_click, group_click } from '@/pages/Home/utils'
import { Toast } from 'antd-mobile'
import styles from './index.less'

import QuitKingdomModal from '@/pages/Kingdom/QuitKingdomModal'        // 退出王国提示弹窗

interface PropsType {
  componentData: any,                                      // 组件数据，格式{ dataList: [] }
  style?: any,                                             // 样式
  getPageInfo: any,                                        // 加入王国成功回调，刷新页面数据
  pageType?: string,                                       // 页面类型（99广场）
  isHomePage?: any,                                         // 是否为首页，1是，0否
  moduleIndex?: any,                                        // 当前组件在所有王国组件中的索引
  isClassifyGuide?: any,                                   // 是否在分类导航组件中，1是
  classifyGuideTabIndex?: any,                             // 在分类导航组件中时，选中的tab标签
}

// 按钮显示文案
const hasJoinedText = ['加入', '已加入', '我创建']

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { dispatch, componentData, style = {}, pageType, isHomePage, moduleIndex, isClassifyGuide, classifyGuideTabIndex } = props
  const dataList = componentData.dataList || []

  const [isVisible, setIsVisible] = useState(false)        // 是否退出王国弹窗
  const [currentData, setCurrentData] = useState()         // 当前数据

  // 点击王国跳转到王国详情
  const goToUrl = (item, index) => {
    // 跳转王国详情，传王国Id
    history.push(`/Kingdom/${item.id}`)

    // 首页友盟统计，isHomePage=1
    if (isHomePage != 1) {
      return
    }
    // 在分类导航组件中
    if (isClassifyGuide == 1) {
      // 首页，分类组件点击量
      group_click(moduleIndex, `第${moduleIndex}个分类组件，第${classifyGuideTabIndex}个标签，第${index + 1}个王国`)
      return
    }
    // 首页，王国点击量
    kingdom_click(moduleIndex, index + 1)
  }

  // 加入王国
  const joinKingdom = (e?, item) => {
    e && e.preventDefault()
    e && e.stopPropagation()

    // 没登录跳转登录页
    if (!localStorage.getItem('access_token')) {
      history.push({
        pathname: '/User/login',
        query: {
          redirectByPush: window.location.href,
        }
      })
      return
    }

    // 我创建状态，不能点；已加入状态，在首页不能点
    if (item.hasJoined == 2 || (item.hasJoined == 1 && !pageType)) {
      return
    }

    Toast.show({
      icon: 'loading',
      content: '',
      duration: 0,
    })

    const userInfoStr = localStorage.getItem('userInfo')
    const userInfo = userInfoStr ? JSON.parse(userInfoStr) : {}
    dispatch({
      type: 'activity/joinOrQuitKingdom',
      payload: {
        id: item.id,                                       // 王国ID
        wxUserId: userInfo.friUserId,                             // 用户ID
        type: item.hasJoined == 0 ? 1 : 2,                 // 类型(1加入，2退出)
      }
    }).then(res => {
      Toast.clear()
      const { code, content, msg } = res
      if (code == 200 && content) {
        if (item.hasJoined != 1) {
          // 加入成功
          Toast.show('加入王国成功！')
        } else {
          // 退出成功
          Toast.show('退出王国成功')
        }
        props.getPageInfo && props.getPageInfo(1)
      } else {
        if (item.hasJoined != 1) {
          // 加入成功
          Toast.show(msg || '加入王国失败')
        } else {
          // 退出成功
          Toast.show(msg || '退出王国失败')
        }
      }
    }).catch(err => {
      Toast.clear()
    })
  }

  // 退出王国
  const exitJoinKingdom = (e, item) => {
    e.preventDefault()
    e.stopPropagation()
    // 没登录跳转登录页
    if (!localStorage.getItem('access_token')) {
      history.push({
        pathname: '/User/login',
        query: {
          redirectByPush: window.location.href,
        }
      })
      return
    }
    setCurrentData(item)
    setIsVisible(true)
  }

  // 退出王国-我再想想
  const cancelFn = () => {
    setIsVisible(false)
  }

  // 退出王国-狠心退出
  const submitFn = () => {
    joinKingdom('', currentData)
    setIsVisible(false)
  }

  return (
    <div className={styles.kingdom_container} style={style}>
      {
        dataList.map((item, index) => {
          return (
            <div key={item.id} className={styles.item_box} onClick={() => goToUrl(item, index)}>
              <div className={styles.left_avatar} style={item?.kingdomCoverUrlShow || item.kingImgUrlShow ? {backgroundImage: `url(${item?.kingdomCoverUrlShow || item.kingImgUrlShow})`} : {background: randomColor(item.wxUserId)}}>
                {!item?.kingdomCoverUrlShow && !item.kingImgUrlShow && processNames(item.kingName)}
              </div>
              <div className={styles.right}>
                <div className={styles.details_box}>
                  <div className={styles.title} dangerouslySetInnerHTML={{__html: item.name}}></div>
                  <div className={styles.info_1}>
                    <span className={styles.name}>{item.kingName}</span>
                    <span className={styles.label}>王国创建者</span>
                  </div>
                  <div className={styles.info_2}>
                    <span>{gdpFormat(item.gdp)}GDP</span>
                    <span>{gdpFormat(item.nationalNum)}国民在交流</span>
                    <span>{item.spaceNum}个热议空间</span>
                  </div>
                </div>
                {
                  !localStorage.getItem('access_token') ?
                    <div className={classNames(styles.btn_box)} onClick={() => goToUrl(item, index)}>查看</div>
                    :
                    <div className={classNames(styles.btn_box, {
                      [styles.visited]: item.hasJoined == 1,
                    })} onClick={
                      (e) => pageType && item.hasJoined == 1 ? exitJoinKingdom(e, item) : joinKingdom(e, item)
                    }>
                      {hasJoinedText[item.hasJoined]}
                    </div>
                }

              </div>
            </div>
          )
        })
      }
      {/* 退出王国弹窗 */}
      <QuitKingdomModal visible={isVisible} onCancel={cancelFn} onSubmit={submitFn}/>
    </div>
  )
}

export default connect(({ activity, loading }: any) => ({activity, loading}))(Index)
