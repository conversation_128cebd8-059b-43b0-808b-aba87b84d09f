/**
 * @Description: 图文模块
 */
import {
  deleteImgTextInfo,
  editImgTextInfo,
  getCreateKingdomList,
  getRelationList,
  imgTextInfoById,
  imgTextInfoUpdateId,
  insideShareStarSpace,
  lowUpFrameImgTextInfo,
  ossParameter,
  personImageTextList,
  signaturecom,
} from '@/services/graphicsText'

export default {
  namespace: 'graphicsText',

  state: {
    selectedKingdomId: null,           // 选择的王国的ID
    selectedKingdomName: null,         // 选择的王国的name

    selectedTopicId: null,             // 选择的话题的ID
    selectedTopicName: null,           // 选择的话题的name
    selectTopicType: 2,                // 2 点击工具栏中井号跳转，1 输入井号触发的跳转
  },

  effects: {
    // PC创建图文信息
    * editImgTextInfo({payload}, {call}) {
      const response = yield call(editImgTextInfo, payload)
      return response
    },

    // 关联话题列表
    * getRelationList({payload}, {call}) {
      const response = yield call(getRelationList, payload)
      return response
    },

    // 移动端获取王国列表
    * getCreateKingdomList({payload}, {call}) {
      const response = yield call(getCreateKingdomList, payload)
      return response
    },

    // 图文内容视频上传获取OSS参数
    * ossParameter({payload}, {call}) {
      const response = yield call(ossParameter, payload)
      return response
    },

    // 编辑图文获取原数据
    * imgTextInfoUpdateId({payload}, {call}) {
      const response = yield call(imgTextInfoUpdateId, payload)
      return response
    },

    // 图文详情
    * imgTextInfoById({payload}, {call}) {
      const response = yield call(imgTextInfoById, payload)
      return response
    },

    // 个人中心获取用户图文列表信息
    * personImageTextList({payload}, {call}) {
      const response = yield call(personImageTextList, payload)
      return response
    },

    // 草稿图文删除
    * deleteImgTextInfo({payload}, {call}) {
      const response = yield call(deleteImgTextInfo, payload)
      return response
    },

    // PC图文下架
    * lowUpFrameImgTextInfo({payload}, {call}) {
      const response = yield call(lowUpFrameImgTextInfo, payload)
      return response
    },

    // 图文内容视频上传获取OSS参数
    * signaturecom({payload}, {call}) {
      const response = yield call(signaturecom, payload)
      return response
    },

    // 空间内部分享到广场
    * insideShareStarSpace({payload}, {call}) {
      const response = yield call(insideShareStarSpace, payload)
      return response
    },
  },

  reducers: {
    // 保存数据
    save(state, {payload}) {
      return {
        ...state,
        ...payload,
      }
    },
    // 清除数据
    clean(state, {payload}) {
      return {
        selectedKingdomId: null,           // 选择的王国的ID
        selectedKingdomName: null,         // 选择的王国的name

        selectedTopicId: null,             // 选择的话题的ID
        selectedTopicName: null,           // 选择的话题的name
        selectTopicType: 2,                // 2 点击工具栏中井号跳转，1 输入井号触发的跳转
      }
    }
  },

  subscriptions: {
    setup({dispatch, history}) {
      return history.listen(({pathname, search}) => {
        if (
          pathname.indexOf('/CreateGraphicsText/CreateExternalLinks') == -1 &&
          pathname.indexOf('/CreateGraphicsText/CreatePost') == -1 &&
          pathname.indexOf('/CreateGraphicsText/CreateArticle') == -1 &&
          pathname.indexOf('/CreateGraphicsText/CreateForward') == -1 &&
          pathname.indexOf('/CreateGraphicsText/SelectTopic') == -1 &&
          pathname.indexOf('/CreateGraphicsText/SelectKingdom') == -1
        ) {
          dispatch({
            type: 'clean',
          })
        }
      })
    }
  }
}
