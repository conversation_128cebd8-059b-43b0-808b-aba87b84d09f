/**
 * @Description: PC端，创建空间成功提示弹窗
 */
import React from 'react'
import { history } from 'umi'
import { Button, Modal } from 'antd'
import styles from './index.less'

import successImage from '@/assets/GlobalImg/success.png'

const Index: React.FC = (props: any) => {

  const { visible, spaceId,starSpaceType,editId } = props
  // 类型：1 直播，2 会议
  let starSpaceTypeText= starSpaceType == 2 ? '会议' : '直播'
  let editIdText = editId? '编辑' : '创建'

  // 进入空间
  const goToUrl = () => {
    if (starSpaceType == 2) {
      history.replace(`/PlanetChatRoom/Meet/${spaceId}`)
    } else {
      history.replace(`/PlanetChatRoom/Live/${spaceId}`)
    }
  }

  return (
    <Modal
      visible={visible}
      className={styles.modal}
      title={`${editIdText}成功`}
      onCancel={props.onCancel}
      destroyOnClose
      footer={null}
      width={469}
    >
      <div className={styles.content}>
        <div className={styles.image_wrap}>
          <img src={successImage} width={72} height={72} alt=""/>
        </div>
        <div className={styles.message}>恭喜您成功{editIdText}{starSpaceTypeText}!</div>
        <div className={styles.tips}>快分享给小伙伴们一起来观看吧~</div>
      </div>
      <div className={styles.footer}>
        <Button onClick={goToUrl}>进入{starSpaceTypeText}</Button>
        <Button type="primary" onClick={props.handleGetPoster}>生成海报</Button>
      </div>
    </Modal>
  )
}

export default Index
