/**
 * @Description: pc端-个人中心-编辑账户
 */
import { useCallback, useEffect, useState } from "react";
import { connect } from "umi";
import styles from './index.less';
import classNames from "classnames";
import { stringify } from "qs";
import { Input, Modal, Spin, Upload, Form, message } from "antd";
import { getOperatingEnv, processNames, randomColor } from "@/utils/utils";

const editIcon = 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/edit.png'; // 编辑小图标

const Index = (props: any) => {
  const { userInfo, open, dispatch } = props;
  const [form] = Form.useForm();
  const [userName, setUserName] = useState(userInfo?.name ?? ''); // 姓名
	const [userNickName, setUserNickName] = useState( userInfo?.nickname ?? ''); // 昵称
  const [isEditName, setIsEditName] = useState(false); // 编辑姓名
  const [isEditNickname, setIsEditNickname] = useState(false); // 编辑昵称
  const [loadingByUpload, setLoadingByUpload] = useState(false);  // loading
  const [fileListByState, setFileListByState] = useState<{fileUrl?: string;fileUrlView?: string;}>(); // 头像
  const UerInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  // 如果父组件里的名字改变，进行同步，弹框打开时也需要同步，覆盖上次修改中的内容
  useEffect(() => {
    setUserName(userInfo?.name);
    setUserNickName(userInfo?.nickName);
  }, [userInfo?.name, userInfo?.nickName, open])

  // 上传图片headers
  const getHeaders=() =>{
    return {// token
      access_token: localStorage.getItem('access_token'),
      username: (localStorage.getItem('vxOpenIdCipherText')
        ? localStorage.getItem('vxOpenIdCipherText')
        : UerInfo?.phone) || '',
      client: 'WX',
      type: getOperatingEnv() === '1' ? '' : '1', // h5 传1
    }
  }

	// 上传事件
	const onChangeByUpload = (info)=>{
    let fileList;
    fileList = info && info.fileList;
    if (info.file.status === 'uploading') {
      setLoadingByUpload(true);
      return;
    }

    // 判断当前上传已被拦截
    if (info && !info.file.status) { fileList = null;return;}
    if (info && info.file.status === 'error') {
      setLoadingByUpload(false);
      message.error('上传失败');fileList = null;return
    }
    if (info && info.file.status === 'done') {
      setLoadingByUpload(false);
      if(info && info.file.response && info.file.response.code != 200) {
        message.error(info.file.response.msg ? info.file.response.msg : '上传失败')
        fileList = [];
        return
      }
    }

    if (info.file.type === "image/png" || info.file.type === "image/jpeg" || info.file.type === "image/gif") {
      if(info.file.response && info.file.response.code== 200 && info.file.response.content) {
        setFileListByState(info.file.response.content)
      }
    }
  }

	// 上传校验规则
	const beforeUpload = (file) => {
    const isSize = file.size / 1024 / 1024 < 15;
    if (!isSize) {
      message.error('超过15M限制，不允许上传~');
      return false;
    }

    const { name:fileName } = file || {}
    // 添加对文件后缀名的限制
    const suffix = fileName.substring(fileName.lastIndexOf('.')+1)
    const isJpgOrPng = file.type === 'image/jpg' || file.type === 'image/jpeg' ||  file.type === 'image/png';
    // 文件后缀名可以大写,所以需要添加大写后缀名的判断
    const isSuffixByJpgOrPng = (
      suffix === 'jpg'
      || suffix === 'JPG'
      || suffix === 'jpeg'
      || suffix === 'JPEG'
      || suffix === 'png'
      || suffix === 'PNG'
    )
    if (!isJpgOrPng || !isSuffixByJpgOrPng) {
      message.error('只能上传JPG 、JPEG  、PNG 格式的图片~');
      return false;
    }
    return isJpgOrPng;
  };

  // 保存-修改用户信息
  const confirmEditAccountHandle = async () => {
    await form.validateFields();
    dispatch({
      type: 'userInfoStore/editUserinfo',
      payload: {
        headUrl: fileListByState && fileListByState.fileUrl || undefined, // 上传头像短路径
        name: userName || undefined, // 名字
        nickName: userNickName || '', // 昵称
      }
    }).then(res => {
      const { code } = res || {};
      if(code == 200) {
        message.success('保存成功')
        props.updataInitData(true);
      }
    }).catch(err=>{
      message.error(err.msg);
    })
  }

	// 取消
	const cancelEditAccountHandle = () => {
    props.updataInitData();
    setIsEditNickname(false)
    setIsEditName(false)
	}

  // 编辑昵称
  const editNicknameHandle = useCallback(() => {
    setIsEditNickname(true);
    form.setFieldValue('nickname', userNickName);
  }, [form, userNickName])

  const nicknameBlurHandle = useCallback(async () => {
    const values = await form.validateFields(['nickname'])
    // 如果能执行，说明是返回resolved
    setIsEditNickname(false)
    setUserNickName(values.nickname)
  }, [form])

  // 编辑姓名
  const editNameHandle = useCallback(() => {
    setIsEditName(true);
    form.setFieldValue('name', userName);
  }, [form, userName])

  const nameBlurHandle = useCallback(async () => {
    const values = await form.validateFields(['name'])
    // 如果能执行，说明是返回resolved
    setIsEditName(false)
    setUserName(values.name)
  }, [form])

  // 弹框打开时，把上次上传的文件状态清除
  useEffect(() => {
    setFileListByState(void 0);
  }, [open])

  const { id, name, nickName, phone, headUrlShow } = userInfo || {};

  return (
    <Modal
      open={open}
      title='编辑账户'
      wrapClassName={styles.edit_account_modal}
      onCancel={cancelEditAccountHandle}
      onOk={confirmEditAccountHandle}
      width={474}
      centered={true}
    >
      <Form form={form}>
        <ul className={styles.content}>
          <li className={classNames(styles.list_item, styles.head_item)}>
            <span>我的头像</span>
            <Spin spinning={loadingByUpload}>
              <div className={styles.item_HeadPicture}>
                {
                  (fileListByState && fileListByState.fileUrlView) ? <img src={fileListByState.fileUrlView} alt="" /> : headUrlShow ? <img src={headUrlShow} alt="" /> :
                  <div className={styles.head_sculpture_name} style={{background: name || nickName ? randomColor(id) : "none"}}>
                    { processNames(nickName || name) }
                  </div>
                }
              </div>
              <Upload
                headers={getHeaders()}
                accept="image/*"
                action={`/api/server/base/uploadFile?${stringify({ fileType: 1, userId: UerInfo?.friUserId})}`}
                listType="picture-card"
                className={styles.edit_head_picture}
                onChange={onChangeByUpload}
                onRemove={()=>{}}
                beforeUpload={beforeUpload}
                showUploadList={false}
              />
            </Spin>
          </li>
          <li className={styles.list_item}>
            <span>昵称</span>
            <span>
              {
                isEditNickname || !userNickName ?
                <Form.Item
                  className={styles.edit_form_item}
                  style={{right: isEditNickname || !userNickName ? 5 : 23}}
                  name='nickname'
                  rules={[{ pattern: /^[\u4E00-\u9FA5A-Za-z0-9]*$/, message: '仅支持中、英文和数字!'}]}
                >
                  <Input type="text" autoComplete="off" bordered={false} max={12}
                    className={styles.edit_input}
                    placeholder='请输入昵称'
                    onBlur={nicknameBlurHandle}
                  /></Form.Item>
                : <span>{userNickName}</span>
              }
              {!isEditNickname && userNickName && <img src={editIcon} alt="" className={styles.edit_img} onClick={editNicknameHandle} />}
            </span>
          </li>
          <li className={styles.list_item}>
            <span>姓名</span>
            <span>
              {
                isEditName ?
                <Form.Item className={styles.edit_form_item}
                  style={{right: isEditName ? 5 : 23}}
                  name='name'
                  rules={[{ pattern: /^[\u4E00-\u9FA5A-Za-z]*$/, message: '昵称仅支持中、英文!'}]}
                >
                  <Input type="text" autoComplete="off" bordered={false}
                    className={styles.edit_input} max={32}
                    placeholder='请输入姓名'
                    onBlur={nameBlurHandle}
                  /></Form.Item>
                : <span>{userName}</span>
              }
              {!isEditName &&<img src={editIcon} alt="" className={styles.edit_img} onClick={editNameHandle} />}
            </span>
            </li>
          <li className={styles.list_item}>
            <span>手机号</span>
            <span>{phone}</span>
            </li>
        </ul>
      </Form>
    </Modal>
  )
}
export default connect(({ userInfoStore, loading }: any) => ({ userInfoStore, loading}))(Index)
