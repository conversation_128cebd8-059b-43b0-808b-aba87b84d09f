import React, { useState, useEffect } from 'react';
import { connect, history } from 'umi';
import { Form, Input, Spin, message, Button, Modal } from 'antd';
import { Toast } from 'antd-mobile';
import styles from './index.less';
import { debounce } from 'lodash';
import loginIcon from '@/assets/GlobalImg/logo.png';
import wechat_logo from '@/assets/GlobalImg/wechat.png'
const loginBg = 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/logoBg.png';
import goBackPc from '@/assets/GlobalImg/pc_goback.png';
import {
  getRegisterMsgCode,  // 注册验证码
  getMsgCodeUserInfo,
  signUp,       // 注册
  signUpByArrailToUc, // 瑞尔转星球子系统的注册
  imgCode,      // 获取图片验证码
} from "@/services/login/login";
import {
  signUpByWxWorkToUc, // 企微授权注册医生星球账号
  wxWorkLoginUserToken
} from "@/services/common/api"
import NavBar from '@/components/NavBar';
import {getOperatingEnv, getAES, getWechatAuth} from '@/utils/utils';

// 隐私政策图片列表
const agreementImgList = [
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/Agreement/pc_agreement_1.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/Agreement/pc_agreement_2.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/Agreement/pc_agreement_3.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/Agreement/pc_agreement_4.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/Agreement/pc_agreement_5.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/Agreement/pc_agreement_6.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/Agreement/pc_agreement_7.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/Agreement/pc_agreement_8.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/Agreement/pc_agreement_9.png',
]

// 第三方登录list
const TripartiteList =[
  {
    key:1,
    name:'微信',
    icon:wechat_logo,
  },
]

const Index: React.FC = (props: any) => {
  const [form] = Form.useForm();
  const [countdown, setCountdown] = useState(0); // 验证码状态
  const pageKeyByRendom = Math.random().toString(36).substr(2, 8); // pageKey 当前页面唯一标识
  const [imgCodeUrl, setImgCodeUrl] = useState(''); // 图片验证码地址
  const [pageKey, setPageKey] = useState(pageKeyByRendom); // 页面pageKey
  const [loading, setLoading] = useState(false);  // loading
  const [loadingBySendCode, setLoadingBySendCode] = useState(false); // 添加发送验证码loading
  const {query = {}} = history?.location;
  const {inputPhone,arrailToUcKey, weComToUcKey} = query;
  const isPhoneVisible = /^1[3-9]\d{9}$/.test(inputPhone);
  const [visible, setVisible] = useState(false); // 隐私政策弹框打开状态

  const [pageType, setPageType] = useState(); // 1pc 2 移动端
  // ① 判定当前页面视口是否小于750 如果小于750则为移动端
  let updateType = () => {
    // let clientWidth = document.documentElement.clientWidth;
    let env = getOperatingEnv() //  1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用
    let type = env == 4 ? 1 :env == 7? 7: 2;
    setPageType(type);
  };
  updateType = debounce(updateType, 100);
  window.addEventListener('resize', updateType);
  // 进入页面判定是否存在token
  useEffect(() => {
    updateType();
  }, []);

  // 页面打开生成pageKey
  useEffect(() => {
    let loginDataByform = sessionStorage.getItem('registDataByform')
    if(!!loginDataByform) {
      // 回显保存的临时表单数据
      setDataBySessionStorage();
    }else {
      // 获取图片验证码
      getImgCode();
    }
    sessionStorage.removeItem('orderId');
  }, []);

  // 获取图形验证码 或 切换图形验证码
  const getImgCode = async ()=>{
    let DataByImgCode = await imgCode({
      pageKey: pageKey,
    },{
      responseType:'blob',
    });
    if (DataByImgCode && !DataByImgCode.status) {
      const reader = new FileReader();
      reader.readAsDataURL(DataByImgCode);
      reader.onloadend = function () {
        const base64data = reader.result;
        setImgCodeUrl(base64data);
      };
    }
  }

  // 保存页面的临时数据
  const saveDataBySessionStorage=()=>{
    let phone     = form.getFieldValue('phone');
    let imgCode   = form.getFieldValue('imgCode');
    let phoneCode = form.getFieldValue('phoneCode');
    let name     = form.getFieldValue('userName');

    let loginDataByform = {
      phone:phone,
      imgCode: imgCode,
      phoneCode: phoneCode,
      name: name,
      imgCodeUrl: imgCodeUrl,
      pageKey: pageKey,
    }
    sessionStorage.setItem('registDataByform',JSON.stringify(loginDataByform));
  }

  // 回显原表单数据
  const setDataBySessionStorage=()=>{
    let loginDataByform = sessionStorage.getItem('registDataByform')
    if(!!loginDataByform){
      loginDataByform = JSON.parse(loginDataByform);
      const {
        phone:phoneByfrom,
        imgCode:imgCodeByfrom,
        phoneCode:phoneCodeByfrom,
        name: userNameByform,
        imgCodeUrl:imgCodeUrlByfrom,
        pageKey:pageKeyByfrom,
        password:passwordByform,
      } = loginDataByform || {}
      form && form.setFieldsValue({
        phone:phoneByfrom,
        imgCode:imgCodeByfrom,
        phoneCode:phoneCodeByfrom,
        userName:userNameByform,
        password:passwordByform,
      })
      setImgCodeUrl(imgCodeUrlByfrom);
      setPageKey(pageKeyByfrom);
      let loginDataByformArraykeys = Object.keys(loginDataByform)
      let filterByKeys = loginDataByformArraykeys.filter((item)=>{return !!loginDataByform[item]})
      form && form.validateFields(filterByKeys)

      sessionStorage.removeItem('registDataByform');
    }
  }

  // 发送验证码并开始倒计时
  const sendCode = async () => {
    const phone = form.getFieldValue('phone');
    // 获取图形验证码
    const imgCode = form.getFieldValue('imgCode');
    if (!phone) {
      pageType == 1 ? message.error('请输入手机号') : Toast.show({content: '请输入手机号'})
      return;
    }
    if (!imgCode) {
      pageType == 1 ? message.error('请输入图形验证码') : Toast.show({content: '请输入图形验证码'})
      return;
    }
    if(!(/^1[3456789]\d{9}$/.test(phone))) {
      pageType == 1 ? message.error('请输入正确手机号') : Toast.show({content: '请输入正确手机号'})
      return;
    }

    setLoadingBySendCode(true); // 开启发送验证码loading
    try {
      // 发送验证码请求
      const res = await getRegisterMsgCode({
        phone: phone,
        imgCode:imgCode,
        pageKey: pageKey,
      });

      if (res.code === 200 && res.content) {
        setLoadingBySendCode(false); // 关闭发送验证码loading
        pageType == 1 ? message.success('验证码发送成功') : Toast.show({content: '验证码发送成功'})

        // 开始倒计时 60s
        let time = 60;
        setCountdown(time);
        const timer = setInterval(() => {
          time--;
          setCountdown(time);
          if (time === 0) {
            clearInterval(timer);
          }
        }, 1000);

      } else {
        setLoadingBySendCode(false); // 关闭发送验证码loading
        pageType == 1 ? message.error(res.msg) : Toast.show({content: res.msg})
      }
    } catch (error) {
      setLoadingBySendCode(false); // 关闭发送验证码loading
      pageType == 1 ? message.error('验证码发送失败') : Toast.show({content: '验证码发送失败'})
    }
  }

  // form注册按钮
  const onFinish = debounce(async (values) => {
    const {
      phone,     // 手机号
      phoneCode, // 验证码
      imgCode,   // 校验图形验证码
      userName,  // 姓名
      password,  // 密码
    } = values;

    // 加密下密码
    const AgetES = getAES(password, 'arrail-dentail&2', 'arrail-dentail&3');

    // 开启loading
    setLoading(true);

    // 请求注册参数
    let paramsBySignUp = {
      phone: phone, // 手机号
      phoneCode: phoneCode, // 短信验证码
      pageKey: pageKey, // 页面key(8位)
      imgCode: imgCode, // 图形验证码
      name: userName, // 姓名
      password: AgetES, // 密码
      infoSource: getOperatingEnv() == '4' ? '4' : '3', // 用户信息来源，1:小程序；2:运营；3:H5；4:WEB；5:org-web；6:org-app
    }
    let res = {};
    if (!!arrailToUcKey) {
      // 瑞尔转星球子系统的注册
      res = await signUpByArrailToUc({
        ...paramsBySignUp,
        arrailToUcKey:arrailToUcKey,
      });
    }else if(!!weComToUcKey) {
      // 企微转星球子系统的注册
      res = await signUpByWxWorkToUc({
        ...paramsBySignUp,
        weComToUcKey:weComToUcKey,
      });
    } else {
      // 注册请求
      res = await signUp(paramsBySignUp);
    }

    // 注册成功
    if (res.code === 200 && res.content) {
      if (!!weComToUcKey) {
        // 登录请求
        const res = await wxWorkLoginUserToken({
          weComToUcKey
        });
        // 登录成功
        if (res.code === 200) {
          // 保存token
          localStorage.setItem('access_token', res.content.access_token);
          localStorage.setItem('userInfo', JSON.stringify({
            phone: res.content.username
          }));
          // 登录成功后获取用户信息
          getMsgCodeUserInfo_func(res.content.username);
        } else {
          setLoading(false) // 关闭登录loading
          pageType == 1 ? message.error(res.msg) : Toast.show({content: res.msg})
        }
      }else{
        setLoading(false) // 关闭登录loading
        pageType == 1 ? message.success('注册成功,请返回登录页进行登录操作!') : Toast.show({content: '注册成功，请返回登录页进行登录!'});
        // 重定向到指定页面
        const { redirect } = history.location.query;
        if (!!redirect) {
          window.location.replace(redirect);
        } else {
          // 去登录页面
          history.replace('/User/login')
        }
      }
    } else {
      setLoading(false) // 关闭登录loading
      pageType == 1 ? message.error(res.msg) : Toast.show({content: res.msg})
    }
  },1000)

  // 登录后获取用户信息
  const getMsgCodeUserInfo_func = async (mobile) => {
    try {
      let token_text = localStorage.getItem('access_token');
      let userInfo = await getMsgCodeUserInfo({ token: token_text, username: mobile});
      if (userInfo?.code === 200) {
        setLoading(false) // 关闭登录loading
        // 保存用户信息
        localStorage.setItem('userInfo', JSON.stringify({
          ...userInfo.content,
          id: userInfo?.content?.friUserId
        }));
        // 重定向到指定页面
        const { redirect } = history.location.query;
        if (!!redirect) {
          window.location.replace(redirect);
        } else {
          // PC端
          if (pageType == 1) {
            history.replace('/home')
          } else {
            history.replace('/Square')
          }
        }
      }
    } catch (error) {
      setLoading(false) // 关闭登录loading
      console.error('获取用户信息失败：', error);
    }
  };


  // 渲染验证码倒计时
  const renderCountdown = () => {
    if (countdown > 0) {
      return `${countdown}`;
    }
    return '获取验证码';
  };

  // pc端返回首页事件
  const pcGoHomeFn = () => {
    history.push('/home')
  }

  // h5跳转政策协议页面
  const h5GoAgreementFn = () => {
    saveDataBySessionStorage();
    history.push('/UserInfo/Agreement');
  }

  // pc打开隐私政策协议
  const pcOpenAgreement = () => {
    setVisible(true)
  }

  // 第三方登录方法
  const tripartiteLoginFn =(type) => {
    if(type==1){
      getWechatAuth()
    }
  }

  return pageType ? (
    <div className={pageType == 1 ? styles.pc_login_wrap : styles.login_wrap}>
      <div className={styles.pc_gohome} onClick={pcGoHomeFn}><img src={goBackPc} alt=""/>返回首页</div>

      <div className={styles.login_bg_wrap}>
        <NavBar title={'注册'} className={styles.registerNavBar}></NavBar>
        <div className={styles.login_bg_box}><img src={loginBg} alt="" /></div>
        <div className={styles.login_content_title}>
          <div className={styles.login_img}><img src={loginIcon} alt="" /></div>
          <div className={styles.login_text}>
            欢迎注册医生星球
          </div>
        </div>
        <Spin spinning={!!loading}>
          <div className={styles.login_form_wrap}>
            <Form form={form} onFinish={onFinish}>
              <div className={styles.login_form_input}>
                <Form.Item
                  label=""
                  name="userName"
                  rules={[
                    {required: true, message: '请输入真实姓名'}
                  ]}
                >
                  <Input autoComplete="off" bordered={false} placeholder='请输入真实姓名'/>
                </Form.Item>
              </div>
              <div className={styles.login_form_input}>
                <Form.Item
                  label=""
                  name="phone"
                  initialValue={isPhoneVisible ? inputPhone : undefined}
                  rules={[
                    {required: true, message: '请输入手机号'},
                    {pattern: /^1[3-9]\d{9}$/, message: '请输入正确的联系电话'}
                  ]}
                >
                  <Input bordered={false} disabled={isPhoneVisible && pageType == 7} autoComplete="off"
                         placeholder='请输入手机号'/>
                </Form.Item>
              </div>
              {/* 图形验证码 */}
              <div className={styles.login_form_input1}>
                <Form.Item
                  name="imgCode"
                  className={styles.img_code}
                  rules={[
                    {
                      required: true,
                      message: '请输入图形验证码',
                    }
                  ]}
                >
                  <Input bordered={false} autoComplete="off" max={10} placeholder={'请输入图形验证码'}></Input>
                </Form.Item>
                <div className={styles.verification_code} onClick={() => {
                  getImgCode();
                }}>
                  <img src={imgCodeUrl}></img>
                </div>
              </div>
              <div className={styles.login_form_input2}>
                <Form.Item
                  label=""
                  name="phoneCode"
                  className={styles.phone_code}
                  rules={[
                    {required: true, message: '请输入验证码'}
                  ]}
                >
                  <Input bordered={false} autoComplete="off" maxLength={6} placeholder='请输入验证码'/>
                </Form.Item>
                <Spin spinning={!!loadingBySendCode}>
                  <div className={styles.sendCode} onClick={() => {
                    if (countdown > 0) {
                      return;
                    }
                    sendCode()
                  }}>
                    {renderCountdown()}
                  </div>
                </Spin>
              </div>
              <div className={styles.login_form_input}>
                <Form.Item
                  label=""
                  name="password"
                  rules={[
                    {
                      pattern: /^[a-zA-Z0-9\s~`!@#$%^&*()_+={}\[\]|\\:;"'<>,.?/\-]{6,20}$/,
                      message: '请输入6-20位密码，可支持字母、数字、基本符号'
                    },
                  ]}
                >
                  <Input autoComplete="off" bordered={false} placeholder='请设置6-20位密码'/>
                </Form.Item>
              </div>

              <div className={styles.agreement}>
                注册即表示您已阅读并同意
                <span onClick={() => {
                  pageType == 1 ? pcOpenAgreement() : h5GoAgreementFn()
                }}>《隐私政策》</span>
              </div>
              <Button className={styles.login_Btn} htmlType='submit' loading={loading}>
                {
                  (!!weComToUcKey) ? '注册并绑定' : '注册'
                }
              </Button>
            </Form>
            {
              (getOperatingEnv() != 3&&getOperatingEnv()!=6) && <div className={styles.login_TripartiteList}>
                {
                  TripartiteList.map((item, index) => {
                    return <img key={index} src={item.icon} onClick={() => {
                      tripartiteLoginFn(item.key)
                    }}></img>
                  })
                }
              </div>
            }
            <div className={styles.login_go}>
              已有账号?<span onClick={() => {
              history.replace('/User/login')
            }}>立即登录</span>
            </div>
          </div>
        </Spin>
      </div>
      <Modal
        title="隐私政策"
        centered
        footer={null}
        open={visible}
        onCancel={() => setVisible(false)}
        width={1100}
      >
        <div className={styles.agreementImgWrap}>
          {
            agreementImgList && agreementImgList.map((item, ind) => {
              return <div key={ind} className={styles.agreementImgBox}><img width={'100%'} src={item} alt="" /></div>
            })
          }
        </div>
      </Modal>
    </div>
  ) : null
}
export default connect(({ activity, loading }: any) => ({activity, loading}))(Index)
