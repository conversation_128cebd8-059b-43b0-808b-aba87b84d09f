import React, { useState,useEffect } from 'react';
import { history,connect } from 'umi';
import styles from "@/pages/PaymentByConsultation/MyConsultationDetails/index.less";
import {getOperatingEnv} from "@/utils/utils";
import classNames from "classnames";
import {fontSize} from "html2canvas/dist/types/css/property-descriptors/font-size";

// 支付订单
const OrderPayment: React.FC = (props) => {

  // 微信浏览器使用
  let OperatingEnv = getOperatingEnv();

  return (
    <div className={styles.Mobile_Wrap}>
      {OperatingEnv != 1 &&  // 当前是小程序端
        <div className={styles.Mobile_title_statusbar}></div>
      }

      {OperatingEnv != 1 && OperatingEnv != 2 &&  // 当前是小程序端
        <div className={styles.Mobile_title_Wrap}>
          <div className={styles.Mobile_title}>支付订单</div>
        </div>
      }

      <div>
        <div></div>
        <div></div>
        <div></div>
      </div>

      <div>
        <div></div>
        <div></div>
      </div>

      <div className={styles.Mobile_box_bottom_warp}>
        <div className={styles.Mobile_box}>
          <div className={styles.Mobile_text}>实付: </div>
          <div className={styles.Mobile_price}><span>¥</span><span style={{fontSize:24}}>700</span></div>
        </div>
        <div>
          <div className={styles.Mobile_pay}>立即支付</div>
        </div>
      </div>
    </div>
  )
}
export default connect(({ login, loading }: any) => ({
  login, loading
}))(OrderPayment)
