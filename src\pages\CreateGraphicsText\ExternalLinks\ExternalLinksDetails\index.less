.spin {
  height: 100%;
  :global {
    .ant-spin-container {
      height: 100%;
    }
  }
}

.container {
  padding-top: 68px;
  padding-bottom: 52px;
  height: 100%;
  overflow-y: auto;
}

.user_card_wrap {
  padding: 0 16px;
}

.image_text_content_wrap {
  padding: 12px 16px 0;
  margin-bottom: 8px;
  .image_title {
    font-size: 14px;
    color: #000;
    line-height: 20px;
    word-break: break-all;
    margin-bottom: 8px;
  }
  .no_cover_wrap {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    border: 1px solid #EBEBEB;
    border-radius: 4px;
    padding: 8px;
    .left {
      flex-shrink: 0;
      margin-right: 8px;
    }
    .right {
      flex: 1;
      overflow: hidden;
      .title_wrap {
        font-size: 13px;
        color: #666;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 18px;
        margin-bottom: 3px;
      }
      .btn_wrap_no_wrap {
        height: 18px;
        display: flex;
        align-items: center;
        font-size: 13px;
        color: #0095FF;
        img {
          margin-right: 4px;
        }
      }
    }
  }
  .cover_image {
    width: 100%;
    height: 142px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
  }
  .btn_wrap {
    height: 34px;
    display: flex;
    align-items: center;
    padding-left: 4px;
    background: #F5F6F8;
    border-radius: 0 0 4px 4px;
    font-size: 13px;
    color: #0095FF;
    img {
      margin-right: 4px;
    }
  }
}

.kingdom_wrap {
  padding: 0 16px;
  margin-bottom: 11px;
}

.no_data_wrap {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-y: auto;
}
