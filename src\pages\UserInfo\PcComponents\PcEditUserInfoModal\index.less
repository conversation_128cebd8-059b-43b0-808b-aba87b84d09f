.edit_account_modal {
  
  :global {
    .ant-modal-header {
      padding: 15px;
    }

    .ant-modal-title {
      font-size: 18px;
    }
  }

  .content {
    padding: 0;
    margin: 0;
    color: #606266;

    .list_item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      height: 54px;
      padding: 0 16px;
      &.head_item {
        height: 58px;
        line-height: 58px;
      }
      .edit_form_item {
        position: absolute;
        top: 10px;
        right: 0;
        :global {
          .ant-form-item-explain {
            font-size: 12px;
            padding-right: 11px;
            text-align: right;
          }
        }
      }
      .item_HeadPicture {
        display: flex;
        overflow: hidden;
        width: 58px;
        height: 58px;
        border-radius: 50%;

        .head_sculpture_name {
          width: 58px;
          height: 58px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 20px;
          font-weight: 500;
        }

        img {
          width: 58px;
          height: 58px;
          border-radius: 50%;
        }
      }
      .edit_head_picture {
        position: absolute;
        top: 0;
        left: 0;
        height: 58px;
        width: 58px;
        opacity: 0;

        :global {
          .ant-upload.ant-upload-select-picture-card {
            height: 58px;
            width: 58px;
            margin: 0;
            border: 0;
            border-radius: 50%;
            display: block !important;
          }
        }
      }
      .edit_img {
        width: 12px;
        margin-left: 6px;
        cursor: pointer;
      }
      .edit_input {
        text-align: right;
        color: #606266;
      }
    }
  }
  :global {
    .ant-modal-footer {
      border-top: 0;
    }
    .ant-modal-header {
      border-bottom: 0;
    }
    .ant-modal-body {
      padding: 0;
    }
  }
}