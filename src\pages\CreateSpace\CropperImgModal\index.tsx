/**
 * @Description: h5图片裁剪弹窗
 */
import React, {useRef, useState} from 'react'
import Cropper from 'react-cropper';             // 裁剪工具
import request from '@/utils/request'
import { Mask, Toast, Popup } from 'antd-mobile';
import 'cropperjs/dist/cropper.css';
import styles from './index.less'
import NavBar from "@/components/NavBar";
import {connect} from "umi";
import {stringify} from "qs";
import {Upload, message, Spin} from 'antd'
import {getOperatingEnv} from "@/utils/utils";
import GoBackIcon from "@/assets/GlobalImg/go_back.png";
interface PropsType {
  visible: boolean,                    // 弹窗是否显示
  imageUrlShow: string,                // 图片地址
  handleCroppingImage: () => {},       // 裁剪完成回调
  onCancel: () => {},                  // 关闭弹窗
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  let {
    visible,
    userInfoStore,
  } = props
  const { dispatch,createModalVisible } = props;
  const [ loadingByUpload, setLoadingByUpload] = useState(false);

  let {
    spaceCoverUrl,
    spaceCoverUrlView,
    openCropperImgModal,
  } = userInfoStore || {};
  const UerInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');

  const cropperRef = useRef<HTMLImageElement>(null);       // 裁剪组件ref
  // imageUrlShow = "https://js.5i5ya.com/dmpImgText/test/imgTextContent/64a1c20c64ff4c13803c0d3d6eace867.jpeg";
  // 重置图片
  const resetImage = () => {
    cropperRef.current?.cropper.reset()
  }
  // 裁剪确定事件
  const ok = () => {
    // getCroppedCanvas() 获取裁剪后的内容 base64
    const cropUrl = cropperRef?.current?.cropper?.getCroppedCanvas().toDataURL();
    // 上传图片，将base64文件上传获取裁剪后图片的url
    uploadImg(cropUrl)
  }

  // 上传图片获取链接
  const uploadImg = (cropUrl) => {
    Toast.show({
      icon: 'loading',
      content: '',
      duration: 0,
    })
    // base64转文件
    const imgFile = dataURLtoFile(cropUrl, 'cropping_image')
    let formData = new FormData()
    formData.append('file',imgFile)
    request(`/api/server/base/uploadFile?${stringify({ fileType: 15, userId: UerInfo?.friUserId})}`, {
      method: 'POST',
      body: formData,
    }).then(res => {
      Toast.clear()
      const { code, content, msg } = res
      if (code == 200 && content) {
        dispatch({
          type: 'userInfoStore/setTaskListState',
          payload: {
            createModalVisible: false,
            openCropperImgModal: false,
            spaceCoverUrl: { fileUrlView: content.fileUrlView, fileUrl: content.fileUrl },
            spaceCoverUrlView: content.fileUrlView,
          }
        })
      } else {
        Toast.show(msg || '数据加载失败')
      }
    })
  }

  // base64图片转文件
  const dataURLtoFile = (dataurl, fileName) => {
    let arr = dataurl.split(','),
      mime = arr[0].match(/:(.*?);/)[1],
      bstr = atob(arr[1]),
      n = bstr.length,
      u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    const suffix = (mime == 'image/png' ? '.png' : '.jpg')
    return new File([u8arr], fileName + suffix, { type: mime });
  }

  // 上传图片headers
  const getHeaders=() =>{
    // 获取环境类型，1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用   5 FRIDAY app中使用
    const env = getOperatingEnv()
    // FRIDAY APP中本地存储值：access_token、client、phone、user_name、wxuserId、userInfo
    return {// token
      access_token:localStorage.getItem('access_token') || '',
      username: env == 5 ? localStorage.getItem('user_name') : localStorage.getItem('vxOpenIdCipherText') ? localStorage.getItem('vxOpenIdCipherText')  : UerInfo?.phone,
      client: env == 5 ? localStorage.getItem('client') : 'WX',
      type: env === '1' ? '' : '1', // h5 传1
    }
  }

  // 上传空间封面、空间广告
  const onChangeByUpload = (info: any)=>{
    let fileList;
    fileList = info && info.fileList;
    if (info.file.status === 'uploading') {
      setLoadingByUpload(true);
      return;
    }

    // 判断当前上传已被拦截
    if (info && !info.file.status) {
      setLoadingByUpload(false);
      fileList = null;
      return;
    }
    if (info && info.file.status === 'error') {
      setLoadingByUpload(false);
      message.error('上传失败');
      fileList = null;
      return
    }
    // 上传结束
    if (info && info.file.status === 'done') {
      setLoadingByUpload(false);
      const res = info.file.response || {}
      const { code, content, msg } = res
      if (code == 200 && content) {
        // 封面
        dispatch({
          type: 'userInfoStore/setTaskListState',
          payload: {
            createModalVisible: false,
            spaceCoverUrl:{fileUrlView: content.fileUrlView, fileUrl: content.fileUrl},
            spaceCoverUrlView: content.fileUrlView,
          }
        })
      } else {
        fileList = [];
        message.error(msg || '上传失败')
      }
    }
  }

  // 上传校验规则
  const beforeUpload = (file: { size?: any; type?: any; }) => {
    const isSize = file.size / 1024 / 1024 < 15;
    if (!isSize) {
      message.error('超过15M限制，不允许上传~');
      return false;
    }

    const { name:fileName } = file || {}
    // 添加对文件后缀名的限制
    const suffix = fileName.substring(fileName.lastIndexOf('.')+1)
    const isJpgOrPng = file.type === 'image/jpg' || file.type === 'image/jpeg' ||  file.type === 'image/png' ||  file.type === 'image/gif';
    // 文件后缀名可以大写,所以需要添加大写后缀名的判断
    const isSuffixByJpgOrPng = (
      suffix === 'jpg'
      || suffix === 'JPG'
      || suffix === 'jpeg'
      || suffix === 'JPEG'
      || suffix === 'png'
      || suffix === 'PNG'
      || suffix === 'gif'
      || suffix === 'GIF'
    )
    if (!isJpgOrPng || !isSuffixByJpgOrPng) {
      message.error('只能上传JPG、JPEG、PNG、GIF 格式的图片~');
      return false;
    }
    return isJpgOrPng;
  };


  const closeModalBtnFn = () => {
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        openCropperImgModal: false,
        // selectCreateType: 0
      }
    });
  }


  return (
    /*<Mask className={styles.mask} visible={visible} opacity={1}>*/
    <div>
      <Popup
        visible={openCropperImgModal}
        onMaskClick={closeModalBtnFn}
        className={styles.popup_container}
        bodyStyle={{ height: '90vh' }}
        destroyOnClose
      >
        <div className={styles.header}>
          <div className={styles.line} onClick={closeModalBtnFn}></div>
        </div>
        <div>
          <div className={styles.title_box}>
            <div className={styles.title_btn} onClick={() => closeModalBtnFn()}>
              <img src={GoBackIcon} width={12} height={24} alt="" />
            </div>
            <div className={styles.title}>裁切封面</div>
            <div className={styles.title_right}>
              <Upload
                headers={getHeaders()}
                accept="image/*"
                action={`/api/server/base/uploadFile?${stringify({ fileType: 15, userId: UerInfo?.friUserId})}`}
                onChange={(info)=>onChangeByUpload(info)}
                onRemove={() => { }}
                beforeUpload={beforeUpload}
                showUploadList={false}
              >
                <div className={styles.img_select}>选取</div>
              </Upload>
            </div>
          </div>

          <div className={styles.container}>
            <Spin spinning={!!loadingByUpload}>
              { spaceCoverUrlView &&
                <div className={styles.CropperWarp}>
                  <Cropper
                  ref={cropperRef}
                  style={{ height: 400, width: "100%" }}
                  viewMode={1}                      // 1 限制裁剪框不超过画布的大小
                  initialAspectRatio={1}            // 初始纵横比
                  background={false}                // 是否显示容器的网格背景 默认为true
                  aspectRatio={25 / 14}
                  minCropBoxHeight={50}             // 最小裁剪框高度
                  minCropBoxWidth={50}              // 最小裁剪框宽度
                  zoomOnTouch={false}               // 通过拖动触摸来缩放图像
                  dragMode='none'                   // 拖动模式(保持默认状态，不可拖动画布)
                  toggleDragModeOnDblclick={false}  // 启用可在单击两次裁剪器时在“裁剪”和“移动”之间切换拖动模式
                  src={spaceCoverUrlView}           // 图片url
                />
                </div>
              }
            </Spin>
          </div>

          <div className={styles.footer}>
            <div className={styles.footer_reduction_btn} onClick={resetImage}>还原</div>
            <div className={styles.footer_ok_btn} onClick={ok}>完成</div>
          </div>

        </div>

      </Popup>
    </div>
    /*</Mask>*/
  )
}

export default connect(({ userInfoStore, loading }: any) => ({userInfoStore, loading}))(Index)
