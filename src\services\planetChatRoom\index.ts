import request,{ requestDownload } from "@/utils/request";
import {getOperatingEnv} from "@/utils/utils";
import {stringify} from "qs";

// 登录后获取该用户的IM秘钥信息
export const getImInfoByUser = (params) => {
  return request(`/api/server/imBase/getImInfoByUser`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

/**
 *  /user/h5User/getMsgCodeUserInfo
 *  个人中心、个人主页获取用户信息
 */
export async function getSpaceInfo(params: {
  wxUserId?: any,  // 微信userid
  spaceId?: any,   // 空间id
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/getSpaceInfo`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

/**
 /server/space/getGuestListInfo
 查看空间嘉宾列表
*/
export async function getGuestListInfo(params: {
  wxUserId?: any,  // 微信userid
  spaceId?: any,   // 空间id
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/getGuestListInfo`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

/**
 * 用户进入直播、会议的一些操作
 */
export async function userActiveJoinSpace(params: {
  spaceId?: any,   // 空间id
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/user-active-join-space?${stringify(params)}`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

/**
 * /server/space/getApplyAdmissionList
 * 获取申请进入会议成员列表
 */
export async function getApplyAdmissionList(params: {
  spaceId?: any,   // 空间id
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/getApplyAdmissionList`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}


/**
 * /server/space/getShareImUser
 * 共享屏幕获取辅流用户
 * */
export async function getShareImUser(params: {
  wxUserId?: any,  // 微信userid
  spaceId?: any,   // 空间id
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/getShareImUser`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

/**
 * /server/space/spaceCollect
 * 空间收藏
 * */
export async function spaceCollect(params: {
  wxUserId?: any,       // 微信userid
  spaceId?: any,        // 空间id
  collectType?: any,    // 收藏类型 1:收藏 2:取消收藏
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/spaceCollect?${stringify(params)}`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

/**
 * HTTP GET
 * /server/space/getHandUpList
 * 查看空间连麦列表
 * */
export async function getHandUpList(params: {
  pageNum?: any,    // 微信userid
  pageSize?: any,   // 条数
  spaceId?: any,    // 空间id
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/getHandUpList`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

/**
 * HTTP GET
 * /server/space/getSignInList
 * 查看空间打卡签到列表
 * */
export async function getSignInList(params: {
  pageNum?: any,    // 微信userid
  pageSize?: any,   // 条数
  spaceId?: any,    // 空间id
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/getSignInList`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

/**
 * /server/space/operateHandUp
 * 操作连麦请求
 * */
export async function operateHandUp(params: {
  spaceId?: any,    // 空间id
  wxUserId?:any,    // [string]	是	用户ID
  guestUserId?:any, // [string]		需操作连麦的用户ID，当前用户是主持人时必传
  statusType?:any,  // [string]	是	1 接受连麦 2暂不同意 3下麦
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/operateHandUp?${stringify(params)}`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}


/**
 * /server/space/getSpaceGroupMsg
 * 分页获取空间消息，分页倒序
 * */
export async function getSpaceGroupMsg(params: {
  spaceId?: any,  // 空间id
  pageSize?:any,  // 条数
  msgSeq?:any,    // 页码
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/getSpaceGroupMsg`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

/**
 * /server/space/getSpaceGroupMsg
 * 获取空间弹幕消息
 * */
export async function getSpaceBulletScreen(params: {
  eventTime?: any,  // 时间点
  spaceId?:any,     // 空间id
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/getSpaceBulletScreen`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}


/**
 * server/space/checkSpacePassword
 * 校验空间密码
 * */
export async function checkSpacePassword(params: {
  id?: any,       // 	空间ID
  password?: any,  // 密码
},options?: { [key: string]: any }) {
  return request(`/api/server/space/checkSpacePassword?${stringify(params)}`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}


/**
 * /server/space/getAppointmentList
 * 查看空间直播预约列表
 * */
export async function getAppointmentList(params: {
  pageNum?: any,    // 微信userid
  pageSize?: any,   // 条数
  spaceId?: any,    // 空间id
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/getAppointmentList`, {
    method: 'GET',
    params: {
     ...params,
    },
  });
}

/**
 * /server/space/liveAppointment
 * 空间直播预约
 * */
export async function liveAppointment(params: {
  wxUserId?: any,           // 当前用户ID
  appointmentType?: any,    // 预约类型，1预约 2取消预约
  spaceId?: any,            // 空间ID
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/liveAppointment?${stringify(params)}`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

/**
 *  /server/space/openCloseHandUp
 *  主持人开启/关闭连麦
 * */
export async function openCloseHandUp(params: {
  wxUserId?: any,           // 当前用户ID
  spaceId?: any,            // 空间ID
  handUpType?: any,         // 1开启 2关闭
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/openCloseHandUp?${stringify(params)}`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

/**
 * /server/space/liveRecord
 * 录制视频
 * */
export async function liveRecord(params: {
  wxUserId?: any,           // 当前用户ID
  spaceId?: any,            // 空间ID
  recordType?: any,         // 1开始录制 2停止录制
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/liveRecord?${stringify(params)}`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

/**
 * /server/maExperts/isFocus
 * 关注、取关 专家
 * */
export async function isFocus(params: {
  wxUserId?: any,           // 当前用户ID
  isFocus?: any,            // 0取消关注 1关注
  expertsUserId?: any,      // 关注的专家用户ID
}, options?: { [key: string]: any }) {
  return request(`/api/server/h5Experts/isH5Focus?${stringify(params)}`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}


/**
 * /server/space/startEndLive
 * 开始/结束直播
 * */
export async function startEndLive(params: {
  wxUserId?: any,           // 当前用户ID
  spaceId?: any,            // 空间ID
  liveType?: any,           // 1开始直播 2结束直播
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/startEndLive?${stringify(params)}`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

// 保存观看时长
export async function saveVideoTime(params: {
  wxUserId?: any,           // 当前用户ID
  spaceId?: any,            // 空间ID
  videoSecond?: any,        // 已经观看的秒数
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/saveVideoTime?${stringify(params)}`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

// 关闭空间/异常退出等，需要关闭空间，记录用时（录播调）
// /space/closeSpaceWindow
export async function closeSpaceWindow(params: {
  spaceId?: any,      //  [string] 是 空间ID
  joinRandStr?: any,  // [string] 是进入时的字符串，从详情中取
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/closeSpaceWindow?${stringify(params)}`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

// 通过空间ID，进入直播会议后获取转码文件
export async function getTranscodeFileBySpace(params: {
  spaceId?: any,    // 空间id
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/get-transcode-file-by-space`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}



// 屏幕分享失败，帮助文档下载
export async function downScreenShareHelpFile(params: {}, options?:
  { [key: string]: any }) {
  return requestDownload(`/api/server/space/down-screen-share-help-file?${stringify(params)}`, {
    method: 'POST',
    data: {
      ...params,
    },
    headers: {
      Accept: '*/*',
      "Content-Type":'application/pdf'
    }
  });
}

// 申请入会 addStarSpaceApplyAdmission
export async function addStarSpaceApplyAdmission(params: {
  spaceId?: any,      //  [string] 是 空间ID
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/addStarSpaceApplyAdmission?${stringify(params)}`, {
    method: 'GET',
  });
}


// 空间课件文件转码
export async function spaceCoursewareTranscode(params: {
  coursewareName?: any, // --课件原始名称
  coursewarePath?: any, // --课件在OSS的短路径
  spaceId?: any         // --空间ID
}, options?: { [key: string]: any }) {
  return request(`/api/server/spaceManagement/space-courseware-transcode`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}


// 申请入会拒绝或准入
export async function updateStarSpaceApplyAdmission(params: {
  applyAdmissionId?: any, //	是 1 申请记录ID
  refuseAdmittance?: any, //	是 1 操作类型：1拒绝、2准入
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/updateStarSpaceApplyAdmission?${stringify(params)}`, {
    method: 'GET',
  });
}


// 根据空间ID获取转码前的课件信息 get-courseware
export async function getCourseware(params: {
  id?: any, // 空间ID
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/updateStarSpaceApplyAdmission?${stringify(params)}`, {
    method: 'GET',
  });
}


// 主动轮询检查转码结果
export async function checkSpaceCoursewareTranscode(params: {
  coursewarePath?: any, // 课件在OSS的短路径
}, options?: { [key: string]: any }) {
  return request(`/api/server/spaceManagement/check-space-courseware-transcode`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

// 开始白板推流
export async function startWhiteboardPush(params: {
  spaceId?: any,          // --空间ID
  tiwPushUserId?: any,    // --推流机器人imuserId
  tiwPushUserSig?: any,   // --推流机器人签名
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/start-whiteboard-push`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

// 结束白板推流
export async function stopWhiteboardPush(params: {
  spaceId?: any,          // --空间ID
  tiwPushTaskId?: any,    // --要结束推流的taskID
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/stop-whiteboard-push`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

// 获取白板互动im推流机器人
export async function getTiwImUser(params: {
  spaceId?: any,          // --空间ID
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/get-tiw-im-user?${stringify(params)}`, {
    method: 'GET',
    // data: { ...params },
  });
}

// 会议主持人离开移交主持人
export async function getMeetingTransferCompereUser(params: {
  spaceId?: any,          // 会议ID
  transferUserId?: any,          // 离开会议移交主持人用户ID，非正常离开时可不传
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/get-meeting-transfer-compere-user?${stringify(params)}`, {
    method: 'GET',
    // data: { ...params },
  });
}

// 空间在线用户列表（会议详情、成员管理）
export async function getManageMembersInTheMeeting(params: {
  spaceId?: any,          // 会议ID
  sceneType?: any,          // 场景类型，1：会议详情在线用户，2：管理成员在线用户
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/get-manage-members-in-the-meeting?${stringify(params)}`, {
    method: 'GET',
    // data: { ...params },
  });
}

// 移出会议在线用户
export async function getMeetingRemoveUser(params: {
  spaceId?: any,          // 会议ID
  removeUserId?: any,          // 移出会议用户ID
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/get-meeting-remove-user?${stringify(params)}`, {
    method: 'GET',
    // data: { ...params },
  });
}

// 会议设置
export async function spaceInsideSetting(params: {
  spaceId?: any,          // --空间ID
  isShowPassword?: any,    // --是否展示密码 1是 0否
  isSpectatorUse?: any,    // --是否允许非参会人进入 1是/关 0否/开
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/space-inside-setting`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

// 全体静音OR全体解除静音
export async function getMeetingMuteAll(params: {
  spaceId?: any,     // --空间ID
  muteType?: any,    // 操作类型：1:全体静音 0：全体解除静音
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/get-meeting-mute-all?${stringify(params)}`, {
    method: 'GET',
    data: {
      ...params,
    },
  });
}


// 获取预约空间订阅公众号弹窗中的 getTemplateList获取私有模板列表
export async function getTemplateList(params: {
  wxUserId?: any,  // 微信userid
  spaceId?: any,   // 空间id
}, options?: { [key: string]: any }) {
  return request(`/api/server/space/getSpaceInfo`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}


// 预约成功订阅消息-V1.11
export async function getSubscribeUrl(params: {
  wxUserId?: any,  // 微信userid
  spaceId?: any,   // 空间id
}, options?: { [key: string]: any }) {
  return request(`/api/server/weChat/getSubscribeUrl`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}


// weChat/getCallback
// 预约成功订阅消息回调-V1.11
/**
 * openid       是 222 用户唯一标识，只在用户确认授权时才会带上
 * template_id  是 21321312 订阅消息模板ID
 * action       是 confirm 用户点击动作，"confirm"代表用户确认授权，"cancel"代表用户取消授权
 * scene        是 订阅场景值 999
 * reserved     是 1_2 请求带入原样返回
 */

export async function getCallback(params: {
  openid,       // 222 用户唯一标识，只在用户确认授权时才会带上
  template_id,  // 21321312 订阅消息模板ID
  action,       // confirm 用户点击动作，"confirm"代表用户确认授权，"cancel"代表用户取消授权
  scene,        // 订阅场景值 999
  reserved,     // 1_2 请求带入原样返回
}, options?: { [key: string]: any }) {
  return request(`/api/server/weChat/getCallback`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}
