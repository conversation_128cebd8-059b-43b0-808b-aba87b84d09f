/**
 * @Description: 移动端聊天底部输入框组件
 * @author: 赵斐
 */
import React, { useState, useEffect } from 'react';
import { Toast } from 'antd-mobile'
import { useThrottle } from '@/utils/utils'
import { emojiMap, emojiName } from '@/emoticon/index'
import chatKeyboardIcon from '@/assets/Consultation/H5/chat_keyboard_icon.png'
import chatVoiceIcon from '@/assets/Consultation/H5/chat_voice_icon.png'
import chatEmoteIcon from '@/assets/Consultation/H5/chat_emote_icon.png'
import chatOtherIcon from '@/assets/Consultation/H5/chat_other_icon.png'
import Recorder from 'js-audio-recorder';
import { onSendMessage, localDealNotSendMessage, getFileURL, getVideoBase64, cloudCustomUserInfoData } from '@/utils/im-index'
import heic2any from 'heic2any'
import styles from './index.less'
interface PropsType {
  isDoctor: number,               // 当前人是否是专家：0:否，1:是
  imGroupId: any,                 // 群组ID
  timObj: any,                    // Im 对象
  emojiVisible: boolean,         // 表情内容展示状态
  otherVisible: boolean,         // 其它内容展示状态
  onClickEmojiShow: () => void,  // 点击表情Icon展示表情内容方法
  onClickEmojiHide: () => void,  // 点击表情Icon隐藏表情内容方法
  onClickOhterShow: () => void,  // 点击其它Icon展示其它内容方法
  onClickOhterHide: () => void,  // 点击其它Icon隐藏其它内容方法
  uploadLoading: (v: boolean) => void,  // IM上传图片、视频所需loading
  onBlurInput: () => void,  // IM上传图片、视频所需loading
  onFocusInput: () => void,  // IM上传图片、视频所需loading
  refreshPage: any,  // 刷新
  stateImList: any,        // 聊天记录最新数据
  UploadSchedule: any      // 获取上传进度方法
}
interface Emoji {
  emojiName: string;
  url: string;
}
const initState = {
  isKeyboard: 1,   //  1 输入框 2 语音
  emojiData: [],   // 表情包数据
}

const initStateMedia = {
  clientY: 0,     //录音点击起始位置
  startTs: 0,   // 记录录音开始时间
  audioFile: null,    // 语音file文件
  title1: ""
}

const emojiUrl = 'https://static.jwsmed.com/public/3M/SmallerProject/assets/'
let timer: any = null; // 判断长按的定时器

// 1. 开始录制
let recorder = new Recorder({
  sampleBits: 16,                 // 采样位数，支持 8 或 16，默认是16
  sampleRate: 48000,              // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值，我的chrome是48000
  numChannels: 1,                 // 声道，支持 1 或 2， 默认是1
});

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {
    isDoctor,// 当前人是否是专家：0:否，1:是
    imGroupId,
    emojiVisible,
    otherVisible,
    onClickEmojiShow,
    onClickEmojiHide,
    onClickOhterShow,
    onClickOhterHide,
    timObj,
    refreshPage,
    uploadLoading,  // IM上传图片、视频所需loading
    onBlurInput,
    onFocusInput,
    stateImList,
    UploadSchedule
  } = props;
  const [state, setState] = useState(initState)
  const [isRecording, setIsRecording] = useState(false)  // 是否正在录音
  const [stateMedia, setStateMedia] = useState(initStateMedia)
  const [isMediaAuthority, setIsMediaAuthority] = useState(false)   // 麦克风是否授权
  const [stateInput, setStateInput] = useState("")  // 输入框数据

  const [seconds, setSeconds] = useState(0);

  const {
    isKeyboard,
    emojiData,
  } = state

  const {
    clientY,
    startTs,
    audioFile,
    title1,
  } = stateMedia

  useEffect(() => {
    if (startTs > 0 && isRecording) {
      const timer = setInterval(() => {
        setSeconds((seconds) => seconds + 1);
      }, 1000);

      return () => {
        clearInterval(timer);
      };
    }
  }, [startTs]);

  useEffect(() => {
    console.log(seconds)
    let userInfoStr = cloudCustomUserInfoData()
    let msgSeq = 1
    if (Array.isArray(stateImList) && stateImList.length) {
      msgSeq = stateImList[stateImList.length - 1].msgSeq + 1
    }
    if (seconds == 60) {
      setStateMedia({
        ...stateMedia,
        startTs: 0,
        title1: '',
        // canSend: true,
      })
      // 2. 结束录制
      recorder.stop();
      // 3. 计算录音时长，获取 WAV 数据
      let duration = Date.now() - startTs; // 单位：ms
      let wavBlob = recorder.getWAVBlob();
      // 4. blob 数据转成 File 对象
      let audioFile = new File([wavBlob], 'hello.wav', { type: 'wav' });
      audioFile.duration = duration;
      // 发送语音
      console.log(audioFile, 'file')
      let data = {
        msgSeq,
        desc: null,
        mediaDuration:Math.floor(duration/1000)
      }
      localDealNotSendMessage(3, data, refreshPage, userInfoStr)
      onSendMessage(3, imGroupId, audioFile, refreshPage, timObj, userInfoStr)
      setIsRecording(false)

    }
  }, [seconds])

  useEffect(() => {
    if (isDoctor == 1) {

      // 检查浏览器是否支持 getUserMedia API
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        // 请求录音权限
        navigator.mediaDevices.getUserMedia({ audio: true })
          .then((stream) => {
            // 成功获取录音权限后的操作，例如将音频流连接到 Web Audio API 进行录音处理
            setIsMediaAuthority(true)
            Toast.show("获取录音权限成功")
          })
          .catch((error) => {
            setIsMediaAuthority(false)
            console.error('获取录音权限失败：', error);
            // Toast.show('获取录音权限失败')
          });
      } else {
        console.error('浏览器不支持 getUserMedia API');
      }
    }
  }, [isDoctor])

  // 渲染表情展示
  useEffect(() => {
    let arr = []
    for (let i = 0; i < emojiName.length; i++) {
      arr.push({
        emojiName: emojiName[i],
        url: emojiUrl + emojiMap[emojiName[i]],
      });
    }
    setState({
      ...state,
      emojiData: arr
    })
  }, [])


  // 点击Icon切换是否语音、输入框
  const onClickSwitch = () => {
    if (!isMediaAuthority) {
      Toast.show("无麦克风权限，暂时无法使用语音功能")
      return
    }
    setState({
      ...state,
      isKeyboard: isKeyboard == 1 ? 2 : 1
    })
    setStateInput("")
  }


  // 点击表情，展示到输入里面（呈现文字）
  const onClickEmojiFun = (val: string) => {

    let value = stateInput + val
    setStateInput(value)
  }
  // 文本输入框
  const onChangeInput = (value: string) => {
    console.log(value)
    setStateInput(value)
  }

  // 点击发送按钮
  const onSend = () => {
    let userInfoStr = cloudCustomUserInfoData()
    let msgSeq = 1
    if (Array.isArray(stateImList) && stateImList.length) {
      msgSeq = stateImList[stateImList.length - 1].msgSeq + 1
    }
    if (stateInput.length > 300) {
      Toast.show("输入内容过长！")
      return
    }
    let data = {
      msgSeq,
      desc: stateInput,
    }
    localDealNotSendMessage(1, data, refreshPage, userInfoStr)
    // 发送文本
    onSendMessage(1, imGroupId, stateInput, refreshPage, timObj, userInfoStr)
    setStateInput("")
  }


  // 当手指触摸屏幕时候触发
  // 点击录制语音开始
  let handleTouchStart = (e: any) => {
    setStateMedia({
      ...stateMedia,
      clientY: e.touches[e.touches.length - 1].clientY,
    })
    recorder.start().then(() => {
      // 开始录音，记录起始时间戳
      setStateMedia({
        ...stateMedia,
        clientY: e.touches[e.touches.length - 1].clientY,
        startTs: Date.now()
      })
    }, (error) => {
      // 出错了
      if (error) {
        console.log(`${error.name} : ${error.message}`);
      }
    });
    setIsRecording(true)
  }

  handleTouchStart = useThrottle(handleTouchStart, 800)
  // 当手指在屏幕上滑动的时候连续地触发
  const handleTouchMove = (e: any) => {
    console.log(e.touches[e.touches.length - 1].clientY);
    if (clientY - e.touches[e.touches.length - 1].clientY > 80) {
      setStateMedia({
        ...stateMedia,
        title1: '松开手指，取消发送',
        clientY: 0,
        startTs: 0,
        // canSend: false,
      })
      setSeconds(0)
    } else if (clientY - e.touches[e.touches.length - 1].clientY > 20) {
      setStateMedia({
        ...stateMedia,
        title1: '上划可取消',
        // canSend: true,
      })
    } else {
      setStateMedia({
        ...stateMedia,
        title1: '',
        // canSend: true,
      })
    }

  }
  // 当手指从屏幕上离开的时候触发
  // 点击录制语音结束
  const handleTouchEnd = () => {
    let userInfoStr = cloudCustomUserInfoData()
    let msgSeq = 1
    if (Array.isArray(stateImList) && stateImList.length) {
      msgSeq = stateImList[stateImList.length - 1].msgSeq + 1
    }
    if (seconds == 60) {
      setSeconds(0)
      return
    }
    setStateMedia({
      ...stateMedia,
      title1: '',
      // canSend: true,
    })
    // 2. 结束录制
    recorder.stop();
    // 3. 计算录音时长，获取 WAV 数据
    let duration = Date.now() - startTs; // 单位：ms
    let wavBlob = recorder.getWAVBlob();
    if (duration > 1000 && seconds > 1) {
      // 4. blob 数据转成 File 对象
      let audioFile = new File([wavBlob], 'hello.wav', { type: 'wav' });
      audioFile.duration = duration;
      // 发送语音
      console.log(audioFile, 'filefilefilefile')

      // recorder.stop();
      setSeconds(0)
      let data = {
        msgSeq,
        desc: null,
        mediaDuration:Math.floor(duration/1000)
      }
      localDealNotSendMessage(3, data, refreshPage, userInfoStr)
      onSendMessage(3, imGroupId, audioFile, refreshPage, timObj, userInfoStr)

      setStateMedia({
        ...stateMedia,
        clientY: 0,
        startTs: 0,
        title1: '',
      })
    } else {
      // recorder.stop();
      setStateMedia({
        ...stateMedia,
        clientY: 0,
        startTs: 0,
        title1: '',
      })
      Toast.show("录音时间太短，请长按录音")
    }
    setIsRecording(false)
  }

  // 判断上传类型 heic 格式需转义格式
  const getFileType = (reader) => {
    const bufferInt = new Uint8Array(reader.result);
    const arr = bufferInt.slice(0, 4);  // 通用格式图片
    const headerArr = bufferInt.slice(0, 16);  // heic格式图片
    let header = '';
    let allHeader = '';
    let realMimeType;

    for (let i = 0; i < arr.length; i++) {
      header += arr[i].toString(16); // 转成16进制的buffer
    }

    for (let i = 0; i < headerArr.length; i++) {
      allHeader += headerArr[i].toString(16);
    }
    // magic numbers: http://www.garykessler.net/library/file_sigs.html
    switch (header) {
      case '89504e47':
        realMimeType = 'image/png';
        break;
      case '47494638':
        realMimeType = 'image/gif';
        break;
      case 'ffd8ffDB':
      case 'ffd8ffe0':
      case 'ffd8ffe1':
      case 'ffd8ffe2':
      case 'ffd8ffe3':
      case 'ffd8ffe8':
        realMimeType = 'image/jpeg';
        break;
      case '00020':  // heic开头前4位可能是00020也可能是00018，其实这里应该是判断头尾000的，可以自己改下
      case '00018':
        (allHeader.lastIndexOf('68656963') === 13 || allHeader.lastIndexOf('68656966') === 13) ? (realMimeType = 'image/heic') : (realMimeType = 'unknown');
        break;
      default:
        realMimeType = 'unknown';
        break;
    }
    return realMimeType;
  }

  // base64图片转文件
  const dataURLtoFile = (dataurl, fileName) => {
    let arr = dataurl.split(','),
      mime = arr[0].match(/:(.*?);/)[1],
      bstr = atob(arr[1]),
      n = bstr.length,
      u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], fileName, { type: mime });
  }

  // 聊天图片上传事件
  const photoFileChange = (e: any) => {
    let userInfoStr = cloudCustomUserInfoData()
    let msgSeq = 1
    if (Array.isArray(stateImList) && stateImList.length) {
      msgSeq = stateImList[stateImList.length - 1].msgSeq + 1
    }
    if (!e.target.files || !e.target.files[0]) return;
    const file = e.target.files[0];
    let isJpgOrPng = file.type === 'image/jpg' || file.type === 'image/jpeg' || file.type === 'image/png';

    const isSize = file.size / 1024 / 1024 < 15; // 图片大小
    if (!isSize) {
      Toast.show('超过15M限制，不允许上传~');
      return;
    }
    if (!isJpgOrPng) {
      Toast.show('只能上传JPG 、JPEG  、PNG 格式的图片~');
      return;
    }

    const fileReader = new FileReader();
    const fileReaderBuffer = new FileReader();

    fileReaderBuffer.onload = async () => {
      const type = getFileType(fileReaderBuffer);
      if (type === 'unknown') {
        console.error('unknown image type');
        e.target.value = '';
        return;
      }
      if (type.includes('/heic')) {
        heic2any({ blob: file, toType: 'image/jpeg' }).then((blob) => {
          fileReader.readAsDataURL(blob);
        }).catch(() => {
          e.target.value = '';
        });
        fileReader.onload = function (e) {
          // e.target.result 即为base64结果
          const base64Data = e.target.result
          const file2 = dataURLtoFile(base64Data, 'aaa.jpeg')
          uploadLoading(true)
          localDealNotSendMessage(2, {
            msgSeq,
            desc: base64Data,
          }, refreshPage, userInfoStr)
          onSendMessage(2, imGroupId, file2, refreshPage, timObj, userInfoStr, UploadSchedule)
        }
        return;
      } else {
        // fileReader.readAsDataURL(file); // 异步操作， IO操作
        // 4.利用文件阅读器将文件展示到前端页面，修改是src属性
        // 5. 等待文件阅读器加载完毕  myFileObj.result---图片路径
        fileReader.onload = function () {
          console.log(fileReader.result)
          localDealNotSendMessage(2, {
            msgSeq,
            desc: fileReader.result,
          }, refreshPage, userInfoStr)
        }
        onSendMessage(2, imGroupId, file, refreshPage, timObj, userInfoStr, UploadSchedule)
      }
      fileReader.readAsDataURL(file);
    };

    fileReaderBuffer.readAsArrayBuffer(file);
  }

  // 聊天视频上传事件
  const videoFileChange = (e: any) => {
    let userInfoStr = cloudCustomUserInfoData()
    let msgSeq = 1
    if (Array.isArray(stateImList) && stateImList.length) {
      msgSeq = stateImList[stateImList.length - 1].msgSeq + 1
    }
    if (!e.target.files || !e.target.files[0]) return;
    const file = e.target.files[0];
    const fileType = file.type == 'video/mp4'; // 视频格式
    const oversize = file.size / 1024 / 1024 / 1024 > 2; // 视频大小
    // 判断上传视频是否大于2G或者上传文件是否为mp4格式的，不是则提示
    if (oversize || !fileType) {
      Toast.show({ duration: 2000, content: <div>请上传格式为MP4，<br />大小2G以内的视频</div> });
      e.target.value = ''; // 解决同一个视频重复上传后，导致不再提示
      return;
    }
    // IM回调未成功之前使用此方法展示数据
    getFirstVideoImg(file).then(res => {
      localDealNotSendMessage(4, {
        msgSeq,
        msgContent: null,
        thumbUrlShow: res,
      }, refreshPage, userInfoStr)
    })

    // 发送视频
    onSendMessage(4, imGroupId, file, refreshPage, timObj, userInfoStr, UploadSchedule)
  }
  /**
   * 获取视频封面图
   * @param file  视频file
   * @returns
   */
  const getFirstVideoImg = async (file) => {
    // 使用
    const objUrl = await getFileURL(file as File)
    const objBase = await getVideoBase64(objUrl)
    return objBase
  }
  return (
    <div className={styles.wrap} style={emojiVisible || otherVisible ? { height: '262px' } : { height: '58px' }}>
      <div className={styles.content} style={isKeyboard == 2 ? { userSelect: 'none' } : {}}>
        {
          isDoctor == 1 ? <div className={styles.chat_voice} onClick={() => { onClickSwitch() }}>
            <img src={isKeyboard == 1 ? chatVoiceIcon : chatKeyboardIcon} alt="icon" />
          </div> : null
        }

        {
          isKeyboard == 1 ? <div className={styles.chat_input}>
            <input
              className={styles.input}
              placeholder={isDoctor == 1 ? '请输入内容...' : '请输入要问诊的内容'}
              type="text"
              value={stateInput}
              onChange={(e) => { onChangeInput(e.target.value) }}
              onFocus={() => { onFocusInput() }}
              onBlur={() => { onBlurInput() }}
            />
          </div> : null
        }
        {
          isKeyboard == 2 ? <div className={styles.chat_say}
            onTouchStart={(e) => { handleTouchStart(e) }}
            onTouchEnd={() => { handleTouchEnd() }}
            onTouchMove={(e) => { handleTouchMove(e) }}
          >
            {
              isRecording ? <span className={styles.say}></span> : <span className={styles.an_say}>按住说话</span>
            }

          </div> : null
        }
        {
          title1 ? <div className={styles.close_say}><span>{title1}</span></div> : null
        }

        <div className={styles.chat_emote} onClick={emojiVisible ? () => { onClickEmojiHide() } : () => { onClickEmojiShow(), setState({ ...state, isKeyboard: 1 }) }}>
          <img src={chatEmoteIcon} alt="icon" />
        </div>
        {
          !stateInput ? <div className={styles.chat_other} onClick={otherVisible ? () => { onClickOhterHide() } : () => { onClickOhterShow(), setState({ ...state, isKeyboard: 1 }) }}>
            <img src={chatOtherIcon} alt="icon" />
          </div> : null
        }
        {
          stateInput ? <div className={styles.send_text} onClick={() => { onSend() }}>发送</div> : null
        }
      </div>

      {/* 表情包内容 */}
      {
        emojiVisible && <div className={styles.emoji_content}>
          {
            emojiData.map((item: Emoji, index: number) => {
              return (
                <span onClick={() => { onClickEmojiFun(item.emojiName) }} className={styles.emoji_icon} key={index}><img src={item.url} alt={item.emojiName} /></span>
              )
            })
          }

        </div>
      }

      {/* 其他内容 */}
      {
        otherVisible ? <div className={styles.other_content}>
          <div className={styles.upload_photo}>
            <span className={styles.upload_photo_icon}>
              <input type="file" name="uploadPhotoInput" id="uploadPhotoInput" accept='image/*'
                className={styles.upload_video_input} onChange={photoFileChange}
              />
            </span>
            <span className={styles.upload_characters}>图片</span>
          </div>
          <p className={styles.upload_video}>
            <span className={styles.upload_video_icon}>
              <input type="file" name="uploadVideoInput" id="uploadVideoInput" accept='video/mp4'
                className={styles.upload_video_input} onChange={videoFileChange}
              />
            </span>
            <span className={styles.upload_characters}>视频</span>
          </p>
        </div> : null
      }

    </div>
  )
}
export default Index
