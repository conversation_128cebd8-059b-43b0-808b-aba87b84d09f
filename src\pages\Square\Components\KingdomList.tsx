/**
 * @Description: 广场-王国
 */
import React, { useState, useEffect } from 'react';
import { connect } from 'umi';
import KingdomView from '@/components/KingdomView'; // 王国
import { InfiniteScroll } from 'antd-mobile'; // 滚动加载
import NotDataRender from '@/components/NoDataRender'; // 暂无数据
import { Spin } from 'antd';
import type { DateItem } from './CollectList';

const initState: {
  pageNum: number;
  pageSize: number;
  total: number;
  kingdomDataList: DateItem[];
} = {
  pageNum: 1, // 当前页
  pageSize: 30, // 每页条数
  total: 0, // 总条数
  kingdomDataList: [], // 空间数据集合
};

const KingdomList: React.FC = (props: any) => {
  const { dispatch, loading } = props;
  const [state, setState] = useState(initState); // 列表数据
  const [hasMore, setHasMore] = useState(true); // 更多数据状态
  const { pageNum, pageSize, total, kingdomDataList } = state;

  useEffect(() => {
    getKingdomListFn()
  }, [])

  // 王国列表数据
  const getKingdomListFn = (currentPage?: any) => {
    return dispatch({
      type: 'square/getStarKingdomList',
      payload: {
        pageNum: currentPage || pageNum,
        pageSize,
      },
    }).then((res) => {
      const { code, content } = res || {};
      if (res && code == 200) {
        const { total: responseTotal, resultList } = content || {};

        setState((prevState) => ({
          ...prevState,
          kingdomDataList: currentPage == 1 ? resultList : [...prevState.kingdomDataList, ...resultList],
          total: responseTotal,
          pageNum: prevState.pageNum + 1,
        }));
      } else {
        setState((prevState) => ({
          ...prevState,
          kingdomDataList: [],
          total: 0,
          pageNum: 1,
        }));
        console.log('数据失败!');
      }
    });
  };
  // 滚动到底部加载更多
  useEffect(() => {
    if (kingdomDataList.length >= total) {
      setHasMore(false);
    } else {
      setHasMore(true);
    }
  }, [kingdomDataList, total]);

  // 加载更多数据
  const loadMore = async () => {
    await getKingdomListFn();
  };

  // 分页
  const getPageInfo = (val:number) => {
    setState((prevState) => ({
      ...prevState,
      kingdomDataList: [],
      total: 0,
      pageNum: 1,
    }));
    getKingdomListFn(val)
  }

  const getStarKingdomListLoading = !!loading.effects['square/getStarKingdomList']; // loading

  return (
    <Spin spinning={getStarKingdomListLoading}>
      {kingdomDataList && kingdomDataList.length ? (
        <>
        <KingdomView
          componentData={{ dataList: kingdomDataList }}
          getPageInfo={(val:number)=>getPageInfo(val)}
          pageType={'99'}
        />
        <InfiniteScroll loadMore={loadMore} hasMore={hasMore} threshold={30} />
        </>
      ) : (
        <NotDataRender text="暂无王国" />
      )}
    </Spin>
  );
};
export default connect(({ square, loading }: any) => ({ square, loading }))(KingdomList);
