.step_container {
  position: relative;
  background: #fff;
  width: 100%;
  height: 60px;
  padding: 0 180px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px 8px 0 0;
  border-bottom: 1px solid #D9D9D9;
}

.step_content {
  text-align: center;
  font-size: 20px;
  font-weight: 500;
  color: #000;
}

.btn {
  position: absolute;
  right: 24px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  color: #0095FF;
  height: 17px;
  line-height: 17px;
  display: flex;
  align-items: center;
  cursor: pointer;
  :global {
    .anticon {
      font-size: 10px;
      margin-right: 2px;
    }
  }
}
