/**
 * @Description: 移动端指导附件数量
 * @author: 赵斐
 */
import React from 'react';
import styles from './index.less'

interface PropsType {
  annexNumber: number,      // 附件数量
  onLookJump: () => void,   // 点击查看回调
}

const Index: React.FC<PropsType>= (props: PropsType) => {
  const { annexNumber ,onLookJump } = props;
  return (
    <div className={styles.wrap}>
      该病例有<span className={styles.num}>{annexNumber}</span>份附件<span className={styles.look} onClick={()=>{onLookJump()}}>点击查看</span>
    </div>
  )
}
export default Index
