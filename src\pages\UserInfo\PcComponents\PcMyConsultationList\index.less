.content {
  width: 100%;
  height: calc(100vh - 343px);
  overflow: hidden;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.operation {
  cursor: pointer;
  user-select: none;
}

.tab_wrap {
  width: 100%;
  height: 43px;
  background: #FFFFFF;
  border-radius: 8px;
  padding: 16px 20px 0;
  margin-bottom: 12px;
  display: flex;

  .tab_init {
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    line-height: 16px;
    margin-right: 16px;
    height: 27px;
    cursor: pointer;

    &.tab_active {
      font-size: 14px;
      font-weight: 600;
      color: #000000;
      line-height: 16px;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 12px;
        height: 3px;
        background: #000000;
        border-radius: 6px 6px 6px 6px;
      }
    }
  }
}

.warp_filter {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  justify-content: space-between;

  .Statusfilter_warp {
    display: flex;
    align-items: center;
    margin-right: 12px;
    padding-left: 20px;

    .Statusfilter_box {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      line-height: 14px;
      margin-right: 24px;
      cursor: pointer;
      user-select: none;
    }

    .Statusfilter_Activity {
      font-size: 14px;
      font-weight: 500;
      color: #000000;
      line-height: 14px;
    }
  }

  .filter_box_box {
    display: flex;
    align-items: center;
  }


  .warp_filter_box {
    display: flex;
    align-items: center;
    user-select: none;
    cursor: pointer;
  }

  .warp_filter_box_Icon {
    width: 16px;
    height: 16px;
    background: url('../../../../assets/GlobalImg/PcMyConsultationList_Filter.png') no-repeat;
    background-size: 16px 16px;
    margin-right: 3px;
  }

  .warp_filter_box_text {
    font-size: 14px;
    font-weight: 400;
    color: #0095FF;
    line-height: 14px;
  }


  .warp_filter_box {
    margin-right: 12px;
  }

  .input_filter_search_box {
    display: flex;

    .warp_filter_select {
      width: 98px;
      height: 32px;
      background: #FFFFFF;
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
      border-top-right-radius: 0px;
      border-bottom-right-radius: 0px;
      opacity: 1;
      border: 1px solid #CFD7DF;
      border-right-style: none;
      cursor: pointer;
      user-select: none;

      :global {
        .ant-select:not(.ant-select-customize-input) .ant-select-selector {
          position: relative;
          background-color: #fff;
          border: none;
          border-top-left-radius: 4px;
          border-bottom-left-radius: 4px;
          border-top-right-radius: 0px;
          border-bottom-right-radius: 0px;
          height: 30px;
          transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
        }
      }
    }
  }

  .warp_filter_input_warp {
    position: relative;
    overflow: hidden;
    :global {
      .ant-input {
        width: 281px;
        border-top-left-radius: 0px;
        border-bottom-left-radius: 0px;
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
      }
    }
    .SearchOrderNumber_Icon {
      position: absolute;
      right: 13px;
      top: 5px;
      width: 20px;
      height: 20px;
      background: url('../../../../assets/GlobalImg/SearchOrderNumber_Icon.png');
      background-size: 20px 20px;
      display: inline-block;
      cursor: pointer;
      user-select: none;
    }
  }
}

.ModalByFilter {
  .fieldName {
    font-size: 14px;
    font-weight: 500;
    color: #000000;
    line-height: 14px;
  }

  .fieldContent {
    padding-top: 12px;
    padding-bottom: 12px;
  }
  :global {
    .ant-checkbox-wrapper + .ant-checkbox-wrapper {
      margin-left: 0;
    }
    .ant-checkbox-wrapper {
      margin-right: 12px;
      margin-bottom: 12px;
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-top: 12px;
    padding-bottom: 12px;
    padding-right: 12px;
  }
}

.space_wrap {
  :global {
    .ant-table-thead > tr > th, .ant-table-tbody > tr > td, .ant-table tfoot > tr > th, .ant-table tfoot > tr > td {
      padding: 16px 12px;
    }
  }
}
