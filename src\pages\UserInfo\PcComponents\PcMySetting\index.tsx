/**
 * @Description: pc端-个人中心-设置tab页
 */
import React, { useState } from 'react';
import { connect, history } from 'umi';
import styles from './index.less';
import classNames from 'classnames';
import { message, Checkbox, Button, Modal } from 'antd';
import {stringify} from "qs"
const pcLogouAgreementImg = 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/UserInfo_pc_logou.png'; // 注销协议图片


const tabLists = [
  { id: 1, val: '隐私政策' },
  { id: 2, val: '账号注销' },
]

// 隐私政策图片列表
const agreementImgList = [
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/Agreement/pc_agreement_1.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/Agreement/pc_agreement_2.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/Agreement/pc_agreement_3.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/Agreement/pc_agreement_4.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/Agreement/pc_agreement_5.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/Agreement/pc_agreement_6.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/Agreement/pc_agreement_7.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/Agreement/pc_agreement_8.png',
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/Agreement/pc_agreement_9.png',
]

const Index: React.FC = (props: any) => {
  const { dispatch, pcAccount } = props;
  const { query } = history.location

  const [tabType, setTabType] = useState(query.subTabKey || pcAccount?.subTabState || 1); // tab切换
  const [logouVisible, setLogouVisible] = useState(false);
  const [checked, setChecked] = useState(false);

  const onClickTabFun = (tabKey:any) => {
    history.replace(`${history.location.pathname}?${stringify({
      ...history.location.query,
      subTabKey: tabKey,
    })}`)

    setTabType(tabKey)
    dispatch({
      type: 'pcAccount/save',
      payload: {
        subTabState: tabKey
      }
    })
  }

  // 选中
  const onChange = (e:any) => {
    setChecked(e?.target?.checked)
  }

  // 我要注销按钮
  const logoutClickBtn = () => {
    if(checked) {
      setLogouVisible(true)
		} else {
      message.error('请勾选同意协议!')
		}
  }

  // 确定事件
  const handleOk = () => {
    dispatch({
      type: 'userInfoStore/unsubscribe'
    }).then(res => {
      if(res && res.code == 200) {
        message.success('账号注销成功')
        // 清除本地存储
        localStorage.clear();

        // 注销后跳转登录页
        history.replace('/User/login')
      } else {
        message.error('账号注销失败!')
      }
    })
    setLogouVisible(false)
  }

  // 关闭注销弹框
  const handleCancel = () => {
    setLogouVisible(false)
  }

  return <>
    <div className={styles.content}>
      <div className={styles.tab_wrap}>
        {
          tabLists.map(item => {
            return <div key={item.id} className={classNames({[styles.tab_init]: true, [styles.tab_active]: tabType == item.id })} onClick={() => { onClickTabFun(item.id) }}>{item.val}</div>
          })
        }
      </div>
      <div className={styles.tab_content}>
        {
          tabType == 1 ?
          <div className={styles.agreementBox}>
            {agreementImgList.map((item, ind) => {
              return <img key={ind} src={item}/>
            })}
          </div> :
          <div className={styles.logou_box}>
            <div className={styles.logou_agreement_img}><img src={pcLogouAgreementImg} alt="" /></div>
            <div className={styles.logou_btn_box}>
              <div className={styles.logou_text}><Checkbox checked={checked} onChange={onChange}>我已阅读并同意<span className={styles.logou_agreement}>《账号注销协议》</span></Checkbox></div>
              <Button type="primary" shape="round" className={styles.logou_btn} onClick={logoutClickBtn}>我要注销</Button>
            </div>
          </div>
        }
      </div>
      <Modal
        title="账号注销"
        open={logouVisible}
        onOk={handleOk}
        confirmLoading={false}
        onCancel={handleCancel}
        className={styles.modal_wrap}
        width={474}
      >
        <p>注销后，您绑定的saas系统账号将解除绑定，确定继续注销？</p>
      </Modal>
    </div>
  </>
}
export default connect(({ userInfoStore, loading }: any) => ({ userInfoStore, loading }))(Index)
