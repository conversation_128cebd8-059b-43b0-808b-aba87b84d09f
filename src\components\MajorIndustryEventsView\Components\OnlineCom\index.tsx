import React from 'react';
import classNames from 'classnames';
import Item from '../Components/NormalItem';
import styles from './index.less';

interface IProps {
  dataList?: any;
  styleType?: string;
  pcOrMobileMode?: string;
}
const Index: React.FC<IProps> = ({ dataList, styleType, pcOrMobileMode }) => {
  return (<div
    className={classNames(styles.normal_wrapper, {
      [styles.normal_mobile_wrapper]: pcOrMobileMode === 'mobile',
      [styles.normal_pc_wrapper]: pcOrMobileMode === 'pc'
    })}
  >
    {dataList && dataList.map((item, index) => {
      return <Item
        itemData={item}
        styleType={styleType}
        key={`${item.id}-${index}`}
        pcOrMobileMode={pcOrMobileMode} />
    })}
  </div>)
}

export default Index;