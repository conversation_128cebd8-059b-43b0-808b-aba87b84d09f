.wrap {
  max-height: 282px;
  border-radius: 12px 12px 0px 0px;
  padding-bottom: 20px;

  :global {
    .adm-action-sheet-button-item-wrapper {
      border-bottom: none;
    }

    .adm-action-sheet-button-list {
      border-bottom: none;
      padding-top: 30px;

      .adm-action-sheet-button-item-name {
        font-size: 16px;
        font-weight: 500;
        color: #000000;
        line-height: 19px;
      }

      .adm-plain-anchor:focus {
        outline: none; /* 去掉点击时的虚线框 */
        background: #fff;
        color: #fff;
      }

      .adm-action-sheet-button-item:focus {
        background: #fff;
        color: #fff;
      }
      .adm-action-sheet-button-item:active {
        background-color: #fff;
      }

      .adm-action-sheet-button-item-wrapper:last-child {
        .adm-action-sheet-button-item-name {
          font-size: 16px;
          font-weight: 500;
          color: #888888;
          line-height: 19px;
        }
      }
    }
  }
}

.tips_content {
  :global {
    .adm-modal-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-direction: row-reverse;
    }
  }
}
