.case_container {
  position: relative;
  font-size: 14px;
  color: #666;
  padding: 20px 16px;
  border-radius: 8px;
  background: #fff;
  cursor: pointer;
  height: 100%;
  .topic {
    font-size: 17px;
    line-height: 24px;
    font-weight: 500;
    color: #000;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 8px;
  }
  .subject_box {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 4px;
    & > span {
      display: block;
      border-radius: 2px;
      background: #EDF9FF;
      height: 21px;
      line-height: 21px;
      padding: 0 4px;
      font-size: 12px;
      color: #0095FF;
      margin-right: 6px;
      margin-bottom: 4px;
      &.achievement {
        background: #FFF7E2;
        color: #D3A221;
      }
      &.difficult1 {
        background: #E6FBF3;
        color: #00D78B;
      }
      &.difficult2 {
        background: #FFF8EC;
        color: #E39D16;
      }
      &.difficult3 {
        background: #FFF4E9;
        color: #FF921F;
      }
      &.difficult4 {
        background: #FCE9E8;
        color: #FF5F57;
      }
    }
  }
  .doctor_box {
    display: flex;
    flex-wrap: nowrap;
    margin-bottom: 4px;
    line-height: 21px;
    .label {
      white-space: nowrap;
    }
    .value {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .keywords_box {
    display: flex;
    flex-wrap: nowrap;
    margin-bottom: 8px;
    .label {
      white-space: nowrap;
      line-height: 24px;
    }
    .value_box {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      overflow: hidden;
      & > span {
        display: block;
        height: 24px;
        line-height: 24px;
        padding: 0 4px;
        background: #F5F5F5;
        border-radius: 4px;
        margin-right: 6px;
        margin-bottom: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  .solution_box {
    padding: 8px;
    position: relative;
    background: #F5F5F5;
    border-radius: 6px;
    .solution_icon {
      position: absolute;
      top: 8px;
      left: 8px;
      display: block;
      width: 57px;
      height: 20px;
      background: url("../../assets/GlobalImg/word_art.png") no-repeat center;
      background-size: 100% 100%;
    }
    .solution_text {
      text-indent: 64px;
      word-break: break-all;
      text-overflow: ellipsis;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
    }
  }

  // 图片
  .cover_img_box {
    margin-bottom: 12px;
    position: relative;
    &:last-child {
      margin-bottom: 0;
    }
    :global {
      .ant-carousel .slick-slider {
        position: relative;
      }
      .slick-disabled {
        display: none;
      }
    }
    .arrow {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      font-size: 12px;
      color: #fff;
      line-height: 22px;
      height: 22px;
      padding: 0 4px;
      background: rgba(0,0,0,0.6);
      white-space: nowrap;
      z-index: 10;
    }
    .arrow_left {
      left: 0;
      border-radius: 0px 2px 2px 0px;
    }
    .arrow_right {
      right: 6px;
      border-radius: 2px 0px 0px 2px;
    }
    .cover_img_block {
      width: 100%;
      padding-right: 8px;
    }
    .cover_img{
      width: 100%;
      height: 107px;
      border-radius: 4px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      //img {
      //  width: 100%;
      //  height: 100%;
      //  border-radius: 4px;
      //}
    }
  }
  // 图片结束
}

.case_container.container_form_1 {
  padding: 0 16px;
  .cover_img_box {
    .arrow_right {
      right: 8px;
    }
  }
}
