/**
 * @Description: 公共提示弹窗
 */
import React, { useRef, useState, useEffect } from 'react'
import classNames from 'classnames'
import { Modal, Carousel, Button } from 'antd'
import styles from './index.less'

interface PropsType {
  visible: boolean,                              // true，false
  title: string, // 第一行文案
  text?: string, // 第二行文案
  leftBtnText?: string, // 左边按钮文案
  rightBtnText?: string, // 右边按钮文案
  onClickLeftBtn?: any, // 点击左边按钮回调
  onClickRightBtn?: any, // 点击右边按钮回调
  loading?: boolean,
}

const Index: React.FC<PropsType> = (props: any) => {
  const {
    visible = false,
    title = '', // 第一行文案
    text = '', // 第二行文案
    leftBtnText = '', // 左边按钮文案
    rightBtnText = '', // 右边按钮文案
    onClickLeftBtn = () => {}, // 点击左边按钮回调
    onClickRightBtn = () => {}, // 点击右边按钮回调
    loading = false,
  } = props

  return (
    <Modal
      title="提示"
      className={styles.modal}
      visible={visible}
      onCancel={onClickLeftBtn}
      width={474}
      footer={null}
      destroyOnClose
    >
      <div className={styles.text_wrap}>
        <div>{title}</div>
        <div>{text}</div>
      </div>
      <div className={styles.btn_wrap}>
        {leftBtnText && <Button onClick={onClickLeftBtn}>{leftBtnText}</Button>}
        <Button type="primary" onClick={onClickRightBtn} loading={loading}>{rightBtnText}</Button>
      </div>
    </Modal>
  )
}

export default Index
