.popup_container {
  :global {
    .adm-popup-body {
      border-radius: 16PX 16PX 0 0;
    }
  }
}

.spin {
  height: 100%;
  :global {
    .ant-spin-container {
      height: 100%;
    }
  }
}

.container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: nowrap;
  flex-direction: column;
}

// 头部
.header_line {
  flex-shrink: 0;
  width: 100%;
  height: 28PX;
  display: flex;
  align-items: center;
  justify-content: center;
  .header_line_bar {
    width: 48PX;
    height: 4PX;
    background: #D0D4D7;
    border-radius: 4PX;
  }
}

// 标题
.header_title_wrap {
  width: 100%;
  flex-shrink: 0;
  display: flex;
  flex-wrap: nowrap;
  align-items: flex-end;
  column-gap: 8PX;
  padding: 16PX 0 12PX 16PX;
  .title {
    font-size: 17PX;
    color: #000;
    font-weight: 500;
    line-height: 24PX;
  }
  .text {
    font-size: 14PX;
    color: #666;
    line-height: 20PX;
  }
}

// 搜索
.search_wrap {
  width: 100%;
  flex-shrink: 0;
  padding-bottom: 12PX;
}

// 标签栏
.tabs_wrap {
  width: 100%;
  flex-shrink: 0;
  display: flex;
  padding: 0 16PX 8PX;
  column-gap: 16PX;
  .tabs_item {
    font-size: 15PX;
    color: #999;
    font-weight: 500;
    height: 28PX;
    position: relative;
    &.checked {
      color: #000;
      font-weight: 600;
      .tabs_bar {
        display: block;
        position: absolute;
        width: 12PX;
        height: 3PX;
        border-radius: 6PX;
        background: #000;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
      }
    }
    .badge {
      position: absolute;
      right: -4PX;
      top: -1PX;
      min-width: 12PX;
      height: 12PX;
      line-height: 10PX;
      border-radius: 6PX;
      padding: 1PX;
      text-align: center;
      background: #FF180D;
      font-size: 9PX;
      color: #fff;
    }
  }
}

// 用户列表
.user_list_wrap {
  :global {
    .ant-dropdown-menu {
      padding: 0;
    }
    .ant-dropdown-menu-item {
      font-size: 12PX;
      color: #333;
      padding: 10PX 16PX;
    }
    //.adm-popover-menu.adm-popover {
    //  margin-top: -8PX;
    //}
    //.adm-popover-arrow {
    //  display: none;
    //}
  }
  flex: 1;
  overflow-y: auto;
  padding: 8PX 12PX 8PX 16PX;
  position: relative;
  // 会议中用户
  .user_item_1 {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    padding: 8PX 0;
    .left {
      flex: 1;
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      overflow: hidden;
      padding-right: 8PX;
      .avatar_wrap {
        width: 24PX;
        height: 24PX;
        flex-shrink: 0;
        margin-right: 12PX;
      }
      .user_name {
        font-size: 14PX;
        color: #000;
        height: 20PX;
        line-height: 18PX;
        margin-right: 8PX;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .user_role {
        flex-shrink: 0;
        height: 16PX;
        line-height: 16PX;
        border-radius: 2PX;
        padding: 0 2PX;
        font-size: 10PX;
        &.audience {
          background: rgba(255,146,46,0.1);
          color: #FF922E;
        }
        &.guest {
          background: rgba(0,199,119,0.1);
          color: #06A777;
        }
        &.host {
          background: rgba(0,149,255,0.1);
          color: #0095FF;
        }
      }
    }
    .right {
      flex-shrink: 0;
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      column-gap: 12PX;
    }
  }

  // 申请进入用户
  .user_item_2 {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    padding: 8PX 0;
    .left {
      flex: 1;
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      overflow: hidden;
      padding-right: 8PX;
      .avatar_wrap {
        width: 24PX;
        height: 24PX;
        flex-shrink: 0;
        margin-right: 12PX;
      }
      .user_name {
        font-size: 14PX;
        color: #000;
        height: 20PX;
        line-height: 18PX;
        margin-right: 12PX;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .right {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      column-gap: 24PX;
      padding-right: 4PX;
      font-size: 14PX;
      color: #0095FF;
      line-height: 20PX;
    }
  }
}

// 底部按钮
.btn_wrap {
  width: 100%;
  flex-shrink: 0;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  column-gap: 24PX;
  padding: 8PX 24PX 34PX 16PX;
  .voice_btn_wrap {
    flex: 1;
    display: flex;
    flex-wrap: nowrap;
    column-gap: 16PX;
    .btn_left, .btn_right {
      flex: 1;
      height: 40PX;
      line-height: 40PX;
      border-radius: 20PX;
      text-align: center;
      font-size: 16PX;
    }
    .btn_left {
      background: #EDF9FF;
      color: #0095FF;
    }
    .btn_right {
      background: #0095FF;
      color: #fff;
    }
  }
  .invite_wrap {
    flex-shrink: 0;
    font-size: 11PX;
    color: #666;
    line-height: 15PX;
  }
}
