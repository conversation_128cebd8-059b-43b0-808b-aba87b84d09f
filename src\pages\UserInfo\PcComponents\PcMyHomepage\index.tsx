/**
 * @Description: pc端-个人中心-主页tab页
 */
import React, { useState } from 'react';
import { history, connect } from 'umi';
import {stringify} from "qs"
import classNames from 'classnames';
import { Spin } from 'antd';
import styles from './index.less';

import PcMyHomepageSpace from './PcMyHomepageSpace';
import PcMyHomepageMeeting from './PcMyHomepageMeeting';
import PcMyHomepageCase from './PcMyHomepageCase';
import PcMyHomePostGroup from './PcMyHomePostGroup';

const tabLists = [
  { id: 1, val: '直播' },
  { id: 2, val: '会议' },
  { id: 3, val: '病例' },
  { id: 4, val: '帖子' },
]

const Index: React.FC = (props: any) => {
  const { isExperts,dispatch,pcAccount, loading } = props;
  const { query } = history.location
  const [tabType, setTabType] = useState(query.subTabKey || pcAccount?.subTabState || 1);

  // 切换空间、病例
  const onClickTabFun = (tabKey:any) => {
    history.replace(`${history.location.pathname}?${stringify({
      ...history.location.query,
      subTabKey: tabKey,
    })}`)
    setTabType(tabKey)
    dispatch({
      type: 'pcAccount/save',
      payload: {
        subTabState: tabKey,
        threeTabState: 1,     // 三级tab状态
      }
    })
  }

  const getCaseInfoByExpertsUserIdLoading = !!loading.effects['expertAdvice/getCaseInfoByExpertsUserId']; // loading
  const getStarSpaceListBySearchUserIdLoading = !!loading.effects['expertAdvice/getStarSpaceListBySearchUserId']; // loading
  const loadingPersonImageTextList = !!loading.effects['graphicsText/personImageTextList']; // loading

  return <>
    <div className={styles.content}>
      <div className={styles.tab_wrap}>
        {
          tabLists.map(item => {
            if (isExperts != 1 && item.id == 3) return;
            return <div key={item.id} style={{display: 'flex'}}>
              {item.id == 6 ? <div key='line' className={styles.lines}></div> : null}
              <div className={classNames({[styles.tab_init]: true, [styles.tab_active]: tabType == item.id })} onClick={() => { onClickTabFun(item.id) }}>{item.val}</div>
            </div>
          })
        }
      </div>
      <Spin wrapperClassName={styles.tab_content} spinning={getCaseInfoByExpertsUserIdLoading || getStarSpaceListBySearchUserIdLoading || loadingPersonImageTextList}>
        {tabType == 1 && <PcMyHomepageSpace starSpaceType={1} /> }
        {tabType == 2 && <PcMyHomepageMeeting starSpaceType={2} /> }
        {/* 判断是否是专家, 并且是否切换到病例 */}
        {isExperts == 1 && tabType == 3 ? <PcMyHomepageCase /> : null}

        {/* 帖子3合1 */}
        {tabType == 4 && <PcMyHomePostGroup />}
      </Spin>
    </div>
  </>
}
export default connect(({ expertAdvice,pcAccount, loading }: any) => ({ expertAdvice,pcAccount, loading }))(Index)
