/**
 * @Description: 活动组件
 */
import React, { useState, useEffect } from 'react'
import { history } from 'umi'
import classNames from 'classnames'
import styles from './index.less'

import SignUpModal from '@/pages/Home/Components/SignUpModal'          // 报名弹窗

interface PropsType {
  pageId: any,                                             // 页面ID
  componentData: any,                                      // 组件数据
  signUpOnOk: any,                                         // 报名成功回调
}

const Index: React.FC<PropsType> = (props: PropsType) => {

  const { componentData } = props

  const initialModalState = {
    signUpVisible: false,                                  // 报名弹窗是否打开
    isActive: false,                                       // 报名弹窗是否打开，动画用
    animationPlayState: 'paused',                          // 动画状态，running 开始，paused 暂停
    animationNameMask: 'show1',                            // 动画名称，遮罩
    animationNameContent: 'show2',                         // 动画名称，内容
  }
  const [modalState, setModalState] = useState(initialModalState)
  const [isWx, setIsWx] = useState(false)

  useEffect(() => {
    if (wx) {
      wx.miniProgram.getEnv(function(res) {
        // {miniprogram:true}
        if (res.miniprogram == true) {
          setIsWx(true)
        }
      })
    }
  }, [])

  // 点击遮罩回调
  const onClickMask = () => {
    setModalState({
      ...modalState,
      isActive: false,                                     // 报名弹窗是否打开，动画用
      animationPlayState: 'running',                       // 动画状态，running 开始，paused 暂停
      animationNameMask: 'hide1',                          // 动画名称，遮罩
      animationNameContent: 'hide2',                       // 动画名称，内容
    })
  }

  // 动画完成后回调，isActive true 打开，false 关闭弹窗
  const onAnimationEnd = (isActive) => {
    if (!isActive) {
      setModalState({
        ...modalState,
        signUpVisible: false,                              // 报名弹窗是否打开
      })
    }
  }

  // 报名成功回调
  const signUpOnOk = () => {
    setModalState({
      ...modalState,
      isActive: false,                                     // 报名弹窗是否打开，动画用
      animationPlayState: 'running',                       // 动画状态，running 开始，paused 暂停
      animationNameMask: 'hide1',                          // 动画名称，遮罩
      animationNameContent: 'hide2',                       // 动画名称，内容
    })
    props.signUpOnOk()
  }

  // 点击立即报名
  const onSignUp = () => {
    // 1立即报名, 2已报名, 3未开始报名, 4报名已结束
    if (componentData.buttonStatus == 1) {

      // 打开弹窗
      setModalState({
        ...modalState,
        signUpVisible: true,                               // 报名弹窗是否打开
        isActive: true,                                    // 报名弹窗是否打开，动画用
        animationPlayState: 'running',                     // 动画状态，running 开始，paused 暂停
        animationNameMask: 'show1',                        // 动画名称，遮罩
        animationNameContent: 'show2',                     // 动画名称，内容
      })
    }
  }

  return (
    <>
      <div className={styles.activity_container}>
        <div className={styles.box}>
          <div className={classNames(styles.btn, styles.share, {
            [styles.disabled]: !isWx,
          })}>
            <i className={styles.icon}></i>
            <span>分享</span>
          </div>
          <div className={classNames(styles.btn, styles.sign_up)} onClick={() => onSignUp()}>
            {
              componentData.buttonStatus == 1 ? '立即报名'
                : componentData.buttonStatus == 2 ? '已报名'
                : componentData.buttonStatus == 3 ? '报名未开始'
                  : componentData.buttonStatus == 4 ? '报名已结束'
                    : null
            }
          </div>
        </div>
      </div>

      {/* 报名弹窗 */}
      <SignUpModal
        visible={modalState.signUpVisible}
        isActive={modalState.isActive}
        pageId={props.pageId}
        activityId={componentData.activityNumber}
        animationPlayState={modalState.animationPlayState}
        animationNameMask={modalState.animationNameMask}
        animationNameContent={modalState.animationNameContent}
        onClickMask={onClickMask}
        onAnimationEnd={onAnimationEnd}
        onOk={signUpOnOk}
      />
    </>
  )
}

export default Index
