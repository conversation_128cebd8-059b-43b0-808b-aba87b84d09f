// @ts-ignore
/* eslint-disable */
import request from '@/utils/request'
import { stringify } from 'qs'
import {getOperatingEnv} from "@/utils/utils";

/**
 * 获取正畸方案病例详情(无指导ID时获取基本信息字典和患者信息)
 * @param params
 */
export async function getOrthodonticCaseInfo(params) {
  return request('/api/server/h5OrthodonticAudit/getOrthodonticCaseInfo', {
    method: 'GET',
    params,
  })
}

/**
 * 正畸病例-会诊查询详情
 * /h5ConsultationOrder/get-consultation-orthodontic-case-info
 * 由https://dugr.w.eolink.com/home/<USER>/inside/5KKZnQ158d232fb65f56a2d3d936032822f03d18234e8b2/api/2728001/detail/52711068?spaceKey=dugr&projectGroup=0&projectType=0
 * （正畸病例审核）接口，与此接口结构一致
 */
export async function getConsultationOrthodonticCaseInfo(params) {
  return request('/api/server/h5ConsultationOrder/get-consultation-orthodontic-case-info', {
    method: 'GET',
    params,
  })
}


/**
 * 新建或编辑正畸方案病例信息
 * bady
 * */
export async function editOrthodonticCaseInfo(data,params) {
  return request(`/api/server/h5OrthodonticAudit/editOrthodonticCaseInfo`, {
    method: 'POST',
    data: data,
    headers: {},
  })
}

/**
 * 编辑会诊订单信息
 */
export async function editConsultationInfo(data,params) {
  return request(`/api/server/h5ConsultationOrder/editConsultationInfo`, {
    method: 'POST',
    data: data,
    headers: {},
  })
}


/**
 * 获取上级医生集合
 * /server/h5OrthodonticAudit/getSuperDoctorList
 */
export async function getSuperDoctorList(params) {
  return request('/api/server/h5OrthodonticAudit/getSuperDoctorList', {
    method: 'GET',
    params,
  })
}

/*
* 正畸修改指导订单节点和状态
* editConsultationNodeAndStatus
* */
export async function editConsultationNodeAndStatus(params) {
  return request(`/api/server/h5ConsultationOrder/editConsultationNodeAndStatus?${stringify({
    consultationId: params.consultationId,
    type: params.type,
  })}`, {
    method: 'POST',
    data: params,
    headers: {},
  })
}

/**
 * 修改会诊用户提问且同步病历问题
 * update-case-info-question
 *
 *    "userName":"志君",
 *    "wxUserId":60,
 *    "id":"f17bc2e4d27c400e80f15c75ef2cbfec", //会诊ID
 *    "firstQuestion":"你好吗？？牙齿好么？？"  // 用户提问修改内容
 */
export async function updateCaseInfoQuestion(params) {
  return request(`/api/server/h5ConsultationOrder/update-case-info-question`, {
    method: 'POST',
    data: params,
    headers: {},
  })
}
