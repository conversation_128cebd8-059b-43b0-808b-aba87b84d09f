import React, { useEffect, useState, useRef } from 'react'
import { connect, history, useAliveController } from 'umi'
import { processNames, randomColor, useThrottle } from '@/utils/utils'
import { Spin } from 'antd'
import { Toast } from 'antd-mobile'
import styles from './index.less'
import NavBar from '@/components/NavBar'
import QuillDom from '@/pages/CreateGraphicsText/Quill'
import ToolbarMobile from '@/pages/CreateGraphicsText/Quill/ToolbarMobile'
import ArticleCard from '@/components/ArticleCard'
import classNames from 'classnames'
import { cloneDeep } from 'lodash'
let timer2 = null // 定时器

const Index: React.FC = (props: any) => {
  const {
    forwardId,                         // 图文ID
  } = history.location.query
  const { loading, dispatch, graphicsText } = props
  const quillRef = useRef(null) // ref
  const [toolbarPanelVisible, setToolbarPanelVisible] = useState(false)  // 面板状态
  const [quillHistoryStack, setQuillHistoryStack] = useState({})          // 历史记录
  const [quillFormat, setQuillFormat] = useState({})                 // 获取格式
  const [detailsState, setDetailsState] = useState({})     // 详情
  const { clear } = useAliveController()

  useEffect(() => {
    imgTextInfoById()
  }, [])

  // 插入话题
  useEffect(() => {
    if (graphicsText.selectedTopicName) {
      insertTopic(graphicsText.selectedTopicId, graphicsText.selectedTopicName, graphicsText.selectTopicType)
    }
  }, [graphicsText.selectedTopicName])

  // 图文详情
  const imgTextInfoById = () => {
    dispatch({
      type: 'graphicsText/imgTextInfoById',
      payload: {
        imageTextId: forwardId,                            // 图文ID
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200 && content) {
        if (content.isForward == 1 && content.forwardDescribeJson) {
          setDetailsState(content.forwardSquareRecommendDto || {})
          const forwardDescribeJsonObj = JSON.parse(content.forwardDescribeJson)
          let ops = forwardDescribeJsonObj.ops
          ops.unshift({
            insert: {
              user: {
                id: content.createUserId,
                name: `@${content.userName}:`,
              }
            }
          })
          ops.unshift({
            insert: '//'
          })
          quillRef?.current?.quill.setContents(forwardDescribeJsonObj)
          quillRef?.current?.quill.setSelection(0)
        } else {
          setDetailsState(content)
        }
      } else {
        Toast.show(msg || '数据加载失败')
      }
    }).catch(err => {})
  }

  // 插入话题
  const insertTopic = (topicId, topicName, selectTopicType) => {
    dispatch({
      type: 'graphicsText/save',
      payload: {
        selectedTopicId: null,
        selectedTopicName: null,
        selectTopicType: 2,
      }
    })
    timer2 = setTimeout(() => {
      insertTopicTimeout(topicId, topicName, selectTopicType)
    }, 100)
  }

  // 定时器循环
  const insertTopicTimeout = (topicId, topicName, selectTopicType) => {
    if (quillRef && quillRef.current && quillRef.current.quill && quillRef.current.quill.getSelection(true)) {
      quillRef.current.topicFn({topicId, topicName: `#${topicName}#`}, selectTopicType)
      clearTimeout(timer2)
    } else {
      timer2 = setTimeout(() => {
        insertTopicTimeout(topicId, topicName, selectTopicType)
      }, 50)
    }
  }

  // 获取quill历史记录
  const getQuillHistoryStack = (stack) => {
    setQuillHistoryStack(cloneDeep(stack))
  }

  // 获取quill格式
  const getQuillFormat = (format) => {
    setQuillFormat(cloneDeep(format))
  }

  // 面板打开或关闭
  const toolbarPanelOnChange = (visible) => {
    setToolbarPanelVisible(visible)
  }

  // 发布
  let submit = async () => {
    const articleLength = quillRef?.current?.unprivilegedEditor?.getText().replace(/\n/g, '').length
    const imageTextContent = quillRef?.current?.unprivilegedEditor?.getHTML()
    const contentJson = JSON.stringify(quillRef?.current?.unprivilegedEditor?.getContents())
    if (articleLength == 0) {
      Toast.show('请输入正文')
      return
    }
    if (articleLength > 5000) {
      Toast.show('正文字数不能超过5000字')
      return
    }
    Toast.show({
      icon: 'loading',
      content: '',
      duration: 0,
    })
    await clear()
    dispatch({
      type: 'graphicsText/editImgTextInfo',
      payload: {
        forwardDescribe: imageTextContent,                 // 转发描述
        forwardDescribeJson: contentJson,                  // JSON数据
        imageTextId: forwardId,                            // 图文ID
        originalImageTextId: detailsState.id,              // 原始图文的ID
        isForward: 1,                                      // 是否转发
        saveType: 2,                                       // 提交类型
      }
    }).then(res => {
      Toast.clear()
      const { code, content, msg } = res
      if (code == 200 && content) {
        Toast.show('转发成功')
        history.goBack()
      } else {
        Toast.show(msg || '数据加载失败')
      }
    }).catch(err => {})
  }
  submit = useThrottle(submit, 500)


  const loadingImgTextInfoById = !!loading.effects['graphicsText/imgTextInfoById']
  return (
    <Spin spinning={loadingImgTextInfoById} wrapperClassName={styles.spin}>
      <NavBar
        title="转发"
        RightRender={() => <span className={styles.nav_bar_btn} onClick={submit}>发布</span>}
        bordered
      />
      <div className={classNames(styles.container, {[styles.show_panel]: toolbarPanelVisible})}>
        <div className={styles.editor_box}>
          <QuillDom
            deviceType="mobile"
            placeholder="分享您的想法..."
            ref={quillRef}
            getQuillHistoryStack={getQuillHistoryStack}
            getQuillFormat={getQuillFormat}
          />
        </div>
        <div className={styles.forward_content}>
          {
            detailsState.imageType == 1 ?
              <ArticleCard style={{padding: 0, marginBottom: 0, background: 'none'}} item={detailsState} disabledClick={true}/>
              : detailsState.imageType == 2 ?
              <div className={styles.post_content}>
                <div
                  className={styles.init_img}
                  style={
                    detailsState.textImgList && detailsState.textImgList[0] ?
                      {backgroundImage: `url(${detailsState.textImgList[0].imageUrlShow})`}
                      : detailsState.headUrlShow ?
                      {backgroundImage: `url(${detailsState.headUrlShow})`}
                      : {background: randomColor(detailsState.createUserId)}
                  }
                >
                  {
                    detailsState.textImgList && detailsState.textImgList[0] || detailsState.headUrlShow ? null
                      : processNames(detailsState.userName)
                  }
                </div>
                <div
                  className={classNames('ql-editor', styles.text)}
                  dangerouslySetInnerHTML={{__html: detailsState.imageTextContent}}
                ></div>
              </div>
              : detailsState.imageType == 4 ?
                <div className={styles.space_wrap}>
                  <div
                    className={styles.left_cover_image}
                    style={
                      detailsState.textImgList && detailsState.textImgList[0] ? {backgroundImage: `url(${detailsState.textImgList[0].imageUrlShow})`}
                        : {backgroundColor: `${randomColor(detailsState.createUserId)}`}
                    }
                  >
                    {detailsState.textImgList && detailsState.textImgList[0] ? '' : processNames(detailsState.userName)}
                  </div>
                  <div className={styles.right}>
                    <div className={styles.space_title}>{detailsState.imageTitle}</div>
                    <div className={styles.space_introduce}>{detailsState.imageTextContent}</div>
                  </div>
                </div>
                : null
          }
        </div>
      </div>
      <ToolbarMobile
        quillRef={quillRef}
        quillHistoryStack={quillHistoryStack}
        quillFormat={quillFormat}
        toolbarPanelOnChange={toolbarPanelOnChange}
        createType="createForward"
      />
    </Spin>
  )
}

export default connect(({ graphicsText, loading }: any) => ({ graphicsText, loading }))(Index)
