.header{
  border-radius: 6px;
  height: 84px;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  :global{
    .ant-input-search .ant-input-group .ant-input-affix-wrapper:not(:last-child){
      height: 36px;
    }
    .ant-input{
      line-height: 1.6715;
    }
  }
  .header_title{
    width: 140px;
    font-size: 24px;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #000000;
    .header_title_icon{
      width: 24px;
      height: 24px;
      margin-right: 16px;
      cursor: pointer;
    }
  }
  .header_right{
    line-height: 34px;
    .header_screen_title,.header_screen_capacity,.header_screen_city ,.header_screen_subject{
      cursor: pointer;
      display: inline-block;
      font-size: 14px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #0095FF;
      margin-left: 18px;
      position: relative;
      &>span{
        vertical-align: middle;
      }
      .screen_icon{
        width: 15px;
        height: 15px;
        margin-right: 4px;
      }
      .select_city_style ,.select_capacity_style,.select_title_style,.select_subject_style{
        position: absolute;
        top: 0;
        left: 0;
        opacity: 0;
      }
    }


    .header_search{
      margin-left: 24px;
      display: inline-block;
      width: 379px;
      position: relative;
      :global{
        .ant-input::placeholder{
          color: #BCC8D4;
        }
        .ant-input-search > .ant-input-group > .ant-input-group-addon:last-child{
          display: none;
        }
        .ant-input-group > .ant-input-affix-wrapper:not(:last-child) .ant-input{
          padding-right: 20px;
        }
      }
      .header_search_icon{
        display: inline-block;
        position: absolute;
        top: 4px;
        right: 16px;
        width: 20px;
        height: 20px;
        z-index: 99;
        cursor: pointer;
        &>img{
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}