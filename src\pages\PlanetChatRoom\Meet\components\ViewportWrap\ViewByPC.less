// 顶部导航栏
.header_wrap {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 901;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 60px;
  padding: 0 39px 0 52px;
  background: #0b1238;
  .header_left {
    display: flex;
    flex: 1;
    flex-wrap: nowrap;
    align-items: center;
    overflow: hidden;
    .meeting_info_wrap {
      overflow: hidden;
    }
    .meeting_name_wrap {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      margin-bottom: 3px;
      column-gap: 4px;
      cursor: pointer;
      .meeting_name {
        overflow: hidden;
        color: #fff;
        font-size: 15px;
        line-height: 21px;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      :global(.anticon) {
        flex-shrink: 0;
        color: #fff;
        font-size: 10px;
      }
    }
    .meeting_other_wrap {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      color: #b2b2b2;
      .gdp {
        margin-right: 16px;
        font-size: 10px;
        line-height: 14px;
      }
      .time {
        font-size: 11px;
        line-height: 15px;
      }
    }
  }
  .meeting_password_wrap {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    margin-right: 24px;
    color: #0095ff;
    font-size: 12px;
  }
}

// 圆圈背景图形
.bg_circle_wrap {
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 1;
  width: 341px;
  height: 341px;
  background: linear-gradient(180deg, #1a265a 0%, #0c1533 71%);
  border-radius: 50%;
  filter: blur(4px);
  //display: none;
}

// 评论区滚动条样式
.scroll_bar_wrap {
  position: absolute;
  bottom: 142px;
  left: 60px;
  z-index: 900;
  :global {
    ::-webkit-scrollbar {
      width: 4px;
      height: 10px;
    }
    ::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.6);
      border-radius: 10px;
      -webkit-box-shadow: inset 0 0 0 5px rgba(0, 0, 0, 0);
    }
    ::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0);
      border-radius: 10px;
      -webkit-box-shadow: inset 0 0 0 5px rgba(0, 0, 0, 0);
    }
  }
}

// 聊天区
.im_message_list_wrap {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 400px;
  height: 180px;
  padding-right: 8px;
  overflow-y: auto;
  .im_message_item {
    display: inline-flex;
    flex-wrap: nowrap;
    width: auto;
    max-width: 100%;
    margin-bottom: 4px;
    padding: 3px 8px 4px;
    color: #fff;
    font-size: 12px;
    line-height: 17px;
    word-break: break-all;
    column-gap: 4px;
    border-radius: 10px;
  }
  // 在深色背景里
  &.in_black {
    .im_message_item {
      background: rgba(255, 255, 255, 0.1);
    }
  }
  // 在浅色背景里
  &.in_white {
    .im_message_item {
      background: rgba(0, 0, 0, 0.2);
    }
  }
}

// 评论输入框和右侧按钮
.bottom_wrap {
  position: absolute;
  bottom: 88px;
  left: 0;
  z-index: 900;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 4px 54px 16px 60px;
  .bottom_input_wrap {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    width: 400px;
    padding: 0 12px;
    border-radius: 20px;

    :global {
      .ant-input {
        height: 32px;
        padding: 0 8px;
        color: #fff;
        font-size: 13px;
        background: transparent;
        border: 0;
        outline: 0;
        box-shadow: none;
      }
    }
  }
  .bottom_btn_wrap {
    display: flex;
    flex-shrink: 0;
    flex-wrap: nowrap;
    align-items: flex-end;
    column-gap: 12px;
    .bottom_btn_item {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      cursor: pointer;
    }
    .bottom_call_btn {
      margin-left: 2px;
      cursor: pointer;
    }
  }
  // 在深色背景里
  &.in_black {
    .bottom_input_wrap {
      background: rgba(255, 255, 255, 0.1);
    }
    .bottom_btn_wrap {
      .bottom_btn_item {
        background: rgba(255, 255, 255, 0.1);
      }
    }
  }
  // 在浅色背景里
  &.in_white {
    .bottom_input_wrap {
      background: rgba(0, 0, 0, 0.2);
    }
    .bottom_btn_wrap {
      .bottom_btn_item {
        background: rgba(0, 0, 0, 0.2);
      }
    }
  }
}

// 底部操作按钮区
.footer_btn_wrap {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 920;
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  width: 100%;
  height: 62px;
  padding: 12px 40px 0;
  background: #303953;
  .footer_center {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    width: 48.6%;
    min-width: 700px;
    .footer_btn_item_wrap {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 42px;
      color: #fff;
      font-size: 10px;
      line-height: 14px;
      white-space: nowrap;
      cursor: pointer;
      .badge {
        position: absolute;
        top: -4px;
        left: 50%;
        min-width: 12px;
        height: 12px;
        margin-left: 4px;
        padding: 1px;
        color: #fff;
        font-size: 9px;
        line-height: 10px;
        text-align: center;
        background: #ff180d;
        border-radius: 6px;
      }
    }
  }
  .footer_right {
    display: flex;
    flex-shrink: 0;
    flex-wrap: nowrap;
    justify-content: space-between;
    .footer_btn_item_wrap {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 48px;
      color: #fff;
      font-size: 12px;
      line-height: 17px;
      white-space: nowrap;
      cursor: pointer;
    }
    .footer_end_btn {
      width: 60px;
      height: 32px;
      margin-top: 2px;
      color: #fff;
      font-size: 14px;
      line-height: 32px;
      text-align: center;
      background: #ff5f57;
      border-radius: 20px;
      cursor: pointer;
    }
  }
  :global {
    .ant-dropdown-menu-item {
      padding: 6px 16px;
      color: #333;
    }
  }
}

// 顶部消息通知
.message_notify_wrap {
  position: absolute;
  top: 76px;
  left: 0;
  z-index: 900;
  display: flex;
  justify-content: center;
  width: 100%;
  padding: 0 24px;
  .message_content {
    width: 320px;
    height: 24px;
    color: #fff;
    font-size: 12px;
    line-height: 24px;
    text-align: center;
    border-radius: 2px;
    cursor: pointer;
    & > span {
      margin: 0 4px;
      color: #0095ff;
    }
  }
  // 在深色背景里
  &.in_black {
    .message_content {
      background: rgba(255, 255, 255, 0.1);
    }
  }
  // 在浅色背景里
  &.in_white {
    .message_content {
      background: rgba(0, 0, 0, 0.2);
    }
  }
}

// 申请连麦小手
.apply_speak_status_btn {
  position: absolute;
  right: 56px;
  bottom: 254px;
  z-index: 900;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  .apply_speak_count {
    position: absolute;
    top: -1px;
    right: -2px;
    width: 12px;
    height: 12px;
    color: #fff;
    font-size: 9px;
    line-height: 13px;
    white-space: nowrap;
    text-align: center;
    background: #ff180d;
    border-radius: 50%;
  }
  // 在深色背景里
  &.in_black {
    background: rgba(255, 255, 255, 0.1);
  }
  // 在浅色背景里
  &.in_white {
    background: rgba(0, 0, 0, 0.2);
  }
}

// 强制下麦浮动按钮
.forced_mute_btn_wrap {
  position: absolute;
  right: 0;
  bottom: 138px;
  z-index: 910;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  height: 37px;
  padding: 0 8px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20px 0 0 20px;
  box-shadow: 0 -4px 4px 0 rgba(0, 0, 0, 0.1);
  .user_name {
    color: #000;
    font-size: 14px;
    white-space: nowrap;
  }
  .mute_btn {
    width: 64px;
    height: 21px;
    color: #fff;
    font-size: 12px;
    line-height: 21px;
    text-align: center;
    background: #ff5f57;
    border-radius: 14px;
    cursor: pointer;
  }
}

// 打call连击消息
.call_group_msg_wrap {
  position: absolute;
  bottom: 263px;
  left: 0;
  z-index: 910;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  height: 32px;
  padding-right: 28px;
  padding-left: 16px;
  color: #fff;
  font-weight: 500;
  font-size: 15px;
  line-height: 21px;
  background: linear-gradient(90deg, rgba(255, 222, 141, 0.8) 0%, rgba(255, 222, 141, 0) 100%);
}

// 有课件时的悬浮按钮
.ppt_list_btn_wrap {
  position: absolute;
  bottom: 62px;
  left: 50%;
  z-index: 910;
  display: flex;
  display: none;
  flex-wrap: nowrap;
  align-items: center;
  height: 34px;
  padding: 0 8px;
  color: #fff;
  font-size: 12px;
  line-height: 17px;
  column-gap: 8px;
  background: #636d8b;
  border-radius: 4px;
  transform: translateX(-50%);
  .separator_line {
    width: 0;
    height: 15px;
    margin: 0 4px;
    border-left: 1px solid #fff;
  }
  .ppt_list_btn {
    display: flex;
    align-items: center;
    column-gap: 4px;
    cursor: pointer;
  }
}

// 正在讲话悬浮信息
.now_speaking_user_wrap {
  position: absolute;
  top: 84px;
  right: 162px;
  z-index: 910;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  width: 175px;
  height: 28px;
  padding: 0 8px;
  column-gap: 8px;
  background: #303953;
  border-radius: 2px;
  .separator_line {
    width: 0;
    height: 20px;
    border-left: 1px solid #68728e;
  }
  .speaking_user_content {
    display: flex;
    overflow: hidden;
    color: #fff;
    font-size: 12px;
    line-height: 17px;
    white-space: nowrap;
    text-overflow: ellipsis;
    .speaking_user_item {
      margin-right: 8px;
    }
  }
}
