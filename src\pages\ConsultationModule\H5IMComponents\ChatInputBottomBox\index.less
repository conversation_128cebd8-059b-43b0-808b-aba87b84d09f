.wrap {

  width: 100%;
  background: #fff;
  // align-items: center;
  position: relative;
  .close_say {
    position: absolute;
    bottom: 58px;
    left: 0;
    background: rgba(0, 0, 0, 0.4);
    height: 130px;
    width: 100%;
    border-radius: 100% 100% 0 0;
    text-align: center;
    user-select: none;
    &>span {
      font-size: 16px;
      color: #FFFFFF;
      line-height: 80px;
    }
  }

  .content {
    padding: 9px 12px;
    display: flex;
    align-items: center;
    position: relative;
    .send_wrap {
      position: absolute;
      right: 22px;
      bottom: -208px;
      width: 90px;
      height: 45px;
      background: #fff;
      opacity: 0.8;
    }

    .send {
      position: absolute;
      right: 16px;
      bottom: -190px;
      display: block;
      width: 64px;
      height: 36px;
      background: #0095FF;
      border-radius: 4px 4px 4px 4px;
      opacity: 1;
      text-align: center;
      line-height: 36px;
      font-size: 14px;
      font-weight: 400;
      color: #FFFFFF;
    }

    .chat_voice,
    .chat_emote,
    .chat_other {
      width: 32px;
      height: 32px;
      user-select: none;
      &>img {
        width: 100%;
        height: 100%;
      }
    }
    .send_text{
      background: #0095FF;
      border-radius: 4px 4px 4px 4px;
      padding: 4px 10px;
      margin-left: 8px;
      font-size: 14px;
      font-weight: 400;
      color: #FFFFFF;
    }

    .chat_voice {
      margin-right: 12px;
    }

    .chat_emote,
    .chat_other {
      margin-left: 12px;
    }

    .chat_input {
      flex: 1;
      height: 40px;
      background: #F5F5F5;
      border-radius: 22px 22px 22px 22px;

      .input {
        width: 100%;
        border: 0;
        height: 40px;
        background: #F5F5F5;
        border-radius: 22px 22px 22px 22px;
        outline: none;
        padding: 0 12px;
      }
    }

    .chat_say {
      flex: 1;
      height: 40px;
      background: #F5F5F5;
      border-radius: 22px 22px 22px 22px;
      text-align: center;
      line-height: 40px;
      position: relative;
      user-select: none;
      .say::before {
        content: "正在说话";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 14px;
        font-weight: 400;
        color: #191919;
        }
      .say,.an_say {
        -moz-user-select: none;
        -webkit-user-select: none;
        -ms-user-select: none;
        -khtml-user-select: none;
        user-select: none;
        font-size: 14px;
        font-weight: 400;
        color: #191919;
      }
    }
  }

  .emoji_content {
    height: 205px;
    overflow: auto;
    width: 100%;
    background: #fff;
    border-top: 1px solid #EEEEEE;
    padding: 11px 8px 0;
    user-select: none;
    .emoji_icon {
      display: inline-block;
      width: 28px;
      height: 28px;
      margin: 0 8px 16px;

      &>img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .other_content {
    user-select: none;
    height: 205px;
    width: 100%;
    background: #fff;
    border-top: 1px solid #EEEEEE;
    padding: 16px 32px;
    display: flex;

    .upload_photo {
      width: 52px;
      text-align: center;
      margin-bottom: 0;
    }

    .upload_video {
      width: 52px;
      text-align: center;
      margin-bottom: 0;
      margin-left: 34px;
    }

    .upload_characters {
      display: block;
      margin-top: 11px;
      font-size: 12px;
      font-weight: 400;
      color: #666666;
    }

    .upload_photo_icon,
    .upload_video_icon {
      display: inline-block;
      width: 52px;
      height: 52px;
      cursor: pointer;
      &>img {
        width: 100%;
        height: 100%;
      }
    }
    .upload_photo_icon{
      background: url('../../../../assets/Consultation/H5/upload_photo_icon.png') no-repeat;
      background-size: 100% 100%;
    }
    .upload_video_icon{
      background: url('../../../../assets/Consultation/H5/upload_video_icon.png') no-repeat;
      background-size: 100% 100%;
    }

    .upload_video_input{
      width: 52px;
      height: 52px;
      overflow: hidden;
      opacity: 0;
    }
    .edit_head_picture {
      :global {
        .ant-upload.ant-upload-select-picture-card {
          height: 52px;
          width: 52px;
          display: block!important;
          background: transparent;
          border: 0;
          margin-bottom: 0;
        }
      }
    }
  }
}