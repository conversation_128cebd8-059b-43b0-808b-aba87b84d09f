
import request from "@/utils/request";
import { getOperatingEnv } from "@/utils/utils";
import { stringify } from "qs";


// 登录后获取该用户的IM秘钥信息
export const getLatelyPublishUser = (params) => {
  return request(`/api/server/squareRecommend/getLatelyPublishUser`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

// 推荐-分页获取内容
export const getList = (params) => {
  return request(`/api/server/squareRecommend/getList`, {
    method: 'GET',
    params: {
     ...params,
    },
  });
}

// 获取运营后台设置前3条信息
export const getWaterfallManageData = (params) => {
  return request(`/api/server/squareRecommend/getWaterfallManageData`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

// 图文评论列表
export const getCommentList = (params) => {
  return request(`/api/server/imageTextCommentsH5/commentsList`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

// 获取图文详情
export const imgTextInfoById = (params) => {
  return request(`/api/server/imageTextInfoH5/imgTextInfoById`, {
    method: 'GET',
    params: {
    ...params,
    },
  });
}


// 评论下的回复列表
export const commentsDetailList = (params) => {
  return request(`/api/server/imageTextCommentsH5/commentsDetailList`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

// 评论或回复评论或引用回复评论
export const saveCommentsOrReply = (params) => {
  return request(`/api/server/imageTextCommentsH5/saveCommentsOrReply`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

// 图文点赞与取消点赞 imageTextLikeOrCancel
export const imageTextLikeOrCancel = (params) => {
  return request(`/api/server/imageTextSpotLikeH5/imageTextLikeOrCancel`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

// 评论/回复点赞
export const commentsLike = (params) => {
  return request(`/api/server/imageTextCommentsH5/commentsLike`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

// 热门话题排行榜 /server/h5ImageTopic/getHotList
export const getHotList = (params) => {
  return request(`/api/server/h5ImageTopic/getHotList`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

// 话题-分页获取内容 /server/squareRecommend/getTopicList
/**
 * pageNum  是 页码
   pageSize  是 条数
   topicId  是 话题ID
 * @param params
 */
export const getTopicList = (params) => {
  return request(`/api/server/squareRecommend/getTopicList`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

// 用户个人主页（图文：文章，帖子 外链、草稿 ）
export const personImageTextList = (params) => {
  const { page,size } = params;
  // /server/imageTextInfoH5/personImageTextList
  return request(`/api/server/imageTextInfoH5/personImageTextList?${stringify({page,size})}`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

// 专家个人主页（图文：文章，帖子 外链 ）
/*
* pageNum	      是	页码
  pageSize	    是	条数
  expertsUserId	否	专家用户ID
  imageTextType	否	图文类型 1.文章 2.帖子 3.外链
* */
export const getExpertsPersonList = (params) => {
  const { page,size } = params;
  return request(`/api/server/squareRecommend/getExpertsPersonList`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

/**
 * 获取广场页展示的话题(“大家都在聊”)
 */
export async function getSquareShowTopic(params) {
  return request(`/api/server/h5ImageTopic/get-square-show-topic`, {
    method: 'GET',
    params,
  });
}
