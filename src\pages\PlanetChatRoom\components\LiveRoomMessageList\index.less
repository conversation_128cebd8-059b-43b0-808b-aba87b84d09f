/* 隐藏动态区域的滚动条 */
.liveRoom_msg_list::-webkit-scrollbar {
  display: none;
}

.liveRoom_msg_list_Warp {
  // height: 216px;
  width: 260px;
  position: absolute;
  top: calc(45% + 3vh);
  left: 20px;
  z-index: 2;
  // background: blanche<PERSON><PERSON>;
}

.liveRoom_msg_list_Warp_PC {
  top: calc(48% + 3vh);
}

// 消息列表
.liveRoom_msg_list {
  width: 100%;
  height: 26vh;
  overflow-y: auto;
  scrollbar-width: none; /* 隐藏火狐浏览器的滚动条 */
  -ms-overflow-style: none; /* 隐藏 IE 和 Edge 浏览器的滚动条 */

  .avatar_msg_item {
    color: #FFFFFF;
    display: flex;
    font-size: 12px;
    background: rgba(0,0,0,0.3);
    width: max-content;
    max-width: 100%;
    align-items: flex-start;
    border-radius: 16px 16px 16px 16px;
    padding-left: 5px;
    padding-top: 5px;
    padding-bottom: 5px;
    padding-right: 5px;
    margin-bottom: 6px;
  }
  .avatar_msg_avatar {
    width: 24px;
    height: 24px;
    border-radius:50%;
    background: #FFF;
    margin-right: 8px;

    .msg_content_item_icon_img {
      width: 100%;
      height: 100%;
      border-radius:50%;
    }
  }

  .text_msg {
    display: flex;
    width: calc(100% - 45PX);
  }

  .avatar_msg_name {
    margin-right: 0px;
    display: flex;
    .avatar_msg_name_text {
      max-width:50px;
      overflow:hidden;
      text-overflow:ellipsis;
      white-space:nowrap;
    }
  }

  .avatar_msg_content {
    margin-right: 5px;
    word-break: break-all;
    color: #FFFFFF;
  }

  .msg_content_item_msgContent_emcee {
    color: #FFFFFF;
  }

  .HorizontalLiveRoom_send_guzhang_icon {
    width: 18px;
    height: 18px;
    background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_send_guzhang_icon.png') no-repeat;
    background-size: 18px 18px;
    display: inline-block;
    margin-right: 4px;
  }

  .HorizontalLiveRoom_send_flowers_content_icon {
    width: 18px;
    height: 18px;
    background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_send_flowers_content_icon.png') no-repeat;
    background-size: 18px 18px;
    display: inline-block
  }

  .HorizontalLiveRoom_send_message_icon {
    // width: 16px;
    // height: 13px;
    // background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_send_message_icon.png') no-repeat;
    // background-size: 16px 13px;
    color: #FCBD33;
    display: inline-block;
    position: relative;
    font-style: italic;
    top: -1.5px;
    font-weight: 800;
  }
}



.inputWarpByPC {
  flex: 1;
  position: relative;
  width: 332px;
  .input {
    width: 332px;
    height: 32px;
    font-size: 12px;
    color: #fff;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 20px 20px 20px 20px;
    opacity: 1;
    padding-left: 16px;
    padding-right: 16px;
    // cursor: pointer;
  }
  .input:hover {
    border: 1px solid rgba(0, 0, 0, 0.3);
  }
  .input:active {
    border: 1px solid rgba(0, 0, 0, 0.3);
  }
  .input::placeholder {
    color: #fff;
  }
  .input:focus-visible {
    border: 1px solid rgba(186, 186, 186, 0.3);
    outline: -webkit-focus-ring-color none;
  }
  .OpenDanmuBtn {
    width: 20px;
    height: 20px;
    background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_open_danmu_Icon.png') no-repeat;
    background-size: cover;
    opacity: 1;
    user-select: none;
    cursor: pointer;
    margin-right: 20px;
    position: absolute;
    right: 0;
    top:6px;
    z-index: 5;
    user-select: none;
    cursor: pointer;
  }

  .OpenDanmuBtnHidden {
    width: 20px;
    height: 20px;
    background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_hidden_danmu_Icon.png') no-repeat;
    background-size: cover;
    opacity: 1;
    user-select: none;
    cursor: pointer;
    margin-right: 20px;
    position: absolute;
    right: 0;
    top:6px;
    z-index: 5;
    user-select: none;
    cursor: pointer;
  }
}
