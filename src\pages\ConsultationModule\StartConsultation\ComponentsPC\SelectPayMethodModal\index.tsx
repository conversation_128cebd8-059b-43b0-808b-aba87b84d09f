/**
 * @Description: 选择支付方式弹窗
 */
import React, { useRef, useState, useEffect } from 'react'
import classNames from 'classnames'
import { Modal, Carousel, Button, Checkbox } from 'antd'
import styles from './index.less'

interface PropsType {
  visible: boolean, // true，false
  usableFreeTimes: number, // 剩余免费次数
  onCancel: any, // 关闭弹窗
  onClickPayBtn: any, // 点击确认支付回调
  loading: any, // loading
  vipUnitPrice: any, // 支付金额
}

const Index: React.FC<PropsType> = (props: any) => {
  const {
    visible,
    usableFreeTimes, // 剩余免费次数
    onCancel, // 关闭弹窗
    onClickPayBtn, // 确认支付回调
    loading,
    vipUnitPrice, // 支付金额
  } = props
  const [checkedPayMethod, setCheckedPayMethod] = useState(null) // 支付方式，1微信，2支付宝，3免费次数

  useEffect(() => {
    if (visible) {
      if (usableFreeTimes > 0) {
        setCheckedPayMethod(3)
      }
    } else {
      setCheckedPayMethod(null)
    }
  }, [visible])

  // 选择支付方式
  const onChangePayMethod = (e) => {
    setCheckedPayMethod(e.target.value)
  }

  return (
    <Modal
      title="支付"
      className={styles.modal}
      visible={visible}
      onCancel={onCancel}
      width={474}
      footer={null}
      destroyOnClose
    >
      <div className={styles.title}>请选择支付方式</div>
      <div className={styles.checkbox_wrap}>
        <Checkbox.Group value={checkedPayMethod ? [checkedPayMethod] : []}>
          {
            usableFreeTimes > 0 &&
            <>
              <Checkbox value={3} onChange={onChangePayMethod}>免费指导</Checkbox>
              <span className={styles.option_text}>剩余{usableFreeTimes}次</span>
            </>
          }
          <Checkbox value={2} onChange={onChangePayMethod}>支付宝</Checkbox>
          <Checkbox value={1} onChange={onChangePayMethod}>微信支付</Checkbox>
        </Checkbox.Group>
      </div>
      <div className={styles.footer_wrap}>
        {
          checkedPayMethod != 3 &&
          <div className={styles.price_wrap}>合计：<span className={styles.price_value}>¥{vipUnitPrice}</span></div>
        }
        <div className={styles.btn_wrap}>
          <Button onClick={onCancel}>取消</Button>
          <Button type="primary" onClick={() => onClickPayBtn(checkedPayMethod)} loading={loading}>确认支付</Button>
        </div>
      </div>
    </Modal>
  )
}

export default Index
