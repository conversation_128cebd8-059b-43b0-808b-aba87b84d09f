// 关注页面store

export default {
  namespace: 'pcAttention',
  state: {
    tabState: 1, // tab状态
    subTabState: 1,   // PC专家页面，二级tab状态
  },

  effects: {},

  reducers: {
    // 保存数据
    save(state, {payload}) {
      return {
        ...state,
        ...payload,
      }
    },
    // 清空数据
    clean(state, {payload}) {
      return {
        ...state,
        tabState: 1, // tab状态
        subTabState: 1,   // PC专家页面，二级tab状态
      }
    },
  },

  subscriptions: {
    setup({dispatch, history}) {
      return history.listen(({pathname, search}) => {
        if (
          pathname.indexOf('/Case/CaseDetails') == -1 &&
          pathname.indexOf('/PlanetChatRoom/') == -1 &&
          pathname.indexOf('/Expert/ExpertDetails') == -1 &&
          pathname.indexOf('/ConsultationModule/StartConsultation/') == -1 &&
          pathname.indexOf('/CreateGraphicsText/CreateArticle') == -1 &&
          pathname.indexOf('/CreateGraphicsText/CreateArticle/Step2') == -1 &&
          pathname.indexOf('/CreateGraphicsText/CreateExternalLinks') == -1 &&
          pathname.indexOf('/UserInfo/CreateSpaceByPc') == -1
        ) {
          dispatch({
            type: 'clean',
          })
        }
      })
    }
  }
}
