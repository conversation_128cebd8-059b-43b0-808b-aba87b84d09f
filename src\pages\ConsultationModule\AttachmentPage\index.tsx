/**
 * @Description: 移动端指导附件页面
 * @author: 赵斐
 */
import React, { useState, useEffect } from 'react';
import { connect } from "umi";
import { Toast } from 'antd-mobile'
import { Spin, Typography, message } from 'antd';

import styles from './index.less'
// 导航组件
import NavBar from '@/components/NavBar'
// 附件列表
import AttachmentList from '@/pages/ConsultationModule/H5Components/AttachmentList'
// 发送附件弹窗
import AttachmentModal from '@/pages/ConsultationModule/H5Components/AttachmentModal'


const Index: React.FC = (props: any) => {
  const { dispatch, loading } = props;
  const { match, history } = props
  const { params } = match
  const { caseId } = params    // 病例ID
  const { query } = history.location || {}
  const { consultationId, consultationType } = query || {}   // 指导ID\指导类型
  const [attachmentModalVisible, setAttachmentModalVisible] = useState(false)  // 发送附件到邮箱弹窗
  const [state, setState] = useState([])         // 附件列表数据
  const userInfoStr = localStorage.getItem('userInfo')
  const userInfo = userInfoStr ? JSON.parse(userInfoStr) : {}  // 获取本地用户信息

  useEffect(() => {
    getCaseAttachmentList()
  }, [])

  // 查询病例附件列表
  const getCaseAttachmentList = () => {
    dispatch({
      type: "consultation/getCaseAttachmentList",
      payload: {
        caseId,
        userId: userInfo.friUserId
      }
    }).then((res: any) => {
      if (res != undefined) {
        console.log(res)
        let { code, content, msg } = res || {}
        if (code == 200) {
          setState(content)
        } else {
          Toast.show(msg)
        }
      }
    })
  }

  /**
   * 发送指导病例附件到邮箱
   * @param emailAddress   邮箱地址
   */
  const sendEMail = (emailAddress: string) => {
    dispatch({
      type: "consultation/sendConsultationCaseAttachmentEMail",
      payload: {
        getParams: {
          caseId,
          emailAddress,
          userId: userInfo.friUserId
        }
      }
    }).then((res: any) => {
      if (res != undefined) {
        let { code, msg } = res || {}
        if (code == 200) {
          setAttachmentModalVisible(false)
          getCaseAttachmentList()
        } else {
          Toast.show(msg)
        }
      }
    })
  }

  // 复制提示文案
  const onCopy = () => {
    message.success('复制成功')
  }
  const load = !!loading.effects['consultation/getCaseAttachmentList']     // 附件页面loading
  const eMailLoading = !!loading.effects['consultation/sendConsultationCaseAttachmentEMail']   // 发送接口地址
  return (
    <Spin spinning={load}>
      <div className={styles.wrap} id='wrap'>
        <div className={styles.header}>
          <NavBar title="附件" className={styles.header_nav} />
        </div>
        <div className={styles.content}>
          {
            Array.isArray(state) && state.length ? <AttachmentList dataSource={state} isOpenAnnexStatus={true} /> : null
          }
        </div>
        <div className={styles.tips}>
          zip、stl格式的文件，请复制链接在电脑端打开进行下载，或将所有附件发送至您指定的邮箱
        </div>
        {
          Array.isArray(state) && state.length ? <div className={styles.footer} style={/iPhone/.test(navigator.userAgent) ? { paddingBottom: '34px' } : {}}>
            <div className={styles.footer_content}>
              <div className={styles.copy}>
                <Typography.Paragraph copyable={{
                  text: `${location.host}/ConsultationModule/ConsultationDetails?consultationId=${consultationId}&consultationType=${consultationType}`,
                  icon: [<p className={styles.copy_link}>复制链接</p>, <p className={styles.copy_link}>复制链接</p>],
                  tooltips: [false, false],
                  onCopy: onCopy,
                }}></Typography.Paragraph>
              </div>

              <p className={styles.send_to_email} onClick={() => { setAttachmentModalVisible(true) }}>发送至邮箱</p>
            </div>
          </div> : null
        }

        {/* 发送邮件弹窗 */}
        {
          attachmentModalVisible && <AttachmentModal
            visible={attachmentModalVisible}
            dataSource={state}
            onCancel={() => { setAttachmentModalVisible(false) }}
            sendEMail={sendEMail}
            loading={eMailLoading}
          />
        }
      </div>
    </Spin>
  )
}
export default connect(({ consultation, loading }: any) => ({ consultation, loading }))(Index)
