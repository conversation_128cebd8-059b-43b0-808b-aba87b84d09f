import React from 'react'
import classNames from 'classnames'
import styles from './FontSize.less'

// 字体大小
const fontSizeData = ['12', '14', '16', '18', '20', '24', '32']

class FontSize extends React.Component {
  static defaultProps = {
    itemOnClick: () => {},
  }

  constructor(props) {
    super(props)
    this.state = {}
  }

  componentDidMount(): void {

  }

  // 点击字号
  itemOnClick = (value) => {
    this.props.itemOnClick(value + 'px')
  }

  // 阻止默认事件
  onMouseDown = (e) => {
    e.preventDefault()
  }

  render() {
    const { selectedValue } = this.props
    return (
      <div className={styles.font_size_container}>
        {
          fontSizeData.map(item => (
            <div
              key={item}
              className={classNames(styles.font_size_item, {
                // [styles.selected]: selectedValue == item + 'px',
              })}
              style={{fontSize: item + 'px'}}
              onMouseDown={this.onMouseDown}
              onClick={() => this.itemOnClick(item)}
            >
              {item}
            </div>
          ))
        }
      </div>
    )
  }
}

export default FontSize
