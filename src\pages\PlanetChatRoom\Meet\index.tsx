/**
 * 星球空间详情页面-直播间-聊天室
 */
import React, {useEffect, useState, useRef, useCallback} from 'react';
import { connect, Helmet, history, useAliveController } from "umi";
import { debounce, throttle } from 'lodash';
import dynamic from "next/dynamic";
import {stringify} from "qs";
import moment from "moment";
import fullscreen from "fullscreen.js";
import {message, Spin} from "antd";
import { Toast } from 'antd-mobile'
import styles from './index.less';

import TIM from 'tim-js-sdk';
import TIMUploadPlugin from 'tim-upload-plugin';
import TIMProfanityFilterPlugin from 'tim-profanity-filter-plugin';
import TRTC from "trtc-js-sdk";

import {
  clearLocalStateByStreamById,
  getLocalStateByStream,
  getShareRemoteStreamConfig,
  getUrlParam,
  saveLocalStateByStream,
} from "@/utils/utilsByTRTC";

import {
  getH5UserInfo,
} from "@/services/userInfo";

import {
  audience,
  anchor,
  SIGN_IN,
  BULLET_SCREEN,
  SEND_FLOWERS,
  SEND_APPLAUSE,
  SEND_CALL,
  HAND_UP,
  HAND_DOWN,
  FORCED_END,
  UPDATA_STATE,
  ROOM_DISBAND,
  NO_PW_APPLY,
  WHITEBOARD_MSG,
  CAMERA_TOGGLE,
  MICROPHONE_TOGGLE,
  APPLY_RECORD,
  APPLY_RECORD_RESULT,
  MICROPHONE_TOGGLE_RESULT,
  CAMERA_TOGGLE_RESULT,
  TRANSFER_HOST,
  SPACE_GDP_PV,
} from '@/app/config';

import {
  imResponseFormatLocalData,
  WxAppIdByPublicAccount,
  getShareUrl,
  getOperatingEnv,
  backInApp,
  requestMicrophoneInApp,
  requestCameraInApp,
  useDebounce,
  goToHomePage,
  getArrailUrl,
  getViewportHeightWithoutSafeArea,
  checkDownloadAppCardExpiration,
} from "@/utils/utils";

const DynamicRtc = dynamic(import('@/componentsByTRTC/BaseRTC'), { ssr: false });
const DynamicShareRtc = dynamic(import('@/componentsByTRTC/ShareRTC'), { ssr: false });
const DeviceDetectorByDetector = dynamic(import('@/componentsByTRTC/DeviceDetector/detector'), { ssr: false });

import ViewportWrap from "./components/ViewportWrap/index";
import Viewport from "./components/Viewport/index";
import DeviceDetector from '@/componentsByTRTC/DeviceDetector/index';
import PosterModal from "@/pages/Poster/PosterModal"
import DownloadAppCard from "@/components/DownloadAppCard";

// 数据
const userAgent = typeof navigator !== 'undefined' && navigator && navigator.userAgent;
const baseSize = 15;

const Index: React.FC = (props) => {
  // 是否嵌套在5i5ya的iframe中
  const isInIframe = (self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya')

  const { pathname, query } = history.location
  const { drop,clear } = useAliveController();
  // 直播间详情props
  const {
    PlanetChatRoom,
    dispatch,
  } = props;
  const {
    isHorizontalLive:isHorizontalLiveByProps,
    isMobile,
    SpaceInfo,
    isInitialized,
    userInfo,
    screenShareUser,
    handUpList,
    currentUserType,
    ModalVisibleByLeaveMicrophone,
    ModalVisibleByKickedOut,
    isNotLogin,
  } = PlanetChatRoom || {};

  const {
    imAppId,
    trtcAppId,
    imGroupId,
    imUserId,
    userSig,
    roomId,
    isNeedPwd,
    status,
    handUpStatusType,
    handUpType,
    videoList,
    recordType,
    recordStartTime,
    name:nameBySpaceInfo,
    imagePhotoPathShow,
    starSpaceType,
    isMute,
  } = SpaceInfo || {}
  const val = React.useRef();
  let starSpaceTypeText = '会议'
  let starSpaceTypeLianMaiText = starSpaceType == 2 ? '发言' : '连麦';

  const posterModalRef = useRef(null)
  const video = true;
  const audio = true;
  const mode = 'rtc';

  // 直播详情页面state
  const [RTC, setRTC] = useState(null);                                             // rtc对象
  const [shareRTC, setShareRTC] = useState(null);                                   // rtc分享对象
  const [userID, setUserID] = useState('');                                       // 用户id
  const [roomID, setRoomID] = useState('');                                       // 房间id
  const [cameraID, setCameraID] = useState('');                                   // 摄像头id
  const [microphoneID, setMicrophoneID] = useState('');                           // 麦克风id
  const [timObj, setTimObj] = useState(null);                                      // im对象
  const [localStreamConfig, setLocalStreamConfig] = useState(null);                // 本地流配置
  const [remoteStreamConfigList, setRemoteStreamConfigList] = useState([]);      // 远端流配置
  const [isJoined, setIsJoined] = useState(false);                              // 是否加入
  const [isPublished, setIsPublished] = useState(false);                        // 是否发布流
  const [userRole, setUserRole] = useState(anchor);                                       // 设置默认角色
  const [mountFlag, setMountFlag] = useState(false);                            // 是否挂载
  const [pwdArray, setPwdArray] = useState([null,null,null,null]);                // 空间密码校验
  const [loadingCheckSpacePassword, setLoadingCheckSpacePassword] = useState(null);// 空间密码校验的请求是否进行中
  const [loadingByCheckDevices, setLoadingByCheckDevices] = useState(null);        // 是否展示loading
  const [startTime, setStartTime] = useState(null);                                // 录制开始时间
  const [elapsedTime, setElapsedTime] = useState(0);                            // 经过的时间
  const [loadingByPage,setLoadingByPage] = useState(null);                         // 页面loading
  const [maskVisible, setMaskVisible ] = useState(false)                        // 分享状态
  const [SpaceInfoObj, setSpaceInfoObj] = useState(null)                           // 空间资料
  const [isHorizontalLive, setIsHorizontalLive] = useState(null)                   // 是否横屏直播

  const [msgGroupId,setMsgGroupId] = useState(`${moment().format('YYYYMMDDHHmmss')}${Math.random().toString().slice(2,6)}`);

  const [clickNumByAPPLAUSE,setClickNumByAPPLAUSE] = useState(0);

  const [clickNumByFLOWERS,setClickNumByFLOWERS] = useState(0);

  const [clickNumByCALL,setClickNumByCALL] = useState(0);

  const [debounceTimeout,setDebounceTimeout] = useState(null);

  const RefByHorizontalLiveRoom = useRef(null);
  const RefByViewportWrap = useRef(null);
  const UerInfo = JSON.parse(localStorage.getItem('userInfo')) || {};

  // 远端流中屏幕分享流或白板流
  const shareHostRemoteStreamConfig = getShareRemoteStreamConfig(SpaceInfo,remoteStreamConfigList)
  // 初始化方法
  useEffect(async () => {
    let isHorizontalLiveByPrams = getUrlParam('isHorizontalLive') == 1
    await cleanRTC();
    await cleanVal();
    await dispatch({ type: 'PlanetChatRoom/clean'});
    await dispatch({ type: 'PlanetChatRoom/closeSmallWindow'});  // 关闭直播间小窗口
    await dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {
        isNotLogin: UerInfo && UerInfo.friUserId ? false : true,
        ModalVisibleByUserTokenInvalid: UerInfo && UerInfo.friUserId ? false : true, // 是否显示弹窗(用户未登录)
      } // 是否登录
    })
    const userAgent = typeof navigator !== 'undefined' && navigator && navigator.userAgent;
    const IsMobile = Boolean(userAgent.match(/Android|BlackBerry|iPhone|iPad|iPod|Opera Mini|IEMobile|WPDesktop/i));
    if(!IsMobile){
      let isHorizontalLiveByParams = isHorizontalLiveByPrams ? isHorizontalLiveByPrams : !IsMobile
      await dispatch({
        type: 'PlanetChatRoom/setState',
        payload: {
          ModalVisibleByOrientationWrong: false,
          isHorizontalLive: isHorizontalLiveByParams,
        }
      })
    } else {
      // 页面挂载完成获取当前屏幕方向
      const orientation = window.orientation;
      await dispatch({
        type: 'PlanetChatRoom/setState',
        payload: {
          isHorizontalLive: orientation === 90 || orientation === -90,
        }
      })
    }
    await callBackByUPDATASTATE(true);
    message.config({
      duration: 3,
      maxCount: 1,
    });
    window.document.body.style.overflow = 'auto';
    onSize();
    window.addEventListener('orientationchange', onOrientationchange);
    window.addEventListener('orientationchange', onSize);
    // 验证下载Friday App是否过期
    checkDownloadAppCardExpiration();
    // 移动端使用是检查页面是否在前台
    if(isMobile) {
      document.addEventListener('visibilitychange', handleVisibilityChange);
    }
  }, []);


  // 补充判定页面是否在前台展示状态
  const handleVisibilityChange= useDebounce(()=> {
    // 当前是移动设备上才保持前台逻辑判定
    if (isMobile) {
      if (document.visibilityState === 'hidden') {
        console.log('handleVisibilityChange-未在前台');
        hiddenByMutedAudio();
      } else {
        console.log('handleVisibilityChange-当前在前台中 ');
        activeByMutedAudio();
      }
    }
  },500)

  /**
   * 当会议页面被切换到后台中
   * 则停止设备的麦克风占用
   */
  const hiddenByMutedAudio = useCallback(()=> {
    if(localStreamConfig) {
      RTC?.muteAudio();
      setLocalStreamConfig({...localStreamConfig, mutedAudio: true});
    }
  },[RTC,localStreamConfig])

  /**
   * 当会议回到前台展示时
   * 则回复切换后台麦克风之前的状态
   */
  const activeByMutedAudio = useCallback(()=> {
    let LocalStateByStreamConfig = getLocalStateByStream(props?.match?.params?.RoomId)
    if (localStreamConfig && LocalStateByStreamConfig) {
      let {
        mutedAudio,
      } = LocalStateByStreamConfig
      // 开启静音
      if (mutedAudio) {
        RTC?.muteAudio();
        setLocalStreamConfig({ ...localStreamConfig,  mutedAudio: true });
      }else {
        RTC?.unmuteAudio();
        setLocalStreamConfig({ ...localStreamConfig,  mutedAudio: false });
      }
    }else {
      RTC?.muteAudio();
      setLocalStreamConfig({ ...localStreamConfig,  mutedAudio: true });
    }
  },[localStreamConfig,RTC])



  // 销毁组件关闭直播空间页面的事件方法
  useEffect(() => {
    return ()=> {
      window.document.body.style = ''
      dispatch({ type: 'PlanetChatRoom/clean', })
      setTimObj(null);
      setRTC(null);
      setShareRTC(null);
      // 判定是否需要开启小窗口
      if (!!val.current) {
        // 关闭白板推流
        dispatch({
          type: 'PlanetChatRoom/stopWhiteboardPush',
          payload: { spaceId: val.current.id }
        })
        if (
          (val.current.status == 1
            && val.current.isNeedPwd == 0)
          || (val.current.isNeedPwd == 0
            && val.current.status == 3
            && !!Array.isArray(val.current.videoList)
            && val.current.videoList.length > 0
          )
        ) {
          // 开启直播间小窗口
          dispatch({
            type: 'PlanetChatRoom/showSmallWindow',
            payload: {
              isShowLiveSmallWindow: val.current.id,
            }
          });
        }
        // 关闭空间/异常退出等，需要关闭空间，记录用时（录播调)
        if (val.current.status == 3 && val.current.joinRandStr){
          clear().then(()=>{});
          dispatch({
            type: 'PlanetChatRoom/closeSpaceWindow',
            payload: {
              spaceId:val.current.id,      //  [string] 是 空间ID
              joinRandStr:val.current.joinRandStr,  // [string] 是进入时的字符串，从详情中取
            }
          })
        }
      }
    }
  },[])

  // 初始化RTC完成后
  useEffect(() => {
    return async ()=> {
      // 本地流发布状态下 停止发布
      if (RTC && (RTC.isPublished || RTC.isPublishing)) {
        await handleUnPublish(); // 关闭本地流
      }
      // 加入房间状态下 退出房间
      if (RTC && RTC.isJoined) {
        await handleLeave(); // 退出房间
      }
      RTC && RTC.destroyLocalStream();
    }
  },[RTC])


  // 保存空间详情信息
  useEffect(() => {
    const {
      status  // 状态：1直播中、2预约中、3弹幕轰炸中
    } = SpaceInfoObj || {};
    val.current = null;
    val.current = {
        ...SpaceInfoObj,
        currentUserType: currentUserType, // currentUserType: 1主播 2嘉宾 3观众
    };
  },[SpaceInfoObj])

  useEffect(() => {
    if (!!isPublished && currentUserType == 1 && recordType == 1) {
      message.success(`您已开始录制`)
    }
  },[isPublished])


  useEffect(() => {
    setLoadingByPage(false); // 设置页面加载状态
    // 销毁组件时 关闭本地流并退出房间
    return async ()=> {
      // 当在连麦状态下退出连麦状态
      if(val.current && val.current.handUpStatusType == 1) {
        await operateHandUp({
          statusType:3,
          wxUserId: val.current.wxUserId,
          guestUserId: val.current.wxUserId,
          imUserId: val.current.imUserId
        })
        await sendMessageByIm({dataType: HAND_DOWN, description: '1'})
      }

      // 退出tim直播群
      if (!!timObj) {
        // 补充销毁接收信息事件逻辑
        timObj.off(TIM.EVENT.MESSAGE_RECEIVED,onMessageReceived);
        timObj.off(TIM.EVENT.KICKED_OUT, onKickedOut);
      }
    }
  },[timObj])

  // 初始化数据调用完成 初始化配置im和trtc
  useEffect( async () => {
    if(isInitialized) {
      // 更新gpd
      if (query.shareUserId && SpaceInfoObj) {
        shareUpdateByType(query.shareUserId)
      }
      // 初始化页面分享卡片
      if (!!wx && SpaceInfoObj) {
        onShareAppMessage();
      }
      // 获取弹幕消息
      getSpaceBulletScreen()

      // 获取到 imUserId 初始化IM组件和对象
      if (imUserId && !timObj) {
        // 初始化Tim和TRTC对象
        await initializationByIm();
        if (trtcAppId && roomId) {
          await initializationByTRTC();
        }
      }
    }
  },[isInitialized])

  useEffect(() => {
    let intervalId;
    if (recordType == 1) {
      // 1主播 2嘉宾 3观众
      if (currentUserType == 1) { message.success('您已开始录制') }
      intervalId = setInterval(() => {
        const currentTime = moment()
        let IntervalSeconds = currentTime.diff(moment(recordStartTime,'YYYY-MM-DD HH:mm:ss'), 'seconds')
        setElapsedTime(IntervalSeconds);
      }, 1000);
    } else {
      clearInterval(intervalId);
      setElapsedTime(0);
    }
    return () => clearInterval(intervalId);
  }, [recordType]);


  // 监听直播间状态变化,当直播间状态改变
  // 从预约中(2)改变为直播中(1)状态时,则自动加入房间并开启直播流
  useEffect(() => {
    // 当前直播间是开始状态则直接加入房间
    // isNeedPwd 需要输入密码 0：不需要  1需要
    // 直播状态下 且 不需要密码 且 未加入房间时
    // 加入房间 开启直播流
    // 状态：1直播中、2预约中、3弹幕轰炸中
    if (
      status == 1
      && isNeedPwd == 0
      && RTC
      && !RTC.isJoined
    ) {
      handleJoin();
      dispatch({
        type:'PlanetChatRoom/setState',
        payload:{ ModalVisibleByApplicationSubmitted:false }
      })
    }

    // 如果当前直播间结束状态
    // 直播中(1)状态改变成弹幕轰炸中状态(3),则关闭直播流 退出直播房间
    if (
      status == 3
      && isNeedPwd == 0
      && RTC
      // && RTC.isJoined
    ) {
      handleLeave(); // 退出房间
      RTC && RTC.destroyLocalStream();
      // 清空本地存储的状态数据
      clearLocalStateByStreamById(props?.match?.params?.RoomId);
    }

    if (
      status == 2
      && isNeedPwd == 0
      && !!RTC
      && !!RTC.isJoined
    ) {
      handleLeave(); // 退出房间
      RTC && RTC.destroyLocalStream();
      // 清空本地存储的状态数据
      clearLocalStateByStreamById(props?.match?.params?.RoomId);
    }

    // 需要密码
    // 2024-04-10 变更说明 有密码的会议，在主持人/参会者分享带密码的链接后，受邀人打开链接，仍需要输入会议密码。
    /*if(isNeedPwd == 1 && query && query.pwd) {
      // 当前地址栏携带密码则直接校验密码
      // let vodByPassword = query.pwd ? getDAesString(query.pwd,'arrail-dentail&2', 'arrail-dentail&3') : query.pwd;
      checkSpacePassword(query.pwd);
    }*/
  },[status,isNeedPwd])

  // 完成初始化配置后 判定当前状态开启直播流
  // 直播间状态 状态：1直播中、2预约中、3弹幕轰炸中
  // 当直播间状态在直播中时,则直接加入并开启直播流
  useEffect(() => {
    // 当前直播间是开始状态则直接加入房间
    // isNeedPwd 需要输入密码 0：不需要  1需要
    if (
      status == 1
      && isNeedPwd == 0
      && !loadingByCheckDevices
      && RTC
    ) { // 直播中状态
      handleJoin();
    }
  },[loadingByCheckDevices])

  useEffect(() => {
    // 当前直播间是开始状态则直接加入房间
    // isNeedPwd 需要输入密码 0：不需要  1需要
    // 状态：1直播中、2预约中、3弹幕轰炸中
    if (
      status == 1
      && isNeedPwd == 0
      && RTC
      && !RTC.isJoined
      && (currentUserType == 3 && handUpStatusType != 1)
    ) { // 直播中状态
      handleJoin();
    }
  },[RTC])

  // 接收申请连麦转换身份
  useEffect(async () => {
    if(RTC && status == 1) {
      if (handUpStatusType == 1 && currentUserType == 3) {
        // 切换身份 进行连麦
        // await changeRole()  // 切换角色
        await handleJoin();    // 加入房间
        await handlePublish(); // 发布本地流
      } else if (
        handUpStatusType == null
        && currentUserType == 3
        && (RTC && RTC.isPublished || RTC && RTC.isPublishing))
      {
        // ModalVisibleByLeaveMicrophone:false // 取消连麦
        if(!ModalVisibleByLeaveMicrophone) {
          message.info('您已被主持人下麦')
        }
        // await changeRole()    // 切换角色
        await handleUnPublish(); // 关闭本地流
        // await handleLeave();     // 退出直播
        dispatch({
          type: 'PlanetChatRoom/setState',
          payload: { ModalVisibleByLeaveMicrophone:false }
        })
      }else {
        if(!RTC.isPublished && !RTC.isPublishing) {
          // await changeRole()     // 切换角色
        }
      }
    }
    if(handUpStatusType){
      val.current = {
        ...val.current,
        handUpStatusType:handUpStatusType,
        currentUserType: currentUserType,
      };
    }
  },[handUpStatusType])

  // 申请连麦功能被主持人强制关闭-退出连麦-停止发布本地流
  useEffect(async () => {
    /* if (handUpType == 0
      && currentUserType == 3
      && handUpStatusType == 1
      && (RTC && RTC.isPublished || RTC && RTC.isPublishing)
    ) {
      await handleUnPublish(); // 关闭本地流
      await changeRole()     // 切换角色
    } */
  },[handUpType])


  useEffect(() => {
    if (clickNumByAPPLAUSE >= 20 ) {
      setMsgGroupId(`${moment().format('YYYYMMDDHHmmss')}${Math.random().toString().slice(2,6)}`)
      setClickNumByAPPLAUSE(0);
    }
    if(clickNumByFLOWERS >= 20){
      setMsgGroupId(`${moment().format('YYYYMMDDHHmmss')}${Math.random().toString().slice(2,6)}`)
      setClickNumByFLOWERS(0);
    }
    if (clickNumByCALL >= 20) {
      setMsgGroupId(`${moment().format('YYYYMMDDHHmmss')}${Math.random().toString().slice(2,6)}`)
      setClickNumByCALL(0);
    }
  },[clickNumByAPPLAUSE,clickNumByFLOWERS, clickNumByCALL])


  // isHorizontalLive
  useEffect(()=>{
    if(!!isHorizontalLiveByProps) {
      if(isMobile) {
        setTimeout(()=>{
          if(!!isInitialized) {
            try {
              if (!fullscreen.is() && fullscreen.enabled()) {
                const element = document.documentElement; // 整个文档
                // 判断浏览器是否支持全屏API
                try {
                  if (element.requestFullscreen) {
                    element.requestFullscreen(); // 进入全屏模式
                  } else if (element.mozRequestFullScreen) {
                    element.mozRequestFullScreen(); // Firefox
                  } else if (element.webkitRequestFullscreen) {
                    element.webkitRequestFullscreen(); // Chrome、Safari和Opera
                  } else if (element.msRequestFullscreen) {
                    element.msRequestFullscreen(); // IE和Edge
                  }
                } catch (e) {}
              }
            } catch (e) {}
          }
        },500)
      }

      onOrientationchange();
      // 更新状态，表示页面处于横屏模式
      setIsHorizontalLive(isHorizontalLiveByProps)
      dispatch({
        type: 'PlanetChatRoom/setState',
        payload: { isHorizontalLive:isHorizontalLiveByProps }
      })
    }else {
      const isLandscape = window.matchMedia("(orientation: landscape)").matches;
      if (isLandscape) {
        // console.log("屏幕处于[横]屏状态");
        // 在横屏模式下，通过 dispatch 更新状态
        dispatch({
          type: 'PlanetChatRoom/setState',
          payload: {
            // ModalVisibleByVerticalPageWrong: true
          }
        })
      }else {
        // console.log("屏幕处于竖屏状态");
        // 在竖屏模式下，通过 dispatch 更新状态
        dispatch({
          type: 'PlanetChatRoom/setState',
          payload: { ModalVisibleByVerticalPageWrong: false }
        })
      }
      if(isMobile) {
        setTimeout(()=>{
          try {
            if (!!fullscreen.is() && fullscreen.enabled()) {
              // 如果全屏可用且当前处于全屏状态，则退出全屏
              fullscreen && fullscreen.exit()
            }
          }catch(e){
            message.warning('退出全屏失败!')
          }
        },500)
      }
    }
    setIsHorizontalLive(isHorizontalLiveByProps)
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: { isHorizontalLive:isHorizontalLiveByProps }
    })
    onSize();
  },[isHorizontalLiveByProps])



  const onSize = debounce(async ()=> {
    // currentWatchMode: null,       // 当前观看模式 1 竖屏非全屏, 2 竖屏全屏, 3 横屏
    // 是否是PC端
    if (!isMobile) {
      // PC 端是固定font-size 无需改变
      let scale =  1.5;
      document.documentElement.style.fontSize = baseSize * Math.min(scale, 2) + 'px';
      return;
    }
    let isHorizontalLiveByPrams = await dispatch({ type: 'PlanetChatRoom/getIsHorizontalLive' })
    const isLandscape = window.matchMedia("(orientation: landscape)").matches;
    // 移动先判定当前设备方向
    if (isLandscape) {
      // console.log("屏幕处于[横]屏状态");
      dispatch({ type: 'PlanetChatRoom/setState' ,payload: { currentWatchMode: 3 } })
      let scale = document.documentElement.clientWidth / 812;
      document.documentElement.style.fontSize = baseSize * Math.min(scale, 2) + 'px';
    }else {
      // 当前设备是竖屏方向-判定是否处于全屏模式下
      if(isHorizontalLiveByPrams){
        // 全屏模式 - 竖屏状态
        dispatch({ type: 'PlanetChatRoom/setState' ,payload: { currentWatchMode: 2 } })
        let scale = document.documentElement.clientWidth / 400;
        document.documentElement.style.fontSize = baseSize * Math.min(scale, 2) + 'px';
      }else {
        //
        dispatch({ type: 'PlanetChatRoom/setState' ,payload: { currentWatchMode: 1 } })
        let scale = document.documentElement.clientWidth / 375;
        document.documentElement.style.fontSize = baseSize * Math.min(scale, 2) + 'px';
      }
    }
    setTimeout(()=>{
      let DerailWarp = document.querySelector('#DerailWarp');
      let videoContent = document.querySelector('#videoContent');
      let ViewportHeight = getViewportHeightWithoutSafeArea();
      //  message.info(`ViewportHeight :: ${ViewportHeight} ,window.outerHeight:${window.outerHeight},availHeight:${window.screen.availHeight}`)
      if (!!DerailWarp) { DerailWarp.style.height = `${ViewportHeight}px`; }
      if (!!videoContent) {
        if(isMobile){
          videoContent.style.height = !!isHorizontalLiveByPrams ? `${ViewportHeight}px` : '57VW';
        }else {
          videoContent.style.height = '100%'
        }
      }
    },500)
    dispatch({ type: 'PlanetChatRoom/setState',payload: { isMobile: Boolean(userAgent.match(/Android|BlackBerry|iPhone|iPad|iPod|Opera Mini|IEMobile|WPDesktop/i))}})
  },50)


  // 当前是否是横屏展示
  const onOrientationchange = async () =>{
    if (!isMobile) { return }
    let isHorizontalLiveByPrams = await dispatch({
      type: 'PlanetChatRoom/getIsHorizontalLive',
    })
    if(!!isHorizontalLiveByPrams) {
      // 开启全屏模式
      var orientation = window.orientation;
      if (orientation === 90 || orientation === -90) {
        // 横屏
        dispatch({
          type: 'PlanetChatRoom/setState',
          payload: { ModalVisibleByOrientationWrong: false,}
        })
      } else {
        // 竖屏
        /*dispatch({
          type: 'PlanetChatRoom/setState',
          payload: {
           ModalVisibleByOrientationWrong: !!isHorizontalLiveByPrams ? true : false,
          }
        })*/
        //
        dispatch({
          type: 'PlanetChatRoom/setState',
          payload: { isHorizontalLive: false,}
        })
      }
    }else {
      // 关闭了全屏模式
      var orientation = window.orientation;
      if (orientation === 90 || orientation === -90) {
        // 横屏- 竖版模式在横屏下展示
        dispatch({
          type: 'PlanetChatRoom/setState',
          payload: { isHorizontalLive: true,}
        })
        changeUrlParams({isHorizontalLive:1})
      }else {
        dispatch({
          type: 'PlanetChatRoom/setState',
          payload: {
            ModalVisibleByOrientationWrong: false,
            ModalVisibleByVerticalPageWrong: false,
          }
        })
      }
    }
  }

  // 微信分享
  // 分享病例
  const onShareAppMessage = () => {
    const url = window.location.href
    const shareUrl = getShareUrl(url)
    dispatch({
      type: 'userInfoStore/getJsapiTicket',
      payload: {
        currentUrl: url,
        appId: WxAppIdByPublicAccount,
      },
    }).then((res:any) => {
      if (res && res.code == 200) {
        wx && wx.config({
          debug: false,
          appId: res.content.appId,
          timestamp: res.content.timestamp,
          nonceStr: res.content.nonceStr,
          signature: res.content.signature,
          jsApiList: [
            'updateAppMessageShareData',
            'updateTimelineShareData',
            'onMenuShareTimeline',
            'onMenuShareAppMessage',
            'onMenuShareQQ',
            'onMenuShareWeibo',
            'onMenuShareQZone',
          ],
        })

        wx && wx.ready(() => {
          const shareDate = {
            title: '【FRIDAY医生星球】牙医都来这里学习和交流！',
            desc: nameBySpaceInfo,
            link: shareUrl,
            imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/DigitalHealthShareImg.png',
          };
          wx.updateAppMessageShareData(shareDate);
          wx.updateTimelineShareData(shareDate);
          wx.onMenuShareTimeline(shareDate);
          wx.onMenuShareAppMessage(shareDate);
          wx.onMenuShareQQ(shareDate);
          wx.onMenuShareWeibo(shareDate);
          wx.onMenuShareQZone(shareDate);
        })
      } else {
        // Toast.show('请求微信配置失败～！')
      }
    })
  }

  // 分享操作更新gdp等数据
  const shareUpdateByType = (shareUserId) => {
    let RoomId = props?.match?.params?.RoomId;
    dispatch({
      type: 'userInfoStore/shareUpdateByType',
      payload: {
        id: RoomId,                                        // 被分享ID(王国、空间)
        shareId: shareUserId,                              // 分享人ID
        type: 3,                                           // 类型(2王国，3空间)
        hostId: SpaceInfoObj.hostUserInfo && SpaceInfoObj.hostUserInfo.wxUserId,     // 空间主持人ID
      }
    }).then(res => {

    }).catch(err => {})
  }

  // 重置发送消息的groupId
  const debouncedResetMsgGroupId = () => {
    if (debounceTimeout) {
      clearTimeout(debounceTimeout);
      setDebounceTimeout(null)
    }
    let debounceTimeoutBy = setTimeout(() => {
      setMsgGroupId(`${moment().format('YYYYMMDDHHmmss')}${Math.random().toString().slice(2, 6)}`);
      setClickNumByAPPLAUSE(0);
      setClickNumByFLOWERS(0);
      setClickNumByCALL(0);
    }, 3000); // 防抖时间设置为 1 秒
    setDebounceTimeout(debounceTimeoutBy);
  };

  // 初始化空间接口
  const userActiveJoinSpace = async () => {
    let RoomId = props?.match?.params?.RoomId;
    let response = await dispatch({
      type: 'PlanetChatRoom/userActiveJoinSpace',
      payload: { spaceId: RoomId },
    });

    if (response && response.code == 401){
      dispatch({
        type:'PlanetChatRoom/setState',
        payload:{ ModalVisibleByUserTokenInvalid:true }
      })
    }else if (response && response.code == 422){
      // ①会议不允许非参会人进入
      dispatch({
        type:'PlanetChatRoom/setState',
        payload:{ ModalVisibleBySpaceRemoved:response.msg ? response.msg : `会议不允许非参会人进入，如需要进入，请联系主持人`}
      })
      return response
    }
  }

  // 更新用户信息
  const getMsgCodeUserInfo = async () => {
    let wxuserId = UerInfo && UerInfo.friUserId;
    if (!!wxuserId) {
      let response = await dispatch({
        type: 'PlanetChatRoom/getMsgCodeUserInfo',
        payload: {},
      });
    }
  }

  // 获取空间信息
  const getSpaceInfo = async () => {
    let wxuserId = UerInfo && UerInfo.friUserId;
    let RoomId = props?.match?.params?.RoomId;

    let response = await dispatch({
      type: 'PlanetChatRoom/getSpaceInfo',
      payload: {
        spaceId: RoomId,
        wxUserId: wxuserId,
      },
    });

    if (response && response.code == 401){
      /*Modal.alert({
        title: '滚去登录',
        content: `${JSON.stringify(response)}`,
        okText: '确定',
        onConfirm: () => {
          userTokenInvalid('/PlanetChatRoom')
        }
      })*/

      dispatch({
        type:'PlanetChatRoom/setState',
        payload:{ ModalVisibleByUserTokenInvalid:true }
      })

    }else if (response && response.code == 422){
      dispatch({
        type:'PlanetChatRoom/setState',
        payload:{ ModalVisibleBySpaceRemoved:response.msg ? response.msg : `该${starSpaceTypeText}已下架!`}
      })
      return response
    }else if (!response || response && response.code != 200) {
      if (response && response.status != 401) {
        message.error(`获取${starSpaceTypeText}信息失败`);
      }
      return response
    } else if(response && response.code == 200 && response.content){
      setSpaceInfoObj(response.content)
      val.current =  {
        ...response.content,
        currentUserType: currentUserType,
      };
      return response
    }
    return response;
  }

  // 获取嘉宾列表信息
  const getGuestList = async () => {
    let wxuserId = UerInfo && UerInfo.friUserId
    let RoomId = props?.match?.params?.RoomId;
    let response = await dispatch({
      type: 'PlanetChatRoom/getGuestListInfo',
      payload: {
        spaceId: RoomId,
        wxUserId: wxuserId,
      },
    });
    if (!response || response && response.code!= 200) {
      if (response && response.status != 401) {
        message.error('获取嘉宾列表信息失败');
      }
      return
    }
  }


  // 获取申请进入会议成员列表
  const getApplyAdmissionList = async (callback) => {
    let RoomId = props?.match?.params?.RoomId;
    let response = await dispatch({
      type: 'PlanetChatRoom/getApplyAdmissionList',
      payload: {
        spaceId: RoomId,
      },
    });
    if (!response || response && response.code!= 200) {
      if (response && response.status != 401) {
        message.error('获取成员列表信息失败');
      }
      return
    }
    callback && callback()
  }

  // 获取空间历史消息
  const getSpaceGroupMsg = async (msgSeq) => {
    const dataByGetSpaceGroupMsg = await dispatch({
      type:'PlanetChatRoom/getSpaceGroupMsg',
      payload: {
        spaceId: props?.match?.params?.RoomId,
        msgSeq:msgSeq,
        pageSize:10,
      },
    })
  }

  // 获取空间弹幕消息
  const getSpaceBulletScreen = async () => {
    const {
      recordEndTime,
      recordStartTime,
      recordDuration
    } = videoList && videoList[0] || {}
    const dataByGetSpaceGroupMsg = await dispatch({
      type:'PlanetChatRoom/getSpaceBulletScreen',
      payload: {
        spaceId: props?.match?.params?.RoomId,
        eventTime:recordStartTime ? recordStartTime : null
      },
    })
  }

  // 初始化TRTC
  // TODO 补充观众不检查设备, 预约状态下的空间不检查设备
  const initializationByTRTC = async () => {
    // 获取房间号和用户id
    /*
      const defaultTextByUserID = getDefaultTextByUserId(imUserId, 'UserID');
      await setUserID(defaultTextByUserID);
      const useStringRoomID = getUrlParam('useStringRoomID') === 'true';
      const defaultTextByRoomId = getDefaultTextByRoomId(roomID, useStringRoomID, 'RoomID');
      await setRoomID(defaultTextByRoomId);
    */
    await setLoadingByCheckDevices(true);
    await setUserID(imUserId);
    await setRoomID(roomId);

    // 修改地址栏参数
    /* changeUrlParams({
      UserID: imUserId,
      RoomID: roomId,
    }) */

    const checkResult = await TRTC.checkSystemRequirements();
    const { result, detail } = checkResult;
    // 无法规避问题
    // （2022-01-19）iOS 15 以下版本，canvas.captureStream 采集出的视频流，无法使用 video 标签播放直播流
    // https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-02-info-webrtc-issues.html#h2-4
    const iOSVersionRegex = /iPhone OS (\d+)/;
    const versionMatch = window.navigator.userAgent.match(iOSVersionRegex);
    // let iosVersion = versionMatch ? parseInt(versionMatch[1], 10) : null;
    if (
      !result
      || !detail.isBrowserSupported
      || !detail.isWebRTCSupported
      // || iosVersion && iosVersion < 15
    ) {
      setLoadingByCheckDevices(false);
      return;
    }

    // 当前为直播状态并且,当前用户不是观众,或者当前用户是观众并且是预约状态
    if (
      status != 3
    ) {
      if((currentUserType != 3 ) || (currentUserType == 3 && handUpStatusType == 1)) {
        let CameraIDByCheckCamera, MicrophoneIDByCheckMicrophone = null;
        let checkCamera,checkMicrophone = false;
        // 申请权限添加判定
        // 网页非APP环境内 通过checkDevices即可弹出网页授权弹窗
        // Android APP内环境需要通过requestCameraInApp和requestMicrophoneInApp来申请APP内的权限
        // ios WebView自发会弹窗申请授权弹窗,与safari浏览器一致
        let  env =  getOperatingEnv()
        let userAgent = navigator.userAgent
        const isAndroid = userAgent.indexOf('Android') > -1 || userAgent.indexOf('Adr') > -1;
        if((env == 5||env == 6) && isAndroid){
          // 检查设备状态
          checkMicrophone = await requestMicrophoneInApp();
          checkCamera = await  requestCameraInApp();
        }else {
          checkMicrophone = await checkDevices('microphone')
          checkCamera = await checkDevices('camera')
        }

        if (checkMicrophone) {
          // 获取麦克风设备
          let MicrophonesDeviceList = await TRTC.getMicrophones();
          console.log('MicrophonesDeviceList123123 :: 获取麦克风列表 --- ',MicrophonesDeviceList);
          MicrophoneIDByCheckMicrophone = await Array.isArray(MicrophonesDeviceList) && MicrophonesDeviceList.length > 0 && MicrophonesDeviceList[0].deviceId;
          await setMicrophoneID(MicrophoneIDByCheckMicrophone)
        }
        if (checkCamera) {
          // 获取摄像头设备
          let CamerasDeviceList = await TRTC.getCameras();
          CameraIDByCheckCamera = await Array.isArray(CamerasDeviceList) && CamerasDeviceList.length > 0 && CamerasDeviceList[0].deviceId;
          await setCameraID(CameraIDByCheckCamera);
        }
        if (checkMicrophone) {
          await showDeviceDetector(CameraIDByCheckCamera, MicrophoneIDByCheckMicrophone);
        }
      }

      await setLoadingByCheckDevices(false);
      if (
        status == 1
        && isNeedPwd == 0
        && RTC
        && !RTC.isJoined
      ) {
        await handleJoin();
        await setLoadingByCheckDevices(false);
      }
    }else {
      await setLoadingByCheckDevices(false);
    }
  }

  // 检查设备状态 获取麦克风和摄像头权限
  const  showDeviceDetector = async (CameraIDByCheckCamera,MicrophoneIDByCheckMicrophone) => {
    if (
      !(CameraIDByCheckCamera && MicrophoneIDByCheckMicrophone)
      && (currentUserType == 1 || currentUserType == 2)
      && !!props.PlanetChatRoom.isLive
    ) {
      await  message.warning('获取设备麦克风和摄像头权限失败,开启检查设备状态');
      await  DeviceDetector && DeviceDetector.show();
    }
  }

  // 修改地址栏地址参数
  const changeUrlParams = (params) => {
    const { pathname,query } = history.location;
    let newQuery = {
      ...query,
      ...params,
    }
    history.replace(`${pathname}?${stringify(newQuery)}`);
  }

  // 检查设备环境 检查麦克风 和 摄像头权限
  const checkDevices = async (deviceType) => {
    let checkType = true;  // 默认检查所有设备的状态
    const checkResult = await TRTC.checkSystemRequirements();
    const { result, detail } = checkResult;

    if (!result) {
      checkType = false
      message.error('您的浏览器环境不支持TRTC，请使用Chrome浏览器体验');
      // return checkType;
    }

    try {
      if(navigator.mediaDevices.getUserMedia) {
        const mediaStream = await navigator.mediaDevices.getUserMedia({
          audio: deviceType === 'microphone',
          video: deviceType === 'camera'
        });
        console.log('mediaStream12313 :: ',mediaStream);
        mediaStream.getTracks()[0].stop();
      }else {
        setLoadingByCheckDevices(false);
        checkType = false
        return checkType;
      }
    } catch (error) {
      // setLoadingByCheckDevices(false);
      // message.error(`检查${deviceType === 'microphone' ? '麦克风' : '摄像头'}设备状态失败！${error.name}`)
      if (error.name === 'NotAllowedError') {
        message.error(`请允许网页访问${deviceType === 'microphone' ? '麦克风' : '摄像头'}的权限！`);
        /*Modal.alert({
          title: 'NotAllowedError',
          content: `请允许网页访问${deviceType === 'microphone' ? '麦克风' : '摄像头'}的权限！`,
          okText: '确定',
          onConfirm: () => {}
        });*/
      } else if (error.name === 'NotFoundError') {
        message.error(`请检查${deviceType === 'microphone' ? '麦克风' : '摄像头'}设备连接是否正常！`);
        /* Modal.alert({
           title: 'NotFoundError',
           content: `请检查${deviceType === 'microphone' ? '麦克风' : '摄像头'}设备连接是否正常！`,
           okText: '确定',
           onConfirm: () => {}
         });*/
      } else if (error.name === 'NotReadableError') {
        message.error(`请检查${deviceType === 'microphone' ? '麦克风' : '摄像头'}设备是否被其它应用占用或未授权应用权限！`);
        // alert(`请检查${deviceType ==='microphone'? '麦克风' : '摄像头'}设备是否被其它应用占用或未授权应用`);
        /*Modal.alert({
          title: 'NotReadableError',
          content: `请检查${deviceType ==='microphone'? '麦克风' : '摄像头'}设备是否被其它应用占用或未授权应用`,
          okText: '确定',
          onConfirm: () => {}
        });*/
        // alert(`请检查${deviceType === 'microphone' ? '麦克风' : '摄像头'}设备是否被其它应用占用或未授权应用权限！`);
      }
      checkType = false
      return checkType;
    }
    return checkType;
  }

  // 接收到直播群消息
  let onMessageReceived = (value)=> {
    // 接收新推送的一对一消息、群组消息、群组提示或群组系统通知。你可以遍历事件。
    // 获取消息列表并将其呈现给UI。
    // event.name - time . event. message_received
    // 事件。data -存储Message对象的数组- [Message]
    const { data } = value || {}
    // 接收自定义[弹幕消息]并转换成弹幕
    if( Array.isArray(data)) {
      data.map((item) => {
        const { payload,to } = item || {}
        // 如果消息来源不是当前直播间直接忽略
        if(to != imGroupId) { return }

        const {
          data: dataByPayload,
          description: descriptionByPayload,
          extension: extensionByPagload,
        } = payload || {}

        // 接收更新GDP和观看人数消息 "SPACE_GDP_PV"
        if(dataByPayload == SPACE_GDP_PV){
          let ObjDescriptionByPayload = descriptionByPayload && JSON.parse(descriptionByPayload)
          const {
            gdp, // GDP数量
            pv   // PV数量
          } = ObjDescriptionByPayload || {}
          // 更新GDP和观看人数
          dispatch({ type:'PlanetChatRoom/updateGDPAndPV', payload: {gdp,pv}})
        }

        // 接收移交主持人事件
        if(dataByPayload == TRANSFER_HOST) {
          let ObjDescriptionByPayload = descriptionByPayload && JSON.parse(descriptionByPayload)
          const {
            imUserId:imUserIdByMsg,
            isAgree:isAgree,
            name:nameByMsg,
          } = ObjDescriptionByPayload || {}

            /*if(imUserIdByMsg == imUserId) {*/
              // dispatch({ type:'PlanetChatRoom/clean' })
              // callBackByUPDATASTATE();
              // RefByViewportWrap.current &&  RefByViewportWrap.current.openGuestToHostTipsModalVisible()
            /*}*/
            if(imUserIdByMsg == imUserId) {
              // dispatch({ type:'PlanetChatRoom/clean' })
              // callBackByUPDATASTATE();
              RefByViewportWrap.current &&  RefByViewportWrap.current.openGuestToHostTipsModalVisible()
            }

          }

          // 参会人点击了保持静音
          if(dataByPayload == MICROPHONE_TOGGLE_RESULT){
            let ObjDescriptionByPayload = descriptionByPayload && JSON.parse(descriptionByPayload)
            const {
              imUserId:imUserIdByMsg,
              isAgree:isAgree,
              name:nameByMsg,
            } = ObjDescriptionByPayload || {}
            if(currentUserType == 1) {
              // 开启保持静音的提醒弹窗
              RefByViewportWrap.current &&  RefByViewportWrap.current.openGuestRefuseOpenMikeTipsModal(nameByMsg);
            }
          }

          // 参会人点击了保持关闭摄像头
          if(dataByPayload == CAMERA_TOGGLE_RESULT) {
            let ObjDescriptionByPayload = descriptionByPayload && JSON.parse(descriptionByPayload)
            const {
              imUserId:imUserIdByMsg,
              isAgree:isAgree,
              name:nameByMsg,
            } = ObjDescriptionByPayload || {}
            if(currentUserType == 1) {
              // 开启保持关闭摄像头的提示弹窗
              RefByViewportWrap.current &&  RefByViewportWrap.current.openGuestRefuseOpenCameraTipsModal(nameByMsg);
            }
          }

          // 接收申请录制
          if(dataByPayload == APPLY_RECORD_RESULT) {
            let ObjDescriptionByPayload = descriptionByPayload && JSON.parse(descriptionByPayload)
            const {
              imUserId:imUserIdByMsg,
              isAgree:isAgree,
              name:nameByMsg,
            } = ObjDescriptionByPayload || {}
            if(imUserIdByMsg == imUserId) {
              if(isAgree) {
                message.success('主持人已同意你的录制申请');
              }else {
                message.warning('主持人已拒绝你的录制申请');
              }
              getSpaceInfo();
            }
          }

          // 申请录制
          if(dataByPayload == APPLY_RECORD) {
            let ObjDescriptionByPayload = descriptionByPayload && JSON.parse(descriptionByPayload)
            if(currentUserType == 1) {
              // 开启申请录制弹窗
              RefByViewportWrap.current &&  RefByViewportWrap.current.openApplyRecordTipsModal(ObjDescriptionByPayload);
            }
          }
          // 主持人请求你开启麦克风
          if(dataByPayload == MICROPHONE_TOGGLE){
            let ObjDescriptionByPayload = descriptionByPayload && JSON.parse(descriptionByPayload)
            const {
              imUserId:imUserIdByMsg,
              isAgree:isAgree,
              name:nameByMsg,
            } = ObjDescriptionByPayload || {}
            const { mutedAudio:mutedAudio } = ObjDescriptionByPayload || {}
            // 指定参会人静音
            if(imUserIdByMsg == imUserId) {
              if (!!mutedAudio) {
                // 解除指定参会人静音
                RefByViewportWrap.current &&  RefByViewportWrap.current.openHostOpenYouMikeTipsModal();
              }else {
                // 开启指定参会人静音
                RefByViewportWrap.current &&  RefByViewportWrap.current.openHostCloseYouMikeTipsModal();
              }
            }
            // 全体静音
            if(imUserIdByMsg == 'ALL') {
              if(mutedAudio) {
                // 解除全体静音
                RefByViewportWrap.current &&  RefByViewportWrap.current.openHostOpenAllMikeTipsModal();
                // 刷新
                callBackByUPDATASTATE();
              }else {
                // 开启全体静音
                RefByViewportWrap.current && RefByViewportWrap.current.toMuteAllAndTips();
                // 刷新
                callBackByUPDATASTATE();
              }
            }
          }

          // 接收关闭或开启指定成员的摄像头
          if(dataByPayload == CAMERA_TOGGLE) {
            let ObjDescriptionByPayload = descriptionByPayload && JSON.parse(descriptionByPayload)
            const {
              imUserId:imUserIdByMsg,
              isAgree:isAgree,
              name:nameByMsg,
            } = ObjDescriptionByPayload || {}
            if(imUserIdByMsg == imUserId) {
              const { mutedVideo } = ObjDescriptionByPayload || {};
              if (!!mutedVideo) {
                RefByViewportWrap.current &&  RefByViewportWrap.current.openHostOpenYouCameraTipsModal();
              }else {
                // 关闭摄像头-tips弹窗（主持人已关闭你的视频）
                RefByViewportWrap.current &&  RefByViewportWrap.current.openHostCloseYouCameraTipsModal();
              }
            }
          }

          // 白板消息的接收 告知用户谁启用了分享课件
          if(dataByPayload == WHITEBOARD_MSG) {
            let objByExtensionByPagload = extensionByPagload && JSON.parse(extensionByPagload)
            message.info(`${objByExtensionByPagload.name}启用分享课件`);
            // 当前嘉宾占用白板 主持人分享白板时退出白板
            if(currentUserType == 2 && objByExtensionByPagload.currentUserType == 1){
              //  dispatch({type: 'PlanetChatRoom/closeTEduBoard', payload: {isOpenTEduBoard: !isOpenTEduBoard}})
            }
          }
          // 自定义消息中的用户信息
          // 接收自定义消息
          // 接收自定义[弹幕消息]并转换成弹幕
          // 平齐开启弹幕功能才发送弹幕
          if (dataByPayload == BULLET_SCREEN) {
            let objByExtensionByPagload = extensionByPagload && JSON.parse(extensionByPagload)
            // RefByHorizontalLiveRoom.current && RefByHorizontalLiveRoom.current.sendDanmu({
            //   text: descriptionByPayload,
            //   userInfoByDanmu:objByExtensionByPagload,
            // });
            RefByViewportWrap.current && RefByViewportWrap.current.sendDanmu({
              text: descriptionByPayload,
              userInfoByDanmu:objByExtensionByPagload,
            });
          }

          // 当接受消息类型是更新直播间状态 则进行更新 直播间权益更新或者 指定更新
          if (dataByPayload == UPDATA_STATE) {
            // 更新指定用户的直播间状态
            let ObjDescriptionByPayload = descriptionByPayload && JSON.parse(descriptionByPayload)
            const {
              imUserId:imUserIdByMsg,
              isAgree:isAgree,
              name:nameByMsg,
            } = ObjDescriptionByPayload || {}
            if(imUserIdByMsg == imUserId) {
              getSpaceInfo()
            }else if(imUserIdByMsg == 'ALL'){
              callBackByUPDATASTATE();
            }
          }
          // 接受当前房间被解散的消息
          if (
            dataByPayload == ROOM_DISBAND
            && currentUserType != 1
          ) {
            dispatch({
              type:'PlanetChatRoom/setState',
              payload:{
                ModalVisibleBySpaceRemoved:`主持人已结束会议`
              },
            })
          }

          // 当有申请连麦或者 申请取消连麦消息时
          // 并且当前是主持人端
          if (
            (dataByPayload == HAND_UP
              || dataByPayload == HAND_DOWN)
            && currentUserType == 1
          ) {
            // 更新连麦列表
            setTimeout(()=>{
              getHandUpList(() => {
                // 回调，弹出顶部消息通知
                if (dataByPayload == HAND_UP) {
                  dispatch({
                    type: 'PlanetChatRoom/setState',
                    payload: {
                      isShowTopMessageNotify: true,
                      topMessageNotifyType: HAND_UP,
                    }
                  })
                  RefByViewportWrap.current && RefByViewportWrap.current.resetTimer2()
                }
              });
            },1000);
          }

          // 当前申请无密码入会列表人员
          if(dataByPayload == NO_PW_APPLY) {
            getGuestList();  // 获取嘉宾列表
            getApplyAdmissionList(() => {
              // 回调，弹出顶部消息通知
              dispatch({
                type: 'PlanetChatRoom/setState',
                payload: {
                  isShowTopMessageNotify: true,
                  topMessageNotifyType: NO_PW_APPLY,
                }
              })
              RefByViewportWrap.current && RefByViewportWrap.current.resetTimer2()
            }); // 获取申请入会成员列表
          }

          // 强制下架直播间
          if (dataByPayload == FORCED_END) {
            if(RTC){
              handleLeave()
              RTC && RTC.destroyLocalStream();
            }
            // getSpaceInfo();
            const  { consultationId } = val.current || {}
            dispatch({
              type:'PlanetChatRoom/setState',
              payload:{ ModalVisibleBySpaceViolation:consultationId ? '视频指导结束' : `该${starSpaceTypeText}因违反平台规范已被关闭`, }
            })
            /*dispatch({
              type:'PlanetChatRoom/clean',
            })*/
        }
      })

      let msgList = []
      data.map((item)=>{
        const { payload,to } = item || {}
        const {
          data: dataByPayload,
        } = payload || {}

        if(to != imGroupId) { return }

        if (dataByPayload == SEND_APPLAUSE) {  // 鼓掌
          let LocalData = imResponseFormatLocalData(item)
          LocalData && msgList.push(LocalData)
          dispatch({ type:'PlanetChatRoom/addSendApplauseCount' })
        }
        if (dataByPayload == SEND_FLOWERS) {   // 送花
          let LocalData = imResponseFormatLocalData(item)
          LocalData && msgList.push(LocalData)
          dispatch({ type:'PlanetChatRoom/addSendFlowersCount' })
        }
        if (dataByPayload == SEND_CALL) {   // 打call
          let LocalData = imResponseFormatLocalData(item)
          LocalData && msgList.push(LocalData)
          dispatch({ type:'PlanetChatRoom/addSendCallCount' })

          const extension = LocalData && LocalData.extension && JSON.parse(LocalData.extension)
          if (extension && extension.msgGroupCount >= 10) {
            dispatch({
              type:'PlanetChatRoom/setState',
              payload: {
                sendCallData: extension,
              }
            })
          }
        }
        if (dataByPayload == SIGN_IN) {        // 签到 打卡
          let LocalData = imResponseFormatLocalData(item)
          LocalData && msgList.push(LocalData)
        }
        if(dataByPayload == BULLET_SCREEN){
          let LocalData = imResponseFormatLocalData(item)
          LocalData && msgList.push(LocalData)
        }
      })


      if (msgList.length > 0) {
        dispatch({
          type:'PlanetChatRoom/updateMsgListBySENDAPPLAUSE',
          payload: { msgList:msgList }
        })
      }
    }
  }
  onMessageReceived = useDebounce(onMessageReceived,10);



  const callBackByUPDATASTATE = async (isOnPageDidMount)=>{
    // 1. 获取当前用户信息 当前用户信息可能存在错误的情况
    await setLoadingByPage(true); // 设置页面加载状态
    let resByUserInfo = await getH5UserInfo(UerInfo.friUserId);
    if (resByUserInfo && resByUserInfo.code == 200 && resByUserInfo.content) {
      localStorage.setItem('userInfo', JSON.stringify({
        ...UerInfo,
        name: resByUserInfo.content.name,
        nickName: resByUserInfo.content.nickName,
        headUrl: resByUserInfo.content.headUrlShow,
        isExperts: resByUserInfo.content.isExperts, // 1 医生，0 不是医生
      }))
    }
    await userActiveJoinSpace();            // 后端初始化空间
    await getMsgCodeUserInfo();             // 更新用户信息
    await getSpaceInfo();                   // 获取空间信息
    await getGuestList();                   // 获取嘉宾列表信息
    // 进入页面初次加载调用，后面更新数据就不调用了
    if (isOnPageDidMount) {
      await getSpaceGroupMsg();               // 获取空间历史消息
    }
    await getApplyAdmissionList();          // 获取成员列表信息
    await getHandUpList();                  // 获取连麦列表
    await getManageMembersInTheMeeting();   // 空间在线用户列表
    await getTranscodeFileBySpace();        // 通过空间ID，进入直播会议后获取转码文件
    // 全部初始化页面接口获取完毕后
    // 执行判定用户身份权限方法,判定用户是否是主播,是否是嘉宾
    // currentUserType: 1主播 2嘉宾 3观众
    await dispatch({
      type: 'PlanetChatRoom/setCurrentUserType',
      payload: {}
    })
    await setLoadingByPage(false); // 设置页面加载状态
    return null;
  }

    // 初始化Im及时通信组件 实例名称定义为"tim"
  const initializationByIm = () => {
    return new Promise((resolve, reject) => {
      setLoadingByPage(true); // 设置页面加载状态
      let options = {
        // SDKAppID: SDKAPPID // 接入时需要将0替换为您的即时通信 IM 应用的 SDKAppID
        SDKAppID: imAppId
      };

      // 创建 SDK 实例，`TIM.create()`方法对于同一个 `SDKAppID` 只会返回同一份实例
      let tim = TIM.create(options); // SDK 实例通常用 tim 表示
      setTimObj(tim)
      // 设置 SDK 日志输出级别，详细分级请参见 setLogLevel https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#setLogLevel 接口的说明</a>
      tim.setLogLevel(0); // 普通级别，日志量较多，接入时建议使用
      // tim.setLogLevel(1); // release 级别，SDK 输出关键信息，生产环境时建议使用
      // 注册腾讯云即时通信 IM 上传插件
      tim.registerPlugin({'tim-upload-plugin': TIMUploadPlugin});
      // 注册腾讯云即时通信 IM 本地审核插件
      tim.registerPlugin({'tim-profanity-filter-plugin': TIMProfanityFilterPlugin});
      // 监听事件，例如：SdkReady
      tim.on(TIM.EVENT.SDK_READY, () => {
        // 当SDK进入ready状态时触发此事件。
        // 当在监听过程中检测到此事件时，
        // 访问端可以调用SDK API(比如消息发送API)来使用SDK的各种特性
        // 当IM登录成功后，保存tim对象

      });

      // 监听事件, 关于收到的消息的事件
      tim.off(TIM.EVENT.MESSAGE_RECEIVED,onMessageReceived);
      tim.on(TIM.EVENT.MESSAGE_RECEIVED, onMessageReceived);

      // 监听被踢出事件
      tim.on(TIM.EVENT.KICKED_OUT, onKickedOut);

      // 网络状态发生改变
      tim.on(TIM.EVENT.NET_STATE_CHANGE, (event)=>{
        /*
          *  TIM.TYPES.NET_STATE_CONNECTED    - 已接入网络
          // TIM.TYPES.NET_STATE_CONNECTING   - 连接中。很可能遇到网络抖动，SDK 在重试。接入侧可根据此状态提示“当前网络不稳定”或“连接中”
          // TIM.TYPES.NET_STATE_DISCONNECTED - 未接入网络。接入侧可根据此状态提示“当前网络不可用”。SDK 仍会继续重试，若用户网络恢复，SDK 会自动同步消息
        */
        if(event.data.state == TIM.TYPES.NET_STATE_CONNECTING){
          let showMessage = throttle(()=>{message.warning('当前网络不稳定');},500)
          showMessage();

        }
        if(event.data.state == TIM.TYPES.NET_STATE_DISCONNECTED){
          let showMessage = throttle(()=>{message.error('当前网络不可用!');},500)
          showMessage();
        }
      });

      // SDK 进入 not ready 状态时触发，此时接入侧将无法使用
      // SDK 发送消息等功能。如果想恢复使用，接入侧需调用 login 接口，驱动 SDK 进入 ready 状态
      tim.on(TIM.EVENT.SDK_NOT_READY, ()=>{
        setLoadingByPage(false); // 设置页面加载状态
      });

      if (imUserId) {
        if (isNotLogin) { // 未登录
          setLoadingByPage(false);  // 设置页面加载状态
          joinGroupByIm(tim);       // 加入当前直播群组
        }else {
          let promise = tim.login({
            userID: imUserId,
            userSig: userSig,
          });
          setLoadingByPage(false);    // 设置页面加载状态
          promise.then( (imResponse)=> {
            if (imResponse.data.repeatLogin === true) {} // 重复登录
            setLoadingByPage(false);  // 设置页面加载状态
            joinGroupByIm(tim);       // 加入当前直播群组
            resolve();
          }).catch( (imError)=> {
            setLoadingByPage(false);  // 设置页面加载状态
            message.error('TIM登录失败!!');
            console.warn('login error:', imError); // Error information
            resolve();
          });
        }
      }
    })
  }

  // 加入直播群
  const joinGroupByIm = async (tim) => {
    // let Groupid = getUrlParam('Groupid')
    let promise = tim.joinGroup({ groupID: imGroupId });
    promise.then(function(imResponse) {
      switch (imResponse.data.status) {
        case TIM.TYPES.JOIN_STATUS_WAIT_APPROVAL:                    // 等待管理员同意
          break;
        case TIM.TYPES.JOIN_STATUS_SUCCESS:                          // 加群成功
          console.log('加群成功 :: ',imResponse.data.group); // 加入的群组资料
          break;
        case TIM.TYPES.JOIN_STATUS_ALREADY_IN_GROUP:                // 已经在群中
          console.log('已经在群 :: ',imResponse.data.group); // 加入的群组资料
          break;
        default:
          break;
      }
    }).catch(function(imError){
      if(imError.code == 10013){
        // 用户已经是当前群成员
      }else {
        console.warn('joinGroup error:', imError); // 申请加群失败的相关信息
        message.error(imError.message);
      }
      /*if (imError.code == 10037) {
        message.error(imError.message);
      }*/
    });
  }

  // 监听被踢出事件 - 例如：多端登录被踢
  let onKickedOut = (event)=> {
    /*
     * 默认情况下，不支持多实例登录，即如果此帐号已在其他页面登录，
     * 若继续在当前页面登录成功，有可能会将其他页面踢下线。
     * 用户被踢下线时会触发事件TIM.EVENT.KICKED_OUT，
     * 用户可在监听到事件后做相应处理。示例如下：
     */
    // TIM.TYPES.KICKED_OUT_MULT_ACCOUNT(Web 端，同一帐号，多页面登录被踢)
    // TIM.TYPES.KICKED_OUT_MULT_DEVICE(同一帐号，多端登录被踢)
    // TIM.TYPES.KICKED_OUT_USERSIG_EXPIRED(签名过期)
    // TIM.TYPES.KICKED_OUT_REST_API(REST API kick 接口踢出。v2.20.0起支持)
    dispatch({
      type:'PlanetChatRoom/setState',
      payload:{ ModalVisibleByKickedOut:true, }
    })
  }
  // 监听被用户被踢下线时立即熔断本地TRTC,保证用户在另一端使用不受到阻碍
  useEffect( ()=>{
    if(!!ModalVisibleByKickedOut){
      if (RTC) {
       RTC.handleUnPublish();
       RTC.handleLeave();
       RTC.destroyLocalStream();
      }
    }
  },[!!ModalVisibleByKickedOut,RTC])


  // 发送im实时消息
  const sendMessageByIm=({dataType, description, relativeTime}: { dataType: any, description: any, relativeTime: any })=>{
    console.log('dataType :: ',dataType,description,relativeTime);

    if (!timObj) { message.error('由于网络异常消息发送失败，请刷新后重试'); return }
    let msgGroupIdBySendMess = null;
    let msgGroupCount = 0;
    if(dataType == SEND_APPLAUSE){
      msgGroupIdBySendMess = `APPLAUSE_${msgGroupId}`;
      msgGroupCount = clickNumByAPPLAUSE + 1;
      setClickNumByAPPLAUSE(clickNumByAPPLAUSE + 1)
    }else if(dataType == SEND_FLOWERS){
      msgGroupIdBySendMess = `FLOWERS_${msgGroupId}`;
      msgGroupCount = clickNumByFLOWERS + 1;
      setClickNumByFLOWERS(clickNumByFLOWERS + 1)
    } else if (dataType == SEND_CALL) {
      msgGroupIdBySendMess = `CALL_${msgGroupId}`;
      msgGroupCount = clickNumByCALL + 1;
      setClickNumByCALL(clickNumByCALL + 1)
    }

    let messageByTim = timObj.createCustomMessage({
      to: imGroupId,
      // 会话类型，取值TIM.TYPES.CONV_C2C(端到端会话) 或 TIM.TYPES.CONV_GROUP(群组会话)
      // conversationType : TIM.TYPES.CONV_GROUP,
      conversationType : TIM.TYPES.CONV_GROUP,
      // 消息优先级，用于群聊（v2.4.2起支持）。如果某个群的消息超过了频率限制，后台会优先下发高优先级的消息，详细请参考：https://cloud.tencent.com/document/product/269/3663#.E6.B6.88.E6.81.AF.E4.BC.98.E5.85.88.E7.BA.A7.E4.B8.8E.E9.A2.91.E7.8E.87.E6.8E.A7.E5.88.B6)
      // 支持的枚举值：TIM.TYPES.MSG_PRIORITY_HIGH, TIM.TYPES.MSG_PRIORITY_NORMAL（默认）, TIM.TYPES.MSG_PRIORITY_LOW, TIM.TYPES.MSG_PRIORITY_LOWEST
      // priority: TIM.TYPES.MSG_PRIORITY_HIGH,
      priority : TIM.TYPES.MSG_PRIORITY_NORMAL,
      payload: {
        data: dataType,             // 用于标识该消息类型消息
        description: description,   // 获取内容
        extension: JSON.stringify({
          ...userInfo,
          headUrlShow: imagePhotoPathShow ? imagePhotoPathShow : userInfo.headUrlShow,
          currentUserType:currentUserType,
          imagePhotoPathShow:imagePhotoPathShow,
          msgGroupId:msgGroupIdBySendMess,
          msgGroupCount:msgGroupCount,
          relativeTime:relativeTime,
        })         // 扩展信息
      }
      // 消息自定义数据（云端保存，会发送到对端，程序卸载重装后还能拉取到，v2.10.2起支持）
      // cloudCustomData: 'your cloud custom data'
    });

    let promise = timObj.sendMessage(messageByTim);
    promise.then((imResponse) => {
      console.log('imResponse123 :: ',imResponse);
      // 发送成功
      let msgList = []
      let { data:{ message } } = imResponse || {};
      let { payload } = message || {}
      let {
        data: dataByPayload,
        description: descriptionByPayload,
        extension:extensionByPagload,
      } = payload || {}
      let objExtensionByPagload = extensionByPagload && JSON.parse(extensionByPagload)

      let msgLocalData = imResponseFormatLocalData(message);
      const { msgSeq } = msgLocalData || {}
      dispatch({
        type:'PlanetChatRoom/setState',
        payload: { newMySnedSeq:msgSeq }
      })
      // 鼓掌
      if (dataByPayload == SEND_APPLAUSE) {
        msgLocalData && msgList.push(msgLocalData)
        dispatch({ type:'PlanetChatRoom/addSendApplauseCount' }) // 鼓掌 +1
      }
      // 送花
      if (dataByPayload == SEND_FLOWERS) {
        msgLocalData && msgList.push(msgLocalData)
        dispatch({ type:'PlanetChatRoom/addSendFlowersCount' }) // 送花 +1
      }
      // 打call
      if (dataByPayload == SEND_CALL) {
        msgLocalData && msgList.push(msgLocalData)
        dispatch({ type:'PlanetChatRoom/addSendFlowersCount' }) // 打call +1

        const extension = msgLocalData && msgLocalData.extension && JSON.parse(msgLocalData.extension)
        if (extension && extension.msgGroupCount >= 10) {
          dispatch({
            type:'PlanetChatRoom/setState',
            payload: {
              sendCallData: extension,
            }
          })
        }
      }
      // 签到 打卡
      if (dataByPayload == SIGN_IN) { msgLocalData && msgList.push(msgLocalData)}

      // 发送弹幕消息
      // isOpenDanmu 并且开启弹幕功能才发送弹幕
      if(dataByPayload == BULLET_SCREEN){
        msgLocalData && msgList.push(msgLocalData)
        let objByExtensionByPagload = extensionByPagload && JSON.parse(extensionByPagload)
        // RefByHorizontalLiveRoom.current && RefByHorizontalLiveRoom.current.sendDanmu({
        //   text: descriptionByPayload,
        //   userInfoByDanmu:objByExtensionByPagload,
        // });
        RefByViewportWrap.current && RefByViewportWrap.current.sendDanmu({
          text: descriptionByPayload,
          userInfoByDanmu:objByExtensionByPagload,
        });
      }

      if (msgList.length > 0) {
        dispatch({
          type:'PlanetChatRoom/updateMsgListBySENDAPPLAUSE',
          payload: { msgList:msgList }
        })
      }

    }).catch(function(imError) {
      // 发送失败
      // console.warn('sendMessage error:', imError);
      message.error(imError.message);
    });

    debouncedResetMsgGroupId()
  }

  // ------------------[TRTC方法]------------------

  // 增加流
  const addStream = (stream) => {
    const streamType = stream.getType();
    const userID = stream.getUserId();
    switch (streamType) {
      case 'local':
        setLocalStreamConfig({
          stream,
          streamType,
          userID,
          hasAudio: audio,
          hasVideo: video,
          mutedAudio: true,
          mutedVideo: true,
          shareDesk: false,
          audioVolume: 0,
          userInfo:JSON.stringify(userInfo)
        });
        break;
      default: {
        setRemoteStreamConfigList((preList) => {
          const newRemoteStreamConfigList = preList.length > 0
            ? preList.filter(streamConfig => !(streamConfig.userID === userID
              && streamConfig.streamType === streamType))
            : [];

          newRemoteStreamConfigList
            .push({
              stream,
              streamType,
              userID,
              hasAudio: stream.hasAudio(),
              hasVideo: stream.hasVideo(),
              mutedAudio: true,
              mutedVideo: true,
              subscribedAudio: true,
              subscribedVideo: true,
              resumeFlag: false,
              audioVolume: 0,
            });
          return newRemoteStreamConfigList;
        });
        break;
      }
    }
  };

  // 修改状态
  const setState = (type, value) => {
    switch (type) {
      case 'join':
        setIsJoined(value);
        break;
      case 'publish':
        setIsPublished(value);
        break;
      default:
        break;
    }
    if(type == 'publish' && value) {
      initSetLocalStreamConfig()
    }
    if(type == 'join' && value) {
      initSetLocalStreamConfig();
      val.current =  {
        ...val.current,
        currentUserType: currentUserType,
      };
    }
  };

  // 更新流数据
  const updateStream = (stream) => {
    if (stream.getType() === 'local') {
      setLocalStreamConfig({
        ...localStreamConfig,
        stream,
        hasAudio: stream.hasAudio(),
        hasVideo: stream.hasVideo(),
      });
    } else {
      setRemoteStreamConfigList(preList => preList.map(config => {
        return (
          config.stream === stream ? {
            ...config,
            stream,
            hasAudio: stream.hasAudio(),
            hasVideo: stream.hasVideo(),
          } : config
        )
      }));
    }
  };
  const debounceBysetRemoteStreamConfigList =useDebounce((stream)=>{
    let streamId = stream.getId()
    setRemoteStreamConfigList(preList => preList.map(config => {
      return (
        config.stream === stream ? {
          ...config,
          stream,
          hasAudio: stream.hasAudio(),
          hasVideo: stream.hasVideo(),
        } : config
      )
    }));
  },1000)


  // 更新对本地流和远端流的操作状态
  const updateStreamConfig = (userID, type, value) => {
    // 更新本地流配置
    if (localStreamConfig && localStreamConfig.userID === userID) {
      const config = {};
      switch (type) {
        case 'audio-volume':
          if (localStreamConfig.audioVolume === value) {
            break;
          }
          config.audioVolume = value;
          break;
        case 'share-desk':
          config.shareDesk = value;
          break;
        case 'uplink-network-quality':
          // PromptDeviceNetworkState(value);
          config.uplinkNetworkQuality = value > 0 ? 6 - value : value;
          break;
        case 'downlink-network-quality':
          config.downlinkNetworkQuality = value > 0 ? 6 - value : value;
          break;
        default:
          break;
      }
      setLocalStreamConfig(prevConfig => ({
        ...prevConfig,
        ...config,
      }));
      return;
    }
    // 更新远端流配置
    const config = {};
    switch (type) {
      case 'mute-audio':
        config.mutedAudio = true;
        break;
      case 'unmute-audio':
        config.mutedAudio = false;
        break;
      case 'mute-video':
        config.mutedVideo = true;
        break;
      case 'unmute-video':
        config.mutedVideo = false;
        break;
      case 'resume-stream':
        config.resumeFlag = true;
        break;
      case 'audio-volume':
        if (config.audioVolume === value) {
          break;
        }
        config.audioVolume = value;
        break;
      default:
        break;
    }

    setRemoteStreamConfigList(preList => preList.map(item => (
      item.userID === userID ? { ...item, ...config } : item
    )));
  };



  // 当前设备网络状态提示 五分钟内只提示一次
  let PromptDeviceNetworkState = throttle((data) => {
    if(data < 3){
      message.success('当前网络状态良好')
    }else {
      message.warning('当前网络状态较差')
    }
  },300000, { 'trailing': false })

  // 处理远端流 streamBar 的响应逻辑
  const handleRemoteChange = async (data) => {
    const remoteStream = data.stream;
    const config = remoteStreamConfigList.find(config => config.stream === remoteStream);
    switch (data.name) {
      case 'subscribedVideo':
        await RTC.handleSubscribe(remoteStream, {
          video: !config.subscribedVideo,
          audio: config.subscribedAudio,
        });

        setRemoteStreamConfigList(preList => preList.map(config => (
          config.stream === remoteStream ? ({
            ...config,
            subscribedVideo: !config.subscribedVideo,
          }) : config
        )));
        break;
      case 'subscribedAudio':
        await RTC.handleSubscribe(remoteStream, {
          video: config.subscribedVideo,
          audio: !config.subscribedAudio,
        });
        setRemoteStreamConfigList(preList => preList.map(config => (
          config.stream === remoteStream ? ({
            ...config,
            subscribedAudio: !config.subscribedAudio,
          }) : config
        )));
        break;
      case 'resumeFlag':
        await RTC.resumeStream(config.stream);
        setRemoteStreamConfigList(preList => preList.map(config => (
          config.stream === remoteStream ? ({
            ...config,
            resumeFlag: !config.resumeFlag,
          }) : config
        )));
      default:
        break;
    }
  };

  // 加入直播间并发布直播流
  const handleJoin = async () => {
    await RTC?.handleJoin().then(async (value) => {
      // console.log('handleJoin123123 : ',currentUserType);
    })

    if (
      currentUserType == 1 // 主持人
      || currentUserType == 2 // 嘉宾
      || (currentUserType == 3 && handUpStatusType == 1) // 正在连麦状态的观众
      // handUpStatusType 申请连麦状态类型：0申请连麦 1接受连麦，默认null
      // handUpType 主持人连麦开启状态  1开启中
    ) {
      // 当前是主播或者嘉宾没有开启麦克风,则弹出弹窗限制推送直播流
      if (microphoneID) {
        await setLoadingByCheckDevices(false);
        await RTC?.handlePublish();
      }else {
        dispatch({
          type:'PlanetChatRoom/setState',
          payload: {
            ModalVisibleByNoMicrophone:true,
          }
        })
      }
    }
  };


  // 离开直播间并取消发布直播流
  const handleLeave = async () => {
    shareRTC && shareRTC.isJoined && shareRTC.handleLeave();
    RTC && RTC.isJoined && await RTC.handleLeave();
    return null;
  }

  const handlePublish = async () => {
    if (userRole === audience) {
      message.error('please change to Anchor', 2000)
      return;
    }
     RTC && await RTC.handlePublish();
  };

  const handleUnPublish = async () => {
    await RTC && RTC.handleUnPublish();
  };

  // 初始化调整本地流状态
  const initSetLocalStreamConfig = async () => {
    let LocalStateByStreamConfig = getLocalStateByStream(props?.match?.params?.RoomId)

    if(!!LocalStateByStreamConfig) {
      // 1主持人开启全体静音  0非静音状态
      if (isMute == 1 && currentUserType != 1) {
        if (LocalStateByStreamConfig) {
          LocalStateByStreamConfig = {
            ...LocalStateByStreamConfig,
            mutedAudio: true,
          }
        }
      } else {
        if (LocalStateByStreamConfig) {
          LocalStateByStreamConfig = {
            ...LocalStateByStreamConfig,
          }
        }
      }
    }else {
      LocalStateByStreamConfig = {
        mutedAudio: true,
        mutedVideo: true,
      }
    }

    if (localStreamConfig && LocalStateByStreamConfig) {
      let {
        mutedAudio,
        mutedVideo,
      } = LocalStateByStreamConfig
      // 开启静音
      if (mutedAudio) {
        await RTC?.muteAudio();
      }
      // 关闭摄像头
      if (mutedVideo) {
        await RTC?.muteVideo();
      }
      if(mutedAudio || mutedVideo) {
        await setLocalStreamConfig({
            ...localStreamConfig,
            ...LocalStateByStreamConfig,
          }
        )
      }
    }

  }


  // 处理本地流 streamBar 的响应逻辑
  const handleLocalChange = async (data) => {
    switch (data.name) {
      //  本地流开启视频/关闭视频按钮
      case 'video':
        if (!localStreamConfig.mutedVideo) {
          RTC.muteVideo();
          saveLocalStateByStream({ id: props?.match?.params?.RoomId, mutedVideo: true })
        } else {
          RTC.unmuteVideo();
          saveLocalStateByStream({ id: props?.match?.params?.RoomId, mutedVideo: false })
        }
        setLocalStreamConfig({
          ...localStreamConfig,
          mutedVideo: !localStreamConfig.mutedVideo,
        });
        break;
      // 本地流开启音频/关闭音频按钮
      case 'audio':
        if (!localStreamConfig.mutedAudio) {
          RTC.muteAudio();
          saveLocalStateByStream({ id: props?.match?.params?.RoomId, mutedAudio: true })
          setLocalStreamConfig({
            ...localStreamConfig,
            mutedAudio: !localStreamConfig.mutedAudio,
          });
        } else {
          if (isMute == 1 && currentUserType != 1) {
            message.warning('主持人已将全体静音!')
          }else {
            RTC.unmuteAudio();
            saveLocalStateByStream({id: props?.match?.params?.RoomId, mutedAudio: false})
            setLocalStreamConfig({
              ...localStreamConfig,
              mutedAudio: !localStreamConfig.mutedAudio,
            });
          }
        }
        break;
      // 屏幕分享按钮
      case 'shareDesk':
        if (!localStreamConfig.shareDesk) {
          const wxuserId = UerInfo && UerInfo.friUserId;
          const response = await dispatch({
            type:'PlanetChatRoom/getScreenShareUser',
            payload:{
              spaceId: props?.match?.params?.RoomId,
              wxUserId: wxuserId,
            }
          })
          if(response && response.code === 200){
            try {
              await shareRTC?.handleJoin();
              setLocalStreamConfig({
                ...localStreamConfig,
                shareDesk: !localStreamConfig.shareDesk,
              });
            } catch (error) {
              await dispatch({
                type:'PlanetChatRoom/setState',
                payload:{ ModalVisibleByShareScreenError:true }
              })
            }
          }
        } else {
          await shareRTC.handleLeave();
          setLocalStreamConfig({
            ...localStreamConfig,
            shareDesk: !localStreamConfig.shareDesk,
          });
        }

      default:
        break;
    }
  };

  // 切换角色
  const changeRole = async () => {
    if (!RTC.isJoined) {
      // message.error('please join room!', 2000);
      return;
    }
    if (RTC.isPublished || RTC.isPublishing) {
      // message.error('please change role in unpublish ', 2000);
      return;
    }
    try {
      const targetRole = userRole === audience ? anchor : audience;
      await RTC.client.switchRole(targetRole);
      RTC.role = targetRole;
      setUserRole(targetRole);
    } catch (error) {
      console.log('basic live change role error = ', error);
    }
  };


  // 移除流
  const removeStream = (stream) => {
    if (!stream) { return; }
    const streamType = stream.getType();
    const userID = stream.getUserId();

    switch (streamType) {
      case 'local':
        setLocalStreamConfig(prevConfig => ({
          ...prevConfig,
          hasAudio: false,
          hasVideo: false,
          mutedAudio: true,
          mutedVideo: true,
          stream:null,
        }));
        break;
      default: {
        setRemoteStreamConfigList(preList => preList
          .map(streamConfig => (streamConfig.userID === userID && streamConfig.streamType === streamType
            ? {
              ...streamConfig,
              hasAudio: false,
              hasVideo: false,
              mutedAudio: true,
              mutedVideo: true,
              subscribedAudio: false,
              subscribedVideo: false,
            } : streamConfig)));
        break;
      }
    }
  };

  // 新增用户
  const addUser = (userID, streamType) => {
    if (streamType === 'local') {
      setLocalStreamConfig({
        stream: null,
        streamType,
        userID,
        hasAudio: false,
        hasVideo: false,
        mutedAudio: false,
        mutedVideo: false,
        shareDesk: false,
        audioVolume: 0,
      });
    } else {
      setRemoteStreamConfigList((preList) => {
        const newRemoteStreamConfigList = preList.length > 0
          ? preList.filter(streamConfig => streamConfig.userID !== userID)
          : [];
        newRemoteStreamConfigList
          .push({
            stream: null,
            streamType: 'main',
            userID,
            hasAudio: false,
            hasVideo: false,
            mutedAudio: false,
            mutedVideo: false,
            subscribedAudio: false,
            subscribedVideo: false,
            resumeFlag: false,
            audioVolume: 0,
          });
        return newRemoteStreamConfigList;
      });
    }
  };

  // 移除用户
  const removeUser = (userID, streamType) => {
    if (streamType === 'local') {
      setLocalStreamConfig(null);
      setRemoteStreamConfigList([]);
    } else {
      setRemoteStreamConfigList(preList => preList.filter(streamConfig => streamConfig.userID !== userID));
    }
  };

  // ------------------[TRTC方法END]------------------


  /* 直播操作按钮方法 */
  const openCloseHandUp = async ({ handUpType }) => {

    // 接受连麦
    let wxuserId = UerInfo && UerInfo.friUserId
    let res = await dispatch({
      type:'PlanetChatRoom/openCloseHandUp',
      payload:{
        spaceId: props?.match?.params?.RoomId, // [string]	是	空间ID
        wxUserId: wxuserId,
        handUpType:handUpType,
      }
    })
    // 开启接受连麦权限后 通知各客户页面 更新本页面状态
    const { code,content } = res || {}
    if (code == 200) {
      sendMessageByIm({
        dataType: UPDATA_STATE,
        description: JSON.stringify({imUserId:'ALL'})
      })
    }
    return res
  }


  // 点击录制视频
  const liveRecord = async ({recordType}) => {
    // 接受连麦
    let wxuserId = UerInfo && UerInfo.friUserId
    let streamType =  shareHostRemoteStreamConfig && shareHostRemoteStreamConfig.streamType == 'main' ? 0 : 1; // 分享白板
    let res = await dispatch({
      type:'PlanetChatRoom/liveRecord',
      payload:{
        spaceId: props?.match?.params?.RoomId, // [string]	是	空间ID
        wxUserId: wxuserId,
        recordType:recordType,
        streamType: streamType || streamType == 0 ? streamType : null,
      }
    })
    if(res && res.code === 200){
      sendMessageByIm({
        dataType: UPDATA_STATE,
        description: JSON.stringify({imUserId:'ALL'}),
      })
    }
    return res
  }

  // 通过空间ID，进入直播会议后获取转码文件
  const getTranscodeFileBySpace = async () => {
    let res = await dispatch({
      type: 'PlanetChatRoom/getTranscodeFileBySpace',
      payload: {
        spaceId: props?.match?.params?.RoomId,
        // wxUserId: wxuserId,
      }
    })
    return res
  }

  // 获取查看空间连麦列表
  const getHandUpList = async (callback) => {
    let wxuserId = UerInfo && UerInfo.friUserId
    let res = await dispatch({
      type: 'PlanetChatRoom/getHandUpList',
      payload: {
        spaceId: props?.match?.params?.RoomId,
        wxUserId: wxuserId,
        pageNum:1,
        pageSize:100,
      }
    })
    callback && callback()
    return res
  }

  // 空间在线成员列表
  const getManageMembersInTheMeeting = async () => {
    let res = await dispatch({
      type: 'PlanetChatRoom/getManageMembersInTheMeeting',
      payload: {
        spaceId: props?.match?.params?.RoomId,
        sceneType: 2, // 场景类型，1：会议详情在线用户，2：管理成员在线用户
      }
    })
    return res
  }

  // 操作连麦请求
  const operateHandUp = async ({statusType,guestUserId,imUserId}) => {
    Toast.show({icon: 'loading', duration: 0, maskClickable: false}) // 加loading
    let resByHandUpList = await getHandUpList();
    let { code:codeByHandUpList,content:contentByHandUpList } = resByHandUpList || {}
    const { resultList } = contentByHandUpList || {}
    // 判定当前是否还有其余用户正在连麦中
    if (statusType == 1 && Array.isArray(resultList) && resultList.find(item => item.statusType == 1)) {
      let currentHandUpUser = handUpList.find(item => item.statusType == 1)
      // 当前有用户正在连麦中
      dispatch({
        type:"PlanetChatRoom/setState",
        payload:{ ModalVisibleByAcceptLienMai:{
            statusType,    // [string]	是	1 接受连麦 2暂不同意 3下麦
            guestUserId,   // [string]	需操作连麦的用户ID，当前用户是主持人时必传
            imUserId,
            currentHandUpUser
          }}
      })
      Toast.clear()
      return;
    }
    // 接受连麦
    let wxuserId = UerInfo && UerInfo.friUserId
    const res = await dispatch({
      type:'PlanetChatRoom/operateHandUp',
      payload:{
        spaceId:props?.match?.params?.RoomId,                 // [string]	是	空间ID
        // xwxUserId:wxuserId,           // [string]	是	用户ID
        guestUserId:guestUserId,         // [string]	需操作连麦的用户ID，当前用户是主持人时必传
        statusType:statusType,           // [string]	是	1 接受连麦 2暂不同意 3下麦
      }
    })
    Toast.clear()
    const { code,content } = res || {}
    if (code == 200) {
      sendMessageByIm({
        dataType: UPDATA_STATE,
        description: JSON.stringify({imUserId:'ALL'}),
      })
    }
    return res;
  }

  /**申请连麦**/
  const onClickLianMai = async () => {
    if(SpaceInfo?.handUpStatusType == null) {
      // 开启申请连麦先检查用户设备权限
      let checkMicrophone = await checkDevices('microphone')
      let checkCamera = await checkDevices('camera')
      let CameraIDByCheckCamera, MicrophoneIDByCheckMicrophone = null;
      if(checkMicrophone) {
        // 获取麦克风设备
        let MicrophonesDeviceList = await TRTC.getMicrophones();
        MicrophoneIDByCheckMicrophone = await Array.isArray(MicrophonesDeviceList) && MicrophonesDeviceList.length > 0 && MicrophonesDeviceList[0].deviceId;
        await setMicrophoneID(MicrophoneIDByCheckMicrophone)
      }
      if (checkCamera) {
        // 获取摄像头设备
        let CamerasDeviceList = await TRTC.getCameras();
        CameraIDByCheckCamera = await Array.isArray(CamerasDeviceList) && CamerasDeviceList.length > 0 && CamerasDeviceList[0].deviceId;
        await setCameraID(CameraIDByCheckCamera);
      }
      if(!MicrophoneIDByCheckMicrophone) {
        // 当前用户 无麦克风设备
        dispatch({
          type: 'PlanetChatRoom/setState',
          payload: { ModalVisibleByAcceptLienMaiNoMicrophone:true }
        })
        return
      }
      // 申请连麦
      await dispatch({
        type: 'PlanetChatRoom/setState',
        payload: {SpaceInfo: {
            ...SpaceInfo,
            handUpStatusType:0,
          }} // 申请连麦
      })
      await sendMessageByIm({dataType: HAND_UP, description: '1'})
    }else if(SpaceInfo?.handUpStatusType == 0) {
      await sendMessageByIm({dataType: HAND_DOWN, description: '1'})
      // 取消申请连麦
      await dispatch({
        type: 'PlanetChatRoom/setState',
        payload: {SpaceInfo: {
            ...SpaceInfo,
            handUpStatusType:null,
          }} // 取消连麦
      })
    }else if(SpaceInfo?.handUpStatusType == 1) {
      // 主动下麦
      // 连麦成功-主动下麦 调用接口 调用调整状态接口
     /* await operateHandUp({statusType:3,wxUserId:wxUserId,guestUserId:wxUserId,imUserId:imUserId})
      await sendMessageByIm({dataType: HAND_UP, description: '1'})
      await dispatch({
        type: 'PlanetChatRoom/setState',
        payload: {SpaceInfo: {
            ...SpaceInfo,
            handUpStatusType:null,
          }} // 取消连麦
      })*/
      dispatch({
        type: 'PlanetChatRoom/setState',
        payload: { ModalVisibleByLeaveMicrophone:true }
      })
    }
  }
  // 关闭筛选弹窗
  const shareCloseOnClick = () => {
    setMaskVisible(false)
  }
  const shareOnClick = () => {
    // shareUpdateByType()
    // setMaskVisible(true)
    // val.current = null;
    /* dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {ModalVisibleByActionSheetShare: true}
    }) */
    posterModalRef && posterModalRef.current.init(2, SpaceInfoObj)
    // history.push(`/Poster?id=${props?.match?.params?.RoomId}`)
  }
  // 点击返回按钮
  const onClickBack = (historylen) => {
    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'goBack',       // 页面地址onchange事件
      }
      window.parent.postMessage(postData, getArrailUrl())
      return
    }

    const { isGoback } = history.location.query || {}
    if(isGoback == 1) { // 直接回到首页
      goToHomePage(dispatch, 'replace')
      return
    }
    // 如果是非app下的环境
    if (history.action != 'POP' && history.action != 'REPLACE') {
      history.goBack()
    } else if (history.action == 'REPLACE' && history.length > (!!historylen ? historylen : 1)) {
      history.goBack()
    } else {
      if (getOperatingEnv() == 5||getOperatingEnv() == 6) {
        // 如果是app环境则调用app返回 关闭webView
        backInApp();
      }else {
        // 如果不是app环境返回首页
        goToHomePage(dispatch, 'replace')
      }
    }
  }

  // 屏幕分享失败，帮助文档下载
  // const downScreenShareHelpFile = async ()=> {
  //   let resByDownScreenShareHelpFile = await dispatch({
  //     type: 'PlanetChatRoom/downScreenShareHelpFile',
  //     payload: {}
  //   })
  //
  //   if (resByDownScreenShareHelpFile) {
  //     if (resByDownScreenShareHelpFile.code == 500) {
  //       message.error('下载失败！');
  //     }else {
  //       getDownLoad(resByDownScreenShareHelpFile, `医生星球${starSpaceTypeText}帮助文档.pdf`);
  //     }
  //   }else {
  //     message.error('下载失败！');
  //   }
  // }

  // 清空val
  const cleanVal = () => {
    val.current = null
  }

  // 清空RTC
  const cleanRTC = async () => {
    await setRTC(null)
  }

  return (
    <>
      <Helmet>
        <title>{nameBySpaceInfo ? nameBySpaceInfo : `${starSpaceTypeText}详情`}</title>
        <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, shrink-to-fit=no, viewport-fit=cover" />
      </Helmet>

      <Spin wrapperClassName={styles.spin} spinning={
        !!loadingByPage ||
        !!loadingByCheckDevices ||
        !!props.loading.effects['PlanetChatRoom/getSpaceInfo'] ||
        !!props.loading.effects['PlanetChatRoom/getMsgCodeUserInfo'] ||
        !!props.loading.effects['PlanetChatRoom/getScreenShareUser']
      }>
        <div id={'DerailWarp'} className={styles.DerailWarp}>
          {/*
             外层viewPort 组件内区分纵向屏幕模式/和横向屏幕模式<br/>
             封装第一层-视口展示区域包含组件(横向/纵向)<br/>
             1. [修改] title区域<br/>
             2. [修改] 弹幕展示区域<br/>
             3. [修改] 底部个人操控区域<br/>
             4. [新增/延用] 各种操作所需的提示弹窗<br/>
             5. [新增/延用] 参会人列表 申请人列表 设置页面 各种列表<br/>
          */}
          <ViewportWrap
            callBackByUPDATASTATE={callBackByUPDATASTATE}
            isHorizontalLive={isHorizontalLive}                 // 是否是全屏
            onRefByViewportWrap={RefByViewportWrap}             // refObj 暴露给父组件的方法
            sendMessageByIm={sendMessageByIm}                   // function 发送im实时通信消息
            localStreamConfig={localStreamConfig}               // 本地流配置
            remoteStreamConfigList={remoteStreamConfigList}     // 远端流配置列表
            RTC={RTC}                                           // rtc实例
            shareRTC={shareRTC}                                 // 分享RTC实例
            isJoined={isJoined}                                 // 是否加入房间
            isPublished={isPublished}                           // 是否发布流
            handleJoin={handleJoin}                             // onClick进入房间
            handleLeave={handleLeave}                           // onClick离开房间结束直播
            onChange={handleLocalChange}                        // onChange
            changeUrlParams={changeUrlParams}                   // 修改地址栏的状态参数
            spaceId={props?.match?.params?.RoomId}              // 空间id
            openCloseHandUp={openCloseHandUp}                   // 操作连麦
            liveRecord={liveRecord}                             // 录制视频
            getGuestList={getGuestList}                         // 获取嘉宾列表
            getSpaceInfo={getSpaceInfo}                         // 获取用户信息
            onClickLianMai={onClickLianMai}                     // 申请连麦
            operateHandUp={operateHandUp}                       // 接受连麦
            elapsedTime={elapsedTime}                           // 正在录播时长
            shareOnClick={shareOnClick}                         // 分享
            onClickBack={onClickBack}                           // 返回事件
            cleanVal={cleanVal} // 清空val
            cleanRTC={cleanRTC} // 清空RTC
            getApplyAdmissionList={getApplyAdmissionList}       // 获取申请进入会议成员列表
            userActiveJoinSpace={userActiveJoinSpace}           // 用户进入直播、会议的一些操作
            getManageMembersInTheMeeting={getManageMembersInTheMeeting} // 空间在线用户列表
          >
            <Viewport
              isHorizontalLive={isHorizontalLive}                 // 是否是全屏
              onRefByViewportWrap={RefByViewportWrap}             // refObj 暴露给父组件的方法
              sendMessageByIm={sendMessageByIm}                   // function 发送im实时通信消息
              localStreamConfig={localStreamConfig}               // 本地流配置
              remoteStreamConfigList={remoteStreamConfigList}     // 远端流配置列表
              RTC={RTC}                                           // rtc实例
              shareRTC={shareRTC}                                 // 分享RTC实例
              isJoined={isJoined}                                 // 是否加入房间
              isPublished={isPublished}                           // 是否发布流
              handleJoin={handleJoin}                             // onClick进入房间
              handleLeave={handleLeave}                           // onClick离开房间结束直播
              onChange={handleLocalChange}                        // onChange
              changeUrlParams={changeUrlParams}                   // 修改地址栏的状态参数
              spaceId={props?.match?.params?.RoomId}              // 空间id
              openCloseHandUp={openCloseHandUp}                   // 操作连麦
              liveRecord={liveRecord}                             // 录制视频
              getGuestList={getGuestList}                         // 获取嘉宾列表
              getSpaceInfo={getSpaceInfo}                         // 获取用户信息
              onClickLianMai={onClickLianMai}                     // 申请连麦
              operateHandUp={operateHandUp}                       // 接受连麦
              elapsedTime={elapsedTime}                           // 正在录播时长
              shareOnClick={shareOnClick}                         // 分享
              onClickBack={onClickBack}                           // 返回事件
              cleanVal={cleanVal} // 清空val
              cleanRTC={cleanRTC} // 清空RTC
              tim={timObj} // tim实例
            />
          </ViewportWrap>

          {/* DynamicRtc和DynamicShareRtc基础类 */}
          {
            userID
            && roomID && trtcAppId
            && <DynamicRtc
              onRef={ref => setRTC(ref)}
              SDKAPPID={trtcAppId}
              userSig={userSig}
              userID={imUserId}
              roomID={roomID}
              // useStringRoomID={useStringRoomID}
              cameraID={cameraID}
              microphoneID={microphoneID}
              audio={audio}
              video={video}
              mode={mode}
              setState={setState}
              addUser={addUser}
              removeUser={removeUser}
              addStream={addStream}
              updateStream={updateStream}
              updateStreamConfig={updateStreamConfig}
              removeStream={removeStream}
              onKickedOut={onKickedOut} // 被踢出的回调
              role={userRole}
              setAutoExpandGuestArea={async (value)=>{
                if (value == 1) {
                  await dispatch({ type: 'PlanetChatRoom/setState', payload: { autoExpandGuestArea:null }})
                  await dispatch({ type: 'PlanetChatRoom/setState', payload: { autoExpandGuestArea:value }})
                }else {
                  setTimeout(()=>{
                    dispatch({ type: 'PlanetChatRoom/setState', payload: { autoExpandGuestArea:null }})
                    dispatch({ type: 'PlanetChatRoom/setState', payload: { autoExpandGuestArea:value }})
                  },1000)
                }
              }}
            ></DynamicRtc>
          }

          {
            localStreamConfig
            && <DynamicShareRtc
              onRef={ref => setShareRTC(ref)}
              SDKAPPID={trtcAppId}
              userSig={screenShareUser && screenShareUser.userSig || userSig}
              mainStreamUserID={imUserId}
              userID={screenShareUser && screenShareUser.imUserId || imUserId}
              roomID={roomID}
              relatedUserID={screenShareUser && screenShareUser.imUserId || imUserId}
              updateStreamConfig={updateStreamConfig}>
            </DynamicShareRtc>
          }
          {/*引导下载Friday App下载==顶部浮窗*/}
          {(getOperatingEnv() == 2 || getOperatingEnv() == 3) && <div className={styles.DownloadAppCardBody}><DownloadAppCard info={{roomId:SpaceInfo?.id,type:2}} /></div>}
        </div>
      </Spin>

      <DeviceDetectorByDetector/>

      {/* 海报弹窗 */}
      <PosterModal ref={posterModalRef}/>
    </>
  )
}
export default connect(({ tim,userInfoStore,PlanetChatRoom,loading }: any) => ({tim,userInfoStore,PlanetChatRoom,loading}))(Index)

