.container {
  padding: 12px 16px;
  background: #fff;
}
.title_box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  .title {
    font-weight: 500;
    color: #000;
    height: 20px;
    line-height: 20px;
  }
  .title_btn {
    font-size: 12px;
    color: #0095FF;
    height: 20px;
    line-height: 20px;
    display: flex;
    align-items: center;
    :global {
      .anticon {
        font-size: 13px;
        margin-right: 3px;
      }
    }
  }
}
.step_content {
  display: flex;
  flex-wrap: nowrap;
  overflow: hidden;
  .step_item {
    flex: 1;
    position: relative;
    word-break: break-all;
    padding: 0 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    .step_item_wrap {
      position: relative;
      margin-bottom: 8px;
      .step_item_icon_box {
        position: relative;
        z-index: 10;
        height: 18px;
        padding-left: 10px;
        padding-right: 10px;
        background: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        .step_item_icon {
          background: #F5F5F5;
          width: 16px;
          height: 16px;
          font-size: 11px;
          color: #D5D5D5;
          line-height: 17px;
          text-align: center;
          border-radius: 50%;
        }
      }
      .step_item_line {
        content: "";
        display: block;
        width: 9999px;
        height: 2px;
        background: #F5F5F5;
        position: absolute;
        left: 0;
        top: 9px;
        z-index: 1;
      }
    }

    .step_item_text {
      text-align: center;
      font-size: 12px;
      color: #999;
      line-height: 17px;
    }
  }
  .step_item:first-child {
    padding-left: 0;
    align-items: flex-start;
    .step_item_wrap {
      .step_item_icon_box {
        padding-left: 0;
      }
    }
  }
  .step_item:last-child {
    padding-right: 0;
    align-items: flex-end;
    .step_item_wrap {
      .step_item_icon_box {
        padding-right: 0;
      }
      .step_item_line {
        display: none;
      }
    }
  }
  .step_item.step_item_finished {
    .step_item_wrap {
      .step_item_icon_box {
        .step_item_icon {
          width: 18px;
          height: 18px;
          background: #0095FF;
          border: 3px solid #CCEEFF;
          display: flex;
          align-items: center;
          justify-content: center;

          :global {
            .anticon {
              display: block;
              color: #fff;
              font-size: 10px;
            }
          }
        }
      }
      .step_item_line {
        background: #CCEEFF;
      }
    }
  }
  .step_item.step_item_active {
    .step_item_wrap {
      .step_item_icon_box {
        .step_item_icon {
          background: #0095FF;
          border: 4px solid #CCEEFF;
        }
      }
    }
    .step_item_text {
      color: #000;
    }
  }

}

