import React, { useState } from 'react'
import { connect, history } from 'umi'
import styles from './index.less'
import {Input, message} from 'antd';
import logoImg from '@/assets/home_logo.png';
import searchIcon from '@/assets/GlobalImg/search.png';
import pcIcon1 from '@/assets/pc_head_1.png';
import pcIcon2 from '@/assets/pc_head_2.png';
import pcIcon3 from '@/assets/pc_head_3.png';
import pcIcon4 from '@/assets/pc_head_4.png';
import pcIcon5 from '@/assets/pc_head_5.png';
import pcIcon6 from '@/assets/pc_head_6.png';

interface PropsType {
  dispatch?: any;
  isShowInput?: Boolean; // 是否展示input搜索框
  inputChangeFn?: any; // input输入事件
  inputOnSearch?: any; // input按下回车事件
}

const headerDataList = [
  {id: 1, text: '首页', icon: pcIcon1},
  {id: 2, text: '全国病例', icon: pcIcon2, jumpUrl: '/Case/CaseResult'},
  {id: 3, text: '专家指导', icon: pcIcon3, jumpUrl: '/Expert/ExpertResult'},
  {id: 4, text: '课程放映室', icon: pcIcon4, jumpExternalUlr: 'https://apptmq2ocpm6396.pc.xiaoe-tech.com/'},
  {id: 6, text: '发布', icon: pcIcon6},
  {id: 5, text: '个人中心', icon: pcIcon5},
]

// 发布数据列表
const releaseList = [
  {id: 1, name: '文章', jumpUrl: '/CreateGraphicsText/CreateArticle'},
  {id: 2, name: '外链', jumpUrl: '/CreateGraphicsText/CreateExternalLinks'},
  {id: 3, name: '王国', jumpUrl: '/UserInfo/CreateKingdomByPc'},
  {id: 4, name: '直播', jumpUrl: '/UserInfo/CreateSpaceByPc/Live'},
  {id: 5, name: '会议', jumpUrl: '/UserInfo/CreateSpaceByPc/Meet'},
]

// 个人中心数据列表
const userInfoList = [
  {id: 1, name: '个人中心', jumpUrl: '/UserInfo'},
  {id: 2, name: '退出登录', jumpUrl: '/User/login'},
]

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {
    dispatch,
    isShowInput = false,
    inputChangeFn = () => {},
    inputOnSearch = () => {},
  } = props
  const [isShow, setIsShow] = useState(false); // 是否展示发布下拉列表
  const [isShowUserInfo, setIsShowUserInfo] = useState(false); // 是否展示个人中心下拉列表

  // 跳转事件
  const jumpClickFn = (item) => {
    const {pathname} = history.location;
    // 刷新首页
    if(pathname == '/home' && item.id == 1) {
      return window.location.reload()
    }
    // 跳首页
    if (item.id == 1) {
      history.push(`/home`)
      return
    }

    // 是个人中心tab + 未登录 时，跳转登录页面
    if (item.id === 5 && !localStorage.getItem('access_token')) {
      history.push({
        pathname: '/User/login',
        query: {
          redirectByPush: window.location.href,
        }
      })
      return
    }

    if(item && item.jumpExternalUlr) {
      return window.open(item.jumpExternalUlr)
    }
    history.push(item.jumpUrl)
  }

  // 发布（空间、王国、外链、文章）
  const releaseClickFn = (e, url) => {
    e.stopPropagation()
    e.preventDefault()
    if (history.location.pathname == url) {
      return
    }
    history.push(url)
  }

  // 个人中心（个人中心、退出登录）
  const personalCenterClickFn = (e, url) => {
    e.stopPropagation()
    e.preventDefault()
    if (history.location.pathname == url) {
      return
    }
    if(url == '/User/login') {
      dispatch({
        type: 'userInfoStore/exitLogout',
        payload: {}
      }).then((res: any) => {
        if(res && res.code == 200) {
          localStorage.clear(); // 清空本地存储
          history.replace(url)
          return;
        } else {
          return message.error({content: res.msg || '数据加载失败'})
        }
      }).catch((err: any) => {
        console.log(err)
      })
      return
    }else{
      // 清除个人中心models数据
      dispatch({
        type: 'pcAccount/clean',
      })
      history.push(url)
    }
  }

  return (
    <div className={styles.pc_header_box}>
      <div className={styles.pc_header_wrap}>
        <div className={styles.pc_left_wrap}>
          <div className={styles.pc_left_logo}><img src={logoImg} alt="" /></div>
          {
            isShowInput && <div className={styles.pc_left_input}>
              <Input
                placeholder='搜索...'
                suffix={<img src={searchIcon} alt='' onClick={inputOnSearch}/>}
                onChange={inputChangeFn}
                onPressEnter={inputOnSearch}
              />
            </div>
          }
        </div>
        <div className={styles.pc_right_wrap}>
          {
            headerDataList.map(item => {
              return (
                <div className={styles.pc_right_item} key={item.id} onClick={()=>{jumpClickFn(item)}} onMouseEnter={
                  ()=>{
                    if(item.id === 5&&localStorage.getItem('access_token')){
                      setIsShowUserInfo(true)
                    }else if(item.id === 6){
                      setIsShow(true)
                    }
                  }
                } onMouseLeave={
                  ()=>{
                    if(item.id === 5&&localStorage.getItem('access_token')){
                      setIsShowUserInfo(false)
                    }else if(item.id === 6){
                      setIsShow(false)
                    }
                  }
                }>
                  <img src={item.icon} alt="" />{item.text}
                  {
                    item.id === 6 ?
                      <div className={styles.pc_release_list} style={{display: !isShow ? 'none' : 'block'}}>
                        {
                          releaseList.map(val => {
                            return <div className={styles.list_item} key={val.id} onClick={(e)=>releaseClickFn(e, val.jumpUrl)}>{val.name}</div>
                          })
                        }
                      </div> : item.id === 5&&localStorage.getItem('access_token')? <div className={styles.pc_userInfoList_list} style={{display: !isShowUserInfo ? 'none' : 'block'}}>
                        {
                          userInfoList.map(val => {
                            return <div className={styles.list_item} key={val.id} onClick={(e)=>personalCenterClickFn(e, val.jumpUrl)}>{val.name}</div>
                          })
                        }
                      </div> : null
                  }
                </div>
              )
            })
          }
        </div>
      </div>
    </div>
  )
}

export default connect(({ userInfoStore, loading }: any) => ({ userInfoStore, loading}))(Index)

