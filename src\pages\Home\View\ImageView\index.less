.image_container {
  position: relative;
  padding: 0 12px 8px;
  &.one {

  }
  &.two {
    display: flex;
    flex-wrap: wrap;
    padding: 0 6.5px 8px;
    .item_box {
      width: 50%;
      padding: 0 5.5px;
    }
  }
  &.three {
    display: flex;
    flex-wrap: wrap;
    padding: 0 8px 8px;
    .item_box {
      width: 33.33%;
      padding: 0 4px;
    }
  }
  &.four {
    display: flex;
    flex-wrap: wrap;
    .item_box {
      width: 25%;
    }
  }
  &.five {
    display: flex;
    flex-wrap: wrap;
    .item_box {
      width: 20%;
    }
  }
  &.fifty {
    :global {
      .adm-swiper-horizontal .adm-swiper-indicator {
        bottom: 12px;
      }
      .adm-page-indicator-dot {
        width: 8px;
        height: 4px;
        margin-right: 2px;
        border-radius: 5px;
      }
      .adm-page-indicator-dot:last-child {
        margin-right: 0;
      }
      .adm-page-indicator-dot-active {
        background: #B7D4FF;
      }
    }
  }
  &.sixty {
    padding: 0 0 8px;
    :global {
      .adm-swiper-track {
        padding: 0 12px;
      }
      .adm-swiper-item {
        padding-right: 12px;
      }
      .adm-swiper-slide:last-child .adm-swiper-item {
        padding-right: 0;
      }
      .adm-swiper-indicator {
        display: none;
      }
    }
    .item_box {
      //width: 45.01%;
      //min-width: 45.01%;
      //margin-right: 12px;
      //&:last-child {
      //  margin-right: 0;
      //}
    }
  }
  .item_box {
    margin-bottom: 8px;
    .img {
      width: 100%;
      height: auto;
    }
  }
}
