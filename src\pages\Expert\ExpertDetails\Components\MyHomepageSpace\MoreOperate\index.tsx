import React, { useState, useRef } from 'react';
import styles from './index.less';
import { history, connect } from 'umi';
import { ActionSheet, Toast } from 'antd-mobile';

import CommonConfirmModal from '@/components/CommonConfirmModal';
import PosterModal from '@/pages/Poster/PosterModal'    // 海报弹窗（APP、H5）

const Index: React.FC = (props: any) => {
  let { friUserId } = JSON.parse(localStorage.getItem('userInfo') || '{}');
  const {visible, close, spaceItem, dispatch, myHomeSpaceFilter } = props || {};
  const { starSpaceType } = spaceItem  || {};
  const [offShelfVisible, setOffShelfVisible] = useState(false); // 下架二次提示弹框
  const [deleteVisible, setDeleteVisible] = useState(false); // 删除二次提示弹框
  const [deleteFromListVisible, setDeleteFromListVisible] = useState(false); // 从列表删除二次提示弹框

  const posterModalRef = useRef(null)     // 海报弹窗ref

  let starSpaceTypeText = starSpaceType == 2 ? '会议' : '直播'

  // 主持人更多操作
  const actions = spaceItem?.isDisable!=1?[
    { text: '分享', key: 'share', onClick: () => shareClickFn() },
    { text: '下架', key: 'offShelf'},
    { text: '删除', key: 'delete'},
    { text: '取消', key: 'cancel', onClick: () => cancelClickFn() },
  ]:[
    { text: '删除', key: 'delete'},
    { text: '取消', key: 'cancel', onClick: () => cancelClickFn() },
  ]

  // 非主持人的更多操作
  const otherPersonActions = spaceItem?.isDisable!=1?[
    { text: '分享', key: 'share', onClick: () => shareClickFn() },
    { text: '从列表中删除', key: 'deleteFromList' },
    { text: '取消', key: 'cancel', onClick: () => cancelClickFn() },
  ]:[
    { text: '从列表中删除', key: 'deleteFromList' },
    { text: '取消', key: 'cancel', onClick: () => cancelClickFn() },
  ]

  // 分享
  const shareClickFn = () => {
    props.close && props.close() // 关闭弹框

    // 跳转空间分享海报页
    posterModalRef && posterModalRef.current.init(2, spaceItem)

    // 临时存储筛选条件
    sessionStorage.setItem('myHomeSpaceFilter', JSON.stringify(myHomeSpaceFilter));
  }

  // 下架确定按钮
  const offShelfOkFn = () => {
    Toast.show({icon: 'loading', maskClickable: false})
    const userInfoData = JSON.parse(localStorage.getItem('userInfo') || '{}');
    const { friUserId:id } = userInfoData || {}  // 获取用户id
    dispatch({
      type: 'userInfoStore/editSpaceInfo',
      payload: {
        id: spaceItem && spaceItem.id, // 空间id
        updateUserId: id, // 操作人用户ID
        isDisable: 1, // 1是下架，下架时必传
      }
    }).then(res => {
      const { code } = res || {};
      if(res && code == 200) {
        Toast.show({content: '下架成功'})
        setOffShelfVisible(false);
        props.close && props.close() // 关闭弹框
        props.refreshFn && props.refreshFn() // 刷新页面
      } else {
        return Toast.show({content: res.msg})
      }
    }).catch(err => {
      console.log(err)
    })
  }

  // 下架取消按钮
  const offShelfCancelFn = () => {
    setOffShelfVisible(false)
  }

  // 删除确定按钮
  const deleteOkFn = () => {
    Toast.show({icon: 'loading', maskClickable: false})
    const userInfoData = JSON.parse(localStorage.getItem('userInfo'));
    const { friUserId:id } = userInfoData || {}  // 获取用户id
    dispatch({
      type: 'userInfoStore/editSpaceInfo',
      payload: {
        id: spaceItem && spaceItem.id, // 空间id
        updateUserId: id, // 操作人用户ID
        isDel: 1, // 1是删除，删除时必传
      }
    }).then(res => {
      const { code } = res || {};
      if(res && code == 200) {
        Toast.show({content: '删除成功'})
        setDeleteVisible(false);
        props.close && props.close() // 关闭弹框
        props.refreshFn && props.refreshFn() // 刷新页面
      } else {
        return Toast.show({content: res.msg})
      }
    }).catch(err => {
      console.log(err)
    })
  }

  // 删除取消按钮
  const deleteCancelFn = () => {
    setDeleteVisible(false)
  }

  // 从列表中删除
  const deleteFromListFn = () => {
    Toast.show({icon: 'loading', maskClickable: false})
    dispatch({
      type: 'userInfoStore/deleteSpaceFromList',
      payload: {
        spaceId: spaceItem && spaceItem.id, // 空间id
        spaceJoinType:myHomeSpaceFilter.spaceJoinType,
      }
    }).then(res => {
      const { code } = res || {};
      if(res && code == 200) {
        Toast.show({content: '从列表删除成功'})
        setDeleteFromListVisible(false);
        props.close && props.close() // 关闭弹框
        props.refreshFn && props.refreshFn() // 刷新页面
      } else {
        return Toast.show({content: res.msg})
      }
    }).catch(err => {
      console.log(err)
    })
  }

  // 从列表中删除取消按钮
  const deleteFromListCancelFn = () => {
    setDeleteFromListVisible(false)
  }

  // 取消
  const cancelClickFn = () => {
    props.close()
  }

  return <>
    <ActionSheet
      className={styles.wrap}
      visible={visible}
      actions={(spaceItem?.hostUserId==friUserId)?actions:otherPersonActions}
      onAction={action => {
        if (action.key === 'offShelf') {
          setOffShelfVisible(true)
          return
        }
        if (action.key === 'delete') {
          setDeleteVisible(true)
          return
        }
        if (action.key === 'deleteFromList') {
          setDeleteFromListVisible(true)
          return
        }
      }}
      onClose={() => close()}
    />
    <CommonConfirmModal isVisible={offShelfVisible} title={'确定下架'} text={`确定要下架这个${starSpaceTypeText}吗?`} onSubmit={offShelfOkFn} onCancel={offShelfCancelFn} />
    <CommonConfirmModal isVisible={deleteVisible} title={'确定删除'} text={`确定要删除这个${starSpaceTypeText}吗?`} onSubmit={deleteOkFn} onCancel={deleteCancelFn} />
    <CommonConfirmModal isVisible={deleteFromListVisible} title={'确定删除'} text={`确定要从列表中删除这个${starSpaceTypeText}吗?`} onSubmit={deleteFromListFn} onCancel={deleteFromListCancelFn} />

    {/* 海报弹窗 */}
    <PosterModal ref={posterModalRef}/>
  </>
}
export default connect(({ expertAdvice, userInfoStore, loading }: any) => ({ expertAdvice, userInfoStore, loading }))(Index)
