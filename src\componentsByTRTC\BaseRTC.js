import a18n from 'a18n';
import React from 'react';
import TRTC from 'trtc-js-sdk';
import {
  isUndefined,
  joinRoomSuccessUpload,  // 进房成功上报到 TAM
  joinRoomFailedUpload,   // 进房失败上报到 TAM
  publishSuccessUpload,   // 推流成功上报到 TAM
  publishFailedUpload,    // 推流失败上报到 TAM
  initLocalStreamSuccessUpload, // 初始化流成功上报到 TAM
  initLocalStreamFailedUpload,  // 初始化流失败上报到 TAM
} from '@/utils/utilsByTRTC';
import toast from '@/componentsByTRTC/Toast';
import { message } from "antd";
import {  Modal, } from 'antd-mobile'
import { history } from "umi";
import _ from "lodash";


export default class RTC extends React.Component {
  constructor(props) {
    super(props);
    this.SDKAPPID = props.SDKAPPID;                 // 腾讯云实时音视频的SDKAppID
    this.userSig = props.userSig;                   // 用户签名，用于身份验证
    this.userID = props.userID;                     // 用户ID
    this.roomID = props.roomID;                     // 房间ID
    this.useStringRoomID = props.useStringRoomID;   // 是否使用字符串类型的房间ID
    this.cameraID = props.cameraID;                 // 摄像头ID
    this.microphoneID = props.microphoneID;         // 麦克风ID
    this.setState = props.setState;                 // 设置状态的方法
    this.addUser = props.addUser;                   // 添加用户的方法
    this.removeUser = props.removeUser;             // 移除用户的方法
    this.addStream = props.addStream;               // 添加流的方法
    this.updateStream = props.updateStream;         // 更新流的方法
    this.updateStreamConfig = props.updateStreamConfig; // 更新流配置的方法
    this.removeStream = props.removeStream;         // 移除流的方法
    this.mode = props.mode;                         // 模式（rtc）
    this.audio = props.audio;                       // 音频配置
    this.video = props.video;                       // 视频配置
    this.setAutoExpandGuestArea = props.setAutoExpandGuestArea; // 设置自动扩展嘉宾区域的方法
    this.onKickedOut = props.onKickedOut;           // 被踢出房间时的回调函数
    this.localStream = null,                        // 本地流对象
    this.remoteStreamList = [],                     // 远程流对象列表
    this.client = null;                             // 当前TRTC SDK生成创建的实例
    this.shareClient = null;                        // 当前TRTC 分享屏幕创建功能的实例
    this.isJoining = false;                         // 当前用户是否正在加入房间
    this.isJoined = false;                          // 当前用户是否已经加入房间
    this.isPublishing = false;                      // 当前用户是否正在发布流
    this.isPublished = false;                       // 当前用户是否已经发布流
    this.isUnPublishing = false;                    // 当前用户是否正在取消发布流
    this.isLeaving = false;                         // 当前用户是否正在离开房间
    this.privateMapKey = 255;
    this.mirror = false;                            // 是否开启画面镜像效果
    this.dom = null;                                // DOM元素
    global.$TRTC = TRTC;
  }

  // eslint-disable-next-line camelcase
  async UNSAFE_componentWillReceiveProps(props) {
    if (this.userID !== props.userID) {
      this.userID = props.userID;
    }
    this.roomID = props.roomID;
    this.useStringRoomID = props.useStringRoomID;
    this.cameraID = props.cameraID;
    this.microphoneID = props.microphoneID;
    this.setState = props.setState;
    this.addStream = props.addStream;
    this.removeStream = props.removeStream;
    this.updateStream = props.updateStream;
    this.updateStreamConfig = props.updateStreamConfig;
    this.mode = props.mode;
    this.audio = props.audio;
    this.video = props.video;
    this.userSig = props.userSig;
    this.setAutoExpandGuestArea = props.setAutoExpandGuestArea;
  }

  async componentDidMount() {
    this.props.onRef(this);
    // 当前版本 4.15.12
    const checkResult = await TRTC.checkSystemRequirements();
    const { result, detail } = checkResult;
    let pressTimer = 0;
    // 无法规避问题
    // （2022-01-19）iOS 15 以下版本，canvas.captureStream 采集出的视频流，无法使用 video 标签播放直播流
    // https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-02-info-webrtc-issues.html#h2-4
    if (!result
      || !detail.isBrowserSupported
      || !detail.isWebRTCSupported
    ) {
      Modal.alert({
        title: (
          <div
            style={{
              userSelect: 'none',
            }}
            onClick={()=>{
              if (pressTimer >= 3) {
                pressTimer = 0;
                window.location.href = `https://web.sdk.qcloud.com/trtc/webrtc/demo/detect/index.html`;
              }else {
                pressTimer++;
              }
            }}
          >设备不支持</div>
        ),
        content: `当前使用设备浏览器版本不支持直播相关功能, 请更换其他浏览器`,
        okText: '确定',
        onConfirm: () => {
          pressTimer = 0;
          history.goBack();
        }
      })
      return;
    }

    const that = this;
    window.addEventListener('beforeunload', (event) => {
      if (that.isJoined) {
        event.preventDefault();
        // eslint-disable-next-line no-param-reassign
        event.returnValue = '确定要关闭当前页面吗？';
      }
    });
  }

  async componentWillUnmount() {
    this.handleLeave();
  }


  // 初始化客户端
  async initClient() {
    // 音视频通话客户端对象 Client 通过 TRTC.createClient() 创建，代表一次音视频会话。
    // Client 客户端对象提供 TRTC Web SDK 的核心功能，包括:
    // 进房 join()
    // 退房 leave()
    // 发布本地流 publish()
    // 取消发布本地流 unpublish()
    // 订阅远端流 subscribe()
    // 取消订阅远端流 unsubscribe()
    // 销毁 Client 实例 destroy()
    this.client = TRTC.createClient({
      mode: this.mode,
      sdkAppId: this.SDKAPPID,
      userId: this.userID,
      userSig: this.userSig,
      useStringRoomId: this.useStringRoomID,
    });
    this.handleClientEvents();
    // [获取新增、移除的设备列表] 1. 保存一份设备列表

    // console.log('prevDevices123123 :: ',prevDevices);
    return this.client;
  }

  async initLocalStream() {
    // [获取新增、移除的设备列表] 1. 保存一份设备列表
    this.prevDevices = await TRTC.getDevices();
    this.localStream = TRTC.createStream({
      audio: this.audio,
      video: !!this.cameraID ? this.video : false,
      userId: this.userID,
      cameraId: !!this.cameraID ? this.cameraID : '',
      microphoneId: this.microphoneID,
    });
    try {
      await this.localStream.initialize();
      this.addStream && this.addStream(this.localStream);
      initLocalStreamSuccessUpload(this.SDKAPPID);
      return this.localStream;
    } catch (error) {
      const errorCode = error.getCode();
      console.log('errorCode1231233 :: ',errorCode);
      /*error.name === 'DOWNLINK_RECONNECTION_FAILED 0x4008'
      || error.name === 'SIGNAL_CHANNEL_RECONNECTION_FAILED 0x4006'
      || error.name === 'UPLINK_RECONNECTION_FAILED 0x4007'*/
      if(
        errorCode == 0x4008
        || errorCode ==  0x4006
        || errorCode ==  0x4007
      ){
        /**
         * 下行 PeerConnection 重连失败
         * 描述：当下行 PeerConnection 异常断开时，SDK 会尝试多次重连，如果都失败了，则会抛出此错误。
         * 处理建议：提醒用户检查网络，然后重新进房。
         * */
        this.localStream = null;
        alert(`重连失败，请检查网络,并刷新页面`)
        initLocalStreamFailedUpload(this.SDKAPPID, `${JSON.str1ingify(error.message)}`);
      }else if( errorCode == 0x4004 ){
        // 提交弱网环境的提示
        alert(`当前网络请求超时，请检查网络,并刷新页面!`)
      }else {
        this.localStream = null;
        console.log('errorerror :: ', error);
        alert(`${JSON.stringify(error.message)}`);
        initLocalStreamFailedUpload(this.SDKAPPID, `${JSON.stringify(error.message)}`);
      }
    }
  }

  destroyLocalStream() {
    this.removeStream && this.removeStream(this.localStream);
    this.localStream && this.localStream.stop();
    this.localStream && this.localStream.close();
    this.localStream = null;
    this.dom = null;
  }

  playStream(stream, dom, config) {
    let streamByDeep = stream;
    if(streamByDeep.getType() == 'local'){
      streamByDeep.play(dom, {
        objectFit: 'cover',
        mirror: false,
        muted:true,
        ...config,
      }).catch((e)=>{
        console.log('playStream error :: ',e);
      });
    }else if (streamByDeep.getUserId().indexOf('share') >= 0) {
      streamByDeep.play(dom, {
        objectFit: 'contain',
        mirror: false,
        muted:false,
        ...config,
      }).catch((e)=>{
        console.log('playStream error :: ',e);
      });
    } else if (streamByDeep.getUserId().indexOf('tiw') >= 0){
      streamByDeep.play(dom, {
        objectFit: 'contain',
        mirror: false,
        muted:false,
        ...config,
      }).catch((e)=>{
        console.log('playStream error :: ',e);
      });
    }else {
      streamByDeep.play(dom,{
        objectFit: 'cover',
        muted:false,
        ...config,
      }).catch((e)=>{
        console.log('playStream error :: ',e);
      });
      if (streamByDeep === this.localStream) {
        this.dom = dom;
      }
    }
  }

  resumeStream(stream) {
    stream.resume();
  }

  async handleJoin() {
    if (this.isJoining || this.isJoined) {
      return;
    }
    this.isJoining = true;
    await this.initClient();
    try {
      await this.client.join({roomId: this.roomID});
      toast.success('join room success!', 2000);
      joinRoomSuccessUpload(this.SDKAPPID);

      this.isJoining = false;
      this.isJoined = true;
      this.setState && this.setState('join', this.isJoined);
      this.addUser && this.addUser(this.userID, 'local');

      this.startGetAudioLevel();
    } catch (error) {
      if (error?.message?.indexOf('-100013') != -1) {
        message.error('TRTC服务挂起。请检查包裹余额是否为0或腾讯云账户欠款。');
      } else if (error?.getCode() == 0x4004) {
        // 提交弱网环境的提示
        alert(`当前网络请求超时，请检查网络,并刷新页面!`)
      } else {
        this.isJoining = false;
        toast.error('join room failed!', 20000);
        console.error('join room failed', error);
        joinRoomFailedUpload(this.SDKAPPID, `${JSON.stringify(error.message)}`);
      }
    }
  }

  async handlePublish() {
    if (!this.isJoined || this.isPublishing || this.isPublished) {
      return;
    }
    this.isPublishing = true;
    !this.localStream && (await this.initLocalStream());
    console.log('this.localStream :: ',this.localStream);
    try {
      await this.client.publish(this.localStream);
      toast.success('publish localStream success!', 2000);
      console.log('publish localStream success!', 2000);
      publishSuccessUpload(this.SDKAPPID);

      this.isPublishing = false;
      this.isPublished = true;
      this.setState && this.setState('publish', this.isPublished);
    } catch (error) {
      this.isPublishing = false;
      console.error('publish localStream failed', error);
      toast.error('publish localStream failed!', 2000);
      publishFailedUpload(this.SDKAPPID, `${JSON.stringify(error.message)}`);
    }
  }

  async handleUnPublish() {
    if (!this.isPublished || this.isUnPublishing) {
      return;
    }
    this.isUnPublishing = true;
    try {
      await this.client.unpublish(this.localStream);
      toast.success('unpublish localStream success!', 2000);

      this.isUnPublishing = false;
      this.isPublished = false;
      this.setState && this.setState('publish', this.isPublished);
    } catch (error) {
      this.isUnPublishing = false;
      console.error('unpublish localStream failed', error);
      switch (error.getCode()) {
        case 4096: // ErrorCode = 0x1001 INVALID_OPERATION
          toast.error('stream has not been published yet, please publish first', 2000);
          break;
        case 4097: // ErrorCode = 0x1001 INVALID_PARAMETER
          toast.error('publish is ongoing, please try unpublish later', 2000);
          break;
        default:
          toast.error('unpublish localStream failed! please try again later', 2000);
          break;
      }
    }
    this.localStream && (await this.destroyLocalStream());
  }

  async handleSubscribe(remoteStream, config = { audio: true, video: true }) {
    try {
      await this.client.subscribe(remoteStream, {
        audio: isUndefined(config.audio) ? true : config.audio,
        video: isUndefined(config.video) ? true : config.video,
      });
    } catch (error) {
      console.error(`subscribe ${remoteStream.getUserId()} with audio: ${config.audio} video: ${config.video} error`, error);
      toast.error(`subscribe ${remoteStream.getUserId()} failed!`, 2000);
    }
  }

  async handleUnSubscribe(remoteStream) {
    try {
      await this.client.unsubscribe(remoteStream);
    } catch (error) {
      console.error(`unsubscribe ${remoteStream.getUserId()} error`, error);
      toast.error(`unsubscribe ${remoteStream.getUserId()} failed!`, 2000);
    }
  }

  async handleLeave() {
    console.log('走到这里了吗',this);
    if (!this.isJoined || this.isLeaving) {
      return;
    }
    this.isLeaving = true;
    this.stopGetAudioLevel();
    if (this.isPublished) {
      await this.handleUnPublish();
    }
    try {
      await this.client.leave();
      // toast.success('leave room success', 2000);

      this.removeUser && this.removeUser(this.userID, 'local');

      this.isLeaving = false;
      this.isJoined = false;
      this.setState && this.setState('join', this.isJoined);
    } catch (error) {
      this.isLeaving = false;
      // console.error('leave room error', error);
      // toast.error('leave room error', 2000);
    }
  }

  handleStartPublishCDNStream() {
    this.client.startPublishCDNStream();
  }

  handleSopPublishCDNStream() {
    this.client.handleSopPublishCDNStream();
  }

  handleStartMixTranscode(otherRoomID, otherRoomUserID) {
    const mixTranscodeConfig = {
      videoWidth: 1280,
      videoHeight: 480,
      videoBitrate: 1500,
      videoFramerate: 15,
      mixUsers: [
        {
          userId: this.userID,
          roomId: this.roomID, // roomId 字段自 v4.11.5 版本开始支持，支持跨房间混流
          pureAudio: false,
          width: 640,
          height: 480,
          locationX: 0,
          locationY: 0,
          streamType: 'main', // 指明该配置为远端主流
          zOrder: 1,
        },
        {
          userId: otherRoomUserID,
          roomId: otherRoomID, // roomId 字段自 v4.11.5 版本开始支持，支持跨房间混流
          pureAudio: false,
          width: 640,
          height: 480,
          locationX: 640,
          locationY: 0,
          streamType: 'main', // 指明该配置为远端辅流
          zOrder: 1,
        },
      ],
    };
    this.client.startMixTranscode(mixTranscodeConfig);
  }

  handleStopMixTranscode() {
    this.client.stopMixTranscode();
  }

  async muteVideo() {
    this.localStream && this.localStream.muteVideo();
    // 关闭摄像头
    const videoTrack = this.localStream.getVideoTrack();
    if (videoTrack) {
      this.localStream && await this.localStream.removeTrack(videoTrack)
      // 停止采集，关闭摄像头
      videoTrack.stop();
    }
  }

  muteAudio() {
    this.localStream && this.localStream.muteAudio();
  }

  async unmuteVideo () {
    // 打开摄像头
    const videoStream = TRTC.createStream({ userId:this.userID, audio: false, video: true });
    await videoStream.initialize();
    this.localStream && await this.localStream.addTrack(videoStream.getVideoTrack());
    this.localStream && await this.localStream.unmuteVideo();
  }

  unmuteAudio() {
    this.localStream && this.localStream.unmuteAudio();
  }

  startGetAudioLevel() {
    // 文档：https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/module-ClientEvent.html#.AUDIO_VOLUME
    this.client.on('audio-volume', (event) => {
      event.result.forEach(({ userId, audioVolume }) => {
        if (audioVolume > 2) {
          // console.log(`user: ${userId} is speaking, audioVolume: ${audioVolume}`);
          this.updateStreamConfig && this.updateStreamConfig(userId, 'audio-volume', audioVolume);
        } else {
          this.updateStreamConfig && this.updateStreamConfig(userId, 'audio-volume', 0);
        }
      });
    });
    this.client.enableAudioVolumeEvaluation(200);
  }

  stopGetAudioLevel() {
    this.client && this.client.enableAudioVolumeEvaluation(-1);
  }

  handleStreamEvents(stream) {
    stream.on('error', (error) => {
      const errorCode = error.getCode();
      if (errorCode === 0x4043) {
        // PLAY_NOT_ALLOWED,引导用户手势操作并调用 stream.resume 恢复音视频播放
        this.updateStreamConfig && this.updateStreamConfig(stream.getUserId(), 'resume-stream');
      }else if( errorCode == 0x4004 ){
        // 提交弱网环境的提示
        alert(`当前网络请求超时，请检查网络,并刷新页面!`)
      }
    });
  }

  // 监听客户端对象回调事件
  handleClientEvents() {
    // 监听并提示客户端报错
    this.client.on('error', (error) => {
      alert(error);
    });
    // 监听客户端被踢出房间的事件
    this.client.on('client-banned', async (event) => {
      if(event.reason == 'room-disband') {  // 当前房间已解散
      } else if(event.reason == 'banned') {  // 当前用户被踢出房间
      } else if(event.reason == 'kick'){  // 当前用户被踢出房间
        this.onKickedOut && this.onKickedOut();
      }else {
        // alert(event.reason);
        console.error(`client has been banned for ${event.reason}`);
      }
      // 设置发布状态为 false
      this.isPublished = false;
      // 清空本地流
      this.localStream = null;
      // 更新状态
      this.setState && this.setState('publish', this.isPublished);
      // 处理离开房间的逻辑
      await this.handleLeave();
    });

    // 被移除房间的监听
    /*this.client.on(TRTC.EVENT.KICKED_OUT, async (event) => {})*/

    // 当有新的远程用户加入房间时触发，会调用addUser方法添加用户。
    this.client.on('peer-join', (event) => {
      const { userId } = event;
      console.log(`peer-join ${userId}`, event);
      this.addUser && this.addUser(userId);
    });
    // 当有远程用户离开房间时触发，会调用removeUser方法移除用户。
    this.client.on('peer-leave', (event) => {
      const { userId } = event;
      console.log(`peer-leave ${userId}`, event);
      this.removeUser && this.removeUser(userId);
    });


    // 监听设备变更的事件变化
    navigator.mediaDevices.addEventListener('devicechange', async () => {
      // 1. 获取设备列表
      let prevDevices = this.prevDevices
      // 3. 设备变更时，获取变更后的设备列表，用于和 prevDevices 比对
      const devices = await TRTC.getDevices();
      const speakers = await TRTC.getSpeakers();
      // 4. 新增的设备列表
      const devicesAdded = devices.filter(device => prevDevices && prevDevices.findIndex(({ deviceId }) =>  device && device.deviceId === deviceId) < 0);
      // 5. 移除的设备列表
      const devicesRemoved = Array.isArray(prevDevices) && prevDevices.filter(prevDevice => devices && devices.findIndex(({ deviceId }) => prevDevice && prevDevice.deviceId === deviceId) < 0);
      if (Array.isArray(devicesAdded) && devicesAdded.length > 0) {
        handleDevicesAdded(devicesAdded);
      }
      if (Array.isArray(devicesRemoved) && devicesRemoved.length > 0) {
        handleDevicesRemoved(devicesRemoved);
      }
      this.prevDevices = devices;
    });

    // 处理设备新增、移除的设备列表
    const handleDevicesAdded=(devicesAdded) => {
      devicesAdded.forEach(device => {
        const { deviceId,kind } = device || {};
        if (device.kind === 'audioinput') {
          // 提示用户检测到新麦克风插入。若用户需要切换到新设备，可以调用 localStream.switchDevice 接口切换设备
          /**
           * deviceId: "82040efea433263bab8f9f4ae3967829cefe67d02c73d3d1db808addc6d1967d"
           * getCapabilities: ƒ ()
           * groupId: "9078ea12c6e73e619afa160714a920ef44b1d66efcece8330bd19c0ebc4b6c39"
           * kind: "audioinput"
           * label: "外置麦克风 (Built-in)"
           */
          this.localStream &&  this.localStream.switchDevice('audio',deviceId);
        } else if (device.kind === 'videoinput') {
          // 提示用户检测到新摄像头插入。若用户需要切换到新设备，可以调用 localStream.switchDevice 接口切换设备
          this.localStream &&  this.localStream.switchDevice('video',deviceId);
        }else if (device.kind === 'audiooutput') {
          // 切换到新的扬声器
          console.log('audiooutput - deviceId',deviceId);
          this.localStream &&  this.localStream.setAudioOutput(deviceId);
        }
      });
    }

    // 当设备被拔出后的监听
    const handleDevicesRemoved = (devicesRemoved) => {
      devicesRemoved.forEach(device => {
        if (device.kind === 'audioinput') {
          // 提示用户检测到麦克风拔出。
          if (isCurrentMicrophoneRemoved(device.deviceId)) {
            // 当前正在使用的麦克风被拔出
            console.log('当前正在使用的麦克风被拔出');
          }
        } else if (device.kind === 'videoinput') {
          // 提示用户检测到摄像头拔出。
          if (isCurrentCameraRemoved(device.deviceId)) {
            // 当前正在使用的摄像头被拔出
            console.log('当前正在使用的摄像头被拔出');
          }
        }
      });
    }

    // 检测是否当前正在使用的麦克风设备被拔出
    // microphoneIdRemoved 是被移除的麦克风 deviceId
    const isCurrentMicrophoneRemoved = (microphoneIdRemoved) => {
      console.log('localStream123123 :: ',this);
      /*const audioTrack = this.localStream && this.localStream.getAudioTrack();
      if (audioTrack && audioTrack.getSettings().deviceId === microphoneIdRemoved) {
        return true;
      } else {
        return false;
      }*/
      return false;
    }

    // 检测是否当前正在使用的摄像头设备被拔出
    // cameraIdRemoved 是被移除的摄像头 deviceId
    const isCurrentCameraRemoved = (cameraIdRemoved) => {
      const videoTrack = this.localStream && this.localStream.getVideoTrack();
      // 当前
      if (videoTrack && videoTrack.getSettings().deviceId === cameraIdRemoved) {
        return true;
      } else {
        return false;
      }
    }




    // fired when a remote stream is added
    this.client.on('stream-added', (event) => {
      const { stream: remoteStream } = event;
      const remoteUserID = remoteStream.getUserId();
      if (remoteUserID === `share_${this.userID}`) {
        // don't need screen shared by us
        this.handleUnSubscribe(remoteStream);
      } else {
        console.log(`remote stream added: [${remoteUserID}] type: ${remoteStream.getType()}`);
        // subscribe to this remote stream
        this.handleSubscribe(remoteStream);
        this.addStream && this.addStream(remoteStream);
      }
    });
    // fired when a remote stream has been subscribed
    this.client.on('stream-subscribed', (event) => {
      const { stream: remoteStream } = event;
      console.log('stream-subscribed userId: ', remoteStream.getUserId());
    });
    // fired when the remote stream is removed, e.g. the remote user called Client.unpublish()
    this.client.on('stream-removed', (event) => {
      const { stream: remoteStream } = event;
      remoteStream.stop();
      this.removeStream && this.removeStream(remoteStream);
      console.log(`stream-removed userId: ${remoteStream.getUserId()} type: ${remoteStream.getType()}`);
    });

    this.client.on('stream-updated', (event) => {
      const { stream: remoteStream } = event;
      this.updateStream && this.updateStream(remoteStream);
      console.log(`type: ${remoteStream.getType()} stream-updated hasAudio: ${remoteStream.hasAudio()} hasVideo: ${remoteStream.hasVideo()}`);
    });

    this.client.on('mute-audio', (event) => {
      const { userId } = event;
      console.log(`${userId} mute audio`);
      this.setAutoExpandGuestArea && this.setAutoExpandGuestArea(2);
      console.log('this.removeStream13123 ::',this.localStream,event,this);
      this.updateStreamConfig && this.updateStreamConfig(userId, 'mute-audio');
    });
    this.client.on('unmute-audio', (event) => {
      const { userId } = event;
      console.log(`${userId} unmute audio`);
      this.setAutoExpandGuestArea && this.setAutoExpandGuestArea(1);
      this.updateStreamConfig && this.updateStreamConfig(userId, 'unmute-audio');
    });
    this.client.on('mute-video', (event) => {
      const { userId } = event;
      console.log(`${userId} mute video`);
      this.setAutoExpandGuestArea && this.setAutoExpandGuestArea(2);
      this.updateStreamConfig && this.updateStreamConfig(userId, 'mute-video');
    });
    this.client.on('unmute-video', (event) => {
      const { userId } = event;
      console.log(`${userId} unmute video`);
      this.setAutoExpandGuestArea && this.setAutoExpandGuestArea(1);
      this.updateStreamConfig && this.updateStreamConfig(userId, 'unmute-video');
    });

    this.client.on('connection-state-changed', (event) => {
      console.log(`RtcClient state changed to ${event.state} from ${event.prevState}`);
    });

    this.client.on('network-quality', (event) => {
      const { uplinkNetworkQuality, downlinkNetworkQuality } = event;
      this.updateStreamConfig && this.updateStreamConfig(this.userID, 'uplink-network-quality', uplinkNetworkQuality);
      this.updateStreamConfig && this.updateStreamConfig(this.userID, 'downlink-network-quality', downlinkNetworkQuality);
    });
  }

  render() {
    return (
     <div style={{ width: 0, height: 0 }}></div>
    );
  }
}
