@import '~@/utils/imageText.less';
.post_wrap {
  width: 100%;
  background: #fff;
  padding: 16px;
  margin-bottom: 10px;
}

.content {
  width: 100%;

  .post_info {
    font-size: 14px;
    font-weight: 400;
    color: #000000;
    line-height: 20px;
    margin-bottom: 8px;
    word-break: break-all;
    :global {
      .ql-editor {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 7;
        overflow: hidden;
      }
    }
  }

  .post_img_info {
    width: 100%;

    .img_wrap {
      width: 100%;
      display: flex;
      justify-content: space-between;
      height: 194px;
      border-radius: 4px;
      overflow: hidden;

      &.img_length1 {
        width: 50%;

        .img_box {
          width: 100%;
          overflow: hidden;
          //display: flex;
          //align-items: center;
          //justify-content: center;
          border:1px solid #E4E4E4;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }

      &.img_length2 {
        display: flex;
        justify-content: space-between;

        .img_box {
          width: 50%;
          margin-right: 2px;
          overflow: hidden;
          //display: flex;
          //align-items: center;
          //justify-content: center;
          border:1px solid #E4E4E4;

          &:last-child {
            margin-left: 2px;
            margin-right: 0;
          }

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }

      &.img_length3 {
        width: 100%;
        display: flex;
        justify-content: space-between;
        overflow: hidden;
        position: relative;

        .right_img {
          width: 50%;
          margin-right: 2px;
          height: 194px;
          overflow: hidden;
          //display: flex;
          //align-items: center;
          //justify-content: center;
          border:1px solid #E4E4E4;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

        }

        .left_img {
          width: 50%;
          margin-left: 2px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .img_box {
            width: 100%;
            height: 95px;
            overflow: hidden;
            //display: flex;
            //align-items: center;
            //justify-content: center;
            border:1px solid #E4E4E4;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
        }

        .img_num {
          position: absolute;
          bottom: 0;
          right: 0;
          padding: 2px 8px;
          box-sizing: border-box;
          background: rgba(0,0,0,0.5);
          border-radius: 4px 0px 4px 0px;
          font-size: 14px;
          font-weight: 400;
          color: #FFFFFF;
          line-height: 20px;
        }
      }
    }
  }
}

// 王国
.kingdom_wrap {
  display: inline-flex;
  align-items: center;
  column-gap: 4px;
  height: 19px;
  padding: 0 4px;
  border-radius: 4px;
  background: #E4F0FC;
  font-size: 11px;
  color: #0095FF;
  margin-top: 8px;
}
