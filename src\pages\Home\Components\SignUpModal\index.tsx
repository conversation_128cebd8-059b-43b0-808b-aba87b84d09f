/**
 * @Description: 报名弹窗
 */
import React, { useState, useEffect, useRef } from 'react'
import { connect } from 'umi'
import { Input, Spin } from 'antd'
import { Toast } from 'antd-mobile'
import { CloseOutlined } from '@ant-design/icons'
import styles from './index.less'

interface PropsType {
  loading: any,
  dispatch: any,
  pageId: any,                                             // 页面ID
  visible: any,                                            // 弹窗显隐
  isActive: any,                                           // 弹窗显隐，动画用
  activityId: any,                                         // 活动ID
  animationPlayState: any,                                 // 动画状态，running 开始，paused 暂停
  animationNameMask: any,                                  // 动画名称，遮罩
  animationNameContent: any,                               // 动画名称，内容
  onClickMask: any,                                        // 点击遮罩回调
  onAnimationEnd: any,                                     // 动画完成后回调
  onOk: any,                                               // 报名成功回调
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { loading, dispatch } = props

  let userInfo = localStorage.getItem('userInfo') || null
  if (userInfo) {
    userInfo = JSON.parse(userInfo)
  }

  const initialState = {
    userName: '',                                          // 姓名
    phone: '',                                             // 手机号
    orgName: '',                                           // 机构
  }
  const [state, setState] = useState(initialState)
  const divRef = useRef(null)

  useEffect(() => {
    if (props.visible) {
      setState({
        ...state,
        userName: userInfo && userInfo.name || '',         // 姓名
        phone: userInfo && userInfo.phone || '',           // 手机号
      })
    } else {
      setState(initialState)
    }
  }, [props.visible])

  // 动画结束
  const animationEnd = () => {
    props.onAnimationEnd(props.isActive)
  }

  // 添加监听事件
  useEffect(() => {
    if (divRef.current) {
      divRef.current.addEventListener('webkitAnimationEnd', animationEnd)
    }
    return () => {
      if (divRef.current) {
        divRef.current.removeEventListener('webkitAnimationEnd', animationEnd)
      }
    }
  }, [props.isActive])

  // 输入姓名
  const nameOnChange = (e) => {
    setState({
      ...state,
      userName: e.target.value,                            // 姓名
    })
  }

  // 输入手机号
  const phoneOnChange = (e) => {
    setState({
      ...state,
      phone: e.target.value,                               // 手机号
    })
  }

  // 输入机构
  const orgNameOnChange = (e) => {
    setState({
      ...state,
      orgName: e.target.value,                             // 机构
    })
  }

  // 点击提交报名
  const onSubmit = () => {

    if (!state.userName) {
      Toast.show('请输入姓名')
      return
    }

    if (!state.orgName) {
      Toast.show('请输入机构')
      return
    }

    const payload = {
      activityId: props.activityId,                        // 活动ID
      userName: state.userName,                            // 姓名
      phone: state.phone,                                  // 手机号
      orgName: state.orgName,                              // 机构
      applySource: 2,                                      // 报名来源(1小程序，2H5)
      pageId: props.pageId,                                // 页面ID
    }
    submitApplyInfo(payload)
  }

  // 提交报名，发送请求
  const submitApplyInfo = (payload) => {
    dispatch({
      type: 'activity/submitApplyInfo',
      payload,
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200 && content) {
        Toast.show({
          icon: 'success',
          content: '报名成功',
        })
        props.onOk()
      } else {
        Toast.show(msg || '报名失败')
      }
    }).catch(err => {

    })
  }

  // loading
  const loadingSubmitApplyInfo = !!loading.effects['activity/submitApplyInfo']

  return (
    <div className={styles.modal_container} style={{display: props.visible ? 'block' : 'none'}}>
      <div ref={divRef} className={styles.mask} onClick={props.onClickMask} style={{
        animationPlayState: props.animationPlayState,
        animationName: styles[props.animationNameMask],
      }}></div>
      <div className={styles.modal_content} style={{
        animationPlayState: props.animationPlayState,
        animationName: styles[props.animationNameContent],
      }}>
        <div className={styles.title_box}>
          <div className={styles.title}>报名信息</div>
          <div className={styles.icon} onClick={props.onClickMask}>
            <CloseOutlined />
          </div>
        </div>

        <div className={styles.content_box}>
          <div className={styles.input_box}>
            <div className={styles.label}>
              <span className={styles.remark}>*</span>
              姓名：
            </div>
            <div className={styles.value}>
              <Input placeholder="请输入姓名" value={state.userName} onChange={nameOnChange}/>
            </div>
          </div>
          <div className={styles.input_box}>
            <div className={styles.label}>
              <span className={styles.remark}>*</span>
              手机号：
            </div>
            <div className={styles.value}>
              <Input placeholder="请输入手机号" value={state.phone} disabled={!!state.phone} onChange={phoneOnChange}/>
            </div>
          </div>
          <div className={styles.input_box}>
            <div className={styles.label}>
              <span className={styles.remark}>*</span>
              机构：
            </div>
            <div className={styles.value}>
              <Input placeholder="请输入机构名称" value={state.orgName} onChange={orgNameOnChange}/>
            </div>
          </div>
        </div>

        {/* 按钮 */}
        <div className={styles.btn_box}>
          <Spin spinning={loadingSubmitApplyInfo}>
            <div className={styles.btn} onClick={onSubmit}>提交报名</div>
          </Spin>
        </div>

      </div>
    </div>
  )
}

export default connect(({ activity, loading }: any) => ({activity, loading}))(Index)
