import React, { useEffect } from 'react'
import { history, KeepAlive } from 'umi'
import { stringify } from 'qs'
import { getOperatingEnv, useDebounce } from '@/utils/utils'

import Step2Content from './Step2Content'

const Index: React.FC = () => {

  let updateType = () => {
    const env = getOperatingEnv()
    if (env == 4) {
      history.replace(`/CreateGraphicsText/CreateArticle?${stringify(history.location.query)}`)
    }
  }
  updateType = useDebounce(updateType, 100)

  useEffect(() => {
    updateType()

    window.addEventListener('resize', updateType, {passive: true})
    return () => {
      window.removeEventListener('resize', updateType)
    }
  }, [])

  return (
    <KeepAlive
      saveScrollPosition="screen"
      id={history.location.pathname}
      when={() => {
        return history.action == 'PUSH';
      }}
    >
      <Step2Content/>
    </KeepAlive>
  )
}

export default Index
