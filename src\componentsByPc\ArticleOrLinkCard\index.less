.wrap {
  display: flex;
  flex-wrap: nowrap;
  padding: 20px 0;
  border-bottom: 1px solid #ddd;

  .left {
    flex: 1;
    display: flex;
    flex-wrap: nowrap;

    .cover_img_wrap {
      flex-shrink: 0;
      width: 117px;
      height: 90px;
      border-radius: 4px;
      overflow: hidden;
      margin-right: 16px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      border: 1px solid #ddd;
      .no_cover_wrap {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #666;
        :global {
          .anticon {
            font-size: 17px;
          }
        }
      }
    }

    .details_wrap {
      flex: 1;
      display: flex;
      flex-wrap: nowrap;
      flex-direction: column;
      justify-content: space-between;
      padding-top: 4px;
      .title {
        font-size: 16px;
        font-weight: 500;
        color: #000;
        line-height: 22px;
        margin-bottom: 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .status {
        display: inline-block;
        width: auto;
        height: 21px;
        line-height: 21px;
        padding: 0 8px;
        border-radius: 4px;
        font-size: 12px;
        &.status_0 {
          background: #FFF5E5;
          color: #EAA434;
        }
        &.status_1 {
          background: #F8F8F9;
          color: #999;
        }
        &.status_2 {
          background: rgba(249,101,91,.2);
          color: #dc3545;
        }
      }
      .status_2_tip{
        margin-left: 8px;
        font-size: 12px;
        color: #999;
      }
      .details_data {
        font-size: 14px;
        color: #000;
        line-height: 20px;
      }
    }
  }

  .right {
    flex-shrink: 0;
    padding-top: 5px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: space-between;

    .date {
      font-size: 14px;
      color: #999;
      line-height: 20px;
    }

    .btn_wrap {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      font-size: 16px;
      color: #000;
      line-height: 22px;
      cursor: pointer;
      :global {
        .ant-typography {
          margin-bottom: 0;
        }
        .ant-typography-copy {
          color: #000;
        }
      }

      & > div + div {
        margin-left: 32px;
      }

      .btn_disabled {
        color: #999;
        cursor: not-allowed;
      }
    }
  }
}
