.downloadCard{
  display: flex;
  height: 64px;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background: #000000;
  z-index: 999999;
  .downloadCardLeft{
    display: flex;
    align-items: center;
    img{
      width: 21px;
      height: 28px;
      margin-right: 10px;
    }
    >div{
      h3{
        font-size: 14px;
        font-weight: 400;
        color: #FFFFFF;
        margin-top: 10px;
        margin-bottom: 0px;
      }
      p{
        font-size: 9px;
        color: #CCCCCC;
      }
    }
  }
  .downloadCardRight{
    display: flex;
    position: relative;
    align-items: center;
    .searchIcon{
      width: 16px;
      height: 16px;
      margin-right: 14px;
    }
    .closeIcon{
      position: absolute;
      top: -11px;
      right: -14px;
      width: 17px;
      height: 17px;
    }
    >span{
      display: inline-block;
      font-size: 14px;
      color:#ffffff;
      background: #0095FF;
      padding: 5px 8px;
      border-radius: 15px;
      margin-right: 10px;
    }
  }
}
.downloadCardHidden{
  display: none;
}
