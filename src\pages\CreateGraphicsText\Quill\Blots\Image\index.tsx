import { Quill } from 'react-quill'

const BlockEmbed = Quill.import('blots/block/embed')

class ImageBlot extends BlockEmbed {
  static create(value) {
    let node = super.create();
    node.setAttribute('contenteditable', false)
    node.setAttribute('data-type', 'image')
    node.setAttribute('id', value.code)
    node.setAttribute('data-src', value.src)
    this.buildContentNode(node, value)
    return node;
  }

  static buildContentNode(node, value) {
    const imgNode = document.createElement('img')
    imgNode.setAttribute('src', value.src)
    node.appendChild(imgNode)
  }

  static value(node: Element) {
    return {
      code: node.id,
      src: node.dataset.src,
    }
  }

  format(name: string, value: string) {

  }

}

ImageBlot.blotName = 'image';                              // 格式名
ImageBlot.tagName = 'div';                                 // dom标签
ImageBlot.className = 'quill_image_format_wrap';           // dom类名

export default ImageBlot
