.spin {
  height: 100%;
  :global {
    .ant-spin-container {
      height: 100%;
    }
  }
}
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #eef3f9;
}
.content {
  flex: 1;
  padding: 16px 0;
  overflow-y: auto;
}
.content_inner {
  width: 816px;
  min-height: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
}

.nav_bar {
  flex-shrink: 0;
  width: 100%;
  display: flex;
  align-items: center;
  background: #fff;
  padding: 15px 20px;
  margin-bottom: 16px;
  border-radius: 8px;
  i {
    display: inline-block;
    width: 32px;
    height: 32px;
    background: url("../../../../../assets/GlobalImg/pc_goback.png") no-repeat center;
    background-size: 24px 24px;
    margin-right: 4px;
    cursor: pointer;
  }
  span {
    font-size: 20px;
    color: #000;
    line-height: 32px;
  }
}

.wrapper {
  flex: 1;
  background: #fff;
  border-radius: 0 0 8px 8px;
  padding: 0 40px;
  .title_box {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    border-bottom: 1px solid #ddd;
    padding: 24px 0 16px;
    :global {
      .ant-input-lg {
        padding-left: 0;
        padding-right: 0;
        font-size: 22px;
        border: 0 !important;
        box-shadow: none !important;
      }
    }
    .tips {
      font-size: 14px;
      color: #aaa;
      white-space: nowrap;
      line-height: 48px;
      span {
        color: #000;
      }
    }
  }
  .editor_box {
    border-bottom: 1px solid #ddd;
  }
  .form_box {
    padding-top: 32px;
    padding-bottom: 124px;
    :global {
      .ant-form-item {
        margin-bottom: 8px;
      }
      .ant-form-item-label {
        width: 100px;
        text-align: left;
      }
    }
    .form_cover_img {
      padding-left: 100px;
      font-size: 12px;
      color: #999;
      margin-bottom: 32px;
      .cover_img_box {
        margin-bottom: 8px;
        display: flex;
        column-gap: 4px;
        align-items: flex-end;
        .cover_img_item {
          position: relative;
          width: 117px;
          height: 90px;
          background-repeat: no-repeat;
          background-position: center;
          background-size: cover;
          .cover_img_item_btn_box {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 23px;
            background: rgba(0,0,0,0.5);
            font-size: 11px;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-evenly;
            & > span {
              cursor: pointer;
            }
          }
        }
        .upload_btn {
          width: 117px;
          height: 90px;
          font-size: 24px;
          color: #CBCBCB;
          background: #F8F8F8;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .preview_btn {
          cursor: pointer;
          font-size: 16px;
          color: #0095FF;
          margin-left: 12px;
        }
      }
    }
  }
}

.bottom_tips_bar {
  position: fixed;
  z-index: 990;
  left: 0;
  right: 0;
  bottom: 79px;
  width: 100%;
  height: 33px;
  background: #FFF7DA;
  border-bottom: 1px solid #EFD989;
  text-align: center;
  line-height: 33px;
  font-size: 12px;
  color: #8C772B;
  .bar_btn_wrap {
    position: absolute;
    width: 12px;
    height: 12px;
    right: 16px;
    top: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 9px;
  }
}

.footer {
  background: #fff;
  border-top: 1px solid #ddd;
  height: 80px;
  flex-shrink: 0;
  .footer_content {
    width: 816px;
    height: 100%;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .footer_content_left {
      font-size: 16px;
      color: #999;
      display: flex;
      & > span + span {
        margin-left: 16px;
      }
    }
    .footer_content_right {
      :global {
        .ant-btn + .ant-btn {
          margin-left: 24px;
        }
        .ant-btn {
          height: 38px;
          padding-left: 20px;
          padding-right: 20px;
        }
      }
    }
  }
}


.custom_dropdown_render {
  :global {
    ::-webkit-scrollbar{
      width: 7px;
      height: 7px;
    }
    ::-webkit-scrollbar-thumb{
      border-radius:10px;
      border: 2px solid #fff;
      -webkit-box-shadow: inset 0 0 0 5px rgba(0,0,0,0.2);
      background: rgba(0,0,0,0.2);
    }
    ::-webkit-scrollbar-track{
      // -webkit-box-shadow: inset 0 0 0 5px rgba(0,0,0,0.2);
      border-radius:0px;
      background:#fff;
    }
  }
}
.select_dropdown_container {
  padding-bottom: 16px;
  max-height: 256px;
  overflow-y: auto;
  .select_dropdown_title {
    font-size: 14px;
    color: #666;
    padding: 12px 16px 4px;
  }
  .kingdom_item {
    padding: 12px 16px;
    border-bottom: 1px solid #F5F6F8;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    cursor: pointer;
    &:hover {
      background: #f5f5f5;
    }
    &.selected {
      background: #e6f7ff;
    }
    & > i {
      width: 40px;
      height: 40px;
      flex-shrink: 0;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      margin-right: 8px;
      font-style: normal;
      line-height: 40px;
      color: #fff;
      font-size: 12px;
      text-align: center;
      white-space: nowrap;
    }
    .kingdom_item_details {
      flex: 1;
      .kingdom_name {
        font-size: 15px;
        color: #000;
        font-weight: 500;
        line-height: 21px;
        margin-bottom: 2px;
      }
      .kingdom_info_box {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #999;
        line-height: 17px;
        & > span + span {
          margin-left: 4px;
        }
      }
    }
  }
}


