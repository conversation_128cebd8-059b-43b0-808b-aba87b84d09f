import {
  getCasesWordList,
  getExcellentCaseDict,
  getExcellentCaseInfo,
  getExcellentCaseList,
  getMaExcellentCaseCommentsList,
  imgShowOrHide,
  isCollect,
  PcGetExcellentCaseInfo,
  saveCaseComments,
} from '../services/casesApi';

export default {
  namespace: 'cases',
  state: {
    imgShowOrHide: false,    // 图文展示状态

    searchValue: '',         // 搜索条件
    checkDepSubject: [],    // 选中学科
    checkAbilityLevel: [],  // 选中能力等级
    checkAchievement: [],   // 选中病例成就
    startDate: null,        // 开始时间
    endDate: null,          // 结束时间

    screenHighlight: false,  // 筛选文案高亮
  },

  effects: {
    // 分页获取优秀病历数据
    * getExcellentCaseList({payload}: any, {put, call}: any) {
      const response = yield call(getExcellentCaseList, payload);
      return response
    },
    // 优秀病历获取搜索关键字
    * getCasesWordList({payload}: any, {put, call}: any) {
      const response = yield call(getCasesWordList, payload);
      return response
    },
    // 优秀病历 - 获取字典项数据
    * getExcellentCaseDict({payload}: any, {put, call}: any) {
      const response = yield call(getExcellentCaseDict, payload);
      return response
    },
    // 获取优秀病历信息
    * getExcellentCaseInfo({payload}: any, {put, call}: any) {
      const response = yield call(getExcellentCaseInfo, payload);
      return response
    },
    // 通过病历ID获取评论
    * getMaExcellentCaseCommentsList({payload}: any, {put, call}: any) {
      const response = yield call(getMaExcellentCaseCommentsList, payload);
      return response
    },
    // 收藏、取消收藏优秀病历
    * isCollect({payload}: any, {put, call}: any) {
      const response = yield call(isCollect, payload);
      return response
    },
    // 小程序评论或回复
    * saveCaseComments({payload}: any, {put, call}: any) {
      const response = yield call(saveCaseComments, payload);
      return response
    },
    // 图片/文本模式切换
    * imgShowOrHide({payload}: any, {put, call}: any) {
      const response = yield call(imgShowOrHide, payload);
      return response
    },
    // pc端获取优秀病例信息
    * PcGetExcellentCaseInfo({payload}: any, {put, call}: any) {
      const response = yield call(PcGetExcellentCaseInfo, payload);
      return response
    },
  },

  reducers: {
    // 保存数据
    save(state: any, {payload}: any) {
      return {
        ...state,
        ...payload,
      }
    },
    // 清空数据
    clean(state: any, {payload}: any) {
      return {
        ...state,
        ...payload,
      }
    },
  },
  subscriptions: {
    setup({dispatch, history}: any) {
      return history.listen(({pathname, search}: any) => {
        if (pathname.indexOf('/Case/CaseResult') == -1 && pathname.indexOf('/Case/CaseDetails') == -1) {
          dispatch({
            type: 'clean',
            payload: {
              searchValue: '',         // 搜索条件
              checkDepSubject: [],    // 选中学科
              checkAbilityLevel: [],  // 选中能力等级
              checkAchievement: [],   // 选中病例成就
              startDate: null,        // 开始时间
              endDate: null,          // 结束时间

              screenHighlight: false,  // 筛选文案高亮
            }
          })
        }
        if (pathname.indexOf('/Case/CaseInputList') == -1 && pathname.indexOf('/Case/CaseSearch') == -1 && pathname.indexOf('/Case/CaseResult') == -1 && pathname.indexOf('/Case/CaseDetails') == -1) {
          dispatch({
            type: 'clean',
            payload: {
              imgShowOrHide: false
            }
          })
        }
      })
    }
  }
};
