.more_box {
  width: 24px;
  height: 24px;
}

.wrap {
  max-height: 282px;
  border-radius: 12px 12px 0px 0px;
  padding-bottom: 20px;

  :global {
    .adm-action-sheet-button-item-wrapper {
      border-bottom: none;
    }

    .adm-action-sheet-button-list {
      border-bottom: none;
      padding-top: 30px;

      .adm-action-sheet-button-item-name {
        font-size: 16px;
        font-weight: 500;
        color: #000000;
        line-height: 19px;
      }

      .adm-action-sheet-button-item-danger .adm-action-sheet-button-item-name {
        color: #E03333;
      }

      .adm-plain-anchor:focus {
        outline: none; /* 去掉点击时的虚线框 */
        background: #fff;
        color: #fff;
      }

      .adm-action-sheet-button-item:focus {
        background: #fff;
        color: #fff;
      }
      .adm-action-sheet-button-item:active {
        background-color: #fff;
    }

      .adm-action-sheet-button-item-wrapper:last-child {
        .adm-action-sheet-button-item-name {
          font-size: 16px;
          font-weight: 500;
          color: #888888;
          line-height: 19px;
        }
      }
    }
  }
}

.tips_content {
  :global {
    .adm-modal-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-direction: row-reverse;
    }
  }
}

.popup_container {
  position: relative;

  :global {
    .adm-popup-body {
      background: #F9F9F9;
      border-radius: 16px 16px 0px 0px;
      padding-top: 48px;
      box-sizing: border-box;
    }
    div.ant-typography {
      margin-bottom: 0;
      margin-left: 21px;
    }
    .ant-typography-copy {
      margin-left: 0;
    }
  }

  .list_box {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    padding: 0 16px 12px;

    .list_item {
      margin-left: 21px;
      display: flex;
      white-space: nowrap;
      flex-direction: column;
      align-items: center;
      font-size: 14px;
      font-weight: 400;
      color: #333333;
      line-height: 16px;
      &:first-child {
        margin-left: 0;
      }

      .item_img {
        width: 48px;
        height: 48px;
        background: #FFFFFF;
        border-radius: 50%;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .list_btn {
    width: 100%;
    height: 44px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
    font-weight: 400;
    color: #000000;
    position: absolute;
    bottom: 0;
    left: 0;
  }
}

.fixed_share_box {
  transform: none;
  display: none;
  position: fixed;
  z-index: 1001;
  right: 21px;
  top: 24px;
  &.fixed_share_box_show {
    display: block;
  }
  .icon1 {
    position: relative;
    display: block;
    background: url("../../assets/GlobalImg/arrow_up.png") no-repeat right center;
    background-size: 122px 108px;
    width: 100%;
    height: 108px;
  }
  .message_box {
    position: relative;
    font-size: 18px;
    font-weight: 400;
    line-height: 25px;
    color: #fff;
    white-space: nowrap;
    top: -18px;
    margin-bottom: 14px;
  }
  .icon_box {
    display: flex;
    align-items: center;
    justify-content: center;
    i {
      display: block;
      width: 48px;
      height: 48px;
      &.icon2 {
        background: url("../../assets/GlobalImg/share_one.png") no-repeat center;
        background-size: 100% 100%;
        margin-right: 24px;
      }
      &.icon3 {
        background: url("../../assets/GlobalImg/share_more.png") no-repeat center;
        background-size: 100% 100%;
      }
    }
  }
}
