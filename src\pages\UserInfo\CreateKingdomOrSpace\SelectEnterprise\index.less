.container {
  height: calc(70vh - 28px);
  position: relative;
}

.title_box {
  position: relative;
  .title {
    font-size: 17px;
    color: #000;
    font-weight: 500;
    line-height: 24px;
    text-align: center;
  }
  .title_btn {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    padding: 0 16px;
  }
}

.wrap {
  padding: 16px;
  box-sizing: border-box;

  .tabs_box {
    width: 100%;
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .tabs_item {
      font-size: 15px;
      font-weight: 500;
      color: #999999;
      line-height: 21px;
      margin-right: 16px;
    }

    .checked {
      font-size: 15px;
      font-weight: 600;
      color: #000000;
      line-height: 21px;
      position: relative;

      &::after {
        content: '';
        width: 12px;
        height: 3px;
        background: #000000;
        border-radius: 6px 6px 6px 6px;
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
      }
    }
  } 
}

.content {
  width: 100%;
  box-sizing: border-box;
  height: calc(70vh - 180px);
  overflow-y: auto;
  display: none;

  &.contentShow {
    display: block;
  }

  .all_staff {
    width: 100%;
    font-size: 14px;
    font-weight: 500;
    color: #000000;
    line-height: 20px;
    padding: 16px 0;
    box-sizing: border-box;
    border-bottom: 1px solid #E1E4E7;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .right_check {
      width: 20px;
      height: 20px;
    }
  }

  .region_wrap {
    width: 100%;
    border-bottom: 1px solid #E1E4E7;
    
    .region_top_wrap {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 0;
      box-sizing: border-box;

      .region_content_left {
        font-size: 14px;
        font-weight: 500;
        color: #000000;
        line-height: 20px;
      }

      .arrow {
        width: 16px;
        height: 16px;
      }
    }

    .region_bot_wrap {
      width: 100%;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin-bottom: 4px;

      .init_style {
        font-size: 14px;
        font-weight: 400;
        color: #666666;
        line-height: 20px;
        padding: 4px 12px;
        background: #F5F5F5;
        border-radius: 21px 21px 21px 21px;
        margin-right: 12px;
        flex-shrink: 0;
        margin-bottom: 12px;
      }

      .checked_style {
        font-size: 14px;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 20px;
        background: #0095FF;
        border-radius: 21px 21px 21px 21px;
        padding: 4px 12px;
        flex-shrink: 0;
        margin-bottom: 12px;
      }
    }
  }

  .institution_wrap {
    width: 100%;
    border-bottom: 1px solid #E1E4E7;
    
    .institution_top_wrap {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 0;
      box-sizing: border-box;

      .institution_content_left {
        font-size: 14px;
        font-weight: 500;
        color: #000000;
        line-height: 16px;

        .arrow_soso {
          margin-left: 8px;
          width: 16px;
          height: 16px;
          position: relative;
          top: -2px;
        }
      }

      .arrow {
        width: 16px;
        height: 16px;
      }
    }

    .institution_bot_wrap {
      width: 100%;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin-bottom: 4px;

      .init_style {
        font-size: 14px;
        font-weight: 400;
        color: #666666;
        line-height: 20px;
        padding: 4px 12px;
        background: #F5F5F5;
        border-radius: 21px 21px 21px 21px;
        margin-right: 12px;
        flex-shrink: 0;
        margin-bottom: 12px;
      }

      .checked_style {
        font-size: 14px;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 20px;
        background: #0095FF;
        border-radius: 21px 21px 21px 21px;
        padding: 4px 12px;
        flex-shrink: 0;
        margin-bottom: 12px;
      }
    }
  }
}

.fixed_box {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding-bottom: constant(safe-area-inset-bottom);//兼容 IOS<11.2
  padding-bottom: env(safe-area-inset-bottom);//兼容 IOS>11.2
  .btn_box {
    padding: 0 16px 8px;
    .btn {
      height: 40px;
      line-height: 40px;
      background: #0095FF;
      border-radius: 20px;
      text-align: center;
      font-size: 16px;
      color: #fff;
    }
  }
}