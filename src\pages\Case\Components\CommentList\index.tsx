/**
 * @Description: 病例评论列表组件
 * @author: 赵斐
 */
import { randomColor , processNames } from '@/utils/utils'
import styles from './index.less';

// @ts-ignore
const Index = (props) => {
  const { dataSource ,onClickReplyFun} = props
  return (
    <div>
      {
        dataSource.map((item: any, idx: number) => {
          return <div className={styles.cases_details_comment_content} key={idx} onClick={(e) => { onClickReplyFun(e, item.id, item.commentsUserId, item.commentsUserName) }}>
            {
              item.commentsUserHeadUrl?<img className={styles.comment_head} src={item.commentsUserHeadUrl} />:
              <div className={styles.no_comment_head} style={{background:randomColor(item.commentsUserId)}}>{processNames(item.commentsUserName)}</div>
            }
            
            <div className={styles.comment_detail}>
              <div className={styles.comment_issuer}>{item.commentsUserName}</div>
              <div className={styles.comment_desc}>{item.commentsContent}</div>
              {
                Array.isArray(item.excellentCaseCommentsDtos) && item.excellentCaseCommentsDtos.length ? <>
                  {
                    item.excellentCaseCommentsDtos.map((val: any, index: number) => {
                      return <div className={styles.comment_reply} key={index} onClick={(e) => { onClickReplyFun(e, item.id, val.commentsUserId, val.commentsUserName) }}>
                        <span className={styles.comment_reply_people}>{val.commentsUserName}</span>&nbsp;回复<span className={styles.comment_reply_people}>&nbsp;{val.commentsSuperUserName}</span>:
                        {val.commentsContent}
                      </div>
                    })
                  }
                </> : null
              }
            </div>
          </div>
        })
      }
    </div>
  )
}
export default Index
