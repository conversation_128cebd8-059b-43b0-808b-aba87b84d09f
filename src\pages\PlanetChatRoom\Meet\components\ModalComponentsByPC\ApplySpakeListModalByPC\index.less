.drawer {
  :global {
    .ant-drawer-body {
      padding: 0;
      background: #141414;
    }
  }
}

.spin {
  height: 100%;
  & > :global(.ant-spin-container) {
    height: 100%;
  }
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  width: 100%;
  flex-shrink: 0;
  font-size: 15PX;
  color: #fff;
  font-weight: 500;
  line-height: 21PX;
  padding: 24PX 24PX 16PX;
}

.search_wrap {
  width: 100%;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  column-gap: 16PX;
  padding: 0 24PX 16PX;
  :global {
    .ant-input-affix-wrapper {
      flex: 1;
      background: rgba(255,255,255,0.2);
      border-radius: 23PX;
      padding: 0 12PX;
      border: 0;
      outline: 0;
      box-shadow: none;
    }
    .ant-input {
      height: 40PX;
      background: transparent;
      font-size: 14PX;
      color: #fff;
    }
    .ant-input-clear-icon {
      color: #fff;
    }
  }
  .search_cancel_btn {
    flex-shrink: 0;
    cursor: pointer;
    font-size: 14PX;
    color: #fff;
  }
}

// 用户列表
.user_list_wrap {
  flex: 1;
  padding: 0 24PX 16PX;
  overflow-y: auto;
  .user_item {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    padding: 8PX 0;
    .left {
      flex: 1;
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      overflow: hidden;
      padding-right: 8PX;
      .avatar_wrap {
        width: 24PX;
        height: 24PX;
        flex-shrink: 0;
        margin-right: 12PX;
      }
      .user_name {
        font-size: 14PX;
        color: #fff;
        height: 24PX;
        line-height: 23PX;
        margin-right: 8PX;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .right {
      flex-shrink: 0;
      display: flex;
      font-size: 14PX;
      color: #0095FF;
      & > span {
        cursor: pointer;
      }
    }
  }
}
