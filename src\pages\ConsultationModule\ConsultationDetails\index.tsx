/**
 * @Description: 图文指导详情入口
 * @author: 赵斐
 */
import React, { useEffect, useState, lazy, Suspense } from 'react';
import { getOperatingEnv, } from '@/utils/utils'
import { debounce } from 'lodash';
// H5端专家图文指导详情
const H5Details = lazy(() => import('./H5Details'))
// PC端专家图文指导详情
const PcDetails = lazy(() => import('./PcDetails'))
const Index: React.FC = (props: any) => {
  const [pageType, setPageType] = useState<any>(); // 1pc 2 移动端
  // ① 判定当前页面视口是否小于750 如果小于750则为移动端
  let updateType = () => {
    // let clientWidth = document.documentElement.clientWidth;
    let env = getOperatingEnv() // 获取当前操作环境 1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用
    let type = env == '4' ? 1 : 2;
    setPageType(type);
  };
  updateType = debounce(updateType, 100);
  // 进入页面判定是否存在token
  useEffect(() => {
    // ① 判定当前页面视口是否小于750 如果小于750则为移动端
    updateType();
    window.addEventListener('resize', updateType, { passive: true });
    return () => {
      window.removeEventListener('resize', updateType)
    }
  }, []);
  return (
    <Suspense fallback={<div></div>}>
      {
        pageType == 1 ?
          <PcDetails />
          : pageType == 2 ? <H5Details/> : null
      }
    </Suspense>
  )
}
export default Index
