.container {
  min-height: 100vh;
  background: #f5f6f8;
  padding-top: 44px;
  overflow-y: auto;
}

.complete_process_wrap {
  padding: 14px 16px 0;
  background: #fff;
}

.block {
  background: #fff;
  padding-left: 16px;
  margin-bottom: 8px;
  position: relative;
  .form_error {
    font-size: 14px;
    color: #ff4d4f;
    position: absolute;
    bottom: -4px;
    left: 16px;
  }
  &.block_padding {
    padding-bottom: 16px;
  }
  .form_label {
    padding: 16px 0 12px;
    font-size: 16px;
    color: #000;
    font-weight: 600;
    line-height: 22px;
    display: flex;
    align-items: center;
    &.form_label_border {
      border-bottom: 1px solid #E1E4E7;
    }
    .required_mark {
      display: block;
      color: #FF5F57;
    }
    .notes {
      display: block;
      font-size: 12px;
      color: #999;
      font-weight: 400;
      line-height: 17px;
      margin-left: 4px;
    }
  }
  .form_content_select {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    font-size: 14px;
    padding: 16px 16px 16px 0;
    line-height: 20px;
    :global {
      .anticon {
        display: block;
      }
    }
    .select_label {
      color: #666;
      white-space: nowrap;
      margin-right: 8px;
    }
    .select_arrow {
      width: 16px;
      min-width: 16px;
      height: 16px;
      color: #999;
      margin-left: 8px;
    }
    .select_value {
      flex: 1;
      color: #000;
      text-align: right;
      word-break: break-all;
      &.select_value_empty {
        color: #CCC;
      }
    }
  }
  .form_content_textarea {
    padding-right: 16px;
    textarea {
      border-radius: 2px;
      background: #f8f8f8;
      border: 1px solid #eee;
      padding: 12px;
      font-size: 14px;
      color: #000;
      height: 112px;
    }
  }
  .form_content_box {
    margin-right: 16px;
    border-radius: 2px;
    background: #f8f8f8;
    border: 1px solid #eee;
    padding: 12px;
    font-size: 14px;
    color: #000;
    height: 112px;
    overflow-y: auto;
    :global {
      .adm-text-area-element {
        font-size: 14px;
        color: #000;
      }
      .adm-text-area {
        position: relative;
      }
    }
    .child_item {
      display: flex;
      flex-wrap: nowrap;
      overflow: hidden;
      .child_item_label {
        white-space: nowrap;
        line-height: 24px;
        height: 22px;
      }
      .child_item_content {
        flex: 1;
        overflow: hidden;
        padding-top: 2px;
      }
    }
  }
  .textarea_template {
    display: flex;
    padding-top: 8px;
    .textarea_template_item {
      height: 25px;
      line-height: 25px;
      padding: 0 4px;
      border-radius: 2px;
      border: 1px solid #EEE;
      font-size: 12px;
      color: #666;
      margin-right: 8px;
      &.textarea_template_item_checked {
        color: #0095FF;
        border-color: #0095FF;
      }
      :global {
        .anticon {
          margin-right: 2px;
        }
      }
    }
  }
  .form_content_upload {
    display: flex;
    flex-wrap: wrap;
    padding-bottom: 8px;
    padding-right: 8px;
    .upload_item {
      position: relative;
      width: 60px;
      min-width: 60px;
      height: 60px;
      margin-right: 8px;
      margin-bottom: 8px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      .upload_delete_btn {
        position: absolute;
        right: -5px;
        top: -5px;
        font-size: 10px;
        color: rgba(0,0,0,0.5);
        padding: 2px;
        :global {
          .anticon {
            display: block;
          }
        }
      }
    }
    .upload_btn {
      &.disabled {
        display: none;
        width: 0;
        height: 0;
        opacity: 0;
        overflow: hidden;
        visibility: hidden;
        z-index: -9999;
      }
      position: relative;
      width: 60px;
      height: 60px;
      margin-right: 8px;
      margin-bottom: 8px;
      border-radius: 2px;
      border: 1px solid #eee;
      background: #F8F8F8;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 23px;
      color: #ccc;
      :global {
        .ant-upload-picture-card-wrapper {
          width: 100%;
          height: 100%;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 99;
        }
        .ant-upload.ant-upload-select-picture-card {
          width: 100%;
          height: 100%;
          display: block !important;
          opacity: 0;
        }
      }
    }
  }
}

.remark_box {
  padding: 0 16px;
  font-size: 12px;
  color: #666;
  line-height: 20px;
  margin-bottom: 48px;
  :global {
    .ant-typography {
      display: inline-block;
      margin-bottom: 0;
    }
    .ant-typography-copy {
      margin-left: 8px;
      color: #0095FF;
    }
  }
}

.fixed_box {
  padding: 0 16px 1px;
  padding-bottom: constant(safe-area-inset-bottom);//兼容 IOS<11.2
  padding-bottom: env(safe-area-inset-bottom);//兼容 IOS>11.2
  .btn {
    margin-bottom: 12px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
    background: #0095FF;
    font-size: 16px;
    color: #fff;
    text-align: center;
  }
}
