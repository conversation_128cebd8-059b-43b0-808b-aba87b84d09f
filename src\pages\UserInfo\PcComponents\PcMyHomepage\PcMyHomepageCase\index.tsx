import NoDataRender from '@/components/NoDataRender';
import { useEffect, useRef, useState } from 'react';
import { connect } from 'umi';
import styles from './index.less';
import CaseCard from '@/componentsByPc/CaseCard';
import { message } from 'antd';

const PcMyHomepageCase: React.FC<any> = (props) => {
  const { dispatch } = props;
  const scrollParentRef = useRef<HTMLDivElement | null>(null);
  const [caseList, setCaseList] = useState([]); // 病例列表
  const UerInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');

  useEffect(() => {
    getInitData()
  }, [])

  const getInitData = () => {
    dispatch({
      type: "expertAdvice/getCaseInfoByExpertsUserId",
      payload: {
        expertsUserId: UerInfo?.friUserId,  // 专家id
      }
    }).then((res: any) => {
      const { code, content, msg } = res || {};
      if (res && code == 200) {
        setCaseList(content)
      } else {
        message.error(msg)
      }
    }).catch((err) => {
      console.log(err)
    });
  }

  return (
    <div className={styles.tab_content_list} ref={(ref) => (scrollParentRef.current = ref)}>
      <div className={styles.space_wrap}>
        {caseList && caseList.length ? (
          caseList.map((item: any, ind) => (
            <div className={styles.tab_case_list} key={ind}>
              <CaseCard key={ind} caseData={item} />
            </div>
          ))
        ) : (
          <div className={styles.no_data_wrap_box}><NoDataRender className={styles.noDataStyle} /></div>
        )}
      </div>
    </div>
  );
};

export default connect(({ userInfoStore, expertAdvice, loading }: any) => ({ userInfoStore, expertAdvice, loading }))(
  PcMyHomepageCase,
);
