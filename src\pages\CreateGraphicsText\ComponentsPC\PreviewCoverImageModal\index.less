.modal {
  :global {
    .ant-modal-content {
      background: transparent;
      box-shadow: none;
    }
    .ant-modal-close {
      color: #fff;
    }
    .ant-modal-close-x {
      font-size: 22px;
    }
    .ant-modal-body {
      padding-top: 150px;
    }
  }
}
.container {
  background: #fff;
  width: 649px;
  border-radius: 4px;
  margin: 0 auto;
  .modal_title {
    text-align: center;
    font-size: 20px;
    color: #000;
    font-weight: 500;
    padding-top: 24px;
    margin-bottom: 34px;
  }
  .box {
    width: 399px;
    min-height: 363px;
    background: url("../../../../assets/GlobalImg/preview_head.png") no-repeat center top;
    background-size: 100% 363px;
    margin: 0 auto;
    padding: 184px 12px 0;
    .inner {
      padding: 16px 12px;
      .article_title {
        font-size: 15px;
        color: #222;
        line-height: 21px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2; /* 指定显示三行 */
        overflow: hidden;
      }
      .article_user_box {
        display: flex;
        align-items: center;
        i {
          display: inline-block;
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background-repeat: no-repeat;
          background-position: center;
          background-size: cover;
          margin-right: 4px;
          font-style: normal;
          line-height: 16px;
          color: #fff;
          font-size: 8px;
          text-align: center;
          white-space: nowrap;
        }
        span {
          font-size: 12px;
          color: #000;
        }
        span + span {
          margin-left: 8px;
          color: #666;
        }
      }
      .article_kingdom_box {
        & > div {
          display: inline-flex;
          align-items: center;
          width: auto;
          background: #E4F0FC;
          border-radius: 4px;
          padding: 2px 4px;
          vertical-align: middle;
        }
        i {
          width: 14px;
          height: 14px;
          border-radius: 50%;
          margin-right: 4px;
          background: url("../../../../assets/GlobalImg/blue_associated.png") no-repeat center;
          background-size: cover;
        }
        span {
          color: #0095FF;
          font-size: 11px;
          height: 15px;
          line-height: 17px;
        }
      }
    }
    .inner.horizontal {
      display: flex;
      flex-wrap: nowrap;
      .left {
        flex: 1;
        padding-right: 8px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
      .right {
        width: 122px;
        flex-shrink: 0;
        height: 86px;
        border-radius: 4px;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;

        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        color: #999;
      }
    }
    .inner.vertical {
      .article_title {
        margin-bottom: 8px;
      }
      .article_image_box {
        display: flex;
        flex-wrap: nowrap;
        column-gap: 4px;
        margin-bottom: 8px;
        .article_image_item {
          flex: 1;
          max-width: 33.3333%;
          height: 86px;
          background-repeat: no-repeat;
          background-position: center;
          background-size: cover;

          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          color: #999;
          &:first-child {
            border-radius: 4px 0 0 4px;
          }
          &:last-child {
            border-radius: 0 4px 4px 0;
          }
        }
      }
      .article_kingdom_box {
        margin-bottom: 8px;
      }
    }
  }
  .footer {
    display: flex;
    justify-content: center;
    padding-bottom: 40px;
    :global {
      .ant-btn-lg {
        padding-left: 20px;
        padding-right: 20px;
        height: 38px;
      }
    }
  }
}
