import React, { useEffect, useState, useRef } from 'react'
import { connect, history, useAliveController } from 'umi'
import classNames from 'classnames'
import { stringify } from 'qs'
import { cloneDeep } from 'lodash'
import request from '@/utils/request'
import {
  getHeaders,
  useThrottle,
  isIOS,
  getIsIniPhoneAndWeixin,
  getOperatingEnv,
  randomColor,
  processNames
} from '@/utils/utils'
import { Upload, Spin } from 'antd'
import { Toast, Popover } from 'antd-mobile'
import { PlusOutlined, CloseOutlined, PlusCircleOutlined } from '@ant-design/icons'
import styles from './index.less'
import articleIcon from '@/assets/UserInfo/article.png'
import linkIcon from '@/assets/UserInfo/link.png'
import NavBar from '@/components/NavBar'
import QuillDom from '@/pages/CreateGraphicsText/Quill'
import ToolbarMobile from '@/pages/CreateGraphicsText/Quill/ToolbarMobile'
import CropModal from '@/pages/CreateGraphicsText/ComponentsH5/CropModal'
let timer2 = null
let initialPageY = 0

const Index: React.FC = (props: any) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
  const { query } = history.location
  const {
    id, // 图文id
    topicId: topicHomeTopicId, // 话题id
    topicName: topicHomeTopicName, // 话题名称
  } = query
  const { loading, graphicsText, dispatch } = props
  const quillRef = useRef(null)
  const initialModalState = { // 裁剪
    cropVisible: false,
    cropImageIndex: null,
    cropImageUrlShow: null,
  }
  const [toolbarPanelVisible, setToolbarPanelVisible] = useState(false) // 面板是否打开
  const [quillHistoryStack, setQuillHistoryStack] = useState({}) // 历史记录
  const [quillFormat, setQuillFormat] = useState({}) // 获取格式
  const [textImgList, setTextImgList] = useState([]) // 封面图
  const [refreshPageState, setRefreshPageState] = useState(false) // 刷新页面
  const [modalState, setModalState] = useState(initialModalState)
  const [isHaveKingdomData, setIsHaveKingdomData] = useState(true)
  const { clear } = useAliveController()

  useEffect(() => {
    if (id) {
      // 编辑图文获取原数据
      imgTextInfoUpdateId()
    } else {
      // 插入话题
      if (topicHomeTopicId && topicHomeTopicName) {
        timer2 = setTimeout(() => {
          insertTopicByTopicName()
        }, 100)
      }
    }
    // 获取王国数据
    getCreateAndJoinKingdomList()

    const env = getOperatingEnv()
    if (isIOS() && window.visualViewport && env != '5' && env != '6') {
      document.documentElement.addEventListener('touchstart', touchstart, {passive: false})
      document.documentElement.addEventListener('touchmove', touchmove, {passive: false})
      document.documentElement.addEventListener('touchend', touchend, {passive: false})
      window.visualViewport.addEventListener('resize', visualViewportResize)
    }
    // 取消监听
    return () => {
      clearTimeout(timer2)
      initialPageY = 0
      if (isIOS() && window.visualViewport && env != '5' && env != '6') {
        document.documentElement.removeEventListener('touchstart', touchstart, {passive: false})
        document.documentElement.removeEventListener('touchmove', touchmove, {passive: false})
        document.documentElement.removeEventListener('touchend', touchend, {passive: false})
        window.visualViewport.removeEventListener('resize', visualViewportResize)
      }
    }
  }, [])

  // 页面高度改变
  const visualViewportResize = () => {
    const dom = document.getElementById('content')
    const dom2 = document.getElementById('fixed_toolbar_mobile')
    const originHeight = document.documentElement.clientHeight
    const newHeight = window.visualViewport.height;
    const keyboardHeight = originHeight - newHeight
    document.documentElement.scrollTop = 0
    if (keyboardHeight == 0) {
      dom.style.height = ''
      dom2.style.paddingBottom = getIsIniPhoneAndWeixin() ? '34px' : ''
    } else {
      dom.style.height = `calc(100% - 45px - ${keyboardHeight}px)`
      dom2.style.paddingBottom = `${keyboardHeight}px`
      if (quillRef.current && quillRef.current.quill) {
        quillRef.current.quill.scrollIntoView()
      }
    }
  }

  // 触摸事件
  const touchstart = (e) => {
    initialPageY = e.touches[0].pageY
  }

  // 移动事件
  const touchmove = (e) => {
    const scrollDom = getScrollableElement(e.target)
    if (scrollDom) {
      if (scrollDom.scrollHeight - scrollDom.clientHeight - scrollDom.scrollTop < 10) {
        const pageY = e.touches[0].pageY
        if (pageY < initialPageY) {
          e.preventDefault()
        }
      }
    } else {
      e.preventDefault()
    }
  }

  // 触摸结束
  const touchend = (e) => {
    initialPageY = 0
  }

  // 获取滚动dom
  const getScrollableElement = (el: HTMLElement | null) => {
    let current = el?.parentElement
    while (current) {
      if (current.clientHeight < current.scrollHeight) {
        return current
      }
      current = current.parentElement
    }

    return null
  }

  useEffect(() => {
    if (graphicsText.selectedTopicName) {
      insertTopic(graphicsText.selectedTopicId, graphicsText.selectedTopicName, graphicsText.selectTopicType)
    }
  }, [graphicsText.selectedTopicName])

  // 获取图文详情
  const imgTextInfoUpdateId = () => {
    dispatch({
      type: 'graphicsText/imgTextInfoUpdateId',
      payload: {
        imageTextId: id, // 图文id
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200 && content) {
        if (content.createUserId != UserInfo.friUserId) {
          Toast.show('您没有编辑权限')
          setTimeout(() => {
            goBack()
          }, 1000)
          return
        }
        setTextImgList(content.textImgList || [])
        dispatch({
          type: 'graphicsText/save',
          payload: {
            selectedKingdomId: content.kingdomId,
            selectedKingdomName: content.kingdomName,
          }
        })
        if (content.contentJson) {
          const contentJsonObj = JSON.parse(content.contentJson)
          quillRef?.current?.quill.setContents(contentJsonObj)
        }
      } else {
        Toast.show(msg || '数据加载失败')
      }
    }).catch(err => {})
  }

  // 获取王国数据
  const getCreateAndJoinKingdomList = () => {
    dispatch({
      type: 'userInfoStore/getCreateAndJoinKingdomList',
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200 && content) {
        if (content['1'] || content['2']) {
          setIsHaveKingdomData(true)
        } else {
          setIsHaveKingdomData(false)
        }
      } else {
        setIsHaveKingdomData(false)
      }
    }).catch()
  }

  // 插入话题
  const insertTopicByTopicName = () => {
    if (quillRef && quillRef.current) {
      quillRef.current.topicFn({topicId: topicHomeTopicId, topicName: `#${topicHomeTopicName}#`}, 2)
      clearTimeout(timer2)
    } else {
      timer2 = setTimeout(() => {
        insertTopicByTopicName()
      }, 50)
    }
  }

  // 选择王国
  const selectKingdom = () => {
    if (isHaveKingdomData) {
      setTimeout(() => {
        history.push('/CreateGraphicsText/SelectKingdom')
      }, 150)
    }
  }

  // 插入话题
  const insertTopic = (topicId, topicName, selectTopicType) => {
    dispatch({
      type: 'graphicsText/save',
      payload: {
        selectedTopicId: null,
        selectedTopicName: null,
        selectTopicType: 2,
      }
    })
    timer2 = setTimeout(() => {
      insertTopicTimeout(topicId, topicName, selectTopicType)
    }, 100)
  }

  // 定时器循环
  const insertTopicTimeout = (topicId, topicName, selectTopicType) => {
    if (quillRef && quillRef.current && quillRef.current.quill && quillRef.current.quill.getSelection(true)) {
      quillRef.current.topicFn({topicId, topicName: `#${topicName}#`}, selectTopicType)
      clearTimeout(timer2)
    } else {
      timer2 = setTimeout(() => {
        insertTopicTimeout(topicId, topicName, selectTopicType)
      }, 50)
    }
  }

  // 获取历史记录
  const getQuillHistoryStack = (stack) => {
    setQuillHistoryStack(cloneDeep(stack))
  }

  // 获取格式
  const getQuillFormat = (format) => {
    setQuillFormat(cloneDeep(format))
  }

  // 面板打开、关闭
  const toolbarPanelOnChange = (visible) => {
    setToolbarPanelVisible(visible)
  }

  // 上传图片
  const handleUploadImage = (fileUrlView) => {
    const textImgListClone = cloneDeep(textImgList)
    textImgListClone.push({
      imageUrlShow: fileUrlView,
      isCover: 1,
    })
    setTextImgList(textImgListClone)
  }

  // 删除图片
  const deleteImage = (index) => {
    const textImgListClone = cloneDeep(textImgList)
    textImgListClone.splice(index, 1)
    setTextImgList(textImgListClone)
  }

  // 裁剪图片
  const cropModalShow = (cropImageIndex, cropImageUrlShow) => {
    setModalState({
      ...modalState,
      cropVisible: true,
      cropImageIndex,
      cropImageUrlShow,
    })
  }

  // 裁剪图片关闭
  const cropModalHide = () => {
    setModalState({
      ...modalState,
      cropVisible: false,
      cropImageIndex: null,
      cropImageUrlShow: null,
    })
  }

  // 裁剪确定
  const handleCroppingImage = (imageUrlShow) => {
    let textImgListClone = cloneDeep(textImgList)
    textImgListClone.splice(modalState.cropImageIndex, 1, {
      imageUrlShow,
      isCover: 1,
    })
    setTextImgList(textImgListClone)
    cropModalHide()
  }

  // 上传校验
  const beforeUpload = (file) => {
    const isSize = file.size / 1024 / 1024 < 15
    if (!isSize) {
      Toast.show('超过15M限制，不允许上传~')
      return false
    }
    const { name: fileName } = file || {}
    const suffix = fileName.substring(fileName.lastIndexOf('.')+1).toLowerCase()
    const isJpgOrPng = file.type === 'image/jpg' || file.type === 'image/jpeg' ||  file.type === 'image/png' || file.type === 'image/gif'
    const isSuffixByJpgOrPng = suffix === 'jpg' || suffix === 'jpeg' || suffix === 'png' || suffix === 'gif'
    if (!isJpgOrPng || !isSuffixByJpgOrPng) {
      Toast.show('只能上传JPG、JPEG、PNG、GIF格式的图片~')
      return false
    }
    if (textImgList.length >= 20) {
      Toast.show(`最多上传20张图片`)
      return false
    }
    return true
  }

  // 上传完成
  const uploadOnChange = (info) => {
    if (info && !info.file.status) {
      return
    }
    if (info.file.status === 'uploading') {
      Toast.show({
        icon: 'loading',
        content: '',
        duration: 0,
      })
    }
    if (info && info.file.status === 'error') {
      Toast.clear()
      Toast.show('上传失败')
      return
    }
    if (info && info.file.status === 'done') {
      Toast.clear()
      if (textImgList.length >= 20) {
        Toast.show(`最多上传20张图片`)
        return false
      }
      const res = info.file.response || {}
      const { code, content, msg } = res
      if (code == 200 && content) {
        handleUploadImage(content.fileUrlView)
      } else {
        Toast.show(msg || '上传失败')
      }
    }
  }

  // 自定义上传方法
  const uploadCustomRequest = ({onProgress,onError,onSuccess,data,filename,file,withCredentials,action,headers}) => {
    const formData = new FormData()
    formData.append('file', file)
    if (/iPhone/.test(window.navigator.userAgent) && /QQBrowser/.test(window.navigator.userAgent)) {
      const random = Math.floor(Math.random()*1000) + 100
      setTimeout(() => {
        request(action, {
          method: 'POST',
          body: formData,
        }).then(res => {
          const { code } = res
          if (code == 200) {
            onSuccess(res)
          } else {
            onError(res)
          }
        })
      }, random)
    } else {
      request(action, {
        method: 'POST',
        body: formData,
      }).then(res => {
        const { code } = res
        if (code == 200) {
          onSuccess(res)
        } else {
          onError(res)
        }
      })
    }
  }

  // 存草稿
  let submitDraft = () => {
    submit(1)
  }
  submitDraft = useThrottle(submitDraft, 500)

  // 上传前
  let beforeSubmit = (isDisabled) => {
    if (isDisabled) {
      return
    }
    submit(2)
  }
  beforeSubmit = useThrottle(beforeSubmit, 500)

  // 上传
  const submit = async (saveType) => {
    const imageTextContent = quillRef?.current?.unprivilegedEditor?.getHTML()
    const articleLength = quillRef?.current?.unprivilegedEditor?.getText().replace(/\n/g, '').length
    const contentJson = JSON.stringify(quillRef?.current?.unprivilegedEditor?.getContents())
    if (saveType == 2 && articleLength == 0) {
      Toast.show('请输入正文')
      return
    }
    if (saveType == 2 && articleLength > 500) {
      Toast.show('正文字数不能超过500字')
      return
    }

    Toast.show({
      icon: 'loading',
      content: '',
      duration: 0,
    })
    await clear()
    dispatch({
      type: 'graphicsText/editImgTextInfo',
      payload: {
        forwardDescribe: null,
        id: id || null,
        imageTextContent: imageTextContent,
        contentJson: contentJson,
        imageTextId: null,
        imageTitle: null,
        imageType: 2,
        textImgList: textImgList,
        isForward: 0,
        kingdomId: graphicsText.selectedKingdomId,
        saveType,
      }
    }).then(res => {
      Toast.clear()
      const { code, content, msg } = res
      if (code == 200 && (content || content == 0)) {
        if (saveType == 1) {
          Toast.show('保存成功')
          if (!id) {
            history.replace(`/CreateGraphicsText/CreatePost?${stringify({
              ...query,
              id: content,
            })}`)
            setRefreshPageState(!refreshPageState)
          }
        } else {
          history.replace('/Square?tabKey=2&publish=1')
        }
      } else {
        Toast.show(msg || '数据加载失败')
      }
    }).catch(err => {})
  }

  // 跳转文章
  const goToCreateArticle = () => {
    if (topicHomeTopicId && topicHomeTopicName) {
      history.replace(`/CreateGraphicsText/CreateArticle?${stringify({
        topicId: topicHomeTopicId,
        topicName: topicHomeTopicName,
      })}`)
    } else {
      history.replace(`/CreateGraphicsText/CreateArticle`)
    }
  }

  // 跳转外链
  const goToCreateLink = () => {
    history.replace('/CreateGraphicsText/CreateExternalLinks')
  }

  // 返回
  const goBack = () => {
    if (history.length > 1) {
      history.goBack()
    } else {
      history.replace('/')
    }
  }

  const loadingImgTextInfoUpdateId = !!loading.effects['graphicsText/imgTextInfoUpdateId']
  return (
    <Spin spinning={loadingImgTextInfoUpdateId} wrapperClassName={styles.spin}>
      <NavBar
        title="发帖子"
        RightRender={() => (
          <div className={styles.nav_bar_right}>
            <span className={styles.nar_bar_link_btn} onClick={submitDraft}>存草稿</span>
            <span className={classNames(styles.nar_bar_btn, {
              [styles.disabled]: quillRef?.current?.unprivilegedEditor?.getText().replace(/\n/g, '').length == 0,
            })} onClick={() => beforeSubmit(quillRef?.current?.unprivilegedEditor?.getText().replace(/\n/g, '').length == 0)}>发布</span>
          </div>
        )}
        bordered
      />
      <div className={classNames(styles.container, {[styles.show_panel]: toolbarPanelVisible})} id="content">
        <div className={styles.select_kingdom_box} id="select_kingdom_box">
          <div className={styles.select_left} onClick={selectKingdom}>
            <i className={styles.select_left_icon_1}></i>
            <span>选择关联王国</span>
            <i className={styles.select_left_icon_2}></i>
            <span className={styles.divider}></span>
          </div>
          {
            graphicsText.selectedKingdomName ?
              <div className={styles.select_right}>{graphicsText.selectedKingdomName}</div>
              :
              <div className={styles.select_right_default}>
                {
                  isHaveKingdomData ? '不关联王国，默认只发布在个人主页'
                    : '当前无可关联王国'
                }
              </div>
          }
          <Popover
            getContainer={() => document.getElementById('select_kingdom_box')}
            content={
              <div className={styles.popover_container}>
                <div className={styles.popover_link} onClick={goToCreateArticle}>
                  <img src={articleIcon} width={18} height={18} alt=""/>
                  <span>创建文章</span>
                </div>
                <div className={styles.popover_link} onClick={goToCreateLink}>
                  <img src={linkIcon} width={18} height={18} alt=""/>
                  <span>创建外链</span>
                </div>
              </div>
            }
            trigger="click"
            placement="bottomLeft"
          >
            <div className={styles.change_page_btn}>
              <PlusCircleOutlined />
            </div>
          </Popover>
        </div>

        <div className={styles.editor_box}>
          <QuillDom
            deviceType="mobile"
            placeholder="分享您的想法..."
            ref={quillRef}
            getQuillHistoryStack={getQuillHistoryStack}
            getQuillFormat={getQuillFormat}
          />
        </div>

        <div className={styles.cover_img_box}>
          {
            textImgList.map((item, index) => {
              return (
                <div key={item.imageUrlShow} className={styles.cover_img_item} style={{backgroundImage: `url(${item.imageUrlShow})`}}>
                  <div className={styles.cover_img_item_shadow}></div>
                  <CloseOutlined onClick={() => deleteImage(index)}/>
                  <div className={styles.cover_img_item_edit} onClick={() => cropModalShow(index, item.imageUrlShow)}>编辑</div>
                </div>
              )
            })
          }
          <div className={styles.cover_img_btn}>
            <PlusOutlined />
            <Upload
              headers={getHeaders()}
              accept="image/*"
              action={`/api/server/base/uploadFile?${stringify({ fileType: 23, userId: UserInfo?.friUserId})}`}
              onChange={uploadOnChange}
              beforeUpload={beforeUpload}
              showUploadList={false}
              multiple={true}
              customRequest={uploadCustomRequest}
            />
          </div>
        </div>
      </div>

      <ToolbarMobile
        quillRef={quillRef}
        quillHistoryStack={quillHistoryStack}
        quillFormat={quillFormat}
        toolbarPanelOnChange={toolbarPanelOnChange}
        createType="createPost"
        handleUploadImage={handleUploadImage}
      />

      <CropModal
        visible={modalState.cropVisible}
        imageUrlShow={modalState.cropImageUrlShow}
        handleCroppingImage={handleCroppingImage}
        onCancel={cropModalHide}
      />
    </Spin>
  )
}

export default connect(({ graphicsText, loading }: any) => ({ graphicsText, loading }))(Index)
