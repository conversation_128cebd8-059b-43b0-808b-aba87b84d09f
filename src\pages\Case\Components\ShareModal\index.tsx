/**
 * @Description: 病例分享弹窗
 */
import React, { useState } from 'react'
import { createPortal } from 'react-dom'
import { shareWeChatInApp, shareInApp, isIOS, getOperatingEnv } from '@/utils/utils'
import { Toast, Popup, Mask } from 'antd-mobile'
import { Typography } from 'antd'
import styles from './index.less'

// 图片、icon
import wechat from '@/assets/GlobalImg/wechat.png'
import wechat_friend from '@/assets/GlobalImg/wechat_friend.png'
import enterprise_wechat from '@/assets/GlobalImg/enterprise_wechat.png'
import copy_link from '@/assets/GlobalImg/copy_link.png'

interface PropsType {
  visible: boolean,          // 弹窗是否显示
  topicName: string,         // 病例名称
  onCancel: any,             // 关闭弹窗回调
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { visible } = props
  // 获取当前操作环境，2 微信浏览器   5 FRIDAY app   6 Jarvis App   7 企微浏览器
  const env = getOperatingEnv()

  const [shareTipsByH5Visible, setShareTipsByH5Visible] = useState(false) // 分享提示弹窗（手机自带浏览器环境）
  const [shareTipsVisible, setShareTipsVisible] = useState(false)         // 分享提示箭头弹窗（微信浏览器环境）

  // 分享到微信
  const onClickWeChat = (scene) => {
    props.onCancel()
    if (env == '5' || env == '6') {
      shareWeChatInApp({
        type: 'link',                      // type取link（默认）或image
        url: window.location.href,         // url为网页链接或图片链接，必传
        thumbnail: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/DigitalHealthShareImg.png', // 缩略图链接
        title: '【FRIDAY医生星球】牙医都来这里学习和交流！',    // 标题
        description: props.topicName || '',   // 描述
        scene: scene,                         // scene取session（默认）或timeline
      }).then(res => {
      })
    } else if (env == '2' || env == '7') {
      setShareTipsVisible(true)
    } else {
      setShareTipsByH5Visible(true)
    }

  }

  // 系统分享
  const onClickSystem = () => {
    props.onCancel()
    if (env == '5' || env == '6') {
      shareInApp({
        type: 'link',                // type取link（默认）或image
        url: window.location.href,   // url为网页链接或图片链接，必传
      }).then(res => {
      })
    } else if (env == '7') {
      setShareTipsVisible(true)
    }
  }

  // 复制成功
  const onCopy = () => {
    props.onCancel()
    Toast.show('复制成功')
  }

  // 关闭分享提示箭头弹窗（微信浏览器环境）
  const onCloseShareTipsModal = () => {
    setShareTipsVisible(false)
  }

  // 关闭分享提示弹窗（手机自带浏览器环境）
  const onCloseShareTipsByH5Modal = () => {
    setShareTipsByH5Visible(false)
  }

  return (
    <>
      <Popup
        className={styles.popup}
        onMaskClick={props.onCancel}
        visible={visible}
      >
        <div className={styles.header} onClick={props.onCancel}>
          <div className={styles.line}></div>
        </div>

        <div className={styles.bottom_btn_wrap}>
          {/* android-APP环境暂不支持，所以先隐藏 */}
          {
            (env == '5' || env == '6') && !isIOS() ? null :
              <>
                <div className={styles.btn_item} onClick={() => onClickWeChat('session')}>
                  <img src={wechat} width={48} height={48} alt=""/>
                  <p>转发给朋友</p>
                </div>
                <div className={styles.btn_item} onClick={() => onClickWeChat('timeline')}>
                  <img src={wechat_friend} width={48} height={48} alt=""/>
                  <p>分享到朋友圈</p>
                </div>
              </>
          }

          {/* 系统分享 */}
          {
            (env == '5' || env == '6' || env == '7') &&
            <div className={styles.btn_item} onClick={onClickSystem}>
              <img src={enterprise_wechat} width={48} height={48} alt=""/>
              <p>转发到企业微信</p>
            </div>
          }

          {/* 复制链接 */}
          <Typography.Paragraph copyable={{
            text: window.location.href,
            icon: [
              <div className={styles.btn_item}>
                <img src={copy_link} width={48} height={48} alt=""/>
                <p>复制链接</p>
              </div>,
              <div className={styles.btn_item}>
                <img src={copy_link} width={48} height={48} alt=""/>
                <p>复制链接</p>
              </div>
            ],
            tooltips: [false, false],
            onCopy: onCopy,
          }}></Typography.Paragraph>
        </div>

      </Popup>

      {/* 分享提示箭头弹窗（微信浏览器环境） */}
      <Mask style={{ '--z-index': "1010" }} opacity={0.7} visible={shareTipsVisible} onMaskClick={onCloseShareTipsModal} />
      {/* 修改挂载的 HTML 节点 */
        shareTipsVisible && createPortal((
          <div className={styles.fixed_share_box}>
            <i className={styles.icon1}></i>
            <div className={styles.message_box}>
              <div>点击右上角</div>
              <div>发送到 微信好友 或者 分享到朋友圈</div>
            </div>
            <div className={styles.icon_box}>
              <i className={styles.icon2}></i>
              <i className={styles.icon3}></i>
            </div>
          </div>
        ), document.body)
      }

      {/* 分享提示弹窗（手机自带浏览器环境） */}
      <Mask style={{ '--z-index': "1010" }} opacity={0.7} visible={shareTipsByH5Visible} onMaskClick={onCloseShareTipsByH5Modal} />
      {/* 修改挂载的 HTML 节点 */
        shareTipsByH5Visible && createPortal((
          <div className={styles.fixed_share_box} style={{top: '40%', left: '50%', right: 'initial', transform: 'translateX(-50%)'}}>
            <div className={styles.message_box}>
              <div>请使用浏览器自带分享功能</div>
              <div>发送到 微信 或者 朋友圈</div>
            </div>
            <div className={styles.icon_box}>
              <i className={styles.icon2}></i>
              <i className={styles.icon3}></i>
            </div>
          </div>
        ), document.body)
      }
    </>
  )
}

export default Index
