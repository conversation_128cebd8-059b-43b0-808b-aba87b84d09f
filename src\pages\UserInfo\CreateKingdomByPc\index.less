.container {
  background: #EEF3F9;
  height: 100vh;
}

.wrap {
  width: 816px;
  margin: 0 auto;
  .header{
    margin: 16px 0;
    background: #fff;
    padding: 17px 24px;
    height: 62px;
    display: flex;
    border-radius: 8px;
    align-items: center;
    .header_title{
      font-size: 20px;
      font-weight: 600;
      color: #000000;
      .header_title_icon{
        width: 24px;
        height: 24px;
        margin-right: 10px;
        cursor: pointer;
        vertical-align: sub;
      }
    }
  }

  .content{
    height: calc(100vh - 274px);
    overflow: auto;
    overflow-x: hidden;
    background: #FFFFFF;
    border-radius: 8px;
    padding: 40px;
  
    .form_input{
      border-bottom: 1px solid #DDDDDD;
      :global{
        .ant-input::placeholder{
          font-size: 22px;
          font-weight: 600;
          color: #AAAAAA;
        }
        .ant-input{
          font-size: 22px;
          padding-left: 0;
        }
      }
    }
    .form_text_area{
      margin-top: 16px;
      border-bottom: 1px solid #DDDDDD;
      padding-bottom: 8px;
      :global{
        .ant-input {
          padding: 0;
        }
        .ant-input::placeholder{
          font-size: 14px;
          font-weight: 400;
          color: #999999;
        }
      }
    }
    .form_radio{
      :global{
        .ant-radio-wrapper{
          margin-right: 25px;
        }
        .ant-form-item{
          margin-bottom: 12px;
        }
        .ant-form-item-label > label::after{
          content: '';
        }
        .ant-form-item-label > label{
          // margin-left: 8px;
          padding-right: 36px;
        }
      }
    }
    .form_select{
      margin-top: 36px;
      :global{
        .ant-form-item-label > label{
          margin-left: 10px;
          padding-right: 36px;
        }
        .ant-form-item-label > label::after{
          content: '';
        }
        .ant-form-item{
          margin-bottom: 16px;
        }
        .ant-select-item{
          padding: 0 16px;
        }
        .ant-select-dropdown{
          padding: 0;
        }
        .ant-select-item-option-selected:not(.ant-select-item-option-disabled),.ant-select-item-option-active:not(.ant-select-item-option-disabled){
          background: none;
        }
      }
    }

    .upload_kingdom_wrap{
      padding-left: 110px;
      .tips{
        font-size: 12px;
        font-weight: 400;
        color: #999999;
        margin-top: 8px;
      }
      :global{
        .ant-upload-select-picture-card{
          width: 120px;
          height: 120px;
          background: #F8F8F8;
          border: 0;
          margin: 0;
        }
        .ant-form-item{
          margin-bottom: 12px;
        }
      }
      .upload_box{
        width: 120px;
        height: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;

        :global {
          .ant-spin-nested-loading {
            width: 100%;
          }
        }

        .edit_btn_box {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 120px;
          height: 23px;
          background: rgba(0,0,0,0.5);
          padding: 0 27px;
          display: flex;
          align-items: center;
          justify-content: space-between;

          div {
            cursor: pointer;
            font-size: 11px;
            font-weight: 400;
            color: #FFFFFF;
            line-height: 13px;
          }
        }

        .upload_img{
          width: 100%;
          height: 120px;
        }
        
        .init_upload_img{
          width: 32px;
          height: 32px;
          margin: 0 auto;
        }
      }
    }
  }
}

// 
.kingdom_wrap{
  padding-top: 16px;
  border-radius: 4px;
  overflow: hidden;
  .kingdom_header{
    border-radius: 4px;
    height: 36px;
    background: #F5F6F8;
    margin: 0 16px;
    padding: 0 12px 0 25px;
    position: relative;
    &>img{
      position: absolute;
      width: 16px;
      height: 16px;
      left: 12px;
      top: 10px;
    }
    :global{
      .ant-input{
        line-height: 30px;
      }
      .ant-input::placeholder{
        color: #999999;
      }
    }
    
  }
  
}
.kingdom_li{
  cursor: pointer;
  height: 48px;
  line-height: 48px;
  border-bottom: 1px solid #F5F6F8;
  display: flex;
  align-items: center;
  position: relative;
  padding-right: 16px;
  &:hover{
    .kingdom_name{
      max-width: 190px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 14px;
      font-weight: 400;
      color: #0095FF;
    }
    .kingdom_phone{
      padding: 0 12px;
      display: block;
      width: 120px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 14px;
      font-weight: 400;
      color: #0095FF;
    }
  }
  .kingdom_name{
    max-width: 190px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 14px;
    font-weight: 400;
    color: #666666;
  }
  .kingdom_phone{
    padding: 0 12px;
    display: block;
    width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 14px;
    font-weight: 400;
    color: #666666;
  }
  .check_kingdom_name{
    max-width: 190px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 14px;
    font-weight: 400;
    color: #0095FF;
  }
  .check_kingdom_phone{
    padding: 0 12px;
    display: block;
    width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 14px;
    font-weight: 400;
    color: #0095FF;
  }
  .kingdom_img{
    position: absolute;
    display: block;
    width: 24px;
    height: 24px;
    top: 0;
    right: 16px;
    &>img{
      width: 100%;
      height: 100%;
    }
  }
}
.footer{
  position: fixed;
  bottom: 0;
  left: 0;
  height: 80px;
  width: 100%;
  border-top: 1px solid #DDDDDD;
  background: #FFFFFF;
  
  display: flex;
  align-items: center;
  .footer_content{
    width: 816px;
    margin: 0 auto;
    text-align: right;
    :global{
      .ant-btn-primary{
        background: #0095FF;
        border-radius: 4px;
      }
    }
  }
}