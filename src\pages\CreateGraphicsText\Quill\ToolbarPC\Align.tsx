import React from 'react'
import classNames from 'classnames'
import { Align<PERSON>eftOutlined, Align<PERSON>enterOutlined, AlignRightOutlined } from '@ant-design/icons'
import styles from './Align.less'
// 对齐方式
const alignData = [{ value: 'left', label: '左对齐', icon: <AlignLeftOutlined /> }, { value: 'center', label: '居中对齐', icon: <AlignCenterOutlined /> }, { value: 'right', label: '右对齐', icon: <AlignRightOutlined /> },]
class Align extends React.Component {
  static defaultProps = {
    itemOnClick: () => {},
  }

  constructor(props) {
    super(props)
    this.state = {}
  }

  componentDidMount(): void {

  }

  // 点击对齐方式
  itemOnClick = (value) => {
    this.props.itemOnClick(value)
  }

  // 阻止默认事件
  onMouseDown = (e) => {
    e.preventDefault()
  }

  render() {
    const { selectedValue } = this.props
    return (
      <div className={styles.align_container}>
        {
          alignData.map(item => (
            <div
              key={item.value}
              className={classNames(styles.align_item, {
                // [styles.selected]: selectedValue == item.value,
              })}
              onMouseDown={this.onMouseDown}
              onClick={() => this.itemOnClick(item.value)}
            >
              {item.icon}
              <span>{item.label}</span>
            </div>
          ))
        }
      </div>
    )
  }
}

export default Align
