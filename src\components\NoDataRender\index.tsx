/**
 * @Description: 暂无数据组件
 */
import classNames from 'classnames';
import styles from './index.less';

import noDataImg from '@/assets/GlobalImg/no_data.png'; // 无数据图片

interface PropsType {
  className?: string,
  style?: object,
  text?: string,
}

const Index = (props: PropsType) => {
  const {
    className = '',
    style = {},
    text = '暂无数据'
  } = props;

  return(
    <div className={classNames(styles.nodata, [className])} style={style}>
      <img src={noDataImg} alt="" />
      {text}
    </div>
  )
}
export default Index
