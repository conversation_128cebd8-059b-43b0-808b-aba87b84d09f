
// 移动端
.login_wrap {
  width: 100%;
  height: 100vh;
  background: #fff;
  position: relative;

  .pc_gohome {
    display: none;
  }

  .loginNavBar {
    display: block!important;
    position: relative!important;
    z-index: 99!important;
    background: none!important;
  }

  .login_bg_wrap {
    width: 100%;
    height: 100vh;
  }
  .login_bg_box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 250px;
    z-index: 0;
    display: block;

    img {
      width: 100%;
      height: auto;
    }
  }
  .login_content_title {
    position: relative;
    z-index: 2;
    padding-left: 16px;
    margin-bottom: 38px;
    padding-top: 48px;

    .login_img {
      width: 124px;
      height: 26px;
      margin-bottom: 16px;

      img {
        width: 100%;
        height: auto;
      }
    }

    .login_text {
      font-size: 24px;
      font-weight: 600;
      color: #000;
      line-height: 34px;
    }
  }
  .login_form_wrap {
    padding: 0 16px;
    box-sizing: border-box;
    position: relative;
    z-index: 10;

    .login_form_input {
      height: 54px;
      border-bottom: 1px solid #EEF3F9;
      margin-bottom: 20px;

      :global {
        .ant-form-item-control-input {
          min-height: 54px;
        }
        .ant-form-item-explain {
          margin-left: 12px;
        }
      }
    }

    .login_form_input1 {
      height: 54px;
      border-bottom: 1px solid #EEF3F9;
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;

      .img_code {
        flex: 1;
      }

      .verification_code {
        width: 96px;
        flex-shrink: 0;
        padding-top: 16px;
        text-align: right;

        img {
          width: 96px;
          height: 40px;
          position: relative;
          top: -10px;
        }
      }

      :global {
        .ant-form-item-control-input {
          min-height: 54px;
        }
        .ant-form-item-explain {
          margin-left: 12px;
        }
      }
    }

    .login_form_input2 {
      height: 54px;
      border-bottom: 1px solid #EEF3F9;
      margin-bottom: 48px;
      display: flex;
      justify-content: space-between;

      .phone_code {
        flex: 1;
      }

      .sendCode {
        flex: 1;
        flex-shrink: 0;
        padding-top: 16px;
        font-size: 16px;
        font-weight: 400;
        color: #0095FF;
        line-height: 19px;
        cursor: pointer;
      }

      :global {
        .ant-form-item-control-input {
          min-height: 54px;
        }
        .ant-form-item-explain {
          margin-left: 12px;
        }
      }
    }
    .forget_password{
      cursor: pointer;
      font-size: 16px;
      text-align: right;
      margin-bottom: 10px;
      color: #0095FF;
    }

    .login_Btn {
      width: 100%;
      height: 40px;
      border: none;
      background: #0095FF;
      border-radius: 20px;
      font-size: 16px;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 22px;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .login_TripartiteList{
      display: flex;
      justify-content: center;
      align-items: center;
      img{
        width: 48px;
        height: 48px;
        margin: 10px 0 20px 0;
      }
    }
    .login_go {
      font-size: 13px;
      font-weight: 400;
      color: #666;
      line-height: 15px;
      text-align: center;

      span {
        color: #0095FF;
      }
    }
  }
  .login_title_wrap {
    width: 100%;
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 44px;
    margin-bottom: 48px;

    .login_title {
      font-size: 17px;
      font-weight: 500;
      color: #000000;
      line-height: 20px;
    }
  }
}

// pc端
.pc_login_wrap {
  width: 100%;
  height: 100vh;
  background: #fff;
  position: relative;
  background: url("https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/Global_pc_bg.png") no-repeat;
  background-size: contain;
  display: flex;
  justify-content: center;
  position: relative;

  .loginNavBar {
    display: none!important;
  }

  .pc_gohome {
    position: absolute;
    top: 24px;
    left: 24px;
    display: block;
    font-size: 24px;
    font-weight: 600;
    color: #000000;
    line-height: 28px;
    cursor: pointer;

    img {
      width: 24px;
      height: 24px;
      margin-right: 16px;
      position: relative;
      top: -3px;
    }
  }

  .login_bg_wrap {
    width: 343px;
    height: 100vh;
  }
  .login_bg_box {
    display: none;
  }
  .login_content_title {
    position: relative;
    z-index: 2;
    padding-left: 0;
    margin-bottom: 38px;
    padding-top: 136px;

    .login_img {
      width: 124px;
      height: 26px;
      margin-bottom: 16px;

      img {
        width: 100%;
        height: auto;
      }
    }

    .login_text {
      font-size: 24px;
      font-weight: 600;
      color: #000;
      line-height: 34px;
    }
  }
  .login_form_wrap {
    padding: 0;
    box-sizing: border-box;
    position: relative;
    z-index: 10;

    .login_form_input {
      height: 54px;
      border-bottom: 1px solid #EEF3F9;
      margin-bottom: 20px;

      :global {
        .ant-form-item-control-input {
          min-height: 54px;
        }
        .ant-form-item-explain {
          margin-left: 12px;
        }
      }
    }

    .login_form_input1 {
      height: 54px;
      border-bottom: 1px solid #EEF3F9;
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;

      .img_code {
        flex: 1;
      }

      .verification_code {
        width: 96px;
        flex-shrink: 0;
        padding-top: 16px;
        text-align: right;

        img {
          width: 96px;
          height: 40px;
          position: relative;
          top: -10px;
          cursor: pointer;
        }
      }

      :global {
        .ant-form-item-control-input {
          min-height: 54px;
        }
        .ant-form-item-explain {
          margin-left: 12px;
        }
      }
    }

    .login_form_input2 {
      height: 54px;
      border-bottom: 1px solid #EEF3F9;
      margin-bottom: 48px;
      display: flex;
      justify-content: space-between;

      .phone_code {
        flex: 1;
      }

      .sendCode {
        flex: 1;
        flex-shrink: 0;
        padding-top: 16px;
        font-size: 16px;
        font-weight: 400;
        color: #0095FF;
        line-height: 19px;
        cursor: pointer;
      }

      :global {
        .ant-form-item-control-input {
          min-height: 54px;
        }
        .ant-form-item-explain {
          margin-left: 12px;
        }
      }
    }
    .forget_password{
      cursor: pointer;
      font-size: 16px;
      text-align: right;
      margin-bottom: 10px;
      color: #0095FF;
    }

    .login_Btn {
      width: 100%;
      height: 40px;
      border: none;
      background: #0095FF;
      border-radius: 20px;
      font-size: 16px;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 22px;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .login_TripartiteList{
      display: flex;
      justify-content: center;
      align-items: center;
      img{
        width: 36px;
        height: 36px;
        margin: 10px 0 20px 0;
      }
    }
    .login_go {
      font-size: 13px;
      font-weight: 400;
      color: #666;
      line-height: 15px;
      text-align: center;
      cursor: pointer;

      span {
        color: #0095FF;
      }
    }
  }
  .login_title_wrap {
    width: 100%;
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 44px;
    margin-bottom: 48px;

    .login_title {
      font-size: 17px;
      font-weight: 500;
      color: #000000;
      line-height: 20px;
    }
  }
}
