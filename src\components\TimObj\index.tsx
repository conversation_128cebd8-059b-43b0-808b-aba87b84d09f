import React, { useEffect, } from 'react';
import {connect} from "umi";

const TimObj= props => {
  const {
    dispatch,
  } = props;
  const userInfoData = JSON.parse(localStorage.getItem('userInfo'));
  const { friUserId } = userInfoData || {}  // 获取用户

  useEffect(async () => {
    if(props.id) {
      let getImInfoByUser = await dispatch({
        type: 'tim/getImInfoByUser',
        payload: {wxUserId: friUserId}
      })
      let loginTim = await dispatch({
        type: 'tim/loginTim',
      })
      let timObj = await dispatch({type: 'tim/getTim'})
    }
  },[props.id]);

  // 登录后获取该用户的IM秘钥信息
  useEffect(async () => {
    const userInfoData = JSON.parse(localStorage.getItem('userInfo'));
    const { friUserId } = userInfoData || {}  // 获取用户id
    if(!friUserId) { return }
    // 获取IM秘钥信息
    let getImInfoByUser = await dispatch({
      type: 'tim/getImInfoByUser',
      payload: { wxUserId: friUserId }
    })
    let loginTim = await dispatch({
      type: 'tim/loginTim',
    })
    let timObj = await dispatch({ type: 'tim/getTim' })

  },[]);

  return ( <></> )
};
export default connect(({ tim, login }) => ({ tim, login }))(TimObj);
