@import (reference) '~antd/es/style/themes/index';

.page_warp {
  display: flex;
  justify-content: space-around;
}
.page_content {
  width: 1000px;
  // background: #3b434b;
}

.Payment_result {
  width: 1000px;
  height: 338px;
  background: #FAFAFA;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  .Payment_result_icon {
    width: 40px;
    height: 40px;
    // background: url("../../assets/Payment/Payment_Step_success.png") no-repeat;
    background-size: 40px 40px;
  }

  .Payment_result_icon_Success {
    width: 40px;
    height: 40px;
    background: url("../../assets/Payment/Payment_Step_success.png") no-repeat;
    background-size: 40px 40px;
  }

  .Payment_result_icon_fail {
    width: 40px;
    height: 40px;
    background: url("../../assets/Payment/Payment_CallBack_fail.png") no-repeat;
    background-size: 40px 40px;
  }

  .Payment_result_icon_audit {
    width: 40px;
    height: 40px;
    background: url("../../assets/Payment/Payment_CallBack_audit.png") no-repeat;
    background-size: 40px 40px;
  }

  .Payment_result_title {
    font-size: 24px;
    font-weight: 600;
    color: #333333;
    margin-top: 8px;
    margin-bottom: 8px;
  }
  .Payment_result_desc {
    font-size: 16px;
    font-weight: 400;
    color: #666666;
    margin-bottom: 44px;
  }

  .Payment_result_btn {
    display: flex;
    .Payment_result_btn_repay {
      width: 112px;
      height: 38px;
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      border: 1px solid #999999;
      font-size: 16px;
      font-weight: 400;
      color: #666666;
      text-align:center;
      line-height: 38px;
      cursor: pointer;
      user-select: none;
    }
    .Payment_result_btn_back {
      width: 112px;
      height: 38px;
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      background: #3C89FD;
      font-size: 16px;
      font-weight: 400;
      color: #FFFFFF;
      text-align:center;
      line-height: 38px;
      margin-left: 16px;
      cursor: pointer;
      user-select: none;
    }
  }

}




// 移动端布局

  .Mobile_Wrap {
    width: 100%;
    min-width: 285px;
    height: 100vh;
    // background: linear-gradient(180deg, #EDE7FF 0%, #FFFFFF 100%, #FAFAFA 100%);
    background: #ffffff;
    border-radius: 0px 0px 0px 0px;
    opacity: 1;
  }

  .Mobile_title_statusbar {
    width: 100%;
    height: 44px;
  }

  .Mobile_title_Wrap {
    width: 100%;
    height: 44px;
    margin-bottom: 12px;
    display: flex;
    justify-content: center;
    align-items: center;

    .Mobile_title {
      font-size: 17px;
      font-weight: 500;
      color: #000000;
    }
  }

  .Mobile_content {
    width: 100%;
    height: 100%;
    background: #FAFAFA;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 24px;

    .Mobile_content_title {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 16px;

      .Mobile_content_title_status_icon {
        width: 20px;
        height: 20px;
        // background: #58C13E;
        opacity: 1;
        border-radius: 50%;
        margin-right: 8px;
      }
      .Mobile_content_title_status_icon_Success {
        background: url('../../assets/Payment/Payment_CallBack_success.png');
        width: 20px;
        height: 20px;
        background-size: 20px 20px;
        display: inline-block;
      }
      .Mobile_content_title_status_icon_fail {
        background: url('../../assets/Payment/Payment_CallBack_fail.png');
        width: 20px;
        height: 20px;
        background-size: 20px 20px;
        display: inline-block;
      }
      .Mobile_content_title_status_icon_audit {
        background: url('../../assets/Payment/Payment_CallBack_audit.png');
        width: 20px;
        height: 20px;
        background-size: 20px 20px;
        display: inline-block;
      }

      .Mobile_content_title_status_text {
        font-weight: 600;
        color: #333333;
        font-size: 20px;
      }
    }

    .Mobile_content_title_status_desc {
      font-size: 14px;
      font-weight: 400;
      color: #999999;
      text-align: center;
      max-width: 312px;
      margin-bottom: 24px;
    }

    .Mobile_content_btn_wrap {
      display: flex;
      justify-content: center;
    }

    .Mobile_content_btn {
      height: 33px;
      border-radius: 17px 17px 17px 17px;
      opacity: 1;
      border: 1px solid #CCCCCC;
      font-size: 15px;
      font-weight: 400;
      color: #000000;
      line-height: 33px;
      padding-left: 16px;
      padding-right: 16px;
      cursor: pointer;
      user-select: none;
    }

    .Mobile_content_repay {
      height: 33px;
      border-radius: 17px 17px 17px 17px;
      opacity: 1;
      // border: 1px solid #CCCCCC;
      background: linear-gradient(270deg, #8966FF 0%, #6BA6FF 100%);
      font-size: 15px;
      font-weight: 400;
      color: #fff;
      line-height: 33px;
      padding-left: 16px;
      padding-right: 16px;
      cursor: pointer;
      user-select: none;
    }
    .Mobile_content_repay:active {
      background: linear-gradient(270deg, #6BA6FF 0%, #8966FF 100%);
    }
  }



