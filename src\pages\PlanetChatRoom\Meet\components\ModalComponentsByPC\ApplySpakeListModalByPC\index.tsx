/**
 * @Description: 申请发言弹窗（PC端）
 */
import React, { useState, useEffect, useRef } from 'react';
import { history, connect } from 'umi'
import { Drawer, Switch, Input, Spin } from 'antd';
import styles from './index.less';

import SearchInput from '@/components/SearchInput'   // 公共搜索框组件
import Avatar from '@/pages/PlanetChatRoom/components/Avatar'             // 头像组件
import NoDataRender from '@/components/NoDataRender' // 暂无数据组件

// 图片图标
import mikeOpenIcon from '@/assets/GlobalImg/mike_open.png'
import search_white_icon from '@/assets/PlanetChatRoom/search_white_icon.png'

interface PropsType {
  visible: boolean,          // 弹窗是否显示
  onCancel: () => void,      // 关闭弹窗
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {
    visible,
    PlanetChatRoom,
    onCancel,
    onClickStopSpeaking, // 点击停止发言
    onClickAgreeSpeaking, // 点击同意发言
  } = props

  const {
    handUpList, // 申请连麦列表
  } = PlanetChatRoom || {}

  const [queryKey, setQueryKey] = useState('')             // 搜索关键词

  useEffect(() => {
    if (visible) {

    } else {
      // 清空数据
      cleanState()
    }
  }, [visible])

  // 关闭弹窗清空数据
  const cleanState = () => {
    setQueryKey('')
  }

  // 搜索框输入事件
  const onChanceInput = (e) => {
    console.log('搜索框输入', e.target.value)
    setQueryKey(e.target.value)
  }

  // 点击搜索框的取消按钮
  const onClickCancel = () => {
    console.log('点击搜索框的取消按钮')
    setQueryKey('')
  }

  return (
    <Drawer
      open={visible}
      className={styles.drawer}
      placement="right"
      onClose={onCancel}
      destroyOnClose
      closable={false}
      maskStyle={{background: 'rgba(0,0,0,0)'}}
      width={383}
    >
      <Spin wrapperClassName={styles.spin} spinning={false}>
        <div className={styles.container}>
          <div className={styles.header}>申请发言</div>

          {/* 搜索框 */}
          <div className={styles.search_wrap}>
            <Input
              placeholder="搜索成员"
              prefix={<img src={search_white_icon} width={20} height={20} alt="" />}
              value={queryKey}
              onChange={onChanceInput}
              allowClear={true}
            />

            <span className={styles.search_cancel_btn} onClick={onClickCancel}>取消</span>
          </div>

          {/* 用户列表 */}
          <div className={styles.user_list_wrap}>
            {
              handUpList && handUpList.length > 0 ? handUpList.map(item => {
                if (queryKey && item.name && item.name.indexOf(queryKey.trim()) == -1) {
                  return null
                }
                return (
                  <div key={item.wxUserId} className={styles.user_item}>
                    <div className={styles.left}>
                      <div className={styles.avatar_wrap}>
                        <Avatar size={24} userInfo={item}/>
                      </div>
                      <div className={styles.user_name}>{item.name}</div>
                      {/* 发言中，statusType状态类型：0申请连麦 1接受连麦 */}
                      {
                        item.statusType == 1 &&
                        <img src={mikeOpenIcon} width={24} height={24} style={{padding: 4, flexShrink: 0}} alt=""/>
                      }
                    </div>
                    <div className={styles.right}>
                      {
                        item.statusType == 1 ? <span onClick={() => onClickStopSpeaking(item)}>停止发言</span>
                          : <span onClick={() => onClickAgreeSpeaking(item)}>同意发言</span>
                      }
                    </div>
                  </div>
                )
              }): <NoDataRender style={{marginTop: 40, color: '#fff'}} text="暂无申请~"/>
            }
          </div>
        </div>
      </Spin>
    </Drawer>
  )
}
export default connect(({ loading, PlanetChatRoom }: any) => ({ loading, PlanetChatRoom }))(Index)
