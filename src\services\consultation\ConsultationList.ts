// @ts-ignore
/* eslint-disable */
import request from '@/utils/request'
import {stringify} from "qs";

/**
 * 获取指导订单列表
 * @params wxUserId                     当前登录用户ID
 */
export async function getConsultationList(params) {
  const {
    pageNum,
    pageSize,
    queryType,     // 查询类型(1我的指导、2我发起的指导)
    type,         //  [string] 是 指导类型(1图文、2视频)
    processNode,  // [string] 是 流程节点(图文流程节点[1选择指导方式、2描述病例问题、3支付指导费用、4病例资料被查看、5问题被回复并对话、6结束指导交易成功]; 视频流程节点[1选择指导方式、2描述病例问题、3提交指导单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、 7结束指导、8确认并支付指导费用、9交易成功])
    orderNumber,  // [string] 是 订单号
    userId,       //  [string] 是 操作人ID
    expertsId,    // [string] 是 指导医生ID
    startDate,    //  [string] 是 起始时间
    endDate,      // [string] 是 结束时间
  } = params || {}
  // /api/server/consultationOrder/getConsultationOrderList
  return request(`/api/server/h5ConsultationOrder/getConsultationOrderList?${stringify({pageNum,pageSize,queryType})}`, {
    method: 'POST',
    data: { ...params }
  })
}


/**
 * 查询指导订单详情
 * @param params
 */
export async function getConsultationOrderInfo(params) {
  /*const {
    consultationId, // [string] 是 指导订单ID
    type, // [string] 是 (1:运营端订单详情, 2:H5/WEB端视频订单详情)
  } = params || {}*/
  return request(`/api/server/consultationOrder/getConsultationOrderInfo?${stringify(params)}`, {
    method: 'GET',
    data: { ...params }
  })
}


/**
 * 图文指导提交订单或去支付
 * @param params
 */
export async function submitConsultationPictureOrderPay(params) {
  const {
    wxUserId,           // [string]
    userName,           // [string]
    id,                 // [string] 是 订单id
    amount,             // [int] 是 订单金额
    freeTimes,          // [int] 是 免费次数
    payMethod,          // [int] 是 图文 1提交指导 2立即支付
    operationPlatform,  // [int] 是 操作平台 1H5 2PC
    payType,            // [int] 是 1微信、2支付宝
} = params || {}
  return request(`/api/server/h5ConsultationOrderPay/submitConsultationPictureOrderPay?${stringify({wxUserId:wxUserId,userName:userName})}`, {
    method: 'POST',
    data: { ...params }
  })
}

/**
 * 视频指导提交订单或去支付
 * submitConsultationVideoOrderPay
 */
export async function submitConsultationVideoOrderPay(params) {
  const {
    wxUserId,           // [string]
    userName,           // [string]
    id,                 // [string] 是 订单id
    payMethod,          // [int] 是 视频 1确认订单 2立即支付
    operationPlatform,  // [int] 是 操作平台 1H5 2PC
    payType,            // [string] 1微信、2支付宝
  } = params || {}
  return request(`/api/server/h5ConsultationOrderPay/submitConsultationVideoOrderPay?${stringify({wxUserId:wxUserId,userName:userName})}`, {
    method: 'POST',
    data: { ...params }
  })
}

/**
 * 根据订单id查询支付状态
 * getConsultationOrderPayStatus
 */
export async function getConsultationOrderPayStatus(params) {
  const {
    wxUserId,           // [string]
    userName,           // [string]
    id,                 // [string] 是 订单id
  } = params || {}
  return request(`/api/server/h5ConsultationOrderPay/getConsultationOrderPayStatus?${stringify(params)}`, {
    method: 'POST',
    data: { ...params }
  })
}

/**
 * 视频指导支付弹框确认支付/发送支付订单给用户
 * confirmPayOrSendOrder
 */
export async function confirmPayOrSendOrder(params) {
  const {
    wxUserId,           // [string]
    userName,           // [string]
    id,                 // [string] 是 订单id
  } = params || {}
  return request(`/api/server/h5ConsultationOrderPay/confirmPayOrSendOrder?${stringify(params)}`, {
    method: 'POST',
    data: { ...params }
  })
}
