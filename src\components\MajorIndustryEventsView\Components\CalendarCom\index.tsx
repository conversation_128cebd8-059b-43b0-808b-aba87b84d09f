import React from 'react';
import classNames from 'classnames';
import CalendarItem from '../Components/CalendarItem';
import styles from './index.less';
interface IProps {
  dataList?: any;
  pcOrMobileMode?: string;
}
const Index: React.FC<IProps> = ({ dataList, pcOrMobileMode }) => {
  return <div className={classNames(styles.normal_wrapper, {
    [styles.normal_mobile_wrapper]: pcOrMobileMode === 'mobile',
    [styles.normal_pc_wrapper]: pcOrMobileMode === 'pc'
  })}>
    {dataList && dataList.map((item, index) => {
      return <CalendarItem
        itemData={item}
        key={`${item.id}-${index}`}
        pcOrMobileMode={pcOrMobileMode} 
      />
    })}
  </div>
}

export default Index;