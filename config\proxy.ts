/**
 * xxx为后端zuul的名称
 */
function proxy(REACT_APP_ENV:string) {
  let EnvPath = '', MpEnvPath = '', StaticPath='';
  if (process.env.NODE_ENV === 'development') {
    switch (REACT_APP_ENV) {
      case 'dev': EnvPath = 'https://dhealth-dev.friday.tech/api', MpEnvPath= 'https://mp-dev.friday.tech', StaticPath = 'https://dhealth-dev.friday.tech';
      // case 'dev': EnvPath = 'http://47.99.34.80:6009', StaticPath = 'https://xxxx.com';
      // case 'dev': EnvPath = 'http://192.168.3.153:6009', StaticPath = 'https://xxxx.com';
      // case 'dev': EnvPath = 'http://47.99.34.80:6009', StaticPath = 'https://xxxx.com';
        break;
      case 'test': EnvPath = 'https://dhealth-test.friday.tech/api', MpEnvPath= 'https://mp-test.friday.tech/mpapi', StaticPath = 'https://dhealth-test.friday.tech';
        break;
      case 'pre': EnvPath = 'https://dhealth-pre.friday.tech/api', MpEnvPath= 'https://mp-pre.friday.tech/mpapi', StaticPath = 'https://dhealth-pre.friday.tech';
        break;
      case 'pro': EnvPath = 'https://dhealth.friday.tech/api', MpEnvPath= 'https://mp.friday.tech/mpapi', StaticPath = 'https://dhealth.friday.tech';
        break;
    }
    let poxy = {
      // '/api/': {
      //   target: 'http://192.168.1.17:6008',
      //   "changeOrigin": true,
      // },
      '/api/server': {
        target:`${EnvPath}/server`,
        // target: 'http://192.168.3.153:6008',
        // target: 'http://192.168.1.17:6008',
        pathRewrite: { "/api/server": "" },
        "changeOrigin": true,
      },
      '/api/user': {
        target: `${EnvPath}/user`,
        // target: 'http://192.168.3.153:6001',
        pathRewrite: { "^/api/user": "" },
        "changeOrigin": true,
      },
      '/api/web-server': {
        // target: `${EnvPath}/user`,
        target: 'http://192.168.3.153:6008/',
        pathRewrite: { "^/api/web-server": "" },
        "changeOrigin": true,
      },
      '/api/fri-uc': {
        target: `${EnvPath}/fri-uc`,
        // target: `${MpEnvPath}/fri-uc`,
        pathRewrite: { "^/api/fri-uc": "" },
        "changeOrigin": true,
      },
    }
    return poxy;
  }
}


export default proxy;
