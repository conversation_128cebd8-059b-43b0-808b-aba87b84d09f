﻿export default [
  {
    path: '/',
    component: '../layouts/BlankLayout',
    layout: false,
    routes: [
      {
        path: '/',
        redirect: '/Redirect'
      },

      {
        // 中转页，要跳到首页去
        path: '/Redirect',
        layout: false,
        title: 'Home',
        name: 'Home',
        component: './Home/Redirect',
      },
      {
        // Sass诊所端跳转进来中转页自动登录
        path:'/ExternalPage',
        layout: false,
        component: './ExternalPage/index',
      },
      {
        // 5i5ya嵌套iframe
        path:'/IframeLoadingPage',
        layout: false,
        routes: [
          {
            // 中转页
            path: '/IframeLoadingPage',
            component: './IframeLoadingPage/index',
          },
        ]
      },
      {
        // 用户相关页面
        path: '/User',
        // layout: {
        //   menuRender: false,
        //   hideMenu: true,
        // },
        layout: false,
        routes: [
          {
            // 用户登录页面
            title: 'login',
            name: 'login',
            path: '/User/login',
            component: './Login/index',
          },
          {
            // 注册页
            title: 'Register',
            name: 'Register',
            path: '/User/register',
            component: './Register/index',
          },
          {
            // 设置&找回密码页
            title: 'ForgetPassword',
            name: 'ForgetPassword',
            path: '/User/forgetPassword',
            component: './User/ForgetPassword/index',
          },
          {
            // 用户协议页面
            // title: 'agreement',
            // name: 'agreement',
            path: '/User/agreement/:agreementId',
            component: './User/agreement',
          },
          {
            // 微信授权登录回调
            title: 'wechatAuthLogin',
            name: 'wechatAuthLogin',
            path: '/User/wechatAuthLogin',
            component: './Login/WechatAuthLogin',
          },

          // -----企业管理的登录流程----
          {
            // 企业管理员和员工微信扫码 - 中转首页
            title: 'enterpriseEntrance',
            name: 'enterpriseEntrance',
            path: '/User/enterpriseEntrance',
            component: './Login/EnterpriseQRcodeLogin/Entrance/index'
          },
          {
            // 企业管理员和员工微信扫码 - 微信登录授权提醒页面
            title: 'enterpriseQRcodeTip',
            name: 'enterpriseQRcodeTip',
            path: '/User/enterpriseQRcodeTip',
            component: './Login/EnterpriseQRcodeLogin/AuthPrePage/index'
          },
          {
            // 企业管理员和员工微信扫码 -  微信登录授权页面
            title: 'enterpriseQRcodeAuth',
            name: 'enterpriseQRcodeAuth',
            path: '/User/enterpriseQRcodeAuth',
            component: './Login/EnterpriseQRcodeLogin/Auth/index'
          },

          {
            component: './404',
          },
        ],
      },
      {
        // 个人中心
        path: '/UserInfo',
        name: 'UserInfo',
        layout: false,
        routes: [
          {
            // 我的主页
            title: 'UserInfo',
            name: 'UserInfo',
            path: '/UserInfo',
            component: './UserInfo/NewIndex',
          },
          {
            // 编辑个人信息
            title: 'UserInfo',
            name: 'EditUserInfo',
            path: '/UserInfo/EditUserInfo',
            component: './UserInfo/EditUserInfo',
          },
          {
            // 关注
            title: 'UserInfo',
            name: 'Interest',
            path: '/UserInfo/Interest',
            component: './UserInfo/Interest',
          },
          {
            // 收藏
            title: 'UserInfo',
            name: 'Collect',
            path: '/UserInfo/Collect',
            component: './UserInfo/Collect',
          },
          {
            // 隐私政策
            title: 'UserInfo',
            name: 'Agreement',
            path: '/UserInfo/Agreement',
            component: './UserInfo/Agreement',
          },
          {
            // 服务付费协议
            title: 'UserInfo',
            name: 'ConsultationServiceAgreement',
            path: '/UserInfo/ConsultationServiceAgreement',
            component: './UserInfo/Agreement/ConsultationServiceAgreement',
          },
          {
            // 设置
            title: 'UserInfo',
            name: 'Setting',
            path: '/UserInfo/Setting',
            component: './UserInfo/Setting',
          },
          {
            // 实名认证-选择身份
            title: 'UserInfo',
            name: 'SelectRoles',
            path: '/UserInfo/Certification/SelectRoles',
            component: './UserInfo/Certification/SelectRoles',
          },
          {
            // 实名认证-填写认证信息
            title: 'UserInfo',
            name: 'Information',
            path: '/UserInfo/Certification/EditInformation',
            component: './UserInfo/Certification/EditInformation',
          },
          {
            // 实名认证-查看认证信息
            title: 'UserInfo',
            name: 'Information',
            path: '/UserInfo/Certification/InformationDetails',
            component: './UserInfo/Certification/InformationDetails',
          },
          {
            // 注销
            title: 'UserInfo',
            name: 'Logout',
            path: '/UserInfo/Logout',
            component: './UserInfo/Logout',
          },
          {
            // 创建空间成功
            title: 'UserInfo',
            name: 'SuccessPage',
            path: '/UserInfo/SpaceSuccessPage',
            component: './UserInfo/SpaceSuccessPage/index',
          },
          {
            // 创建王国成功
            title: 'UserInfo',
            name: 'SuccessPage',
            path: '/UserInfo/KingDomSuccessPage',
            component: './UserInfo/KingDomSuccessPage/index',
          },
          {
            // PC 创建王国
            title: 'UserInfo',
            name: 'CreateKingdomByPc',
            path: '/UserInfo/CreateKingdomByPc',
            component: './UserInfo/CreateKingdomByPc/index',
          },
          {
            // PC 创建直播
            title: 'CreateLive',
            name: 'CreateLive',
            path: '/UserInfo/CreateSpaceByPc/Live',
            component: './UserInfo/CreateSpaceByPc/Live/index',
          },

          {
            // PC 创建会议
            title: 'CreateMeet',
            name: 'CreateMeet',
            path: '/UserInfo/CreateSpaceByPc/Meet',
            component: './UserInfo/CreateSpaceByPc/Meet/index',
          },
        ]
      },
      {
        // 专家列表
        path: '/Expert',
        layout: false,
        routes: [
          {
            // 专家列表(问专家)
            title: 'ExpertAdvice',
            name: 'ExpertAdvice',
            path: '/Expert/ExpertAdvice',
            component: './Expert/ExpertAdvice',
          },
          {
            // 专家搜索
            title: 'ExpertSearch',
            name: 'ExpertSearch',
            path: '/Expert/ExpertSearch',
            component: './Expert/ExpertSearch',
          },
          {
            // 专家搜索结果页
            title: 'ExpertResult',
            name: 'ExpertResult',
            path: '/Expert/ExpertResult',
            component: './Expert/ExpertResult',
          },
          {
            // 专家详情页/主页
            title: 'ExpertDetails',
            name: 'ExpertDetails',
            path: '/Expert/ExpertDetails',
            component: './Expert/ExpertDetails/NewIndex',
          },
        ]
      },
      {
        // 病例列表
        path: '/Case',
        layout: false,
        routes: [
          {
            // 病例列表页
            title: 'CaseInputList',
            name: 'CaseInputList',
            path: '/Case/CaseInputList',
            component: './Case/CaseInputList',
          },
          {
            // 病例搜索
            title: 'CaseSearch',
            name: 'CaseSearch',
            path: '/Case/CaseSearch',
            component: './Case/CaseSearch',
          },
          {
            // 病例搜索结果页
            title: 'CaseResult',
            name: 'CaseResult',
            path: '/Case/CaseResult',
            component: './Case/CaseResult',
          },
          {
            // 病例详情页
            title: 'CaseDetails',
            name: 'CaseDetails',
            path: '/Case/CaseDetails',
            component: './Case/CaseDetails/NewIndex',
          },
        ]
      },
      {
        // 活动宣传页面，老的，进入页面重定向到/Home路由
        path:'/activity',
        layout: false,
        routes: [
          {
            title: 'Activity',
            name: 'Activity',
            path: '/activity',
            component: './Activity/index',
          },
        ]
      },
      {
        // 首页、首页搜索
        path:'/home',
        name: 'Home',
        layout: false,
        // component: '../layouts/BlankLayout',
        routes: [
          // 首页
          {
            title: 'Home',
            name: 'Home',
            path: '/home',
            component: './Home/NewIndex',
          },
          // 小程序码页
          {
            title: 'Home',
            name: 'MiniProgramCode',
            path: '/Home/MiniProgramCode',
            component: './Home/MiniProgramCode/index',
          },
          // 首页搜索
          {
            title: 'Home',
            name: 'Search',
            path: '/Home/Search',
            component: './Home/Search/index',
          },
          // 首页搜索结果
          {
            title: 'Home',
            name: 'Search',
            path: '/Home/SearchResult',
            component: './Home/SearchResult/NewIndex',
          },
        ]
      },
      {
        // 广场
        path:'/Square',
        name: 'Square',
        layout: false,
        // component: '../layouts/BlankLayout',
        routes: [
          // 广场首页
          {
            title: 'Square',
            name: 'Square',
            path: '/Square',
            component: './Square/index',
          },
          // 广场搜索
          {
            title: 'Square',
            name: 'Search',
            path: '/Square/Search',
            component: './Square/Search/index',
          },
          // 广场搜索结果
          {
            title: 'Square',
            name: 'Search',
            path: '/Square/SearchResult',
            component: './Square/SearchResult/index',
          },
        ]
      },
      {
        // 王国
        path:'/Kingdom',
        layout: false,
        routes: [
          {
            title: 'Kingdom',
            name: 'Kingdom',
            path: '/Kingdom/:KingdomId',
            component: './Kingdom/index',
          },
        ]
      },
      // 星球聊天室
      {
        path:'/PlanetChatRoom',
        layout: false,
        // component: '../layouts/BlankLayout',
        routes: [
          // 中转页
          {
            title: 'PlanetChatRoom',
            name: 'PlanetChatRoom',
            path: '/PlanetChatRoom/:RoomId',
            component: './PlanetChatRoom/Redirect',
          },
          // 直播
          {
            title: 'PlanetChatRoom',
            name: 'PlanetChatRoom',
            path: '/PlanetChatRoom/Live/:RoomId',
            component: './PlanetChatRoom/SpatialDetail',
          },
          // 会议
          {
            title: 'PlanetChatRoom',
            name: 'PlanetChatRoomMeet',
            path: '/PlanetChatRoom/Meet/:RoomId',
            component: './PlanetChatRoom/Meet/index',
          },
        ]
      },
      // 创建直播
      {
        path:'/CreateSpace',
        layout: false,
        // component: '../layouts/BlankLayout',
        routes: [
          // 创建直播
          {
            title: 'CreateLive',
            name: 'CreateLive',
            path: '/CreateSpace/Live',
            component: './CreateSpace/Live/index',
          },
          // 创建会议
          {
            title: 'CreateMeet',
            name: 'CreateMeet',
            path: '/CreateSpace/Meet',
            component: './CreateSpace/Meet/index',
          },
          // 直播/会议 裁切页面
          {
            title: 'CreateSpace',
            name: 'CropperImg',
            path: '/CreateSpace/CropperImg',
            component: './CreateSpace/CropperImg/index',
          },
        ]
      },
      {
        // title: 'Payment',
        path:'/Payment',
        layout: false,
        /*layout: {
          menuRender: false,
          hideMenu: true,
        },*/
        routes: [
          // 默认跳转到第一步
          {
            path: '/Payment',
            redirect: '/Payment/Step1',
          },
          {
            // 会员支付第一步-填写信息
            title: 'Step1',
            name: 'Step1',
            path: '/Payment/Step1',
            component: './Payment/Step1',
          },
          {
            // 会员支付第第二步-确认支付
            // orderId 是订单号
            title: 'Step2',
            name: 'Step2',
            path: '/Payment/Step2/:orderId',
            component: './Payment/Step2',
          },
          {
            // 会员支付第三步-支付状态回显
            title: 'Step3',
            name: 'Step3',
            path: '/Payment/Step3/:orderId',
            component: './Payment/Step3',
          },
          {
            // 会员支付第三步-支付成功-APP唤起支付后的外部成功页面
            title: 'Step3ByApp',
            name: 'Step3ByApp',
            path: '/Payment/Step3ByApp/:orderId',
            component: './Payment/Step3ByApp',
          },
          {
            // 会员权益宣传页面
            title:'MemberBenefitsPage',
            name: 'MemberBenefitsPage',
            path: '/Payment/MemberBenefitsPage',
            component: './Payment/MemberBenefitsPage/index',
          },
        ]
      },
      {
        // title: 'Payment',
        path:'/PaymentByConsultation',
        layout: false,
        /*layout: {
          menuRender: false,
          hideMenu: true,
        },*/
        routes: [
          // 我的指导
          {
            title: 'MyConsultationList',
            name: 'MyConsultationList',
            path: '/PaymentByConsultation/MyConsultationList',
            component: './PaymentByConsultation/MyConsultationList/index',
          },
          // 确认订单
          {
            title: 'MyConsultationDetails',
            name: 'MyConsultationDetails',
            path: '/PaymentByConsultation/MyConsultationDetails/:id',
            component: './PaymentByConsultation/MyConsultationDetails/index',
          },
          // 收银台
          {
            title: 'ConsultationDetailsPayment',
            name: 'ConsultationDetailsPayment',
            path: '/PaymentByConsultation/ConsultationDetailsPayment/:id',
            component: './PaymentByConsultation/ConsultationDetailsPayment/index',
          },
          // 支付状态回显
          {
            title: 'CompleteOrder',
            name: 'CompleteOrder',
            path: '/PaymentByConsultation/CompleteOrder/:id',
            component: './PaymentByConsultation/CompleteOrder/index',
          },
          // 支付订单
          {
            title: 'OrderPayment',
            name: 'OrderPayment',
            path: '/PaymentByConsultation/OrderPayment/:id',
            component: './PaymentByConsultation/OrderPayment/index',
          }
        ]
      },
      {
        // 指导模块
        path: '/ConsultationModule',
        layout: false,
        routes: [
          {
            // 图文指导详情
            title: 'GraphicConsultationDetails',
            name: 'GraphicConsultationDetails',
            path: '/ConsultationModule/ConsultationDetails',
            component: './ConsultationModule/ConsultationDetails/index',
          },
          {
            // 附件页面
            title: 'AttachmentPage',
            name: 'AttachmentPage',
            path: '/ConsultationModule/AttachmentPage/:caseId',
            component: './ConsultationModule/AttachmentPage/index',
          },
          {
            // 发起指导，第1步选择类型
            title: 'StartConsultationStep1',
            name: 'StartConsultationStep1',
            path: '/ConsultationModule/StartConsultation/Step1',
            component: './ConsultationModule/StartConsultation/PC/Step1',
          },
          {
            // 发起指导，第2步选择模板
            title: 'StartConsultationStep2',
            name: 'StartConsultationStep2',
            path: '/ConsultationModule/StartConsultation/Step2',
            component: './ConsultationModule/StartConsultation/PC/Step2',
          },
          {
            // 发起指导，第3步创建病例
            title: 'StartConsultationStep3',
            name: 'StartConsultationStep3',
            path: '/ConsultationModule/StartConsultation/Step3',
            component: './ConsultationModule/StartConsultation/Step3Index',
          },
          {
            // 发起指导，第4步提交
            title: 'StartConsultationStep4',
            name: 'StartConsultationStep4',
            path: '/ConsultationModule/StartConsultation/Step4',
            component: './ConsultationModule/StartConsultation/Step4Index',
          },
          {
            // 正畸病例详情
            title: 'OrthodonticCasesDetail',
            name: 'OrthodonticCasesDetail',
            path: '/ConsultationModule/OrthodonticCasesDetail/:consultationId',
            component: './ConsultationModule/OrthodonticCasesDetail/index',
          },

          {
            // 正畸病例详情H5版本
            title: 'OrthodonticCasesDetail',
            name: 'OrthodonticCasesDetail',
            path: '/ConsultationModule/H5OrthodonticCasesDetail/:consultationId',
            component: './ConsultationModule/H5OrthodonticCasesDetail/index',
          }

        ]
      },
      // 学习金核销记录页面
      {
        path: '/StudyBonus',
        layout: false,
        routes: [
          {
            title: 'StudyBonusList',
            name: 'StudyBonusList',
            path:'/StudyBonus/list',
            component: './StudyBonus/index',
          },
        ]
      },
      // 外链文章帖子相关页面
      {
        path: '/CreateGraphicsText',
        layout: false,
        routes: [
          {
            // 创建外链(pc端/移动端)
            title: 'CreateExternalLinks',
            name: 'CreateExternalLinks',
            path:'/CreateGraphicsText/CreateExternalLinks',
            component: './CreateGraphicsText/ExternalLinks/CreateExternalLinks',
          },
          {
            // 外链详情(移动端)
            title: 'ExternalLinksDetails',
            name: 'ExternalLinksDetails',
            path:'/CreateGraphicsText/ExternalLinksDetails',
            component: './CreateGraphicsText/ExternalLinks/ExternalLinksDetails',
          },
          {
            // 空间详情(移动端)
            title: 'GraphicsTextSpaceDetails',
            name: 'GraphicsTextSpaceDetails',
            path:'/CreateGraphicsText/SpaceDetails',
            component: './CreateGraphicsText/Space/SpaceDetails',
          },
          {
            // 发帖子（移动端）
            title: 'CreatePost',
            name: 'CreatePost',
            path:'/CreateGraphicsText/CreatePost',
            component: './CreateGraphicsText/Post/CreatePost/index',
          },
          {
            // 帖子详情（移动端）
            title: 'PostDetails',
            name: 'PostDetails',
            path:'/CreateGraphicsText/PostDetails',
            component: './CreateGraphicsText/Post/PostDetails',
          },
          {
            // 发布文章（PC端/移动端）
            title: 'CreateArticle',
            name: 'CreateArticle',
            path:'/CreateGraphicsText/CreateArticle',
            component: './CreateGraphicsText/Article/CreateArticle/index',
          },
          {
            // 发布文章-第2步设置封面（移动端）
            title: 'CreateArticle',
            name: 'CreateArticle',
            path:'/CreateGraphicsText/CreateArticle/Step2',
            component: './CreateGraphicsText/Article/CreateArticle/Mobile/Step2',
          },
          {
            // 文章详情页（移动端）
            title: 'ArticleDetails',
            name: 'ArticleDetails',
            path:'/CreateGraphicsText/ArticleDetails',
            component: './CreateGraphicsText/Article/ArticleDetails/index',
          },
          {
            // 转发（移动端）
            title: 'Forward',
            name: 'Forward',
            path:'/CreateGraphicsText/CreateForward',
            component: './CreateGraphicsText/Forward/CreateForward/index',
          },
          {
            // 转发详情（移动端）
            title: 'Forward',
            name: 'ForwardDetails',
            path:'/CreateGraphicsText/ForwardDetails',
            component: './CreateGraphicsText/Forward/ForwardDetails/index',
          },
          {
            // 选择话题页（移动端）
            title: 'SelectTopic',
            name: 'SelectTopic',
            path:'/CreateGraphicsText/SelectTopic',
            component: './CreateGraphicsText/SelectTopic/index',
          },
          {
            // 选择王国页（移动端）
            title: 'SelectKingdom',
            name: 'SelectKingdom',
            path:'/CreateGraphicsText/SelectKingdom',
            component: './CreateGraphicsText/SelectKingdom/index',
          },
          {
            // 话题主页
            title: 'TopicHome',
            name: 'TopicHome',
            path:'/CreateGraphicsText/TopicHome',
            component: './CreateGraphicsText/TopicHome/index', // topicId 前往话题主页需要携带topicId
          },
        ]
      },
      {
        // 创建指导模块
        path: '/CreationOrthodontics',
        layout: false,
        routes: [
          // Step1-基本信息
          {
            title: 'CreationOrthodontics',
            name: 'CreationOrthodontics',
            path: '/CreationOrthodontics/Step1',
            component: './CreationOrthodontics/index',
          },
          // Step2-检查及分析
          {
            title: 'CreationOrthodontics',
            name: 'CreationOrthodontics',
            path: '/CreationOrthodontics/Step2',
            component: './CreationOrthodontics/index',
          },
          //  Step3-问题清单及诊断
          {
            title: 'CreationOrthodontics',
            name: 'CreationOrthodontics',
            path: '/CreationOrthodontics/Step3',
            component: './CreationOrthodontics/index',
          },
          // Step4-治疗方案
          {
            title: 'CreationOrthodontics',
            name: 'CreationOrthodontics',
            path: '/CreationOrthodontics/Step4',
            component: './CreationOrthodontics/index',
          },
          // Step5-影像资料
          {
            title: 'CreationOrthodontics',
            name: 'CreationOrthodontics',
            path: '/CreationOrthodontics/Step5',
            component: './CreationOrthodontics/index',
          },
          // 正畸初诊检查表
          {
            title: 'OrthodonticChecklist',
            name: 'OrthodonticChecklist',
            path: '/CreationOrthodontics/OrthodonticChecklist',
            component: './CreationOrthodontics/components/Step2/OrthodonticChecklist',
          }
        ]
      },
      {
        title: 'SpacePoster',
        name: 'SpacePoster',
        path: '/Poster',
        component: './Poster/index',
        layout: false,
      },
      // Friday App下载页
      {
        title: 'AppDownload',
        name: 'AppDownload',
        path: '/AppDownload',
        component: './AppDownload/index',
        layout: false,
      },
      {
        title: 'WXsubscribeBack',
        name: 'WXsubscribeBack',
        path: '/WXsubscribeBack',
        component: './WXsubscribeBack/index',
        layout: false,
      },
      {
        component: './404',
      },
    ],
  },

  {
    component: './404',
  },
];
