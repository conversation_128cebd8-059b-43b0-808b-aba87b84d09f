.offlineCom_city {
  display: flex;
  flex-wrap: wrap;
 
  .city_item {
    flex-shrink: 0;
    background: #ffffff;
    text-align: center;
    height: 26px;
    margin-right: 8px;
    line-height: 26px;
    margin-bottom: 6px;
  }
  .active {
    background: #009DFF;
    color: #fff;
  }
}

.offlineCom_mobile_city {
  padding: 4px 12px 10px;
  .city_item {
    padding: 0 14px;
    border-radius: 13px;
    
  }
}

.offlineCom_pc_city {
  padding: 0 12px 24px;
  .city_item {
    padding: 0 16px;
    border-radius: 13px;
    cursor: pointer;
  }
}

.normal_pc_wrapper {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 0 12px;
}