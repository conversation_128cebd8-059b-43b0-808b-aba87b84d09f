import {gdpExplain} from "@/services/common/api";

export default {
  namespace: 'global',
  state: {
    gdpExplain: null, // gdp介绍文案
  },

  effects: {
    * gdpExplain({payload}: any, {put, call}: any) {
      const response = yield call(gdpExplain, payload);
      if (response.code === 200) {
        yield put({type: 'save', payload: {gdpExplain: response.content}});
      }
      return response
    }
  },

  reducers: {
    save(state: any, {payload}: any) {
      return {
        ...state,
        ...payload,
      }
    },
  },
};
