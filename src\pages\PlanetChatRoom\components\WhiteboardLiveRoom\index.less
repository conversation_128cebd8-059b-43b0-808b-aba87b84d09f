.sketch_wrap {
  width: 100%;
  height: 100%;
  background: #a6a6a6;
  position: relative;
}

.StreamAudioListWarp {
  width: 1px;
  height: 1px;
  position: absolute;
  top: -1px;
  left: -1px;
}

.initloadingWarp {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.Bottombar {
  // width: 100%;
  padding-top: 10PX;
  padding-bottom: 10PX;
  padding-left: 10px;
  padding-right: 10px;
  background: rgba(0,0,0,0.5);
  box-shadow: 0px 0px 10px 5px rgba(0, 0, 0, 0.1);
  border-radius: 3px 3px 3px 3px;
  display: flex;
  align-items: center;
  position: absolute;
  // bottom: 130px;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  white-space: nowrap;

  .Bottom_btn {
    width: 10px;
    height: 10px;
    font-size: 10px;
    margin-left: 8px;
    margin-right: 8px;
    line-height: 10px;
    cursor: pointer;
    user-select: none;
    color: #fff;
  }
  .fileList {
    width: 10px;
    height: 10px;
    font-size: 10px;
    line-height: 10px;
    cursor: pointer;
    user-select: none;
    color: #fff;
    margin-right: 4px;
  }
  .UnorderedListOutlinedWarp {
    display: flex;
    margin-left: 8px;
    margin-right: 8px;
    color: #fff;
    align-items: baseline;
    cursor: pointer;
    user-select: none;
  }

  .currentPageNum {
    font-weight: 400;
    font-size: 13px;
    color: #FFFFFF;
    text-align: center;
  }

  .line {
    width: 1px;
    height: 11px;
    background: rgba(255,255,255,0.3);
    border-radius: 0px 0px 0px 0px;
  }
}

.SharedCoursewareWarp {}

.SharedCourseware_Modal {
  .SharedCourseware {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-evenly;

    .coursewareItem {
      width: 98px;
      height: 105px;
      margin-right:8px;
      margin-left: 8px;
      margin-bottom: 8px;
      background: #fff;
      border-radius: 12px 12px 12px 12px;
      border: 1px dashed #EAEAEA;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      overflow: hidden;
    }

    .coursewareItem:hover {
      border: 1px dashed #0095FF;
      background: #F4FAFF;
    }
    .coursewareItem:active {
      border: 1px dashed #0095FF;
      background: #F4FAFF;
    }

    .coursewareItemActive {
      border: 1px dashed #0095FF;
      background: #F4FAFF;
    }

    .img_item {
      width: 45px;
      height: 45px;
      margin-bottom: 3px;
      user-select: none;
      .ppt {
        width: 45px;
        height: 45px;
        background: url('~@/assets/PlanetChatRoom/WhiteboardLiveRoom_ppt_icon.png');
        background-size: 45px 45px;
        display: inline-block;
      }
      .pdf {
        width: 45px;
        height: 45px;
        background: url('~@/assets/PlanetChatRoom/WhiteboardLiveRoom_PDF_icon.png');
        background-size: 45px 45px;
        display: inline-block;
      }
      .word {
        width: 45px;
        height: 45px;
        background: url('~@/assets/PlanetChatRoom/WhiteboardLiveRoom_word_icon.png');
        background-size: 45px 45px;
        display: inline-block;
      }
    }

    .img_title {
      width: 100%;
      height: 30px;
      font-weight: 400;
      font-size: 9px;
      color: #666666;
      text-align: center;
    }
  }
}
