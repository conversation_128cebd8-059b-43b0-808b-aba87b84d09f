import React, {useState, useEffect, useRef} from 'react';
import { history,connect } from 'umi';
import styles from "./index.less";
import CreationOrthodonticsSteps from './components/CreationOrthodonticsSteps';
import Step1 from './components/Step1';
import Step2 from './components/Step2';
import Step3 from "./components/Step3";
import Step4 from "./components/Step4";
import Step5 from "./components/Step5";
import PcHeader from "@/componentsByPc/PcHeader";
import { getArrailUrl } from '@/utils/utils'
import {Form,Spin} from "antd";
import {stringify} from "qs";
import TipsModal from '@/pages/ConsultationModule/StartConsultation/ComponentsPC/TipsModal' // 返回上一页提示弹窗

const CreationOrthodonticsPage: React.FC = (props) => {
  const [ form] = Form.useForm();
  const { CreationOrthodontics,loading,history } = props || {}
  const { location } = history || {}
  const { pathname,query } = location || {}
  const { medicalRecordJson,DictionaryData } = CreationOrthodontics || {}
  const [goBackTipsModalVisible, setGoBackTipsModalVisible] = useState(false) // 返回提示弹窗
  const {
    id:consultationId,       // :consultationId 8dad6e1a68714dc7b1f908902ad2d4f4
    orderCaseTemplate, // : 0, -- 订单病例模板 1通用病例 2正畸病例
    type,             // "type": 0, --会诊类型(1图文、2视频,3审核)
  } = DictionaryData || {}
  const {
    pageFrom, //  'ConsultationDetails'详情来的保持原逻辑
  } = query || {}
  const { dispatch } = props || {};

  // 是否嵌套在5i5ya的iframe中
  const isInIframe = (self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya')

  // 从地址栏url判定当前是第几步
  const isStepNum = (stepNum)=>{
    const { match } = props || {}
    const { path } = match || {}
    // 判定path是否包含"step"
    if(path && path.indexOf(`Step${stepNum}`) != -1 && !!medicalRecordJson){
      return true
    }else {
      return false
    }
  }

  // 从地址栏url获取当前是第几步
  const getStepNum = ()=>{
    const { match } = props || {}
    const { path } = match || {}
    // 判定path是否包含"step"
    if(path && path.indexOf(`Step`)!= -1){
      return parseInt(path.split('Step')[1])
    }else {
      return 1
    }
  }

  // 返回提示弹窗关闭
  const goBackTipsModalClose = () => {
    console.log('pageFrom13123 :: ',pageFrom);
    if (pageFrom == 'ConsultationDetails') {
      history.goBack()
      dispatch({ type:'CreationOrthodontics/clean' })
    }else {
      history.replace(`/ConsultationModule/StartConsultation/Step2?${
        stringify({
          consultationId,
        })
      }`)
      dispatch({ type:'CreationOrthodontics/clean' })
    }
    setGoBackTipsModalVisible(false)
    goBack()
  }

  /**
   * 获取字典数据
   * 参数名 类型 必填 说明  数据字典
   * consultationId [string] 订单ID
   * tenantId [string] 是 租户ID
   * customerId [long] 患者ID
   * type [string] type(0查全部、1基本信息、2检查及分析、3问题清单及诊断、4治疗方案、5影像资料)
   */
  const getDictionaryData= async ()=>{
    const { dispatch } = props || {};
    let {
      consultationId,
      tenantId,
      customerId,
      orthodonticConsultationId
    } = query || {}

    if (!medicalRecordJson || !!orthodonticConsultationId) {
      await dispatch({
        type: 'CreationOrthodontics/getDictionaryData',
        payload: {
          orthodonticConsultationId:orthodonticConsultationId,
          consultationId: consultationId,
          tenantId: tenantId,
          customerId: customerId,
          type: 0,
        }
      })
    }else {
      const { id:consultationIdByMedicalRecordJson } = medicalRecordJson || {}
      if(
        consultationIdByMedicalRecordJson != consultationId &&
        consultationIdByMedicalRecordJson != orthodonticConsultationId
      ){
        await dispatch({
          type: 'CreationOrthodontics/getDictionaryData',
          payload: {
            orthodonticConsultationId:orthodonticConsultationId,
            consultationId: consultationId,
            tenantId: tenantId,
            customerId: customerId,
            type: 0,
          }
        })
      }
    }
  }


  const createOrthodonticCaseInfo = async ()=>{
    const { dispatch } = props || {};
    const { tenantId, customerId} = query || {}
    let dataByGetDictionaryData = await dispatch({
      type: 'CreationOrthodontics/getDictionaryData',
      payload: {
        tenantId: tenantId,
        customerId: customerId,
        type: 1,
      }
    })
    let DataByConsultationId = await dispatch({
      type: 'CreationOrthodontics/createOrthodonticCaseInfo',
      payload: {
        processNode: 1,
        type: 1,
      }
    })
    const { code,  content } = DataByConsultationId || {}
    if(code == 200) {
      await dispatch({
        type: 'CreationOrthodontics/getDictionaryData',
        payload: {
          consultationId: content,
          tenantId: tenantId,
          customerId: customerId,
          type: 0,
        }
      })

      // 在5i5ya的iframe中
      if (isInIframe) {
        const postData = {
          dataType: 'pathname',       // 页面地址onchange事件
          pathnameByChild: '/CreationOrthodontics/Step1',  // 路由信息
          searchByChild: `?${stringify({ consultationId: content, customerId: customerId, tenantId: tenantId })}`,  // 路由信息
        }
        console.log('子级发送数据：', postData, getArrailUrl())
        window.parent.postMessage(postData, getArrailUrl())
        return
      }

      history.replace(`/CreationOrthodontics/Step1?${
        stringify({ consultationId: content, customerId: customerId, tenantId: tenantId })
      }`)
    }
  }

  useEffect(async () => {
    const {
      consultationId,
      tenantId,
      customerId,
      orthodonticConsultationId,
    } = query || {}
    if(!!consultationId || !!orthodonticConsultationId) {
      // 地址栏url有参数，直接获取数据
      await getDictionaryData()
    }else {
      // 地址栏url没有参数，拼接参数
      // 如果地址栏没有consultationId参数 先新建订单
      await createOrthodonticCaseInfo()
    }
  },[])

  const goBack = () => {
    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'goBack',       // 页面地址onchange事件
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
      return
    }


    let hisLenght = pathname.slice(-1);
    if (hisLenght > 1) {
      history.replace(`/CreationOrthodontics/Step${hisLenght - 1}?${stringify(query)}`)
    }else {
      // 第一步
      if(type != 3  && orderCaseTemplate == 2){
        /*
        if (pageFrom == 'ConsultationDetails') {
          history.goBack()
          dispatch({ type:'CreationOrthodontics/clean' })
        }else {
          history.replace(`/ConsultationModule/StartConsultation/Step2?${
            stringify({
              consultationId,
            })
          }`)
          dispatch({ type:'CreationOrthodontics/clean' })
        }
        */
        setGoBackTipsModalVisible(true);
      }else {
        if (pageFrom == 'ExternalPage') {
          dispatch({
            type: 'pcAccount/save',
            payload: {
              tabState: 6,
              subTabState: null,
            }
          })
          history.replace('/UserInfo')
          return;
        }else {
          history.goBack()
        }
        dispatch({ type:'CreationOrthodontics/clean' })
      }
      /*dispatch({
        type: 'pcAccount/save',
        payload: {
          tabState: 6,
          subTabState: null,
        }
      })
      history.replace('/UserInfo')*/
    }
  }

  return (
    <div className={styles.page_Warp}>
      {/* iframe中隐藏header */}
      {
        isInIframe ? null :
          <div className={styles.page_PcHeader}>
            <PcHeader />
          </div>
      }

      <div className={styles.page_Warp_flex}>
        <div className={styles.page_basic_content_Warp}>

          <div className={styles.nav_title}>
            <i onClick={goBack} className={styles.backspace}></i> 创建病例
          </div>

          <div className={styles.page_form_warp}>
            <div>
              <CreationOrthodonticsSteps
                // type={2}
                processNode={getStepNum()}
                isFinish={false}
                width={"100%"}
              />
            </div>
            <div className={styles.page_content_Warp}>
              <Spin spinning={!!loading.effects['CreationOrthodontics/getDictionaryData']}>
                <div className={styles.page_content}>
                  {isStepNum(1) && <Step1 goBack={goBack}/>}
                  {isStepNum(2) && <Step2/>}
                  {isStepNum(3) && <Step3/>}
                  {isStepNum(4) && <Step4/>}
                  {isStepNum(5) && <Step5/>}
                </div>
              </Spin>
            </div>
          </div>
        </div>
      </div>

      {/* 返回上一页提示弹窗 */}
      <TipsModal
        visible={goBackTipsModalVisible}
        title={'病例草稿已自动保存！您可在个人中心-我的病例内，'}
        text={'随时查阅或继续编辑'}
        rightBtnText={'我知道了'}
        onClickLeftBtn={goBackTipsModalClose}
        onClickRightBtn={goBackTipsModalClose}
      />

    </div>
  )
}

export default connect(({ CreationOrthodontics,pcAccount, loading }: any) => ({
  CreationOrthodontics,pcAccount, loading
}))(CreationOrthodonticsPage)
