import React, { useState,useEffect } from 'react';
import { history,connect } from 'umi';
import styles from "@/pages/PaymentByConsultation/MyConsultationDetails/index.less";
import {getOperatingEnv, goConsultationDetail, StatusRuleByConsultation,
} from "@/utils/utils";
import NavBar from "@/components/NavBar";
import Orderheader from "@/components/ModalByConsultationDetails/Orderheader";
import ExpertCard from "@/components/ModalByConsultationDetails/ExpertCard";
import {
  getConsultationOrderInfo,
  submitConsultationPictureOrderPay as submitConsultationPictureOrderPayAction,
  submitConsultationVideoOrderPay as submitConsultationVideoOrderPayAction,
} from "@/services/consultation/ConsultationList";
import {message} from "antd";

const MyConsultationDetails: React.FC = (props) => {
  const { dispatch } = props;
  let { match: { params: { id } } } = props
  const userInfoData = JSON.parse(localStorage.getItem('userInfo') || '{}');
  const { friUserId: userId } = userInfoData || {}  // 获取用户id
  const [ consultationOrderInfo,setConsultationOrderInfo ] =  useState(null);
  const userInfoStr = localStorage.getItem('userInfo')
  const userInfo = userInfoStr ? JSON.parse(userInfoStr) : {}
  const { friUserId:idByUserInfo, name} = userInfo || {}
  const {
    id:idByOrderInfo, // : "7c4df30622a0489ca978674374da4248",//指导订单ID
    orderNumber,      // : "2023100811415964635",//订单号
    expertsId,        // : 29,//指导医生ID
    expertsName,      // : null,
    status,           // : 1,//支付状态(0已取消、1未支付(待评估)、2去支付(已评估)、3已支付)
    type,             // : 2,//指导类型(1图文、2视频)
    duration,         // : null,//指导时长
    freeTimes,        // : null,//免费次数
    amount,           // : 0.10,//账单金额
    usableFreeTimes,  // : 1,//剩余免费次数
    unitPrice,        // : 1.00,//基础单价(图文是/次，视频是/30min)
    vipUnitPrice,     // : 1.00,//会员单价(图文是/次，视频是/30min)
    isFinish,         // : 0, 是否已完成订单
    h5BaseUserDto,    // : 专家信息
    createUserId,     // : 创建订单的用户id
  } = consultationOrderInfo || {};



  // 微信浏览器使用
  let OperatingEnv = getOperatingEnv();

  useEffect(() => {
    getConsultationOrderInfoByPage();
  },[])

  // 查询指导订单详情
  const getConsultationOrderInfoByPage = async () => {
    let getConsultationOrderInfoByData = await getConsultationOrderInfo({
      consultationId:id, // [string] 是 指导订单ID
      type:'2'  //  // [string] 是 (1:运营端订单详情, 2:H5/WEB端视频订单详情)
    })
    const {
      code,
      content,
    } = getConsultationOrderInfoByData || {};
    if(code == 200) {
      setConsultationOrderInfo(content);
    }else {
      setConsultationOrderInfo(null);
    }
  }

  // 支付完成,跳转到支付完成页面
  const goCompleteOrder = () => {
    // 支付完成从收银台跳转到支付完成页面
    history.replace(`/PaymentByConsultation/CompleteOrder/${id}`);
  }
  // 核销免费次数
  const writeOffFreeTimes = async () => {
    if(type == 1) {
      // 判定当前指导类型 图文指导
      let dataBySubmitConsultationPictureOrderPayAction = await submitConsultationPictureOrderPayAction({
        wxUserId: idByUserInfo,
        userName: name,
        id: idByOrderInfo,                 //          指导订单ID
        payMethod: 1,                  // [int]    是 图文 1提交指导 2立即支付
      })
      const { code, content,msg} = dataBySubmitConsultationPictureOrderPayAction || {}
      if (code == 200) {
        // 核销成功
        message.success('核销成功');
        // 支付完成,跳转到支付完成页面
        goCompleteOrder();
      }else {
        message.error(msg ? msg : '核销失败');
      }
    }else {
      // 视频指导结算
      let dataBySubmitConsultationVideoOrderPayAction = await submitConsultationVideoOrderPayAction({
        wxUserId: idByUserInfo,
        userName: name,
        id: idByOrderInfo,                 //          指导订单ID
        payMethod: 1,                  // [int]    是 图文 1提交指导 2立即支付
      })
      const { code, content, msg} = dataBySubmitConsultationVideoOrderPayAction || {}
      if (code == 200) {
        // 核销成功
        message.success('核销成功');
        // 支付完成,跳转到支付完成页面
        goCompleteOrder();
      }else {
        message.error(msg ? msg : '核销失败');
      }
    }
  }

  // 展示支付价格
  const showPaymentPrice = ()=>{
    // type == 1 ? vipUnitPrice || 0 :  amount || 0
    // 图文指导
    if(type == 1) {
      // 是否已支付
      if(status == 3) {
        // 已支付
        // 已支付是否使用免费次数
        if (freeTimes && freeTimes != 0) {
          // 图文指导-已支付-使用免费次数
          return amount;
        }else {
          // 图文指导-已支付-未使用免费次数
          return vipUnitPrice;
        }
      }else {
        // 图文指导-未支付
        return vipUnitPrice;
      }
    }else {
      // 视频指导
      return amount;
    }
  }

  return (
    <div className={styles.Mobile_Wrap}>
      {OperatingEnv != 1 &&  // 当前是小程序端
        <div className={styles.Mobile_title_statusbar}></div>
      }

      {OperatingEnv != 1 && OperatingEnv != 2 &&  // 当前是小程序端
        <div className={styles.Mobile_title_Wrap}>
          <NavBar title={'订单详情'}></NavBar>
        </div>
      }


      {/* 指导订单信息 */}
      <div className={styles.Orderheader_Warp}>
        {consultationOrderInfo &&
          <div className={styles.OrderheaderWarp}>
            <Orderheader
              consultationType={type}
              data={consultationOrderInfo}
            />
          </div>
        }
      </div>

      {/* 指导医生信息 */}
      <div>
        {h5BaseUserDto &&
          <div className={styles.ExpertCardWarp}>
            <ExpertCard
              expertData={h5BaseUserDto}
            />
          </div>
        }
      </div>


      <div className={styles.Mobile_info_content}>
        {[
          {
            title: type == 1 ?  `图文指导` : '视频指导',
            content:
              <div>
                <span>¥</span>
                <span>{vipUnitPrice ? vipUnitPrice : '0.00'}</span>
                <span>/次</span>
              </div>
          },
          {
            title: '状态',
            content: <div>
              <span onClick={()=>{
                goConsultationDetail(consultationOrderInfo)
              }} className={styles.blueText}>查看指导详情</span>
              <span> {StatusRuleByConsultation(consultationOrderInfo) ? StatusRuleByConsultation(consultationOrderInfo) : '-'} </span>
            </div>
          },
          {
            title: '指导时长',
            content: <div> <span>{ duration ? duration : 0 }</span><span>min</span> </div>
          },
          {
            title: '免费指导',
            content:  <div> <span>扣除 { freeTimes ? freeTimes : 0 } 次</span> <span>(30min/次)</span> </div>
          },
          {
            title: '免费指导剩余',
            content: <div> <span>{usableFreeTimes ? usableFreeTimes : 0}</span><span>次</span> </div>
          },
          {
            title: '现金支付',
            content: <div> <span>¥</span><span>{showPaymentPrice() ? showPaymentPrice() : '0'}</span> </div>
          },
        ].map((item,index)=>{
          const { title,content } = item || {};
          return (
            <div key={index} style={{
              marginBottom: index == 5 ? 0 : 12,
            }} className={styles.Mobile_info_content_item}>
              <div className={styles.Mobile_info_content_item_left}>{ title }</div>
              <div className={styles.Mobile_info_content_item_right}>{ content }</div>
            </div>
          )
        })}
      </div>

      {(status == 2 && createUserId == userId)  &&
        <div className={styles.Mobile_box_bottom_warp}>
          <div className={styles.Mobile_box}>
            <div className={styles.Mobile_text}>实付: </div>
            <div className={styles.Mobile_price}><span>¥</span><span style={{fontSize:24}}>{showPaymentPrice() ? showPaymentPrice() : '0'}</span></div>
          </div>
          <div>
            <div
              onClick={()=>{
                if((!amount || (amount && amount == 0)) && (freeTimes && freeTimes != 0)) {
                  // 使用了免费次数
                  writeOffFreeTimes()
                }else {
                  history.push({
                    pathname: `/PaymentByConsultation/ConsultationDetailsPayment/${id}`,
                  })
                }
              }}
              className={styles.Mobile_pay}> {showPaymentPrice() ? '确认支付' : '确认提交'}</div>
          </div>
        </div>
      }
    </div>
  )
}
export default connect(({ ConsultationList, loading }: any) => ({
  ConsultationList, loading
}))(MyConsultationDetails)
