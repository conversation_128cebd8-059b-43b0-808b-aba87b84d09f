/**
 * @Description: 首页搜索
 */
import React, { useState, useEffect } from 'react'
import classNames from 'classnames';
import { getOperatingEnv, backInApp, getIsFirstPageInApp } from '@/utils/utils'
import { page_search } from '../utils/index'
import { history, connect } from 'umi'
import styles from './index.less'

import SearchPage from '@/components/SearchPage'           // 搜索页面组件
import NavBar from '@/components/NavBar'                   // 导航栏组件

const Index: React.FC = (props: any) => {
  const { dispatch } = props
  const { pathname, query,search } = history.location

  const initialState = {
    searchKey: query.searchKey || '',                      // 搜索关键词
  }
  const [state, setState] = useState(initialState)
  const [customKeyWords, setCustomKeyWords] = useState([]) // 搜索热词
  const [searchKeys, setSearchKeys] = useState([])         // 搜索历史

  useEffect(() => {
    if (!(getIsFirstPageInApp()&&(getOperatingEnv() == 5))) {
      history.replace(pathname)
    }
    getWordList()
  }, [])

  // 根据用户ID获取搜索关键字
  const getWordList = () => {
    dispatch({
      type: 'activity/getWordList',
      payload: {}
    }).then(res => {
      const { code, content } = res
      if (code == 200) {
        setCustomKeyWords(content && content.customKeyWords || [])
        setSearchKeys(content && content.searchKeys || [])
      }
    })
  }

  // 输入事件
  const inputOnChange = (value) => {
    setState({
      ...state,
      searchKey: value,                                    // 搜索关键词
    })
  }

  // 搜索词点击事件，跳转搜索结果
  const keywordsOnClick = (value) => {
    if (getIsFirstPageInApp()&&(getOperatingEnv() == 5)) {
      history.replace({
        pathname: '/Home/SearchResult',
        query: {
          AIhelper:'Friday',
          searchKey: value,                                  // 搜索关键词
        }
      })
    }else{
      history.replace({
        pathname: '/Home/SearchResult',
        query: {
          searchKey: value,                                  // 搜索关键词
        }
      })
    }
    // 首页，搜索词搜索次数排行，记录搜索词
    page_search(value)
  }

  // 搜索回调，跳转搜索结果
  const onPressEnter = () => {
    if (getIsFirstPageInApp()&&(getOperatingEnv() == 5)) {
      history.replace({
        pathname: '/Home/SearchResult',
        query: {
          AIhelper:'Friday',
          searchKey: state.searchKey,                 // 搜索关键词
        }
      })
    }else{
      history.replace({
        pathname: '/Home/SearchResult',
        query: {
          searchKey: state.searchKey,                // 搜索关键词
        }
      })
    }
    // 首页，搜索词搜索次数排行，记录搜索词
    page_search(state.searchKey)
  }

  // 点击取消按钮事件
  const cancelBtnOnClick = () => {
    // APP环境中，如果当前页面是打开的第一个页面，正常返回会失效，此时调用app的返回
    if (getIsFirstPageInApp()) {
      backInApp()
      return
    }

    if (history.length > 1) {
      history.goBack()
    } else {
      history.replace('/')
    }
  }

  return (
    <>
      {/*<NavBar title="搜索"/>*/}
      <div className={classNames(styles.container, {
        [styles.container_pc]: getOperatingEnv() == 4
      })}>
        <SearchPage
          isHistoryStatus={1}
          inputPlaceholder={state.searchKey ? state.searchKey : '搜索直播、会议、王国、用户、病例'}
          isShowPopularSearch={true}
          historyData={searchKeys}
          popularData={customKeyWords}
          inputChangeFn={inputOnChange}
          historyClickFn={keywordsOnClick}
          popularDataClickFn={keywordsOnClick}
          onPressEnterFun={onPressEnter}
          cancelBtnFn={cancelBtnOnClick}
        />
      </div>
    </>
  )
}

export default connect(({ activity, loading }: any) => ({activity, loading}))(Index)
