/**
 * @Description: 发起指导，第4步提交
 */
import React, { useState, useEffect } from 'react'
import { history, connect } from 'umi'
import classNames from 'classnames'
import { Helmet } from 'react-helmet'
import { getOperatingEnv } from '@/utils/utils'
import {message, Spin} from 'antd'
import { Toast, Modal, Input, Radio } from 'antd-mobile'
import { CheckCircleOutlined } from '@ant-design/icons'
import styles from './Step4.less'

import NavBar from '@/components/NavBar'                                       // 导航栏组件
import ExpertCard from '../../H5Components/ExpertCard'                         // 医生卡片
import ConsultationCaseCard from '../../H5Components/ConsultationCaseCard'     // 病例卡片
import StartConsultationSteps from '@/pages/ConsultationModule/StartConsultation/ComponentsH5/StartConsultationSteps' // 查看完整服务流程按钮及弹窗
import SubmitSuccessModal from '@/pages/ConsultationModule/StartConsultation/ComponentsH5/SubmitSuccessModal' // 提交视频指导成功弹窗

const Index: React.FC = (props) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}') // 登录用户信息

  const { dispatch, loading } = props
  const { query, pathname } = history.location
  const {
    consultationType, // 指导类型，1 图文，2 视频
    consultationId, // 指导ID
  } = query
  const initialState = {}
  const initialFormState = {
    isUseFreeTimes: false,                                 // 是否使用免费次数
    agreementIsChecked: false,                             // 是否勾选协议
    firstQuestion: '', // 用户提问
    type: '', // 指导类型，1图文，2视频
  }
  const [state, setState] = useState(initialState)
  const [formState, setFormState] = useState(initialFormState)
  const [submitSuccessModalVisible, setSubmitSuccessModalVisible] = useState(false)

  useEffect(() => {
    if (consultationId) {
      getConsultationAndCaseInfo()
    } else {
      message.error('指导ID缺失')
    }
  }, [])

  // 查询指导和病例详情
  const getConsultationAndCaseInfo = () => {
    dispatch({
      type: 'consultation/getConsultationAndCaseInfo',
      payload: {
        consultationId: consultationId,                    // 指导ID
        type: 1,                                           // (1:图文支付/视频提交, 2:其它通用详情)
      }
    }).then(res => {
      const { code, content, msg } = res
      // 除专家和用户之外的第三人查看，提示
      if (code == 422) {
        Modal.alert({
          content: msg,
          onConfirm: () => {
            goBack()
          }
        })
        return
      }
      // 专家也不能看
      if (content && content.createUserId != UserInfo.friUserId) {
        Modal.alert({
          content: '对不起，您无权限查看该数据！',
          onConfirm: () => {
            goBack()
          }
        })
        return
      }
      // 流程节点
      // 图文[1选择指导方式、2描述病例问题、3支付指导费用、4病例资料被查看、5问题被回复并对话、6结束指导交易成功];
      // 视频[1选择指导方式、2描述病例问题、3提交指导单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、7结束指导、8确认并支付指导费用、9交易成功]
      if (content && content.processNode > 3) {
        Modal.alert({
          content: '链接已失效，可能因为指导订单已经提交成功，请前往我的指导查看订单',
          onConfirm: () => {
            goBack()
          }
        })
        return
      }
      if (code == 200 && content) {
        // 更新一下地址
        history.replace({
          pathname,
          query: {
            ...query,
            expertsUserId: content.expertsId, // 专家ID
            consultationType: content.type, // 指导类型，1图文，2视频
            orderCaseTemplate: content.orderCaseTemplate, // 1通用模版，2正畸模版
          }
        })
        setState(content)
        setFormState({
          ...formState,
          isUseFreeTimes: content.usableFreeTimes > 0,       // 是否使用免费次数
          firstQuestion: content.consultationCaseInfoDto && content.consultationCaseInfoDto.firstQuestion || '', // 用户提问
          type: content.type, // 指导类型，1图文，2视频
        })
      } else {
        Toast.show(msg || '查询指导和病例详情失败')
      }
    }).catch(err => {})
  }

  // 免费指导次数勾选框修改
  const selectFreeTimes = (isChecked) => {
    // 没有可用次数时禁选
    if (!state.usableFreeTimes || state.usableFreeTimes == 0) {
      return
    }
    // 视频指导，选中之后不能取消
    if (formState.type == 2 && !isChecked) {
      return
    }
    setFormState({
      ...formState,
      isUseFreeTimes: isChecked, // 是否使用免费次数
    })
  }

  // 协议勾选框修改
  const agreementOnChange = (isChecked) => {
    setFormState({
      ...formState,
      agreementIsChecked: isChecked,
    })
  }

  // 输入初始提问
  const onChangeFirstQuestion = (value) => {
    setFormState({
      ...formState,
      firstQuestion: value,
    })
  }

  // 修改指导类型
  const onChangeType = (value) => {
    setFormState({
      ...formState,
      type: value,
      isUseFreeTimes: value == 2 && state.usableFreeTimes > 0 ? true : formState.isUseFreeTimes, // 是否使用免费次数
    })
  }

  // 病例卡片点击编辑，返回上一步
  const onClickEditBtn = () => {
    if (state.orderCaseTemplate == 1) {
      history.replace({
        pathname: '/ConsultationModule/StartConsultation/Step3',
        query: {
          ...query,
        }
      })
    } else {
      history.replace(`/CreationOrthodontics/Step1?orthodonticConsultationId=${consultationId}`)
    }
  }

  // 修改会诊用户提问且同步病历问题
  const updateCaseInfoQuestion = () => {
    return dispatch({
      type: 'consultation/updateCaseInfoQuestion',
      payload: {
        userName: UserInfo.name, // 用户名
        wxUserId: UserInfo?.friUserId, // 用户ID
        id: consultationId, // 指导ID
        firstQuestion: formState.firstQuestion.trim(), // 用户提问
        type: formState.type, // 指导类型，1图文，2视频
      }
    }).then(res => {
      return res
    }).catch(err => {
      return null
    })
  }

  // 提交
  const submit = async () => {
    if (!(formState.firstQuestion && formState.firstQuestion.trim())) {
      message.error('请输入提问问题')
      return
    }
    if (!formState.agreementIsChecked) {
      Toast.show('请阅读并勾选FRIDAY服务协议')
      return
    }
    const res = await updateCaseInfoQuestion()
    const { code, content, msg } = res || {}
    if (code == 200 && content) {

    } else {
      message.error(msg || '数据加载失败')
      return
    }

    // 图文
    if (formState.type == 1) {
      if (formState.isUseFreeTimes) {
        submitConsultationPictureOrderPay()
      } else {
        // 跳转支付页
        history.replace(`/PaymentByConsultation/ConsultationDetailsPayment/${consultationId}`)
      }
    } else {
      // 视频
      editConsultationNodeAndStatus()
    }
  }

  // 图文指导
  const submitConsultationPictureOrderPay = () => {
    Toast.show({
      icon: 'loading',
      content: '',
      duration: 0,
    })
    dispatch({
      type: 'consultation/submitConsultationPictureOrderPay',
      payload: {
        id: consultationId,                                // 订单id
        amount: 0,                                         // 订单金额
        freeTimes: 1,                                      // 免费次数
        payMethod: 1,                                      // 图文 1提交指导 2立即支付
        operationPlatform: getOperatingEnv() == 4 ? 2 : 1, // 操作平台 1H5 2PC
        // payType: null,                                  // 1微信、2支付宝
      }
    }).then(res => {
      Toast.clear()
      const { code, content, msg } = res
      if (code == 200) {
        history.replace({
          pathname: '/ConsultationModule/ConsultationDetails',
          query: {
            consultationId,                                // 指导ID
            consultationType: formState.type, // 指导类型，1 图文，2 视频
          }
        })
      } else {
        Toast.show(msg || '提交失败')
      }
    }).catch(err => {})
  }

  // 视频指导
  const editConsultationNodeAndStatus = () => {
    Toast.show({
      icon: 'loading',
      content: '',
      duration: 0,
    })
    dispatch({
      type: 'consultation/editConsultationNodeAndStatus',
      payload: {
        consultationId: consultationId,       // 指导ID
        type: 1,   // 1:先体验后付费, 2:图文或视频病例被查看, 3:图文问题被回复并对话, 4:图文结束指导交易成功, 5:视频预约视频会议, 6:视频沟通, 7:结束指导
      }
    }).then(res => {
      Toast.clear()
      const { code, content, msg } = res
      if (code == 200) {
        setSubmitSuccessModalVisible(true)
      } else {
        Toast.show(msg || '提交失败')
      }
    }).catch(err => {})
  }

  // 提交成功弹窗关闭
  const submitSuccessModalClose = () => {
    setSubmitSuccessModalVisible(false)
    history.replace({
      pathname: '/ConsultationModule/ConsultationDetails',
      query: {
        consultationId,                                // 指导ID
        consultationType: formState.type, // 指导类型，1 图文，2 视频
      }
    })
  }

  // 服务协议
  const goToAgreement = () => {
    history.push('/UserInfo/ConsultationServiceAgreement')
  }

  // 返回
  const goBack = () => {
    if (history.length > 1) {
      history.goBack()
    } else {
      history.replace('/')
    }
  }

  // loading
  const getConsultationAndCaseInfoLoading = !!loading.effects['consultation/getConsultationAndCaseInfo']

  return (
    <Spin spinning={getConsultationAndCaseInfoLoading}>
      <Helmet>
        <title>向{`${state.h5BaseUserDto && state.h5BaseUserDto.name || ''}`}专家提问</title>
        <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, shrink-to-fit=no" />
      </Helmet>
      <NavBar title={`向${state.h5BaseUserDto && state.h5BaseUserDto.name || ''}专家提问`} style={{background: 'linear-gradient(116deg, #FFF 0%, #E4EEFC 100%)'}}/>
      <div className={styles.container}>
        <div className={styles.header}>专家查看病例后，将会在24小时内回复，您也可以向专家发起提问</div>
        {/* 查看完整服务流程按钮及弹窗 */}
        <div className={styles.complete_process_wrap}>
          <StartConsultationSteps/>
        </div>
        {/* 专家卡片 */}
        <ExpertCard data={{
          ...state,
          thisUserIsExperts: 0,
        }}/>
        <div className={styles.gray_bar}></div>

        {/* 病例卡片 */}
        <ConsultationCaseCard
          caseData={state.consultationCaseInfoDto || {}}
          consultationId={consultationId}
          isShowBtn={state.createUserId == UserInfo.friUserId}
          onClickEditBtn={onClickEditBtn}
          orderCaseTemplate={state.orderCaseTemplate}
        />
        <div className={styles.gray_bar}></div>

        {/* 提问 */}
        <div className={styles.question_box}>
          <div className={styles.question_label}>提问</div>
          <div className={styles.question_value}>
            <Input
              placeholder="请输入提问"
              value={formState.firstQuestion}
              onChange={onChangeFirstQuestion}
              maxLength={200}
            />
          </div>
        </div>

        {/* 指导方式 */}
        <div className={styles.details_box1}>
          <div className={styles.label}>指导方式</div>
          <div>
            <Radio.Group value={formState.type} onChange={onChangeType}>
              <Radio value={1}>图文指导</Radio>
              <Radio value={2}>视频指导</Radio>
            </Radio.Group>
          </div>
        </div>
        {
          state.usableFreeTimes > 0 &&
          <div className={styles.details_box2}>
            <div className={styles.label}>免费指导</div>
            <div className={styles.details_content}>
              <div className={styles.count}>
                剩余{state.usableFreeTimes}次可用
                {formState.type == 2 && '，视频指导每次30min'}
              </div>
              <div className={classNames(styles.checkbox, {
                [styles.checked]: formState.isUseFreeTimes,
              })} onClick={() => selectFreeTimes(!formState.isUseFreeTimes)}><CheckCircleOutlined/></div>
            </div>
          </div>
        }

        {
          state.createUserId == UserInfo.friUserId &&
          <>
            <div className={styles.agreement}>
              <div className={classNames(styles.agreement_checkbox, {
                [styles.checked]: formState.agreementIsChecked,
              })} onClick={() => agreementOnChange(!formState.agreementIsChecked)}><CheckCircleOutlined/></div>
              <div className={styles.agreement_text}>
                我已阅读并同意
                <span className={styles.highlight} onClick={goToAgreement}>《FRIDAY服务协议》</span>
              </div>
            </div>

            <div className={styles.fixed_box}>
              <div className={styles.pay_box}>
                {
                  formState.type == 1 ?
                    <div className={styles.pay_price}>
                      <div>合计:</div>
                      <div className={styles.unit}>¥</div>
                      <div className={styles.price}>{formState.isUseFreeTimes ? '0' : state.vipUnitPrice}</div>
                    </div>
                    :
                    <div className={styles.pay_text}>具体费用根据实际指导时长计算，将优先使用免费指导时长</div>
                }

                <div className={classNames(styles.pay_btn, {
                  [styles.disabled]: !formState.agreementIsChecked,
                })} onClick={submit}>{formState.type == 2 ? '先体验后付费' : '立即提交'}</div>
              </div>
            </div>
          </>
        }
      </div>

      {/* 视频指导提交成功弹窗 */}
      <SubmitSuccessModal visible={submitSuccessModalVisible} onCancel={submitSuccessModalClose}/>
    </Spin>
  )
}

export default connect(({ consultation, loading }: any) => ({ consultation, loading }))(Index)
