.warp_SpaceRoomReservationCard {

}

.Modal_SpaceRoomReservationCard {
  :global {
    .ant-modal {
      padding-bottom: 0;
      max-width: calc(100vw - 8px);
      margin: 0px auto;
    }

    .ant-modal-content {
      width: 100%;
      background: rgba(0,0,0,0.8);
      box-shadow: 0px 4px 20px 0px rgba(196,196,196,0.6);
      border-radius: 8px 8px 8px 8px;
    }

    .ant-modal-close {
      display: none;
    }

    .ant-modal-body {
      padding: 12px;
    }
  }
}

.modalContent {
  position: relative;

  .titleSpan {
    font-weight: 400;
    font-size: 12px;
    color: #FFFFFF;
    line-height: 12px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-bottom: 12px;
  }
}

.close {
  position: absolute;
  top: 0px;
  right: 0px;
  width: 20px;
  height: 20px;
  background: url("~@/assets/GlobalImg/white_close.png");
  background-size: 20px 20px;
}


.card_info {
  display: flex;
  .card_img {
    width: 129px;
    height: 73px;
    overflow: hidden;
    margin-right: 12px;
    background: #a6a6a6;
    border-radius: 4px 4px 4px 4px;
    img {
      width: 100%;
      height: 100%;
    }
  }

  .card_right {
    width: calc(100% - 129px - 12px);

    .card_title_warp {
      .card_title {
        font-weight: 600;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 14px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-bottom: 8px;
      }
      .card_time {
        font-weight: 400;
        font-size: 12px;
        color: #FFFFFF;
        line-height: 12px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }
}

.card_res_btn_warp {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 3px;

  .btn_Res {
    width: 88px;
    height: 32px;
    background: #0095FF;
    border-radius: 18px 18px 18px 18px;
    font-weight: 400;
    font-size: 14px;
    color: #FFFFFF;
    line-height: 32px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    // --disabled-color: ;
  }

  .btn_Res_disabled {
    width: 88px;
    height: 32px;
    background: #F5F5F5;
    border-radius: 18px 18px 18px 18px;
    font-weight: 400;
    font-size: 14px;
    color: #999999;
    line-height: 32px;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
}

.SpaceRoomReservationCard_btn_liveing {
  width: 12px;
  height: 12px;
  background: url("~@/assets/PlanetChatRoom/SpaceRoomReservationCard_btn_liveing_icon.png");
  background-size: 100% 100%;
  display: inline-block;
  margin-right: 5px;
}

.profileWarp {
  display: flex;
  justify-content: space-between;
  align-items: center;
 .profile_img {
   width: 23px;
   height: 23px;
   border-radius: 50%;
   overflow: hidden;
   background: #a6a6a6;
   margin-right: 8px;

   .img {
     width: 100%;
     height: 100%;
   }
 }
 .name {
   font-weight: 400;
   font-size: 13px;
   color: #FFFFFF;
   line-height: 15px;
   text-align: left;
   font-style: normal;
   text-transform: none;
 }
}



