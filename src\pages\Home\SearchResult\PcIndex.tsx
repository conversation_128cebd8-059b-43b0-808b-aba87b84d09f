/**
 * @Description: 首页搜索结果页
 */
import React, { useState, useEffect } from 'react'
import { history, connect } from 'umi'
import classNames from 'classnames'
import { useThrottle } from '@/utils/utils'
import { Spin, message, Input } from 'antd'
import styles from './PcIndex.less'

import NoDataImg from '@/assets/GlobalImg/no_data.png'

import PcHeader from '@/componentsByPc/PcHeader'           // header组件
import CaseCard from '@/componentsByPc/CaseCard'           // 病例组件
import ExpertCard from '@/componentsByPc/ExpertCard'       // 专家组件


const Index: React.FC = (props: any) => {
  const { dispatch, loading, activity } = props
  const { query, pathname } = history.location

  const initialState = {
    excellentCase: [],                                     // 病例集合
    expertsInfo: [],                                       // 专家集合

    excellentCaseCount: 0,                                 // 病例数量
    expertsCount: 0,                                       // 专家数量

  }

  const [state, setState] = useState(initialState)
  const [searchKeyState, setSearchKeyState] = useState(query.searchKey || activity.searchKey || '')  // 带过来的搜索关键词

  useEffect(() => {
    history.replace(pathname)
    webHomeIndexSearch()
  }, [])

  // 根据用户ID获取搜索关键字
  let webHomeIndexSearch = ({searchKey = searchKeyState} = {}) => {
    dispatch({
      type: 'activity/webHomeIndexSearch',
      payload: {
        searchKey,                                         // 搜索关键词
        // type: 0,                                        // 默认不传，专家 1  病历2
        pageNum: 1,                                        // 页码
        pageSize: 3,                                       // 每页条数
      }
    }).then(res => {
      const { code, content = {}, msg } = res
      if (code == 200) {
        const excellentCase = content.excellentCase && content.excellentCase.resultList || []
        const expertsInfo = content.expertsInfo && content.expertsInfo.resultList || []
        setState({
          ...state,

          excellentCase: excellentCase,                                // 病例集合
          expertsInfo: expertsInfo,                                    // 用户集合

          excellentCaseCount: content.excellentCaseCount || 0,         // 病例数量
          expertsCount: content.expertsCount || 0,                     // 用户数量
        })

        dispatch({
          type: 'activity/save',
          payload: {
            searchKey,                                                 // 搜索关键词
          }
        })
      } else if (msg) {
        message.error(msg)
      }
    })
  }

  webHomeIndexSearch = useThrottle(webHomeIndexSearch, 300)

  // 输入框输入事件
  const searchKeyOnChange = (e) => {
    setSearchKeyState(e.target.value)
  }

  // 按下回车建
  const searchKeyOnSearch = () => {
    webHomeIndexSearch({
      searchKey: searchKeyState,                           // 搜索关键词
    })
  }

  // 病例查看更多
  const caseLookMore = () => {
    history.push(`/Case/CaseResult?searchValue=${searchKeyState}`)
  }

  // 专家查看更多
  const userLookMore = () => {
    history.push(`/Expert/ExpertResult?searchValue=${searchKeyState}`)
  }

  //返回
  const goBack = () => {
    history.goBack()
  }

  // loading
  const loadingWebHomeIndexSearch = !!loading.effects['activity/webHomeIndexSearch']
  return (
    <Spin spinning={loadingWebHomeIndexSearch}>
      <div className={styles.container}>
        <PcHeader/>
        <div className={styles.content_box}>
          <div className={styles.content}>

            <div className={styles.header_box}>
              <i className={styles.back_icon} onClick={goBack}></i>
              <Input
                placeholder="搜索病例/专家"
                defaultValue={searchKeyState}
                suffix={<i className={styles.search_icon} onClick={searchKeyOnSearch}></i>}
                style={{width: 379, height: 36}}
                onChange={searchKeyOnChange}
                onPressEnter={searchKeyOnSearch}
              />
            </div>

            {/* 病例 */}
            <div className={styles.result_box}>
              <div className={styles.result_title}>
                <div className={styles.title_text}>病例({state.excellentCaseCount})</div>
                <div className={styles.title_btn_box} onClick={caseLookMore}>
                  <div className={styles.title_btn_text}>查看更多</div>
                  <i className={styles.title_btn_icon}></i>
                </div>
              </div>
              <div className={styles.result_content}>
                {
                  state.excellentCase.length == 0 ?
                    <div className={classNames(styles.result_item_empty, styles.result_item_empty_case)}>
                      <img src={NoDataImg} width={150} height={113} alt=""/>
                      <div className={styles.empty_title}>暂无该搜索结果</div>
                      <div className={styles.empty_text}>请试试其他搜索关键词</div>
                    </div>
                    :
                    state.excellentCase.map((item, index) => (
                      <div key={index} className={styles.result_item}>
                        <CaseCard caseData={item}/>
                      </div>
                    ))
                }
              </div>
            </div>

            {/* 专家 */}
            <div className={styles.result_box}>
              <div className={styles.result_title}>
                <div className={styles.title_text}>专家({state.expertsCount})</div>
                <div className={styles.title_btn_box} onClick={userLookMore}>
                  <div className={styles.title_btn_text}>查看更多</div>
                  <i className={styles.title_btn_icon}></i>
                </div>
              </div>
              <div className={styles.result_content}>
                {
                  state.expertsInfo.length == 0 ?
                    <div className={classNames(styles.result_item_empty, styles.result_item_empty_user)}>
                      <img src={NoDataImg} width={150} height={113} alt=""/>
                      <div className={styles.empty_title}>暂无该搜索结果</div>
                      <div className={styles.empty_text}>请试试其他搜索关键词</div>
                    </div>
                    :
                    state.expertsInfo.map((item, index) => (
                      <div key={index} className={styles.result_item}>
                        <ExpertCard item={item}/>
                      </div>
                    ))

                }
              </div>
            </div>

          </div>
        </div>
      </div>
    </Spin>
  )
}

export default connect(({ activity, loading }: any) => ({activity, loading}))(Index)
