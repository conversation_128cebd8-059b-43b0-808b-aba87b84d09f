import { emojiMap } from '@/emoticon/index';
import TIM from 'tim-js-sdk';
import dayjs from 'dayjs';
const emojiUrl = 'https://static.jwsmed.com/public/3M/SmallerProject/assets/';   // 图片地址
// 解析text, 表情信息也是[嘻嘻]文本
export function parseText(message:string,imgSize = 28) {
  // 构建正则表达式
  let regexpStr = Object.keys(emojiMap).map(item => item.replace(/[\[\]]/g, '\\$&')).join('|');
  let regexp = new RegExp(regexpStr, 'g');

  // 替换字符串
  let result = message.replace(regexp, match => {
    return `<img width="${imgSize}" height="${imgSize}" style="vertical-align: middle" src="${emojiUrl}${emojiMap[match]}">`;
  });
  return result
};

// 当前人发送消息直接展示
export const localDealNotSendMessage = (type:number,data:any,callback:any,userInfoStr:any)=>{
  let {
    msgSeq,
    desc,
    thumbUrlShow,
    mediaDuration,
  } = data
  let obj  = {
    wxUserId:userInfoStr.friUserId,       // 用户ID
    id:userInfoStr.friUserId,
    name:userInfoStr.name,           // 用户名
    headUrlShow:userInfoStr.headUrl,    // 用户头像
    imUserId:null,       // 用户IM-id
    msgSeq,         // 消息序列号，用于标识唯一消息，值越小发送的越早
    msgDataTime:dayjs().format('YYYY-MM-DD HH:mm:ss'),    // 消息时间
    msgType:type,        // 消息类型 1文本 2图片 3语音 4视频 5自定义
    msgContent:desc,     // 消息内容/图片地址/语音地址/视频地址/
    mediaDuration:mediaDuration || null,  // 媒体文件时长，语音有值
    fileSize:null,       // 数据大小
    thumbUrlShow:thumbUrlShow || null,   // 缩略图url地址，用于展示图片和视频的缩略图片
    islocal:1,
    randomNumber:userInfoStr.randomNumber
  }
  callback && callback([obj],1,2)
}

/**
 * 调用IM发送消息方法
 * @param {*} type      内容类型  1、文本 2、图片 3、语音 4、视频
 * @param {*} groupId   消息接收方的 userID 或 groupID
 * @param {*} data      文本消息内容
 * @param {*} callback  自定义回调方法（刷新页面数据）
 * @param {*} timObj    IM对象
 * @param {*} userInfo  发送消息的用户信息
 */
export const onSendMessage = (type:number,groupId:string,data:any,callback:any,timObj:any,userInfo:any,uploadSchedule?:any) => {

  let message =null;
  let newUserInfo = {
    ...userInfo,
    wxUserId:userInfo.friUserId
  }
  let userInfoStr = userInfo?JSON.stringify(newUserInfo):null
  if(type == 1){
    message = timObj.createTextMessage({
      to: groupId,
      conversationType: TIM.TYPES.CONV_GROUP,   // 会话类型【取值TIM.TYPES.CONV_C2C（端到端会话）或TIM.TYPES.CONV_GROUP（群组会话）】
      payload: {text:data},                              // 消息内容
      cloudCustomData:userInfoStr,
      // v2.20.0起支持C2C消息已读回执功能，如果您发消息需要已读回执，需购买旗舰版套餐，并且创建消息时将 needReadReceipt 设置为 true
      needReadReceipt: true,
    });
  }else if(type == 2){
    message = timObj.createImageMessage({
      to: groupId,
      conversationType: TIM.TYPES.CONV_GROUP,   // 会话类型【取值TIM.TYPES.CONV_C2C（端到端会话）或TIM.TYPES.CONV_GROUP（群组会话）】
      payload: { file: data },       // 消息内容
      cloudCustomData:userInfoStr,
      onProgress: function(event:object) {
        let randomNumber = userInfo && userInfo.randomNumber
        let obj = {randomNumber,schedule:event}
        uploadSchedule(obj)
      }, // 获取上传进度的回调函数
      needReadReceipt: true
    });
  }else if(type == 3){
    message = timObj.createAudioMessage({
      to: groupId,
      conversationType: TIM.TYPES.CONV_GROUP,   // 会话类型【取值TIM.TYPES.CONV_C2C（端到端会话）或TIM.TYPES.CONV_GROUP（群组会话）】
      payload: { file: data },       // 消息内容
      cloudCustomData:userInfoStr,
      // onProgress: function(event:object) { console.log('file uploading:', event)}, // 获取上传进度的回调函数
      needReadReceipt: true,
    });
  }else if(type == 4){
    message = timObj.createVideoMessage({
      to: groupId,
      conversationType: TIM.TYPES.CONV_GROUP,   // 会话类型【取值TIM.TYPES.CONV_C2C（端到端会话）或TIM.TYPES.CONV_GROUP（群组会话）】
      payload: { file: data },       // 消息内容
      cloudCustomData:userInfoStr,
      onProgress: function(event:object) {
        let randomNumber = userInfo && userInfo.randomNumber
        let obj = {randomNumber,schedule:event}
        uploadSchedule(obj)
      }, // 获取上传进度的回调函数

      needReadReceipt: true
    });
  }
  // 2. 发送消息
  let promise = timObj.sendMessage(message);
  promise.then((imResponse:any) => {
    // 发送成功
    let { data } = imResponse || {}
    let { message } = data || {}
    // console.log(imResponse, "发送成功");
    // 发送成功之后刷新数据
    let arr = [message]
    callback && callback(arr,3,1)
  }).catch(function (imError:string) {
    // 发送失败
    console.warn('sendMessage error:', imError);
  });
}

// 处理聊天页面昵称、头像展示
export const getUserData = (groupMemberDtoList:any, id:number)=> {
  return groupMemberDtoList.find((user:any) => user.imUserId === id);
}

/**
 * 处理IM返回数据
 * @param list
 */
export const ProcessingImData= (list:any) => {
  let arr: {
    wxUserId: any; // 用户ID
    name: any; // 用户名
    headUrlShow: any; // 用户头像
    imUserId: null; // 用户IM-id
    msgSeq: any; // 消息序列号，用于标识唯一消息，值越小发送的越早
    msgDataTime: string; // 消息时间
    msgType: number; // 消息类型 1文本 2图片 3语音 4视频 5自定义
    msgContent: any; // 消息内容/图片地址/语音地址/视频地址/
    mediaDuration: null; // 媒体文件时长，语音有值
    fileSize: null; // 数据大小
    thumbUrlShow: null;
    randomNumber:number,
  }[] = []
  if(Array.isArray(list) && list.length==0){
    return
  }
  list.forEach((item: { cloudCustomData: string; type: string; sequence: any; time: number ; payload:any; })=>{
    const userInfo = item.cloudCustomData ? JSON.parse(item.cloudCustomData) : {}
    let obj  = {
      wxUserId:userInfo.friUserId,       // 用户ID
      name:userInfo.name,           // 用户名
      headUrlShow:userInfo.headUrl,    // 用户头像
      imUserId:null,       // 用户IM-id
      msgSeq:item.sequence,         // 消息序列号，用于标识唯一消息，值越小发送的越早
      msgDataTime:dayjs.unix(item.time).format('YYYY-MM-DD HH:mm:ss'),    // 消息时间
      msgType:1,        // 消息类型 1文本 2图片 3语音 4视频 5自定义
      msgContent:item.payload.text,     // 消息内容/图片地址/语音地址/视频地址/
      mediaDuration:null,  // 媒体文件时长，语音有值
      fileSize:null,       // 数据大小
      thumbUrlShow:null,   // 缩略图url地址，用于展示图片和视频的缩略图片
      randomNumber:userInfo.randomNumber
    }
    if(item.type == TIM.TYPES.MSG_TEXT){
      arr.push({...obj,msgType:1,msgContent:item.payload.text})
    }else if(item.type == TIM.TYPES.MSG_IMAGE){
      arr.push({...obj,msgType:2,msgContent:item.payload.imageInfoArray[0].imageUrl,thumbUrlShow:item.payload.imageInfoArray[1].imageUrl})
    }else if(item.type == TIM.TYPES.MSG_SOUND){
      arr.push({...obj,msgType:3,msgContent:item.payload.remoteAudioUrl,mediaDuration:item.payload.second})
    }else if(item.type == TIM.TYPES.MSG_VIDEO){
      arr.push({...obj,msgType:4,msgContent:item.payload.remoteVideoUrl,thumbUrlShow:item.payload.thumbUrl,fileSize:item.payload.videoSize})
    }else if(item.type == TIM.TYPES.MSG_CUSTOM && item.payload.data== 'SYSTEM_MSG'){
      arr.push({...obj,msgType:5,msgContent:item.payload.description})
    }
  })

  return arr
}

export const uniqueArray=(arr)=> {
  return Array.from(new Set(arr.map(JSON.stringify))).map(JSON.parse);
}


 // 获取file文件url
export const getFileURL = (file) => {
  var url = null;
  if (window.URL != undefined) { // mozilla(firefox)
    url = window.URL.createObjectURL(file);
  } else if (window.webkitURL != undefined) { // webkit or chrome
    url = window.webkitURL.createObjectURL(file);
  }
  return url;
}
// 截取视频第一帧，为base64
export const getVideoBase64 = (url) => {
  return new Promise(function (resolve) {
    let dataURL = "";
    const video = document.createElement("video");
    video.setAttribute("crossOrigin", "anonymous"); // 处理跨域
    video.setAttribute("src", url);
    video.setAttribute("preload", "auto");
    video.addEventListener("loadeddata", function () {
      const canvas = document.createElement("canvas");
      console.log("video.clientWidth", video.videoWidth); // 视频宽
      console.log("video.clientHeight", video.videoHeight); // 视频高
      const width = video.videoWidth || 400; // canvas的尺寸和图片一样
      const height = video.videoHeight || 240; // 设置默认宽高为  400  240
      canvas.width = width;
      canvas.height = height;
      canvas.getContext("2d").drawImage(video, 0, 0, width, height); // 绘制canvas
      dataURL = canvas.toDataURL("image/png"); // 转换为base64
      resolve(dataURL);
    })
  })
}


// 随机数
const getRandomInt = (min, max)=> {
  min = Math.ceil(min);
  max = Math.floor(max);
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 自定义数据（主要用于发送消息回调未成功时，本地使用随机数区分哪一条数据成功）
export const cloudCustomUserInfoData = ()=>{
  const randomNumber = getRandomInt(100000, 999999);
  let userInfoStr = localStorage.getItem('userInfo')
  let obj = userInfoStr?JSON.parse(userInfoStr):{}
  return {...obj,randomNumber}
}
