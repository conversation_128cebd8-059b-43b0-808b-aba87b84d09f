/**
 * @Description: 点赞卡片-组件
 */
import React, { useEffect, useRef, useState } from 'react'
import { history, connect } from 'umi'
import { randomColor , processNames } from '@/utils/utils'
import styles from './index.less'
import ArticleCard from '../ArticleCard';
import Avatar from "@/components/Avatar";
import classNames from "classnames";
import {stringify} from "qs"; // 文章组件

interface PropsType {
  item? :any;     // 点赞的内容
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  // const [type, setType] = useState(3); // 1 帖子 2 空间 3文章 卡片
  const { item } = props || {};
  const {
    createUserId,     //: [创建人id] : 60
    headUrlShow,      //: [用户头像] : null
    userName,         //: [用户名称] : "志君"
    operateDateDescs,
    forwardSquareRecommendDto, // 被点赞的图文信息
    isExperts,   // 是否是专家
  } = item || {}
  let {
    createUserId: createUserId2,
    userName: userName2,
    forwardDescribe,  //: [转发描述] : null
    isForward,        //: [是否转发：1.转发 0，非转发] : null
    forwardSquareRecommendDto: forwardSquareRecommendDto2, // 被转发图文信息
  } = forwardSquareRecommendDto || {};
  // forwardDto = forwardDto || {};
  // let forwardDtoObj =  forwardDto.isForward == 1 ? forwardDto.forwardSquareRecommendDto || {} : forwardDto
  const forwardDto = isForward == 1 ? forwardSquareRecommendDto2 : forwardSquareRecommendDto

  // 跳转到转发详情
  const jumpDetailsFn = (e) => {
    e.stopPropagation()
    e.preventDefault()
    if (e.target && e.target.dataset && e.target.dataset.type == 'user') {
      history.push(`/Expert/ExpertDetails?id=${e.target.dataset.id}`)
      return
    }
    // 话题，老版结构
    if (e.target && e.target.dataset && e.target.dataset.type == 'topic') {
      // 如果当前在话题主页，并且显示的就是点击的话题
      if (history.location.pathname == '/CreateGraphicsText/TopicHome' && e.target.dataset.id == history.location.query.topicId) {
        return
      }
      history.push(`/CreateGraphicsText/TopicHome?topicId=${e.target.dataset.id}`)
      return
    }
    // 话题，新版结构
    if (e.target && e.target.parentNode && e.target.parentNode.dataset && e.target.parentNode.dataset.type == 'topic') {
      // 如果当前在话题主页，并且显示的就是点击的话题
      if (history.location.pathname == '/CreateGraphicsText/TopicHome' && e.target.parentNode.dataset.id == history.location.query.topicId) {
        return
      }
      history.push(`/CreateGraphicsText/TopicHome?topicId=${e.target.parentNode.dataset.id}`)
      return
    }
    // 图文类型：1.文章 2.帖子 3.外链 4.空间
    // 帖子详情
    if (isForward == 1) {
      return history.push(`/CreateGraphicsText/ForwardDetails?${stringify({id:forwardSquareRecommendDto.id})}`);
    }
  }

  // 点击原始内容
  const onClickOriginContent = (e) => {
    e.stopPropagation()
    // 帖子
    if(forwardDto.imageType == 2) {
      return history.push(`/CreateGraphicsText/PostDetails?${stringify({id:forwardDto.id})}`);
    }
    // 空间卡片
    if(forwardDto.imageType == 4) {
      return history.push(`/CreateGraphicsText/SpaceDetails?${stringify({id:forwardDto.id})}`)
    }
    // 文章卡片
    if(forwardDto.imageType == 1) {
      return history.push(`/CreateGraphicsText/ArticleDetails?${stringify({id:forwardDto.id})}`);
    }
  }

  // 空间内容，点击用户
  const onClickSpaceCreateUser = (e) => {
    e.stopPropagation()
    e.preventDefault()
    history.push(`/Expert/ExpertDetails?id=${forwardDto.createUserId}`)
  }

  // 将用户插入到前面
  useEffect(() => {
    const dom = document.getElementById(`forward_title${item.id}`)
    if (dom && dom.childNodes && dom.childNodes[0]) {
      const spanNode = document.createElement('span')
      spanNode.classList.add('quill_user_format_wrap')
      spanNode.setAttribute('contenteditable', false);
      spanNode.setAttribute('data-id', createUserId2);
      spanNode.setAttribute('data-type', 'user');
      const textNode = document.createTextNode(`@${userName2}:`)
      spanNode.appendChild(textNode)
      dom.childNodes[0].insertBefore(spanNode, dom.childNodes[0].firstChild)
    }
  }, [])

  return (
    <div className={styles.content}>
      <div className={styles.forward_box}>
        <Avatar
          userInfo={{
            userId:createUserId,
            name:userName,
            headUrlShow:headUrlShow,
          }}
          size={16}
        ></Avatar>
        <span className={styles.forward_name}>{userName}</span>
        {isExperts == 1 &&
          <div className={styles.expertCertificationIconWarp}><i className={styles.expertCertificationIcon}></i></div>
        }
        {operateDateDescs && <span className={styles.operateDateDescs}>{operateDateDescs}·</span>}
        赞了

      </div>
      {/* [imageType图文类型：1.文章 2.帖子 3.外链 4.空间] */}
      {
        forwardSquareRecommendDto ?
          <div>
            {forwardDescribe &&
              <div className={styles.forward_title_warp} onClick={jumpDetailsFn}>
                <div className={styles.line}></div>
                <div id={`forward_title${item.id}`} className={styles.forward_title} dangerouslySetInnerHTML={{__html: forwardDescribe}}></div>
              </div>
            }
            {
              forwardDto ?
                <div className={styles.forward_card} onClick={onClickOriginContent}>
                  {
                    forwardDto.imageType == 2 &&
                    <>
                      {/* 帖子卡片 */}
                      <div className={styles.post_content}>
                        <div
                          className={styles.init_img}
                          style={
                            forwardDto.textImgList && forwardDto.textImgList[0] ?
                              {backgroundImage: `url(${forwardDto.textImgList[0].imageUrlShow})`}
                              : forwardDto.headUrlShow ?
                              {backgroundImage: `url(${forwardDto.headUrlShow})`}
                              : {background: randomColor(forwardDto.createUserId)}
                          }
                        >
                          {
                            forwardDto.textImgList && forwardDto.textImgList[0] || forwardDto.headUrlShow ? null
                              : processNames(forwardDto.userName)
                          }
                        </div>
                        <div
                          className={classNames('ql-editor', styles.text)}
                          dangerouslySetInnerHTML={{__html: forwardDto.imageTextContent}}
                        ></div>
                      </div>
                    </>
                  }
                  {
                    forwardDto.imageType == 4 &&  <>
                      {/* 空间卡片 */}
                      <div className={styles.space_forward_title}>
                        <span onClick={onClickSpaceCreateUser}>@{forwardDto.userName}</span>
                        <span>{forwardDto.starSpaceType == 2 ? '发布了一条会议，快来参与聊天吧~' : '发布了一场直播，快来参与聊天吧~'}</span>
                      </div>
                      <div className={styles.space_list_content}>
                        <div className={styles.space_img_box}>
                          {/* 无封面时，展示主持人默认头像 */}
                          {
                            Array.isArray(forwardDto.textImgList) && forwardDto.textImgList.length > 0 ?
                              <div className={styles.space_init_img}>
                                {/* 封面中的标题 */}
                                {forwardDto.isTemplateCover == 1 && <div className={styles.title_in_cover_image}>{forwardDto.imageTitle}</div>}
                                <img src={forwardDto.textImgList[0].imageUrlShow} />
                              </div> : <div
                              className={styles.space_init_img}
                              // style={item.hostImgUrlShow ? {backgroundImage: `url(${item.hostImgUrlShow})`} : {background: randomColor(item.wxUserId)}}
                              style={{background: randomColor(forwardDto.createUserId)}}
                            >{processNames(forwardDto.userName)}</div>
                          }

                        </div>
                        <div className={styles.space_info_box}>
                          <div className={styles.space_title}>{forwardDto.imageTitle}</div>
                          <div className={styles.space_introduce}>{forwardDto.imageTextContent}</div>
                        </div>
                      </div>
                    </>
                  }
                  {
                    forwardDto.imageType == 1 &&
                    <>
                      {/* 文章卡片 */}
                      <ArticleCard
                        style={{padding: 0, background: 'none'}}
                        item={forwardDto}
                      />
                    </>
                  }
                </div>
                :
                <div className={styles.forward_card}>
                  <div className={styles.forward_card_removeOriginalContent}>
                    原内容已下架
                  </div>
                </div>
            }

          </div>
          :
          <div className={styles.forward_card}>
            <div className={styles.forward_card_removeOriginalContent}>
              原内容已下架
            </div>
          </div>
      }
    </div>
  )
}

export default connect(({ activity, loading }: any) => ({activity, loading}))(Index)
