/**
 * @Description: PC-专家列表头部组件
 * @author: 赵斐
 */
import React, { useState } from 'react';
import { connect ,history} from 'umi';
import { Input, Select } from 'antd';
const { Search } = Input;
const { Option } = Select;
import styles from './index.less';
import screenIcon from '@/assets/Expert/screen_icon.png'   // 筛选图标
import pcGobackIcon from '@/assets/GlobalImg/pc_goback.png'
import searchIcon from '@/assets/GlobalImg/search.png'
const Index: React.FC = (props: any) => {
  const { data ,expertAdvice ,dispatch, refreshInterface  } = props;
  const [searchVal , setSearchVal] = useState(expertAdvice.searchValue)
  const {
    depSubject,      // 科室（学科）
    abilityLevel,    // 能力等级
    postTitle,       // 职称
    city,            // 城市
  } = data || {}

  const {
    checkCity,           // 城市
    checkDepSubject,  // 学科初始数据
    checkAbilityLevel,  // 能力等级初始数据
    checkPostTitle,  // 职级初始数据
  } = expertAdvice || {}
  /**
   * 专家指导筛选
   * @param {*} val   当前选中数据
   * @param {*} type  1 城市 2 学科 3 能力等级 4 职级
   */
  const onScreenFun = (val:any, type:number) => {

    let obj = {
      checkCity: type == 1 ? val == 0 ? null : val : checkCity,       // 选中城市
      checkDepSubject: type == 2 ? val != 0 ? val : null : checkDepSubject,  // 选中学科
      checkAbilityLevel: type == 3 ? val != 0 ? val : null : checkAbilityLevel,  // 选中能力等级
      checkPostTitle: type == 4 ? val != 0 ? val : null : checkPostTitle,  // 选中医生职称
    }
    dispatch({
      type: "expertAdvice/save",
      payload: {
        ...obj
      }
    })
    refreshInterface(1,searchVal,obj)
  }

  // 点击回车调用接口
  const onPressEnter = ()=>{
    dispatch({
      type: "expertAdvice/save",
      payload: {
        searchValue:searchVal
      }
    })
    refreshInterface(1,searchVal,expertAdvice)
  }

  // 搜索数据
  const onChangeFn = (value:string)=>{
    setSearchVal(value)
  }

  // 返回
  const goBack = ()=>{
    history.goBack()
  }

  /**
   * 筛选显示名称处理
   * @param data  筛选数据
   * @param code  选中code
   * @returns
   */
  const screenTitleDom = (data:any,code: string) => {
    if (Array.isArray(data) && data.length) {
      var result = data.find((item:any) => item.code == code);
      return result ? result.name : null;
    }
  }
  return (
    <div className={styles.header}>
      <div className={styles.header_title} onClick={()=>{goBack()}}><img className={styles.header_title_icon} src={pcGobackIcon} alt="" />专家指导</div>
      <div className={styles.header_right}>
        {
          Array.isArray(city) && city.length ? <div className={styles.header_screen_city}>
            <img className={styles.screen_icon} src={screenIcon} />
            {
              checkCity ? <span>{checkCity}</span> : <span>全国</span>
            }
            <Select
              value={checkCity}
              className={styles.select_city_style}
              style={{ width: 96, height: 20 }}
              onChange={(v) => { onScreenFun(v, 1) }}
            >
              {
                city.map((item, idx) => {
                  return <Option key={idx} value={item}>{item}</Option>
                })
              }
            </Select>
          </div> : null
        }

        {
          Array.isArray(depSubject) && depSubject.length ? <div className={styles.header_screen_subject}>
            <img className={styles.screen_icon} src={screenIcon} />
            {checkDepSubject ? <span>{screenTitleDom(depSubject,checkDepSubject)}</span> : <span>所属学科</span>}

            <Select
              value={checkDepSubject}
              className={styles.select_subject_style}
              style={{ width: 88, height: 20 }}
              onChange={(v) => { onScreenFun(v, 2) }}
            >
              {
                depSubject.map((item, idx) => {
                  return <Option key={idx} value={item.code}>{item.name}</Option>
                })
              }
            </Select>
          </div> : null
        }
        {
          Array.isArray(abilityLevel) && abilityLevel.length ? <div className={styles.header_screen_capacity}>
            <img className={styles.screen_icon} src={screenIcon} />
            {checkAbilityLevel ? <span>{screenTitleDom(abilityLevel,checkAbilityLevel)}</span> : <span>能力等级</span>}
            <Select
              value={checkAbilityLevel}
              className={styles.select_capacity_style}
              style={{ width: 88, height: 20 }}
              onChange={(v) => { onScreenFun(v, 3) }}
            >
              {
                abilityLevel.map((item, idx) => {
                  return <Option key={idx} value={item.code}>{item.name}</Option>
                })
              }
            </Select>
          </div> : null
        }
        {
          Array.isArray(postTitle) && postTitle.length ? <div className={styles.header_screen_title}>
            <img className={styles.screen_icon} src={screenIcon} />
            {checkPostTitle ? <span>{screenTitleDom(postTitle,checkPostTitle)}</span> : <span>医生职称</span>}
            <Select
              value={checkPostTitle}
              className={styles.select_title_style}
              style={{ width: 124, height: 20 }}
              onChange={(v) => { onScreenFun(v, 4) }}
            >
              {
                postTitle.map((item, idx) => {
                  return <Option key={idx} value={item.code}>{item.name}</Option>
                })
              }
            </Select>
          </div> : null
        }

        <div className={styles.header_search}>
          <Search
            placeholder={'请输入搜索内容'}
            onPressEnter={() => onPressEnter()}
            onChange={(e) => onChangeFn(e.target.value)}
            value={searchVal || ''}
            suffix={<span className={styles.header_search_icon} onClick={() => onPressEnter()}><img src={searchIcon} alt="" /></span>}
          />
        </div>
      </div>
    </div>
  )
}
export default connect(({ expertAdvice, loading }: any) => ({ expertAdvice, loading }))(Index)
