// 分享按钮
import React from 'react';
import styles from './index.less';  // 引入自定义样式

const ShareButton = ({ isNotLogin, setModalVisibleByUserTokenInvalid, shareOnClick }) => {
  const handleShareClick = () => {
    // [判定登录] 判定是否已登录,未登录则弹出登录弹窗
    if (isNotLogin) {
      setModalVisibleByUserTokenInvalid();
      return null;
    }

    // 处理分享逻辑
    shareOnClick();
  };

  return (
    <div onClick={handleShareClick} className={styles.HorizontalLiveRoom_Btn_Warp}>
      <i className={styles.title_Icon_Share_Icon}></i>
      <div className={styles.text}>分享</div>
    </div>
  );
};

export default ShareButton;
