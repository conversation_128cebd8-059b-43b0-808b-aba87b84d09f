import React, { useRef, useEffect, useCallback } from 'react';
import { parse, stringify } from 'querystring';
import { history } from 'umi';
import defaultHeadIcon from '@/assets/GlobalImg/default_head_picture.png';
import moment from 'moment/moment';
import CryptoJS from 'crypto-js';
import { emojiMap } from '@/emoticon/index';
import dayjs from 'dayjs';
import { message } from 'antd';
import { Toast } from 'antd-mobile';
import { PullStatus } from 'antd-mobile/es/components/pull-to-refresh';
export const getPageQuery = () => parse(window.location.href.split('?')[1]);
export const WxAppIdByMini = 'wx0070ba793e934dd6'; // 微信小程序的APPId

const WxAppIdByPublicAccountByPRO = 'wxe8fc94c9167c46f7'; // (生产环境)微信公众号的AppId
const WxAppIdByPublicAccountByNotPRO = 'wxe8fc94c9167c46f7'; // (非生产环境)微信公众号的AppId
export const WxAppIdByPublicAccount = !!location.host.includes('dhealth.friday.tech')
  ? WxAppIdByPublicAccountByPRO
  : WxAppIdByPublicAccountByNotPRO; // 微信公众号的AppId
// export const WxAppIdByPublicAccount = WxAppIdByPublicAccountByPRO; // 微信公众号的AppId
export const WxAppIdByPublicFridayAppAccount = 'wxc656ab055bc594bb'; // 微信开放平台FridayApp的AppId

export const emojiUrl = 'https://static.jwsmed.com/public/3M/SmallerProject/assets/'; // Im表情包图片地址

/*
      下载图片、pdf、excel文档方法
      dataFlow:         接口返回的blob流数据
      fileName：        文件夹名字
*/
export const getDownLoad = (dataFlow, fileName) => {
  const blob = new Blob([dataFlow]);
  if (window.navigator && window.navigator.msSaveOrOpenBlob) {
    // IE下载
    navigator.msSaveBlob(blob, fileName);
  } else {
    // 非IE下载
    const obj = document.createElement('a');
    const elink = obj;
    elink.download = fileName;
    elink.href = URL.createObjectURL(blob);
    elink.click();
    URL.revokeObjectURL(elink.href); // 释放URL 对象
    obj.remove();
  }
};

/*
 * 首尾去空格
 *
 * */
export function StrTrim(str) {
  str = str.replace(/^\s+/, '');
  for (var i = str.length - 1; i >= 0; i--) {
    if (/\S/.test(str.charAt(i))) {
      str = str.substring(0, i + 1);
      break;
    }
  }
  return str;
}

/**
 * 处理价格格式，保留两位小数，3位1逗
 * @param price
 * @returns {string}
 */
export function priceFormat(price) {
  let priceStr = parseFloat(price);
  if (isNaN(priceStr)) {
    return '0.00';
  }
  priceStr = price.toString();
  let index = priceStr.indexOf('.');
  if (index < 0) {
    index = priceStr.length;
    priceStr += '.';
  }
  if (priceStr.length > index + 2 && index > -1) {
    priceStr = priceStr.substr(0, index + 3);
  } else {
    while (priceStr.length <= index + 2) {
      priceStr += '0';
    }
  }
  const priceStrArr = priceStr
    .split('')
    .reverse()
    .join('')
    .replace(/(\d{3})(?=\d)/g, '$1,')
    .split('')
    .reverse();
  return priceStrArr.join('');
}

/*----------------加密方法-------------------*/
// pd=>AES=Fun
export function getAesString(data, key1, iv1) {
  let key = CryptoJS.enc.Utf8.parse(key1);
  let iv = CryptoJS.enc.Utf8.parse(iv1);
  let srcs = CryptoJS.enc.Utf8.parse(data);
  let encrypted = CryptoJS.AES.encrypt(srcs, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  return encrypted.toString();
}
// get pd=>AES=Fun
export function getAES(password, key, iv) {
  // 加密
  const encrypted = getAesString(password, key, iv); // 密文
  return encrypted;
}
/*----------------解密方法-------------------*/
// pd=>AES=Fun
/*export function getDAesString(encrypted, key1, iv1) {
  let key = CryptoJS.enc.Utf8.parse(key1);
  let iv = CryptoJS.enc.Utf8.parse(iv1);
  let encryptedHexStr = CryptoJS.enc.Hex.parse(encrypted);
  let srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr);
  let decrypted = CryptoJS.AES.decrypt(srcs, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  });
  return decrypted.toString(CryptoJS.enc.Utf8);
}*/

export function getDAesString(encrypted, key1, iv1) {
  let key = CryptoJS.enc.Utf8.parse(key1);
  let iv = CryptoJS.enc.Utf8.parse(iv1);
  let encryptedHexStr = CryptoJS.enc.Hex.parse(encrypted);
  let srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr);
  let decrypted = CryptoJS.AES.decrypt(srcs, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  return decrypted.toString(CryptoJS.enc.Utf8);
}

// check pd=>AES=Fun
export function getDAes(mpassword, key, iv) {
  // 解密
  const decryptedStr = getDAesString(mpassword, key, iv);
  return decryptedStr;
}

export function decrypt(word, paw, iv) {
  const key = CryptoJS.enc.Utf8.parse(paw);
  const decrypt = CryptoJS.AES.decrypt(word, key, {
    iv: iv,
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  });
  return CryptoJS.enc.Utf8.stringify(decrypt).toString();
}

// 手机号，4位1空格处理
export function phoneFormat(text) {
  if (text) {
    return text.toString().replace(/^(.{3})(.*)(.{4})$/, '$1 $2 $3');
  }
  return text;
}

// 获取当前操作环境 1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用   5 FRIDAY app中使用 6 Jarvis App内使用 7 企微浏览器使用
export function getOperatingEnv() {
  let ua = window.navigator.userAgent.toLowerCase();
  if (/fridayapp/.test(ua)) {
    // FRIDAY app中使用
    return '5';
  } else if (/jarvisapp/.test(ua)) {
    // Jarvis App内使用
    return '6';
  } else if (/miniprogram/.test(ua)) {
    // 微信小程序内使用
    return '1';
  } else if (/micromessenger/.test(ua)) {
    if (/wxwork/.test(ua)) {
      // 当前在企微浏览器使用
      return '7';
    } else {
      // 当前在微信浏览器中使用
      return '2';
    }
  } else if (/mobile/.test(ua)) {
    // 移动端浏览器使用
    return '3';
  } else {
    // PC浏览器使用
    return '4';
  }
}

// 是否是iphone手机中的微信或者企微浏览器（为了留出ios中底部手势线黑条的空间）
export function getIsIniPhoneAndWeixin() {
  const ua = window.navigator.userAgent.toLowerCase();
  if (/^(?=.*iphone)(?=.*micromessenger).*$/.test(ua)) {
    return true;
  }
  return false;
}

// 判断当前环境是否为iOS，如果检测到是iOS设备，则isIOS函数返回true，否则返回false
export function isIOS() {
  const ua = window.navigator.userAgent.toLowerCase();
  return /ipad|iphone|ipod/.test(ua);
}

// 判断当前环境是否为Android，是返回true，否则返回false
export function isAndroid() {
  const ua = window.navigator.userAgent.toLowerCase();
  return /android/.test(ua);
}

//防抖
export function useDebounce(fn, delay, dep = []) {
  const { current } = useRef({ fn, timer: null });
  useEffect(
    function () {
      current.fn = fn;
    },
    [fn],
  );

  return useCallback(function f(...args) {
    if (current.timer) {
      clearTimeout(current.timer);
    }
    current.timer = setTimeout(() => {
      current.fn.call(this, ...args);
    }, delay);
  }, dep);
}

// 节流
export function useThrottle(fn, delay, dep = []) {
  const { current } = useRef({ fn, timer: null });
  useEffect(
    function () {
      current.fn = fn;
    },
    [fn],
  );

  return useCallback(function f(...args) {
    if (!current.timer) {
      current.timer = setTimeout(() => {
        delete current.timer;
      }, delay);
      current.fn.call(this, ...args);
    }
  }, dep);
}

// 手机号，中间四位加密处理
export function phoneEncryption(val) {
  if (val) {
    return val.toString().replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2');
  }
  return val;
}

/*
 * 创建Danmu的实例dom
 * */
export function createDom(el = 'div', tpl = '', attrs = {}, cname = '', danmuType = 1) {
  let dom = document.createElement(el);
  dom.className = cname;
  dom.innerHTML = tpl;

  if (danmuType == 1) {
    // 主播
    dom.style.color = '#FFDE8D';
    dom.style.background = 'rgba(0, 11, 39,0.5)';
    dom.style.borderRadius = '24px';
    dom.style.opacity = '0.82';
    dom.style.fontSize = '14px';
    dom.style.paddingTop = '10px';
    dom.style.paddingBottom = '10px';
    dom.style.paddingLeft = '10px';
    dom.style.paddingRight = '10px';
  } else if (danmuType == 2) {
    // 嘉宾
    dom.style.color = '#FFFFFF';
    dom.style.background = 'rgba(116, 180, 225,0.5)';
    dom.style.borderRadius = '24px';
    dom.style.opacity = '0.82';
    dom.style.fontSize = '14px';
    dom.style.paddingTop = '10px';
    dom.style.paddingBottom = '10px';
    dom.style.paddingLeft = '10px';
    dom.style.paddingRight = '10px';
  } else if (danmuType == 3) {
    // 观众
    dom.style.color = '#FFFFFF';
    dom.style.fontSize = '14px';
  } else if (danmuType == 4) {
    // 观众自己
    // 用户自己发的弹幕需要与其他用户做区别,头像+姓名+内容,背景底色#0095FF,透明度50%
    dom.style.color = '#FFFFFF';
    dom.style.background = 'rgba(0,149,255,0.5)';
    dom.style.borderRadius = '24px';
    dom.style.opacity = '0.82';
    dom.style.fontSize = '14px';
    dom.style.paddingTop = '10px';
    dom.style.paddingBottom = '10px';
    dom.style.paddingLeft = '10px';
    dom.style.paddingRight = '10px';
  }

  Object.keys(attrs).forEach((item) => {
    let key = item;
    let value = attrs[item];
    if (el === 'video' || el === 'audio') {
      if (value) {
        dom.setAttribute(key, value);
      }
    } else {
      dom.setAttribute(key, value);
    }
  });
  return dom;
}

export function sendDanmuCreateItemDom({ text, userInfoByDanmu }) {
  const {
    id,
    name,
    currentUserType, // 当前用户类型 1:主播(可发言,有连麦列表权限,有开始直播,有结束直播) 2:嘉宾(可发言) 3:观众(无权限)
    headUrl,
    imagePhotoPathShow,
  } = userInfoByDanmu || {};
  const userInfoData = JSON.parse(localStorage.getItem('userInfo'));

  // 用户自己发的弹幕需要与其他用户做区别,头像+姓名+内容,背景底色#0095FF,透明度50%
  let headUrlByImg = imagePhotoPathShow || headUrl || defaultHeadIcon;
  let levelByfunc = currentUserType || 3;

  // 头像区域
  let domByheadUrlByImg = `
  ${
    !headUrlByImg
      ? `<img
              style="width: 23px;
                     height: 23px;
                     border-radius: 23px;
                     overflow: hidden;
                     "
               src="${headUrlByImg}"/>
        `
      : `
         <div
             style=" width: 23px;
                     height: 23px;
                     border-radius: 23px;
                     overflow: hidden;
                     background: #F9B4E3;
                     opacity: 1;
                     background: ${id ? randomColor(id) + '' : 'none'};
                     position: absolute;
                     top: 7px;
                     line-height: 23px;
                     "
         >
            <div
              style="
                  font-size: 14px;
                  font-weight: 500;
                  color: #FFFFFF;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  white-space: nowrap;
              "
            >
              ${processNames(name)}
            </div>
          </div>
        `
  }
  `;

  let elByDanmu = createDom(
    'xg-el',
    `<div  style="
                      text-shadow: 0px 1px 0px rgba(0,0,0,0.7);
                      "
        ><span>${text}</span></div>`,
    {},
    'xgplayer-el',
    3,
  );
  if (userInfoByDanmu.id == userInfoData.friUserId) {
    elByDanmu = createDom(
      'xg-el',
      `<div  style="
                      display: inline-block;
                      width: 23px;
                      height: 23px;
                      border-radius: 23px;
                      position: relative;
                      top: -2px;
                      margin-top: 10px;
                      "
        >
        ${domByheadUrlByImg}
        </div>
        <span style="fon-size:18px;">${name}：</span>
        <span>${text}</span>`,
      {},
      'xgplayer-el',
      4,
    );
  }

  // 关注弹幕
  if (levelByfunc == 2) {
    elByDanmu = createDom(
      'xg-el',
      `<div  style="
                      display: inline-block;
                      width: 23px;
                      height: 23px;
                      border-radius: 23px;
                      position: relative;
                      top: -2px;
                      margin-top: 10px;
                      "
        >
        ${domByheadUrlByImg}
        </div>
        <span style="font-size:14px;">${name}：</span>
        <span>${text}</span>`,
      {},
      'xgplayer-el',
      2,
    );
  } else if (levelByfunc == 1) {
    // 高级弹幕
    elByDanmu = createDom(
      'xg-el',
      `<div  style="display: inline-block;
                      width: 23px;
                      height: 23px;
                      border-radius: 23px;
                      position: relative;
                      top: -2px;
                      margin-top: 10px;
                      "
        >
        ${domByheadUrlByImg}
        </div>
        <span style="font-size:14px;">${name}：</span>
        <span>${text}</span>`,
      {},
      'xgplayer-el',
      1,
    );
  }

  return elByDanmu;
}

// 王国-gdp、国民数量、观看数量格式化
export function gdpFormat(text) {
  if (text && text > 10000) {
    return Math.floor(text / 1000) / 10 + 'w';
  }
  return text || 0;
}

// imResponseFormatLocalData
// im消息格式化
export function imResponseFormatLocalData(message) {
  let { payload, sequence, from } = message || {};
  let {
    data: dataByPayload,
    description: description,
    extension: extensionByPagload,
  } = payload || {};
  let extensionObj = extensionByPagload && JSON.parse(extensionByPagload);
  let {
    headUrl,
    name,
    id: idByExtensionObj,
    imagePhotoPathShow,
    msgGroupId: msgGroupIdByExtensionObj,
    msgGroupCount: msgGroupCountByExtensionObj,
    isExperts: isExpertsByExtensionObj, // 1 医生，0 不是医生
  } = extensionObj || {};

  let msgContent = {
    ...payload,
    headUrlShow: imagePhotoPathShow ? imagePhotoPathShow : headUrl,
    imUserId: from,
    // msgBody: JSON.stringify(message),
    msgGroupId: msgGroupIdByExtensionObj ? msgGroupIdByExtensionObj : null,
    msgGroupCount: msgGroupCountByExtensionObj ? msgGroupCountByExtensionObj : null,
    msgDataTime: moment().format('YYYY-MM-DD HH:mm:ss'),
    bs: description,
    msgSeq: sequence,
    msgType: dataByPayload,
    name: name,
    desc: '',
    wxUserId: idByExtensionObj,
    isExperts: isExpertsByExtensionObj,
  };
  return msgContent;
}

// 头像默认展示背景随机颜色
export function randomColor(userId) {
  const colorArr = ['#F9B4E3', '#EBE988', '#F0CC97', '#F5C1A5', '#A8C1DA', '#BFBCEF'];
  let index = 0;
  if (userId) {
    index = userId % 6;
  }
  return colorArr[index];
}

// 无头像时默认展示
export function processNames(name: string) {
  if (!name) return '';
  const isChinesePattern = /[\u4e00-\u9fa50-9]/;
  const isEnglishPattern = /[a-zA-Z]/;
  if (isChinesePattern.test(name)) {
    // 名字2位的，展示最后一位
    if (name.length <= 2) {
      return name.slice(-1);
    }
    // 名字大于2位的，展示最后两位
    return name.slice(-2);
  } else if (isEnglishPattern.test(name)) {
    const firstChar = name[0];
    return firstChar.toUpperCase();
  } else {
    return '';
  }
}

// 建立跨域通信
export function commCORS(domId, message) {
  const fridayUrl = getFridayURL();
  // console.info(message,"传过来的高度是多少？？？")
  let iframe;
  if (document.getElementById('commWithFriday')) {
    iframe = document.getElementById('commWithFriday');
    iframe.src = `${fridayUrl}/MemberBenefit/Middleware#${message}`;
  } else {
    iframe = document.createElement('iframe');
    iframe.src = `${fridayUrl}/MemberBenefit/Middleware#${message}`;
    iframe.id = 'commWithFriday';
    // iframe.name = message;
    iframe.style.display = 'none';
    document.getElementById(domId).appendChild(iframe);
  }
}

// 清除跨域通信
export function unCommCORS(domId, iframeId) {
  const iframe = document.getElementById(iframeId);
  document.getElementById(domId).removeChild(iframe);
}

// 设置获取官网域名地址
export function getFridayURL() {
  const isLocalhost = location.host.includes('8000');
  const dhealth =
    location.host.match(/-([^.]+)(\.+)/) == null
      ? '.friday.tech'
      : `${location.host.match(/-([^.]+)(\.+)/)[0]}friday.tech`;
  return isLocalhost ? `http://localhost:8080` : `https://www${dhealth}`;
}

export function formatTime(seconds) {
  const duration = moment.duration(seconds, 'seconds');
  const hours = String(Math.floor(duration.asHours())).padStart(2, '0');
  const minutes = String(duration.minutes()).padStart(2, '0');
  const secs = String(duration.seconds()).padStart(2, '0');
  const formattedTime = `${hours}:${minutes}:${secs}`;
  return formattedTime;
}

// 格式化时间
export const formatTimeBySeconds = (seconds) => {
  const duration = moment.duration(seconds, 'seconds');
  const hours = String(Math.floor(duration.asHours())).padStart(2, '0');
  const minutes = String(duration.minutes()).padStart(2, '0');
  const secs = String(duration.seconds()).padStart(2, '0');
  const formattedTime = `${hours}:${minutes}:${secs}`;
  return formattedTime;
};

// 获取url中的参数
export function extractQueryStringFromURL(url) {
  const parts = url.split('?');
  // 获取分隔后数组的第二个元素，即问号后的内容
  if (parts.length > 1) {
    return parts[1];
  } else {
    return null;
  }
}

// 获取微信分享url
export function getShareUrl(url) {
  let queryUrl = '';
  const baseUrl = url.split('?')[0];
  const queryUrlStr = url.split('?')[1];
  const UerInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');

  if (queryUrlStr) {
    queryUrl = stringify({
      ...parse(queryUrlStr),
      isShare: '1',
      shareUserId: UerInfo && UerInfo.friUserId,
    });
  } else {
    queryUrl = stringify({
      isShare: '1',
      shareUserId: UerInfo && UerInfo.friUserId,
    });
  }

  return baseUrl + '?' + queryUrl + '&random=' + Math.random().toString(36).substr(2, 8);
}

// 筛选年龄展示
export function screenData(s) {
  if (s != undefined && s != null) {
    let v = s.toString();
    if (v.includes('岁以上')) {
      let num = v.indexOf('上');
      return v.substr(0, num + 1);
    } else if (v.endsWith('岁')) {
      if (v.includes('岁以上')) {
        return v.substr(v.length - 1, 1);
      } else {
        return v;
      }
    } else if (v.endsWith('以上')) {
      return v;
    } else if (
      v == '儿童' ||
      v == '未成年' ||
      v == '未成年人' ||
      v == '成年' ||
      v == '成人' ||
      v == '成年人' ||
      v == '老年' ||
      v == '老人' ||
      v == '老年人' ||
      v == '不详' ||
      v == '未知'
    ) {
      return v;
    } else {
      return `${v}岁`;
    }
  }
}

// APP相关逻辑

// 判断是否是app环境并且当前页面是打开的第一个页面（正常返回会失效，此时需要调用app的返回）
export function getIsFirstPageInApp() {
  const urlSearch = history.location.search;
  // APP环境中，如果当前页面是打开的第一个页面，正常返回会失效，此时调用app的返回
  if (urlSearch && (urlSearch.indexOf('AIhelper') > -1 || urlSearch.indexOf('MyAccount') > -1)) {
    return true;
  }
  return false;
}

// app，返回
export function backInApp() {
  if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
    window.flutter_inappwebview.callHandler('back', ...['返回']);
  }
}

// app，关闭窗口
export function closeInApp() {
  if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
    window.flutter_inappwebview.callHandler('close', ...['关闭']);
  }
}

// app，打开登录页
export function openLoginInApp() {
  if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
    window.flutter_inappwebview.callHandler('openLogin', ...['跳转登录']);
  }
}

// app requestCamera  申请相机摄像头权限请求相机权限
export function requestCameraInApp() {
  if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
    return window.flutter_inappwebview.callHandler('requestCamera', ...['申请相机权限']);
  } else {
    return false;
  }
}

// app 申请麦克风权限
export function requestMicrophoneInApp() {
  if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
    return window.flutter_inappwebview.callHandler('requestMicrophone', ...['申请麦克风权限']);
  } else {
    return false;
  }
}

// app，保存图片
export function saveImageInApp(imgUrl) {
  if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
    return window.flutter_inappwebview.callHandler('saveImage', ...[imgUrl]);
  }
}

// app，分享到微信
export function shareWeChatInApp(obj) {
  if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
    return window.flutter_inappwebview.callHandler('shareWeChat', ...[obj]);
  }
}

// app，系统分享
export function shareInApp(obj) {
  if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
    return window.flutter_inappwebview.callHandler('share', ...[obj]);
  }
}

// 指导列表指导状态展示规则
export function StatusRuleByConsultation(params) {
  const {
    id, // : "a2c99d2774a54ddf900bfcd23b2be533",//指导订单ID
    type, // : 1,//指导类型(1图文、2视频)
    processNode, // : 2,//流程节点(图文流程节点[1选择指导方式、2描述病例问题、3支付指导费用、4病例资料被查看、5问题被回复并对话、6结束指导交易成功];  视频流程节点[1选择指导方式、2描述病例问题、3提交指导单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、  7结束指导、8确认并支付指导费用、9交易成功])
    status, // : 支付状态(0已取消、1未支付(待评估)、2去支付(已评估)、3已支付)
    isFinish, // 是否以完成状态展示
  } = params || {};

  if (status == 0) {
    return '已取消';
  } // 支付状态(0已取消、1未支付(待评估)、2去支付(已评估)、3已支付)
  if (type == 1) {
    // 指导类型:1 图文
    if (processNode == 3) {
      return '待支付';
    }
    if (processNode == 4) {
      return '待查看';
    }
    if (processNode == 5) {
      return '待回复';
    }
    if (processNode == 6 && isFinish == 0) {
      return '已回复';
    }
    if (processNode == 6 && isFinish == 1) {
      return '已结束';
    }
  } else if (type == 2) {
    // 指导类型:2 视频
    if (processNode == 4) {
      return '待查看';
    }
    if (processNode == 5) {
      return '待预约';
    }
    if (processNode == 6) {
      return '待沟通';
    }
    if (processNode == 7) {
      return '待结束';
    }
    if (processNode == 8) {
      return '待评估';
    }
    if (processNode == 9 && isFinish == 0) {
      return '待支付';
    }
    if (processNode == 9 && isFinish == 1) {
      return '已支付';
    }
  } else if (type == 3) {
    if (processNode == 6) {
      return '待查看';
    } else if (processNode == 7) {
      return '病例被查看';
    } else if (processNode == 8) {
      return '审核驳回';
    } else if (processNode == 9) {
      return '审核通过';
    } else if (processNode == 10) {
      return '指导结束';
    } else {
      return '草稿';
    }
  }
  return null;
}

// 前往指导详情或创建指导页
export function goConsultationDetail(params) {
  const isInIframe = self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya'; // 是否嵌套在5i5ya的iframe中
  const env = getOperatingEnv(); // 4 PC

  const {
    isReplace, // : 是否使用replace跳转
    id, // : "a2c99d2774a54ddf900bfcd23b2be533",//指导订单ID
    orderNumber, // : "2023100813402475762",//订单号
    expertsId, // : 29,//指导医生ID
    expertsName, // : "zhang",//指导医生名称
    type, // : 1,//指导类型(1图文、2视频)
    processNode, // : 2,//流程节点(图文流程节点[1选择指导方式、2描述病例问题、3支付指导费用、4病例资料被查看、5问题被回复并对话、6结束指导交易成功];  视频流程节点[1选择指导方式、2描述病例问题、3提交指导单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、  7结束指导、8确认并支付指导费用、9交易成功])
    createDate, // : "2023-10-08 13:40:25",//创建时间
    amount, // : 0.10,//账单金额
    status, // : 支付状态(0已取消、1未支付(待评估)、2去支付(已评估)、3已支付)
    customerId,
    tenantId,
    orderCaseTemplate, // 1通用模板，2正畸模版
  } = params || {};

  // 备注：我创建的指导、需要我的指导列表，点击按钮都走这个方法。需要我的指导list里，全部都是进入到服务流程的数据（不会有processNode<=3的数据）
  // 当前是图文、视频指导
  if (type == 1 || type == 2) {
    let paramsByhistory = {};
    if (processNode > 3) {
      // 视频会诊去支付，跳转订单详情页（H5端）
      if (status == 2 && type == 2 && env != '4') {
        paramsByhistory = {
          pathname: `/PaymentByConsultation/MyConsultationDetails/${id}`,
          query: {},
        };
      } else {
        // 已提交指导单 前往指导详情页面
        paramsByhistory = {
          pathname: '/ConsultationModule/ConsultationDetails',
          query: {
            consultationId: id,
            consultationType: type,
          },
        };
      }
    } else if (processNode < 3) {
      // 未提交指导单 前往创建指导
      if (orderCaseTemplate == 1) {
        // 通用模板病例编辑
        paramsByhistory = {
          pathname: '/ConsultationModule/StartConsultation/Step3',
          query: {
            consultationId: id,
          },
        };
      } else if (orderCaseTemplate == 2) {
        // 正畸模板的病例不能再H5端编辑
        if (env != '4') {
          message.warning('请在电脑端浏览器打开');
          return;
        }
        // 正畸模板病例编辑
        paramsByhistory = {
          pathname: '/CreationOrthodontics/Step1',
          query: {
            orthodonticConsultationId: id,
          },
        };
      }
    } else {
      // processNode = 3，前往创建指导第4步
      paramsByhistory = {
        pathname: '/ConsultationModule/StartConsultation/Step4',
        query: {
          consultationId: id,
        },
      };
    }

    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'pathname', // 页面地址onchange事件
        pathnameByChild: paramsByhistory.pathname, // 路由信息
        searchByChild: `?${stringify(paramsByhistory.query)}`, // 路由信息
      };
      // console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl());
      return;
    }

    if (!!isReplace) {
      history.replace(paramsByhistory);
    } else {
      history.push(paramsByhistory);
    }
  }

  // 当前是正畸指导
  if (type == 3) {
    // 正畸方案审核不能再H5端查看
    if (env != '4') {
      message.warning('请在电脑端浏览器打开');
      return;
    }
    let paramsByhistory = {};
    // 流程节点正畸审核[1基本信息、2检查及分析、3问题清单及诊断、4治疗方案、5影像资料、6提交病例、7病例被查看、8审核驳回、9审核通过、10指导结束])
    if (processNode >= 6) {
      paramsByhistory = {
        pathname: '/ConsultationModule/ConsultationDetails',
        query: {
          consultationId: id,
          consultationType: type,
        },
      };
    } else {
      paramsByhistory = {
        pathname: '/CreationOrthodontics/Step1',
        query: {
          consultationId: id,
          customerId: customerId,
          tenantId: tenantId,
        },
      };
    }

    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'pathname', // 页面地址onchange事件
        pathnameByChild: paramsByhistory.pathname, // 路由信息
        searchByChild: `?${stringify(paramsByhistory.query)}`, // 路由信息
      };
      // console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl());
      return;
    }

    if (!!isReplace) {
      history.replace(paramsByhistory);
    } else {
      history.push(paramsByhistory);
    }
  }
}

/* 推荐页面-文章-帖子等item需要展示的创建日期
方法参数createDate 格式为"2024-01-09 14:53:47"
方法输出 为"01-09" */
export function getItemCreateDate(createDate) {
  if (!createDate) {
    return '';
  }
  // Parse the input date string
  const parsedDate = dayjs(createDate);
  // Format the date in "MM-DD" format
  const formattedDate = parsedDate.format('MM-DD');
  // Return the formatted date
  return formattedDate;
}

// 上传图片，获取图片类型，主要判断是否是heic，heif
export function getFileType(reader) {
  const bufferInt = new Uint8Array(reader.result);
  const arr = bufferInt.slice(0, 4); // 通用格式图片
  const headerArr = bufferInt.slice(0, 16); // heic格式图片
  let header = '';
  let allHeader = '';
  let realMimeType;

  for (let i = 0; i < arr.length; i++) {
    header += arr[i].toString(16); // 转成16进制的buffer
  }

  for (let i = 0; i < headerArr.length; i++) {
    allHeader += headerArr[i].toString(16);
  }
  // magic numbers: http://www.garykessler.net/library/file_sigs.html
  switch (header) {
    case '89504e47':
      realMimeType = 'image/png';
      break;
    case '47494638':
      realMimeType = 'image/gif';
      break;
    case 'ffd8ffDB':
    case 'ffd8ffe0':
    case 'ffd8ffe1':
    case 'ffd8ffe2':
    case 'ffd8ffe3':
    case 'ffd8ffe8':
      realMimeType = 'image/jpeg';
      break;
    case '00020': // heic开头前4位可能是00020也可能是00018，其实这里应该是判断头尾000的，可以自己改下
    case '00018':
      allHeader.lastIndexOf('68656963') === 13 || allHeader.lastIndexOf('68656966') === 13
        ? (realMimeType = 'image/heic')
        : (realMimeType = 'unknown');
      break;
    default:
      realMimeType = 'unknown';
      break;
  }
  return realMimeType;
}

// 上传图片headers
export function getHeaders() {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  // 获取环境类型，1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用   5 FRIDAY app中使用
  const env = getOperatingEnv();
  // FRIDAY APP中本地存储值：access_token、client、phone、user_name、wxuserId、userInfo
  return {
    // token
    access_token: localStorage.getItem('access_token') || '',
    username:
      env == 5
        ? localStorage.getItem('user_name')
        : localStorage.getItem('vxOpenIdCipherText')
        ? localStorage.getItem('vxOpenIdCipherText')
        : UserInfo?.phone,
    client: env == 5 ? localStorage.getItem('client') : 'WX',
    type: env === '1' ? '' : '1', // h5 传1
  };
}

// 下拉刷新的文案
export const statusRecord: Record<PullStatus, string> = {
  pulling: '用力拉',
  canRelease: '松开吧',
  refreshing: '正在加载中...',
  complete: '加载完成',
};

/*
 * 获取微信开放平台的appid
 * */
export const getCurrentWechatApp = () => {
  const env = getOperatingEnv();
  let currentWechatApp;
  if (env != 2) {
    currentWechatApp = location.host.includes('-test')
      ? { appid: 'wx92706c3b96410fd3' }
      : location.host.includes('-pre')
      ? { appid: 'wxd3d3b7febb22d1d4' }
      : { appid: 'wxb7daead40705a62b' };
  } else {
    currentWechatApp = { appid: 'wxe8fc94c9167c46f7' };
  }
  return currentWechatApp;
};

/*
 * 获取当前企微环境下是哪个企微应用
 * friday：ww4fed1b7a837a8504, 1000082 =>域名:idental
 * friday非正式应用：ww0a6175cdf1574db4 1000082 =>域名:digidental
 * arrail：wx53956bdd9fba8623, 1000135 =>域名:dhealth
 * */
export const getCurrentWXWorkApp = () => {
  const currentWXWorkApp =
    location.host.includes('dhealth') || location.host.includes('localhost')
      ? { appid: 'wx53956bdd9fba8623', agentid: '1000135' }
      : location.host.includes('digidental')
      ? { appid: 'ww0a6175cdf1574db4', agentid: '1000082' }
      : { appid: 'ww4fed1b7a837a8504', agentid: '1000082' };
  return currentWXWorkApp;
};

// 获取微信授权登录 userType: normal个人用户  企业用户
export const getWechatAuth = (userType = 'normal') => {
  const { redirectByPush, redirect } = history.location.query;
  let redirectUrl = redirectByPush || redirect;
  const env = getOperatingEnv();
  const getCurrentWechatAccount = getCurrentWechatApp();
  let newRandom = Math.random().toString(36).substr(2, 9);
  let params = {
    appid: getCurrentWechatAccount.appid,
    redirect_uri:
      userType == 'normal'
        ? `${window.location.protocol}//${window.location.host}/User/wechatAuthLogin?${stringify({
            random: newRandom,
          })}`
        : `${window.location.protocol}//${window.location.host}/User/enterpriseQRcodeAuth?${stringify({
            random: newRandom,
          })}`,
    response_type: 'code', //
    scope: env != 2 ? 'snsapi_login' : 'snsapi_userinfo',
    state:
      env != 2
        ? redirectUrl
          ? `#redirectByPush=${redirectUrl}`
          : null
        : redirectUrl
        ? `STATE#wechat_redirect#redirectByPush=${redirectUrl}`
        : 'STATE#wechat_redirect',
  };
  window.location.href =
    env != 2
      ? `https://open.weixin.qq.com/connect/qrconnect?${stringify(params)}`
      : `https://www.friday.tech/getWXcodeInfo.html?${stringify(params)}`;
};

// 获取企微授权登录
export const getWXWorkAccountAuth = () => {
  const getWXWorkAccount = getCurrentWXWorkApp();
  let newRandom = Math.random().toString(36).substr(2, 9);
  let params = {
    appid: getWXWorkAccount.appid,
    redirect_uri: `${window.location.href.split('?')[0]}?${stringify({ random: newRandom })}`,
    response_type: 'code', //
    scope: 'snsapi_privateinfo',
    agentid: getWXWorkAccount.agentid,
    state: 'STATE#wechat_redirect',
  };
  window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?${stringify(params)}`;
};

// 获取嵌套iframe的父级的域名
export function getArrailUrl() {
  const emrUrl = location.host;
  let arrailEmrUrl;
  const tenantId = localStorage.getItem('tenantId');
  if (tenantId == 'ba67e6cf30dc4f9c9c46adef188bbd04') {
    arrailEmrUrl = {
      'dhealth-dev.friday.tech': `https://arrail-dev.5i5ya.com`, //开发瑞尔
      'dhealth-test.friday.tech': `https://arrail-test.5i5ya.com`, //测试瑞尔
      'dhealth-pre.friday.tech': `https://arrail-virtual.5i5ya.com`, //虚拟瑞尔
      'dhealth.friday.tech': `https://arrail.5i5ya.com`, //生产瑞尔
    };
  } else if (tenantId == '77057aed269f4a14957ae0ad0eff359a') {
    arrailEmrUrl = {
      'dhealth-dev.friday.tech': `https://rytime-dev.5i5ya.com`, //开发瑞泰
      'dhealth-test.friday.tech': `https://rytime-test.5i5ya.com`, //测试瑞泰
      'dhealth-pre.friday.tech': `https://rytime-virtual.5i5ya.com`, //虚拟瑞泰
      'dhealth.friday.tech': `https://rytime.5i5ya.com`, //生产瑞泰
    };
  } else {
  }
  // 本地调试
  if (emrUrl.indexOf('localhost') > -1) {
    return 'http://localhost:8001';
  }
  return arrailEmrUrl[emrUrl];
}

// 跳转首页公共方法
export function goToHomePage(dispatch, goToType) {
  if (!dispatch) {
    Toast.show('缺失参数dispatch');
    return;
  }
  const homePageLink = localStorage.getItem('homePageLink');
  if (homePageLink) {
    if (goToType == 'push') {
      history.push(`/home?${homePageLink.split('?')[1]}`);
    } else {
      history.replace(`/home?${homePageLink.split('?')[1]}`);
    }
  } else {
    dispatch({
      type: 'activity/getHomePageLink',
      payload: {},
    }).then((res) => {
      const { code, content, msg } = res;
      if (code == 200 && content) {
        localStorage.setItem('homePageLink', content.pageUrl || '');
        localStorage.setItem('squarePageLink', content.directUrl || '');
        if (content.pageUrl) {
          if (goToType == 'push') {
            history.push(`/home?${content.pageUrl.split('?')[1]}`);
          } else {
            history.replace(`/home?${content.pageUrl.split('?')[1]}`);
          }
        } else {
          history.replace('/Square');
          Toast.show('首页未配置，请联系管理员~');
        }
      } else {
        history.replace('/Square');
        Toast.show(msg || '数据加载失败');
      }
    });
  }
}

// 获取视口区域的安全高度
export function getViewportHeightWithoutSafeArea() {
  const safeAreaTop = window.outerHeight - window.innerHeight;
  const viewportHeight = window.innerHeight;
  const heightWithoutSafeArea = viewportHeight - safeAreaTop;
  return viewportHeight;
}

// 设置一个月后需要删除的localStorage，FRIDAY App下载浮窗状态
export function checkDownloadAppCardExpiration() {
  const storedExpirationDate = localStorage.getItem('expirationDateFridayCard');
  if (storedExpirationDate) {
    const expirationTime = parseInt(storedExpirationDate);
    const now = dayjs().valueOf();

    if (now > expirationTime) {
      // 过期后执行删除操作
      localStorage.removeItem('expirationDateFridayCard');
    }
  }
}
