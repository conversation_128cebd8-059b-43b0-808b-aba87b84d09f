import React from 'react'
import styles from './Color.less'

// 颜色
const colors = [
  '#000', '#666', '#e03333', '#ee732e', '#33bd62', '#4481dd', '#864ae9',
]

class Color extends React.Component {

  static defaultProps = {
    itemOnClick: () => {},
  }

  constructor(props) {
    super(props)
    this.state = {}
  }

  componentDidMount(): void {

  }

  // 阻止默认事件
  onMouseDown = (e) => {
    e.preventDefault()
  }

  // 点击颜色
  itemOnClick = (value) => {
    this.props.itemOnClick(value)
  }


  render() {
    return (
      <div className={styles.color_container}>
        {
          colors.map(item => (
            <div
              key={item}
              className={styles.color_item}
              style={{background: item}}
              onMouseDown={this.onMouseDown}
              onClick={() => this.itemOnClick(item)}
            ></div>
          ))
        }
      </div>
    )
  }
}

export default Color
