// 顶部导航栏
.header_wrap {
  position: absolute;
  z-index: 900;
  left: 0;
  top: 0;
  width: 100%;
  height: 51PX;
  padding: 7PX 16PX 0 10PX;

  .header_row1 {
    display: flex;
    align-items: center;
    margin-bottom: 3PX;

    .row1_left {
      flex: 1;
      display: flex;
      align-items: center;
    }

    .row1_center {
      width: 51%;
      display: flex;
      justify-content: center;
      align-items: center;
      column-gap: 4PX;

      .row1_center_name {
        font-size: 15PX;
        color: #fff;
        line-height: 21PX;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      svg {
        flex-shrink: 0;
      }
    }

    .row1_right {
      flex: 1;
      font-size: 14PX;
      color: #FF5F57;
      line-height: 20PX;
      text-align: right;
    }
  }

  .header_row2 {
    display: flex;
    align-items: center;

    .row2_left {
      flex: 1;
      display: flex;
      align-items: center;
      font-size: 10PX;
      line-height: 14PX;
      color: #B2B2B2;
    }

    .row2_center {
      width: 40PX;
      text-align: center;
      font-size: 10PX;
      line-height: 14PX;
      color: #B2B2B2;
      white-space: nowrap;
    }

    .row2_right {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      font-size: 12PX;
      color: #0095FF;
      line-height: 17PX;
    }
  }
}

// 圆圈背景图形
.bg_circle_wrap {
  position: absolute;
  z-index: 1;
  right: -38PX;
  bottom: 24PX;
  width: 188PX;
  height: 188PX;
  border-radius: 50%;
  background: linear-gradient( 180deg, #1A265A 0%, #0C1533 71%);
  filter: blur(4PX);
  //display: none;
}

// 聊天区
.im_message_list_wrap {
  position: absolute;
  z-index: 900;
  left: 16PX;
  bottom: 114PX;
  width: 216PX;
  height: 180PX;
  padding-right: 8PX;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  .im_message_item {
    display: inline-flex;
    width: auto;
    max-width: 100%;
    flex-wrap: nowrap;
    column-gap: 4PX;
    margin-bottom: 4PX;
    padding: 3PX 8PX 4PX;
    border-radius: 10PX;
    font-size: 12PX;
    color: #fff;
    line-height: 17PX;
    word-break: break-all;
  }
  // 在深色背景里
  &.in_black {
    .im_message_item {
      background: rgba(255,255,255,0.1);
    }
  }
  // 在浅色背景里
  &.in_white {
    .im_message_item {
      background: rgba(0,0,0,0.2);
    }
  }
}

// 真实的输入框
.real_input_wrap {
  position: fixed;
  z-index: 940;
  left: 0;
  bottom: 0;
  width: 100%;
  display: flex;
  align-items: center;
  column-gap: 24PX;
  background: #fff;
  padding: 10PX 12PX 24PX;
  box-shadow: 0 -3PX 5PX 0 rgba(0,0,0,0.1);
  :global {
    .adm-input {
      flex: 1;
    }
    .adm-input-element {
      height: 32PX;
      font-size: 13PX;
      background: #F5F5F5;
      border-radius: 20PX;
      padding: 0 12PX;
      color: #000;
    }
  }
  .real_input_btn {
    flex-shrink: 0;
    border-radius: 16PX;
    width: 64PX;
    height: 32PX;
    line-height: 32PX;
    font-size: 14PX;
    text-align: center;
    background: #0095FF;
    color: #fff;
    white-space: nowrap;
  }
}

// 底部输入框和右侧按钮
.bottom_wrap {
  position: absolute;
  z-index: 900;
  left: 0;
  bottom: 62PX;
  width: 100%;
  padding: 3PX 16PX 15PX;
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
  .bottom_input_wrap {
    flex-shrink: 0;
    width: 180PX;
    display: flex;
    align-items: center;
    column-gap: 8PX;
    padding: 0 12PX;
    border-radius: 20PX;
    .input_box {
      flex: 1;
      color: #fff;
      font-size: 13PX;
      height: 32PX;
      line-height: 32PX;
    }
  }
  .bottom_btn_wrap {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    column-gap: 12PX;
    .bottom_btn_item {
      width: 32PX;
      height: 32PX;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .bottom_call_btn {
      margin-left: 2PX;
    }
  }
  // 在深色背景里
  &.in_black {
    .bottom_input_wrap {
      background: rgba(255,255,255,0.1);
    }
    .bottom_btn_wrap {
      .bottom_btn_item {
        background: rgba(255,255,255,0.1);
      }
    }
  }
  // 在浅色背景里
  &.in_white {
    .bottom_input_wrap {
      background: rgba(0,0,0,0.2);
    }
    .bottom_btn_wrap {
      .bottom_btn_item {
        background: rgba(0,0,0,0.2);
      }
    }
  }
}

// 底部操作按钮区
.footer_btn_wrap {
  position: absolute;
  z-index: 900;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 12PX 24PX 6PX;
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  background: #303953;
  .footer_btn_item_wrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 40PX;
    white-space: nowrap;
    font-size: 10PX;
    color: #fff;
    line-height: 14PX;
    position: relative;
    .badge {
      position: absolute;
      left: 50%;
      top: -4PX;
      margin-left: 4PX;
      min-width: 12PX;
      height: 12PX;
      line-height: 10PX;
      border-radius: 6PX;
      padding: 1PX;
      text-align: center;
      background: #FF180D;
      font-size: 9PX;
      color: #fff;
    }
  }
}

// 顶部消息通知
.message_notify_wrap {
  position: absolute;
  z-index: 900;
  top: 59PX;
  left: 0;
  width: 100%;
  padding: 0 16PX;
  .message_content {
    border-radius: 2PX;
    text-align: center;
    height: 24PX;
    line-height: 24PX;
    font-size: 12PX;
    color: #fff;
    & > span {
      color: #0095FF;
      margin: 0 4PX;
    }
  }
  // 在深色背景里
  &.in_black {
    .message_content {
      background: rgba(255,255,255,0.1);
    }
  }
  // 在浅色背景里
  &.in_white {
    .message_content {
      background: rgba(0,0,0,0.2);
    }
  }
}

// 申请连麦小手
.apply_speak_status_btn {
  position: absolute;
  z-index: 900;
  right: 16PX;
  bottom: 258PX;
  width: 32PX;
  height: 32PX;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  .apply_speak_count {
    position: absolute;
    right: -2PX;
    top: -1PX;
    width: 12PX;
    height: 12PX;
    border-radius: 50%;
    background: #FF180D;
    font-size: 9PX;
    color: #fff;
    text-align: center;
    line-height: 13PX;
    white-space: nowrap;
  }
  // 在深色背景里
  &.in_black {
    background: rgba(255,255,255,0.1);
  }
  // 在浅色背景里
  &.in_white {
    background: rgba(0,0,0,0.2);
  }
}

// 强制下麦浮动按钮
.forced_mute_btn_wrap {
  position: absolute;
  z-index: 910;
  right: 0;
  bottom: 116PX;
  height: 37PX;
  padding: 0 8PX;
  border-radius: 20PX 0 0 20PX;
  background: rgba(255,255,255,0.8);
  box-shadow: 0 -4PX 4PX 0 rgba(0,0,0,0.1);
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  .user_name {
    font-size: 14PX;
    color: #000;
    white-space: nowrap;
  }
  .mute_btn {
    width: 64PX;
    height: 21PX;
    text-align: center;
    line-height: 21PX;
    background: #FF5F57;
    border-radius: 14PX;
    font-size: 12PX;
    color: #fff;
  }
}

// 打call连击消息
.call_group_msg_wrap {
  position: absolute;
  z-index: 910;
  left: 0;
  bottom: 253PX;
  height: 32PX;
  padding-right: 28PX;
  padding-left: 16PX;
  background: linear-gradient(90deg, rgba(255,222,141,0.8) 0%, rgba(255,222,141,0) 100%);
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  font-size: 15PX;
  font-weight: 500;
  color: #fff;
  line-height: 21PX;
}

// 有课件时的悬浮按钮
.ppt_list_btn_wrap {
  position: absolute;
  z-index: 910;
  left: 50%;
  bottom: 52PX;
  transform: translateX(-50%);
  height: 34PX;
  background: #636D8B;
  border-radius: 4PX;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  column-gap: 8PX;
  padding: 0 8PX;
  font-size: 12PX;
  color: #fff;
  line-height: 17PX;
  .separator_line {
    width: 0;
    height: 15PX;
    border-left: 1PX solid #fff;
    margin: 0 4PX;
  }
  .ppt_list_btn {
    display: flex;
    align-items: center;
    column-gap: 4PX;
  }
}

// 正在讲话悬浮信息
.now_speaking_user_wrap {
  position: absolute;
  z-index: 910;
  top: 94PX;
  right: 6PX;
  background: #303953;
  border-radius: 2PX;
  width: 175PX;
  height: 28PX;
  padding: 0 8PX;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  column-gap: 8PX;
  .separator_line {
    width: 0;
    height: 20PX;
    border-left: 1PX solid #68728E;
  }
  .speaking_user_content {
    font-size: 12PX;
    color: #fff;
    line-height: 17PX;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: flex;
    .speaking_user_item {
      margin-right: 8PX;
    }
  }
}
