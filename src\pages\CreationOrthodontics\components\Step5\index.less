.page_info {

}
.warp_content {
  width: 100%;
  display: flex;
  flex-direction: column;
  // align-items: center;
}
.title_box {
  font-size: 20px;
  font-weight: 500;
  color: #000000;
  line-height: 23px;
  text-align: center;
  margin-bottom: 32px;
  margin-top:36px;
}

.Item_Title {
  width: 100%;
  height: 45px;
  line-height:45px;
  background: #F8FAFD;
  border-radius: 3px 3px 3px 3px;
  opacity: 1;
  padding-left: 20px;
  padding-right: 20px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;

  .Item_Span_dot {
    color: #FF5F57;
  }
  .Item_Span_text {
    font-size: 14px;
    font-weight: 500;
    color: #000;
    line-height: 26px;
  }

  .edit_text {
    color: #0095FF;
  }
}

.item_box_werp {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 48px;

  .item_box {
    width: 150px;
    height: 114px;
    background: #7ec1ff;
    margin-bottom: 15px;
  }
  .item_span {
    margin-top: 10px;
    font-size: 15px;
    font-weight: 400;
    color: #333333;
    line-height: 18px;
  }
}


.pattern_Span {
  margin-bottom: 32px;

  .pattern_title {
    font-size: 14px;
    font-weight: 500;
    color: #000000;
    line-height: 14px;
    margin-bottom: 12px;
  }


  .Item_pattern {
    display: flex;
    width: 100%;
    padding-left: 11px;


    .Item_title_lable {
      font-size: 14px;
      font-weight: 400;
      color: #000000;
      line-height: 32px;
      margin-right: 8px;
      width: 110px;
      text-align: right;
    }

    .Item_title_value {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      width: calc(100% - 120px);

      .Item_title_value_content {
        display: flex;
        font-size: 14px;
        font-weight: 400;
        color: #000000;
        line-height: 26px;
        flex-direction: row;
        align-items: center;
        margin-right: 40px;
        margin-bottom: 15px;
      }
    }
  }
}

.content_warp {
  width: 873px;
}

.title_span {
  font-size: 14px;
  font-weight: 500;
  color: #000000;
  line-height: 14px;
  margin-bottom: 16px;
}

.item_value_content {
  display: flex;
  flex-wrap: wrap;
}


.submitWarp {
  display: flex;
  justify-content: space-around;
  margin-top: 48px;
  padding-bottom: 53px;
  .submitBox {
    display: flex;
    .submit_btn_Cancel {
      width: 92px;
      height: 36px;
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      border: 1px solid #0095FF;
      font-size: 14px;
      font-weight: 400;
      color: #0095FF;
      line-height: 34px;
      user-select: none;
      cursor: pointer;
      text-align: center;
      margin-right: 30px;
    }
    .submit_btn_Cancel:active {
      opacity: 0.8;
    }
    .submit_btn_Enter {
      width: 92px;
      height: 36px;
      background: #0095FF;
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      font-size: 14px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 34px;
      text-align: center;
      user-select: none;
      cursor: pointer;
    }
    .submit_btn_Enter:active {
      opacity: 0.8;
    }
  }
}

.Modal_title {
  font-size: 14px;
  font-weight: 500;
  color: #000000;
  line-height: 14px;
  margin-bottom: 11px;
}

