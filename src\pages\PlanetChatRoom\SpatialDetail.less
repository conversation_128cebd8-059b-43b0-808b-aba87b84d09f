.DerailWarp {
  width: 100%;
  height: 100%;
  background-color:#fff;
  position: relative;
  :global {
    .adm-toast-mask .adm-toast-main {
      max-width: 300px;
    }
  }
  .DownloadAppCardBody{
    width: 100%;
    position: absolute;
    top:0;
    z-index: 999;
  }
}

.PopupWarp {

}

.PopupContent {
  width: 100%;
  height: 100%;
  padding-left: 16px;
  padding-right: 16px;
}

.lineWarp {
  width: 100%;
  display:flex;
  justify-content: center;
}

.line {
  width: 48PX;
  height: 4PX;
  background: #D0D4D7;
  border-radius: 4PX 4PX 4PX 4PX;
  opacity: 1;
  margin-top: 12PX;
}

.content {
  width: 100%;
  height: calc(100% - 48PX);


  .contentTitle {
    font-size: 17px;
    font-weight: 500;
    color: #000000;
    line-height: 17PX;
    text-align: center;
    margin-top:12px;
    margin-bottom:24px;
  }

  .contentBox {
    .pwdTitle {
      font-size: 14px;
      font-weight: 500;
      color: #666666;
      line-height: 16px;
      margin-bottom: 16px;
    }
    .stress {
      color: #FF5F57;
      margin-right: 3px;
    }
  }

  .PwdFlex {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .PwdInput {
      width: 56px;
      height: 68px;
      background: #FFFFFF;
      border-radius: 8px 8px 8px 8px;
      opacity: 1;
      border: 1px solid #CCCCCC;
      font-size: 40px;
      justify-content: center;
      align-items: center;
      text-align: center;
    }

    .line_pwd {
      width: 22px;
      height: 1px;
      background: #CCCCCC;
      border-radius: 0px 0px 0px 0px;
      opacity: 1;
    }

  }



  .contentitemWarp {
    display:flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    .contentitem {
      display:flex;
      align-items: center;
    }
    .contentitemAvatar {
      width: 24PX;
      height: 24PX;
      background: #F9B4E3;
      opacity: 1;
      border-radius: 50%;
    }
    .contentitemName {
      font-size: 14PX;
      font-weight: 500;
      color: #000000;
      line-height: 14PX;
      margin-left: 8PX;
    }

    .contentPhone {
      font-size: 14PX;
      font-weight: 400;
      color: #000000;
      line-height: 14PX;
    }
    .contentTime {
      font-size: 14PX;
      font-weight: 400;
      color: #999999;
      line-height: 14PX;
    }
  }
}

.WarnModalWarp {
  :global {
    .adm-modal-content {
      padding: 0px 32px 32px;
    }

    .adm-modal-body:not(.adm-modal-with-image) {
      padding-top: 32px;
    }
  }
  .WarnModal {
    .WarnModalTitle {
      display: flex;
      justify-content: center;

      .SpatialDetail_modal_warn_icon {
        width: 24px;
        height: 24px;
        background: url('../../assets/PlanetChatRoom/SpatialDetail_modal_warn_icon.png') no-repeat;
        background-size: contain;
        display: inline-block;
        position: relative;
        top: -2px;
        margin-right: 8px;
      }

      .SpatialDetail_modal_warn_title {
        max-width: 183px;
        font-size: 17px;
        font-weight: 500;
        color: #000000;
        line-height: 20px;
        text-align: center;
      }

      .SpatialDetail_modal_title {
        margin-right: 15px;
        font-size: 17px;
        font-weight: 500;
        color: #000000;
        line-height: 20px;
        text-align: center;
      }
    }

    .SpatialDetail_modal_desc {
      width: 100%;
      min-width: 230px;
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      line-height: 16px;
      margin-top: 10px;
      text-align: center;
    }

    .WarnModalBtnWarp {
      display: flex;
      margin-top: 17px;
      justify-content: center;
      align-items: center;

      .CancelBtn {
        width: 108px;
        height: 37px;
        background: #EDF9FF;
        border-radius: 20px 20px 20px 20px;
        opacity: 1;
        line-height: 37px;
        text-align: center;
        font-size: 15px;
        font-weight: 400;
        color: #0095FF;
        margin-right: 14px;
        cursor: pointer;
        user-select: none;
      }

      .CancelBtn:active {
        background: #aeb7bc;
      }

      .EnterBtn {
        width: 108px;
        height: 37px;
        background: #0095FF;
        border-radius: 20px 20px 20px 20px;
        opacity: 1;
        line-height: 37px;
        text-align: center;
        font-size: 15px;
        font-weight: 400;
        color: #FFFFFF;
        cursor: pointer;
        user-select: none;
      }

      .EnterBtn:active {
        background: #0170c0;
      }
    }
  }
}


.WarnModalWarp_NoRem {
  position: relative;
  z-index: 999;

  .Download_Documents {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
    color: #0095FF;
    user-select: none;
    cursor: pointer;
    font-size: 14PX;
  }

  :global {
    .adm-modal-content {
      padding: 0px 16PX 16PX;
    }

    .adm-modal-body:not(.adm-modal-with-image) {
      padding-top: 32PX;
    }
  }
  .WarnModal {
    .WarnModalTitle {
      display: flex;
      justify-content: center;

      .SpatialDetail_modal_warn_icon {
        width: 24PX;
        height: 24PX;
        background: url('../../assets/PlanetChatRoom/SpatialDetail_modal_warn_icon.png') no-repeat;
        background-size: contain;
        display: inline-block;
        position: relative;
        top: -2PX;
        margin-right: 8PX;
      }

      .SpatialDetail_modal_warn_title {
        max-width: 210PX;
        font-size: 17PX;
        font-weight: 500;
        color: #000000;
        line-height: 20PX;
        text-align: center;
      }

      .SpatialDetail_modal_title {
        margin-right: 15PX;
        font-size: 17PX;
        font-weight: 500;
        color: #000000;
        line-height: 20PX;
        text-align: center;
      }
    }

    .SpatialDetail_modal_desc {
      width: 100%;
      min-width: 230PX;
      font-size: 14PX;
      font-weight: 400;
      color: #666666;
      line-height: 16PX;
      margin-top: 10PX;
      text-align: center;
    }

    .WarnModalBtnWarp {
      display: flex;
      margin-top: 17PX;
      justify-content: center;
      align-items: center;

      .CancelBtn {
        width: 108PX;
        height: 37PX;
        background: #EDF9FF;
        border-radius: 20PX 20PX 20PX 20PX;
        opacity: 1;
        line-height: 37PX;
        text-align: center;
        font-size: 15PX;
        font-weight: 400;
        color: #0095FF;
        margin-right: 14PX;
        cursor: pointer;
        user-select: none;
      }

      .CancelBtn:active {
        background: #aeb7bc;
      }

      .EnterBtn {
        width: 108PX;
        height: 37PX;
        background: #0095FF;
        border-radius: 20PX 20PX 20PX 20PX;
        opacity: 1;
        line-height: 37PX;
        text-align: center;
        font-size: 15PX;
        font-weight: 400;
        color: #FFFFFF;
        cursor: pointer;
        user-select: none;
      }

      .EnterBtn:active {
        background: #0170c0;
      }
    }
  }
}


.ToastWarp {

}

.ShowContent {
  // min-width: 150px;
}

.fixed_share_box {
  display: none;
  position: fixed;
  z-index: 1000;
  right: 21px;
  top: 24px;
  &.fixed_share_box_show {
    display: block;
  }
  .icon1 {
    position: relative;
    display: block;
    background: url("../../assets/GlobalImg/arrow_up.png") no-repeat right center;
    background-size: 122px 108px;
    width: 100%;
    height: 108px;
  }
  .message_box {
    position: relative;
    font-size: 18px;
    font-weight: 400;
    line-height: 25px;
    color: #fff;
    white-space: nowrap;
    top: -18px;
    margin-bottom: 14px;
  }
  .icon_box {
    display: flex;
    align-items: center;
    justify-content: center;
    i {
      display: block;
      width: 48px;
      height: 48px;
      &.icon2 {
        background: url("../../assets/GlobalImg/share_one.png") no-repeat center;
        background-size: 100% 100%;
        margin-right: 24px;
      }
      &.icon3 {
        background: url("../../assets/GlobalImg/share_more.png") no-repeat center;
        background-size: 100% 100%;
      }
    }
  }
}

.allowApplications_box {
  margin-top: 40px;

  .spin_box {
    margin-right: 10px;
  }

  .allowApplications_box_warp {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .allowApplications_box_text {
    font-weight: 400;
    font-size: 14px;
    color: #0095FF;
    line-height: 14px;
    text-align: center;
  }
}


.EnterBtnByModalVisibleByApplicationSubmitted {
  width: 100%;
  height: 37PX;
  background: #0095FF;
  border-radius: 20PX 20PX 20PX 20PX;
  opacity: 1;
  line-height: 37PX;
  text-align: center;
  font-size: 15PX;
  font-weight: 400;
  color: #FFFFFF;
  cursor: pointer;
  user-select: none;
}

.SpatialDetail_modal_desc_ApplicationSubmitted {
  width: 100%;
  min-width: 230PX;
  font-size: 14PX;
  font-weight: 400;
  color: #666666;
  line-height: 16PX;
  margin-top: 10PX;
  text-align: left;
}

.SpatialDetail_modal_success_icon {
  width: 24px;
  height: 24px;
  background: url('../../assets/PlanetChatRoom/SpatialDetail_modal_success_icon.png') no-repeat;
  background-size: contain;
  display: inline-block;
  position: relative;
  top: -2px;
  margin-right: 8px;
}
