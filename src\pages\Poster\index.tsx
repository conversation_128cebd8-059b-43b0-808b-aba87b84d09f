/**
 * @Description: 海报页（H5）
 */
import React, { useEffect, useState } from 'react'
import { connect, history } from 'umi'
import classNames from 'classnames'
import html2canvas from 'html2canvas'
import { WxAppIdByPublicAccount } from '@/utils/utils'
import { Toast } from 'antd-mobile'
import styles from './index.less'

import NavBar from '@/components/NavBar'                       // 导航栏
import PosterTemplateDom from './Components/PosterTemplateDom'
import {stringify} from "qs"; // 海报dom

// 模板数据list
const templateDataSource = [
  { id: 1, outerBgColor1: '#CEF9FF', outerBgColor2: '#E1E5FF', textColor: '#000', bgColor1: '#95C4FF', bgColor2: '#4E41FF', imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/poster_bg_1.png' },
  { id: 2, outerBgColor1: '#FFE483', outerBgColor2: '#FFFCEA', textColor: '#000', bgColor1: '#FFDD47', bgColor2: '#FF5C00',  imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/poster_bg_2.png' },
  { id: 3, outerBgColor1: '#FFCDDA', outerBgColor2: '#FFF4F7', textColor: '#000', bgColor1: '#FFE2E2', bgColor2: '#FF48B6',  imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/poster_bg_3.png' },
  { id: 4, outerBgColor1: '#B3E794', outerBgColor2: '#F1FBE7', textColor: '#fff', bgColor1: '#CADE90', bgColor2: '#00B15C',  imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/poster_bg_4.png' },
  { id: 5, outerBgColor1: '#C9E5FF', outerBgColor2: '#EAFAFF', textColor: '#fff', bgColor1: '#86E4FF', bgColor2: '#0067FF',  imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/poster_bg_5.png' },
  { id: 6, outerBgColor1: '#9FF6DF', outerBgColor2: '#EAFFF5', textColor: '#fff', bgColor1: '#5AEDB8', bgColor2: '#039BB0',  imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/poster_bg_6.png' },
]

const Index: React.FC = (props: any) => {
  const { dispatch } = props
  const { id } = history.location.query
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');

  const initialTemplateData = {
    id: 1,
    outerBgColor1: '#CEF9FF',      // 页面背景色
    outerBgColor2: '#E1E5FF',
    textColor: '#000',             // 文字颜色
    bgColor1: '#95C4FF',           // 主持人背景色
    bgColor2: '#4E41FF',
    imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/poster_bg_1.png',   // 背景图片
  }
  const initialOutBgColorData = {
    outerBgColor1: '#CEF9FF',
    outerBgColor2: '#E1E5FF',
  }
  const [imgUrl, setImgUrl] = useState(null)                                   // 海报图片url
  const [templateData, setTemplateData] = useState(initialTemplateData)        // 当前模板数据
  const [outBgColorData, setOutBgColorData] = useState(initialOutBgColorData)  // 页面背景色
  const [loadingCreatePoster, setLoadingCreatePoster] = useState(false)        // 创建海报loading
  const [spaceState, setSpaceState] = useState({})                             // 直播or会议数据

  useEffect(() => {
    // 获取空间海报信息
    getSpacePosterInfo()
  }, [])

  useEffect(() => {
    if (spaceState.id || spaceState.id == 0) {
      // 触发生成图片
      setLoadingCreatePoster(true)

      // 分享配置
      onShareAppMessage()

      // 增加GDP
      shareUpdateByType()
    }
  }, [spaceState])

  useEffect(() => {
    if (loadingCreatePoster) {
      // 添加定时器，延时生成图片避免图片文字错位
      beforeCreatePoster()
    }
  }, [loadingCreatePoster])

  // 获取空间海报信息
  const getSpacePosterInfo = () => {
    dispatch({
      type: 'userInfoStore/getSpacePosterInfo',
      payload: {
        spaceId: id,                   // 空间ID
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200) {
        setSpaceState(content || {})
      } else {
        Toast.show(msg || '数据加载失败')
      }
    }).catch(err => {})
  }

  // 微信分享配置
  const onShareAppMessage = () => {
    const url = window.location.href
    let shareUrl = `${window.location.origin}/PlanetChatRoom/${spaceState.starSpaceType == 2 ? 'Meet' : 'Live'}/${spaceState.id}?${stringify({
      shareUserId:UserInfo?.friUserId,
      isShare: 1,
      pwd: spaceState.starSpaceType == 2 && spaceState.password ? spaceState.password : '',
    })}`
    if (spaceState.starSpaceType == 2) {
      // meet
      shareUrl = `${window.location.origin}/PlanetChatRoom/${spaceState.starSpaceType == 2 ? 'Meet' : 'Live'}/${spaceState.id}?${stringify({
        shareUserId:UserInfo?.friUserId,
        isShare: 1,
        pwd: spaceState.starSpaceType == 2 && spaceState.password ? spaceState.password : '',
      })}`
    }else {
      // live
      shareUrl = `${window.location.origin}/Square?${
        stringify({
          ReservationId: spaceState.id,
          shareUserId:UserInfo?.friUserId,
          isShare: 1,
        })
      }`
    }

    dispatch({
      type: 'userInfoStore/getJsapiTicket',
      payload: {
        currentUrl: url,                                   // 页面url
        appId: WxAppIdByPublicAccount,                     // 公众号appId
      },
    }).then(res => {
      if (res && res.code == 200) {
        wx.config({
          debug: false,
          appId: res.content.appId,
          timestamp: res.content.timestamp,
          nonceStr: res.content.nonceStr,
          signature: res.content.signature,
          jsApiList: [
            'updateAppMessageShareData',
            'updateTimelineShareData',
            'onMenuShareTimeline',
            'onMenuShareAppMessage',
            'onMenuShareQQ',
            'onMenuShareWeibo',
            'onMenuShareQZone',
          ],
        })
        wx.ready(() => {
          const shareDate = {
            title: '【FRIDAY医生星球】牙医都来这里学习和交流！',
            desc: spaceState.name,
            link: shareUrl,
            imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/DigitalHealthShareImg.png',
          };
          console.log(shareDate)
          wx.updateAppMessageShareData(shareDate);
          wx.updateTimelineShareData(shareDate);
          wx.onMenuShareTimeline(shareDate);
          wx.onMenuShareAppMessage(shareDate);
          wx.onMenuShareQQ(shareDate);
          wx.onMenuShareWeibo(shareDate);
          wx.onMenuShareQZone(shareDate);
        })
      } else {
        // Toast.show('请求微信配置失败～！')
      }
    })
  }

  // 分享操作更新gdp等数据
  const shareUpdateByType = () => {
    dispatch({
      type: 'userInfoStore/shareUpdateByType',
      payload: {
        id: id,                                            // 被分享ID(王国、空间)
        shareId: UserInfo?.friUserId,                      // 分享人ID
        type: 3,                                           // 类型(2王国，3空间)
        hostId: spaceState.hostUserInfo.wxUserId,          // 空间主持人ID
      }
    }).then(res => {

    }).catch(err => {})
  }

  // 延迟加载
  const beforeCreatePoster = () => {
    Toast.show({
      icon: 'loading',
      content: '',
      duration: 0,
    })
    setTimeout(() => {
      createPoster()
    }, 1000)
  }

  // 生成海报
  const createPoster = () => {
    html2canvas(document.querySelector('#poster_dom'),{
      foreignObjectRendering: false,
      backgroundColor: 'transparent',
      removeContainer: true,
      useCORS: true,
      // allowTaint:true,
      // proxy: 'https://dhealth-test.friday.tech/'
    }).then(canvas => {
      const url = canvas.toDataURL('image/png')
      setImgUrl(url)                     // 海报图片url
      setLoadingCreatePoster(false)      // 生成海报loading
      setOutBgColorData({
        ...outBgColorData,
        outerBgColor1: templateData.outerBgColor1,   // 页面背景色
        outerBgColor2: templateData.outerBgColor2,
      })
      Toast.clear()
    }).catch(err => {
      setLoadingCreatePoster(false)
      Toast.clear()
    })
  }

  // 点击切换模板
  const onClickTemplate = (obj) => {
    if (loadingCreatePoster) {
      return
    }
    setTemplateData({
      ...obj,
    })
    setLoadingCreatePoster(true)
  }

  return (
    <>
      <NavBar title="分享海报" bordered/>

      <div className={styles.container}>
        <div className={styles.content} style={{background: `linear-gradient(180deg, ${outBgColorData.outerBgColor1} 0%, ${outBgColorData.outerBgColor2} 100%)`}}>
          <div className={styles.img_wrap}>
            <img src={imgUrl} width={'auto'} height={'auto'} alt=""/>
            {
              imgUrl && <div className={styles.tips}>长按分享给朋友</div>
            }
          </div>
        </div>

        {/* 海报dom-用于生成图片 */}
        <PosterTemplateDom templateData={templateData} data={spaceState}/>

        {/* 模板 */}
        <div className={styles.bottom_wrap}>
          <p className={styles.bottom_title}>选择模板</p>
          <div className={styles.bottom_template_wrap}>
            {
              templateDataSource.map(item => {
                return (
                  <div
                    key={item.id}
                    className={classNames(styles.template_option, {
                      [styles.checked]: templateData.id == item.id,
                    })}
                    onClick={() => onClickTemplate(item)}
                  >
                    <img src={item.imgUrl} width={71} height={90} alt=""/>
                  </div>
                )
              })
            }
          </div>
        </div>
      </div>
    </>
  )
}

export default connect(({ loading }: any) => ({loading}))(Index)
