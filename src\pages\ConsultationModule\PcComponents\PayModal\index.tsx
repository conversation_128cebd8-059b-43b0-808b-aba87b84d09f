/**
 * @Description: 在线支付弹窗
 */
import React, {useEffect, useState} from 'react'
import classNames from 'classnames'
import PayResultModal from '../PayResultModal'
import {message, Modal,Spin,Checkbox} from 'antd'
import { CloseOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import QRcode from 'qrcode.react'
import styles from './index.less'
import _ from "lodash";
import {
  getConsultationOrderInfo, getConsultationOrderPayStatus,
  submitConsultationPictureOrderPay as submitConsultationPictureOrderPayAction, // 图文指导
  submitConsultationVideoOrderPay as submitConsultationVideoOrderPayAction,     // 视频指导
} from "@/services/consultation/ConsultationList";

import { history } from "@@/core/history";


interface PropsType {
  visible: boolean,                      // 标题
  onCancel: any,                         // 标题
  consultationId: any,                   // 指导订单ID
  consultationType: any,                 // 指导类型[1图文、2视频]
  payType?: any,                         // 支付类型
}

const Index: React.FC<PropsType> = (props: any) => {
  const val = React.useRef();
  const [ consultationOrderInfo,setConsultationOrderInfo ] =  useState(null);
  const [ payResultVisible,setPayResultVisible ] =  useState(null);
  const [ payQrUrl, setPayQrUrl ] = useState(null);                    // 支付二维码路径
  const [ loading, setLoading ] = useState(false);                  // 获取订单详情loading
  const [ visibleByDefaultAssistantUrl, setVisibleByDefaultAssistantUrl ] = useState(false);
  const [ payType, setPayType ] = useState(props.payType ? props.payType : 1);        // 支付类型 1微信 2支付宝
  const { dispatch,consultationId,consultationType } = props
  const userInfo = JSON.parse(localStorage.getItem('userInfo'));
  const { friUserId:idByUserInfo, name} = userInfo || {}
  const {
    id:idByOrderInfo, // : "a2c99d2774a54ddf900bfcd23b2be533",//指导订单ID
    orderNumber,      // : "2023100813402475762",//订单号
    type,             // : 1,//指导类型(1图文、2视频)
    isFinish,         // : 0,
    status,           // : 1,//支付状态(0已取消、1未支付(待评估)、2去支付(已评估)、3已支付)
    amount,           // : 0.10,//账单金额
    expertsName,      // : "zhang",//指导医生名称
    vipUnitPrice,     // : 1.00,//会员单价(图文是/次，视频是/30min)
    defaultAssistantUrl, // 默认助手二维码
  } = consultationOrderInfo || {};

  useEffect(() => {
    if(props.visible) {
      setPayType(props.payType ? props.payType : 1)
    }else {
      setConsultationOrderInfo(null)
      setPayResultVisible(null)
      setPayQrUrl(null)
      setPayType(null)
      if(val.current && val.current.pollingId) {
        clearInterval(val.current.pollingId)
      }
    }
    return () => {
      if(val.current && val.current.pollingId) {
        clearInterval(val.current.pollingId)
      }
    }
  },[props.visible])

  useEffect(()=>{
    if(props.payType) { setPayType(props.payType) }else { setPayType(1) }
  },[props.payType])

  useEffect(()=>{
    if(payType) {
      getConsultationAndCaseInfo()
    }
  },[payType])


  useEffect(() => {
    if(!!payQrUrl) {
      getOrderIsPayByCallBack()
    }else {
      if(val.current && val.current.pollingId) {
        clearInterval(val.current.pollingId)
      }
    }
  },[payQrUrl])

  useEffect(() => {
    // 关闭了[支付结果]弹窗
    if(!payResultVisible){
      getConsultationAndCaseInfo()
    }
  },[payResultVisible])


  // [支付]PC扫码支付后没有支付回调需要接口轮询判定支付状态
  // 当支付二维码生成之后每5秒获取状态一次
  // 修改设置轮询器在getOrderIsPayByCallBack调用之后
  const getOrderIsPayByCallBack = async (interval) => {
    const startTime = Date.now()
    if(val.current && !!val.current.pollingId){ clearInterval(val.current.pollingId);val.current.pollingId = null }
    // 定义轮询函数
    const polling = async () => {
      const dataByOrder = await getConsultationOrderPayStatus({
        wxUserId: idByUserInfo,
        userName: name,
        id:consultationId,       // 指导订单ID
      })
      // message.info(JSON.stringify({...dataByOrder,id}))
      if (dataByOrder && dataByOrder.code === 200) {
        const { content } = dataByOrder || {}
        if (content) {
          // 清除轮询定时器
          if(val.current && !!val.current.pollingId){ clearInterval(val.current.pollingId);val.current.pollingId = null }
          // 支付完成,跳转到支付完成页面
          PaymentCompletion();
        }
      }
    }
    // 开始轮询，每 5 秒钟调用一次 polling 函数
    let pollingId = setInterval(polling, 5000)
    val.current = { pollingId:pollingId }
  }

  const PaymentCompletion = async () => {
    setPayResultVisible(true);
  }

  const getConsultationAndCaseInfo = _.debounce(async () => {
    /**
     *  consultationId, // [string] 是 指导订单ID
     *  type, // [string] 是 (1:运营端订单详情, 2:H5/WEB端视频订单详情)
     */
    await setLoading(true)
    let dataByGetConsultationAndCaseInfo = await getConsultationOrderInfo({
        consultationId: consultationId,
        type: consultationType,
    })
    const { code,content,msg:msgByGetConsultationAndCaseInfo } = dataByGetConsultationAndCaseInfo || {}
    if (code == 200 && content) {
      setConsultationOrderInfo(content);

      if (content.createUserId != userInfo.friUserId) {
        message.warning('您不能支付非本人的订单！')
        props.onCancel()
        return;
      }
      // 判定当前订单未支付
      if(content.status != 3) {
        // 判定当前指导类型 图文指导
        if(content.type == 1) {
          let dataBySubmitConsultationPictureOrderPayAction = await submitConsultationPictureOrderPayAction({
            wxUserId: idByUserInfo,
            userName: name,
            id:content.id,                 //          指导订单ID
            payType: payType,              // [int]    是 1微信、2支付宝
            operationPlatform: 2,          // [int]    是 操作平台 1H5 2PC
            payMethod: 2,                  // [int]    是 图文 1提交指导 2立即支付
            freeTimes: null,               // [int]    否 免费次数
            amount: content.vipUnitPrice,  // [double] 是 金额
          })
          setLoading(false);
          if(
            dataBySubmitConsultationPictureOrderPayAction
            && dataBySubmitConsultationPictureOrderPayAction.code == 200
            && dataBySubmitConsultationPictureOrderPayAction.content
          ){
            setPayQrUrl(dataBySubmitConsultationPictureOrderPayAction.content)
          }else {
            message.error(dataBySubmitConsultationPictureOrderPayAction && dataBySubmitConsultationPictureOrderPayAction.msg ?dataBySubmitConsultationPictureOrderPayAction.msg : '申请支付订单失败!')
            setPayQrUrl(null)
          }
        }else {
          // 判定当前指导类型 视频指导
          // 视频指导结算
          let dataBySubmitConsultationVideoOrderPayAction = await submitConsultationVideoOrderPayAction({
            wxUserId: idByUserInfo,
            userName: name,
            id:content.id,                 // id [string] 是 订单id
            payMethod: 2,                  // [int] 是 图文 1确认订单 2立即支付
            operationPlatform: 2,          // [int] 是 操作平台 1H5 2PC
            payType: payType,        // [int] 是 1微信、2支付宝
          })
          setLoading(false);
          if(
            dataBySubmitConsultationVideoOrderPayAction
            && dataBySubmitConsultationVideoOrderPayAction.code == 200
            && dataBySubmitConsultationVideoOrderPayAction.content
          ){
            setPayQrUrl(dataBySubmitConsultationVideoOrderPayAction.content)
          }else {
            message.error(dataBySubmitConsultationVideoOrderPayAction && dataBySubmitConsultationVideoOrderPayAction.msg ? dataBySubmitConsultationVideoOrderPayAction.msg : '申请支付订单失败!')
            setPayQrUrl(null)
          }
        }
      }else {
        if(content.status == 3){
          // 当前订单已经支付完成
          props.onCancel()
          message.success('订单已经支付完成!')
        }else {
          // 订单无法支付
          message.error('当前订单无法支付!')
          setLoading(false);
        }
      }
    }else {
      setLoading(false);
      setConsultationOrderInfo(null);
      message.error(msgByGetConsultationAndCaseInfo ? msgByGetConsultationAndCaseInfo : '获取订单信息失败!')
    }
  },500)

  return (
    <>
      <div id={'modal_pay'} className={classNames({
        [styles.modal_pay]:true,

      })}>
        <Modal
          title="在线支付"
          visible={!visibleByDefaultAssistantUrl && !payResultVisible && props.visible}
          onCancel={props.onCancel}
          className={styles.modal}
          destroyOnClose
          footer={null}
        >
          <Spin spinning={!!loading}>
            <div className={styles.item}>
              <div className={styles.label}>指导订单：</div>
              <div className={styles.value}>{orderNumber}</div>
            </div>
            <div className={styles.item}>
              <div className={styles.label}>支付金额：</div>
              <div className={styles.price_value}>
                ¥ {type == 1 ? vipUnitPrice : amount}</div>
            </div>
            {props.payType ?
              <div className={styles.item}>
                <div className={styles.label}>支付方式：</div>
                {/* props.payType, // [int] 是 1微信、2支付宝 */}
                <div className={styles.value}>{ payType == 1 ? '微信' : '支付宝' }</div>
              </div> :
              <div className={styles.item}>
                <div className={styles.label}>支付方式：</div>
                {/* props.payType, // [int] 是 1微信、2支付宝 */}
                <div className={styles.value}>
                  <Checkbox.Group value={[payType]}>
                    <Checkbox value={2} onChange={(e)=>{setPayType(e.target.value)}}>支付宝</Checkbox>
                    <Checkbox value={1} onChange={(e)=>{setPayType(e.target.value)}}>微信支付</Checkbox>
                  </Checkbox.Group>
                </div>
              </div>
            }

            <div className={styles.qrcode}>
              {payQrUrl &&
                <QRcode
                  style={{marginBottom: 10}}
                  value={payQrUrl}
                  size={125}
                />
              }
              <div>打开 {payType == 1 ? '微信支付' : '支付宝钱包'}</div>
              <div>扫一扫继续付款</div>
            </div>

            <div onClick={()=>{setVisibleByDefaultAssistantUrl(true)}} className={styles.pay_btn}>
              <QuestionCircleOutlined/>
              <div>支付遇到问题</div>
            </div>
          </Spin>
        </Modal>
      </div>


      <Modal
        getContainer={() => document.getElementById('modal_pay')}
        open={visibleByDefaultAssistantUrl}
        title={"添加客服小忆微信"}
        onCancel={()=>{setVisibleByDefaultAssistantUrl(false)}}
        width={524}
        footer={null}
      >
        <div className={styles.content}>
          <div className={styles.qr_code}>
            <img src={defaultAssistantUrl} alt="code" />
            <p className={styles.word}>联系客服小忆</p>
          </div>
        </div>
      </Modal>

      <PayResultModal
        visible={payResultVisible}
        onCancel={()=>{
          setPayResultVisible(null)
          props.onCancel(true)
        }}
        consultationId={consultationId}
        consultationType={consultationType}
      />
    </>
  )
}

export default Index
