.spin {
  height: 100%;
  & > :global(.ant-spin-container) {
    height: 100%;
  }
}

.header_wrap {
  position: fixed;
  z-index: 989;
  top: 0;
  left: 0;
  width: 100%;
  height: 50px;
  background: #F5F6F8;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .header_left {
    display: flex;
    align-items: center;
    column-gap: 12px;
    font-size: 19px;
    color: #000;
    font-weight: 500;
  }
}

.container {
  height: 100%;
  overflow-y: auto;
  background: #fff;
  padding-bottom: 34px;
  .bg_wrap {
    min-height: 100%;
    background: #F5F6F8;
    padding-top: 50px;
    display: flex;
    flex-direction: column;
  }
}

.top_wrap {
  flex-shrink: 0;
  padding-bottom: 23px;
  padding-top: 30px;
  .avatar_wrap {
    display: flex;
    justify-content: center;
    margin-bottom: 18px;
    .avatar {
      border: 2px solid #fff;
      border-radius: 50%;
    }
  }

  .name_wrap {
    text-align: center;
    padding: 0 20px;
    word-break: break-all;
    font-size: 23px;
    line-height: 33px;
    color: #000;
    font-weight: 600;
    margin-bottom: 8px;
  }

  .nickname_wrap {
    text-align: center;
    font-size: 14px;
    color: #000;
    line-height: 20px;
    margin-bottom: 12px;
  }

  .certified_wrap {
    display: flex;
    justify-content: center;
    .certified {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      column-gap: 4px;
      border-radius: 11px;
      height: 24px;
      padding: 0 9px;
      font-size: 13px;
      background: #D9F0FF;
      color: #009DFF;
      &.not_certified {
        background: #E9E9E9;
        color: #666;
      }
    }
  }
}

.bottom_wrap {
  flex: 1;
  background: #fff;
  border-radius: 16px 16px 0 0;
  padding: 21px 16px 16px;
  .bottom_title {
    font-size: 16px;
    color: #000;
    line-height: 23px;
    font-weight: 500;
    margin-bottom: 10px;
  }
  .setting_item_wrap {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    column-gap: 8px;
    height: 40px;
    margin-bottom: 8px;
    img  {
      flex-shrink: 0;
    }
    .setting_item_text {
      flex: 1;
      font-size: 14px;
      color: #666;
    }
  }
}

.btn_wrap {
  width: 100%;
  padding-bottom: 20px;
  background: #fff;
  display: flex;
  justify-content: center;
  .btn {
    white-space: nowrap;
    width: 110px;
    height: 40px;
    line-height: 38px;
    border: 1px solid #FF5F57;
    border-radius: 24px;
    font-size: 14px;
    color: #FF5F57;
    text-align: center;
  }
}
