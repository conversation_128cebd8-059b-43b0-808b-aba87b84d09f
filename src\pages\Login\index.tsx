import React, { useState, useEffect } from 'react';
import { connect, history } from 'umi';
import { Form, Input, Spin, Button, message, Tabs } from 'antd';
import { EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';
import { Toast } from 'antd-mobile';
import styles from './index.less';
import { debounce } from 'lodash';
import loginIcon from '@/assets/GlobalImg/logo.png';
import wechat_logo from '@/assets/GlobalImg/wechat.png'
const loginBg = 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/logoBg.png';
import goBackPc from '@/assets/GlobalImg/pc_goback.png';
import {
  getLoginMsgCode,
  getMsgCodeUserInfo,
  msgCodeLogin,
  imgCode,      // 获取图片验证码
} from "@/services/login/login";
import NavBar from '@/components/NavBar'; // 头部返回
import { getAES, getOperatingEnv, getWechatAuth } from '@/utils/utils';


// 第三方登录list
const TripartiteList =[
  {
    key:1,
    name:'微信',
    icon:wechat_logo,
  },
]

const Index: React.FC = (props: any) => {
  const [form] = Form.useForm();
  const [tabkey, setTabkey] = useState('2'); // tabkey 1:验证码登录 2:密码登录
  const [countdown, setCountdown] = useState(0); // 验证码倒计时状态
  const pageKeyByRendom = Math.random().toString(36).substr(2, 8); // pageKey 当前页面唯一标识 用于验证码登录 生成8位随机整数
  const [imgCodeUrl, setImgCodeUrl] = useState(''); // 图片验证码地址
  const [pageKey, setPageKey] = useState(pageKeyByRendom); // 页面pageKey
  const [loading, setLoading] = useState(false);  // 添加登录loading
  const [loadingBySendCode, setLoadingBySendCode] = useState(false); // 添加发送验证码loading
  const [passwordVisible, setPasswordVisible] = useState(false); // 显示隐藏密码按钮
  const { dispatch } = props;

  const [pageType, setPageType] = useState(); // 1pc 2 移动端
  // ① 判定当前页面视口是否小于750 如果小于750则为移动端
  let updateType = () => {
    // let clientWidth = document.documentElement.clientWidth;
    let env = getOperatingEnv() // 获取当前操作环境 1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用
    let type = env == 4 ? 1 : 2;
    setPageType(type);
  };
  updateType = debounce(updateType, 100);
  window.addEventListener('resize', updateType);
  // 进入页面判定是否存在token
  useEffect(() => {
    const { query = {}} = history?.location;
    if(query?.Tab!==undefined){
      setTabkey(query?.Tab);
    }
    // ① 判定当前页面视口是否小于750 如果小于750则为移动端
    updateType();
  }, []);

  // 页面打开生成pageKey
  useEffect(() => {
    // 获取图片验证码
    getImgCode();
  }, []);

  // 获取图形验证码 或 切换图形验证码
  const getImgCode = async ()=>{
    let DataByImgCode = await imgCode({
      pageKey: pageKey,
    },{
      responseType:'blob',
    });
    if (DataByImgCode && !DataByImgCode.status) {
      const reader = new FileReader();
      reader.readAsDataURL(DataByImgCode);
      reader.onloadend = function () {
        // 将读取的图片数据流转换为 base64 格式
        const base64data = reader.result;
        setImgCodeUrl(base64data);
      };
    }
  }

  // 发送验证码并开始倒计时
  const sendCode = async () => {
    const phone = form.getFieldValue(`phone${tabkey}`);
    // 获取图形验证码
    const imgCode = form.getFieldValue(`imgCode`);
    if (!phone) {
      pageType == 1 ? message.error('请输入手机号') : Toast.show({content: '请输入手机号'});
      return;
    }
    if (!imgCode) {
      pageType == 1 ? message.error('请输入图形验证码') : Toast.show({content: '请输入图形验证码'});
      return;
    }
    if(!(/^1[3456789]\d{9}$/.test(phone))) {
      pageType == 1 ? message.error('请输入正确手机号') : Toast.show({content: '请输入正确手机号'});
      return;
    }

    setLoadingBySendCode(true); // 开启发送验证码loading

    try {
      // 发送验证码请求
      const res = await getLoginMsgCode({
        phone: phone,
        imgCode:imgCode,
        pageKey: pageKey,
      });

      if (res.code === 200 && res.content) {
        setLoadingBySendCode(false); // 关闭发送验证码loading
        pageType == 1 ? message.success('验证码发送成功') : Toast.show({content: '验证码发送成功'});
        // 开始倒计时 60s
        let time = 60;
        setCountdown(time);
        const timer = setInterval(() => {
          time--;
          setCountdown(time);
          if (time === 0) {
            clearInterval(timer);
          }
        }, 1000);

      } else {
        setLoadingBySendCode(false); // 关闭发送验证码loading
        pageType == 1 ? message.error(res.msg) : Toast.show({content: res.msg});
      }

    } catch (error) {
      setLoadingBySendCode(false); // 关闭发送验证码loading
      pageType == 1 ? message.error('验证码发送失败') : Toast.show({content: '验证码发送失败'});
    }
  }

  // form成功登录按钮
  const onFinish = debounce(async (values) => {
    const {
      phone1,     // 手机号
      phone2,     // 手机号
      phoneCode, // 验证码
      password,  // 密码
      imgCode,   // 校验图形验证码
    } = values;
    // 如果是密码加密下密码，如果是验证码则不用处理
    const AgetES = tabkey==1?getAES(phoneCode, 'arrail-dentail&2', 'arrail-dentail&3'):getAES(password, 'arrail-dentail&2', 'arrail-dentail&3');

    // 开启登录loading
    setLoading(true);
    const params = tabkey==1 ? { username: phone1, password: AgetES, loginMode:tabkey, pageKey: pageKey, imgCode:imgCode}: { username: phone2, password: AgetES, loginMode:tabkey, pageKey: pageKey };
    // 登录请求
    const res = await msgCodeLogin(params);
    // 登录成功
    if (res.code === 200) {
      pageType == 1 ? message.success('登录成功') : Toast.show({content: '登录成功'});
      // 保存token
      localStorage.setItem('access_token', res.content.access_token);
      // 获取用户信息接口时，header头里面需要传userName，值为手机号
      localStorage.setItem('userInfo', JSON.stringify({
        phone: res.content.username
      }));
      // 登录成功后获取用户信息
      getMsgCodeUserInfo_func();
    } else {
      setLoading(false) // 关闭登录loading
      pageType == 1 ? message.error(res.msg) : Toast.show({content: res.msg});
    }
  },1000)

  // 登录后获取用户信息
  const getMsgCodeUserInfo_func = async () => {
    try {
      let token_text = localStorage.getItem('access_token');
      const UserInfo = JSON.parse(localStorage.getItem('userInfo'));
      let userInfo = await getMsgCodeUserInfo({ token: token_text, username: UserInfo.phone});

      if (userInfo?.code === 200) {
        setLoading(false) // 关闭登录loading
        // 保存用户信息
        localStorage.setItem('userInfo', JSON.stringify({
          ...userInfo.content,
          id: userInfo?.content?.friUserId
        }));
        // 重定向到指定页面
        const { redirect, redirectByPush } = history.location.query;
        if (!!redirect) {
          window.location.replace(redirect);
        } else if (!!redirectByPush) {
          history.goBack()
        } else {
          // PC端
          if (pageType == 1) {
            history.replace('/home')
          } else {
            history.replace('/Square')
          }
        }
      }else{
        setLoading(false) // 关闭登录loading
        message.error('获取用户信息失败：', userInfo?.msg);
      }
    } catch (error) {
      setLoading(false) // 关闭登录loading
      message.error('获取用户信息失败：', error);
    }
  };

  // 渲染验证码倒计时
  const renderCountdown = () => {
    if (countdown > 0) {
      return `${countdown}`;
    }
    return '获取验证码';
  };

  // pc端返回首页
  const pcGoHomeFn = () => {
    history.push('/home')
  }

  // 切换登录方式
  const onChangeTabs = (key) => {
    console.log(key);
    setTabkey(key);
  };

  // 第三方登录方法
  const tripartiteLoginFn =(type) => {
    if(type==1){
      getWechatAuth()
    }
  }
  return pageType ? (
    <div className={pageType == 1 ? styles.pc_login_wrap : styles.login_wrap}>
      <div className={styles.pc_gohome} onClick={pcGoHomeFn}><img src={goBackPc} alt=""/>返回首页</div>

      <div className={styles.login_bg_wrap}>
        <NavBar title={'登录'} className={styles.loginNavBar}></NavBar>
        <div className={styles.login_bg_box}><img src={loginBg} alt="" /></div>
        <div className={styles.login_content_title}>
          <div className={styles.login_img}><img src={loginIcon} alt="" /></div>

          <div className={styles.login_text}>
            欢迎登录FRIDAY数智化平台
          </div>
        </div>
        <Spin spinning={!!loading}>
          <div className={styles.login_form_wrap}>
            <Form form={form} onFinish={onFinish}>
              <Tabs
                defaultActiveKey={tabkey}
                onChange={onChangeTabs}
                items={[
                  {
                    label:'密码',
                    key: '2',
                    children: <div>
                      <div className={styles.login_form_input}>
                        <Form.Item
                          label=""
                          name='phone2'
                          rules={[
                            { required: tabkey == '2' ? true : false, message: '请输入手机号' },
                            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的联系电话' }
                          ]}
                        >
                          <Input bordered={false} autoComplete="off" placeholder='请输入手机号' />
                        </Form.Item>
                      </div>
                      {/* 密码 */}
                      <div className={styles.login_form_input2}>
                        <Form.Item
                          label=""
                          name="password"
                          className={styles.phone_code}
                          rules={[
                            { required: tabkey == '2' ? true : false, message: '请输入密码' },
                            { max:20,  message: '请输入正确的6-20位密码' }
                          ]}
                        >
                          <Input.Password iconRender={passwordVisible => (passwordVisible ? <EyeTwoTone style={{fontSize: '18px'}} /> : <EyeInvisibleOutlined style={{fontSize: '18px'}} />)} visibilityToggle={{ visible: passwordVisible, onVisibleChange: setPasswordVisible }} bordered={false} autoComplete="off" placeholder='请输入密码' />
                        </Form.Item>
                      </div>
                      <div className={styles.forget_password}>
                        <span onClick={()=>{history.push('/User/forgetPassword?from=login')}}>忘记密码</span>
                      </div>
                    </div>,
                  },
                  {
                    label: '验证码',
                    key: '1',
                    children:<div>
                      <div className={styles.login_form_input}>
                        <Form.Item
                          label=""
                          name='phone1'
                          rules={[
                            { required: tabkey == '1' ? true : false, message: '请输入手机号' },
                            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的联系电话' }
                          ]}
                        >
                          <Input bordered={false} autoComplete="off" placeholder='请输入手机号' />
                        </Form.Item>
                      </div>
                      {/* 图形验证码 */}
                      <div className={styles.login_form_input1}>
                        <Form.Item
                          name='imgCode'
                          className={styles.img_code}
                          rules={[
                            {
                              required: tabkey == '1' ? true : false,
                              message: '请输入图形验证码',
                            }
                          ]}
                        >
                          <Input bordered={false} autoComplete="off" max={10} placeholder={'请输入图形验证码'}></Input>
                        </Form.Item>
                        <div className={styles.verification_code} onClick={()=>{getImgCode();}}>
                          <img src={imgCodeUrl}></img>
                        </div>
                      </div>
                      {/* 短信验证码 */}
                      <div className={styles.login_form_input2}>
                        <Form.Item
                          label=""
                          name="phoneCode"
                          className={styles.phone_code}
                          rules={[
                            { required: tabkey == '1' ? true : false, message: '请输入验证码' }
                          ]}
                        >
                          <Input bordered={false} autoComplete="off" maxLength={6} placeholder='请输入验证码' />
                        </Form.Item>
                        <Spin spinning={!!loadingBySendCode}>
                          <div className={styles.sendCode} onClick={()=>{
                            if (countdown > 0) { return; }
                            sendCode()
                          }}>
                            {renderCountdown()}
                          </div>
                        </Spin>
                      </div>
                    </div> ,
                  },
                ]}
              />
              <Button className={styles.login_Btn} htmlType="submit" loading={loading}>
                登录
              </Button>
            </Form>
            {(getOperatingEnv()!=3&&getOperatingEnv()!=6)&& <div className={styles.login_TripartiteList}>
                {
                  TripartiteList.map((item, index) => {
                    return <img key={index} src={item.icon} onClick={() => {
                      tripartiteLoginFn(item.key)
                    }}></img>
                  })
                }
              </div>
            }
            <div className={styles.login_go}>
              没有账号?<span onClick={() => {
              history.push({pathname: '/User/register', query: {inputPhone: form.getFieldValue('phone')}})
            }}>立即注册</span>
            </div>
          </div>
        </Spin>
      </div>
    </div>
  ) : null
}
export default connect(({ activity, loading }: any) => ({ activity, loading }))(Index)
