// 打卡按钮
import React from 'react';
import classNames from 'classnames';
import styles from './index.less';  // 引入自定义样式
import {
  SDKAPPID,       // TRTC 的 sdkappid
  EXPIRETIME,     // TRTC 签名过期时间，建议不要设置的过短
  SECRETKEY,      // TRTC 计算签名用的加密密钥
  audience,       // TRTC 角色类型-观众
  anchor,         // TRTC 角色类型-主播
  SDKAppIDByIm,   // 接入时需要将0替换为您的即时通信 IM 应用的 SDKAppID
  SIGN_IN,        // IM 自定义消息类型-签到
  BULLET_SCREEN,  // IM 自定义消息类型-弹幕
  SEND_FLOWERS,   // IM 自定义消息类型-送花
  SEND_APPLAUSE,  // IM 自定义消息类型-掌声
  HAND_UP,        // IM 自定义消息类型-举手连麦
  HAND_DOWN,      // IM 自定义消息类型-放弃连麦断开
  FORCED_END,
  UPDATA_STATE,      // IM 自定义消息类型-强制结束连麦
  licenseUrl,
} from '@/app/config';

const SignInButton = ({
                        isShowApplyForLinkMicList,
                        isNotLogin,
                        setModalVisibleByUserTokenInvalid,
                        sendMessageByIm,
                        dispatch,
                        SpaceInfo,
                        spaceId,
                        currentUserType,
                        isSignIn,
                        setIsShowApplyForLinkMicList,
                        setHasMoreBySignInList,
                        SideListTypeForSignInList
}) => {
  const handleSignInClick = async (e) => {
    // 阻止事件冒泡和默认行为
    e.stopPropagation();
    e.preventDefault();

    // [判定登录] 判定是否已登录,未登录则弹出登录弹窗
    if (isNotLogin) {
      setModalVisibleByUserTokenInvalid();
      return null;
    }

    // 当前身份非主持人并且未打卡
    if (currentUserType !== 1 && isSignIn === 0) {
      sendMessageByIm({ dataType: SIGN_IN, description: '1' });

      // 更新状态，表示已打卡
      dispatch({
        type: 'PlanetChatRoom/setState',
        payload: {
          SpaceInfo: {
            ...SpaceInfo,
            isSignIn: 1,
          },
        },
      });
    }

    // 主持人点击打卡展示打卡列表
    if (currentUserType === 1) {
      setIsShowApplyForLinkMicList(SideListTypeForSignInList == isShowApplyForLinkMicList ? null : SideListTypeForSignInList);
      let dataBySignInList = await dispatch({
        type: 'PlanetChatRoom/getSignInList',
        payload: {
          spaceId: spaceId,
          pageNum: 1,
          pageSize: 10,
        },
      });
      const { content } = dataBySignInList || {};
      const { resultList } = content || {};
      setHasMoreBySignInList(Array.isArray(resultList) && resultList.length > 0);
    }
  };

  return (
    <div onClick={handleSignInClick} className={styles.HorizontalLiveRoom_Btn_Warp}>
      <i
        className={classNames({
          [styles.title_Icon_dakai_Icon]: true,
          [styles.title_Icon_dakai_Icon_active]: isSignIn === 1,
        })}
      ></i>
      <div
        className={classNames({
          [styles.text]:true,
          [styles.title_Icon_shoucang_Icon_text]: true,
          [styles.title_Icon_shoucang_Icon_text_active]: isSignIn === 1,
        })}
      >
        打卡
      </div>
    </div>
  );
};

export default SignInButton;
