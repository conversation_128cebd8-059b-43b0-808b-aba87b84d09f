.container {
  height: 100vh;
  overflow-y: auto;
  position: relative;
  padding-top: 68px;
  padding-bottom: constant(safe-area-inset-bottom);//兼容 IOS<11.2
  padding-bottom: env(safe-area-inset-bottom);//兼容 IOS>11.2
  .bg_color {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    height: 100px;
    background: #FFF linear-gradient(135deg, #FFF 0%, #E4EEFC 100%);
  }
}

.info_box {
  position: relative;
  background: #fff;
  border-radius: 12px 12px 0px 0px;
  padding: 49px 12px 16px;
  .avatar {
    width: 64px;
    height: 64px;
    border-radius: 2px;
    border: 2px solid #fff;
    color: #fff;
    text-align: center;
    line-height: 60px;
    font-size: 24px;
    font-weight: 500;
    position: absolute;
    top: -23px;
    left: 12px;

    .head_img {
      width: 64px;
      height: 64px;
      border-radius: 2px;
    }

    .no_comment_head {
      border-radius: 2px;
    }
  }
  .info_title {
    font-size: 24px;
    font-weight: 500;
    color: #000;
    line-height: 34px;
    margin-bottom: 4px;
    word-break: break-all;
  }
  .user {
    font-size: 14px;
    color: #666;
    line-height: 20px;
    margin-bottom: 4px;
  }
  .details {
    display: flex;
    margin-bottom: 8px;
    .details_item {
      font-size: 14px;
      color: #666;
      line-height: 20px;
      margin-right: 8px;
      &:last-child {
        margin-right: 0;
      }
      & > span + span {
        margin-left: 4px;
      }
      .number {
        font-weight: 600;
      }
    }
  }
  .introduce_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .introduce_title {
      font-size: 17px;
      font-weight: 600;
      color: #000;
    }
    .introduce_btn_box {
      display: flex;
      align-items: center;
      color: #0095FF;
      padding: 8px 0;
      line-height: 20px;
      font-size: 14px;

      img {
        width: 16px;
        height: 16px;
      }
    }
  }
  .brief_introduction_show {
    display: flex;
    flex-direction: column;
    white-space: pre-wrap;
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    line-height: 20px;

    .text {
      word-break: break-all;
    }
  }

  .brief_introduction_hide {
    display: block;
    white-space: pre-wrap;
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    line-height: 20px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 3;
    text-overflow: ellipsis;

    .text {
      word-break: break-all;
    }
  }
  .introduce {
    font-size: 14px;
    color: #666;
  }
}

.space {
  height: 8px;
  background: #F5F6F8;
}

.content_wrap {
  // padding-top: 16px;
  padding-bottom: 58px;

  // 会议
  .meeting_list_wrap {
    padding: 0 12px;
    .meeting_item_wrap {
      border-bottom: 1px solid #E1E4E7;
      margin-bottom: 16px;
    }
  }
}

.fixed_box {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 58px;
  background: #fff;
  z-index: 799;
  border-top: 1px solid #eee;
  padding-bottom: constant(safe-area-inset-bottom);//兼容 IOS<11.2
  padding-bottom: env(safe-area-inset-bottom);//兼容 IOS>11.2

  &.my_kingdom_style {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
  }

  &.fixed_box_pc {
    max-width: 750px;
    margin: 0 auto;
  }

  .edit_btn_style {
    height: 40px;
    background: #F5F5F5;
    border-radius: 20px 20px 20px 20px;
    padding: 9px 37px;
    flex-shrink: 0;
    font-size: 16px;
    font-weight: 400;
    color: #000000;
    margin-right: 11px;
  }

  .create_space_style {
    flex: 1;
    height: 40px;
    background: #0095FF;
    border-radius: 20px 20px 20px 20px;
    font-size: 16px;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 40px;
    text-align: center;
  }

  .box {
    padding: 9px 16px;
    .btn {
      background: #0095FF;
      height: 40px;
      line-height: 40px;
      border-radius: 20px;
      text-align: center;
      font-size: 16px;
      color: #fff;
    }
    .visible_btn {
      background: #F5F5F5;
      color: #999;
      height: 40px;
      line-height: 40px;
      border-radius: 20px;
      text-align: center;
      font-size: 16px;
    }
  }
}

.none_data {
  width: 100%;
  height: auto;
  padding-top: 110px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 15px;
  font-weight: 400;
  color: #333333;
  line-height: 18px;

  img {
    width: 150px;
    height: auto;
    margin-bottom: 4px;
  }
}

.tab_wrap {
  width: 100%;
  display: flex;
  padding: 12px 0 7px 12px;
  align-items: center;
  margin-bottom: 12px;
  border-bottom: 1px solid #DDDDDD;

  .tab_init {
    font-size: 16px;
    font-weight: 400;
    color: #666666;
    line-height: 19px;
    margin-right: 20px;

    &:nth-last-child(1) {
      margin-right: 0;
    }
  }

  .tab_active {
    font-size: 16px;
    font-weight: 400;
    color: #000;
    line-height: 19px;
    position: relative;
    font-weight: 600;

    &::after {
      content: '';
      width: 12px;
      height: 3px;
      background: #000000;
      border-radius: 6px;
      position: absolute;
      top: 120%;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}
