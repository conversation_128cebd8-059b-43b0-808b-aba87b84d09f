.MeetingCardInfo_item {
  width: 100%;
  min-height: 100px;
  background: #FFFFFF;
 /* border-radius: 8px 8px 8px 8px;
  padding: 12px;
  margin-bottom: 15px;*/
}

.MeetingCardInfo_rounded {
  border-radius: 8px 8px 8px 8px;
}

.MeetingCardInfo_roundedTop {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}
.MeetingCardInfo_none {
  border-radius: 0px;
}

.MeetingCardInfo_title {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;

  .title {
    font-weight: 600;
    font-size: 14px;
    color: #000000;
    line-height: 20px;
    width: calc(100% - 85px);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .status {
    font-weight: 500;
    font-size: 14px;
    color: #0095FF;
    line-height: 20px;
    width: 45px;
  }

  .toBegin {
    color: #0095FF;
  }

  .progress {
    color: #FF8F28;
  }

  .Ended {
    color: #999999;
  }

}

.MeetingCardInfo_Content {
  display: flex;

  .left_img {
    width: 100px;
    height: 57px;
    border-radius: 4px;
    // background: #7ec1ff;
    margin-right: 10px;
    position: relative;
    overflow: hidden;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  // 封面中的标题
  .title_in_cover_image {
    position: absolute;
    z-index: 600;
    width: 79%;
    top: 21px;
    left: 0;
    padding-left: 8px;
    font-size: 12px;
    line-height: 16px;
    color: #fff;
    font-weight: 500;
    word-break: break-all;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /* 指定显示行数 */
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .img_enterprise {
    height: 21px;
    background: #EEFFF9;
    border-radius: 4px 0px 4px 0px;
    font-size: 12px;
    line-height: 21px;
    color: #06A777;
    text-align: center;
    padding-left: 2px;
    padding-right: 2px;
    position: absolute;
    z-index: 666;
    top: 0px;
    left: 0px;
  }

  .right_content {
    font-size: 11px;
    line-height: 11px;
    color: #AAAAAA;
    margin-top: 3px;
    width: calc(100% - 115px);

    .date_item_box {
      display: flex;
      margin-bottom: 5px;
      .date_item {
        margin-right: 5px;
      }
    }
    .content_item {
      display: flex;
      margin-bottom: 4px;
      line-height: 14px;

      .title { }
      .content {
        width: calc(100% - 60px);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
      }

    }
  }
}
