/**
 * @Description: 移动端视频播放弹窗
 * @author: 赵斐
 */
import React, { useEffect, useRef, useState } from 'react';
import { Popup } from 'antd-mobile'

import TCPlayer from 'tcplayer.js';
import 'tcplayer.js/dist/tcplayer.min.css';
import styles from './index.less'
import closeIcon from '@/assets/Case/close_icon.png'
import { licenseUrl } from "@/app/config";

interface PropsType {
  dataSource: any,      // 视频数据
  visible: boolean,       // 视频弹窗状态
  onCancel: () => void,   // 取消回调
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { visible, onCancel, dataSource } = props;
  const {
    url,   // 视频地址
    id     // 当前数据id
  } = dataSource || {}

  let playerRef = useRef(null);
  const [videoPlayer, setVideoPlayer] = useState(null);
  // playerStateData 播放器状态数据
  const [playerStateData, setPlayerStateData] = useState(null)
  
  useEffect(() => {
    if (!!visible) {
      if (!videoPlayer && url) {
        // await setTimeout(() =>  {}, 1000);
        let videoJsNode = playerRef.current;
        console.log('idididi', id, !videoPlayer, videoJsNode && videoJsNode.id);
        if (videoJsNode && videoJsNode.id) {
          // 确认节点是否在异步执行的时候被销毁掉
          let element = document.getElementById(videoJsNode.id); // 通过ID获取节点
          if (!element) { return }
          // let vodByDAes = getDAesString(vodPathUrl,'arrail-dentail&2', 'arrail-dentail&3')
          let sources = [{ src: url }]
          let tcPlayerObj = TCPlayer(videoJsNode, {
            sources: sources,
            licenseUrl: licenseUrl,
            autoplay: true,
            loop: true,
            muted: true,
            preload: "meta"
          });
          if (!tcPlayerObj) { return }
          tcPlayerObj.ready(() => {
            setVideoPlayer(tcPlayerObj);
            // tcPlayerObj.volume(0);
            // tcPlayerObj.play();
          })
          tcPlayerObj.on('error', (value) => {
            setPlayerStateData(null)
          });
          tcPlayerObj.on('blocked', (value) => {
            // message.error('自动播放被浏览器阻止');
            setPlayerStateData(null)
            // tcPlayerObj.volume(0);
            // tcPlayerObj.play();
          });
          tcPlayerObj.on('pause', (value) => {
            // setPlayerStateData(null)
          });

          tcPlayerObj.on('playing', (value) => {
            setPlayerStateData(true)
          });

          tcPlayerObj.on('progress', (value) => {

            setPlayerStateData(true)
          });
        }
      } else {
        // await setTimeout(()=>{},1000);
        let videoJsNode = playerRef.current;
        if (!videoJsNode) { return }
        let element = document.getElementById(videoJsNode.id); // 通过ID获取节点
        if (!element) { return }
        videoPlayer && playerStateData && videoPlayer.volume(0);
        videoPlayer && playerStateData && videoPlayer.play();
      }
    } else {
      if (!!videoPlayer) {
        videoPlayer && playerStateData && videoPlayer.pause();
      }
    }
  }, [visible])

  useEffect(() => {
    return () => {
      if (!!videoPlayer) {
        videoPlayer.dispose();
      }
    };
  }, [videoPlayer])

  // 关闭弹窗
  const handleCancel = () => {
    onCancel()
  };
  return (
    <div className={styles.im_inlet_container}>
      <Popup
        visible={visible}
        onMaskClick={() => { handleCancel() }}
        onClose={() => { handleCancel() }}
        className={styles.modal_content}
        bodyStyle={{ height: '90vh' }}
      >
        <div className={styles.close_icon} onClick={() => { handleCancel() }}><img src={closeIcon} alt="icon" /></div>
        <video
          key={`video_spaceList_id_${id}`}
          ref={playerRef}
          id={`video_spaceList_id_${id}`}
          className={styles.video}
          x-webkit-airplay="allow"
          playsInline={true}
        />
      </Popup>
    </div>
  )
}
export default Index
