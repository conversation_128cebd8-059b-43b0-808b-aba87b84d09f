import React, { useCallback } from 'react';
import styles from './index.less'

const FullScreenToggle = ({ isMobile, dispatch, props, resetTimer, playerStateData, changeUrlParams }) => {
  const handleFullScreenToggle = useCallback((e) => {
    e.stopPropagation();
    resetTimer();

    const element = document.documentElement;

    try {
      if (!!document.fullscreenElement && document.fullscreenEnabled) {
        document.exitFullscreen();
      } else {
        if (element.requestFullscreen) {
          element.requestFullscreen();
        } else if (element.mozRequestFullScreen) {
          element.mozRequestFullScreen();
        } else if (element.webkitRequestFullscreen) {
          element.webkitRequestFullscreen();
        } else if (element.msRequestFullscreen) {
          element.msRequestFullscreen();
        }
      }
    } catch (error) {
      console.error('Toggle fullscreen failed:', error);
    }

    if (isMobile) {
      setTimeout(() => {
        dispatch({
          type: 'PlanetChatRoom/setState',
          payload: {
            isHorizontalLive: false,
            playerInfo: playerStateData,
          },
        });
        props.changeUrlParams({ isHorizontalLive: null });
      }, 500);
    } else {
      dispatch({
        type: 'PlanetChatRoom/setState',
        payload: {
          isHorizontalLive: false,
          playerInfo: playerStateData,
        },
      });
      props.changeUrlParams({ isHorizontalLive: null });
    }
  }, [isMobile, dispatch, props, resetTimer, playerStateData, changeUrlParams]);

  return (
    <div
      onClick={handleFullScreenToggle} className={styles.HorizontalLiveRoom_Btn_Warp}>
      <div className={styles.HorizontalLiveRoom_cancel_full_screen_btn}></div>
      <div className={styles.text}>全屏</div>
    </div>
  );
};

export default FullScreenToggle;
