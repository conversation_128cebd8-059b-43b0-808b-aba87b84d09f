import React, { useEffect, useState, useRef } from 'react'
import { history, connect } from 'umi'
import classNames from 'classnames'
import { cloneDeep } from 'lodash'
import { gdpFormat, processNames, randomColor } from '@/utils/utils'
import { Button, Input, Form, Radio, Select, Spin, message } from 'antd'
import { PlusOutlined, CloseOutlined } from '@ant-design/icons'
import styles from './index.less'

import NoDataRender from '@/components/NoDataRender'
import PcHeader from '@/componentsByPc/PcHeader'
import UploadImageModal from '@/pages/CreateGraphicsText/ComponentsPC/UploadImageModal'
import PreviewCoverImageModal from '@/pages/CreateGraphicsText/ComponentsPC/PreviewCoverImageModal'
import PreviewArticleModal from '@/pages/CreateGraphicsText/ComponentsPC/PreviewArticleModal'
import CroppingImageModal from '@/pages/CreateGraphicsText/ComponentsPC/CroppingImageModal'

import QuillDom from '@/pages/CreateGraphicsText/Quill'
import ToolbarPC from '@/pages/CreateGraphicsText/Quill/ToolbarPC/index'

const coverImageListEmpty = [
  { isEmpty: true, },
  { isEmpty: true, },
  { isEmpty: true, },
]

let timer = null
let pageDidMount = false

const Index: React.FC = (props: any) => {
  // 用户信息
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
  const { id } = history.location.query
  const { loading, dispatch } = props

  // 关联王国数据
  const [createAndJoinKingdomDataSource, setCreateAndJoinKingdomDataSource] = useState([])
  const [selectVisible, setSelectVisible] = useState(false)

  const quillRef = useRef(null)

  const [quillHistoryStack, setQuillHistoryStack] = useState({})
  const [quillFormat, setQuillFormat] = useState({})

  const initialModalState = {
    previewArticleVisible: false,
    previewArticleModalType: 1,
    previewCoverImageVisible: false,
    uploadImageVisible: false,
    uploadImageModalType: 1,
    uploadImageReplaceIndex: 0,
    uploadImageMaxNumber: 0,
    articleTextImgList: [],
    croppingImageVisible: false,
    croppingImageIndex: 0,
    croppingImageUrlShow: null,
    bottomTipsBarVisible: true,
  }
  const initialState = {
    imageTitle: '',
    imageTextContent: null,
    contentJson: null,
    articleLength: 0,
    kingdomId: null,
    kingdomName: null,
    coverImageNumber: 1,
    textImgList: [],
  }

  const [modalState, setModalState] = useState(initialModalState)
  const [state, setState] = useState(initialState)

  const [refreshPageState, setRefreshPageState] = useState(false)
  const [loadingSave, setLoadingSave] = useState(false)
  const [loadingSave2, setLoadingSave2] = useState(false)
  const [loadingUploadVideoOrImage, setLoadingUploadVideoOrImage] = useState(0)

  useEffect(() => {
    if (!pageDidMount) {
      return
    }
    console.log('暂存暂存暂存暂存1111111111',loadingUploadVideoOrImage)
    clearTimeout(timer)
    if (loadingUploadVideoOrImage) {
      return
    }
    timer = setTimeout(() => {
      editImgTextInfo2()
    }, 2000)
  }, [state.imageTitle, state.kingdomId, state.textImgList])

  useEffect(() => {
    console.log(123,quillRef)
    getCreateAndJoinKingdomList()
    if (id) {
      imgTextInfoUpdateId()
    }
    pageDidMount = true

    return () => {
      pageDidMount = false
      clearTimeout(timer)
    }
  }, [])

  const getCreateAndJoinKingdomList = () => {
    dispatch({
      type: 'userInfoStore/getCreateAndJoinKingdomList',
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200) {
        const arr = []
        if (content['1']) {
          arr.push({
            id: 1,
            name: '自己创建的',
            children: content[1],
          })
        }
        if (content['2']) {
          arr.push({
            id: 2,
            name: '已加入王国',
            children: content[2],
          })
        }
        setCreateAndJoinKingdomDataSource(arr);
      }
      if (code == 400) {
      }
    }).catch()
  }

  const imgTextInfoUpdateId = () => {
    dispatch({
      type: 'graphicsText/imgTextInfoUpdateId',
      payload: {
        imageTextId: id,
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200 && content) {
        if (content.createUserId != UserInfo.friUserId) {
          message.error('您没有编辑权限')
          setTimeout(() => {
            goBack()
          }, 1000)
          return
        }
        setState({
          ...state,
          imageTitle: content.imageTitle || '',
          kingdomId: content.kingdomId,
          kingdomName: content.kingdomName,
          coverImageNumber: content.textImgList && content.textImgList.length > 1 ? 3 : 1,
          textImgList: content.textImgList || [],
        })
        if (content.contentJson) {
          const contentJsonObj = JSON.parse(content.contentJson)
          quillRef?.current?.quill.setContents(contentJsonObj)
        }
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch(err => {
    })
  }

  const getQuillHistoryStack = (stack) => {
    setQuillHistoryStack(cloneDeep(stack))
  }

  const getQuillFormat = (format) => {
    setQuillFormat(cloneDeep(format))
  }

  const onChangeImageTitle = (e) => {
    setState({
      ...state,
      imageTitle: e.target.value,
    })
  }

  const onChangeImageTextContent = () => {
    console.log('输入正文内容触发回调，暂存',loadingUploadVideoOrImage)
    clearTimeout(timer)
    if (loadingUploadVideoOrImage) {
      return
    }
    timer = setTimeout(() => {
      editImgTextInfo2()
    }, 2000)
  }

  const onChangeCoverImageNumber = (e) => {
    if (e.target.value == 1 && state.textImgList.length > 1) {
      let textImgListClone = cloneDeep(state.textImgList)
      textImgListClone = textImgListClone.splice(0, 1)
      setState({
        ...state,
        coverImageNumber: e.target.value,
        textImgList: textImgListClone,
      })
    } else {
      setState({
        ...state,
        coverImageNumber: e.target.value,
      })
    }

  }

  const previewCoverImageModalShow = () => {
    setModalState({
      ...modalState,
      previewCoverImageVisible: true,
    })
  }

  const previewCoverImageModalHide = () => {
    setModalState({
      ...modalState,
      previewCoverImageVisible: false,
    })
  }

  const dropdownRender = (originNode) => {
    // return originNode
    return (
      <div className={styles.select_dropdown_container}>
        {
          createAndJoinKingdomDataSource.length == 0 ? <NoDataRender style={{marginTop: 0}} text="当前无可关联王国~"/>
          : createAndJoinKingdomDataSource.map(item => {
              return (
                <div key={item.id}>
                  <div className={styles.select_dropdown_title}>{item.name}({item.children.length}个)</div>
                  {
                    item.children.map(itemChild => {
                      return (
                        <div key={itemChild.id} className={classNames(styles.kingdom_item, {
                          [styles.selected]: state.kingdomId == itemChild.id,
                        })} onClick={() => onChangeKingdomId(itemChild.id, itemChild.name)}>
                          <i style={itemChild.kingdomCoverUrlShow || itemChild.kingImgUrlShow ? {backgroundImage: `url(${itemChild.kingdomCoverUrlShow || itemChild.kingImgUrlShow})`} : {background: randomColor(itemChild.wxUserId)}}>
                            {!itemChild.kingdomCoverUrlShow && !itemChild.kingImgUrlShow && processNames(itemChild.kingName)}
                          </i>
                          <div className={styles.kingdom_item_details}>
                            <div className={styles.kingdom_name}>{itemChild.name}</div>
                            <div className={styles.kingdom_info_box}>
                              <span>{gdpFormat(itemChild.gdp)}GDP</span>
                              <span>{gdpFormat(itemChild.nationalNum)}国民在交流</span>
                              <span>{itemChild.spaceNum}个热议空间</span>
                            </div>
                          </div>
                        </div>
                      )
                    })
                  }
                </div>
              )
            })
        }
      </div>
    )
  }

  const onChangeKingdomId = (kingdomId, kingdomName) => {
    setState({
      ...state,
      kingdomId,
      kingdomName,
    })
    setSelectVisible(false)
  }

  const onChangeKingdomIdByClear = (value, option) => {
    setState({
      ...state,
      kingdomId: null,
      kingdomName: null,
    })
  }

  const uploadImageModalShow = (uploadImageModalType, uploadImageReplaceIndex) => {

    const quillContents = quillRef?.current?.unprivilegedEditor?.getContents()
    const articleTextImgList = []
    if (quillContents && quillContents.ops) {
      quillContents.ops.forEach(item => {
        if (typeof item.insert == 'object' && item.insert.image) {
          articleTextImgList.push(item.insert.image.src)
        }
      })
    }
    setModalState({
      ...modalState,
      uploadImageVisible: true,
      uploadImageModalType,
      uploadImageReplaceIndex,
      uploadImageMaxNumber: uploadImageModalType == 1 ? state.coverImageNumber - state.textImgList.length : 1,
      articleTextImgList,
    })
  }


  const uploadImageModalHide = () => {
    setModalState({
      ...modalState,
      uploadImageVisible: false,
      uploadImageModalType: 1,
      uploadImageReplaceIndex: 0,
      uploadImageMaxNumber: 0,
      articleTextImgList: [],
    })
  }


  const handleUploadImage = (uploadTextImgList) => {
    let textImgListClone = cloneDeep(state.textImgList)

    if (modalState.uploadImageModalType == 1) {
      uploadTextImgList.forEach(item => {
        textImgListClone.push({
          imageUrlShow: item,
          isCover: 1,
        })
      })
    } else {
      textImgListClone.splice(modalState.uploadImageReplaceIndex, 1, {
        imageUrlShow: uploadTextImgList[0],
        isCover: 1,
      })
    }

    setState({
      ...state,
      textImgList: textImgListClone,
    })
    uploadImageModalHide()
  }


  const croppingImageModalShow = (index, imageUrlShow) => {
    setModalState({
      ...modalState,
      croppingImageVisible: true,
      croppingImageIndex: index,
      croppingImageUrlShow: imageUrlShow,
    })
  }


  const croppingImageModalHide = () => {
    setModalState({
      ...modalState,
      croppingImageVisible: false,
      croppingImageIndex: 0,
      croppingImageUrlShow: null,
    })
  }


  const handleCroppingImage = (imageUrlShow) => {
    let textImgListClone = cloneDeep(state.textImgList)
    textImgListClone.splice(modalState.croppingImageIndex, 1, {
      imageUrlShow,
      isCover: 1,
    })
    setState({
      ...state,
      textImgList: textImgListClone,
    })
    croppingImageModalHide()
  }


  const previewArticleModalShow = (previewArticleModalType) => {
    setModalState({
      ...modalState,
      previewArticleVisible: true,
      previewArticleModalType,
    })
  }


  const previewArticleModalHide = () => {
    setModalState({
      ...modalState,
      previewArticleVisible: false,
      previewArticleModalType: 1,
    })
  }


  const preview = () => {
    const imageTextContent = quillRef?.current?.unprivilegedEditor?.getHTML()
    const articleLength = quillRef?.current?.unprivilegedEditor?.getText().replace(/\n/g, '').length
    setState({
      ...state,
      imageTextContent,
      articleLength,
    })
    previewArticleModalShow(1)
  }


  const publish = () => {
    if (!state.imageTitle || !state.imageTitle.trim()) {
      message.error('请输入标题(2-50个字)')
      return
    }
    if (state.imageTitle.trim().length > 50 || state.imageTitle.trim().length < 2) {
      message.error('请输入标题(2-50个字)')
      return
    }
    // if (state.textImgList.length == 0) {
    //   message.error('请上传封面图')
    //   return
    // }
    if (state.coverImageNumber == 3 && state.textImgList.length < 3) {
      message.error('请上传3张封面图')
      return
    }
    const imageTextContent = quillRef?.current?.unprivilegedEditor?.getHTML()
    const articleLength = quillRef?.current?.unprivilegedEditor?.getText().replace(/\n/g, '').length
    const contentJson = JSON.stringify(quillRef?.current?.unprivilegedEditor?.getContents())
    console.log('文章contentJson', contentJson)
    if (articleLength == 0) {
      message.error('请输入正文')
      return
    }
    if (articleLength > 5000) {
      message.error('正文字数不能超过5000字')
      return
    }
    setState({
      ...state,
      imageTextContent,
      articleLength,
      contentJson,
    })
    previewArticleModalShow(2)
  }


  const handlePublish = () => {
    editImgTextInfo()
  }


  const editImgTextInfo = () => {
    clearTimeout(timer)
    setLoadingSave(true)
    dispatch({
      type: 'graphicsText/editImgTextInfo',
      payload: {
        forwardDescribe: null,
        id: id || null,
        imageTextContent: state.imageTextContent,
        contentJson: state.contentJson,
        imageTextId: null,
        imageTitle: state.imageTitle.trim(),
        imageType: 1,
        textImgList: state.textImgList,
        isForward: 0,
        kingdomId: state.kingdomId,
        saveType: 2,
      }
    }).then(res => {
      setLoadingSave(false)
      const { code, content, msg } = res
      if (code == 200 && (content || content == 0)) {
        message.success('提交成功~正在快马加鞭地审核')
        history.replace('/UserInfo')
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch(err => {})
  }


  const editImgTextInfo2 = () => {
    console.log('暂存暂存暂存暂存222222222')
    if (loadingSave2) {
      return
    }
    const articleLength = quillRef?.current?.unprivilegedEditor?.getText().replace(/\n/g, '').length
    const imageTextContent = quillRef?.current?.unprivilegedEditor?.getHTML()
    const contentJson = JSON.stringify(quillRef?.current?.unprivilegedEditor?.getContents())

    if (articleLength > 5000) {
      message.error('正文字数不能超过5000字，保存失败')
      return
    }
    setLoadingSave2(true)
    dispatch({
      type: 'graphicsText/editImgTextInfo',
      payload: {
        forwardDescribe: null,
        id: id || null,
        imageTextContent: imageTextContent,
        contentJson: contentJson,
        imageTextId: null,
        imageTitle: state.imageTitle ? state.imageTitle.trim() : null,
        imageType: 1,
        textImgList: state.textImgList,
        isForward: 0,
        kingdomId: state.kingdomId,
        saveType: 1,
      }
    }).then(res => {
      setLoadingSave2(false)
      const { code, content, msg } = res
      if (code == 200 && (content || content == 0)) {

        if (!id) {
          history.replace(`/CreateGraphicsText/CreateArticle?id=${content}`)
          setRefreshPageState(!refreshPageState)
        }
      } else {
        message.error(msg || '保存失败')
      }
    }).catch(err => {})
  }


  const getUploadVideoOrImageLoading = (isLoading) => {
    if (isLoading) {
      setLoadingUploadVideoOrImage(loadingUploadVideoOrImage + 1)
    } else {
      setLoadingUploadVideoOrImage(loadingUploadVideoOrImage - 1)
    }
  }


  const bottomTipsBarHide = () => {
    setModalState({
      ...modalState,
      bottomTipsBarVisible: false,
    })
  }


  const goBack = () => {
    if (history.length > 1) {
      history.goBack()
    } else {
      history.replace('/')
    }
  }


  const loadingGetCreateAndJoinKingdomList = !!loading.effects['userInfoStore/getCreateAndJoinKingdomList']
  const loadingImgTextInfoUpdateId = !!loading.effects['graphicsText/imgTextInfoUpdateId']
  const loadingEditImgTextInfo = !!loading.effects['graphicsText/editImgTextInfo']

  return (
    <Spin spinning={loadingGetCreateAndJoinKingdomList || loadingImgTextInfoUpdateId} wrapperClassName={styles.spin}>
      <div className={styles.container}>
        <PcHeader/>

        <div className={styles.content} id="content">

          <div className={styles.content_inner}>
            <div className={styles.nav_bar}>
              <i onClick={goBack}></i>
              <span>发布文章</span>
            </div>

            <ToolbarPC
              quillRef={quillRef}
              quillHistoryStack={quillHistoryStack}
              quillFormat={quillFormat}
              getUploadVideoOrImageLoading={getUploadVideoOrImageLoading}
            />

            <div className={styles.wrapper}>
              <div className={styles.title_box}>
                <Input.TextArea
                  placeholder="请输入标题（2-50个字）"
                  autoSize
                  size="large"
                  autoComplete="off"
                  maxLength={50}
                  value={state.imageTitle}
                  onChange={onChangeImageTitle}
                />
                <div className={styles.tips}>
                  {
                    state.imageTitle.trim().length >= 2 ?
                      `${state.imageTitle.trim().length}/50`
                      : <>还需输入<span>{2 - state.imageTitle.trim().length }</span>个字</>
                  }
                </div>
              </div>

              <Spin spinning={false}>
                <div className={styles.editor_box}>
                  <QuillDom
                    ref={quillRef}
                    getQuillHistoryStack={getQuillHistoryStack}
                    getQuillFormat={getQuillFormat}
                    onChangeImageTextContent={onChangeImageTextContent}
                  />
                </div>
              </Spin>

              <div className={styles.form_box}>
                <Form.Item label="封面设置" colon={false}>
                  <Radio.Group value={state.coverImageNumber} onChange={onChangeCoverImageNumber}>
                    <Radio value={1}>单图</Radio>
                    <Radio value={3}>3图</Radio>
                  </Radio.Group>
                </Form.Item>

                <div className={styles.form_cover_img}>
                  <div className={styles.cover_img_box}>
                    {
                      state.textImgList.concat(coverImageListEmpty).map((item, index) => {
                        if (index >= state.coverImageNumber) {
                          return null
                        }
                        if (item.isEmpty) {
                          return (
                            <div key={index} className={styles.upload_btn} onClick={() => uploadImageModalShow(1)}>
                              <PlusOutlined/>
                            </div>
                          )
                        }
                        return (
                          <div key={index} className={styles.cover_img_item} style={{backgroundImage: `url(${item.imageUrlShow})`}}>
                            <div className={styles.cover_img_item_btn_box}>
                              <span onClick={() => {croppingImageModalShow(index, item.imageUrlShow)}}>编辑</span>
                              <span onClick={() => uploadImageModalShow(2, index)}>替换</span>
                            </div>
                          </div>
                        )
                      })
                    }
                    <div className={styles.preview_btn} onClick={previewCoverImageModalShow}>预览</div>
                  </div>
                  <div>优质的封面有利于推荐，格式支持JPEG、PNG</div>
                </div>

                <div id="kingdomId" style={{position: 'relative'}}>
                  <Form.Item label="关联王国" colon={false}>
                    <Select
                      getPopupContainer={() => document.getElementById('kingdomId')}
                      popupClassName={styles.custom_dropdown_render}
                      placeholder={createAndJoinKingdomDataSource.length == 0 ? '当前无可关联王国' : '不关联王国，默认只发布在个人主页'}
                      style={{width: 400}}
                      value={state.kingdomId}
                      listHeight={256}
                      fieldNames={{ label: 'name', value: 'id', options: 'children' }}
                      options={createAndJoinKingdomDataSource}
                      dropdownRender={dropdownRender}
                      open={selectVisible}
                      onDropdownVisibleChange={open => setSelectVisible(open)}
                      allowClear
                      onChange={onChangeKingdomIdByClear}
                    >
                    </Select>
                  </Form.Item>
                </div>
              </div>
            </div>
          </div>

        </div>

        {
          modalState.bottomTipsBarVisible &&
          <div className={styles.bottom_tips_bar}>
            为了维护良好的社区氛围，每篇文章和外链都会经过审核，请耐心等待，我们将尽快审核并发布你的文章。
            <div className={styles.bar_btn_wrap} onClick={bottomTipsBarHide}>
              <CloseOutlined />
            </div>
          </div>
        }

        <div className={styles.footer}>
          <div className={styles.footer_content}>
            <div className={styles.footer_content_left}>
              <span style={{width: 113}}>{loadingSave2 ? '保存中...' : '草稿将自动保存'}</span>
              <span>共{quillRef?.current?.unprivilegedEditor?.getText().replace(/\n/g, '').length}字</span>
            </div>
            <div className={styles.footer_content_right}>
              <Button size="large" onClick={preview}>预览</Button>
              <Button type="primary" size="large" onClick={publish}>预览并提交</Button>
            </div>
          </div>
        </div>

        {
          modalState.uploadImageVisible &&
          <UploadImageModal
            visible={modalState.uploadImageVisible}
            articleTextImgList={modalState.articleTextImgList}
            uploadImageMaxNumber={modalState.uploadImageMaxNumber}
            handleUploadImage={handleUploadImage}
            onCancel={uploadImageModalHide}
          />
        }

        {
          modalState.previewCoverImageVisible &&
          <PreviewCoverImageModal
            visible={modalState.previewCoverImageVisible}
            coverImageNumber={state.coverImageNumber}
            imageTitle={state.imageTitle}
            kingdomName={state.kingdomName}
            textImgList={state.textImgList}
            onCancel={previewCoverImageModalHide}
          />
        }

        {
          modalState.previewArticleVisible &&
          <PreviewArticleModal
            visible={modalState.previewArticleVisible}
            previewArticleModalType={modalState.previewArticleModalType}
            articleLength={state.articleLength}
            imageTitle={state.imageTitle}
            imageTextContent={state.imageTextContent}
            handlePublish={handlePublish}
            loadingSave={loadingSave}
            onCancel={previewArticleModalHide}
          />
        }

        {
          modalState.croppingImageVisible &&
          <CroppingImageModal
            visible={modalState.croppingImageVisible}
            imageUrlShow={modalState.croppingImageUrlShow}
            handleCroppingImage={handleCroppingImage}
            onCancel={croppingImageModalHide}
          />
        }

      </div>
    </Spin>
  )
}

export default connect(({ graphicsText, loading }: any) => ({ graphicsText, loading }))(Index)
