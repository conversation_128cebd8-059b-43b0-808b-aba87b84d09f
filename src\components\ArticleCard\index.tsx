/**
 * @Description: 移动端-文章卡片组件
 */
import React, { useEffect, useState } from 'react'
import { history } from 'umi'
import classNames from 'classnames'
import { stringify } from 'qs'
import { getItemCreateDate, gdpFormat } from '@/utils/utils'
import { Button, Form, Radio, Select } from 'antd'
import { Input, InfiniteScroll, Toast } from 'antd-mobile'
import { PlusOutlined, CloseCircleFilled, CloseOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'
import styles from './index.less'

import Avatar from '@/components/Avatar' // 用户头像组件

interface PropsType {
  style?: object,                                 // 样式
  isMyPages?: any,  // 是否是从主页过来的
  item?:any,        // 文章详情数据
  disabledClick?: boolean,        // 是否禁用点击事件
  pageFrom?: string,        // forwardCard，来自转发卡片，此时标题中显示@用户名
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { style = {}, isMyPages,item, disabledClick, pageFrom } = props
  const [visible, setVisible] = useState(false)
  let {
    createDate,       //: [创建时间] : "2024-01-09 14:53:47"
    createUserId,     //: [创建人id] : 60
    expertsInfo,      //: [专家信息] : null
    forwardDescribe,  //: [转发描述] : null
    gdp,              //: [页面GDP] : 210
    headUrlShow,      //: [用户头像] : null
    id,               //: [主键ID] : 57
    imageTextContent, //: [文章、帖子内容] : null
    imageTitle,       //: [标题] :null
    imageType,        //: [图文类型：1.文章 2.帖子 3.外链 4.空间] : 2
    isExperts,        //: [是否是专家：0:否，1:是] : 0
    isFocus,          //: [0未关注 1已关注] : 0
    isForward,        //: [是否转发：1.转发 0，非转发] : null
    isSpotLike,       //: [是否点赞 1是 0否] : 0
    kingdomId,        //: [关联王国ID] : 1
    kingdomName,      //: [关联王国名称] : "数字化讨论"
    outerChain,       //: [外链地址] : null
    spaceId,          //: [空间ID] :null
    spaceStatus,      //: [空间状态: 1直播中、2预约中、3弹幕轰炸中] : null
    spotLikeCount,    //: [点赞数量] : 0
    spotLikeUserList, //: [点赞用户信息，最多3条] : []
    textImgList,      //: [关联的图片] : null
    topicInfoList,    //: [关联的话题信息] : null
    userName,         //: [用户名称] : "志君"
    status,           // 状态：1.审核通过（已发布） 0.未审核 2.审核未通过 3.草稿
  } = item || {};


  useEffect(() => {
    // console.log('item', item)
  }, [])


  const loadMore = () => {
    console.log('loadMore')
  }

  // 点击卡片
  const goToUrl = (e) => {
    if (disabledClick) { return }
    e.stopPropagation()
    // 点击转发时展示的用户名
    if (e.target && e.target.dataset && e.target.dataset.type == 'user') {
      history.push(`/Expert/ExpertDetails?id=${createUserId}`)
      return
    }
    // 是从我的主页过来的，并且为草稿，需要跳转编辑页
    if (isMyPages && status == 3) {
      return history.push(`/CreateGraphicsText/CreateArticle?${stringify({id: id})}`);
    }
    if (isMyPages && status == 2) {
      Toast.show('审核未通过')
      history.push(`/CreateGraphicsText/CreateArticle?id=${id}`);
      return
    }
    if (status == 0) {
      Toast.show('审核中')
      return
    }
    history.push(`/CreateGraphicsText/ArticleDetails?id=${id}`)
  }

  // 点击王国
  const jumpKingdom = (e) => {
    e.stopPropagation()
    if (window.location.pathname == `/Kingdom/${kingdomId}`) { return; }
    history.push(`/Kingdom/${kingdomId}`)
  }

  return (
    <div className={classNames(styles.container, {
      [styles.vertical]: textImgList && textImgList.length > 1,    // 3图
      [styles.horizontal]: !(textImgList && textImgList.length > 1),
    })} onClick={goToUrl} style={style}>
      {
        textImgList && textImgList.length > 1 ?
          <>
            {/* 3图样式 */}
            <div
              className={styles.title}
              dangerouslySetInnerHTML={{ __html: imageTitle ? `${pageFrom == 'forwardCard' ? `<span data-type="user" class="username_in_title">@${userName}</span>` : ''}${imageTitle}` : '无标题' }}
            >
            </div>

            {/* 封面 */}
            <div className={styles.cover_img_box}>
              {
                textImgList.map((item, index) => {
                  return (
                    <div key={index} className={styles.cover_img_item}>
                      <img className={styles.article_img} src={item.imageUrlShow} alt=""/>
                    </div>
                  )
                })
              }
            </div>

            {
              kingdomName &&
              <div onClick={jumpKingdom} className={styles.kingdom_box}>
                <i></i>
                <span>{kingdomName}</span>
              </div>
            }

            <div className={styles.article_user}>
              <Avatar
                userInfo={{
                  userId: createUserId,
                  name: userName,
                  headUrlShow: headUrlShow,
                }}
                size={16}
              />
              <span className={styles.name}>{userName}</span>
              <span>{getItemCreateDate(createDate)}</span>
              <i className={styles.icon}></i>
              <span>{gdpFormat(gdp)}GDP</span>
            </div>
          </>
          :
          <>
            {/* 1图样式 */}
            <div className={styles.article_left}>
              <div
                className={styles.title}
                dangerouslySetInnerHTML={{ __html: imageTitle ? `${pageFrom == 'forwardCard' ? `<span data-type="user" class="username_in_title">@${userName}</span>` : ''}${imageTitle}` : '无标题' }}
              >
              </div>
              {
                kingdomName &&
                <div onClick={jumpKingdom} className={styles.kingdom_box}>
                  <i></i>
                  <span>{kingdomName}</span>
                </div>
              }

              <div className={styles.article_user}>
                <Avatar
                  userInfo={{
                    userId: createUserId,
                    name: userName,
                    headUrlShow: headUrlShow,
                  }}
                  size={16}
                />

                <span className={styles.name}>{userName}</span>
                <span>{getItemCreateDate(createDate)}</span>
                <i className={styles.icon}></i>
                <span>{gdpFormat(gdp)}GDP</span>
              </div>
            </div>
            {
              textImgList && textImgList.length > 0 &&
              <div className={styles.article_right}>
                <img className={styles.article_img} src={textImgList[0].imageUrlShow} alt=""/>
              </div>
            }
          </>
      }
    </div>
  )
}

export default Index
