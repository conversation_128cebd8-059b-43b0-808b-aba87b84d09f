/**
 * @Description: PC-病例列表头部组件
 * @author: 赵斐
 */
import React, { useState, useEffect, useRef } from 'react';
import { connect ,history} from 'umi';
import { Input } from 'antd';
const { Search } = Input;
import styles from './index.less';
import screenIcon from '@/assets/Expert/screen_icon.png'   // 筛选图标
import noScreenIcon from '@/assets/Expert/no_screen_icon.png'   // 无筛选图标
import pcGobackIcon from '@/assets/GlobalImg/pc_goback.png'
import searchIcon from '@/assets/GlobalImg/search.png'
import {getArrailUrl} from "@/utils/utils"
const Index: React.FC = (props: any) => {
  // 是否嵌套在5i5ya的iframe中
  const isInIframe = (self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya')

  const { cases , dispatch, refreshInterface,showScreenModal , screenHeaderHighlight} = props;
  const [searchVal , setSearchVal] = useState(cases.searchValue)
  // 点击回车调用接口
  const onPressEnter = ()=>{
    dispatch({
      type: "cases/save",
      payload: {
        searchValue:searchVal
      }
    })
    refreshInterface(1,searchVal,cases)

    // 如果在5i5ya中，保存数据到父窗口model
    if (isInIframe) {
      // 发送登录成功的消息
      const postData = {
        dataType: 'saveData',       // 保存数据事件
        pageType: 'CaseResult',
        saveDataObj: {
          searchValue:searchVal
        }
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
    }
  }

  // 搜索数据
  const onChangeFn = (value:string)=>{
    setSearchVal(value)
  }

  // 返回
  const goBack = ()=>{
    history.goBack()
  }
  return (
    <div className={styles.header}>
      <div className={styles.header_title} onClick={()=>{goBack()}}><img className={styles.header_title_icon} src={pcGobackIcon} alt="" />病例学习</div>
      <div className={styles.header_right}>
         <div className={styles.header_screen} onClick={()=>{showScreenModal()}}>
          <img className={styles.screen_icon} src={screenHeaderHighlight?screenIcon:noScreenIcon}/>
          <span className={screenHeaderHighlight?styles.screen_word:styles.no_screen_word}>筛选</span>
        </div>
        <div className={styles.header_search}>
          <Search
            placeholder={'请输入搜索内容'}
            onPressEnter={() => onPressEnter()}
            onChange={(e) => onChangeFn(e.target.value)}
            value={searchVal || ''}
            suffix={<span className={styles.header_search_icon} onClick={() => onPressEnter()}><img src={searchIcon} alt="" /></span>}
          />

        </div>
      </div>
    </div>
  )
}
export default connect(({ cases, loading }: any) => ({ cases, loading }))(Index)
