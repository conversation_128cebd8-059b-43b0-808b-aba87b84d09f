/**
 * @Description: 移动端-下载Frida App页
 */
import React, { useRef, useEffect } from 'react';
import {connect, history} from 'umi'
import { Toast, Modal } from 'antd-mobile'
import styles from './index.less';
import { isIOS, getOperatingEnv, WxAppIdByPublicAccount } from '@/utils/utils';
import AppDownloadBG from '@/assets/AppDownloadBG.png';  // 背景图片
import AppDownloadLogoIcon from '@/assets/AppDownloadLogoIcon.png';  // logo图片
import goBackIcon from '@/assets/GlobalImg/go_back.png';  // 返回上一页icon
import AppDownloadNameIcon from '@/assets/AppDownloadNameIcon.png';   // 名称icon
import AppDownloadWxModalIcon from '@/assets/AppDownloadWxModalIcon.png';   // 微信引导在默认浏览器打开icon
import { getJsapiTicket } from "@/services/userInfo";


const AppDownload: React.FC = (props:any) => {
  const appBodyref = useRef(null);

  // 返回上一页
  const goBack = () => {
    if (history.length > 1) {
      history.goBack()
    } else {
      history.replace('/')
    }
  }

  // 下载APP
  const downloadApp = () => {
    if (isIOS()) {
      window.location.href = `https://apps.apple.com/cn/app/%E5%91%A8%E4%BA%94%E7%89%99%E5%8C%BB/id1508260075`
    } else {
      window.location.href = `https://jwsmedstatic.oss-cn-hangzhou.aliyuncs.com/fileByHand/app-release.apk`
    }
  }

  // 打开APP
  const openApp = () => {
    const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    const { query:{ info } } = history.location || {};
    const message = JSON.parse(decodeURIComponent(info)) || {};
    console.info(message,"message");
    let appUrl = message.type==1?`${window.location.origin.replace('https', 'friday')}/PlanetChatRoom/Live/${message.roomId}?token=${localStorage.getItem('access_token')?localStorage.getItem('access_token'):''}&phone=${UserInfo?.phone?UserInfo?.phone:''}`:`${window.location.origin.replace('https', 'friday')}/PlanetChatRoom/Meet/${message.roomId}?token=${localStorage.getItem('access_token')?localStorage.getItem('access_token'):''}&phone=${UserInfo?.phone?UserInfo?.phone:''}`;
    const start = Date.now();
    let timeout;
    const checkOpen = () => {
      const elapsed = Date.now() - start;
      if (elapsed< 2000) {
        window.location.href = isIOS()?'https://apps.apple.com/cn/app/%E5%91%A8%E4%BA%94%E7%89%99%E5%8C%BB/id1508260075':'https://jwsmedstatic.oss-cn-hangzhou.aliyuncs.com/fileByHand/app-release.apk';
      }
    };
    const handleVisibilityChange=()=> {
      if (document.visibilityState === 'hidden') {
        clearTimeout(timeout);
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      }
    }

    timeout = setTimeout(checkOpen, 1500);
    if (isIOS()) {
      window.location.href = appUrl;
    } else {
      if (navigator.userAgent.indexOf("Firefox") > -1) { //判断是否Firefox浏览器
        window.location.href = appUrl;
        timeout = setTimeout(() => {
          checkOpen();
          clearTimeout(timeout);
        }, 1500);
      }else{
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.src = appUrl;
        document.body.appendChild(iframe);
        timeout = setTimeout(() => {
          document.body.removeChild(iframe);
          checkOpen();
          clearTimeout(timeout);
        }, 1500);
      }
    }
    window.addEventListener('blur', () => {
      clearTimeout(timeout);
    });
    document.addEventListener('visibilitychange', handleVisibilityChange);
  }
  return (
    <div className={styles.app_download_content} ref={appBodyref}  id={'app_download_content'}>
      <div className={styles.app_download_TopBox}>
        <img src={AppDownloadBG} alt="AppDownloadBG" className={styles.app_download_BG}/>
        <img src={AppDownloadLogoIcon} alt="FRIDAY" className={styles.app_download_logo}/>
        {
          getOperatingEnv() == 3 && <img src={goBackIcon} className={styles.app_download_back} onClick={goBack} alt=""/>
        }
      </div>
      <img src={AppDownloadNameIcon} alt="Logo" className={styles.app_download_name}/>
      <p>牙医手边的私人助理</p>
      <div className={styles.app_download_btn}><span onClick={downloadApp}>下载APP</span></div>
      <i onClick={openApp}>已安装app？从此打开</i>
      <Modal
        visible={getOperatingEnv() == 2 ? true : false}
        getContainer={appBodyref.current}
        content={
          <div className={styles.wxTipsBox}>
            <img src={AppDownloadWxModalIcon} alt="AppDownloadWxModalIcon"/>
          </div>
        }
      >
      </Modal>
    </div>
  );
};

export default connect(({loading}: any) => ({loading}))(AppDownload)
