import {
  followAndCheck,
  getCaseInfoByExpertsUserId,
  getExpertsInfo,
  getExpertsList,
  getExpertsQrCode,
  getFilterDict,
  getStarSpaceListBySearchUserId,
  getWordList,
} from '../services/expertAdvice';
import {getExpertsPersonList, personImageTextList,} from "@/services/recommended";

export default {
  namespace: 'expertAdvice',
  state: {
    searchValue: null,   // 搜索值
    checkCity: null,           // 城市
    checkDepSubject: null,  // 学科初始数据
    checkAbilityLevel: null,  // 能力等级初始数据
    checkPostTitle: null,  // 职级初始数据
    tabKey: 1,    // 专家详情tab选中状态
    subTabKey: 1,   // 专家详情二级tab选中状态
  },

  effects: {
    // 首页字典项
    * getFilterDict({payload}: any, {put, call}: any) {
      const response = yield call(getFilterDict, payload);
      return response
    },
    // 专家列表数据
    * getExpertsList({payload}: any, {put, call}: any) {
      const response = yield call(getExpertsList, payload);
      return response
    },
    // 专家搜索关键词
    * getWordList({payload}: any, {put, call}: any) {
      const response = yield call(getWordList, payload);
      return response
    },
    // 专家详情
    * getExpertsInfo({payload}: any, {put, call}: any) {
      const response = yield call(getExpertsInfo, payload);
      return response
    },
    // 通过专家用户ID，获取专家关联的优秀病历信息
    * getCaseInfoByExpertsUserId({payload}: any, {put, call}: any) {
      const response = yield call(getCaseInfoByExpertsUserId, payload);
      return response
    },
    // 关注、取关专家
    * followAndCheck({payload}: any, {put, call}: any) {
      const response = yield call(followAndCheck, payload);
      return response
    },
    // 视频获取专家二维码
    * getExpertsQrCode({payload}: any, {put, call}: any) {
      const response = yield call(getExpertsQrCode, payload);
      return response
    },
    // 获取检索用户的空间数据列表
    * getStarSpaceListBySearchUserId({payload}: any, {put, call}: any) {
      const response = yield call(getStarSpaceListBySearchUserId, payload);
      return response
    },
    // 专家个人主页（图文：文章，帖子 外链 ）
    * personImageTextList({payload}, {call, put}) {
      const response = yield call(personImageTextList, payload);
      return response;
    },
    // 获取个人中心获取图文列表信息
    * getExpertsPersonList({payload}: any, {put, call}: any) {
      const response = yield call(getExpertsPersonList, payload);
      return response
    },
  },

  reducers: {
    // 保存数据
    save(state: any, {payload}: any) {
      return {
        ...state,
        ...payload,
      }
    },
    // 清空数据
    clean(state: any, {payload}: any) {
      return {
        ...state,
        searchValue: null,   // 搜索值
        checkCity: null,           // 城市
        checkDepSubject: null,  // 学科初始数据
        checkAbilityLevel: null,  // 能力等级初始数据
        checkPostTitle: null,  // 职级初始数据
      }
    },
  },
  subscriptions: {
    setup({dispatch, history}: any) {
      return history.listen(({pathname, search}: any) => {
        if (
          pathname.indexOf('/PlanetChatRoom') == -1 &&
          pathname.indexOf('/Case/CaseDetails') == -1 &&
          pathname.indexOf('/Expert/ExpertDetails') == -1 &&
          pathname.indexOf('/ConsultationModule/StartConsultation/') == -1 &&
          pathname.indexOf('/CreateGraphicsText/ArticleDetails') == -1 &&
          pathname.indexOf('/CreateGraphicsText/PostDetails') == -1 &&
          pathname.indexOf('/CreateGraphicsText/ForwardDetails') == -1 &&
          pathname.indexOf('/CreateGraphicsText/CreateArticle') == -1 &&
          pathname.indexOf('/CreateGraphicsText/ExternalLinksDetails') == -1 &&
          pathname.indexOf('/CreateGraphicsText/CreatePost') == -1 &&
          pathname.indexOf('/CreateGraphicsText/CreateExternalLinks') == -1 &&
          pathname.indexOf('/CreateGraphicsText/CreateForward') == -1
        ) {
          // 重置tabKey
          dispatch({
            type: 'save',
            payload: {
              tabKey: 1,
              subTabKey: 1,   // 专家详情二级tab选中状态
            }
          })
        }
        if (pathname.indexOf('/Expert/ExpertResult') == -1 && pathname.indexOf('/Expert/ExpertDetails') == -1) {
          dispatch({
            type: 'clean',
          })
        }
      })
    }
  }
};
