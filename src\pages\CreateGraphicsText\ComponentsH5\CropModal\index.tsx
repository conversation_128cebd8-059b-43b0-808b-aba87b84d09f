import React, { useRef } from 'react'
import Cropper from 'react-cropper';
import request from '@/utils/request'
import { Mask, Toast } from 'antd-mobile';
import 'cropperjs/dist/cropper.css';
import styles from './index.less'

interface PropsType {
  visible: boolean,                    // 是否显示
  imageUrlShow: string,                // 图片地址
  handleCroppingImage: () => {},
  onCancel: () => {},
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { visible, imageUrlShow } = props
  const cropperRef = useRef<HTMLImageElement>(null);       // ref

  // 重置
  const resetImage = () => {
    cropperRef.current?.cropper.reset()
  }

  // 确定
  const ok = () => {
    const cropUrl = cropperRef?.current?.cropper?.getCroppedCanvas().toDataURL();
    uploadImg(cropUrl)
  }

  // 上传图片
  const uploadImg = (cropUrl) => {
    Toast.show({
      icon: 'loading',
      content: '',
      duration: 0,
    })
    const imgFile = dataURLtoFile(cropUrl, 'cropping_image')
    let formData = new FormData()
    formData.append('file',imgFile)
    request('/api/server/base/uploadFile?fileType=23', {
      method: 'POST',
      body: formData,
    }).then(res => {
      Toast.clear()
      const { code, content, msg } = res
      if (code == 200 && content) {
        props.handleCroppingImage(content.fileUrlView)
      } else {
        Toast.show(msg || '数据加载失败')
      }
    })
  }

  // base64转文件
  const dataURLtoFile = (dataurl, fileName) => {
    let arr = dataurl.split(','),
      mime = arr[0].match(/:(.*?);/)[1],
      bstr = atob(arr[1]),
      n = bstr.length,
      u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    const suffix = (mime == 'image/png' ? '.png' : '.jpg')
    return new File([u8arr], fileName + suffix, { type: mime });
  }

  return (
    <Mask className={styles.mask} visible={visible} opacity={1}>
      <div className={styles.container}>
        <Cropper
          ref={cropperRef}
          // style={{ height: 500, width: "100%" }}
          viewMode={1}
          initialAspectRatio={1}
          background={false}
          minCropBoxHeight={50}
          minCropBoxWidth={50}
          zoomOnTouch={false}
          dragMode='none'
          toggleDragModeOnDblclick={false}
          src={imageUrlShow}
        />
      </div>
      <div className={styles.footer}>
        <div className={styles.footer_cancel_btn} onClick={props.onCancel}>取消</div>
        <div className={styles.footer_reduction_btn} onClick={resetImage}>还原</div>
        <div className={styles.footer_ok_btn} onClick={ok}>完成</div>
      </div>
    </Mask>
  )
}

export default Index
