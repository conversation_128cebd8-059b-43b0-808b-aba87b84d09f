import React, { useState, useEffect ,useRef, forwardRef, useImperativeHandle } from 'react';
import { connect } from 'umi';
import { TextArea, Mask, Toast } from 'antd-mobile'
import styles from './CommentInput.less';
import ChatInputBottomBox from '@/pages/ConsultationModule/H5IMComponents/ChatInputBottomBox'// 移动端聊天输入框组件
import Emoji from "@/pages/CreateGraphicsText/Quill/ToolbarMobile/Emoji";

interface PropsType {
  ref?: any; // ref
  post?: any; // 评论
  onClick?: any; //
}

let CommentInput: React.FC<PropsType> = (props: PropsType) => {
  const { refInstance } = props
  const [ isShowCommentInput, setIsShowCommentInput] = useState(false)
  const [ commentInputInfo,setCommentInput] = useState(null)
  const [ textAreaValue,setTextAreaValue ] = useState(null)
  const [ isShowEmoji,setIsShowEmoji ] = useState(null)
  useEffect(()=>{

  },[])

  useEffect(()=>{
    if(!isShowCommentInput){
      setCommentInput(null);
      setIsShowEmoji(false);
    }
  },[isShowCommentInput])

  // 点击评论
  const postComment = (item) => {
    setTextAreaValue(null);
    setCommentInput(item);
    setIsShowCommentInput(true)
  }

  // 获取ref
  useImperativeHandle(refInstance, (item) => {
    return {
      postComment: (item) => {
        // 这里操作的是input自带的focus方法
        postComment(item)
      }
    }
  }, [])

  // 输入框输入
  const textAreaOnChange = (value) => {
    if (!(/^\s*$/.test(value))) {
      setTextAreaValue(value)
    } else {
      setTextAreaValue(null);
      // Toast.show('请输入非空的评论内容')
    }
  }

  // 点击表情
  const itemOnClick = (value,item) => {
    let textAreaValueStr = textAreaValue || '';
    setTextAreaValue(textAreaValueStr + item);
  }

  // 发布
  const post = () => {
    if (!(/^\s*$/.test(textAreaValue)) && textAreaValue!=null && textAreaValue.length>0) {
      props.post({ commentInputInfo:commentInputInfo, value:textAreaValue});
      setTextAreaValue(null);
      setIsShowEmoji(null);
    } else {
      Toast.show('请输入非空的评论内容')
    }
    // setIsShowCommentInput(false)
  }

  // 关闭弹窗
  const closeMask = () => {
    setIsShowCommentInput(false)
  }


  return(
    <>
      <Mask
        visible={isShowCommentInput}
        opacity={0}
        onMaskClick={closeMask}
        style={{
          '--z-index': 997
        }}
      />
      <div className={styles.container}>

        {
          isShowCommentInput ?
            <div>
              <div className={styles.true_input_box}>
                <div className={styles.textarea_box}>
                  <TextArea
                    value={textAreaValue}
                    placeholder="友善评论..."
                    autoSize={{maxRows: 6, minRows: 1}}
                    rows={1}
                    autoFocus
                    onChange={textAreaOnChange}
                  />
                  <i
                    className={styles.textarea_Icon}
                    onClick={()=>{ setIsShowEmoji(!isShowEmoji) }}
                  ></i>
                </div>
                <div className={styles.btn_box}>
                  {/*<i></i>*/}
                  <span onClick={post}>发布</span>
                </div>
              </div>
              {!!isShowEmoji &&
                <Emoji itemOnClick={itemOnClick}/>
              }
            </div>
            :
            <div>
              <div className={styles.false_input_box} onClick={()=>{
                props.onClick && props.onClick()
              }}>
                <div className={styles.wrap}>
                  <div className={styles.left}>
                    <i></i>
                    <span>友善评论...</span>
                  </div>
                  <i className={styles.right}></i>
                </div>
              </div>
            </div>
        }

      </div>
    </>
  )
}
CommentInput = connect(({ graphicsText, loading }: any) => ({ graphicsText, loading }))(CommentInput)
export default forwardRef((props, ref) => <CommentInput {...props} refInstance={ref}/>)
