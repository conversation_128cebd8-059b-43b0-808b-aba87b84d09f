/**
 * @Description: PC-专家列表页面
 * @author: 赵斐
 */
import React, { useState, useEffect, useRef } from 'react';
import { connect, history } from 'umi';
import InfiniteScroll from 'react-infinite-scroller';
import { randomColor, processNames } from '@/utils/utils'
import { Spin, message } from 'antd'
import { throttle } from 'lodash';
import styles from './index.less';
// 公共导航组件
import PcHeader from '@/componentsByPc/PcHeader'
// pc-专家列表头部
import Header from './Header';
// 数据加载异常
import LoadingException from '@/components/LoadingException'
const initState = {
  total: 0,
  dataSource: [],

}

const initStatePage = {
  pageNum: 1,      // 当前第几页
  hasMore: true,   // 加载更多
  loadMore: false, // 阻止滚动连续加载
}
const Index: React.FC = (props: any) => {
  const { dispatch, loading, expertAdvice } = props;
  const { searchValue } = expertAdvice;     // 搜索值
  const listRef = useRef<any>(null);
  const [interfaceStatus, setInterfaceStatus] = useState(2);  // 1 数据加载失败 2 暂无数据 3 筛选暂无数据
  const [tabData, setTabData] = useState<any>(null)           // 学科分类数据
  const [state, setState] = useState(initState)               // 专家列表数据
  const [statePage, setStatePage] = useState(initStatePage)   // 当前分页
  const {
    total,
    dataSource,
  } = state

  const {
    pageNum,
    hasMore,
    loadMore,
  } = statePage || {}

  useEffect(() => {
    getFilterDict()
    getExpertsList(1, searchValue, expertAdvice)
  }, [])

  // 按学科找专家字典数据
  const getFilterDict = () => {
    dispatch({
      type: "expertAdvice/getFilterDict",
      payload: {}
    }).then((res: any) => {
      if (res && res.code == 200) {
        const { content } = res || {}
        const { depSubject, abilityLevel, postTitle, city } = content || {}
        abilityLevel.unshift({ code: 0, name: "全部", iconName: null, children: null })
        postTitle.unshift({ code: 0, name: "全部", iconName: null, children: null })
        city.unshift("全国")
        depSubject.unshift({ code: 0, name: "全部", iconName: "", children: null })
        setTabData({ ...content })
      }
    }).catch((err: String) => {
      console.log(err)
    })
  }

  /**
   * 获取专家列表数据
   * @param pageNum    当前第几页
   * @param searchKey  搜索值
   * @param screenObj  筛选条件数据
   */
  const getExpertsList = (pageNum: number = 1, searchKey: string = "", screenObj: any = {}) => {
    const { checkCity, checkDepSubject, checkAbilityLevel, checkPostTitle } = screenObj || {}
    dispatch({
      type: "expertAdvice/getExpertsList",
      payload: {
        pageNum,
        pageSize: 30,
        city: checkCity != "全国" ? checkCity : null,							  // 所属机构城市
        depSubjectDict: checkDepSubject != 0 ? checkDepSubject : null, // 科室字典
        abilityLevelDict: checkAbilityLevel != 0 ? checkAbilityLevel : null,		// 能力等级字典
        postTitleDict: checkPostTitle != 0 ? checkPostTitle : null,      // 职称字典
        searchKey,          // 搜索关键字
      }
    }).then((res: any) => {
      if (res && res.code == 200) {
        const { content } = res || {};
        const { total, resultList } = content || {};
        let data = pageNum == 1 ? [] : dataSource;
        data = data.concat(resultList);
        const hasMore = data.length !== total;
        if (searchKey || checkCity || checkDepSubject || checkAbilityLevel || checkPostTitle) {
          setInterfaceStatus(3)
        } else {
          setInterfaceStatus(3)
        }
        if (Array.isArray(data) && data.length == 0) {
          setState({
            ...state,
            dataSource: [],
            total: 0,

          })

          return
        }
        setState({
          ...state,
          dataSource: [...data],
          total,

        })
        setStatePage({
          ...statePage,
          loadMore: false,
          hasMore,
          pageNum,
        })
      } else {
        setInterfaceStatus(1)
      }
    }).catch((err: String) => {
      console.log(err)
    })
  }

  /**
   * 刷新数据
   * @param pageNum    当前页
   * @param searchKey  搜索值
   * @param screenObj  筛选值
   */
  const refreshInterface = (pageNum: number, searchKey: string, screenObj: any) => {
    listRef.current.scrollTop = 0
    setStatePage({
      ...statePage,
      loadMore: true,
      hasMore: false
    })
    getExpertsList(pageNum, searchKey, screenObj)
  }
  // 滚动加载分页
  let handleInfiniteOnLoad = () => {
    if (dataSource.length > total - 1) {
      message.warning('已加载完毕');
      setStatePage({
        ...statePage,
        loadMore: false,
        hasMore: false
      })
      return;
    }
    const pages = pageNum + 1;
    setStatePage({
      ...statePage,
      loadMore: true,
    })
    getExpertsList(pages, searchValue, expertAdvice)
  }
  handleInfiniteOnLoad = throttle(handleInfiniteOnLoad, 100);

  /**
 * 点击卡片跳转专家详情
 * @param id    专家id
 */
  const clickJumpExpertDetails = (id: number) => {
    const UerInfo = JSON.parse(localStorage.getItem('userInfo')) || {};
    history.push(`/Expert/ExpertDetails?id=${id}&shareUserId=${UerInfo?.friUserId}`)
  }

  const load = !!loading.effects['expertAdvice/getExpertsList'] ||   // 专家列表接口loading
    !!loading.effects['expertAdvice/getFilterDict']       // 学科字典接口loading
  return (
    <Spin spinning={load}>
      <div className={styles.container}>
        <PcHeader />
        <div className={styles.wrap}>
          <div className={styles.wrap_header}>
            <Header data={tabData} refreshInterface={refreshInterface} />
          </div>
          <div className={styles.content} ref={listRef}>
            {
              dataSource.length ?
                <InfiniteScroll
                  id="qwer"
                  initialLoad={false}
                  pageStart={0}
                  loadMore={handleInfiniteOnLoad}
                  hasMore={!loadMore && hasMore}
                  useWindow={false}
                  threshold={50}
                >
                  <div className={styles.table_title}>
                    <div>头像</div>
                    <div>医生姓名</div>
                    <div>职称</div>
                    <div>学科等级</div>
                    <div>所属单位</div>
                    <div>简介</div>
                    <div>操作</div>
                  </div>
                  <div className={styles.content_list}>
                    {
                      dataSource.map((item: any, index) => {
                        return (
                          <div className={styles.content_detail} key={index} id={`stored${index}`}>
                            <div>
                              {
                                item.imagePhotoPathShow ? <img src={item.imagePhotoPathShow} alt="" /> :
                                  <div className={styles.no_doctor_pic} style={{ background: randomColor(item.expertsUserId) }}>{processNames(item.name)}</div>
                              }
                            </div>
                            <div>{item.name}</div>
                            <div>{item.postTitleDictName}</div>
                            <div><span>{item.depSubjectDictName}{item.abilityLevelDictName ? "·" : null}{item.abilityLevelDictName}</span></div>
                            <div>{item.organizationName}</div>
                            <div>{item.intro}</div>
                            <div onClick={() => { clickJumpExpertDetails(item.expertsUserId) }}>问同行</div>
                          </div>
                        )
                      })
                    }</div></InfiniteScroll> : <div>
                  <LoadingException exceptionStyle={{ paddingTop: 220 }} interfaceStatus={interfaceStatus} />
                </div>
            }
          </div>
        </div>
      </div>
    </Spin>
  )
}
export default connect(({ expertAdvice, loading }: any) => ({ expertAdvice, loading }))(Index)
