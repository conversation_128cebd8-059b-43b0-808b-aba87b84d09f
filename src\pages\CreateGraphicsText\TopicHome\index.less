.wrap_padding {
  width: 100%;
  height:44px;
  background: #F5F6F8;
}
.wrap {
  width: 100%;
  height: calc(100vh - 44px);
  padding-top: 0px;
  box-sizing: border-box;
  background: #F5F6F8;
  overflow-y: auto;
}

.head_wrap {
  width: 100%;
  padding: 12px;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  margin-bottom: 8px;

  .left_box {
    .title {
      font-size: 20px;
      font-weight: 500;
      color: #000000;
      line-height: 23px;
      margin-bottom: 4px;
    }

    .text_info {
      font-size: 13px;
      font-weight: 400;
      color: #333333;
      line-height: 15px;
      display: flex;
      align-items: center;
    }
  }

  .right_box {
    flex-shrink: 0;
    white-space: nowrap;
    font-size: 15px;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 21px;
    background: #0095FF;
    border-radius: 15px;
    padding: 4px 12px;
    box-sizing: border-box;
  }
}

// 数据列表
.list_wrap {
  width: 100%;
  .item_wrap {
    padding: 16px;
    margin-bottom: 10px;
    background: #fff;
  }
}
