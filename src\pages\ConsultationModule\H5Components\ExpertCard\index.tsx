/**
 * @Description: 移动端专家（用户）信息卡片
 * @author: 赵斐
 */
import React from 'react';
import { randomColor , processNames } from '@/utils/utils'
import styles from './index.less'

interface PropsType {
  data: any,   // 专家、用户信息数据
}
const Index: React.FC<PropsType> = (props: PropsType) => {
  const { data } = props;
  const {
    h5BaseUserDto,      // 专家、用户信息
  } = data || {}
  const {
    id,                  // 用户ID
    name,                // 姓名
    depSubjectDictName,  // 科室字典名称
    abilityLevelDictName,// 能力等级字典名称
    postTitleDictName,   // 职称字典名称
    organizationName,    // 机构名称
    headUrlShow,         // 头像展示路径
  } = h5BaseUserDto || {}
  return (
    <div className={styles.wrap}>
      <div className={styles.doctor_img}>
        <div className={styles.doctor_img}>
            {
              headUrlShow ? <img className={styles.doctor_pic} src={headUrlShow} alt='头像' /> :
                <div className={styles.no_doctor_pic} style={{ background: randomColor(id) }}>{processNames(name)}</div>
            }
          </div>
      </div>
      <div className={styles.doctor_content}>
          <p className={styles.doctor_content_top}>
          <span className={styles.doctor_name}>{name}</span>
          <span className={styles.doctor_line}>|</span>
          <span className={styles.doctor_rank}>{postTitleDictName}</span>
          <span className={styles.doctor_grade}>{depSubjectDictName}·{abilityLevelDictName}</span>
        </p>
        <p className={styles.doctor_content_bottom}>{organizationName}</p>
      </div>
    </div>
  )
}
export default Index
