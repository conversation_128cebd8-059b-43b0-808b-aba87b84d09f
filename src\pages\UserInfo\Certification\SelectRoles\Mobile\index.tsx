/**
 * @Description: 实名认证-选择身份（H5）
 */
import React, { useState, useEffect } from 'react';
import { connect, history } from 'umi';
import { message, Spin } from 'antd';
import styles from './index.less';

// 图片icon
import right_arrow from '@/assets/GlobalImg/right_arrow.png' // 右箭头

import NavBar from '@/components/NavBar'; // 头部返回
import { getOperatingEnv } from '@/utils/utils'

const Index: React.FC = (props: any) => {
  const { loading, dispatch } = props

  const [identityTypeDict, setIdentityTypeDict] = useState([]) // 身份类型字典

  useEffect(() => {
    getIdentityTypeDict()
  }, [])

  // 获取认证的身份字典-实名认证版本
  const getIdentityTypeDict = () => {
    dispatch({
      type: 'userInfoStore/getIdentityTypeDict',
      payload: {}
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200) {
        setIdentityTypeDict(content || [])
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch(err => {})
  }

  // 点击身份类型
  const onClickIdentityTypeItem = (value) => {
    history.push(`/UserInfo/Certification/EditInformation?dictId=${value}`)
  }

  // loading
  const loadingGetIdentityTypeDict = !!loading.effects['userInfoStore/getIdentityTypeDict']

  return (
    <Spin wrapperClassName={styles.spin} spinning={loadingGetIdentityTypeDict}>
      {/* 导航栏 */}
      <NavBar title={'选择身份'}></NavBar>
      {/* 内容 */}
      <div className={styles.container}>
        {/* 灰条 */}
        <div className={styles.gray_bar}></div>
        {
          identityTypeDict.map(item => {
            return (
              <div key={item.dictId} className={styles.role_item_wrap} onClick={() => onClickIdentityTypeItem(item.dictId)}>
                <img src={item.iconPath} width={48} height={48} alt=""/>
                <div className={styles.role_item_name}>{item.dictName}</div>
                <img src={right_arrow} width={16} height={16} alt=""/>
              </div>
            )
          })
        }
      </div>
    </Spin>
  )
}

export default connect(({ userInfoStore, loading }: any) => ({ userInfoStore, loading}))(Index)
