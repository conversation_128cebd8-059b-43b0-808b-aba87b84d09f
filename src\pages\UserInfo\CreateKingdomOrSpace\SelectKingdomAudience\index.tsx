/**
 * @Description: 选择可见王国
 */
import React, { useState, useEffect, useCallback } from 'react'
import { connect } from 'umi'
import styles from './index.less'
import GoBackIcon from '@/assets/GlobalImg/go_back.png' // 返回图片
import { Spin } from 'antd';
import { Toast } from 'antd-mobile';
import classNames from 'classnames';
import { processNames, randomColor } from '@/utils/utils'; // 默认头像文案，背景色处理
import noDataImg from '@/assets/GlobalImg/no_data.png';

const Index: React.FC = (props: any) => {
  const { goBack, userInfoStore, dispatch, loading } = props || {};
  const { selectedKingdomAudience, kingdomListArr } = userInfoStore || {};
  const [kingSpaceTab, setKingSpaceTab] = useState(1); // 我创建/我加入的王国tab
  const [createKingdomList, setCreateKingdomList] = useState([]); // 创建的王国数据
  const [joinKingdomList, setJoinKingdomList] = useState([]); // 加入的王国数据
  const [thisSelectKingdomList, setThisSelectKingdomList] = useState([]); // 勾选中的王国id数组

  // 初始化赋值state变量勾选中的值
  useEffect(() => {
    setThisSelectKingdomList(selectedKingdomAudience);
  }, [selectedKingdomAudience]);

  useEffect(() => {
    getInitDate()
  },[])

  // 获取创建王国或加入王国相关数据
  const getInitDate = useCallback(() => {
    setCreateKingdomList(kingdomListArr && kingdomListArr[1]);
    setJoinKingdomList(kingdomListArr && kingdomListArr[2]);
  }, [kingdomListArr])

  // 王国勾选事件
  const checkKingdomList = useCallback((item) => {
    const isSelected = thisSelectKingdomList.find(it => it === item);
    setThisSelectKingdomList((kingdom: any) => {
      return isSelected ? kingdom.filter(it => it !== item) : [...kingdom, item];
    });
  },[thisSelectKingdomList]);

  // 确定按钮
  const submitFn = () => {
    console.log(thisSelectKingdomList, '这是啥？？')
    // 未选择王国时提示
    if (thisSelectKingdomList.length == 0) {
      Toast.show('请选择王国')
      return
    }
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        spectatorType: 1, // 观众类型 0默认所有人 1指定王国成员可见 2指定企业品牌用户可见
        enterpriseText: `王国成员可见，已选${thisSelectKingdomList.length}个王国`,
        selectedKingdomAudience: thisSelectKingdomList, // 选择王国成员
        allViewersState: false, // 所有人员
        enterpriseUserData: [], // 选择可见企业/品牌用户，当前企业下的机构数据
        enterpriseUserTab: 0, // 选择可见企业/品牌用户，页面tab
        enterpriseUserSelectData: [], // 选择可见企业/品牌用户，选择后的数据
        spaceTypeId: null, // 空间类型 1专家讲课 2指导 3复杂病例讨论
        spaceTypeName: null, // 空间类型名称
      }
    })

    goBack(1)
  }

  const getCreateAndJoinKingdomListLoading = !!loading.effects['userInfoStore/getCreateAndJoinKingdomList'];
  const renderKingDomList: any[] = kingSpaceTab == 1 ? createKingdomList : joinKingdomList; // 根据tab切换，来展示加入或创建王国相应的列表

  return (
    <div className={styles.container}>
      <div className={styles.title_box}>
        <div className={styles.title_btn} onClick={()=>goBack(7)}>
          <img src={GoBackIcon} width={12} height={24} alt=""/>
        </div>
        <div className={styles.title}>选择可见王国</div>
      </div>
      <div className={styles.wrap}>
        <div className={styles.tabs_box}>
          <div className={classNames({[styles.tabs_item]: true, [styles.checked]: kingSpaceTab == 1})} onClick={()=>{setKingSpaceTab(1)}}>我创建的</div>
          <div className={classNames({[styles.tabs_item]: true, [styles.checked]: kingSpaceTab == 2})} onClick={()=>{setKingSpaceTab(2)}}>我加入的</div>
        </div>
        <Spin spinning={getCreateAndJoinKingdomListLoading}>
          <div className={styles.data_box}>
            {
              renderKingDomList && renderKingDomList.length ?
              renderKingDomList.map(item =>
              <div className={styles.data_item} key={item.id}>
                <div className={styles.avatar}>
                  {item.kingdomCoverUrlShow || item.kingImgUrlShow ?
                    <img src={item.kingdomCoverUrlShow || item.kingImgUrlShow} alt="" /> :
                    <div className={styles.no_comment_head} style={{background:randomColor(item.wxUserId)}}><span>{processNames(item?.kingName)}</span></div>
                  }
                </div>
                <div className={styles.data_name}>{item.name}</div>
                <div className={classNames({
                    [styles.data_btn]: true,
                    [styles.checked]: thisSelectKingdomList.find(it => it === item.id)
                  })}
                  onClick={() => checkKingdomList(item.id)}
                >{thisSelectKingdomList.find(it => it === item.id) ? '已选择' : '选择'}</div>
              </div>) :
              <div className={styles.nodata}>
                <img src={noDataImg} alt="" />
                <div className={styles.empty_title}>{kingSpaceTab == 1 ? '您还没有创建王国～' : '您还没有加入王国～'}</div>
              </div>
            }
          </div>
        </Spin>
      </div>
      <div className={styles.fixed_box}>
        <div className={styles.btn_box}>
          <div className={styles.btn} onClick={submitFn}>确定</div>
        </div>
      </div>
    </div>
  )
}

export default connect(({ userInfoStore, loading }: any) => ({userInfoStore, loading}))(Index)
