.container {
  height: 100%;
  background: #EEF3F9;
  display: flex;
  flex-wrap: nowrap;
  flex-direction: column;
  align-items: center;
  .content {
    flex: 1;
    width: 100%;
    overflow-y: auto;
    .content_inner {
      max-width: 1228px;
      min-height: 100%;
      padding: 16px 0;
      margin: 0 auto;
      display: flex;
      flex-wrap: nowrap;
      flex-direction: column;
    }
  }
}

.header {
  width: 100%;
  flex-shrink: 0;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  height: 48px;
  .header_icon {
    width: 40px;
    height: 40px;
    background: url("../../../../assets/GlobalImg/pc_goback.png") no-repeat center;
    background-size: 20px 20px;
    margin-right: 8px;
    cursor: pointer;
  }
  .header_title {
    font-size: 18px;
    color: #000;
    font-weight: 600;
    line-height: 34px;
  }
}
.box {
  width: 100%;
  flex: 1;
  background: #fff;
  border-radius: 8px;
  padding-bottom: 16px;
}

.method {
  display: flex;
  justify-content: center;
  padding-top: 80px;
  margin-bottom: 24px;
  .block + .block {
    margin-left: 24px;
  }
  .block {
    width: 260px;
    border-radius: 8px;
    background: #F8F9FC;
    position: relative;
    padding-top: 28px;
    padding-bottom: 26px;
    .method_sign {
      position: absolute;
      top: 0;
      right: 0;
      background: #E0F2FF;
      border-radius: 0 8px 0 8px;
      width: 57px;
      height: 25px;
      line-height: 25px;
      text-align: center;
      font-size: 12px;
      color: #0095FF;
    }
    .method_icon {
      width: 54px;
      height: 54px;
      margin: 0 auto 14px;
      &.method_icon_1 {
        background: url("../../../../assets/Consultation/Pc/image_text_consultation.png") no-repeat center;
        background-size: 100% 100%;
      }
      &.method_icon_2 {
        background: url("../../../../assets/Consultation/Pc/video_consultation.png") no-repeat center;
        background-size: 100% 100%;
      }
    }
    .method_title {
      font-size: 16px;
      color: #000;
      font-weight: 500;
      line-height: 22px;
      text-align: center;
      margin-bottom: 12px;
    }
    .method_text {
      padding: 0 20px;
      text-align: center;
      font-size: 12px;
      color: #999;
      line-height: 17px;
      min-height: 34px;
      margin-bottom: 12px;
    }
    .method_price1 {
      display: flex;
      justify-content: center;
      align-items: baseline;
      color: #FF5F57;
      line-height: 24px;
      .price {
        font-size: 20px;
        font-weight: bold;
      }
      .unit {
        font-size: 12px;
      }
    }
    .method_price2 {
      font-size: 12px;
      color: #999;
      min-height: 17px;
      line-height: 17px;
      text-align: center;
      text-decoration: line-through;
      margin-top: 4px;
    }
    .method_btn1 {
      height: 28px;
      border-radius: 2px;
      font-size: 14px;
      color: #fff;
      margin: 20px auto 16px;
      display: block;
      padding: 0 23px;
    }
    .method_btn2 {
      font-size: 12px;
      color: #0095FF;
      height: 17px;
      line-height: 17px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      :global {
        .anticon {
          font-size: 10px;
          margin-right: 2px;
        }
      }
    }
  }
}

.bottom {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 14px;
  :global {
    .anticon {
      margin-right: 4px;
    }
  }
  .bottom_text {
    height: 20px;
    line-height: 22px;
  }
  .highlight {
    color: #FF5F57;
  }
}
