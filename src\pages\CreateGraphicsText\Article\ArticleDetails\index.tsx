import React, { useEffect, useState, useRef } from 'react'
import { connect, history, useAliveController } from 'umi'
import { getIsIniPhoneAndWeixin, gdpFormat } from '@/utils/utils'
import { Spin } from 'antd'
import { Toast } from 'antd-mobile'
import styles from './index.less'
import NoDataRender from '@/components/NoDataRender'
import NavBar from '@/components/NavBar'
import MoreOperate from '@/components/MoreOperate'
import UserCardByImageText from '@/components/UserCardByImageText'
import CommentList from '@/pages/CreateGraphicsText/ComponentsH5/CommentList'
import CommentFooter from '@/pages/CreateGraphicsText/ComponentsH5/CommentFooter'
import KingdomCard from '@/pages/CreateGraphicsText/ComponentsH5/KingdomCard'
import UserCardByNavBar from '@/pages/CreateGraphicsText/ComponentsH5/UserCardByNavBar'
import { saveCommentsOrReply } from '@/services/recommended'

const Index: React.FC = (props: any) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
  const { id } = history.location.query;
  const { dispatch } = props;

  const [detailsState, setDetailsState] = useState<any>(null)        // 详情数据
  const [navBarToggle, setNavBarToggle] = useState(false)            // 导航状态
  const [loadingImgTextInfoById, setLoadingImgTextInfoById] = useState(false)  // loading
  const [pageLoadStatus, setPageLoadStatus] = useState(null)

  const refContainer = useRef(null)
  const refUserCard = useRef(null)
  const commentListRef = useRef(null);
  const { clear } = useAliveController()

  const {
    imageTitle,                        // 标题
    kingdomInfo,                       // 王国信息
    createUserId,                      // 创建人id
    userName,                          // 用户名称
    headUrlShow,                       // 头像
    isExperts,                         // 是否是专家
    operateDateDescs,                  // 创建时间
    imageTextContent,                  // 内容
    gdp,                               // GDP
    isFocus,                           // 是否关注
    expertsInfo,                       // 专家信息
    status,                            // 状态
  } = detailsState || {};

  // 滚动事件
  useEffect(() => {
    document.getElementById('container') && document.getElementById('container').addEventListener('scroll', handleScroll)

    return () => {
      document.getElementById('container') && document.getElementById('container').removeEventListener('scroll', handleScroll)
    }
  }, [])

  useEffect(() => {
    imgTextInfoById()
  }, [])

  // 滚动事件
  const handleScroll = () => {
    const scrollTop = refContainer.current.scrollTop + 44
    const offset = refUserCard.current.offsetHeight + refUserCard.current.offsetTop
    if (scrollTop >= offset) {
      setNavBarToggle(true)
    } else {
      setNavBarToggle(false)
    }
  }

  // 编辑图文获取原数据
  const imgTextInfoById = (isLocalUpdate = false) => {
    if (!isLocalUpdate) {
      setLoadingImgTextInfoById(true)
    }
    dispatch({
      type: 'graphicsText/imgTextInfoById',
      payload: {
        imageTextId: id,                                   // 图文主ID  修改时存在
      }
    }).then(res => {
      setLoadingImgTextInfoById(false)
      const { code, content, msg } = res
      if (code == 200 && content) {
        setDetailsState(content)
        setPageLoadStatus(1)
      } else {
        setPageLoadStatus(2)
        Toast.show(msg || '数据加载失败')
      }
    }).catch(err => {
      setPageLoadStatus(2)
    })
  }

  // 关注或取消关注回调，刷新数据
  const handleFollowAndCheck = () => {
    imgTextInfoById(true)
  }

  // 图文点击事件
  const onClickImageTextContent = (e) => {
    console.log('点击图文内容', e.target, e)
    if (e.target && e.target.dataset && e.target.dataset.type == 'topic') {
      history.push(`/CreateGraphicsText/TopicHome?topicId=${e.target.dataset.id}`)
      return
    }
    if (e.target && e.target.parentNode && e.target.parentNode.dataset && e.target.parentNode.dataset.type == 'topic') {
      history.push(`/CreateGraphicsText/TopicHome?topicId=${e.target.parentNode.dataset.id}`)
      return
    }
  }

  // 提交
  const onPost = async ({ commentInputInfo,  value}) => {
    if(commentInputInfo && value) {
      let params = {
        imageTextId: commentInputInfo.id,                     //  number 非必须  上级图文ID
        imageTextType: commentInputInfo.imageType,            //	number 非必须  上级图文类型
        commentsContent: value,                               //	string 非必须  上级评论/回复
        commentsType: 0,
      }
      const data = await saveCommentsOrReply(params);
      const { code, content } = data || {}
      if (code == 200) {
        Toast.show('评论成功!')
        commentListRef?.current?.commentsList()
      }else {
        Toast.show('评论失败!')
      }
    }
  }

  // 返回
  const handleDeleteOrLow = async () => {
    await clear()

    if (history.length > 1) {
      history.goBack()
    } else {
      history.replace('/')
    }
  }

  // 更新评论数据
  const updateCommentFooter = (commentsCount) => {
    setDetailsState(prevState => {
      return {
        ...prevState,
        commentsCount
      }
    })
  }

  return (
    <Spin spinning={loadingImgTextInfoById} wrapperClassName={styles.spin}>
      {/* 滚动后导航条 */}
      {
        navBarToggle ?
          <UserCardByNavBar
            headUrlShow={headUrlShow}
            userName={userName}
            createUserId={createUserId}
            isExperts={isExperts}
            operateDateDescs={operateDateDescs}
            isFocus={isFocus}
            expertsInfo={expertsInfo}
            handleFollowAndCheck={handleFollowAndCheck}
            id={id}
            imageType={1}
            status={status}
            handleDeleteOrLow={handleDeleteOrLow}
          />
          :
          <NavBar
            title="文章详情"
            bordered
            RightRender={
              () => UserInfo.friUserId == createUserId ? <MoreOperate id={id} imageType={1} status={status} handleDeleteOrLow={handleDeleteOrLow}/>
                : null
            }
          />
      }
      <div id="container" className={styles.container} ref={refContainer} style={getIsIniPhoneAndWeixin() ? { paddingBottom: '86px' } : {}}>
        {
          pageLoadStatus == 1 ?
            <>
              <div className={styles.article_title}>{imageTitle}</div>
              <div ref={refUserCard} className={styles.user_card_wrap}>
                <UserCardByImageText
                  headUrlShow={headUrlShow}
                  userName={userName}
                  createUserId={createUserId}
                  isExperts={isExperts}
                  operateDateDescs={operateDateDescs}
                  isFocus={isFocus}
                  expertsInfo={expertsInfo}
                  handleFollowAndCheck={handleFollowAndCheck}
                  isShowMoreOperate={false}
                />
              </div>
              <div className={styles.image_text_content_wrap}>
                <div className="ql-editor" onClick={onClickImageTextContent} dangerouslySetInnerHTML={{__html: imageTextContent}}></div>
              </div>
              {
                kingdomInfo &&
                <div className={styles.kingdom_wrap}>
                  <KingdomCard kingdomInfo={kingdomInfo}/>
                </div>
              }
              <div className={styles.gdp_wrap}>
                <i></i>
                <span>{gdpFormat(gdp)}GDP</span>
              </div>
              <CommentList
                onRef={commentListRef}
                imageTextId={id}
                updateCommentFooter={updateCommentFooter}
              />
              <CommentFooter
                imageTextId={id}
                imgTextInfo={detailsState}
                onPost={onPost}
              />
            </>
            : pageLoadStatus == 2 ?
            <div className={styles.no_data_wrap}>
              <NoDataRender style={{marginTop: 0}}/>
            </div>
            : null
        }
      </div>
    </Spin>
  )
}

export default connect(({ loading }: any) => ({ loading }))(Index)

