/**
 * @Description: 移动端添加客服小忆微信弹窗
 * @author: 赵斐
 */
import React from 'react';
import { Popup, Toast } from 'antd-mobile'
import styles from './index.less'

interface PropsType {
  visible: boolean,       // 客服小忆微信弹窗状态
  onCancel: () => void,   // 取消回调
  url:string,             // 客服小忆微信二维码地址
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { visible, onCancel ,url } = props;

  // 点击确认并关闭弹窗
  const onConfirm = () => {
    Toast.show("长按图片保存二维码")
  }

  return (
    <div>
      <Popup
        className={styles.wrap}
        visible={visible}
        onMaskClick={() => { onCancel() }}
        bodyStyle={{ height: '406px' }}   // 436
      >
        <p className={styles.title}>添加客服小忆微信</p>
        <div className={styles.content}>
          <img src={url} alt="" />
        </div>
        <div className={styles.tips}>联系客服小忆更改会议时间</div>
        <div className={styles.footer}>
          <p className={styles.footer_btn} onClick={() => { onConfirm() }}>保存二维码</p>
        </div>
      </Popup>
    </div>
  )
}
export default Index
