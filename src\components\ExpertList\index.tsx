import styles from './index.less';
import classNames from 'classnames'
import { history } from 'umi';
import avatarIcon from '@/assets/GlobalImg/default_head_picture.png';
import { randomColor , processNames } from '@/utils/utils'
type propsType = {
  dataSource?: any; // 数据
  tipsModalStatus?: boolean; // 默认不传 传 true 能力等级出提示弹窗
  isHideContent?: boolean; // 默认不传 传 true 表示不展示简历和问专家（关注列表 不需要）
  isShowFollow?: boolean; // 默认不传 传 true 表示展示关注按钮（首页搜索结果页，需要）
  style?: any;                         // 样式
  onFollowBtnClick?: any,
}

const ExpertList = (props: propsType) => {
  const {
    dataSource,
    tipsModalStatus, // 弹出弹框
    isHideContent,
    style = {},
    isShowFollow,
    onFollowBtnClick = () => {},
  } = props;

  /**
   * 点击卡片跳转专家详情
   * @param id    专家id
   */
  const clickJumpExpertDetails = (id:number)=>{
    history.push(`/Expert/ExpertDetails?id=${id}`)
  }

  // 点击关注按钮
  const followBtnClick = (e, item) => {
    e.stopPropagation()
    e.preventDefault()
    onFollowBtnClick({
      expertsUserId: item.wxUserId,
      isFocus: item.isFocus == 0 ? 1 : 0,
    })
  }

  return (
    <div className={styles.expert_content} style={style}>
      {
        dataSource && dataSource.map((item:any, idx:number) => {
          return (
            <div className={styles.expert_doctor} key={idx} onClick={()=>{clickJumpExpertDetails(item.expertsUserId)}}>
              <div className={styles.doctor_img}>
                {
                  item.imagePhotoPathShow?<img className={styles.doctor_pic} src={item.imagePhotoPathShow?item.imagePhotoPathShow:avatarIcon} alt='头像'/>:
                  <div className={styles.no_doctor_pic} style={{background:randomColor(item.expertsUserId)}}>{processNames(item.name)}</div>
                }
              </div>
              <div className={styles.doctor_detail}>
                <div className={styles.doctor_detail_content}>
                  <div className={styles.doctor_detail_head}>
                    <span className={styles.doctor_detail_title}>{item.name}</span>
                    {
                      item.postTitleDictName?<div className={styles.doctor_vertical}></div>:null
                    }
                    {
                      item.postTitleDictName? <span className={styles.doctor_detail_attending}>{item.postTitleDictName}</span>:null
                    }

                    {
                      tipsModalStatus && idx==0?<div className={styles.doctor_detail_major_highlight}>
                        <span className={styles.major_highlight}>{item.depSubjectDictName}{item.abilityLevelDictName?"·":null}{item.abilityLevelDictName}</span>
                      </div>:<>{item.depSubjectDictName && item.abilityLevelDictName ? <span className={styles.doctor_detail_major}>{item.depSubjectDictName}{item.abilityLevelDictName?"·":null}{item.abilityLevelDictName}</span> : null}</>
                    }
                  </div>
                  {
                    item.organizationName ? <div className={styles.organization_name}>{item.organizationName}</div> : null
                  }
                  {
                    isHideContent ? '' :
                      <>
                        <span className={styles.doctor_detail_desc}>简介：{item.intro}</span>
                        <div className={styles.ask_experts}><span>问同行</span></div>
                      </>
                  }
                </div>

                {/* 关注按钮 */}
                {
                  isShowFollow &&
                  <div className={styles.doctor_follow_btn_box}>
                    <div className={classNames(styles.follow_btn, {
                      [styles.checked]: item.isFocus == 1
                    })} onClick={(e) => followBtnClick(e, item)}>
                      {item.isFocus == 0 && <i className={styles.follow_btn_icon}></i>}
                      <div className={styles.follow_btn_text}>{item.isFocus == 1 ? '已关注' : '关注'}</div>
                    </div>
                  </div>
                }
              </div>
            </div>
          )
        })
      }
    </div>
  )
}

export default ExpertList;
