/**
 * @Description: PC-创建王国
 * @author: 赵斐
 */
import React, { useState, useEffect } from 'react';
import { connect, history } from 'umi';
import { Spin, Form, Input, Radio, Upload, Button, message, Select } from 'antd'
import { throttle } from 'lodash';
import { stringify } from 'qs';
import pcGobackIcon from '@/assets/GlobalImg/pc_goback.png'
import searchIcon from '@/assets/GlobalImg/search_icon_2.png'
import selectedIcon from '@/assets/GlobalImg/selected_icon.png'
import { getOperatingEnv } from '@/utils/utils';
const uploadImg = 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/big_add.png'; // 上传图片图标
import styles from './index.less';
// 公共导航组件
import PcHeader from '@/componentsByPc/PcHeader'
import CroppingImageModal from '@/pages/CreateGraphicsText/ComponentsPC/CroppingImageModal';
const { TextArea } = Input;
const { Option } = Select;

const initUploadObj = {
  imgLoading: false,   // 上传loading
  fileUrl: null        // 上传图片地址
}

const Index: React.FC = (props: any) => {
  const { dispatch } = props
  const [form] = Form.useForm();
  const UerInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  const [uploadObj, setUploadObj] = useState(initUploadObj)           // 上传所需参数
  const [radioValue, setRadioValue] = useState(1)                     // 有无封面默认选中
  const [isSuperAccount, setIsSuperAccount] = useState(false)          // 是否是超级账号
  const [kingdomDataSource, setKingdomDataSource] = useState<any>([]) // 指定国王数据
  const [checkedKingdomId, setCheckedKingdomId] = useState(null)
  const [visible, setVisible] = useState(false);                          // 是否展示裁剪弹框

  const {
    imgLoading,
    fileUrl
  } = uploadObj || {}

  useEffect(() => {
    checkSuperAccount()
  }, [])

  // 是否为超级账号
  const checkSuperAccount = () => {
    dispatch({
      type: 'userInfoStore/checkSuperAccount',
    }).then((res: any) => {
      if (res && res.code == 200) {
        setIsSuperAccount(res.content)
        getListData('')
        return;
      } else {
        return message.error({ content: res.msg || '数据加载失败' })
      }
    }).catch((err: any) => {
      console.log(err)
    })
  }

  // 获取国王列表
  const getListData = (val: string) => {
    dispatch({
      type: 'userInfoStore/searchUserListByQueryKey',
      payload: {
        queryKey: val && val.trim(),
      }
    }).then((res: any) => {
      if (res && res.code == 200) {
        if (res.content && res.content.length) {
          setKingdomDataSource(res.content)
        } else {
          setKingdomDataSource([]);
        }
      }
    })
  }

  /**
   * 选中王国数据
   * @param value 选中当前数据
   */
  const onCheckKingdomFun = (value) => {
    console.log(value)
    setCheckedKingdomId(value)
  }


  // 返回
  const goBack = () => {
    history.goBack()
  }
  // 上传图片headers
  const getHeaders = () => {
    // 获取环境类型，1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用   5 FRIDAY app中使用
    const env = getOperatingEnv()

    // FRIDAY APP中本地存储值：access_token、client、phone、user_name、wxuserId、userInfo
    return {// token
      access_token: localStorage.getItem('access_token') || '',
      username: env == '5' ? localStorage.getItem('user_name') : localStorage.getItem('vxOpenIdCipherText') ? localStorage.getItem('vxOpenIdCipherText') : UerInfo?.phone,
      client: env == '5' ? localStorage.getItem('client') : 'WX',
      type: env === '1' ? '' : '1', // h5 传1
    }
  }

  // 上传事件
  const onChangeByUpload = (info: any) => {
    let fileList;
    fileList = info && info.fileList;
    if (info.file.status === 'uploading') {
      setUploadObj({
        ...uploadObj,
        imgLoading: true
      })
    }

    // 判断当前上传已被拦截
    if (info && !info.file.status) { fileList = null; return; }
    if (info && info.file.status === 'error') {
      setUploadObj({
        ...uploadObj,
        imgLoading: false
      })
      message.error('上传失败');
      fileList = null;
      return
    }
    // 上传结束
    if (info && info.file.status === 'done') {
      setUploadObj({
        ...uploadObj,
        imgLoading: false
      })
      if (info && info.file.response && info.file.response.code != 200) {
        message.error(info.file.response.msg ? info.file.response.msg : '上传失败');
        fileList = [];
        return
      }
    }

    if (info.file.type === "image/png" || info.file.type === "image/jpeg") {
      if (info.file.response && info.file.response.code == 200 && info.file.response.content) {

        setUploadObj({
          imgLoading: false,
          fileUrl: info.file.response.content
        })
        console.log(info.file.response.content)
      }
    }
  }

  // 上传校验规则
  const beforeUpload = (file: { size?: any; type?: any; }) => {
    const isSize = file.size / 1024 / 1024 < 15;
    if (!isSize) {
      message.warning('超过15M限制，不允许上传~');
      return false;
    }

    const { name: fileName } = file || {}
    // 添加对文件后缀名的限制
    const suffix = fileName.substring(fileName.lastIndexOf('.') + 1)
    const isJpgOrPng = file.type === 'image/jpg' || file.type === 'image/jpeg' || file.type === 'image/png';
    // 文件后缀名可以大写,所以需要添加大写后缀名的判断
    const isSuffixByJpgOrPng = (
      suffix === 'jpg'
      || suffix === 'JPG'
      || suffix === 'jpeg'
      || suffix === 'JPEG'
      || suffix === 'png'
      || suffix === 'PNG'
    )
    if (!isJpgOrPng || !isSuffixByJpgOrPng) {
      message.warning({ content: '只能上传JPG、JPEG、PNG格式的图片~' });
      return false;
    }
    return isJpgOrPng;
  };

  // 解决上传初始化 fileList 报错问题
  const normFile = (e: any) => {  //如果是typescript, 那么参数写成 e: any
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };

  /**
   * 有无封面选中
   * @param e
   */
  const onChangeRadio = (e: any) => {
    // 无封面选中时, 清空重置
    if(e.target.value == 1) {
      setUploadObj({
        imgLoading: false,
        fileUrl: null,
      })
    }
    setRadioValue(e.target.value);
    setUploadObj(initUploadObj)
  };

  /**
   * 校验王国名称是否重复
   */
  const titleRule = (_rule: any, value: string, callback: any) => {
    dispatch({
      type: 'userInfoStore/checkKingdomName',
      payload: {
        name: value && value.trim(), // 王国名称
        // id: KingdomId, // 王国Id-编辑时必传
      }
    }).then((res: any) => {
      if(res && res.code == 200) {
        if (res.content) {
          callback('已被抢先创建,请试试别的名字吧!')
        } else {
          callback()
        }
      }
    }).catch((err:any) => {
      console.log(err)
      callback()
    })
  }

  const onChangeSearch = (event)=>{
    event.preventDefault()
    event.stopPropagation()
    console.log(event.target.value)
    getListData(event.target.value)
  }

  // 编辑图片，弹出裁剪弹框
  const editUrlBtnFn = () => {
    setVisible(true)
  }

  // 裁剪弹框关闭事件
  const cropCancelFn = () => {
    setVisible(false)
  }

  // 封面剪裁，确定回调
  const handleCroppingImage = (imageUrl, imageUrlShow) => {
      setUploadObj({
        ...uploadObj,
        fileUrl: {
          fileUrl: imageUrl,
          fileUrlView:imageUrlShow,
        },
      })
    cropCancelFn()
  }


  // 确认创建
  const submitBtn = () => {
    // imgLoading 为true时，说明正在上传封面图，不能点击
    if(imgLoading) return;

    form.validateFields().then((err) => {
      const formVal = form.getFieldsValue()

      const kingInfo = formVal?.designated_kingdom || ''; // 处理国王的值，传入的值为：'国王id-国王名称'，需要处理以下
      const kingInfoVal = kingInfo.replace('-', '|').split('|'); // 处理成 [国王id, 国王名称]

      dispatch({
        type: 'userInfoStore/addKingdom',
        payload: {
          wxUserId: kingInfoVal && kingInfoVal[0] || UerInfo?.friUserId, // 国王ID
          kingName: kingInfoVal && kingInfoVal[1] || UerInfo?.name, // 国王名称
          name: formVal?.title || null, // 王国名称
          descriptions: formVal?.desc || null, // 王国描述
          kingdomCoverUrl: fileUrl && fileUrl.fileUrl || null, // 王国封面图
          updateUserId: UerInfo?.friUserId, // 操作人用户ID
        }
      }).then((res:any) => {
        if(res && res.code == 200) {
          history.push('/UserInfo');
        } else {
          return message.error({content: res.msg || '数据加载失败'})
        }
      }).catch((err:any) => {
        console.log(err)
      })
    }).catch(err => {
    })
  }

  return (
    <Spin spinning={false}>
      <div className={styles.container}>
        <PcHeader />
        <div className={styles.wrap}>
          <div className={styles.header}>
            <div className={styles.header_title} onClick={() => { goBack() }}><img className={styles.header_title_icon} src={pcGobackIcon} alt="" />创建王国</div>
          </div>
          <div className={styles.content}>
            <Form form={form}>
              <div className={styles.form_input}>
                <Form.Item
                  label=""
                  name="title"
                  rules={[
                    { required: true, message: '请输入王国名称' },
                    { min: 2, message: '请输入王国名称（2-30个字）' },
                    { max: 30, message: '请输入王国名称（2-30个字）' },
                    {pattern: /^[~\s!！@#$￥%^…&*()_—+{}：:‘’“”《》？"|<>?`\-=[\];',.。，；（）、a-zA-Z0-9\u4e00-\u9fa5/]+$/, message: '王国名称不能包含特殊字符'},
                    {
                      validator: titleRule,   // awite
                    },
                  ]}
                >
                  <Input bordered={false} autoComplete="off" placeholder='请输入王国名称（2-30个字）' />
                </Form.Item>
              </div>

              <div className={styles.form_text_area}>
                <Form.Item
                  label=""
                  name="desc"
                  rules={[
                    {pattern: /^[~\s!！@#$￥%^…&*()_—+{}：:‘’“”《》？"|<>?`\-=[\];',.。，；（）、a-zA-Z0-9\u4e00-\u9fa5/]+$/, message: '王国介绍不能包含特殊字符'},
                  ]}
                >
                  <TextArea bordered={false} style={{ resize: 'none', height: '190px' }} showCount maxLength={100} placeholder='王国介绍：可以写一些你希望王国内分享的话题方向，吸引志同道合的人成为国民，参与分享讨论，让王国更加壮大~' />
                </Form.Item>
              </div>
              {
                isSuperAccount ? <div className={styles.form_select} id='select'>
                  <Form.Item
                    label="指定国王"
                    initialValue={checkedKingdomId}
                    name="designated_kingdom"
                  >
                    <Select
                      getPopupContainer={() => document.getElementById('select')}
                      style={{ width: 383 }}
                      showSearch={false}
                      // defaultOpen={true}
                      placeholder='请选择国王'
                      onChange={onCheckKingdomFun}
                      dropdownRender={(menu) => (
                        <div className={styles.kingdom_wrap}>
                          <div className={styles.kingdom_header}>
                            <img src={searchIcon} alt="" />
                            <Input bordered={false} placeholder='输入关键字搜索' onChange={(e)=>{onChangeSearch(e)}}/>
                          </div>
                          {menu}
                        </div>
                      )}
                      optionLabelProp='title'
                    >
                      {
                        Array.isArray(kingdomDataSource) && kingdomDataSource.length ? <>
                          {
                            kingdomDataSource.map((item: { id: number, name: string, phone: string }, index: number) => (
                              <Option key={index} value={`${item.id}-${item.name}`} title={item.name} >
                                <div className={styles.kingdom_li} key={index}>
                                  <span className={item.id == checkedKingdomId ? styles.check_kingdom_name : styles.kingdom_name}>{item.name}</span>
                                  <span className={item.id == checkedKingdomId ? styles.check_kingdom_phone : styles.kingdom_phone}>{item.phone}</span>
                                  {
                                    item.id == checkedKingdomId && <span className={styles.kingdom_img}><img src={selectedIcon} alt="icon" /></span>
                                  }

                                </div>
                              </Option>
                            ))
                          }
                        </> : null
                      }
                    </Select>
                  </Form.Item>
                </div> : null
              }

              <div className={styles.form_radio} style={!isSuperAccount ? { marginTop: 24 } : {}}>
                <Form.Item
                  label="封面设置"
                  initialValue={1}
                  name="file"
                  rules={[
                    { required: true, message: '请选择封面设置' },
                  ]}
                >
                  <Radio.Group onChange={onChangeRadio}>
                    <Radio value={1}>无封面</Radio>
                    <Radio value={2}>有封面</Radio>
                  </Radio.Group>
                </Form.Item>
              </div>
              {
                radioValue == 2 ?
                <div className={styles.upload_kingdom_wrap}>
                  {
                    fileUrl ?
                    <div className={styles.upload_box}>
                      {/* 展示图片 */}
                      <Spin spinning={imgLoading}>
                        <img className={styles.upload_img} src={fileUrl?.fileUrlView} alt="" />

                        <div className={styles.edit_btn_box}>
                          <div onClick={editUrlBtnFn}>编辑</div>

                          <Upload
                            headers={getHeaders()}
                            accept="image/*"
                            action={`/api/server/base/uploadFile?${stringify({ fileType: 16, userId: UerInfo?.friUserId })}`}
                            name="file"
                            // listType="picture-card"
                            // className={styles.edit_head_picture}
                            onChange={(info) => onChangeByUpload(info)}
                            onRemove={() => { }}
                            beforeUpload={beforeUpload}
                            showUploadList={false}
                          >
                            <div>替换</div>
                          </Upload>
                        </div>
                      </Spin>
                    </div> :
                    <Form.Item
                      label=""
                      name="fileList"
                      validateFirst={true}
                      valuePropName="fileList"
                      preserve={false}
                      getValueFromEvent={normFile}
                      rules={[
                        { required: true, message: '请上传封面图片' },
                      ]}
                    >
                      <Upload
                        headers={getHeaders()}
                        accept="image/*"
                        action={`/api/server/base/uploadFile?${stringify({ fileType: 16, userId: UerInfo?.friUserId })}`}
                        listType="picture-card"
                        // className={styles.edit_head_picture}
                        onChange={(info) => onChangeByUpload(info)}
                        onRemove={() => { }}
                        beforeUpload={beforeUpload}
                        showUploadList={false}
                      >
                        <Spin spinning={imgLoading}>
                          <div className={styles.upload_box}>
                           <img className={styles.init_upload_img} src={uploadImg} alt="" />
                          </div>
                        </Spin>
                      </Upload>
                    </Form.Item>
                  }
                  <div className={styles.tips}>优质的封面有利于推荐，格式支持JPEG、PNG，建议尺寸：200*200</div>
                </div> : null
              }

            </Form>
          </div>
        </div>
        <div className={styles.footer}>
          <div className={styles.footer_content}>
            <Button type="primary" onClick={() => { submitBtn() }}>确认创建</Button>
          </div>
        </div>
      </div>

      {/* 图片裁剪弹窗 */}
      <CroppingImageModal
        visible={visible}
        onCancel={cropCancelFn}
        imageUrlShow={uploadObj?.fileUrl?.fileUrlView}
        handleCroppingImage={handleCroppingImage}
        uploadFileType={16}
      />
    </Spin>
  )
}
export default connect(({ userInfoStore, loading }: any) => ({ userInfoStore, loading }))(Index)
