.wrap {
  // max-width: 750px;
  // margin: 0 auto;
  height: 100%;
  background: #F5F6F8;
  padding-top: 98px;
}

.input_wrap {
  width: 100%;
  padding: 7px 12px;
  background: #fff;
  position: fixed;
  top: 44px;
  left: 0;
  z-index: 99;
  right: 0;
  &.input_wrap_pc {
    max-width: 750px;
    margin: 0 auto;
  }
  .input_box {
    width: 100%;
    display: flex;
    align-items: center;
    background: #F5F5F5;
    border-radius: 20px;
    height: 40px;
    padding: 10px 12px;
    box-sizing: border-box;
    color: #ccc;
    font-size: 14px;
    font-weight: 400;

    img {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }
  }
}

.discipline_wrap {
  width: 100%;
  height: auto;
  background: #fff;
  margin-top: 8px;
  padding-top: 12px;
  margin-bottom: 8px;

  .discipline_title {
    font-size: 20px;
    font-weight: 500;
    color: #000;
    padding-left: 12px;
    margin-bottom: 16px;

    span {
      color: #0095FF;
    }
  }

  .discipline_list {
    display: flex;
    flex-wrap: wrap;

    .discipline_list_item {
      width: 25%;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 16px;

      img {
        width: 32px;
        height: 32px;
        margin-bottom: 4px;
      }

      span {
        font-size: 13px;
        font-weight: 400;
        color: #666666;
        line-height: 18px;
      }
    }
  }
}

.expert_list_wrap {
  width: 100%;
  background: #fff;
  padding-top: 12px;
  height: calc(100vh - 314px);
  .expert_list_title {
    font-size: 20px;
    font-weight: 500;
    color: #000;
    line-height: 28px;
    padding-left: 12px;
  }
}

.expert_lists_item {
  width: 100%;
  background: #fff;


}

.tips_modal_mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 90;
  background: rgba(0,0,0,0.6);
  width: 100%;
  height: 100vh;
}
