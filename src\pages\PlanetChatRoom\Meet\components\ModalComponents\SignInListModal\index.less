.popup_container {
  :global {
    .adm-popup-body {
      border-radius: 16PX 16PX 0 0;
    }
  }
}

.container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: nowrap;
  flex-direction: column;
}

// 头部
.header_line {
  flex-shrink: 0;
  width: 100%;
  height: 28PX;
  display: flex;
  align-items: center;
  justify-content: center;
  .header_line_bar {
    width: 48PX;
    height: 4PX;
    background: #D0D4D7;
    border-radius: 4PX;
  }
}

.header_title {
  flex-shrink: 0;
  width: 100%;
  font-size: 17PX;
  color: #000;
  font-weight: 500;
  line-height: 24PX;
  text-align: center;
  padding-bottom: 12PX;
}

.sign_in_list_wrap {
  flex: 1;
  overflow-y: auto;
  padding: 0 16PX;
  .sign_in_item {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    margin-bottom: 12PX;
    .left {
      flex: 1;
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      padding-right: 30PX;
      overflow: hidden;
      .avatar_wrap {
        flex-shrink: 0;
        width: 24PX;
        height: 24PX;
        margin-right: 12PX;
      }
      .name {
        flex: 1;
        font-size: 14PX;
        color: #000;
        padding-right: 24PX;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .phone {
        font-size: 14PX;
        color: #000;
        flex-shrink: 0;
        white-space: nowrap;
      }
    }
    .right {
      flex-shrink: 0;
      white-space: nowrap;
      font-size: 14PX;
      color: #999;
    }
  }
}
