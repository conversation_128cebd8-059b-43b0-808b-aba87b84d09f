import React, { useState, useEffect, lazy, Suspense } from 'react'
import { getOperatingEnv, useDebounce } from '@/utils/utils'
import { connect, history, KeepAlive } from 'umi'
import CreateForwardContent from './CreateForwardContent'

const Index: React.FC = () => {
  useEffect(() => {

  }, [])

  return (
    <KeepAlive
      saveScrollPosition="screen"
      id={history.location.pathname}
      when={() => {
        return history.action == 'PUSH';
      }}
    >
      <CreateForwardContent/>
    </KeepAlive>
  )
}

export default Index
