import { message, ConfigProvider } from 'antd';
import type { RunTimeLayoutConfig } from 'umi';
import * as Sentry from "@sentry/react";
import { Integrations } from '@sentry/tracing';
import Footer from '@/components/Footer';
import RightContent from '@/components/RightContent';
import type { Settings as LayoutSettings } from '@ant-design/pro-components';
import PageLoading from '@/components/PageLoading'


/* ---------------设置sentry---------------- */
if (process.env.NODE_ENV === 'production') {
  Sentry.init({
    dsn: 'https://<EMAIL>/18',
    environment: process.env.NODE_ENV,
    integrations: [new Integrations.BrowserTracing(),new Sentry.Replay()],
    tracesSampleRate: 1.0,
    replaysSessionSampleRate: 0.1,
    replaysOnErrorSampleRate: 1.0,
    tracePropagationTargets:['https://dhealth.friday.tech/api']
  });
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  Sentry.configureScope((scope) => {
    scope.setUser({
      id: UserInfo?.id ? UserInfo?.id : UserInfo?.friUserId,
      username: UserInfo?.name,
      phone: UserInfo?.phone,
    });
  });
}



/** 获取用户信息比较慢的时候会展示一个 loading */
export const initialStateConfig = {
  loading: <PageLoading />,
};

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * 初始化定义设置
 * */
export async function getInitialState(): Promise<{
  settings?: Partial<LayoutSettings>;
  loading?: boolean;
}> {
  return null;
}

export const dva = {
  config: {
    onError(e: Error) {
      message.error(e.message, 3);
    },
  },
};

// ProLayout 支持的api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({ initialState, setInitialState }) => {
  return {
    rightContentRender: () => <RightContent />,
    disableContentMargin: false,
    footerRender: () => <Footer />,
    onPageChange: () => {
      //  可以根据当前登录信息确认是否拦截操作
    },
    menuHeaderRender: undefined,
    // 自定义 403 页面
    // unAccessible: <div>unAccessible</div>,
    // 增加一个 loading 的状态
    childrenRender: (children, props) => {
      // if (initialState?.loading) return <PageLoading />;
      return (
        <ConfigProvider autoInsertSpaceInButton={false}>
          {children}
        </ConfigProvider>
      );
    },
    ...initialState?.settings,
  };
};
