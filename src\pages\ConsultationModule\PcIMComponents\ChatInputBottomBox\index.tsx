/**
 * @Description: PC聊天底部输入框组件
 * @author: 赵斐
 */
import React, { useState, useEffect } from 'react';
import { Input, Popover, message } from 'antd';
import { emojiMap, emojiName } from '@/emoticon/index'
import emojiIcon from '@/assets/Consultation/Pc/emoji_icon.png'
import styles from './index.less'
import { onSendMessage, localDealNotSendMessage, getFileURL, getVideoBase64 ,cloudCustomUserInfoData } from '@/utils/im-index'
interface PropsType {
  imGroupId: any,                 // 群组ID
  timObj: any,                    // Im 对象
  refreshPage: any,               // 刷新页面数方法
  isDoctor: number,               // 是否是专家
  stateImList: any,
  UploadSchedule:any
}
interface Emoji {
  emojiName: string;
  url: string;
}
const initState = {
  isKeyboard: 1,   //  1 输入框 2 语音
  emojiData: [],   // 表情包数据
}

const emojiUrl = 'https://static.jwsmed.com/public/3M/SmallerProject/assets/'

const { TextArea } = Input;

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {
    imGroupId,
    timObj,
    refreshPage,
    isDoctor,  // 当前人是否是专家：0:否，1:是
    stateImList,
    UploadSchedule
  } = props;

  const [state, setState] = useState(initState) // 表情包数据
  const [open, setOpen] = useState(false); // 表情浮窗状态
  const [stateInput, setStateInput] = useState("")  // 输入框数据
  const {
    emojiData,
  } = state

  // 渲染表情展示
  useEffect(() => {
    let arr = []
    for (let i = 0; i < emojiName.length; i++) {
      arr.push({
        emojiName: emojiName[i],
        url: emojiUrl + emojiMap[emojiName[i]],
      });
    }
    setState({
      ...state,
      emojiData: arr
    })
  }, [])

  /**
   * 点击表情，展示到输入里面（呈现文字）
   * @param val  表情包文字
   */
  const onClickEmojiFun = (val: string) => {
    let value = stateInput + val
    setStateInput(value)
  }

  /**
   * 文本输入框
   * @param value   输入框值
   * @returns 
   */
  const onChangeInput = (value: string) => {
    if (value.length >= 300) {
      return
    }
    setStateInput(value)
  }

  /**
   * 回车获取输入框值
   * @param e 
   */
  const onEnter = (e: any) => {
    let userInfoStr = cloudCustomUserInfoData()
    let msgSeq = 1
    if (Array.isArray(stateImList) && stateImList.length) {
      msgSeq = stateImList[stateImList.length - 1].msgSeq + 1
    }
    if (e.shiftKey && e.keyCode === 13) {
      return
    }
    if (e.keyCode === 13) {
      e.preventDefault();
      let value = e.target.value
      if (value.trim()) {
        if (value.length >= 300) {
          message.error("不超过300字符")
          return
        }
        let data = {
          msgSeq,
          desc: stateInput,
        }
        localDealNotSendMessage(1, data, refreshPage,userInfoStr)
        setTimeout(()=>{
          onSendMessage(1, imGroupId, stateInput, refreshPage, timObj,userInfoStr)
          setStateInput("")
        },0)

      }
    }

  }

  
  // 聊天图片上传事件
  const photoFileChange = (e: any) => {
    let userInfoStr = cloudCustomUserInfoData()
    let msgSeq = 1
    if (Array.isArray(stateImList) && stateImList.length) {
      msgSeq = stateImList[stateImList.length - 1].msgSeq + 1
    }
    if (!e.target.files || !e.target.files[0]) return;
    
    let myFileObj = new FileReader();
    const file = e.target.files[0];
    let isJpgOrPng = file.type === 'image/jpg' || file.type === 'image/jpeg' || file.type === 'image/png';
    const isSize = file.size / 1024 / 1024 < 15; // 图片大小
    if (!isSize) {
      message.error('超过15M限制，不允许上传~');
      e.target.value = ''
      return;
    }
    if (!isJpgOrPng) {
      message.error('只能上传JPG 、JPEG  、PNG 格式的图片~');
      e.target.value = ''
      return;
    }

    myFileObj.readAsDataURL(file); // 异步操作， IO操作
    // 4.利用文件阅读器将文件展示到前端页面，修改是src属性
    // 5. 等待文件阅读器加载完毕  myFileObj.result---图片路径
    myFileObj.onload = function () {
      console.log(myFileObj.result)
      localDealNotSendMessage(2, {
        msgSeq,
        desc: myFileObj.result,
      }, refreshPage,userInfoStr)
    }
    onSendMessage(2, imGroupId, file, refreshPage, timObj,userInfoStr,UploadSchedule)
    let inputElement = document.getElementById("uploadPhotoInput");
    inputElement && inputElement.blur();
    // 上传成功之后清除数据
    e.target.value = ''
  }
  
  // 聊天视频上传事件
  const videoFileChange = (e: any) => {
    let userInfoStr = cloudCustomUserInfoData()
    let msgSeq = 1
    if (Array.isArray(stateImList) && stateImList.length) {
      msgSeq = stateImList[stateImList.length - 1].msgSeq + 1
    }
    let inputElement = document.getElementById("uploadVideoInput");
    if (!e.target.files || !e.target.files[0]) return;
    const file = e.target.files[0];
    const fileType = file.type == 'video/mp4'; // 视频格式
    const oversize = file.size / 1024 / 1024 / 1024 > 2; // 视频大小
    // 判断上传视频是否大于2G或者上传文件是否为mp4格式的，不是则提示
    if (oversize || !fileType) {
      message.error("请上传格式为MP4，大小2G以内的视频");
      e.target.value = ''; // 解决同一个视频重复上传后，导致不再提示
      return;
    }
    // IM回调未成功之前使用此方法展示数据
    getFirstVideoImg(file).then(res => {
      localDealNotSendMessage(4, {
        msgSeq,
        msgContent:null,
        thumbUrlShow: res,
      }, refreshPage,userInfoStr)
    })
    
    // 调用IM发送视频
    onSendMessage(4, imGroupId, file, refreshPage, timObj,userInfoStr,UploadSchedule)
    inputElement && inputElement.blur();
    // 上传成功之后清除数据
    e.target.value = ''
  }
  
  /**
   * 获取视频封面图
   * @param file  视频file
   * @returns 
   */
  const getFirstVideoImg = async (file) => {
    // 使用
    const objUrl = await getFileURL(file as File)
    const objBase = await getVideoBase64(objUrl)
    return objBase
  }

  // 打开表情浮窗
  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
  };
  // 表情包内容
  const emojiContent = () => {
    return (
      <div className={styles.emoji_wrap}>
        {
          <div className={styles.emoji_content}>
            {
              emojiData.map((item: Emoji, index: number) => {
                return (
                  <span onClick={() => { onClickEmojiFun(item.emojiName) }} className={styles.emoji_icon} key={index}><img src={item.url} alt={item.emojiName} /></span>
                )
              })
            }

          </div>
        }
      </div>
    )
  }

  return (
    <div className={styles.wrap}>
      <div className={styles.header} id='header'>
        <Popover
          placement="topLeft"
          getPopupContainer={() => document.getElementById('header')}
          overlayClassName={styles.header_popver}
          content={emojiContent()}
          trigger="click"
          open={open}
          onOpenChange={handleOpenChange}
        >
          <span className={styles.header_emoji}><img src={emojiIcon} alt="icon" /></span>
        </Popover>


        <div className={styles.upload_photo}>
          <input
            type="file"
            name="uploadPhotoInput"
            id="uploadPhotoInput"
            accept='image/*'
            className={styles.upload_photo_input}
            onChange={photoFileChange}
          />
        </div>
        <div className={styles.upload_video}>
          <input
            type="file"
            name="uploadVideoInput"
            id="uploadVideoInput"
            accept='video/mp4'
            className={styles.upload_video_input}
            onChange={videoFileChange}
          />
          <canvas id="thumbnailCanvas"></canvas>
        </div>
      </div>
      <div className={styles.content}>
        <TextArea
          style={{ resize: 'none' }}
          rows={3}
          placeholder={isDoctor == 1 ? "在此输入文字进行回复" : "在此输入文字向专家提问"}
          maxLength={300}
          minLength={1}
          value={stateInput}
          onChange={(e) => { onChangeInput(e.target.value) }}
          onPressEnter={(e: any) => { onEnter(e) }}
        />
      </div>
    </div>
  )
}
export default Index
