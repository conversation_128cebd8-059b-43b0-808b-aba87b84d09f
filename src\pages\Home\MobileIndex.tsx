/**
 * @Description: 管理端组件配置生成的页面
 */
import React, { useState, useEffect, lazy, Suspense } from 'react'
import { history, connect } from 'umi'
import classNames from 'classnames'
import { parse } from 'qs'
import { getOperatingEnv, WxAppIdByPublicAccount } from '@/utils/utils'
import { getNewImageNumber } from './utils'
import { Spin } from 'antd'
import { Toast } from 'antd-mobile'
import styles from './MobileIndex.less'
import { getJsapiTicket } from "@/services/userInfo";  // 微信jsapiTicket
import {Helmet} from "react-helmet"; // 用于添加动态title

const ImageView = lazy(() => import('@/pages/Home/View/ImageView'))                        // 图片
const ImageTextView = lazy(() => import('@/pages/Home/View/ImageTextView'))                // 图文
const ActivityView = lazy(() => import('@/pages/Home/View/ActivityView'))                  // 活动
const SearchView = lazy(() => import('@/pages/Home/View/SearchView'))                      // 搜索框
const ClassifyGuideView = lazy(() => import('@/pages/Home/View/ClassifyGuideView'))        // 分类导航
const HomeSearchView = lazy(() => import('@/pages/Home/View/HomeSearchView'))              // 搜索框（首页中）

const SpaceView = lazy(() => import('@/components/SpaceList'))                             // 空间
const KingdomView = lazy(() => import('@/components/KingdomView'))                         // 王国
const CaseView = lazy(() => import('@/components/CaseList'))                               // 病例
const TabbarView = lazy(() => import('@/components/Tabbar'))                               // 底部菜单
const MajorIndustryEventsView = lazy(() => import('@/components/MajorIndustryEventsView')) // 行业大事件

// 判断是否是经过base64编码的数据
const isBase64 = (str) => {
  if (str === '' || str.trim() === '') {
    return false;
  }
  try {
    return btoa(atob(str)) == str || btoa(atob(str)) == `${str}=` || btoa(atob(str)) == `${str}==`;
  } catch (err) {
    return false;
  }
}

// 从地址栏参数中解析出pageId
const getRealPageId = (pageSource: string, pageUrl = '') => {
  // pageUrl有值，则解析pageUrl（表示即非首页，也非直播页），否则表示找首页或者直播页地址
  const pageUrlResult = pageUrl ? pageUrl : pageSource == 'home' ? localStorage.getItem('homePageLink') : localStorage.getItem('squarePageLink')
  const pageSearch = pageUrlResult ? pageUrlResult.split('?')[1] : ''
  const pageSearchParse = parse(pageSearch)
  const keys = Object.keys(pageSearchParse)

  let pageId = null

  // 循环是为了兼容地址栏参数有多个的情况，比如/home?alhelper&aWQ9OSZyPTA2OTEzMzcy
  keys.forEach(item => {
    if (isBase64(item)) {
      // 每个都解析一遍，哪个能解析出值来取哪个
      const pageIdAtob = atob(item)  // 将ID字符串解码
      const urlParams = new URLSearchParams(pageIdAtob)
      if (urlParams.get('id')) {
        pageId = urlParams.get('id')
      }
    }
  })

  return pageId      // 最终的页面ID
}

const Index: React.FC = (props: any) => {
  const { dispatch, loading, pageSource } = props
  const { pathname, search } = history.location

  const initialState = {
    componentsResultList: [],                              // 组件的集合
    fixedComponentsResultList: [],                         // 组件的集合（固定位置的组件）
    isHomePage: 0,                                         // 是否是首页，1 是，0 否

    // 友盟统计用的数据
    allSpaceList: [],                                      // 所有空间组件集合
    allCaseList: [],                                       // 所有病例组件集合
    allKingdomList: [],                                    // 所有王国组件集合
    allImageList: [],                                      // 所有图片组件集合
    allImageTextList: [],                                  // 所有图文组件集合
    allClassifyGuideList: [],                              // 所有分类组件集合
    allMajorIndustryEventsList: [],                        // 所有行业大事件集合
  }
  const [state, setState] = useState(initialState)
  const [realPageId, setRealPageId] = useState(null)       // 最终的页面ID
  const [wxPageName, setWxPageName] = useState('')             // 行业大事件名称

  useEffect(() => {
    const operatingEnv = getOperatingEnv();
    if (!location.host.includes('dhealth.friday.tech') && operatingEnv == 6) {
      Toast.show({
        content: `'现在环境是'${operatingEnv == 6 ? 'JarvisApp' : '其他非JarvisApp环境'}`,
      })
    }

    // 获取页面ID，有ID查询数据，没有ID的话，根据pageSource转到首页或直播页地址
    const pageId = getRealPageId(pageSource, pageSource == 'home' ? window.location.href : '')
    if (pageId) {
      setRealPageId(pageId)
      getPageInfo(pageId)
    } else {
      getHomePageLink(true, pageSource)
    }
  }, [])

  // 微信config配置
  const wxConfig = async (pageName:string, pageUrl: string) => {
    const jsapiTicketContent = await getJsapiTicket({
      appId: WxAppIdByPublicAccount,
      currentUrl: window.location.href.split('#')[0],
    });
    const { code, content } = jsapiTicketContent || {};
    if (code == 200) {
      wx.config({
        debug: false,
        appId: content.appId,
        timestamp: content.timestamp,
        nonceStr: content.nonceStr,
        signature: content.signature,
        jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData'],
      });
      wx.ready(() => {
        const shareData = {
          title: pageName,
          desc: '【FRIDAY周五牙医】牙医都在这里学习和交流',
          link: pageUrl,
          imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/DigitalHealthShareImg.png',
        };
        //自定义“分享给朋友”及“分享到QQ”按钮的分享内容
        wx.updateAppMessageShareData(shareData);
        //自定义“分享到朋友圈”及“分享到 QQ 空间”按钮的分享内容（1.4.0）
        wx.updateTimelineShareData(shareData);
      });
    } else {
      Toast.show(
        jsapiTicketContent && jsapiTicketContent.msg ? jsapiTicketContent.msg : '获取微信配置失败!',
      );
    }
  }
  // 根据页面ID查询页面详情
  const getPageInfo = (pageId) => {
    dispatch({
      type: 'activity/getPageInfo',
      payload: {
        id: pageId,                               // 页面id
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200 && content) {
        // 处理分享卡片问题
        const { pageName, pageUrl } = content;
        setWxPageName(pageName)
        wxConfig(pageName, pageUrl);

        // 更新本地存储和刷新数据的逻辑
        let isUpdate = false      // 是否更新本地存储
        let isRefresh = false      // 是否刷新页面数据
        let refreshType = null     // 值为home，square
        // 表示首页链接更新了，本地存储的是老的
        if (content.isHomePage == 1 && content.pageUrl != localStorage.getItem('homePageLink')) {
          isUpdate = true
        }
        // 表示广场直播链接更新了，本地存储的是老的
        if (content.isBroadcast == 1 && content.pageUrl != localStorage.getItem('squarePageLink')) {
          isUpdate = true
        }

        // 表示首页链接更新了，本地存储的是老的
        if (content.isHomePage != 1 && content.pageUrl == localStorage.getItem('homePageLink')) {
          isUpdate = true
          // 同时用户想看的是首页
          if (pathname.toLocaleLowerCase() == '/home') {
            isRefresh = true
            refreshType = 'home'
          }
        }

        // 表示广场直播链接更新了，本地存储的是老的
        if (content.isBroadcast != 1 && content.pageUrl == localStorage.getItem('squarePageLink')) {
          isUpdate = true
          // 用户想看的是广场直播页
          if (pathname.toLocaleLowerCase() == '/square') {
            isRefresh = true
            refreshType = 'square'
          }
        }

        if (isUpdate) {
          getHomePageLink(isRefresh, refreshType)
          if (isRefresh) {
            return
          }
        }

        
        const componentsResultList = content.dataList || []

        // 拆分普通组件和位置固定的组件
        const fixedComponentsResultList = []

        // 活动组件
        const index = componentsResultList.findIndex(item => item.type == 'activity')
        if (index > -1) {
          fixedComponentsResultList.push(componentsResultList.splice(index, 1)[0])
        }
        // 搜索组件
        const index2 = componentsResultList.findIndex(item => item.type == 'search')
        if (index2 > -1) {
          fixedComponentsResultList.push(componentsResultList.splice(index2, 1)[0])
        }
        // 底部导航栏组件
        const index3 = componentsResultList.findIndex(item => item.type == 'tabBar')
        if (index3 > -1) {
          fixedComponentsResultList.push(componentsResultList.splice(index3, 1)[0])
        }

        // 首页友盟埋点数据结构保存
        let allSpaceList = []                  // 全部空间组件的集合
        let allKingdomList = []                // 全部王国组件的集合
        let allCaseList = []                   // 全部病例组件的集合
        let allImageList = []                  // 全部图片组件的集合
        let allImageTextList = []              // 全部图文组件的集合
        let allClassifyGuideList = []          // 全部分类组件的集合
        let allMajorIndustryEventsList = []    // 全部行业大事件的集合

        // 如果是首页，才进行统计
        if (content.isHomePage == 1) {
          componentsResultList.forEach(item => {
            if (item.type == 'space' && item.dataList) {
              allSpaceList = allSpaceList.concat(item.dataList)

            } else if (item.type == 'kingdom' && item.dataList) {
              allKingdomList = allKingdomList.concat(item.dataList)

            } else if (item.type == 'case' && item.dataList) {
              allCaseList = allCaseList.concat(item.dataList)

            } else if (item.type == 'image' && item.dataList) {
              allImageList = allImageList.concat(item.dataList)

            } else if (item.type == 'imageText' && item.dataList) {
              allImageTextList = allImageTextList.concat(item.dataList)

            } else if (item.type == 'classifyGuide' && item.dataList) {
              allClassifyGuideList = allClassifyGuideList.concat(item.dataList)

            } else if (item.type == 'industryEvent' && item.dataList) {
              allMajorIndustryEventsList = allMajorIndustryEventsList.concat(item.dataList)

            }
          })
        }

        setState({
          ...state,
          componentsResultList,                          // 组件的集合
          fixedComponentsResultList,                     // 组件的集合（固定位置的组件）
          isHomePage: content.isHomePage,                // 是否是首页，1 是，0 否

          // 友盟统计用
          allSpaceList,                                  // 全部空间组件的集合
          allKingdomList,                                // 全部王国组件的集合
          allCaseList,                                   // 全部病例组件的集合
          allImageList,                                  // 全部图片组件的集合（一行一个的）
          allImageTextList,                              // 全部图文组件的集合
          allClassifyGuideList,                          // 全部分类组件的集合
          allMajorIndustryEventsList,                    // 全部行业大事件的集合
        })
      } else {
        Toast.show(msg || '数据加载失败')

        // 没有查到数据，走一遍刷新的流程
        getHomePageLink(true, pageSource)
      }
    })
  }

  // 更新首页，isRefresh 是否重新加载数据；refreshType home 重新加载首页的数据，square 重新加载广场直播的数据
  const getHomePageLink = (isRefresh, refreshType) => {
    dispatch({
      type: 'activity/getHomePageLink',
      payload: {}
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200 && content) {
        // 保存最新的首页和直播页链接地址
        localStorage.setItem('homePageLink', content.pageUrl || '')
        localStorage.setItem('squarePageLink', content.directUrl || '')
        if (isRefresh) {
          // 还没有结果就到这停止
          if (refreshType == 'home' && !content.pageUrl) {
            Toast.show('首页未配置，请联系管理员~')
            return
          }
          if (refreshType == 'square' && !content.directUrl) {
            Toast.show('直播页未配置，请联系管理员~')
            return
          }

          // 有结果必能解析出ID
          const pageId = getRealPageId(refreshType)
          setRealPageId(pageId)
          getPageInfo(pageId)
          // 刷新一下页面路由
          if (refreshType == 'home') {
            let url = `/home?${content.pageUrl.split('?')[1]}`
            // 兼容多个参数的情况，比如/home?alhelper&xxxxxxx
            if (search) {
              url += `&${search.substring(1)}`
            }
            history.replace(url)
          }
        }
      } else {
        Toast.show(msg || '数据加载失败')
      }
    })
  }

  // 根据type返回对应组件
  const getViewByType = (item) => {
    let moduleIndex = -1
    switch (item.type) {
      case 'image':
        // 在所有图片组件（一行1个）里的索引
        moduleIndex = state.allImageList.filter(itemChild => itemChild.config && getNewImageNumber(itemChild.config.number) == 11).findIndex(itemChild => itemChild.id == item.id)
        // 在所有图片组件（轮播）里的索引
        const moduleIndexBanner = state.allImageList.filter(itemChild => itemChild.config && getNewImageNumber(itemChild.config.number) == 50).findIndex(itemChild => itemChild.id == item.id)
        // 在所有图片组件（一行5个）里的索引
        const moduleIndexJinGang = state.allImageList.filter(itemChild => itemChild.config && getNewImageNumber(itemChild.config.number) == 15).findIndex(itemChild => itemChild.id == item.id)
        // 在所有图片组件（一行3个）里的索引
        const moduleIndexAD = state.allImageList.filter(itemChild => itemChild.config && getNewImageNumber(itemChild.config.number) == 13).findIndex(itemChild => itemChild.id == item.id)
        return <ImageView
          key={item.id}
          componentData={item}
          isHomePage={state.isHomePage}
          moduleIndex={moduleIndex > -1 ? (moduleIndex + 1) : 0}
          moduleIndexBanner={moduleIndexBanner > -1 ? (moduleIndexBanner + 1) : 0}
          moduleIndexJinGang={moduleIndexJinGang > -1 ? (moduleIndexJinGang + 1) : 0}
          moduleIndexAD={moduleIndexAD > -1 ? (moduleIndexAD + 1) : 0}
        />
      case 'case':
        moduleIndex = state.allCaseList.findIndex(itemChild => itemChild.id == item.id)
        return <CaseView
          key={item.id}
          componentData={item}
          isHomePage={state.isHomePage}
          moduleIndex={moduleIndex > -1 ? (moduleIndex + 1) : 0}
        />
      case 'space':
        moduleIndex = state.allSpaceList.findIndex(itemChild => itemChild.id == item.id)
        return <SpaceView
          key={item.id}
          componentData={item}
          isHomePage={state.isHomePage}
          moduleIndex={moduleIndex > -1 ? (moduleIndex + 1) : 0}
        />
      case 'kingdom':
        moduleIndex = state.allKingdomList.findIndex(itemChild => itemChild.id == item.id)
        return <KingdomView
          key={item.id}
          componentData={item}
          getPageInfo={getPageInfo}
          isHomePage={state.isHomePage}
          moduleIndex={moduleIndex > -1 ? (moduleIndex + 1) : 0}
        />
      case 'imageText':
        moduleIndex = state.allImageTextList.findIndex(itemChild => itemChild.id == item.id)
        return <ImageTextView
          key={item.id}
          componentData={item}
          isHomePage={state.isHomePage}
          moduleIndex={moduleIndex > -1 ? (moduleIndex + 1) : 0}
        />
      case 'classifyGuide':
        moduleIndex = state.allClassifyGuideList.findIndex(itemChild => itemChild.id == item.id)
        return <ClassifyGuideView
          key={item.id}
          componentData={item}
          getPageInfo={getPageInfo}
          isHomePage={state.isHomePage}
          moduleIndex={moduleIndex > -1 ? (moduleIndex + 1) : 0}
        />
      case 'industryEvent':
        moduleIndex = state.allMajorIndustryEventsList.findIndex(itemChild => itemChild.id == item.id)
        return <MajorIndustryEventsView
          key={item.id}
          componentData={item}
          pcOrMobileMode='mobile'
        />
      default:
        return null
    }
  }

  // 根据type返回对应组件，位置固定的组件
  const getFixedViewByType = (item) => {
    switch (item.type) {
      case 'activity':
        return (
          <ActivityView
            key={item.id}
            pageId={realPageId}
            componentData={item}
            signUpOnOk={getPageInfo}
          />
        )
      case 'search':
        return state.isHomePage == 1 ? <HomeSearchView key={item.id} /> : <SearchView key={item.id} />
      case 'tabBar':
        // [APP相关] 5表示在FRIDAY APP中，这时隐藏底部菜单栏
        return getOperatingEnv() == 5 ? null : <TabbarView key={item.id} />
      default:
        return null
    }
  }

  // loading
  const loadingGetPageInfo = !!loading.effects['activity/getPageInfo']

  return (
    <>
      <Helmet>
      <title>{wxPageName ? wxPageName : '医生星球'}</title>
      <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, shrink-to-fit=no" />
    </Helmet>
    <Spin spinning={loadingGetPageInfo}>
      {/*<div className={styles.header_box}>*/}
      {/*  <i></i>*/}
      {/*</div>*/}
      <div className={classNames(styles.container_box, {
        [styles.container_box_in_square]: pageSource == 'square',
      })}>
        <div className={classNames(styles.container, {
          [styles.search_container]: state.fixedComponentsResultList.findIndex(item => item.type == 'search') > -1 && state.isHomePage == 0,
          [styles.home_search_container]: state.fixedComponentsResultList.findIndex(item => item.type == 'search') > -1 && state.isHomePage == 1,
          [styles.tabbar_container]: state.fixedComponentsResultList.findIndex(item => item.type == 'tabBar') > -1,
          [styles.activity_container]: state.fixedComponentsResultList.findIndex(item => item.type == 'activity') > -1,
          [styles.in_app_container]: getOperatingEnv() == 5,
        })}>

          {/* 一般组件 */}
          {
            state.componentsResultList.map((item, index) => {
              return (
                <Suspense key={index} fallback={<div></div>}>
                  {getViewByType(item)}
                </Suspense>
              )
            })
          }

          {/* 位置固定的组件 */}
          {
            state.fixedComponentsResultList.map((item, index) => {
              // 广场页，不显示固定在顶部的搜索和底部的tabbar
              if (pageSource == 'square') {
                return null
              }
              return (
                <Suspense key={index} fallback={<div></div>}>
                  {getFixedViewByType(item)}
                </Suspense>
              )
            })
          }
        </div>
      </div>
    </Spin>
    </>
   
  )
}

export default connect(({ activity, loading }: any) => ({ activity, loading }))(Index)
