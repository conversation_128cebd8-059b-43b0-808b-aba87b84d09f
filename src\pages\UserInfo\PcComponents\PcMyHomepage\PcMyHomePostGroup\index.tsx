/**
 * @Description: PC-我的主页-帖子合集
 */
import React, { useState } from 'react';
import { connect } from 'umi';
import classNames from 'classnames'
import styles from './index.less';

import PcMyHomePost from '../PcMyHomePost';                // 个人中心-我的主页-帖子
import PcMyHomeArticle from '../PcMyHomeArticle';          // 个人中心-我的主页-文章
import PcMyHomeLink from '../PcMyHomeLink';                // 个人中心-我的主页-外链
import PcMyHomeDrafts from '../PcMyHomeDrafts';            // 个人中心-我的主页-草稿箱

// tab签数据
const tabsDataSource = [
  { id: 1, name: '帖子' },
  { id: 2, name: '文章' },
  { id: 3, name: '外链' },
  { id: 4, name: '草稿箱' },
]

const Index: React.FC<any> = (props) => {
  const { dispatch, pcAccount } = props;
  const [tab, setTab] = useState(pcAccount?.threeTabState || 1);     // tab状态

  // tab签切换
  const onChangeTabs = (id) => {
    setTab(id)
    dispatch({
      type: 'pcAccount/save',
      payload: {
        threeTabState: id,     // 三级tab状态
      }
    })
  }

  return (
    <>
      <div className={styles.post_group_container}>
        {/* tab签 */}
        <div className={styles.tabs_wrap}>
          {
            tabsDataSource.map(item => {
              return (
                <span
                  key={item.id}
                  className={classNames(styles.tabs_item, {
                    [styles.checked]: tab == item.id,
                  })}
                  onClick={() => onChangeTabs(item.id)}
                >{item.name}</span>
              )
            })
          }
        </div>
        {/* 列表 */}
        {
          tab == 1 ? <PcMyHomePost />
          : tab == 2 ? <PcMyHomeArticle />
          : tab == 3 ? <PcMyHomeLink />
          : tab == 4 ? <PcMyHomeDrafts />
          : null
        }
      </div>

    </>
  );
};

export default connect(({ pcAccount, loading }: any) => ({ pcAccount, loading }))(Index);
