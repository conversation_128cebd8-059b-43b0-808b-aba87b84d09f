.popup_container {
  :global {
    .adm-popup-body {
      border-radius: 12px 12px 0 0;
    }
  }
  .header {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 28px;
    .line {
      width: 48px;
      height: 4px;
      border-radius: 4px;
      background: #D0D4D7;
    }
  }
}

.title_box {
  position: relative;
  .title {
    font-size: 17px;
    color: #000;
    font-weight: 500;
    line-height: 24px;
    text-align: center;
  }
  .title_btn {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    padding: 0 16px;
  }
  .title_right {
    position: absolute;
    right: 20px;
    top: 0;
    bottom: 0;
    padding: 0 16px;
  }
}


.mask {
  :global {
    .adm-mask-content {
      height: 100%;
    }
  }
}
.page_warp {
  width: 100%;
  height: 100%;
}

.container {
  width: 100%;
  height: calc(90vh - 135px);
  display: flex;
  justify-content: center;
  align-items: center;

  //:global {
  //  .cropper-point {
  //    width: 10px;
  //    height: 10px;
  //  }
  //}

}

.CropperWarp {
  width: 100%;
  height: 400px;
}

.footer {
  position: absolute;
  bottom: 40px;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  color: #fff;
  z-index: 990;

  .footer_cancel_btn {
    font-size: 15px;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 18px;
  }

  .footer_reduction_btn {
    /*font-size: 15px;
    font-weight: 400;
    color: #999999;
    line-height: 18px;
*/
    flex: 1 1;
    margin-right: 8px;
    height: 40px;
    background: #EDF9FF;
    border-radius: 20px;
    font-size: 16px;
    font-weight: 400;
    color: #0095FF;
    line-height: 40px;
    text-align: center;
  }

  .footer_ok_btn {
    flex: 1 1;
    height: 40px;
    background: #0095FF;
    border-radius: 20px;
    font-size: 16px;
    font-weight: 400;
    color: #fff;
    line-height: 40px;
    text-align: center;
  }
}

.img_select {
  font-size: 16px;
  color: #0095FF;
  font-weight: 400;
  cursor: pointer;
  user-select: none;
}
