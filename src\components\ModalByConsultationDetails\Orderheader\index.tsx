/**
 * @Description: 指导头部信息卡片
 */
import React, { useState } from 'react';
import photoIcon from '@/assets/Consultation/H5/photo_icon.png'
import videoIcon from '@/assets/Consultation/H5/video_icon.png'
import styles from './index.less'

interface PropsType {
  consultationType: number,   // 1 图文指导  2视频指导
  data:any,                   // 展示数据
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { consultationType ,data} = props;
  const [state, setState] = useState(false)  // 添加客服小忆微信弹窗状态
  const statusText = ['已取消','未支付(待评估)', '待支付', '已支付']
  let {
    processNode,        // 进度状态
    createDate,         // 创建时间
    createUserName,     // 创建人
    thisUserIsExperts,  // 当前人是否是专家：0:否，1:是
    orderNumber,        // 订单号
    status,           // : 1,//支付状态(0已取消、1未支付(待评估)、2去支付(已评估)、3已支付)
  } = data || {}

  /**
   * 状态展示
   * @param status 状态
   */
  const TextDisplayFun = (status: number) => {
    if (consultationType == 1) {
      let type = status == 4?1:status == 5?2:status == 6?3:null
      switch (type) {
        case 1:
          return "病例资料被查看"
        case 2:
          return "问题被回复并对话"
        case 3:
          return "结束指导交易成功"
      }
    } else if (consultationType == 2) {
      let type = status == 4?1:status == 5?2:status == 6?3:status == 7?4:status == 8?5:status == 9?6:null
      switch (type) {
        case 1:
          return "病例资料被查看"
        case 2:
          return "预约视频会议"
        case 3:
          return "视频沟通"
        case 4:
          return "结束指导"
        case 5:
          return "支付指导费用"
        case 6:
          return "交易成功"
      }
    }
  }

  return (
    <div className={styles.wrap}>
      <div className={styles.header}>
        <span className={styles.header_icon}>
          {
            consultationType == 1 ? <img src={photoIcon} alt="" /> : <img src={videoIcon} alt="" />
          }
        </span>
        <span className={styles.header_title}>{consultationType == 1 ? "图文指导" : "视频指导"}</span>
        {/*{consultationType != 1 ? <span className={styles.header_title_tips}>请保持手机畅通</span> : null}*/}
        <span className={styles.header_status}>{status != null && statusText[status]}</span>
      </div>
      <div className={styles.content}>
        <p>订单号：{orderNumber}</p>
        <p>创建人：{createUserName}</p>
        <p>创建时间：{createDate}</p>
      </div>
    </div>
  )
}
export default Index
