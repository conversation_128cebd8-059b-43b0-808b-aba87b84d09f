/**
 * 分享课件-操作按钮
 */
import React from 'react';
import { message } from 'antd';
import styles from './index.less';
import {connect} from "umi";  // 引入自定义样式

const WhiteBoardButton = ({
                             spaceId,
                             isLive,
                             localStreamConfig,
                             tiwRemoteStreamConfig,
                             isJoined,
                             currentUserType,
                             isOpenTEduBoard,
                             onClick,
                             resetTimer,
                             dispatch,
                             shareHostRemoteStreamConfig,
                             handleChangeByLocalStreamConfig,
                           }) => {
  const stopWhiteboardPush = async ()=>{
    let res = await dispatch({
      type: 'PlanetChatRoom/stopWhiteboardPush',
      payload: { spaceId: spaceId }
    })
    res && res.code == 200 && message.success('停止分享课件成功');
  }

  return (
    <>
      { isLive
        && localStreamConfig
        && isJoined
        && (currentUserType == 1 || currentUserType == 2)
        && !localStreamConfig.shareDesk
        &&
        <>
          {
            !isOpenTEduBoard ?
              <div>
                {/*{!!tiwRemoteStreamConfig && tiwRemoteStreamConfig.streamType == 'main' && currentUserType == 1 ?
                  <div className={styles.HorizontalLiveRoom_Btn_Warp}>
                    <div onClick={async (e) => {
                        await resetTimer();
                        if(currentUserType == 1) {
                          await stopWhiteboardPush();
                        }
                    }} className={styles.HorizontalLiveRoom_shared_screen_active_WhiteBoardButton_btn}/>
                    <div style={{ color:'#A8D7EE' }} className={styles.text}>停止分享</div>
                  </div>
                :*/}
                  <div className={styles.HorizontalLiveRoom_Btn_Warp}>
                    <div onClick={async (e) => {
                      await resetTimer();
                      // dispatch({type: 'PlanetChatRoom/setState', payload: {isOpenTEduBoard: !isOpenTEduBoard}})
                      if (!!tiwRemoteStreamConfig) {
                        if(currentUserType == 1) {
                          message.warning('嘉宾正在分享课件，此时无法发起课件分享');
                        }
                        else { message.warning('他人正在分享课件，此时无法发起课件分享'); }
                      }else {
                        if (!!localStreamConfig.shareDesk) { // 当前本地开启了分享投屏
                          await handleChangeByLocalStreamConfig('shareDesk', e)
                        }
                        await dispatch({type: 'PlanetChatRoom/setState', payload: {isOpenTEduBoard: !isOpenTEduBoard}})
                      }
                    }} className={styles.HorizontalLiveRoom_shared_screen_WhiteBoardButton}/>
                    <div className={styles.text}>分享课件</div>
                  </div>
                {/*}*/}
              </div>
              : <>
                <div>
                  <div className={styles.HorizontalLiveRoom_Btn_Warp}>
                    <div onClick={async (e) => {
                      await resetTimer();
                      if (isOpenTEduBoard) {
                        await stopWhiteboardPush();
                        await dispatch({type: 'PlanetChatRoom/setState', payload: {isOpenTEduBoard: !isOpenTEduBoard}})
                      }
                    }} className={styles.HorizontalLiveRoom_shared_screen_active_WhiteBoardButton_btn}/>
                    <div style={{ color:'#A8D7EE' }} className={styles.text}>停止分享</div>
                  </div>
                </div>
              </>
          }
        </>
      }
    </>
  );
};

export default connect(({ PlanetChatRoom,loading }: any) => ({PlanetChatRoom,loading}))(WhiteBoardButton);
