.modal {
  :global {
    .adm-modal-body:not(.adm-modal-with-image) {
      padding-top: 24PX;
      padding-bottom: 16PX;
    }
    .adm-modal-content {
      padding: 0;
    }
  }
}

.header {
  font-size: 17PX;
  color: #000;
  font-weight: 500;
  line-height: 24PX;
  text-align: center;
  margin-bottom: 24PX;
}

// 课件list
.file_list_wrap {
  padding: 0 16PX 6PX;
  .file_item_wrap {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    border-radius: 12PX;
    padding: 9PX 16PX;
    border: 1PX dashed #EAEAEA;
    column-gap: 8PX;
    margin-bottom: 16PX;
    .file_name {
      flex: 1;
      font-size: 14PX;
      color: #000;
      line-height: 20PX;
      word-break: break-all;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2; /* 指定显示2行 */
      overflow: hidden;
    }
  }
}

// 按钮
.btn_wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  column-gap: 16PX;
  padding: 16PX 16PX 0;
  .btn_ok, .btn_cancel {
    flex: 1;
    border-radius: 20PX;
    height: 40PX;
    line-height: 40PX;
    font-size: 16PX;
    text-align: center;
    cursor: pointer;
  }
  .btn_cancel {
    background: #EDF9FF;
    color: #0095FF;
  }
  .btn_ok {
    background: #0095FF;
    color: #fff;
  }
}
