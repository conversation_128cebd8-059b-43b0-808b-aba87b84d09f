import React, {useEffect, useState,} from 'react';
import {connect} from 'umi';
import styles from './index.less';
import classNames from 'classnames';
import Stream from '@/componentsByTRTC/Stream';
import PrimaryStream from '@/pages/PlanetChatRoom/Meet/components/Viewport/component/PrimaryStream';
import {
  getCameralistArr,
  getHandUpRemoteStreamList,
  getHostRemoteStreamConfig,
  getIsModeMatrixCameraRemoteStreamList,
  getShareRemoteStreamConfig,
  getUserCameraRemoteStreamList,
  getUserInfoData,
} from '@/utils/utilsByTRTC';

type propsType = {
  global: any;
};

const Index: React.FC<propsType> = (props) => {
  const {localStreamConfig, RTC, remoteStreamConfigList, PlanetChatRoom, dispatch} = props || {};

  const {
    SpaceInfo,
    handUpList,
    currentLiveUserList,
  } = PlanetChatRoom || {};

  const {
    hostUserInfo,
    name: nameBySpaceInfo,
    status: statusBySpaceInfo,
  } = SpaceInfo || {};

  const userInfoData = getUserInfoData();

  const shareRemoteStreamConfig = getShareRemoteStreamConfig(SpaceInfo, remoteStreamConfigList);

  const hostRemoteStreamConfig = getHostRemoteStreamConfig(
    SpaceInfo,
    hostUserInfo,
    remoteStreamConfigList,
  );

  const userCameraRemoteStreamList = getUserCameraRemoteStreamList(
    SpaceInfo,
    remoteStreamConfigList,
  ); // 远端流中摄像头流 只要又画面或者有声音

  const handUpRemoteStreamList = getHandUpRemoteStreamList(
    SpaceInfo,
    handUpList,
    userCameraRemoteStreamList,
  );

  const isModeMatrixCameraRemoteStreamList = getIsModeMatrixCameraRemoteStreamList(
    SpaceInfo,
    hostUserInfo,
    handUpList,
    userCameraRemoteStreamList,
  );

  let cameralistArr = getCameralistArr(
    hostRemoteStreamConfig,
    localStreamConfig,
    handUpRemoteStreamList,
    isModeMatrixCameraRemoteStreamList,
  );

  const [randomNum, setRandomNum] = useState(Math.random());

  useEffect(() => {
    if (Array.isArray(cameralistArr) && cameralistArr.length > 0) {
      setRandomNum(Math.random());
    }
  }, [Array.isArray(cameralistArr) && cameralistArr.length]);

  return (
    <div className={styles.CameralistWarp}>
      <div className={styles.Cameralist}>
        {cameralistArr.map((itemConfig, index) => {
          if (!itemConfig) {
            return;
          }
          let findBycurrentLiveUser =
            Array.isArray(currentLiveUserList) &&
            currentLiveUserList.find((value) => {
              return value.imUserId == itemConfig.userID;
            });
          if (!findBycurrentLiveUser) {
            findBycurrentLiveUser =
              Array.isArray(handUpList) &&
              handUpList.find((value) => {
                return value.imUserId == itemConfig.userID;
              });
          }

          let width = 0;
          let height = 0;
          if (cameralistArr.length == 1) {
            width = 100;
          } else if (cameralistArr.length != 1 && cameralistArr.length <= 4) {
            width = 50;
          } else if (cameralistArr.length > 4 && cameralistArr.length <= 9) {
            width = 33.33;
          } else {
            width = 25;
          }

          if (cameralistArr.length == 1) {
            height = 100;
          } else if (cameralistArr.length <= 6) {
            height = 50;
          } else if (cameralistArr.length <= 9) {
            height = 33.33;
          } else {
            height = 25;
          }

          return (
            <div
              key={itemConfig.userID}
              style={{
                width: `${width}%`,
                height: `${height}%`,
                aspectRatio: '9/16',
              }}
              className={styles.CameraItem}
            >
              {itemConfig && itemConfig.userID.indexOf('share') != -1 ? (
                <>
                  <div
                    key={`${itemConfig.stream.getUserId()}_${itemConfig.stream.getType()}`}
                    className={classNames({
                      [styles.isModeMatrix_camera_picture_camera_item]: true,
                      [styles.isModeMatrix_camera_picture_camera_item_hidden]:
                      itemConfig && !itemConfig.hasVideo,
                    })}
                  >
                    <div
                      className={classNames({
                        [styles.StreamWarp]: true,
                        [styles.StreamWarpHidden]: itemConfig && !itemConfig.hasVideo,
                      })}
                    >
                      <Stream
                        key={`${itemConfig.stream.getUserId()}_${itemConfig.stream.getType()}_MatrixStreamList_share_${randomNum}`}
                        stream={itemConfig.stream}
                        config={itemConfig}
                        init={(dom) => RTC && RTC.playStream(itemConfig.stream, dom)}
                      ></Stream>
                    </div>
                  </div>
                </>
              ) : (
                <>
                  <PrimaryStream
                    RTC={RTC}
                    showStream={itemConfig}
                    streamUser={findBycurrentLiveUser}
                    randomNum={`${randomNum}_MatrixStreamList`}
                  />
                </>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};
export default connect(({ global, PlanetChatRoom, loading }: any) => ({
  global,
  PlanetChatRoom,
  loading,
}))(Index);
