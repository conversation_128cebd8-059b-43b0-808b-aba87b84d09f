.step_container {
  position: relative;
  background: #fff;
  width: 100%;
  height: 72px;
  padding: 0 180px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px 8px 0 0;
  border-bottom: 1px solid #D9D9D9;
}

.step_content {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  .step_item {
    display: flex;
    align-items: center;
    padding-left: 12px;
    flex: 1;
    overflow: hidden;
    .step_item_icon {
      background: #F5F5F5;
      width: 16px;
      height: 16px;
      font-size: 11px;
      color: #D5D5D5;
      line-height: 17px;
      text-align: center;
      border-radius: 50%;
      margin-right: 8px;
    }
    .step_item_text {
      position: relative;
      font-size: 14px;
      color: #999;
      height: 20px;
      line-height: 21px;
      padding-right: 12px;
      white-space: nowrap;
      &::after {
        content: "";
        position: absolute;
        top: 10px;
        left: 100%;
        display: block;
        width: 9999px;
        height: 1px;
        background: #F5F5F5;
      }
    }
    &:first-child {
      padding-left: 0;
    }
    &:last-child {
      flex: none;
    }
    &.step_item_finished {
      .step_item_icon {
        width: 18px;
        height: 18px;
        background: #0095FF;
        border: 3px solid #CCEEFF;
        display: flex;
        align-items: center;
        justify-content: center;

        :global {
          .anticon {
            display: block;
            color: #fff;
            font-size: 9px;
          }
        }
      }
      .step_item_text::after {
        background: #CCEEFF;
      }
    }

    &.step_item_undone {
      .step_item_icon {
        background: #999999;
        border: 4px solid #F5F5F5;
      }
      .step_item_text {
        color: #000;
      }
    }

    &.step_item_active {
      .step_item_icon {
        background: #0095FF;
        border: 4px solid #CCEEFF;
      }
      .step_item_text {
        color: #000;
      }
    }
    &.step_item_finish {
      .step_item_icon {
        /*
          background: #0095FF;
          border: 4px solid #CCEEFF;
        */
        width: 16px;
        height: 16px;
        background: url('~@/assets/CreationOrthodontics/CreationOrthodontics_step_item_finish_icon.png') no-repeat;
        background-size: 16px 16px;
        // border: 3px solid #CCEEFF;
        display: flex;
        align-items: center;
        justify-content: center;

      }
      .step_item_text::after {
        background: #CCEEFF;
      }
    }
  }
}

.btn {
  position: absolute;
  right: 24px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  color: #0095FF;
  height: 17px;
  line-height: 17px;
  display: flex;
  align-items: center;
  cursor: pointer;
  :global {
    .anticon {
      font-size: 10px;
      margin-right: 2px;
    }
  }
}
