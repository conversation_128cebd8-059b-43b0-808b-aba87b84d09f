import React, {useRef, useState,} from 'react';
import {connect} from 'umi';
import styles from './index.less';
import classNames from 'classnames';
import '../../../components/video-js.min.css';
import 'tcplayer.js/dist/tcplayer.min.css';
import MatrixStreamList from './component/MatrixStreamList';
import ParticipantStreamListByTop from './component/ParticipantStreamListByTop';
import ShareRemoteStream from './component/ShareRemoteStream';
import {
  getCameralistArr,
  getHandUpRemoteStreamList,
  getHostRemoteStreamConfig,
  getIsModeMatrixCameraRemoteStreamList,
  getIsNotHasVideo,
  getShareRemoteStreamConfig,
  getUserCameraRemoteStreamList,
  getUserInfoData,
} from '@/utils/utilsByTRTC';
import HorizontalLiveRoomPictureCameraLive
  from '@/pages/PlanetChatRoom/Meet/components/Viewport/component/HorizontalLiveRoomPictureCameraLive';
import WhiteboardLiveRoom from '@/pages/PlanetChatRoom/components/WhiteboardLiveRoom';
import NoScreenStreamList from '@/pages/PlanetChatRoom/Meet/components/Viewport/component/NoScreenStreamList';
import VideoByMeet from './component/VideoByMeet/index';

type propsType = {
  global: any;
  onRefByVerticalLiveRoom: any;
  sendMessageByIm: any;
  localStreamConfig: any;
  remoteStreamConfigList: any;
  RTC: any;
  shareRTC: any;
  isJoined: boolean;
  isPublished: boolean;
  handleJoin: any;
  handleLeave: any;
  onChange: any;
  spaceId: any;
  openCloseHandUp: any;
  liveRecord: any;
  getGuestList: any;
  getSpaceInfo: any;
  onClickLianMai: any;
  changeUrlParams: any;
  elapsedTime: any;
  shareOnClick: any;
  onClickBack: any;
  isHorizontalLive: any;
};

const Index: React.FC<propsType> = (props) => {
  const {
    localStreamConfig,
    RTC,
    remoteStreamConfigList,
    PlanetChatRoom,
    dispatch,
    isHorizontalLive,
    tim,
    changeUrlParams,
  } = props || {};

  const {
    SpaceInfo,
    handUpList,
    currentUserType,
    isMobile,
    ModalVisibleByKickedOut,
    ModalVisibleBySpaceViolation,
    ModalVisibleBySpaceRemoved,
    isOpenTEduBoard,
  } = PlanetChatRoom || {};

  const {

    hostUserInfo,
    status: statusBySpaceInfo,
    videoList,
    spaceCoverUrlShow,
  } = SpaceInfo || {};

  const playerRefByHorizontalLiveRoom = useRef(null);

  const [isShowCameraList, setIsShowCameraList] = useState(true);

  const userInfoData = getUserInfoData();

  const shareRemoteStreamConfig = getShareRemoteStreamConfig(SpaceInfo, remoteStreamConfigList);

  const hostRemoteStreamConfig = getHostRemoteStreamConfig(
    SpaceInfo,
    hostUserInfo,
    remoteStreamConfigList,
  );

  const userCameraRemoteStreamList = getUserCameraRemoteStreamList(
    SpaceInfo,
    remoteStreamConfigList,
  );

  const isModeMatrixCameraRemoteStreamList = getIsModeMatrixCameraRemoteStreamList(
    SpaceInfo,
    hostUserInfo,
    handUpList,
    userCameraRemoteStreamList,
  );

  const handUpRemoteStreamList = getHandUpRemoteStreamList(
    SpaceInfo,
    handUpList,
    userCameraRemoteStreamList,
  );

  let cameralistArr = getCameralistArr(
    hostRemoteStreamConfig,
    localStreamConfig,
    handUpRemoteStreamList,
    isModeMatrixCameraRemoteStreamList,
  );

  let isNotHasVideo = getIsNotHasVideo({
    localStreamConfig,
    userCameraRemoteStreamList,
  });

  return (
    <>
      {!(Array.isArray(videoList) && videoList.length > 0) &&
        !localStreamConfig &&
        !shareRemoteStreamConfig &&
        ((currentUserType != 1 && !hostRemoteStreamConfig) ||
          (currentUserType == 1 && !localStreamConfig)) && (
          <div className={styles.Live_Video}>
            {!!spaceCoverUrlShow ? (
              <img
                className={classNames({
                  [styles.spaceCoverUrlShow]: true,
                })}
                src={spaceCoverUrlShow}
              ></img>
            ) : (
              <img
                className={classNames({
                  [styles.spaceCoverUrlShow]: true,
                })}
                src={
                  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/PlanetChatRoom_HorizontalLiveRoom_Live_bg.png'
                }
              />
            )}
          </div>
        )}

      <div className={styles.ViewContent}>
        {!!props.PlanetChatRoom.isLive &&
          !shareRemoteStreamConfig &&
          isNotHasVideo &&
          !isOpenTEduBoard && (
            <NoScreenStreamList
              localStreamConfig={localStreamConfig}
              RTC={RTC}
              remoteStreamConfigList={remoteStreamConfigList}
              PlanetChatRoom={PlanetChatRoom}
            />
          )}

        {!!props.PlanetChatRoom.isLive && !shareRemoteStreamConfig && !isNotHasVideo && !isOpenTEduBoard&& (
          <div className={styles.MatrixStreamListWarp}>
            <MatrixStreamList
              localStreamConfig={localStreamConfig}
              RTC={RTC}
              remoteStreamConfigList={remoteStreamConfigList}
              PlanetChatRoom={PlanetChatRoom}
            />
          </div>
        )}

        {!!props.PlanetChatRoom.isLive && !!shareRemoteStreamConfig && !isOpenTEduBoard && (
          <div
            className={classNames({
              [styles.ParticipantStreamListByTopWarp]: isMobile && !isHorizontalLive,
              [styles.ParticipantStreamListByTopWarp_isHorizontalLisive]:
              !isMobile || !!isHorizontalLive,
            })}
          >
            {!isHorizontalLive && (
              <ParticipantStreamListByTop
                RTC={RTC}
                localStreamConfig={localStreamConfig}
                remoteStreamConfigList={remoteStreamConfigList}
              />
            )}

            <div
              className={classNames({
                [styles.ShareRemoteStreamWarp]: isMobile && !isHorizontalLive,
                [styles.ShareRemoteStreamWarp_isHorizontalLisive]: !isMobile || !!isHorizontalLive,
              })}
            >
              <ShareRemoteStream
                RTC={RTC}
                localStreamConfig={localStreamConfig}
                remoteStreamConfigList={remoteStreamConfigList}
              />
            </div>
          </div>
        )}

        {!!props.PlanetChatRoom.isLive && !!isOpenTEduBoard && statusBySpaceInfo == 1 && (
          <WhiteboardLiveRoom
            tim={tim}
            RTC={RTC}
            localStreamConfig={localStreamConfig}
            remoteStreamConfigList={remoteStreamConfigList}
            shareHostRemoteStreamConfig={shareRemoteStreamConfig}
          />
        )}

        {!!props.PlanetChatRoom.isLive && isHorizontalLive && (
          <HorizontalLiveRoomPictureCameraLive
            RTC={RTC}
            localStreamConfig={localStreamConfig}
            remoteStreamConfigList={remoteStreamConfigList}
            isHorizontalLive={isHorizontalLive}
            isShowCameraList={isShowCameraList}
            onClickByHorizontalLiveRoom={() => {
            }}
          />
        )}

        {!props.PlanetChatRoom.isLive &&
          Array.isArray(videoList) &&
          videoList.length > 0 &&
          !ModalVisibleByKickedOut &&
          !ModalVisibleBySpaceViolation &&
          !ModalVisibleBySpaceRemoved && <VideoByMeet changeUrlParams={changeUrlParams}/>}
      </div>
    </>
  );
};
export default connect(({ global, PlanetChatRoom, loading }: any) => ({
  global,
  PlanetChatRoom,
  loading,
}))(Index);
