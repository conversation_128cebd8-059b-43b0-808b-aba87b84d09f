/**
 * @Description: PC端，空间卡片组件
 */
import React, { useEffect, useState } from 'react'
import { history, connect } from 'umi'
import classNames from 'classnames'
import DanmuJs from 'danmu.js'
import { gdpFormat, processNames, randomColor } from '@/utils/utils'
import { message, Modal, Spin } from 'antd'
import styles from './index.less'
import {stringify} from "qs";

const Index: React.FC = (props: any) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  const {
    loading,
    dispatch,
    spaceData = {},
    componentId = null,
    index = null,
    whereFrom = null,                  // 1 首页，2 个人中心，3 医生主页
    number = null,                     // 首页配置
    isInClassifyGuide = false,         // 是否在分类导航中
    classifyGuideNumber = 1,           // 一行几个分类导航
    isMyPage = false, // 是否是我的主页
    myHomeSpaceFilter, // 我的主页筛选条件
    starSpaceType = 1, // 空间类型: 1 直播，2 会议
  } = props

  const [isHover, setIsHover] = useState(false);

  useEffect(() => {
    if (!isInClassifyGuide && number == 11 && spaceData.msgList && spaceData.msgList.length > 0) {
      // 初始化弹幕
      initializationByDanmu()
    }
  }, [])

  // 初始化弹幕组件
  const initializationByDanmu = () => {
    new DanmuJs({
      channelSize: 28,             // 轨道大小
      container: document.getElementById(`vs${componentId}${index}`), // 弹幕容器，该容器发生尺寸变化时会自动调整弹幕行为
      player: document.getElementById(`ms${componentId}${index}`),    // 播放器容器（需要有这个弹幕才能无限轮播）
      area: {
        // 弹幕显示区域
        start: 0,                  // 区域顶部到播放器顶部所占播放器高度的比例
        end: 1,                    // 区域底部到播放器顶部所占播放器高度的比例
      },
      mouseControl: false,         // 打开鼠标控制, 打开后可监听到 bullet_hover 事件。danmu.on('bullet_hover', function (data) {})
      mouseControlPause: false,    // 鼠标触摸暂停。mouseControl: true 生效
      chaseEffect: true,           // 开启滚动弹幕追逐效果, 默认为true
      comments: spaceData.msgList.map((item, index) => ({
        moveV: 100,                // 每秒移动距离，单位px
        id: index,                 // 弹幕id，需唯一
        txt: item,                 // 弹幕文字内容
        style: {
          color: '#FFFFFF',
          fontSize: '11px',
          borderRadius: '20px',
          padding: '0 6px',
          margin: '8px 0',
          height: '19px',
          lingHeight: '19px',
          backgroundColor: 'rgba(0,0,0,0.2)',
        },
      }))
    })
  }

  // 点击空间跳转
  const goToUrl = (spaceData) => {
    if (!localStorage.getItem('access_token')) {
      history.push({
        pathname: '/User/login',
        query: {
          redirectByPush: window.location.href,
        }
      })
      return
    }
    sessionStorage.setItem('myHomeSpaceFilter', JSON.stringify(myHomeSpaceFilter));
    // starSpaceType，1 直播，2 会议
    if (spaceData.starSpaceType == 2) {
      history.push(`/PlanetChatRoom/Meet/${spaceData.id}?shareUserId=${UserInfo?.friUserId}`)
    } else {
      history.push(`/PlanetChatRoom/Live/${spaceData.id}?shareUserId=${UserInfo?.friUserId}`)
    }
  }

  // 编辑空间事件
  const editSpaceFn = (e, spaceIdItem) => {
    e.stopPropagation()
    e.preventDefault()
    getSpaceInfoByEdit(spaceIdItem)
  }

  // 编辑获取空间数据，获取状态
  const getSpaceInfoByEdit = (spaceIdItem) => {
    console.log('编辑获取空间数据')
    const { id,status:statusByItem } = spaceIdItem;
    if(statusByItem == 1) {
      message.warning('该内容正在进行中，不允许编辑~')
      return;
    }else {
      history.push(`/UserInfo/CreateSpaceByPc/live?${stringify({
        id: id,
      })}`)
    }

    /*const loadingGetSpaceInfoByEdit = !!loading.effects['userInfoStore/getSpaceInfoByEdit']
    if (loadingGetSpaceInfoByEdit) {
      return
    }
    dispatch({
      type: 'userInfoStore/getSpaceInfoByEdit',
      payload: { spaceId }
    }).then(res => {
      const { code, msg } = res
      if (code == 200) {
        history.push(`/UserInfo/CreateSpaceByPc/live?id=${spaceId}`)
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch(err => {})*/
  }

  // 下架事件
  const offShelfFn = (e, {id:spaceId,starSpaceType}) => {
    e.stopPropagation()
    e.preventDefault()
    Modal.confirm({
      title: '确定下架',
      content: `确定要下架这个${starSpaceType == 2 ? '会议' : '直播'}吗？`,
      onOk() {
        return editSpaceInfo(spaceId)
      },
      onCancel() {},
    });
  }

  // 下架
  const editSpaceInfo = (spaceId) => {
    return dispatch({
      type: 'userInfoStore/editSpaceInfo',
      payload: {
        id: spaceId, // 空间id
        updateUserId: UserInfo.friUserId, // 操作人用户ID
        isDisable: 1, // 1是下架，下架时必传
      }
    }).then(res => {
      const { code, msg } = res
      if (code == 200) {
        message.success('下架成功')
        props.refreshFn && props.refreshFn() // 刷新页面
        return true
      } else {
        message.error(msg || '数据加载失败')
        return true
      }
    }).catch(err => {})
  }

  const loadingGetSpaceInfoByEdit = !!loading.effects['userInfoStore/getSpaceInfoByEdit']
  return (
    <div className={classNames(styles.space_container, {
      // [styles.container_form_1]: whereFrom == 1,
      [styles.container_form_2]: whereFrom == 2,
      [styles.container_form_3]: whereFrom == 3,

      [styles.container_number_1]: number == 11,
      [styles.container_number_2]: number == 12,
      [styles.container_number_6]: number == 60,

      [styles.container_isInClassifyGuide_number_2]: isInClassifyGuide && classifyGuideNumber == 2,
    })} onClick={() => goToUrl(spaceData)}>
      <div
        className={styles.cover_img}
        style={spaceData.spaceCoverUrlShow ? {backgroundImage: `url(${spaceData.spaceCoverUrlShow})`} : {}}
        onMouseEnter={()=>{isMyPage && !spaceData.isDisable && setIsHover(true)}}
        onMouseLeave={()=>{isMyPage && !spaceData.isDisable && setIsHover(false)}}
      >
        {/* 封面中的标题 */}
        {
          spaceData.isTemplateCover==1 &&
          <div className={styles.title_in_cover_image}>{spaceData.name}</div>
        }
        {isMyPage && spaceData.isBiz == 1 && <div className={styles.sign}>企业</div>}

        {
          (spaceData.userIdentity == 1 || spaceData.userIdentity == 2) &&
          <span className={classNames(styles.user_role, {
            [styles.role_host]: spaceData.userIdentity == 1,
            [styles.role_guest]: spaceData.userIdentity == 2,
          })}>{spaceData.userIdentity == 1 ? '主持人' : '嘉宾'}</span>
        }

        {
          (spaceData.status && spaceData.status == 3) || (isMyPage && spaceData.isDisable) ? '' :
          <div className={styles.status_box}>
            <i className={classNames(styles.status_icon, {
              [styles.icon1]: spaceData.status == 1,
              [styles.icon2]: spaceData.status == 2,
            })}></i>
            <span>{spaceData.status == 1 ? '进行中' : spaceData.status == 2 ? '预约中' : ''}</span>
          </div>
        }
        <span className={styles.gdp}>{gdpFormat(spaceData.gdp)}GDP | {gdpFormat(spaceData.pv)}观看</span>

        {(isMyPage && spaceData.isDisable) ? <span className={styles.offshelf_style}>该内容已下架</span> : ''}
        {(isMyPage && isHover && myHomeSpaceFilter?.spaceJoinType==1&&UserInfo?.friUserId==spaceData.hostUserId) ?
          <div className={styles.operate_btn}>
            <Spin spinning={loadingGetSpaceInfoByEdit}>
              <span onClick={(e)=>editSpaceFn(e, spaceData)}>编辑</span>
            </Spin>
            <span className={styles.lines}></span>
            <span onClick={(e)=>offShelfFn(e, spaceData)}>下架</span>
          </div> : ''}

        {/* 弹幕容器 */}
        <div id={`vs${componentId}${index}`} className={styles.danmu_box}>
          <div id={`ms${componentId}${index}`}></div>
        </div>
      </div>
      <div className={styles.space_info_box}>
        <div className={styles.title}>
          <div className={styles.title_text}>{spaceData.name}</div>
          {spaceData.intro ? <div className={styles.space_intro}>{spaceData.intro}</div> : ''}
        </div>
        <div className={styles.footer}>
          <div className={styles.footer_left}>
            <div
              className={styles.left_avatar}
              style={spaceData.hostImgUrlShow ? {backgroundImage: `url(${spaceData.hostImgUrlShow})`} : {background: randomColor(spaceData.wxUserId)}}
            >
              {!spaceData.hostImgUrlShow && processNames(spaceData.hostName)}
              {spaceData.isKing ? <i></i> : ''}
            </div>
            <div className={styles.left_name}>{spaceData.hostName}</div>
          </div>
          <div className={styles.footer_right}>
            {
              spaceData.guestDataList && spaceData.guestDataList.length > 0 &&
              spaceData.guestDataList.map((itemChild, indexChild) => {
                if (indexChild >= 3) {
                  return null
                }
                return (
                  <div
                    key={itemChild.wxUserId}
                    className={styles.right_avatar}
                    style={itemChild.headUrlShow ? {backgroundImage: `url(${itemChild.headUrlShow})`} : {background: randomColor(itemChild.wxUserId)}}
                  >
                    {!itemChild.headUrlShow && processNames(itemChild.userName)}
                  </div>
                )
              })
            }
            {
              spaceData.guestDataList && spaceData.guestDataList.length > 3 &&
              <div className={styles.avatar_more}>···</div>
            }
          </div>
        </div>
      </div>
    </div>
  )
}

export default connect(({ loading }: any) => ({ loading }))(Index)
