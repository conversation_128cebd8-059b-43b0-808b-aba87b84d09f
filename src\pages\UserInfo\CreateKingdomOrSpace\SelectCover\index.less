.container {
  height: calc(70vh - 28px);
  position: relative;
  .title_box {
    position: relative;
    .title {
      font-size: 17px;
      color: #000;
      font-weight: 500;
      line-height: 24px;
      text-align: center;
    }
    .title_btn {
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      padding: 0 16px;
    }
  }

  .wrap {
    width: 100%;
    height: auto;
    padding-left: 12px;
    padding-right: 12px;
    box-sizing: border-box;

    .warp_list {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      flex-wrap: wrap;
      padding-top: 16px;
    }

    .warp_list_item {
      width: 163px;
      height: 96px;
      border-radius: 7px;
      border: 2px solid #fff;
      margin-bottom: 12px;
      overflow: hidden;
      position: relative;

      .CoverTitle {
        position: absolute;
        top: 18px;
        left: 12px;
        width: 96px;
        height: 31px;
        font-weight: 600;
        font-size: 13px;
        color: #FFFFFF;
        line-height: 15px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-word;
      }

      img {
        width: 161px;
        height: 94px;
        border-radius: 4px 4px 4px 4px;
        object-fit: cover;
      }
    }

    .warp_list_item_select {
      border: 2px solid #0095FF;
      position: relative;
      .warp_list_item_select_icon {
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url("~@/assets/GlobalImg/check_icon_2.png");
        background-size: 100% 100%;
        position: absolute;
        bottom: -2px;
        right: -2px;
      }

    }

    .warp_list_item_box {
      width: 100%;
      height: 100%;
      background: #F8F8F8;
      border-radius: 4px 4px 4px 4px;
      display: flex;
      align-items: center;
      justify-content: space-around;
      .addiconBox {
        text-align: center;
        color: #CBCBCB;
      }
      .addicon {
        font-size: 18px;
      }
    }
  }

  .enter_warp {
    width: 100%;
    height: 40px;
    padding-left: 16px;
    padding-right: 16px;
    margin-top: 50px;

    .enter_btn {
      width: 100%;
      height: 40px;
      background: #0095FF;
      border-radius: 20px 20px 20px 20px;
      font-weight: 400;
      font-size: 16px;
      color: #FFFFFF;
      line-height: 40px;
      text-align: center;
    }
  }
}
