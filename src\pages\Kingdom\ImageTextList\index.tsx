/**
 * 国王详情-图文列表
 * */
import React, {useEffect, useRef, useState} from 'react'
import { history, connect } from 'umi'
import {InfiniteScroll, Modal} from 'antd-mobile'
import styles from './index.less'
import ArticleCard from "@/components/ArticleCard";
import ExternalLinkCard from "@/components/ExternalLinkCard";
import PostCard from "@/components/PostCard";
import ForwardCard from "@/components/ForwardCard";
import classNames from "classnames"

import NoDataRender from '@/components/NoDataRender'       // 暂无数据

// tab签数据
const tabsDataSource = [
  { id: 1, name: '帖子' },
  { id: 2, name: '文章' },
  { id: 3, name: '外链' },
]

interface PropsType {
  tabType: any;
  KingdomId: any;
}
const Index: React.FC = (props: any) => {
  const { tabType,KingdomId,dispatch, kingdom } = props || {};
  const initalDataByGeKingdomList={
    pageNum: 1,
    pageSize: 30,
    total: 0,
  }
  const [ dataByGeKingdomList, setDataByGeKingdomList] = useState(initalDataByGeKingdomList); // 筛选条件
  const [ kingdomList, setKingdomList] = useState([]); // 列表数据
  const [ hasMore,setHasMore] = useState(false); // 更多数据状态
  const resultListDataRef = useRef(null);

  const [tab, setTab] = useState(kingdom.subTabKey || 1)

  // tab签切换
  const onChangeTabs = (id) => {
    setDataByGeKingdomList(initalDataByGeKingdomList);
    setTab(id)
    // store中保存当前tab切换值，防止去详情返回后tab会重置
    dispatch({
      type: 'kingdom/save',
      payload: {
        subTabKey: id,   // 王国详情-二级tab选中状态
      }
    })
  }



  useEffect(()=>{
    resultListDataRef.current = [];
    setKingdomList([])
    geKingdomList()
  },[tab])

  useEffect(()=>{
    resultListDataRef.current = kingdomList;
  },[kingdomList])

  // 获取王国查询图文信息
  const geKingdomList = async () => {
    // tab : 1:帖子    2:文章   3:外链
    let tabObj = { 1:'2', 2:'1', 3:'3' }
    let resByGeKingdomList = await dispatch({
      type: 'userInfoStore/geKingdomList',
      payload: {
        kingdomId: KingdomId,
        imageTextType:tabObj[tab], // 图文类型 1.文章 2.帖子 3.外链
        pageNum:  dataByGeKingdomList?.pageNum,
        pageSize: dataByGeKingdomList?.pageSize
      }
    })
    const { code,content } = resByGeKingdomList || {};
    const { pageNum,  resultList, total } = content || {}
    if (code == 200 && content) {
      if (Array.isArray(resultList) && resultList.length > 0) {
        let concatByResultList = resultListDataRef.current.concat(content.resultList);
        setKingdomList(concatByResultList);
        setHasMore(true);
        setDataByGeKingdomList({
          ...dataByGeKingdomList,
          pageNum: pageNum + 1,
          total:total==0?dataByGeKingdomList.total:total,
        })
      }else{
        setHasMore(false);
      }
    }else {
      setDataByGeKingdomList(initalDataByGeKingdomList);
      setHasMore(false);
    }
  }

  return (
    <div className={styles.tab_content_list}>
      {/* tab签 */}
      <div className={styles.tabs_wrap}>
        {
          tabsDataSource.map(item => {
            return (
              <span
                key={item.id}
                className={classNames(styles.tabs_item, {
                  [styles.checked]: tab == item.id,
                })}
                onClick={() => onChangeTabs(item.id)}
              >{item.name}</span>
            )
          })
        }
      </div>
      <div style={{paddingLeft: '12px', fontSize: '12px'}}>共 { dataByGeKingdomList?.total } 条内容</div>
      {
        kingdomList && kingdomList.length > 0 ?
          <div style={{background: '#F5F6F8'}}>
            {/* ----帖子---- */}
            {tab == 1 && <div>
              {Array.isArray(kingdomList) && kingdomList.map((item) => {
                return (
                  <PostCard
                    pageType='4'
                    item={item}
                    key={item.id}
                    isShowMoreOperate={false} // 是否展示点点点更多操作
                  />
                )
              })}
            </div>}

            {/* ----文章---- */}
            {tab == 2 && <div>
              {Array.isArray(kingdomList) && kingdomList.map((item) => {
                return (
                  <ArticleCard
                    item={item}
                    style={{background: '#fff'}}
                    key={item.id}
                  />
                )
              })}
            </div>}

            {/* ----外链---- */}
            {tab == 3 && <div>
              {Array.isArray(kingdomList) && kingdomList.map((item) => {
                return(
                  <ExternalLinkCard
                    item={item}
                    key={item.id}
                    isShowMoreOperate={false} // 是否展示点点点更多操作
                  />
                )
              })}
            </div>}

            {/* 滚动加载 */}
            {Array.isArray(kingdomList) && kingdomList.length > 0 &&
            <InfiniteScroll
              threshold={10}
              loadMore={geKingdomList}
              hasMore={hasMore}
            />
            }

            {
              // :
              // <div className={styles.none_data}>
              //   <img src={noDataImg} alt="" />
              //   暂无文章数据
              // </div>
            }
          </div>
          :
          <NoDataRender style={{marginTop: 50, paddingBottom: 70}}/>
      }

    </div>
  )
}

export default connect(({ userInfoStore,kingdom, loading }: any) => ({userInfoStore,kingdom, loading}))(Index)
