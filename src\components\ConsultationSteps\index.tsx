/**
 * @Description: 指导步骤条公共组件，移动端
 */
import React, { useState } from 'react'
import classNames from 'classnames'
import { QuestionCircleOutlined, CheckOutlined } from '@ant-design/icons'
import styles from './index.less'

// 完整服务流程弹窗
import CompleteProcessModal from './CompleteProcessModal'

// 图文，下单流程，1
export const consultationStepListOrderImageText = ['选择指导方式', '描述病例问题', '支付指导费用']
// 图文，服务流程，2
export const consultationStepListImageText = ['病例资料被查看', '问题被回复并对话', '结束指导·交易成功']
// 视频，下单流程，3
export const consultationStepListOrderVideo = ['选择指导方式', '描述病例问题', '提交指导单']
// 视频，服务流程，4
export const consultationStepListVideo = ['病例资料被查看', '预约视频会议', '视频沟通', '结束指导', '支付指导费用', '交易成功']
// 默认
const defaultList = ['选择指导方式', '描述病例问题', '支付/提交指导']

interface PropsType {
  type?: number,                        // 1 图文下单流程，2 图文服务流程，3 视频下单流程，4 视频服务流程，5 选择指导方式弹窗中/PC开始指导页
  processNode: any,                     // 值为指导订单的状态。流程节点(图文流程节点[1选择指导方式、2描述病例问题、3支付指导费用、4病例资料被查看、5问题被回复并对话、6结束指导交易成功];  视频流程节点[1选择指导方式、2描述病例问题、3提交指导单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、7结束指导、8确认并支付指导费用、9交易成功])
  isFinish?: any,                        // 当前节点是否完成(1是、0否)
}

const Index: React.FC<PropsType> = (props: any) => {
  const { type = 5, processNode = 1, isFinish = 0 } = props
  const [completeProcessVisible, setCompleteProcessVisible] = useState(false)
  const stepList = type == 1 ? consultationStepListOrderImageText
    : type == 2 ? consultationStepListImageText
      : type == 3 ? consultationStepListOrderVideo
        : type == 4 ? consultationStepListVideo
          : defaultList
  // 根据节点状态计算当前步骤
  const currentStep1 = type == 2 || type == 4 ? processNode - 3 : processNode
  const currentStep2 = (type == 1 || type == 2) && processNode == 6 && isFinish == 1 ? currentStep1 + 1
    : (type == 3 || type == 4) && processNode == 9 && isFinish == 1 ? currentStep1 + 1
      : currentStep1

  // 打开完整服务流程弹窗
  const completeProcessModalShow= () => {
    setCompleteProcessVisible(true)
  }

  // 关闭完整服务流程弹窗
  const completeProcessModalHide = () => {
    setCompleteProcessVisible(false)
  }

  return (
    <div className={styles.container}>
      <div className={styles.title_box}>
        <div className={styles.title}>{type == 2 || type == 4 ? '服务流程' : '下单流程'}</div>
        <div className={styles.title_btn} onClick={completeProcessModalShow}>
          <QuestionCircleOutlined/>
          查看完整服务流程
        </div>
      </div>
      <div className={styles.step_content}>
        {
          stepList.map((item, index) => (
            <div
              key={index}
              className={classNames(styles.step_item, {
                [styles.step_item_finished]: currentStep2 > index + 1,
                [styles.step_item_active]: currentStep2 == index + 1,
              })}
            >
              <div className={styles.step_item_wrap}>
                <div className={styles.step_item_icon_box}>
                  <div className={styles.step_item_icon}>
                    {
                      currentStep2 > index + 1 ? <CheckOutlined />
                      : currentStep2 < index + 1 ? index + 1 : ''
                    }
                  </div>
                </div>
                <div className={styles.step_item_line}></div>
              </div>
              <div className={styles.step_item_text}>{item}</div>
            </div>
          ))
        }
      </div>

      {/* 完整服务流程弹窗 */}
      <CompleteProcessModal
        visible={completeProcessVisible}
        onCancel={completeProcessModalHide}
      />
    </div>
  )
}

export default Index
