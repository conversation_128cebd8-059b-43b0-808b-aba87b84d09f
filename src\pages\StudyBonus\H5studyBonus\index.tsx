import React, { useState, useEffect, useRef } from 'react';
import { history, connect } from 'umi';
import styles from './index.less';
import { getOperatingEnv, priceFormat,getIsFirstPageInApp,backInApp,goToHomePage } from '@/utils/utils';
import {message, Spin} from 'antd';
import InfiniteScroll from 'react-infinite-scroller';
import { ImageViewer } from 'antd-mobile';
import dayjs from 'dayjs';
import NoDataRender from "@/components/NoDataRender";
import pdfIcon from '@/assets/Consultation/H5/pdf_icon.png'

const initStatePage = {
  pageNum: 1,
  hasMore: true, // 加载更多
  loadMore: false,
};
const initState = {
  total: 0,
  listDate: [],
};

const Index: React.FC = (props) => {
  const { global, dispatch, loading, userInfoStore } = props || {};
  const [state, setState] = useState(initState); // 列表数据
  const [statePage, setStatePage] = useState(initStatePage); // 当前分页
  const [loadingResultListByState, setLoadingResultListByState] = useState(null);
  const [friUserToScholarship, setFriUserToScholarship] = useState(false); // 获取学习金余额

  const { total, listDate } = state;
  const { pageNum, hasMore, loadMore } = statePage || {};
  const scrollParentRef = useRef<HTMLDivElement | null>(null);

  const UerInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  // 微信浏览器使用
  let OperatingEnv = getOperatingEnv();

  // 初始化数据
  useEffect(async () => {
    await getFriUserToScholarship(); // 获取用户个人主页展示学习金信息
    await getScholarshipDetailList(1); // 首次获取用户学习金明细新增、核销记录（H5、APP）
  }, []);

  // 获取用户个人主页展示学习金信息
  let getFriUserToScholarship = async () => {
    let resBygetFriUserToScholarship = await dispatch({
      type: 'userInfoStore/getFriUserToScholarship',
      payload: {
        wxUserId: (UerInfo && UerInfo.friUserId) || '',
      },
    });
    if (
      resBygetFriUserToScholarship &&
      resBygetFriUserToScholarship.code == 200 &&
      resBygetFriUserToScholarship.content
    ) {
      setFriUserToScholarship(resBygetFriUserToScholarship.content);
    }else if(
      resBygetFriUserToScholarship &&
      resBygetFriUserToScholarship.code == 422
    ) {
      message.error(resBygetFriUserToScholarship.msg ? resBygetFriUserToScholarship.msg : '学习金获取报错!')
    }
  };

  // 获取用户学习金明细新增、核销记录（H5、APP）
  let getScholarshipDetailList = async (pages) => {
    let resBygetScholarshipDetailList = await dispatch({
      type: 'userInfoStore/getScholarshipDetailList',
      payload: {
        wxUserId: (UerInfo && UerInfo.friUserId) || '',
        pageNum: pages || 1,
        pageSize: 10,
      },
    });
    const { code, content } = resBygetScholarshipDetailList || {};
    if (code == 200 && content) {
      const { pageNum, pageSize, total, resultList } = content || {};
      setState({
        total: total,
        listDate: Array.isArray(resultList) ? [...listDate, ...resultList] : listDate,
      });

      let hasMore = (Array.isArray(resultList) && resultList.length == 0) || !resultList;
      setStatePage({
        ...statePage,
        pageNum: pageNum,
        pageSize: pageSize,
        loadMore: false,
        hasMore: !hasMore,
      });
    }
  };

  // 加载更多
  let handleInfiniteOnLoad = () => {
    const pages = pageNum + 1;
    setStatePage({
      ...statePage,
      loadMore: true,
    });
    getScholarshipDetailList(pages);
  };


  // H5 查看PDF
  const renderFooter = (image: string, index: number,textImgList) => {
    let itemBytextImgList = Array.isArray(textImgList) && textImgList[index]
    const { proofName,proofUrlView } = itemBytextImgList || {}
    return (
      <div className={styles.footer}>
        <div
          className={styles.footerButton}
          onClick={() => {
            window.open(proofUrlView, '_blank');
          }}
        >
          查看PDF文件{proofName}
        </div>
      </div>
    )
  }

  // 查看大图
  const previewBigImage = (item) => {
    if (item && Array.isArray(item.proofs) && item.proofs.length > 0) {
      let textImgList = [];
      item.proofs.map((itByProofs) => {
        let proofUrlViewUrl = itByProofs?.proofUrlView;
        // 判定当前是pdf
        let isPdf = proofUrlViewUrl?.indexOf('.pdf') > -1;

        textImgList.push({
          ...itByProofs,
          imageUrlShow: isPdf ? pdfIcon : proofUrlViewUrl,
          isCover: 1,
        });
      });

      ImageViewer.Multi.show({
        defaultIndex: 0,
        images: textImgList.map((it) => it.imageUrlShow),
        getContainer: () => document.getElementById('StudyWarp'),
        renderFooter:(image,index)=>{
          let itemBytextImgList = Array.isArray(textImgList) && textImgList[index]
          const { proofName,proofUrlView } = itemBytextImgList || {}
          let isPdf = proofUrlView.indexOf('.pdf') > -1;
          if(isPdf) {
            return renderFooter(image, index, textImgList)
          }else {
            return null;
          }
        }
      });
    }
  };

  // 组件销毁时，关闭查看大图弹窗
  useEffect(() => {
    return () => {
      ImageViewer.clear();
    };
  }, []);

  // 点击返回按钮
  const goBack = () => {
    // APP环境中，如果当前页面是打开的第一个页面，正常返回会失效，此时调用app的返回
    if (getIsFirstPageInApp()) {
      backInApp()
      return
    }
    if (history.length > 2) {
      history.goBack()
    } else {
      history.replace('/')
    }
  }

  return (
    <div className={styles.Mobile_Wrap}>
      <Spin spinning={!!loading.effects['userInfoStore/getFriUserToScholarship']}>
        <div id={'StudyWarp'} className={styles.tab_title_Warp}>
          <div className={styles.StudyBonusBox}>
            {(OperatingEnv != 5 && OperatingEnv != 6) &&
              <div
                onClick={goBack}
                className={styles.BackWarp}>
                <i className={styles.nav_bar_icon}></i>
              </div>
            }

            <div className={styles.StudyBonusBox_num}>
              {friUserToScholarship && friUserToScholarship.currentNum && priceFormat(friUserToScholarship.currentNum)}
            </div>
            {friUserToScholarship && friUserToScholarship.validDate && (
              <div className={styles.StudyBonusBox_date}>
                有效期至
                {friUserToScholarship && friUserToScholarship.validDate &&
                  dayjs(friUserToScholarship.validDate, 'YYYY-MM-DD').format('YYYY年MM月DD日')}
              </div>
            )}
            <div className={styles.StudyBonusBg}></div>
          </div>
          <div>
            <div id={'StudyWarpContent'} className={styles.content_box}>
              <InfiniteScroll
                loadMore={handleInfiniteOnLoad}
                threshold={50}
                pageStart={1}
                initialLoad={false}
                hasMore={!loadMore && hasMore}
                useWindow={false}
                getScrollParent={() => document.getElementById('StudyWarpContent')}
                className={styles.scroll_box}
              >
                <div className={styles.title_name}>学习金收支明细</div>

                {/*暂无数据展示*/}
                {Array.isArray(listDate) && listDate.length == 0 &&
                  <div>
                    <NoDataRender text="暂无学习金明细" style={{padding: '100px 0', marginTop: 0}}/>
                  </div>
                }

                {Array.isArray(listDate) &&
                  listDate.map((item, index) => (
                    <>
                      {item && (
                        <div key={`${item.id}_${index}`} className={styles.item_content}>
                          <div className={styles.item_title_box}>
                            <div className={styles.item_title}> {item.reason} </div>
                            <div className={styles.item_title_num}>
                              {/*driftType: 1, // 1增  0减*/}
                              <span>{item.driftType == 1 ? ' + ' : ' - '}</span>
                              <span> {priceFormat(item.num)} </span>
                            </div>
                          </div>
                          <div className={styles.item_title_box_felx}>
                            <div className={styles.item_time}> {item.createDate} </div>
                            {item && item.proofs && item.proofs.length > 0 && (
                              <div
                                onClick={() => {
                                  previewBigImage(item);
                                }}
                                className={styles.btn_box}
                              >
                                查看凭证
                                <i className={styles.btn_icon}></i>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </>
                  ))}

                {/* ----滚动加载中的loading---- */}
                {!!loadMore &&
                  <div className={styles.loadingByInfiniteScroll}>
                    <div className={styles.loadingByInfiniteScrollContent}>
                      <div style={{marginRight:'10px'}}>加载中...</div>
                      <div>
                        <Spin spinning={true}></Spin>
                      </div>
                    </div>
                  </div>
                }
                {/* ----加载中的loading----- */}
              </InfiniteScroll>
            </div>
          </div>
        </div>
      </Spin>
    </div>
  );
};

export default connect(({ ConsultationList, pcAccount, loading }: any) => ({
  ConsultationList,
  pcAccount,
  loading,
}))(Index);
