/**
 * @Description: 实名认证-选择身份（PC）
 */
import React, { useState, useEffect } from 'react';
import { connect, history } from 'umi';
import { message, Spin } from 'antd';
import styles from './index.less';

// 图片icon
import right_arrow from '@/assets/GlobalImg/right_arrow.png' // 右箭头

import PcHeader from '@/componentsByPc/PcHeader' // 顶部导航栏

const Index: React.FC = (props: any) => {
  const { loading, dispatch } = props;

  const [identityTypeDict, setIdentityTypeDict] = useState([]) // 身份类型字典

  useEffect(() => {
    getIdentityTypeDict()
  }, [])

  // 获取认证的身份字典-实名认证版本
  const getIdentityTypeDict = () => {
    dispatch({
      type: 'userInfoStore/getIdentityTypeDict',
      payload: {},
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200) {
        setIdentityTypeDict(content || [])
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch(err => {})
  }

  // 点击身份类型
  const onClickIdentityTypeItem = (value) => {
    history.push(`/UserInfo/Certification/EditInformation?dictId=${value}`)
  }

  // 返回
  const goBack = () => {
    if (history.length > 1) {
      history.goBack()
    } else {
      history.replace('/')
    }
  }

  // loading
  const loadingGetIdentityTypeDict = !!loading.effects['userInfoStore/getIdentityTypeDict']

  return (
    <Spin wrapperClassName={styles.spin} spinning={loadingGetIdentityTypeDict}>
      <div className={styles.container}>
        {/* 头部 */}
        <PcHeader />
        {/* 内容 */}
        <div className={styles.content}>
          <div className={styles.content_inner}>
            {/* 标题导航条 */}
            <div className={styles.nav_bar}>
              <i onClick={goBack}></i>
              <span>个人认证</span>
            </div>

            {/* 身份类型list */}
            <div className={styles.wrap}>
              <div className={styles.role_list_wrap}>
                <div className={styles.role_list}>
                  {
                    identityTypeDict.map(item => {
                      return (
                        <div key={item.dictId} className={styles.role_item_wrap}>
                          <div className={styles.role_item} onClick={() => onClickIdentityTypeItem(item.dictId)}>
                            <img src={item.iconPath} width={48} height={48} alt=""/>
                            <div className={styles.role_item_name}>{item.dictName}</div>
                            <img src={right_arrow} width={16} height={16} alt=""/>
                          </div>
                        </div>
                      )
                    })
                  }
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Spin>
  )
}

export default connect(({ userInfoStore, loading }: any) => ({ userInfoStore, loading}))(Index)
