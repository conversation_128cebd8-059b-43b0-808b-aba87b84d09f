/**
 * @Description: PC-指导模块其他资料展示组件
 * @author: 赵斐
 */
import React from 'react';
import docxIcon from '@/assets/Consultation/H5/docx_icon.png'
import xlsxIcon from '@/assets/Consultation/H5/xlsx_icon.png'
import zipIcon from '@/assets/Consultation/H5/zip_icon.png'
import pptxIcon from '@/assets/Consultation/H5/pptx_icon.png'
import pdfIcon from '@/assets/Consultation/H5/pdf_icon.png'
import stlIcon from '@/assets/Consultation/H5/stl_icon.png'
import styles from './index.less';
interface PropsType {
  fileData: any,             // 病例数据（包含资料数据）
  consultationType:any,   // 指导类型
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { fileData ,consultationType } = props;
  /**
   * 获取文件格式icon
   * @param suffix   文件后缀
   * @returns
   */
  const annexFormatFun = (suffix: string): any => {

    switch (suffix) {
      case 'docx':
        return <img src={docxIcon} alt={suffix} />
      case 'doc':
        return <img src={docxIcon} alt={suffix} />
      case 'xlsx':
        return <img src={xlsxIcon} alt={suffix} />
      case 'xls':
        return <img src={xlsxIcon} alt={suffix} />
      case 'zip':
        return <img src={zipIcon} alt={suffix} />
      case 'pptx':
        return <img src={pptxIcon} alt={suffix} />
      case 'ppt':
        return <img src={pptxIcon} alt={suffix} />
      case 'pdf':
        return <img src={pdfIcon} alt={suffix} />
      case 'stl':
        return <img src={stlIcon} alt={suffix} />
      default:
        return ''
    }
  }

  /**
   * 文件大小处理
   * @param val
   * @returns
   */
  const sizeFun = (val:number)=>{
    let result = Math.ceil(val / 1024);
    return result.toFixed(1);
  }

  return (
    <div className={styles.wrap}>
      <p className={styles.title}>其他资料</p><div className={styles.content}>
        {
          Array.isArray(fileData) && fileData.length ? <>
            {
              fileData.map((item:any, index:number) => {
                // 图文、视频类型，0 图片，1 附件
                if(item.type == 0){
                  return null
                }
                return (
                  <div key={index}>
                    <div className={styles.content_list}>
                      <div className={styles.annex_format}>
                        {annexFormatFun(item.fileSuffix)}
                      </div>
                      <div className={styles.annex_content}>
                        <span className={styles.annex_name}>{item.fileName}.{item.fileSuffix}</span>
                        <span className={styles.annex_size}>{sizeFun(item.fileSize)}kb</span>
                      </div>
                      {
                        <a className={styles.annex_look} href={item.fileUrlShow} download={`${item.fileName}.${item.fileSuffix}`}>下载</a>
                      }
                    </div>
                  </div>
                )
              })
            }
          </> : null
        }

      </div>

    </div>
  )
}
export default Index
