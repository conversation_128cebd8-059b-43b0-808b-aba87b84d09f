/**
 * @Description: 预约空间卡片
 */
import React, {useEffect, useState} from 'react';
import { history, connect } from 'umi'
import { getOperatingEnv, goToHomePage } from '@/utils/utils'
import {Button, message, Modal, Spin} from 'antd';
import styles from './index.less'
import ModalWXsubscribe from './ModalWXsubscribe'
import Avatar from '@/pages/PlanetChatRoom/components/Avatar';
import ModalByUserTokenInvalid from "@/components/ModalByUserTokenInvalid";
import { getCurrentWXWorkApp } from '@/utils/utils'
import { liveAppointment } from '@/services/planetChatRoom'

interface PropsType {
  open: any,
  ReservationId: any,  // 预约空间id
  clearParams: any,
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {
    dispatch,
    ReservationId,  // 预约空间id
    open,
    clearParams,
  } = props
  const pathname = history.location.pathname

  const UerInfo = JSON.parse(localStorage.getItem('userInfo')) || {};
  const [ isModalOpen, setIsModalOpen] = useState(false);
  const [ spaceInfo,setSpaceInfo ] = useState(null);
  const [ isNotLogin,setIsNotLogin ] = useState(!(UerInfo && UerInfo.friUserId));
  // 是否打开预约成功弹窗
  const [isOpenByModalWXsubscribe,setIsOpenByModalWXsubscribe] = useState(false);
  // 是否打开去登录弹窗
  const [ModalVisibleByUserTokenInvalid,setModalVisibleByUserTokenInvalid] = useState(false);
  // loading
  const [LoadingBySpaceRoomReservationCard,setLoadingBySpaceRoomReservationCard] = useState(false);

  const {
    appointmentStartTime,//: "2024-07-04 14:21:00"
    compereUserId,      //: 522
    consultationId,     //: null
    gdp,                //: 33
    handUpStatusType,   //: null
    handUpType,         // : 1
    hostUserInfo,       //: {wxUserId: 522, imUserId: 'c0dd00e1f3634dd8b95e030b2ca79c0a', name: '李德越', postTitleDictName: '主治医师', headUrlShow: 'https://s1-test.5i5ya.com/bda68594e28cd8e430d11c3b….jpg?x-oss-process=image/resize,p_40/quality,q_50', …}
    id,                 //: 2231
    imAppId,            //: 1600003136
    imGroupId,          //: "@DMP#IM_GROUP_2231"
    imUserId,           //: "bc58f155244b5028dd96d182b32bd5af"
    imagePhotoPathShow, //: "https://s1-test.5i5ya.com/4ff07a37850c909f76cddc1379f84497/66922a86/platform/expertsImg/e33b75681f7141b58268f37e2fc015bf.jpeg?x-oss-process=image/resize,p_40/quality,q_50"
    imageTextId,        //: null
    initiatorUserInfo,  //: {wxUserId: 522, imUserId: 'c0dd00e1f3634dd8b95e030b2ca79c0a', name: '李德越', postTitleDictName: '主治医师', headUrlShow: 'https://s1-test.5i5ya.com/bda68594e28cd8e430d11c3b….jpg?x-oss-process=image/resize,p_40/quality,q_50', …}
    intro,              //: null
    isAppointment,      //: 0
    isCollect,          //: 0
    isHasWhiteBoardPush,//: 0
    isHavCourseware,    //: 0
    isKing,//: null
    isMute,//: 0
    isNeedPwd,//: 0
    isNoPasswordApply,//: 0
    isShowPassword,//: 0
    isSignIn,//: 0
    isSpectatorUse,//: 1
    joinRandStr,//: null
    kingdomId,//: null
    kingdomName,//: null
    liveStartTime,//: null
    name,//: "创建预约直播"
    password,//: null
    pv,//: 33
    recordStartTime,//: null
    recordType,//: 0
    roomId,//: 12837
    sendApplauseCount,//: 0
    sendCallCount,//: null
    sendFlowersCount,//: 0
    spaceAdvertisingUrlShow,//: null
    spaceCoverUrlShow,//: "https://s1-test.5i5ya.com/05122ae1b57ca2c8271bb4660f12315d/6684fb86/dmp/spaceCover/live_broadcast_01.png"
    starSpaceType,//: 1
    status,         // 状态：1直播中、2预约中、3弹幕轰炸中
    trtcAppId,      //: 1600003136
    userSig,        //: "eJwtjdsKgkAARP9lXw3Zu6vQS6BlRAjuD2h7aQltMS9R9O*JOm9zhpn5Ankpw1F3IAE4hGC3eKd02zvjFlzfmDCIMUxpzSAWSsVcIYFrgmvFKrN1XupRee8USBCHswgifE3027tOg0RwCuGKetfMAEUojmOEIrpNODv-nQefBXcrn2neXrPCpkU6SUP5MAby6Kfmxg7DpyF5ebJ78PsDmxQ30g__"
    videoList,      //: null
    videoSecond,    //: null
    wxUserId,       //: 3
  } = spaceInfo || {};




  useEffect(()=>{
    setIsModalOpen(open)
  },[open])

  useEffect(()=>{
    if(!!ReservationId){ getSpaceInfo(); }
  },[ReservationId])

  // 获取空间信息
  const getSpaceInfo = async () => {
    let wxuserId = UerInfo && UerInfo.friUserId;
    await setLoadingBySpaceRoomReservationCard(true);
    let response = await dispatch({
      type: 'PlanetChatRoom/getSpaceInfo',
      payload: {
        spaceId: ReservationId,
        wxUserId: wxuserId,
      },
    });
    await setLoadingBySpaceRoomReservationCard(false);
    const { code,content,msg } = response || {};
    if (code == 200 && content) {
      setSpaceInfo(content);
    }else {
      message.warning(msg ? msg : '获取直播信息失败');
      setSpaceInfo(null);
      setIsModalOpen(false);
      setIsOpenByModalWXsubscribe(false);
      setModalVisibleByUserTokenInvalid(false);
    }
  }

  const liveAppointmentByfunc = async () => {
    // 空间预约的接口上加上个企微id的参数的
    // getOperatingEnv() == 7 是企微环境 getCurrentWXWorkApp().appid 是企微id
    let env = getOperatingEnv();
    let wcUserId = localStorage.getItem('wcUserId')
    let params = {
      spaceId: spaceInfo.id,
      wxUserId: wxUserId,
      appointmentType: 1, // 1预约 2取消预约
      wcAppId: env == 7 ? getCurrentWXWorkApp()?.appid : null, // 企微ID  APPID
      wcUserId:wcUserId,
    }
    await setLoadingBySpaceRoomReservationCard(true);
    const response = await liveAppointment(params);
    await setLoadingBySpaceRoomReservationCard(false);
    const { code,content } = response || {}
    if (code == 200) {
      setIsModalOpen(false);
      setIsOpenByModalWXsubscribe(true);
    }else {
      message.warning('预约失败,请稍后重试!')
    }
  }

  const handleOk = () => {
    setIsModalOpen(false);
    clearParams && clearParams();
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    clearParams && clearParams();
  };

  return (
    <div className={styles.warp_SpaceRoomReservationCard}>
      <Modal
        title={false}
        footer={false}
        style={{
          top:'1px'
        }}
        mask={false}
        maskClosable={false}
        className={styles.Modal_SpaceRoomReservationCard}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        //getContainer={false}
      >
        <Spin spinning={!!LoadingBySpaceRoomReservationCard}>
          <div className={styles.modalContent}>
            <div onClick={handleCancel} className={styles.close}></div>
            {/* status 状态：1直播中、2预约中、3弹幕轰炸中 , isAppointment 0:未预约 1:已经预约 */}
            {(status == 2 && isAppointment != 1) && <div className={styles.titleSpan}>您扫码的直播未开始，点击预约</div>}
            {(status == 2 && isAppointment == 1) &&  <div className={styles.titleSpan}>您预约的直播暂未开始，可以先逛逛哦～</div>}
            {status == 3 && <div className={styles.titleSpan}>您扫码的直播已结束</div>}
            {status == 1 && <div className={styles.titleSpan}>您扫码的直播进行中！{pv}人正在热聊～</div>}
            <div className={styles.card_info}>
              <div className={styles.card_img}>
                <img src={spaceCoverUrlShow}></img>
              </div>
              <div className={styles.card_right}>
                <div className={styles.card_title_warp}>
                  <div className={styles.card_title}>
                    {name}
                  </div>
                  <div className={styles.card_time}>
                    {appointmentStartTime || liveStartTime}
                  </div>
                </div>
                <div className={styles.card_res_btn_warp}>
                  <div className={styles.profileWarp}>
                    {/*<div className={styles.profile_img}>
                      <img src={imagePhotoPathShow}></img>
                    </div>*/}
                    <div className={styles.profile_img}>
                      <Avatar userInfo={hostUserInfo} size={24} isPc={true}/>
                    </div>
                    <div className={styles.name}>{hostUserInfo && hostUserInfo.name}</div>
                  </div>
                  <div>
                    {/* status 状态：1直播中、2预约中、3弹幕轰炸中  isAppointment 0:未预约 1:已经预约 */}
                    { (status == 2 && (isAppointment == 0 || isNotLogin)) &&
                      <div
                        className={styles.btn_Res}
                        onClick={()=>{
                          //[判定登录] 判定是否已登录,未登录则弹出登录弹窗
                          if (isNotLogin) {
                            setIsModalOpen(false);
                            setModalVisibleByUserTokenInvalid(true);
                            return null;
                          }
                          liveAppointmentByfunc();
                        }}
                      >预约直播</div>
                    }
                    { (status == 2 && isAppointment == 1) &&
                      <div
                        className={styles.btn_Res_disabled}
                        onClick={()=>{
                          // setIsModalOpen(false);
                          // setIsOpenByModalWXsubscribe(false);
                        }}
                      >已预约</div>
                    }
                    { status == 3 && Array.isArray(videoList) && videoList.length > 0 &&
                      <div
                        className={styles.btn_Res}
                        onClick={()=>{
                          clearParams && clearParams();
                          history.push(`/PlanetChatRoom/Live/${ReservationId}`)
                        }}
                      >查看回放</div>
                    }
                    { status == 1 &&
                      <div
                        className={styles.btn_Res}
                        onClick={()=>{
                          clearParams && clearParams();
                          history.push(`/PlanetChatRoom/Live/${ReservationId}`)
                        }}
                      ><i className={styles.SpaceRoomReservationCard_btn_liveing}/>直播中
                      </div>
                    }
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Spin>
      </Modal>
      {/* 关注订阅 */}
      <ModalWXsubscribe
        open={!!isOpenByModalWXsubscribe}
        spaceInfo={spaceInfo}
        clearParams={clearParams}
      />
      {/* 未登录 */}
      <ModalByUserTokenInvalid
        open={!!ModalVisibleByUserTokenInvalid}
        title={'立即登录，抢先预约精彩直播!'}
        isNotIcon={true}
      />
    </div>
  )
}

export default connect(({ activity, loading }: any) => ({activity, loading}))(Index)
