.warp_content {
  min-width: 320PX;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #FFFFFF;
  // padding-left: 10px;
  // padding-right: 10px;

  .content {
    max-width: 750PX;
    min-width: 320PX;
    position: relative;
    width: 100%;
    height: 100%;
    overflow: auto;

    .contentByNotBottom {
      width: 100%;
      height: calc(100% - 40px);
      // overflow: hidden;
    }
    .contentByNotBottom_isHorizontal {
      width: 100%;
      height: 100%;
      // overflow: hidden;
    }

    .video_content {
      width: 100%;
      // height:57VW;
      height:100vh;
      // aspect-ratio: 16/9;
      background: #999999;
      position: relative;
      // background: transparent;
      overflow: hidden;

      .video_ReturnIcon_Warp {
        width: 100%;
        height: 48px;
        background: linear-gradient(180deg, #000000 0%, rgba(0,0,0,0) 100%);
        border-radius: 0px 0px 0px 0px;
        opacity: 1;
        position: absolute;
        top: 0px;
        left: 0px;
        z-index: 10;
      }
      .ReturnIconBtn_warp {
        padding-left: 17px;
        padding-right: 17px;
        padding-top: 17px;
        width: 34px;
      }

      .ReturnIconBtn {
        width: 9px;
        height: 17px;
        background: url('../../../../assets/PlanetChatRoom/SpatialDetail_Return_icon_btn.png');
        background-size: 9px 17px;
        background-repeat: no-repeat;
        opacity: 1;
        display: block;
        // position: absolute;
        // left: 17px;
        // top: 17px;
        // margin-left: 17px;
        // margin-top: 17px;
        cursor: pointer;
        user-select: none;
      }

      .isShowCameraList {
        position: absolute;
        top: 0px;
        right: -0px;
      }

      .Live_Video {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }

      .CameralistWarp {
        width: 100%;
        height: 100%;
        background: #222;
        .Cameralist {
          width: 100%;
          height: 100%;
          display: flex;
          flex-wrap: wrap;
          flex-direction: row;
          justify-content: center;
          align-content: center;
          .CameraItem {
            width: 25%;
            height: 25%;
            // aspect-ratio: 16/9;
            // border: 0.25PX solid #fff;
          }
        }
      }

      .default_Bg {
        width: 100%;
        height: 100%;
        background: url("https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/PlanetChatRoom_HorizontalLiveRoom_Live_bg.png") no-repeat;
        background-size: contain;
        overflow: hidden;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .spaceCoverUrlShow {
        width: 100%;
        // height: 100%;
      }

      .spaceCoverUrlShowByGif {
        width: unset;
        height: 100%;
      }

      .VideoWarp {
        width: 100%;
        height:100%;
        // aspect-ratio: 16/9;
        // height: 100%;
        overflow: hidden;
        background: #000;
        position: absolute;
        top: 0;
        left: 0;
        user-select: none;

        .video_container {
          width: 100%;
          height:100%;
          // height:unset;
          // aspect-ratio: 16/9;
        }

        :global {
          .video-js .vjs-tech {
            height: 100%;
            // aspect-ratio: 16/9;
          }

          .video-react .video-react-big-play-button {
            display: none;
          }

          .video-react-has-started .video-react-control-bar {
            display: none;
          }

          .vjs-has-started .vjs-control-bar, .vjs-audio-only-mode .vjs-control-bar {
            display: none;
          }
          .vjs-control-bar {
            display: none;
          }
          .video-js .vjs-big-play-button {
            display: none;
          }
          /*.vjs-poster {
            display: none;
          }*/
        }
      }

      .video {
        width: 100%;
        height: 100%;
        /*position: absolute;
        top: 0;
        left: 0;*/
      }

      .LiveViewPortControlsIcon {
        // width: 160px;
        height: 65px;
        position: absolute;
        bottom:0px;
        left: 30px;
        display: flex;

      }

      .LiveViewPortControlsWarp {
        width: 160px;
        height: 65px;
        position: absolute;
        bottom:0px;
        right:0px;
        display: flex;

        .SpatialDetail_LiveViewPort_openDanmu {
          width: 40px;
          height: 40px;
          display:inline-block;
          background: url('../../../../assets/PlanetChatRoom/SpatialDetail_LiveViewPort_openDanmu.png') no-repeat;
          background-size: 40px 40px;
          margin-right: 31px;
          cursor: pointer;
          user-select: none;
        }
        .SpatialDetail_LiveViewPort_FullScreen {
          width: 27px;
          height: 27px;
          display:inline-block;
          background: url('../../../../assets/PlanetChatRoom/SpatialDetail_LiveViewPort_FullScreen.png') no-repeat;
          background-size: 27px 27px;
          position: relative;
          top: 3px;
          cursor: pointer;
          user-select: none;
        }
      }

      .LiveSendFlowers {
        width: 74px;
        height: 39px;
        background: rgba(12,14,43,0.4);
        border-radius: 0px 24px 24px 0px;
        position: absolute;
        left: 0px;
        top: 75px;
        padding-left: 10px;

        .LiveSendIconWarp {
          display: flex;
          align-items: center;
          height: 100%;

          .SpatialDetail_flowers_interaction {
            width:24px;
            height: 24px;
            display:inline-block;
            position: relative;
            // top: -7px;
            margin-right: 7px;
          }

          .LiveSendIconText {
            margin-top: -10px;
            height: 12px;
            font-size: 12px;
            font-weight: 400;
            color: #FFFFFF;
            line-height: 12px;
          }
        }
      }

      .video_box_warp {
        width: 100%;
        height: 50%;
        position: absolute;
        background: transparent;
        top: 0px;
        z-index: 1;

        .video_box {
          width: calc(100% - 100px);
          height: 100%;
          background: transparent;
        }
        .video_box_hidden {
          visibility:hidden;
        }
      }

      .video_box_warp_hidden {
        visibility:hidden;
      }

    }

    .video_ModeratorControl {
      position: absolute;
      bottom: 0px;
      width: 100%;
      height: 48px;
      background: linear-gradient(180deg, rgba(0,0,0,0) 0%, #000000 100%);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-left: 5px;
      padding-right: 5px;
      user-select: none;
      font-size: 13px;

      .PlayCircleOutlined_live {
        width: 17px;
        height: 17px;
        font-size: 17px;
        user-select: none;
        cursor: pointer;
        color: #fff;
      }

      .finish_live {
        width: 17px;
        height: 17px;
        display: inline-block;
        background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_close_live_btn.png') no-repeat;
        background-size: 17px 17px;
      }

      .video_ModeratorControl_box {
        // min-width: 50px;
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;

        .video_ModeratorControl_box_icon {
          // width: 32px;
          height: 16px;
          margin-bottom: 4px;
          // background: #fff;
          // border-radius:50%;
        }
        .video_ModeratorControl_box_text {
          color: #fff;
        }
        .video_ModeratorControl_box_text_Blue {
          color: #9ECEFF;
        }
        .video_ModeratorControl_box_text_red {
          color: #FF7F67
        }
      }

      /*pre {
        font-family:'arial';
        line-height:auto;
      }*/

      .video_ModeratorControl_box_right {
        float: right;
      }

      .playBtn {
        width: 14px;
        height: 14px;
        background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_play_Icon.png') no-repeat;
        background-size: cover;
        opacity: 1;
        user-select: none;
        cursor: pointer;
        margin-right: 14px;
        cursor: pointer;
        user-select: none;
      }

      .PauseBtn {
        width: 14px;
        height: 14px;
        background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_Pause_Icon.png') no-repeat;
        background-size: cover;
        opacity: 1;
        user-select: none;
        cursor: pointer;
        margin-right: 14px;
        cursor: pointer;
        user-select: none;
      }

      .video_Progress_bar_warp {
        display: flex;
        align-items: center;
        flex: 1;
      }
      .video_Progress_bar {
        flex: 1;
      }
      .time_Progress {
        font-size: 12px;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 12px;
        position: relative;
      }

      .FullScreenBtn {
        cursor: pointer;
        user-select: none;
        width: 14px;
        height: 14px;
        display: inline-block;
        background: url('../../../../assets/PlanetChatRoom/SpatialDetail_Full_screen_btn.png') no-repeat;
        background-size: 14px 14px;
        margin-left: 14px;
      }
    }

    .video_interaction {
      margin-top: 15px;
    }
  }
}

.button-container {
  width: 260px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-bottom: 10px;
  &:last-child {
    margin-bottom: -10px;
  }
  &.mobile-device {
    width: 100%;
  }
  button {
    min-width: 125px;
    height: 36px;
    margin-bottom: 10px;
    background-color: #006eff;
    &:hover {
      background-color: #006eff;
    }
    &.forbidden {
      background-color: #eeeeee;
      color: #bbbbbb;
      cursor: auto;
      &:hover {
        background-color: #eeeeee;
      }
    }
    &.full-width {
      width: 100%;
    }
  }
}

.video_advertisement {
  width: 100%;
  height: 57px;
  background: #10174B;
  position: relative;
  overflow: hidden;

  .video_advertisement_img {
    width: 100%;
    height: 100%;
  }

  .video_advertisement_close {
    position: absolute;
    top:-1PX;
    right:0px;
    cursor: pointer;
    user-select: none;
    width: 24px;
    height: 24px;
    background: rgba(0,0,0,0.5);
    border-radius: 0px 0px 0px 8px;
    opacity: 1;
    display: flex;
    justify-content: center;
    align-items: center;

    .video_advertisement_close_icon {
      display: block;
      width: 12px;
      height: 12px;
      background: url("../../../../assets/PlanetChatRoom/SpatialDetail_Advertisement_Off_Icon.png");
      background-size: 12px 12px;
      opacity: 1;
    }
  }
}

.LiveBookingWarp {
  width: 100%;
  height: 50px;
  background: #EDF9FF;
  padding-left: 12px;
  padding-right: 12px;
  display: flex;
  justify-content: space-between;
  .LiveBookingLeft {
    width: unset;
    height: 100%;
    display: flex;
    padding-top: 8px;
    padding-bottom: 8px;

    .LiveBookingBox {

      .LiveBookingTitle {
        display: flex;
        align-items: center;

        .LiveBookingAppmentTitle {
          font-size: 15px;
          font-weight: 600;
          color: #0095FF;
          margin-right: 7px;
        }
        .LiveBookingAppmentInfoTitle {
          font-size: 12px;
          font-weight: 400;
          color: #0095FF;
          cursor: pointer;
          user-select: none;
        }
        .LiveBookingAppmentInfoTitleIcon {
          display: inline-block;
          width: 12px;
          height: 12px;
          top: 1px;
          position: relative;
          background: url('../../../../assets/PlanetChatRoom/SpatialDetail_LiveBookingAppmentInfoTitle_Icon.png') no-repeat;
          background-size: cover;
          cursor: pointer;
          user-select: none;
        }
      }
    }

    .LiveBookingClosedSpace {
      font-size: 12px;
      font-weight: 400;
      color: #999999;
      line-height: 14px;
      white-space:nowrap;
      // max-width:172px;
      overflow:hidden;
      text-overflow:ellipsis;
    }
    .LiveBookingClosedSpaceMaxWidth {
      max-width:180px;
    }

    .LiveBookingClosedSpace_btn {
      font-size: 12px;
      font-weight: 400;
      color: #FF5F57;
      line-height: 14px;
      align-items: center;
      display: flex;
      margin-left: 10px;
      cursor: pointer;
      user-select: none;

      .LiveBookingClosedSpaceIcon {
        width: 13px;
        height: 13px;
        background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_close_live_btn.png') no-repeat;
        background-size: 13px 13px;
        display: inline-block;
        margin-right: 2px;
      }

    }
  }

  .LiveBookingRight {
    display: flex;
    align-items: center;
  }

  .LiveBookingBtn {
    width: 84px;
    height: 30px;
    background: #0095FF;
    border-radius: 16px 16px 16px 16px;
    opacity: 1;
    text-align: center;
    line-height: 30px;
    font-size: 13px;
    color: white;
    cursor: pointer;
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .LiveBookingBtn:active {
    background: #0175c7;
  }

  .SpatialDetai_StartLive_Icon {
    width: 16px;
    height: 16px;
    background: url('../../../../assets/PlanetChatRoom/SpatialDetai_StartLive_Icon.png') no-repeat;
    background-size: 16px 16px;
    display: inline-block;
  }



  .CancelLiveBookingBtn {
    width: 84px;
    height: 30px;
    background: #FF5F57;
    border-radius: 16px 16px 16px 16px;
    opacity: 1;
    text-align: center;
    line-height: 30px;
    font-size: 13px;
    color: white;
    cursor: pointer;
    user-select: none;
  }

}

.video_TitleWarp {
  padding-top: 10px;
  padding-bottom: 10px;
  padding-left: 17px;
  padding-right: 17px;
  display: flex;
  justify-content: space-between;
  .video_Title_box_left {
    display: flex;
    align-items: center;

    .video_Title_box_left_avatar {
      width: 32px;
      height: 32px;
      background: #F9B4E3;
      opacity: 1;
      border-radius: 50%;
      // margin-right: 8PX;
      overflow: hidden;
    }

    .video_Title_box_left_avatar_img {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      overflow: hidden;
    }

    .head_sculpture_name {
      width: 32px;
      height: 32px;
      font-size: 16px;
      font-weight: 500;
      color: #FFFFFF;
      display: flex;
      align-items: center;
      justify-content: center;
      white-space: nowrap;
    }

    .video_Title_box_left_title {
      display: flex;
      font-size: 12px;
      align-items: flex-end;

      :nth-child(1) {
        margin-top: 5px;
      }

      .video_Title_box_left_title_name_text {
        max-width: 130px;
        overflow:hidden;
        text-overflow:ellipsis;
        white-space:nowrap;
        font-size: 14px;
        font-weight: 400;
        color: #0095FF;
        line-height: 16px;
      }
    }

    .video_Title_box_left_title_follow_btn {
      margin-left: 10px;
      height: 17px;
      font-size: 12px;
      font-weight: 400;
      color: #0095FF;
      line-height: 17px;
      padding-left: 8px;
      padding-right: 8px;
      background: #EDF9FF;
      border-radius: 14px 14px 14px 14px;
      cursor: pointer;
      user-select: none;
    }

    .video_Title_box_left_title_Unfollow_btn {
      margin-left: 10px;
      height: 17px;
      font-size: 12px;
      font-weight: 400;
      line-height: 17px;
      padding-left: 8px;
      padding-right: 8px;
      border-radius: 14px 14px 14px 14px;
      cursor: pointer;
      user-select: none;
      color: #CCCCCC;
      background: #F5F5F5;
    }
  }
  .video_Title_box_Right {
    display: flex;
    justify-content: flex-end;

    .box_icon_warp {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-left: 15px;
      color: #C0C3CB;
      cursor: pointer;
      user-select: none;

      .box_icon_title {
        color: #999;
        font-size: 12px;
      }
      .box_icon_title_Blue {
        color: #009DFF;
        font-size: 12px;
      }

      .SpatialDetail_Collect_btn {
        width: 20px;
        height: 20px;
        background: url("../../../../assets/PlanetChatRoom/SpatialDetail_Collect_btn.png");
        background-size:20px 20px;
        margin-bottom: 3px;
      }

      .SpatialDetail_Collect_btn_Open {
        width: 20px;
        height: 20px;
        background: url("../../../../assets/PlanetChatRoom/SpatialDetail_Collect_btn_Open.png");
        background-size:20px 20px;
        margin-bottom: 3px;
      }

      .SpatialDetail_ClockIn_btn {
        width: 20px;
        height: 20px;
        background: url("../../../../assets/PlanetChatRoom/SpatialDetail_ClockIn_btn.png");
        background-size:20px 20px;
        margin-bottom: 3px;
      }

      .SpatialDetail_ClockIn_btn_complete {
        width: 20px;
        height: 20px;
        background: url("../../../../assets/PlanetChatRoom/SpatialDetail_ClockIn_btn_complete.png");
        background-size:20px 20px;
        margin-bottom: 3px;
      }

      .SpatialDetail_Share_btn {
        width: 20px;
        height: 20px;
        background: url("../../../../assets/PlanetChatRoom/SpatialDetail_Share_btn.png");
        background-size:20px 20px;
        margin-bottom: 3px;
      }


    }
  }
}


.video_TitleWarp_title {
  padding-top: 0px;
  padding-bottom: 10px;
  padding-left: 17px;
  padding-right: 17px;
  display: flex;
  justify-content: space-between;
  border-bottom: 0.5PX solid #E1E4E7;

  .video_Title_box_left {
    width: 100%;

    .video_Title_box_Lefttop_nameBySpaceInfo {
      display: flex;
    }

    .video_Title_box_Lefttop {
      font-size: 16px;
      font-weight: 500;
      color: #0C0E2B;
      line-height: 16px;
      word-break: break-all;
      display: flex;
      justify-content: space-between;

      .Icon_ShowSpatialIntroduction {
        width:20px;
        height:20px;
        background: url("../../../../assets/PlanetChatRoom/SpatialDetai_Icon_ShowSpatialIntroduction.png");
        background-size:20px 20px;
        display: inline-block;
        flex-shrink: 0;
      }
      .Icon_ShowSpatialIntroduction_Open {
        // 翻转
        transform: scaleY(-1);
      }

    }

    .video_Title_box_LeftBottom {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // margin-bottom: 7px;
      margin-top: 7px;

      .left_content {
        flex: 1;
        display: flex;
        align-items: center;
        font-size: 12px;
        font-weight: 400;
        color: #999999;
        line-height: 12px;
      }

      .video_Title_box_LeftBottom_icon {
        width: 5px;
        height: 8px;
        display: inline-block;
        background: url("../../../../assets/PlanetChatRoom/PlanetChatRoom_Back_icon.png") no-repeat;
        background-size: contain;
        transform: rotateY(180deg);
        position: relative;
        // top: 5px;
        margin-left: 6px;
        font-size: 12px;
      }
    }

    .video_Title_box_LeftInfo {
      margin-top: 8px;
      color: #999999;
    }

    .video_Title_box_LeftInfo_hidden {
      margin-top: 8px;
      color: #999999;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }


    .LiveViewPortControls_OnlinePopulation {
      .LiveViewPortControls_OnlinePopulation_icon {
        position: relative;
        top: 1px;
        display: inline-block;
        width: 12px;
        height: 12px;
        font-size: 12px;
        background: url('../../../../assets/PlanetChatRoom/SpatialDetail_TitleWarp_title_userPopularity.png') no-repeat;
        background-size: 12px 12px;
        margin-right: 5px;
      }
      .LiveViewPortControls_OnlinePopulation_text {
        display: inline-block;
        font-size: 12px;
        font-weight: 400;
        color: #999999;
      }
    }

    .kingdomNameSpanWarp {
      display: flex;
      align-items: center;
      cursor: pointer;
      user-select: none;
    }

    .kingdomNameSpan {
      margin-left: 5PX;
      margin-top: 2PX;
      max-width: 120px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .video_Title_box_right {
    flex-shrink: 0;
    .SpatialDetai_video_pwd_Icon {
      width: 12px;
      height: 12px;
      background: url('../../../../assets/PlanetChatRoom/SpatialDetai_video_pwd_Icon.png');
      background-size: 12px 12px;
      display: inline-block;
      margin-right: 2px;
      vertical-align: text-top;
    }

    .video_Title_box_pwd {
      font-size: 12px;
      font-weight: 400;
      color: #0095FF;
      line-height: 14px;
      display: flex;
    }
  }
}

.personnel_list_Warp_Region {
  display: flex;
  .personnel_list_warp {
    width: calc(100% - 147px);
    height: calc(100% + 10px);
    display: flex;
    padding-left: 34px;
    padding-right: 34px;
    align-items: center;
    // justify-content: space-between;
    justify-content: flex-start;
    overflow-x: auto;
    // box-shadow: 1 0 1px rgba(0, 0, 0, 0.12);
  }

  .personnel_list_warp::-webkit-scrollbar {
    display: none;
  }

  .personnel_BreakWheat {
    width: 147px;
    height: 100%;
    // background: rgba(174, 173, 173, 0.25);
    // box-shadow: 5px 10px 49px 1px rgba(53,51,97,0.17);
  }

  .personnel_list_item {
    width: 120px;
    margin-right: 5px;
    margin-left: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .personnel_list_item_icon {
      width: 96px;
      height: 96px;
      border-radius: 50%;
      border: 1px solid transparent;
      margin-bottom: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 2PX;

      .personnel_list_item_icon_avatar {
        width: 100%;
        height: 100%;
        // border: 1px solid transparent;
        border-radius: 50%;
        background: url('../../../../assets/GlobalImg/default_head_picture.png') no-repeat;
        background-size: contain;
      }
    }

    .personnel_list_item_icon_Active {
      border: 1px solid #009DFF;
    }

    .personnel_list_item_text {
      font-size: 20px;
      font-weight: 500;
      color: #0C0E2B;
      line-height: 26px;

      .SpatialDetail_MicrophoneDefault_btn_Lianmai {
        display: inline-block;
        width: 17px;
        height: 17px;
        background: url('../../../../assets/PlanetChatRoom/SpatialDetail_MicrophoneDefault_btn_Lianmai.png');
        background-size: 17px 17px;
        margin-left: 3px;
        vertical-align: initial;
      }

      .SpatialDetail_MicrophoneDefault_btn_Lianmai_close {
        display: inline-block;
        width: 17px;
        height: 17px;
        background: url('../../../../assets/PlanetChatRoom/SpatialDetail_MicrophoneDefault_btn_Lianmai_close.png');
        background-size: 17px 17px;
        margin-left: 3px;
        vertical-align: initial;
      }

      .SpatialDetail_MicrophoneOn_btn_Lianmai {
        display: inline-block;
        width: 17px;
        height: 17px;
        background: url('../../../../assets/PlanetChatRoom/SpatialDetail_MicrophoneOn_btn_Lianmai.png');
        background-size: 17px 17px;
        margin-left: 3px;
        vertical-align: initial;
      }
    }

    .follow {
      cursor: pointer;
      user-select: none;
      font-size: 20px;
      line-height: 20px;
      margin-top: 6px;
      background: #E9F6FF;
      color: #2357BA;
      border-radius: 16px;
      padding-top: 6px;
      padding-bottom: 6px;
      width: 72px;
      text-align: center;
    }

    .follow_complete {
      background: #F8F8F8;
      color: #C0C3CB;
    }
  }
}

.reminder_warp {
  display: flex;
  text-align: center;
  align-items: center;
  justify-content: flex-start;
  padding-left: 5px;
  padding-top: 7px;
  padding-bottom: 7px;
  margin-bottom: 7px;
  background: #F5F6F8;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;

  .reminder_icon {
    width: 16px;
    height: 16px;
    background: url('../../../../assets/PlanetChatRoom/SpatialDetail_announcement_icon.png');
    background-size: 16px 16px;
  }
  .reminder_text {
    width: calc(100% - 22px);
    font-weight: 400;
    text-align: left;
    font-size: 12px;
    color: #999999;
    line-height: 14px;
    padding-left: 10px;
  }
}

.InputWarp {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding-left: 34px;
  padding-right: 34px;
  .InputWarp_box {
    flex: 1;
    margin-right: 10px;
    width: 50%;

    .InputWarp_input {
      width: 100%;
      overflow: hidden;//溢出隐藏
      white-space: nowrap;//禁止换行
      text-overflow: ellipsis;//...
    }
  }
}

.Warp_msg_content {
  padding-top: 15px;
  padding-bottom: 5px;
  padding-left: 17px;
  padding-right: 17px;
  width: 100%;
}

.msg_content_tab_warp {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.Warp_msg_Al_guests {
  display: flex;
  align-items: center;
  margin-top: -19px;

  .Warp_msg_Al_guests_text {
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    line-height: 16px;
    margin-right: 5px;
  }
}

.msg_content_tab {
  display: flex;
  margin-bottom: 10px;
  box-shadow: 0px 10px 20px rgba(255, 255, 255, 1), 0px 0px 20px rgba(255, 255, 255, 0);

  .msg_content_tab_item {
    font-size: 13px;
    font-weight: 600;
    color: #000000;
    line-height: 13px;
    justify-content: center;
    align-items: center;
    display: flex;
    flex-direction: column;
    margin-right: 10px;
    cursor: pointer;
    user-select: none;
    position: relative;
    .HandUpListLength {
      display: block;
      width: 14px;
      height: 14px;
      border-radius: 50%;
      background: rgba(251, 65, 65, 0.8);
      position: absolute;
      font-size: 12px;
      line-height:14px;
      text-align: center;
      overflow: hidden;
      color: #FFFFFF;
      top: -1px;
      right: -18px;
      z-index: 10;
    }
  }
  .msg_content_tab_item_line {
    width: 10px;
    height: 3px;
    background: #000000;
    border-radius: 3px 3px 3px 3px;
    opacity: 1;
    margin-top: 5px;
  }

  .msg_content_tab_item_line_placeholder {
    width: 10px;
    height: 3px;
    opacity: 1;
    margin-top: 5px;
  }

  .msg_content_tab_item_line_notActive {
    display: none;
  }
}

.GuestlistWarp {
  width: 100%;
  max-height: calc(100% - 500px);
  min-height: 30px;
  margin-top: 10px;
  overflow: hidden;
  overflow-y: scroll;
  position: relative;

  .GuestlistItem {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
  }

  .GuestlistItemLeft {
    display: flex;
    align-items: center;
  }
  .GuestlistItemRight {
    display: flex;
    align-items: center;
  }
  .GuestlistItemAvatar {
    width: 24px;
    height: 24px;
    background: #a6a6a6;
    opacity: 1;
    border-radius:50%;
    margin-right: 5px;
    .GuestlistItemAvatar_img {
      width: 24px;
      height: 24px;
      border-radius:50%;
      overflow: hidden;
    }
  }

  .GuestlistItemName {
    max-width:100px;
    height: 24px;
    line-height: 24px;
    font-size: 14px;
    font-weight: 400;
    color: #000000;
    margin-right: 5px;
    overflow:hidden;
    text-overflow:ellipsis;
    white-space:nowrap;
  }

  .lianmaiIconWarp {
    display: flex;
    align-items: center;
  }

  .lianmaiIcon {
    width: 16px;
    height: 16px;
    background: url('../../../../assets/PlanetChatRoom/SpatialDetai_lianmaiList_Icon.png') no-repeat;
    background-size: 16px 16px;
    margin-right: 5px;
  }

  .lianmaiText {
    height: 16px;
    font-size: 12px;
    font-weight: 400;
    color: #0095FF;
    line-height: 16px;
  }

  // 取消关注
  .UnfollowGuestlistBtn {
    padding-left: 8px;
    padding-right: 8px;
    height: 21px;
    line-height: 21px;
    border-radius: 14px 14px 14px 14px;
    color: #CCCCCC;
    background: #F5F5F5;
    position: relative;
    top: 3px;
    cursor: pointer;
    user-select: none;
  }

  // 关注
  .FollowGuestlistBtn {
    padding-left: 8px;
    padding-right: 8px;
    height: 21px;
    line-height: 21px;
    border-radius: 14px 14px 14px 14px;
    color: #0095FF;
    background: #EDF9FF;
    position: relative;
    top: 3px;
    cursor: pointer;
    user-select: none;
  }

  // 拒绝
  .refuse {
    padding-left: 8px;
    padding-right: 8px;
    height: 21px;
    line-height: 21px;
    border-radius: 14px 14px 14px 14px;
    color: #FF5F57;
    background: #FFF1F1;
    position: relative;
    top: 3px;
    cursor: pointer;
    user-select: none;
    margin-right: 8px;
  }

  .refuse:active {
    background: #fbd5d5;
  }

  // 准入
  .allow {
    padding-left: 8px;
    padding-right: 8px;
    height: 21px;
    line-height: 21px;
    border-radius: 14px 14px 14px 14px;
    color: #0095FF;
    background: #EDF9FF;
    position: relative;
    top: 3px;
    cursor: pointer;
    user-select: none;
  }

  .allow:active {
    background: #c8ebfd;
  }

  .GuestlistItemRigth_ApplicationMeeting {
    display: flex;
  }

  // 强制下麦
  .forceXiamai {
    padding-left: 8px;
    padding-right: 8px;
    height: 21px;
    line-height: 21px;
    border-radius: 14px 14px 14px 14px;
    color: #FFF;
    background: #FF5F57;
    position: relative;
    top: 3px;
    cursor: pointer;
    user-select: none;
  }


}

.msg_content {
  // max-height:calc(100vh - 470px);
  // margin-top: 10px;
  // padding-left: 34px;
  // padding-right: 34px;
  width: 100%;
  flex: 1;
  max-height:calc(100% - 460px);
  min-height: 30px;
  overflow: hidden;
  overflow-y: scroll;
  position: relative;
  padding-bottom: 40px;

  .msg_content_item {
    display: flex;
    // align-items: center;
    line-height: 15px;
    margin-bottom: 8px;
    font-size: 12px;

    .msg_content_item_icon {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: #a5a5a5;
      margin-right: 8px;
    }

    .msg_content_item_icon_img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      overflow: hidden;
    }

    .msg_content_item_user_warp {
      display: flex;
      margin-top: 4px;
    }

    .msg_content_item_user {
      display: flex;
      color: #0C0E2B;
      font-size: 12px;
    }

    .msg_content_item_user_name {
      max-width: 50px;
      overflow: hidden;
      text-overflow:ellipsis;
      white-space:nowrap;
    }

    .msg_content_item_msgContent {
      display: flex;
      font-size: 12px;
      font-weight: 600;
      color: #0C0E2B;
      word-break: break-word;

      .HorizontalLiveRoom_send_guzhang_icon {
        width: 12px;
        height:12px;;
        background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_send_guzhang_icon.png') no-repeat;
        background-size: 12px 12px;
        display: inline-block;
        margin-right: 4px;
        margin-left: 6px;
        position: relative;
        top: 1px;
      }

      .HorizontalLiveRoom_send_flowers_content_icon {
        width: 12px;
        height: 12px;
        background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_send_flowers_content_icon.png') no-repeat;
        background-size: 12px 12px;
        display: inline-block;
        margin-left: 6px;
        margin-right: 4px;
      }

      .HorizontalLiveRoom_send_message_icon {
        // width: 12px;
        // height: 10px;
        // background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_send_message_icon.png') no-repeat;
        // background-size: 12px 10px;
        color: #FCBD33;
        display: inline-block;
        position: relative;
        font-style: italic;
        top: -1.5px;
        font-weight: 800;
      }
    }
    .msg_content_item_msgContent_emcee {
      color: #0095FF;
    }
  }
}

.sendDanmuWarp {
  width: 100%;
  // height: 78px;
  padding-left: 17px;
  padding-right: 17px;
  padding-top: 9px;
  padding-bottom: 20px;
  box-shadow: 5px 10px 49px 1px rgba(53,51,97,0.17);
  backdrop-filter: blur(8px);
  display: flex;
  position: absolute;
  bottom: 0;
  justify-content: space-between;

  .sendDanmuBox {
    width: calc(100% - 100px);
    // margin-right: 15px;
    height: 31px;

    .sendDanmuBox_inputWarp {
      height: 31px;
      line-height: 31px;
      background: #F5F5F5;
      border-radius: 31px;
      border: 1px solid #F5F5F5;
      width: 100%;
      padding-left: 20px;
      display: flex;
      align-items: center;
    }

    .sendDanmuBoxInput {
      height: 31px;
      background: #F5F5F5;
      border: none;
      width: calc(100% - 52px);
    }

    .sendDanmuBoxInput:focus-visible {
      border: none;
      outline: -webkit-focus-ring-color none;
    }

    .lineInput {
      width: 1px;
      height: 16px;
      background: #CCCCCC;
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
    }

    .ShowDanmuIcon {
      width: 24px;
      height: 24px;
      background: url('../../../../assets/PlanetChatRoom/SpatialDetail_danmu_Icon.png') no-repeat;
      background-size: 24px 24px;
      display: inline-block;
      user-select: none;
      cursor: pointer;
      margin-left: 12px;
    }

    .ShowDanmuIconHidden {
      width: 24px;
      height: 24px;
      background: url('../../../../assets/PlanetChatRoom/SpatialDetail_danmu_Hidden_Icon.png') no-repeat;
      background-size: 24px 24px;
      display: inline-block;
      user-select: none;
      cursor: pointer;
      margin-left: 12px;
    }

  }
  .sendDanmuBoxRight {
    display: flex;
    justify-content: space-between;

    .sendBtn {
      width: 50px;
      height: 31px;
      background: #F5F5F5;
      border-radius: 20px;
      margin-right: 11px;
      text-align: center;
      line-height: 31px;
      font-size: 12px;
      cursor: pointer;
      user-select: none;
    }
    .sendBtn:active {
      background: #c5c5c5;
    }

    .sendDanmuBoxGuzhang {
      width: 31px;
      height: 31px;
      background: #F5F5F5;
      border-radius: 50%;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      user-select: none;
      .SpatialDetail_clap_Btn {
        width: 18px;
        height: 18px;
        background: url('../../../../assets/PlanetChatRoom/SpatialDetail_clap_Btn.png');
        background-size: contain;
        display: block;
      }
    }
    .sendDanmuBoxGuzhang:active {
      background: #c5c5c5;
    }

    .sendDanmuBoxSonghua {
      width: 31px;
      height: 31px;
      background: #F5F5F5;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      user-select: none;
      .SpatialDetail_flowers_Btn {
        width: 18px;
        height: 18px;
        background: url('../../../../assets/PlanetChatRoom/SpatialDetail_flowers_Btn.png');
        background-size: contain;
        display: block;
      }
    }
    .sendDanmuBoxSonghua:active {
      background: #c5c5c5;
    }
  }
}

.isShowKeyboard {
  height: 40px;
}

// 静音
.SpatialDetail_SoundOff_btn {
  width: 17px;
  height: 17px;
  display: inline-block;
  background: url('../../../../assets/PlanetChatRoom/SpatialDetail_SoundOff_btn.png') no-repeat;
  background-size: 17px 17px;
}

.SpatialDetail_SoundOff_btn_Forbidden {
  width: 17px;
  height: 17px;
  display: inline-block;
  background: url('../../../../assets/PlanetChatRoom/SpatialDetail_SoundOff_btn_Forbidden.png') no-repeat;
  background-size: 17px 17px;
}



// 关闭摄像头
.SpatialDetail_Camera_btn {
  width: 17px;
  height: 17px;
  display: inline-block;
  background: url('../../../../assets/PlanetChatRoom/SpatialDetail_Camera_btn.png') no-repeat;
  background-size: 17px 17px;
}

.SpatialDetail_Camera_btn_off {
  width: 17px;
  height: 17px;
  display: inline-block;
  background: url('../../../../assets/PlanetChatRoom/SpatialDetail_Forbidden.png') no-repeat;
  background-size: 17px 17px;
}

// 投屏按钮
.SpatialDetail_OpenScreen_btn {
  width: 17px;
  height: 17px;
  display: inline-block;
  background: url('../../../../assets/PlanetChatRoom/SpatialDetail_OpenScreen_btn.png') no-repeat;
  background-size: 17px 17px;
}

// 暂停投屏
.SpatialDetail_OpenScreen_btn_CloseScreen {
  width: 17px;
  height: 17px;
  display: inline-block;
  background: url('../../../../assets/PlanetChatRoom/SpatialDetail_OpenScreen_btn_CloseScreen.png') no-repeat;
  background-size: 17px 17px;
}

/*分享课件的*/
.HorizontalLiveRoom_shared_screen_WhiteBoardButton {
  width: 17px;
  height: 17px;
  display: inline-block;
  background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_shared_screen_WhiteBoardButton.png') no-repeat;
  background-size: 17px 17px;
  opacity: 1;
  user-select: none;
  cursor: pointer;
}

.HorizontalLiveRoom_shared_screen_active_WhiteBoardButton_btn {
  width: 17px;
  height: 17px;
  display: inline-block;
  background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_shared_screen_active_WhiteBoardButton.png') no-repeat;
  background-size: 17px 17px;
  opacity: 1;
  user-select: none;
  cursor: pointer;
}
/* 分享课件 END */



// 开启连麦
.SpatialDetail_openLianMai_btn {
  width: 17px;
  height: 17px;
  display: inline-block;
  background: url('../../../../assets/PlanetChatRoom/SpatialDetail_openLianMai_btn.png') no-repeat;
  background-size: 17px 17px;
}
// 关闭连麦
.SpatialDetail_SoundOff_btn_off_Connection {
  width: 17px;
  height: 17px;
  display: inline-block;
  background: url('../../../../assets/PlanetChatRoom/SpatialDetail_SoundOff_btn_off_Connection.png') no-repeat;
  background-size: 17px 17px;
}

// 开启录制
.SpatialDetail_record_btn_luzhi {
  width: 17px;
  height: 17px;
  display: inline-block;
  background: url('../../../../assets/PlanetChatRoom/SpatialDetail_record_btn_luzhi.png') no-repeat;
  background-size: 17px 17px;
}

// 关闭录制
.SpatialDetail_record_btn_luzhi_StartRecording {
  width: 17px;
  height: 17px;
  display: inline-block;
  background: url('../../../../assets/PlanetChatRoom/SpatialDetail_record_btn_luzhi_StartRecording.png') no-repeat;
  background-size: 17px 17px;
}

// 全屏
.SpatialDetail_Full_screen_btn {
  width: 17px;
  height: 17px;
  display: inline-block;
  background: url('../../../../assets/PlanetChatRoom/SpatialDetail_Full_screen_btn.png') no-repeat;
  background-size: 17px 17px;
}



// 打卡卡片
.PunchCard {
  width: 326px;
  height: 68px;
  background: #E9F6FF;
  border-radius: 34px;
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  .avatar_box {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #50A8F4;
    box-shadow: 0px 2px 10px 0px #50A8F4;
    border: 2px solid #FFFFFF;
    margin-right: 18px;
    overflow: hidden;

    .avatar_img {
      width: 60px;
      height: 60px;
      border-radius: 50%;
    }
  }

  .PunchUserInfo {
    font-size: 20px;
    font-weight: 400;
    line-height: 20px;
    .PunchUserInfo_name {
      color: #0C0E2B;
      margin-bottom: 4px;
    }
    .PunchUserInfo_stutes {
      color: #2691FF;
    }
  }
}


.LianMaiCard {
  width: 336px;
  height: 98px;
  background: #009DFF;
  border-radius: 49px;
  position: absolute;
  right: 0px;
  display: flex;
  padding-left: 10px;
  padding-right: 10px;
  align-items: center;

  .LianMaiCard_avatar_box {
    width: 76px;
    height: 76px;
    margin-right: 20px;

    .avatar_img {
      width: 76px;
      height: 76px;
    }
  }

  .LianMaiCard_UserInfo {
    .LianMaiCard_UserInfo_name {
      font-size: 24px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 24px;
      margin-bottom: 4px;
    }

    .LianMaiCard_UserInfo_stutes {
      font-size: 28px;
      font-weight: 600;
      color: #FFFFFF;
      line-height: 28px;
    }
  }
}

.PopupWarp {

}

.btn_Off {
  width: calc(100% - 16PX - 16PX);
  height: 40PX;
  background: #0095FF;
  border-radius: 20PX 20PX 20PX 20PX;
  opacity: 1;
  text-align: center;
  font-size: 16PX;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 40PX;
  position: absolute;
  bottom: 10PX;
  cursor:pointer;
  user-select: none;
}
.btn_Off:active {
  background: #017bd1;
}

.PopupContent {
  width: 100%;
  height: 100%;
}

.lineWarp {
  width: 100%;
  display:flex;
  justify-content: center;
}

.line {
  width: 48PX;
  height: 4PX;
  background: #D0D4D7;
  border-radius: 4PX 4PX 4PX 4PX;
  opacity: 1;
  margin-top: 12PX;
}

.content {
  width: 100%;
  height: calc(100% - 48px);

  .contentTitle {
    height: 20px;
    font-size: 17px;
    font-weight: 500;
    color: #000000;
    line-height: 17px;
    text-align: center;
    margin-top:12px;
    margin-bottom:14px;
  }

  .contentitemBox {
    width: 100%;
    height: 252px;
    overflow: auto;
  }

  .contentitemWarp {
    display:flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;

    .contentitem {
      display:flex;
      align-items: center;
      min-width: 80px;
    }
    .contentitemAvatar {
      width: 24px;
      height: 24px;
      background: #F9B4E3;
      opacity: 1;
      border-radius: 50%;
    }

    .contentitemAvatarImg {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      overflow: hidden;
    }

    .contentitemName {
      font-size: 14px;
      font-weight: 500;
      color: #000000;
      line-height: 14px;
      margin-left: 8px;
      max-width: 100px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .contentPhone {
      font-size: 14px;
      font-weight: 400;
      color: #000000;
      line-height: 14px;
    }
    .contentTime {
      font-size: 14px;
      font-weight: 400;
      color: #999999;
      line-height: 14px;
    }
  }
}

.ApplyConnectWheat {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 12px;
  padding-right: 12px;
  background: #0095FF;
  border-radius: 18px 18px 18px 18px;
  opacity: 1;
  font-size: 14PX;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 14PX;
  position: absolute;
  bottom: 100px;
  right: 15px;
  cursor: pointer;
  user-select: none;
}

.lianmaiWarp {
  display: flex;
  align-items: center;
  .SpatialDetaiLianmaiBtnIcon {
    width: 16px;
    height: 16px;
    margin-right: 4px;
    background: url('../../../../assets/PlanetChatRoom/SpatialDetai_lianmai_Btn.png') no-repeat;
    background-size: 16px 16px;
    display: inline-block;
  }
  .SpatialDetaiLianmaiBtnIconCancel {
    width: 16px;
    height: 16px;
    margin-right: 4px;
    background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_UnApply_connect_Icon.png') no-repeat;
    background-size: 16px 16px;
    display: inline-block;
  }

}



.CancelApplyConnectWheat {
  background: #FF5F57;
}

.SpatialDetail_wheat_connection_Warp {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 10%;

  .SpatialDetail_wheat_connection {
    width: 36px;
    height: 36px;
    background: url('../../../../assets/PlanetChatRoom/SpatialDetail_wheat_connection.png') no-repeat;
    background-size: 36px 36px;
    margin-bottom: 8px;
  }
  .SpatialDetail_wheat_connection_text {
    font-size: 12px;
    font-weight: 400;
    color: #999999;
    line-height: 12px;
  }

}




.CameraItem {

  .isModeMatrix_camera_picture_camera_item {
    width: 100%;
    height: 100%;
    background: #efefef;
    border-radius: 0px 0px 0px 0px;
    opacity: 1;
    position: relative;
    // margin-bottom: 3px;

    .HorizontalLiveRoom_camera_picture_camera_bottom_box {
      width: 100%;
      height: 24px;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 100%);
      border-radius: 0px 0px 0px 0px;
      opacity: 1;
      position: absolute;
      bottom: 0px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
      color: #FFFFFF;
      line-height: 24px;
      padding-left: 8px;
      padding-right: 8px;
    }

    .HorizontalLiveRoom_camera_picture_camera_bottom_box_lianmai {
      background: rgba(255, 255, 255, 0.8);
    }

    .HorizontalLiveRoom_camera_picture_camera_bottom_box_Name_Warp {
      display: flex;
      align-items: center;
    }

    .HorizontalLiveRoom_camera_picture_camera_bottom_box_Name {
      max-width: 50px;
      height: 24px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .HorizontalLiveRoom_camera_picture_camera_bottom_box_Name_lianmai {
      color: #000000;
    }

    .HorizontalLiveRoom_camera_picture_mic_icon {
      width: 10px;
      height: 12px;
      background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_camera_picture_mic_icon.png') no-repeat;
      background-size: 10px 12px;
      display: inline-block;
      margin-left: 4px;
    }

    .HorizontalLiveRoom_camera_picture_forbidden_mic_icon {
      width: 10px;
      height: 12px;
      background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_camera_picture_forbidden_mic_icon.png') no-repeat;
      background-size: 10px 12px;
      display: inline-block;
      margin-left: 4px;
    }

    .HorizontalLiveRoom_camera_picture_forbidden_Lianmai {
      width: 10px;
      height: 12px;
      background: url('../../../../assets/PlanetChatRoom/SpatialDetail_MicrophoneOn_btn_Lianmai.png') no-repeat;
      background-size: 10px 12px;
      display: inline-block;
      margin-left: 4px;
    }

    .GuanZhu_btn {
      cursor: pointer;
      user-select: none;
      padding-top: 2px;
      padding-bottom: 2px;
      padding-left: 4px;
      padding-right: 4px;
      font-size: 12px;
      font-weight: 400;
      color: #0095FF;
      line-height: 12px;
      background: #EDF9FF;
      border-radius: 14px 14px 14px 14px;
      opacity: 1;
    }

    .unGuanZhu_btn {
      color: #CCCCCC;
      background: #F5F5F5;
    }

    // 强制下麦按钮
    .forceXiaMai {
      cursor: pointer;
      user-select: none;
      padding-top: 2px;
      padding-bottom: 2px;
      padding-left: 4px;
      padding-right: 4px;
      font-weight: 400;
      color: #FFFFFF;
      font-size: 12px;
      line-height: 12px;
      background: #FF5F57;
      border-radius: 12px 12px;
      opacity: 1;
      white-space: nowrap;
    }

  }


  .isModeMatrix_camera_picture_camera_item_hidden {
    // height: 0px;
    overflow: hidden;
  }

  .headUrlWarp {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .video_Title_box_left_avatar {
    width: 44px;
    height: 44px;
    background: #F9B4E3;
    opacity: 1;
    border-radius: 50%;
    // margin-right: 8PX;
    overflow: hidden;
  }

  .video_Title_box_left_avatar_img {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    overflow: hidden;
  }

  .head_sculpture_name {
    width: 44px;
    height: 44px;
    font-size: 16px;
    font-weight: 500;
    color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
  }

  .StreamWarp {
    width: 100%;
    height: 100%;
  }

  .StreamWarpHidden {
    height: 0px;
    overflow: hidden;
  }
}

.Guestlist {
  .tabHeader {
    width: 100%;
    display: flex;
    .tab_item {
      margin-right: 16px;
      font-size: 14px;
      text-align: left;
      color: #666666;
      position: relative;
    }
    .tab_item_active {
      color: #0095FF;
    }
    .tab_item_num {
      position: relative;
      width: 12px;
      height: 12px;
      background: #FF180D;
      border-radius: 14px 14px 14px 14px;
      display: inline-block;
      font-weight: 400;
      font-size: 9px;
      color: #FFFFFF;
      line-height: 12px;
      text-align: center;
      top: -6px;
      right: -3px;
    }
  }
}

.AvatarByhostUserInfo {
  width: 32px;
  height: 32px;
  overflow: hidden;
  margin-right: 10px;
}



