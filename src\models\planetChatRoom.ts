// 星球详情页 - 聊天室
import { getUrlParam, processMessageArray } from '@/utils/utilsByTRTC';
import {addStarSpaceApplyAdmission, checkSpacePassword, closeSpaceWindow, downScreenShareHelpFile, getApplyAdmissionList, getAppointmentList,
  getGuestListInfo, getHandUpList, getManageMembersInTheMeeting, getMeetingMuteAll, getMeetingRemoveUser, getMeetingTransferCompereUser,
  getShareImUser, getSignInList, getSpaceBulletScreen, getSpaceGroupMsg, getSpaceInfo, getTiwImUser, getTranscodeFileBySpace, isFocus,
  liveAppointment, liveRecord, openCloseHandUp, operateHandUp, saveVideoTime, spaceCollect, spaceCoursewareTranscode, spaceInsideSetting,
  startEndLive, startWhiteboardPush, stopWhiteboardPush, updateStarSpaceApplyAdmission, userActiveJoinSpace,
} from '@/services/planetChatRoom';
import { getMsgCodeUserInfo } from '@/services/login/login';
import moment from 'moment';
import { getHomePageLink } from '@/services/activity/activity';
import { getCurrentWXWorkApp, getOperatingEnv } from '@/utils/utils';

const userAgent = typeof navigator !== 'undefined' && navigator && navigator.userAgent;

const initializedState = {
  isMobile: Boolean(
    userAgent.match(/Android|BlackBerry|iPhone|iPad|iPod|Opera Mini|IEMobile|WPDesktop/i),
  ),
  isHorizontalLisive: getUrlParam('isHorizontalLive') == 1 ? true : false,
  isOpenDanmu: true,
  isLive: true,
  isInitialized: false,
  playerInfo: null,
  SpaceInfo: null,
  guestListInfo: null,
  msgListBySENDAPPLAUSE: [],
  msgListByDanmu: [],
  applyAdmissionList: null,
  userInfo: null,
  userPermission: null,
  NotAgainApplyRecordTipsModal: false,
  sendCallData: null,
  guestRefuseOpenCameraTipsModalVisible: null,
  guestRefuseOpenMikeTipsModalVisible: null,
  hostRemoveYouTipsModalVisible: null,
  hostCloseYouCameraTipsModalVisible: null,
  hostCloseYouMikeTipsModalVisible: null,
  hostOpenYouCameraTipsModalVisible: null,
  hostOpenYouMikeTipsModalVisible: null,
  hostOpenAllMikeTipsModalVisible: null,
  guestToHostTipsModalVisible: null,
  applyRecordTipsModalObj: null,
  applyRecordTipsModalVisible: null,
  membersListInTheMeeting: null,
  topMessageNotifyType: null,
  isShowTopMessageNotify: false,
  TiwTaskID: null,
  HiddenDanmu: null,
  TiwUserSig: null,
  TiwImUserId: null,
  TranscodeFileBySpace: null,
  isOpenTEduBoard: false,
  ModalVisibleByAcceptLienMaiNoMicrophone: false,
  ModalVisibleByActionSheetShare: false,
  ModalVisibleByApplicationSubmitted: false,
  ModalVisibleByShareScreenError: false,
  ModalVisibleByUserTokenInvalid: false,
  ModalVisibleByKickedOut: false,
  ModalVisibleByEndRecording: false,
  ModalVisibleByForceWheat: false,
  ModalVisibleByLeaveMicrophone: false,
  ModalVisibleByVerticalPageWrong: false,
  ModalVisibleByOrientationWrong: false,
  ModalVisibleByNoMicrophone: false,
  ModalVisibleByStartLive: false,
  ModalVisibleByAppointmentClosedSpace: false,
  ModalVisibleByAcceptLienMai: false,
  ModalVisibleByClosedSpace: false,
  ModalVisibleByCancelAppointment: false,
  ModalVisibleBySpaceRemoved: false,
  ModalVisibleBySpaceViolation: false,
  isNotLoginCheckPassword: false,
  sendApplauseCount: null,
  isNotLogin: false,
  currentWatchMode: null,
  isShowCommentArea: true,
  isModeMatrix: false,
  autoExpandGuestArea: null,
  HomePageLink: null,
  sendCallCount: null,
  currentLiveUserList: null,
  sendFlowersCount: null,
  newMySnedSeq: null,
  lastSeq: null,
  msgListByBULLETSCREEN: null,
  currentUserType: null,
  handUpList: null,
  BookingObj: null,
  BookingList: null,
  signInObj: null,
  signInList: null,
  screenShareUser: null,
};

export default {
  namespace: 'PlanetChatRoom',
  state: {
    isShowLiveSmallWindow: localStorage.getItem('isShowLiveSmallWindow'),
    ...initializedState,
  },

  effects: {
    *getHomePageLink({ payload }, { call, put }) {
      const response = yield call(getHomePageLink, payload);
      if (response.code == 200 && response.content) {
        yield put({
          type: 'setState',
          payload: {
            HomePageLink: response.content,
          },
        });
      }
      return response;
    },

    *getMsgCodeUserInfo({ payload }, { call, put, select }) {
      let token_text = localStorage.getItem('access_token');
      let SpaceInfo = yield select((state) => state.PlanetChatRoom.SpaceInfo);
      const { imagePhotoPathShow } = SpaceInfo || {};
      let userInfo = yield call(getMsgCodeUserInfo, { token: token_text });
      let UserInfoByLocalStorage = JSON.parse(localStorage.getItem('userInfo') || '{}');
      if (userInfo && userInfo.code == 200) {
        const { content } = userInfo || {};
        localStorage.setItem(
          'userInfo',
          JSON.stringify({
            ...content,
            id: content.friUserId,
            imagePhotoPathShow,
            isExperts: UserInfoByLocalStorage.isExperts,
          }),
        );
      } else if (userInfo.code == 401) {
        localStorage.removeItem('userInfo');
      }
      return userInfo;
    },

    *getSpaceInfo({ payload }, { call, put, select }) {
      const userAgentByInfo = typeof navigator !== 'undefined' && navigator && navigator.userAgent;
      const userInfoData = JSON.parse(localStorage.getItem('userInfo'));
      const isNotLoginCheckPassword = yield select(
        (state) => state.PlanetChatRoom.isNotLoginCheckPassword,
      );
      const isOpenTEduBoard = yield select((state) => state.PlanetChatRoom.isOpenTEduBoard);
      const SpaceInfo = yield select((state) => state.PlanetChatRoom.SpaceInfo);
      const response = yield call(getSpaceInfo, payload);
      // 分享课件中暂停刷新
      if (!!isOpenTEduBoard) {
        return null;
      }
      const { code, content } = response || {};
      if (code == 200) {
        const { status } = content || {};

        yield put({
          type: 'setState',
          payload: {
            SpaceInfo: {
              ...content,
              joinRandStr: SpaceInfo ? SpaceInfo.joinRandStr : content.joinRandStr, // 只保存首次获取的进入空间详情时间戳字符串
              isNeedPwd: isNotLoginCheckPassword ? 0 : content.isNeedPwd,
            },
            isMobile: Boolean(
              userAgentByInfo.match(
                /Android|BlackBerry|iPhone|iPad|iPod|Opera Mini|IEMobile|WPDesktop/i,
              ),
            ),
            isLive: status == 3 ? false : true,
            userInfo: userInfoData,
            sendFlowersCount: content.sendFlowersCount ? content.sendFlowersCount : 0,
            sendApplauseCount: content.sendApplauseCount ? content.sendApplauseCount : 0,
            sendCallCount: content.sendCallCount ? content.sendCallCount : 0,
            isOpenTEduBoard: content.isHasWhiteBoardPush == 1 ? true : false,
          },
        });
      } else {
        yield put({
          type: 'setState',
          payload: {
            SpaceInfo: null,
            userInfo: userInfoData,
          },
        });
      }
      return response;
    },

    *userActiveJoinSpace({ payload }, { call, put }) {
      const response = yield call(userActiveJoinSpace, payload);
      const { code, content } = response || {};
      if (code == 200 && content) {
      }
      return response;
    },

    *getGuestListInfo({ payload }, { call, put }) {
      const response = yield call(getGuestListInfo, payload);
      const { code, content } = response || {};
      if (code == 200 && Array.isArray(content)) {
        yield put({
          type: 'setState',
          payload: {
            guestListInfo: content,
          },
        });
      } else {
        yield put({
          type: 'setState',
          payload: {
            guestListInfo: [],
          },
        });
      }
      return response;
    },

    *getApplyAdmissionList({ payload }, { call, put }) {
      const response = yield call(getApplyAdmissionList, payload);
      const { code, content } = response || {};
      if (code == 200 && Array.isArray(content)) {
        yield put({
          type: 'setState',
          payload: {
            applyAdmissionList: content,
          },
        });
      } else {
        yield put({
          type: 'setState',
          payload: {
            applyAdmissionList: [],
          },
        });
      }
      return response;
    },

    *getScreenShareUser({ payload }, { call, put }) {
      const response = yield call(getShareImUser, payload);
      const { code, content } = response || {};
      if (code == 200) {
        yield put({
          type: 'setState',
          payload: {
            screenShareUser: content,
          },
        });
      } else {
        yield put({
          type: 'setState',
          payload: {
            screenShareUser: null,
          },
        });
      }
      return response;
    },

    *spaceCollect({ payload }, { call, put, select }) {
      const { collectType } = payload || {};
      const response = yield call(spaceCollect, payload);
      const SpaceInfo = yield select((state) => state.PlanetChatRoom.SpaceInfo);
      const { code } = response || {};
      if (code == 200) {
        yield put({
          type: 'setState',
          payload: {
            SpaceInfo: {
              ...SpaceInfo,
              isCollect: collectType,
            },
          },
        });
      }
      return response;
    },

    *getHandUpList({ payload }, { call, put }) {
      const response = yield call(getHandUpList, payload);
      const { code, content } = response || {};
      if (code == 200 && content && Array.isArray(content.resultList)) {
        yield put({
          type: 'setState',
          payload: {
            handUpList: content.resultList,
          },
        });
      } else {
        yield put({
          type: 'setState',
          payload: {
            handUpList: [],
          },
        });
      }
      return response;
    },

    *getSignInList({ payload }, { call, put, select }) {
      const response = yield call(getSignInList, payload);
      const signInList = yield select((state) => state.PlanetChatRoom.signInList);

      const { code, content } = response || {};
      if (code == 200 && content && Array.isArray(content.resultList)) {
        const { pageNum, pageSize } = content || {};
        if (pageNum > 1 && Array.isArray(signInList)) {
          yield put({
            type: 'setState',
            payload: {
              signInList: signInList.concat(content.resultList),
              signInObj: content,
            },
          });
        } else {
          yield put({
            type: 'setState',
            payload: {
              signInList: content.resultList,
              signInObj: content,
            },
          });
        }
      }
      return response;
    },

    *getAppointmentList({ payload }, { call, put, select }) {
      const response = yield call(getAppointmentList, payload);
      const BookingList = yield select((state) => state.PlanetChatRoom.BookingList);

      const { code, content } = response || {};
      if (code == 200 && content && Array.isArray(content.resultList)) {
        const { pageNum, pageSize } = content || {};
        if (pageNum > 1 && Array.isArray(BookingList)) {
          yield put({
            type: 'setState',
            payload: {
              BookingList: BookingList.concat(content.resultList),
              BookingObj: content,
            },
          });
        } else {
          yield put({
            type: 'setState',
            payload: {
              BookingList: content.resultList,
              BookingObj: content,
            },
          });
        }
      }
      return response;
    },

    *operateHandUp({ payload }, { call, put }) {
      const { spaceId, wxUserId } = payload || {};
      const response = yield call(operateHandUp, payload);
      const { code, content } = response || {};

      if (code == 200) {
        // 操作完成后调用刷新查看空间连麦列表
        yield put({
          type: 'getHandUpList',
          payload: {
            pageNum: 1, // 微信userid
            pageSize: 100, // 条数
            spaceId: spaceId, // 空间id
          },
        });
      }
      return response;
    },

    *getSpaceGroupMsg({ payload }, { call, put, select }) {
      const response = yield call(getSpaceGroupMsg, payload);
      const { msgSeq } = payload || {};
      const msgListBySENDAPPLAUSE = yield select(
        (state) => state.PlanetChatRoom.msgListBySENDAPPLAUSE,
      );

      const { code, content } = response || {};
      if (code == 200 && content && Array.isArray(content.resultList)) {
        const { lastSeq } = content || {};
        yield put({
          type: 'setState',
          payload: {
            msgListBySENDAPPLAUSE: content.resultList.reverse().concat(msgListBySENDAPPLAUSE),
            lastSeq: lastSeq,
          },
        });
        if (!msgSeq) {
          yield put({
            type: 'setState',
            payload: {
              newMySnedSeq: lastSeq,
            },
          });
        }
      }
      return response;
    },

    *closeTEduBoard({ payload }, { call, put, select }) {
      const isOpenTEduBoard = yield select((state) => state.PlanetChatRoom.isOpenTEduBoard);
      if (isOpenTEduBoard) {
        yield put({
          type: 'setState',
          payload: {
            isOpenTEduBoard: false,
          },
        });
      }
    },

    *getSpaceBulletScreen({ payload }, { call, put, select }) {
      const response = yield call(getSpaceBulletScreen, payload);
      const { code, content } = response || {};
      if (code == 200 && content && Array.isArray(content) && content.length > 0) {
        const msgListByBULLETSCREEN = yield select(
          (state) => state.PlanetChatRoom.msgListByBULLETSCREEN,
        );

        let newList = null;
        if (Array.isArray(msgListByBULLETSCREEN) && msgListByBULLETSCREEN.length != 0) {
          let isFindMagItem = msgListByBULLETSCREEN.find((item) => {
            return content[0].msgSeq == item.msgSeq;
          });
          if (!isFindMagItem) {
            newList = [...msgListByBULLETSCREEN, ...content];
          }
        } else {
          newList = [...content];
        }

        if (Array.isArray(newList)) {
          let msgSeqList = [];
          newList.map((item) => {
            let itemByFindMsgSeqList = msgSeqList.find((itemBymsgSeqList) => {
              return itemBymsgSeqList.msgSeq == item.msgSeq;
            });
            if (!itemByFindMsgSeqList) {
              msgSeqList.push(item);
            }
          });

          yield put({
            type: 'setState',
            payload: {
              msgListByBULLETSCREEN: msgSeqList,
            },
          });
        }
      }
      return response;
    },

    *getSpaceGroupMsgByMsgSeqByState({ payload }, { call, put, select }) {
      const msgListByBULLETSCREEN = yield select(
        (state) => state.PlanetChatRoom.msgListByBULLETSCREEN,
      );
      return msgListByBULLETSCREEN;
    },

    *getIsHorizontalLive({ payload }, { select }) {
      const isHorizontalLive = yield select((state) => state.PlanetChatRoom.isHorizontalLive);
      return isHorizontalLive;
    },

    *checkSpacePassword({ payload }, { call, put }) {
      const response = yield call(checkSpacePassword, payload);
      return response;
    },

    *liveAppointment({ payload }, { call, put, select }) {
      const { appointmentType } = payload || {}; // 1预约 2取消预约
      const SpaceInfo = yield select((state) => state.PlanetChatRoom.SpaceInfo);
      let env = getOperatingEnv();
      let wcUserId = localStorage.getItem('wcUserId');
      let params = {
        ...payload,
        wcAppId: env == 7 ? getCurrentWXWorkApp()?.appid : null, // 企微ID  APPID
        wcUserId: wcUserId,
      };
      const response = yield call(liveAppointment, params);
      const { code, content } = response || {};
      if (code == 200 && content) {
        yield put({
          type: 'setState',
          payload: {
            SpaceInfo: {
              ...SpaceInfo,
              isAppointment: appointmentType == 1 ? 1 : 0,
            },
            ModalVisibleByCancelAppointment: false,
          },
        });
      }
      return response;
    },

    *openCloseHandUp({ payload }, { call, put, select }) {
      const { handUpType } = payload || {}; // 1开启 2关闭
      const SpaceInfo = yield select((state) => state.PlanetChatRoom.SpaceInfo);
      const response = yield call(openCloseHandUp, payload);
      const { code, content } = response || {};
      if (code == 200 && content) {
        yield put({
          type: 'setState',
          payload: {
            SpaceInfo: {
              ...SpaceInfo,
              handUpType: handUpType == 1 ? 1 : 0,
            },
          },
        });
      }
      return response;
    },

    *liveRecord({ payload }, { call, put, select }) {
      const { recordType } = payload || {}; // 1开启 2关闭
      const SpaceInfo = yield select((state) => state.PlanetChatRoom.SpaceInfo);
      const response = yield call(liveRecord, payload);
      const { code, content } = response || {};
      if (code == 200 && content) {
        yield put({
          type: 'setState',
          payload: {
            SpaceInfo: {
              ...SpaceInfo,
              recordType: recordType == 1 ? 1 : 0,
              recordStartTime: moment().format('YYYY-MM-DD HH:mm:ss'),
            },
          },
        });
      }
      return response;
    },

    *isFocus({ payload }, { call, put, select }) {
      const response = yield call(isFocus, payload);
      const { code, content } = response || {};
      if (code == 200 && content) {
      }
      return response;
    },

    *startEndLive({ payload }, { call, put, select }) {
      const response = yield call(startEndLive, payload);
      const SpaceInfo = yield select((state) => state.PlanetChatRoom.SpaceInfo);
      // liveType 1开始直播 2结束直播
      const { liveType } = payload || {};
      const { code, content } = response || {};
      if (code == 200 && content) {
        // 开始直播后 更新直播详情状态
        yield put({
          type: 'setState',
          payload: {
            SpaceInfo: {
              ...SpaceInfo,
              status: liveType == 1 ? 1 : 3, // 状态：1直播中、2预约中、3弹幕轰炸中
            },
          },
        });
      }
      return response;
    },

    *showSmallWindow({ payload }, { call, put, select }) {
      const { isShowLiveSmallWindow } = payload || {};
      const UerInfo = JSON.parse(localStorage.getItem('userInfo')) || {};
      let wxuserId = UerInfo && UerInfo.friUserId;
      const response = yield call(getSpaceInfo, {
        spaceId: isShowLiveSmallWindow,
        wxuserId: wxuserId,
      });

      const { code, content } = response || {};
      if (code == 200 && content) {
        let { status, isNeedPwd, videoList, joinRandStr } = content || {};
        if (
          (status == 1 && isNeedPwd == 0) ||
          (isNeedPwd == 0 && status == 3 && !!Array.isArray(videoList) && videoList.length > 0)
        ) {
          localStorage.setItem('isShowLiveSmallWindow', isShowLiveSmallWindow);
          yield put({
            type: 'setState',
            payload: {
              isShowLiveSmallWindow: isShowLiveSmallWindow,
            },
          });
        }
      } else {
        yield put({ type: 'setState', payload: { isShowLiveSmallWindow: null } });
      }
    },

    *saveVideoTime({ payload }, { call, put, select }) {
      const response = yield call(saveVideoTime, payload);
      return response;
    },

    *closeSpaceWindow({ payload }, { call, put, select }) {
      const response = yield call(closeSpaceWindow, payload);
      return response;
    },

    *downScreenShareHelpFile({ payload }, { call, put, select }) {
      const response = yield call(downScreenShareHelpFile, payload);
      return response;
    },

    *getTranscodeFileBySpace({ payload }, { call, put, select }) {
      const response = yield call(getTranscodeFileBySpace, payload);
      const { code, content } = response || {};
      if (code == 200 && content) {
        yield put({
          type: 'setState',
          payload: {
            TranscodeFileBySpace: content,
          },
        });
      } else {
        yield put({
          type: 'setState',
          payload: {
            TranscodeFileBySpace: null,
          },
        });
      }
      return response;
    },

    *spaceCoursewareTranscode({ payload }, { call, put, select }) {
      const response = yield call(spaceCoursewareTranscode, payload);
      return response;
    },

    *addStarSpaceApplyAdmission({ payload }, { call, put, select }) {
      const response = yield call(addStarSpaceApplyAdmission, payload);
      return response;
    },

    *updateStarSpaceApplyAdmission({ payload }, { call, put, select }) {
      const response = yield call(updateStarSpaceApplyAdmission, payload);
      return response;
    },

    *getTiwImUser({ payload }, { call, put, select }) {
      const response = yield call(getTiwImUser, payload);
      const { code, content } = response || {};
      if (code == 200 && content) {
        yield put({
          type: 'setState',
          payload: {
            TiwImUserId: content.imUserId,
            TiwUserSig: content.userSig,
          },
        });
      } else {
        yield put({
          type: 'setState',
          payload: {
            TiwImUserId: null,
            TiwUserSig: null,
          },
        });
      }
      return response;
    },

    *startWhiteboardPush({ payload }, { call, put, select }) {
      const response = yield call(startWhiteboardPush, payload);
      const { code, content } = response || {};
      if (code == 200 && content) {
        yield put({ type: 'setState', payload: { TiwTaskID: content } });
      } else {
        yield put({ type: 'setState', payload: { TiwTaskID: null } });
      }
    },

    *stopWhiteboardPush({ payload }, { call, put, select }) {
      const response = yield call(stopWhiteboardPush, payload);
      return response;
    },

    *getMeetingTransferCompereUser({ payload }, { call, put, select }) {
      const response = yield call(getMeetingTransferCompereUser, payload);
      return response;
    },

    *getManageMembersInTheMeeting({ payload }, { call, put, select }) {
      const response = yield call(getManageMembersInTheMeeting, payload);
      const { code, content } = response || {};
      if (code == 200 && Array.isArray(content)) {
        yield put({
          type: 'setState',
          payload: {
            membersListInTheMeeting: content,
          },
        });
      } else {
        yield put({
          type: 'setState',
          payload: {
            membersListInTheMeeting: [], // 空间在线用户列表
          },
        });
      }
      return response;
    },

    *getMeetingRemoveUser({ payload }, { call, put, select }) {
      const response = yield call(getMeetingRemoveUser, payload);
      return response;
    },

    *spaceInsideSetting({ payload }, { call, put, select }) {
      const { isShowPassword, isSpectatorUse } = payload;
      const response = yield call(spaceInsideSetting, payload);
      const SpaceInfo = yield select((state) => state.PlanetChatRoom.SpaceInfo);
      const { code } = response || {};
      if (code == 200) {
        yield put({
          type: 'setState',
          payload: {
            SpaceInfo: {
              ...SpaceInfo,
              isShowPassword:
                isShowPassword == undefined ? SpaceInfo.isShowPassword : isShowPassword,
              isSpectatorUse:
                isSpectatorUse == undefined ? SpaceInfo.isSpectatorUse : isSpectatorUse,
            },
          },
        });
      }
      return response;
    },

    *getMeetingMuteAll({ payload }, { call, put, select }) {
      const response = yield call(getMeetingMuteAll, payload);
      return response;
    },
  },

  reducers: {
    setState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },

    updateGDPAndPV(state, { payload }) {
      let SpaceInfo = {
        ...state.SpaceInfo,
        ...payload,
      };
      return {
        ...state,
        SpaceInfo,
      };
    },

    addSendFlowersCount(state, { payload }) {
      let { sendFlowersCount } = state || {};
      let newSendFlowersCount = sendFlowersCount + 1;
      return {
        ...state,
        sendFlowersCount: newSendFlowersCount,
      };
    },

    addSendApplauseCount(state, { payload }) {
      let { sendApplauseCount } = state || {};
      let newSendApplauseCount = sendApplauseCount + 1;
      return {
        ...state,
        sendApplauseCount: newSendApplauseCount,
      };
    },

    addSendCallCount(state, { payload }) {
      let { sendCallCount } = state || {};
      let newSendCallCount = sendCallCount + 1;
      return {
        ...state,
        sendCallCount: newSendCallCount,
      };
    },

    closeSmallWindow(state, { payload }) {
      localStorage.removeItem('isShowLiveSmallWindow');
      return {
        ...initializedState,
        isShowLiveSmallWindow: null,
      };
    },

    setCurrentUserType(state, { payload }) {
      const { guestListInfo, applyAdmissionList, SpaceInfo } = state || {};

      let { hostUserInfo, wxUserId } = SpaceInfo || {};

      let objByGuestList =
        Array.isArray(guestListInfo) &&
        guestListInfo.find((item) => {
          return item.isSelf == 1;
        });

      let currentUserType = 1;
      if (hostUserInfo && hostUserInfo.isSelf == 1) {
        currentUserType = 1;
      } else if (!!objByGuestList) {
        currentUserType = 2;
      } else {
        currentUserType = 3;
      }

      let currentLiveUserList = [];
      if (Array.isArray(guestListInfo)) {
        guestListInfo.map((item) => {
          currentLiveUserList.push(item);
        });
      }

      if (!!hostUserInfo) {
        currentLiveUserList.push(hostUserInfo);
      }

      return {
        ...state,
        isInitialized: true,
        currentUserType: currentUserType,
        currentLiveUserList: currentLiveUserList,
      };
    },

    updateMsgListBySENDAPPLAUSE(state, { payload }) {
      const { msgListBySENDAPPLAUSE } = state;
      const { msgList } = payload || {};
      let newMsgListBySENDAPPLAUSE = msgListBySENDAPPLAUSE.concat(msgList);
      let proBynewMsgListBySENDAPPLAUSE = processMessageArray(newMsgListBySENDAPPLAUSE);
      return {
        ...state,
        msgListBySENDAPPLAUSE: proBynewMsgListBySENDAPPLAUSE,
      };
    },

    clean(state, { payload }) {
      return {
        ...state,
        ...initializedState,
      };
    },
  },
};
