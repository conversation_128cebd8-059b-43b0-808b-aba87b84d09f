import React, {useState, useEffect, useRef} from 'react';
import { history,connect } from 'umi';
import { Input,Form,Select } from 'antd'
import styles from "./index.less";
import {
  echoFormValueByForm,
  getFormDataByStep,
  scrollToTop,
  setFormValues,
} from "@/pages/CreationOrthodontics/components/CreationFormUtils";
import { getArrailUrl, screenData } from '@/utils/utils'
import _ from 'lodash'
import {stringify} from "qs";


const { TextArea } = Input;
const Step1: React.FC = (props) => {
  const [ form] = Form.useForm();

  // 是否嵌套在5i5ya的iframe中
  const isInIframe = (self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya')

  // 获取地址栏参数
  const { location } = history || {}
  const { query } = location || {}
  const {
    CreationOrthodontics,
    dispatch,
    goBack:goBackByProps,
  } = props || {}
  const { DictionaryData,medicalRecordJson,DataBymedicalRecordJson } = CreationOrthodontics || {}
  const {
    pageFrom, //  'ConsultationDetails'详情来的保持原逻辑
  } = query || {}

  const {
    id:consultationId,       // :consultationId 8dad6e1a68714dc7b1f908902ad2d4f4
    customerId,              // 客户id
    createUserId,            // 创建人id
    tenantId,                // 租户id
    consultationCaseInfoDto,
    orderCaseTemplate, // : 0, -- 订单病例模板 1通用病例 2正畸病例
    type,             // "type": 0, --会诊类型(1图文、2视频,3审核)
  } = DictionaryData || {}

  const checkJson1  = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item)=>{return item.dictCode == '1'})
  // 现病史
  const checkJson2  = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item)=>{return item.dictCode == '2'})
  // 既往史
  const checkJson3  = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item)=>{return item.dictCode == '3'})
  // 全身健康状况
  const checkJson4  = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item)=>{return item.dictCode == '4'})
  const ArrByForm = [checkJson1,checkJson2,checkJson3,checkJson4]


  const {
    customerName, // 患者名称
    age,          // 患者年龄
    fileNumber,    // 病历号
    sex,          // 患者性别
  } = consultationCaseInfoDto || {}

  useEffect(()=>{scrollToTop()},[]);
  const onFormLayoutChange = (value) => {
    getFormFieldsValue();
  };

  useEffect(() => {
    echoFormValue()
  },[DataBymedicalRecordJson]);

  useEffect(()=>{

  },[DictionaryData])

  // 回显表单值
  const echoFormValue = ()=>{
    form.resetFields();
    const { consultationCaseInfoDto } = DictionaryData || {};
    const {
      age,
      sex,
    } = consultationCaseInfoDto || {};
    age && form.setFieldsValue({age:age});
    sex && form.setFieldsValue({sex:sex});
    if (Array.isArray(ArrByForm) && ArrByForm.length > 0) {
      setFormValues(ArrByForm,form)
    }
  }

  // 表单
  const getFormFieldsValue = _.debounce(()=>{
    let values = form.getFieldsValue();
    if (Array.isArray(ArrByForm) && ArrByForm.length > 0) {
      let formDataArr = ArrByForm.map((item)=>{
        // 此段代码添加判断逻辑 当form.getFieldValue(item.id)有值时 只截取200字保存超出200以外则忽略 以实现暂存逻辑
        let inputContent = form.getFieldValue(item.id);
        if (inputContent && inputContent.length > 200) {
          inpuContent = inputContent.substring(0, 200);
        }
        return { id: item.id,  inputContent: inputContent }
      })
      dispatch({
        type: 'CreationOrthodontics/saveDataByMedicalRecordJson',
        payload: {
          processNode:1,            // 基本信息
          formDataArr:formDataArr,
          isSubmit:false,
          age: values.age,
          sex: values.sex,
        }
      })
    }
  },3000)

  // 提交表单
  const onFinish = _.debounce((value,isSubmitLoading) => {
    const { errorFields } = value || {}
    if (Array.isArray(errorFields) && errorFields.length > 0) { return }

    let formDataArr = Object.keys(value).map((key) => {
      return { id: key,  inputContent: value[key] }
    })
    dispatch({
      type: 'CreationOrthodontics/saveDataByMedicalRecordJson',
      payload: {
        processNode:1,            // 基本信息
        formDataArr:formDataArr,
        isSubmit:isSubmitLoading,
        age: value.age,
        sex: value.sex,
      }
    }).then((value)=>{
      if (isSubmitLoading) {
        // 在5i5ya的iframe中
        if (isInIframe) {
          let postData = {
            dataType: 'pathname',       // 页面地址onchange事件
            pathnameByChild: '/CreationOrthodontics/Step2',  // 路由信息
            searchByChild: `?${stringify({consultationId,customerId,tenantId})}`,  // 路由信息
          }
          if(orderCaseTemplate == 2){
            postData = {
              dataType: 'pathname',       // 页面地址onchange事件
              pathnameByChild: '/CreationOrthodontics/Step2',  // 路由信息
              searchByChild: `?${stringify({
                orthodonticConsultationId:consultationId,
                pageFrom:pageFrom,
              })}`,  // 路由信息
            }
          }
          console.log('子级发送数据：', postData, getArrailUrl())
          window.parent.postMessage(postData, getArrailUrl())
          return
        }
        if(orderCaseTemplate == 2){
          history.replace(`/CreationOrthodontics/Step2?${
            stringify({
              orthodonticConsultationId:consultationId,
              pageFrom:pageFrom,
            })
          }`)
        }else {
          history.replace(`/CreationOrthodontics/Step2?${
            stringify({consultationId, customerId, tenantId,pageFrom})
          }`)
        }
      }
    }).catch(()=>{})
  },500)

  const goBack = () => {
    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'goBack',       // 页面地址onchange事件
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
      return
    }

    if (history.length > 1) {
      history.goBack()
    } else {
      history.replace('/')
    }
  }

  // 重新选择
  const selectNewCustomer = () => {
    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'goBack',       // 页面地址onchange事件
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
      return
    }

    window.close()
  }
  return (
    <div className={styles.page_info}>
      <div className={styles.warp_content}>
        <div className={styles.title_box}>
          <div>基本信息</div>
        </div>

        {orderCaseTemplate != 2 &&
          <div className={styles.basic_information}>
            <div className={styles.basic_information_flex}>
              <div className={styles.basic_disposition_Warp}>
                <div className={styles.basic_disposition}>{ customerName }</div>
                <div className={styles.basic_disposition}>{ sex == 1 ? "男" : sex == 2 ? "女" : null }</div>
                <div className={styles.basic_disposition}>{screenData(age)}</div>
              </div>
              <div className={styles.Medical_record_number_warp}>
                <div className={styles.Medical_record_number_lable}>病例号：</div>
                <div className={styles.Medical_record_number_value}>{fileNumber}</div>
              </div>
            </div>

            <div
              onClick={selectNewCustomer}
              className={styles.basic_research_btn_warp}>
              <div className={styles.basic_research_btn}>
                <i className={styles.basic_research_Icon}></i>
                <span>重新选择</span>
              </div>
            </div>
          </div>
        }

        <Form
          form={form}
          initialValues={{}}
          onValuesChange={onFormLayoutChange}
          onFinish={(value)=>{onFinish(value,true)}}
          onFinishFailed={(value,errorFields, outOfDate )=>{ onFinish(value,false) }}
        >
          <div className={styles.basic_information_info}>
            {/*
              consultationCaseInfoDto: {
                age: null
                sex: null
              }
            */}
            {type != 3 && orderCaseTemplate == 2 &&
              <>
                <div key={'age'} className={styles.basic_information_item}>
                  <div className={styles.Item_pattern}>
                    <div className={styles.Item_title_lable}> <span style={{color:'red'}}>*</span>{'患者年龄'}：</div>
                    <div className={styles.Item_title_value}>
                      <div style={{width:'100%', marginBottom:'16px' }}>
                        <Form.Item
                          label={null}
                          name={'age'}
                          rules={[
                            { required: true, message: '请输入内容!!' },
                            { max: 20, message: `${'患者年龄'}最多输入20字符` }
                          ]}
                        >
                          <Input autoComplete="off" placeholder={`请输入${'患者年龄'}...`} style={{}}></Input>
                        </Form.Item>
                      </div>
                    </div>
                  </div>
                </div>

                <div key={'sex'} className={styles.basic_information_item}>
                  <div className={styles.Item_pattern}>
                    <div className={styles.Item_title_lable}> <span style={{color:'red'}}>*</span>{'性别'}：</div>
                    <div className={styles.Item_title_value}>
                      <div style={{width:'100%', marginBottom:'16px' }}>
                        <Form.Item
                          label={null}
                          name={'sex'}
                          rules={[
                            { required: true, message: '请选择性别!' }
                          ]}
                        >
                          <Select placeholder={`请选择性别`} style={{}}>
                            <Select.Option value={'男'}>男</Select.Option>
                            <Select.Option value={'女'}>女</Select.Option>
                          </Select>
                        </Form.Item>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            }

              {
                ArrByForm.map((itemBy1)=>{
                  if(!itemBy1){ return null }
                  const { dictName,id } = itemBy1 || {};
                  if(itemBy1.operateType == 2) {
                    return (
                      <div key={itemBy1.id} className={styles.basic_information_item}>
                        <div className={styles.Item_pattern}>
                          <div className={styles.Item_title_lable}>{id == 277 && <span style={{color:'red'}}>*</span>}{dictName}：</div>
                          <div className={styles.Item_title_value}>
                            <div style={{width:'100%', marginBottom:'16px' }}>
                              <Form.Item
                                label={null}
                                name={itemBy1.id}
                                rules={id == 277 ? [
                                  { required: true, message: '请输入内容!!' },
                                  { max: 200, message: `${dictName}最多输入200字符` }
                                ] : [{ max: 200, message: `${dictName}最多输入200字符` }]}
                              >
                                <TextArea placeholder={`请输入${dictName}...`} style={{ height: 80 }} />
                              </Form.Item>
                            </div>
                          </div>
                        </div>
                      </div>
                    )
                  }
                })
              }
          </div>
        </Form>

        {/* 上一步下一步 */}
        <div className={styles.submitWarp}>
          <div className={styles.submitBox}>
            <div
              onClick={()=>{
                let value = form.getFieldsValue()
                onFinish(value,false);
                // orderCaseTemplate // : 0, -- 订单病例模板 1通用病例 2正畸病例
                if(type != 3  && orderCaseTemplate == 2){
                  /*if (pageFrom == 'ConsultationDetails') {
                    goBack()
                    dispatch({ type:'CreationOrthodontics/clean' })
                  }else {
                    history.replace(`/ConsultationModule/StartConsultation/Step2?${
                      stringify({
                        consultationId,
                      })
                    }`)
                    dispatch({ type:'CreationOrthodontics/clean' })
                  }*/
                  goBackByProps && goBackByProps();
                }else {
                  dispatch({ type:'CreationOrthodontics/clean' })
                  goBack()
                }
              }}
              className={styles.submit_btn_Cancel}>
              {orderCaseTemplate == 2 && pageFrom != 'ConsultationDetails' ? '上一步' : '取消'}
            </div>
            <div
              className={styles.submit_btn_Enter}
              onClick={()=>{
                if (form) {
                  form.submit();
                }
              }}
            >下一步</div>
            {/*<div
              className={styles.submit_btn_Enter}
              onClick={()=>{
                if (form) {
                  let value = form.getFieldsValue();
                  onFinish(value);
                }
              }}
            >暂存</div>*/}
          </div>
        </div>

      </div>
    </div>
  )
}

export default connect(({ CreationOrthodontics,pcAccount, loading }: any) => ({
  CreationOrthodontics,pcAccount, loading
}))(Step1)
