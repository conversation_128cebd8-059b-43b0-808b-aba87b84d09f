/**
 * @Description: 选择嘉宾列表
 */
import React, { useCallback, useEffect, useState } from 'react';
import { Input, Toast } from 'antd-mobile';
import { connect } from 'umi';
import styles from './index.less';
import searchIcon from '@/assets/GlobalImg/search.png'; // 搜索小图标
import goBackIcon from '@/assets/GlobalImg/go_back.png'; // 返回按钮小图标
import noDataImg from '@/assets/GlobalImg/no_data.png'; // 无网络图片
import { Spin } from 'antd';
import { processNames, randomColor } from '@/utils/utils';
import classNames from "classnames";

const Index: React.FC = (props: any) => {
  const { userInfoStore, dispatch, goBack, loading } = props;
  const { selectedGuest } = userInfoStore || {};
  const [list, setList] = useState<any[]>([]); // 嘉宾列表
  const [isHasData, setIsHasData] = useState(null); // 是否有数据
  const [thisSelectedGuest, setThisSelectedGuest] = useState<Record<string, any>[]>([]); // 勾选中的数据

  // 获取嘉宾列表
  const getListData = (val) => {
    dispatch({
      type: 'userInfoStore/searchUserListByQueryKey',
      payload: {
        queryKey: val && val.trim(),
      }
    }).then(res => {
      if(res && res.code == 200) {
        if(res.content && res.content.length) {
          setList(res.content);
          setIsHasData(0)
        } else {
          setList([]);
          setIsHasData(1)
        }
      }
    })
  }

  // input 搜索嘉宾
  const changeInputFn = (val) => {
    if(!val || !val.trim()) return setList([]);
    getListData(val)
  }

  // 同步store里已保存的选中嘉宾
  useEffect(() => {
    setThisSelectedGuest(selectedGuest);
  }, [selectedGuest]);

  // 选中处理
  const selectHandle = useCallback((item) => {
    const isSelected = thisSelectedGuest.find(it => it?.id === item.id);
    // 已经选择了15位，并且不是取消选择
    if (!isSelected && thisSelectedGuest.length >= 15) return Toast.show({content: '最多能选15位哦~'});
    setThisSelectedGuest(guest => {
      return isSelected ? guest.filter(it => it.id !== item.id) : [...guest, item];
    });
  },[thisSelectedGuest]);

  // 确定，保存到store，返回上层
  const confirmHandle = useCallback(() => {
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        selectedGuest: thisSelectedGuest
      }
    })
    goBack(2);
  }, [dispatch, goBack, thisSelectedGuest]);

  const searchUserListByQueryKeyLoading = !!loading.effects['userInfoStore/searchUserListByQueryKey'] // loading
  return (
    <>
      <div className={styles.titleInfo}>
        <div className={styles.titleWarp}>
          <div className={styles.titleBackIcon} onClick={()=>{goBack(2)}}><img src={goBackIcon} alt="" /></div>
          <div className={styles.titleText}>邀请嘉宾</div>
        </div>
      </div>
      <div className={styles.content}>
        <div className={styles.input_box}>
          <img className={styles.search_icon} src={searchIcon} alt="" />
          <Input placeholder='请输入内容' clearable onChange={changeInputFn} />
        </div>
        <div className={styles.select_num}>已选择{thisSelectedGuest.length}/15</div>
        <Spin spinning={searchUserListByQueryKeyLoading}>
          {list && list.length >= 1 ?
            <div className={styles.list_box}>
              {
                list && list.length && list.map(item =>
                <div  onClick={() => selectHandle(item)} className={styles.list_item} key={item.id}>
                  <div className={styles.list_item_info_wrap}>
                    <div
                      className={classNames({
                        [styles.selectGuest_select]: true,
                        [styles.selectGuest_select_user]: !!thisSelectedGuest.find(
                          (it) => it.id === item.id,
                        ),
                        [styles.selectGuest_Unselect_user]: !thisSelectedGuest.find(
                          (it) => it.id === item.id,
                        ),
                      })}
                    ></div>

                    <div className={styles.list_item_img}>
                      {
                        item.headUrlShow ?
                        <img src={item.headUrlShow} alt="" /> :
                        <div className={styles.no_comment_head} style={{background:randomColor(item?.id)}}>{processNames(item?.name)}</div>
                      }
                    </div>
                    <div className={styles.list_item_info}>
                      <div className={styles.name} dangerouslySetInnerHTML={{__html: item.highlightName}}></div>
                      <div className={styles.phone}>{item.phone}</div>
                    </div>
                  </div>
                  {/*<div className={thisSelectedGuest.find(it => it.id === item.id)
                    ? styles.active_select : styles.init_select}
                  >
                    {thisSelectedGuest.find(it => it.id === item.id) ? '取消选择' : '选择'}
                  </div>*/}
                </div>
                )
              }
            </div> : null
          }
          {
            isHasData == 1 ?
            <div className={styles.nodata}>
              <img src={noDataImg} alt="" />
              <div className={styles.empty_title}>暂无该搜索结果</div>
              <div className={styles.empty_msg}>请试试其他搜索关键词</div>
            </div> : null
          }
        </Spin>
      </div>
      <div className={styles.btn_wrap}>
        <div className={styles.tip}>
          <div className={styles.tip_box}>
            最多支持15个嘉宾自由发言或共享屏幕，其余嘉宾请发送邀请链接进入
          </div>
        </div>
       {/* <div className={styles.cancelBtn}  onClick={()=>{goBack(2)}}>取消</div>
        <div className={styles.submitBtn}  onClick={confirmHandle}>确定</div>*/}

        <div className={styles.btn_box}>
          <div className={styles.btn_wrap_left}>
            {Array.isArray(thisSelectedGuest) &&
              thisSelectedGuest.map((item) => {
                return (
                  <div className={styles.item}>
                    {item.headUrlShow ? (
                      <img src={item.headUrlShow} alt="" />
                    ) : (
                      <div
                        className={styles.no_comment_head}
                        style={{ background: randomColor(item?.id) }}
                      >
                        {processNames(item?.name)}
                      </div>
                    )}
                  </div>
                );
              })}
          </div>
          <div className={styles.btn_wrap_right}>
            <div onClick={confirmHandle} className={styles.btn_submit}>
              确定
            </div>
          </div>
        </div>

      </div>
    </>
  )
}
export default connect(({ userInfoStore, loading }: any) => ({userInfoStore, loading}))(Index)
