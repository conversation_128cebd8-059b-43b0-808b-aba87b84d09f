.content {
  width: 100%;
  height: auto;
  overflow-y: auto;
}

.top_wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  box-sizing: border-box;
  position: relative;

  .total_num {
    font-size: 12px;
    font-weight: 400;
    color: #000000;
    line-height: 14px;
  }

  .screen_btn {
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    line-height: 16px;
    display: flex;
    align-items: center;

    img {
      margin-left: 8px;
    }
  }
}

.bot_wrap {
  background: #F5F6F8;
  :global {
    .adm-swipe-action {
      background: #F5F6F8;
    }
  }
}

.mask_box {
  position: absolute;
  top: 103%;
  left: 0;
  height: 100vh;
}

.screen_wrap{
  padding: 15px 0 0 12px;
  background: #fff;
}
.screen_container{
  height: 100px;
  // overflow-y: auto;
  padding-bottom: 16px;
}
.screen_wrap_content {
  display: flex;
  flex-wrap: wrap;
}

.screen_child_word, .screen_check_child_word {
  margin-right: 12px;
  margin-top: 12px;
  display: inline-block;
  width: 30%;
  height: 32px;
  background: #F5F5F5;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 400;
  color: #666666;
  text-align: center;
  line-height: 32px;
}
.screen_check_child_word{
  background: #EDF9FF;
  color: #0095FF;
}
.screen_check_cases_word,.screen_cases_word{
  // margin-right: 12px;
  margin-top: 12px;
  width: calc(50% - 6px);
  height: 32px;
  background: #F5F5F5;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 400;
  color: #666666;
  text-align: center;
  line-height: 32px;
}

.screen_check_cases_word{
  background: #EDF9FF;
  color: #0095FF;
}

.screen_wrap_footer{
  width: 100%;
  height: 72px;
  padding-right: 12px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  .screen_wrap_footer_close ,.screen_wrap_footer_confirm{
    flex: 1;
    height: 40px;
    border-radius: 22px;
    font-size: 15px;
    font-weight: 500;
    text-align: center;
    line-height: 40px;
  }
  .screen_wrap_footer_close{
    margin-right: 15px;
    background: #EDF9FF;
    color: #0095FF;
  }
  .screen_wrap_footer_confirm{
    background: #0095FF;
    color: #FFFFFF;
  }
}
.screen_picker{
  display: inline-block;
  width: 170px;
  height: 32px;
  background: #f5f5f5;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 400;
  color: #999999;
  text-align: center;
  line-height: 32px;

  .select_date {
    color: #666;
  }
}
.screen_picker_line{
  width: 6px;
  border-top: 1px solid #CCCCCC;
  margin: 0 3px;
}
.screen_wrap_content_picker{
  display: flex;
  justify-content: space-between;
  padding-right: 12px;
  margin-top: 12px;
  align-items: center;
}
