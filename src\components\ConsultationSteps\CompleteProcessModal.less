.popup_container {
  z-index: 1001;
  :global {
    .adm-popup-body {
      border-radius: 16px 16px 0 0;
    }
  }
}
.header_line {
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  .header_line_bar {
    width: 48px;
    height: 4px;
    background: #D0D4D7;
    border-radius: 4px;
  }
}
.header_title {
  position: relative;
  font-size: 18px;
  line-height: 25px;
  text-align: center;
  font-weight: 500;
  margin-bottom: 15px;
  .close_icon {
    position: absolute;
    width: 32px;
    height: 32px;
    top: -3px;
    right: 16px;
    color: #000;
    display: flex;
    align-items: center;
    justify-content: center;
    :global {
      .anticon {
        font-size: 16px;
      }
    }
  }
}
.tab_box {
  padding: 0 16px;
  display: flex;
  margin-bottom: 16px;
  .tab_item {
    flex: 1;
    height: 38px;
    line-height: 40px;
    border-radius: 4px;
    background: #F5F5F5;
    font-size: 16px;
    color: #999;
    text-align: center;
    &:first-child {
      margin-right: 8px;
    }
    &.active {
      color: #0095FF;
      background: #EDF9FF;
    }
  }
}

.process_box {
  position: relative;
  padding: 0 20px;
  .process_name {
    position: absolute;
    top: 7px;
    bottom: 18px;
    right: 32px;
    color: #aaa;
    font-size: 11px;
    word-break: break-all;
    width: 11px;
    padding-left: 9px;
    border-left: 1px dashed #ccc;
    display: flex;
    align-items: center;
    &::before {
      content: "";
      display: block;
      position: absolute;
      top: 0;
      left: -8px;
      width: 8px;
      border-top: 1px dashed #ccc;
    }
    &::after {
      content: "";
      display: block;
      position: absolute;
      bottom: 0;
      left: -8px;
      width: 8px;
      border-top: 1px dashed #ccc;
    }
  }
}
.process_item {
  display: flex;
  flex-wrap: nowrap;
  padding-bottom: 12px;
  margin-bottom: 5px;
  position: relative;
  &::after {
    content: "";
    display: block;
    border-left: 1px dashed #CCC;
    position: absolute;
    top: 25px;
    bottom: 0;
    left: 7px;
  }
  .process_item_icon {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background: #fff;
    border: 4px solid #0095FF;
    margin-top: 3px;
    margin-right: 18px;
  }
  .process_item_number {
    font-size: 14px;
    color: #000;
    font-weight: 500;
    line-height: 20px;
  }
  .content_title {
    font-size: 14px;
    color: #000;
    font-weight: 500;
    line-height: 20px;
    margin-bottom: 2px;
  }
  .content_text {
    font-size: 12px;
    color: #999;
    line-height: 17px;
  }
}
.process_box_last {
  margin-bottom: 32px;
  .process_item:last-child::after {
    display: none;
  }
}
