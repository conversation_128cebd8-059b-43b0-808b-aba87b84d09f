/**
 * @Description: 外部进入中间页面
 * @author: 赵斐
 */
import React, { useEffect, useState } from 'react';
import { connect, history } from 'umi';
import { Spin ,message} from 'antd'

import {
  getMsgCodeUserInfo,
  arrailToUcLogin,
  fridayToUcLogin,
} from "@/services/login/login";
import {stringify} from "qs";
const Index: React.FC = (props: any) => {
  const [state, setState] = useState(false)
  useEffect(()=>{
      autoLogin()
  },[])

  // 自动登录
  const autoLogin = async ()=>{
    const { location } = history
    const { query } = location
    const { isFriday,pageCode ,pageType } = query || {}
    console.log(query,"---222")
    // 开启登录loading
    setState(true);

    localStorage.removeItem('wxuserId')
    // 登录请求
    const res = isFriday =='1'? await fridayToUcLogin({fridayToUcKey:pageCode}):await arrailToUcLogin({arrailToUcKey:pageCode});
    // 登录成功
    if (res.code === 200) {
      // message.success('登录成功');
      // 保存token
      localStorage.setItem('access_token', res.content.access_token);
      // 获取用户信息接口时，header头里面需要传userName，值为手机号
      localStorage.setItem('userInfo', JSON.stringify({
        phone: res.content.username
      }));
      await getMsgCodeUserInfo_func(pageType,res.content.username)
    } else {
      setState(false) // 关闭登录loading
      message.error(res.msg);
    }
  }

    /**
   * [请求] 登录后获取用户信息
   * 获取成功后跳转到"redirect" 如果无 'redirect'
   * 跳转到活动页面
   * */
    const getMsgCodeUserInfo_func = async (pageType:any,phone:number) => {
      try {
        let token_text = localStorage.getItem('access_token');
        let userInfo = await getMsgCodeUserInfo({ token: token_text, username:phone });

        if (userInfo?.code === 200) {
          setState(false) // 关闭登录loading
          // 保存用户信息
          localStorage.setItem('userInfo', JSON.stringify({
            ...userInfo.content,
            id: userInfo?.content?.friUserId
          }));
          // 重定向到指定页面
          await jumpFun(pageType)
        }
      } catch (error) {
        setState(false) // 关闭登录loading
        console.error('获取用户信息失败：', error);
      }
    };
  /**
 * 跳转
 * @param type  1 跳转空间详情 2 跳转病例列表搜索结果页面  3 指导详情 4 创建指导 5 跳转到创建正畸病例页面
 * @param type  6 跳创建指导-第3步编辑病例（通用模板），7 跳创建指导-第3步编辑病例（正畸模板），8 跳创建指导-第4步提交页
 */
  const jumpFun = (type: any) => {
    const { location } = history
    const { query } = location
    const {
      spaceId ,
      consultationId ,
      consultationType ,
      modalStatus ,
      expertsUserId,
      customerId,
      tenantId,
    } = query || {}
    let pageFrom = 'ExternalPage'
    if (type == 1) {
      history.replace(`/PlanetChatRoom/Live/${spaceId}?isGoback=1`)
    } else if (type == 2){
      history.replace("/Payment/MemberBenefitsPage")
    }else if (type == 3){
      history.replace(`/ConsultationModule/ConsultationDetails?consultationId=${consultationId}&consultationType=${consultationType}&isGoback=1&modalStatus=${modalStatus}`)
    }else if (type == 4){
      history.replace(`/ConsultationModule/StartConsultation/Step1?expertsUserId=${expertsUserId}`)
    }else if(type == 5) {
      history.replace(`/CreationOrthodontics/Step1?${stringify({
        customerId,
        tenantId,
        consultationId,
        pageFrom,
      })}`)
    } else if (type == 6) {
      history.replace(`/ConsultationModule/StartConsultation/Step3?consultationId=${consultationId}`)
    } else if (type == 7) {
      history.replace(`/CreationOrthodontics/Step1?orthodonticConsultationId=${consultationId}&${stringify({pageFrom})}`)
    } else if (type == 8) {
      history.replace(`/ConsultationModule/StartConsultation/Step4?consultationId=${consultationId}`)
    }
  }

  return (
    <Spin spinning={state}>
      <div style={{height:"100vh"}}></div>
    </Spin>
  )
}
export default connect(({ cases, loading }: any) => ({ cases, loading }))(Index)

