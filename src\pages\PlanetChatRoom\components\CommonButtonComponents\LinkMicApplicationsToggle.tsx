// 展示申请连麦列表
// SetConfigButton.js
import React from 'react';
import styles from './index.less';  // 引入自定义样式

const SideListTypeForSettings = 'Settings'                  // 设置列表
const SideListTypeForApply = 'Apply'                          // 申请列表
const SideListTypeForDistinguished = 'Distinguished'    // 嘉宾列表
const SideListTypeForSignInList = 'SignInList'             // 打卡列表

const LinkMicApplicationsToggle = ({
                                     isLive,
                                     isJoined,
                                     setIsShowApplyForLinkMicList,
                                     getHandUpList,
                                     handUpList,
                                     isShowApplyForLinkMicList

}) => {
  const handleSetConfigClick = (e) => {
    e.stopPropagation()
    e.preventDefault()
    // isShowApplyForLinkMicList 申请连麦列表
    setIsShowApplyForLinkMicList(isShowApplyForLinkMicList == SideListTypeForApply ? null : SideListTypeForApply)
    getHandUpList()
  };

  return (
    <div className={styles.title_Icon_shenqing_warp}>
      <div
        onClick={handleSetConfigClick} className={styles.HorizontalLiveRoom_Btn_Warp}>
        <i className={styles.title_Icon_shenqing_Icon}></i>
        <div className={styles.text}>申请</div>
      </div>
      {/* 展示当前申请连麦人数 */}
      {Array.isArray(handUpList) && handUpList.length > 0 &&
        <i className={styles.HandUpListLength}>{handUpList.length}</i>
      }
    </div>
  );
};

export default LinkMicApplicationsToggle;
