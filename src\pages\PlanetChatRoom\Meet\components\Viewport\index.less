.ViewContent {
  position: relative;
  width: 100%;
  height: calc(100% - 8px);
  // padding: 10px;
  // border: 1px solid #fff;
  overflow: hidden;
}

.ViewLine {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
}

.WaitNumBox {
  width: 100%;
  height: 24px;
  color: #a0a0a0;
  font-weight: 400;
  font-size: 12px;
  font-style: normal;
  line-height: 24px;
  text-align: center;
  text-transform: none;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px 2px 2px 2px;

  .num {
    color: #0095ff;
  }
}

.swiperWarp {
  width: 100%;
  height: calc(100% - 20px);
  padding-top: 10px;
}

.MatrixStreamListWarp {
  width: 100%;
  height: 100%;
}

.ParticipantStreamListByTopWarp {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
  height: 60%;
}

.ParticipantStreamListByTopWarp_isHorizontalLisive {
  width: 100%;
  height: 100%;
}

.ShareRemoteStreamWarp {
  width: 100%;
  height: 56.25%;
  background: #a6a6a6;
  aspect-ratio: 16/9;
}

.ShareRemoteStreamWarp_isHorizontalLisive {
  width: 100%;
  height: 100%;
}

.Live_Video {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  // background: url("https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/PlanetChatRoom_HorizontalLiveRoom_Live_bg.png") no-repeat;
  // background-size: 100% 100%;
  overflow: hidden;

  .spaceCoverUrlShow {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
