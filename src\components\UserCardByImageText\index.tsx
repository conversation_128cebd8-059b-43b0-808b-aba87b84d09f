/**
 * @Description: 图文模块专用-用户卡片（H5）
 */
import React from 'react';
import { processNames, randomColor } from '@/utils/utils'
import styles from './index.less';

import Follow from '@/components/Follow' // 关注按钮组件
import MoreOperate from '@/components/MoreOperate' // 点点点
import Avatar from '@/components/Avatar' // 头像

// 图片、icon
import expertCertificationIcon from '@/assets/GlobalImg/expertCertificationIcon.png' // 专家标识icon

interface PropsType {
  headUrlShow: string,                 // 头像
  userName: string,                    // 用户名
  createUserId: number,                // 用户ID
  isExperts: number,                   // 是否是专家：0:否，1:是
  operateDateDescs: string,            // 创建日期
  isFocus: number,                     // 0未关注，1已关注
  expertsInfo: any,                    // 专家信息
  handleFollowAndCheck: () => void, // 关注或取消关注回调
  handleDeleteOrLow?: () => void, // 删除回调
  imageType?: number, // 图文类型：1 文章，2 帖子，3 外链，4 空间
  style?: any, // 样式
  isShowMoreOperate?: any, // 是否展示更多操作按钮
  id?: any, // 图文ID
  status?: any, // 状态：1.审核通过（已发布） 0.未审核 2.审核未通过 3.草稿
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}') // 用户信息
  const {
    headUrlShow, // 头像
    userName, // 姓名
    createUserId, // 用户ID
    isExperts, // 是否是专家，1是 0否
    operateDateDescs, // 时间
    isFocus, // 是否关注，1已关注，0未关注
    expertsInfo, // 专家信息
    isShowMoreOperate = false, // 是否展示点点点更多操作，true展示，false不展示
    id, // 图文ID
    imageType, // 图文类型，
    status, // 图文类型：1.文章 2.帖子 3.外链 4.空间
    style = {}, // 样式
    handleFollowAndCheck, // 关注回调
    handleDeleteOrLow, // 删除、下架回调
  } = props;

  return (
    <div className={styles.header} style={style}>
      <div className={styles.header_left}>
        {/* 头像 */}
        <Avatar
          userInfo={{
            userId: createUserId,
            name: userName,
            headUrlShow: headUrlShow,
          }}
          size={36}
        />
        {/* 姓名、职称等 */}
        <div className={styles.left_info_wrap}>
          <div className={styles.user_info}>
            <span className={styles.user_name}>{userName}</span>
            {
              isExperts == 1 && expertsInfo &&
              <>
                <span className={styles.user_gray_bar}></span>
                <span className={styles.user_grade}>{expertsInfo.postTitleDictName}</span>
                <span className={styles.user_tag}>{expertsInfo.depSubjectDictName}·{expertsInfo.abilityLevelDictName}</span>
              </>
            }
          </div>
          <div className={styles.date}>
            <span>
              {operateDateDescs}
              {isExperts == 1 && expertsInfo && `·${expertsInfo.organizationName}`}
            </span>
            {
              isExperts == 1 && expertsInfo &&
              <img src={expertCertificationIcon} width={12} height={12} alt=""/>
            }
          </div>
        </div>
      </div>
      {/* 本人不展示关注按钮 */}
      <div className={styles.header_right}>
        {
          UserInfo.friUserId != createUserId ?
            <Follow isFocus={isFocus} expertsUserId={createUserId} handleFollowAndCheck={handleFollowAndCheck}/>
            : isShowMoreOperate ?
            <MoreOperate id={id} imageType={imageType} status={status} handleDeleteOrLow={handleDeleteOrLow}/>
            : null
        }
      </div>
    </div>
  )
}
export default Index
