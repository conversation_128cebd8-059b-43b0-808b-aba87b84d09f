.container {
  height: 100vh;
  background: #f5f6f8;
  padding-top: 44px;
  overflow-y: auto;
  padding-bottom: 66px;
}
.gray_bar {
  height: 8px;
}
.header {
  background: #FFF7DA;
  border-top: 1px solid #EFD989;
  border-bottom: 1px solid #EFD989;
  padding-left: 9px;
  font-size: 12px;
  color: #8C772B;
  height: 33px;
  line-height: 33px;
}

.complete_process_wrap {
  padding: 14px 16px 0;
  background: #fff;
}

.question_box {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #000;
  font-weight: 500;
  line-height: 20px;
  padding: 0 16px;
  background: #fff;
  margin-bottom: 8px;
  .question_label {
    flex-shrink: 0;
    white-space: nowrap;
    margin-right: 8px;
  }
  .question_value {
    flex: 1;
    :global {
      .adm-input-element {
        text-align: right;
        font-size: 13px;
        color: #000;
        height: 52px;
      }
    }
  }
}

.details_box1 {
  height: 44px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 9px 16px 0;
  background: #fff;
  :global {
    .adm-radio-content {
      font-size: 14px;
      color: #000;
    }
    .adm-radio-checked .adm-radio-content {
      color: #0095FF;
    }
    .adm-radio.adm-radio-checked .adm-radio-icon {
      border-color: #0095FF;
      background: #0095FF;
    }
    .adm-radio + .adm-radio {
      margin-left: 32px;
    }
    .adm-radio {
      --icon-size: 16px;
    }
  }
  .label {
    flex-shrink: 0;
    font-size: 14px;
    color: #000;
    font-weight: 500;
  }
}
.details_box2 {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px 11px;
  background: #fff;
  :global {
    .anticon {
      display: block;
    }
  }
  .label {
    font-size: 14px;
    color: #000;
    font-weight: 500;
  }
  .details_content {
    display: flex;
    align-items: center;
    color: #aaa;
    .count {
      font-size: 13px;
      margin-right: 6px;
    }
    .checkbox {
      font-size: 20px;
      &.checked {
        color: #0095FF;
      }
    }
  }
}

.agreement {
  margin-top: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f6f8;
  padding-bottom: constant(safe-area-inset-bottom);//兼容 IOS<11.2
  padding-bottom: env(safe-area-inset-bottom);//兼容 IOS>11.2
  .agreement_checkbox {
    font-size: 14px;
    color: #C6C6C6;
    padding: 2px 6px;
    :global {
      .anticon {
        display: block;
      }
    }
    &.checked {
      color: #0095FF;
    }
  }
  .agreement_text {
    font-size: 12px;
    color: #666;
    line-height: 17px;
    .highlight {
      color: #0095FF;
    }
  }
}

.fixed_box {
  position: fixed;
  z-index: 999;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding-bottom: constant(safe-area-inset-bottom);//兼容 IOS<11.2
  padding-bottom: env(safe-area-inset-bottom);//兼容 IOS>11.2
  .pay_box {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: space-between;
    padding: 9px 16px 9px 20px;
    border-top: 1px solid #EEE;
    .pay_text {
      font-size: 12px;
      color: #666;
      line-height: 17px;
      width: 160px;
    }
    .pay_price {
      display: flex;
      align-items: baseline;
      font-size: 13px;
      color: #999;
      line-height: 18px;
      .unit {
        font-size: 14px;
        color: #FF5F57;
        font-weight: bold;
        margin-left: 8px;
      }
      .price {
        font-size: 24px;
        color: #FF5F57;
        font-weight: bold;
        line-height: 29px;
      }
    }
    .pay_btn {
      width: 128px;
      min-width: 128px;
      height: 40px;
      line-height: 40px;
      border-radius: 20px;
      font-size: 16px;
      text-align: center;
      background: #0095FF;
      color: #fff;
      &.disabled {
        background: #DEDEDE;
        color: #fff;
      }
    }
  }

}
