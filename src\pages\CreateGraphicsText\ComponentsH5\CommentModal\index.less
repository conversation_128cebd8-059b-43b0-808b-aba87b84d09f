.popup_container {
  :global {
    .adm-popup-body {
      border-radius: 16px 16px 0 0;
      display: flex;
      flex-direction: column;
      height: 100%;
    }
  }
}
.header {
  flex-shrink: 0;
  width: 100%;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  .gray_bar {
    width: 48px;
    height: 4px;
    border-radius: 4px;
    background: #D0D4D7;
  }
}
.title_box {
  flex-shrink: 0;
  width: 100%;
  height: 24px;
  text-align: center;
  position: relative;
  font-size: 17px;
  color: #000;
  line-height: 27px;
  :global {
    .anticon {
      font-size: 16px;
      color: #000;
      position: absolute;
      left: 12px;
      top: 0;
      padding: 4px;
    }
  }
}
.content {
  flex: 1;
  overflow-y: auto;
  padding-top: 13px;
}
.comment_item {
  margin-bottom: 16px;
  padding: 0 12px;
  .comment_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: nowrap;
    margin-bottom: 4px;

    .right {
      flex: 1;
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      justify-content: space-between;
      .right_content {
        flex: 1;
        .user_box {
          display: flex;
          .user_name {
            font-size: 13px;
            color: #000;
            font-weight: 500;
            height: 18px;
            line-height: 17px;
            margin-right: 8px;
          }
          .user_role {
            height: 18px;
            line-height: 20px;
            background: #E4F4FF;
            border-radius: 4px;
            padding: 0 4px;
            font-size: 10px;
            color: #0095FF;
          }
        }
        .time {
          font-size: 10px;
          color: #666;
          height: 14px;
          line-height: 15px;
        }
      }
      .right_follow {
        flex-shrink: 0;
      }
      .like {
        flex-shrink: 0;
        display: flex;
        align-items: center;

        .icon_like {
          width: 16px;
          height: 16px;
          margin-right: 4px;
          background: url("../../../../assets/GlobalImg/like.png") no-repeat center;
          background-size: 100% 100%;
        }
        .like_active {
          width: 16px;
          height: 16px;
          margin-right: 4px;
          background: url("../../../../assets/GlobalImg/like_active.png") no-repeat center;
          background-size: 100% 100%;
        }

        & > span {
          font-size: 12px;
          color: #000;
          height: 16px;
          line-height: 17px;
        }
      }
    }
  }
  .comment_content {
    padding-left: 40px;
    font-size: 14px;
    color: #222;
    line-height: 20px;
    word-break: break-all;
    margin-bottom: 4px;
  }
  .comment_btn_box {
    display: flex;
    align-items: center;
    padding-left: 40px;
    .comment_btn_wrap {
      display: flex;
      align-items: center;
      margin-right: 16px;
      & > i {
        width: 16px;
        height: 16px;
        margin-right: 4px;
        &.icon_like {
          background: url("../../../../assets/GlobalImg/like.png") no-repeat center;
          background-size: 100% 100%;
        }
        &.like_active {
          background: url("../../../../assets/GlobalImg/like_active.png") no-repeat center;
          background-size: 100% 100%;
        }
        &.icon_comment {
          background: url("../../../../assets/GlobalImg/comment.png") no-repeat center;
          background-size: 100% 100%;
        }
      }
      & > span {
        font-size: 12px;
        color: #000;
        height: 16px;
        line-height: 17px;
      }
    }
  }
  .comment_reply_box {
    display: flex;
    padding-left: 40px;
    .reply_btn {
      display: flex;
      align-items: center;
      flex-wrap: nowrap;
      height: 25px;
      padding: 0 4px 0 8px;
      border-radius: 30px;
      background: #F5F5F5;
      & > span {
        font-size: 12px;
        color: #666;
        height: 16px;
        line-height: 17px;
      }
      & > i {
        width: 16px;
        height: 16px;
        background: url("../../../../assets/GlobalImg/right_arrow_2.png") no-repeat center;
        background-size: 100% 100%;
      }
    }
  }
  .comment_quote {
    font-size: 14px;
    color: #666;
    line-height: 20px;
    background: #F5F6F8;
    padding: 4px 8px;
    margin-bottom: 8px;
    margin-left: 40px;
    word-break: break-all;
  }
}
.comment_item.first {
  .comment_header {
    margin-bottom: 8px;
  }
  .comment_content {
    margin-bottom: 8px;
  }
}
.divider {
  width: 100%;
  height: 0;
  border-top: 1px solid #eee;
  margin-bottom: 16px;
}
.comment_title {
  font-size: 16px;
  color: #000;
  line-height: 22px;
  margin-bottom: 16px;
  padding-left: 12px;
}
