/**
 * @Description: 画布区渲染的，行业大事件
 */

import React, { useMemo, useState, useEffect } from 'react';
import dayjs from 'dayjs';
import styles from './index.less';

// 三种展示风格效果
import OfflineCom from './Components/OfflineCom';
import OnlieCom from './Components/OnlineCom';
import CalendarCom from './Components/CalendarCom';

// 公共头部
import Header from './Components/Header';


interface IProps {
  componentData: any,                                      // 组件数据，格式{ dataList: [] }
  pcOrMobileMode: string;                                  // 移动端还是pc端
}

interface IMonth {
  year: string;
  month: string;
}

interface IParams {
  date?: string;
  city?: string;
}

const initialParams = {
  date: '',    // 选择的年月
  city: '推荐', // 推荐：all
}

const Index: React.FC<IProps> = ({ componentData, pcOrMobileMode = 'mobile' }) => {
  const dataList = componentData.dataList || [];     // 所有大事件的列表 -- 初始化
  const styleType = componentData.styleType;         //样式风格
  const [filterDataList, setFilterDataList] = useState(dataList)  // 筛选的数据
  const [filterParams, setFilterParams] = useState<IParams>(initialParams)  // 筛选参数


  // 更新筛选查询的参数
  const getFilterParams = (type: string, params: string) => {
    setFilterParams((prevState) => {
      const newState = { ...prevState, [type]: params };
      return newState;
    })
  }

  // 筛选数据
  useEffect(() => {
    if (filterParams.date == '' && filterParams.city == '推荐') {  //全部数据
      setFilterDataList(dataList)
    } else if (filterParams.date == '' && filterParams.city !== '推荐') { //只是根据城市筛选
      const res = dataList.filter(item => item.location.includes(filterParams.city))
      setFilterDataList(res)
    } else if (filterParams.date && filterParams.city === '推荐') { // 只是根据月份筛选
      const res = dataList.filter(item => item.eventStartDate.startsWith(filterParams.date) || item.eventEndDate.startsWith(filterParams.date))
      setFilterDataList(res)
    } else if (filterParams.date && filterParams.city !== '推荐') { // 根据月份和城市一起
      const res = dataList.filter(item => item.location.includes(filterParams.city))?.filter(it => it.eventStartDate.startsWith(filterParams.date) || it.eventEndDate.startsWith(filterParams.date))
      setFilterDataList(res)
    }

  }, [filterParams])


  // 计算城市 
  const citys = useMemo(() => {
    const res = ['推荐']
    dataList.forEach(item => {
      if (item.location) res.push(item.location.split('/')[1])
    });
    return Array.from(new Set(res))
  }, [dataList])


  // 计算年份 / 月份
  const months = useMemo(() => {
    const res: IMonth[] = [];

    // 使用 Set 去重
    const uniqueMonths = new Set();

    dataList.forEach(item => {
      const date = item.eventStartDate;
      const year = dayjs(date).year();
      const month = (dayjs(date).month() + 1).toString();
      const key = `${year}-${month}`;

      if (!uniqueMonths.has(key)) {
        uniqueMonths.add(key);
        res.push({ year: year.toString(), month });
      }
    });

    // 排序
    res.sort((a, b) => {
      if (a.year === b.year) {
        return a.month - b.month; // 年份相同，按月份排序
      }
      return a.year - b.year; // 按年份排序
    });

    return res
  }, [dataList])

  return <div className={pcOrMobileMode === 'mobile' ? styles.major_mobile_container : styles.major_pc_container}>
    <Header monthsList={months} getFilterParams={getFilterParams} pcOrMobileMode={pcOrMobileMode} />
    {styleType === '1' && <OfflineCom dataList={filterDataList} styleType={styleType} citys={citys} pcOrMobileMode={pcOrMobileMode} getFilterParams={getFilterParams} />}
    {styleType === '2' && <OnlieCom dataList={filterDataList} styleType={styleType} pcOrMobileMode={pcOrMobileMode} />}
    {styleType === '3' && <CalendarCom dataList={filterDataList} pcOrMobileMode={pcOrMobileMode} />}
  </div>
}

export default Index;