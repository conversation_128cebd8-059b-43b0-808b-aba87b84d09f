import React, {useEffect, useRef, useState} from 'react';
import {connect, Helmet, history, useAliveController} from "umi";
import styles from './SpatialDetail.less';
import VerticalLiveRoom from './components/VerticalLiveRoom';
import HorizontalLiveRoom from './components/HorizontalLiveRoom';
import DownloadAppCard from '@/components/DownloadAppCard';
import {userTokenInvalid} from '@/utils/request';
import {
  clearLocalStateByStreamById,
  getLocalStateByStream,
  getUrlParam,
  saveLocalStateByStream,
} from "@/utils/utilsByTRTC";
import TIM from 'tim-js-sdk';
import TIMUploadPlugin from 'tim-upload-plugin';
import TIMProfanityFilterPlugin from 'tim-profanity-filter-plugin';
import TRTC from "trtc-js-sdk";
import {stringify} from "qs";
import {Input, message, Spin} from "antd";
import {ActionSheet, Mask, Modal, Popup, Toast} from 'antd-mobile'
import {
  anchor,
  audience,
  BULLET_SCREEN,
  CAMERA_TOGGLE,
  FORCED_END,
  HAND_DOWN,
  HAND_UP,
  NO_PW_APPLY,
  ROOM_DISBAND,
  SEND_APPLAUSE,
  SEND_FLOWERS,
  SIGN_IN,
  UPDATA_STATE,
  WHITEBOARD_MSG,
} from '@/app/config';
import dynamic from "next/dynamic";
import {debounce, throttle} from 'lodash';
import moment from "moment";
import {
  backInApp,
  checkDownloadAppCardExpiration,
  getArrailUrl,
  getDownLoad,
  getOperatingEnv,
  getShareUrl,
  getViewportHeightWithoutSafeArea,
  goToHomePage,
  imResponseFormatLocalData,
  requestCameraInApp,
  requestMicrophoneInApp,
  useDebounce,
  WxAppIdByPublicAccount,
} from "@/utils/utils";
import DeviceDetector from '@/componentsByTRTC/DeviceDetector/index';
import fullscreen from "fullscreen.js";
import classNames from "classnames";
import PosterModal from "@/pages/Poster/PosterModal"

const userAgent = typeof navigator !== 'undefined' && navigator && navigator.userAgent;

const DynamicRtc = dynamic(import('@/componentsByTRTC/BaseRTC'), {ssr: false});

const DynamicShareRtc = dynamic(import('@/componentsByTRTC/ShareRTC'), {ssr: false});

const DeviceDetectorByDetector = dynamic(import('@/componentsByTRTC/DeviceDetector/detector'), {ssr: false});
const baseSize = 15;


const Index: React.FC = (props) => {
  const isInIframe = (self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya')

  const { pathname, query } = history.location
  const { drop,clear } = useAliveController();

  const {
    PlanetChatRoom,
    dispatch,
  } = props;
  const {
    isHorizontalLive: isHorizontalLiveByProps,
    isMobile,
    isOpenDanmu,
    SpaceInfo,
    isInitialized,
    userInfo,
    screenShareUser,
    handUpList,
    currentUserType,
    ModalVisibleBySpaceViolation,
    ModalVisibleBySpaceRemoved,
    ModalVisibleByCancelAppointment,
    ModalVisibleByClosedSpace,
    ModalVisibleByAcceptLienMai,
    ModalVisibleByAppointmentClosedSpace,
    ModalVisibleByStartLive,
    ModalVisibleByAcceptLienMaiNoMicrophone,
    ModalVisibleByNoMicrophone,
    ModalVisibleByOrientationWrong,
    ModalVisibleByVerticalPageWrong,
    ModalVisibleByLeaveMicrophone,
    ModalVisibleByForceWheat,
    ModalVisibleByEndRecording,
    ModalVisibleByKickedOut,
    ModalVisibleByUserTokenInvalid,
    ModalVisibleByShareScreenError,
    isNotLogin,
    ModalVisibleByApplicationSubmitted,
    ModalVisibleByActionSheetShare,
    isOpenTEduBoard,
  } = PlanetChatRoom || {};

  const {
    imAppId,
    trtcAppId,
    imGroupId,
    imUserId,
    userSig,
    roomId,
    isNeedPwd,
    wxUserId,
    status,
    handUpStatusType,
    handUpType,
    videoList,
    recordType,
    recordStartTime,
    name: nameBySpaceInfo,
    imagePhotoPathShow,
    isNoPasswordApply,
    starSpaceType,
  } = SpaceInfo || {}
  const val = React.useRef();
  const starSpaceTypeText = starSpaceType == 2 ? '会议' : '直播';
  const starSpaceTypeLianMaiText = starSpaceType == 2 ? '发言' : '连麦';

  const posterModalRef = useRef(null)
  const inputRefs = useRef([]);
  const video = true;
  const audio = true;
  const mode = 'rtc';

  const [RTC, setRTC] = useState(null);                                             // rtc对象
  const [shareRTC, setShareRTC] = useState(null);                                   // rtc分享对象
  const [userID, setUserID] = useState('');                                       // 用户id
  const [roomID, setRoomID] = useState('');                                       // 房间id
  const [cameraID, setCameraID] = useState('');                                   // 摄像头id
  const [microphoneID, setMicrophoneID] = useState('');                           // 麦克风id
  const [timObj, setTimObj] = useState(null);                                      // im对象
  const [localStreamConfig, setLocalStreamConfig] = useState(null);                // 本地流配置
  const [remoteStreamConfigList, setRemoteStreamConfigList] = useState([]);      // 远端流配置
  const [isJoined, setIsJoined] = useState(false);                              // 是否加入
  const [isPublished, setIsPublished] = useState(false);                        // 是否发布流
  const [userRole, setUserRole] = useState(anchor);                                       // 设置默认角色
  const [mountFlag, setMountFlag] = useState(false);                            // 是否挂载
  const [pwdArray, setPwdArray] = useState([null,null,null,null]);                // 空间密码校验
  const [loadingCheckSpacePassword, setLoadingCheckSpacePassword] = useState(null);                // 空间密码校验的请求是否进行中
  const [loadingByCheckDevices, setLoadingByCheckDevices] = useState(null);        // 是否展示loading
  const [startTime, setStartTime] = useState(null);                                // 录制开始时间
  const [elapsedTime, setElapsedTime] = useState(0);                            // 经过的时间
  const [loadingByPage,setLoadingByPage] = useState(null);                         // 页面loading
  const [maskVisible, setMaskVisible ] = useState(false)                        // 分享状态
  const [SpaceInfoObj, setSpaceInfoObj] = useState(null)                           // 空间资料
  const [isHorizontalLive, setIsHorizontalLive] = useState(null)                   // 是否横屏直播
  // 创建消息组id
  const [msgGroupId,setMsgGroupId] = useState(`${moment().format('YYYYMMDDHHmmss')}${Math.random().toString().slice(2,6)}`);
  // 连续点击鼓掌的数量
  const [clickNumByAPPLAUSE,setClickNumByAPPLAUSE] = useState(0);
  // 连续点击送花的数量
  const [clickNumByFLOWERS,setClickNumByFLOWERS] = useState(0);
  // 重置发送消息的防抖
  const [debounceTimeout,setDebounceTimeout] = useState(null);

  // 获取子组件
  const RefByHorizontalLiveRoom = useRef(null);
  const RefByVerticalLiveRoom = useRef(null);
  const UerInfo = JSON.parse(localStorage.getItem('userInfo')) || {};

  // 远端流中屏幕分享流或白板流
  const shareHostRemoteStreamConfig = SpaceInfo && Array.isArray(remoteStreamConfigList) && remoteStreamConfigList.find(item => item.userID.indexOf('share') !== -1 && item.hasVideo);
  // 初始化方法
  useEffect(async () => {
    const isHorizontalLiveByPrams = getUrlParam('isHorizontalLive') == 1
    await dispatch({ type: 'PlanetChatRoom/clean'});
    await dispatch({ type: 'PlanetChatRoom/closeSmallWindow'});  // 关闭直播间小窗口
    await dispatch({
      type: 'PlanetChatRoom/setState',
      payload: { isNotLogin: UerInfo && UerInfo.friUserId ? false : true } // 是否登录
    })
    const userAgent = typeof navigator !== 'undefined' && navigator && navigator.userAgent;
    const IsMobile = Boolean(userAgent.match(/Android|BlackBerry|iPhone|iPad|iPod|Opera Mini|IEMobile|WPDesktop/i));
    if(!IsMobile){
      const isHorizontalLiveByParams = isHorizontalLiveByPrams ? isHorizontalLiveByPrams : !IsMobile
      await dispatch({
        type: 'PlanetChatRoom/setState',
        payload: {
          ModalVisibleByOrientationWrong: false,
          isHorizontalLive: isHorizontalLiveByParams,
        }
      })
    }
    await userActiveJoinSpace();   // 后端初始化空间
    await getMsgCodeUserInfo();    // 更新用户信息
    await getSpaceInfo();          // 获取空间信息
    await getGuestList();          // 获取嘉宾列表信息
    await getSpaceGroupMsg();      // 获取空间历史消息
    await getApplyAdmissionList(); // 获取成员列表信息
    await getHandUpList();        // 获取连麦列表


    // 全部初始化页面接口获取完毕后
    // 执行判定用户身份权限方法,判定用户是否是主播,是否是嘉宾
    await dispatch({
      type: 'PlanetChatRoom/setCurrentUserType',
      payload: {}
    })
    message.config({
      duration: 3,
      maxCount: 1,
    });
    // window.document.body.style.margin = 0;
    // window.document.body.style.padding = 0;
    window.document.body.style.overflow = 'auto';
    // window.document.body.style.width = '100%';
    // window.document.body.style.height = `600px`;
    // document.querySelector('')
    onSize();
    window.addEventListener('orientationchange', onOrientationchange);
    window.addEventListener('orientationchange', onSize);
    // 验证下载Friday App是否过期
    checkDownloadAppCardExpiration();
  }, []);

  // 销毁组件关闭直播空间页面的事件方法
  useEffect(() => {
    return ()=> {
      window.document.body.style = ''
      // 清空直播间数据
      dispatch({ type: 'PlanetChatRoom/clean', })
      /*if(val.current && val.current.handUpStatusType == 1) {
        operateHandUp({
          statusType: 3,
          wxUserId: val.current.wxUserId,
          guestUserId: val.current.wxUserId,
          imUserId: val.current.imUserId
        })
      }*/
      setTimObj(null);
      setRTC(null);
      setShareRTC(null);
      // 判定是否需要开启小窗口
      if (!!val.current) {
        // 关闭白板推流
        if (val.current.isOpenTEduBoard) {
          dispatch({
            type: 'PlanetChatRoom/stopWhiteboardPush',
            payload: { spaceId: val.current.id }
          })
        }
        // isNeedPwd,  // 是否需要密码 需要输入密码 0：不需要 1需要
        // status,     // 状态：1直播中、2预约中、3弹幕轰炸中
        // 3弹幕轰炸中 状态下直播间
        if (
          (val.current.status == 1
            && val.current.isNeedPwd == 0)
          || (val.current.isNeedPwd == 0
            && val.current.status == 3
            && !!Array.isArray(val.current.videoList)
            && val.current.videoList.length > 0
          )
        ) {
          // 开启直播间小窗口
          dispatch({
            type: 'PlanetChatRoom/showSmallWindow',
            payload: {
              isShowLiveSmallWindow: val.current.id,
            }
          });
        }
        // 关闭空间/异常退出等，需要关闭空间，记录用时（录播调)
        if (val.current.status == 3 && val.current.joinRandStr){
          clear().then(()=>{});
          dispatch({
            type: 'PlanetChatRoom/closeSpaceWindow',
            payload: {
              spaceId:val.current.id,      //  [string] 是 空间ID
              joinRandStr:val.current.joinRandStr,  // [string] 是进入时的字符串，从详情中取
            }
          })
        }

        // 空间类型 1-直播 2-会议
        if (val.current.starSpaceType == 2) {
          clear().then(()=>{});
        }
      }
    }
  },[])

  //

  // 出事RTC完成后
  useEffect(() => {
    return async ()=> {
      // 本地流发布状态下 停止发布
      if (RTC && (RTC.isPublished || RTC.isPublishing)) {
        await handleUnPublish(); // 关闭本地流
      }
      // 加入房间状态下 退出房间
      if (RTC && RTC.isJoined) {
        await handleLeave(); // 退出房间
      }
    }
  },[RTC])


  // 保存空间详情信息
  useEffect(() => {
    const {
      status  // 状态：1直播中、2预约中、3弹幕轰炸中
    } = SpaceInfoObj || {};
    val.current = null;
    val.current = {
      ...SpaceInfoObj,
      currentUserType: currentUserType, // currentUserType: 1主播 2嘉宾 3观众
      isOpenTEduBoard: isOpenTEduBoard,
    };
  },[SpaceInfoObj,isOpenTEduBoard])

  useEffect(() => {
    if (!!isPublished && currentUserType == 1 && recordType == 1) {
      message.success(`您已开始录制`)
    }
  },[isPublished])


  useEffect(() => {
    setLoadingByPage(false); // 设置页面加载状态
    // 销毁组件时 关闭本地流并退出房间
    return async ()=> {
      // 当在连麦状态下退出连麦状态
      if(val.current && val.current.handUpStatusType == 1) {
        await operateHandUp({
          statusType:3,
          wxUserId: val.current.wxUserId,
          guestUserId: val.current.wxUserId,
          imUserId: val.current.imUserId
        })
        await sendMessageByIm({dataType: HAND_DOWN, description: '1'})
      }

      // 退出tim直播群
      if (!!timObj) {
        // 补充销毁接收信息事件逻辑
        timObj.off(TIM.EVENT.MESSAGE_RECEIVED,onMessageReceived)
        timObj.off(TIM.EVENT.KICKED_OUT, onKickedOut);
        // 暂不销毁 保留tim实例
        /*try {
          await timObj.destroy(); // 销毁
        }catch (error) {
          console.warn('Tim destroy error:', error);
        }*/
      }
    }
  },[timObj])

  // 初始化数据调用完成 初始化配置im和trtc
  useEffect(() => {
    if(isInitialized) {
      // 获取到 imUserId 初始化IM组件和对象
      if (imUserId && !timObj) {
        initializationByIm()
      }
      // 获取到 trtcAppId 初始化TRTC组件和对象
      if (trtcAppId && roomId) {
        initializationByTRTC()
      }

      // 更新gpd
      if (query.shareUserId && SpaceInfoObj) {
        shareUpdateByType(query.shareUserId)
      }
      // 初始化页面分享卡片
      if (!!wx && SpaceInfoObj) {
        onShareAppMessage();
      }
      getSpaceBulletScreen()
       /*Modal.alert({
        title: '当前设备信息',
        content: `${navigator.userAgent}`,
        okText: '确定',
        onConfirm: () => {
          // onClickBack()
        }
      })*/
    }
  },[isInitialized])

  useEffect(() => {
    let intervalId;
    if (recordType == 1) {
      // 1主播 2嘉宾 3观众
      if (currentUserType == 1) { message.success('您已开始录制') }
      intervalId = setInterval(() => {
        const currentTime = moment()
        const IntervalSeconds = currentTime.diff(moment(recordStartTime, 'YYYY-MM-DD HH:mm:ss'), 'seconds')
        setElapsedTime(IntervalSeconds);
      }, 1000);
    } else {
      clearInterval(intervalId);
      setElapsedTime(0);
    }
    return () => clearInterval(intervalId);
  }, [recordType]);


  // 监听直播间状态变化,当直播间状态改变
  // 从预约中(2)改变为直播中(1)状态时,则自动加入房间并开启直播流
  useEffect(() => {
    // 当前直播间是开始状态则直接加入房间
    // isNeedPwd 需要输入密码 0：不需要  1需要
    // 直播状态下 且 不需要密码 且 未加入房间时
    // 加入房间 开启直播流
    // 状态：1直播中、2预约中、3弹幕轰炸中
    if (
      status == 1
      && isNeedPwd == 0
      && RTC
      && !RTC.isJoined
    ) {
      handleJoin();
      dispatch({
        type:'PlanetChatRoom/setState',
        payload:{ ModalVisibleByApplicationSubmitted:false }
      })
    }

    // 如果当前直播间结束状态
    // 直播中(1)状态改变成弹幕轰炸中状态(3),则关闭直播流 退出直播房间
    if (
      status == 3
      && isNeedPwd == 0
      && RTC
      // && RTC.isJoined
    ) {
      handleLeave(); // 退出房间
      // 清空本地存储的状态数据
      clearLocalStateByStreamById(props?.match?.params?.RoomId);
    }

    if (
      status == 2
      && isNeedPwd == 0
      && !!RTC
      && !!RTC.isJoined
    ) {
      handleLeave(); // 退出房间
      // 清空本地存储的状态数据
      clearLocalStateByStreamById(props?.match?.params?.RoomId);
    }

    // 需要密码
    // 2024-04-10 变更说明 有密码的会议，在主持人/参会者分享带密码的链接后，受邀人打开链接，仍需要输入会议密码。
    /*if(isNeedPwd == 1 && query && query.pwd) {
      // 当前地址栏携带密码则直接校验密码
      // let vodByPassword = query.pwd ? getDAesString(query.pwd,'arrail-dentail&2', 'arrail-dentail&3') : query.pwd;
      checkSpacePassword(query.pwd);
    }*/
  },[status,isNeedPwd])

  // 完成初始化配置后 判定当前状态开启直播流
  // 直播间状态 状态：1直播中、2预约中、3弹幕轰炸中
  // 当直播间状态在直播中时,则直接加入并开启直播流
  useEffect(() => {
    // 当前直播间是开始状态则直接加入房间
    // isNeedPwd 需要输入密码 0：不需要  1需要
    if (
      status == 1
      && isNeedPwd == 0
      && !loadingByCheckDevices
      && RTC
    ) { // 直播中状态
      handleJoin();
    }
  },[loadingByCheckDevices])

  useEffect(() => {
    // 当前直播间是开始状态则直接加入房间
    // isNeedPwd 需要输入密码 0：不需要  1需要
    // 状态：1直播中、2预约中、3弹幕轰炸中
    if (
      status == 1
      && isNeedPwd == 0
      && RTC
      && !RTC.isJoined
      && (currentUserType == 3 && handUpStatusType != 1)
    ) { // 直播中状态
      handleJoin();
    }
  },[RTC])

  // 接收申请连麦转换身份
  useEffect(async () => {
    if(RTC && status == 1) {
      if (handUpStatusType == 1 && currentUserType == 3) {
        // 切换身份 进行连麦
        // await changeRole()  // 切换角色
        await handleJoin();    // 加入房间
        await handlePublish(); // 发布本地流
      } else if (
        handUpStatusType == null
        && currentUserType == 3
        && (RTC && RTC.isPublished || RTC && RTC.isPublishing))
      {
        // ModalVisibleByLeaveMicrophone:false // 取消连麦
        if(!ModalVisibleByLeaveMicrophone) {
          message.info('您已被主持人下麦')
        }
        // await changeRole()    // 切换角色
        await handleUnPublish(); // 关闭本地流
        // await handleLeave();     // 退出直播
        dispatch({
          type: 'PlanetChatRoom/setState',
          payload: { ModalVisibleByLeaveMicrophone:false }
        })
      }else {
        if(!RTC.isPublished && !RTC.isPublishing) {
          // await changeRole()     // 切换角色
        }
      }
    }
    if(handUpStatusType){
      val.current = {
        ...val.current,
        handUpStatusType:handUpStatusType,
        currentUserType: currentUserType,
      };
    }
  },[handUpStatusType])



  // 申请连麦功能被主持人强制关闭-退出连麦-停止发布本地流
  useEffect(async () => {
    /* if (handUpType == 0
      && currentUserType == 3
      && handUpStatusType == 1
      && (RTC && RTC.isPublished || RTC && RTC.isPublishing)
    ) {
      await handleUnPublish(); // 关闭本地流
      await changeRole()     // 切换角色
    } */
  },[handUpType])

  // 密码全部输入完成 进行密码校验-密码校验成功后-进入中房间
  useEffect(() => {
    pwdArrayByFunc(pwdArray)
  },[pwdArray])

  const pwdArrayByFunc = useDebounce((pwdArray)=>{
    const pwdArrayData = pwdArray.filter((item) => {
      return !!item && item != ''
    })
    if (pwdArrayData.length == 4) {
      checkSpacePassword(pwdArray.join(''))
    }
  },1000)


  useEffect(() => {
    if (clickNumByAPPLAUSE >= 20 ) {
      setMsgGroupId(`${moment().format('YYYYMMDDHHmmss')}${Math.random().toString().slice(2,6)}`)
      setClickNumByAPPLAUSE(0);
    }
    if(clickNumByFLOWERS >= 20){
      setMsgGroupId(`${moment().format('YYYYMMDDHHmmss')}${Math.random().toString().slice(2,6)}`)
      setClickNumByFLOWERS(0);
    }
  },[clickNumByAPPLAUSE,clickNumByFLOWERS])


  // isHorizontalLive
  useEffect(()=>{
    if(!!isHorizontalLiveByProps) {
      if(isMobile) {
        setTimeout(()=>{
          if(!!isInitialized) {
            try {
              if (!fullscreen.is() && fullscreen.enabled()) {
                const element = document.documentElement; // 整个文档
                // 判断浏览器是否支持全屏API
                try {
                  if (element.requestFullscreen) {
                    element.requestFullscreen(); // 进入全屏模式
                  } else if (element.mozRequestFullScreen) {
                    element.mozRequestFullScreen(); // Firefox
                  } else if (element.webkitRequestFullscreen) {
                    element.webkitRequestFullscreen(); // Chrome、Safari和Opera
                  } else if (element.msRequestFullscreen) {
                    element.msRequestFullscreen(); // IE和Edge
                  }
                } catch (e) {}
              }
            } catch (e) {}
          }
        },500)
      }

      onOrientationchange();
      // 更新状态，表示页面处于横屏模式
      setIsHorizontalLive(isHorizontalLiveByProps)
      dispatch({
        type: 'PlanetChatRoom/setState',
        payload: { isHorizontalLive:isHorizontalLiveByProps }
      })
    }else {
      const isLandscape = window.matchMedia("(orientation: landscape)").matches;
      if (isLandscape) {
        // 在横屏模式下，通过 dispatch 更新状态
        dispatch({
          type: 'PlanetChatRoom/setState',
          payload: {
            // ModalVisibleByVerticalPageWrong: true
          }
        })
      }else {
        // 在竖屏模式下，通过 dispatch 更新状态
        dispatch({
          type: 'PlanetChatRoom/setState',
          payload: { ModalVisibleByVerticalPageWrong: false }
        })
      }
      if(isMobile) {
        setTimeout(()=>{
          try {
            if (!!fullscreen.is() && fullscreen.enabled()) {
              // 如果全屏可用且当前处于全屏状态，则退出全屏
              fullscreen && fullscreen.exit()
            }
          }catch(e){
            message.warning('退出全屏失败!')
          }
        },500)
      }
    }
    setIsHorizontalLive(isHorizontalLiveByProps)
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: { isHorizontalLive:isHorizontalLiveByProps }
    })
    onSize();
  },[isHorizontalLiveByProps])


  const onSize = debounce(async ()=> {
    // currentWatchMode: null,       // 当前观看模式 1 竖屏非全屏, 2 竖屏全屏, 3 横屏
    // 是否是PC端
    if (!isMobile) {
      // PC 端是固定font-size 无需改变
      const scale = 1.5;
      document.documentElement.style.fontSize = baseSize * Math.min(scale, 2) + 'px';
      return;
    }
    const isHorizontalLiveByPrams = await dispatch({type: 'PlanetChatRoom/getIsHorizontalLive'})
    const isLandscape = window.matchMedia("(orientation: landscape)").matches;
    // 移动先判定当前设备方向
    if (isLandscape) {
      dispatch({ type: 'PlanetChatRoom/setState' ,payload: { currentWatchMode: 3 } })
      const scale = document.documentElement.clientWidth / 812;
      document.documentElement.style.fontSize = baseSize * Math.min(scale, 2) + 'px';
    }else {
      // 当前设备是竖屏方向-判定是否处于全屏模式下
      if(isHorizontalLiveByPrams){
        // 全屏模式 - 竖屏状态
        dispatch({ type: 'PlanetChatRoom/setState' ,payload: { currentWatchMode: 2 } })
        const scale = document.documentElement.clientWidth / 400;
        document.documentElement.style.fontSize = baseSize * Math.min(scale, 2) + 'px';
      }else {
        //
        dispatch({ type: 'PlanetChatRoom/setState' ,payload: { currentWatchMode: 1 } })
        const scale = document.documentElement.clientWidth / 375;
        document.documentElement.style.fontSize = baseSize * Math.min(scale, 2) + 'px';
      }
    }
    setTimeout(()=> {
      const DerailWarp = document.querySelector('#DerailWarp');
      const videoContent = document.querySelector('#videoContent');
      const ViewportHeight = getViewportHeightWithoutSafeArea();
      //  message.info(`ViewportHeight :: ${ViewportHeight} ,window.outerHeight:${window.outerHeight},availHeight:${window.screen.availHeight}`)
      if (!!DerailWarp) {
        DerailWarp.style.height = `${ViewportHeight}px`;
      }
      if (!!videoContent) {
        if (isMobile) {
          videoContent.style.height = !!isHorizontalLiveByPrams ? `${ViewportHeight}px` : '57VW';
        } else {
          videoContent.style.height = '100%'
        }
      }
    },500)
    dispatch({ type: 'PlanetChatRoom/setState',payload: { isMobile: Boolean(userAgent.match(/Android|BlackBerry|iPhone|iPad|iPod|Opera Mini|IEMobile|WPDesktop/i))}})
  },50)


  // 当前是否是横屏展示
  const onOrientationchange = async () =>{
    if (!isMobile) { return }
    const isHorizontalLiveByPrams = await dispatch({
      type: 'PlanetChatRoom/getIsHorizontalLive',
    })
    if(!!isHorizontalLiveByPrams) {
      // 开启全屏模式
      var orientation = window.orientation;
      if (orientation === 90 || orientation === -90) {
        // 横屏
        dispatch({
          type: 'PlanetChatRoom/setState',
          payload: { ModalVisibleByOrientationWrong: false,}
        })
      } else {
        // 竖屏
        /*dispatch({
          type: 'PlanetChatRoom/setState',
          payload: {
           ModalVisibleByOrientationWrong: !!isHorizontalLiveByPrams ? true : false,
          }
        })*/
      }
    }else {
      // 关闭了全屏模式
      var orientation = window.orientation;
      if (orientation === 90 || orientation === -90) {
        // 横屏- 竖版模式在横屏下展示
        dispatch({
          type: 'PlanetChatRoom/setState',
          payload: { isHorizontalLive: true,}
        })
        changeUrlParams({isHorizontalLive:1})
      }else {
        dispatch({
          type: 'PlanetChatRoom/setState',
          payload: {
            ModalVisibleByOrientationWrong: false,
            ModalVisibleByVerticalPageWrong: false,
          }
        })
      }
    }
  }

  // 微信分享
  // 分享病例
  const onShareAppMessage = () => {
    const url = window.location.href
    const shareUrl = getShareUrl(url)
    dispatch({
      type: 'userInfoStore/getJsapiTicket',
      payload: {
        currentUrl: url,
        appId: WxAppIdByPublicAccount,
      },
    }).then((res: any) => {
      if (res && res.code == 200) {
        wx && wx.config({
          debug: false,
          appId: res.content.appId,
          timestamp: res.content.timestamp,
          nonceStr: res.content.nonceStr,
          signature: res.content.signature,
          jsApiList: [
            'updateAppMessageShareData',
            'updateTimelineShareData',
            'onMenuShareTimeline',
            'onMenuShareAppMessage',
            'onMenuShareQQ',
            'onMenuShareWeibo',
            'onMenuShareQZone',
          ],
        })

        wx && wx.ready(() => {
          const shareDate = {
            title: '【FRIDAY医生星球】牙医都来这里学习和交流！',
            desc: nameBySpaceInfo,
            link: shareUrl,
            imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/DigitalHealthShareImg.png',
          };
          wx.updateAppMessageShareData(shareDate);
          wx.updateTimelineShareData(shareDate);
          wx.onMenuShareTimeline(shareDate);
          wx.onMenuShareAppMessage(shareDate);
          wx.onMenuShareQQ(shareDate);
          wx.onMenuShareWeibo(shareDate);
          wx.onMenuShareQZone(shareDate);
        })
      } else {
        // Toast.show('请求微信配置失败～！')
      }
    })
  }

  // 分享操作更新gdp等数据
  const shareUpdateByType = (shareUserId) => {
    const RoomId = props?.match?.params?.RoomId;
    dispatch({
      type: 'userInfoStore/shareUpdateByType',
      payload: {
        id: RoomId,                                        // 被分享ID(王国、空间)
        shareId: shareUserId,                              // 分享人ID
        type: 3,                                           // 类型(2王国，3空间)
        hostId: SpaceInfoObj.hostUserInfo && SpaceInfoObj.hostUserInfo.wxUserId,     // 空间主持人ID
      }
    }).then(res => {

    }).catch(err => {})
  }


  // 密码输入框自动聚焦
  const handleInput = debounce((index, value) => {
    // 使用正则表达式检查输入是否为数字
    if (!/^\d*$/.test(value) || !value) {
      // 非数字则清空输入
      inputRefs.current[index].input.value = null;
      const newArray = pwdArray.map((item, i) => (i === index ? null : item));
      setPwdArray(newArray);
    } else {
      const newValue = value ? value[0] : null; // 只取输入的第一个字符
      if (inputRefs && inputRefs.current && inputRefs.current[index] && inputRefs.current[index].input) {
        inputRefs.current[index].input.value = newValue;
        const newArray = pwdArray.map((item, i) => (i === index ? newValue : item));
        setPwdArray(newArray);

        // 自动焦点切换到下一个 input
        if (newValue && index < inputRefs.current.length - 1) {
          inputRefs.current[index + 1].focus();
        }
      }
    }
  },40);

  // 重置发送消息的groupId
  const debouncedResetMsgGroupId = () => {
    if (debounceTimeout) {
      clearTimeout(debounceTimeout);
      setDebounceTimeout(null)
    }
    const debounceTimeoutBy = setTimeout(() => {
      setMsgGroupId(`${moment().format('YYYYMMDDHHmmss')}${Math.random().toString().slice(2, 6)}`);
      setClickNumByAPPLAUSE(0);
      setClickNumByFLOWERS(0);
    }, 1000); // 防抖时间设置为 1 秒
    setDebounceTimeout(debounceTimeoutBy);
  };

  const handleDelete = (index, event) => {
    if (event.keyCode === 8  && index > 0) {
      // 删除键被按下，并且当前输入框为空，且不是第一个输入框
      event.preventDefault();
      inputRefs.current[index - 1].focus();
      const newArray = pwdArray.map((item, i) => (i === index ? null : item));
      setPwdArray(newArray);
    }
  };


  // checkSpacePassword 校验空间密码-密码校验成功后-进入中房间
  const checkSpacePassword = async (pwd) => {
    await setLoadingCheckSpacePassword(true);
    const data = await dispatch({
      type: 'PlanetChatRoom/checkSpacePassword',
      payload: {
        id: props?.match?.params?.RoomId,
        password: pwd,
      }
    })
    await setLoadingCheckSpacePassword(false);
    const { code,content } = data || {};
    if (code == 200 && content) {
      // 密码校验成功 更新直播间状态
      if(isNotLogin) {
        await dispatch({
          type: 'PlanetChatRoom/setState',
          payload: {isNotLoginCheckPassword: true}
        })
      }
      const resBySpaceInfo = await getSpaceInfo();  // 更新获取空间信息
      const { code,content } = resBySpaceInfo || {}
      if (code == 200 && content && content.status == 1) {
        await handleJoin();    // 加入房间
      }
    }else {
      Toast.show({
        // duration:100000,
        maskClassName:'ToastWarp',
        content:  <div className={styles.ShowContent}>{starSpaceTypeText}密码错误,请重新输入</div>,
        afterClose: () => {},
      })
    }
  }

  // 更新用户信息
  const getMsgCodeUserInfo = async () => {
    const wxuserId = UerInfo && UerInfo.friUserId;
    if (!!wxuserId) {
      const response = await dispatch({
        type: 'PlanetChatRoom/getMsgCodeUserInfo',
        payload: {},
      });
    }
  }

  // 初始化空间接口
  const userActiveJoinSpace = async () => {
    const RoomId = props?.match?.params?.RoomId;
    const response = await dispatch({
      type: 'PlanetChatRoom/userActiveJoinSpace',
      payload: {spaceId: RoomId},
    });
  }

  // 获取空间信息
  const getSpaceInfo = async () => {
    const wxuserId = UerInfo && UerInfo.friUserId;
    const RoomId = props?.match?.params?.RoomId;

    const response = await dispatch({
      type: 'PlanetChatRoom/getSpaceInfo',
      payload: {
        spaceId: RoomId,
        wxUserId: wxuserId,
      },
    });

    if (response && response.code == 401) {

      dispatch({
        type:'PlanetChatRoom/setState',
        payload:{ ModalVisibleByUserTokenInvalid:true }
      })

    }else if (response && response.code == 422){
      dispatch({
        type:'PlanetChatRoom/setState',
        payload:{ ModalVisibleBySpaceRemoved:response.msg ? response.msg : `该${starSpaceTypeText}已下架!`}
      })
      return response
    }else if (!response || response && response.code != 200) {
      if (response && response.status != 401) {
        message.error(`获取${starSpaceTypeText}信息失败`);
      }
      return response
    } else if(response && response.code == 200 && response.content){
      setSpaceInfoObj(response.content)
      /*if(response.content && response.content.isHasWhiteBoardPush == 1){
        await dispatch({
          type:'PlanetChatRoom/setState',
          payload:{ isOpenTEduBoard:true }
        })
      }*/
      val.current =  {
        ...response.content,
        currentUserType: currentUserType,
      };
      return response
    }
    return response;
  }

  // 获取嘉宾列表信息
  const getGuestList = async () => {
    const wxuserId = UerInfo && UerInfo.friUserId
    const RoomId = props?.match?.params?.RoomId;
    const response = await dispatch({
      type: 'PlanetChatRoom/getGuestListInfo',
      payload: {
        spaceId: RoomId,
        wxUserId: wxuserId,
      },
    });
    if (!response || response && response.code != 200) {
      if (response && response.status != 401) {
        message.error('获取嘉宾列表信息失败');
      }
      return
    }
  }


  // 获取申请进入会议成员列表
  const getApplyAdmissionList = async () => {
    const RoomId = props?.match?.params?.RoomId;
    const response = await dispatch({
      type: 'PlanetChatRoom/getApplyAdmissionList',
      payload: {
        spaceId: RoomId,
      },
    });
    if (!response || response && response.code != 200) {
      if (response && response.status != 401) {
        message.error('获取成员列表信息失败');
      }
      return
    }
  }

  // 申请入会拒绝或准入
  const updateStarSpaceApplyAdmission = async () => {
    // let RoomId = props?.match?.params?.RoomId;
    const RoomId = props?.match?.params?.RoomId;
    const response = await dispatch({
      type: 'PlanetChatRoom/updateStarSpaceApplyAdmission',
      payload: {
        applyAdmissionId: 0,
        refuseAdmittance: 1
      },
    });
    if (response.code == 200) {
      message.success('申请入会成功');
    }
  }

  // 获取空间历史消息
  const getSpaceGroupMsg = async (msgSeq) => {
    const dataByGetSpaceGroupMsg = await dispatch({
      type:'PlanetChatRoom/getSpaceGroupMsg',
      payload: {
        spaceId: props?.match?.params?.RoomId,
        msgSeq:msgSeq,
        pageSize:10,
      },
    })
  }

  // 获取空间弹幕消息
  const getSpaceBulletScreen = async () => {
    const {
      recordEndTime,
      recordStartTime,
      recordDuration
    } = videoList && videoList[0] || {}
    const dataByGetSpaceGroupMsg = await dispatch({
      type:'PlanetChatRoom/getSpaceBulletScreen',
      payload: {
        spaceId: props?.match?.params?.RoomId,
        eventTime:recordStartTime ? recordStartTime : null
      },
    })
  }

  // 点击 密码弹窗中的 无密码申请入会
  const addStarSpaceApplyAdmission = async () => {
    if (!!isNotLogin) { // 未登录
      await dispatch({
        type:'PlanetChatRoom/setState',
        payload:{
          ModalVisibleByUserTokenInvalid:true,
        }
      })
      return;
    }
    const dataByAddStarSpaceApplyAdmission = await dispatch({
      type:'PlanetChatRoom/addStarSpaceApplyAdmission',
      payload: {
        spaceId: props?.match?.params?.RoomId,
      },
    })
    const { code,content,msg } = dataByAddStarSpaceApplyAdmission || {}
    if (code == 200) {
      sendMessageByIm({
        dataType: NO_PW_APPLY,
        description: JSON.stringify({imUserId:'ALL'})
      });
      dispatch({
        type:'PlanetChatRoom/setState',
        payload:{ ModalVisibleByApplicationSubmitted:true }
      })
    }else {
      message.error(msg ? msg : '无密码申请入会失败!');
    }
  }

  // 初始化TRTC
  // TODO 补充观众不检查设备, 预约状态下的空间不检查设备
  const initializationByTRTC = async () => {

    await setLoadingByCheckDevices(true);
    await setUserID(imUserId);
    await setRoomID(roomId);

    const checkResult = await TRTC.checkSystemRequirements();
    const { result, detail } = checkResult;
    // 无法规避问题
    // （2022-01-19）iOS 15 以下版本，canvas.captureStream 采集出的视频流，无法使用 video 标签播放直播流
    // https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-02-info-webrtc-issues.html#h2-4
    const iOSVersionRegex = /iPhone OS (\d+)/;
    const versionMatch = window.navigator.userAgent.match(iOSVersionRegex);
    // let iosVersion = versionMatch ? parseInt(versionMatch[1], 10) : null;
    if (
      !result
      || !detail.isBrowserSupported
      || !detail.isWebRTCSupported
      // || iosVersion && iosVersion < 15
    ) {
      setLoadingByCheckDevices(false);
      return;
    }

    // 当前为直播状态并且,当前用户不是观众,或者当前用户是观众并且是预约状态
    if (
      status != 3
    ) {
      if((currentUserType != 3 ) || (currentUserType == 3 && handUpStatusType == 1)) {
        let CameraIDByCheckCamera, MicrophoneIDByCheckMicrophone = null;
        let checkCamera, checkMicrophone = false;
        // 申请权限添加判定
        // 网页非APP环境内 通过checkDevices即可弹出网页授权弹窗
        // Android APP内环境需要通过requestCameraInApp和requestMicrophoneInApp来申请APP内的权限
        // ios WebView自发会弹窗申请授权弹窗,与safari浏览器一致
        const env = getOperatingEnv()
        const userAgent = navigator.userAgent
        const isAndroid = userAgent.indexOf('Android') > -1 || userAgent.indexOf('Adr') > -1;
        if ((env == 5 || env == 6) && isAndroid) {
          // 检查设备状态
          checkMicrophone = await requestMicrophoneInApp();
          checkCamera = await requestCameraInApp();
        } else {
          checkMicrophone = await checkDevices('microphone')
          checkCamera = await checkDevices('camera')
        }

        if (checkMicrophone) {
          // 获取麦克风设备
          const MicrophonesDeviceList = await TRTC.getMicrophones();
          MicrophoneIDByCheckMicrophone = await Array.isArray(MicrophonesDeviceList) && MicrophonesDeviceList.length > 0 && MicrophonesDeviceList[0].deviceId;
          await setMicrophoneID(MicrophoneIDByCheckMicrophone)
        }
        if (checkCamera) {
          // 获取摄像头设备
          const CamerasDeviceList = await TRTC.getCameras();
          CameraIDByCheckCamera = await Array.isArray(CamerasDeviceList) && CamerasDeviceList.length > 0 && CamerasDeviceList[0].deviceId;
          await setCameraID(CameraIDByCheckCamera);
        }
        if (checkMicrophone) {
          await showDeviceDetector(CameraIDByCheckCamera, MicrophoneIDByCheckMicrophone);
        }
      }

      await setLoadingByCheckDevices(false);
      if (
        status == 1
        && isNeedPwd == 0
        && RTC
        && !RTC.isJoined
      ) {
        await handleJoin();
        await setLoadingByCheckDevices(false);
      }
    }else {
      await setLoadingByCheckDevices(false);
    }
  }

  // 检查设备状态 获取麦克风和摄像头权限
  const  showDeviceDetector = async (CameraIDByCheckCamera,MicrophoneIDByCheckMicrophone) => {
    if (
      !(CameraIDByCheckCamera && MicrophoneIDByCheckMicrophone)
      && (currentUserType == 1 || currentUserType == 2)
      && !!props.PlanetChatRoom.isLive
    ) {
      await  message.warning('获取设备麦克风和摄像头权限失败,开启检查设备状态');
      await  DeviceDetector && DeviceDetector.show();
    }
  }

  // 修改地址栏地址参数
  const changeUrlParams = (params) => {
    const {pathname, query} = history.location;
    const newQuery = {
      ...query,
      ...params,
    }
    history.replace(`${pathname}?${stringify(newQuery)}`);
  }

  // 检查设备环境 检查麦克风 和 摄像头权限
  const checkDevices = async (deviceType) => {
    let checkType = true;  // 默认检查所有设备的状态
    const checkResult = await TRTC.checkSystemRequirements();
    const { result, detail } = checkResult;

    if (!result) {
      checkType = false
      message.error('您的浏览器环境不支持TRTC，请使用Chrome浏览器体验');
      // return checkType;
    }

    try {
      if(navigator.mediaDevices.getUserMedia) {
        const mediaStream = await navigator.mediaDevices.getUserMedia({
          audio: deviceType === 'microphone',
          video: deviceType === 'camera'
        });
        mediaStream.getTracks()[0].stop();
      }else {
        setLoadingByCheckDevices(false);
        checkType = false
        return checkType;
      }
    } catch (error) {
      // setLoadingByCheckDevices(false);
      // message.error(`检查${deviceType === 'microphone' ? '麦克风' : '摄像头'}设备状态失败！${error.name}`)
      if (error.name === 'NotAllowedError') {
        message.error(`请允许网页访问${deviceType === 'microphone' ? '麦克风' : '摄像头'}的权限！`);
        /*Modal.alert({
          title: 'NotAllowedError',
          content: `请允许网页访问${deviceType === 'microphone' ? '麦克风' : '摄像头'}的权限！`,
          okText: '确定',
          onConfirm: () => {}
        });*/
      } else if (error.name === 'NotFoundError') {
        message.error(`请检查${deviceType === 'microphone' ? '麦克风' : '摄像头'}设备连接是否正常！`);
        /* Modal.alert({
           title: 'NotFoundError',
           content: `请检查${deviceType === 'microphone' ? '麦克风' : '摄像头'}设备连接是否正常！`,
           okText: '确定',
           onConfirm: () => {}
         });*/
      } else if (error.name === 'NotReadableError') {
        message.error(`请检查${deviceType === 'microphone' ? '麦克风' : '摄像头'}设备是否被其它应用占用或未授权应用权限！`);
        // alert(`请检查${deviceType ==='microphone'? '麦克风' : '摄像头'}设备是否被其它应用占用或未授权应用`);
        /*Modal.alert({
          title: 'NotReadableError',
          content: `请检查${deviceType ==='microphone'? '麦克风' : '摄像头'}设备是否被其它应用占用或未授权应用`,
          okText: '确定',
          onConfirm: () => {}
        });*/
        // alert(`请检查${deviceType === 'microphone' ? '麦克风' : '摄像头'}设备是否被其它应用占用或未授权应用权限！`);
      }
      checkType = false
      return checkType;
    }
    return checkType;
  }

    // 初始化Im及时通信组件 实例名称定义为"tim"
  const initializationByIm = async () => {
    await setLoadingByPage(true); // 设置页面加载状态

    const options = {
      // SDKAppID: SDKAPPID // 接入时需要将0替换为您的即时通信 IM 应用的 SDKAppID
      SDKAppID: imAppId
    };

    // 创建 SDK 实例，`TIM.create()`方法对于同一个 `SDKAppID` 只会返回同一份实例
    const tim = TIM.create(options); // SDK 实例通常用 tim 表示
    await setTimObj(tim)
    // 设置 SDK 日志输出级别，详细分级请参见 setLogLevel https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#setLogLevel 接口的说明</a>
    tim.setLogLevel(0); // 普通级别，日志量较多，接入时建议使用
    // tim.setLogLevel(1); // release 级别，SDK 输出关键信息，生产环境时建议使用
    // 注册腾讯云即时通信 IM 上传插件
    tim.registerPlugin({'tim-upload-plugin': TIMUploadPlugin});
    // 注册腾讯云即时通信 IM 本地审核插件
    tim.registerPlugin({'tim-profanity-filter-plugin': TIMProfanityFilterPlugin});
    // 监听事件，例如：SdkReady
    tim.on(TIM.EVENT.SDK_READY, () => {
      // 当SDK进入ready状态时触发此事件。
      // 当在监听过程中检测到此事件时，
      // 访问端可以调用SDK API(比如消息发送API)来使用SDK的各种特性
      // 当IM登录成功后，保存tim对象
    });
    // 监听事件, 关于收到的消息的事件
    tim.off(TIM.EVENT.MESSAGE_RECEIVED,onMessageReceived)
    tim.on(TIM.EVENT.MESSAGE_RECEIVED, onMessageReceived);

    // 监听被踢出事件
    tim.on(TIM.EVENT.KICKED_OUT, onKickedOut);

    // 网络状态发生改变
    tim.on(TIM.EVENT.NET_STATE_CHANGE, (event)=>{
      /*
        *  TIM.TYPES.NET_STATE_CONNECTED    - 已接入网络
        // TIM.TYPES.NET_STATE_CONNECTING   - 连接中。很可能遇到网络抖动，SDK 在重试。接入侧可根据此状态提示“当前网络不稳定”或“连接中”
        // TIM.TYPES.NET_STATE_DISCONNECTED - 未接入网络。接入侧可根据此状态提示“当前网络不可用”。SDK 仍会继续重试，若用户网络恢复，SDK 会自动同步消息
      */
      if(event.data.state == TIM.TYPES.NET_STATE_CONNECTING){
        const showMessage = throttle(() => {
          message.warning('当前网络不稳定');
        }, 500)
        showMessage();

      }
      if(event.data.state == TIM.TYPES.NET_STATE_DISCONNECTED){
        const showMessage = throttle(() => {
          message.error('当前网络不可用!');
        }, 500)
        showMessage();
      }
    });

    // SDK 进入 not ready 状态时触发，此时接入侧将无法使用
    // SDK 发送消息等功能。如果想恢复使用，接入侧需调用 login 接口，驱动 SDK 进入 ready 状态
    tim.on(TIM.EVENT.SDK_NOT_READY, ()=>{
      setLoadingByPage(false); // 设置页面加载状态
      /*let promise = tim.login({
        userID: imUserId,
        userSig: userSig,
      });
      promise.then(function (imResponse) {
        if (imResponse.data.repeatLogin === true) {
          setLoadingByPage(false); // 设置页面加载状态
        } // 重复登录
        joinGroupByIm(tim); // 加入当前直播群组
      }).catch(function (imError) {
        setLoadingByPage(false); // 设置页面加载状态
        message.error('TIM登录失败!!');
        console.warn('login error:', imError); // Error information
      });*/
    });

    if (imUserId) {
      if (isNotLogin) { // 未登录
        setLoadingByPage(false);  // 设置页面加载状态
        joinGroupByIm(tim);       // 加入当前直播群组
      }else {
        const promise = tim.login({
          userID: imUserId,
          userSig: userSig,
        });
        setLoadingByPage(false);    // 设置页面加载状态
        promise.then(function (imResponse) {
          if (imResponse.data.repeatLogin === true) {} // 重复登录
          setLoadingByPage(false);  // 设置页面加载状态
          joinGroupByIm(tim);       // 加入当前直播群组
        }).catch(function (imError) {
          setLoadingByPage(false);  // 设置页面加载状态
          message.error('TIM登录失败!!');
          console.warn('login error:', imError); // Error information
        });
      }
    }
  }

  // 加入直播群
  const joinGroupByIm = async (tim) => {
    // let Groupid = getUrlParam('Groupid')
    const promise = tim.joinGroup({groupID: imGroupId});
    promise.then(function(imResponse) {
      switch (imResponse.data.status) {
        case TIM.TYPES.JOIN_STATUS_WAIT_APPROVAL:                    // 等待管理员同意
          break;
        case TIM.TYPES.JOIN_STATUS_SUCCESS:                          // 加群成功
          console.log('加群成功 :: ',imResponse.data.group); // 加入的群组资料
          break;
        case TIM.TYPES.JOIN_STATUS_ALREADY_IN_GROUP:                // 已经在群中
          console.log('已经在群 :: ',imResponse.data.group); // 加入的群组资料
          break;
        default:
          break;
      }
    }).catch(function(imError){
      if(imError.code == 10013){
        // 用户已经是当前群成员
      }else {
        console.warn('joinGroup error:', imError); // 申请加群失败的相关信息
        message.error(imError.message);
      }
      /*if (imError.code == 10037) {
        message.error(imError.message);
      }*/
    });
  }

  // 监听被踢出事件 - 例如：多端登录被踢
  const onKickedOut = (event) => {
    /*
     * 默认情况下，不支持多实例登录，即如果此帐号已在其他页面登录，
     * 若继续在当前页面登录成功，有可能会将其他页面踢下线。
     * 用户被踢下线时会触发事件TIM.EVENT.KICKED_OUT，
     * 用户可在监听到事件后做相应处理。示例如下：
     */
    // TIM.TYPES.KICKED_OUT_MULT_ACCOUNT(Web 端，同一帐号，多页面登录被踢)
    // TIM.TYPES.KICKED_OUT_MULT_DEVICE(同一帐号，多端登录被踢)
    // TIM.TYPES.KICKED_OUT_USERSIG_EXPIRED(签名过期)
    // TIM.TYPES.KICKED_OUT_REST_API(REST API kick 接口踢出。v2.20.0起支持)
    dispatch({
      type:'PlanetChatRoom/setState',
      payload:{ ModalVisibleByKickedOut:true, }
    })
  }

  // 接收到直播群消息
  const onMessageReceived = (value) => {
    // 接收新推送的一对一消息、群组消息、群组提示或群组系统通知。你可以遍历事件。
    // 获取消息列表并将其呈现给UI。
    // event.name - time . event. message_received
    // 事件。data -存储Message对象的数组- [Message]
    const {data} = value || {}

    // 接收自定义[弹幕消息]并转换成弹幕
    if (Array.isArray(data)) {
      data.map((item) => {
        const {payload, to} = item || {}
        // 如果消息来源不是当前直播间直接忽略
        if(to != imGroupId) { return }

        const {
          data: dataByPayload,
          description: descriptionByPayload,
          extension: extensionByPagload,
        } = payload || {}

        // 接收关闭或开启指定成员的摄像头
        if(dataByPayload == CAMERA_TOGGLE) {
          if(RTC){
            const ObjDescriptionByPayload = descriptionByPayload && JSON.parse(descriptionByPayload)
            const {imUserId: imUserIdByMsg} = ObjDescriptionByPayload || {}
            if(imUserIdByMsg == imUserId) {
              // onChange({ 'video', stream: localStreamConfig.stream });
              // handleLocalChange({name:'video',stream:localStreamConfig.stream})
            }
          }
        }

        // 白板消息的接收 告知用户谁启用了分享课件
        if(dataByPayload == WHITEBOARD_MSG) {
          const objByExtensionByPagload = extensionByPagload && JSON.parse(extensionByPagload)
          message.info(`${objByExtensionByPagload.name}启用分享课件`);
          // 当前嘉宾占用白板 主持人分享白板时退出白板
          if(currentUserType == 2 && objByExtensionByPagload.currentUserType == 1){
           //  dispatch({type: 'PlanetChatRoom/closeTEduBoard', payload: {isOpenTEduBoard: !isOpenTEduBoard}})
          }
        }
        // 自定义消息中的用户信息
        // 接收自定义消息
        // 接收自定义[弹幕消息]并转换成弹幕
        // 平齐开启弹幕功能才发送弹幕
        if (dataByPayload == BULLET_SCREEN && isOpenDanmu) {
          const objByExtensionByPagload = extensionByPagload && JSON.parse(extensionByPagload)
          RefByHorizontalLiveRoom.current && RefByHorizontalLiveRoom.current.sendDanmu({
            text: descriptionByPayload,
            userInfoByDanmu:objByExtensionByPagload,
          });
          RefByVerticalLiveRoom.current && RefByVerticalLiveRoom.current.sendDanmu({
            text: descriptionByPayload,
            userInfoByDanmu:objByExtensionByPagload,
          });
        }

        // 当接受消息类型是更新直播间状态 则进行更新 直播间权益更新或者 指定更新
        if (dataByPayload == UPDATA_STATE) {
          // 更新指定用户的直播间状态
          const ObjDescriptionByPayload = descriptionByPayload && JSON.parse(descriptionByPayload)
          const {
            imUserId: imUserIdByMsg
          } = ObjDescriptionByPayload || {}
          if(imUserIdByMsg == imUserId) {
            getSpaceInfo()
          }else if(imUserIdByMsg == 'ALL'){
            // 分享课件中暂停刷新
            getSpaceInfo();
            getHandUpList(); // 获取连麦列表
            getGuestList();  // 获取嘉宾列表
            getApplyAdmissionList(); // 获取申请入会成员列表
          }
        }

        // 接受当前房间被解散的消息
        if (
          dataByPayload == ROOM_DISBAND
          && currentUserType != 1
        ) {
          dispatch({
            type:'PlanetChatRoom/setState',
            payload:{
              ModalVisibleBySpaceRemoved:`该${starSpaceTypeText}已结束直播`
            },
          })
        }

        // 当有申请连麦或者 申请取消连麦消息时
        // 并且当前是主持人端
        if (
          (dataByPayload == HAND_UP
            || dataByPayload == HAND_DOWN)
          && currentUserType == 1
        ) {
          // 更新连麦列表
          setTimeout(()=>{
            getHandUpList();
          },1000);
        }

        // 当前申请无密码入会列表人员
        if(dataByPayload == NO_PW_APPLY) {
          getGuestList();  // 获取嘉宾列表
          getApplyAdmissionList(); // 获取申请入会成员列表
        }

        // 强制下架直播间
        if (dataByPayload == FORCED_END) {
          if(RTC){
            handleLeave()
          }
          // getSpaceInfo();
          const  { consultationId } = val.current || {}
          dispatch({
            type:'PlanetChatRoom/setState',
            payload:{ ModalVisibleBySpaceViolation:consultationId ? '视频指导结束' : `该${starSpaceTypeText}因违反平台规范已被关闭`, }
          })
        }

      })

      const msgList = []
      data.map((item)=>{
        const { payload,to } = item || {}
        const {
          data: dataByPayload,
        } = payload || {}

        if(to != imGroupId) { return }

        if (dataByPayload == SEND_APPLAUSE) {  // 鼓掌
          const LocalData = imResponseFormatLocalData(item)
          LocalData && msgList.push(LocalData)
          dispatch({ type:'PlanetChatRoom/addSendApplauseCount' })
        }
        if (dataByPayload == SEND_FLOWERS) {   // 送花
          const LocalData = imResponseFormatLocalData(item)
          LocalData && msgList.push(LocalData)
          dispatch({ type:'PlanetChatRoom/addSendFlowersCount' })
        }
        if (dataByPayload == SIGN_IN) {        // 签到 打卡
          const LocalData = imResponseFormatLocalData(item)
          LocalData && msgList.push(LocalData)
        }
        if(dataByPayload == BULLET_SCREEN){
          const LocalData = imResponseFormatLocalData(item)
          LocalData && msgList.push(LocalData)
        }
      })

      if (msgList.length > 0) {
        dispatch({
          type:'PlanetChatRoom/updateMsgListBySENDAPPLAUSE',
          payload: { msgList:msgList }
        })
      }
    }
  }

  // 发送im实时消息
  const sendMessageByIm=({dataType, description, relativeTime}: { dataType: any, description: any, relativeTime: any })=>{
    if (!timObj) { message.error('由于网络异常消息发送失败，请刷新后重试'); return }

    let msgGroupIdBySendMess = null;
    let msgGroupCount = 0;
    if(dataType == SEND_APPLAUSE){
      msgGroupIdBySendMess = `APPLAUSE_${msgGroupId}`;
      msgGroupCount = clickNumByAPPLAUSE + 1;
      setClickNumByAPPLAUSE(clickNumByAPPLAUSE + 1)
    }else if(dataType == SEND_FLOWERS){
      msgGroupIdBySendMess = `FLOWERS_${msgGroupId}`;
      msgGroupCount = clickNumByFLOWERS + 1;
      setClickNumByFLOWERS(clickNumByFLOWERS + 1)
    }

    const messageByTim = timObj.createCustomMessage({
      to: imGroupId,
      // 会话类型，取值TIM.TYPES.CONV_C2C(端到端会话) 或 TIM.TYPES.CONV_GROUP(群组会话)
      conversationType: TIM.TYPES.CONV_GROUP,
      // 消息优先级，用于群聊（v2.4.2起支持）。如果某个群的消息超过了频率限制，后台会优先下发高优先级的消息，详细请参考：https://cloud.tencent.com/document/product/269/3663#.E6.B6.88.E6.81.AF.E4.BC.98.E5.85.88.E7.BA.A7.E4.B8.8E.E9.A2.91.E7.8E.87.E6.8E.A7.E5.88.B6)
      // 支持的枚举值：TIM.TYPES.MSG_PRIORITY_HIGH, TIM.TYPES.MSG_PRIORITY_NORMAL（默认）, TIM.TYPES.MSG_PRIORITY_LOW, TIM.TYPES.MSG_PRIORITY_LOWEST
      // priority: TIM.TYPES.MSG_PRIORITY_HIGH,
      priority: TIM.TYPES.MSG_PRIORITY_NORMAL,
      payload: {
        data: dataType,             // 用于标识该消息类型消息
        description: description,   // 获取内容
        extension: JSON.stringify({
          ...userInfo,
          headUrlShow: imagePhotoPathShow ? imagePhotoPathShow : userInfo.headUrlShow,
          currentUserType:currentUserType,
          imagePhotoPathShow:imagePhotoPathShow,
          msgGroupId:msgGroupIdBySendMess,
          msgGroupCount:msgGroupCount,
          relativeTime:relativeTime,
        }) // 扩展信息
      }
      // 消息自定义数据（云端保存，会发送到对端，程序卸载重装后还能拉取到，v2.10.2起支持）
      // cloudCustomData: 'your cloud custom data'
    });

    const promise = timObj.sendMessage(messageByTim);
    promise.then(function(imResponse) {
      // 发送成功
      const msgList = []
      const {data: {message}} = imResponse || {};
      const {payload} = message || {}
      const {
        data: dataByPayload,
        description: descriptionByPayload,
        extension: extensionByPagload,
      } = payload || {}
      const objExtensionByPagload = extensionByPagload && JSON.parse(extensionByPagload)

      const msgLocalData = imResponseFormatLocalData(message);
      const {msgSeq} = msgLocalData || {}
      dispatch({
        type:'PlanetChatRoom/setState',
        payload: { newMySnedSeq:msgSeq }
      })
      // 鼓掌
      if (dataByPayload == SEND_APPLAUSE) {
        msgLocalData && msgList.push(msgLocalData)
        dispatch({ type:'PlanetChatRoom/addSendApplauseCount' }) // 鼓掌 +1
      }
      // 送花
      if (dataByPayload == SEND_FLOWERS) {
        msgLocalData && msgList.push(msgLocalData)
        dispatch({ type:'PlanetChatRoom/addSendFlowersCount' }) // 送花 +1
      }
      // 签到 打卡
      if (dataByPayload == SIGN_IN) { msgLocalData && msgList.push(msgLocalData)}

      // 发送弹幕消息
      // isOpenDanmu 并且开启弹幕功能才发送弹幕
      if(dataByPayload == BULLET_SCREEN && isOpenDanmu){
        msgLocalData && msgList.push(msgLocalData)
        const objByExtensionByPagload = extensionByPagload && JSON.parse(extensionByPagload)
        RefByHorizontalLiveRoom.current && RefByHorizontalLiveRoom.current.sendDanmu({
          text: descriptionByPayload,
          userInfoByDanmu:objByExtensionByPagload,
        });
        RefByVerticalLiveRoom.current && RefByVerticalLiveRoom.current.sendDanmu({
          text: descriptionByPayload,
          userInfoByDanmu:objByExtensionByPagload,
        });
      }

      if (msgList.length > 0) {
        dispatch({
          type:'PlanetChatRoom/updateMsgListBySENDAPPLAUSE',
          payload: { msgList:msgList }
        })
      }

    }).catch(function(imError) {
      // 发送失败
      // console.warn('sendMessage error:', imError);
      message.error(imError.message);
    });

    debouncedResetMsgGroupId()
  }

  // ------------------[TRTC方法]------------------

  // 增加流
  const addStream = (stream) => {
    const streamType = stream.getType();
    const userID = stream.getUserId();
    switch (streamType) {
      case 'local':
        setLocalStreamConfig({
          stream,
          streamType,
          userID,
          hasAudio: audio,
          hasVideo: video,
          mutedAudio: false,
          mutedVideo: false,
          shareDesk: false,
          audioVolume: 0,
          userInfo:JSON.stringify(userInfo)
        });
        break;
      default: {
        setRemoteStreamConfigList((preList) => {
          const newRemoteStreamConfigList = preList.length > 0
            ? preList.filter(streamConfig => !(streamConfig.userID === userID
              && streamConfig.streamType === streamType))
            : [];
          newRemoteStreamConfigList
            .push({
              stream,
              streamType,
              userID,
              hasAudio: stream.hasAudio(),
              hasVideo: stream.hasVideo(),
              mutedAudio: false,
              mutedVideo: false,
              subscribedAudio: true,
              subscribedVideo: true,
              resumeFlag: false,
              audioVolume: 0,
            });
          return newRemoteStreamConfigList;
        });
        break;
      }
    }
  };

  // 修改状态
  const setState = (type, value) => {
    switch (type) {
      case 'join':
        setIsJoined(value);
        break;
      case 'publish':
        setIsPublished(value);
        break;
      default:
        break;
    }
    if(type == 'publish' && value) {
      initSetLocalStreamConfig()
    }
    if(type == 'join' && value) {
      val.current =  {
        ...val.current,
        currentUserType: currentUserType,
      };
    }
  };

  // 更新流数据
  const updateStream = (stream) => {
    if (stream.getType() === 'local') {
      setLocalStreamConfig({
        ...localStreamConfig,
        stream,
        hasAudio: stream.hasAudio(),
        hasVideo: stream.hasVideo(),
      });
    } else {
      setRemoteStreamConfigList(preList => preList.map(config => (
        config.stream === stream ? {
          ...config,
          stream,
          hasAudio: stream.hasAudio(),
          hasVideo: stream.hasVideo(),
        } : config
      )));
    }
  };


  // 更新对本地流和远端流的操作状态
  const updateStreamConfig = (userID, type, value) => {
    // 更新本地流配置
    if (localStreamConfig && localStreamConfig.userID === userID) {
      const config = {};
      switch (type) {
        case 'audio-volume':
          if (localStreamConfig.audioVolume === value) {
            break;
          }
          config.audioVolume = value;
          break;
        case 'share-desk':
          config.shareDesk = value;
          break;
        case 'uplink-network-quality':
          // PromptDeviceNetworkState(value);
          config.uplinkNetworkQuality = value > 0 ? 6 - value : value;
          break;
        case 'downlink-network-quality':
          config.downlinkNetworkQuality = value > 0 ? 6 - value : value;
          break;
        default:
          break;
      }
      setLocalStreamConfig(prevConfig => ({
        ...prevConfig,
        ...config,
      }));
      return;
    }
    // 更新远端流配置
    const config = {};
    switch (type) {
      case 'mute-audio':
        config.mutedAudio = true;
        break;
      case 'unmute-audio':
        config.mutedAudio = false;
        break;
      case 'mute-video':
        config.mutedVideo = true;
        break;
      case 'unmute-video':
        config.mutedVideo = false;
        break;
      case 'resume-stream':
        config.resumeFlag = true;
        break;
      case 'audio-volume':
        if (config.audioVolume === value) {
          break;
        }
        config.audioVolume = value;
        break;
      default:
        break;
    }

    setRemoteStreamConfigList(preList => preList.map(item => (
      item.userID === userID ? { ...item, ...config } : item
    )));
  };



  // 当前设备网络状态提示 五分钟内只提示一次
  const PromptDeviceNetworkState = throttle((data) => {
    if (data < 3) {
      message.success('当前网络状态良好')
    } else {
      message.warning('当前网络状态较差')
    }
  }, 300000, {'trailing': false})

  // 处理远端流 streamBar 的响应逻辑
  const handleRemoteChange = async (data) => {
    const remoteStream = data.stream;
    const config = remoteStreamConfigList.find(config => config.stream === remoteStream);
    switch (data.name) {
      case 'subscribedVideo':
        await RTC.handleSubscribe(remoteStream, {
          video: !config.subscribedVideo,
          audio: config.subscribedAudio,
        });

        setRemoteStreamConfigList(preList => preList.map(config => (
          config.stream === remoteStream ? ({
            ...config,
            subscribedVideo: !config.subscribedVideo,
          }) : config
        )));
        break;
      case 'subscribedAudio':
        await RTC.handleSubscribe(remoteStream, {
          video: config.subscribedVideo,
          audio: !config.subscribedAudio,
        });
        setRemoteStreamConfigList(preList => preList.map(config => (
          config.stream === remoteStream ? ({
            ...config,
            subscribedAudio: !config.subscribedAudio,
          }) : config
        )));
        break;
      case 'resumeFlag':
        await RTC.resumeStream(config.stream);
        setRemoteStreamConfigList(preList => preList.map(config => (
          config.stream === remoteStream ? ({
            ...config,
            resumeFlag: !config.resumeFlag,
          }) : config
        )));
      default:
        break;
    }
  };

  // 加入直播间并发布直播流
  const handleJoin = async () => {
    await RTC?.handleJoin().then(async (value) => {

    })

    if (
      currentUserType == 1 // 主持人
      || currentUserType == 2 // 嘉宾
      || (currentUserType == 3 && handUpStatusType == 1) // 正在连麦状态的观众
      // handUpStatusType 申请连麦状态类型：0申请连麦 1接受连麦，默认null
      // handUpType 主持人连麦开启状态  1开启中
    ) {
      // 当前是主播或者嘉宾没有开启麦克风,则弹出弹窗限制推送直播流
      if (microphoneID) {
        await setLoadingByCheckDevices(false);
        await RTC?.handlePublish();
      }else {
        dispatch({
          type:'PlanetChatRoom/setState',
          payload: {
            ModalVisibleByNoMicrophone:true,
          }
        })
      }
    }
  };


  // 离开直播间并取消发布直播流
  const handleLeave = async () => {
    shareRTC && shareRTC.isJoined && shareRTC.handleLeave();
    RTC && RTC.isJoined && await RTC.handleLeave();
  }

  const handlePublish = async () => {
    if (userRole === audience) {
      message.error('please change to Anchor', 2000)
      return;
    }
    await RTC.handlePublish();
  };

  const handleUnPublish = async () => {
    await RTC && RTC.handleUnPublish();
  };

  // 初始化调整本地流状态
  const initSetLocalStreamConfig = () => {
    const LocalStateByStreamConfig = getLocalStateByStream(props?.match?.params?.RoomId)

    if (localStreamConfig && LocalStateByStreamConfig) {
      const {
        mutedAudio,
        mutedVideo,
      } = LocalStateByStreamConfig
      // 开启静音
      if (mutedAudio) {
        RTC?.muteAudio();
      }
      // 关闭摄像头
      if (mutedVideo) {
        RTC?.muteVideo();
      }
      if(mutedAudio || mutedVideo) {
        setLocalStreamConfig({
            ...localStreamConfig,
            mutedAudio: mutedAudio,
            mutedVideo: mutedVideo,
          }
        )
      }
    }
  }


  // 处理本地流 streamBar 的响应逻辑
  const handleLocalChange = async (data) => {
    switch (data.name) {
      //  本地流开启视频/关闭视频按钮
      case 'video':
        if (!localStreamConfig.mutedVideo) {
          RTC.muteVideo();
          saveLocalStateByStream({ id: props?.match?.params?.RoomId, mutedVideo: true })
        } else {
          RTC.unmuteVideo();
          saveLocalStateByStream({ id: props?.match?.params?.RoomId, mutedVideo: false })
        }
        setLocalStreamConfig({
          ...localStreamConfig,
          mutedVideo: !localStreamConfig.mutedVideo,
        });
        break;
      // 本地流开启音频/关闭音频按钮
      case 'audio':
        if (!localStreamConfig.mutedAudio) {
          RTC.muteAudio();
          saveLocalStateByStream({ id: props?.match?.params?.RoomId, mutedAudio: true })
        } else {
          RTC.unmuteAudio();
          saveLocalStateByStream({ id: props?.match?.params?.RoomId, mutedAudio: false })
        }
        setLocalStreamConfig({
          ...localStreamConfig,
          mutedAudio: !localStreamConfig.mutedAudio,
        });
        break;
      // 屏幕分享按钮
      case 'shareDesk':
        if (!localStreamConfig.shareDesk) {
          const wxuserId = UerInfo && UerInfo.friUserId;
          const response = await dispatch({
            type:'PlanetChatRoom/getScreenShareUser',
            payload:{
              spaceId: props?.match?.params?.RoomId,
              wxUserId: wxuserId,
            }
          })
          if(response && response.code === 200){
            try {
              await shareRTC?.handleJoin();
              setLocalStreamConfig({
                ...localStreamConfig,
                shareDesk: !localStreamConfig.shareDesk,
              });
            } catch (error) {
              await dispatch({
                type:'PlanetChatRoom/setState',
                payload:{ ModalVisibleByShareScreenError:true }
              })
            }
          }
        } else {
          await shareRTC.handleLeave();
          setLocalStreamConfig({
            ...localStreamConfig,
            shareDesk: !localStreamConfig.shareDesk,
          });
        }

      default:
        break;
    }
  };

  // 切换角色
  const changeRole = async () => {
    if (!RTC.isJoined) {
      // message.error('please join room!', 2000);
      return;
    }
    if (RTC.isPublished || RTC.isPublishing) {
      // message.error('please change role in unpublish ', 2000);
      return;
    }
    try {
      const targetRole = userRole === audience ? anchor : audience;
      await RTC.client.switchRole(targetRole);
      RTC.role = targetRole;
      setUserRole(targetRole);
    } catch (error) {
      console.log('basic live change role error = ', error);
    }
  };


  // 移除流
  const removeStream = (stream) => {
    const streamType = stream.getType();
    const userID = stream.getUserId();

    switch (streamType) {
      case 'local':
        setLocalStreamConfig(prevConfig => ({
          ...prevConfig,
          hasAudio: false,
          hasVideo: false,
          stream:null,
        }));
        break;
      default: {
        setRemoteStreamConfigList(preList => preList
          .map(streamConfig => (streamConfig.userID === userID && streamConfig.streamType === streamType
            ? {
              ...streamConfig,
              hasAudio: false,
              hasVideo: false,
              subscribedAudio: false,
              subscribedVideo: false,
            } : streamConfig)));
        break;
      }
    }
  };

  // 新增用户
  const addUser = (userID, streamType) => {
    if (streamType === 'local') {
      setLocalStreamConfig({
        stream: null,
        streamType,
        userID,
        hasAudio: false,
        hasVideo: false,
        mutedAudio: false,
        mutedVideo: false,
        shareDesk: false,
        audioVolume: 0,
      });
    } else {
      setRemoteStreamConfigList((preList) => {
        const newRemoteStreamConfigList = preList.length > 0
          ? preList.filter(streamConfig => streamConfig.userID !== userID)
          : [];
        newRemoteStreamConfigList
          .push({
            stream: null,
            streamType: 'main',
            userID,
            hasAudio: false,
            hasVideo: false,
            mutedAudio: false,
            mutedVideo: false,
            subscribedAudio: false,
            subscribedVideo: false,
            resumeFlag: false,
            audioVolume: 0,
          });
        return newRemoteStreamConfigList;
      });
    }
  };

  // 移除用户
  const removeUser = (userID, streamType) => {
    if (streamType === 'local') {
      setLocalStreamConfig(null);
      setRemoteStreamConfigList([]);
    } else {
      setRemoteStreamConfigList(preList => preList.filter(streamConfig => streamConfig.userID !== userID));
    }
  };

  // ------------------[TRTC方法END]------------------


  /* 直播操作按钮方法 */
  const openCloseHandUp = async ({ handUpType }) => {

    // 接受连麦
    const wxuserId = UerInfo && UerInfo.friUserId
    const res = await dispatch({
      type: 'PlanetChatRoom/openCloseHandUp',
      payload: {
        spaceId: props?.match?.params?.RoomId, // [string]	是	空间ID
        wxUserId: wxuserId,
        handUpType: handUpType,
      }
    })
    // 开启接受连麦权限后 通知各客户页面 更新本页面状态
    const {code, content} = res || {}
    if (code == 200) {
      sendMessageByIm({
        dataType: UPDATA_STATE,
        description: JSON.stringify({imUserId:'ALL'})
      })
    }
    return res
  }

  // 点击录制视频
  const liveRecord = async ({recordType}) => {
    // 接受连麦
    const wxuserId = UerInfo && UerInfo.friUserId
    const streamType = shareHostRemoteStreamConfig && shareHostRemoteStreamConfig.streamType == 'main' ? 0 : 1; // 分享白板
    const res = await dispatch({
      type: 'PlanetChatRoom/liveRecord',
      payload: {
        spaceId: props?.match?.params?.RoomId, // [string]	是	空间ID
        wxUserId: wxuserId,
        recordType: recordType,
        streamType: streamType || streamType == 0 ? streamType : null,
      }
    })
    return res
  }

  // 获取查看空间连麦列表
  const getHandUpList = async () => {
    const wxuserId = UerInfo && UerInfo.friUserId
    const res = await dispatch({
      type: 'PlanetChatRoom/getHandUpList',
      payload: {
        spaceId: props?.match?.params?.RoomId,
        wxUserId: wxuserId,
        pageNum: 1,
        pageSize: 100,
      }
    })
    return res
  }

  // 操作连麦请求
  const operateHandUp = async ({statusType,guestUserId,imUserId}) => {
    const resByHandUpList = await getHandUpList();
    const {code: codeByHandUpList, content: contentByHandUpList} = resByHandUpList || {}
    const {resultList} = contentByHandUpList || {}
    // 判定当前是否还有其余用户正在连麦中
    if (statusType == 1 && Array.isArray(resultList) && resultList.find(item => item.statusType == 1)) {
      const currentHandUpUser = handUpList.find(item => item.statusType == 1)
      // 当前有用户正在连麦中
      dispatch({
        type: "PlanetChatRoom/setState",
        payload: {
          ModalVisibleByAcceptLienMai: {
            statusType,    // [string]	是	1 接受连麦 2暂不同意 3下麦
            guestUserId,   // [string]	需操作连麦的用户ID，当前用户是主持人时必传
            imUserId,
            currentHandUpUser
          }}
      })
      return;
    }
    // 接受连麦
    const wxuserId = UerInfo && UerInfo.friUserId
    const res = await dispatch({
      type: 'PlanetChatRoom/operateHandUp',
      payload: {
        spaceId: props?.match?.params?.RoomId,                 // [string]	是	空间ID
        // xwxUserId:wxuserId,           // [string]	是	用户ID
        guestUserId: guestUserId,         // [string]	需操作连麦的用户ID，当前用户是主持人时必传
        statusType: statusType,           // [string]	是	1 接受连麦 2暂不同意 3下麦
      }
    })
    const { code,content } = res || {}
    if (code == 200) {
      sendMessageByIm({
        dataType: UPDATA_STATE,
        // description: JSON.stringify({imUserId:imUserId}),
        description: JSON.stringify({imUserId:'ALL'}),
      })
    }
    return res;
  }

  /**申请连麦**/
  const onClickLianMai = async () => {
    if(SpaceInfo?.handUpStatusType == null) {
      // 开启申请连麦先检查用户设备权限
      const checkMicrophone = await checkDevices('microphone')
      const checkCamera = await checkDevices('camera')
      let CameraIDByCheckCamera, MicrophoneIDByCheckMicrophone = null;
      if (checkMicrophone) {
        // 获取麦克风设备
        const MicrophonesDeviceList = await TRTC.getMicrophones();
        MicrophoneIDByCheckMicrophone = await Array.isArray(MicrophonesDeviceList) && MicrophonesDeviceList.length > 0 && MicrophonesDeviceList[0].deviceId;
        await setMicrophoneID(MicrophoneIDByCheckMicrophone)
      }
      if (checkCamera) {
        // 获取摄像头设备
        const CamerasDeviceList = await TRTC.getCameras();
        CameraIDByCheckCamera = await Array.isArray(CamerasDeviceList) && CamerasDeviceList.length > 0 && CamerasDeviceList[0].deviceId;
        await setCameraID(CameraIDByCheckCamera);
      }
      if(!MicrophoneIDByCheckMicrophone) {
        // 当前用户 无麦克风设备
        dispatch({
          type: 'PlanetChatRoom/setState',
          payload: { ModalVisibleByAcceptLienMaiNoMicrophone:true }
        })
        return
      }
      // 申请连麦
      await dispatch({
        type: 'PlanetChatRoom/setState',
        payload: {SpaceInfo: {
            ...SpaceInfo,
            handUpStatusType:0,
          }} // 申请连麦
      })
      await sendMessageByIm({dataType: HAND_UP, description: '1'})
    }else if(SpaceInfo?.handUpStatusType == 0) {
      await sendMessageByIm({dataType: HAND_DOWN, description: '1'})
      // 取消申请连麦
      await dispatch({
        type: 'PlanetChatRoom/setState',
        payload: {SpaceInfo: {
            ...SpaceInfo,
            handUpStatusType:null,
          }} // 取消连麦
      })
    }else if(SpaceInfo?.handUpStatusType == 1) {
      // 主动下麦
      // 连麦成功-主动下麦 调用接口 调用调整状态接口
     /* await operateHandUp({statusType:3,wxUserId:wxUserId,guestUserId:wxUserId,imUserId:imUserId})
      await sendMessageByIm({dataType: HAND_UP, description: '1'})
      await dispatch({
        type: 'PlanetChatRoom/setState',
        payload: {SpaceInfo: {
            ...SpaceInfo,
            handUpStatusType:null,
          }} // 取消连麦
      })*/
      dispatch({
        type: 'PlanetChatRoom/setState',
        payload: { ModalVisibleByLeaveMicrophone:true }
      })
    }
  }
  // 关闭筛选弹窗
  const shareCloseOnClick = () => {
    setMaskVisible(false)
  }
  const shareOnClick = () => {
    // shareUpdateByType()
    // setMaskVisible(true)
    // val.current = null;
    /* dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {ModalVisibleByActionSheetShare: true}
    }) */
    posterModalRef && posterModalRef.current.init(2, SpaceInfoObj)
    // history.push(`/Poster?id=${props?.match?.params?.RoomId}`)
  }
  // 点击返回按钮
  const onClickBack = (historylen) => {
    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'goBack',       // 页面地址onchange事件
      }
      window.parent.postMessage(postData, getArrailUrl())
      return
    }

      const { isGoback } = history.location.query || {}
      if(isGoback == 1) { // 直接回到首页
        goToHomePage(dispatch, 'replace')
        return
      }
      // 如果是非app下的环境
      if (history.action != 'POP' && history.action != 'REPLACE') {
        history.goBack()
      } else if (history.action == 'REPLACE' && history.length > (!!historylen ? historylen : 1)) {
        history.goBack()
      } else {
        if (getOperatingEnv() == 5||getOperatingEnv() == 6) {
          // 如果是app环境则调用app返回 关闭webView
          backInApp();
        }else {
          // 如果不是app环境返回首页
          goToHomePage(dispatch, 'replace')
        }
      }
  }

  // 屏幕分享失败，帮助文档下载
  const downScreenShareHelpFile = async ()=> {
    const resByDownScreenShareHelpFile = await dispatch({
      type: 'PlanetChatRoom/downScreenShareHelpFile',
      payload: {}
    })

    if (resByDownScreenShareHelpFile) {
      if (resByDownScreenShareHelpFile.code == 500) {
        message.error('下载失败！');
      }else {
        getDownLoad(resByDownScreenShareHelpFile, `医生星球${starSpaceTypeText}帮助文档.pdf`);
      }
    }else {
      message.error('下载失败！');
    }
  }

  return (
    <>

      <Helmet>
        <title>{nameBySpaceInfo ? nameBySpaceInfo : `${starSpaceTypeText}详情`}</title>
        <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, shrink-to-fit=no, viewport-fit=cover" />
      </Helmet>

      <Spin spinning={
        !!loadingByPage ||
        !!loadingByCheckDevices ||
        !!props.loading.effects['PlanetChatRoom/getSpaceInfo'] ||
        !!props.loading.effects['PlanetChatRoom/getMsgCodeUserInfo'] ||
        !!props.loading.effects['PlanetChatRoom/getScreenShareUser'] ||
        !!props.loading.effects['PlanetChatRoom/spaceCollect']
      }>
        <div id={'DerailWarp'} className={styles.DerailWarp}>
          {/*
            竖版直播间-星球空间详情页面 VerticalLiveRoom
            横版直播间-星球空间详情页面 HorizontalLiveRoom
           */}
            <VerticalLiveRoom
              isHorizontalLive={isHorizontalLive}                 // 是否是全屏
              onRefByVerticalLiveRoom={RefByVerticalLiveRoom}     // refObj 暴露给父组件的方法
              sendMessageByIm={sendMessageByIm}                   // function 发送im实时通信消息
              localStreamConfig={localStreamConfig}               // 本地流配置
              remoteStreamConfigList={remoteStreamConfigList}     // 远端流配置列表
              RTC={RTC}                                           // rtc实例
              shareRTC={shareRTC}                                 // 分享RTC实例
              isJoined={isJoined}                                 // 是否加入房间
              isPublished={isPublished}                           // 是否发布流
              handleJoin={handleJoin}                             // onClick进入房间
              handleLeave={handleLeave}                           // onClick离开房间结束直播
              onChange={handleLocalChange}                        // onChange
              changeUrlParams={changeUrlParams}                   // 修改地址栏的状态参数
              spaceId={props?.match?.params?.RooxmId}              // 空间id
              openCloseHandUp={openCloseHandUp}                   // 操作连麦
              liveRecord={liveRecord}                             // 录制视频
              getGuestList={getGuestList}                         // 获取嘉宾列表
              getSpaceInfo={getSpaceInfo}                         // 获取用户信息
              onClickLianMai={onClickLianMai}                     // 申请连麦
              operateHandUp={operateHandUp}                       // 接受连麦
              elapsedTime={elapsedTime}                           // 正在录播时长
              shareOnClick={shareOnClick}                         // 分享
              onClickBack={onClickBack}                           // 点击返回按钮
            >
              <HorizontalLiveRoom
                tim={timObj}
                isHorizontalLive={isHorizontalLive}                 // 是否是全屏
                onRefByHorizontalLiveRoom={RefByHorizontalLiveRoom} // refObj 暴露给父组件的方法
                sendMessageByIm={sendMessageByIm}                   // function 发送im实时通信消息
                localStreamConfig={localStreamConfig}               // 本地流配置
                remoteStreamConfigList={remoteStreamConfigList}     // 远端流配置列表
                RTC={RTC}                                           // rtc实例
                shareRTC={shareRTC}                                 // 分享RTC实例
                isJoined={isJoined}                                 // 是否加入房间
                isPublished={isPublished}                           // 是否发布流
                handleJoin={handleJoin}                             // [交互] onClick进入房间
                handleLeave={handleLeave}                           // [交互] onClick离开房间结束直播
                onChange={handleLocalChange}                        // [交互] onChange 处理本地流 streamBar 的响应逻辑
                changeUrlParams={changeUrlParams}                   // [改变状态栏参数] 修改地址栏的状态参数
                spaceId={props?.match?.params?.RoomId}              // 空间id
                openCloseHandUp={openCloseHandUp}                   // 操作连麦 开启或关闭空间连麦
                liveRecord={liveRecord}                             // 录制视频 开启或关闭录制视频
                getGuestList={getGuestList}                         // 获取嘉宾列表 获取嘉宾列表方法
                getSpaceInfo={getSpaceInfo}                         // 获取用户信息 获取用户信息方法
                onClickLianMai={onClickLianMai}                     // 申请连麦 提交申请连麦点击事件
                operateHandUp={operateHandUp}                       // 接受连麦 接受连麦的点击时间
                elapsedTime={elapsedTime}                           // 正在录播时长 当前已录播时长
                shareOnClick={shareOnClick}                         // 分享 点击分享按钮
                onClickBack={onClickBack}                           // 点击返回按钮 点击返回按钮
              />
            </VerticalLiveRoom>

          {
            userID
            && roomID && trtcAppId
            && <DynamicRtc
              onRef={ref => setRTC(ref)}
              SDKAPPID={trtcAppId}
              userSig={userSig}
              userID={imUserId}
              roomID={roomID}
              // useStringRoomID={useStringRoomID}
              cameraID={cameraID}
              microphoneID={microphoneID}
              audio={audio}
              video={video}
              mode={mode}
              setState={setState}
              addUser={addUser}
              removeUser={removeUser}
              addStream={addStream}
              updateStream={updateStream}
              updateStreamConfig={updateStreamConfig}
              removeStream={removeStream}
              onKickedOut={onKickedOut} // 被踢出的回调
              role={userRole}
              setAutoExpandGuestArea={async (value)=>{
                if (value == 1) {
                  await dispatch({ type: 'PlanetChatRoom/setState', payload: { autoExpandGuestArea:null }})
                  await dispatch({ type: 'PlanetChatRoom/setState', payload: { autoExpandGuestArea:value }})
                }else {
                  setTimeout(()=>{
                    dispatch({ type: 'PlanetChatRoom/setState', payload: { autoExpandGuestArea:null }})
                    dispatch({ type: 'PlanetChatRoom/setState', payload: { autoExpandGuestArea:value }})
                  },1000)
                }
              }}
            />
          }

          {
            localStreamConfig
            && <DynamicShareRtc
              onRef={ref => setShareRTC(ref)}
              SDKAPPID={trtcAppId}
              userSig={screenShareUser && screenShareUser.userSig || userSig}
              // 主流id
              mainStreamUserID={imUserId}
              userID={screenShareUser && screenShareUser.imUserId || imUserId}
              roomID={roomID}
              relatedUserID={screenShareUser && screenShareUser.imUserId || imUserId}
              // useStringRoomID={useStringRoomID}
              updateStreamConfig={updateStreamConfig}/>
          }

          {/*[空间提示弹窗] - 该空间因违反平台规范已被关闭=>调整为不明确表示被运营端下架*/}
          {!!ModalVisibleBySpaceViolation &&
            <div className={styles.WarnModalWarp_NoRem}>
              <Modal
                visible={!!ModalVisibleBySpaceViolation}
                // content='111111'
                closeOnAction
                onClose={() => {
                  // setVisible(false)
                  dispatch({
                    type:'PlanetChatRoom/setState',
                    payload:{ ModalVisibleBySpaceViolation:false, }
                  })
                  dispatch({ type:'PlanetChatRoom/clean', })
                  val.current = null
                  onClickBack();
                }}
                content={
                  <div className={styles.WarnModal}>
                    <div className={styles.WarnModalTitle}>
                      <i className={styles.SpatialDetail_modal_warn_icon}/>
                      <div
                        className={styles.SpatialDetail_modal_warn_title}>{ModalVisibleBySpaceViolation ? ModalVisibleBySpaceViolation : `该${starSpaceTypeText}已被关闭下架`}</div>
                    </div>
                    <div className={styles.WarnModalBtnWarp}>
                      <div onClick={()=>{
                        dispatch({
                          type: 'PlanetChatRoom/setState',
                          payload: { ModalVisibleBySpaceViolation:false }
                        })
                        dispatch({ type:'PlanetChatRoom/clean', })
                        val.current = null
                        onClickBack();
                      }} className={styles.CancelBtn}>返回</div>
                      <div onClick={()=>{
                        dispatch({
                          type: 'PlanetChatRoom/setState',
                          payload: { ModalVisibleBySpaceViolation:false }
                        })
                        dispatch({ type:'PlanetChatRoom/clean', })
                        val.current = null
                        onClickBack();
                      }} className={styles.EnterBtn}>我知道了</div>
                    </div>
                  </div>
                }
              />
            </div>
          }


          {/* 该空间已下架 或空间直播接口报错 */}
          {!!ModalVisibleBySpaceRemoved &&
          <div className={styles.WarnModalWarp_NoRem}>
            <Modal
              visible={!!ModalVisibleBySpaceRemoved}
              // content='111111'
              closeOnAction
              onClose={() => {
                // setVisible(false)
                dispatch({
                  type:'PlanetChatRoom/setState',
                  payload:{ ModalVisibleBySpaceRemoved:false, }
                })
              }}
              content={
                <div className={styles.WarnModal}>
                  <div className={styles.WarnModalTitle}>
                    <i className={styles.SpatialDetail_modal_warn_icon}/>
                    <div
                      className={styles.SpatialDetail_modal_warn_title}>{ModalVisibleBySpaceRemoved ? ModalVisibleBySpaceRemoved : `该${starSpaceTypeText}已下架!`}</div>
                  </div>
                  <div className={styles.WarnModalBtnWarp}>
                    <div onClick={()=>{
                      dispatch({
                        type: 'PlanetChatRoom/setState',
                        payload: { ModalVisibleBySpaceRemoved:false }
                      })
                      clear().then(()=>{
                        onClickBack();
                      })
                    }} className={styles.CancelBtn}>返回</div>
                    <div onClick={()=>{
                      dispatch({
                        type: 'PlanetChatRoom/setState',
                        payload: { ModalVisibleBySpaceRemoved:false }
                      })
                      clear().then(()=>{
                        onClickBack();
                      })
                    }} className={styles.EnterBtn}>我知道了</div>
                  </div>
                </div>
              }
            />
          </div>
          }

          {/*[空间提示弹窗] - 多端登录被踢*/}
          {!!ModalVisibleByKickedOut &&
          <div className={styles.WarnModalWarp_NoRem}>
            <Modal
              visible={!!ModalVisibleByKickedOut}
              closeOnAction
              onClose={() => {
                dispatch({
                  type:'PlanetChatRoom/setState',
                  payload:{ ModalVisibleByKickedOut:false, }
                })
              }}
              content={
                <div className={styles.WarnModal}>
                  <div className={styles.WarnModalTitle}>
                    <i className={styles.SpatialDetail_modal_warn_icon}/>
                    <div
                      className={styles.SpatialDetail_modal_warn_title}>您已经进入其他{starSpaceTypeText}，当前{starSpaceTypeText}自动退出
                    </div>
                  </div>
                  <div className={styles.WarnModalBtnWarp}>
                    <div onClick={()=>{
                      dispatch({
                        type: 'PlanetChatRoom/setState',
                        payload: { ModalVisibleByKickedOut:false }
                      })
                      val.current = false
                      clear().then(()=>{
                        onClickBack();
                      })
                    }} className={styles.CancelBtn}>返回</div>
                    <div onClick={()=>{
                      dispatch({
                        type: 'PlanetChatRoom/setState',
                        payload: { ModalVisibleByKickedOut:false }
                      })
                      val.current = false
                      clear().then(()=>{
                        onClickBack();
                      })
                    }} className={styles.EnterBtn}>我知道了</div>
                  </div>
                </div>
              }
            />
          </div>
          }


          {/*[空间提示弹窗] - 取消预约 */}
          {!!ModalVisibleByCancelAppointment &&
          <div className={styles.WarnModalWarp_NoRem}>
            <Modal
              visible={!!ModalVisibleByCancelAppointment}
              // content='111111'
              closeOnAction
              onClose={() => {
                dispatch({
                  type:'PlanetChatRoom/setState',
                  payload:{ ModalVisibleByCancelAppointment:false, }
                })
              }}
              content={
                <div className={styles.WarnModal}>
                  <div className={styles.WarnModalTitle}>
                    <i className={styles.SpatialDetail_modal_warn_icon}/>
                    <div className={styles.SpatialDetail_modal_title}>取消预约</div>
                  </div>
                  <div className={styles.SpatialDetail_modal_desc}>
                    取消后将无法接收到该{starSpaceTypeText}相关通知
                  </div>
                  <div className={styles.WarnModalBtnWarp}>
                    <div
                      onClick={()=>{
                        dispatch({
                          type:'PlanetChatRoom/liveAppointment',
                          payload: {
                            spaceId:props?.match?.params?.RoomId,
                            wxUserId:wxUserId,
                            appointmentType:2 // 1预约 2取消预约
                          }
                        })
                      }}
                      className={styles.CancelBtn}>狠心取消</div>
                    <div
                      onClick={()=>{
                        dispatch({
                          type:'PlanetChatRoom/setState',
                          payload:{
                            ModalVisibleByCancelAppointment:false,
                          }
                        })
                      }}
                      className={styles.EnterBtn}
                    >我在想想</div>
                  </div>
                </div>
              }
            />
          </div>
          }


          {/*[空间提示弹窗] - 结束录制 */}
          {!!ModalVisibleByEndRecording &&
          <div className={styles.WarnModalWarp_NoRem}>
            <Modal
              visible={!!ModalVisibleByEndRecording}
              closeOnAction
              onClose={() => {
                dispatch({
                  type:'PlanetChatRoom/setState',
                  payload:{ ModalVisibleByEndRecording:false, }
                })
              }}
              content={
                <div className={styles.WarnModal}>
                  <div className={styles.WarnModalTitle}>
                    <i className={styles.SpatialDetail_modal_warn_icon}/>
                    <div className={styles.SpatialDetail_modal_title}>结束录制</div>
                  </div>
                  <div className={styles.SpatialDetail_modal_desc}>
                    结束录制后将保存当前视频，并在{starSpaceTypeText}关闭后发布
                  </div>
                  <div className={styles.WarnModalBtnWarp}>
                    <div
                      onClick={async ()=>{
                        const res = await liveRecord({recordType: 2});
                        if(res && res.code == 200) {
                          message.success('结束录制成功');
                          await dispatch({
                            type: 'PlanetChatRoom/setState',
                            payload: { ModalVisibleByEndRecording: false }
                          })
                        }else {
                          message.error(res&& res.msg ? res.msg : '结束录制失败');
                          await dispatch({
                            type: 'PlanetChatRoom/setState',
                            payload: { ModalVisibleByEndRecording: false }
                          })
                        }
                      }}
                      className={styles.CancelBtn}>确认结束</div>
                    <div onClick={()=> {
                      dispatch({
                        type: 'PlanetChatRoom/setState',
                        payload: { ModalVisibleByEndRecording: false }
                      })
                    }} className={styles.EnterBtn}>我再想想</div>
                  </div>
                </div>
              }
            />
          </div>
          }


          {/* [空间提示弹窗] - 关闭空间  */}
          {!!ModalVisibleByClosedSpace &&
          <div className={styles.WarnModalWarp_NoRem}>
            <Modal
              visible={!!ModalVisibleByClosedSpace}
              // content='111111'
              closeOnAction
              onClose={() => {
                dispatch({
                  type:'PlanetChatRoom/setState',
                  payload:{ ModalVisibleByClosedSpace:false, }
                })
              }}
              content={
                <div className={styles.WarnModal}>
                  <div className={styles.WarnModalTitle}>
                    <i className={styles.SpatialDetail_modal_warn_icon}/>
                    <div className={styles.SpatialDetail_modal_title}>关闭{starSpaceTypeText}</div>
                  </div>
                  <div className={styles.SpatialDetail_modal_desc}>
                    关闭该{starSpaceTypeText}后，如有录制视频将自动发布
                  </div>
                  <div className={styles.WarnModalBtnWarp}>
                    <div
                      className={styles.CancelBtn}
                      onClick={async ()=>{
                        await handleLeave()
                        await sendMessageByIm({
                          dataType: ROOM_DISBAND,   // 通知消息发送房间被解散
                          description: JSON.stringify({imUserId:'ALL'})
                        })

                        // 点击关闭空间时先判定是否有正在推入的白板流有的话,先停止推流再关闭
                        const resByStopWhiteboardPush = await dispatch({
                          type: 'PlanetChatRoom/stopWhiteboardPush',
                          payload: {spaceId: props?.match?.params?.RoomId}
                        })

                         // 关闭空间
                        const res = await dispatch({
                          type:'PlanetChatRoom/startEndLive',
                          payload: {
                            spaceId: props?.match?.params?.RoomId,
                            wxUserId: wxUserId,
                            liveType:2, // liveType 1开始直播 2结束直播
                          }
                        })


                        const { code,content,msg } = res || {}
                        if (code == 200) {
                          message.success(`关闭${starSpaceTypeText}`)
                          clearLocalStateByStreamById(props?.match?.params?.RoomId); // 清空本地存储的状态
                          await setRTC(null)
                          await sendMessageByIm({
                            dataType: UPDATA_STATE,
                            description: JSON.stringify({imUserId:'ALL'})
                          })
                          await dispatch({
                            type:'PlanetChatRoom/setState',
                            payload:{ModalVisibleByClosedSpace:false}
                          })
                          clear().then(()=>{
                            onClickBack()
                          })
                        }else {
                          message.warning(msg ? msg : '关闭失败')
                        }
                      }}
                    >确认关闭</div>
                    <div
                      onClick={async ()=>{
                        // 关闭空间弹窗
                        await dispatch({
                          type:'PlanetChatRoom/setState',
                          payload:{
                            ModalVisibleByClosedSpace:false
                          }
                        })
                      }}
                      className={styles.EnterBtn}>我再想想</div>
                  </div>
                </div>
              }
            />
          </div>
          }

          {/* [空间提示弹窗] - 接受连麦  */}
          {!!ModalVisibleByAcceptLienMai &&
          <div className={styles.WarnModalWarp_NoRem}>
            <Modal
              visible={!!ModalVisibleByAcceptLienMai}
              // content='111111'
              closeOnAction
              onClose={() => {
                // setVisible(false)
                dispatch({
                  type:'PlanetChatRoom/setState',
                  payload:{ ModalVisibleByAcceptLienMai:false, }
                })
              }}
              content={
                <div className={styles.WarnModal}>
                  <div className={styles.WarnModalTitle}>
                    <i className={styles.SpatialDetail_modal_warn_icon}/>
                    <div className={styles.SpatialDetail_modal_title}>接受{starSpaceTypeLianMaiText}</div>
                  </div>
                  <div className={styles.SpatialDetail_modal_desc}>
                    仅允许1人{starSpaceTypeLianMaiText}中，如您接受新的{starSpaceTypeLianMaiText}申请，将为您自动断连当前{starSpaceTypeLianMaiText}人
                  </div>
                  <div className={styles.WarnModalBtnWarp}>
                    <div
                      onClick={async ()=>{
                        const {
                          statusType,
                          guestUserId,
                          imUserId,
                          currentHandUpUser
                        } = ModalVisibleByAcceptLienMai || {}
                        /**
                         currentHandUpUser.applyDate: "2023-07-11 14:19:11"
                         currentHandUpUser.headUrlShow: null
                         currentHandUpUser.imUserId: "b49b2335fb0c0bb42ea25e8fa5a6b23c"
                         currentHandUpUser.name: "李老师"
                         currentHandUpUser.statusType: 1
                         currentHandUpUser.wxUserId: 55
                         * */
                          // 上一个人强制下麦,下一个人接受连麦
                        const res = await operateHandUp({
                            statusType: 3,
                            guestUserId: currentHandUpUser.wxUserId,
                            imUserId: currentHandUpUser.imUserId
                          })
                        const {code, content, msg} = res || {}
                        if (code == 200) {
                          const res2 = await operateHandUp({statusType: 1, guestUserId, imUserId})
                          if(res2 && res2.code == 200){
                            message.success(`接受${starSpaceTypeLianMaiText}成功`)
                            dispatch({
                              type:'PlanetChatRoom/setState',
                              payload:{ ModalVisibleByAcceptLienMai:false }
                            })
                          }
                        }else {
                          message.error(msg ? msg : '强制下麦失败')
                        }
                      }}
                      className={styles.CancelBtn}>确认接受</div>
                    <div
                      onClick={()=>{
                        dispatch({
                          type:'PlanetChatRoom/setState',
                          payload:{ ModalVisibleByAcceptLienMai:false }
                        })
                      }}
                      className={styles.EnterBtn}>我再想想</div>
                  </div>
                </div>
              }
            />
          </div>
          }

          {/* [空间提示弹窗] - 主播或嘉宾无麦克风  */}
          {!!ModalVisibleByNoMicrophone &&
          <div className={styles.WarnModalWarp_NoRem}>
            <Modal
              visible={!!ModalVisibleByNoMicrophone}
              // content='111111'
              closeOnAction
              onClose={() => {
                // setVisible(false)
                dispatch({
                  type:'PlanetChatRoom/setState',
                  payload:{ ModalVisibleByNoMicrophone:false, }
                })
              }}
              content={
                <div className={styles.WarnModal}>
                  <div className={styles.WarnModalTitle}>
                    <i className={styles.SpatialDetail_modal_warn_icon}/>
                    <div className={styles.SpatialDetail_modal_warn_title}>
                      未检测到麦克风，请检查您的麦克风权限,配置后刷新页面
                    </div>
                  </div>
                  <div className={styles.WarnModalBtnWarp}>
                    <div onClick={()=>{
                      dispatch({
                        type:'PlanetChatRoom/setState',
                        payload:{ ModalVisibleByNoMicrophone:false }
                      })
                      onClickBack()
                    }} className={styles.CancelBtn}>返回
                    </div>
                    <div onClick={() => {
                      dispatch({
                        type: 'PlanetChatRoom/setState',
                        payload: {ModalVisibleByNoMicrophone: false}
                      })
                    }} className={styles.EnterBtn}>我知道了
                    </div>
                  </div>
                  <div onClick={downScreenShareHelpFile} className={styles.Download_Documents}>
                    <Spin style={{marginRight: '5px'}}
                          spinning={!!props.loading.effects['PlanetChatRoom/downScreenShareHelpFile']}/> 帮助文档
                  </div>
                </div>
              }
            />
          </div>
          }

          {/* [空间提示弹窗] - 申请连麦-无麦克风设备-禁用连麦申请 */}
          {!!ModalVisibleByAcceptLienMaiNoMicrophone &&
          <div className={styles.WarnModalWarp_NoRem}>
            <Modal
              visible={!!ModalVisibleByAcceptLienMaiNoMicrophone }
              // content='111111'
              closeOnAction
              onClose={() => {
                // setVisible(false)
                dispatch({
                  type:'PlanetChatRoom/setState',
                  payload:{ ModalVisibleByAcceptLienMaiNoMicrophone:false, }
                })
              }}
              content={
                <div className={styles.WarnModal}>
                  <div className={styles.WarnModalTitle}>
                    <i className={styles.SpatialDetail_modal_warn_icon}/>
                    <div className={styles.SpatialDetail_modal_title}>申请{starSpaceTypeLianMaiText}</div>
                  </div>
                  <div className={styles.SpatialDetail_modal_desc}>
                    未检测到麦克风，请检查您的麦克风和麦克风权限,配置后重新点击申请{starSpaceTypeLianMaiText}
                  </div>
                  <div className={styles.WarnModalBtnWarp}>
                    <div onClick={()=>{
                      dispatch({
                        type:'PlanetChatRoom/setState',
                        payload:{ ModalVisibleByAcceptLienMaiNoMicrophone:false }
                      })
                    }} className={styles.CancelBtn}>返回
                    </div>
                    <div onClick={() => {
                      dispatch({
                        type: 'PlanetChatRoom/setState',
                        payload: {ModalVisibleByAcceptLienMaiNoMicrophone: false}
                      })
                    }} className={styles.EnterBtn}>我知道了
                    </div>
                  </div>
                  <div onClick={downScreenShareHelpFile} className={styles.Download_Documents}>
                    <Spin style={{marginRight: '5px'}}
                          spinning={!!props.loading.effects['PlanetChatRoom/downScreenShareHelpFile']}/> 帮助文档
                  </div>
                </div>
              }
            />
          </div>
          }

          {/*确定下麦?*/}
          {!!ModalVisibleByLeaveMicrophone &&
          <div className={styles.WarnModalWarp_NoRem}>
            <Modal
              visible={!!ModalVisibleByLeaveMicrophone}
              // content='111111'
              closeOnAction
              maskStyle={{backgroundColor:'rgba(0,0,0,0.8)'}}
              onClose={() => {
                // setVisible(false)
                dispatch({
                  type:'PlanetChatRoom/setState',
                  payload:{ ModalVisibleByLeaveMicrophone:false, }
                })
              }}
              content={
                <div className={styles.WarnModal}>
                  <div className={styles.WarnModalTitle}>
                    <i className={styles.SpatialDetail_modal_warn_icon}/>
                    <div className={styles.SpatialDetail_modal_warn_title}>
                      您确认要下麦吗？
                    </div>
                  </div>
                  <div className={styles.WarnModalBtnWarp}>
                    <div
                      onClick={async ()=>{
                         await operateHandUp({statusType:3,wxUserId:wxUserId,guestUserId:wxUserId,imUserId:imUserId})
                         await sendMessageByIm({dataType: HAND_DOWN, description: '1'})
                         await dispatch({
                           type: 'PlanetChatRoom/setState',
                           payload: {
                             SpaceInfo: {
                               ...SpaceInfo,
                               handUpStatusType:null,
                             },
                           }
                         })
                      }}
                      className={styles.CancelBtn}>确认下麦</div>
                    <div
                      onClick={()=>{
                        dispatch({
                          type:'PlanetChatRoom/setState',
                          payload:{ ModalVisibleByLeaveMicrophone:false }
                        })
                      }}
                      className={styles.EnterBtn}>我再想想</div>
                  </div>
                </div>
              }
            />
          </div>
          }


          {/* 横屏检查 */}
          {!!ModalVisibleByOrientationWrong &&
          <div className={styles.WarnModalWarp_NoRem}>
            <Modal
              visible={!!ModalVisibleByOrientationWrong}
              // content='111111'
              closeOnAction
              maskStyle={{backgroundColor:'rgba(0,0,0,0.8)'}}
              onClose={() => {
                // setVisible(false)
                dispatch({
                  type:'PlanetChatRoom/setState',
                  payload:{ ModalVisibleByOrientationWrong:false, }
                })
              }}
              content={
                <div className={styles.WarnModal}>
                  <div className={styles.WarnModalTitle}>
                    <i className={styles.SpatialDetail_modal_warn_icon}/>
                    <div className={styles.SpatialDetail_modal_warn_title}>
                      请将设备横屏以获得最佳浏览体验
                    </div>
                  </div>
                  <div className={styles.WarnModalBtnWarp}>
                    <div onClick={()=>{
                      dispatch({
                        type:'PlanetChatRoom/setState',
                        payload:{
                          ModalVisibleByOrientationWrong:false,
                          isHorizontalLive:false,
                        }
                      })
                      changeUrlParams({isHorizontalLive:null})
                    }} className={styles.EnterBtn}>退出全屏</div>
                  </div>
                </div>
              }
            />
          </div>
          }

          {/* [空间提示弹窗] 横屏但依然是竖版页面*/}
          {!!ModalVisibleByVerticalPageWrong &&
          <div className={styles.WarnModalWarp_NoRem}>
            <Modal
              visible={!!ModalVisibleByVerticalPageWrong}
              // content='111111'
              closeOnAction
              maskStyle={{backgroundColor:'rgba(0,0,0,0.8)'}}
              onClose={() => {
                // setVisible(false)
                dispatch({
                  type:'PlanetChatRoom/setState',
                  payload:{ ModalVisibleByVerticalPageWrong:false, }
                })
              }}
              content={
                <div className={styles.WarnModal}>
                  <div className={styles.WarnModalTitle}>
                    <i className={styles.SpatialDetail_modal_warn_icon}/>
                    <div className={styles.SpatialDetail_modal_warn_title}>
                      是否切换到横屏模式,以获得最佳浏览体验
                    </div>
                  </div>
                  <div className={styles.WarnModalBtnWarp}>
                    {!isMobile &&
                      <div onClick={()=>{
                        dispatch({
                          type:'PlanetChatRoom/setState',
                          payload:{
                            ModalVisibleByVerticalPageWrong:false,
                          }
                        })
                      }} className={styles.CancelBtn}>暂时不用</div>
                    }
                    <div onClick={()=>{
                      dispatch({
                        type:'PlanetChatRoom/setState',
                        payload:{
                          ModalVisibleByVerticalPageWrong:false,
                          isHorizontalLive:true,
                        }
                      })
                      changeUrlParams({isHorizontalLive:1})
                    }} className={styles.EnterBtn}>确定</div>
                  </div>
                </div>
              }
            />
          </div>
          }

          {/* [空间提示弹窗] - 预约状态下-关闭空间  */}
          {!!ModalVisibleByAppointmentClosedSpace &&
          <div className={styles.WarnModalWarp_NoRem}>
            <Modal
              visible={!!ModalVisibleByAppointmentClosedSpace}
              // content='111111'
              closeOnAction
              onClose={() => {
                // setVisible(false)
                dispatch({
                  type:'PlanetChatRoom/setState',
                  payload:{ ModalVisibleByAppointmentClosedSpace:false, }
                })
              }}
              content={
                <div className={styles.WarnModal}>
                  <div className={styles.WarnModalTitle}>
                    <i className={styles.SpatialDetail_modal_warn_icon}/>
                    <div className={styles.SpatialDetail_modal_title}>关闭{starSpaceTypeText}</div>
                  </div>
                  <div className={styles.SpatialDetail_modal_desc}>
                    关闭后，该{starSpaceTypeText}将从您的{starSpaceTypeText}列表中移除
                  </div>
                  <div className={styles.WarnModalBtnWarp}>
                    <div
                      className={styles.CancelBtn}
                      onClick={async ()=>{
                        // 关闭空间
                        const res = await dispatch({
                          type: 'PlanetChatRoom/startEndLive',
                          payload: {
                            spaceId: props?.match?.params?.RoomId,
                            wxUserId: wxUserId,
                            liveType: 2, // liveType 1开始直播 2结束直播
                          }
                        })
                        const {code, content, msg} = res || {}
                        if (code == 200) {
                          message.success(`${starSpaceTypeText}关闭成功`)
                          // 开始直播间后发送通知 更新全部客户端页面状态
                          await sendMessageByIm({
                            dataType: UPDATA_STATE,
                            description: JSON.stringify({imUserId:'ALL'})
                          })
                          await dispatch({
                            type:'PlanetChatRoom/setState',
                            payload:{ ModalVisibleByAppointmentClosedSpace:false }
                          })
                          onClickBack()
                        }else {
                          message.warning(msg ? msg : `关闭${starSpaceTypeText}失败`)
                        }
                      }}
                    >确认关闭</div>
                    <div onClick={async ()=>{
                      await dispatch({
                        type:'PlanetChatRoom/setState',
                        payload:{
                          ModalVisibleByAppointmentClosedSpace:false
                        }
                      })
                    }} className={styles.EnterBtn}>我再想想</div>
                  </div>
                </div>
              }
            />
          </div>
          }

          {/* 强制下麦 */}
          {!!ModalVisibleByForceWheat &&
          <div className={styles.WarnModalWarp_NoRem}>
            <Modal
              visible={!!ModalVisibleByForceWheat}
              // content='111111'
              closeOnAction
              onClose={() => {
                // setVisible(false)
                dispatch({
                  type:'PlanetChatRoom/setState',
                  payload:{ ModalVisibleByForceWheat:false, }
                })
              }}
              content={
                <div className={styles.WarnModal}>
                  <div className={styles.WarnModalTitle}>
                    <i className={styles.SpatialDetail_modal_warn_icon}/>
                    <div className={styles.SpatialDetail_modal_title}>强制下麦</div>
                  </div>
                  <div className={styles.SpatialDetail_modal_desc}>
                    点击强制下麦，当前{starSpaceTypeLianMaiText}观众将被下麦
                  </div>
                  <div className={styles.WarnModalBtnWarp}>
                    <div
                      className={styles.CancelBtn}
                      onClick={async ()=>{
                        await operateHandUp(ModalVisibleByForceWheat)
                        await dispatch({
                          type:'PlanetChatRoom/setState',
                          payload:{ ModalVisibleByForceWheat:false }
                        })
                      }}
                    >确认下麦</div>
                    <div onClick={async ()=>{
                      await dispatch({
                        type:'PlanetChatRoom/setState',
                        payload:{
                          ModalVisibleByForceWheat:false
                        }
                      })
                    }} className={styles.EnterBtn}>我再想想</div>
                  </div>
                </div>
              }
            />
          </div>
          }

          {/* [空间提示弹窗] - 马上开始后，空间状态将变为进行中 */}
          {!!ModalVisibleByStartLive &&
          <div className={styles.WarnModalWarp_NoRem}>
            <Modal
              visible={!!ModalVisibleByStartLive}
              // content='111111'
              closeOnAction
              onClose={() => {
                // setVisible(false)
                dispatch({
                  type:'PlanetChatRoom/setState',
                  payload:{ ModalVisibleByStartLive:false, }
                })
              }}
              content={
                <div className={styles.WarnModal}>
                  <div className={styles.WarnModalTitle}>
                    <i className={styles.SpatialDetail_modal_warn_icon}/>
                    <div
                      className={styles.SpatialDetail_modal_warn_title}>马上开始后，{starSpaceTypeText}状态将变为进行中
                    </div>
                  </div>
                  <div className={styles.WarnModalBtnWarp}>
                    <div
                      onClick={async ()=>{
                        await dispatch({
                          type:'PlanetChatRoom/setState',
                          payload:{ ModalVisibleByStartLive:false }
                        })
                      }}
                      className={styles.CancelBtn}>我再想想</div>
                    <div
                      onClick={async ()=>{
                        // 点击马上开始直播
                        // props.handleJoin
                        const res = await dispatch({
                          type:'PlanetChatRoom/startEndLive',
                          payload: {
                            spaceId: props?.match?.params?.RoomId,
                            wxUserId: wxUserId,
                            liveType:1, // liveType 1开始直播 2结束直播
                          }
                        })
                        const resBySpaceInfo = await getSpaceInfo();  // 更新获取空间信息
                        const { code:codeByInfo,content:contentByInfo } = resBySpaceInfo || {}
                        const { code,content,msg } = res || {}
                        if (code == 200 && codeByInfo == 200) {
                          if(contentByInfo.status == 1){ // ：1直播中、2预约中、3弹幕轰炸中
                            // 马上开始直播 加入房间并开启本地流
                            await handleJoin()
                          }
                          // 开始直播间后发送通知 更新全部客户端页面状态
                          await sendMessageByIm({
                            dataType: UPDATA_STATE,
                            description: JSON.stringify({imUserId:'ALL'})
                          })
                          await dispatch({
                            type:'PlanetChatRoom/setState',
                            payload:{
                              ModalVisibleByStartLive:false,
                            }
                          })
                        }else {
                          message.warning(msg? msg : '开始直播失败!')
                        }
                      }}
                      className={styles.EnterBtn}>马上开始
                    </div>
                  </div>
                </div>
              }
            />
          </div>
          }

          {/* 屏幕分享失败 */}
          {!!ModalVisibleByShareScreenError &&
            <div className={styles.WarnModalWarp_NoRem}>
              <Modal
                visible={!!ModalVisibleByShareScreenError}
                closeOnAction
                onClose={() => {}}
                content={
                  <div className={styles.WarnModal}>
                    <div className={styles.WarnModalTitle}>
                      <i className={styles.SpatialDetail_modal_warn_icon}/>
                      <div
                        className={styles.SpatialDetail_modal_warn_title}>分享屏幕失败,请确保系统允许当前浏览器获取屏幕内容
                      </div>
                    </div>
                    <div className={styles.WarnModalBtnWarp}>
                      <div onClick={()=>{
                        dispatch({
                          type:'PlanetChatRoom/setState',
                          payload:{ ModalVisibleByShareScreenError:false }
                        })
                      }} className={styles.CancelBtn}>返回
                      </div>
                      <div onClick={() => {
                        dispatch({
                          type: 'PlanetChatRoom/setState',
                          payload: {ModalVisibleByShareScreenError: false}
                        })
                      }} className={styles.EnterBtn}>我知道了
                      </div>
                    </div>
                    <div onClick={downScreenShareHelpFile} className={styles.Download_Documents}>
                      <Spin style={{marginRight: '5px'}}
                            spinning={!!props.loading.effects['PlanetChatRoom/downScreenShareHelpFile']}/> 帮助文档
                    </div>
                  </div>
                }
              />
            </div>
          }

          {/* 用户未登录提示弹窗 */}
          {!!ModalVisibleByUserTokenInvalid &&
            <div className={styles.WarnModalWarp_NoRem}>
              <Modal
                visible={!!ModalVisibleByUserTokenInvalid}
                closeOnAction
                onClose={() => {}}
                content={
                  <div className={styles.WarnModal}>
                    <div className={styles.WarnModalTitle}>
                      <i className={styles.SpatialDetail_modal_warn_icon}/>
                      <div className={styles.SpatialDetail_modal_warn_title}>立即登录，畅享精彩内容！</div>
                    </div>
                    <div className={styles.WarnModalBtnWarp}>
                      <div
                        onClick={async ()=>{
                          await dispatch({
                            type:'PlanetChatRoom/setState',
                            payload:{ ModalVisibleByUserTokenInvalid:false }
                          })
                        }}
                        className={styles.CancelBtn}>暂不登录</div>
                      <div
                        onClick={async ()=>{ userTokenInvalid('/PlanetChatRoom') }}
                        className={styles.EnterBtn}>立即登录</div>
                    </div>
                  </div>
                }
              />
            </div>
          }

          {/* 已提交申请 */}
          {!!ModalVisibleByApplicationSubmitted &&
            <div className={styles.WarnModalWarp_NoRem}>
              <Modal
                visible={!!ModalVisibleByApplicationSubmitted}
                closeOnAction
                onClose={() => {}}
                content={
                  <div className={styles.WarnModal}>
                    <div className={styles.WarnModalTitle}>
                      <i className={styles.SpatialDetail_modal_success_icon}/>
                      <div className={styles.SpatialDetail_modal_warn_title}>已提交申请</div>
                    </div>
                    <div className={styles.SpatialDetail_modal_desc_ApplicationSubmitted}>
                      您的申请已成功发送！请耐心等待主持人同意。申请同意后，我们将立即通过短信通知您
                    </div>
                    <div className={styles.WarnModalBtnWarp}>
                      <div
                        onClick={()=> {
                          dispatch({
                            type: 'PlanetChatRoom/setState',
                            payload: {ModalVisibleByApplicationSubmitted: false}
                          })
                          onClickBack();
                        }}
                        className={styles.EnterBtnByModalVisibleByApplicationSubmitted}>
                        确定
                      </div>
                    </div>
                    <div onClick={downScreenShareHelpFile} className={styles.Download_Documents}/>
                  </div>
                }
              />
            </div>
          }


          {/* 直播间密码需要输入密码 0：不需要 1需要 */}
          {(!ModalVisibleByApplicationSubmitted && !ModalVisibleByUserTokenInvalid) &&
            <div className={styles.PopupWarp}>
              <Popup
                visible={isNeedPwd == 1}
                onMaskClick={() => {}}
                bodyStyle={{
                  borderTopLeftRadius: '12px',
                  borderTopRightRadius: '12px',
                  minHeight: '70vh',
                }}
              >
                <div className={styles.PopupContent}>
                  <div className={styles.lineWarp}>
                    <div className={styles.line}/>
                  </div>
                  <div className={styles.content}>
                    <div className={styles.contentTitle}>{starSpaceTypeText}密码</div>
                    <div className={styles.contentBox}>
                      <div className={styles.pwdTitle}><span className={styles.stress}>*</span>{starSpaceTypeText}密码</div>
                    </div>

                    {!!(isNeedPwd == 1) &&
                      <div className={styles.PwdFlex}>
                        {pwdArray.map((item, index) => (
                          <React.Fragment key={index}>
                            <Input
                              type="number"
                              maxLength={1}
                              value={pwdArray[index]}
                              onInput={(e)=>{
                                const value = !!e.target.value ? e.target.value.trim() : null;
                                handleInput(index, value)
                              }}
                              onKeyDown={(e) => handleDelete(index, e)}
                              className={styles.PwdInput}
                              ref={(el) => (inputRefs.current[index] = el)}
                            />
                            {
                              index == pwdArray.length - 1 ? null : <div className={styles.line_pwd}/>
                            }
                          </React.Fragment>
                        ))}
                      </div>
                    }
                    {isNoPasswordApply == 1 &&
                      <div
                        className={styles.allowApplications_box}
                        onClick={addStarSpaceApplyAdmission}
                      >
                        <div className={styles.allowApplications_box_warp}>
                          <Spin className={styles.spin_box}
                                spinning={!!props.loading.effects['PlanetChatRoom/updateStarSpaceApplyAdmission']}/>
                          <div className={styles.allowApplications_box_text}>
                            无密码？向主持人申请进入会议
                          </div>
                        </div>
                      </div>
                    }

                  </div>
                </div>
              </Popup>
            </div>
          }

          <Mask style={{'--z-index': "998"}} opacity={0.7} visible={maskVisible} onMaskClick={shareCloseOnClick}/>
          <div className={classNames(styles.fixed_share_box, {[styles.fixed_share_box_show]: maskVisible})}>
            <i className={styles.icon1}/>
            <div className={styles.message_box}>
              <div>点击右上角</div>
              <div>发送到 微信好友 或者 分享到朋友圈</div>
            </div>
            <div className={styles.icon_box}>
              <i className={styles.icon2}/>
              <i className={styles.icon3}/>
            </div>
          </div>
          <DeviceDetectorByDetector/>

          <ActionSheet
            visible={ModalVisibleByActionSheetShare}
            actions={[
              { text:'分享至微信,企业微信等',key:'wx' },
              { text:'分享海报',key:'poster' }
            ]}
            onClose={() => {
              dispatch({
                type: 'PlanetChatRoom/setState',
                payload: {ModalVisibleByActionSheetShare: false}
              })
            }}
            onAction={(action)=>{
              const { key } = action;
              if (key == 'wx') {
                // 分享微信
                posterModalRef && posterModalRef.current.init(2, SpaceInfoObj)
              }else {
                // 分享海报
                posterModalRef && posterModalRef.current.init(1, SpaceInfoObj)
              }
            }}
          />

          {/* 海报弹窗 */}
          <PosterModal ref={posterModalRef}/>

          {/*引导下载Friday App下载==顶部浮窗*/}
          {(getOperatingEnv() == 2 || getOperatingEnv() == 3) && <div className={styles.DownloadAppCardBody}><DownloadAppCard info={{roomId:SpaceInfo?.id,type:1}}/></div>}
        </div>
      </Spin>
    </>
  )
}
export default connect(({ tim,userInfoStore,PlanetChatRoom,loading }: any) => ({tim,userInfoStore,PlanetChatRoom,loading}))(Index)

