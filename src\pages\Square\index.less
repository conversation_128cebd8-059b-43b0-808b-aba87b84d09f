.fixed_nav_bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #fff;
  z-index: 990;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: space-between;
  padding-right: 12px;
  &.recommend {
    background: #F5F6F8;
  }
  &.pc {
    max-width: 750px;
    margin: 0 auto;
  }
  .left {
    flex: 1;
    display: flex;
    white-space: nowrap;
    align-items: center;
    overflow-x: auto;
    &.in_jws {
      padding-left: 30px;
    }
    .tabs_item {
      position: relative;
      font-size: 16px;
      color: #000;
      line-height: 22px;
      padding: 16px 0;
      margin-left: 16px;

      &.checked {
        font-size: 18px;
        font-weight: 600;

        &::after {
          content: "";
          display: block;
          width: 12px;
          height: 3px;
          background: #000;
          position: absolute;
          bottom: 10px;
          left: 50%;
          transform: translateX(-50%);
          border-radius: 6px;
        }
      }
    }
    // app中返回按钮
    .back_icon {
      width: 48px;
      height: 54px;
      background: url("../../assets/GlobalImg/go_back.png") no-repeat center;
      background-size: 12px 24px;
    }
  }
  .right {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    & > i {
      width: 32px;
      height: 32px;
      &.icon_search {
        background:rgba(255,255,255,0.5) url("../../assets/GlobalImg/search_icon_2.png") no-repeat center;
        background-size: 20px 20px;
        margin-right: 4px;
        border-radius: 50%;
      }
    }
  }
}

// 新建按钮
.add_btn {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: #0095FF;
  position: fixed;
  z-index: 990;
  bottom: 97px;
  right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:global {
  .ka-wrapper, .ka-content {
    height: 100%;
  }
}

.container {
  padding-bottom: 58px;
  padding-top: 54px;
  background: #F5F6F8;
  height: 100%;
  overflow-y: auto;

  .content {
    background: #fff;
    min-height: 100%;
    .gray_bar {
      background: #F5F6F8;
      height: 4px;
    }
  }

  &.collect_container {
    .content {
      .gray_bar {
        margin-bottom: 12px;
      }
    }
  }
  &.space_container {
    .content {
      background: transparent;
      .gray_bar {
        margin-bottom: 4px;
      }
    }
  }
  &.kingdom_container {
    .content {
      .gray_bar {
        margin-bottom: 12px;
      }
    }
  }

  &.recommend_container {

    .content {
      background: transparent;
    }

  }



  &.in_app_container {
    padding-bottom: 8px;
  }



}
