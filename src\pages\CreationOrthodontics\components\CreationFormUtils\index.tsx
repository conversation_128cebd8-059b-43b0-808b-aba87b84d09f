import React from "react";
import styles from "@/pages/CreationOrthodontics/components/Step2/OrthodonticChecklist.less";
import {Input, Upload, Form, Radio, input} from "antd";
import classNames from "classnames";
import ToothBit from "@/components/ToothBit/ToothBit";
import UploadByImage from "../UploadByImage/index.tsx";
import OtherAccessories from "@/pages/CreationOrthodontics/components/OtherAccessories";
import {getToothBitInfoByToothPosition} from "@/utils/ToothSelect";
const { TextArea } = input;


export const CustomStatusText = () => {
  const { status, errors } = Form.Item.useStatus();
  console.log('status1232123 :: ',status);
  return (
    <div>{status}</div>
  );
};

export const getFieldDecoratorByitemByLv2 = (
  {
    item:itemByLv2 ,
    form,
    onClickToothBit:onClickToothBit,
    onChange:onChange,
    isNotRequired:isNotRequired,
  }
)=>{
  const ArritemByLv3AndOperateType0 = Array.isArray(itemByLv2.subsetList) && itemByLv2.subsetList.filter((itemByLv3) =>{
    return itemByLv3.operateType == 0? itemByLv3 : null
  })
  return (
    <>
      <div className={styles.content_span}>
        <div className={styles.item_span}>
          <div
            style={{
              lineHeight: itemByLv2.operateType == 2 ? '32px' : '25px'
            }}
            className={styles.item_lable}>
            {itemByLv2.operateType == 0 ? `${itemByLv2.dictCode}.` : null}{itemByLv2.dictName}：
          </div>
          <div className={styles.item_value}>

            {/* 操作类型(0无操作、1选中、2输入、3牙位、4图片、5文档) */}
            <div className={styles.item_value_content}>
              {(itemByLv2.operateType == 1 || itemByLv2.operateType == 0) &&
                <>
                      <Form.Item
                        key={`${itemByLv2.id}-status`}
                        label={null}
                        name={`${itemByLv2.id}-status`}
                        style={{width:'100%'}}
                        // noStyle={true}
                        rules={[
                          { required: [54,55].indexOf(itemByLv2.id) == -1 && !isNotRequired , message: `请选择${itemByLv2.dictName}` }
                        ]}
                      >
                        <div className={styles.item_value_content_select}>
                          {Array.isArray(itemByLv2.subsetList) && itemByLv2.subsetList.map((itemByLv3) => {
                            if (itemByLv3.operateType != 3) {
                              return getFieldDecoratorByitemByLv3({
                                item:itemByLv3,
                                form:form,
                                onClickToothBit:onClickToothBit,
                                onChange:onChange,
                              })
                            }
                          })}
                        </div>
                      </Form.Item>
                </>
              }
              {itemByLv2.operateType == 2 &&
                <Form.Item key={itemByLv2.id}>
                  <div className={styles.Item_input}>
                    <Form.Item
                      key={itemByLv2.id}
                      label={null}
                      name={itemByLv2.id}
                      style={{width:'100%'}}
                      noStyle={true}
                      rules={[
                        { required: [262,211,223].indexOf(itemByLv2.id) == -1 && !isNotRequired, message: '请输入内容!!' },
                        { max: 200, message: `${itemByLv2.dictName}最多输入200字符` }
                      ]}
                    >
                      {[263,'firstQuestion-sub-id'].indexOf(itemByLv2.id) != -1 ?
                        <TextArea
                          autoComplete="off"
                          style={{ height: 160 }}
                          placeholder={`请输入${itemByLv2.dictName}...`}
                          onChange={()=>{onChange && onChange()}}
                        /> :
                        <Input autoComplete="off" placeholder={`请输入${itemByLv2.dictName}...`} onChange={()=>{onChange && onChange()}} />
                      }

                    </Form.Item>
                  </div>
                </Form.Item>
              }
            </div>
            <div className={styles.item_value_content}>
              { Array.isArray(itemByLv2.subsetList) && itemByLv2.subsetList.filter((itemByLv3) =>(itemByLv3.operateType == 3)).map((item=>{
                return getFieldDecoratorByitemByLv3({
                  item:item,
                  form:form,
                  onClickToothBit:onClickToothBit,
                  onChange:onChange,
                  isNotRequired:isNotRequired,
                })
              }))}
            </div>
          </div>

        </div>
      </div>
      {Array.isArray(ArritemByLv3AndOperateType0) && ArritemByLv3AndOperateType0.map((item)=>{
        return getFieldDecoratorByitemByLv2({
          item:item,
          form:form,
          onClickToothBit:onClickToothBit,
          isNotRequired:isNotRequired,
        })
      })}
    </>
  )
}

export const getFieldDecoratorByitemByLv3 = (
  { item:itemByLv3,
    form,
    onClickToothBit:onClickToothBit,
    onChange:onChange,
    isNotRequired:isNotRequired,
  }
)=>{
  /**
   * 操作类型(0无操作、1选中、2输入、3牙位、4图片、5文档)
   */
  if(itemByLv3.operateType == 1){
    // 1 选中
    return (
      <Form.Item
        key={itemByLv3.id}
        name={itemByLv3.id}
        noStyle={true}
      >
        <div
          style={{ width: itemByLv3.dictName.length > 4 ? '108px' : '72px' } }
          className={classNames({
            [styles.checkBox]:true,
            [styles.active]: !!Form.useWatch(itemByLv3.id, form),
          })}
          onClick={() => {
            onChange && onChange();
            // 获取当前字段的值
            let value = form.getFieldValue(itemByLv3.id);

            // 如果当前值为假（即未选中），则执行以下操作
            if (!value) {
              // 获取整个表单的值
              let formValues = form.getFieldsValue(true);

              if (formValues) {
                // 过滤出具有相同 parentId 的项
                let ArrByFilter = Object.values(formValues).filter((item) => {
                  if (!!item) {
                    return item.parentId == itemByLv3.parentId;
                  }
                });

                // 将这些项设置为 null，取消选中状态
                Array.isArray(ArrByFilter) &&
                ArrByFilter.map((item) => {
                  form.setFieldsValue({ [item.id]: null });
                });
              }
            }

            // 切换当前项的选中状态
            form.setFieldsValue({
              [itemByLv3.id]: !value ? { ...itemByLv3, isCheck: true } : null,
              [`${itemByLv3.parentId}-status`]: !value ? { ...itemByLv3, isCheck: true } : null,
            });
          }}
        >
          {itemByLv3.dictName}
        </div>
        {itemByLv3.id == 20 && !!Form.useWatch(20,form) &&
          <div className={styles.itemByLv3Id20}>
            <div style={{marginRight:'7px'}}>（ </div>
              <Radio.Group
                className={styles.RadioBox}
                onChange={(e)=>{
                  const  { value } = e.target || {};
                  if (!!value) {
                    let ArrByFilter = [...itemByLv3.subsetList]
                    let itemByArrByFilter = ArrByFilter.find((item) => {
                      return item.id == value;
                    })

                    // 将这些项设置为 null，取消选中状态
                    Array.isArray(ArrByFilter) &&
                    ArrByFilter.map((item) => {
                      form.setFieldsValue({ [item.id]: {...item,isCheck:0} });
                    });
                    form.setFieldsValue({ [value.id]: !itemByArrByFilter ? { ...itemByArrByFilter, isCheck: true } : null });
                    let item4 = itemByLv3.subsetList.find((item)=>{return item.id == value})
                    form.setFieldsValue({
                      [itemByLv3.id]: {
                        ...itemByLv3,
                        isCheck: true,
                        subIsCheckValue: value,
                        subIsCheckValueItem: item4 ? { ...item4,isCheck:true} : null,
                      },
                    });
                  }
                }}
                value={!!form.getFieldValue(itemByLv3.id) && form.getFieldValue(itemByLv3.id)?.subIsCheckValue}
              >
                {itemByLv3.subsetList.map((itemByLv4)=>{
                  return (
                    <div><Radio value={itemByLv4.id}>{itemByLv4.dictName}</Radio></div>
                  )
                })}
              </Radio.Group>
            <div> ）</div>
          </div>
        }
      </Form.Item>
    )
  }else if(itemByLv3.operateType == 2){
    // 2 输入
    return (
      <div  style={ itemByLv3.type == 'oneLine' ? {width:'100%'} : {}} className={styles.Item_title_value_content}>
        { itemByLv3.dictName && <div style={{marginRight:'8px'}}>{itemByLv3.dictName}</div> }
        <div style={ itemByLv3.type == 'oneLine' ? {width:'100%'} : { width:'74px' }}>
          <Form.Item
            key={itemByLv3.id}
            name={itemByLv3.id}
            noStyle={true}
          >
            <Input
              autoComplete="off"
              onChange={()=>{
                onChange && onChange();
              }}
              placeholder={'请输入'}
            />
          </Form.Item>
        </div>
        { itemByLv3.unit &&  <div style={{marginLeft:'8px'}} className={styles.Item_title_span_rigth}>{itemByLv3.unit}</div> }
      </div>
    )
  }else if(itemByLv3.operateType == 3){
    // 3 牙位
    return (
      <div className={styles.ToothBitWarp}>
        <Form.Item
          key={itemByLv3.id}
          name={itemByLv3.id}
          noStyle={true}
        >
          <ToothBit
            ToothBefore={itemByLv3.dictName}
            /*ToothInfo={{
              topLeft:[
                {index:1, side:['M','O','C']},
              ],
              topRight:[],
              tottomLeft:[],
              tottomRight:[
                {index:3, side:['M','C']},
                {index:5, side:['M']}
              ],
            }}*/
            ToothInfo={!!form.getFieldValue(itemByLv3.id) ? form.getFieldValue(itemByLv3.id) : null}
            onClickToothBit={()=>{
              onClickToothBit && onClickToothBit(itemByLv3)
              onChange && onChange();
            }}
          />
        </Form.Item>
      </div>
    )
  }else if(itemByLv3.operateType == 4){
    // 4 图片
    return (
      <div>
        <Form.Item
          key={itemByLv3.id}
          name={itemByLv3.id}
          // noStyle={true}
          rules={[
            { required: [274,275].indexOf(itemByLv3.id) == -1 && !isNotRequired, message: `请上传${itemByLv3.dictName}!` }
          ]}
        >
          <UploadByImage
              fieldContent={itemByLv3}
              fileContent={Form.useWatch(itemByLv3.id, form)}
              onUploadComplete={(fileContent)=>{
                console.log('fileContent 123123 ',fileContent);
                form.setFieldsValue({ [itemByLv3.id]: fileContent });
                onChange && onChange();
              }}
          />
        </Form.Item>
      </div>
    )
  }else if(itemByLv3.operateType == 5){
    // 5 其他附件
    return (
      <Form.Item
        key={itemByLv3.id}
        name={itemByLv3.id}
        noStyle={true}
      >
        <OtherAccessories
          fieldContent={itemByLv3}
          fileContent={Form.useWatch(itemByLv3.id, form)}
          onUploadComplete={(fileContent)=>{
            console.log('fileContent123123 :: ',fileContent);
            form.setFieldsValue({ [itemByLv3.id]: fileContent });
            onChange && onChange();
          }}
        />
      </Form.Item>
    )
  } else {

  }
}

// 根据id找到对应的数据并更新inputContent中的数据 并更新到form表单中
export const updateFormInputContentById = ( obj, id, form ) => {
  if (obj.id == id) {
    // 操作类型(0无操作、1选中、2输入、3牙位、4图片、5文档) operateType;
    const { operateType } = obj || {};

    if(operateType == 1){       // 1选中
      form.setFieldsValue({
        [id]: !!obj.isCheck ? obj : null
      });
    }else if(operateType == 2){ // 2输入
      form.setFieldsValue({
        [id]: obj.inputContent
      });
    }else if(operateType == 3) { // 3牙位
      form.setFieldsValue({
        [id]: obj.toothPosition
      });
    }else if(operateType == 4) { // 4图片
      form.setFieldsValue({
        [id]:obj.fileUrl
      });
      /*if(content) {
        obj.fileName = content.fileName
        obj.fileSize = content.fileSize
        obj.fileSuffix = content.fileSuffix
        obj.fileUrl = content.fileUrl
      }*/
    }else if(operateType == 5) { // 5文档
      form.setFieldsValue({
        [id]:obj.subsetList
      });
      /*if(content) {
        obj.inputContent = content;
        /!*obj.fileName = content.fileName
        obj.fileSize = content.fileSize
        obj.fileSuffix = content.fileSuffix
        obj.fileUrl = content.fileUrl*!/
      }*/
    }
  } else if (obj.subsetList && obj.subsetList.length > 0) {
    for (let i = 0; i < obj.subsetList.length; i++) {
      // updateFormInputContentById(obj.subsetList[i], id, form);
    }
  }
  return obj;
}


// 回显表单数据
export const echoFormValueByForm = (DataBymedicalRecordJson,form)=>{
  if (Array.isArray(DataBymedicalRecordJson)) {
    DataBymedicalRecordJson.forEach((item) => {
      updateFormInputContentById(item, item.id, form);
    })
  }
}


// 根据id找到对应的数据并更新inputContent中的数据 并更新到form表单中
export const setFormValues = (items = [],form,isNotRequired=false) => {
  items.forEach((item) => {
    if (!item) { return; }
    const { operateType, id, isCheck, inputContent,toothPosition, subsetList,parentId } = item;
    if (operateType === 1) {
      if (item.id == 20 && Array.isArray(item.subsetList) && item.subsetList.find((item) => { return item.isCheck == 1 })) {
        let subIsCheckValueItem = item.subsetList.find((item) => { return item.isCheck == 1 });
        item.subIsCheckValue = subIsCheckValueItem.id;
        item.subIsCheckValueItem = subIsCheckValueItem;
      }
      form.setFieldsValue({
        [id]: !!item.isCheck ? { ...item } : null,
      });

      if (item.isCheck) {
        form.setFieldsValue({
          [`${parentId}-status`]: { ...item, isCheck: true}
        });
      }

      const parentSubset = items.find((parentItem) => !!parentItem.isCheck);

      if (!parentSubset && !isNotRequired) {
        const firstChildId = items[0]?.id;
        if (firstChildId) {
          form.setFieldsValue({
            [firstChildId]: {...items[0], isCheck:{...items[0]}}
          });
          form.setFieldsValue({
            [`${parentId}-status`]: { ...items[0], isCheck: true}
          });
        }
      }
    }else if(operateType == 2){ // 2输入
      form.setFieldsValue({
        [id]: item.inputContent
      });
      if (!!item.inputContent) {
        form.setFieldsValue({
          [`${parentId}-status`]: item.inputContent
        });
      }
    }else if(operateType == 3) { // 3牙位
      form.setFieldsValue({
        [id]: toothPosition ? getToothBitInfoByToothPosition(toothPosition):null,
      });
    }else if(operateType == 4) { // 4图片
      if(item.fileUrlShow) {
        form.setFieldsValue({
          [item.id]: {
            fileName: item.fileName,
            fileSize: item.fileSize,
            fileSuffix: item.fileSuffix,
            fileUrl: item.fileUrl,
            fileUrlView:item.fileUrlShow,
          }
        });
      }
    }else if(operateType == 5) { // 5文档
      form.setFieldsValue({
        [item.id]: item.subsetList
      });
    }

    if (Array.isArray(subsetList) && subsetList.length > 0 && item.operateType != 5) {
      setFormValues(subsetList,form,isNotRequired); // Recursively handle subsetList
    }
  });
};

export const getItemsByOperateType = (data, targetType) => {
  let result = [];

  function findItems(item) {
    if(!item){ return }
    if (item.operateType === targetType) {
      result.push(item);
    }

    if (Array.isArray(item.subsetList)) {
      item.subsetList.forEach(subItem => {
        findItems(subItem);
      });
    }
  }

  findItems(data);
  return result;
}

export const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth' // You can also use 'auto' for an instant scroll
  });
};

