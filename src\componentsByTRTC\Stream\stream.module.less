.item {
    display: inline-block;
    padding: 0px;
    width: 100%;
    height: 100%;
    position: relative;
    background: #efefef;
    &-view {
        width: 100%;
        height: 100%;
        video {
            width: 100%;
            height: 100%;
            &::-webkit-media-controls-panel{
                display: none;
            }
        }
        &-exits {
            position: absolute;
            z-index: 10;
            box-sizing: border-box;
            background: rgba(0,0,0,.2);
            opacity: 0;
            padding: 0 20px;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 40px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            &:hover {
                opacity: 1;
            }
        }
    }
    // &:hover {
    //     .item-control{
    //         display: flex;
    //     }
    // }
    .item-control{
        display: block;
    }
    &-control {
        display: none;
        position: absolute !important;
        width: calc(100% - 20px) !important;
        height: calc(100% - 20px) !important;
        top: 10px;
        left: 10px;
    }
    &-play-btn-container {
      width: 100%;
      height: 100%;
    }
    &-play-btn{
      position: absolute;
      width: 100px;
      height: 100px;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      cursor: pointer;
    }
}
