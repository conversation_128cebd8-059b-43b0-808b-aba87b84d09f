.container {
  background: #EEF3F9;
  height: 100vh;
}

.wrap {
  width: 1250px;
  margin: 0 auto;

  .wrap_header {
    padding: 16px 0;
  }

  .content {
    // padding: 16px 24px 0 24px;
    // background: #FFFFFF;
    height: calc(100vh - 220px);
    overflow: auto;
    overflow-x: hidden;
   .tab_case{
    display: flex;
    flex-wrap: wrap;
   }
    .tab_case_list {
      width: 399px;
      height: auto;
      margin-bottom: 16px;
      margin-right: 15px;
      &:nth-child(3n) {
        margin-right: 0;
      }
    }
  }
}
