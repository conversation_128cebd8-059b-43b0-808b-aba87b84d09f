/**
 * @Description: 提交视频会诊成功弹窗
 */
import React, { useRef, useState, useEffect } from 'react'
import classNames from 'classnames'
import { Modal, Carousel, Button } from 'antd'
import styles from './index.less'

import successIcon from '@/assets/GlobalImg/success.png'

interface PropsType {
  visible: boolean,                              // true，false
  onCancel: any,                                 // 关闭弹窗
}

const Index: React.FC<PropsType> = (props: any) => {
  const {
    visible,
    onCancel, // 关闭弹窗
  } = props

  return (
    <Modal
      title="提示"
      className={styles.modal}
      visible={visible}
      onCancel={onCancel}
      width={474}
      footer={null}
      destroyOnClose
    >
      <div className={styles.img_wrap}>
        <img src={successIcon} width={72} height={72} alt=""/>
      </div>
      <div className={styles.title}>提交成功!</div>
      <div className={styles.message}>1个工作日内将有专属客服与您联系。具体费用根据实际指导时长计算，将优先使用免费时长</div>
      <div className={styles.btn_wrap}>
        <Button type="primary" className={styles.btn} onClick={onCancel}>我知道了</Button>
      </div>
    </Modal>
  )
}

export default Index
