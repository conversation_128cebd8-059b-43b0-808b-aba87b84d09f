import React, {useCallback, useEffect, useImperativeHandle, useRef, useState,} from 'react';
import {connect, match, useAliveController, useRouteMatch} from 'umi';
import DanmuJs from 'danmu.js';
import classNames from 'classnames';
import moment from 'moment';
import {userTokenInvalid} from '@/utils/request';
import {getDownLoad, sendDanmuCreateItemDom} from '@/utils/utils';
import {message} from 'antd';
import {Toast} from 'antd-mobile';
import styles from './index.less';
import '../../../components/video-js.min.css';
import 'tcplayer.js/dist/tcplayer.min.css';

import MeetingDetailsModal from '@/pages/PlanetChatRoom/Meet/components/ModalComponents/MeetingDetailsModal';
import LeaveMeetingModal from '@/pages/PlanetChatRoom/Meet/components/ModalComponents/LeaveMeetingModal';
import MeetingSettingModal from '@/pages/PlanetChatRoom/Meet/components/ModalComponents/MeetingSettingModal';
import DesignateHostModal from '@/pages/PlanetChatRoom/Meet/components/ModalComponents/DesignateHostModal';
import MembersModal from '@/pages/PlanetChatRoom/Meet/components/ModalComponents/MembersModal';
import ApplySpakeListModal from '@/pages/PlanetChatRoom/Meet/components/ModalComponents/ApplySpakeListModal';
import ShareFileListModal from '@/pages/PlanetChatRoom/Meet/components/ModalComponents/ShareFileListModal';
import CommonTipsModal from '@/pages/PlanetChatRoom/Meet/components/ModalComponents/CommonTipsModal';
import SignInListModal from '@/pages/PlanetChatRoom/Meet/components/ModalComponents/SignInListModal';
import EnterPasswordModal from '@/pages/PlanetChatRoom/Meet/components/ModalComponents/EnterPasswordModal';

import MeetingDetailsModalByPC
  from '@/pages/PlanetChatRoom/Meet/components/ModalComponentsByPC/MeetingDetailsModalByPC';
import DesignateHostModalByPC from '@/pages/PlanetChatRoom/Meet/components/ModalComponentsByPC/DesignateHostModalByPC';
import MeetingSettingModalByPC
  from '@/pages/PlanetChatRoom/Meet/components/ModalComponentsByPC/MeetingSettingModalByPC';
import ApplySpakeListModalByPC
  from '@/pages/PlanetChatRoom/Meet/components/ModalComponentsByPC/ApplySpakeListModalByPC';
import MembersModalByPC from '@/pages/PlanetChatRoom/Meet/components/ModalComponentsByPC/MembersModalByPC';

import ViewByPortrait from './ViewByPortrait';
import ViewByLandscape from './ViewByLandscape';
import ViewByPC from './ViewByPC';

import DownloadAppBtn from '@/components/DownloadAppBtn';
import GoBackHomeIcon from '@/components/GoBackHome';

import {
  clearLocalStateByStreamById,
  getCameralistArr,
  getHandUpRemoteStreamList,
  getHostRemoteStreamConfig,
  getIsModeMatrixCameraRemoteStreamList,
  getIsNotHasVideo,
  getShareRemoteStreamConfig,
  getUserCameraRemoteStreamList,
  getUserInfoData,
} from '@/utils/utilsByTRTC';

import {
  APPLY_RECORD,
  APPLY_RECORD_RESULT,
  BULLET_SCREEN,
  CAMERA_TOGGLE,
  CAMERA_TOGGLE_RESULT,
  HAND_DOWN,
  HAND_UP,
  MICROPHONE_TOGGLE,
  MICROPHONE_TOGGLE_RESULT,
  NO_PW_APPLY,
  ROOM_DISBAND,
  SEND_CALL,
  SIGN_IN,
  TRANSFER_HOST,
  UPDATA_STATE,
} from '@/app/config';

type propsType = {
  global: any;
  onRefByViewportWrap: any;
  sendMessageByIm: any;
  localStreamConfig: any;
  remoteStreamConfigList: any;
  RTC: any;
  shareRTC: any;
  isJoined: boolean;
  isPublished: boolean;
  handleJoin: any;
  handleLeave: any;
  onChange: any;
  spaceId: any;
  openCloseHandUp: any;
  liveRecord: any;
  getGuestList: any;
  getSpaceInfo: any;
  onClickLianMai: any;
  changeUrlParams: any;
  elapsedTime: any;
  shareOnClick: any;
  onClickBack: any;
  isHorizontalLive: any;
};

const Index: React.FC<propsType> = (props) => {
  const isInIframe = self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya';
  const UserInfo = JSON.parse(localStorage.getItem('userInfo')) || {};
  const {clear} = useAliveController();
  const match = useRouteMatch();

  useImperativeHandle(props.onRefByViewportWrap, () => {
    return {
      resetTimer2,
      sendDanmu,
      handleChangeByLocalStreamConfig,
      openHostOpenYouMikeTipsModal,
      openHostCloseYouMikeTipsModal,
      openHostOpenYouCameraTipsModal,
      openHostCloseYouCameraTipsModal,
      toMuteAllAndTips,
      openApplyRecordTipsModal,
      openGuestRefuseOpenMikeTipsModal,
      openGuestRefuseOpenCameraTipsModal,
      openHostOpenAllMikeTipsModal,
      openGuestToHostTipsModalVisible,
    };
  });

  const {
    localStreamConfig,
    RTC,
    isJoined,
    isPublished,
    remoteStreamConfigList,
    PlanetChatRoom,
    dispatch,
    spaceId,
    openCloseHandUp,
    liveRecord,
    getSpaceInfo,
    handleJoin,
    onClickLianMai,
    operateHandUp,
    elapsedTime,
    shareOnClick,
    onClickBack,
    isHorizontalLive,
    sendMessageByIm,
    handleLeave,
    onChange,
    cleanVal,
    cleanRTC,
    getApplyAdmissionList,
    userActiveJoinSpace,
    getManageMembersInTheMeeting,
  } = props;

  const {
    isMobile,
    SpaceInfo,
    currentUserType,
    topMessageNotifyType,
    lastSeq,
    isNotLogin,
    ModalVisibleByAcceptLienMai,
    ModalVisibleByLeaveMicrophone,
    ModalVisibleBySpaceViolation,
    ModalVisibleBySpaceRemoved,
    ModalVisibleByKickedOut,
    ModalVisibleByNoMicrophone,
    ModalVisibleByAcceptLienMaiNoMicrophone,
    ModalVisibleByShareScreenError,
    ModalVisibleByUserTokenInvalid,
    ModalVisibleByApplicationSubmitted,
    isOpenTEduBoard,
    applyRecordTipsModalVisible,
    applyRecordTipsModalObj,
    guestToHostTipsModalVisible,
    hostOpenAllMikeTipsModalVisible,
    hostOpenYouMikeTipsModalVisible,
    hostOpenYouCameraTipsModalVisible,
    hostCloseYouMikeTipsModalVisible,
    hostCloseYouCameraTipsModalVisible,
    hostRemoveYouTipsModalVisible,
    guestRefuseOpenMikeTipsModalVisible,
    guestRefuseOpenCameraTipsModalVisible,
    NotAgainApplyRecordTipsModal,
    currentWatchMode,
  } = PlanetChatRoom || {};

  const {
    wxUserId,
    imUserId,
    hostUserInfo,
    status: statusBySpaceInfo,
    isSignIn,
    isCollect,
    handUpList,
    handUpType,
    recordType,
    handUpStatusType,
    videoList,
    liveStartTime,
  } = SpaceInfo || {};

  const viewRef = useRef(null);

  const [meetingDetailsModalVisible, setMeetingDetailsModalVisible] = useState(false); // 会议详情弹窗
  const [leaveMeetingModalVisible, setLeaveMeetingModalVisible] = useState(false); // 结束弹窗是否展示
  const [meetingSettingModalVisible, setMeetingSettingModalVisible] = useState(false); // 会议设置弹窗
  const [membersModalVisible, setMembersModalVisible] = useState(false); // 管理成员弹窗
  const [applySpakeListModalVisible, setApplySpakeListModalVisible] = useState(false); // 申请发言弹窗
  const [shareFileListModalVisible, setShareFileListModalVisible] = useState(false); // 分享课件list弹窗
  const [designateHostModalVisible, setDesignateHostModalVisible] = useState(false); // 指定主持人弹窗
  const [signInListModalVisible, setSignInListModalVisible] = useState(false); // 打卡详情弹窗
  const [endMeetingTipsModalVisible, setEndMeetingTipsModalVisible] = useState(false); // 结束会议tips弹窗
  const [endRecordTipsModalVisible, setEndRecordTipsModalVisible] = useState(false); // 结束录制tips弹窗
  const [forceMikeOffTipsModalVisible, setForceMikeOffTipsModalVisible] = useState(false); // 强制下麦二次确认tips弹窗
  const [muteAllTipsModalVisible, setMuteAllTipsModalVisible] = useState(false); // tips弹窗（主持人点击全体静音）
  const [membersRemoveTipsModalVisible, setMembersRemoveTipsModalVisible] = useState(null); // tips弹窗（主持人将成员移出会议）
  const [spectatorJoinTipsModalVisible, setSpectatorJoinTipsModalVisible] = useState(null); // tips弹窗（非参会人进入）
  const [applyHostRecordTipsModalVisible, setApplyHostRecordTipsModalVisible] = useState(null); // tips弹窗（请求主持人开启录制）

  // PC弹窗
  const [meetingDetailsModalByPCVisible, setMeetingDetailsModalByPCVisible] = useState(false); // 会议详情弹窗（PC端）
  const [designateHostModalByPCVisible, setDesignateHostModalByPCVisible] = useState(false); // 指定主持人弹窗（PC端）
  const [meetingSettingModalByPCVisible, setMeetingSettingModalByPCVisible] = useState(false); // 会议设置弹窗（PC端）
  const [applySpakeListModalByPCVisible, setApplySpakeListModalByPCVisible] = useState(false); // 申请发言弹窗（PC端）
  const [membersModalByPCVisible, setMembersModalByPCVisible] = useState(false); // 管理成员弹窗（PC端）

  // 其他数据
  const [RTCDanmu, setRTCDanmu] = useState(null); // RTCDanmu
  const [isHiddenControlArea, setIsHiddenControlArea] = useState(false); // isHiddenControlArea 是否隐藏控制区域
  const [timer, setTimer] = useState(null); // 5秒自动隐藏控制区域计时器
  const [membersModalTabKey, setMembersModalTabKey] = useState(1); // 管理成员弹窗-默认tab
  const [isCommentAndDanmuOpen, setIsCommentAndDanmuOpen] = useState(true); // 弹幕和评论区域开关
  const [handUpStatusTypeEnter, setHandUpStatusTypeEnter] = useState(false); // handUpStatusTypeEnter 确认下麦或确认取消连麦
  const [timer2, setTimer2] = useState(null); // 3秒隐藏顶部消息通知计时器
  const [hasMoreByMsgList, setHasMoreByMsgList] = useState(true); // 打卡消息列表是否还有更多
  const [commentListIsInBottomNow, setCommentListIsInBottomNow] = useState(true); // 当前评论列表是否滚动到了底部
  const [meetingDurationTime, setMeetingDurationTime] = useState(true); // 会议已进行时间
  const [loadingCheckSpacePassword, setLoadingCheckSpacePassword] = useState(null); // 空间密码校验的请求是否进行中
  const [loadingCheckStream, setLoadingCheckStream] = useState(null); // 空间密码校验的请求是否进行中

  const userInfoData = getUserInfoData();

  const shareRemoteStreamConfig = getShareRemoteStreamConfig(SpaceInfo, remoteStreamConfigList);

  const hostRemoteStreamConfig = getHostRemoteStreamConfig(
    SpaceInfo,
    hostUserInfo,
    remoteStreamConfigList,
  );

  const userCameraRemoteStreamList = getUserCameraRemoteStreamList(
    SpaceInfo,
    remoteStreamConfigList,
  );

  const isModeMatrixCameraRemoteStreamList = getIsModeMatrixCameraRemoteStreamList(
    SpaceInfo,
    hostUserInfo,
    handUpList,
    userCameraRemoteStreamList,
  );

  const handUpRemoteStreamList = getHandUpRemoteStreamList(
    SpaceInfo,
    handUpList,
    userCameraRemoteStreamList,
  );

  let cameralistArr = getCameralistArr(
    hostRemoteStreamConfig,
    localStreamConfig,
    handUpRemoteStreamList,
    isModeMatrixCameraRemoteStreamList,
  );

  const isNotHasVideo = getIsNotHasVideo({localStreamConfig, userCameraRemoteStreamList});
  const isNotVideoOrScreen =
    (isNotHasVideo == null || isNotHasVideo == true) && !shareRemoteStreamConfig;

  useEffect(() => {
    // 初始化弹幕
    initializationByDanmu();
    // 定时器重置，（横屏）顶部导航栏和底部控制区显隐定时器
    resetTimer();
  }, []);

  // 会议已进行时间
  useEffect(() => {
    if (!liveStartTime) {
      return;
    }
    const timer = setInterval(() => {
      const currentTime = moment();
      let IntervalSeconds = currentTime.diff(
        moment(liveStartTime, 'YYYY-MM-DD HH:mm:ss'),
        'seconds',
      );
      setMeetingDurationTime(IntervalSeconds);
    }, 1000);
    return () => {
      clearInterval(timer);
    };
  }, [liveStartTime]);

  // 接收申请连麦转换身份
  useEffect(async () => {
    if (RTC && statusBySpaceInfo == 1 && currentUserType == 3) {
      setHandUpStatusTypeEnter(false);
    }
  }, [handUpStatusType]);

  // 滚动弹幕处理，投屏或分享课件模式下显示，其他情况不显示
  useEffect(() => {
    if (!isCommentAndDanmuOpen) {
      RTCDanmu && RTCDanmu.stop();
      return;
    }
    if (!!shareRemoteStreamConfig) {
      RTCDanmu && RTCDanmu.start();
    } else {
      RTCDanmu && RTCDanmu.stop();
    }
  }, [!!shareRemoteStreamConfig, isCommentAndDanmuOpen]);

  // 初始化弹幕组件
  const initializationByDanmu = async () => {
    let playerByMS = document.getElementById('ms');

    let danmu = new DanmuJs({
      channelSize: 24, // 轨道大小
      container: document.querySelector('#vs'), //弹幕容器，该容器发生尺寸变化时会自动调整弹幕行为
      containerStyle: {
        //弹幕容器样式
        // zIndex: 1001
      },
      live: true,
      player: playerByMS, // 配合音视频元素（video或audio）同步使用时需提供该项
      isLive: true,
      // direction,
      comments: [], // 弹幕预存数组,配合音视频元素（video或audio）同步使用时需提供该项
      chaseEffect: false, // 开启滚动弹幕追逐效果, 默认为true
      mouseControl: true, // 打开鼠标控制, 打开后可监听到 bullet_hover 事件。danmu.on('bullet_hover', function (data) {})
      mouseControlPause: true, // 鼠标触摸暂停。mouseControl: true 生效
      // channelSize: 24,       // 轨道大小
      area: {
        // 弹幕显示区域
        start: 0, // 区域顶部到播放器顶部所占播放器高度的比例
        end: 1, // 区域底部到播放器顶部所占播放器高度的比例
      },
      dropStaleComments: true,
    });
    danmu.stop();
    setRTCDanmu(danmu); // 存储danmu控制对象
  };

  // 创建新Danmu text:弹幕内容, level:弹幕等级 1:普通弹幕 2:关注弹幕 3:高级弹幕
  const sendDanmu = ({text, userInfoByDanmu}) => {
    if (!isCommentAndDanmuOpen || (RTCDanmu && RTCDanmu.status == 'closed')) {
      return;
    }
    // 弹幕Dom
    let elByDanmu = sendDanmuCreateItemDom({text, userInfoByDanmu});
    let idByNewDanmu = moment().format('YYYYMMDDHHmmss');
    RTCDanmu &&
    RTCDanmu.sendComment({
      duration: 10000,
      start: 100,
      id: parseInt(`${idByNewDanmu}${Math.floor(Math.random() * 10000)}`),
      el: elByDanmu, // 弹幕文字内容
      style: {
        fontSize: '20px',
      },
    });
  };

  // 显示顶部导航栏和底部控制区域
  const handleComplete = () => {
    setIsHiddenControlArea(true);
  };

  // 定时器重置
  const resetTimer = () => {
    clearTimeout(timer);
    setTimer(setTimeout(handleComplete, 5000));
  };

  // 点击视频流区域，显示控制区域
  const onClickContent = () => {
    if (!!isHiddenControlArea) {
      setIsHiddenControlArea(false);
      resetTimer();
    } else {
      // 当打开控制区域时则隐藏
      setIsHiddenControlArea(true);
    }
  };

  // 点击顶部导航栏返回箭头icon
  const onClickBackIcon = () => {
    resetTimer();
    onClickBack();
  };

  // 点击顶部导航栏标题，打开会议详情弹窗
  const onClickNavBarTitle = () => {
    resetTimer();
    setMeetingDetailsModalVisible(true);
  };

  // 关闭会议详情弹窗
  const meetingDetailsModalClose = () => {
    setMeetingDetailsModalVisible(false);
  };

  // 点击顶部导航栏结束按钮，打开结束弹窗
  const onClickEndBtn = () => {
    resetTimer();
    if (currentUserType == 1) {
      setLeaveMeetingModalVisible(true);
    } else {
      // 非主持人离开会议
      handleLeave();
      cleanVal();
      cleanRTC();
      dispatch({type: 'PlanetChatRoom/clean'});
      onClickBack();
    }
  };

  // 关闭结束弹窗
  const leaveMeetingModalClose = () => {
    setLeaveMeetingModalVisible(false);
  };

  // 点击离开会议选项
  const onClickLeaveMeeting = () => {
    setLeaveMeetingModalVisible(false);
    // 打开指定主持人弹窗
    setDesignateHostModalVisible(true);
  };

  // 关闭指定主持人弹窗
  const designateHostModalClose = () => {
    setDesignateHostModalVisible(false);
  };

  // 指定主持人弹窗-指定并离开事件
  const designateHostModalOnOk = async (checkedUserId, checkedImUserId) => {
    const res = await dispatch({
      type: 'PlanetChatRoom/getMeetingTransferCompereUser',
      payload: {
        spaceId: SpaceInfo.id, // 会议ID
        transferUserId: checkedUserId, // 离开会议移交主持人用户ID，非正常离开时可不传
      },
    });
    const {code, content, msg} = res || {};
    if (code == 200 && content) {
      await handleLeave(); // 退出房间
      await sendMessageByIm({
        dataType: TRANSFER_HOST,
        description: JSON.stringify({imUserId: checkedImUserId}),
      });
      await setDesignateHostModalVisible(false);
      await cleanVal();
      await cleanRTC();
      await onClickBack();
    } else {
      message.error(msg || '指定主持人失败');
    }
  };

  // 点击结束会议选项
  const onClickEndMeeting = () => {
    setLeaveMeetingModalVisible(false);
    setDesignateHostModalVisible(false);
    setDesignateHostModalByPCVisible(false);
    // 打开结束会议tips提示弹窗
    setEndMeetingTipsModalVisible(true);
  };

  // 结束会议tips，我再想想
  const endMeetingTipsModalClose = () => {
    setEndMeetingTipsModalVisible(false);
  };

  // 结束会议tips，确认结束
  const endMeetingTipsModalOnLeft = async () => {
    await handleLeave();
    await sendMessageByIm({
      dataType: ROOM_DISBAND, // 通知消息发送房间被解散
      description: JSON.stringify({imUserId: 'ALL'}),
    });
    // 关闭空间
    const res = await dispatch({
      type: 'PlanetChatRoom/startEndLive',
      payload: {
        spaceId: match?.params?.RoomId,
        wxUserId: wxUserId,
        liveType: 2, // liveType 1开始直播 2结束直播
      },
    });
    const {code, content, msg} = res || {};
    if (code == 200) {
      message.success(`关闭会议`);
      clearLocalStateByStreamById(match?.params?.RoomId); // 清空本地存储的状态
      await cleanRTC();
      await cleanVal();
      await sendMessageByIm({
        dataType: UPDATA_STATE,
        description: JSON.stringify({imUserId: 'ALL'}),
      });
      await setEndMeetingTipsModalVisible(false);
      clear().then(() => {
        onClickBack();
      });
    } else {
      message.warning(msg ? msg : '关闭失败');
    }
  };

  // 聊天输入框按下回车，发言
  const onEnterPressCommentInput = (val) => {
    setCommentListIsInBottomNow(true); // 用于聊天区list自动滚动到底部的逻辑
    const value = val.trim();
    // 发送信息为空
    if (!value) {
      viewRef?.current?.resetCommentInput(); // 清空输入框
      return;
    }

    // 发送弹幕消息
    sendMessageByIm({
      dataType: BULLET_SCREEN,
      description: value,
      relativeTime: null,
    });
    viewRef?.current?.resetCommentInput(); // 清空输入框
  };

  // 点击弹幕开关icon
  const onClickDanmuIcon = () => {
    setIsCommentAndDanmuOpen(!isCommentAndDanmuOpen);
    if (!isCommentAndDanmuOpen) {
      message.success('已打开弹幕');
    } else {
      message.success('已关闭弹幕');
    }
  };

  // 聊天区域列表滚动事件，加载更多数据
  const onScrollCommentList = async (e) => {
    const { target } = e || {};
    const { scrollTop, scrollHeight, offsetHeight } = target || {};
    // 用于聊天区list自动滚动到底部的逻辑
    if (scrollHeight - (scrollTop + offsetHeight) < 10) {
      // 表示滚到底部了
      setCommentListIsInBottomNow(true);
    } else {
      setCommentListIsInBottomNow(false);
    }
    // 加载数据
    if (scrollTop == 0) {
      if (!hasMoreByMsgList) {
        return;
      }
      const dataByGetSignInList = await dispatch({
        type: 'PlanetChatRoom/getSpaceGroupMsg',
        payload: {
          spaceId: spaceId,
          msgSeq: lastSeq,
          pageSize: 10,
        },
      });
      const {content} = dataByGetSignInList || {};
      const {resultList} = content || {};
      setHasMoreByMsgList(Array.isArray(resultList) && resultList.length > 0);
    }
  };

  // 点击打卡按钮
  const onClickSignInBtn = async () => {
    if (isSignIn == 1 || currentUserType == 1) {
      // 打开打卡详情弹窗
      setSignInListModalVisible(true);
    } else {
      setCommentListIsInBottomNow(true); // 用于聊天区list自动滚动到底部的逻辑
      sendMessageByIm({dataType: SIGN_IN, description: '1'});
      dispatch({
        type: 'PlanetChatRoom/setState',
        payload: {
          SpaceInfo: {
            ...SpaceInfo,
            isSignIn: 1,
          },
        },
      });
    }
  };

  // 关闭打卡详情弹窗
  const signInListModalClose = () => {
    setSignInListModalVisible(false);
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {
        signInList: null,
        signInObj: null,
      },
    });
  };

  // 点击收藏按钮
  const onClickCollectBtn = async () => {
    const wxUserId = UserInfo && UserInfo.friUserId;
    await dispatch({
      type: 'PlanetChatRoom/spaceCollect',
      payload: {
        spaceId: SpaceInfo.id,
        wxUserId: wxUserId,
        collectType: isCollect == 1 ? 0 : 1, // 空间收藏 0:取消收藏 1:收藏
      },
    });
  };

  // 点击打call按钮事件
  const onClickCallBtn = () => {
    //[判定登录] 判定是否已登录,未登录则弹出登录弹窗
    if (isNotLogin) {
      dispatch({
        type: 'PlanetChatRoom/setState',
        payload: {ModalVisibleByUserTokenInvalid: true},
      });
    }
    setCommentListIsInBottomNow(true); // 用于聊天区list自动滚动到底部的逻辑
    sendMessageByIm({dataType: SEND_CALL, description: '1'});
  };

  // 点击申请发言按钮事件（观众）
  const onClickApplySpeakBtn = () => {
    resetTimer();
    if (handUpType != 1) {
      return;
    }
    if (handUpStatusType == 0) {
      // 当前是申请连麦状态
      if (!handUpStatusTypeEnter) {
        // 第一次点击取消连麦 提示吐司
        setHandUpStatusTypeEnter(true);
        message.warning('再次点击取消发言申请');
      } else {
        // 第二次点击取消连麦
        onClickLianMai();
        setHandUpStatusTypeEnter(false);
      }
    } else if (handUpStatusType == 1) {
      // 当前是连麦状态
      // if (!handUpStatusTypeEnter) {
      //   // 第一次点连麦中 提示吐司
      //   setHandUpStatusTypeEnter(true);
      //   message.warning('再次点击下麦');
      // } else {
      //   // 第二次点击下麦
      //   onClickLianMai();
      //   // setHandUpStatusTypeEnter(false)
      // }
    } else {
      onClickLianMai();
      setHandUpStatusTypeEnter(false);
    }
  };

  // onChange 处理本地流 streamBar 的响应逻辑
  const handleChangeByLocalStreamConfig = (name, e) => {
    e && e.preventDefault();
    onChange({name, stream: localStreamConfig.stream});
  };

  // 点击底部麦克风按钮
  const onClickMikeBtn = () => {
    resetTimer();
    handleChangeByLocalStreamConfig('audio', null);
  };

  // 点击底部摄像头按钮
  const onClickCameraBtn = () => {
    resetTimer();
    handleChangeByLocalStreamConfig('video', null);
  };

  // 点击底部分享课件按钮
  const onClickShareFileBtn = async () => {
    resetTimer();
    // todo...分享课件
    // message.info('todo...分享课件')
    if (isOpenTEduBoard) {
      await stopWhiteboardPush();
      await dispatch({
        type: 'PlanetChatRoom/setState',
        payload: {isOpenTEduBoard: !isOpenTEduBoard},
      });
    } else {
      if (!!shareRemoteStreamConfig) {
        if (currentUserType == 1) {
          message.warning('嘉宾正在分享课件，此时无法发起课件分享');
        } else {
          message.warning('他人正在分享课件，此时无法发起课件分享');
        }
      } else {
        if (!!localStreamConfig.shareDesk) {
          // 当前本地开启了分享投屏
          await handleChangeByLocalStreamConfig('shareDesk', null);
        }
        await dispatch({
          type: 'PlanetChatRoom/setState',
          payload: {isOpenTEduBoard: !isOpenTEduBoard},
        });
      }
    }
  };
  // 停止白板分享
  const stopWhiteboardPush = async () => {
    let res = await dispatch({
      type: 'PlanetChatRoom/stopWhiteboardPush',
      payload: {spaceId: SpaceInfo.id},
    });
    res && res.code == 200 && message.success('停止分享课件成功');
  };

  // 投屏的按钮
  const onClickShareScreenBtn = () => {
    resetTimer();
    // 投屏
    if (isMobile) {
      message.info('手机端暂不支持投屏，请在电脑端使用');
      return;
    }
    handleChangeByLocalStreamConfig('shareDesk', null);
  };

  // 点击聊天弹窗中的发送按钮

  // 点击底部成员按钮
  const onClickMembersBtn = () => {
    resetTimer();
    setMembersModalTabKey(1); // 默认tab签，1 表示会议中，2 表示申请进入
    setMembersModalVisible(true); // 打开成员弹窗
  };

  // 关闭成员弹窗
  const membersModalClose = () => {
    setMembersModalVisible(false);
  };

  // 点击成员弹窗中的邀请按钮
  const onClickInviteBtn = () => {
    setMembersModalVisible(false);
    shareOnClick();
  };

  // 点击成员弹窗中摄像头icon（主持人、参会人）
  const onClickMembersCameraIcon = async (item) => {
    if ((currentUserType == 1 || currentUserType == 2) && item.isSelf == 1) {
      // 主持人/参会人操作自己摄像头
      await handleChangeByLocalStreamConfig('video', null);
      return;
    }
    if (currentUserType == 1) {
      /**
       * headUrlShow: "https://s1-test.5i5ya.com/b535fae63abcbe4a2f73e655a6bef070/665d3869/dmp/userHead/e51cc94ed3e147febc098a56712321af.jpeg?x-oss-process=image/resize,p_40/quality,q_50"
       * imUserId: "bdab391ca18148bdb55874819dbd47cd"
       * isFocus: 0
       * isKing: null
       * isSelf: 0
       * meetingType: 2
       * name: "建光"
       * organizationName: null
       * postTitleDictName: null
       * userEnterDate: "2024-05-22 11:40:04"
       * wxUserId: 1049
       */
      // 主持人操作成员摄像头
      await sendMessageByIm({
        dataType: CAMERA_TOGGLE,
        description: JSON.stringify(item),
      });
      // 主持人提示
      if (!item.mutedVideo) {
        message.success('已关闭该参会人视频');
      }
    }
  };

  // 点击成员弹窗中麦克风icon（主持人、参会人）
  const onClickMembersMikeIcon = (item) => {
    if ((currentUserType == 1 || currentUserType == 2) && item.isSelf == 1) {
      // 主持人/参会人操作自己麦克风
      handleChangeByLocalStreamConfig('audio', null);
      return;
    }
    if (currentUserType == 1) {
      // 主持人操作成员麦克风
      sendMessageByIm({
        dataType: MICROPHONE_TOGGLE,
        description: JSON.stringify(item),
      });
      // 主持人提示
      if (!item.mutedAudio) {
        message.success('已将该参会人静音');
      }
    }
  };

  // 点击成员弹窗中移出会议按钮，打开二次确认弹窗（主持人）
  const onClickMembersRemoveBtn = (item) => {
    setMembersRemoveTipsModalVisible(item);
  };

  // 主持人将成员移出会议二次确认，取消
  const membersRemoveTipsModalClose = () => {
    setMembersRemoveTipsModalVisible(null);
  };

  // 主持人将成员移出会议二次确认，确认
  const membersRemoveTipsModalOnRight = async () => {
    Toast.show({icon: 'loading', duration: 0, maskClickable: false}); // 加loading
    const res = await dispatch({
      type: 'PlanetChatRoom/getMeetingRemoveUser',
      payload: {
        spaceId: SpaceInfo.id, // 会议ID
        removeUserId: membersRemoveTipsModalVisible.wxUserId, // 移出会议用户ID
      },
    });
    Toast.clear();
    const {code, content, msg} = res || {};
    if (code == 200 && content) {
      message.success('移出成功');
      setMembersRemoveTipsModalVisible(null);
      await sendMessageByIm({
        dataType: UPDATA_STATE,
        description: JSON.stringify({imUserId: 'ALL'}),
      });
      await getManageMembersInTheMeeting();
    } else {
      setMembersRemoveTipsModalVisible(null);
      message.error(msg || '移出失败');
    }
  };

  // 拒绝或准入申请进入会议成员（主持人）
  const updateStarSpaceApplyAdmission = async (params) => {
    const {
      wxUserId: remoteWxUserId,
      isAgree, // 是否同意：0申请中,1同意，2拒绝
      refuseAdmittance, // 操作类型：1拒绝、2准入
    } = params || {};
    Toast.show({icon: 'loading', duration: 0, maskClickable: false}); // 加loading
    const res = await dispatch({
      type: 'PlanetChatRoom/updateStarSpaceApplyAdmission',
      payload: {
        spaceId: SpaceInfo.id, //  是 会议ID
        applyAdmissionId: remoteWxUserId, //	是 1 申请记录ID
        refuseAdmittance: refuseAdmittance, //	是 1 操作类型：1:拒绝、2:准入
      },
    });
    Toast.clear();
    const {code, content, msg} = res || {};
    if (code == 200 && content) {
      message.success(refuseAdmittance == 1 ? '移除成功' : '准入成功');
      await getApplyAdmissionList();
      await sendMessageByIm({
        dataType: UPDATA_STATE,
        description: JSON.stringify({ imUserId: 'ALL' }),
      });
    } else {
      message.error(msg || '操作失败');
    }
  };

  // 点击成员弹窗中移除按钮（主持人）
  const onClickMembersRefuseBtn = (item) => {
    updateStarSpaceApplyAdmission({
      wxUserId: item.id,
      refuseAdmittance: 1, // 操作类型：1拒绝、2准入
    });
  };

  // 点击成员弹窗中准入按钮（主持人）
  const onClickMembersAgreeBtn = (item) => {
    updateStarSpaceApplyAdmission({
      wxUserId: item.id,
      refuseAdmittance: 2, // 操作类型：1拒绝、2准入
    });
  };

  // 点击管理成员弹窗全体静音按钮，打开二次确认弹窗（主持人）
  const onClickMembersMuteAllBtn = () => {
    setMuteAllTipsModalVisible(true);
  };

  // 全体静音按钮二次确认弹窗，取消
  const muteAllTipsModalClose = () => {
    setMuteAllTipsModalVisible(false);
  };

  // 全体静音按钮二次确认弹窗，全体静音
  const muteAllTipsModalOnRight = async () => {
    let resByGetMeetingMuteAll = await dispatch({
      type: 'PlanetChatRoom/getMeetingMuteAll',
      payload: {
        spaceId: SpaceInfo.id, //  是 会议ID
        muteType: 1, //	操作类型：1:全体静音 0：全体解除静音
      },
    });
    const {code, content} = resByGetMeetingMuteAll || {};
    if (code == 200) {
      sendMessageByIm({
        dataType: MICROPHONE_TOGGLE,
        description: JSON.stringify({
          imUserId: 'ALL',
          mutedAudio: false, // 开启全体静音
        }),
      });
      message.success('开启全体静音');
      await getSpaceInfo();
      // 关闭静音弹窗 // muteAllTipsModalVisible
      setMuteAllTipsModalVisible(false);
    }
  };

  // 执行全体静音
  const toMuteAllAndTips = () => {
    if (localStreamConfig && currentUserType != 1) {
      if (!!localStreamConfig.mutedAudio) {
        // 当前用户已经静音
        message.info('主持人已开启全体静音');
      } else {
        // 全体静音
        handleChangeByLocalStreamConfig('audio', null);
        // 当前用户已经静音
        message.info('主持人已开启全体静音');
      }
    }
  };

  // 点击管理成员弹窗解除全体静音按钮（主持人）
  const onClickMembersUnmuteAllBtn = async () => {
    let resByGetMeetingMuteAll = await dispatch({
      type: 'PlanetChatRoom/getMeetingMuteAll',
      payload: {
        spaceId: SpaceInfo.id, //  是 会议ID
        muteType: 0, // 操作类型：1:全体静音 0：全体解除静音
      },
    });
    const {code, content} = resByGetMeetingMuteAll || {};
    if (code == 200) {
      // 解除全体静音
      await sendMessageByIm({
        dataType: MICROPHONE_TOGGLE,
        description: JSON.stringify({
          imUserId: 'ALL',
          mutedAudio: true, // 开启全体静音
        }),
      });
      // 同时刷新自己的空间状态
      await getSpaceInfo();
      await message.success('已请求全体用户解除静音');
    }
  };

  // 点击成员弹窗中静音、解除静音按钮（参会人）
  const onClickMembersMuteOrUnmuteBtn = () => {
    handleChangeByLocalStreamConfig('audio', null);
  };

  // 点击底部录制按钮（所有人）
  const onClickRecordBtn = () => {
    resetTimer();
    if (recordType == 1) {
      if (currentUserType == 1) {
        // 打开结束录制提示弹窗
        setEndRecordTipsModalVisible(true);
      } else {
        message.info('正在录制中，只有主持人可以结束录制');
      }
    } else {
      if (currentUserType == 1) {
        liveRecord({recordType: 1});
      } else {
        setApplyHostRecordTipsModalVisible(true);
      }
    }
  };

  // （tips弹窗）申请主持人开启录制，取消
  const applyHostRecordTipsModalOnLeft = () => {
    setApplyHostRecordTipsModalVisible(false);
  };

  // （tips弹窗）申请主持人开启录制，发送请求
  const applyHostRecordTipsModalOnRight = () => {
    setApplyHostRecordTipsModalVisible(false);
    // 非主持人，申请录制,点击申请录制
    sendMessageByIm({
      dataType: APPLY_RECORD,
      description: JSON.stringify({
        imUserId: imUserId,
        ...userInfoData,
      }),
    });
    message.success('已向主持人申请录制');
  };

  // 结束录制二次确认弹窗，我再想想（主持人）
  const endRecordTipsModalClose = () => {
    setEndRecordTipsModalVisible(false);
  };

  // 结束录制二次确认弹窗，确认结束（主持人）
  const endRecordTipsModalOnOk = async () => {
    let res = await liveRecord({recordType: 2});
    if (res && res.code == 200) {
      message.success('结束录制成功');
      sendMessageByIm({
        dataType: UPDATA_STATE,
        description: JSON.stringify({
          imUserId: 'ALL',
        }),
      });
      await setEndRecordTipsModalVisible(false);
    } else {
      message.error(res && res.msg ? res.msg : '结束录制失败');
      await setEndRecordTipsModalVisible(false);
    }
  };

  // 点击底部更多设置按钮（主持人）
  const onClickMoreSettingBtn = () => {
    resetTimer();
    setMeetingSettingModalVisible(true);
  };

  // 关闭更多设置弹窗（主持人）
  const meetingSettingModalClose = () => {
    setMeetingSettingModalVisible(false);
  };

  // 修改不允许非参会人进入开关（主持人）
  const onChangeIsSpectatorUse = async (checked) => {
    const res = await dispatch({
      type: 'PlanetChatRoom/spaceInsideSetting',
      payload: {
        spaceId: SpaceInfo.id, // 会议ID
        isSpectatorUse: checked ? 0 : 1, // 是否允许非参会人进入 1 是 0否(页面中1为关0为开)
      },
    });
    const {code, content, msg} = res || {};
    if (code == 200 && content) {
      await sendMessageByIm({
        dataType: UPDATA_STATE,
        description: JSON.stringify({imUserId: 'ALL'}),
      });
    } else {
      message.error(msg || '操作失败');
    }
  };

  // 修改是否允许发言开关（主持人）
  const onChangeHandUpType = async (checked) => {
    await openCloseHandUp({handUpType: checked ? 1 : 2});
  };

  // 修改是否展示密码开关（主持人）
  const onChangeIsShowPassword = async (checked) => {
    const res = await dispatch({
      type: 'PlanetChatRoom/spaceInsideSetting',
      payload: {
        spaceId: SpaceInfo.id, // 会议ID
        isShowPassword: checked ? 1 : 0, // 是否展示密码 1是 0否
      },
    });
    const {code, content, msg} = res || {};
    if (code == 200 && content) {
      await sendMessageByIm({
        dataType: UPDATA_STATE,
        description: JSON.stringify({imUserId: 'ALL'}),
      });
    } else {
      message.error(msg || '操作失败');
    }
  };

  // 修改小窗播放开关（主持人）
  const onChangeIsOpenSmallWindow = () => {
    setMeetingSettingModalVisible(false);
    openOrCloseH5SmallWindow();
    // onClickBack()
  };

  // 点击底部小窗播放按钮（参会人、观众）
  const onClickShowSmallWindowBtn = () => {
    // onClickBack()
    openOrCloseH5SmallWindow();
  };

  // 开启调用h5小窗口播放
  const openOrCloseH5SmallWindow = async () => {
    // const video = playerRefByHorizontalLiveRoom && playerRefByHorizontalLiveRoom.current;
    let video = null;
    if (statusBySpaceInfo == 1) {
      if (!!shareRemoteStreamConfig) {
        let Share_Remote_Stream_Box = shareRemoteStreamConfig.stream.div_;
        video = Share_Remote_Stream_Box?.querySelector('video');
      } else if (!!hostRemoteStreamConfig) {
        let host_Remote_Stream_Box = hostRemoteStreamConfig.stream.div_;
        video = host_Remote_Stream_Box?.querySelector('video');
      } else if (currentUserType == 1 && !!localStreamConfig) {
        let local_Stream_Config = localStreamConfig.stream.div_;
        video = local_Stream_Config?.querySelector('video');
      }
    } else if (statusBySpaceInfo == 3) {
      if (Array.isArray(videoList) && videoList.length > 0) {
        let videoWarpDom = document.getElementsByClassName('video-react-video')[0];
        video = videoWarpDom?.querySelector('video');
      }
    }
    if (!!video) {
      try {
        if (video !== document.pictureInPictureElement) {
          // 将视频设置为 Picture-in-Picture 模式
          await video.requestPictureInPicture();
        } else {
          // 退出 Picture-in-Picture 模式
          await document.exitPictureInPicture();
        }
      } catch (error) {
        console.error('Error:', error);
        message.warning('当前您使用的浏览器不支持开启画中画模式!');
      }
    } else {
      message.error('当前主画面无视频');
    }
  };

  // 点击顶部消息通知（主持人）
  const onClickTopMessageNotify = () => {
    // 申请发言，打开申请发言弹窗
    if (topMessageNotifyType == HAND_UP) {
      setApplySpakeListModalVisible(true);
    } else if (topMessageNotifyType == NO_PW_APPLY) {
      // 申请进入，打开管理成员弹窗对应tab
      setMembersModalTabKey(2);
      setMembersModalVisible(true);
    }
  };

  // 定时器重置2，顶部消息通知定时器
  const resetTimer2 = () => {
    clearTimeout(timer2);
    const timerValue = setTimeout(() => {
      dispatch({
        type: 'PlanetChatRoom/setState',
        payload: {
          isShowTopMessageNotify: false, // 是否显示页面顶部消息通知
          topMessageNotifyType: null, // 类型，分为申请进入提示和申请发言提示
        },
      });
    }, 5000);
    setTimer2(timerValue);
  };

  // 关闭申请发言list弹窗（主持人）
  const applySpakeListModalClose = () => {
    setApplySpakeListModalVisible(false);
  };

  // 点击当前有人正在申请发言时出现的小手图标（主持人）
  const onClickHandUpBtn = () => {
    // 打开申请发言弹窗
    setApplySpakeListModalVisible(true);
  };

  // 申请发言弹窗，点击同意发言（主持人）
  const onClickAgreeSpeaking = (item) => {
    // 1 接受连麦 2暂不同意 3下麦
    operateHandUp({
      statusType: 1,
      guestUserId: item.wxUserId,
      imUserId: item.imUserId,
    });
  };

  // 申请发言弹窗，点击停止发言（主持人）
  const onClickStopSpeaking = (item) => {
    // 打开二次确认弹窗，1 接受连麦 2暂不同意 3下麦
    setForceMikeOffTipsModalVisible({
      statusType: 3,
      guestUserId: item.wxUserId,
      imUserId: item.imUserId,
    });
  };

  // 强制下麦提示，确认下麦（主持人）
  const forceMikeOffTipsModalOnOk = async () => {
    await operateHandUp(forceMikeOffTipsModalVisible);
    await setForceMikeOffTipsModalVisible(false);
  };

  // 强制下麦提示，我再想想（主持人）
  const forceMikeOffTipsModalClose = () => {
    setForceMikeOffTipsModalVisible(false);
  };

  // 有人发言中时，主持人接受发言提示弹窗，确认接受（主持人）
  const acceptMikeTipsModalOnOk = async () => {
    const {statusType, guestUserId, imUserId, currentHandUpUser} =
    ModalVisibleByAcceptLienMai || {};
    /**
     currentHandUpUser.applyDate: "2023-07-11 14:19:11"
     currentHandUpUser.headUrlShow: null
     currentHandUpUser.imUserId: "b49b2335fb0c0bb42ea25e8fa5a6b23c"
     currentHandUpUser.name: "李老师"
     currentHandUpUser.statusType: 1
     currentHandUpUser.wxUserId: 55
     * */
      // 上一个人强制下麦,下一个人接受连麦
    let res = await operateHandUp({
        statusType: 3,
        guestUserId: currentHandUpUser.wxUserId,
        imUserId: currentHandUpUser.imUserId,
      });
    const {code, content, msg} = res || {};
    if (code == 200) {
      let res2 = await operateHandUp({statusType: 1, guestUserId, imUserId});
      if (res2 && res2.code == 200) {
        message.success(`接受发言成功`);
        dispatch({
          type: 'PlanetChatRoom/setState',
          payload: {ModalVisibleByAcceptLienMai: false},
        });
      }
    } else {
      message.error(msg ? msg : '强制下麦失败');
    }
  };

  // 有人发言中时，主持人接受发言提示弹窗，我再想想（主持人）
  const acceptMikeTipsModalClose = () => {
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {ModalVisibleByAcceptLienMai: false},
    });
  };

  // 自己主动下麦，二次确认弹窗，确认下麦（观众）
  const leaveMikeTipsModalOnOk = async () => {
    await operateHandUp({
      statusType: 3,
      wxUserId: wxUserId,
      guestUserId: wxUserId,
      imUserId: imUserId,
    });
    await sendMessageByIm({dataType: HAND_DOWN, description: '1'});
    await dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {
        SpaceInfo: {
          ...SpaceInfo,
          handUpStatusType: null,
        },
      },
    });
  };

  // 自己主动下麦，二次确认弹窗，我再想想（观众）
  const leaveMikeTipsModalClose = () => {
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {ModalVisibleByLeaveMicrophone: false},
    });
  };

  // 主持人点击强制下麦悬浮按钮（主持人）
  const onClickForceMikeOffBtn = (item) => {
    // 1 接受连麦 2暂不同意 3下麦
    setForceMikeOffTipsModalVisible({
      statusType: 3,
      guestUserId: item.wxUserId,
      imUserId: item.imUserId,
    });
  };

  // 观众自己点击结束连麦悬浮按钮（观众）
  const onClickEndMikeOffBtn = () => {
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {ModalVisibleByLeaveMicrophone: true},
    });
  };

  // 点击分享课件时的悬浮控制器，上一页icon
  const onClickShareFileNextIcon = () => {
    // 点击分享课件时的悬浮控制器，下一页icon
    // 已经包含在白板组件内
  };

  // 点击分享课件时的悬浮控制器，下一页icon
  const onClickShareFilePrevIcon = () => {
    // 点击分享课件时的悬浮控制器，上一页icon
    // 已经包含在白板组件内
  };

  // 点击分享课件时的悬浮控制器，课件列表
  const onClickShareFileListBtn = () => {
    // 打开课件list弹窗
    setShareFileListModalVisible(true);
  };

  // 关闭课件list弹窗
  const shareFileListModalClose = () => {
    setShareFileListModalVisible(false);
  };

  // 课件list弹窗，确定
  const shareFileListModalOnOk = (fileId) => {
    // 课件list弹窗，确定
    // 已经包含白板组件内
  };

  // [空间提示弹窗] - 该空间因违反平台规范已被关闭
  const spaceViolationTipsModalClose = () => {
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {ModalVisibleBySpaceViolation: false},
    });
    handleLeave();
    dispatch({type: 'PlanetChatRoom/clean'});
    cleanVal();
    cleanRTC();
    clear().then(() => {
      onClickBack();
    });
  };

  // [空间提示弹窗] - 该空间已下架 或空间直播接口报错
  const spaceRemovedTipsModalClose = () => {
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {ModalVisibleBySpaceRemoved: false},
    });
    cleanVal();
    cleanRTC();
    clear().then(() => {
      onClickBack();
    });
  };

  // [空间提示弹窗] - 多端登录被踢
  const kickedOutTipsModalClose = () => {
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {ModalVisibleByKickedOut: false},
    });
    cleanVal();
    clear().then(() => {
      onClickBack();
    });
  };

  // [空间提示弹窗] - 主播或嘉宾无麦克风，返回
  const noMicrophoneTipsModalOnOk = () => {
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {ModalVisibleByNoMicrophone: false},
    });
    onClickBack();
  };

  // [空间提示弹窗] - 主播或嘉宾无麦克风，我知道了
  const noMicrophoneTipsModalClose = () => {
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {ModalVisibleByNoMicrophone: false},
    });
  };

  // [空间提示弹窗] - 申请连麦-无麦克风设备-禁用连麦申请
  const acceptLienMaiNoMicrophoneTipsModalClose = () => {
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {ModalVisibleByAcceptLienMaiNoMicrophone: false},
    });
  };

  // 屏幕分享失败
  const shareScreenErrorTipsModalClose = () => {
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {ModalVisibleByShareScreenError: false},
    });
  };

  // 用户未登录提示弹窗，暂不登录
  const userTokenInvalidTipsModalClose = () => {
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {ModalVisibleByUserTokenInvalid: false},
    });
    onClickBack();
  };

  // 用户未登录提示弹窗，立即登录
  const userTokenInvalidTipsModalOnRight = () => {
    userTokenInvalid('/PlanetChatRoom');
  };

  // 已提交申请
  const applicationSubmittedTipsModalOnRight = () => {
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {ModalVisibleByApplicationSubmitted: false},
    });
    onClickBack();
  };

  // 帮助文档下载
  const downScreenShareHelpFile = async () => {
    let resByDownScreenShareHelpFile = await dispatch({
      type: 'PlanetChatRoom/downScreenShareHelpFile',
      payload: {},
    });

    if (resByDownScreenShareHelpFile) {
      if (resByDownScreenShareHelpFile.code == 500) {
        message.error('下载失败！');
      } else {
        getDownLoad(resByDownScreenShareHelpFile, `医生星球会议帮助文档.pdf`);
      }
    } else {
      message.error('下载失败！');
    }
  };

  // checkSpacePassword 校验空间密码-密码校验成功后-进入中房间
  const checkSpacePassword = async (pwd) => {
    await setLoadingCheckSpacePassword(true);
    let data = await dispatch({
      type: 'PlanetChatRoom/checkSpacePassword',
      payload: {
        id: match?.params?.RoomId,
        password: pwd,
      },
    });
    await setLoadingCheckSpacePassword(false);
    const {code, content} = data || {};
    if (code == 200 && content) {
      // 密码校验成功 更新直播间状态
      if (isNotLogin) {
        await dispatch({
          type: 'PlanetChatRoom/setState',
          payload: {isNotLoginCheckPassword: true},
        });
      }
      await userActiveJoinSpace();
      const resBySpaceInfo = await getSpaceInfo(); // 更新获取空间信息
      const {code, content} = resBySpaceInfo || {};
      if (code == 200 && content && content.status == 1) {
        await handleJoin(); // 加入房间
      }
    } else {
      Toast.show({
        // duration:100000,
        maskClassName: 'ToastWarp',
        content: <div className={styles.ShowContent}>会议密码错误,请重新输入</div>,
        afterClose: () => {
        },
      });
    }
  };

  // 点击 密码弹窗中的 无密码申请入会
  const addStarSpaceApplyAdmission = async () => {
    if (!!isNotLogin) {
      // 未登录
      await dispatch({
        type: 'PlanetChatRoom/setState',
        payload: {
          ModalVisibleByUserTokenInvalid: true,
        },
      });

      /* await dispatch({
        type:'PlanetChatRoom/setState',
        payload:{
          ModalVisibleByUserTokenInvalid:true,
        }
      }) */
      return;
    }
    const dataByAddStarSpaceApplyAdmission = await dispatch({
      type: 'PlanetChatRoom/addStarSpaceApplyAdmission',
      payload: {
        spaceId: match?.params?.RoomId,
      },
    });
    const {code, content, msg} = dataByAddStarSpaceApplyAdmission || {};
    if (code == 200) {
      sendMessageByIm({
        dataType: NO_PW_APPLY,
        description: JSON.stringify({imUserId: 'ALL'}),
      });
      dispatch({
        type: 'PlanetChatRoom/setState',
        payload: {ModalVisibleByApplicationSubmitted: true},
      });
    } else {
      message.error(msg ? msg : '无密码申请入会失败!');
    }
  };

  // tips弹窗（非参会人进入时），我知道了
  const spectatorJoinTipsModalOnRight = () => {
    // todo...tips弹窗（非参会人进入时），我知道了
    setSpectatorJoinTipsModalVisible(null);
  };

  // tips弹窗（主持人收到录制申请），拒绝
  const applyRecordTipsModalClose = ({isChecked}) => {
    // tips弹窗（主持人收到录制申请），拒绝
    sendMessageByIm({
      dataType: APPLY_RECORD_RESULT,
      description: JSON.stringify({
        imUserId: applyRecordTipsModalObj && applyRecordTipsModalObj.imUserId,
        isAgree: false, // 是否同意
      }),
    });
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {
        applyRecordTipsModalVisible: null, // tips弹窗（主持人收到录制申请）
        applyRecordTipsModalObj: null, // tips弹窗（主持人收到录制申请）携带的信息
        NotAgainApplyRecordTipsModal: isChecked, // 是否不重复接收申请录制提示弹窗
      },
    });
  };

  // tips弹窗（主持人收到录制申请），同意
  const applyRecordTipsModalOnRight = ({isChecked}) => {
    // tips弹窗（主持人收到录制申请），同意
    message.success('开启录制');
    liveRecord({recordType: 1});
    sendMessageByIm({
      dataType: APPLY_RECORD_RESULT,
      description: JSON.stringify({
        imUserId: applyRecordTipsModalObj && applyRecordTipsModalObj.imUserId,
        isAgree: true, // 是否同意
      }),
    });
    sendMessageByIm({
      dataType: UPDATA_STATE,
      description: JSON.stringify({imUserId: 'ALL'}),
    });
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {
        applyRecordTipsModalVisible: null, // tips弹窗（主持人收到录制申请）
        applyRecordTipsModalObj: null, // tips弹窗（主持人收到录制申请）携带的信息
        NotAgainApplyRecordTipsModal: isChecked, // 是否不重复接收申请录制提示弹窗
      },
    });
  };

  // 开启申请录制弹窗
  const openApplyRecordTipsModal = (value) => {
    /**
     * friUserId: 1049
     * headUrl: "https://s1-test.5i5ya.com/03462a8b91d73c2abbce20bf7e33ff09/665435f7/dmp/userHead/e51cc94ed3e147febc098a56712321af.jpeg"
     * id: 1049
     * imUserId: "bdab391ca18148bdb55874819dbd47cd"
     * isBizUser: 1
     * memberType: 0
     * name: "建光"
     * nickName: "kaka"
     * phone: "13521217375"
     * serialNumber: "001054"
     * status: 1
     * vestUcStarIdentityList: (5) [{…}, {…}, {…}, {…}, {…}]
     */
    const {name} = value || {};
    if (!NotAgainApplyRecordTipsModal) {
      dispatch({
        type: 'PlanetChatRoom/setState',
        payload: {
          applyRecordTipsModalVisible: `${name}请求您开启会议录制，点击同意后开启录制`, // tips弹窗（主持人收到录制申请）
          applyRecordTipsModalObj: value, // tips弹窗（主持人收到录制申请）
        },
      });
    }
  };

  // 打开移交主持人弹窗
  const openGuestToHostTipsModalVisible = async () => {
    /*await handleLeave();
    await cleanRTC();
    await dispatch({ type:'PlanetChatRoom/clean' });
    await callBackByUPDATASTATE && callBackByUPDATASTATE()*/
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {
        guestToHostTipsModalVisible: true, // tips弹窗（主持人收到录制申请）
      },
    });
  };

  // tips弹窗（参会被指定为主持人），我知道了
  const guestToHostTipsModalOnRight = () => {
    sendMessageByIm({
      dataType: UPDATA_STATE,
      description: JSON.stringify({imUserId: 'ALL'}),
    });
    window.location.reload();
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {
        guestToHostTipsModalVisible: null, // tips弹窗（主持人收到录制申请）
      },
    });
  };

  // tips弹窗（主持人请求全体成员解除静音），保持静音,关闭弹窗
  const hostOpenAllMikeTipsModalClose = () => {
    //（主持人请求全体成员解除静音），保持静音 关闭弹窗
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {
        hostOpenAllMikeTipsModalVisible: null, // tips弹窗（主持人请求全体成员解除静音）
      },
    });
  };

  // tips弹窗（主持人请求全体成员解除静音），解除静音
  const hostOpenAllMikeTipsModalOnRight = () => {
    // tips弹窗（主持人请求全体成员解除静音），解除静音
    handleChangeByLocalStreamConfig('audio', null);
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {
        hostOpenAllMikeTipsModalVisible: null, // tips弹窗（主持人请求全体成员解除静音）
      },
    });
  };

  const openHostOpenAllMikeTipsModal = () => {
    // tips弹窗（主持人请求全体成员解除静音）
    if (localStreamConfig && isPublished && isJoined) {
      if (localStreamConfig.mutedAudio) {
        dispatch({
          type: 'PlanetChatRoom/setState',
          payload: {
            hostOpenAllMikeTipsModalVisible: true, // tips弹窗（主持人请求全体成员解除静音）
          },
        });
      }
    }
  };

  // tips弹窗（主持人请求你解除静音），点击了-保持静音
  const hostOpenYouMikeTipsModalClose = () => {
    // ...tips弹窗（主持人请求你解除静音），保持静音
    sendMessageByIm({
      dataType: MICROPHONE_TOGGLE_RESULT,
      description: JSON.stringify({
        imUserId: imUserId,
        isAgree: true, // 是否同意
        ...userInfoData,
      }),
    });
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {
        hostOpenYouMikeTipsModalVisible: null, // tips弹窗（主持人请求你解除静音）
      },
    });
  };

  // tips弹窗（主持人请求你解除静音），解除静音
  const hostOpenYouMikeTipsModalOnRight = () => {
    // tips弹窗（主持人请求你解除静音），解除静音
    handleChangeByLocalStreamConfig('audio', null);
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {
        hostOpenYouMikeTipsModalVisible: null, // tips弹窗（主持人请求你解除静音）
      },
    });
  };

  // 弹窗（主持人已将你静音），我知道了
  const openHostCloseYouMikeTipsModal = useCallback(() => {
    handleChangeByLocalStreamConfig('audio', null);
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {
        hostCloseYouMikeTipsModalVisible: true, // tips弹窗（主持人已将你静音），我知道了
      },
    });
  }, [localStreamConfig]);

  // tips弹窗（主持人请求你解除静音），解除静音
  const openHostOpenYouMikeTipsModal = () => {
    if (localStreamConfig) {
      if (localStreamConfig.mutedAudio) {
        dispatch({
          type: 'PlanetChatRoom/setState',
          payload: {
            hostOpenYouMikeTipsModalVisible: true, // tips弹窗（主持人已将你静音）
          },
        });
      }
    }
  };

  // tips弹窗（主持人请求你开启视频），保持关闭 (拒绝开启摄像头)
  const hostOpenYouCameraTipsModalClose = () => {
    // tips弹窗（主持人请求你开启视频），保持关闭 关闭弹窗
    sendMessageByIm({
      dataType: CAMERA_TOGGLE_RESULT,
      description: JSON.stringify({
        imUserId: imUserId,
        isAgree: true, // 是否同意
        ...userInfoData,
      }),
    });
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {
        hostOpenYouCameraTipsModalVisible: null, // tips弹窗（主持人请求你开启视频）
      },
    });
  };

  // tips弹窗（主持人请求你开启视频），开启视频
  const hostOpenYouCameraTipsModalOnRight = async () => {
    setLoadingCheckStream(true);
    // tips弹窗（主持人请求你开启视频），开启视频 关闭弹窗
    setTimeout(async () => {
      await setLoadingCheckStream(false);
      await onClickCameraBtn();
      await dispatch({
        type: 'PlanetChatRoom/setState',
        payload: {
          hostOpenYouCameraTipsModalVisible: null, // tips弹窗（主持人请求你开启视频）
        },
      });
    }, 2000);
  };

  // tips弹窗（主持人已将你静音），我知道了
  const hostCloseYouMikeTipsModalOnRight = () => {
    // tips弹窗（主持人已将你静音），我知道了 关闭弹窗
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {
        hostCloseYouMikeTipsModalVisible: null, // tips弹窗（主持人已将你静音）
      },
    });
  };

  // tips弹窗（主持人请求你开启视频）开启弹窗
  const openHostOpenYouCameraTipsModal = () => {
    // 弹窗（主持人请求你开启视频）
    if (localStreamConfig && !!localStreamConfig.mutedVideo) {
      dispatch({
        type: 'PlanetChatRoom/setState',
        payload: {
          hostOpenYouCameraTipsModalVisible: true, // tips弹窗（主持人请求你开启视频）
        },
      });
    }
  };

  // tips弹窗（主持人已关闭你的视频）
  const openHostCloseYouCameraTipsModal = () => {
    handleChangeByLocalStreamConfig('video', null);
    // 主持人已关闭你的视频
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {
        hostCloseYouCameraTipsModalVisible: true, // tips弹窗（主持人请求你开启视频）
      },
    });
  };

  // tips弹窗（主持人已关闭你的视频），我知道了
  const hostCloseYouCameraTipsModalOnRight = () => {
    // tips弹窗（主持人已关闭你的视频），我知道了 关闭弹窗
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {
        hostCloseYouCameraTipsModalVisible: null, // tips弹窗（主持人已关闭你的视频）
      },
    });
  };

  // tips弹窗（你被主持人移出会议）
  const hostRemoveYouTipsModalOnRight = () => {
    // todo...tips弹窗（你被主持人移出会议），我知道了
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {
        hostRemoveYouTipsModalVisible: null, // tips弹窗（你被主持人移出会议）
      },
    });
  };

  // 开启参会人拒绝开启麦克风弹窗
  const openGuestRefuseOpenMikeTipsModal = (nameByMsg) => {
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {
        guestRefuseOpenMikeTipsModalVisible: nameByMsg, // tips弹窗（参会人拒绝开启麦克风）
      },
    });
  };

  // tips弹窗（参会人拒绝开启麦克风），我知道了
  const guestRefuseOpenMikeTipsModalOnRight = () => {
    // tips弹窗（参会人拒绝开启麦克风），我知道了
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {
        guestRefuseOpenMikeTipsModalVisible: null, // tips弹窗（参会人拒绝开启麦克风）
      },
    });
  };

  // 开启参会人拒绝开启摄像头弹窗
  const openGuestRefuseOpenCameraTipsModal = (nameByMsg) => {
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {
        guestRefuseOpenCameraTipsModalVisible: nameByMsg, // tips弹窗（参会人拒绝开启视频）
      },
    });
  };

  // tips弹窗（参会人拒绝开启视频），我知道了
  const guestRefuseOpenCameraTipsModalOnRight = () => {
    // tips弹窗（参会人拒绝开启视频），我知道了
    dispatch({
      type: 'PlanetChatRoom/setState',
      payload: {
        guestRefuseOpenCameraTipsModalVisible: null, // tips弹窗（参会人拒绝开启视频）
      },
    });
  };

  // PC端
  // 打开PC会议详情弹窗
  const onClickNavBarTitleByPC = () => {
    resetTimer();
    setMeetingDetailsModalByPCVisible(true);
  };

  // 关闭PC会议详情弹窗
  const meetingDetailsModalCloseByPC = () => {
    setMeetingDetailsModalByPCVisible(false);
  };

  // 点击结束按钮悬浮菜单中的选项
  const onClickEndBtnByPC = ({ item, key, keyPath, domEvent }) => {
    if (key == 'leave') {
      if (currentUserType == 1) {
        // 主持人点击离开会议
        onClickLeaveMeetingByPC();
      } else {
        // 非主持人离开会议
        handleLeave();
        cleanVal();
        cleanRTC();
        dispatch({type: 'PlanetChatRoom/clean'});
        onClickBack();
      }
    } else if (key == 'end') {
      // 点击结束会议
      onClickEndMeeting();
    }
  };

  // 点击离开会议选项，打开PC指定主持人弹窗
  const onClickLeaveMeetingByPC = () => {
    setDesignateHostModalByPCVisible(true);
  };

  // 关闭PC指定主持人弹窗
  const designateHostModalCloseByPC = () => {
    setDesignateHostModalByPCVisible(false);
  };

  // 打开PC会议设置弹窗
  const onClickMoreSettingBtnByPC = () => {
    setMeetingSettingModalByPCVisible(true);
  };

  // 关闭PC会议设置弹窗
  const meetingSettingModalCloseByPC = () => {
    setMeetingSettingModalByPCVisible(false);
  };

  // 点击顶部消息通知（主持人）
  const onClickTopMessageNotifyByPC = () => {
    // 申请发言，打开申请发言弹窗
    if (topMessageNotifyType == HAND_UP) {
      setApplySpakeListModalByPCVisible(true);
    } else if (topMessageNotifyType == NO_PW_APPLY) {
      // 申请进入，打开管理成员弹窗对应tab
      setMembersModalTabKey(2);
      setMembersModalByPCVisible(true);
    }
  };

  // 点击有人申请发言时出现的小手图标（主持人）
  const onClickHandUpBtnByPC = () => {
    setApplySpakeListModalByPCVisible(true);
  };

  // 关闭PC申请发言弹窗
  const applySpakeListModalCloseByPC = () => {
    setApplySpakeListModalByPCVisible(false);
  };

  // 点击底部成员按钮，打开PC成员弹窗
  const onClickMembersBtnByPC = () => {
    setMembersModalByPCVisible(true);
  };

  // 关闭PC成员弹窗
  const membersModalCloseByPC = () => {
    setMembersModalByPCVisible(false);
  };

  return (
    <>
      <div
        id="warp_content"
        className={classNames(styles.warp_content, {
          [styles.pc]: !isMobile, // PC端
          [styles.mobile_portrait]: isMobile && !isHorizontalLive, // H5竖屏
          [styles.mobile_landscape]: isMobile && !!isHorizontalLive, // H5横屏
          [styles.mobile_video]: statusBySpaceInfo == 3, // H5竖屏_弹幕轰炸中
        })}
        onClick={onClickContent}
      >
        {/* 视频流内容 */}
        <div className={styles.content}>{props.children}</div>

        {/* 竖屏布局 */}
        {isMobile && !isHorizontalLive && (
          <ViewByPortrait
            viewRef={viewRef}
            meetingDurationTime={meetingDurationTime} // 会议已进行时间
            onClickBackIcon={onClickBackIcon} // 点击顶部导航栏返回箭头icon
            onClickInviteIcon={shareOnClick} // 点击顶部导航栏分享icon
            onClickNavBarTitle={onClickNavBarTitle} // 打开会议详情弹窗
            onClickEndBtn={onClickEndBtn} // 结束按钮弹窗
            onScrollCommentList={onScrollCommentList} // 发言评论区域列表滚动事件
            commentListIsInBottomNow={commentListIsInBottomNow} // 当前评论列表是否滚动到了底部
            onEnterPressCommentInput={onEnterPressCommentInput} // 评论输入框点击回车
            onClickSignInBtn={onClickSignInBtn} // 点击打卡按钮事件
            onClickCollectBtn={onClickCollectBtn} // 点击收藏按钮事件
            onClickCallBtn={onClickCallBtn} // 点击打call按钮事件
            isJoined={isJoined} // 是否加入房间
            isPublished={isPublished} // 是否发布流
            onClickDanmuIcon={onClickDanmuIcon} // 点击评论和弹幕区域开关
            isCommentAndDanmuOpen={isCommentAndDanmuOpen} // 弹幕和评论区域开关
            handUpStatusTypeEnter={handUpStatusTypeEnter} // 观众取消申请连麦二次确认参数
            localStreamConfig={localStreamConfig} // 本地流配置
            remoteStreamConfigList={remoteStreamConfigList}
            onClickApplySpeakBtn={onClickApplySpeakBtn} // 点击底部申请发言按钮
            onClickMikeBtn={onClickMikeBtn} // 点击底部麦克风按钮
            onClickCameraBtn={onClickCameraBtn} // 点击底部摄像头按钮
            onClickShareFileBtn={onClickShareFileBtn} // 点击底部分享课件按钮
            onClickMembersBtn={onClickMembersBtn} // 点击底部成员按钮
            onClickRecordBtn={onClickRecordBtn} // 点击底部录制按钮
            onClickMoreSettingBtn={onClickMoreSettingBtn} // 点击底部更多按钮
            onClickShowSmallWindowBtn={onClickShowSmallWindowBtn} // 点击底部小窗播放按钮
            onClickTopMessageNotify={onClickTopMessageNotify} // 点击顶部消息通知
            onClickHandUpBtn={onClickHandUpBtn} // 点击当前有人正在申请发言时出现的小手图标（主持人）
            onClickForceMikeOffBtn={onClickForceMikeOffBtn} // 主持人点击强制下麦悬浮按钮
            onClickEndMikeOffBtn={onClickEndMikeOffBtn} // 观众自己点击结束连麦悬浮按钮
            onClickShareFileNextIcon={onClickShareFileNextIcon} // 点击分享课件时的悬浮控制器，下一页icon
            onClickShareFilePrevIcon={onClickShareFilePrevIcon} // 点击分享课件时的悬浮控制器，上一页icon
            onClickShareFileListBtn={onClickShareFileListBtn} // 点击分享课件时的悬浮控制器，课件列表按钮
            elapsedTime={elapsedTime} // 正在录播时长
            isNotVideoOrScreen={isNotVideoOrScreen} // 是否即没有视频有没有投屏（此时认为是深色背景）
          />
        )}

        {/* 横屏布局 */}
        {isMobile && !!isHorizontalLive && (
          <ViewByLandscape
            viewRef={viewRef}
            meetingDurationTime={meetingDurationTime} // 会议已进行时间
            onClickBackIcon={onClickBackIcon} // 点击顶部导航栏返回箭头icon
            onClickInviteIcon={shareOnClick} // 点击顶部导航栏分享icon
            onClickNavBarTitle={onClickNavBarTitle} // 打开会议详情弹窗
            onClickEndBtn={onClickEndBtn} // 结束按钮弹窗
            onScrollCommentList={onScrollCommentList} // 发言评论区域列表滚动事件
            commentListIsInBottomNow={commentListIsInBottomNow} // 当前评论列表是否滚动到了底部
            onEnterPressCommentInput={onEnterPressCommentInput} // 评论输入框点击回车
            onClickSignInBtn={onClickSignInBtn} // 点击打卡按钮事件
            onClickCollectBtn={onClickCollectBtn} // 点击收藏按钮事件
            onClickCallBtn={onClickCallBtn} // 点击打call按钮事件
            isJoined={isJoined} // 是否加入房间
            isPublished={isPublished} // 是否发布流
            onClickDanmuIcon={onClickDanmuIcon} // 点击评论和弹幕区域开关
            isCommentAndDanmuOpen={isCommentAndDanmuOpen} // 弹幕和评论区域开关
            handUpStatusTypeEnter={handUpStatusTypeEnter} // 观众取消申请连麦二次确认参数
            localStreamConfig={localStreamConfig} // 本地流配置
            remoteStreamConfigList={remoteStreamConfigList}
            onClickApplySpeakBtn={onClickApplySpeakBtn} // 点击底部申请发言按钮
            onClickMikeBtn={onClickMikeBtn} // 点击底部麦克风按钮
            onClickCameraBtn={onClickCameraBtn} // 点击底部摄像头按钮
            onClickShareFileBtn={onClickShareFileBtn} // 点击底部分享课件按钮
            onClickMembersBtn={onClickMembersBtn} // 点击底部成员按钮
            onClickRecordBtn={onClickRecordBtn} // 点击底部录制按钮
            onClickMoreSettingBtn={onClickMoreSettingBtn} // 点击底部更多按钮
            onClickShowSmallWindowBtn={onClickShowSmallWindowBtn} // 点击底部小窗播放按钮
            onClickTopMessageNotify={onClickTopMessageNotify} // 点击顶部消息通知
            onClickHandUpBtn={onClickHandUpBtn} // 点击当前有人正在申请发言时出现的小手图标（主持人）
            onClickForceMikeOffBtn={onClickForceMikeOffBtn} // 主持人点击强制下麦悬浮按钮
            onClickEndMikeOffBtn={onClickEndMikeOffBtn} // 观众自己点击结束连麦悬浮按钮
            onClickShareFileNextIcon={onClickShareFileNextIcon} // 点击分享课件时的悬浮控制器，下一页icon
            onClickShareFilePrevIcon={onClickShareFilePrevIcon} // 点击分享课件时的悬浮控制器，上一页icon
            onClickShareFileListBtn={onClickShareFileListBtn} // 点击分享课件时的悬浮控制器，课件列表按钮
            elapsedTime={elapsedTime} // 正在录播时长
            isHiddenControlArea={isHiddenControlArea} // 是否隐藏控制区域
            isNotVideoOrScreen={isNotVideoOrScreen} // 是否即没有视频有没有投屏（此时认为是深色背景）
          />
        )}

        {/* PC布局 */}
        {!isMobile && (
          <ViewByPC
            viewRef={viewRef}
            meetingDurationTime={meetingDurationTime} // 会议已进行时间
            onClickBackIcon={onClickBackIcon} // 点击顶部导航栏返回箭头icon
            onClickInviteIcon={shareOnClick} // 点击顶部导航栏分享icon
            onClickNavBarTitle={onClickNavBarTitleByPC} // 打开会议详情弹窗
            onClickEndBtn={onClickEndBtnByPC} // 结束按钮弹窗
            onScrollCommentList={onScrollCommentList} // 发言评论区域列表滚动事件
            commentListIsInBottomNow={commentListIsInBottomNow} // 当前评论列表是否滚动到了底部
            onEnterPressCommentInput={onEnterPressCommentInput} // 评论输入框点击回车
            onClickSignInBtn={onClickSignInBtn} // 点击打卡按钮事件
            onClickCollectBtn={onClickCollectBtn} // 点击收藏按钮事件
            onClickCallBtn={onClickCallBtn} // 点击打call按钮事件
            isJoined={isJoined} // 是否加入房间
            isPublished={isPublished} // 是否发布流
            onClickDanmuIcon={onClickDanmuIcon} // 点击评论和弹幕区域开关
            isCommentAndDanmuOpen={isCommentAndDanmuOpen} // 弹幕和评论区域开关
            handUpStatusTypeEnter={handUpStatusTypeEnter} // 观众取消申请连麦二次确认参数
            localStreamConfig={localStreamConfig} // 本地流配置
            remoteStreamConfigList={remoteStreamConfigList}
            onClickApplySpeakBtn={onClickApplySpeakBtn} // 点击底部申请发言按钮
            onClickMikeBtn={onClickMikeBtn} // 点击底部麦克风按钮
            onClickCameraBtn={onClickCameraBtn} // 点击底部摄像头按钮
            onClickShareFileBtn={onClickShareFileBtn} // 点击底部分享课件按钮
            onClickMembersBtn={onClickMembersBtnByPC} // 点击底部成员按钮
            onClickRecordBtn={onClickRecordBtn} // 点击底部录制按钮
            onClickMoreSettingBtn={onClickMoreSettingBtnByPC} // 点击底部更多按钮
            onClickShowSmallWindowBtn={onClickShowSmallWindowBtn} // 点击底部小窗播放按钮
            onClickTopMessageNotify={onClickTopMessageNotifyByPC} // 点击顶部消息通知
            onClickHandUpBtn={onClickHandUpBtnByPC} // 点击当前有人正在申请发言时出现的小手图标（主持人）
            onClickForceMikeOffBtn={onClickForceMikeOffBtn} // 主持人点击强制下麦悬浮按钮
            onClickEndMikeOffBtn={onClickEndMikeOffBtn} // 观众自己点击结束连麦悬浮按钮
            onClickShareFileNextIcon={onClickShareFileNextIcon} // 点击分享课件时的悬浮控制器，下一页icon
            onClickShareFilePrevIcon={onClickShareFilePrevIcon} // 点击分享课件时的悬浮控制器，上一页icon
            onClickShareFileListBtn={onClickShareFileListBtn} // 点击分享课件时的悬浮控制器，课件列表按钮
            elapsedTime={elapsedTime} // 正在录播时长
            onClickShareScreenBtn={onClickShareScreenBtn} // 点击底部分享屏幕按钮
            isNotVideoOrScreen={isNotVideoOrScreen} // 是否即没有视频有没有投屏（此时认为是深色背景）
          />
        )}

        {/* 弹幕 */}
        <div id={'vs'} className={styles.video_box_warp}>
          <div id={'ms'} className={styles.video_box}></div>
        </div>

        {/*在Friday App内打开按钮 */}
        <DownloadAppBtn
          BoxStyle={!!isHorizontalLive ? {bottom: '20px'} : null}
          InnerStyle={{height: '40px', lineHeight: '40px', padding: '0px', textAlign: 'center'}}
          info={{roomId: SpaceInfo?.id}}
          type={2}
        />
        {currentWatchMode == 1 && <GoBackHomeIcon style={{bottom: '200px'}}/>}
      </div>

      {/* 会议详情弹窗 */}
      {meetingDetailsModalVisible && (
        <MeetingDetailsModal
          visible={meetingDetailsModalVisible}
          onCancel={meetingDetailsModalClose} // 关闭弹窗
        />
      )}

      {/* 点击结束按钮的弹窗 */}
      {leaveMeetingModalVisible && (
        <LeaveMeetingModal
          visible={leaveMeetingModalVisible}
          onCancel={leaveMeetingModalClose} // 关闭弹窗
          onClickLeaveMeeting={onClickLeaveMeeting} // 点击离开会议
          onClickEndMeeting={onClickEndMeeting} // 点击结束会议
        />
      )}

      {/* 指定主持人弹窗 */}
      {designateHostModalVisible && (
        <DesignateHostModal
          visible={designateHostModalVisible}
          onCancel={designateHostModalClose} // 关闭弹窗
          designateHostModalOnOk={designateHostModalOnOk} // 指定并离开事件
          onClickEndMeeting={onClickEndMeeting} // 点击结束会议
        />
      )}

      {/* 打卡详情弹窗 */}
      {signInListModalVisible && (
        <SignInListModal
          visible={signInListModalVisible}
          onCancel={signInListModalClose} // 关闭弹窗
        />
      )}

      {/* 管理成员弹窗 */}
      {membersModalVisible && (
        <MembersModal
          visible={membersModalVisible}
          localStreamConfig={localStreamConfig} // 本地流配置
          remoteStreamConfigList={remoteStreamConfigList} // 远端流配置列表
          sendMessageByIm={sendMessageByIm} // 发送TIM消息
          defaultTabKey={membersModalTabKey} // 默认打开的tab
          onCancel={membersModalClose} // 关闭弹窗
          onClickInviteBtn={onClickInviteBtn} // 点击邀请
          onClickMembersCameraIcon={onClickMembersCameraIcon} // 点击摄像头icon
          onClickMembersMikeIcon={onClickMembersMikeIcon} // 点击麦克风icon
          onClickMembersRemoveBtn={onClickMembersRemoveBtn} // 点击移出会议按钮
          onClickMembersAgreeBtn={onClickMembersAgreeBtn} // 点击批准进入
          onClickMembersRefuseBtn={onClickMembersRefuseBtn} // 点击移除进入
          onClickMembersMuteAllBtn={onClickMembersMuteAllBtn} // 点击全体静音
          onClickMembersUnmuteAllBtn={onClickMembersUnmuteAllBtn} // 点击解除全体静音
          onClickMembersMuteOrUnmuteBtn={onClickMembersMuteOrUnmuteBtn} // 点击静音、解除静音（参会人）
        />
      )}

      {/* 会议设置弹窗 */}
      {meetingSettingModalVisible && (
        <MeetingSettingModal
          visible={meetingSettingModalVisible}
          onCancel={meetingSettingModalClose} // 关闭弹窗
          onChangeIsSpectatorUse={onChangeIsSpectatorUse} // 不允许非参会人进入开关
          onChangeHandUpType={onChangeHandUpType} // 允许申请发言开关
          onChangeIsShowPassword={onChangeIsShowPassword} // 展示会议密码开关
          onChangeIsOpenSmallWindow={onChangeIsOpenSmallWindow} // 小窗播放开关
        />
      )}

      {/* 申请发言弹窗 */}
      {applySpakeListModalVisible && (
        <ApplySpakeListModal
          visible={applySpakeListModalVisible}
          onCancel={applySpakeListModalClose} // 关闭弹窗
          onClickStopSpeaking={onClickStopSpeaking} // 点击停止发言
          onClickAgreeSpeaking={onClickAgreeSpeaking} // 点击同意发言
        />
      )}

      {/* 分享课件list弹窗 */}
      {shareFileListModalVisible && (
        <ShareFileListModal
          visible={shareFileListModalVisible}
          onCancel={shareFileListModalClose} // 关闭弹窗
          onOk={shareFileListModalOnOk} // 点击确定事件
        />
      )}

      {/* 直播间密码需要输入密码 0：不需要 1需要 */}
      {!ModalVisibleByApplicationSubmitted && !ModalVisibleByUserTokenInvalid && (
        <EnterPasswordModal
          checkSpacePassword={checkSpacePassword} // 校验空间密码-密码校验成功后-进入中房间
          addStarSpaceApplyAdmission={addStarSpaceApplyAdmission} // 点击 密码弹窗中的 无密码申请入会
        />
      )}

      {/* [空间提示弹窗] - 结束会议提示弹窗 */}
      {endMeetingTipsModalVisible && (
        <CommonTipsModal
          visible={endMeetingTipsModalVisible}
          onClickLeftBtn={endMeetingTipsModalOnLeft} // 点击左边按钮
          onClickRightBtn={endMeetingTipsModalClose} // 点击右边按钮
          title={'关闭会议'} // 标题
          text={'关闭该会议后，如有录制视频将自动发布'} // 提示文案
          leftBtnText={'确认关闭'} // 左边按钮文案
          rightBtnText={'我再想想'} // 右边按钮文案
        />
      )}

      {/* [空间提示弹窗] - 结束录制提示弹窗 */}
      {endRecordTipsModalVisible && (
        <CommonTipsModal
          visible={endRecordTipsModalVisible}
          onClickLeftBtn={endRecordTipsModalOnOk} // 点击左边按钮
          onClickRightBtn={endRecordTipsModalClose} // 点击右边按钮
          title={'结束录制'} // 标题
          text={'结束录制后将保存当前视频，并在会议关闭后发布'} // 提示文案
          leftBtnText={'确认结束'} // 左边按钮文案
          rightBtnText={'我再想想'} // 右边按钮文案
        />
      )}

      {/* [空间提示弹窗] - 强制下麦二次确认弹窗 */}
      {!!forceMikeOffTipsModalVisible && (
        <CommonTipsModal
          visible={!!forceMikeOffTipsModalVisible}
          onClickLeftBtn={forceMikeOffTipsModalOnOk} // 点击左边按钮
          onClickRightBtn={forceMikeOffTipsModalClose} // 点击右边按钮
          title={'强制下麦'} // 标题
          text={'点击强制下麦，当前发言观众将被下麦'} // 提示文案
          leftBtnText={'确认下麦'} // 左边按钮文案
          rightBtnText={'我再想想'} // 右边按钮文案
        />
      )}

      {/* [空间提示弹窗] - 有人连麦中，主持人接受连麦提示弹窗 */}
      {!!ModalVisibleByAcceptLienMai && (
        <CommonTipsModal
          visible={!!ModalVisibleByAcceptLienMai}
          onClickLeftBtn={acceptMikeTipsModalOnOk} // 点击左边按钮
          onClickRightBtn={acceptMikeTipsModalClose} // 点击右边按钮
          title={'接受发言'} // 标题
          text={'仅允许1人发言中，如您接受新的发言申请，将为您自动断连当前发言人'} // 提示文案
          leftBtnText={'确认接受'} // 左边按钮文案
          rightBtnText={'我再想想'} // 右边按钮文案
        />
      )}

      {/* [空间提示弹窗] - 自己主动下麦，二次确认弹窗 */}
      {!!ModalVisibleByLeaveMicrophone && (
        <CommonTipsModal
          visible={!!ModalVisibleByLeaveMicrophone}
          onClickLeftBtn={leaveMikeTipsModalOnOk} // 点击左边按钮
          onClickRightBtn={leaveMikeTipsModalClose} // 点击右边按钮
          title={'您确认要下麦吗？'} // 标题
          leftBtnText={'确认下麦'} // 左边按钮文案
          rightBtnText={'我再想想'} // 右边按钮文案
        />
      )}

      {/*[空间提示弹窗] - 该空间因违反平台规范已被关闭*/}
      {!!ModalVisibleBySpaceViolation && (
        <CommonTipsModal
          visible={!!ModalVisibleBySpaceViolation}
          onClickLeftBtn={spaceViolationTipsModalClose} // 点击左边按钮
          onClickRightBtn={spaceViolationTipsModalClose} // 点击右边按钮
          title={
            ModalVisibleBySpaceViolation
              ? ModalVisibleBySpaceViolation
              : '该会议因违反平台规范已被关闭'
          } // 标题
          leftBtnText={'返回'} // 左边按钮文案
          rightBtnText={'我知道了'} // 右边按钮文案
        />
      )}

      {/* 该空间已下架 或空间直播接口报错 */}
      {!!ModalVisibleBySpaceRemoved && (
        <CommonTipsModal
          visible={!!ModalVisibleBySpaceRemoved}
          // onClickLeftBtn={spaceRemovedTipsModalClose} // 点击左边按钮
          onClickRightBtn={spaceRemovedTipsModalClose} // 点击右边按钮
          text={ModalVisibleBySpaceRemoved ? ModalVisibleBySpaceRemoved : '该会议已下架'} // 标题
          // leftBtnText={'返回'} // 左边按钮文案
          rightBtnText={'我知道了'} // 右边按钮文案
        />
      )}

      {/*[空间提示弹窗] - 多端登录被踢*/}
      {!!ModalVisibleByKickedOut && (
        <CommonTipsModal
          visible={!!ModalVisibleByKickedOut}
          onClickLeftBtn={kickedOutTipsModalClose} // 点击左边按钮
          onClickRightBtn={kickedOutTipsModalClose} // 点击右边按钮
          title={'您已经进入其他会议，当前会议自动退出'} // 标题
          leftBtnText={'返回'} // 左边按钮文案
          rightBtnText={'我知道了'} // 右边按钮文案
        />
      )}

      {/* [空间提示弹窗] - 主播或嘉宾无麦克风  */}
      {!!ModalVisibleByNoMicrophone && (
        <CommonTipsModal
          visible={!!ModalVisibleByNoMicrophone}
          onClickLeftBtn={noMicrophoneTipsModalOnOk} // 点击左边按钮
          onClickRightBtn={noMicrophoneTipsModalClose} // 点击右边按钮
          title={'未检测到麦克风，请检查您的麦克风权限,配置后刷新页面'} // 标题
          leftBtnText={'返回'} // 左边按钮文案
          rightBtnText={'我知道了'} // 右边按钮文案
          isShowHelpFile={true} // 展示帮助文档
          onClickHelpFileBtn={downScreenShareHelpFile} // 点击帮助文案，下载文件
        />
      )}

      {/* [空间提示弹窗] - 申请连麦-无麦克风设备-禁用连麦申请 */}
      {!!ModalVisibleByAcceptLienMaiNoMicrophone && (
        <CommonTipsModal
          visible={!!ModalVisibleByAcceptLienMaiNoMicrophone}
          onClickLeftBtn={acceptLienMaiNoMicrophoneTipsModalClose} // 点击左边按钮
          onClickRightBtn={acceptLienMaiNoMicrophoneTipsModalClose} // 点击右边按钮
          title={'申请发言'} // 标题
          text={'未检测到麦克风，请检查您的麦克风和麦克风权限,配置后重新点击申请发言'} // 文案
          leftBtnText={'返回'} // 左边按钮文案
          rightBtnText={'我知道了'} // 右边按钮文案
          isShowHelpFile={true} // 展示帮助文档
          onClickHelpFileBtn={downScreenShareHelpFile} // 点击帮助文案，下载文件
        />
      )}

      {/* [空间提示弹窗] - 屏幕分享失败 */}
      {!!ModalVisibleByShareScreenError && (
        <CommonTipsModal
          visible={!!ModalVisibleByShareScreenError}
          onClickLeftBtn={shareScreenErrorTipsModalClose} // 点击左边按钮
          onClickRightBtn={shareScreenErrorTipsModalClose} // 点击右边按钮
          title={'分享屏幕失败,请确保系统允许当前浏览器获取屏幕内容'} // 标题
          leftBtnText={'返回'} // 左边按钮文案
          rightBtnText={'我知道了'} // 右边按钮文案
          isShowHelpFile={true} // 展示帮助文档
          onClickHelpFileBtn={downScreenShareHelpFile} // 点击帮助文案，下载文件
        />
      )}

      {/* [空间提示弹窗] - 用户未登录提示弹窗 */}
      {!!ModalVisibleByUserTokenInvalid && (
        <CommonTipsModal
          visible={!!ModalVisibleByUserTokenInvalid}
          onClickLeftBtn={userTokenInvalidTipsModalClose} // 点击左边按钮
          onClickRightBtn={userTokenInvalidTipsModalOnRight} // 点击右边按钮
          title={'立即登录，畅享精彩内容！'} // 标题
          leftBtnText={'暂不登录'} // 左边按钮文案
          rightBtnText={'立即登录'} // 右边按钮文案
        />
      )}

      {/* [空间提示弹窗] - 已提交申请 */}
      {!!ModalVisibleByApplicationSubmitted && (
        <CommonTipsModal
          visible={!!ModalVisibleByApplicationSubmitted}
          onClickRightBtn={applicationSubmittedTipsModalOnRight} // 点击右边按钮
          title={'已提交申请'} // 标题
          text={'您的申请已成功发送！请耐心等待主持人同意。申请同意后，我们将立即通过短信通知您'} // 文案
          rightBtnText={'确定'} // 右边按钮文案
        />
      )}

      {/* [空间提示弹窗] - 主持人点击全体静音 */}
      {muteAllTipsModalVisible && (
        <CommonTipsModal
          visible={muteAllTipsModalVisible}
          onClickLeftBtn={muteAllTipsModalClose} // 点击左边按钮
          onClickRightBtn={muteAllTipsModalOnRight} // 点击右边按钮
          text={'所有以及新加入的成员将被静音'} // 文案
          leftBtnText={'取消'} // 左边按钮文案
          rightBtnText={'全体静音'} // 右边按钮文案
        />
      )}

      {/* [空间提示弹窗] - 主持人将成员移出会议 */}
      {!!membersRemoveTipsModalVisible && (
        <CommonTipsModal
          visible={!!membersRemoveTipsModalVisible}
          onClickLeftBtn={membersRemoveTipsModalClose} // 点击左边按钮
          onClickRightBtn={membersRemoveTipsModalOnRight} // 点击右边按钮
          text={`确定将${
            membersRemoveTipsModalVisible ? membersRemoveTipsModalVisible.name : ''
          }移出会议？`} // 文案
          leftBtnText={'取消'} // 左边按钮文案
          rightBtnText={'确认'} // 右边按钮文案
        />
      )}

      {/* [空间提示弹窗] - 主持人收到申请录制请求 */}
      {!!applyRecordTipsModalVisible && (
        <CommonTipsModal
          visible={!!applyRecordTipsModalVisible}
          onClickLeftBtn={applyRecordTipsModalClose} // 点击左边按钮
          onClickRightBtn={applyRecordTipsModalOnRight} // 点击右边按钮
          text={applyRecordTipsModalVisible} // 文案
          checkBoxText={'不再接收请求'}
          leftBtnText={'拒绝'} // 左边按钮文案
          rightBtnText={'同意'} // 右边按钮文案
        />
      )}

      {/* [空间提示弹窗] - 参会人成为主持人 */}
      {!!guestToHostTipsModalVisible && (
        <CommonTipsModal
          visible={!!guestToHostTipsModalVisible}
          onClickRightBtn={guestToHostTipsModalOnRight} // 点击右边按钮
          text={`主持人离开会议，你已成为主持人`} // 文案
          rightBtnText={'我知道了'} // 右边按钮文案
        />
      )}

      {/* [空间提示弹窗] - 非参会人进入 */}
      {/*<CommonTipsModal*/}
      {/*  visible={!!spectatorJoinTipsModalVisible}*/}
      {/*  onClickRightBtn={spectatorJoinTipsModalOnRight} // 点击右边按钮*/}
      {/*  text={`会议不允许非参会人进入，如需要进入，请联系主持人`} // 文案*/}
      {/*  rightBtnText={'我知道了'} // 右边按钮文案*/}
      {/*/>*/}

      {/* [空间提示弹窗] - 主持人请求全体成员解除静音 */}
      {!!hostOpenAllMikeTipsModalVisible && (
        <CommonTipsModal
          visible={!!hostOpenAllMikeTipsModalVisible}
          onClickLeftBtn={hostOpenAllMikeTipsModalClose} // 点击左边按钮
          onClickRightBtn={hostOpenAllMikeTipsModalOnRight} // 点击右边按钮
          text={`主持人请求全体成员解除静音`} // 文案
          leftBtnText={'保持静音'} // 左边按钮文案
          rightBtnText={'解除静音'} // 右边按钮文案
        />
      )}

      {/* [空间提示弹窗] - 主持人请求你开启麦克风 */}
      {!!hostOpenYouMikeTipsModalVisible && (
        <CommonTipsModal
          visible={!!hostOpenYouMikeTipsModalVisible}
          onClickLeftBtn={hostOpenYouMikeTipsModalClose} // 点击左边按钮
          onClickRightBtn={hostOpenYouMikeTipsModalOnRight} // 点击右边按钮
          text={`主持人请求你开启麦克风`} // 文案
          leftBtnText={'保持静音'} // 左边按钮文案
          rightBtnText={'解除静音'} // 右边按钮文案
        />
      )}

      {/* [空间提示弹窗] - 主持人请求你开启视频 */}
      {!!hostOpenYouCameraTipsModalVisible && (
        <CommonTipsModal
          visible={!!hostOpenYouCameraTipsModalVisible}
          onClickLeftBtn={hostOpenYouCameraTipsModalClose} // 点击左边按钮
          onClickRightBtn={hostOpenYouCameraTipsModalOnRight} // 点击右边按钮
          ModalLoading={!!loadingCheckStream}
          text={`主持人请求你开启视频`} // 文案
          leftBtnText={'保持关闭视频'} // 左边按钮文案
          rightBtnText={'开启视频'} // 右边按钮文案
        />
      )}

      {/* [空间提示弹窗] - 主持人已将你静音 */}
      {!!hostCloseYouMikeTipsModalVisible && (
        <CommonTipsModal
          visible={!!hostCloseYouMikeTipsModalVisible}
          onClickRightBtn={hostCloseYouMikeTipsModalOnRight} // 点击右边按钮
          text={`主持人已将你静音`} // 文案
          rightBtnText={'我知道了'} // 右边按钮文案
        />
      )}

      {/* [空间提示弹窗] - 主持人已关闭你的视频 */}
      {!!hostCloseYouCameraTipsModalVisible && (
        <CommonTipsModal
          visible={!!hostCloseYouCameraTipsModalVisible}
          onClickRightBtn={hostCloseYouCameraTipsModalOnRight} // 点击右边按钮
          text={`主持人已关闭你的视频`} // 文案
          rightBtnText={'我知道了'} // 右边按钮文案
        />
      )}

      {/* [空间提示弹窗] - 您已经被主持人移出会议 */}
      {!!hostRemoveYouTipsModalVisible && (
        <CommonTipsModal
          visible={!!hostRemoveYouTipsModalVisible}
          onClickRightBtn={hostRemoveYouTipsModalOnRight} // 点击右边按钮
          text={`您已经被主持人移出会议`} // 文案
          rightBtnText={'我知道了'} // 右边按钮文案
        />
      )}

      {/* [空间提示弹窗] - 参会人xxx已拒绝您的开启麦克风请求 */}
      {!!guestRefuseOpenMikeTipsModalVisible && (
        <CommonTipsModal
          visible={!!guestRefuseOpenMikeTipsModalVisible}
          onClickRightBtn={guestRefuseOpenMikeTipsModalOnRight} // 点击右边按钮
          text={
            !!guestRefuseOpenMikeTipsModalVisible
              ? `参会人${guestRefuseOpenMikeTipsModalVisible}已拒绝您的开启麦克风请求`
              : null
          } // 文案
          rightBtnText={'我知道了'} // 右边按钮文案
        />
      )}

      {/* [空间提示弹窗] - 参会人xxx已拒绝您的开启视频请求 */}
      {!!guestRefuseOpenCameraTipsModalVisible && (
        <CommonTipsModal
          visible={!!guestRefuseOpenCameraTipsModalVisible}
          onClickRightBtn={guestRefuseOpenCameraTipsModalOnRight} // 点击右边按钮
          text={
            !!guestRefuseOpenCameraTipsModalVisible
              ? `参会人${guestRefuseOpenCameraTipsModalVisible}已拒绝您的开启视频请求`
              : null
          } // 文案
          rightBtnText={'我知道了'} // 右边按钮文案
        />
      )}

      {/* [空间提示弹窗] - 请求主持人开启录制 */}
      {!!applyHostRecordTipsModalVisible && (
        <CommonTipsModal
          visible={!!applyHostRecordTipsModalVisible}
          onClickLeftBtn={applyHostRecordTipsModalOnLeft} // 点击左边按钮
          onClickRightBtn={applyHostRecordTipsModalOnRight} // 点击右边按钮
          text={`请求主持人开启录制？当前会议仅主持人可以进行录制`} // 文案
          leftBtnText={'取消'} // 右边按钮文案
          rightBtnText={'发送请求'} // 右边按钮文案
        />
      )}

      {/* PC端的 */}
      {/* 会议详情弹窗（PC端） */}
      {meetingDetailsModalByPCVisible && (
        <MeetingDetailsModalByPC
          visible={meetingDetailsModalByPCVisible}
          onCancel={meetingDetailsModalCloseByPC}
        />
      )}

      {/* 指定主持人弹窗（PC端） */}
      {designateHostModalByPCVisible && (
        <DesignateHostModalByPC
          visible={designateHostModalByPCVisible}
          onCancel={designateHostModalCloseByPC}
          designateHostModalOnOk={designateHostModalOnOk} // 指定并离开事件
          onClickEndMeeting={onClickEndMeeting} // 点击结束会议
        />
      )}

      {/* 会议设置弹窗（PC端） */}
      {meetingSettingModalByPCVisible && (
        <MeetingSettingModalByPC
          visible={meetingSettingModalByPCVisible}
          onCancel={meetingSettingModalCloseByPC}
          onChangeIsSpectatorUse={onChangeIsSpectatorUse} // 不允许非参会人进入开关
          onChangeHandUpType={onChangeHandUpType} // 允许申请发言开关
          onChangeIsShowPassword={onChangeIsShowPassword} // 展示会议密码开关
        />
      )}

      {/* 申请发言弹窗（PC端） */}
      {applySpakeListModalByPCVisible && (
        <ApplySpakeListModalByPC
          visible={applySpakeListModalByPCVisible}
          onCancel={applySpakeListModalCloseByPC}
          onClickStopSpeaking={onClickStopSpeaking} // 点击停止发言
          onClickAgreeSpeaking={onClickAgreeSpeaking} // 点击同意发言
        />
      )}

      {/* 管理成员弹窗（PC端） */}
      {membersModalByPCVisible && (
        <MembersModalByPC
          visible={membersModalByPCVisible}
          onCancel={membersModalCloseByPC} // 关闭弹窗
          localStreamConfig={localStreamConfig} // 本地流配置
          remoteStreamConfigList={remoteStreamConfigList} // 远端流配置列表
          sendMessageByIm={sendMessageByIm} // 发送TIM消息
          defaultTabKey={membersModalTabKey} // 默认打开的tab
          onClickMembersCameraIcon={onClickMembersCameraIcon} // 点击摄像头icon
          onClickMembersMikeIcon={onClickMembersMikeIcon} // 点击麦克风icon
          onClickMembersRemoveBtn={onClickMembersRemoveBtn} // 点击移出会议按钮
          onClickMembersAgreeBtn={onClickMembersAgreeBtn} // 点击批准进入
          onClickMembersRefuseBtn={onClickMembersRefuseBtn} // 点击移除进入
          onClickMembersMuteAllBtn={onClickMembersMuteAllBtn} // 点击全体静音
          onClickMembersUnmuteAllBtn={onClickMembersUnmuteAllBtn} // 点击解除全体静音
          onClickMembersMuteOrUnmuteBtn={onClickMembersMuteOrUnmuteBtn} // 点击静音、解除静音（参会人）
        />
      )}
    </>
  );
};
export default connect(({ global, PlanetChatRoom, loading }: any) => ({
  global,
  PlanetChatRoom,
  loading,
}))(Index);
