/**
 * @Description: PC-创建空间
 * @author: 赵斐
 */
import React, { useState, useEffect } from 'react';
import { connect, history, Helmet } from 'umi';
import classNames from 'classnames';
import { cloneDeep } from 'lodash';
import { stringify } from 'qs';
import dayjs from 'dayjs';
import moment from 'moment';
import TcVod from 'vod-js-sdk-v6';
import { gdpFormat, getOperatingEnv, processNames, randomColor, getArrailUrl } from '@/utils/utils';
import {
  Spin,
  Form,
  Input,
  Radio,
  Upload,
  Button,
  message,
  Select,
  Checkbox,
  DatePicker,
  Modal,
  TimePicker,
  Switch,
} from 'antd';
import styles from './index.less';
import { UpOutlined, DownOutlined } from '@ant-design/icons';
import { getConsultationAndCaseInfo } from '@/services/consultation';
import {checkSpaceCoursewareTranscode, spaceCoursewareTranscode} from '@/services/planetChatRoom';

const { TextArea } = Input;
import type { RangePickerProps } from 'antd/es/date-picker';
import PosterModalByPc from '@/pages/Poster/PosterModalByPc';

import PcHeader from '@/componentsByPc/PcHeader'; // 公共导航栏组件
import CroppingImageModal from '@/pages/CreateGraphicsText/ComponentsPC/CroppingImageModal'; // 图片裁剪弹窗
import CreateSpaceSuccessModal from '../../PcComponents/CreateSpaceSuccessModal'; // 创建空间成功弹窗
import NoDataRender from '@/components/NoDataRender'; // 暂无数据组件
import SelectCoverModal from '@/pages/CreateGraphicsText/ComponentsPC/SelectCoverModal/index'; // 选择封

// 图标、icon
import pcGobackIcon from '@/assets/GlobalImg/pc_goback.png';
import searchIcon from '@/assets/GlobalImg/search_icon_2.png';
import selectedIcon from '@/assets/GlobalImg/selected_icon.png';
import frameIcon from '@/assets/GlobalImg/frame.png';
import request from "@/utils/request";
const uploadImg =
  'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/big_add.png'; // 上传图片图标

// 空间类型数据
const spaceTypeDataSource = [
  { id: 1, name: '专家讲课' },
  { id: 2, name: '指导' },
  { id: 3, name: '复杂病例讨论' },
];
// 观众一级数据
const spectatorDataSource = [
  { id: 0, name: '所有人' },
  { id: 1, name: '王国成员' },
  { id: 2, name: '企业/品牌用户' },
];

const Index: React.FC = (props: any) => {
  // 是否嵌套在5i5ya的iframe中
  const isInIframe = (self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya')

  const UerInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  const { id, consultationId } = history.location.query;
  const { dispatch, loading } = props;
  const [form] = Form.useForm();
  let spaceCoursewareType = Form.useWatch('spaceCoursewareType', form);

  // 主持人、嘉宾数据
  const [hostOrGuestDataSource, setHostOrGuestDataSource] = useState([]);
  // 关联王国数据
  const [createAndJoinKingdomDataSource, setCreateAndJoinKingdomDataSource] = useState([]);
  // 企业分组数据
  const [bizGroupDataSource, setBizGroupDataSource] = useState([]);

  const [loadingByUpload, setLoadingByUpload] = useState(false); // 空间封面上传loading
  const [loadingByAdvertUpload, setLoadingByAdvertUpload] = useState(false); // 空间广告上传loading
  const [uploadVideoLoading, setUploadVideoLoading] = useState(false); // 视频上传loading
  const [uploadProgress, setUploadProgress] = useState(0); // 视频上传进度

  // 不在表单内的数据
  const [valuesNotInForm, setValuesNotInForm] = useState({
    hostName: null, // 主持人名字
    kingdomId: null, // 王国ID
    kingdomName: null, // 王国name
  });
  // 空间封面数据
  const [spaceCoverState, setSpaceCoverState] = useState({
    spaceCoverUrl: null,
    spaceCoverUrlView: null,
  });
  // 空间广告数据
  const [spaceAdvertisingState, setSpaceAdvertisingState] = useState({
    spaceAdvertisingUrl: null,
    spaceAdvertisingUrlView: null,
  });
  // 空间视频数据
  const [vodFileState, setVodFileState] = useState({
    vodFileId: null,
    vodFileUrl: null,
    vodFileName: null,
  });
  // 直播课件数据
  const [spaceCoursewares,setSpaceCoursewares] = useState([]);
  // 主持人、嘉宾数据下拉框搜索关键词
  const [hostOrGuestQueryKey, setHostOrGuestQueryKey] = useState('');
  // 观众数据
  const [spectatorState, setSpectatorState] = useState({
    spectatorDropdownShow: false, // 弹窗是否显示
    spectatorType: null, // 观众类型 0默认所有人 1指定王国成员可见 2指定企业品牌用户可见
    spectatorShowText: null, // 选择观众后展示的文案
    spectatorOfKingdomList: [], // 王国数据
    spectatorOfBizList: [], // 企业机构数据
    spectatorType2: null, // 索引
    isNoPasswordApply: null, // 是否开启无密码申请：0不开启，1
    starSpaceType: null, // 类型：1 直播，2 会议
  });
  let starSpaceTypeText = spectatorState.starSpaceType == 2 ? '会议' : '直播';
  let idByCreateText = !!id ? '编辑' : '创建';
  // state
  const [state, setState] = useState({
    isSubmit: false, // 是否点击了提交
    isSuperAccount: false, // 是否是超级账号
  });
  // 弹窗state
  const [modalState, setModalState] = useState({
    createSpaceSuccessVisible: false, // 创建成功弹窗是否显示
    spaceId: null, // 空间ID
    spaceName: null, // 空间名称
    posterVisible: false, // 海报弹窗是否显示
    selectCoverModalVisible: false, // 选择封面弹窗是否显示
    croppingImageVisible: false, // 是否展示裁剪弹框
    croppingImageUrlShow: null, // 裁剪图片地址
    croppingImageType: null, // COVER 封面，ADVERT 广告
    croppingImageUploadFileType: null, // 14 封面，15 广告'
    starSpaceType: null, // 类型：1 直播，2 会议
  });
  const [kingdomDropdownRenderVisible, setKingdomDropdownRenderVisible] = useState(false); // 王国下拉框是否显示
  // 展示直播设置
  const [showLiveSettings, setShowLiveSettings] = useState(null);
  // 转码状态setTime
  const [ checkTranscodeTimeoutId,setCheckTranscodeTimeoutId ] = useState(null);

  useEffect(async () => {
    await dispatch({ type: 'userInfoStore/clean' });
    // 是否是超级管理员账号
    await checkSuperAccount();
    // 初始化数据
    await getInitialData();
  }, []);

  // 初始化
  const getInitialData = async () => {
    // 有指导ID表示从视频指导详情点击创建空间跳转过来的
    if (consultationId) {
      if (id) {
        getSpaceInfoByEdit([], []);
      }
    } else {
      // 编辑时，先加载王国和企业机构数据，再加载空间详情并做相关回显
      if (id) {
        let kingdomData = await getCreateAndJoinKingdomList();
        let bizData = null;
        // isBizUser 1 表示企业用户
        if (UerInfo.isBizUser == 1) {
          bizData = await getBizGroupByTenantId();
        } else {
          spectatorDataSource.pop();
        }
        getSpaceInfoByEdit(kingdomData || [], bizData || []);
      }
    }
    if (!id) {
      getCreateSpaceInfo();
      getCreateAndJoinKingdomList();
      // isBizUser 1 表示企业用户
      if (UerInfo.isBizUser == 1) {
        getBizGroupByTenantId();
      } else {
        spectatorDataSource.pop();
      }
    }
  };

  // 初始化直播
  const getCreateSpaceInfo = async () => {
    if (consultationId) {
      let DataBygetConsultationAndCaseInfo = await getConsultationAndCaseInfo({
        consultationId,
        type: 2,
      });
      const { code, content: consultationDataSource } = DataBygetConsultationAndCaseInfo || {};
      if (code == 200 && consultationDataSource) {
        form.setFieldsValue({
          name: `${
            consultationDataSource.h5BaseUserDto && consultationDataSource.h5BaseUserDto.name
          }和${consultationDataSource.createUserName}的指导`,
        });
      }
    }

    let resByGetSpaceCover = await dispatch({
      type: 'userInfoStore/getSpaceCover',
      payload: {
        spaceId: null, // 空间ID
        starSpaceType: 1, // 封面类型 1直播、2会议
      },
    });
    // 查验是否为超级账号
    let resByCheckSuperAccount = await dispatch({
      type: 'userInfoStore/checkSuperAccount',
    });

    const { code, content } = resByGetSpaceCover || {};
    if (code == 200 && content) {
      if (Array.isArray(content) && content.length > 0) {
        let isSelectByItem = content.find((item) => {
          return item.isSelect == 1;
        });
        let selectByItem = !!isSelectByItem ? isSelectByItem : content[0];
        /**
         * isSelect: 0
         * showSpaceCoverUrl: "https://s1-test.5i5ya.com/58bc440c245ceab9035ab1778339529e/6604d5be/dmp/spaceCover/live_broadcast_01.png"
         * spaceCoverUrl: "dmp/spaceCover/live_broadcast_01.png"
         */
        dispatch({
          type: 'userInfoStore/setTaskListState',
          payload: {
            spaceCoverUrl: selectByItem
              ? { fileUrlView: selectByItem.showSpaceCoverUrl, fileUrl: selectByItem.spaceCoverUrl }
              : null, // 空间封面
            spaceCoverUrlView:
              selectByItem && selectByItem.showSpaceCoverUrl
                ? selectByItem.showSpaceCoverUrl
                : null, // 获取的空间封面
            isSuperAccount: resByCheckSuperAccount && resByCheckSuperAccount.content,
            starSpaceType: 1,
          },
        });

        setSpaceCoverState({
          ...spaceCoverState,
          spaceCoverUrl: selectByItem.spaceCoverUrl, // 空间封面
          spaceCoverUrlView:
            selectByItem && selectByItem.showSpaceCoverUrl ? selectByItem.showSpaceCoverUrl : null, // 获取的空间封面
        });
      }
    }
  };

  // 编辑获取空间数据
  const getSpaceInfoByEdit = (kingdomData, bizData) => {
    dispatch({
      type: 'userInfoStore/getSpaceInfoByEdit',
      payload: {
        spaceId: id, // 空间ID
      },
    })
      .then((res) => {
        const { code, content, msg } = res;
        if (code == 200) {
          // 回显王国
          if (content.kingdomId || content.kingdomId == 0) {
            setValuesNotInForm({
              ...valuesNotInForm,
              kingdomId: content.kingdomId,
            });
          }
          // 回显嘉宾
          if (content.guestDataList && content.guestDataList.length > 0) {
            setHostOrGuestDataSource(content.guestDataList);
          }
          // 回显封面
          if (
            Array.isArray(content.spaceCoverList) &&
            content.spaceCoverList[0] &&
            content.spaceCoverUrlShow
          ) {
            setSpaceCoverState({
              ...spaceCoverState,
              spaceCoverUrl: content.spaceCoverList[0].spaceCoverUrl,
              spaceCoverUrlView: content.spaceCoverUrlShow,
            });
          }
          // 回显广告
          if (content.spaceAdvertisingUrl && content.spaceAdvertisingUrlShow) {
            setSpaceAdvertisingState({
              ...spaceAdvertisingState,
              spaceAdvertisingUrl: content.spaceAdvertisingUrl,
              spaceAdvertisingUrlView: content.spaceAdvertisingUrlShow,
            });
          }
          // 回显视频
          if (content.vodFileId) {
            setVodFileState({
              ...vodFileState,
              vodFileId: content.vodFileId,
              vodFileUrl: content.vodPathUrl,
              vodFileName: content.vodFileName,
            });
          }
          // 回显上传后的课件
          if(Array.isArray(content.spaceCoursewares)) {
            let spaceCoursewaresArr = []
            content.spaceCoursewares.map((item)=>{
              spaceCoursewaresArr.push({
                ...item,
                code:item.coursewareName,
                src:item.coursewarePathView,
                fileUrlView:item.coursewarePathView,
                name:item.coursewareName,
                uploadPath:item.coursewarePath,
                percent:100,   // 上传进度
                status:'done',
              })
            })
            setSpaceCoursewares(spaceCoursewaresArr)
          }

          // 处理观众数据
          let spectatorShowText = null;
          let spectatorOfKingdomList = [];
          let spectatorOfBizList = [];
          if (content.spectatorType == 0) {
            spectatorShowText = '所有人可见';
          } else if (content.spectatorType == 1) {
            spectatorShowText = `王国成员可见，已选${content.spectatorCount}个分组`;
            kingdomData.forEach((item) => {
              item.children.forEach((itemChild) => {
                if (itemChild.isChecked == 1) {
                  spectatorOfKingdomList.push(itemChild.id);
                }
              });
            });
          } else if (content.spectatorType == 2) {
            let isAllUser = 1;
            bizData.map((item) => {
              isAllUser = item.isAllUser;
              spectatorOfBizList.push({
                tenantId: item.tenantId,
                isAllUser: item.isAllUser,
                areaList:
                  item.isAllUser == 1
                    ? []
                    : !item.areaList
                      ? []
                      : item.areaList
                        .filter((itemChild) => itemChild.isChecked == 1)
                        .map((itemChild) => itemChild.id),
                orgList:
                  item.isAllUser == 1
                    ? []
                    : !item.orgList
                      ? []
                      : item.orgList
                        .filter((itemChild) => itemChild.isChecked == 1)
                        .map((itemChild) => itemChild.id),
              });
            });
            if (isAllUser == 1) {
              spectatorShowText = `企业/品牌用户成员可见，已选所有成员`;
            } else {
              spectatorShowText = `企业/品牌用户成员可见，已选${content.spectatorCount}个分组`;
            }
          }
          setSpectatorState({
            ...spectatorState,
            spectatorType: content.spectatorType, // 观众类型 0默认所有人 1指定王国成员可见 2指定企业品牌用户可见
            spectatorShowText, // 选择观众后的展示文案
            spectatorOfKingdomList, // 已选王国
            spectatorOfBizList, // 已选企业机构
            starSpaceType: content.starSpaceType, // 类型：1 直播，2 会议
            isNoPasswordApply: content.isNoPasswordApply, // 是否开启无密码申请：0不开启，1
          });

          // 回显表单数据
          form.setFieldsValue({
            name: content.name, // 空间名
            intro: content.intro || undefined, // 空间介绍
            guestIdList: content.guestDataList
              ? content.guestDataList.map((item) => item.id)
              : undefined, // 嘉宾
            spaceCoverUrlType: content.spaceCoverUrl ? 2 : 1, // 空间封面，1 无封面，2 有封面
            spaceAdvertisingUrlType: content.spaceAdvertisingUrl ? 2 : 1, // 空间广告，1 无，2 有
            spaceVideoType: content.vodFileId ? 2 : 1,  // 空间视频，1 无，2 有
            passwordType: content.password ? true : false, // 密码，1 无，2 有
            password: content.password || undefined, // 密码值
            isAutoRecord: content.isAutoRecord ? true : false, // 是否开启自动录播 1:开启  0或其他关闭
            startStatus: content.startStatus, // 开始状态：1立刻、2预约
            spaceCoursewareType: content.spaceCoursewares ? 2 : 1,  // 课件，1 无，2 有
            appointmentStartTime_date:
              content.startStatus == 2
                ? moment(content.appointmentStartTime, 'YYYY-MM-DD')
                : undefined, // 开始日期
            appointmentStartTime_time:
              content.startStatus == 2
                ? moment(content.appointmentStartTime, 'YYYY-MM-DD HH:mm:ss')
                : undefined, // 开始时间
            spaceType: content.spectatorType == 2 ? content.spaceType : undefined, // 空间类型 1专家讲课 2指导 3复杂病例讨论
          });
        } else {
          message.error(msg || '数据加载失败');
        }
      })
      .catch((err) => {});
  };

  // 查询是否为超级账号
  const checkSuperAccount = () => {
    dispatch({
      type: 'userInfoStore/checkSuperAccount',
    })
      .then((res) => {
        const { code, content, msg } = res;
        if (code == 200) {
          setState({
            ...state,
            isSuperAccount: content, // 是否是超级管理员账号
          });
        } else {
          message.error(msg || '数据加载失败');
        }
      })
      .catch((err) => {});
  };

  // 获取创建王国或加入王国相关数据
  const getCreateAndJoinKingdomList = () => {
    return dispatch({
      type: 'userInfoStore/getCreateAndJoinKingdomList',
      payload: {
        spaceId: id, // 空间ID，编辑时传做回显，返回的数据有是否已选择的状态
      },
    })
      .then((res) => {
        const { code, content, msg } = res;
        if (code == 200) {
          const arr = [];
          if (content['1']) {
            arr.push({
              id: 1,
              name: '我创建的',
              children: content[1],
            });
          }
          if (content['2']) {
            arr.push({
              id: 2,
              name: '我加入的',
              children: content[2],
            });
          }
          setCreateAndJoinKingdomDataSource(arr); // 赋值-关联王国列表数据
          return cloneDeep(arr);
        }
        if (code == 400) {
          // 未加入和创建王国
        }
      })
      .catch();
  };

  // 获取企业分组数据
  const getBizGroupByTenantId = () => {
    return dispatch({
      type: 'userInfoStore/getBizGroupByTenantId',
      payload: {
        spaceId: id, // 空间ID，编辑必传，做回显，返回的数据有是否已选择的状态
      },
    })
      .then((res: any) => {
        const { code, content, msg } = res;
        if (code == 200) {
          setBizGroupDataSource(content || []);
          return cloneDeep(content || []);
        }
      })
      .catch((err) => {});
  };

  // 获取主持人、嘉宾数据
  const searchUserListByQueryKey = (value) => {
    dispatch({
      type: 'userInfoStore/searchUserListByQueryKey',
      payload: {
        queryKey: value, // 搜索关键词
      },
    })
      .then((res: any) => {
        const { code, content, msg } = res;
        if (code == 200) {
          setHostOrGuestDataSource(content || []);
        } else {
          message.error(msg || '数据加载失败');
        }
      })
      .catch((err) => {});
  };

  // 主持人、嘉宾下拉搜索框输入
  const onChangeHostOrGuestQueryKey = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
    const { value } = e.target;
    setHostOrGuestQueryKey(value);
    if (!value || !value.trim()) {
      setHostOrGuestDataSource([]);
    } else {
      // 搜索用户
      searchUserListByQueryKey(value.trim());
    }
  };

  // 主持人或者嘉宾失去焦点事件
  const onBlurHostOrGuestSearchInput = () => {
    // 重置搜索框内容，并重置数据
    setHostOrGuestQueryKey('');
    setHostOrGuestDataSource([]);
  };

  // 选中主持人事件
  const onChangeHost = (value, option) => {
    setValuesNotInForm({
      ...valuesNotInForm,
      hostName: option?.title,
    });
  };

  // 上传图片headers
  const getHeaders = () => {
    // 获取环境类型，1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用   5 FRIDAY app中使用
    const env = getOperatingEnv();

    // FRIDAY APP中本地存储值：access_token、client、phone、user_name、wxuserId、userInfo
    return {
      // token
      access_token: localStorage.getItem('access_token') || '',
      username:
        env == 5
          ? localStorage.getItem('user_name')
          : localStorage.getItem('vxOpenIdCipherText')
          ? localStorage.getItem('vxOpenIdCipherText')
          : UerInfo?.phone,
      client: env == 5 ? localStorage.getItem('client') : 'WX',
      type: env === '1' ? '' : '1', // h5 传1
    };
  };

  // 上传直播课件
  const beforeCoursewareUpload = (file: { size?: any; type?: any }) => {
    const isSize = file.size / 1024 / 1024 < 100;

    if (!isSize) {
      message.warning('超过100M限制，不允许上传~');
      return false;
    }
    const { name: fileName } = file || {};
    // 添加对文件后缀名的限制
    const suffix = fileName.substring(fileName.lastIndexOf('.') + 1);
    let isBySuffix = ['pdf', 'ppt', 'pptx', 'doc', 'docx'].find((item)=>{return item === suffix});
    if (!isBySuffix) {
      message.warning('课件仅支持上传pdf、ppt、pptx、doc、docx格式的课件~');
      return false;
    }

    if (Array.isArray(spaceCoursewares) && spaceCoursewares.length >= 3) {
      message.warning('最多上传3个课件~');
      return false;
    }
    return true;
  }

  // 上传校验规则
  const beforeUpload = (file: { size?: any; type?: any }) => {
    const isSize = file.size / 1024 / 1024 < 15;
    if (!isSize) {
      message.error('超过15M限制，不允许上传~');
      return false;
    }

    const { name: fileName } = file || {};
    // 添加对文件后缀名的限制
    const suffix = fileName.substring(fileName.lastIndexOf('.') + 1);
    const isJpgOrPng =
      file.type === 'image/jpg' ||
      file.type === 'image/jpeg' ||
      file.type === 'image/png' ||
      file.type === 'image/gif';
    // 文件后缀名可以大写,所以需要添加大写后缀名的判断
    const isSuffixByJpgOrPng =
      suffix === 'jpg' ||
      suffix === 'JPG' ||
      suffix === 'jpeg' ||
      suffix === 'JPEG' ||
      suffix === 'png' ||
      suffix === 'PNG' ||
      suffix === 'gif' ||
      suffix === 'GIF';
    if (!isJpgOrPng || !isSuffixByJpgOrPng) {
      message.error('只能上传JPG、JPEG、PNG、GIF 格式的图片~');
      return false;
    }
    return isJpgOrPng;
  };

  // 上传空间封面、空间广告
  const onChangeByUpload = (info: any, type: string) => {
    let fileList;
    fileList = info && info.fileList;
    if (info.file.status === 'uploading') {
      if (type == 'COVER') {
        setLoadingByUpload(true);
      } else {
        setLoadingByAdvertUpload(true);
      }
      return;
    }

    // 判断当前上传已被拦截
    if (info && !info.file.status) {
      if (type == 'COVER') {
        setLoadingByUpload(false);
      } else {
        setLoadingByAdvertUpload(false);
      }
      fileList = null;
      return;
    }
    if (info && info.file.status === 'error') {
      if (type == 'COVER') {
        setLoadingByUpload(false);
      } else {
        setLoadingByAdvertUpload(false);
      }
      message.error('上传失败');
      fileList = null;
      return;
    }
    // 上传结束
    if (info && info.file.status === 'done') {
      if (type == 'COVER') {
        setLoadingByUpload(false);
      } else {
        setLoadingByAdvertUpload(false);
      }
      const res = info.file.response || {};
      const { code, content, msg } = res;
      if (code == 200 && content) {
        if (type == 'COVER') {
          // 封面
          setSpaceCoverState({
            ...spaceCoverState,
            spaceCoverUrl: content.fileUrl,
            spaceCoverUrlView: content.fileUrlView,
          });
        } else {
          // 广告
          setSpaceAdvertisingState({
            ...spaceAdvertisingState,
            spaceAdvertisingUrl: content.fileUrl,
            spaceAdvertisingUrlView: content.fileUrlView,
          });
        }
      } else {
        fileList = [];
        message.error(msg || '上传失败');
      }
    }
  };


  // 自定义直播课件上传请求
  const coursewareFileRequest = ({onProgress,onError,onSuccess,data,filename,file,withCredentials,action,headers}) => {
    const fileNameOrigin = file.name                                         // 视频文件原名字
    let fileType = file.type                                                 // 视频文件type类型
    let fileSuffix = fileNameOrigin.substring(file.name.lastIndexOf('.')+1)  // 文件后缀名

    // 请求接口获取上传课件携带签名的ossUrl
    request(`/api/server/base/oss-all-file-signature-upload?fileType=25&contentType=${fileType}&fileSuffix=${fileSuffix}`, {
      method: 'GET',
      headers,
      isNoNeedCommonParams: true,
    }).then(res => {
      const { code, content } = res;
      const url = content.signUrl;                   // 签名url
      const fileName = content.fileName;             // 生成的文件名
      const fileUrlView = content.fileUrlView;       // 文件的全url
      const uploadPath = content.uploadPath;         // 文件的oss地址

      // 使用签名url上传文件
      let request = new XMLHttpRequest()

      request.onreadystatechange = async () => {
        if (request.readyState == 4) { // 数据接收完毕,此时可以通过通过responseBody和responseText获取完整的回应数据
          if (request.status == 200) {
          } else {
            // 上传失败
            onError({
              code: fileName,                       // 唯一值
            })
          }
        }
      }

      request.upload.onprogress = async (e) => {
        if (e.lengthComputable) {
          const percent = (e.loaded / e.total * 100).toFixed(1)
          // onProgress({percent: Number(percent)})
          onProgress({percent: Number(percent / 2)})
          if (percent == 100) { // 完成上传'
            await setTimeout(()=>{},1000);
            let resBySpaceCoursewareTranscode = await spaceCoursewareTranscode({
              coursewareName:fileNameOrigin,
              coursewarePath:uploadPath,
              spaceId:id ? id : null,
            })
            if (resBySpaceCoursewareTranscode && resBySpaceCoursewareTranscode.code == 200) {
              const { spaceCoursewareId } = resBySpaceCoursewareTranscode && resBySpaceCoursewareTranscode.content || {};
              onProgress({percent: Number(50)})
              // 课件转码调用成功后 轮询检查转码结果
              checkTranscodeStatus({
                file:file,
                uploadPath:uploadPath,
                spaceCoursewareId:spaceCoursewareId,
                fileName:fileName,
                fileUrlView:fileUrlView,
                fileNameOrigin:fileNameOrigin,
                onSuccess:onSuccess,   // 上传成功的回调
                onError:onError,       // 上传失败的回调
                onProgress:onProgress, // 上传进行中的回调
              });
            }else {
              // 开始转码失败
              message.error(resBySpaceCoursewareTranscode.msg ? resBySpaceCoursewareTranscode.msg : '课件文件格式不支持!');
              onError({
                code: fileName,                       // 唯一值
              })
            }
          }
        }else {
          onError({
            code: fileName,                       // 唯一值
          })
        }
      }

      // 上传文件
      request.open('PUT', url);                                          // 使用PUT方式上传文件
      request.setRequestHeader('Content-type', fileType);                  // 添加文件类型
      request.setRequestHeader('Access-Control-Allow-Origin', "*");  // 允许跨域
      request.send(new File([file], fileName));
    })
  }

  // 完成OSS上传
  const uploadOnChange = async (info) => {
    // 判断当前上传已被拦截
    if (info && !info.file.status) { return }
    if (info.file.status === 'uploading') {
      setSpaceCoursewares(info.fileList);
    }

    // 上传结束
    if (info && info.file.status === 'error') {
      message.error('上传失败')
      let filterByFileList = info.fileList.filter((item)=>{
        return item.status != 'error'
      })
      setSpaceCoursewares([...filterByFileList]);
      return
    }
    // 上传成功
    if (info && info.file.status === 'done') {
      const res = info.file.response || {}
      if (Array.isArray(info.fileList)) {
        let filterByFileList = info.fileList.filter((item)=>{
          if(item.status != 'done') {
            return true
          }else if(!item.response) {
            return true
          }else {
            return item.response.spaceCoursewareId != res.spaceCoursewareId
          }
        })
        setSpaceCoursewares([...filterByFileList,{
          code: res.code,                       // 唯一值
          spaceCoursewareId: res.spaceCoursewareId,
          src: res.src,                     // 视频url
          name: res.name,                 // 文件原名字
          coursewareName: res.name,        // 源文件名称
          coursewarePathView: res.src,
          coursewarePath: res.uploadPath,
          fileUrlView: res.fileUrlView,
          uploadPath: res.uploadPath,
          percent: 100,   // 上传进度
          status: 'done',
        }]);
      }
    }

  }

  // 轮询检查课件转码结果
  const checkTranscodeStatus = async ({
                                        file,
                                        uploadPath,
                                        spaceCoursewareId,
                                        fileName,
                                        fileUrlView,
                                        fileNameOrigin,
                                        onSuccess,
                                        onProgress,
                                        onError,
  }) => {
    let resBycheckSpaceCoursewareTranscode = await checkSpaceCoursewareTranscode({ coursewarePath: uploadPath });
    if (resBycheckSpaceCoursewareTranscode) {
      if (resBycheckSpaceCoursewareTranscode.code == 200) {
        const {
          transcodeResult, // 转码是否成功 1成功，0正在转码，-1转码失败”
          progress,        // 转码进度 0-100 转码失败是-1
        } = resBycheckSpaceCoursewareTranscode && resBycheckSpaceCoursewareTranscode.content || {};
        if(progress == 100 || progress == -1){
          setLoadingByUpload(false);
        }else {
          setLoadingByUpload(true);
        }
        if (progress && progress != -1) {
          onProgress({percent: Number((progress / 2) + 50)})
        }
        if (transcodeResult == 1) {
          // 轮询结果转码成功
          if (checkTranscodeTimeoutId) {
            clearTimeout(checkTranscodeTimeoutId);
          }
          message.success('课件转码成功!')
          onSuccess({
            code: fileName,                       // 唯一值
            src: fileUrlView,                     // 视频url
            name: fileNameOrigin,                 // 文件原名字
            fileUrlView: fileUrlView,
            uploadPath: uploadPath,
            spaceCoursewareId:spaceCoursewareId,
          })

          // 课件id
          /*let filterByspaceCoursewares = spaceCoursewares.filter((item) => {
            return item.lastModified != file.lastModified;
          })*/

          /*setSpaceCoursewares([...filterByspaceCoursewares, {
            code: fileName,                       // 唯一值
            spaceCoursewareId: spaceCoursewareId,
            src: fileUrlView,                     // 视频url
            name: fileNameOrigin,                 // 文件原名字
            coursewareName: fileNameOrigin,        // 源文件名称
            coursewarePathView: fileUrlView,
            coursewarePath: uploadPath,
            fileUrlView: fileUrlView,
            uploadPath: uploadPath,
            percent: 100,   // 上传进度
            status: 'done',
          }])*/
          // 转码成功
          return resBycheckSpaceCoursewareTranscode;
        } else if (transcodeResult == -1) {
          // 轮询结果转码失败
          if (checkTranscodeTimeoutId) {
            clearTimeout(checkTranscodeTimeoutId);
          }
          message.error('课件转码失败,请更换课件!')
          onError({
            code: fileName,                       // 唯一值
          })
          return null;
        } else {
          // 轮询结果-课件正在转码中
          let checkTranscodeTimeoutId = setTimeout(() => {
            checkTranscodeStatus({
              file,
              uploadPath,
              spaceCoursewareId,
              fileName,
              fileUrlView,
              fileNameOrigin,
              onSuccess,
              onProgress,
              onError,
            });
          }, 3000);
          setCheckTranscodeTimeoutId(checkTranscodeTimeoutId);
        }
      } else {
        // 转码异常 接口报错
        if (checkTranscodeTimeoutId) {
          clearTimeout(checkTranscodeTimeoutId);
        }
        message.error(resBycheckSpaceCoursewareTranscode.msg ? resBycheckSpaceCoursewareTranscode.msg : '课件转码失败,请更换课件!')
        return null;
      }
    } else {
      // 转码异常 接口报错
      if (checkTranscodeTimeoutId) {
        clearTimeout(checkTranscodeTimeoutId);
      }
      message.error(resBycheckSpaceCoursewareTranscode.msg ? resBycheckSpaceCoursewareTranscode.msg : '课件转码失败,请更换课件!')
      return null;
    }
  };



  // 获取空间视频签名
  const getSignature: () => Promise<string> = () => {
    return new Promise((resolve, reject) => {
      dispatch({
        type: 'userInfoStore/getVodUploadSign',
      })
        .then((res) => {
          if (res && res.code === 200) {
            resolve(res.content);
          } else {
            reject();
          }
        })
        .catch(reject);
    });
  };

  // 空间视频上传事件
  const videoFileChange = (e: any) => {
    const { pathname } = window.location;
    if (!e.target.files || !e.target.files[0]) return;
    const file = e.target.files[0];
    const fileType = file.type == 'video/mp4'; // 视频格式
    const oversize = file.size / 1024 / 1024 / 1024 > 2; // 视频大小

    // 判断上传视频是否大于2G或者上传文件是否为mp4格式的，不是则提示
    if (oversize || !fileType) {
      message.error('请上传格式为MP4，大小2G以内的视频');
      e.target.value = ''; // 解决同一个视频重复上传后，导致不再提示
      return;
    }

    // 重置状态
    setVodFileState({
      ...vodFileState,
      vodFileId: null, // 上传视频成功后返回的id
      vodFileUrl: null, // 上传视频后返回的路径
      vodFileName: null, // 视频文件名字
    });

    // 上传签名
    const tcVod = new TcVod({
      getSignature: getSignature, // 获取上传签名的函数
    });
    setUploadProgress(0); // 重置进度
    setUploadVideoLoading(true); // 打开loading

    // 上传视频文件
    const uploader = tcVod.upload({
      mediaFile: file, // 媒体文件（视频或音频或图片），类型为 File
    });

    // 获取上传视频进度
    uploader.addListener('media_progress', (info) => {
      // 如果返回上一个页面时，关闭上传动作
      if (pathname != '/UserInfo/CreateSpaceByPc/Live') {
        uploader.cancel();
        return;
      }
      setUploadProgress(Math.round(info.percent * 100));
    });

    // 上传结束
    uploader
      .done()
      .then((result) => {
        setUploadVideoLoading(false);
        // 如果返回上一个页面时，则静默，不赋值任何内容，放弃本次上传的东西
        if (pathname != '/UserInfo/CreateSpaceByPc/Live') {
          return;
        }

        setVodFileState({
          ...vodFileState,
          vodFileId: result.fileId, // 上传视频成功后返回的id
          vodFileUrl: result.video.url, // 上传视频后返回的路径
          vodFileName: file.name, // 视频文件名字
        });

        setUploadVideoLoading(false);
      })
      .catch((err) => {
        setUploadVideoLoading(false);
        // 如果返回上一个页面时，则静默，不赋值任何内容，放弃本次上传的东西
        if (pathname != '/UserInfo/CreateSpaceByPc/Live') {
          return;
        }
        message.error(err);
      });
    e.target.value = ''; // 解决同一个视频重复上传时不能上传问题
  };

  // 获取视频文件名称，截取后缀名前面的部分内容，以便文件名字过长时，做隐藏处理
  const videoNameModify = () => {
    return (
      vodFileState.vodFileName &&
      vodFileState.vodFileName.substring(0, vodFileState.vodFileName.lastIndexOf('.'))
    );
  };

  // 空间封面 change 事件
  const coverChangeFn = (e) => {
    // 切换到无封面时，清空上传的封面图
    if (e?.target?.value == 1) {
      setSpaceCoverState({
        ...spaceCoverState,
        spaceCoverUrl: null,
        spaceCoverUrlView: null,
      });
      return;
    }
  };

  // 空间广告 change 事件
  const bannerChangeFn = (e) => {
    // 切换到无广告封面时，清空上传的封面图
    if (e?.target?.value == 1) {
      setSpaceAdvertisingState({
        ...spaceAdvertisingState,
        spaceAdvertisingUrl: null,
        spaceAdvertisingUrlView: null,
      });
      return;
    }
  };

  // 空间视频 change 事件
  const videoChangeFn = (e) => {
    // 切换到无空间视频时，重置内容
    if (e?.target?.value == 1) {
      setVodFileState({
        ...vodFileState,
        vodFileId: null, // 上传视频成功后返回的id
        vodFileUrl: null, // 上传视频后返回的路径
        vodFileName: null, // 视频文件名字
      });
      setState({
        ...state,
        isSubmit: false, // 是否点击了提交
      });
    }
  };

  // 解决上传初始化 fileList 报错问题
  const normFile = (e: any) => {
    //如果是typescript, 那么参数写成 e: any
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };

  // 点击观众框
  const onClickSpectator = (disabled) => {
    // 若选择了关联王国，则观众默认为该王国成员
    if (disabled) {
      message.info('已选择关联王国，不能修改观众');
      return;
    }
    setSpectatorState({
      ...spectatorState,
      spectatorDropdownShow: true,
    });
  };

  // 点击观众下拉菜单一级
  const onClickSpectator1 = (id) => {
    if (id == 0) {
      setSpectatorState({
        ...spectatorState,
        spectatorType: id, // 观众类型 0默认所有人 1指定王国成员可见 2指定企业品牌用户可见
        spectatorShowText: '所有人可见', // 选择观众后的展示文案
        spectatorDropdownShow: false, // 观众弹窗是否显示
        spectatorOfBizList: [], // 已选的企业机构数据
        spectatorOfKingdomList: [], // 已选的王国数据
        spectatorType2: null, // 这是索引
      });
    } else {
      setSpectatorState({
        ...spectatorState,
        spectatorType: id, // 观众类型 0默认所有人 1指定王国成员可见 2指定企业品牌用户可见
        spectatorType2: null, // 这是索引
      });
    }
  };

  // 点击观众下拉菜单二级
  const onClickSpectator2 = (index) => {
    setSpectatorState({
      ...spectatorState,
      spectatorType2: index, // 这是索引
    });
  };

  // 观众选择-选择王国
  const onChangeSpectatorOfKingdomList = (e) => {
    const { value, checked } = e.target;
    let spectatorOfKingdomListClone = cloneDeep(spectatorState.spectatorOfKingdomList);
    if (checked) {
      spectatorOfKingdomListClone.push(value);
    } else {
      spectatorOfKingdomListClone = spectatorOfKingdomListClone.filter((item) => item != value);
    }
    setSpectatorState({
      ...spectatorState,
      spectatorOfKingdomList: spectatorOfKingdomListClone,
      spectatorOfBizList: [],
    });
  };

  // 观众选择-企业，选择所有人
  const onChangeSpectatorOfBizListAll = (e) => {
    const { value, checked } = e.target;
    let spectatorOfBizListClone = cloneDeep(spectatorState.spectatorOfBizList);
    const index = spectatorOfBizListClone.findIndex((item) => item.tenantId == value);
    if (index > -1) {
      spectatorOfBizListClone[index] = {
        tenantId: value,
        isAllUser: checked ? 1 : 0,
        areaList: [],
        orgList: [],
      };
    } else {
      spectatorOfBizListClone.push({
        tenantId: value,
        isAllUser: checked ? 1 : 0,
        areaList: [],
        orgList: [],
      });
    }
    setSpectatorState({
      ...spectatorState,
      spectatorOfKingdomList: [],
      spectatorOfBizList: spectatorOfBizListClone,
    });
  };

  // 观众选择-企业，选择区域
  const onChangeAreaList = (e) => {
    const { value, checked, tenantId } = e.target;
    let spectatorOfBizListClone = cloneDeep(spectatorState.spectatorOfBizList);
    const index = spectatorOfBizListClone.findIndex((item) => item.tenantId == tenantId);
    if (index > -1) {
      let areaList = spectatorOfBizListClone[index].areaList;
      if (checked) {
        areaList.push(value);
      } else {
        areaList = areaList.filter((item) => item != value);
      }
      spectatorOfBizListClone[index].areaList = areaList;
    } else {
      spectatorOfBizListClone.push({
        tenantId: tenantId,
        isAllUser: 0,
        areaList: [value],
        orgList: [],
      });
    }
    setSpectatorState({
      ...spectatorState,
      spectatorOfKingdomList: [],
      spectatorOfBizList: spectatorOfBizListClone,
    });
  };

  // 观众选择-企业，选择机构
  const onChangeOrgList = (e) => {
    const { value, checked, tenantId } = e.target;
    let spectatorOfBizListClone = cloneDeep(spectatorState.spectatorOfBizList);
    const index = spectatorOfBizListClone.findIndex((item) => item.tenantId == tenantId);
    if (index > -1) {
      let orgList = spectatorOfBizListClone[index].orgList;
      if (checked) {
        orgList.push(value);
      } else {
        orgList = orgList.filter((item) => item != value);
      }
      spectatorOfBizListClone[index].orgList = orgList;
    } else {
      spectatorOfBizListClone.push({
        tenantId: tenantId,
        isAllUser: 0,
        areaList: [],
        orgList: [value],
      });
    }
    setSpectatorState({
      ...spectatorState,
      spectatorOfKingdomList: [],
      spectatorOfBizList: spectatorOfBizListClone,
    });
  };

  // 点击观众-关闭弹框事件
  const modalCancelFn = () => {
    let spectatorShowText = null;
    let spectatorType = null; // 观众类型 0默认所有人 1指定王国成员可见 2指定企业品牌用户可见

    // 选择王国
    if (spectatorState.spectatorOfKingdomList.length > 0) {
      spectatorShowText = `王国成员可见，已选${spectatorState.spectatorOfKingdomList.length}个王国`;
      spectatorType = 1;
    } else if (spectatorState.spectatorOfBizList.length > 0) {
      // 选择企业
      let sum = 0; // 已选几个分组
      let isAllUser = 1; // 是否选所有人
      spectatorState.spectatorOfBizList.forEach((item) => {
        isAllUser = item.isAllUser;
        if (item.isAllUser == 1) {
          sum = sum + 1;
        } else {
          sum = sum + item.areaList.length + item.orgList.length;
        }
      });
      if (sum == 0 && isAllUser == 0) {
        // 什么都没选
      } else if (isAllUser == 1) {
        // 选择了所有企业
        spectatorShowText = '企业/品牌用户成员可见，已选所有成员';
        spectatorType = 2;
      } else {
        spectatorShowText = `企业/品牌用户成员可见，已选${sum}个分组`;
        spectatorType = 2;
      }
    } else if (spectatorState.spectatorShowText == '所有人可见') {
      // 所有人
      spectatorShowText = '所有人可见';
      spectatorType = 0;
    }

    // 什么都没选
    if (!spectatorShowText) {
      spectatorType = null;
    }

    setSpectatorState({
      ...spectatorState,
      spectatorDropdownShow: false, // 选择观众弹窗是否显示
      spectatorShowText: spectatorShowText, // 选择观众后的展示文案
      spectatorType: spectatorType, // 观众类型 0默认所有人 1指定王国成员可见 2指定企业品牌用户可见
    });
  };

  // 选择王国下拉框
  const onKingdomDropdownVisibleChange = (visible) => {
    setKingdomDropdownRenderVisible(visible);
  };

  // 选择王国下拉框
  const dropdownRender = (originNode) => {
    // return originNode
    return (
      <div className={styles.select_dropdown_container}>
        {createAndJoinKingdomDataSource.length == 0 ? (
          <NoDataRender style={{ marginTop: 0 }} text="当前无可关联王国~" />
        ) : (
          createAndJoinKingdomDataSource.map((item) => {
            return (
              <div key={item.id}>
                <div className={styles.select_dropdown_title}>
                  {item.name}({item.children.length}个)
                </div>
                {item.children.map((itemChild) => {
                  return (
                    <div
                      key={itemChild.id}
                      className={classNames(styles.kingdom_item, {
                        [styles.selected]: state.kingdomId == itemChild.id,
                      })}
                      onClick={() => onChangeKingdomId(itemChild.id, itemChild.name)}
                    >
                      <i
                        style={
                          itemChild.kingdomCoverUrlShow || itemChild.kingImgUrlShow
                            ? {
                                backgroundImage: `url(${
                                  itemChild.kingdomCoverUrlShow || itemChild.kingImgUrlShow
                                })`,
                              }
                            : { background: randomColor(itemChild.wxUserId) }
                        }
                      >
                        {!itemChild.kingdomCoverUrlShow &&
                          !itemChild.kingImgUrlShow &&
                          processNames(itemChild.kingName)}
                      </i>
                      <div className={styles.kingdom_item_details}>
                        <div className={styles.kingdom_name}>{itemChild.name}</div>
                        <div className={styles.kingdom_info_box}>
                          <span>{gdpFormat(itemChild.gdp)}GDP</span>
                          <span>{gdpFormat(itemChild.nationalNum)}国民在交流</span>
                          <span>{itemChild.spaceNum}个热议直播</span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            );
          })
        )}
      </div>
    );
  };

  // 选择王国
  const onChangeKingdomId = (kingdomId, kingdomName) => {
    setValuesNotInForm({
      ...valuesNotInForm,
      kingdomId, // 王国ID
      kingdomName, // 王国name
    });
    setKingdomDropdownRenderVisible(false);
  };

  // 选择王国（清空）
  const onChangeKingdomIdByClear = (value, option) => {
    setValuesNotInForm({
      ...valuesNotInForm,
      kingdomId: null, // 王国ID
      kingdomName: null, // 王国name
    });
  };

  // 禁选日期
  const disabledDate: RangePickerProps['disabledDate'] = (current) => {
    // Can not select days before today and today
    // console.log(current.format('YYYY-MM-DD HH:mm:ss'),dayjs().subtract(1, "days").format('YYYY-MM-DD HH:mm:ss'))
    return current && current < dayjs(dayjs().format('YYYY-MM-DD') + ' 00:00:00');
  };

  // 禁选时间
  const getDisabledTime = (now) => {
    const nowDate = now.date();

    // 未选日期、或者选择的日期不是今天，则时间全部可选
    if (
      !form.getFieldValue('appointmentStartTime_date') ||
      form.getFieldValue('appointmentStartTime_date').date() != nowDate
    ) {
      return {};
    }

    // 日期选择今天时，已经过去的时间不可选择
    const nowHour = now.hour();
    let disabledHours = [];
    for (let i = 0; i < 24; i++) {
      if (i < nowHour) {
        disabledHours.push(i);
      } else {
        break;
      }
    }
    return {
      disabledHours: () => disabledHours,
      disabledMinutes: (selectedHour: number) => {
        if (selectedHour == nowHour) {
          const nowMinute = now.minute();
          let disabledMinutes = [];
          for (let i = 0; i < 60; i++) {
            if (i < nowMinute) {
              disabledMinutes.push(i);
            } else {
              break;
            }
          }
          return disabledMinutes;
        }
        return [];
      },
    };
  };

  // 选择日期时，清空时间
  const appointmentStartTime_dateOnChange = (value, a, b) => {
    form.setFieldValue('appointmentStartTime_time', undefined);
  };

  // 编辑事件
  const editSpaceInfo = (values) => {
    let appointmentStartTime = null;
    // 预约开始的，处理时间数据
    if (values.startStatus == 2) {
      appointmentStartTime =
        values.appointmentStartTime_date.format('YYYY-MM-DD') +
        ' ' +
        values.appointmentStartTime_time.format('HH:mm:ss');
    }
    const idByspaceCoursewares = Array.isArray(spaceCoursewares) && spaceCoursewares.map((it: { spaceCoursewareId: any }) => it.spaceCoursewareId)
    dispatch({
      type: 'userInfoStore/editSpaceInfo',
      payload: {
        consultationId: consultationId || undefined, // 指导id(创建视频指导时需必传)
        id, // 空间ID
        name: values.name, // 空间名称
        password: values.password || null, // 密码
        startStatus: values.startStatus, // 开始状态：1立刻、2预约
        appointmentStartTime: appointmentStartTime, // 预约开始时间
        spaceCoverUrl: spaceCoverState.spaceCoverUrl, // 空间封面路径
        spaceCoverList: !!spaceCoverState.spaceCoverUrl
          ? [
              {
                spaceCoverUrl: spaceCoverState.spaceCoverUrl, // string 必须 空间封面路径
                isSelect: 1, // number 必须 是否选中封面
              },
            ]
          : [],
        spaceCoursewares: idByspaceCoursewares, // 空间课件id集合
        spaceAdvertisingUrl: spaceAdvertisingState.spaceAdvertisingUrl, // 空间广告路径
        updateUserId: UerInfo && UerInfo.friUserId, // 操作人用户ID
        guestIdList: values.guestIdList || [], // 嘉宾id集合
        vodFileId: vodFileState.vodFileId, // 空间视频id
        intro: values.intro, // 空间介绍
        spectatorType: spectatorState.spectatorType || 0, // 观众类型 0默认所有人 1指定王国成员可见 2指定企业品牌用户可见
        spaceType: spectatorState.spectatorType == 2 ? values.spaceType : null, // 空间类型 1专家讲课 2指导 3复杂病例讨论
        spectatorOfKingdomList:
          valuesNotInForm.kingdomId || valuesNotInForm.kingdomId == 0
            ? [valuesNotInForm.kingdomId]
            : spectatorState.spectatorOfKingdomList, // 指定王国成员时传参
        spectatorOfBizList: spectatorState.spectatorOfBizList, // 指定企业品牌用户时传参
        starSpaceType: spectatorState.starSpaceType, // 类型：1 直播，2 会议
        isNoPasswordApply: spectatorState.isNoPasswordApply, // 是否开启无密码申请：0不开启，1
      },
    })
      .then((res) => {
        const { code, content, msg } = res;
        if (code == 200) {
          // 打开创建成功弹窗
          setModalState({
            ...modalState,
            createSpaceSuccessVisible: true,
            spaceId: content,
            starSpaceType: spectatorState.starSpaceType,
          });
        } else {
          message.error(msg || '数据加载失败');
        }
      })
      .catch((err) => {});
  };

  // 创建空间
  const addSpace = (values) => {
    let appointmentStartTime = null;
    if (values.startStatus == 2) {
      appointmentStartTime =
        values.appointmentStartTime_date.format('YYYY-MM-DD') +
        ' ' +
        values.appointmentStartTime_time.format('HH:mm:ss');
    }
    const idByspaceCoursewares = Array.isArray(spaceCoursewares) && spaceCoursewares.map((it: { spaceCoursewareId: any }) => it.spaceCoursewareId)
    dispatch({
      type: 'userInfoStore/addSpace',
      payload: {
        consultationId: consultationId || undefined, // 指导id，从指导创建的必传
        name: values.name, // 直播、会议名称
        wxUserId: values.wxUserId != undefined ? values.wxUserId : UerInfo?.friUserId, // 主持人id
        hostName: values.wxUserId != undefined ? valuesNotInForm.hostName : UerInfo?.name, // 主持人名称
        kingdomId: valuesNotInForm.kingdomId, // 关联王国id
        kingdomName: valuesNotInForm.kingdomName, // 关联王国名称
        password: values.password || null, // 密码
        startStatus: values.startStatus, // 开始状态：1立刻、2预约
        appointmentStartTime: appointmentStartTime, // 预约开始时间
        spaceCoverUrl: spaceCoverState.spaceCoverUrl, // 空间封面路径
        spaceCoverList: !!spaceCoverState.spaceCoverUrl
          ? [
              {
                spaceCoverUrl: spaceCoverState.spaceCoverUrl, // string 必须 空间封面路径
                isSelect: 1, // number 必须 是否选中封面
              },
            ]
          : [],
        spaceAdvertisingUrl: spaceAdvertisingState.spaceAdvertisingUrl, // 空间广告路径
        spaceCoursewares: idByspaceCoursewares, // 空间课件id集合
        updateUserId: UerInfo?.friUserId, // 操作人用户ID
        guestIdList: values.guestIdList || [], // 嘉宾id集合
        vodFileId: vodFileState.vodFileId, // 空间视频id
        intro: values.intro, // 空间介绍
        spectatorType:
          valuesNotInForm.kingdomId || valuesNotInForm.kingdomId == 0
            ? 1
            : spectatorState.spectatorType || 0, // 观众类型 0默认所有人 1指定王国成员可见 2指定企业品牌用户可见
        spaceType: spectatorState.spectatorType == 2 ? values.spaceType : null, // 空间类型 1专家讲课 2指导 3复杂病例讨论
        spectatorOfKingdomList:
          valuesNotInForm.kingdomId || valuesNotInForm.kingdomId == 0
            ? [valuesNotInForm.kingdomId]
            : spectatorState.spectatorOfKingdomList, // 指定王国成员时传参
        spectatorOfBizList: spectatorState.spectatorOfBizList, // 指定企业品牌用户时传参
        isAutoRecord: values.isAutoRecord ? 1 : 0, // 是否开启自动录播 1:开启  0或其他关闭
        starSpaceType: 1, // 类型：1 直播，2 会议 PC只有创建直播类型
      },
    })
      .then((res) => {
        const { code, content, msg } = res;
        if (code == 200) {
          // 打开创建成功弹窗
          setModalState({
            ...modalState,
            createSpaceSuccessVisible: true,
            spaceId: content,
            starSpaceType: 1, // 类型：1 直播，2 会议 PC只有创建直播类型
          });
        } else {
          message.error(msg || '数据加载失败');
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  // 点击确定
  const submit = () => {
    // 图片、视频在上传的时候不可点击，上传完后才可点击
    if (uploadVideoLoading || loadingByUpload || loadingByAdvertUpload) {
      return;
    }

    const uploadingBySpaceCoursewares = Array.isArray(spaceCoursewares) && spaceCoursewares.filter((it) => it.status == 'uploading')
    if (Array.isArray(uploadingBySpaceCoursewares) && uploadingBySpaceCoursewares.length > 0) {
      message.warning('课件正在上传中,请稍后再试!');
      return;
    }

    setState({
      ...state,
      isSubmit: true, // 是否点击了提交
    });
    form
      .validateFields()
      .then((values) => {
        if (id) {
          editSpaceInfo(values);
        } else {
          addSpace(values);
        }
      })
      .catch((err) => {
        console.log(err, '有没有捕获到哟');
      });
  };

  // 返回
  const goBack = () => {
    dispatch({ type: 'userInfoStore/clean' });

    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'goBack',       // 页面地址onchange事件
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
      return
    }

    if (history.length > 1) {
      history.goBack();
    } else {
      history.replace('/');
    }
  };

  // 生成海报弹窗打开
  const handleGetPoster = () => {
    setModalState({
      ...modalState,
      createSpaceSuccessVisible: false,
      posterVisible: true,
    });
  };

  // 编辑图片，弹出裁剪弹框
  const croppingImageModalShow = (
    croppingImageUrlShow,
    croppingImageType,
    croppingImageUploadFileType,
  ) => {
    setModalState({
      ...modalState,
      croppingImageVisible: true, // 是否展示裁剪弹框
      croppingImageUrlShow, // 裁剪图片地址
      croppingImageType, // COVER 封面，ADVERT 广告
      croppingImageUploadFileType, // 14 封面，15 广告
    });
  };

  // 裁剪弹框关闭事件
  const croppingImageModalHide = () => {
    setModalState({
      ...modalState,
      croppingImageVisible: false, // 是否展示裁剪弹框
      croppingImageUrlShow: null, // 裁剪图片地址
      croppingImageType: null, // COVER 封面，ADVERT 广告
      croppingImageUploadFileType: null, // 14 封面，15 广告
    });
  };

  // 图片裁剪，确定回调
  const handleCroppingImage = (imageUrl, imageUrlShow) => {
    // 封面
    if (modalState.croppingImageType == 'COVER') {
      setSpaceCoverState({
        ...spaceCoverState,
        spaceCoverUrl: imageUrl,
        spaceCoverUrlView: imageUrlShow,
      });
    } else if (modalState.croppingImageType == 'ADVERT') {
      // 广告
      setSpaceAdvertisingState({
        ...spaceAdvertisingState,
        spaceAdvertisingUrl: imageUrl,
        spaceAdvertisingUrlView: imageUrlShow,
      });
    }
    croppingImageModalHide();
  };

  // 创建空间loading
  const loadingGetSpaceInfoByEdit = !!loading.effects['userInfoStore/getSpaceInfoByEdit'];
  const loadingAddSpace = !!loading.effects['userInfoStore/addSpace'];
  const loadingEditSpaceInfo = !!loading.effects['userInfoStore/editSpaceInfo'];
  const loadingCheckSuperAccount = !!loading.effects['userInfoStore/checkSuperAccount'];
  const loadingGetCreateAndJoinKingdomList =
    !!loading.effects['userInfoStore/getCreateAndJoinKingdomList'];
  const loadingGetBizGroupByTenantId = !!loading.effects['userInfoStore/getBizGroupByTenantId'];

  return (
    <>
      <Helmet>
        <title>{`${idByCreateText}${starSpaceTypeText}`}</title>
      </Helmet>

      <Spin
        spinning={
          loadingCheckSuperAccount ||
          loadingGetCreateAndJoinKingdomList ||
          loadingGetBizGroupByTenantId ||
          loadingGetSpaceInfoByEdit
        }
      >
        <div className={styles.container}>
          {/* iframe中隐藏header */}
          {
            isInIframe ? null : <PcHeader />
          }

          {/* 中间内容 */}
          <div className={styles.inner_container}>
            <div className={styles.wrap}>
              {/* 标题 */}
              <div className={styles.header}>
                <div className={styles.header_title} onClick={goBack}>
                  <img className={styles.header_title_icon} src={pcGobackIcon} alt="" />
                  {idByCreateText}
                  {starSpaceTypeText}
                </div>
              </div>

              {/* 表单 */}
              <div className={styles.content}>
                <Form form={form} component={false} autoComplete="off">
                  <div className={styles.form_item_name}>
                    {/* 空间封面 */}
                    <div
                      onClick={() => {
                        setModalState({
                          ...modalState,
                          createSpaceSuccessVisible: false,
                          posterVisible: false,
                          croppingImageVisible: false,
                          selectCoverModalVisible: true,
                          spaceName: form.getFieldValue('name'),
                        });
                      }}
                      className={styles.form_box_img}
                    >
                      <div className={styles.form_box_img_box}>
                        {spaceCoverState && spaceCoverState.spaceCoverUrlView && (
                          <img src={spaceCoverState.spaceCoverUrlView} alt={''} />
                        )}
                        <div className={styles.space_form_box_btn}>更换</div>
                      </div>
                    </div>

                    {/* 空间名称 */}
                    <div className={styles.form_input}>
                      <Form.Item
                        name="name"
                        validateFirst
                        rules={[
                          {
                            required: true,
                            whitespace: true,
                            message: `请输入${starSpaceTypeText}名称`,
                          },
                          {
                            min: 2,
                            max: 50,
                            message: `请输入${starSpaceTypeText}名称（2-50个字）`,
                            transform: (value) => (value ? value.trim() : value),
                          },
                        ]}
                      >
                        <Input
                          bordered={false}
                          autoComplete="off"
                          placeholder={`请输入${starSpaceTypeText}名称（2-50个字）`}
                          maxLength={50}
                        />
                      </Form.Item>
                    </div>
                  </div>

                  {/* 空间介绍 */}
                  <div className={styles.form_text_area}>
                    <Form.Item
                      name="intro"
                      rules={[
                        {
                          pattern:
                            /^[~\s!！@#$￥%^…&*()_—+{}：:‘’“”《》？"|<>?`\-=[\];',.。，；（）、a-zA-Z0-9\u4e00-\u9fa5/]+$/,
                          message: `${starSpaceTypeText}介绍不能包含特殊字符`,
                        },
                        {
                          max: 100,
                          message: '不能超过100个字',
                          transform: (value) => (value ? value.trim() : value),
                        },
                      ]}
                    >
                      <TextArea
                        bordered={false}
                        style={{ resize: 'none', height: '100px' }}
                        showCount
                        maxLength={100}
                        placeholder={`${starSpaceTypeText}介绍：详细描述${starSpaceTypeText}分享的话题内容~`}
                      />
                    </Form.Item>
                  </div>

                  <div className={styles.from_wrap}>
                    {/* 主持人 */}
                    {state.isSuperAccount && !id && (
                      <div className={styles.from_content}>
                        <div className={styles.from_title}>主持人</div>
                        <div className={styles.from_desc} id="hostSelect">
                          <Form.Item name="wxUserId">
                            <Select
                              getPopupContainer={() => document.getElementById('hostSelect')}
                              style={{ width: 400 }}
                              showSearch={false}
                              allowClear
                              placeholder="请选择"
                              onChange={onChangeHost}
                              onBlur={onBlurHostOrGuestSearchInput}
                              dropdownRender={(menu) => (
                                <div className={styles.select_dropdown_content}>
                                  <div className={styles.dropdown_content_input}>
                                    <img src={searchIcon} alt="" />
                                    <Input
                                      bordered={false}
                                      placeholder="输入关键字搜索"
                                      value={hostOrGuestQueryKey}
                                      onChange={onChangeHostOrGuestQueryKey}
                                    />
                                  </div>
                                  {menu}
                                </div>
                              )}
                              optionLabelProp="title"
                            >
                              {Array.isArray(hostOrGuestDataSource) &&
                              hostOrGuestDataSource.length ? (
                                <>
                                  {hostOrGuestDataSource.map((item, index) => (
                                    <Option key={item.id} value={item.id} title={item.name}>
                                      <div className={styles.select_option_content}>
                                        <span
                                          className={styles.option_content_name}
                                          style={{ maxWidth: 190 }}
                                        >
                                          {item.name}
                                        </span>
                                        <span className={styles.option_content_phone}>
                                          {item.phone}
                                        </span>
                                        <span className={styles.option_content_img}>
                                          <img src={selectedIcon} alt="icon" />
                                        </span>
                                      </div>
                                    </Option>
                                  ))}
                                </>
                              ) : null}
                            </Select>
                          </Form.Item>
                        </div>
                      </div>
                    )}

                    {/* 邀请嘉宾 */}
                    <div className={styles.from_content}>
                      <div className={styles.from_title}>邀请嘉宾</div>
                      <div className={styles.from_desc} id="guestSelect">
                        <Form.Item
                          noStyle
                          shouldUpdate={(prevValues, currentValues) =>
                            prevValues.guestIdList &&
                            currentValues.guestIdList &&
                            prevValues.guestIdList.length !== currentValues.guestIdList.length
                          }
                        >
                          {({ getFieldValue }) => (
                            <Form.Item name="guestIdList">
                              <Select
                                getPopupContainer={() => document.getElementById('guestSelect')}
                                style={{ width: 400 }}
                                showSearch={false}
                                mode="multiple"
                                allowClear
                                maxTagCount={9}
                                optionLabelProp="title"
                                placeholder="最多可邀请15位嘉宾"
                                onBlur={onBlurHostOrGuestSearchInput}
                                dropdownRender={(menu) => (
                                  // onKeyDown事件不可删除，会导致input删除内容时，触发select已选中的内容被删除的bug
                                  <div
                                    className={styles.select_dropdown_content}
                                    onKeyDown={(e) => {
                                      e.stopPropagation();
                                    }}
                                  >
                                    <div className={styles.dropdown_content_input}>
                                      <img src={searchIcon} alt="" />
                                      <Input
                                        bordered={false}
                                        placeholder="输入关键字搜索"
                                        value={hostOrGuestQueryKey}
                                        onChange={onChangeHostOrGuestQueryKey}
                                      />
                                    </div>
                                    <div className={styles.dropdown_content_title}>
                                      邀请嘉宾
                                      {getFieldValue('guestIdList')
                                        ? getFieldValue('guestIdList').length
                                        : 0}
                                      /15
                                    </div>
                                    {menu}
                                  </div>
                                )}
                              >
                                {Array.isArray(hostOrGuestDataSource) &&
                                hostOrGuestDataSource.length ? (
                                  <>
                                    {hostOrGuestDataSource.map((item, index) => (
                                      <Option
                                        key={item.id}
                                        value={item.id}
                                        title={item.name}
                                        disabled={
                                          getFieldValue('guestIdList') &&
                                          getFieldValue('guestIdList').length >= 15
                                        }
                                      >
                                        <div className={styles.select_option_content}>
                                          <span className={styles.option_content_name}>
                                            {item.name}
                                          </span>
                                          <span className={styles.option_content_img}>
                                            <img src={selectedIcon} alt="icon" />
                                          </span>
                                        </div>
                                      </Option>
                                    ))}
                                  </>
                                ) : null}
                              </Select>
                            </Form.Item>
                          )}
                        </Form.Item>
                      </div>
                    </div>

                    {/* 直播设置 */}
                    <div
                      onClick={() => {
                        // setShowLiveSettings(!showLiveSettings);
                      }}
                      className={styles.title_item_box}
                    >
                      <div className={styles.title_item}>直播设置</div>
                      <div
                        onClick={() => {
                          setShowLiveSettings(!showLiveSettings);
                        }}
                        className={styles.title_item_btn}
                      >
                        {!showLiveSettings && <UpOutlined />}
                        {showLiveSettings && <DownOutlined />}
                      </div>
                    </div>

                    <div
                      className={classNames({
                        [styles.from_box_warp_hidden]: !!showLiveSettings,
                      })}
                    >
                      {/* 空间密码 */}
                      <div className={styles.from_content}>
                        <div className={styles.from_title}>{starSpaceTypeText}密码</div>
                        <div className={styles.from_switch}>
                          <Form.Item
                            initialValue={consultationId ? true : false}
                            name="passwordType"
                            valuePropName={'checked'}
                          >
                            {/* <Radio.Group disabled={!!id}>
                            <Radio value={1}>无{starSpaceTypeText}密码</Radio>
                            <Radio value={2}>有{starSpaceTypeText}密码</Radio>
                          </Radio.Group> */}

                            <Switch disabled={!!id} onChange={(value, tag) => {}} />
                          </Form.Item>
                        </div>
                      </div>

                      {/* 空间密码输入框 */}
                      <Form.Item
                        noStyle
                        shouldUpdate={(prevValues, currentValues) =>
                          prevValues.passwordType !== currentValues.passwordType
                        }
                      >
                        {({ getFieldValue }) =>
                          !!getFieldValue('passwordType') ? (
                            <div className={styles.space_password_wrap}>
                              <Form.Item
                                name="password"
                                rules={[
                                  { required: true, message: `请输入${starSpaceTypeText}密码` },
                                  { pattern: /^[0-9]{4}$/, message: '请输入4位数字密码' },
                                ]}
                              >
                                <Input
                                  style={{ width: 400 }}
                                  maxLength={4}
                                  autoComplete="off"
                                  placeholder={`请输入${starSpaceTypeText}密码`}
                                  disabled={!!id}
                                />
                              </Form.Item>
                            </div>
                          ) : null
                        }
                      </Form.Item>

                      {/* 屏幕录制 */}
                      <div className={styles.from_content}>
                        <div className={styles.from_title}>屏幕录制</div>
                        <div className={styles.from_switch}>
                          <Form.Item
                            initialValue={false}
                            name="isAutoRecord"
                            valuePropName={'checked'}
                          >
                            <Switch onChange={(value, tag) => {}} />
                          </Form.Item>
                        </div>
                      </div>

                      {/* 空间广告 */}
                      {/*<div className={styles.from_content}>
                      <div className={styles.from_title}>{starSpaceTypeText}广告</div>
                      <div className={styles.from_radio}>
                        <Form.Item
                          initialValue={1}
                          name="spaceAdvertisingUrlType"
                        >
                          <Radio.Group onChange={bannerChangeFn}>
                            <Radio value={1}>无广告</Radio>
                            <Radio value={2}>有广告</Radio>
                          </Radio.Group>
                        </Form.Item>
                      </div>
                    </div>*/}

                      <div className={styles.from_content}>
                        <div className={styles.from_title}>{starSpaceTypeText}广告</div>
                        <div className={styles.from_radio}>
                          {/* 上传空间广告 */}
                          <Form.Item
                            noStyle
                            shouldUpdate={(prevValues, currentValues) =>
                              prevValues.spaceAdvertisingUrlType !==
                              currentValues.spaceAdvertisingUrlType
                            }
                          >
                            <div className={styles.upload_banner_wrap}>
                              {spaceAdvertisingState.spaceAdvertisingUrlView ? (
                                <div className={styles.upload_banner_box}>
                                  {/* 展示图片 */}
                                  <Spin spinning={loadingByAdvertUpload}>
                                    <img
                                      className={styles.upload_banner_img}
                                      src={spaceAdvertisingState.spaceAdvertisingUrlView}
                                      alt=""
                                    />

                                    <div className={styles.edit_btn_box}>
                                      <div
                                        onClick={() =>
                                          croppingImageModalShow(
                                            spaceAdvertisingState.spaceAdvertisingUrlView,
                                            'ADVERT',
                                            15,
                                          )
                                        }
                                      >
                                        编辑
                                      </div>

                                      <Upload
                                        headers={getHeaders()}
                                        accept="image/*"
                                        action={`/api/server/base/uploadFile?${stringify({
                                          fileType: 15,
                                          userId: UerInfo?.friUserId,
                                        })}`}
                                        // listType="picture"
                                        name="file"
                                        className={styles.edit_head_picture}
                                        onChange={(info) => onChangeByUpload(info, 'ADVERT')}
                                        onRemove={() => {}}
                                        beforeUpload={beforeUpload}
                                        showUploadList={false}
                                      >
                                        <div>替换</div>
                                      </Upload>
                                    </div>
                                  </Spin>
                                </div>
                              ) : (
                                <Form.Item
                                  name="spaceAdvertisingImage"
                                  validateFirst={true}
                                  // valuePropName="bannerFileList"
                                  preserve={false}
                                  getValueFromEvent={normFile}
                                  rules={
                                    [
                                      /* { required: true, message: `请上传${starSpaceTypeText}广告` }, */
                                    ]
                                  }
                                >
                                  <Upload
                                    headers={getHeaders()}
                                    accept="image/*"
                                    action={`/api/server/base/uploadFile?${stringify({
                                      fileType: 15,
                                      userId: UerInfo?.friUserId,
                                    })}`}
                                    listType="picture-card"
                                    // className={styles.edit_head_picture}
                                    onChange={(info) => onChangeByUpload(info, 'ADVERT')}
                                    onRemove={() => {}}
                                    beforeUpload={beforeUpload}
                                    showUploadList={false}
                                  >
                                    <Spin spinning={loadingByAdvertUpload}>
                                      <div className={styles.upload_banner_box}>
                                        <img
                                          className={styles.init_upload_banner_img}
                                          src={uploadImg}
                                          alt=""
                                        />
                                      </div>
                                    </Spin>
                                  </Upload>
                                </Form.Item>
                              )}
                              <div className={styles.tips}>
                                格式支持JPEG、PNG，建议尺寸：750*114
                              </div>
                            </div>
                          </Form.Item>
                        </div>
                      </div>

                      {/* 空间视频 */}
                      {!consultationId && (
                        <div className={styles.from_content}>
                          <div className={styles.from_title}>{starSpaceTypeText}视频</div>
                          <div className={styles.from_radio}>
                            {/* <Form.Item
                            initialValue={1}
                            name="spaceVideoType"
                          >
                            <Radio.Group onChange={videoChangeFn}>
                              <Radio value={1}>无{starSpaceTypeText}视频</Radio>
                              <Radio value={2}>有{starSpaceTypeText}视频</Radio>
                            </Radio.Group>
                          </Form.Item> */}

                            {/* 上传空间视频 */}
                            <Form.Item
                              noStyle
                              shouldUpdate={(prevValues, currentValues) =>
                                prevValues.spaceVideoType !== currentValues.spaceVideoType
                              }
                            >
                              <div className={styles.upload_video_wrap}>
                                {
                                  // 视频上传完后展示的样式
                                  vodFileState.vodFileUrl ? (
                                    <div className={classNames(styles.upload_video_content)}>
                                      <div className={styles.uploading_icon_content}>
                                        <img
                                          src={frameIcon}
                                          className={styles.init_upload_img}
                                          alt=""
                                        />
                                        <div className={styles.video_name_text}>
                                          <span className={styles.uploading_name_text}>
                                            {videoNameModify()}
                                          </span>
                                          .mp4
                                        </div>
                                      </div>

                                      <div className={styles.edit_btn_box}>
                                        <div className={styles.edit_btn_input}>
                                          替换
                                          <input
                                            type="file"
                                            name="uploadVideoInput"
                                            id="uploadVideoInput"
                                            accept="video/mp4"
                                            className={styles.upload_video_input}
                                            onChange={videoFileChange}
                                            disabled={uploadVideoLoading}
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  ) : (
                                    // 未上传默认样式以及上传中的样式
                                    <div className={classNames(styles.upload_video_content)}>
                                      {uploadVideoLoading ? (
                                        // 上传中
                                        <div
                                          className={styles.uploading_icon_content}
                                          style={{ background: 'none' }}
                                        >
                                          <img
                                            src={frameIcon}
                                            className={styles.init_upload_img}
                                            alt=""
                                          />
                                          <div className={styles.uploading_icon_text}>
                                            上传{uploadProgress}%...
                                          </div>
                                        </div>
                                      ) : (
                                        // 未上传
                                        <div className={styles.uploading_init_icon_content}>
                                          <img
                                            className={styles.init_upload_img}
                                            src={uploadImg}
                                            alt=""
                                          />
                                        </div>
                                      )}
                                      <input
                                        type="file"
                                        name="uploadVideoInput"
                                        id="uploadVideoInput"
                                        accept="video/mp4"
                                        className={styles.upload_video_input}
                                        onChange={videoFileChange}
                                        disabled={uploadVideoLoading}
                                      />
                                    </div>
                                  )
                                }
                                {/*{
                                !vodFileState.vodFileUrl && state.isSubmit &&
                                <div className={styles.form_error_message}>请上传视频</div>
                              }*/}

                                <div className={styles.tips}>支持MP4，大小2G以内</div>
                              </div>
                            </Form.Item>
                          </div>
                        </div>
                      )}


                      {/* 直播课件 */}
                      {!consultationId && (
                        <div style={{marginBottom:'20px'}} className={styles.from_content}>
                          <div className={styles.from_title}>{starSpaceTypeText}课件</div>
                          <div className={styles.from_radio}>
                            <Form.Item
                              initialValue={1}
                              name="spaceCoursewareType"
                            >
                              <Radio.Group
                                disabled={(!!id && vodFileState && vodFileState.vodFileUrl)}
                                onChange={(value)=>{
                                if(value.target.value == 2){
                                  if (checkTranscodeTimeoutId) { clearTimeout(checkTranscodeTimeoutId); }
                                  setSpaceCoursewares([])
                                }
                              }}>
                                <Radio value={1}>无课件</Radio>
                                <Radio value={2}>有课件</Radio>
                              </Radio.Group>
                            </Form.Item>

                            {/* 上传直播课件 */}
                            {spaceCoursewareType == 2 &&
                              <Upload
                                disabled={(!!id && vodFileState && vodFileState.vodFileUrl)}
                                accept='.pdf,.ppt,.doc,.docx,.pptx' // 限制只能上传PDF文件
                                showUploadList={true}
                                beforeUpload={beforeCoursewareUpload}
                                headers={getHeaders()}
                                customRequest={coursewareFileRequest}
                                onChange={(info) => uploadOnChange(info)}
                                onRemove={(info) => {
                                  let filterByspaceCoursewares = spaceCoursewares.filter((item) => {
                                    return item.src != info.src;
                                  });
                                  setSpaceCoursewares([...filterByspaceCoursewares])
                                  if (checkTranscodeTimeoutId) { clearTimeout(checkTranscodeTimeoutId) };
                                }}
                                fileList={spaceCoursewares}
                              >
                                <Spin spinning={loadingByAdvertUpload}>
                                  <div>
                                    <div className={styles.upload_banner_courseware}>
                                      <img
                                        className={styles.init_upload_img}
                                        src={uploadImg}
                                        alt=""
                                      />
                                    </div>
                                    <div className={styles.init_upload_tip}>
                                      支持上传PPT、Word、PDF，大小100m以内，最多上传3个课件 <br/>部分ppt动效、fash动画不支持上传，建议您在上传前检查并移除这些元素，以确保最佳的展示效果。谢谢您的合作!
                                    </div>
                                  </div>
                                </Spin>
                              </Upload>
                            }
                            </div>
                        </div>
                      )}




                      {/* 关联王国 */}
                      {!consultationId && (
                        <div className={styles.from_content}>
                          <div className={styles.from_title}>关联王国</div>
                          <div className={styles.from_desc} id="kingdomId">
                            <Form.Item>
                              <Select
                                getPopupContainer={() => document.getElementById('kingdomId')}
                                popupClassName={styles.custom_dropdown_render}
                                placeholder={
                                  createAndJoinKingdomDataSource.length == 0
                                    ? '当前无可关联王国'
                                    : '不关联王国，默认只发布在个人主页'
                                }
                                style={{ width: 400 }}
                                value={valuesNotInForm.kingdomId}
                                listHeight={256}
                                fieldNames={{ label: 'name', value: 'id', options: 'children' }}
                                options={createAndJoinKingdomDataSource}
                                dropdownRender={dropdownRender}
                                open={kingdomDropdownRenderVisible}
                                onDropdownVisibleChange={onKingdomDropdownVisibleChange}
                                disabled={
                                  spectatorState.spectatorType ||
                                  spectatorState.spectatorType == 0 ||
                                  !!id
                                }
                                allowClear
                                onChange={onChangeKingdomIdByClear}
                              ></Select>
                            </Form.Item>
                          </div>
                        </div>
                      )}

                      {/* 开始时间 */}
                      <div className={styles.from_content}>
                        <div className={styles.from_title}>开始时间</div>
                        <div className={styles.from_radio}>
                          <Form.Item initialValue={2} name="startStatus">
                            <Radio.Group>
                              <Radio value={1}>马上开始</Radio>
                              <Radio value={2}>预约开始</Radio>
                            </Radio.Group>
                          </Form.Item>
                        </div>
                      </div>

                      {/* 选择预约时间 */}
                      <Form.Item
                        noStyle
                        shouldUpdate={(prevValues, currentValues) =>
                          prevValues.startStatus !== currentValues.startStatus
                        }
                      >
                        {({ getFieldValue }) =>
                          getFieldValue('startStatus') == 2 ? (
                            <div className={styles.start_date_time_wrap}>
                              <Form.Item
                                style={{ marginRight: 12 }}
                                name="appointmentStartTime_date"
                                rules={[{ required: true, message: '请选择日期' }]}
                              >
                                <DatePicker
                                  placeholder="选择日期"
                                  format="YYYY-MM-DD"
                                  disabledDate={disabledDate}
                                  onChange={appointmentStartTime_dateOnChange}
                                />
                              </Form.Item>
                              <Form.Item
                                name="appointmentStartTime_time"
                                rules={[{ required: true, message: '请选择时间' }]}
                              >
                                <TimePicker
                                  placeholder="选择时间"
                                  showNow={false}
                                  format="HH:mm"
                                  disabledTime={getDisabledTime}
                                />
                              </Form.Item>
                            </div>
                          ) : null
                        }
                      </Form.Item>
                    </div>
                  </div>
                </Form>
              </div>
            </div>
          </div>

          {/* 底部按钮 */}
          <div className={styles.footer}>
            <div className={styles.footer_content}>
              <Button onClick={goBack} style={{ marginRight: '24px' }}>
                取消
              </Button>
              <Button
                type="primary"
                onClick={submit}
                loading={loadingAddSpace || loadingEditSpaceInfo}
              >
                确定
              </Button>
            </div>
          </div>
        </div>
      </Spin>

      {/* 图片裁剪弹窗 */}
      {modalState.croppingImageVisible && (
        <CroppingImageModal
          visible={modalState.croppingImageVisible}
          imageUrlShow={modalState.croppingImageUrlShow}
          uploadFileType={modalState.croppingImageUploadFileType}
          handleCroppingImage={handleCroppingImage}
          onCancel={croppingImageModalHide}
        />
      )}

      {/* 创建成功弹窗 */}
      {modalState.createSpaceSuccessVisible && (
        <CreateSpaceSuccessModal
          editId={id}
          visible={modalState.createSpaceSuccessVisible}
          spaceId={modalState.spaceId}
          starSpaceType={modalState.starSpaceType}
          onCancel={goBack}
          handleGetPoster={handleGetPoster}
        />
      )}

      {/* 生成海报弹窗 */}
      {modalState.posterVisible && (
        <PosterModalByPc
          visible={modalState.posterVisible}
          spaceId={modalState.spaceId}
          onCancel={goBack}
        />
      )}

      {/* 选择封面弹窗 */}
      <SelectCoverModal
        visible={modalState.selectCoverModalVisible}
        spaceId={modalState.spaceId}
        spaceName={modalState.spaceName}
        spaceCoverState={spaceCoverState}
        starSpaceType={1} // 直播模式
        onCancel={() => {
          setModalState({
            ...modalState,
            selectCoverModalVisible: false,
          });
        }}
        onSelect={(selectByObj) => {
          setModalState({
            ...modalState,
            selectCoverModalVisible: false,
          });
          setSpaceCoverState({
            ...spaceCoverState,
            spaceCoverUrl: selectByObj.spaceCoverUrl,
            spaceCoverUrlView: selectByObj.showSpaceCoverUrl,
          });
        }}
      />
    </>
  );
};
export default connect(({ userInfoStore, loading }: any) => ({ userInfoStore, loading }))(Index);
