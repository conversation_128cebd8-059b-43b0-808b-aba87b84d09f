import React from 'react';
import {connect} from 'umi';
import styles from './index.less';
import {processNames, randomColor} from '@/utils/utils';
import classNames from 'classnames';

type propsType = {
  global: any;
  onRefByVerticalLiveRoom: any;
  sendMessageByIm: any;
  localStreamConfig: any;
  remoteStreamConfigList: any;
  RTC: any;
  shareRTC: any;
  isJoined: boolean;
  isPublished: boolean;
  handleJoin: any;
  handleLeave: any;
  onChange: any;
  spaceId: any;
  openCloseHandUp: any;
  liveRecord: any;
  getGuestList: any;
  getSpaceInfo: any;
  onClickLianMai: any;
  changeUrlParams: any;
  elapsedTime: any;
  shareOnClick: any;
  onClickBack: any;
  isHorizontalLive: any;
};

const Index: React.FC<propsType> = (props) => {
  const {PlanetChatRoom, userInfo, size, isPc, itemByStream} = props || {};

  const {SpaceInfo, isMobile, isHorizontalLive} = PlanetChatRoom || {};

  const {hostUserInfo} = SpaceInfo || {};

  let containerStyle = {
    background: userInfo && userInfo.wxUserId ? randomColor(userInfo.wxUserId) : 'none',
  };
  if (isPc) {
    containerStyle = {
      background: userInfo && userInfo.wxUserId ? randomColor(userInfo.wxUserId) : 'none',
    };
  }

  return (
    <>
      <div
        className={classNames({
          [styles.AvatarByMeetWarp]: true,
          [styles.AvatarByMeetWarpBoxMargin_H5]: !!isMobile && !isHorizontalLive,
          [styles.AvatarByMeetWarpBoxMargin_H5_H]: !!isMobile && !!isHorizontalLive,
          [styles.AvatarByMeetWarpBoxMargin_PC]: !isMobile,
        })}
      >
        {hostUserInfo.imUserId == userInfo.imUserId && (
          <i className={styles.AvatarByMeetHostIcon}></i>
        )}
        <div style={containerStyle} className={styles.AvatarByMeetBox}>
          {!!userInfo && userInfo.imagePhotoPathShow && (
            <img
              className={styles.video_Title_box_left_avatar_img}
              src={userInfo.imagePhotoPathShow}
              alt=""
            />
          )}
          {!!userInfo && !userInfo.imagePhotoPathShow && userInfo.headUrlShow && (
            <img
              className={styles.video_Title_box_left_avatar_img}
              src={userInfo.headUrlShow}
              alt=""
            />
          )}
          {userInfo && !userInfo.headUrlShow && !userInfo.imagePhotoPathShow && (
            <div
              className={styles.head_sculpture_name}
              style={{
                background: userInfo && userInfo.wxUserId ? randomColor(userInfo.wxUserId) : 'none',
              }}
            >
              {userInfo && processNames(userInfo.name ? userInfo.name : userInfo.userName)}
            </div>
          )}
        </div>
        <div className={styles.userInfoNameWarp}>
          <div className={styles.userInfoNameBox}>
            <div className={styles.userInfoName}> {userInfo && userInfo.name} </div>
            <i
              className={classNames({
                [styles.HorizontalLiveRoom_camera_picture_mic_icon]: true,
                [styles.HorizontalLiveRoom_camera_picture_forbidden_mic_icon]:
                itemByStream && itemByStream.mutedAudio,
                [styles.HorizontalLiveRoom_camera_picture_forbidden_Lianmai]:
                itemByStream && !itemByStream.mutedAudio && itemByStream.audioVolume > 0,
              })}
            ></i>
          </div>
        </div>
      </div>
    </>
  );
};
export default connect(({ global, PlanetChatRoom, loading }: any) => ({
  global,
  PlanetChatRoom,
  loading,
}))(Index);
