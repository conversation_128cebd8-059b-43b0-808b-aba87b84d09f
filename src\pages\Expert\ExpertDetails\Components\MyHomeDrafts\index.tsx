/**
 * @Description: 专家主页-草稿箱
 */
import React, { useEffect, useState } from 'react';
import { connect, history } from 'umi';
import InfiniteScroll from 'react-infinite-scroller';
import { message, Select } from 'antd';
import styles from './index.less';

import NoDataRender from '@/components/NoDataRender';                  // 暂无数据
import ArticleOrLinkCard from '@/componentsByPc/ArticleOrLinkCard';    // 文章、链接的卡片
import PostCard from '@/componentsByPc/PostCard';                      // 帖子卡片
import DeleteOrLowModal from '@/pages/CreateGraphicsText/ComponentsPC/DeleteOrLowModal'  // 删除或下架弹窗

// 筛选数据
const selectOptions = [
  {value: 'all', label: '全部'},
  {value: 1, label: '文章'},
  {value: 2, label: '帖子'},
  {value: 3, label: '外链'},
]

interface PropsType {
  expertsUserId: string,     // 专家ID
  isMyPages: boolean,        // 是否是自己的主页
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { dispatch, isMyPages, expertsUserId } = props;

  // 页面state
  const initialState = {
    dataList: [],              // 数据list
    total: 0,                  // 总条数
    hasMore: false,            // 是否还有更多数据
  }
  // 筛选数据
  const initialFilterState = {
    page: 1,                   // 页码
    size: 30,                  // 每页条数
    imageType: 'all',          // 图文类型：1.文章 2.帖子 3.外链 4.空间
  }
  // 弹窗state
  const initialModalState = {
    deleteOrLowVisible: false,     // 删除或下架弹窗是否显示
    deleteOrLowType: 1,            // 1 删除，2 下架
    deleteOrLowId: null,           // 图文ID
  }
  const [state, setState] = useState(initialState)
  const [filterState, setFilterState] = useState(initialFilterState)
  const [modalState, setModalState] = useState(initialModalState)
  const [loadingPersonImageTextList, setLoadingPersonImageTextList] = useState(false)  // loading

  useEffect(() => {
    personImageTextList()
  }, [filterState.page, filterState.imageType])

  // 个人中心获取用户图文列表信息，page，size 删除或下架后刷新数据时有值
  const personImageTextList = (page, size) => {
    setLoadingPersonImageTextList(true)

    const pageResult = page || filterState.page
    const sizeResult = size || filterState.size
    dispatch({
      type: 'graphicsText/personImageTextList',
      payload: {
        getParams: {
          page: pageResult,
          size: sizeResult,
        },
        postParams: {
          imageType: filterState.imageType == 'all' ? null : filterState.imageType,  // 图文类型：1.文章 2.帖子 3.外链 4.空间
          status: [3],                 // 状态：1.审核通过 0.未审核 2.审核未通过 3.草稿
          friUserId: expertsUserId,    // 专家ID
        },
      }
    }).then(res => {
      setLoadingPersonImageTextList(false)
      const { code, content, msg } = res
      if (code == 200) {
        const resultList = content.resultList || []
        setState({
          ...state,
          dataList: pageResult == 1 ? resultList : state.dataList.concat(resultList),
          total: content.total || 0,
          hasMore: true,
        })
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch();
  }

  // 滚动加载分页
  const loadMore = () => {
    console.log('loadMore')
    // 没有更多数据了
    if (state.dataList.length >= state.total) {
      setState({
        ...state,
        hasMore: false,
      })
      return
    }
    setLoadingPersonImageTextList(true)
    setFilterState({
      ...filterState,
      page: filterState.page + 1,  // 页码
    })
  }

  // 修改，imageType 图文类型：1.文章 2.帖子 3.外链 4.空间
  const onEdit = (id, imageType) => {
    if (imageType == 1) {
      history.push(`/CreateGraphicsText/CreateArticle?id=${id}`)
    } else if (imageType == 3) {
      history.push(`/CreateGraphicsText/CreateExternalLinks?id=${id}`)
    }
  }

  // 删除
  const onDelete = (id) => {
    setModalState({
      ...modalState,
      deleteOrLowVisible: true,
      deleteOrLowType: 1,          // 1 删除，2 下架
      deleteOrLowId: id,           // 图文ID
    })
  }

  // 下架
  const onLow = (id) => {
    setModalState({
      ...modalState,
      deleteOrLowVisible: true,
      deleteOrLowType: 2,          // 1 删除，2 下架
      deleteOrLowId: id,           // 图文ID
    })
  }

  // 弹窗关闭
  const deleteOrLowModalHide = () => {
    setModalState({
      ...modalState,
      deleteOrLowVisible: false,
      deleteOrLowType: 1,          // 1 删除，2 下架
      deleteOrLowId: null,         // 图文ID
    })
  }

  // 下架或删除成功回调
  const handleDeleteOrLow = () => {
    deleteOrLowModalHide()
    // 刷新数据
    personImageTextList(1, state.dataList.length)
  }

  //  体裁-筛选
  const handleChange = (value) => {
    setFilterState({
      ...filterState,
      page: 1,                     // 页码
      imageType: value,            // 图文类型：1.文章 2.帖子 3.外链 4.空间
    })
  }

  return (
    <>
      <div className={styles.tab_content_list}>
        <div className={styles.screen_box}>
          体裁：
          <Select
            style={{ width: 120 }}
            options={selectOptions}
            value={filterState.imageType}
            onChange={handleChange}
          />
        </div>
        {
          state.dataList.length > 0 ?
            <InfiniteScroll
              loadMore={loadMore}
              threshold={50}
              pageStart={1}
              initialLoad={false}
              hasMore={!loadingPersonImageTextList && state.hasMore}
              useWindow={false}
            >
              <div className={styles.total_content}>共 {state.total} 条内容</div>
              {
                state.dataList.map(item => {
                  // 帖子
                  if (item.imageType == 2) {
                    return (
                      <PostCard
                        key={item.id}
                        onLow={onLow}
                        onDelete={onDelete}
                        isMyPages={isMyPages}
                        data={item}
                      />
                    )
                  }
                  return (
                    <ArticleOrLinkCard
                      key={item.id}
                      onEdit={onEdit}
                      onLow={onLow}
                      onDelete={onDelete}
                      isMyPages={isMyPages}
                      data={item}
                    />
                  )
                })
              }

            </InfiniteScroll>
            :
            <div className={styles.no_data_wrap}>
              <NoDataRender className={styles.noDataStyle} />
            </div>
        }
      </div>

      {/* 删除弹窗，是我的主页时有操作按钮 */}
      {
        isMyPages &&
        <DeleteOrLowModal
          visible={modalState.deleteOrLowVisible}
          deleteOrLowType={modalState.deleteOrLowType}
          deleteOrLowId={modalState.deleteOrLowId}
          onCancel={deleteOrLowModalHide}
          handleDeleteOrLow={handleDeleteOrLow}
        />
      }
    </>
  );
};

export default connect(({ loading }: any) => ({ loading }))(Index);
