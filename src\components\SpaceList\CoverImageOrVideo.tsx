/**
 * @Description: 空间封面/视频组件
 */
import React, { useEffect, useRef, useState } from 'react';
import classNames from 'classnames';
import { getDAesString, getOperatingEnv } from '@/utils/utils';
import styles from './CoverImageOrVideo.less';
import TCPlayer from 'tcplayer.js';
import 'tcplayer.js/dist/tcplayer.min.css';
import { licenseUrl } from '@/app/config';
import { useInView } from 'react-intersection-observer'; // 判断元素是否可见插件

interface PropsType {
  spaceCoverUrlShow: string;
  data: object;
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { spaceCoverUrlShow, data } = props;
  const { id, vodPathUrl, status } = data || {};
  // let videoPlayer = useRef(null);
  let playerRef = useRef(null);
  const [videoPlayer, setVideoPlayer] = useState(null);
  // playerStateData 播放器状态数据
  const [playerStateData, setPlayerStateData] = useState(null);
  // 元素是否可见的配置
  const { ref, inView } = useInView({
    threshold: 0.6, // 何时触发回调的阈值
  });

  // useEffect(() => {
  //   // 是推荐首页,则展示评论框
  //   if (inView) {
  //     console.log('已经展示了', id);
  //   } else {
  //     console.log('未展示', id);
  //   }
  // }, [inView]);

  useEffect(async () => {
    if (!!inView) {
      if (!videoPlayer && vodPathUrl) {
        // await setTimeout(() =>  {}, 1000);
        let videoJsNode = playerRef.current;
        if (videoJsNode && videoJsNode.id) {
          // 确认节点是否在异步执行的时候被销毁掉
          let element = document.getElementById(videoJsNode.id); // 通过ID获取节点
          if (!element) {
            return;
          }
          let vodByDAes = getDAesString(vodPathUrl, 'arrail-dentail&2', 'arrail-dentail&3');
          let sources = [{ src: vodByDAes }];
          let tcPlayerObj = TCPlayer(videoJsNode, {
            sources: sources,
            licenseUrl: licenseUrl,
            autoplay: true,
            loop: true,
            muted: true,
            preload: 'meta',
          });
          console.log('tcPlayerObj 123123 :: ', tcPlayerObj);
          if (!tcPlayerObj) {
            return;
          }
          tcPlayerObj.ready(() => {
            setVideoPlayer(tcPlayerObj);
            // tcPlayerObj.volume(0);
            // tcPlayerObj.play();
          });
          tcPlayerObj.on('error', (value) => {
            setPlayerStateData(null);
          });
          tcPlayerObj.on('blocked', (value) => {
            // message.error('自动播放被浏览器阻止');
            setPlayerStateData(null);
            // tcPlayerObj.volume(0);
            // tcPlayerObj.play();
          });
          tcPlayerObj.on('pause', (value) => {
            // setPlayerStateData(null)
          });

          tcPlayerObj.on('playing', (value) => {
            setPlayerStateData(true);
          });

          tcPlayerObj.on('progress', (value) => {
            let state = {
              currentTime: tcPlayerObj.currentTime(),
              duration: tcPlayerObj.duration(),
              // paused:TCPlayerObj.paused(),
            };
            setPlayerStateData(true);
          });
        }
      } else {
        // await setTimeout(()=>{},1000);
        let videoJsNode = playerRef.current;
        if (!videoJsNode) {
          return;
        }
        let element = document.getElementById(videoJsNode.id); // 通过ID获取节点
        if (!element) {
          return;
        }
        videoPlayer && playerStateData && videoPlayer.volume(0);
        videoPlayer && playerStateData && videoPlayer.play();
      }
    } else {
      if (!!videoPlayer) {
        videoPlayer && playerStateData && videoPlayer.pause();
        // videoPlayer && playerStateData && videoPlayer.dispose();
        // setVideoPlayer(null)
        // setPlayerStateData(null)
      }
    }
  }, [inView]);

  useEffect(() => {
    return () => {
      if (!!videoPlayer) {
        videoPlayer.dispose();
      }
    };
  }, [videoPlayer]);

  const env = getOperatingEnv();
  return (
    <>
      <div
        ref={ref}
        className={styles.cover_img_div}
        style={spaceCoverUrlShow ? { backgroundImage: `url(${spaceCoverUrlShow})` } : {}}
      ></div>
      {!!vodPathUrl &&
        status == 3 &&
        env != 2 && ( // 由于微信环境 不能自动播放视频，所以放弃使用video标签，GIF动图
          <div
            key={`${id}`}
            className={classNames({
              [styles.video_box]: true,
              [styles.video_box_Hidden]: !inView || !playerStateData,
            })}
          >
            <video
              key={`video_spaceList_id_${id}`}
              ref={playerRef}
              id={`video_spaceList_id_${id}`}
              className={styles.videoContent}
              x-webkit-airplay="allow"
              playsInline={true}
            />
          </div>
      )}
    </>
  );
};
export default Index;
