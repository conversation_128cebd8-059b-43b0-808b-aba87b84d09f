/**
 * @Description: PC端我的聊天展示组件
 * @author: 赵斐
 */
import React, { useRef } from 'react';
import { connect } from 'umi';
import { Image } from 'antd';
import { randomColor, processNames } from '@/utils/utils'
import voiceIcon from '@/assets/Consultation/H5/voice_icon.png'
import voiceRightIcon from '@/assets/Consultation/H5/voice_right_icon.gif'
import playCircleIcon from '@/assets/Consultation/Pc/play_circle_icon.png'
import loadingIcon from '@/assets/Consultation/loading.gif'
import styles from './index.less'
import { parseText } from '@/utils/im-index'

interface PropsType {
  data: any,   // 数据
  onClickOpenVideoFun: (val: string, id: number) => void,  // 点击视频播放
  playAudioFun: (k: number, v: any) => void,  // 点击语音播放
  onClickAmplifyImg: (url:string,visible:any) => void,  // 点击图片放大
  playAudioObj: any,    // 播放ID
  consultation: any
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { consultation } = props;
  const { uploadProgress } = consultation || {}
  const audioRef = useRef<HTMLAudioElement>(null);
  const { data, onClickOpenVideoFun, playAudioFun, playAudioObj } = props;

  const {
    audioId
  } = playAudioObj || {}
  const {
    wxUserId,       // 用户ID
    name,           // 用户名
    headUrlShow,    // 用户头像
    msgSeq,         // 消息序列号，用于标识唯一消息，值越小发送的越早
    msgDataTime,    // 消息时间
    msgType,        // 消息类型 1文本 2图片 3语音 4视频 5自定义
    msgContent,     // 消息内容/图片地址/语音地址/视频地址/
    mediaDuration,  // 媒体文件时长，语音有值
    thumbUrlShow,   // 缩略图url地址，用于展示图片和视频的缩略图片
    islocal,        // 未发送成功标识
    randomNumber,   // 本地数据唯一标识
  } = data || {};
  /**
  * 进度展示
  * @param num   当前本地存储数据唯一标识
  * @returns
  */
  const progressDisplayFun = (num: number) => {
    let status = '1%'
    uploadProgress.filter((v: any) => {
      if (v.randomNumber == num) {
        status = `${Math.floor(v.schedule * 100)}%`
      }
    })
    return status
  }

  return (
    <div className={styles.wrap}>
      {
        msgType == 1 ? <div className={styles.characters_content}>
          <div className={styles.characters}>
            <p className={styles.desc} dangerouslySetInnerHTML={{ __html: parseText(msgContent) }}></p>
            <p className={styles.time}>{msgDataTime}</p>
            {
              islocal == 1 ? <img className={styles.characters_loading} src={loadingIcon} alt="" /> : null
            }
          </div>
        </div> : null
      }
      {
        msgType == 2 ? <div className={styles.picture_content}>
          <div className={styles.picture}>
            <Image width={120} height={120} src={msgContent} />
            {/* {
              islocal == 1 ? <>
                <div className={styles.picture_mask}></div>
                <div className={styles.picture_rate}>{progressDisplayFun(randomNumber)}</div>
                <img className={styles.picture_loading} src={loadingIcon} alt="" />
              </> : null
            } */}
          </div>
        </div> : null
      }
      {
        msgType == 3 ? <div className={styles.voice_content}>
          <div className={styles.voice} style={{ width: `${140 + mediaDuration * 2}px` }}>
            <p className={styles.desc} onClick={islocal == 1 ?()=>{}:() => { playAudioFun(msgSeq, audioRef) }}>
              <span className={styles.second}>{mediaDuration}"</span>
              <img className={styles.second_icon} src={msgSeq == audioId ? voiceRightIcon : voiceIcon} alt="语音" />
            </p>
            <p className={styles.time}>{msgDataTime}</p>
            {
              islocal == 1 ? <img className={styles.characters_loading} src={loadingIcon} alt="" /> : null
            }
          </div>

          <audio src={msgContent} ref={audioRef} controls className={styles.message_audio}> </audio>
        </div> : null
      }

      {
        msgType == 4 ? <div className={styles.video_content}>
          <div className={styles.video}>
            <img src={thumbUrlShow} alt="icon" />
            {
              islocal != 1 ? <span onClick={() => { onClickOpenVideoFun(msgContent, msgSeq) }} className={styles.play_icon}><img className={styles.pay_circle} src={playCircleIcon} alt="" /></span> : null
            }
            {
              islocal == 1 ? <>
                <div className={styles.video_mask}></div>
                <div className={styles.video_rate}>{progressDisplayFun(randomNumber)}</div>
                <img className={styles.video_loading} src={loadingIcon} alt="" />
              </> : null
            }
          </div>
        </div> : null
      }

      <div className={styles.avatar}>
        {
          headUrlShow ? <img className={styles.avatar_pic} src={headUrlShow} alt='头' /> :
            <div className={styles.no_avatar_pic} style={{ background: randomColor(wxUserId) }}>{processNames(name)}</div>
        }
      </div>
    </div>
  )
}
export default connect(({ consultation, loading }: any) => ({ consultation, loading }))(Index)
