.kingdom_container {
  position: relative;
  padding: 0 12px 4px;
  .item_box {
    display: flex;
    flex-wrap: nowrap;
    margin-bottom: 12px;
    padding-top: 5px;
    .left_avatar {
      width: 40px;
      min-width: 40px;
      height: 40px;
      margin-right: 12px;
      position: relative;
      border-radius: 2px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      font-size: 14px;
      font-weight: 500;
      color: #fff;
      text-align: center;
      line-height: 40px;

      i {
        position: absolute;
        top: -3px;
        right: -3px;
        width: 17px;
        height: 17px;
        background: url("../../assets/GlobalImg/crown.png") no-repeat center;
        background-size: 100%;
      }
    }
    .right {
      flex: 1;
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      overflow: hidden;
      .details_box {
        flex: 1;
        overflow: hidden;
        .title {
          font-size: 15px;
          color: #000000;
          font-weight: 600;
          word-break: break-all;
          line-height: 21px;
          margin-bottom: 2px;
        }
        .info_1 {
          font-size: 11px;
          color: #999999;
          display: flex;
          align-items: flex-end;
          line-height: 15px;
          margin-bottom: 2px;
          .name {
            font-size: 13px;
            color: #666666;
            margin-right: 4px;
            line-height: 18px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .label {
            white-space: nowrap;
          }
        }
        .info_2 {
          font-size: 12px;
          color: #999999;
          line-height: 17px;
          & > span + span {
            margin-left: 4px;
          }
        }
      }
      .btn_box {
        padding: 0 16px;
        white-space: nowrap;
        height: 32px;
        line-height: 32px;
        border-radius: 18px;
        background: #E6F4FF;
        text-align: center;
        font-size: 14px;
        color: #1DA1FF;
        margin-top: 2px;
        &.visited {
          background: #F5F5F5;
          color: #999999;
        }
      }
    }
  }
}
