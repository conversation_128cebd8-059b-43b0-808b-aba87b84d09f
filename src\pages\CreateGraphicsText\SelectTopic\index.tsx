import React, { useEffect, useState } from 'react'
import { history, connect } from 'umi'
import { useThrottle } from '@/utils/utils'
import { Spin } from 'antd'
import { Input, Toast } from 'antd-mobile'
import styles from './index.less'
import NoDataImage from '@/assets/GlobalImg/no_data.png'
import NavBar from '@/components/NavBar'

const Index: React.FC = (props: any) => {
  const { selectTopicType } = history.location.query       // 类型，1输入，2点击
  const { loading, dispatch } = props
  const [topicName, setTopicName] = useState('')           // 搜索关键词
  const [relationList, setRelationList] = useState([])     // 话题list

  useEffect(() => {
    getRelationList()
  }, [])

  // 关联话题列表
  const getRelationList = () => {
    dispatch({
      type: 'graphicsText/getRelationList',
      payload: {
        topicName,                     // 话题name
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200) {
        setRelationList(content || [])
      } else {
        Toast.show(msg || '数据加载失败')
      }
    }).catch(err => {})
  }

  // 输入搜索关键词
  const inputOnChange = (value) => {
    setTopicName(value)
  }

  // 搜索
  let inputOnSearch = () => {
    getRelationList()
  }
  inputOnSearch = useThrottle(inputOnSearch, 500)

  // 选择话题
  let selectItem = (item) => {
    dispatch({
      type: 'graphicsText/save',
      payload: {
        selectedTopicId: item.topicId,
        selectedTopicName: item.topicName,
        selectTopicType: selectTopicType || 2,
      }
    })
    history.goBack()
  }
  selectItem = useThrottle(selectItem, 500)

  const loadingGetRelationList = !!loading.effects['graphicsText/getRelationList']
  return (
    <Spin spinning={loadingGetRelationList} wrapperClassName={styles.spin}>
      <NavBar
        title="选择话题"
        bordered
      />
      <div className={styles.container}>
        <div className={styles.header_box}>
          <div className={styles.header}>
            <i className={styles.header_icon}></i>
            <Input
              placeholder="输入关键字搜索话题"
              autoComplete="off"
              value={topicName}
              onChange={inputOnChange}
              onEnterPress={inputOnSearch}
            />
          </div>
        </div>
        {
          relationList.length == 0 ?
            <div className={styles.item_empty_box}>
              <img src={NoDataImage} width={150} height={113} alt=""/>
              <div className={styles.empty_title}>暂无数据</div>
              <div className={styles.empty_msg}>请试试其他搜索关键词</div>
            </div>
            :
            <>
              <div className={styles.page_title}>全部话题</div>
              <div className={styles.topic_list_box}>
                {
                  relationList.map(item => {
                    return <div key={item.topicId} className={styles.topic_list_item} onClick={() => selectItem(item)}>#{item.topicName}#</div>
                  })
                }
              </div>
            </>
        }
      </div>
    </Spin>
  )
}

export default connect(({ graphicsText, loading }: any) => ({ graphicsText, loading }))(Index)
