/**
 * @Description: 实名认证-填写认证信息（PC）
 */
import React, { useState, useEffect } from 'react';
import { connect, history } from 'umi';
import { cloneDeep } from 'lodash'
import { getOperatingEnv } from '@/utils/utils';
import { Button, message, Input, Upload, Form, Spin } from 'antd';
import { Toast } from 'antd-mobile'
import styles from './index.less';

// 图片icon
import add_icon from '@/assets/GlobalImg/add.png' // 加号
import close_icon from '@/assets/Case/close_icon.png' // 叉号

import PcHeader from '@/componentsByPc/PcHeader' // 公共导航组件
import SubmitSuccessModalByPC from '../../SubmitSuccessModalByPC' // 认证成功提示弹窗

const Index: React.FC = (props: any) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}') // 用户信息
  const { dictId } = history.location.query
  const { loading, dispatch } = props;

  const [phone, setPhone] = useState(UserInfo?.phone || '') // 手机号
  const [credentials, setCredentials] = useState([]) // 认证证明
  const [submitSuccessModalByPCVisible, setSubmitSuccessModalByPCVisible] = useState(false); // 提交成功提示弹窗

  const [form] = Form.useForm() // form

  useEffect(() => {
    getEchoSelfAuthInfo()
  }, [])

  // 个人实名认证回显-星球-实名认证版本
  const getEchoSelfAuthInfo = () => {
    dispatch({
      type: 'userInfoStore/getEchoSelfAuthInfo',
      payload: {}
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200 && content) {
        setPhone(content.phone || UserInfo?.phone)
        setCredentials(content.credentials || [])
        form.setFieldsValue({
          realName: content.realName, // 真实姓名
          unitWorkName: content.unitWorkName, // 单位/学校
          specialityName: content.specialityName, // 职务/专业
        })
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch(err => {})
  }

  // 上传图片headers
  const getHeaders = () => {
    // 获取环境类型，1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用   5 FRIDAY app中使用
    const env = getOperatingEnv()
    // FRIDAY APP中本地存储值：access_token、client、phone、user_name、wxuserId、userInfo
    return {// token
      access_token:localStorage.getItem('access_token') || '',
      username: env == 5 ? localStorage.getItem('user_name') : localStorage.getItem('vxOpenIdCipherText') ? localStorage.getItem('vxOpenIdCipherText')  : UserInfo?.phone,
      client: env == 5 ? localStorage.getItem('client') : 'WX',
      type: env === '1' ? '' : '1', // h5 传1
    }
  }

  // 上传校验规则，图片
  const beforeUpload = (file) => {
    const isSize = file.size / 1024 / 1024 < 15
    if (!isSize) {
      message.error('超过15M限制，不允许上传~')
      return false
    }

    const { name: fileName } = file || {}
    // 添加对文件后缀名的限制
    const suffix = fileName.substring(fileName.lastIndexOf('.')+1).toLowerCase()
    const isJpgOrPng = file.type === 'image/jpg' || file.type === 'image/jpeg' ||  file.type === 'image/png'
    const isSuffixByJpgOrPng = (
      suffix === 'jpg'
      || suffix === 'jpeg'
      || suffix === 'png'
    )
    if (!isJpgOrPng || !isSuffixByJpgOrPng) {
      message.error({content: '只能上传JPG、JPEG、PNG格式的图片~'})
      return false
    }
    return true
  }

  // 上传完成回调，图片
  const uploadOnChange = (info) => {
    console.log('uploadOnChange', info)
    if (info.file.status === 'uploading') {
      Toast.show({
        icon: 'loading',
        content: '',
        duration: 0,
      })
    }

    // 状态不为uploading时，代表上传事件结束
    if (info.file.status != 'uploading') {
      Toast.clear()
    }

    // 判断当前上传已被拦截
    if (info && !info.file.status) {
      return
    }

    // 上传结束
    if (info && info.file.status === 'error') {
      message.error('上传失败')
      return
    }

    if (info && info.file.status === 'done') {
      const res = info.file.response || {}
      const { code, content, msg } = res
      if (code == 200 && content) {
        setCredentials(prevState => {
          if (prevState.length >= 3) {
            return
          }
          const prevStateClone = cloneDeep(prevState)
          prevStateClone.push({
            credentialName: info.file.name, // 图片名
            credentialUrl: content.fileUrl, // 图片地址
            credentialUrlView: content.fileUrlView, // 图片全地址
          })
          return prevStateClone
        })
      } else {
        message.error(msg || '上传失败')
      }
    }
  }

  // 删除认证证明
  const onClickDeleteImgIcon = (index) => {
    const credentialsClone = cloneDeep(credentials)
    credentialsClone.splice(index, 1)
    setCredentials(credentialsClone)
  }

  // 点击提交认证
  const onClickSubmitBtn = () => {
    form.validateFields().then(values => {
      dispatch({
        type: 'userInfoStore/saveSelfAuth',
        payload: {
          identityType: dictId, // 身份类型ID
          realName: values.realName, // 真实姓名
          specialityName: values.specialityName, // 职务/专业
          unitWorkName: values.unitWorkName, // 单位/学校
          credentials: credentials, // 认证证明
        }
      }).then(res => {
        const { code, content, msg } = res
        if (code == 200 && content) {
          setSubmitSuccessModalByPCVisible(true)
        } else {
          message.error(msg || '数据加载失败')
        }
      }).catch(err => {})
    }).catch(err => {
      console.log('err',err)
    })
  }

  // 关闭认证成功提示弹窗
  const submitSuccessModalByH5Close = () => {
    history.replace(`/UserInfo`)
  }

  // 上一步
  const onClickPrevStepBtn = () => {
    history.goBack()
  }

  // 返回
  const goBack = () => {
    if (history.length > 1) {
      history.goBack()
    } else {
      history.replace('/')
    }
  }

  // loading
  const loadingGetEchoSelfAuthInfo = !!loading.effects['userInfoStore/getEchoSelfAuthInfo']
  const loadingSaveSelfAuth = !!loading.effects['userInfoStore/saveSelfAuth']

  return (
    <Spin wrapperClassName={styles.spin} spinning={loadingGetEchoSelfAuthInfo}>
      <div className={styles.container}>
        {/* 头部 */}
        <PcHeader />
        {/* 内容 */}
        <Form form={form} component={false} autoComplete="off">
          <div className={styles.content}>
          <div className={styles.content_inner}>
            {/* 标题导航条 */}
            <div className={styles.nav_bar}>
              <i onClick={goBack}></i>
              <span>个人认证</span>
            </div>

            <div className={styles.wrap}>
              <h1 className={styles.content_title}>填写认证信息</h1>
              <p className={styles.content_tips}>所有信息仅用于资料认证，我们将严格保护您的隐私</p>
              <Form.Item
                name="realName"
                label="真实姓名"
                required={true}
                colon={false}
                rules={[
                  { required: true, message: '请输入真实姓名', whitespace: true },
                  { pattern: /^[~a-zA-Z\u4e00-\u9fa5\s/]+$/, message: '只能输入中英文' },
                ]}
              >
                <Input placeholder="请输入真实姓名" style={{width: 383}} maxLength={20} autoComplete="off"/>
              </Form.Item>

              <Form.Item
                label="手机号"
                required={true}
                colon={false}
              >
                <Input placeholder="请输入手机号" style={{width: 383}} readOnly value={phone} autoComplete="off"/>
              </Form.Item>

              <Form.Item
                name="unitWorkName"
                label="单位/学校"
                required={true}
                colon={false}
                rules={[
                  { required: true, message: '请输入单位/学校', whitespace: true },
                  { pattern: /^[~a-zA-Z\u4e00-\u9fa5\s/]+$/, message: '只能输入中英文' },
                ]}
              >
                <Input placeholder="请输入单位/学校" style={{width: 383}} maxLength={30} autoComplete="off"/>
              </Form.Item>

              <Form.Item
                name="specialityName"
                label="职务/专业"
                colon={false}
                rules={[
                  { pattern: /^[~a-zA-Z\u4e00-\u9fa5\s/]+$/, message: '只能输入中英文' },
                ]}
              >
                <Input placeholder="请输入职务/专业" style={{width: 383}} maxLength={20} autoComplete="off"/>
              </Form.Item>

              <div className={styles.form_upload_img}>
                <div className={styles.upload_img_title}>认证证明</div>
                <div>
                  <div className={styles.upload_img_list}>
                    {
                      credentials.map((item, index) => {
                        return (
                          <div key={item.credentialUrlView} className={styles.upload_img_item}>
                            <img src={item.credentialUrlView} width={120} height={120} alt=""/>
                            {/* 删除icon */}
                            <div className={styles.delete_icon_wrap} onClick={() => onClickDeleteImgIcon(index)}>
                              <img src={close_icon} width={20} height={20} alt=""/>
                            </div>
                          </div>
                        )
                      })
                    }

                    {/* 上传按钮 */}
                    <div className={styles.upload_img_btn} style={credentials.length >= 3 ? {display: 'none'} : {}}>
                      <Upload
                        headers={getHeaders()}
                        accept="image/*"
                        listType="picture-card"
                        action={`/api/fri-uc/self-auth/upload-credential`}
                        onChange={uploadOnChange}
                        beforeUpload={beforeUpload}
                        showUploadList={false}
                        // multiple={true}
                        // customRequest={uploadCustomRequest}
                      >
                        <div>
                          <img src={add_icon} width={32} height={32} alt=""/>
                        </div>
                      </Upload>
                    </div>
                  </div>
                  <div className={styles.upload_img_tips}>您可以上传医师证、助理医师资格证、护士证、毕业证、学生证、身份证、工作胸牌等工作相关证件照片。</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        </Form>
        {/* 按钮 */}
        <div className={styles.footer}>
          <div className={styles.footer_content}>
            <div className={styles.footer_content_right}>
              <Button size="large" onClick={onClickPrevStepBtn}>上一步</Button>
              <Button type="primary" size="large" onClick={onClickSubmitBtn} loading={loadingSaveSelfAuth}>提交认证</Button>
            </div>
          </div>
        </div>
      </div>

      {/* 认证成功提示弹窗 */}
      <SubmitSuccessModalByPC visible={submitSuccessModalByPCVisible} onCancel={submitSuccessModalByH5Close}/>
    </Spin>
  )
}

export default connect(({ userInfoStore, loading }: any) => ({ userInfoStore, loading}))(Index)
