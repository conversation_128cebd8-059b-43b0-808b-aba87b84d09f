/**
 * @Description: 广场搜索
 */
import React, { useState, useEffect } from 'react'
import classNames from 'classnames'
import { getOperatingEnv } from '@/utils/utils'
import { history, connect } from 'umi'
import styles from './index.less'

import SearchPage from '@/components/SearchPage'           // 搜索页组件
import NavBar from '@/components/NavBar'                   // 导航栏组件
import popularIcon from '@/assets/GlobalImg/popular.png'
import {stringify} from "qs";   // 热门icon

const Index: React.FC = (props: any) => {
  const { dispatch } = props

  const initialState = {
    searchKey: null,                                       // 搜索关键词
  }
  const [state, setState] = useState(initialState)
  const [customKeyWords, setCustomKeyWords] = useState([]) // 搜索热词
  const [searchKeys, setSearchKeys] = useState([])         // 搜索历史
  const [ ArrByGetHotList, setArrByGetHotList ]  = useState([])

  useEffect(() => {
    getWordList()
    getHotListByfunc()
  }, [])

  // 根据用户ID获取搜索关键字
  const getWordList = () => {
    dispatch({
      type: 'square/getWordList',
      payload: {}
    }).then(res => {
      const { code, content } = res
      if (code == 200) {
        setCustomKeyWords(content && content.customKeyWords || [])
        setSearchKeys(content && content.searchKeys || [])
      }
    })
  }

  // 获取热门话题排行榜
  const getHotListByfunc = async ()=>{
    // 热门话题排行榜
    let DataBygetHotList = await dispatch({
      type: 'recommended/getHotList'
    });
    const { code, content } = DataBygetHotList || {};
    if (code == 200 && Array.isArray(content)) {
      console.log('content',content);
      setArrByGetHotList(content)
    }else {
      setArrByGetHotList([])
    }
  }

  // 输入事件
  const inputOnChange = (value) => {
    setState({
      ...state,
      searchKey: value,                                    // 搜索关键词
    })
  }

  // 搜索词点击事件，跳转搜索结果页
  const keywordsOnClick = (value) => {
    history.replace({
      pathname: '/Square/SearchResult',
      query: {
        searchKey: value,                                  // 搜索关键词
      }
    })
  }

  // 搜索回调，跳转搜索结果页
  const onPressEnter = () => {
    history.replace({
      pathname: '/Square/SearchResult',
      query: {
        searchKey: state.searchKey,                        // 搜索关键词
      }
    })
  }

  // 点击取消按钮事件
  const cancelBtnOnClick = () => {
    history.goBack()
  }

  // 话题点击事件
  const topicItemClick = (item) => {
    const { topicName,topicId } = item || {};
    history.push(`/CreateGraphicsText/TopicHome?${
      stringify({topicId:topicId})
    }`)
  }

  // 热门话题排行模版
  const TopicRankingFn = () => {
    return <>
      {
        ArrByGetHotList.length != 0 && <div className={styles.popular_wrap}>
          <div className={styles.popular_title}><img src={popularIcon} alt="" width={16} height={16}/>热门话题排行榜</div>
          <div className={styles.popular_list}>
            {
              ArrByGetHotList.map((item, ind) => {
                const {topicName} = item || {};
                return <div key={ind} className={styles.popular_list_item} onClick={() => topicItemClick(item)}><span
                  className={styles.serial_num}>{ind + 1}</span><span>{topicName}</span></div>
              })
            }
          </div>
        </div>
      }
    </>
  }

  return (
    <>
      {/*<NavBar title="搜索"/>*/}
      <div className={classNames(styles.container, {
        [styles.container_pc]: getOperatingEnv() == 4,
      })}>
        <SearchPage
          isHistoryStatus={1}
          inputPlaceholder="搜索您想要的关键词"
          isShowPopularSearch={true}
          historyData={searchKeys}
          popularData={customKeyWords}
          inputChangeFn={inputOnChange}
          historyClickFn={keywordsOnClick}
          popularDataClickFn={keywordsOnClick}
          onPressEnterFun={onPressEnter}
          cancelBtnFn={cancelBtnOnClick}
          TopicRanking={TopicRankingFn}
        />
      </div>
    </>
  )
}

export default connect(({ square, loading }: any) => ({square, loading}))(Index)
