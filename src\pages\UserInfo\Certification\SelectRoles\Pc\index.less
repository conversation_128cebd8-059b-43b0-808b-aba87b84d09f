.spin {
  height: 100%;
  & > :global(.ant-spin-container) {
    height: 100%;
  }
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #eef3f9;
}

.content {
  flex: 1;
  padding: 16px 0;
  overflow-y: auto;
}

.content_inner {
  width: 816px;
  min-height: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
}

.nav_bar {
  flex-shrink: 0;
  width: 100%;
  display: flex;
  align-items: center;
  background: #fff;
  padding: 15px 20px;
  margin-bottom: 16px;
  border-radius: 8px;
  i {
    display: inline-block;
    width: 32px;
    height: 32px;
    background: url("../../../../../assets/GlobalImg/pc_goback.png") no-repeat center;
    background-size: 24px 24px;
    margin-right: 4px;
    cursor: pointer;
  }
  span {
    font-size: 20px;
    color: #000;
    line-height: 32px;
  }
}

.wrap {
  flex: 1;
  width: 100%;
  background: #fff;
  border-radius: 8px;
  padding-top: 24px;
  .role_list_wrap {
    display: flex;
    justify-content: center;
  }
  .role_list {
    width: 734px;
    display: flex;
    flex-wrap: wrap;
    row-gap: 16px;
  }
  .role_item_wrap {
    width: 50%;
    padding: 0 12px;
  }
  .role_item {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    column-gap: 12px;
    background: #FAFAFA;
    border-radius: 8px;
    padding: 16px 12px;
    cursor: pointer;
    img {
      flex-shrink: 0;
    }
    .role_item_name {
      flex: 1;
      word-break: break-all;
      font-size: 18px;
      color: #000;
      font-weight: 500;
    }
  }
}
