import React, {useState, useEffect, useRef} from 'react';
import { history,connect } from 'umi';
import styles from "./index.less";
import {Form, Input} from "antd";
import _ from "lodash";
const  { TextArea } = Input;
import {
  echoFormValueByForm,
  getFieldDecoratorByitemByLv2, scrollToTop, setFormValues
} from "@/pages/CreationOrthodontics/components/CreationFormUtils";
import { useDebounce, getArrailUrl } from '@/utils/utils';
import {stringify} from "qs";


const Step3: React.FC = (props) => {
  // 是否嵌套在5i5ya的iframe中
  const isInIframe = (self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya')

  const [ form] = Form.useForm();
  const { CreationOrthodontics, dispatch } = props || {}
  const { medicalRecordJson,DictionaryData,DataBymedicalRecordJson } = CreationOrthodontics || {}  // 正畸病例字典结构
  const {
    id:consultationId,       // :consultationId 8dad6e1a68714dc7b1f908902ad2d4f4
    customerId,              // 客户id
    createUserId,            // 创建人id
    tenantId,                // 租户id
    orderCaseTemplate,       // "orderCaseTemplate": 0, -- 订单病例模板 1通用病例 2正畸病例
  } = DictionaryData || {}

  let isNotRequired = orderCaseTemplate == 2 ? true : false;

  // 问题清单
  const checkJson7  = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item)=>{return item.dictCode == '7'})
  // 诊断
  const checkJson8  = DataBymedicalRecordJson && DataBymedicalRecordJson.find((item)=>{return item.dictCode == '8'})

  useEffect(()=>{scrollToTop()},[]);

  useEffect(() => {
    echoFormValue()
  },[DataBymedicalRecordJson])

  // 回显表单数据
  const echoFormValue = ()=>{
    if (checkJson7 && checkJson8) {
      let FormArr = [].concat(checkJson7.subsetList).concat(checkJson8.subsetList)
      setFormValues(FormArr,form,isNotRequired)
    }
  }



  // 问题清单
  // getFieldDecoratorBy
  const getFieldDecoratorByProblems = ()=>{
    return (
      <>
        {checkJson7 && checkJson8 && [checkJson7,checkJson8].map((itemByLv1) => {
          return (
            <div style={{marginBottom:'30px'}}>

                <div className={styles.title_span}>
                  {!isNotRequired &&
                    <span style={{color:'red'}}>*</span>
                  }
                  {itemByLv1.dictName}：</div>
              {Array.isArray(itemByLv1.subsetList) && itemByLv1.subsetList.map((itemByLv2) => {
                return getFieldDecoratorByitemByLv2(
                  {
                    item:itemByLv2,
                    form,
                    onChange:onChange,
                    isNotRequired:isNotRequired,
                  }
                );
              })}
            </div>
          )
        })}
      </>
    )
  }

  const onChange = useDebounce((value) => {
    let valuesByForm = form.getFieldsValue();
    onFinish(valuesByForm)
  },3000)

  const onFinish = _.debounce((value: any,isSubmitLoading) => {
    const { dispatch } = props || {}
    const { errorFields } = value || {}
    if (Array.isArray(errorFields) && errorFields.length > 0) { return }

    let formDataArr = Object.keys(value).map((key) => {
      let inputContent = value[key];
      // 判定当前是否是字符串
      if (typeof inputContent == 'string' && inputContent.length > 200) {
        inputContent = inputContent.substring(0, 200);
      }
      return { id: key,  inputContent: inputContent }
    })
    dispatch({
      type: 'CreationOrthodontics/saveDataByMedicalRecordJson',
      payload: {
        processNode:3,         // 问题清单及诊断
        formDataArr:formDataArr,
        isSubmit:isSubmitLoading,
      }
    }).then(()=>{
      if(isSubmitLoading) {
        // 在5i5ya的iframe中
        if (isInIframe) {
          let postData = {
            dataType: 'pathname',       // 页面地址onchange事件
            pathnameByChild: '/CreationOrthodontics/Step4',  // 路由信息
            searchByChild: `?${stringify(history.location.query)}`,  // 路由信息
          }
          console.log('子级发送数据：', postData, getArrailUrl())
          window.parent.postMessage(postData, getArrailUrl())
          return
        }

        history.replace(`/CreationOrthodontics/Step4?${stringify(history.location.query)}`)
      }
    })
  },500)

  return (
    <div className={styles.page_info}>
      <div className={styles.warp_content}>
        {/* 标题 */}
        <div className={styles.title_box}>
          <div>问题清单及诊断</div>
        </div>

        {/* 内容 */}
        {checkJson7 && checkJson8 &&
          <Form
            form={form}
            initialValues={{}}
            onValuesChange={onChange}
            onFinish={(value)=>{onFinish(value,true)}}
            onFinishFailed={onFinish}
          >
            <div className={styles.content_warp}>
                {getFieldDecoratorByProblems()}
            </div>
          </Form>
        }

        {/* 上一步下一步 */}
        <div className={styles.submitWarp}>
          <div className={styles.submitBox}>
            <div onClick={()=>{
              if (form) {
                let value = form.getFieldsValue()
                onFinish(value,false);

                // 在5i5ya的iframe中
                if (isInIframe) {
                  const postData = {
                    dataType: 'goBack',       // 页面地址onchange事件
                  }
                  console.log('子级发送数据：', postData, getArrailUrl())
                  window.parent.postMessage(postData, getArrailUrl())
                  return
                }

                // history.goBack();
                history.replace(`/CreationOrthodontics/Step2?${stringify(history.location.query)}`)
              }
            }} className={styles.submit_btn_Cancel}>上一步</div>
            <div onClick={()=>{
              if (form) {
                form.submit()
              }
            }} className={styles.submit_btn_Enter}>下一步</div>
          </div>
        </div>

      </div>
    </div>
  )
}

export default connect(({ CreationOrthodontics,pcAccount, loading }: any) => ({
  CreationOrthodontics,pcAccount, loading
}))(Step3)
