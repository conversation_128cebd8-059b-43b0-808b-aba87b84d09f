/**
 * @Description: 数据加载异常
 * @author: 赵斐
 */
import { useState, useEffect } from 'react';
import styles from './index.less';
import networkImg from '@/assets/GlobalImg/network.png'; // 无网络图片
import noDataImg from '@/assets/GlobalImg/no_data.png';  // 无数据图片

type propsType = {
  interfaceStatus: number,   // 1 数据加载失败  2 暂无数据
  exceptionStyle: any,       // 样式调整
  retryFun?: any,             // 点击重试调用页面接口
  resetStatusFun?: any,       // 重置父组件接口状态
}

const Index = (props: propsType) => {
  const { interfaceStatus, retryFun, resetStatusFun , exceptionStyle } = props;
  const [ countDownNumber, setCountDownNumber ] = useState(0); // 总秒数


  useEffect(() => {
    if(countDownNumber == 0){
      resetParentComponentStatus()
    }
    if (!countDownNumber) return;

    const intervalId = setInterval(() => {
      setCountDownNumber(countDownNumber - 1);
    }, 1000);

    return () => clearInterval(intervalId);
  }, [countDownNumber]);

  /**
   * 点击重试
   * @param val  
   */
  const onRetryClick = (val: number) => {
    if (!val) {
      retryFun && retryFun()
      setCountDownNumber(30)
    }
  }

  // 重置父组件状态
  const resetParentComponentStatus = ()=>{
    resetStatusFun && resetStatusFun()
  }

  return (
    <div className={styles.no_content} style={exceptionStyle ? exceptionStyle : { paddingTop: 110 }}>
      {
        interfaceStatus == 1 ?<div className={styles.net_work_img}></div>:<div className={styles.no_data_img}></div>
      }
      
      <div className={styles.no_content_title}>{interfaceStatus == 1 ? "网络出现异常" : interfaceStatus == 2 ? "暂无数据" : "暂无该搜索结果"}</div>
      {
        interfaceStatus != 2 ? <div className={styles.no_content_tips}>{interfaceStatus == 1 ? "请检查一下网络连接..." : "请试试其他搜索关键词"}</div> : null
      }
      {
        interfaceStatus == 1 ?
          <div className={styles.no_content_btn} onClick={() => { onRetryClick(countDownNumber) }}>
            重试{countDownNumber ? `(${countDownNumber}s)` : null}
          </div> : null
      }
    </div>
  )
}

export default Index;
