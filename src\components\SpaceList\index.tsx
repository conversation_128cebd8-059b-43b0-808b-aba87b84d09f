/**
 * @Description: 空间组件
 */
import React, { useEffect, useState } from 'react'
import { history } from 'umi'
import classNames from 'classnames'
import DanmuJs from 'danmu.js'
import { randomColor, processNames, gdpFormat, getOperatingEnv } from '@/utils/utils'
import { getNewImageNumber, space_click, group_click } from '@/pages/Home/utils'
import { Swiper } from 'antd-mobile'
import styles from './index.less'

import CoverImageOrVideo from './CoverImageOrVideo'        // 空间封面/视频组件

interface PropsType {
  componentData: any,                                      // 组件数据，格式：{ dataList: [], config: { number: xx } }
  style?: any,                                             // 样式
  isHomePage?: any,                                         // 是否为首页，1是，0否
  moduleIndex?: any,                                        // 当前组件在所有空间组件中的索引
  isClassifyGuide?: any,                                   // 是否在分类导航组件中，1是
  classifyGuideTabIndex?: any,                             // 在分类导航组件中时，选中的tab标签
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { componentData, style = {}, isHomePage, moduleIndex, isClassifyGuide, classifyGuideTabIndex } = props
  // config: { number: 11/12/13/14/15/50/60 } (11~15)-10， 一行几个，50 轮播，60 横向滑动
  const { config } = componentData
  const dataList = componentData.dataList || []
  const newNumber = getNewImageNumber(config.number)

  // 当前数据长度
  const [currentLength, setCurrentLength] = useState(0)

  // 点击空间跳转到空间详情
  const goToUrl = (item, index) => {
    // 传空间id，starSpaceType 1 直播，2 会议
    if (item.starSpaceType == 2) {
      history.push(`/PlanetChatRoom/Meet/${item.id}`)
    } else {
      history.push(`/PlanetChatRoom/Live/${item.id}`)
    }


    // 首页友盟统计，isHomePage=1
    if (isHomePage != 1) {
      return
    }
    // 在分类导航组件中
    if (isClassifyGuide == 1) {
      // 首页，分类组件点击量
      group_click(moduleIndex, `第${moduleIndex}个分类组件，第${classifyGuideTabIndex}个标签，第${index + 1}个空间`)
      return
    }
    // 首页，空间点击量
    space_click(moduleIndex, index + 1)
  }

  useEffect(() => {
    // 一行一个的时候，显示弹幕
    if (newNumber == 11 && dataList.length > 0) {
      // 保存当前数组长度，加载下一页数据后，从新加载出的数据开始初始化
      setCurrentLength(dataList.length)
      // 初始化弹幕
      initializationByDanmu()
    }
  }, [dataList.length])

  // 初始化弹幕组件
  const initializationByDanmu = () => {
    dataList.forEach((item, index) => {
      if (index >= currentLength && item.msgList && item.msgList.length > 0) {
        new DanmuJs({
          channelSize: 28,             // 轨道大小
          container: document.getElementById(`vs${componentData.id}${index}`), // 弹幕容器，该容器发生尺寸变化时会自动调整弹幕行为
          player: document.getElementById(`ms${componentData.id}${index}`),    // 播放器容器（需要有这个，弹幕才能无限轮播）
          area: {
            // 弹幕显示区域
            start: 0,                  // 区域顶部到播放器顶部所占播放器高度的比例
            end: 1,                    // 区域底部到播放器顶部所占播放器高度的比例
          },
          mouseControl: false,         // 打开鼠标控制, 打开后可监听到 bullet_hover 事件。danmu.on('bullet_hover', function (data) {})
          mouseControlPause: false,    // 鼠标触摸暂停。mouseControl: true 生效
          chaseEffect: true,           // 开启滚动弹幕追逐效果, 默认为true
          comments: item.msgList && item.msgList.length > 0 ? item.msgList.map((item, index) => ({
            moveV: 100,                // 每秒移动距离，单位px
            id: index,                 // 弹幕id，需唯一
            txt: item,                 // 弹幕文字内容
            style: {
              color: '#FFFFFF',
              fontSize: '11px',
              borderRadius: '20px',
              padding: '0 6px',
              margin: '8px 0',
              height: '19px',
              lingHeight: '19px',
              backgroundColor: 'rgba(0,0,0,0.2)',
            },
          })) : []
        })
      }
    })
  }


  return (
    <div className={classNames(styles.space_container, {
      [styles.one]: newNumber == 11,
      [styles.two]: newNumber == 12,
      [styles.sixty]: newNumber == 60,
    })} style={style}>
      {
        newNumber == 60 ?
          <Swiper rubberband={false} stuckAtBoundary={true} slideSize={45.86}>
            {
              dataList.map((item, index) => {
                return (
                  <Swiper.Item key={`${item.id}${index}`}>
                    <div key={item.id} className={styles.item_box} onClick={() => goToUrl(item, index)}>
                      <div className={styles.cover_img}>
                        <CoverImageOrVideo
                          isVideo={false}
                          spaceCoverUrlShow={item.spaceCoverUrlShow}
                          onClick={() => {goToUrl(item, index);}}
                        />
                        {/* 封面中的标题 */}
                        {
                          item.isTemplateCover==1 &&
                          <div className={styles.title_in_cover_image}>{item.name}</div>
                        }
                        {/* 状态 */}
                        {
                          item.status && item.status == 3 ? '' :
                          <div className={styles.status_box}>
                            <i className={classNames(styles.status_icon, {
                              [styles.icon1]: item.status == 1,
                              [styles.icon2]: item.status == 2,
                            })}></i>
                            <span>{item.status == 1 ? '进行中' : item.status == 2 ? '预约中' : ''}</span>
                          </div>
                        }
                        {/*GDP */}
                        <span className={styles.gdp}>{gdpFormat(item.gdp)}GDP | {gdpFormat(item.pv)}观看</span>
                      </div>
                      <div className={styles.space_info_box}>
                        <div className={styles.title}>
                          <div className={styles.title_text}>{item.name}</div>
                          {item.intro ? <div className={styles.space_intro}>{item.intro}</div> : ''}
                        </div>
                        <div className={styles.footer}>
                          <div className={styles.footer_left}>
                            <div className={styles.left_avatar} style={item.hostImgUrlShow ? {backgroundImage: `url(${item.hostImgUrlShow})`} : {background: randomColor(item.wxUserId)}}>
                              {!item.hostImgUrlShow && processNames(item.hostName)}
                              {item.isKing ? <i></i> : ''}
                            </div>
                            <div className={styles.left_name}>{item.hostName}</div>
                          </div>
                          <div className={styles.footer_right}>
                            {
                              item.guestDataList && item.guestDataList.length > 0 &&
                              item.guestDataList.map((itemChild, indexChild) => {
                                if (indexChild >= 3) {
                                  return null
                                }
                                return (
                                  <div key={itemChild.wxUserId} className={styles.right_avatar} style={itemChild.headUrlShow ? {backgroundImage: `url(${itemChild.headUrlShow})`} : {background: randomColor(itemChild.wxUserId)}}>
                                    {!itemChild.headUrlShow && processNames(itemChild.userName)}
                                  </div>
                                )
                              })
                            }
                            {
                              item.guestDataList && item.guestDataList.length > 3 &&
                              <div className={styles.avatar_more}>···</div>
                            }
                          </div>
                        </div>
                      </div>
                    </div>
                  </Swiper.Item>
                )
              })
            }
          </Swiper>
          :
          dataList.map((item, index) => {
            return (
              <div key={`${index}`} className={classNames(styles.item_box,'item_box_BySpaceList')} onClick={(event) => {event.stopPropagation(); goToUrl(item, index);}}>
                <div className={styles.cover_img}>
                  <CoverImageOrVideo
                    data={item}
                    spaceCoverUrlShow={item.spaceCoverUrlShow}
                    onClick={() => {goToUrl(item, index);}}
                  />
                  {/* 封面中的标题 */}
                  {
                    item.spaceCoverUrlShow && item.spaceCoverUrlShow.indexOf('live_broadcast') > -1 && !(item.vodPathUrl && getOperatingEnv() != '2') ?
                    <div className={styles.title_in_cover_image}>{item.name}</div> : null
                  }
                  {/* 状态 */}
                  {
                    item.status && item.status == 3 ? '' :
                    <div className={styles.status_box}>
                      <i className={classNames(styles.status_icon, {
                        [styles.icon1]: item.status == 1,
                        [styles.icon2]: item.status == 2,
                      })}></i>
                      <span>{item.status == 1 ? '进行中' : item.status == 2 ? '预约中' : ''}</span>
                    </div>
                  }
                  {/* GDP */}
                  <span className={styles.gdp}>{gdpFormat(item.gdp)}GDP | {gdpFormat(item.pv)}观看</span>

                  {/* 弹幕容器 */}
                  <div id={`vs${componentData.id}${index}`} className={styles.danmu_box}>
                    <div id={`ms${componentData.id}${index}`}></div>
                  </div>
                </div>
                <div className={styles.space_info_box}>
                  <div className={styles.title}>
                    <div className={styles.title_text}>{item.name}</div>
                    {item.intro ? <div className={styles.space_intro}>{item.intro}</div> : ''}
                  </div>
                  <div className={styles.footer}>
                    <div className={styles.footer_left}>
                      <div className={styles.left_avatar} style={item.hostImgUrlShow ? {backgroundImage: `url(${item.hostImgUrlShow})`} : {background: randomColor(item.wxUserId)}}>
                        {!item.hostImgUrlShow && processNames(item.hostName)}
                        {item.isKing ? <i></i> : ''}
                      </div>
                      <div className={styles.left_name}>{item.hostName}</div>
                    </div>
                    <div className={styles.footer_right}>
                      {
                        item.guestDataList && item.guestDataList.length > 0 &&
                        item.guestDataList.map((itemChild, indexChild) => {
                          if (indexChild >= 3) {
                            return null
                          }
                          return (
                            <div key={itemChild.wxUserId} className={styles.right_avatar} style={itemChild.headUrlShow ? {backgroundImage: `url(${itemChild.headUrlShow})`} : {background: randomColor(itemChild.wxUserId)}}>
                              {!itemChild.headUrlShow && processNames(itemChild.userName)}
                            </div>
                          )
                        })
                      }
                      {
                        item.guestDataList && item.guestDataList.length > 3 &&
                        <div className={styles.avatar_more}>···</div>
                      }
                    </div>
                  </div>
                </div>
              </div>
            )
          })
      }
    </div>
  )
}

export default Index
