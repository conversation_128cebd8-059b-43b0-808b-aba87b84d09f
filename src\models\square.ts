import {getSquareList, getStarKingdomList, getStarSpaceList, getWordList,} from '@/services/square'

export default {
  namespace: 'square',
  state: {
    checkedTab: 0,                                         // 搜索结果页，选中tab
    checkedChildTab: 6,                                    // 搜索结果页，选中二级tab
    searchKey: '',                                         // 搜索关键词
    meetingTabState: 2,                                     // 会议tab选中状态
    spaceStatusList: [],                                   // 空间状态：1直播中、2预约中、3弹幕轰炸中
    meetingStatusList: [],                                 // 会议状态：1直播中、2预约中、3弹幕轰炸中
  },

  effects: {
    // 分页获取王国数据
    *getStarKingdomList({ payload }, { call }) {
      const response = yield call(getStarKingdomList, payload)
      return response
    },
    // 分页获取空间数据
    *getStarSpaceList({ payload }, { call }) {
      const response = yield call(getStarSpaceList, payload)
      return response
    },

    // 根据用户ID获取搜索关键字
    *getWordList({ payload }, { call }) {
      const response = yield call(getWordList, payload)
      return response
    },

    // 广场检索
    *getSquareList({ payload }, { call }) {
      const response = yield call(getSquareList, payload)
      return response
    },
  },

  reducers: {
    // 保存数据
    save(state, { payload }) {
      return {
        ...state,
        ...payload,
      }
    },
    // 清空数据
    clean(state, { payload }) {
      return {
        checkedTab: 0,                                         // 搜索结果页，选中tab
        checkedChildTab: 6,                                    // 搜索结果页，选中二级tab
        searchKey: '',                                         // 搜索关键词
        meetingTabState: 2,                                    // 会议tab选中状态
        spaceStatusList: [],                                   // 空间状态：1直播中、2预约中、3弹幕轰炸中
        meetingStatusList: [],                                 // 会议状态：1直播中、2预约中、3弹幕轰炸中
      }
    },
  },

  subscriptions: {
    setup({ dispatch, history }) {
      return history.listen(({ pathname, search }) => {
        if (
          pathname.indexOf('/Square') == -1 &&
          pathname.indexOf('/Square/SearchResult') == -1 &&
          pathname.indexOf('/PlanetChatRoom/') == -1 &&
          pathname.indexOf('/Kingdom/') == -1 &&
          pathname.indexOf('/CreateGraphicsText/ArticleDetails') == -1 &&
          pathname.indexOf('/CreateGraphicsText/PostDetails') == -1 &&
          pathname.indexOf('/CreateGraphicsText/ExternalLinksDetails') == -1&&
          pathname.indexOf('/Expert/ExpertDetails') == -1
        ) {
          dispatch({
            type: 'clean',
          })
        }
      })
    }
  }
}
