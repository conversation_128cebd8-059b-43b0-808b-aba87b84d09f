/**
 * @Description: 支付结果弹窗
 */
import React, {useEffect, useState} from 'react'
import classNames from 'classnames'
import { Modal, Button, Spin } from 'antd'
import { CloseOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import styles from './index.less'

import photo_icon from '@/assets/Consultation/Pc/emoji_icon.png'
import {getConsultationOrderInfo} from "@/services/consultation/ConsultationList";
import {history} from "@@/core/history";
import {goConsultationDetail} from "@/utils/utils";
import {connect} from "umi";

import successIcon from '@/assets/GlobalImg/success.png'
import pay_fail from '@/assets/Consultation/Pc/pay_fail.png'


interface PropsType {
  visible: boolean,                      // 标题
  onCancel: any,                         // 标题
  consultationId:any,                    // 指导订单ID
  consultationType:any,                  // 指导类型[1图文、2视频]
}

const Index: React.FC<PropsType> = (props: any) => {
  const [checkedTab, setCheckedTab] = useState(1)
  const [ consultationOrderInfo,setConsultationOrderInfo ] =  useState(null);
  const [ loading, setLoading ] = useState(false); // 获取订单详情loading
  const userInfo = JSON.parse(localStorage.getItem('userInfo'));
  const { consultationId, consultationType, dispatch } = props || {}

  const { friUserId:idByUserInfo, name} = userInfo || {}
  const {
    id:idByOrderInfo, // : "a2c99d2774a54ddf900bfcd23b2be533",//指导订单ID
    orderNumber,      // : "2023100813402475762",//订单号
    type,             // : 1,//指导类型(1图文、2视频)
    isFinish,         // : 0,
    status,           // : 1,//支付状态(0已取消、1未支付(待评估)、2去支付(已评估)、3已支付)
    amount,           // : 0.10,//账单金额
    expertsName,      // : "zhang",//指导医生名称
    vipUnitPrice,     // : 1.00,//会员单价(图文是/次，视频是/30min)
  } = consultationOrderInfo || {};


  useEffect(() => {
    if(props.visible) {
      getConsultationAndCaseInfo()
    }else {
      setConsultationOrderInfo(null);
    }
  },[props.visible])

  const getConsultationAndCaseInfo = async () => {
    /**
     *  consultationId, // [string] 是 指导订单ID
     *  type, // [string] 是 (1:运营端订单详情, 2:H5/WEB端视频订单详情)
     */
    setLoading(true);
    let dataByGetConsultationAndCaseInfo = await getConsultationOrderInfo({
      consultationId: consultationId,
      type: consultationType,
    })
    setLoading(false);
    const {code, content} = dataByGetConsultationAndCaseInfo || {}
    if (code == 200 && content) {
      setConsultationOrderInfo(content);
    }else {
      setConsultationOrderInfo(null);
    }
  }

  // 切换tab
  const tabOnChange = (value) => {
    setCheckedTab(value)
  }

  return (
    <Modal
      title="支付结果"
      visible={props.visible}
      onCancel={props.onCancel}
      className={styles.modal}
      destroyOnClose
      footer={null}
    >
      <Spin spinning={loading}>
        <div className={styles.img_wrap}>
          <img src={status == 3 ? successIcon : pay_fail} width={72} height={72} alt=""/>
        </div>
        <div className={styles.title}>{ status == 3 ? '支付成功' : '支付失败' }</div>

        <div className={styles.btn_wrap}>
          <Button onClick={()=>{
            if(history.location.pathname.indexOf('/UserInfo') > -1) {
              // 当前页面是指导列表页面
              props.onCancel()
            }else {
              dispatch({
                type: 'pcAccount/save',
                payload: {
                  tabState: 6,
                  subTabState: 1,
                }
              })
              history.replace('/UserInfo')
            }
          }} className={styles.btn_left}>返回订单</Button>

          {status == 3 &&
            <Button
              type="primary"
              onClick={()=>{
                if(history.location.pathname.indexOf('/ConsultationModule/ConsultationDetails') > -1) {
                  // 当前页面本身是指导详情页面
                  window.location.replace(window.location.href)
                }else if (history.location.pathname.indexOf('/ConsultationModule/StartConsultation') > -1) {
                  // 跳转到指导详情页面
                  goConsultationDetail({isReplace:true, ...consultationOrderInfo})
                } else {
                  // 跳转到指导详情页面
                  goConsultationDetail(consultationOrderInfo)
                }
              }}
              className={styles.btn_right}
            >
              查看指导详情
            </Button>
          }
        </div>
      </Spin>

    </Modal>
  )
}
export default connect(({ pcAccount, loading }: any) => ({
  pcAccount, loading
}))(Index)
