
import request from "@/utils/request";
import {getOperatingEnv} from "@/utils/utils";
import {stringify} from "qs";

/**
 *  /user/h5User/getMsgCodeUserInfo
 *  个人中心、个人主页获取用户信息
 */
export async function getH5UserInfo(params: {
  wxUserId?: any,  // token
}, options?: { [key: string]: any }) {
  return request(`/api/user/h5User/getH5UserInfo`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}
/*
* 个人中心、个人主页切换马甲
* vestUserId 马甲id
* */
export async function setSwitchWaistcoatAccount(params: {
  vestUserId?: any,  //
}, options?: { [key: string]: any }) {
  return request(`/api/fri-uc/user/switch-vest-user`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

/*
* 个人中心、个人主页切换马甲获取新token
* vestToUcKey 马甲id的key
* */
export async function getSwitchWaistcoatToken(params: {
  vestToUcKey?: any,
}, options?: { [key: string]: any }) {
  return request(`/api/fri-uc/fri-auth/vest-to-uc-login`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

/**
 * /user/h5User/editUserinfo
 * 修改用户信息
 * headUrl [string]  头像短路径
 * name  [string]    姓名
 * nickName [string]  昵称
 */
export async function editUserinfo(params: {}, options?: { [key: string]: any }) {
  return request(`/api/user/h5User/editUserinfo`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

/**
 * /server/h5Index/getMemberIconList
 * 获取会员图标列表
 * wxUserId [string]  当前登录用户ID，没登录不用传
 */
export async function getMemberIconList(params: {}, options?: { [key: string]: any }) {
  return request(`/api/server/h5Index/getMemberIconList`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}


/**
 * /user/h5User/unsubscribe
 * 注销UC账号
 * */
export async function unsubscribe(params: {}, options?: { [key: string]: any }) {
  return request(`/api/fri-uc/user/cancel`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

/**
 * /user/h5User/checkSuperAccount
 * 验证用户是否为超级账户
 * wxUserId [string] 用户ID
 */
export async function checkSuperAccount(params: {},options: {}) {
  return request(`/api/user/h5User/checkSuperAccount`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

/**
 * /server/h5Experts/getH5FocusList
 * H5-获取我关注的用户专家列表
 * */
export async function getH5FocusList(params: {},options: {}) {
  return request(`/api/server/h5Experts/getH5FocusList`, {
    method: 'GET',
    params: params,
  });
}

/**
* /server/h5ExcellentCase/getCollectList
* 我的收藏-分页获取优秀病例数据
* wxUserId 当前登录用户ID
* pageNum  页码
* pageSize 条数
*/
export async function getCollectList(params: {},options: {}) {
  return request(`/api/server/h5ExcellentCase/getCollectList`, {
    method: 'GET',
    params: params,
  });
}

/**
* /server/square/getStarSpaceCollect
* 我的收藏-分页获取收藏空间数据
* wxUserId 当前登录用户ID
* pageNum  页码
* pageSize 条数
 * starSpaceType 1:直播 2:会议
*/
export async function getStarSpaceCollect(params: {},options: {}) {
  return request(`/api/server/square/getStarSpaceCollect`, {
    method: 'GET',
    params: params,
  });
}

/**
* /server/kingdom/checkKingdomName
* 校验王国名称
* name 王国名称
* id  王国id 编辑时必传
*/
export async function checkKingdomName(params: {},options: {}) {
  return request(`/api/server/kingdom/checkKingdomName`, {
    method: 'GET',
    params: params,
  });
}

/**
* /server/space/addSpace
* 创建空间
* name 空间名称
* wxUserId 主持人id
* hostName 主持人名称
* kingdomId 王国id
* kingdomName 王国名称
* password   密码
* startStatus  开始状态：1立刻、2预约
* appointmentStartTime 预约开始时间
* spaceCoverUrl  空间封面路径
* spaceAdvertisingUrl 空间广告路径
* updateUserId    操作人用户ID
* guestIdList     嘉宾id集合
* intro　　　　空间介绍
*/
export async function addSpace(params: {},options: {}) {
  return request(`/api/server/space/addSpace`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

/**
* /server/space/checkSpacePassword
* 校验空间密码
* id           空间名称
* password     密码
*/
export async function checkSpacePassword(params: {},options: {}) {
  return request(`/api/server/space/checkSpacePassword`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

/**
* /server/kingdom/getCreateAndJoinKingdomList
* 查询我创建的王国和我加入的王国
* wxUserId     用户ID
*/
export async function getCreateAndJoinKingdomList(params: {},options: {}) {
  return request(`/api/server/kingdom/getCreateAndJoinKingdomList`, {
    method: 'GET',
    params: params,
  });
}

/**
* /server/H5Base/searchUserListByQueryKey
* 根据姓名、手机号，模糊查询用户信息
* queryKey     姓名或手机号
*/
export async function searchUserListByQueryKey(params: {},options: {}) {
  return request(`/api/server/H5Base/searchUserListByQueryKey`, {
    method: 'GET',
    params: params,
  });
}

/**
* /server/kingdom/addKingdom
* 新增王国
* wxUserId     国王ID
* kingName     国王名称
* name         王国名称
* descriptions 王国描述
* updateUserId
*/
export async function addKingdom(params: {},options: {}) {
  return request(`/api/server/kingdom/addKingdom`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

/**
* /server/kingdom/joinOrQuitKingdom
* 加入或退出王国
* id           王国ID
* wxUserId     用户ID
* type         类型(1加入，2退出)
*/
export async function joinOrQuitKingdom(params: {},options: {}) {
  return request(`/api/server/kingdom/joinOrQuitKingdom`, {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

/**
* /server/kingdom/getKingdomInfo
* 查询王国详情
* id           王国ID
* wxUserId     用户ID
*/
export async function getKingdomInfo(params: {},options: {}) {
  return request(`/api/server/kingdom/getKingdomInfo`, {
    method: 'GET',
    params,
  });
}

/*
* kingdomId 王国ID
* starSpaceType 空间类型 1直播 2会议
* */
export async function getKingdomSpaceList(params: {},options: {}) {
  return request(`/api/server/kingdom/get-star-info-by-kingdom`, {
    method: 'GET',
    params,
  })
}

/**
 * 获取空间海报信息（张志军）
 */
export async function getSpacePosterInfo(params) {
  return request('/api/server/space/getSpacePosterInfo', {
    method: 'GET',
    params,
  })
}

/**
 * 获取微信公众号下JSAPI权限config信息（张志军）
 * @params appId                       appId
 * @params currentUrl                  当前页面url
 */
export async function getJsapiTicket(params) {
  return request('/api/server/wxMpAuth/getJsapiTicket', {
    method: 'GET',
    params,
  })
}

/**
 * 分享操作更新gdp等数据（时俊瑶）
 * @params id                          被分享ID(王国、空间)
 * @params shareId                     分享人ID
 * @params type                        类型(2王国，3空间)
 * @params hostId                      空间主持人ID
 */
export async function shareUpdateByType(params) {
  return request(`/api/server/H5Base/shareUpdateByType`, {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取空间视频上传签名
 */
export async function getVodUploadSign(params) {
  return request(`/api/server/space/getVodUploadSign`, {
    method: 'GET',
    params,
  })
}

/**
 * 退出登录
 */
export async function exitLogout(params) {
  return request(`/api/user/h5User/logout`, {
    method: 'POST',
    data: params,
  })
}

/**
 * 空间编辑时获取基础信息
 * spaceId  空间ID
 * wxUserId  当前登录人ID
 */
export async function getSpaceInfoByEdit(params) {
  return request(`/api/server/space/getSpaceInfoByEdit`, {
    method: 'GET',
    params,
  })
}

/**
 * 编辑保存空间信息
 *  id 空间ID-必传
    name 空间名称
    password 密码
    startStatus 开始状态：1立刻、2预约
    appointmentStartTime 预约开始时间
    spaceCoverUrl 空间封面路径
    spaceAdvertisingUrl  空间广告路径
    updateUserId  操作人用户ID -必传
    guestIdList  嘉宾id集合
    vodFileId  vod视频文件id
    intro　　　　空间介绍
    isDel  1是删除，删除时必传
    isDisable  1是下架，下架时必传
 */
export async function editSpaceInfo(params) {
  return request(`/api/server/space/editSpaceInfo`, {
    method: 'POST',
    data: params,
  })
}

/*
* 个人中心空间从列表删除空间
* spaceId 空间ID
* */

export async function deleteSpaceFromList(params) {
  return request(`/api/server/space/updateIsSearchById`, {
    method: 'GET',
    params,
  })
}

/**
 * H5编辑王国获取基础信息
 * kingdomId  王国ID
 * wxUserId  当前登录人ID
 */
export async function getKingdomInfoByEdit(params) {
  return request(`/api/server/kingdom/getKingdomInfoByEdit`, {
    method: 'GET',
    params,
  })
}

/**
 * 编辑保存空间信息
 *  id 王国ID-必传
    wxUserId 当前登录的用户ID-必传
    name 王国名称
    descriptions 王国描述
    kingdomCoverUrl 王国封面
    updateUserId 操作人用户ID
 */
export async function editKingdomInfo(params) {
  return request(`/api/server/kingdom/editKingdomInfo`, {
    method: 'POST',
    data: params,
  })
}


/**
 * 根据用户ID与空间ID获取品牌下所有分组信息
 * spaceId  空间ID，编辑必传
 */
export async function getBizGroupByTenantId(params) {
  return request(`/api/server/h5BizGroup/getBizGroupByTenantId`, {
    method: 'GET',
    params,
  })
}

/**
 * 王国查询图文信息
 */
export async function geKingdomList(params) {
  return request(`/api/server/imageTextInfoH5/geKingdomList`, {
    method: 'GET',
    params,
  })
}

/**
 * 获取直播 会议封面信息
 */
export async function getSpaceCover(params) {
  return request(`/api/server/space/getSpaceCover`, {
    method: 'GET',
    params,
  })
}

/**
 * 会议参会人
 */
export async function attendeeSearchUserByQueryKey(params) {
  return request(`/api/server/H5Base/attendeeSearchUserByQueryKey`,{
    method: 'GET',
    params,
  })
}

/**
 * 根据区域或者机构获取用户
 */
export async function getAttendeeSearchUserByBizGroupId(params){
  return request(`/api/server/H5Base/getAttendeeSearchUserByBizGroupId`,{
    method: 'GET',
    params,
  })
}

/**
 * 用户个人主页展示学习金信息（H5、APP）
 */
export async function getFriUserToScholarship(params){
  return request(`/api/server/scholarship-account/fri-user-to-scholarship`,{
    method: 'GET',
    params,
  })
}
/**
 * 获取用户学习金明细新增、核销记录（H5、APP）
 * /get-scholarship-detail-list
 */
export async function getScholarshipDetailList(params){
  return request(`/api/server/scholarship-account/get-scholarship-detail-list`,{
    method: 'GET',
    params,
  })
}

/**
 * 获取认证的身份字典-实名认证版本
 */
export async function getIdentityTypeDict(params){
  return request(`/api/fri-uc/self-auth/get-identity-type-dict`,{
    method: 'GET',
    params,
  })
}

/**
 * 保存个人实名认证-星球-实名认证版本
 * xxx
 */
export async function saveSelfAuth(params) {
  return request(`/api/fri-uc/self-auth/save-self-auth`, {
    method: 'POST',
    data: params,
  })
}

/**
 * 个人实名认证详情-星球-实名认证版本
 */
export async function getSelfAuthInfo(params){
  return request(`/api/fri-uc/self-auth/get-self-auth-info`,{
    method: 'GET',
    params,
  })
}

/**
 * 个人实名认证回显-星球-实名认证版本
 */
export async function getEchoSelfAuthInfo(params){
  return request(`/api/fri-uc/self-auth/get-echo-self-auth-info`,{
    method: 'GET',
    params,
  })
}

/**
 * 企业管理员或者员工 - 个人信息
 */
export async function getEnterpriseUserInfoApi(params){
  return request(`/api/fri-uc/friUser/homePageInfo`,{
    method: 'POST',
    params,
  })
}


/**
 * 企业员工 - 离职
 */

export async function staffResignApi(params) {
  return request(`/api/fri-uc/friBizManage/resign`,{
    method: 'POST',
    data: params,
  })
}

/**
 * 企业员工 - 邀请
 */

export async function getInviteImgApi(params) {
  return request(`/api/fri-uc/friBizManage/inviteQrCode`,{
    method: 'POST',
    data: params,
  })
}

