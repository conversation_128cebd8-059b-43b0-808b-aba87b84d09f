/**
 * @Description: 中转页，跳转到首页去
 */
import React, { useEffect } from 'react'
import { history } from 'umi'
import { getOperatingEnv } from '@/utils/utils'
import { Spin } from 'antd'

const Index: React.FC = (props) => {

  useEffect(() => {
    // 4 PC端
    if (getOperatingEnv() == 4) {
      history.push(`/home`)
    } else {
      history.push(`/Square`)
    }
  }, [])

  return (
    <Spin spinning={true}>
      <div style={{height: '100vh', background: '#fff'}}></div>
    </Spin>
  )
}

export default Index
