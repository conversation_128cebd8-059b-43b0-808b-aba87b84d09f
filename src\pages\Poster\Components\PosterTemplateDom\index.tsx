/**
 * @Description: 用于生成图片的海报模板dom
 */
import React from 'react'
import QRcode from 'qrcode.react'
import styles from './index.less'

// 图片、icon
import home_logo from '@/assets/home_logo.png'

import Avatar from '@/components/Avatar'
import {stringify} from "qs";         // 头像组件

interface PropsType {
  templateData: any,       // 模板数据
  data: any,                   // 数据
  // starSpaceType: number,       // 1 直播，2 会议
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')   // 用户信息
  const { templateData, data } = props;

  let shareUrl = `${window.location.origin}/PlanetChatRoom/${data.starSpaceType == 2 ? 'Meet' : 'Live'}/${data.id}?${stringify({
    shareUserId:UserInfo?.friUserId,
    isShare: 1,
    pwd: data.starSpaceType == 2 && data.password ? data.password : '',
  })}`
  if (data.starSpaceType == 2) {
    // meet
    shareUrl =  `${window.location.origin}/PlanetChatRoom/${data.starSpaceType == 2 ? 'Meet' : 'Live'}/${data.id}?${stringify({
      shareUserId:UserInfo?.friUserId,
      isShare: 1,
      pwd: data.starSpaceType == 2 && data.password ? data.password : '',
    })}`
  }else {
    // live
    shareUrl = `${window.location.origin}/Square?${
      stringify({
        ReservationId: data.id,
        shareUserId:UserInfo?.friUserId,
        isShare: 1,
      })
    }`
  }


  return (
    <div className={styles.template_wrap}>
      {/* 海报的dom */}
      <div id="poster_dom" className={styles.template_content}>
        <img src={templateData.imgUrl} width={'100%'} height={'100%'} alt=""/>

        <div className={styles.template_header}>
          <Avatar size={24} userInfo={{
            userId: UserInfo.friUserId,
            name: UserInfo.name,
            headUrlShow: UserInfo.headUrl,
          }}/>
          <span style={{color: templateData.textColor}}>{UserInfo.name}邀请你一起{data.starSpaceType == 1 ? '观看' : '参加会议'}</span>
        </div>

        <div className={styles.template_footer}>
          {/* 主持人 */}
          <div className={styles.footer_host_wrap}>
            <div className={styles.footer_host_content} style={{background: `linear-gradient(90deg, ${templateData.bgColor1} 0%, ${templateData.bgColor2} 100%)`}}>
              <Avatar size={28} userInfo={{
                userId: data.hostUserInfo && data.hostUserInfo.wxUserId,
                name: data.hostUserInfo && data.hostUserInfo.name,
                headUrlShow: data.hostUserInfo && data.hostUserInfo.headUrlShow,
              }} style={{marginLeft: -1}}/>
              <span className={styles.host_name}>{data.hostUserInfo && data.hostUserInfo.name}</span>
              <span className={styles.host_role}>{data.starSpaceType == 1 ? '直播主持人' : '会议发起人'}</span>
            </div>
          </div>

          {/* 直播/会议名 */}
          <p className={styles.footer_space_name}>{data.name}</p>

          {/* 嘉宾 */}
          <div className={styles.footer_guest_wrap}>
            {
              data.guestUserList && data.guestUserList.length > 0 &&
              <>
                <span className={styles.guest_label}>{data.starSpaceType == 1 ? '特邀嘉宾' : '参会人'}：</span>
                <div className={styles.guest_value}>
                  {
                    data.guestUserList.map((item, index) => {
                      return <span key={index}>{item.name}</span>
                    })
                  }
                </div>
              </>
            }
          </div>

          {/* logo、二维码 */}
          <div className={styles.footer_img_wrap}>
            <img className={styles.footer_img_logo} src={home_logo} width={90} height={19} alt=""/>
            <div className={styles.footer_img_qrcode}>
              <QRcode
                value={shareUrl}
                size={64}
                renderAs={'svg'}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Index
