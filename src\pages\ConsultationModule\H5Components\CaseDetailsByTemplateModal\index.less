.popup_container {
  :global {
    .adm-popup-body {
      border-radius: 16px 16px 0 0;
    }
  }
}
.header_line {
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  .header_line_bar {
    width: 48px;
    height: 4px;
    background: #D0D4D7;
    border-radius: 4px;
  }
}
.header_title {
  position: relative;
  font-size: 18px;
  line-height: 25px;
  text-align: center;
  font-weight: 500;
  margin-bottom: 16px;
  .close_icon {
    position: absolute;
    width: 32px;
    height: 32px;
    top: -3px;
    right: 16px;
    color: #000;
    display: flex;
    align-items: center;
    justify-content: center;
    :global {
      .anticon {
        font-size: 16px;
      }
    }
  }
}

.container {
  height: calc(100% - 70px);
  overflow-y: auto;
  padding: 0 12px 32px;
  :global {
    .adm-mask {
      z-index: 9992;
    }
  }
}
.case_title {
  font-size: 18px;
  color: #000;
  font-weight: 500;
  line-height: 25px;
  margin-bottom: 8px;
  word-break: break-all;
}
.case_tag {
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 2px;
  .tag {
    height: 21px;
    line-height: 21px;
    border-radius: 2px;
    background: #EDF9FF;
    padding: 0 4px;
    font-size: 12px;
    color: #0095FF;
    margin-right: 6px;
    margin-bottom: 6px;
  }
}
.case_problem {
  font-size: 15px;
  color: #666;
  line-height: 21px;
  display: flex;
  flex-wrap: nowrap;
  margin-bottom: 16px;
  .problem_label {
    white-space: nowrap;
  }
  .problem_value {
    word-break: break-all;
  }
}
.details_header {
  position: relative;
  padding-top: 8px;
  font-size: 18px;
  color: #091715;
  font-weight: 600;
  line-height: 25px;
  margin-bottom: 18px;
  i {
    display: block;
    position: absolute;
    left: 0;
    bottom: -2px;
    width: 72px;
    height: 10px;
    border-radius: 10px;
    background: linear-gradient(90deg, #0095FF 0%, rgba(255,255,255,0) 100%);
  }
}
.details_wrap {
  display: flex;
  margin-bottom: 16px;
  .details_item_horizontal {
    flex: 1;
    display: flex;
    .item_label {
      font-size: 15px;
      color: #091715;
      font-weight: 500;
      line-height: 24px;
      margin-right: 17px;
      white-space: nowrap;
    }
    .item_value {
      font-size: 15px;
      color: #000;
      line-height: 24px;
      word-break: break-all;
      white-space: pre-wrap;
    }
  }
}
.details_item {
  margin-bottom: 16px;
  .item_label {
    font-size: 15px;
    color: #091715;
    font-weight: 500;
    line-height: 24px;
    margin-bottom: 8px;
    white-space: nowrap;
  }
  .item_value {
    font-size: 15px;
    color: #000;
    line-height: 21px;
    word-break: break-all;
    white-space: pre-wrap;
  }
}

.details_item_img {
  display: flex;
  flex-wrap: wrap;
  margin-right: -12px;
  max-width: 400px;
  .img {
    width: 108px;
    height: 110px;
    margin-right: 8px;
    margin-bottom: 8px;
    border-radius: 6px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    &:nth-child(3n) {
      margin-right: 0;
    }
  }
}
