// 开启/关闭摄像头
import React from 'react';
import classNames from 'classnames';
import styles from './index.less';  // 引入自定义样式

const CameraToggle = ({ isLive, isJoined, isPublished, currentUserType, handUpStatusType, resetTimer, localStreamConfig, handleChangeByLocalStreamConfig }) => {
  const handleToggleClick = (e) => {
    // 阻止事件冒泡
    e.stopPropagation();
    resetTimer();
    handleChangeByLocalStreamConfig('video', null);
  };

  return (
      <div onClick={handleToggleClick} className={styles.HorizontalLiveRoom_Btn_Warp}>
        <i
          className={classNames({
            [styles.SpatialDetail_Camera_btn]: true,
            [styles.SpatialDetail_Camera_btn_off]: localStreamConfig && !!localStreamConfig.mutedVideo,
          })}
        />
        <div className={styles.text}>
          {localStreamConfig && !!localStreamConfig.mutedVideo ? '开启摄像头' : '关闭摄像头'}
        </div>
      </div>
  );
};

export default CameraToggle;
