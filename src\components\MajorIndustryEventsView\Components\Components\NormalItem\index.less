.normal_item {
  margin: 0 12px 12px;
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #FFFFFF;

  // min-height: 203px;
  display: flex;
  flex-direction: column;

  .header {
    display: flex;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;
    background: #F9FCFF;
    border-radius: 4px 4px 0px 0px;
    padding: 0 12px;

    .header_box {
      display: flex;
      align-items: center;

      img {
        margin-right: 6px;
      }

      span {
        font-weight: 500;
        font-size: 14px;
        color: #2C5678;
      }
    }

    .header_redtip {
      font-size: 12px;
      color: #FF4740;
      line-height: 10px;

      .redBg {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 16px;
        height: 16px;
        border-radius: 2px;
        background: #FF4740;
        color: #ffffff;
        font-size: 12px;
        margin: 0 2px;
      }
    }

    .header_bluetip {
      font-size: 12px;
      color: #009DFF;
      line-height: 10px;

      .blueBg {
        display: flex;
        justify-content: center;
        min-width: 16px;
        height: 16px;
        line-height: 16px;
        border-radius: 2px;
        background: #009DFF;
        color: #ffffff;
        font-size: 12px;
        margin: 0 2px;
      }
    }
  }

  .content {
    padding: 12px 12px 12px;

    .tags {
      margin-bottom: 8px;

      .tagSpan {
        border-radius: 2px;
        display: inline-block;
        box-sizing: border-box;
        line-height: 14px;
        font-size: 12px;
        padding: 2px 4px;
        margin-right: 4px;
      }
    }

    .title {
      font-weight: 600;
      font-size: 16px;
      color: #333333;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .detail {
      font-weight: 400;
      font-size: 14px;
      color: #59637F;
      max-height: 40px;
      // line-height: 20px;
      margin-top: 4px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .footer {
    flex: 1;
    padding: 0 12px 12px;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    .footer_box {
      display: flex;
      align-items: center;
    }

    .footer_box {
      display: flex;
      align-items: center;
    }

    .footer_btn {
      padding: 6px 20px;
      border-radius: 20px;
      font-size: 13px;
      margin-left: 12px;
    }
  }
}

.normal_mobile_item :last-of-type {
  margin-bottom: 0;
}


.normal_pc_item {
  width: 580px;
  margin: 0 0 16px;
  box-shadow: 0px 2px 10px 0px #E8EEF2;
  .footer_btn {
    cursor: pointer;
  }
}