/**
 * @Description: 首页搜索结果页
 */
import React, { useState, useEffect, useRef } from 'react'
import { history, connect } from 'umi'
import classNames from 'classnames'
import { cloneDeep } from 'lodash'
import {gdpFormat, getIsFirstPageInApp, getOperatingEnv, processNames, randomColor} from '@/utils/utils'
import { Spin, message } from 'antd'
import { Toast, Mask, InfiniteScroll } from 'antd-mobile'
import styles from './MobileIndex.less'

import FilterIcon from '@/assets/Case/filter_icon.png'
import RightArrow2Icon from '@/assets/GlobalImg/right_arrow_2.png'
import Space1Icon from '@/assets/GlobalImg/space1.png'
import Space2Icon from '@/assets/GlobalImg/space2.png'
import Space3Icon from '@/assets/GlobalImg/space3.png'
import CrownIcon from '@/assets/GlobalImg/crown.png'
import NoDataImage from '@/assets/GlobalImg/no_data.png'
import KingdomView from '@/components/KingdomView'
import CaseList from '@/components/CaseList'
import ExpertList from '@/components/ExpertList'

import SearchInput from '@/components/SearchInput'                             // 搜索框组件
import FilterModal from '@/pages/Home/SearchResult/Components/FilterModal'   // 筛选弹窗
import MeetingCard from '@/components/MeetingCardInfo';            // 会议卡片组件
import ArticleCard from "@/components/ArticleCard";
import ExternalLinkCard from "@/components/ExternalLinkCard";
import PostCard from "@/components/PostCard";
import ForwardCard from "@/components/ForwardCard";
import SpaceCardBySquare from "@/components/SpaceCardBySquare"

// tab签数据
const tabsData = [
  { id: 0, name: '全部', fieldName: '' },
  { id: 1, name: '直播', fieldName: 'starSpaceCount' },
  { id: 2, name: '会议', fieldName: 'meetingSpaceCount' },
  { id: 3, name: '王国', fieldName: 'starKingdomCount' },
  { id: 6, name: '帖子', fieldName: 'postsCount' },
  { id: 7, name: '病例', fieldName: 'excellentCaseCount' },
  { id: 8, name: '用户', fieldName: 'baseUserCount' },
]

// 帖子的子tab
const childTabsData = [
  { id: 6, name: '帖子', fieldName: 'postsCount' },
  { id: 4, name: '文章', fieldName: 'articleCount' },
  { id: 5, name: '外链', fieldName: 'outerChainCount' },
]

// 空间筛选数据
const spaceFilterData = [
  { code: null, name: '全部' },
  { code: 1, name: '直播中' },
  { code: 2, name: '预约中' },
  // { code: 3, name: '弹幕轰炸中' },
]

// 病例筛选条件字典
let depSubjectCase = []                // 学科
let difficultLevel = []                // 难度等级
let achievement = []                   // 病例成就

// 用户筛选条件字典
let depSubjectUser = []                // 科室
let abilityLevel = []                  // 能力等级
let postTitle = []                     // 职称
let city = []                          // 城市

const Index: React.FC = (props: any) => {
  const { dispatch, loading, activity } = props
  const { query, pathname,search } = history.location
  const initialState = {
    starSpace: [],                                         // 空间集合
    meetingSpace:[],                                       // 会议集合
    starKingdom: [],                                       // 王国集合
    excellentCase: [],                                     // 病例集合
    baseUser: [],                                          // 用户集合
    articleList:[],                                         // 文章集合
    outerChainList:[],                                      // 外链集合
    postsList:[],                                           // 帖子集合

    starSpaceCount: 0,                                     // 空间数量
    meetingSpaceCount:0,                                    // 会议数量
    starKingdomCount: 0,                                   // 王国数量
    excellentCaseCount: 0,                                 // 病例数量
    baseUserCount: 0,                                      // 用户数量
    articleCount: 0,                                       // 文章数量
    outerChainCount: 0,                                     // 外链数量
    postsCount: 0,                                          // 帖子数量
    postsSumCount: 0,                                      // 帖子总数（帖子+外链数量+文章数量）

    starSpaceTotal: 0,                                     // 空间总条数
    meetingSpaceTotal: 0,                                    // 会议总条数
    starKingdomTotal: 0,                                   // 王国总条数
    excellentCaseTotal: 0,                                 // 病例总条数
    baseUserTotal: 0,                                      // 用户总条数
    articleListTotal: 0,                                   // 文章总条数
    outerChainListTotal: 0,                                // 外链总条数
    postsListTotal: 0,                                     // 帖子总条数
  }

  // 筛选条件
  const initialFilterState = {
    pageNum: 1,                                            // 页码

    spaceStatusList: [],                                   // 空间状态：1直播中、2预约中、3弹幕轰炸中
    meetingStatusList: [],                                  // 会议状态：1直播中、2预约中、3弹幕轰炸中
    achievementList: [],                                   // 病例成就
    difficultLevelDictList: [],                            // 难度等级
    startDate: null,                                       // 开始日期
    endDate: null,                                         // 结束日期
    depSubjectDictList: [],                                // 学科/科室

    cityList: [],                                          // 城市
    abilityLevelDictList: [],                              // 能力等级
    postTitleDictList: [],                                 // 职级
  }
  const initialModalState = {
    maskVisible: false,                                    // 筛选弹窗遮罩
    filterListData: [],                                    // 筛选弹窗字典项数据
  }
  const [state, setState] = useState(initialState)
  const [filterState, setFilterState] = useState(initialFilterState)
  const [modalState, setModalState] = useState(initialModalState)
  const [searchKeyState, setSearchKeyState] = useState(query.searchKey || activity.searchKey || '')  // 带过来的搜索关键词
  const [checkedTabState, setCheckedTabState] = useState(0)                    // 当前选中tab签
  const [checkedChildTabState, setCheckedChildTabState] = useState(6)          // 当前选中帖子的子tab签
  const ref = useRef(null)

  useEffect(() => {
    if (getIsFirstPageInApp()&&(getOperatingEnv() == 5)) {
      history.replace(`${pathname}?AIhelper=Friday`)
    }else{
      history.replace(pathname)
    }
    getExcellentCaseDict()
    getFilterDict()

    const {
      checkedTab, checkedChildTab,
      spaceStatusList,
      achievementList, difficultLevelDictList, startDate, endDate, depSubjectDictList,
      cityList, abilityLevelDictList, postTitleDictList,
    } = activity

    homeIndexSearch({
      checkedTab,
      checkedChildTab,
      spaceStatusList,
      achievementList,
      difficultLevelDictList,
      startDate,
      endDate,
      depSubjectDictList,
      cityList,
      abilityLevelDictList,
      postTitleDictList,
    })

    setCheckedTabState(checkedTab)
    setCheckedChildTabState(checkedChildTab)
  }, [])

  // 获取病例筛选字典
  const getExcellentCaseDict = () => {
    dispatch({
      type: 'cases/getExcellentCaseDict',
      payload: {}
    }).then(res => {
      const { code, content } = res
      if (code == 200) {
        depSubjectCase = content && content.depSubject || []
        depSubjectCase.unshift({ code: null, name: '全部'})

        difficultLevel = content && content.difficultLevel || []
        difficultLevel.unshift({ code: null, name: '全部'})

        achievement = content && content.achievement || []
        achievement.unshift({ code: null, name: '全部'})
      }
    }).catch(err => {})
  }

  // 获取专家筛选字典
  const getFilterDict = () => {
    dispatch({
      type: 'expertAdvice/getFilterDict',
      payload: {}
    }).then(res => {
      const { code, content } = res
      if (code == 200) {
        depSubjectUser = content && content.depSubject || []
        depSubjectUser.unshift({ code: null, name: '全部'})

        abilityLevel = content && content.abilityLevel || []
        abilityLevel.unshift({ code: null, name: '全部'})

        postTitle = content && content.postTitle || []
        postTitle.unshift({ code: null, name: '全部'})

        city = content && content.city && content.city.map(item => ({
          code: item,
          name: item,
        })) || []
        city.unshift({ code: null, name: '全国'})
      }
    }).catch(err => {})
  }

  // 根据用户ID获取搜索关键字
  const homeIndexSearch = async (
    {
      searchKey = searchKeyState, checkedTab = checkedTabState, checkedChildTab = checkedChildTabState, pageNum = 1, pageSize = 30,
      spaceStatusList = [], meetingStatusList= [],
      achievementList = [], depSubjectDictList = [], difficultLevelDictList = [], startDate = null, endDate = null,
      cityList = [], abilityLevelDictList = [], postTitleDictList = []
    } = {}
  ) => {

    // 全部null 直播空间1 会议空间2 王国3  文章4  外链 5 帖子6  病例7 用户8
    await dispatch({
      type: 'activity/homeIndexSearch',
      payload: {
        searchKey,                                         // 搜索关键词
        type: checkedTab == 0 ? null : checkedTab == 6 ? checkedChildTab : checkedTab,              // 默认不传
        pageSize: checkedTab == 0 ? 3 : pageSize,          // 每页条数
        pageNum,                                           // 页码
        spaceStatusList:spaceStatusList.length!=0?spaceStatusList:meetingStatusList.length!=0?meetingStatusList:[],                                   // 直播&会议状态：1直播中、2预约中、3弹幕轰炸中
        achievementList,                                   // 病例成就
        difficultLevelDictList,                            // 难度等级
        startDate,                                         // 开始日期
        endDate,                                           // 结束日期
        depSubjectDictList,                                // 学科/科室

        cityList,                                          // 城市
        abilityLevelDictList,                              // 能力等级
        postTitleDictList,                                 // 职级
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200 && content) {
        const starSpace = content.starSpace && content.starSpace.resultList || []
        const meetingSpace = content.meetingSpace && content.meetingSpace.resultList || []
        const starKingdom = content.starKingdom && content.starKingdom.resultList || []
        const excellentCase = content.excellentCase && content.excellentCase.resultList || []
        const baseUser = content.baseUser && content.baseUser.resultList || []
        const articleList = content.articleList && content.articleList.resultList || []       // 文章集合
        const outerChainList = content.outerChainList && content.outerChainList.resultList || [] // 外链集合
        const postsList = content.postsList && content.postsList.resultList || []           // 帖子集合

        setState({
          ...state,

          starSpace: pageNum == 1 ? starSpace : state.starSpace.concat(starSpace),                   // 直播集合
          meetingSpace: pageNum == 1 ? meetingSpace : state.meetingSpace.concat(meetingSpace),       // 会议集合
          starKingdom: pageNum == 1 ? starKingdom : state.starKingdom.concat(starKingdom),           // 王国集合
          excellentCase: pageNum == 1 ? excellentCase : state.excellentCase.concat(excellentCase),   // 病例集合
          baseUser: pageNum == 1 ? baseUser : state.baseUser.concat(baseUser),                       // 用户集合
          articleList: pageNum == 1 ? articleList : state.articleList.concat(articleList),              // 文章集合
          outerChainList: pageNum == 1 ? outerChainList : state.outerChainList.concat(outerChainList),  // 外链集合
          postsList: pageNum == 1 ? postsList : state.postsList.concat(postsList),                      // 帖子集合

          starSpaceCount: content.starSpaceCount || 0,                                         // 直播数量
          meetingSpaceCount: content.meetingSpaceCount || 0,                                     // 会议数量
          starKingdomCount: content.starKingdomCount || 0,                                     // 王国数量
          excellentCaseCount: content.excellentCaseCount || 0,                                 // 病例数量
          baseUserCount: content.baseUserCount || 0,                                           // 用户数量
          articleCount: content.articleCount || 0,                                            // 文章数量
          outerChainCount: content.outerChainCount || 0,                                       // 外链数量
          postsCount: content.postsCount || 0,                                                 // 帖子数量
          postsSumCount: content.postsSumCount || 0,                                           // 帖子总数（帖子+外链数量+文章数量）

          starSpaceTotal: checkedTab != 0 && content.starSpace && content.starSpace.total || 0,              // 直播总条数
          meetingSpaceTotal: checkedTab != 0 && content.meetingSpace && content.meetingSpace.total || 0,  // 会议总条数
          starKingdomTotal: checkedTab != 0 && content.starKingdom && content.starKingdom.total || 0,        // 王国总条数
          excellentCaseTotal: checkedTab != 0 && content.excellentCase && content.excellentCase.total || 0,  // 病例总条数
          baseUserTotal: checkedTab != 0 && content.baseUser && content.baseUser.total || 0,                 // 用户总条数
          articleListTotal: checkedTab != 0 && content.articleList && content.articleList.total || 0,        // 文章总条数
          outerChainListTotal: checkedTab != 0 && content.outerChainList && content.outerChainList.total || 0,        // 外链总条数
          postsListTotal: checkedTab != 0 && content.postsList && content.postsList.total || 0,        // 帖子总条数
        })

        // 加入王国成功回调刷新列表的时候不保存pageNum
        if (pageSize != 30) {
          return
        }

        setFilterState({
          ...filterState,
          pageNum,                                                     // 页码

          spaceStatusList: cloneDeep(spaceStatusList),                 // 空间状态：1直播中、2预约中、3弹幕轰炸中
          meetingStatusList: cloneDeep(meetingStatusList),                 // 会议状态：1直播中、2预约中、3弹幕轰炸中
          achievementList: cloneDeep(achievementList),                 // 病例成就
          difficultLevelDictList: cloneDeep(difficultLevelDictList),   // 难度等级
          startDate,                                                   // 开始日期
          endDate,                                                     // 结束日期
          depSubjectDictList: cloneDeep(depSubjectDictList),           // 学科/科室

          cityList: cloneDeep(cityList),                               // 城市
          abilityLevelDictList: cloneDeep(abilityLevelDictList),       // 能力等级
          postTitleDictList: cloneDeep(postTitleDictList),             // 职级
        })

        dispatch({
          type: 'activity/save',
          payload: {
            checkedTab,
            checkedChildTab,
            searchKey,                                                   // 搜索关键词

            spaceStatusList: cloneDeep(spaceStatusList),                 // 空间状态：1直播中、2预约中、3弹幕轰炸中

            achievementList: cloneDeep(achievementList),                 // 病例成就
            difficultLevelDictList: cloneDeep(difficultLevelDictList),   // 难度等级
            startDate,                                                   // 开始日期
            endDate,                                                     // 结束日期
            depSubjectDictList: cloneDeep(depSubjectDictList),           // 学科/科室

            cityList: cloneDeep(cityList),                               // 城市
            abilityLevelDictList: cloneDeep(abilityLevelDictList),       // 能力等级
            postTitleDictList: cloneDeep(postTitleDictList),             // 职级
          }
        })
      } else {
        Toast.show(msg || '数据加载失败')
      }
    })
  }

  // 切换页签
  const checkedTabOnChange = (checkedTab) => {
    if (checkedTab == checkedTabState) {
      return
    }
    setCheckedTabState(checkedTab)
    setCheckedChildTabState(6)
    // 点击的非帖子tab
    // if (checkedTab != 5) {
    //   setCheckedChildTabState(5)
    // }
    maskHide()

    if (ref && ref.current) {
      ref.current.scrollTop = 0
    }
    // H5首页检索
    homeIndexSearch({
      checkedTab: checkedTab,                                        // 当前选中tab签
      checkedChildTab: checkedChildTabState,        // 当前选中帖子的子tab签
    })
  }

  // 切换帖子子页签
  const onChangeChildTabs = (checkedChildTab) => {
    if (checkedChildTab == checkedChildTabState) {
      return
    }
    setCheckedChildTabState(checkedChildTab)
    if (checkedTabState == 0) {

    } else {
      maskHide()

      if (ref && ref.current) {
        ref.current.scrollTop = 0
      }
      // H5首页检索
      homeIndexSearch({
        checkedChildTab: checkedChildTab,                         // 当前选中帖子的子tab签
      })
    }
  }

  // 输入事件
  const inputOnChange = (value) => {
    setSearchKeyState(value)
  }

  // 搜索回调
  const onPressEnter = () => {
    if (ref && ref.current) {
      ref.current.scrollTop = 0
    }
    // H5首页检索
    homeIndexSearch({
      searchKey: searchKeyState,                           // 搜索关键词
    })
  }

  // 点击取消按钮事件
  const cancelBtnOnClick = () => {
    maskHide()

    setSearchKeyState('')
    if (ref && ref.current) {
      ref.current.scrollTop = 0
    }
    // H5首页检索
    homeIndexSearch({
      searchKey: '',                                       // 搜索关键词
    })
  }

  // 切换筛选弹窗显隐
  const toggleMask = () => {
    if (modalState.maskVisible) {
      maskHide()
    } else {
      maskShow()
    }
  }

  // 打开筛选弹窗
  const maskShow = () => {
    let filterListData = []
    if (checkedTabState == 1) {  // 直播筛选
      filterListData = [
        {
          code: 'spaceStatusList',
          name: '直播状态',
          children: spaceFilterData,
        }
      ]
    }else if (checkedTabState == 2) {  // 会议筛选
      filterListData = [
        {
          code: 'meetingStatusList',
          name: '会议状态',
          children: spaceFilterData,
        }
      ]
    } else if (checkedTabState == 7) { // 病例筛选
      filterListData = [
        {
          code: 'depSubjectDictList',
          name: '学科',
          children: depSubjectCase,
        },
        {
          code: 'difficultLevelDictList',
          name: '难度等级',
          children: difficultLevel,
        },
        {
          code: 'date',
          name: '日期',
          children: [],
        },
        {
          code: 'achievementList',
          name: '病例成就',
          children: achievement,
        },
      ]
    } else if (checkedTabState == 8) { // 用户筛选
      filterListData = [
        {
          code: 'depSubjectDictList',
          name: '科室',
          children: depSubjectUser,
        },
        {
          code: 'cityList',
          name: '城市',
          children: city,
        },
        {
          code: 'abilityLevelDictList',
          name: '能力等级',
          children: abilityLevel,
        },
        {
          code: 'postTitleDictList',
          name: '职级',
          children: postTitle,
        },
      ]
    }
    setModalState({
      ...modalState,
      maskVisible: true,                                   // 筛选弹窗
      filterListData,                                      // 筛选字典项数据
    })
  }

  // 关闭筛选弹窗
  const maskHide = () => {
    setModalState({
      ...modalState,
      maskVisible: false,                                  // 筛选弹窗
      filterListData: [],                                  // 筛选字典项数据
    })
  }

  // 点击空间跳转
  const goToUrl = (item) => {
    if (item.starSpaceType == 2) {
      history.push(`/PlanetChatRoom/Meet/${item.id}`)
    } else {
      history.push(`/PlanetChatRoom/Live/${item.id}`)
    }
  }

  // 筛选点击取消
  const filterOnCancel = () => {
    maskHide()
    if (ref && ref.current) {
      ref.current.scrollTop = 0
    }
    // H5首页检索
    homeIndexSearch()
  }

  // 点击确定
  const filterOnOk = (filterParams) => {
    maskHide()
    console.log('filterParams:', filterParams)
    if (ref && ref.current) {
      ref.current.scrollTop = 0
    }
    // H5首页检索
    homeIndexSearch({
      ...filterParams,
    })
  }

  // 加入王国成功回调
  const refreshHomeIndexSearch = () => {
    homeIndexSearch({
      pageNum: 1,
      pageSize: state.starKingdom.length,
    })
  }

  // 关注用户
  const isH5Focus = ({expertsUserId, isFocus}) => {
    dispatch({
      type: 'expertAdvice/followAndCheck',
      payload: {
        expertsUserId,                                     // 关注用户ID
        isFocus,                                           // 0取消关注 1关注
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200 && content) {
        if (isFocus == 1) {
          Toast.show('已关注')
        } else {
          Toast.show('已取消关注')
        }
        homeIndexSearch({
          ...filterState,
          pageNum: 1,
          pageSize: state.baseUser.length,
        })
      } else {
        if (isFocus == 1) {
          Toast.show(msg || '关注失败')
        } else {
          Toast.show(msg || '取消关注失败')
        }
      }
    }).catch(err => {})
  }

  // 返回
  const goBack = () => {
    if (history.length > 1) {
      history.goBack()
    } else {
      history.replace('/')
    }
  }

  // 加载更多
  const loadMore = async () => {
    // H5首页检索
    await homeIndexSearch({
      pageNum: filterState.pageNum + 1,                                // 页码

      spaceStatusList: filterState.spaceStatusList,                    // 直播状态：1直播中、2预约中、3弹幕轰炸中
      meetingStatusList: filterState.meetingStatusList,                // 会议状态：1直播中、2预约中、3弹幕轰炸中

      achievementList: filterState.achievementList,                    // 病例成就
      depSubjectDictList: filterState.depSubjectDictList,              // 学科
      difficultLevelDictList: filterState.difficultLevelDictList,      // 难度等级
      startDate: filterState.startDate,                                // 开始日期
      endDate: filterState.endDate,                                    // 结束日期

      cityList: filterState.cityList,                                  // 城市
      abilityLevelDictList: filterState.abilityLevelDictList,          // 能力等级
      postTitleDictList: filterState.postTitleDictList,                // 职级
    })
  }

  // loading
  const loadingHomeIndexSearch = !!loading.effects['activity/homeIndexSearch']

  return (
    <>
      {/* 筛选弹窗 */}
      <Mask style={{'--z-index': 988}} visible={modalState.maskVisible} onMaskClick={maskHide}/>
      <FilterModal
        visible={modalState.maskVisible}
        filterListData={modalState.filterListData}
        {...filterState}
        onOk={filterOnOk}
        onCancel={filterOnCancel}
      />
      {/* 内容 */}
      <Spin spinning={loadingHomeIndexSearch}>
        <div className={styles.container} ref={ref}>
          <div className={styles.header_box}>
            {/* 导航搜索框 */}
            <div className={styles.header_box_content}>
              {/* 返回按钮 */}
              <i className={classNames(styles.nav_bar_icon)} onClick={goBack}></i>
              {/* 搜索框 */}
              <SearchInput
                isHistoryStatus={2}
                defaultInputValue={searchKeyState}
                inputPlaceholder="搜索直播、会议、王国、帖子、病例、用户"
                inputChangeFn={inputOnChange}
                onPressEnterFun={onPressEnter}
                cancelBtnFn={cancelBtnOnClick}
              />
            </div>
            {/* 页签 */}
            <div className={styles.tabs_box}>
              <div className={styles.tabs_data_box}>
                {
                  tabsData.map((item, index) => {
                    return <div key={item.id} className={classNames({
                      [styles.checked]: item.id == checkedTabState,
                    })} onClick={() => checkedTabOnChange(item.id)}>
                      {item.name}{item.id == 6 ? (state.postsSumCount) : state[item.fieldName]}
                    </div>
                  })
                }
              </div>
              {
                (checkedTabState == 1 || checkedTabState == 2 || checkedTabState == 7  ||checkedTabState == 8) &&
                <div className={classNames(styles.tabs_btn_box, {
                  [styles.checked]: (
                    filterState.spaceStatusList.length > 0 ||
                    filterState.meetingStatusList.length > 0 ||
                    filterState.achievementList.length > 0 ||
                    filterState.depSubjectDictList.length > 0 ||
                    filterState.difficultLevelDictList.length > 0 ||
                    filterState.startDate ||
                    filterState.endDate ||
                    filterState.cityList.length > 0 ||
                    filterState.abilityLevelDictList.length > 0 ||
                    filterState.postTitleDictList.length > 0
                  ),
                })} onClick={toggleMask}>
                  <span>筛选</span>
                  <img src={FilterIcon} width={12} height={12} alt=""/>
                </div>
              }
            </div>
          </div>

          {/* 灰条 */}
          <div className={styles.space}></div>

          {/* 直播list */}
          {
            (checkedTabState == 0 || checkedTabState == 1) &&
            <div className={styles.result_box}>
              {
                checkedTabState == 0 &&
                <div className={styles.title_box}>
                  <div className={styles.title}>直播({state.starSpaceCount})</div>
                  {
                    state.starSpaceCount > 0 &&
                    <div className={styles.title_btn_box} onClick={() => checkedTabOnChange(1)}>
                      <span>查看更多</span>
                      <img src={RightArrow2Icon} width={16} height={16} alt=""/>
                    </div>
                  }

                </div>
              }
              {
                state.starSpace.length > 0 ?
                  state.starSpace.map(item => {
                    return (
                      <div key={item.id} className={styles.item_box} onClick={() => goToUrl(item)}>
                        <div className={styles.left} style={item.spaceCoverUrlShow ? {backgroundImage: `url(${item.spaceCoverUrlShow})`} : {}}>
                          {/* 封面中的标题 */}
                          {
                            item.isTemplateCover==1 &&
                            <div className={styles.title_in_cover_image}>{item.originalName}</div>
                          }
                          {
                            item.status && item.status == 3 ? '' :
                            <div className={styles.left_status_box}>
                              <img
                                src={item.status == 1 ? Space1Icon : item.status == 2 ? Space2Icon : null}
                                width={10} height={10} alt=""/>
                              <span>{item.status == 1 ? '进行中' : item.status == 2 ? '预约中' : null}</span>
                            </div>
                          }
                        </div>
                        <div className={styles.right}>
                          <div className={styles.space_name} dangerouslySetInnerHTML={{__html: item.name}}></div>
                          {item.intro ? <div className={styles.space_intro}>{item.intro}</div> : ''}
                          <div className={styles.space_info_style}>
                            <div className={styles.user_box}>
                              <div className={styles.avatar}
                                  style={item.hostImgUrlShow ? {backgroundImage: `url(${item.hostImgUrlShow})`} : {background: randomColor(item.wxUserId)}}>
                                {item.isKing ? <img src={CrownIcon} width={11} height={11} alt=""/> : ''}
                                {!item.hostImgUrlShow && processNames(item.originalHostName)}
                              </div>
                              <div className={styles.name} dangerouslySetInnerHTML={{__html: item.hostName}}></div>
                            </div>
                            <div className={styles.gdp}>{gdpFormat(item.gdp)}GDP | {gdpFormat(item.pv)}观看</div>
                          </div>
                        </div>
                      </div>
                    )
                  })
                  :
                  <div className={styles.item_empty_box}>
                    <img src={NoDataImage} width={150} height={113} alt=""/>
                    <div className={styles.empty_title}>暂无该搜索结果</div>
                    <div className={styles.empty_msg}>请试试其他搜索关键词</div>
                  </div>
              }
            </div>
          }
          {/* 会议list */}
          {
            (checkedTabState == 0 || checkedTabState == 2) &&
            <div className={styles.result_box}>
              {
                checkedTabState == 0 &&
                <div className={styles.title_box}>
                  <div className={styles.title}>会议({state.meetingSpaceCount})</div>
                  {
                    state.meetingSpaceCount > 0 &&
                    <div className={styles.title_btn_box} onClick={() => checkedTabOnChange(2)}>
                      <span>查看更多</span>
                      <img src={RightArrow2Icon} width={16} height={16} alt=""/>
                    </div>
                  }

                </div>
              }
              {
                state.meetingSpace.length > 0 ?
                  state.meetingSpace.map(item => {
                    return (
                      <div key={item?.id} className={styles.meeting_item_wrap} onClick={() => goToUrl(item)}>
                        <MeetingCard key={item?.id} item={item} style={{minHeight: 'auto', paddingBottom: 12}} />
                      </div>
                    )
                  })
                  :
                  <div className={styles.item_empty_box}>
                    <img src={NoDataImage} width={150} height={113} alt=""/>
                    <div className={styles.empty_title}>暂无该搜索结果</div>
                    <div className={styles.empty_msg}>请试试其他搜索关键词</div>
                  </div>
              }
            </div>
          }

          {/* 王国list */}
          {
            (checkedTabState == 0 || checkedTabState == 3) &&
            <div className={styles.result_box}>
              {
                checkedTabState == 0 &&
                <div className={styles.title_box}>
                  <div className={styles.title}>王国({state.starKingdomCount})</div>
                  {
                    state.starKingdomCount > 0 &&
                    <div className={styles.title_btn_box} onClick={() => checkedTabOnChange(3)}>
                      <span>查看更多</span>
                      <img src={RightArrow2Icon} width={16} height={16} alt=""/>
                    </div>
                  }

                </div>
              }
              {
                state.starKingdom.length > 0 ?
                  <KingdomView
                    componentData={{dataList: state.starKingdom}}
                    style={{padding: '0'}}
                    getPageInfo={refreshHomeIndexSearch}
                  />
                  :
                  <div className={styles.item_empty_box}>
                    <img src={NoDataImage} width={150} height={113} alt=""/>
                    <div className={styles.empty_title}>暂无该搜索结果</div>
                    <div className={styles.empty_msg}>请试试其他搜索关键词</div>
                  </div>
              }
            </div>
          }

          {/* 帖子3合1 list */}
          {
            (checkedTabState == 0 || checkedTabState == 6) &&
            <div className={styles.result_box}>
              {
                checkedTabState == 0 &&
                <div className={styles.title_box}>
                  <div className={styles.title}>帖子({state.postsSumCount})</div>
                  {
                    state.postsCount > 0 &&
                    <div className={styles.title_btn_box} onClick={() => checkedTabOnChange(6)}>
                      <span>查看更多</span>
                      <img src={RightArrow2Icon} width={16} height={16} alt=""/>
                    </div>
                  }
                </div>
              }
              {/* 子级tab */}
              <div className={styles.child_title_box}>
                {
                  childTabsData.map(item => {
                    return <span key={item.id} className={classNames({
                      [styles.checked]: checkedChildTabState == item.id,
                    })} onClick={() => onChangeChildTabs(item.id)}>{item.name}</span>
                  })
                }
              </div>

              {
                checkedChildTabState == 6 && state.postsList.length > 0 ?
                  <div className={styles.result_wrap}>
                    {
                      state.postsList.map(item => {
                        return (
                          <PostCard
                            key={item.id}
                            pageType='5'
                            item={item}
                            isShowMoreOperate={false} // 是否展示点点点更多操作
                          />
                        )
                      })
                    }
                  </div>
                  : checkedChildTabState == 4 && state.articleList.length > 0 ?
                  <div className={styles.result_wrap}>
                    {
                      state.articleList.map(item => {
                        return (
                          <ArticleCard
                            key={item.id}
                            item={item}
                          />
                        )
                      })
                    }
                  </div>
                  : checkedChildTabState == 5 && state.outerChainList.length > 0 ?
                    <div className={styles.result_wrap}>
                      {
                        state.outerChainList.map(item => {
                          return (
                            <ExternalLinkCard
                              key={item.id}
                              item={item}
                              isShowMoreOperate={false} // 是否展示点点点更多操作
                            />
                          )
                        })
                      }
                    </div>
                    :
                    <div className={styles.item_empty_box}>
                      <img src={NoDataImage} width={150} height={113} alt=""/>
                      <div className={styles.empty_title}>暂无该搜索结果</div>
                      <div className={styles.empty_msg}>请试试其他搜索关键词</div>
                    </div>
              }
            </div>
          }

          {/* 病例list */}
          {
            (checkedTabState == 0 || checkedTabState == 7) &&
            <div className={styles.result_box}>
              {
                checkedTabState == 0 &&
                <div className={styles.title_box}>
                  <div className={styles.title}>病例({state.excellentCaseCount})</div>
                  {
                    state.excellentCaseCount > 0 &&
                    <div className={styles.title_btn_box} onClick={() => checkedTabOnChange(7)}>
                      <span>查看更多</span>
                      <img src={RightArrow2Icon} width={16} height={16} alt=""/>
                    </div>
                  }

                </div>
              }


              {
                state.excellentCase.length > 0 ?
                  <CaseList isShowImage={true} componentData={{dataList: state.excellentCase}} style={{padding: '0'}}/>
                  :
                  <div className={styles.item_empty_box}>
                    <img src={NoDataImage} width={150} height={113} alt=""/>
                    <div className={styles.empty_title}>暂无该搜索结果</div>
                    <div className={styles.empty_msg}>请试试其他搜索关键词</div>
                  </div>
              }
            </div>
          }


          {/* 用户 */}
          {
            (checkedTabState == 0 || checkedTabState == 8) &&
            <div className={styles.result_box}>
              {
                checkedTabState == 0 &&
                <div className={styles.title_box}>
                  <div className={styles.title}>用户({state.baseUserCount})</div>
                  {
                    state.baseUserCount > 0 &&
                    <div className={styles.title_btn_box} onClick={() => checkedTabOnChange(8)}>
                      <span>查看更多</span>
                      <img src={RightArrow2Icon} width={16} height={16} alt=""/>
                    </div>
                  }

                </div>
              }

              {
                state.baseUser.length > 0 ?
                  <ExpertList
                    dataSource={state.baseUser}
                    isHideContent={true}
                    isShowFollow={true}
                    style={{padding: '0', marginRight: '-12px'}}
                    onFollowBtnClick={isH5Focus}
                  />
                  :
                  <div className={styles.item_empty_box}>
                    <img src={NoDataImage} width={150} height={113} alt=""/>
                    <div className={styles.empty_title}>暂无该搜索结果</div>
                    <div className={styles.empty_msg}>请试试其他搜索关键词</div>
                  </div>
              }

            </div>
          }



          {/* 滚动加载 */}
          {
            (
              (checkedTabState == 1 && state.starSpaceTotal > 0) ||  // 直播
              (checkedTabState == 2 && state.meetingSpaceTotal > 0) ||  // 会议
              (checkedTabState == 3 && state.starKingdomTotal > 0) || // 王国
              (checkedTabState == 6 && checkedChildTabState == 4 && state.articleListTotal > 0) ||  // 文章
              (checkedTabState == 6 && checkedChildTabState == 5 && state.outerChainListTotal > 0) ||   // 外链
              (checkedTabState == 6 && checkedChildTabState == 6 && state.postsListTotal > 0) ||   // 帖子
              (checkedTabState == 7 && state.excellentCaseTotal > 0) || // 病例
              (checkedTabState == 8 && state.baseUserTotal > 0)       // 用户
            ) &&
            <InfiniteScroll
              threshold={100}
              loadMore={loadMore}
              hasMore={
                (checkedTabState == 1 && state.starSpaceTotal > state.starSpace.length) ||
                (checkedTabState == 2 && state.meetingSpaceTotal > state.meetingSpace.length) ||
                (checkedTabState == 3 && state.starKingdomTotal > state.starKingdom.length) ||
                (checkedTabState == 6 && checkedChildTabState == 4 && state.articleListTotal > state.articleList.length) ||
                (checkedTabState == 6 && checkedChildTabState == 5 && state.outerChainListTotal > state.outerChainList.length) ||
                (checkedTabState == 6 && checkedChildTabState == 6 && state.postsListTotal > state.postsList.length) ||
                (checkedTabState == 7 && state.excellentCaseTotal > state.excellentCase.length) ||
                (checkedTabState == 8 && state.baseUserTotal > state.baseUser.length)
              }
            />
          }

        </div>
      </Spin>


    </>
  )
}

export default connect(({ activity, loading }: any) => ({activity, loading}))(Index)
