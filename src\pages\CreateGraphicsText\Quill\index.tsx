import React from 'react'
import { history } from 'umi'
import classNames from 'classnames'
import PropTypes from 'prop-types'
import ReactQuill, { Quill } from 'react-quill';
import { emojiUrl } from '@/utils/utils'
import { Popover } from 'antd'
import 'react-quill/dist/quill.core.css';
import styles from './index.less'

import Topic from './ToolbarPC/Topic'
const Delta = Quill.import('delta')
const SizeStyle = Quill.import('attributors/style/size')
SizeStyle.whitelist = ['12px', '14px', '16px', '18px', '20px', '24px', '32px']
Quill.register(SizeStyle, true)
import ImageBlot from './Blots/Image'
Quill.register(ImageBlot);
import EmojiBlot from './Blots/Emoji'
Quill.register(EmojiBlot);
import TopicBlot from './Blots/Topic'
Quill.register(TopicBlot);
import VideoBlot from './Blots/Video'
Quill.register(VideoBlot);
import UserBlot from './Blots/User'
Quill.register(UserBlot);
import ProgressBlot from './Blots/Progress'
Quill.register(ProgressBlot);
import ImageProgress from './Blots/ImageProgress'
Quill.register(ImageProgress);

class Index extends React.Component {
  static propTypes = {
    getQuillHistoryStack: PropTypes.func,
    getQuillFormat: PropTypes.func,
    deviceType: PropTypes.string,
    placeholder: PropTypes.string,
    onChangeImageTextContent: PropTypes.func,
  }
  static defaultProps = {
    getQuillHistoryStack: () => {},
    getQuillFormat: () => {},
    deviceType: 'pc',
    placeholder: '请输入正文',
    onChangeImageTextContent: () => {},
  }

  constructor(props) {
    super(props);
    this.state = {
      currentRangeOnBlur: null,
      topicPopVisible: false,
      editorReadonly: false,
    };

    this.reactQuillRef = null;
    this.quill = null
    this.unprivilegedEditor = null

    this.topicPopoverRef = null
  }

  componentDidMount(): void {
    this.quill = this.reactQuillRef.getEditor()
    this.unprivilegedEditor = this.reactQuillRef.makeUnprivilegedEditor(this.quill)
    this.quill.on('selection-change', this.selectionChange)
    this.quill.root.addEventListener('dragstart', this.handleDragStart, false);
    // this.quill.root.addEventListener('dragover', this.handleDragover, false);
    this.quill.root.addEventListener('drop', this.handleDrop, false);
    this.quill.root.addEventListener('click', this.handleClick, false);
    this.quill.addContainer(this.topicPopoverRef)
    this.quill.clipboard.addMatcher(Node.ELEMENT_NODE, function(node, delta) {
      console.log('clipboardclipboardclipboard333',node,node.nodeName, delta)
      if (delta.ops && delta.ops.length > 0) {
        return new Delta({
          ops: delta.ops.filter(item => typeof item.insert == 'string').map(item => ({
            insert: item.insert
          }))
        })
      }
      return new Delta()
    });
  }

  // 拖动
  handleDragStart = (e) => {
    e.preventDefault()
  }

  // 拖动
  // handleDragover = (e) => {
  //   e.preventDefault()
  // }

  // 放下
  handleDrop = (e) => {
    e.preventDefault()
  }

  // 点击事件回调
  handleClick = (e) => {
    const range = this.quill.getSelection()
    if ((e.target && e.target.dataset.type == 'topic') || (e.target.parentNode && e.target.parentNode.dataset && e.target.parentNode.dataset.type == 'topic')) {
      const index = range.length == 0 ? range.index - 1 : range.index
      setTimeout(() => {
        this.quill.setSelection(index, 1)
      }, 0)
    }
  }

  // 光标切换
  selectionChange = (range, oldRange, source) => {
    if (range) {
      this.props.getQuillFormat(this.quill.getFormat())
    }
  }

  // onChange
  onChange = (html, delta, source, quill) => {
    if (!this.quill) {
      return
    }
    this.props.onChangeImageTextContent()
    if (delta.ops.length == 1) {
      if (delta.ops[0].insert && delta.ops[0].insert == '#') {
        this.onInsertPoundKey()
      } else if (delta.ops[0].delete) {
        const range = quill.getSelection()
        if (range) {
          const index = range.index
          const formatFront = this.quill.getFormat(index - 1, 1)
          if (formatFront && formatFront.blotsName == 'topic') {
            this.quill.deleteText(index - 1, 1, Quill.sources.USER)
          }
        }
      } else {
        this.handleTopicOpenChange(false)
      }
    } else if (delta.ops.length == 2) {
      if (delta.ops[1].insert && delta.ops[1].insert == '#') {
        this.onInsertPoundKey()
      } else if (delta.ops[1].delete) {
        const range = quill.getSelection()
        if (range) {
          const index = range.index
          const formatFront = this.quill.getFormat(index - 1, 1)
          if (formatFront && formatFront.blotsName == 'topic') {
            this.quill.deleteText(index - 1, 1, Quill.sources.USER)
          }
        }
      } else {
        this.handleTopicOpenChange(false)
      }
    }
    this.props.getQuillHistoryStack(this.quill.history.stack)
  }

  // 输入井号
  onInsertPoundKey = () => {
    const { deviceType } = this.props
    if (deviceType == 'pc') {
      this.topicPopoverShow()
    } else {
      history.push('/CreateGraphicsText/SelectTopic?selectTopicType=1')
    }
  }

  // 打开话题选择
  topicPopoverShow = () => {
    this.setState({
      topicPopVisible: true,
    }, () => {
      const index = this.quill.getSelection()
      const bounds = this.quill.getBounds(index)
      const x = bounds.left
      const y = bounds.top
      this.topicPopoverRef.style.transform = `translate(${x}px, ${y}px)`
    })
  }

  // 关闭话题
  handleTopicOpenChange = (visible) => {
    this.setState({
      topicPopVisible: visible,
    })
  }

  // 选择话题
  topicItemOnClick = (value, type = 2) => {
    this.topicFn(value, type)
    this.handleTopicOpenChange(false)
  }

  // 撤销
  undoFn = () => {
    this.quill.history.undo()
  }

  // 重做
  redoFn = () => {
    this.quill.history.redo()
  }

  // 字号
  fontSizeFn = (value) => {
    const currentFormat = this.quill.getFormat()
    this.props.getQuillFormat({
      ...currentFormat,
      size: value == '16px' ? '' : value,
    })
    this.quill.format('size', value == '16px' ? '' : value)
  }

  // 修改格式
  toggleFormatCommon = (formatName) => {
    const currentFormat = this.quill.getFormat()
    this.props.getQuillFormat({
      ...currentFormat,
      [formatName]: !currentFormat[formatName],
    })
    if (currentFormat[formatName]) {
      this.quill.format(formatName, false)
    } else {
      this.quill.format(formatName, true)
    }
  }

  // 加粗
  boldFn = () => {
    this.toggleFormatCommon('bold')
  }

  // 斜体
  italicFn = () => {
    this.toggleFormatCommon('italic')
  }

  // 删除线
  strikethroughFn = () => {
    this.toggleFormatCommon('strike')
  }

  // 下划线
  underlineFn = () => {
    this.toggleFormatCommon('underline')
  }

  // 对齐
  alignFn = (value) => {
    const currentFormat = this.quill.getFormat()
    this.props.getQuillFormat({
      ...currentFormat,
      align: value,
    })
    this.quill.format('align', value == 'left' ? '' : value)
  }

  // 颜色
  fontColorFn = (value) => {
    const currentFormat = this.quill.getFormat()
    if (value == '#000') {
      value = ''
    }
    this.props.getQuillFormat({
      ...currentFormat,
      color: value,
    })
    this.quill.format('color', value)
  }

  // 插入话题
  topicFn = (value, type = 2) => {
    const range = this.quill.getSelection(true)
    let index = range.index
    if (type == 1) {
      index = index - 1
      this.quill.deleteText(index, 1, Quill.sources.USER);
    }
    this.quill.insertEmbed(index, 'topic', value, Quill.sources.USER)
    this.quill.insertText(index + 1, ' ', Quill.sources.USER)
    this.quill.setSelection(index + 2, Quill.sources.SILENT)
  }

  // 插入表情
  emojiFn = (emojiFileName, callback) => {
    const range = this.quill.getSelection(true)
    this.quill.insertEmbed(range.index, 'emoji', `${emojiUrl}${emojiFileName}`, Quill.sources.USER)
    this.quill.setSelection(range.index + 1, Quill.sources.SILENT)
    if (callback) {
      this.quill.blur()
      callback()
    }
  }

  // 插入图片
  imageFn = (value) => {
    this.setState({
      editorReadonly: false,
    }, () => {
      const progressDom = document.getElementById(value.code)
      if (progressDom) {
        const progressBlot = Quill.find(progressDom)
        const index = this.quill.getIndex(progressBlot)
        this.quill.deleteText(index, 1, Quill.sources.API)
        this.quill.insertEmbed(index, 'image', value, Quill.sources.USER)
        this.quill.setSelection(index + 1, Quill.sources.SILENT)
        setTimeout(() => {
          this.quill.scrollIntoView()
        }, 300)
      } else {
        const range = this.quill.getSelection(true)
        this.quill.insertText(range.index, '\n', Quill.sources.USER)
        this.quill.insertEmbed(range.index + 1, 'image', value, Quill.sources.USER)
        this.quill.setSelection(range.index + 2, Quill.sources.SILENT)
        setTimeout(() => {
          this.quill.scrollIntoView()
        }, 300)
      }
    })

  }

  // 图片上传进度
  imageProgressFn = (value) => {
    const range = this.quill.getSelection(true)
    this.setState({
      editorReadonly: true,
    }, () => {
      this.quill.insertText(range.index, '\n', Quill.sources.API)
      this.quill.insertEmbed(range.index + 1, 'imageProgress', value, Quill.sources.API)
      this.quill.setSelection(range.index + 2, Quill.sources.SILENT)
    })
  }

  // 图片上传进度
  imageProgressChangeFn = (value) => {
    const progressDom = document.getElementById(value.code)
    if (progressDom) {
      const progressBlot = Quill.find(progressDom)
      const index = this.quill.getIndex(progressBlot)
      this.quill.formatText(index, 1, { width: value.percent }, Quill.sources.API);
    }
  }

  // 删除图片进度条
  imageProgressDeleteFn = (value) => {
    this.setState({
      editorReadonly: false,
    }, () => {
      const progressDom = document.getElementById(value.code)
      if (progressDom) {
        const progressBlot = Quill.find(progressDom)
        const index = this.quill.getIndex(progressBlot)
        this.quill.deleteText(index, 1, Quill.sources.API)
      }
    })
  }

  // 视频
  videoFn = (value) => {
    this.setState({
      editorReadonly: false,
    }, () => {
      const progressDom = document.getElementById(value.code)
      if (progressDom) {
        const progressBlot = Quill.find(progressDom)
        const index = this.quill.getIndex(progressBlot)
        this.quill.deleteText(index, 1, Quill.sources.API)
        this.quill.insertEmbed(index, 'video', value, Quill.sources.USER)
        this.quill.setSelection(index + 1, Quill.sources.SILENT)
      } else {
        const range = this.quill.getSelection(true)
        this.quill.insertText(range.index, '\n', Quill.sources.USER)
        this.quill.insertEmbed(range.index + 1, 'video', value, Quill.sources.USER)
        this.quill.setSelection(range.index + 2, Quill.sources.SILENT)
      }
    })
  }

  // 视频进度条
  videoProgressFn = (value) => {
    const range = this.quill.getSelection(true)
    this.setState({
      editorReadonly: true,
    }, () => {
      this.quill.insertText(range.index, '\n', Quill.sources.API)
      this.quill.insertEmbed(range.index + 1, 'progress', value, Quill.sources.API)
      this.quill.setSelection(range.index + 2, Quill.sources.SILENT)
    })
  }

  // 视频进度条修改
  videoProgressChangeFn = (value) => {
    const progressDom = document.getElementById(value.code)
    if (progressDom) {
      const progressBlot = Quill.find(progressDom)
      const index = this.quill.getIndex(progressBlot)
      this.quill.formatText(index, 1, { width: value.percent }, Quill.sources.API);
    }
  }

  // 删除视频进度条
  videoProgressDeleteFn = (value) => {
    this.setState({
      editorReadonly: false,
    }, () => {
      const progressDom = document.getElementById(value.code)
      if (progressDom) {
        const progressBlot = Quill.find(progressDom)
        const index = this.quill.getIndex(progressBlot)
        this.quill.deleteText(index, 1, Quill.sources.API)
      }
    })
  }

  render() {
    const { deviceType, placeholder } = this.props
    const { topicPopVisible } = this.state
    return (
      <div className={classNames(styles.container, {
        [styles.pc]: deviceType == 'pc',
        [styles.mobile]: deviceType == 'mobile',
      })}>
        <ReactQuill
          ref={(node) => {this.reactQuillRef = node}}
          placeholder={placeholder}
          scrollingContainer={'#content'}
          theme={false}
          id="quill"
          onChange={this.onChange}
          modules={{
            toolbar: false,
            history: {
              userOnly: true
            },
          }}
        />
        <div id="topic_popover_ref" ref={node => this.topicPopoverRef = node} className={styles.topic_popover_ref}>
          <Popover
            trigger="click"
            open={topicPopVisible}
            onOpenChange={this.handleTopicOpenChange}
            destroyTooltipOnHide
            content={<Topic itemOnClick={(value) => this.topicItemOnClick(value, 1)}/>}
            showArrow={false}
            autoAdjustOverflow={true}
            overlayClassName={styles.format_pop}
            placement="bottomLeft"
            getPopupContainer={() => document.getElementById('topic_popover_ref')}
          >
          </Popover>
        </div>
      </div>
    )
  }
}

export default Index
