/**
 * 录屏用的按钮
 */
import React from 'react';
import classNames from 'classnames';
import styles from './index.less';  // 引入自定义样式
import {
  formatTimeBySeconds,
} from '@/utils/utils';

const RecordButton = ({
                        isLive,
                        localStreamConfig,
                        isJoined,
                        currentUserType,
                        resetTimer,
                        recordType,
                        dispatch,
                        liveRecord,
                        elapsedTime
                      }) => {

  const handleRecordButtonClick = (e) => {
    e.stopPropagation();
    resetTimer();

    if (recordType === 1) {
      // 开启录制时弹出结束录制的弹窗
      dispatch({
        type: 'PlanetChatRoom/setState',
        payload: { ModalVisibleByEndRecording: true }
      });
    } else {
      // 执行开始录制逻辑
      liveRecord({ recordType: 1 });
    }
  };

  return (
    <>
      {isLive && localStreamConfig && isJoined && currentUserType === 1 && (
        <div className={styles.HorizontalLiveRoom_Btn_Warp}>
          <div
            className={classNames({
              [styles.HorizontalLiveRoom_record_btn]: true,
              [styles.HorizontalLiveRoom_record_btn_active]: recordType === 1
            })}
            onClick={handleRecordButtonClick}
          >
          </div>
          {recordType === 1 ? (
            <div className={styles.text}>{formatTimeBySeconds(elapsedTime)}</div>
          ) : (
            <div className={styles.text}>录制</div>
          )}
        </div>
      )}
    </>
  );
};

export default RecordButton;
