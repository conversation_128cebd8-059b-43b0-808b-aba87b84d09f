import {
  getHomeMenuList,
  getHomePageLink,
  getPageInfo,
  getWordList,
  homeIndexSearch,
  joinOrQuitKingdom,
  qrCodeGenerator,
  submitApplyInfo,
  webHomeIndexSearch,
} from '@/services/activity/activity'

export default {
  namespace: 'activity',
  state: {
    checkedTab: 0,                                         // 搜索结果页，选中tab
    checkedChildTab: 6,                                    // 搜索结果页，选中二级tab
    searchKey: '',                                         // 搜索关键词

    spaceStatusList: [],                                   // 直播状态：1直播中、2预约中、3弹幕轰炸中
    meetingStatusList: [],                                 // 会议状态：1直播中、2预约中、3弹幕轰炸中
    achievementList: [],                                   // 病例成就
    difficultLevelDictList: [],                            // 难度等级
    startDate: null,                                       // 开始日期
    endDate: null,                                         // 结束日期
    depSubjectDictList: [],                                // 学科/科室

    cityList: [],                                          // 城市
    abilityLevelDictList: [],                              // 能力等级
    postTitleDictList: [],                                 // 职级
  },

  effects: {
    // 根据页面ID查询页面详情
    * getPageInfo({payload}, {call}) {
      const response = yield call(getPageInfo, payload)
      return response
    },

    // 提交报名
    * submitApplyInfo({payload}, {call}) {
      const response = yield call(submitApplyInfo, payload)
      return response
    },

    // 生成小程序码
    * qrCodeGenerator({payload}, {call}) {
      const response = yield call(qrCodeGenerator, payload)
      return response
    },

    // 获取首页地址
    * getHomePageLink({payload}, {call}) {
      const response = yield call(getHomePageLink, payload)
      return response
    },

    // 根据用户ID获取搜索关键字
    * getWordList({payload}, {call}) {
      const response = yield call(getWordList, payload)
      return response
    },

    // 加入或退出王国
    * joinOrQuitKingdom({payload}, {call}) {
      const response = yield call(joinOrQuitKingdom, payload)
      return response
    },

    // H5首页检索
    * homeIndexSearch({payload}, {call}) {
      const response = yield call(homeIndexSearch, payload)
      return response
    },

    // web端-首页检索
    * webHomeIndexSearch({payload}, {call}) {
      const response = yield call(webHomeIndexSearch, payload)
      return response
    },

    // web端-获取WEB首页菜单
    * getHomeMenuList({payload}, {call}) {
      const response = yield call(getHomeMenuList, payload)
      return response
    },
  },

  reducers: {
    // 保存数据
    save(state, {payload}) {
      return {
        ...state,
        ...payload,
      }
    },
    // 清空数据
    clean(state, {payload}) {
      return {
        checkedTab: 0,                                         // 搜索结果页，选中tab
        checkedChildTab: 6,                                    // 搜索结果页，选中二级tab
        searchKey: '',                                         // 搜索关键词

        spaceStatusList: [],                                   // 空间状态：1直播中、2预约中、3弹幕轰炸中

        achievementList: [],                                   // 病例成就
        difficultLevelDictList: [],                            // 难度等级
        startDate: null,                                       // 开始日期
        endDate: null,                                         // 结束日期
        depSubjectDictList: [],                                // 学科/科室

        cityList: [],                                          // 城市
        abilityLevelDictList: [],                              // 能力等级
        postTitleDictList: [],                                 // 职级
      }
    },
  },

  subscriptions: {
    setup({dispatch, history}) {
      return history.listen(({pathname, search}) => {
        if (
          pathname.indexOf('/Home/SearchResult') == -1 &&
          pathname.indexOf('/PlanetChatRoom/') == -1 &&
          pathname.indexOf('/Kingdom/') == -1 &&
          pathname.indexOf('/Expert/ExpertDetails') == -1 &&
          pathname.indexOf('/Case/CaseDetails') == -1 &&
          pathname.indexOf('/Case/CaseResult') == -1 &&
          pathname.indexOf('/Expert/ExpertResult') == -1 &&
          pathname.indexOf('/CreateGraphicsText/ArticleDetails') == -1 &&
          pathname.indexOf('/CreateGraphicsText/PostDetails') == -1 &&
          pathname.indexOf('/CreateGraphicsText/ExternalLinksDetails') == -1
        ) {
          dispatch({
            type: 'clean',
          })
        }
      })
    }
  }
}
