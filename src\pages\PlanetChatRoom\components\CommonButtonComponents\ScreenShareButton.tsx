/**
 * 点击投屏按钮
 * 或取消投屏
 */
import React from 'react';
import { message } from 'antd';
import styles from './index.less';  // 引入自定义样式

const ScreenShareButton = ({
                             isLive,
                             localStreamConfig,
                             isJoined,
                             currentUserType,
                             shareRemoteStreamConfig,
                             resetTimer,
                             handleChangeByLocalStreamConfig,
                             isMobile
}) => {
  return (
    <>
      { isLive
        && localStreamConfig
        && isJoined
        && (currentUserType == 1 || currentUserType == 2)
        &&
        <>
          {
            localStreamConfig.shareDesk ?
              <div className={styles.HorizontalLiveRoom_Btn_Warp}>
                <div onClick={(e) => {
                  e.stopPropagation(); resetTimer();
                  if (isMobile) {
                    message.info('手机端暂不支持投屏，请在电脑端使用');
                  }else {
                    handleChangeByLocalStreamConfig('shareDesk', e)
                  }
                }} className={styles.HorizontalLiveRoom_shared_screen_active_btn}/>
                <div className={styles.text}>结束投屏</div>
              </div>
              : <>
                {!shareRemoteStreamConfig  &&
                    <div className={styles.HorizontalLiveRoom_Btn_Warp}>
                      <div onClick={(e) => {
                        if (isMobile) {
                          message.info('手机端暂不支持投屏，请在电脑端使用');
                        }else {
                          handleChangeByLocalStreamConfig('shareDesk', e)
                        }
                      }} className={styles.HorizontalLiveRoom_shared_screen_btn}/>
                      <div className={styles.text}>投屏</div>
                    </div>
                }
              </>
          }
        </>
      }
    </>
  );
};

export default ScreenShareButton;
