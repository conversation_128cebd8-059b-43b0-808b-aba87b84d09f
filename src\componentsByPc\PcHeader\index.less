.pc_header_box {
  width: 100%;
  height: 84px;
  background: #fff;
  display: flex;
  justify-content: center;
  flex-shrink: 0;
}

.pc_header_wrap {
  width: 1228px;
  min-width: 1228px;
  height: 84px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .pc_left_wrap {
    display: flex;
    align-items: center;

    .pc_left_logo {
      width: 124px;
      margin-right: 20px;
      margin-left: 24px;

      img {
        width: 124px;
        height: auto;
      }
    }

    .pc_left_input {
      width: 379px;
      height: 36px;

      :global {
        .ant-input-affix-wrapper {
          height: 36px;
        }
        .ant-input-suffix {
          cursor: pointer;
          img{
            width: 20px;
            height: 20px;
          }
        }
      }
    }
  }

  .pc_right_wrap {
    display: flex;
    align-items: center;
    position: relative;
    height: 84px;

    .pc_right_item {
      font-size: 16px;
      font-weight: 400;
      color: #000000;
      line-height: 84px;
      margin-left: 27px;
      cursor: pointer;
      display: flex;
      align-items: center;
      height: 84px;

      img {
        width: 18px;
        height: 18px;
        margin-right: 8px;
        position: relative;
        top: -1px;
      }
    }

    .pc_release_list, .pc_userInfoList_list {
      position: absolute;
      top: 80px;
      right: 14px;
      z-index: 666;
      width: 162px;
      //height: 240px;
      background: #FFFFFF;
      box-shadow: 0px 4px 16px 0px rgba(0,0,0,0.1);
      border-radius: 4px 4px 4px 4px;
      padding: 8px 0;
      box-sizing: border-box;

      .list_item {
        font-size: 16px;
        font-weight: 400;
        color: #000000;
        height: 54px;
        line-height: 19px;
        width: 100%;
        text-align: center;
        line-height: 54px;
        cursor: pointer;
        &:hover {
          background: #F8F8F9;
        }
      }
    }
    .pc_userInfoList_list{
      right: 0;
    }
  }
}
