.container {
  background: #EEF3F9;
  height: 100vh;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
}

.inner_container {
  width: 100%;
  flex: 1;
  overflow-y: auto;
  padding-bottom: 16px;
}
.wrap {
  width: 816px;
  margin: 0 auto;
  .header{
    margin: 16px 0;
    background: #fff;
    padding: 17px 24px;
    height: 62px;
    display: flex;
    border-radius: 8px;
    align-items: center;
    .header_title{
      font-size: 20px;
      font-weight: 600;
      color: #000000;
      .header_title_icon{
        width: 24px;
        height: 24px;
        margin-right: 10px;
        cursor: pointer;
        vertical-align: sub;
      }
    }
  }


}

.content{
  background: #FFFFFF;
  border-radius: 8px;
  padding: 40px 40px 16px;

  .form_item_name {
    width: 100%;
    display: flex;

    .form_box_img {
      width: 169px;
      height: 96px;
      border-radius: 4px 4px 4px 4px;
      margin-right: 24px;
      overflow: hidden;

      .form_box_img_box {
        width: 100%;
        height: 100%;
        position: relative;
        user-select: none;
        cursor: pointer;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        .space_form_box_btn {
          width: 60px;
          height: 24px;
          background: rgba(0,0,0,0.3);
          border-radius: 30px 30px 30px 30px;
          font-weight: 400;
          font-size: 11px;
          color: #FFFFFF;
          line-height: 24px;
          text-shadow: 0px 0px 4px rgba(0,0,0,0.5);
          text-align: center;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          z-index: 1;
        }
      }
    }
  }

  .form_input{
    border-bottom: 1px solid #DDDDDD;
    flex:1;
    height: 54px;

    :global{
      .ant-input::placeholder{
        font-size: 22px;
        font-weight: 600;
        color: #AAAAAA;
      }
      .ant-input{
        font-size: 22px;
        padding: 0;
      }
    }
  }
  .form_text_area{
    margin-top: 16px;
    border-bottom: 1px solid #DDDDDD;
    padding-bottom: 8px;
    :global{
      .ant-input::placeholder{
        font-size: 14px;
        font-weight: 400;
        color: #999999;
      }
      .ant-input{
        padding: 0;
      }
    }
  }

  .from_wrap{
    margin-top: 36px;
    .from_content{
      display: flex;
      :global{
        .ant-select-item{
          padding: 0 16px;
        }
        .ant-select-dropdown{
          padding: 0;
        }
        .ant-select-item-option-selected:not(.ant-select-item-option-disabled),.ant-select-item-option-active:not(.ant-select-item-option-disabled){
          background: none;
        }
      }
      .from_title{
        width: 100px;
        font-size: 14px;
        font-weight: 400;
        color: #000000;
        line-height: 34px;
      }
      .from_desc{
        flex: 1;
        position: relative;

        .modal_wrap {
          opacity: 0;
        }

        :global{
          .ant-cascader-menu{
            width: 212px;
          }
          .ant-cascader-menu-item:hover{
            color: #0095FF;
            background: none;
          }
          .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled), .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled):hover{
            background: none;
            color: #0095FF;
            font-weight: 500;
          }

          .ant-select-selection-placeholder {
            font-size: 14px;
            font-weight: 400;
            color: #AAAAAA;
          }

          .ant-modal-wrap {
            z-index: 888;
          }

          .ant-modal-mask {
            z-index: 888;
            opacity: 0!important;
          }

          .ant-modal {
            width: 0!important;
          }
        }
        .init_text {
          font-size: 14px;
          font-weight: 400;
          color: #AAAAAA;
          line-height: 16px;
        }
        .audience_input,.audience_input_focus{
          width: 400px;
          height: 32px;
          box-sizing: border-box;
          margin: 0;
          // padding: 0;
          font-variant: tabular-nums;
          list-style: none;
          font-feature-settings: 'tnum', "tnum";
          position: relative;
          display: inline-block;
          // width: 100%;
          min-width: 0;
          padding: 4px 11px;
          color: rgba(0, 0, 0, 0.85);
          font-size: 14px;
          line-height: 1.5715;
          background-color: #fff;
          background-image: none;
          border: 1px solid #d9d9d9;
          border-radius: 2px;
          transition: all 0.3s;
          display: flex;
          align-items: center;
        }
        .audience_input:hover{
          border-color: var(--ant-primary-5);
          border-right-width: 1px;
        }
        .audience_input_focus{
          border-color: var(--ant-primary-color-hover);
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          box-shadow: 0 0 0 2px var(--ant-primary-color-outline);
          border-right-width: 1px;
          outline: 0;
        }
        .audience_input_disabled {
          color: rgba(0, 0, 0, 0.25);
          background: #f5f5f5;
          cursor: not-allowed;
          border: 1px solid #d9d9d9;
        }
        .select_wrap{
          position: absolute;
          top: 34px;
          left: 0;
          z-index: 999;
          // width: 542px;
          height: 190px;
          background: #FFFFFF;
          box-shadow: 0px 2px 12px 0px rgba(0,0,0,0.1);
          border-radius: 4px 4px 4px 4px;
          opacity: 1;
          display: flex;
          border: 1px solid #DCDFE6;
        }
        .right_arrow_icon{
          width: 14px;
          height: 14px;
        }
        .select_first,.select_two {
          padding: 12px 10px 6px 20px;
          height: 190px;
          width: 180px;
          margin: 0;

          .select_first_li ,.select_two_li{
            height: 34px;
            font-size: 14px;
            font-weight: 400;
            color: #606266;
            cursor: pointer;
            .select_first_span,.select_two_span{
              display: inline-block;
              width: 135px;
              font-size: 14px;
              font-weight: 400;
              color: #606266;
              &.selected {
                font-weight: 500;
                color: #0095FF;
              }
            }
          }

        }
        .select_two {
          border-left: 1px solid #DCDFE6;
        }
        .select_three{
          padding: 12px 6px 12px 12px;
          border-left: 1px solid #DCDFE6;
          width: 200px;
          overflow-y: auto;
          :global{
            .ant-checkbox-wrapper + .ant-checkbox-wrapper{
              margin-left: 0;
            }
            .ant-checkbox + span{
              display: flex;
            }
          }
          .select_three_title{
            font-size: 12px;
            font-weight: 400;
            color: #999999;
            margin: 6px 0;
          }
          .three_content{
            width: 100%;
            height: 160px;
            overflow-y: scroll;

            :global{
              .ant-checkbox-wrapper{
                margin-bottom: 12px;
                width: 100%;
              }
              .ant-checkbox-wrapper:last-child{
                margin-bottom: 0;
              }
            }
          }
          .select_three_content{
            padding-left: 8px;
            :global{
              .ant-checkbox-wrapper{
                margin-bottom: 12px;
              }
              .ant-checkbox-wrapper:last-child{
                margin-bottom: 0;
              }
            }
          }
        }

      }
      .from_radio{
        flex: 1;
        :global{
          .ant-form-item{
            margin-bottom: 12px;
          }
          .ant-radio-wrapper{
            margin-right: 25px;
          }
        }
      }

      /*.upload_banner_courseware {
        font-size: 14px;
        padding: 6px 15px;
        border: #eeee 1px solid;
        border-radius: 7px;
        cursor: pointer;
      }*/

      .upload_banner_courseware {
        // position: absolute;
        width: 120px;
        height: 120px;
        cursor: pointer;
        background: #F8F8F8;
        overflow: hidden;
        border: #F8F8F8 1px solid;
        cursor: pointer;
        text-align: center;
        line-height: 120px;

        .init_upload_img {
          width: 32px;
          height: 32px;
        }
      }

      .init_upload_tip {
        margin-top: 8px;
        font-weight: 400;
        font-size: 9px;
        color: #999999;
        line-height: 14px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }



      .from_switch {
        flex: 1;
        padding-left: 358px;

        :global{
          .ant-form-item{
            margin-bottom: 12px;
          }
          .ant-radio-wrapper{
            margin-right: 25px;
          }
        }
      }
    }

    .start_date_time_wrap {
      margin-left: 100px;
      display: flex;
    }
  }

  .space_password_wrap{
    margin-left: 100px;
    width: 400px;
  }



  // 上传封面
  .upload_wrap{
    padding-left: 110px;
    margin-bottom: 24px;
    .tips{
      font-size: 12px;
      font-weight: 400;
      color: #999999;
      margin-top: 8px;
    }
    :global{
      .ant-upload-select-picture-card{
        width: 200px;
        height: 112px;
        background: #F8F8F8;
        border: 0;
        margin: 0;
      }
      .ant-form-item{
        margin-bottom: 8px;
      }
    }
    .upload_box{
      width: 200px;
      height: 112px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;

      :global {
        .ant-spin-container {
          width: 200px;
          height: 112px;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
        }
      }

      .edit_btn_box {
        position: absolute;
        bottom: 8px;
        left: 50%;
        transform: translateX(-50%);
        width: 117px;
        height: 23px;
        background: rgba(0,0,0,0.5);
        padding: 0 27px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        div {
          cursor: pointer;
          font-size: 11px;
          font-weight: 400;
          color: #FFFFFF;
          line-height: 13px;
        }
      }

      .upload_img{
        width: 100%;
        height: 100%;
      }
      .init_upload_img{
        width: 32px!important;
        height: 32px!important;
        margin: 0 auto;
      }
    }
  }

  // 上传空间广告
  .upload_banner_wrap{
    // padding-left: 110px;
    margin-top: 10px;
    margin-bottom: 24px;
    .tips{
      font-size: 12px;
      font-weight: 400;
      color: #999999;
      margin-top: 8px;
    }
    :global{
      .ant-upload-select-picture-card{
        width: 350px;
        height: 53px;
        background: #F8F8F8;
        border: 0;
        margin: 0;
      }
      .ant-form-item{
        margin-bottom: 8px;
      }
    }

    :global {
      .ant-spin-container {
        width: 350px;
        height: 53px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
      }
    }

    .edit_btn_box {
      position: absolute;
      top: 17px;
      left: 50%;
      transform: translateX(-50%);
      width: 117px;
      height: 23px;
      background: rgba(0,0,0,0.5);
      padding: 0 27px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      div {
        cursor: pointer;
        font-size: 11px;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 13px;
      }
    }

    .upload_banner_box{
      width: 350px;
      height: 53px;
      display: flex;
      align-items: center;
      justify-content: center;

      .upload_banner_img{
        width: 100%;
        height: 100%;
      }

      .init_upload_banner_img{
        width: 32px!important;
        height: 32px!important;
        margin: 0 auto;
      }
    }
  }

  // 上传空间视频
  .upload_video_wrap{
    // padding-left: 110px;
    margin-bottom: 24px;
    margin-top: 10px;

    .form_error_message {
      font-size: 14px;
      line-height: 1.5715;
      color: var(--ant-error-color);
    }

    .tips{
      font-size: 12px;
      font-weight: 400;
      color: #999999;
      margin-top: 8px;
    }

    .upload_video_content {
      width: 120px;
      height: 120px;
      background: #F8F8F8;
      overflow: hidden;
      position: relative;

      .edit_btn_box {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 23px;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;

        div {
          cursor: pointer;
          font-size: 11px;
          font-weight: 400;
          color: #FFFFFF;
          line-height: 13px;
        }
      }

      .uploading_icon_content {
        width: 120px;
        height: 120px;
        background: #F8F8F8;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .init_upload_img {
          width: 32px;
          height: 32px;
        }

        .uploading_icon_text {
          font-size: 14px;
          font-weight: 400;
          color: #999999;
          line-height: 16px;
          margin-top: 8px;
        }

        .video_name_text {
          font-size: 14px;
          font-weight: 400;
          color: #999999;
          line-height: 16px;
          margin-top: 8px;
          display: flex;

          .uploading_name_text {
            display: block;
            max-width: 70px;
            white-space: nowrap;
            overflow: hidden;
          }
        }
      }

      .uploading_init_icon_content {
        width: 120px;
        height: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
        .init_upload_img {
          width: 32px;
          height: 32px;
        }
      }

      .upload_video_input {
        position: absolute;
        width: 120px;
        height: 120px;
        top: 0;
        left: 0;
        display: block!important;
        opacity: 0;
        cursor: pointer;
      }

      .edit_btn_input {
        position: relative;

        .upload_video_input {
          position: absolute;
          top: -5px;
          left: 0;
          display: block!important;
          opacity: 0;
          width: 22px;
          height: 23px;
        }
      }
    }

  }
}

// 主持人\嘉宾下拉样式
.select_dropdown_content {
  padding-top: 16px;
  border-radius: 4px;
  overflow: hidden;
  .dropdown_content_input {
    border-radius: 4px;
    height: 36px;
    background: #F5F6F8;
    margin: 0 16px 12px;
    padding: 0 12px 0 25px;
    position: relative;
    &>img{
      position: absolute;
      width: 16px;
      height: 16px;
      left: 12px;
      top: 10px;
    }
    :global{
      .ant-input{
        line-height: 30px;
      }
      .ant-input::placeholder{
        color: #999999;
      }
    }

  }
  .dropdown_content_title {
    font-size: 12px;
    color: #999;
    padding: 0 16px;
    line-height: 17px;
  }

}
.select_option_content {
  cursor: pointer;
  height: 48px;
  border-bottom: 1px solid #F5F6F8;
  display: flex;
  align-items: center;
  position: relative;
  padding-right: 16px;
  font-size: 14px;
  color: #666;
  &:hover{
    .option_content_name {
      color: #0095FF;
    }
    .option_content_phone {
      color: #0095FF;
    }
  }
  .option_content_name {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .option_content_phone {
    padding: 0 12px;
    display: block;
    width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .option_content_img {
    position: absolute;
    display: none;
    width: 24px;
    height: 24px;
    top: 50%;
    transform: translateY(-50%);
    right: 11px;
    & > img {
      width: 100%;
      height: 100%;
    }
  }
}

.content {
  :global(.ant-select-item-option-selected) {
    .select_option_content {
      .option_content_name, .option_content_phone {
        color: #0095FF;
      }
      .option_content_img {
        display: block;
      }
    }
  }
}

.from_content {
  :global {
    .ant-select-item-option-state {
      display: none;
    }
    .ant-select-item-option-disabled.ant-select-item-option-selected {
      background: none;
    }
  }
}

.footer{
  flex-shrink: 0;
  height: 80px;
  width: 100%;
  border-top: 1px solid #DDDDDD;
  background: #FFFFFF;

  display: flex;
  align-items: center;
  .footer_content{
    width: 816px;
    margin: 0 auto;
    text-align: right;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    :global{
      .ant-btn-primary{
        background: #0095FF;
        border-radius: 4px;
      }
    }

    .appointment_start_wrap {
      position: relative;
      cursor: pointer;

      .picker_input {
        position: absolute;
        top: 0;
        left: 0;
        opacity: 0;
      }
    }
  }
}

// 关联王国下拉框
.custom_dropdown_render {
  :global {
    ::-webkit-scrollbar{
      width: 7px;
      height: 7px;
    }
    ::-webkit-scrollbar-thumb{
      border-radius:10px;
      border: 2px solid #fff;
      -webkit-box-shadow: inset 0 0 0 5px rgba(0,0,0,0.2);
      background: rgba(0,0,0,0.2);
    }
    ::-webkit-scrollbar-track{
      // -webkit-box-shadow: inset 0 0 0 5px rgba(0,0,0,0.2);
      border-radius:0px;
      background:#fff;
    }
  }
  .select_dropdown_container {
    padding-bottom: 16px;
    max-height: 256px;
    overflow-y: auto;
    .select_dropdown_title {
      font-size: 14px;
      color: #666;
      padding: 12px 16px 4px;
    }
    .kingdom_item {
      padding: 12px 16px;
      border-bottom: 1px solid #F5F6F8;
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      cursor: pointer;
      &:hover {
        background: #f5f5f5;
      }
      &.selected {
        background: #e6f7ff;
      }
      & > i {
        width: 40px;
        height: 40px;
        flex-shrink: 0;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        margin-right: 8px;
        font-style: normal;
        line-height: 40px;
        color: #fff;
        font-size: 12px;
        text-align: center;
        white-space: nowrap;
      }
      .kingdom_item_details {
        flex: 1;
        .kingdom_name {
          font-size: 15px;
          color: #000;
          font-weight: 500;
          line-height: 21px;
          margin-bottom: 2px;
        }
        .kingdom_info_box {
          display: flex;
          align-items: center;
          font-size: 12px;
          color: #999;
          line-height: 17px;
          & > span + span {
            margin-left: 4px;
          }
        }
      }
    }
  }
}

.title_item_box {
  display: flex;
  margin-bottom: 20px;
  user-select: none;
  cursor: pointer;
  width: 500px;
  align-items: center;
  justify-content: space-between;

  .title_item {
    font-weight: 700;
    font-size: 16px;
    color: #000000;
    line-height: 16px;
    font-style: normal;
  }

  .title_item_btn {
    color: #999999;
    border-radius: 0px 0px 0px 0px;
    font-weight: 700;
    height: 13px;
    line-height: 13px;
    font-size: 13px;

  }
}

.from_box_warp_hidden {
  height: 0;
  overflow: hidden;
}

.upload_banner_courseware {
  width: 90px;
  height: 40px;
  background: #F8F8F8;
  text-align: center;
  line-height: 40px;
  font-size: 16px;
  cursor: pointer;
  user-select: none;
  border-radius: 5px;
}
