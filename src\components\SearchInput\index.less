.search_input_wrap {
  width: 100%;
  background: #fff;
  padding: 8px 0 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .search_input_box {
    flex: 1;
    height: 40px;
    :global {
      .ant-input-affix-wrapper {
        padding: 0 12px;
        border-radius: 23px;
        background: #F5F5F5;
        border: none;
        align-items: center;

        .ant-input-prefix {
          width: 20px;
          height: 20px;
          margin-right: 8px;
          img {
            width: 20px;
            height: 20px;
          }
        }

        .ant-input {
          background: none;
          color: #000;
          height: 40px;
          &::placeholder {
            color: #ccc;
          }
        }

        .ant-input:focus, .ant-input-focused {
          border: none!important;
          outline: none;
          box-shadow: none;
        }
      }
      .ant-input-affix-wrapper:focus, .ant-input-affix-wrapper-focused {
        border: none!important;
        outline: none;
        box-shadow: none;
      }
      .ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):hover {
        border: none!important;
        outline: none;
        box-shadow: none;
      }
    }
  }

  .search_close {
    padding: 0 20px;
    flex-shrink: 0;
    font-size: 14px;
    font-weight: 400;
    color: #666;
    line-height: 20px;
  }
}
