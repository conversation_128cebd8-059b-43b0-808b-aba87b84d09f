import {getImInfoByUser,} from "@/services/planetChatRoom";
import TIM from 'tim-js-sdk';
import TIMUploadPlugin from 'tim-upload-plugin';
import TIMProfanityFilterPlugin from 'tim-profanity-filter-plugin';
import {message} from "antd";

const initializedState = {
    imUserId:null,  // IM用户id
    userSig:null,   // IM用户密匙
    imAppId:null,   // IM-appID
    tim:null,       // IM实例
}

export default {
  namespace:'tim',
  state: { ...initializedState },

  effects: {
    // 登录后获取该用户的IM秘钥信息
    *getImInfoByUser({ payload }, { call,put }) {
      const access_token = localStorage.getItem('access_token');
      if (access_token) {
        const response = yield call(getImInfoByUser);
        const {content, code} = response || {};
        if (code == 200 && !!content) {
          yield put({
            type: 'setState',
            payload: content
          })
        }
        return response
      }
    },

    // 登录tim
    *loginTim({ payload }, { call,put,select }) {
      const access_token = localStorage.getItem('access_token');
      if (!access_token) { return }

      const timState  = yield select(state => state.tim);
      const {
        imUserId,  // IM用户id
        userSig,   // IM用户密匙
        imAppId,   // IM-appID
      } = timState || {};

      if (!(userSig && imAppId)) { return }

      let options = {
        // SDKAppID: SDKAPPID // 接入时需要将0替换为您的即时通信 IM 应用的 SDKAppID
        SDKAppID: imAppId
      };

      // 创建 SDK 实例，`TIM.create()`方法对于同一个 `SDKAppID` 只会返回同一份实例
      let tim = TIM.create(options); // SDK 实例通常用 tim 表示
      // 设置 SDK 日志输出级别，详细分级请参见 setLogLevel https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#setLogLevel 接口的说明</a>
      tim.setLogLevel(0); // 普通级别，日志量较多，接入时建议使用
      // tim.setLogLevel(1); // release 级别，SDK 输出关键信息，生产环境时建议使用
      // 注册腾讯云即时通信 IM 上传插件
      tim.registerPlugin({'tim-upload-plugin': TIMUploadPlugin});
      // 注册腾讯云即时通信 IM 本地审核插件
      tim.registerPlugin({'tim-profanity-filter-plugin': TIMProfanityFilterPlugin});

      tim.on(TIM.EVENT.SDK_READY, async () => {
        // 当SDK进入ready状态时触发此事件。
        // 当在监听过程中检测到此事件时，
        // 访问端可以调用SDK API(比如消息发送API)来使用SDK的各种特性
        // 当IM登录成功后，保存tim对象
        // setTimObj(tim)

        // 登录IM
        /*let promise = tim.login({userID: imUserId, userSig: userSig});
        promise.then(function (imResponse) {
          if (imResponse.data.repeatLogin === true) {
            message.success('TIM登录成功!');
          } // 重复登录
        }).catch(function (imError) {
          message.error('TIM登录失败!!');
          console.warn('login error:', imError); // Error information
        });*/
      });

      let promise = tim.login({userID: imUserId, userSig: userSig});
     /* yield promise.then((imResponse)=> {
        // message.success('TIM登录成功!'); // 重复登录
        if (imResponse.data.repeatLogin === true) {}
        return true;
      }).catch(function (imError) {
        message.warn(' TIM登录失败 login error:', imError); // Error information
        return false;
      });*/
      const imResponse = yield promise;
      // 处理重复登录
      if (imResponse.data.repeatLogin === true) {}
      yield put({
        type: 'setState',
        payload: {tim: tim}
      })
      return tim;
    },

    // 获取tim实例
    *getTim({ payload }, { call,put,select }) {
      const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
      const access_token = localStorage.getItem('access_token');
      const { friUserId } = UserInfo || {};

      if (!access_token) {
        message.error('请先登录');
        return null
      }
      const timState  = yield select(state => state.tim);
      // IM实例
      const { tim } = timState || {};
      if (!!tim) {
        return tim;
      }else {
        // 初始化tim 登录tim 并返回
        // ① 登录后获取该用户的IM秘钥信息
        const response = yield call(getImInfoByUser);
        const {content, code,msg} = response || {};
        if(code != 200 || !content){ message.error(msg ? msg : '获取IM信息失败'); return null }
        yield put({ type: 'setState', payload: content })
        // ② 初始化tim
        const {
          imUserId,  // IM用户id
          userSig,   // IM用户密匙
          imAppId,   // IM-appID
        } = content || {};
        if (!(userSig && imAppId)) {  message.error('tim配置缺失');  return null }
        let options = {
          // SDKAppID: SDKAPPID // 接入时需要将0替换为您的即时通信 IM 应用的 SDKAppID
          SDKAppID: imAppId
        };

        // 创建 SDK 实例，`TIM.create()`方法对于同一个 `SDKAppID` 只会返回同一份实例
        let tim = TIM.create(options); // SDK 实例通常用 tim 表示
        // 设置 SDK 日志输出级别，详细分级请参见 setLogLevel https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#setLogLevel 接口的说明</a>
        tim.setLogLevel(0); // 普通级别，日志量较多，接入时建议使用
        // tim.setLogLevel(1); // release 级别，SDK 输出关键信息，生产环境时建议使用
        // 注册腾讯云即时通信 IM 上传插件
        tim.registerPlugin({'tim-upload-plugin': TIMUploadPlugin});
        // 注册腾讯云即时通信 IM 本地审核插件
        tim.registerPlugin({'tim-profanity-filter-plugin': TIMProfanityFilterPlugin});
        // tim初始化完成
        tim.on(TIM.EVENT.SDK_READY, async () => {});

        // ③ 登录IM
        let promise = tim.login({userID: imUserId, userSig: userSig});
        let stutse = null;  // 是否登录成功
        yield promise.then((imResponse)=> {
          // message.success('TIM登录成功!'); // 重复登录
          if (imResponse.data.repeatLogin === true) {}
          stutse = true;
        }).catch(function (imError) {
          message.warn(' TIM登录失败 login error:', imError); // Error information
          stutse = false;
        });
        if(stutse) {
          yield put({type: 'setState', payload: {tim: tim}})
          return tim;
        }else { return null; }
      }
    }
  },

  reducers: {
    // 更新状态值数据
    setState(state, { payload }) {
      return {
        ...state,
        ...payload
      }
    },

    // 清空数据
    clean(state, { payload }){
      return {
        ...state,
        ...initializedState,
      }
    }
  },
};
