// @ts-ignore
/* eslint-disable */
import request from '@/utils/request'
import { stringify } from 'qs'

/**
 * 登录后获取该用户的IM秘钥信息   张志军
 * @params wxUserId                     当前登录用户ID
 */
export async function getImInfoByUser(params) {
  return request('/api/server/imBase/getImInfoByUser', {
    method: 'GET',
    params,
  })
}
/**
 * 获取图文聊天消息，分页倒序   张志军
 * @params msgSeq                     不传为初始索引值
 * @params pageSize                   条数
 * @params consultationId              指导ID
 */
export async function getChatGroupMsg(params) {
  return request('/api/server/h5ConsultationOrder/getChatGroupMsg', {
    method: 'GET',
    params,
  })
}
/**
 * 查询指导和病例详情   时均瑶
 * @params type                   (1:图文支付/视频提交, 2:其它通用详情)
 * @params consultationId         指导ID
 */
export async function getConsultationAndCaseInfo(params:any) {
  return request('/api/server/h5ConsultationOrder/getConsultationAndCaseInfo', {
    method: 'GET',
    params,
  })
  }
/**
 * 查询指导方式 时俊瑶
 * @params wxUserId                     操作人ID
 * @params expertsUserId               专家微信用户ID
 */
export async function getConsultationWay(params) {
  return request('/api/server/h5ConsultationOrder/getConsultationWay', {
    method: 'GET',
    params,
  })
}
/**
 * 查询指导流程节点 时俊瑶
 * @params consultationId      指导ID
 */
export async function getConsultationProcessNode(params) {
  return request('/api/server/h5ConsultationOrder/getConsultationProcessNode', {
    method: 'GET',
    params,
  })
}
/**
 * 修改指导订单节点和状态   时俊瑶
 * @param {string} consultationId    指导ID
 * @param {string} type              1:视频指导提交, 2:图文或视频病例被查看, 3:图文问题被回复并对话, 4:图文结束指导交易成功, 5:视频预约视频会议, 6:视频沟通, 7:结束指导 取消指导传8
 * @param {string} userId           操作人ID
 * @returns
 */
export const editConsultationNodeAndStatus = (params:any) =>
request(`/api/server/h5ConsultationOrder/editConsultationNodeAndStatus?${stringify({
  consultationId: params.consultationId,
  type: params.type,
})}`, {
  method: 'POST',
  data: params,
  headers: {},
})


/**
 * 查询学科列表 时俊瑶
 * @params expertsUserId               专家微信用户ID(非必传)
 */
export async function getDepSubjectDict(params) {
  return request('/api/server/h5ConsultationOrder/getDepSubjectDict', {
    method: 'GET',
    params,
  })
}

/**
 * 创建订单(选择学科后确定) 时俊瑶
 * @params type                        指导类型(1图文、2视频)
 * @params expertsId                   指导医生ID
 * @params discount                    折扣(非会员无折扣NULL、个人7折、企业5折)
 * @params unitPrice                   基础单价(图文是/次，视频是/30min)
 * @params vipUnitPrice                会员单价(图文是/次，视频是/30min)
 * @params memberOrderId               会员订单ID
 * @params userId                      操作人id
 * @params consultationCaseInfoDto     病例信息
 * @params consultationCaseInfoDto[caseName]          病例名称
 * @params consultationCaseInfoDto[depSubjectDicts]   学科字典ID(多值逗号隔开)
 */
export async function saveConsultationInfo(params) {
  return request('/api/server/h5ConsultationOrder/saveConsultationInfo', {
    method: 'POST',
    data: params,
  })
}

/**
 * 编辑指导订单信息 时俊瑶
 * @params url参数 type                  (1复制地址，2下一步)
 * @params id                          指导ID
 * @params userId                      操作人id
 * @params type                        指导类型(1图文、2视频)
 * @params consultationCaseInfoDto     病例信息
 * @params -- [id]                     病例ID
 * @params -- [depSubjectDicts]        学科字典ID(多值逗号隔开)
 * @params -- [isTemplate]             是否是模板(1是，0否)
 * @params -- [noTemplateDescription]  无模板的描述
 * @params -- [firstQuestion]          初始提问
 * @params -- [consultationCaseMediaDtoList]   病例媒体信息集合
 * @params -- -- [type]                资料类型(0星球影像、1其他资料、2全景片、3侧位片、4正面像、5侧面像、6正面咬合像、7正面咬合45度像、8左侧咬合像、9右侧咬合像、10上牙弓像、11下牙弓像)
 * @params -- -- [fileUrl]             文件路径
 * @params -- -- [fileName]            文件名称
 * @params -- -- [fileSuffix]          文件后缀
 * @params -- -- [fileSize]            文件大小
 * @params -- [questionInventory]      问题清单(瑞尔)
 * @params -- [diagnosticAnalysis]     诊断分析(瑞尔)
 * @params -- [treatmentPlanList]      治疗方案集合(兼容瑞尔数组)
 * @params -- [diagnosis]              诊断
 * @params -- [checkUp]                检查
 * @params -- [wholeHealth]            全身健康情况
 * @params -- [previousHistory]        既往史
 * @params -- [presentDisease]         现病史
 * @params -- [chiefComplaint]         主诉
 * @params -- [sex]                    性别
 * @params -- [age]                    年龄
 * @params -- [templateType]           模板类型(1星球、2瑞尔)
 */
export async function editConsultationInfo(params) {
  return request(`/api/server/h5ConsultationOrder/editConsultationInfo`, {
    method: 'POST',
    params: params.getParams,
    data: params.postParams,
  })
}

/**
 * 图文指导提交订单或去支付 王少彬
 * @params type                        指导类型(1图文、2视频)
 */
export async function submitConsultationPictureOrderPay(params) {
  return request('/api/server/h5ConsultationOrderPay/submitConsultationPictureOrderPay', {
    method: 'POST',
    data: params,
  })
}

/**
 * 视频指导提交订单或去支付 王少彬
 * @params type                        指导类型(1图文、2视频)
 */
export async function submitConsultationVideoOrderPay(params) {
  return request('/api/server/h5ConsultationOrderPay/submitConsultationVideoOrderPay', {
    method: 'POST',
    data: params,
  })
}

/**
 * 查询病例附件列表 时俊瑶
 * @params caseId    病例ID
 */
export async function getCaseAttachmentList(params) {
  return request('/api/server/h5ConsultationOrder/getCaseAttachmentList', {
    method: 'GET',
    params,
  })
}

/**
 * 发送指导病例附件到邮箱 时俊瑶
 * @params caseId         病例ID
 * @params emailAddress   邮件地址
 */
export async function sendConsultationCaseAttachmentEMail(params) {
  return request('/api/server/h5ConsultationOrder/sendConsultationCaseAttachmentEMail', {
    method: 'POST',
    params: params.getParams,
  })
}
/**
 * 记录用户已关注公众号 张志军
 * @params wxUserId       当前用户ID
 */
export async function isFocusMp(params) {
  return request('/api/server/H5Base/isFocusMp', {
    method: 'POST',
    params: params.getParams,
  })
}

/**
 * 获取正畸方案病例详情(无指导ID时获取基本信息字典和患者信息) 时俊瑶
 * @params consultationId    订单ID
 * @params tenantId          租户ID
 * @params customerId        患者ID
 * @params type              type(0查全部、1基本信息、2检查及分析、3问题清单及诊断、4治疗方案、5影像资料)
 */
export async function getOrthodonticCaseInfo(params) {
  return request('/api/server/h5OrthodonticAudit/getOrthodonticCaseInfo', {
    method: 'GET',
    params,
  })
}
/**
 * 正畸病例-会诊查询详情
 * /h5ConsultationOrder/get-consultation-orthodontic-case-info
 * 由https://dugr.w.eolink.com/home/<USER>/inside/5KKZnQ158d232fb65f56a2d3d936032822f03d18234e8b2/api/2728001/detail/52711068?spaceKey=dugr&projectGroup=0&projectType=0
 * （正畸病例审核）接口，与此接口结构一致
 */
export async function getConsultationOrthodonticCaseInfo(params) {
  return request('/api/server/h5ConsultationOrder/get-consultation-orthodontic-case-info', {
    method: 'GET',
    params,
  })
}

/**
 * 切换病例模板（常英杰）
 * @params consultationId              会诊ID
 */
export async function switchOrderCaseTemplate(params) {
  return request(`/api/server/h5ConsultationOrder/switch-order-case-template?${stringify(params.getParams)}`, {
    method: 'POST',
    data: params.postParams,
  })
}

/**
 * 修改会诊用户提问且同步病历问题，修改会诊类型（赵志君）
 * @params userName              用户名称
 * @params wxUserId              用户ID
 * @params id              会诊ID
 * @params firstQuestion              用户提问修改内容
 * @params type              指导类型(1图文、2视频)
 */
export async function updateCaseInfoQuestion(params) {
  return request(`/api/server/h5ConsultationOrder/update-case-info-question`, {
    method: 'POST',
    data: params,
  })
}
