/**
 * @Description: 选择病例模板弹窗
 */
import React, { useState, useEffect } from 'react'
import { connect, history } from 'umi'
import classNames from 'classnames'
import { Typography, message } from 'antd'
import { Popup, Toast, Popover } from 'antd-mobile'
import { RightOutlined, InfoCircleOutlined, CloseOutlined } from '@ant-design/icons'
import styles from './index.less'

import copy_icon from '@/assets/Consultation/copy_icon.png' // 复制icon

// 指导步骤条
import StartConsultationSteps from '@/pages/ConsultationModule/StartConsultation/ComponentsH5/StartConsultationSteps'

interface PropsType {
  visible: boolean,                              // true，false
  expertsUserId: any,                            // 专家ID
  consultationType: any, // 指导类型，1图文，2视频
  onCancel: any,                                 // 关闭弹窗
}

const Index: React.FC<PropsType> = (props: any) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}') // 登录用户信息
  const {
    dispatch,
    visible,
    expertsUserId, // 专家ID
    consultationType, // 指导类型，1图文，2视频
    onCancel,
  } = props

  // 点击通用模板，1 通用模版，2 正畸模版
  const onClickTemplate = async () => {
    Toast.show({
      icon: 'loading',
      content: '',
      duration: 0,
    })
    const res = await editConsultationInfo()
    Toast.clear()
    const { code, content, msg } = res || {}
    if (code == 200 && content) {
      const { consultationId: consultationIdByRequest } = content
      history.push({
        pathname: '/ConsultationModule/StartConsultation/Step3',
        query: {
          expertsUserId,
          consultationType, // 指导类型，1图文，2视频
          orderCaseTemplate: 1, // 订单病例模板 1. 通用病例 2正畸病例
          consultationId: consultationIdByRequest, // 指导ID
        }
      })
    } else {
      Toast.show(msg || '数据加载失败')
    }
  }

  // 编辑指导订单信息，生成指导ID，1 通用模版，2 正畸模版
  const editConsultationInfo = () => {
    return dispatch({
      type: 'consultation/editConsultationInfo',
      payload: {
        postParams: {
          id: null,
          type: consultationType, // 指导类型，1 图文，2 视频
          expertsId: expertsUserId, // 指导医生ID
          orderCaseTemplate: 1, // 订单病例模板 1. 通用病例  2正畸病例
          processNode: 2, // 图文[1选择指导方式、2描述病例问题、3支付指导费用、4病例资料被查看、5问题被回复并对话、6结束指导交易成功];
          // 视频[1选择指导方式、2描述病例问题、3提交指导单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、7结束指导、8确认并支付指导费用、9交易成功]
        },
      }
    }).then(res => {
      return res
    }).catch(err => {
      return null
    })
  }

  // 点击正畸模版
  const onClickOrthodonticsTemplate = () => {
    message.warning('请使用电脑打开上传资料')
  }

  // 复制成功
  const onCopy = () => {
    message.success('复制成功')
    onCancel()
  }

  return (
    <Popup
      visible={visible}
      onMaskClick={onCancel}
      className={styles.popup_container}
      destroyOnClose
      bodyStyle={{minHeight:'60%'}}
    >
      <div className={styles.header_line} onClick={onCancel}>
        <div className={styles.header_line_bar}></div>
      </div>
      <div className={styles.header_title}>
        选择病例信息模板
        <div className={styles.close_icon} onClick={onCancel}>
          <CloseOutlined/>
        </div>
      </div>

      {/* 点击查看完整服务流程按钮及弹窗 */}
      <div className={styles.complete_process_wrap}>
        <StartConsultationSteps/>
      </div>

      {/* 提示信息 */}
      <div className={styles.tips_message_wrap}>
        <InfoCircleOutlined/>
        <div className={styles.tips_message}>如您需要提交正畸等病例，推荐使用正畸专科模板。如病例信息不全，可能影响咨询效果</div>
      </div>

      {/* 通用模板 */}
      <div className={styles.block} onClick={onClickTemplate}>
        <div className={styles.block_left_wrap}>
          <div className={styles.block_title_wrap}>通用模版</div>
          <div className={styles.block_info_wrap}>提供基础的病例信息输入，支持上传附件</div>
        </div>
        <div className={styles.block_right_wrap}>
          <RightOutlined/>
        </div>
      </div>

      {/* 正畸模版 */}
      <div className={styles.block} id="block2" onClick={onClickOrthodonticsTemplate}>
        <div className={styles.block_left_wrap}>
          <div className={styles.block_title_wrap}>
            <span>正畸专科模版</span>
            <Popover
              getContainer={() => document.getElementById('block2')}
              content={
                <div className={styles.block_tips_wrap}>
                  <p className={styles.tips_title}>请您提前准备以下资料以便精准咨询：</p>
                  <p>1、影像资料</p>
                  <p>全景片、侧位片、正面像、侧面像、正面咬合像、正面咬合45度像、左侧咬合像、右侧咬合像、上牙弓像、下牙弓像等</p>
                  <p>2、检查及分析</p>
                  <p>包括口外检查、口内检查、颞颌关节检查，模型分析、侧位片分析、全景片分析</p>
                </div>
              }
              trigger="click"
              placement="right"
              stopPropagation={['click']}
            >
              <InfoCircleOutlined onClick={e => e.stopPropagation()}/>
            </Popover>
          </div>
          <div className={styles.block_info_wrap}>
            <p>专业的正畸病例模板，全方位上传患者病例信息，</p>
            <p>完整的信息填写，专家指导更快</p>
          </div>
          <div className={styles.block_extra_wrap}>
            <p>请使用电脑打开上传资料：</p>
            <Typography.Paragraph onMouseDown={e => e.stopPropagation()} copyable={{
              text: `${window.location.origin}/ConsultationModule/StartConsultation/Step2?consultationType=${consultationType}&expertsUserId=${expertsUserId}&copyUserId=${UserInfo.friUserId}`,
              icon: [
                <div className={styles.copy_icon_wrap}>
                  <span>{window.location.origin}/ConsultationModule/StartConsultation/Step2?consultationType={consultationType}&expertsUserId={expertsUserId}&copyUserId={UserInfo.friUserId}</span>
                  <img src={copy_icon} width={12} height={12} alt=""/>
                </div>,
                <div className={styles.copy_icon_wrap}>
                  <span>{window.location.origin}/ConsultationModule/StartConsultation/Step2?consultationType={consultationType}&expertsUserId={expertsUserId}&copyUserId={UserInfo.friUserId}</span>
                  <img src={copy_icon} width={12} height={12} alt=""/>
                </div>
              ],
              tooltips: ['', ''],
              onCopy: onCopy,
            }}></Typography.Paragraph>
          </div>
        </div>
        <div className={styles.block_right_wrap}>
          {/*<RightOutlined/>*/}
        </div>
      </div>

    </Popup>
  )
}

export default connect(({ consultation, loading }: any) => ({ consultation, loading }))(Index)
