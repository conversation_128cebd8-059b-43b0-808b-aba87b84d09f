.container {
  height: calc(57vh - 28px);
  position: relative;
  .title_box {
    position: relative;
    .title {
      font-size: 17px;
      color: #000;
      font-weight: 500;
      line-height: 24px;
      text-align: center;
    }
    .title_btn {
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      padding: 0 16px;
    }
  }
  .form_box {
    height: calc(70vh - 72px);
    padding-left: 16px;
    overflow-y: scroll;
    padding-bottom: 80px;
    .form_item {
      border-bottom: 1px solid #E1E4E7;
      padding-right: 16px;
      .label {
        font-size: 14px;
        color: #666;
        line-height: 20px;
        margin-right: 8px;
        width: 30%;
        flex-shrink: 0;
        .required_mark {
          color: #FF5F57;
        }
      }
    }

    .form_item_special {
      display: flex;
      flex-direction: column;
      padding: 16px 0;
      border-bottom: 1px solid #E1E4E7;

      :global {
        .ant-form-item {
          margin-bottom: 0;
        }

        .ant-form-item-control-input {
          height: 22px;
          min-height: auto;
        }

        .ant-form-item-explain-error {
          font-size: 12px;
          font-weight: 400;
          color: #FF5F57;
          line-height: 17px;
          text-align: right;
        }
      }

      .label {
        font-size: 14px;
        color: #666;
        line-height: 20px;
        margin-right: 8px;
        width: 30%;
        flex-shrink: 0;

        .required_mark {
          color: #FF5F57;
        }
      }

      .kingdom_form_box {
        display: flex;
        width: 100%;
        padding-right: 16px;
        align-items: flex-start;
        
      }

      .input_tips {
        font-size: 12px;
        font-weight: 400;
        color: #FF5F57;
        line-height: 17px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-right: 16px;
  
        img {
          width: 10px;
          height: 10px;
        }
      }
    }

    .form_item_inline {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .form_item_vertical {
      padding-top: 16px;
      .label {
        margin-bottom: 12px;
      }
    }

    .upload_kingdom_wrap {
      margin-top: 20px;
      padding-bottom: 30px;

      .upload_img_title {
        font-size: 14px;
        font-weight: 400;
        color: #666666;
        line-height: 16px;
        margin-bottom: 16px;
      }

      :global {
        .ant-spin-nested-loading {
          width: 80px;
        }
      }

      .upload_img_content {
        width: 80px;
        height: 80px;
        border-radius: 8px 8px 8px 8px;
        border: 1px dashed #CCCCCC;
        position: relative;
        &.upload_video_content {
          width: 80px;
          height: 80px;
        }
        .uploading_icon_content {
          text-align: center;
        }
        .uploading_icon_text {
          // text-align: center;
          color: #ccc;
        }
        .upload_video_input {
          width: 0;
          height: 0;
        }
        .upload_box {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
  
          .upload_img {
            width: 100%;
            height: 100%;
            border-radius: 8px 8px 8px 8px;
          }
  
          .init_upload_img {
            width: 30px!important;
            height: 30px!important;
            flex-shrink: 0;
          }
        }
  
        :global {
          .ant-upload-picture-card-wrapper {
            width: 80px;
            height: 80px;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 99;
          }
          .ant-upload.ant-upload-select-picture-card {
            width: 80px;
            height: 80px;
            display: block!important;
            opacity: 0;
          }
          
          .adm-image-uploader-upload-button-wrap .adm-image-uploader-upload-button {
            background: none;
            width: 80px;
            height: 80px;
          }
  
          .adm-space-item {
            width: 80px;
            height: 80px;
          }
  
          .adm-image-uploader-cell {
            position: relative;
            height: 80px;
  
            &.adm-image-uploader-upload-button {
              position: relative;
              &::after {
                content: '';
                width: 40px;
                height: 40px;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                margin: auto;
                background: url("https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/big_add.png") no-repeat;
                background-size: cover;
              }
            }
          }
  
          .adm-image-uploader-upload-button-icon {
            .antd-mobile-icon {
              display: none;
            }
          }
        }
      }
    }
    .king_value_box {
      display: flex;
      align-items: center;
      height: 52px;

      .user_name {
        font-size: 14px;
        color: #ccc;
        line-height: 24px;
        margin-right: 8px;
        word-break: break-all;

        &.user_name_selected {
          color: #000;
        }

        .select_wrap {
          display: flex;
          max-width: 200px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .no_comment_head{
          width: 24px;
          height: 24px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: 500;
          color: #fff;
          margin-right: 8px;
        }

        img {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          margin-right: 8px;
        }
      }
    }
    .name_value_box {
      :global {
        .adm-input {
          height: 100%;
        }
        .adm-input-element {
          height: 100%;
          text-align: right;
          font-size: 14px;
          color: #000;
        }
      }
      flex: 1;
    }
    .introduce_value_box {
      :global {
        .adm-text-area-element {
          font-size: 14px;
        }
        .adm-text-area-count {
          font-size: 13px;
          color: #ccc;
          padding-top: 0;
          line-height: 18px;
          padding-bottom: 12px;
        }
        .ant-form-item-explain-error {
          text-align: right;
          font-size: 12px;
          font-weight: 400;
          color: #FF5F57;
          line-height: 17px;
        }
      }
    }
  }


  .fixed_box {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding-bottom: constant(safe-area-inset-bottom);//兼容 IOS<11.2
    padding-bottom: env(safe-area-inset-bottom);//兼容 IOS>11.2
    .btn_box {
      padding: 16px;
      .btn {
        height: 40px;
        line-height: 40px;
        background: #0095FF;
        border-radius: 20px;
        text-align: center;
        font-size: 16px;
        color: #fff;
      }
    }

    .edit_btn_style {
      width: 100%;
      height: 58px;
      padding: 0 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-top: 1px solid #EEEEEE;

      .edit_cancel_btn {
        flex: 1;
        height: 40px;
        background: #EDF9FF;
        border-radius: 20px 20px 20px 20px;
        font-size: 16px;
        font-weight: 400;
        color: #0095FF;
        line-height: 19px;
        text-align: center;
        line-height: 40px;
        margin-right: 4px;
      }

      .edit_ok_btn {
        flex: 1;
        margin-left: 4px;
        height: 40px;
        background: #0095FF;
        border-radius: 20px 20px 20px 20px;
        font-size: 16px;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 40px;
        text-align: center;
      }
    }
  }
}

@media screen and (max-height: 500px) {
  .container{
    .fixed_box{
      display: none;
    }
  }  
}