.popup {
  :global {
    .adm-popup-body {
      border-radius: 16px 16px 0 0;
      padding-bottom: 34px;
    }
  }
}

.header {
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  .line {
    width: 48px;
    height: 4px;
    border-radius: 4px;
    background: #D0D4D7;
  }
}

.bottom_btn_wrap {
  width: 100%;
  display: flex;
  flex-wrap: nowrap;
  padding: 0 16px 12px;
  overflow-x: auto;
  background: #fff;
  :global {
    div.ant-typography {
      margin-bottom: 0;
      margin-left: 50px;
    }
    .ant-typography-copy {
      margin-left: 0;
    }
  }
  .btn_item {
    margin-left: 50px;
    width: 48px;
    display: flex;
    flex-direction: column;
    align-items: center;
    &:first-child {
      margin-left: 0;
    }
    img {
      margin-bottom: 8px;
    }
    p {
      font-size: 10px;
      color: #666;
      margin-bottom: 0;
      line-height: 14px;
      white-space: nowrap;
    }
  }
}

// 分享提示弹窗
.fixed_share_box {
  position: fixed;
  z-index: 1020;
  right: 21px;
  top: 24px;
  .icon1 {
    position: relative;
    display: block;
    background: url("../../../../assets/GlobalImg/arrow_up.png") no-repeat right center;
    background-size: 122px 108px;
    width: 100%;
    height: 108px;
  }
  .message_box {
    position: relative;
    font-size: 18px;
    font-weight: 400;
    line-height: 25px;
    color: #fff;
    white-space: nowrap;
    top: -18px;
    margin-bottom: 14px;
  }
  .icon_box {
    display: flex;
    align-items: center;
    justify-content: center;
    i {
      display: block;
      width: 48px;
      height: 48px;
      &.icon2 {
        background: url("../../../../assets/GlobalImg/share_one.png") no-repeat center;
        background-size: 100% 100%;
        margin-right: 24px;
      }
      &.icon3 {
        background: url("../../../../assets/GlobalImg/share_more.png") no-repeat center;
        background-size: 100% 100%;
      }
    }
  }
}
