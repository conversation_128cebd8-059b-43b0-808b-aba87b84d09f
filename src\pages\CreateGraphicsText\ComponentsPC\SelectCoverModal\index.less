.modal {
  :global {
    .ant-modal-header {
      border-bottom: 0;
    }
    .ant-modal-title {
      font-size: 20px;
      color: #000;
    }
    .ant-modal-close-x {
      font-size: 20px;
    }
    .ant-modal-body {
      padding: 0;
    }
  }
}
.content {
  padding: 13px 24px 24px 24px;

  .content_title {
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 14px;
  }

  .content_flex {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    padding-top: 16px;

    .warp_list_item {
      cursor: pointer;
      user-select: none;
      width: 163px;
      height: 96px;
      border-radius: 7px;
      border: 2px solid #fff;
      margin-bottom: 12px;
      overflow: hidden;
      position: relative;

      .CoverTitle {
        position: absolute;
        top: 18px;
        left: 12px;
        width: 96px;
        height: 31px;
        font-weight: 600;
        font-size: 13px;
        color: #FFFFFF;
        line-height: 15px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-word;
      }

      img {
        width: 161px;
        height: 94px;
        border-radius: 4px 4px 4px 4px;
        object-fit: cover;
        user-select: none;
        -webkit-user-drag: none;
      }
    }

    .warp_list_item_select {
      border: 2px solid #0095FF;
      position: relative;
      .warp_list_item_select_icon {
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url("~@/assets/GlobalImg/check_icon_2.png");
        background-size: 100% 100%;
        position: absolute;
        bottom: -2px;
        right: -2px;
      }
    }


    .warp_list_item_box {
      width: 100%;
      height: 100%;
      background: #F8F8F8;
      border-radius: 7px;
      display: flex;
      align-items: center;
      justify-content: space-around;
      .addiconBox {
        text-align: center;
        color: #CBCBCB;
      }
      .addicon {
        font-size: 18px;
      }
    }

    .warp_list_item_box_img_warp {
      width: 100%;
      height: 100%;
      background: #F8F8F8;
      border-radius: 7px;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

}
.footer {
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #ddd;
  padding: 21px 24px;
  :global {
    .ant-btn + .ant-btn {
      margin-left: 24px;
    }
  }
}
