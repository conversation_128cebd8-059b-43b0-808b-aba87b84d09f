/**
 * @Description: 退出王国弹窗
 */
import React from 'react'
import { connect } from 'umi'
import { Modal } from 'antd-mobile'
import styles from './index.less'
import WranningIcon from '@/assets/GlobalImg/wranning.png'

const Index: React.FC = (props: any) => {
  return (
    <Modal
      visible={props.visible}
      content={
        <div className={styles.container}>
          <div className={styles.title_box}>
            <img src={WranningIcon} width={20} height={20} alt=""/>
            <div className={styles.title}>您确定要退出王国吗?</div>
          </div>
          <div className={styles.btn_box}>
            <div className={styles.cancel} onClick={()=>props.onSubmit()}>狠心退出</div>
            <div className={styles.ok} onClick={()=>props.onCancel()}>我再想想</div>
          </div>
        </div>
      }
    >
    </Modal >
  )
}

export default connect(({ activity, loading }: any) => ({activity, loading}))(Index)
