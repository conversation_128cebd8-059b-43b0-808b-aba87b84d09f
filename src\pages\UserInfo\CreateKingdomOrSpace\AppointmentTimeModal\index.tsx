/**
 * @Description: 选择预约时间
 */
import { PickerView, Toast } from 'antd-mobile';
import { connect, history, useRouteMatch ,useAliveController } from 'umi';
import styles from './index.less';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs'
import goBackIcon from '@/assets/GlobalImg/go_back.png'; // 返回按钮小图标

const Index: React.FC = (props: any) => {
  const {path} = useRouteMatch();
  const { dispatch, goBack, userInfoStore } = props;
  const { selectCreateType, selectedKingdom, selectedCompere,appointmentStartTime, selectedGuest, spaceName, spaceCoverUrl, spaceAdvertisingUrl, spacePasswordText, spaceVideoId, spaceIntroduceVal, spaceFromEnter, isEditSpace,isAutoRecord,allowApplications,spaceCoursewares } = userInfoStore || {}; // 从仓库中取值
  const { creatTabSelectDate, spaceTypeId, spectatorType,  selectedKingdomAudience, enterpriseUserSelectData,starSpaceType } = userInfoStore || {};
  const { type } = creatTabSelectDate && creatTabSelectDate || {};
  const [ BasicColumns, setBasicColumns ] = useState<any[]>([]); // 预约时间列表选择相关数据（月日星期 时分）
  const [ timeStr, setTimeStr ] = useState<any[]>([dayjs().format('YYYY-MM-DD'), dayjs().format('HH'), dayjs().format('mm')]); // 预约时间
  const { drop,clear } = useAliveController();

  let CreateSpaceText =  selectCreateType == 14 ? '会议' : '直播';
  let isMeeting = selectCreateType == 14 // 判定是否是创建会议

  const generateBasicColumns = (isSelectToday: boolean | undefined) => {
    // 获取年前的日期
    let startOfYear = dayjs().startOf('year');
    // 获取今天的日期
    const today = dayjs();
    // 获取年底的日期
    const endOfYear = dayjs().add(1, 'year').endOf('year');
    // 日期集合
    const dateArray = []


    // 使用循环将日期添加到数组中
    while (startOfYear.isSameOrBefore(endOfYear)) {
      const isTodayByWeek =  startOfYear.format('YYYY-MM-DD') == dayjs().format('YYYY-MM-DD'); // 是否是今天
      const dddd = isTodayByWeek ? '今天' : startOfYear.format('dddd').replace('星期', '周'); // 星期替换为周

      dateArray.push({
        label: isTodayByWeek ? dddd : startOfYear.format('MM月DD日') + "  " + dddd,
        value: startOfYear.format('YYYY-MM-DD'),
      });
      startOfYear = startOfYear.add(1, 'day');
    }

    const currentHH = isSelectToday ? dayjs().format('HH') : '0';
    const hourArray = [];
    for (let i = 0; i <= 23; i++) {
      if(i >= +currentHH) {
        const hour = i.toString().padStart(2, '0');
        hourArray.push(hour);
      }
    }
    const currentmm = isSelectToday ? dayjs().format('mm') : '0';
    const minuteArray = [];
    for (let i = 0; i <= 59; i++) {
      if(i >= +currentmm) {
        const minute = i.toString().padStart(2, '0');
        minuteArray.push(minute);
      }
    }
    return [
      [...dateArray],
      [...hourArray],
      [...minuteArray],
    ]
  }

  useEffect(() => {
    // 初始化日期数据
    setBasicColumns(generateBasicColumns(false))
  },[])

  // 预约确定按钮
  const submitbtn = () => {
    // starSpaceType 创建的是直播还是会议 1:直播 2:会议
    if(starSpaceType == 2) {
      saveMeetTiem();
      return;
    }
    // isEditSpace 是否是编辑空间 默认 false
    if(isEditSpace) {
      // 编辑空间
      editSubmit()
    } else {
      // 创建空间
      appointmentStartSubmit()
    }
  }

  // 保存会议预约的开始时间
  const saveMeetTiem = ()=> {
    const time = timeStr && (timeStr[0]+' '+timeStr[1]+':'+timeStr[2]+':00'); // 选择的预约时间
    dispatch({
      type:'userInfoStore/setTaskListState',
      payload: {
        appointmentStartTime: time,
        createModalVisible:false,
      }
    })
  }

  // 编辑空间
  const editSubmit = () => {
    Toast.show({icon: 'loading'});
    const { tipText, spaceId, pageType, consultationId } = spaceFromEnter || {};
    const time = timeStr && (timeStr[0]+' '+timeStr[1]+':'+timeStr[2]+':00'); // 选择的预约时间
    const guestIdList = selectedGuest.map((it: { id: any; }) => it.id); // 嘉宾取id
    const UerInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    const idByspaceCoursewares = Array.isArray(spaceCoursewares) && spaceCoursewares.map((it: { spaceCoursewareId: any }) => it.spaceCoursewareId)

    if (pageType == 4) {
      dispatch({
        type: 'userInfoStore/editSpaceInfo',
        payload: {
          consultationId: pageType == '4' ? consultationId : undefined,
          id: spaceId, // 空间ID
          startStatus: 2, // 开始状态：1立刻、2预约
          appointmentStartTime: time, // 预约开始时间
          updateUserId: UerInfo && UerInfo.friUserId, // 操作人用户ID
          // spaceCoursewares:idByspaceCoursewares,
        }
      }).then((res: { code: number; content: any; msg: any;}) => {
        if(res && res.code == 200) {
          // 1. 关闭弹框
          // 2. Toast提示
          // 3. 刷新页面
          dispatch({
            type: 'userInfoStore/setTaskListState',
            payload: {
              createModalVisible: false
            }
          });
          Toast.show({content: tipText})
          clear().then(()=>{
            history.goBack();
          }).catch(()=>{
            history.goBack();
          })

        } else {
          Toast.show({content: res.msg || '数据加载失败'})
          return;
        }
      }).catch((err:any) => {
        console.log(err)
      })
      return
    }

    dispatch({
      type: 'userInfoStore/editSpaceInfo',
      payload: {
        consultationId: pageType == '4' ? consultationId : undefined,
        id: spaceId, // 空间ID
        name: spaceName, // 空间名称
        password: spacePasswordText || null, // 密码
        startStatus: 2, // 开始状态：1立刻、2预约
        appointmentStartTime: time, // 预约开始时间
        spaceCoverUrl: spaceCoverUrl && spaceCoverUrl.fileUrl || null, // 空间封面路径
        spaceCoverList: spaceCoverUrl ? [
          {
            spaceCoverUrl:spaceCoverUrl && spaceCoverUrl.fileUrl,  // string 必须 空间封面路径
            isSelect:1,                   // number 必须 是否选中封面
          }
        ] : [],  // 空间封面列表
        spaceAdvertisingUrl: spaceAdvertisingUrl && spaceAdvertisingUrl.fileUrl || null, // 空间广告路径
        updateUserId: UerInfo && UerInfo.friUserId, // 操作人用户ID
        guestIdList: guestIdList, // 嘉宾id集合
        vodFileId: spaceVideoId, // 空间视频id
        intro: spaceIntroduceVal, // 空间介绍
        spectatorType: spectatorType, // 观众类型 0默认所有人 1指定王国成员可见 2指定企业品牌用户可见
        spaceType: spaceTypeId || null, // 空间类型 1专家讲课 2指导 3复杂病例讨论
        spectatorOfKingdomList: selectedKingdomAudience, // 指定王国成员时传参
        spectatorOfBizList: enterpriseUserSelectData || null, // 指定企业品牌用户时传参
        starSpaceType: isMeeting ? 2 : 1,               // 类型：1 直播，2 会议
        spaceCoursewares:idByspaceCoursewares,
      }
    }).then((res: { code: number; content: any; msg: any;}) => {
      if(res && res.code == 200) {
        // 1. 关闭弹框
        // 2. Toast提示
        // 3. 刷新页面
        dispatch({
          type: 'userInfoStore/setTaskListState',
          payload: {
            createModalVisible: false
          }
        });
        Toast.show({content: tipText})
        clear().then(()=>{
          history.goBack();
        }).catch(()=>{
          history.goBack();
        })

      } else {
        Toast.show({content: res.msg || '数据加载失败'})
        return;
      }
    }).catch((err:any) => {
      console.log(err)
    })
  }

  // 预约开始时间确定按钮
  const appointmentStartSubmit = () => {
    Toast.show({icon: 'loading'});
    const { pageType, consultationId } = spaceFromEnter || {};
    const time = timeStr && (timeStr[0]+' '+timeStr[1]+':'+timeStr[2]+':00'); // 选择的预约时间（秒是死值 00）
    const guestIdList = selectedGuest.map((it: { id: any; }) => it.id); // 嘉宾取id
    const UerInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    const idByspaceCoursewares = Array.isArray(spaceCoursewares) && spaceCoursewares.map((it: { spaceCoursewareId: any }) => it.spaceCoursewareId)

    dispatch({
      type: 'userInfoStore/addSpace',
      payload: {
        consultationId: pageType == '4' ? consultationId : undefined, // 指导id (创建视频指导时，需必传)
        name: spaceName, // 空间名称
        wxUserId: selectedCompere && selectedCompere.id || UerInfo && UerInfo.friUserId, // 主持人id
        hostName: selectedCompere && selectedCompere.name || UerInfo && UerInfo.name, // 主持人名称
        kingdomId: selectedKingdom && selectedKingdom.id || null, // 王国id
        kingdomName: selectedKingdom && selectedKingdom.name || null, // 王国名称
        password: spacePasswordText || null, // 密码
        startStatus: 2, // 开始状态：1立刻、2预约
        appointmentStartTime: time, // 预约开始时间
        spaceCoverUrl: spaceCoverUrl && spaceCoverUrl.fileUrl || null, // 空间封面路径
        spaceCoverList: spaceCoverUrl ? [
          {
            spaceCoverUrl:spaceCoverUrl && spaceCoverUrl.fileUrl,  // string 必须 空间封面路径
            isSelect:1,                   // number 必须 是否选中封面
          }
        ] : [],  // 空间封面列表
        spaceAdvertisingUrl: spaceAdvertisingUrl && spaceAdvertisingUrl.fileUrl || null, // 空间广告路径
        updateUserId: UerInfo && UerInfo.friUserId, // 操作人用户ID
        guestIdList: guestIdList, // 嘉宾id集合
        vodFileId: spaceVideoId, // 空间视频id
        intro: spaceIntroduceVal, // 空间介绍
        spectatorType: type == 2 ? 1 : spectatorType, // 观众类型 0默认所有人 1指定王国成员可见 2指定企业品牌用户可见
        spaceType: spaceTypeId || null, // 空间类型 1专家讲课 2指导 3复杂病例讨论
        spectatorOfKingdomList: type == 2 ? [selectedKingdom && selectedKingdom.id] : selectedKingdomAudience, // 指定王国成员时传参
        spectatorOfBizList: enterpriseUserSelectData || null, // 指定企业品牌用户时传参
        isAutoRecord:isAutoRecord ? 1 : 0, // 是否开启自动录播 1:开启  0或其他关闭
        starSpaceType: isMeeting ? 2 : 1,               // 类型：1 直播，2 会议
        isNoPasswordApply:  allowApplications ? 1 : 0,  // 是否开启无密码申请：0不开启，1开启
        spaceCoursewares:idByspaceCoursewares, // 课件id集合
      }
    }).then((res: { code: number; content: any; msg: any}) => {
      if(res && res.code == 200) {
        if (pageType == '4') {
          // 关闭弹框
          history.go(-1);
          return
        }
        // 是在王国详情中创建的王国空间
        if(spaceFromEnter?.isJumpOrRefresh) {
          // 关闭弹框
          dispatch({
            type: 'userInfoStore/setTaskListState',
            payload: {
              createModalVisible: false
            }
          });
          // 提示Toast
          Toast.show({content: spaceFromEnter?.tipText})
          history.goBack();
        } else {
          // 关闭弹框并跳转创建空间成功页
          dispatch({
            type: 'userInfoStore/setTaskListState',
            payload: {
              createModalVisible: false
            }
          })
          // 判断如果是成功页，则使用replace，成功页只展示一次，不允许返回
          let historyJump = history.replace
          historyJump({
            pathname: '/UserInfo/SpaceSuccessPage',
            query: {
              id: res.content,
              starSpaceType: isMeeting ? 2 : 1,
            }
          });
        }
      } else {
        Toast.show({content: res.msg || '数据加载失败'})
      }
    })
  }

  useEffect(() => {
    const savedTime = dayjs(appointmentStartTime || null);
    const currentTime = dayjs();
    const initTime = savedTime > currentTime ? savedTime : currentTime;
    setTimeStr([initTime.format('YYYY-MM-DD'), initTime.format('HH'), initTime.format('mm')])
  }, [appointmentStartTime]);

  const { pageType } = spaceFromEnter || {};
  return (
    <>
      <div className={styles.titleInfo}>
        <div className={styles.titleWarp}>
          {
            <div className={styles.titleBackIcon}
                                  onClick={()=>{
                                    dispatch({
                                      type: 'userInfoStore/setTaskListState',
                                      payload: { createModalVisible:false, }
                                    })
                                  }}>
              <img src={goBackIcon} alt="" />
            </div>
          }

          <div className={styles.titleText}>预约开始时间</div>
        </div>
      </div>
      <div className={styles.PickerViewDataByWarp}>
        <PickerView
          columns={BasicColumns}
          defaultValue={[dayjs().format('YYYY-MM-DD'), dayjs().format('HH'), dayjs().format('mm')]}
          onChange={(value, extend)=>{
            // 选择了过去的天
            if (dayjs(value[0]).isBefore(dayjs(), 'day')) {
              Toast.show('不可选择过去的时间');
              setTimeStr([dayjs().format('YYYY-MM-DD'), dayjs().format('HH'), dayjs().format('mm')])
              return;
            }

            // 选择了过去的小时
            if (dayjs(value[0]).isSame(dayjs(), 'day') && value[1] && +value[1] < dayjs().get('hour')) {
              Toast.show('不可选择过去的时间');
              setTimeStr([dayjs().format('YYYY-MM-DD'), dayjs().format('HH'), dayjs().format('mm')])
              return;
            }

            // 选择了过去的分钟
            if (dayjs(value[0]).isSame(dayjs(), 'day') &&
              value[1] && +value[1] == dayjs().get('hour') &&
              value[2] && +value[2] < dayjs().get('minute')
            ) {
              Toast.show('不可选择过去的时间');
              setTimeStr([dayjs().format('YYYY-MM-DD'), dayjs().format('HH'), dayjs().format('mm')])
              return;
            }
            setTimeStr(value)
          }}
          value={timeStr}
          style={{
            '--item-font-size': '20px',
          }}
        />
      </div>

      <div className={styles.btnAppointmentStartWarp}>
        <div
          className={styles.btnAppointmentStart}
          onClick={submitbtn}
        >
          {isEditSpace ? '保存' : '确认'}
        </div>
      </div>
    </>
  )
}
export default connect(({ userInfoStore, loading }: any) => ({ userInfoStore, loading }))(Index)
