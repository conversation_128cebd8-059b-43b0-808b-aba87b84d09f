@import '../../../../utils/imageText.less';
.spin {
  height: 100%;
  :global {
    .ant-spin-container {
      height: 100%;
    }
  }
}
:global {
  .ka-wrapper, .ka-content {
    height: 100%;
  }
}
.nav_bar_btn {
  display: block;
  width: 54px;
  height: 29px;
  background: #0095FF;
  font-size: 15px;
  color: #fff;
  line-height: 30px;
  border-radius: 15px;
  text-align: center;
}

.container {
  padding: 44px 0 20px;
  height: calc(100% - 45px);
  overflow-y: auto;
  .editor_box {
    margin-bottom: 20px;
    :global {
      .ql-editor {
        min-height: 138px !important;
        padding: 16px 12px !important;
      }
      .ql-editor.ql-blank::before {
        left: 12px;
      }
    }
  }
}
.container.show_panel {
  height: calc(100% - 45px - 300px);
}


.forward_content {
  background: #f8f8f9;
  padding: 12px;
}

.post_content {
  padding: 8px;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  border: 1px solid #EBEBEB;
  border-radius: 4px;
  overflow: hidden;
  :global {
    .ql-editor {
      font-size: 14px !important;
      color: #333333 !important;
      line-height: 20px !important;
    }
  }
  .init_img {
    width: 68px;
    height: 68px;
    border-radius: 4px;
    font-size: 30px;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    flex-shrink: 0;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
  }

  .text {
    flex: 1;
    font-size: 14px;
    color: #333333;
    line-height: 18px;
    word-break: break-all;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3; /* 指定显示三行 */
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.space_wrap {
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #EBEBEB;
  display: flex;
  flex-wrap: nowrap;
  .left_cover_image {
    flex-shrink: 0;
    margin-right: 8px;
    width: 68px;
    height: 68px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    line-height: 68px;
    color: #fff;
    font-size: 30px;
    text-align: center;
    white-space: nowrap;
  }
  .right {
    flex: 1;
    overflow: hidden;
    .space_title {
      font-size: 13px;
      color: #000;
      font-weight: 500;
      line-height: 18px;
      word-break: break-all;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      text-overflow: ellipsis;
    }
    .space_introduce {
      font-size: 12px;
      color: #333;
      line-height: 16px;
      word-break: break-all;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      text-overflow: ellipsis;
    }
  }
}

