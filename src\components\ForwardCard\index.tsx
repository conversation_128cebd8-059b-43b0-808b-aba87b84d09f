/**
 * 转发卡片-组件
 */
import React, { useEffect, useRef, useState } from 'react'
import { history, connect } from 'umi'
import classNames from 'classnames'
import { randomColor , processNames, gdpFormat } from '@/utils/utils'
import styles from './index.less'
import ListComments from '../ListComments'; // 评论组件
import ArticleCard from '../ArticleCard'; // 文章组件
import { useInView } from 'react-intersection-observer';
import {stringify} from "qs";  // 判断元素是否可见插件（作用：用户停留在某一内容超过3秒，展示评论输入框）
import Avatar from '@/components/Avatar';
import ExternalLinkCard from "@/components/ExternalLinkCard"; // 用户头像组件
interface PropsType {
  style?: any;    // 样式
  pageType?: any; //  从哪个页面过来的标识，推荐首页过来传 1:场推荐 2:话题页面 3:我的主页草稿箱 4:国王详情 5:搜索页面
  item? :any;     // 转发的内容
  refreshDataById? :any; // 刷新指定id的数据
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {style,pageType,item} = props || {};
  let {
    createDate,       //: [创建时间] : "2024-01-09 14:53:47"
    createUserId,     //: [创建人id] : 60
    expertsInfo,      //: [专家信息] : null
    forwardDescribe,  //: [转发描述] : null
    gdp,              //: [页面GDP] : 210
    headUrlShow,      //: [用户头像] : null
    id,               //: [主键ID] : 57
    imageTextContent, //: [文章、帖子内容] : null
    imageTitle,       //: [标题] :null
    imageType,        //: [图文类型：1.文章 2.帖子 3.外链 4.空间] : 2
    isExperts,        //: [是否是专家：0:否，1:是] : 0
    isFocus,          //: [0未关注 1已关注] : 0
    isForward,        //: [是否转发：1.转发 0，非转发] : null
    isSpotLike,       //: [是否点赞 1是 0否] : 0
    kingdomId,        //: [关联王国ID] : 1
    kingdomName,      //: [关联王国名称] : "数字化讨论"
    outerChain,       //: [外链地址] : null
    spaceId,          //: [空间ID] :null
    spaceStatus,      //: [空间状态: 1直播中、2预约中、3弹幕轰炸中] : null
    spotLikeCount,    //: [点赞数量] : 0
    spotLikeUserList, //: [点赞用户信息，最多3条] : []
    // textImgList,      //: [关联的图片] : null
    topicInfoList,    //: [关联的话题信息] : null
    userName,         //: [用户名称] : "志君"
    operateDateDescs,
    forwardSquareRecommendDto:forwardDto, // 被转发图文信息
  } = item || {};
  forwardDto = forwardDto ? forwardDto : {};
  const {
    textImgList,
  } = forwardDto || {};

  // 元素是否可见的配置
  const { ref, inView } = useInView({
    threshold: 0.6,
  });
  const [showComments , setShowComments] = useState(false); // 是否展示评论框
  const timer = useRef<NodeJS.Timeout| null>(null); // 定时器

  useEffect(() => {
    // 是推荐首页,则展示评论框
    if(pageType == '1') {
      // 判断当前内容是否可见,并停留3秒时,展示评论框
      if (inView) {
        timer.current = setTimeout(() => {
          console.log('显示')
          setShowComments(true);
          if (timer.current) {
            clearTimeout(timer.current);
          }
        }, 3000)
        return;
      }
      setShowComments(false);
      if (timer.current) {
        clearTimeout(timer.current);
      }
      return;
    } else {
      setShowComments(false);
    }
  }, [inView]);

  // 点击评论跳转详情
  const jumpDetailsFn = (e) => {
    e.stopPropagation()
    // 图文类型：1.文章 2.帖子 3.外链 4.空间
    // 帖子详情
    if(forwardDto.imageType == 2) {
      return history.push(`/CreateGraphicsText/PostDetails?${stringify({id:forwardDto.id})}`);
    }
    // 空间卡片
    if(forwardDto.imageType == 4) {
      return history.push(`/CreateGraphicsText/SpaceDetails?${stringify({id:forwardDto.id})}`)
    }
    // 文章卡片
    if(forwardDto.imageType == 1) {
      return history.push(`/CreateGraphicsText/ArticleDetails?${stringify({id:forwardDto.id})}`);
    }
  }

  // 跳转到转发详情
  const goForwardDetails = (e) =>{
    e.stopPropagation();
    if (e.target && e.target.dataset && e.target.dataset.type == 'topic') {
      // 话题，老版结构
      // 如果当前在话题主页，并且显示的就是点击的话题
      if (history.location.pathname == '/CreateGraphicsText/TopicHome' && e.target.dataset.id == history.location.query.topicId) {
        return
      }
      history.push(`/CreateGraphicsText/TopicHome?topicId=${e.target.dataset.id}`)
    } else if (e.target && e.target.parentNode && e.target.parentNode.dataset && e.target.parentNode.dataset.type == 'topic') {
      // 话题，新版结构
      // 如果当前在话题主页，并且显示的就是点击的话题
      if (history.location.pathname == '/CreateGraphicsText/TopicHome' && e.target.parentNode.dataset.id == history.location.query.topicId) {
        return
      }
      history.push(`/CreateGraphicsText/TopicHome?topicId=${e.target.parentNode.dataset.id}`)
    } else if (e.target && e.target.dataset && e.target.dataset.type == 'user') {
      history.push(`/Expert/ExpertDetails?id=${e.target.dataset.id}`)
    }  else {
      return history.push(`/CreateGraphicsText/ForwardDetails?${stringify({id: id})}`);
    }
  }

  // 空间内容，点击用户
  const onClickUserName = (e) => {
    e.stopPropagation()
    e.preventDefault()
    history.push(`/Expert/ExpertDetails?id=${forwardDto.createUserId}`)
  }


  return (
    <div className={styles.content} ref={ref}>
      <div onClick={goForwardDetails} className={styles.forward_box}>
        <div className={styles.forward_head}>
          <Avatar
            userInfo={{
              userId:createUserId,
              name:userName,
              headUrlShow:headUrlShow,
            }}
            size={16}
          ></Avatar>
          <span className={styles.forward_name}>{userName}</span>
          {isExperts == 1 &&
            <div className={styles.expertCertificationIconWarp}><i className={styles.expertCertificationIcon}></i></div>
          }
          {operateDateDescs && <span className={styles.operateDateDescs}>{operateDateDescs}·</span>}
          转发了
        </div>
      </div>

      <div onClick={goForwardDetails} className={styles.forward_text}>
        <div className={'ql-editor'} dangerouslySetInnerHTML={{__html: forwardDescribe}}></div>
      </div>

      {/* 判定是否 */}
      {!!forwardDto && forwardDto.id ?
        <div onClick={jumpDetailsFn}  className={styles.forward_card}>
          {/* [imageType图文类型：1.文章 2.帖子 3.外链 4.空间] */}
          {
            forwardDto.imageType == 2 &&
            <>
              {/* 帖子卡片 */}
              <div className={styles.post_content}>
                <div
                  className={styles.init_img}
                  style={
                    forwardDto.textImgList && forwardDto.textImgList[0] ?
                      {backgroundImage: `url(${forwardDto.textImgList[0].imageUrlShow})`}
                      : forwardDto.headUrlShow ?
                      {backgroundImage: `url(${forwardDto.headUrlShow})`}
                      : {background: randomColor(forwardDto.createUserId)}
                  }
                >
                  {
                    forwardDto.textImgList && forwardDto.textImgList[0] || forwardDto.headUrlShow ? null
                      : processNames(forwardDto.userName)
                  }
                </div>
                <div
                  className={classNames('ql-editor', styles.text)}
                  dangerouslySetInnerHTML={{__html: forwardDto.imageTextContent}}
                ></div>
              </div>
            </>
          }
          {
            forwardDto.imageType === 4 &&
            <>
              {/* 空间卡片 [imageType图文类型：1.文章 2.帖子 3.外链 4.空间]  */}
              <div className={styles.space_forward_title}>
                <span onClick={onClickUserName}>@{forwardDto.userName}</span>
                {forwardDto.starSpaceType == 2 ? "发布了一条会议，快来一起聊天~" : "发布了一场直播，快来一起聊天~"}
              </div>
                <div className={styles.space_list_content}>
                <div className={styles.space_img_box}>
                  {/* 无封面时，展示主持人默认头像 */}
                  {
                    Array.isArray(forwardDto.textImgList) && forwardDto.textImgList.length > 0 ?
                      <div className={styles.space_init_img}>
                        {/* 封面中的标题 */}
                        {forwardDto.isTemplateCover == 1 && <div className={styles.title_in_cover_image}>{forwardDto.imageTitle}</div>}
                        <img src={forwardDto.textImgList[0].imageUrlShow} />
                      </div> : <div
                      className={styles.space_init_img}
                      // style={item.hostImgUrlShow ? {backgroundImage: `url(${item.hostImgUrlShow})`} : {background: randomColor(item.wxUserId)}}
                      style={{background: randomColor(forwardDto.createUserId)}}
                    >{processNames(forwardDto.userName)}</div>
                  }

                </div>
                <div className={styles.space_info_box}>
                  <div className={styles.space_title}>{forwardDto.imageTitle}</div>
                  <div className={styles.space_introduce}>{forwardDto.imageTextContent}</div>
                </div>
              </div>
            </>
          }
          {
            forwardDto.imageType === 1 &&
            <>
              {/* 文章卡片 [imageType图文类型：1.文章 2.帖子 3.外链 4.空间] */}
              <ArticleCard
                style={{padding: 0, background: 'none'}}
                item={forwardDto}
                pageFrom={'forwardCard'} // 是否转发：1.转发 0，非转发
              />
            </>
          }
        </div> :
        <div className={styles.forward_card}>
          <div className={styles.forward_card_removeOriginalContent}>
            原内容已下架
          </div>
        </div>
      }

      <div className={styles.comment_box}>
        <ListComments
          showComments={showComments}
          pageType={pageType}
          commentJupm={goForwardDetails}
          item={item}
        />
      </div>
    </div>
  )
}

export default connect(({ activity, loading }: any) => ({activity, loading}))(Index)
