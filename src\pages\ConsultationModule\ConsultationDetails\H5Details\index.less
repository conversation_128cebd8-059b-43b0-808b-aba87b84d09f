.wrap{
  background: #F5F6F8;
  height: 100vh;
  position: relative;
  padding-bottom: constant(safe-area-inset-bottom);//兼容 IOS<11.2
  padding-bottom: env(safe-area-inset-bottom);//兼容 IOS>11.2\
}
.wrap_bottom{
  background: #F5F6F8;
  // height:  calc(100vh - 91px);
  height:  calc(100vh - 28px);
  position: relative;
}
.end_consultation{
  position: fixed;
  top: 8px;
  right: 16px;
  font-size: 16px;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #FF5F57;
  z-index: 9990;
  .end_span{
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 4px;
    &>img{
      width: 100%;
      height: 100%;
    }
  }
}
.header{
  width: 100%;
  background: #FFFFFF;
  height: 44px;
  position: relative;
  
}
.case_header{
  position: sticky;
  top: 44px;
  left: 0;
  width: 100%;
  background: #FFFFFF;
  height: 54px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  z-index: 999;
  .left{
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 30px;
    font-size: 16px;
    font-weight: 500;
    color: #000;
  }
  .right{
    display: inline-block;
    width: 80px;
    font-size: 14px;
    font-weight: 400;
    color: #0095FF;
  }
}
.content_tips {
  height: 31px;
  background: #FFF7DA;
  opacity: 1;
  border: 1px solid #EFD989;
  font-size: 12px;
  font-weight: 400;
  color: #8C772B;
  text-align: center;
  line-height: 31px;
}
.progress_bar{
  background: #FFFFFF;
}
// 内容区域正常高度
.content{
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding-bottom: 16px;
}

.progress_bar{
  background: #FFFFFF;
}
.chat_bottom{
  width: 100%;
  border-top: 1px solid #EEEEEE;
  // height: 58px;
  background: #fff;
  box-shadow: 5px 10px 49px 1px rgba(53,51,97,0.17);
  backdrop-filter: blur(8px);
  display: flex;
  position: fixed;
  bottom: 0;
  z-index: 999;
}
.chat_topping_icon{
  position: absolute;
  bottom: 134px;
  right: 16px;
  width: 46px;
  height: 46px;
  background: #FFFFFF;
  border-radius: 50%;
  &>img{
    width: 100%;
    height: 100%;
  }
}
// 其他（表情包、上传图文、视频）内容高度变化
.chat_bottom_show{
  position: static;
  height: 262px;
}
.topping_icon{
  position: absolute;
  bottom: 42px;
  right: 16px;
  width: 46px;
  height: 46px;
  background: #FFFFFF;
  border-radius: 50%;
  &>img{
    width: 100%;
    height: 100%;
  }
}

.footer {
  width: 100%;
  border-top: 1px solid #EEEEEE;
  background: #FFFFFF;
  box-shadow: 5px 10px 49px 1px rgba(53, 51, 97, 0.17);
  backdrop-filter: blur(8px);
  // display: flex;
  position: fixed;
  bottom: 0;
  .footer_content {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 9px 16px 9px 1px;

    &>p {
      margin-bottom: 0;
    }

    .reserved_conference {
      border-radius: 20px 20px 20px 20px;
      opacity: 1;
      border: 1px solid #0095FF;
      padding: 8px 0;
      font-size: 16px;
      font-weight: 400;
      color: #0095FF;
      width: 50%;
      text-align: center;
      margin-left: 15px;
    }

    .chat_end_consultation {
      width: 50%;
      margin-left: 15px;
      background: #0095FF;
      border-radius: 20px 20px 20px 20px;
      font-size: 16px;
      font-weight: 400;
      color: #FFFFFF;
      padding: 9px 0;
      text-align: center;
    }
    .cancel_a_meeting{
      background: #0095FF;
      border-radius: 20px 20px 20px 20px;
      font-size: 16px;
      font-weight: 400;
      color: #FFFFFF;
      padding: 9px 0;
      text-align: center;
      margin-left: 16px;
      margin-bottom: 0;
    }
  }
}
.chat_mask{
  width: 100%;
  height: 100vh;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 99;
  background: #fff;
  opacity: 0.1;
}