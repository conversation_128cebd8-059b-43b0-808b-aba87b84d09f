.popup_container {
  :global {
    .adm-popup-body {
      border-radius: 16PX 16PX 0 0;
    }
  }
}

.container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: nowrap;
  flex-direction: column;
}

// 头部
.header_line {
  flex-shrink: 0;
  width: 100%;
  height: 28PX;
  display: flex;
  align-items: center;
  justify-content: center;
  .header_line_bar {
    width: 48PX;
    height: 4PX;
    background: #D0D4D7;
    border-radius: 4PX;
  }
}

.content {
  padding: 16PX 0 50PX 16PX;
  .meeting_name {
    font-weight: 500;
    font-size: 17PX;
    color: #000;
    line-height: 24PX;
    margin-bottom: 8PX;
    word-break: break-all;
    padding-right: 16PX;
  }
  .meeting_info {
    border-bottom: 1PX solid #E1E4E7;
    padding: 16PX 16PX 16PX 0;
    display: flex;
    flex-wrap: nowrap;
    font-size: 14PX;
    line-height: 20PX;
    .meeting_info_label {
      flex-shrink: 0;
      width: 64PX;
      color: #666;
    }
    .meeting_info_value {
      flex: 1;
      color: #333;
      word-break: break-all;
      text-align: right;
    }
  }
}


