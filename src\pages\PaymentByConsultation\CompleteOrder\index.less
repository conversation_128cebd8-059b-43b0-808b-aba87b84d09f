.Mobile_Wrap {
  width: 100%;
  min-width: 285px;
  height: 100vh;
  background: #F5F6F8;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  // padding-left: 12px;
  // padding-right: 12px;

  .Mobile_title_statusbar {
    width: 100%;
    height: 44px;
  }

  .Mobile_title_Wrap {
    width: 100%;
    height: 44px;
    margin-bottom: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: transparent;

    .Mobile_title {
      font-size: 17px;
      font-weight: 500;
      color: #000000;
    }
  }

  .Mobile_box_info {
    width: 100%;
    min-height: 147px;
    background: #FFFFFF;
    border-radius: 0px 0px 0px 0px;
    opacity: 1;
    .Mobile_box_title {}
  }

  .Warp_box {
    width: 100%;
    margin-top: 30px;

    .box_payment {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      margin-bottom: 56px;

      .Payment_result_icon_Success {
        width: 72px;
        height: 72px;
        background: url("../../../assets/Payment/CompleteOrder_Success_payment.png") no-repeat;
        background-size: 72px 72px;
        margin-bottom: 24px;
      }

      .Payment_result_icon_fail {
        width: 72px;
        height: 72px;
        background: url("../../../assets/Payment/CompleteOrder_Fail_payment.png") no-repeat;
        background-size: 72px 72px;
        margin-bottom: 24px;
      }

      .payment_title {
        font-size: 18px;
        font-weight: 500;
        color: #000000;
        line-height: 18px;
      }
    }
  }

  .Warp_btn {
    padding-left: 51px;
    padding-right: 51px;
    display: flex;
    justify-content: space-around;

    .goBack_btn {
      width: 112px;
      height: 40px;
      background: #EDF9FF;
      border-radius: 20px 20px 20px 20px;
      opacity: 1;
      font-size: 16px;
      font-weight: 400;
      color: #0095FF;
      line-height: 40px;
      text-align: center;
    }
    .goBack_btn:active {
      opacity: 0.8;
    }

    .ViewDetails_btn {
      width: 144px;
      height: 40px;
      line-height: 40px;
      background: #0095FF;
      border-radius: 20px 20px 20px 20px;
      opacity: 1;
      font-size: 16px;
      font-weight: 400;
      color: #FFFFFF;
      text-align: center;
    }
    .ViewDetails_btn:active {
      opacity: 0.8;
    }
  }
}
