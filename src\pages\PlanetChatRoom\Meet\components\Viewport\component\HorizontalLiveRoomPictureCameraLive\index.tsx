import React, {useEffect, useState,} from 'react';
import {connect} from 'umi';
import styles from './index.less';
import classNames from 'classnames';
import Stream from '@/componentsByTRTC/Stream';
import {randomColor} from '@/utils/utils';
import Avatar from '@/pages/PlanetChatRoom/components/Avatar';
import {
  getCameralistArr,
  getHandUpRemoteStreamList,
  getHostRemoteStreamConfig,
  getIsModeMatrixCameraRemoteStreamList,
  getShareRemoteStreamConfig,
  getUserCameraRemoteStreamList,
  getUserInfoData,
} from '@/utils/utilsByTRTC';

type propsType = {
  global: any;
};

function sortRemoteStreamConfig(data, speakers = []) {
  if (!Array.isArray(data)) {
    return null;
  }
  let dataCopy = [];
  data.map((item) => {
    dataCopy.push(item);
  });
  return dataCopy.sort((a, b) => {
    if (speakers.includes(a.userID) && !a.mutedAudio && !b.mutedAudio) {
      return -1;
    }
    if (speakers.includes(b.userID) && !a.mutedAudio && !b.mutedAudio) {
      return 1;
    }
    if (a.mutedAudio > 0 && b.mutedAudio <= 0) {
      return -1;
    }
    if (a.mutedAudio <= 0 && b.mutedAudio > 0) {
      return 1;
    }
    return b.audioVolume - a.audioVolume;
  });
}

const Index: React.FC<propsType> = (props) => {
  const {
    localStreamConfig,
    RTC,
    remoteStreamConfigList,
    PlanetChatRoom,
    dispatch,
    isHorizontalLive,
  } = props || {};

  const {
    msgListBySENDAPPLAUSE,
    guestListInfo,
    SpaceInfo,
    signInList,
    signInObj,
    BookingList,
    BookingObj,
    handUpList,
    currentUserType,
    isMobile,
    isInitialized,
    currentLiveUserList,
    msgListByBULLETSCREEN,
    lastSeq,
    newMySnedSeq,
    playerInfo,
    HiddenDanmu,
    sendFlowersCount,
    sendApplauseCount,
    HomePageLink,
    ModalVisibleBySpaceViolation,
    ModalVisibleBySpaceRemoved,
    ModalVisibleByCancelAppointment,
    ModalVisibleByClosedSpace,
    ModalVisibleByAcceptLienMai,
    ModalVisibleByAppointmentClosedSpace,
    ModalVisibleByStartLive,
    ModalVisibleByNoMicrophone,
    ModalVisibleByOrientationWrong,
    ModalVisibleByVerticalPageWrong,
    ModalVisibleByLeaveMicrophone,
    ModalVisibleByForceWheat,
    ModalVisibleByEndRecording,
    ModalVisibleByKickedOut,
    autoExpandGuestArea,
    isModeMatrix,
    isShowCommentArea,
    isNotLogin,
    currentWatchMode,
    applyAdmissionList,
    isOpenTEduBoard,
  } = PlanetChatRoom || {};

  const {
    wxUserId,
    imUserId,
    userSig,
    hostUserInfo,
    name: nameBySpaceInfo,
    kingdomName,
    status: statusBySpaceInfo,
    spaceAdvertisingUrlShow,
    isSignIn,
    isCollect,
    isAppointment,
    appointmentStartTime,
    handUpType,
    recordType,
    handUpStatusType,
    videoList,
    pv,
    gdp,
    videoSecond,
    spaceCoverUrlShow,
    kingdomId,
    isNeedPwd,
    starSpaceType,
  } = SpaceInfo || {};

  const [isShowCameraList, setIsShowCameraList] = useState(false);
  const [speakers, setSpeakers] = useState([]);
  const [randomNum, setRandomNum] = useState(Math.random());
  const userInfoData = getUserInfoData();
  const shareRemoteStreamConfig = getShareRemoteStreamConfig(SpaceInfo, remoteStreamConfigList);
  const hostRemoteStreamConfig = getHostRemoteStreamConfig(
    SpaceInfo,
    hostUserInfo,
    remoteStreamConfigList,
  );
  const userCameraRemoteStreamList = getUserCameraRemoteStreamList(
    SpaceInfo,
    remoteStreamConfigList,
  );
  const isModeMatrixCameraRemoteStreamList = getIsModeMatrixCameraRemoteStreamList(
    SpaceInfo,
    hostUserInfo,
    handUpList,
    userCameraRemoteStreamList,
  );
  const handUpRemoteStreamList = getHandUpRemoteStreamList(
    SpaceInfo,
    handUpList,
    userCameraRemoteStreamList,
  );
  let cameralistArr = getCameralistArr(
    hostRemoteStreamConfig,
    localStreamConfig,
    handUpRemoteStreamList,
    isModeMatrixCameraRemoteStreamList,
  );
  let userCameraRemoteStreamListSort =
    Array.isArray(userCameraRemoteStreamList) &&
    sortRemoteStreamConfig(userCameraRemoteStreamList, speakers);
  let isUserSpeak =
    SpaceInfo &&
    Array.isArray(remoteStreamConfigList) &&
    remoteStreamConfigList.find(
      (item) => item.userID.indexOf('share') == -1 && item.audioVolume > 0,
    );

  useEffect(() => {
    Array.isArray(userCameraRemoteStreamList) &&
    userCameraRemoteStreamList.length > 0 &&
    setRandomNum(Math.random());
  }, [Array.isArray(userCameraRemoteStreamList) && userCameraRemoteStreamList.length]);
  useEffect(() => {
    if (!!isUserSpeak) {
      setSpeakers([...speakers, isUserSpeak.userID]);
    }
  }, [isUserSpeak && isUserSpeak.userID]);

  return (
    <>
      {!!props.PlanetChatRoom.isLive &&
        RTC &&
        RTC.isJoined &&
        !isModeMatrix &&
        !!shareRemoteStreamConfig && (
          <div
            className={classNames({
              [styles.HorizontalLiveRoom_camera_picture_Box_VerticalLiveRoom]: !isHorizontalLive,
              [styles.HorizontalLiveRoom_camera_picture_Box]: !!isHorizontalLive,
              [styles.isShowCameraList]: isShowCameraList,
            })}
          >
            <div
              onClick={() => {
                setIsShowCameraList(!isShowCameraList);
              }}
              className={classNames({
                [styles.HorizontalLiveRoom_camera_picture_btn]: true,
                [styles.HorizontalLiveRoom_camera_picture_take_back_btn]: isShowCameraList,
              })}
            />
            <div className={styles.HorizontalLiveRoom_camera_picture_camera_live}>
              {!localStreamConfig &&
                (!Array.isArray(userCameraRemoteStreamList) ||
                  (Array.isArray(userCameraRemoteStreamList) &&
                    userCameraRemoteStreamList.filter((item) =>
                      !shareRemoteStreamConfig ? item.userID != hostUserInfo.imUserId : true,
                    ).length == 0)) && (
                  <div className={styles.noGuestsListWarp}>
                    <div className={styles.noGuestsListIcon}>
                      <i className={styles.noGuestsIcon}/>
                      <div className={styles.noGuestsText}>暂无嘉宾进入</div>
                    </div>
                  </div>
                )}
              {localStreamConfig && RTC?.isPublished && localStreamConfig.hasVideo && (
                <>
                  {(currentUserType == 1 ? !!shareRemoteStreamConfig : true) ? (
                    <div
                      style={{order: 1}}
                      className={classNames({
                        [styles.HorizontalLiveRoom_camera_picture_camera_item]: true,
                        [styles.HorizontalLiveRoom_camera_picture_camera_item_hidden]:
                        localStreamConfig && localStreamConfig.mutedVideo,
                      })}
                    >
                      <div
                        className={classNames({
                          [styles.StreamWarp]: true,
                          [styles.StreamWarpHidden]:
                          localStreamConfig && localStreamConfig.mutedVideo,
                        })}
                      >
                        <Stream
                          key={`${localStreamConfig?.stream?.getUserId()}_${localStreamConfig?.stream?.getType()}_HorizontalLiveRoomPictureCameraLive_${randomNum}`}
                          stream={localStreamConfig.stream}
                          config={localStreamConfig}
                          init={(dom) => RTC && RTC.playStream(localStreamConfig.stream, dom)}
                          onChange={(e) => props.handleLocalChange(e)}
                        ></Stream>
                      </div>
                      {localStreamConfig && localStreamConfig.mutedVideo && (
                        <div className={styles.headUrlWarp}>
                          <div
                            style={{background: wxUserId ? randomColor(wxUserId) : 'none'}}
                            className={styles.video_Title_box_left_avatar}
                          >
                            <Avatar userInfo={userInfoData} size={32} isPc={!isMobile}></Avatar>
                          </div>
                        </div>
                      )}
                      <div className={styles.HorizontalLiveRoom_camera_picture_camera_bottom_box}>
                        <div
                          className={
                            styles.HorizontalLiveRoom_camera_picture_camera_bottom_box_Name_Warp
                          }
                        >
                          <div>{'本人'}</div>
                          <div
                            className={classNames({
                              [styles.HorizontalLiveRoom_camera_picture_mic_icon]:
                              !localStreamConfig.mutedAudio && localStreamConfig.audioVolume == 0,
                              [styles.HorizontalLiveRoom_camera_picture_forbidden_mic_icon]:
                              localStreamConfig.mutedAudio,
                              [styles.HorizontalLiveRoom_camera_picture_forbidden_Lianmai]:
                              !localStreamConfig.mutedAudio && localStreamConfig.audioVolume > 0,
                            })}
                          ></div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div
                      style={{order: 1}}
                      className={classNames({
                        [styles.HorizontalLiveRoom_camera_picture_camera_item]: true,
                        [styles.HorizontalLiveRoom_camera_picture_camera_item_hidden]: true,
                      })}
                    >
                      <div
                        className={classNames({
                          [styles.StreamWarp]: true,
                          [styles.StreamWarpHidden]: true,
                        })}
                      ></div>
                      <div className={styles.headUrlWarp}>
                        <div
                          style={{background: wxUserId ? randomColor(wxUserId) : 'none'}}
                          className={styles.video_Title_box_left_avatar}
                        >
                          <Avatar userInfo={userInfoData} size={32} isPc={!isMobile}></Avatar>
                        </div>
                      </div>
                      <div className={styles.HorizontalLiveRoom_camera_picture_camera_bottom_box}>
                        <div
                          className={
                            styles.HorizontalLiveRoom_camera_picture_camera_bottom_box_Name_Warp
                          }
                        >
                          <div>{'本人'}</div>
                          <div
                            className={classNames({
                              [styles.HorizontalLiveRoom_camera_picture_mic_icon]:
                              !localStreamConfig.mutedAudio && localStreamConfig.audioVolume == 0,
                              [styles.HorizontalLiveRoom_camera_picture_forbidden_mic_icon]:
                              localStreamConfig.mutedAudio,
                              [styles.HorizontalLiveRoom_camera_picture_forbidden_Lianmai]:
                              !localStreamConfig.mutedAudio && localStreamConfig.audioVolume > 0,
                            })}
                          ></div>
                        </div>
                      </div>
                    </div>
                  )}
                </>
              )}

              {Array.isArray(userCameraRemoteStreamList) &&
                userCameraRemoteStreamList.map((userCameraRemoteStreamConfig, index) => {
                  if (
                    !userCameraRemoteStreamConfig ||
                    (!!shareRemoteStreamConfig ? false : isModeMatrix)
                  ) {
                    return;
                  }

                  if (
                    !shareRemoteStreamConfig &&
                    userCameraRemoteStreamConfig.userID == hostUserInfo.imUserId
                  ) {
                    return;
                  }

                  let findBycurrentLiveUser =
                    Array.isArray(currentLiveUserList) &&
                    currentLiveUserList.find((value) => {
                      return value.imUserId == userCameraRemoteStreamConfig.userID;
                    });
                  if (!findBycurrentLiveUser) {
                    findBycurrentLiveUser =
                      Array.isArray(handUpList) &&
                      handUpList.find((value) => {
                        return value.imUserId == userCameraRemoteStreamConfig.userID;
                      });
                  }

                  let order =
                    Array.isArray(userCameraRemoteStreamListSort) &&
                    userCameraRemoteStreamListSort.findIndex(
                      (itemByuserCameraRemoteStreamListSort) => {
                        return (
                          userCameraRemoteStreamConfig.userID ==
                          itemByuserCameraRemoteStreamListSort.userID
                        );
                      },
                    ) + 2;

                  return (
                    <div
                      key={`${userCameraRemoteStreamConfig.userID}_${randomNum}`}
                      style={{order: order ? order : index + 2}}
                      className={classNames({
                        [styles.HorizontalLiveRoom_camera_picture_camera_item]: true,
                        [styles.HorizontalLiveRoom_camera_picture_camera_item_hidden]:
                        userCameraRemoteStreamConfig && !userCameraRemoteStreamConfig.hasVideo,
                      })}
                    >
                      <div
                        className={classNames({
                          [styles.StreamWarp]: true,
                          [styles.StreamWarpHidden]:
                          userCameraRemoteStreamConfig && !userCameraRemoteStreamConfig.hasVideo,
                        })}
                      >
                        {userCameraRemoteStreamConfig && (
                          <Stream
                            key={`${userCameraRemoteStreamConfig.userID}_Stream_${randomNum}`}
                            stream={userCameraRemoteStreamConfig.stream}
                            config={userCameraRemoteStreamConfig}
                            init={(dom) => {
                              let config = {
                                objectFit: 'cover',
                              };
                              return (
                                RTC &&
                                RTC.playStream(userCameraRemoteStreamConfig.stream, dom, config)
                              );
                            }}
                            // onChange = {e => shareRemoteStreamConfig(e)}
                          ></Stream>
                        )}
                      </div>
                      {userCameraRemoteStreamConfig && !userCameraRemoteStreamConfig.hasVideo && (
                        <div className={styles.headUrlWarp}>
                          <div
                            style={{background: wxUserId ? randomColor(wxUserId) : 'none'}}
                            className={styles.video_Title_box_left_avatar}
                          >
                            <Avatar
                              userInfo={findBycurrentLiveUser}
                              size={32}
                              isPc={!isMobile}
                            ></Avatar>
                          </div>
                        </div>
                      )}
                      <div
                        className={classNames({
                          [styles.HorizontalLiveRoom_camera_picture_camera_bottom_box]: true,
                          [styles.HorizontalLiveRoom_camera_picture_camera_bottom_box_lianmai]:
                          findBycurrentLiveUser?.statusType == 1,
                        })}
                      >
                        <div
                          className={
                            styles.HorizontalLiveRoom_camera_picture_camera_bottom_box_Name_Warp
                          }
                        >
                          <div
                            className={classNames({
                              [styles.HorizontalLiveRoom_camera_picture_camera_bottom_box_Name]:
                                true,
                              [styles.HorizontalLiveRoom_camera_picture_camera_bottom_box_Name_lianmai]:
                              findBycurrentLiveUser?.statusType == 1,
                            })}
                          >
                            {findBycurrentLiveUser && findBycurrentLiveUser.name}
                          </div>
                          <div
                            className={classNames({
                              [styles.HorizontalLiveRoom_camera_picture_mic_icon]:
                              !userCameraRemoteStreamConfig.mutedAudio &&
                              userCameraRemoteStreamConfig.audioVolume == 0,
                              [styles.HorizontalLiveRoom_camera_picture_forbidden_mic_icon]:
                              userCameraRemoteStreamConfig.mutedAudio,
                              [styles.HorizontalLiveRoom_camera_picture_forbidden_Lianmai]:
                              !userCameraRemoteStreamConfig.mutedAudio &&
                              userCameraRemoteStreamConfig.audioVolume > 0,
                            })}
                          ></div>
                        </div>
                        {/*{ findBycurrentLiveUser
                          && findBycurrentLiveUser.statusType == 1
                          && currentUserType != 1
                          &&
                          <div onClick={()=>{
                            isFocusByOnClick({
                              expertsUserId:findBycurrentLiveUser.wxUserId,
                              isFocus:findBycurrentLiveUser.isFocus == 1 ? 0 : 1,
                              type:3,
                            })
                          }} className={classNames({
                            [styles.GuanZhu_btn]:true,
                            [styles.unGuanZhu_btn]:findBycurrentLiveUser.isFocus == 1,
                          })}>
                            {  findBycurrentLiveUser.isFocus == 1 ? '取消关注' : '关注'}
                          </div>
                        }*/}

                        {findBycurrentLiveUser &&
                          findBycurrentLiveUser.statusType == 1 &&
                          currentUserType == 1 &&
                          isHorizontalLive && (
                            <div
                              onClick={(e) => {
                                e.stopPropagation();
                                resetTimer();
                                const {wxUserId, imUserId} = findBycurrentLiveUser || {};
                                dispatch({
                                  type: 'PlanetChatRoom/setState',
                                  payload: {
                                    ModalVisibleByForceWheat: {
                                      statusType: 3,
                                      guestUserId: wxUserId,
                                      imUserId: imUserId,
                                    },
                                  },
                                });
                              }}
                              className={classNames({
                                [styles.forceXiaMai]: true,
                              })}
                            >
                              强制下麦
                            </div>
                          )}
                      </div>
                    </div>
                  );
                })}
            </div>
          </div>
        )}
    </>
  );
};
export default connect(({ global, PlanetChatRoom, loading }: any) => ({
  global,
  PlanetChatRoom,
  loading,
}))(Index);
