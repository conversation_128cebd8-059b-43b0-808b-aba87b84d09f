.modal {
  :global {
    .ant-modal-header {
      border: 0;
    }
    .ant-modal-title {
      font-size: 20px;
      color: #000;
    }
    .ant-modal-body {
      padding: 0;
    }
  }
}
.container {
  width: 100%;
  padding: 8px 24px 24px;
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80px;
  border-top: 1px solid #ddd;
  padding: 0 24px;
  :global {
    .ant-btn + .ant-btn {
      margin-left: 24px;
    }
    .ant-btn-lg {
      padding-left: 20px;
      padding-right: 20px;
      height: 38px;
    }
  }
  .left {
    display: flex;
    column-gap: 40px;
    .action_item {
      display: flex;
      align-items: center;
      cursor: pointer;
      font-size: 16px;
      color: #000;
      i {
        width: 24px;
        height: 24px;
        margin-right: 8px;
        &.rotate_right {
          background: url("../../../../assets/GlobalImg/rotate_right.png") no-repeat center;
          background-size: 100% 100%;
        }
        &.rotate_horizontal {
          background: url("../../../../assets/GlobalImg/rotate_horizontal.png") no-repeat center;
          background-size: 100% 100%;
        }
        &.rotate_vertical {
          background: url("../../../../assets/GlobalImg/rotate_vertical.png") no-repeat center;
          background-size: 100% 100%;
        }
        &.rotate_reset {
          background: url("../../../../assets/GlobalImg/rotate_reset.png") no-repeat center;
          background-size: 100% 100%;
        }
      }
    }
  }
}
