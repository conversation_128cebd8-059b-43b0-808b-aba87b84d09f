import React from 'react'
import { emojiMap, emojiName } from '@/emoticon'
import { emojiUrl } from '@/utils/utils'
import styles from './index.less'

interface PropsType {
  itemOnClick: any,
}

const Index: React.FC<PropsType> = (props: PropsType) => {

  // 点击表情
  const itemOnClick = (value,item) => {
    props.itemOnClick && props.itemOnClick(value,item)
  }

  return (
    <div className={styles.emoji_container}>
      <div className={styles.emoji_title}>全部表情</div>
      <div className={styles.emoji_content}>
        {
          emojiName.map(item => (
            <div key={item} className={styles.emoji_item} onClick={() => itemOnClick(emojiMap[item],item)}>
              <img src={`${emojiUrl}${emojiMap[item]}`} width={28} height={28} alt={item}/>
            </div>
          ))
        }
      </div>
    </div>
  )
}

export default Index
