/**
 * @Description: 移动端-下载Frida App页
 */
import React, { useRef, useEffect } from 'react';
import {connect, history} from 'umi'
import styles from './index.less';
import { getCallback } from "@/services/planetChatRoom";
import {message, Spin} from "antd";



const Index: React.FC = (props:any) => {
  const { pathname, query } = history.location;
  const {
    openid,       // 222 用户唯一标识，只在用户确认授权时才会带上
    template_id,  // 21321312 订阅消息模板ID
    action,       // confirm 用户点击动作，"confirm"代表用户确认授权，"cancel"代表用户取消授权
    scene,        // 订阅场景值 999
    reserved,     // 1_2 请求带入原样返回
  } = query
  const { dispatch } = props

  useEffect(()=>{
    if(action) {
      getCallbackFunc({
        openid:openid,
        template_id:template_id,
        action:action,
        scene:scene,
        reserved:reserved,
      });
    }else {
      message.warning('缺少必要参数action,订阅通知授权回调失败!')
    }
  },[action])

  // 预约成功订阅消息回调-V1.11
  const getCallbackFunc = async (parmas)=>{
    const {
      openid,       // 222 用户唯一标识，只在用户确认授权时才会带上
      template_id,  // 21321312 订阅消息模板ID
      action,       // confirm 用户点击动作，"confirm"代表用户确认授权，"cancel"代表用户取消授权
      scene,        // 订阅场景值 999
      reserved,     // 1_2 请求带入原样返回
    } = parmas || {}

    console.log('获取微信-订阅回调参数',parmas);

    let resByGetCallback = await getCallback({
      openId:openid,
      template_id:template_id,
      action:action,
      scene:scene,
      reserved:reserved,
    });

    const { code,content } = resByGetCallback || {}
    /**
     * https://dhealth-test.friday.tech/api/server/weChat/getCallback?
     * openid=oi1B_6UivKfo6mp3eGTHghA-hZ58
     * &template_id=_q3t5MbAyZvftGvPYo8lxXwYceM5JdAWuYkbcaUZnZQ
     * &action=confirm
     * &reserved=2246_1
     * &scene=999
     */
    if (code == 200) {
      if(action == 'confirm') {
        message.success('订阅公众号通知成功!')
      }
      history.replace('/Square');
    }else {
      history.replace('/Square');
    }
  }

  return (
    <Spin spinning={true} size="large">
      <div className={styles.content}></div>
    </Spin>
  );
};

export default connect(({loading}: any) => ({loading}))(Index)
