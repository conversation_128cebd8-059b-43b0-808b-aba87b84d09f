/**
 * @Description: 图文组件
 */
import React from 'react'
import { history } from 'umi'
import { Toast } from 'antd-mobile'
import { imageText_click, group_click } from '../../utils'
import styles from './index.less'

interface PropsType {
  componentData: any,                                      // 组件数据，格式：{ dataList: [], config: { number: xx } }
  isHomePage?: any,                                         // 是否为首页，1是，0否
  moduleIndex?: any,                                        // 当前组件在所有图文组件中的索引
  isClassifyGuide?: any,                                   // 是否在分类导航组件中，1是
  classifyGuideTabIndex?: any,                             // 在分类导航组件中时，选中的tab标签
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { componentData, isHomePage, moduleIndex, isClassifyGuide, classifyGuideTabIndex } = props
  const dataList = componentData.dataList || []

  // 点击图片跳转，linkType：1 H5链接，2 小程序页面，3 小鹅通链接，4 鹅直播链接
  const goToUrl = (item, index) => {
    // 链接地址正则
    const pattern = /^((https|http):\/\/)[^\s]+/
    if (!item.linkType) {
      return
    } else if (item.linkType == 1) {
      // 跳转h5
      if (item.linkUrl) {
        if (pattern.test(item.linkUrl)) {
          // 跳转项目内地址
          if (item.linkUrl.indexOf(window.location.origin) > -1) {

            const linkPathname = item.linkUrl.split(window.location.origin)[1]
            // 避免路由相同跳转后页面不刷新
            if (linkPathname.indexOf('/home?') > -1) {
              window.location.href = item.linkUrl
            } else {
              history.push(linkPathname)
            }
          } else {
            window.location.href = item.linkUrl
          }
        } else {
          Toast.show('链接地址不正确')
        }
      }

    } else if (item.linkType == 2) {
      // 跳转小程序
      if (item.resourceType && item.resourceId) {

        history.push({
          pathname: '/Home/MiniProgramCode',
          query: {
            resourceType: item.resourceType,           // 链接资源类型，1 病例，2 专家
            resourceId: item.resourceId,               // 资源ID
          }
        })
      }

    } else {
      // 跳转小鹅通、鹅直播
      if (item.linkUrl) {
        if (pattern.test(item.linkUrl)) {
          window.location.href = item.linkUrl
        } else {
          Toast.show('链接地址不正确')
        }
      }
    }

    // 首页友盟统计，isHomePage=1
    if (isHomePage != 1) {
      return
    }
    // 在分类导航组件中
    if (isClassifyGuide == 1) {
      // 首页，分类组件点击量
      group_click(moduleIndex, `第${moduleIndex}个分类组件，第${classifyGuideTabIndex}个标签，第${index + 1}个左图右文`)
      return
    }
    // 首页，图文点击量
    imageText_click(moduleIndex, index + 1)
  }

  return (
    <div className={styles.image_text_container}>
      {
        dataList.map((item, index) => {
          return (
            <div key={index} className={styles.item_box} onClick={() => goToUrl(item, index)}>
              <div className={styles.left}>
                <img src={item.imgUrlDto && item.imgUrlDto.fileUrlView} alt=""/>
              </div>
              <div className={styles.right}>
                <div className={styles.title}>{item.title}</div>
                <div className={styles.subtitle}>{item.subtitle}</div>
                <div className={styles.price}>{item.price}</div>
              </div>
            </div>
          )
        })
      }
    </div>
  )
}

export default Index
