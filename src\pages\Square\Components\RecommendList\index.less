.swiper_wrap {
  display: flex;
  flex-wrap: nowrap;
  width: 100%;
  padding: 0 0 16px 12px;
  overflow-x: auto;
  background: #f5f6f8;

  &::-webkit-scrollbar {
    width: 0;
    background: transparent;
  }

  .user_release_box {
    flex-shrink: 0;
    margin-right: 12px;

    .user_img_box {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2px solid #ffffff;
      border-radius: 50%;

      .init_img {
        width: 50px;
        height: 50px;
        color: #fff;
        font-weight: 500;
        font-size: 18px;
        line-height: 46px;
        white-space: nowrap;
        text-align: center;
        //background-image: url("../../../../assets/GlobalImg/default_head_picture.png");
        background-repeat: no-repeat;
        background-position: center;
        background-size: 100% 100%;
        border: 3px solid #f5f6f8;
        border-radius: 50%;
      }

      .user_release_icon {
        position: absolute;
        right: 3px;
        bottom: 3px;
        width: 16px;
        height: 16px;
        background: url('../../../../assets/GlobalImg/release.png') no-repeat center;
        background-size: 100% 100%;
        border-radius: 50%;
      }
    }

    .user_name {
      margin-top: 4px;
      color: #000000;
      font-size: 11px;
      line-height: 15px;
      text-align: center;
    }
  }

  .release_people_box {
    flex-shrink: 0;
    margin-right: 12px;

    .release_people_init {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 2px;
      overflow: hidden;
      background: linear-gradient(
        132deg,
        rgba(234, 151, 255, 1),
        rgba(101, 200, 255, 1),
        rgba(180, 241, 255, 1)
      );
      border-radius: 50%;

      &.release_people_active {
        background: #ccc;
      }

      .init_img {
        width: 50px;
        height: 50px;
        color: #fff;
        font-weight: 500;
        font-size: 18px;
        line-height: 46px;
        white-space: nowrap;
        text-align: center;
        //background-image: url("../../../../assets/GlobalImg/default_head_picture.png");
        background-repeat: no-repeat;
        background-position: center;
        background-size: 100% 100%;
        border: 3px solid #f5f6f8;
        border-radius: 50%;
      }
    }
    .release_people_name {
      width: 54px;
      margin-top: 4px;
      overflow: hidden;
      color: #000000;
      font-size: 11px;
      line-height: 15px;
      white-space: nowrap;
      text-align: center;
      text-overflow: ellipsis;
    }
  }
}

.progress_bar_wrap {
  position: sticky;
  top: 0;
  left: 0;
  z-index: 990;
  width: 100%;
  background: #fff;
  .progress_bar_text {
    display: flex;
    align-items: center;
    height: 30px;
    padding: 0 12px;
    color: #666;
    font-size: 11px;
    .progress_bar_avatar {
      width: 20px;
      height: 20px;
      margin-right: 8px;
      color: #fff;
      font-size: 10px;

      line-height: 20px;
      white-space: nowrap;
      text-align: center;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
    }
  }
}

// 数据列表
.list_wrap {
  width: 100%;
}

.InfiniteScroll_Warp {
  :global {
    .adm-infinite-scroll {
      padding: 0px;
    }
  }
}

// 话题模块
.topic_wrap {
  padding: 6px 16px 16px;
  .topic_content {
    padding: 16px 16px 12px;
    background: #fff;
    border-radius: 8px;
    .topic_title {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      color: #000;
      font-weight: bold;
      font-size: 16px;
      line-height: 20px;
      column-gap: 4px;
    }
    .topic_item {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      height: 26px;
      line-height: 18px;
      column-gap: 16px;
      .topic_item_name {
        flex: 1;
        overflow: hidden;
        color: #333;
        font-size: 13px;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .topic_item_number {
        flex-shrink: 0;
        color: #777;
        font-size: 10px;
        white-space: nowrap;
      }
    }
  }
}

// 广告位
.ad_wrapper {
  display: flex;
  justify-content: space-between;
  padding: 0 16px 10px;
  font-family:  PingFang SC;
  .ad_item {
    box-sizing: border-box;
    height: 112px;
    padding: 10px 0 0 12px;
    border-radius: 8px 8px 8px 8px;
  }
  .ad_item1 {
    width: 24%;
    background: linear-gradient(180deg, #24c173 0%, #66dca2 100%);
  }
  .ad_item2 {
    width: 24%;
    margin: 0 6px;
    background: linear-gradient(180deg, #ff8d01 0%, #ffd968 100%);
  }
  .ad_item3 {
    flex: 1;
    background: linear-gradient(180deg, #0293fa 0%, #8bdbfa 100%);
    .title {
      margin: 0px;
    }
  }
  .icon {
    display: block;
    width: 18px;
  }
  .title {
    margin: 9px 0 23px 0;
    color: #ffffff;
    font-weight: 600;
    font-size: 15px;
    line-height: 18px;
  }
  .more {
    display: flex;
    align-items: center;
    color: #ffffff;
    font-size: 12px;
    img {
      width: 12px;
    }
  }
  .subTitle {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 300;
    font-size: 9px;
    line-height: 11px;
    margin-top: 4px;
  }
  .teachers {
    margin-top: 32px;
    img {
      margin-right: 4px;
      width: 24px;
      height: 24px;
    }
  }
}
