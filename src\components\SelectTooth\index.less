.DiagnosisModalTab{
  overflow: hidden;
  :global{
    .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab {
      margin: 0;
      border: 1px solid #ccc;
      border-bottom: 0;
      border-radius: 4px 4px 0 0;
      background: #E0E2E7;
      margin-right: 2px;
      padding: 0 16px;
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      line-height: 38px;
    }

    .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
      background: #fff!important;
      border-color: #ccc;
      color: #4BA2FF;
      padding-bottom: 1px;
    }

    .ant-tabs-content.ant-tabs-content-no-animated.ant-tabs-top-content.ant-tabs-card-content{
      border-left: 1px solid #ccc;
      border-right: 1px solid #ccc;
      border-bottom: 1px solid #ccc;
    }
    .ant-tabs-bar.ant-tabs-top-bar.ant-tabs-card-bar{
      margin: 0;
      /*padding: 0 28px;*/
    }
    /*.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab{
      margin: 0;
      border: 1px solid #ccc;
      border-bottom: 0;
      border-radius: 4px 4px 0 0;
      background: #fafafa;
      margin-right: 2px;
      padding: 0 28px;
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      line-height: 38px;
    }*/
  }
  .ToothPosition{
    margin: 20px;
    overflow: hidden;
    background: #d8eeff;
    .Toothcenter{
      margin: 0 auto;
    }
    tr{
      td{
        /*border: 1px solid #e8eaec;*/
      }
      td:first-child{
        border-left: 0;
        :global{
         /* .antd-pro\\components\\-tag-select\\index-tagSelect .ant-tag{
            border: 1px solid #e2e3e6;
            border-radius: 50%;
            width: 26px;
            height: 25px;
            text-align: center;
            line-height: 23px;
            padding: 0 5px;
            margin-right: 6px;
          }*/
        }
      }
      td:last-child{
        border-right: 0;
        :global{
         /* .antd-pro\\components\\-tag-select\\index-tagSelect .ant-tag{
            border: 1px solid #e2e3e6;
            border-radius: 50%;
            width: 26px;
            height: 25px;
            text-align: center;
            line-height: 23px;
            padding: 0 5px;
            margin-right: 6px;
          }*/
        }
      }
    }
    tr:first-child td{
      border-top: 0;
    }
    tr:last-child td{
      border-bottom:0;
    }
  }
}

.ToothTopRow {
  :global {
    .ant-tag{
      padding: 0 0px!important;
      //margin-right: 12px;
      margin-right: 0px!important;
      font-size: 14px!important;
      display: inline-block;
      width: 32px!important;
      height: 28px!important;
      //background: #4ca2ff;
      color: #1890FF;
      text-align: center;
      line-height: 27px;
      background-image: url('../../assets/doctorwork/Tooth1.png');
      background-size: 31px 27px;
      background-repeat: no-repeat;
    }
    .ant-tag-checkable-checked {
      color: #ffffff;
      background: url('../../assets/doctorwork/blueTooth1.png') no-repeat;
    }

    .ant-tag:active{
      background: none;
    }
  }
}

.ToothBottomRow {
  :global {
    .ant-tag {
      padding: 0 0px!important;
      //margin-right: 12px;
      margin-right: 0px!important;
      font-size: 14px!important;
      display: inline-block;
      width: 32px!important;
      height: 28px!important;
      //background: #4ca2ff;
      color: #1890FF;
      text-align: center;
      line-height: 27px;
      background: url('../../assets/doctorwork/bottomTooth1.png') no-repeat;
    }

    .ant-tag-checkable-checked {
      color: #ffffff;
      background: url('../../assets/doctorwork/bottomBlueTooth1.png') no-repeat;
    }

    .ant-tag:active{
      background: none;
    }

  }
}
