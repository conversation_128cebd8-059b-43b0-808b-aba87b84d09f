/**
 * @Description: 导航栏组件
 */
import React, { useEffect, useState } from 'react'
import { history, connect } from 'umi'
import classNames from 'classnames'
import { getOperatingEnv, backInApp, getIsFirstPageInApp, goToHomePage } from '@/utils/utils'
import styles from './index.less'

interface PropsType {
  className?: any,                                         // 类名
  title?: any,                                             // 标题
  style?: any,                                             // 样式
  onBack?: any,                                            // 点击返回按钮回调事件
  useWhiteIcon?: any,                                      // 是否使用白色的返回箭头
  LeftRender?: any,                                        // 头部左侧内容
  RightRender?: any,                                       // 头部右侧内容
  bordered?: boolean,                                      // 是否展示下边框
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {
    dispatch, title = '', className = '', style = {}, useWhiteIcon = false, LeftRender, RightRender,
    bordered = false,
  } = props
  const { query, pathname } = history.location

  // 点击返回按钮
  const goBack = () => {
    // APP环境中，如果当前页面是打开的第一个页面，正常返回会失效，此时调用app的返回
    if (getIsFirstPageInApp()) {
      backInApp()
      return
    }

    if (title == '隐私政策' && getOperatingEnv() == 5) {
      backInApp()
      return
    }

    if (props.onBack) {
      props.onBack()
    } else if (query.isShare == 1) {
      goToHomePage(dispatch, 'push')

    }else if (query.from == 'Setting'&&pathname.indexOf('/User/login') >-1) {
      history.push('/UserInfo')
    }else if(query.from == 'login'&&pathname.indexOf('/forgetPassword') >-1){
      history.push('/User/login?Tab=2')
    }else if(pathname.indexOf('/wechatAuthLogin') >-1 || pathname.indexOf('/enterpriseQRcodeAuth') >-1){
      // 如果是微信授权登录，则返回系统默认进入页
      history.replace('/')
    } else {
      if (history.length > 2) {
        history.goBack()
      } else {
        history.replace('/')
      }
    }
  }

  return (
    <div className={classNames(styles.nav_bar_wrap, [className], {
      [styles.nav_bar_box_pc]: getOperatingEnv() == 4,  // PC端
      [styles.bordered]: bordered,
    })} style={style}>
      <div className={styles.nav_bar}>
        {/* 返回按钮 */}
        {
          getOperatingEnv()==5&&(title=='选择身份'||title=='个人认证')?null:<i className={classNames(styles.nav_bar_icon, {
            [styles.icon_white]: !!useWhiteIcon,
            [styles.share_page]: query.isShare == 1
          })} onClick={goBack}></i>
        }


        {/* 标题 */}
        <div className={styles.nav_bar_title}>{(getOperatingEnv() != 2 && getOperatingEnv() !=7) && title}</div>

        {/* 左侧渲染内容 */}
        <div className={styles.left}>
          {LeftRender && LeftRender()}
        </div>

        {/* 右侧渲染内容 */}
        <div className={styles.right}>
          {RightRender && RightRender()}
        </div>

      </div>
    </div>
  )
}

export default connect(({ activity, loading }: any) => ({activity, loading}))(Index)
