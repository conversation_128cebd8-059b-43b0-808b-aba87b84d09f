/**
 * 腾讯云 SDKAppId，需要替换为您自己账号下的 SDKAppId。
 *
 * 进入腾讯云实时音视频[控制台](https://console.cloud.tencent.com/rav ) 创建应用，即可看到 SDKAppId，
 * 它是腾讯云用于区分客户的唯一标识。
 */
// const SDKAPPID = 1400812135;
const SDKAPPID = 1600024989;

/**
 * 签名过期时间，建议不要设置的过短
 * <p>
 * 时间单位：秒
 * 默认时间：7 x 24 x 60 x 60 = 604800 = 7 天
 */
const EXPIRETIME = 604800;

/**
 * 计算签名用的加密密钥，获取步骤如下：
 *
 * step1. 进入腾讯云实时音视频[控制台](https://console.cloud.tencent.com/rav )，如果还没有应用就创建一个，
 * step2. 单击“应用配置”进入基础配置页面，并进一步找到“帐号体系集成”部分。
 * step3. 点击“查看密钥”按钮，就可以看到计算 UserSig 使用的加密的密钥了，请将其拷贝并复制到如下的变量中
 *
 * 注意：该方案仅适用于调试Demo，正式上线前请将 UserSig 计算代码和密钥迁移到您的后台服务器上，以避免加密密钥泄露导致的流量盗用。
 * 文档：https://cloud.tencent.com/document/product/647/17275#Server
 */
// const SECRETKEY = '31e064e3939344afc67e9a7547cdb1732808eeb7507f9edf240bd849b793c68a';
const SECRETKEY = 'fbcedd3d07985f442f2fe5e03c022a51f59f0d6cb82982739bdee369d10fc13f';

/**
* 直播角色类型
* */
const audience = 'audience';  // 观众
const anchor = 'anchor';        // 主播

/**
* 接入时需要将0替换为您的即时通信 IM 应用的 SDKAppID
* */
const SDKAppIDByIm = 0;



/**
* 自定义消息类型
* 签到    SIGN_IN
* 弹幕    BULLET_SCREEN
* 送花    SEND_FLOWERS
* 掌声    SEND_APPLAUSE
* 举手连麦 HAND_UP
* 强制下架直播间 FORCE_DOWN
* */
const SIGN_IN = 'SIGN_IN'               // 签到
const BULLET_SCREEN = 'BULLET_SCREEN'   // 弹幕
const SEND_FLOWERS = 'SEND_FLOWERS'     // 送花
const SEND_APPLAUSE = 'SEND_APPLAUSE'   // 掌声
const SEND_CALL = 'SEND_CALL'           // 打call
const HAND_UP = 'HAND_UP'               // 举手连麦
const HAND_DOWN = 'HAND_DOWN'           // 放弃连麦
const UPDATA_STATE = 'UPDATA_STATE'     // 更新状态
const NO_PW_APPLY = 'NO_PW_APPLY'       // 无密码申请入会
const WHITEBOARD_MSG = 'WHITEBOARD_MSG' // 白板推流
const FORCED_END = 'FORCED_END'         // 强制下架直播间
const ROOM_DISBAND = 'ROOM_DISBAND'     // 解散房间
const CAMERA_TOGGLE = 'CAMERA_TOGGLE'   // 开启或关闭成员摄像头
const CAMERA_TOGGLE_RESULT = 'CAMERA_TOGGLE_RESULT' // 参会人拒绝开启摄像头
const MICROPHONE_TOGGLE = 'MICROPHONE_TOGGLE' // 开启或关闭成员麦克风
const MICROPHONE_TOGGLE_RESULT = 'MICROPHONE_TOGGLE_RESULT' // 参会人拒绝开启麦克风
const APPLY_RECORD = 'APPLY_RECORD'           // 申请录制
const APPLY_RECORD_RESULT = 'APPLY_RECORD_RESULT'// 申请录制的结果
const TRANSFER_HOST = 'TRANSFER_HOST' // 移交主持人事件
const SPACE_GDP_PV = 'SPACE_GDP_PV' // 更新空间GDP_PV

// licenseUrl TCPlayer的版权许可
const licenseUrl = 'https://license.vod2.myqcloud.com/license/v2/1318191163_1/v_cube.license'



export {
  SDKAPPID,
  EXPIRETIME,
  SECRETKEY,
  audience,
  anchor,
  SDKAppIDByIm,
  SIGN_IN,
  BULLET_SCREEN,
  SEND_FLOWERS,
  SEND_APPLAUSE,
  SEND_CALL,
  HAND_UP,
  HAND_DOWN,
  UPDATA_STATE,
  FORCED_END,
  ROOM_DISBAND,
  NO_PW_APPLY,
  WHITEBOARD_MSG,
  CAMERA_TOGGLE,
  MICROPHONE_TOGGLE,
  licenseUrl,
  APPLY_RECORD,
  APPLY_RECORD_RESULT,
  MICROPHONE_TOGGLE_RESULT,
  CAMERA_TOGGLE_RESULT,
  TRANSFER_HOST,
  SPACE_GDP_PV,
};
