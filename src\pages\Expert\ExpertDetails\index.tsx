/**
 * @Description: H5-专家详情 / 个人中心（H5）
 * @author: 赵斐
 */
import React, { useEffect, useState, useRef } from 'react';
import { history, connect } from 'umi';
import { stringify } from "qs"
import classNames from 'classnames';
import { Helmet } from "react-helmet";
import ReactHtmlParser  from 'react-html-parser';  // dom字符串转dom结构
import { randomColor, processNames, WxAppIdByPublicAccount, getOperatingEnv, getShareUrl } from '@/utils/utils'
import { Spin } from 'antd';
import { InfiniteScroll, Mask, Toast, Modal } from 'antd-mobile'
import styles from './index.less';

// 图标、icon
import { QuestionCircleFilled } from '@ant-design/icons';
import followIcon from '@/assets/Expert/follow.png';        // 关注小图标
import unfollowIcon from '@/assets/Expert/unfollow.png';    // 关注小图标
import blueUpArrowIcon from '@/assets/Expert/blue_up_arrow.png';      // 向上箭头小图标
import blueDownArrowIcon from '@/assets/Expert/blue_down_arrow.png';  // 向下箭头小图标
import wxIcon from '@/assets/GlobalImg/wx_icon.png';        // 微信分享小图标

import NavBar from '@/components/NavBar'                   // 导航组件
import TipsModal from './Components/TipsModal';            // 取消关注二次弹框
import MyHomepageSpace from './Components/MyHomepageSpace';// 我的主页空间
import MyHomepageMeeting from './Components/MyHomepageMeeting';  // 我的主页会议
import SpaceList from '@/components/SpaceList';            // 空间列表组件
import MeetingCard from '@/components/MeetingCardInfo';            // 会议卡片组件
import CaseList from '@/components/CaseList';              // 病例列表组件
import LoadingException from '@/components/LoadingException'                           // 数据加载异常
import SelectConsultationMethodModal from './ComponentsH5/SelectConsultationMethodModal' // 选择指导方式弹窗
import SelectCaseTemplateModal from './ComponentsH5/SelectCaseTemplateModal' // 选择病例模板弹窗

import H5PostGroup from './ComponentsH5/H5PostGroup';       // 帖子3合1


const initExpertAdviceData = {
  id: null,                   // 微信登录用户ID
  name: "",                   // 专家名称
  postTitleDictName: "",      // 职称字典名称
  depSubjectDictName: "",     // 科室字典
  abilityLevelDictName: "",   // 能力等级字典名称
  organizationName: "",       // 机构名称
  clinicalExperience: "",     // 临床经验
  intro: "",                  // 介绍
  imagePhotoPathShow: "",     // 专家形象路径
  treatCosts: "",             // 新诊费
  originalTreatCosts: "",     // 原诊费
  isFocus: 0,                 // 是否关注 0未关注  1关注
  isExperts: 0,               // 是否是专家：0:否，1:是
  personGdpCount: 0,          // 个人GDP
  fansCount: 0,               // 被关注用户数
  focusCount: 0,              // 关注用户数
}

// 空间列表所需数据
const initSpace = {
  spaceDataSource: [],        // 空间列表数据集合
  total: 0,                   // 总条数
}

const Index: React.FC = (props: any) => {
  const { global, dispatch, loading ,expertAdvice} = props;
  const { query } =  history.location || {}
  const { id } = query || {}   // 专家（用户）ID
  const UerInfo = JSON.parse(localStorage.getItem('userInfo')) || {};
  const isMyPages = UerInfo.friUserId == id              // 判断是否是当前用户

  // tab签数据
  const tabLists =isMyPages? [
    { id: 1, val: '直播' },
    { id: 2, val: '会议' },
    { id: 3, val: '病例' },
    { id: 4, val: '帖子' },
  ]:[
    { id: 1, val: '直播' },
    { id: 3, val: '病例' },
    { id: 4, val: '帖子' },
  ]

  const myHomeSpaceFilter = sessionStorage.getItem('myHomeSpaceFilter')!='undefined'?JSON.parse(sessionStorage.getItem('myHomeSpaceFilter')):{}; // 个人中心空间筛选条件
  const listRef = useRef<any>(null);
  const introduceRef = useRef<any>(null);
  const [interfaceStatus, setInterfaceStatus] = useState(2);                       // 接口状态
  const [isShowBtn, setIsShowBtn] = useState(false);                               // 收起、展开简介
  const [maskVisible, setMaskVisible] = useState(false)                            // 分享状态
  const [unfollowVisible, setUnfollowVisible] = useState(false);                   // 取消关注二次提框
  const [selectConsultationMethodVisible, setSelectConsultationMethodVisible] = useState(false)  // 选择指导方式弹窗
  const [selectCaseTemplateModalVisible, setSelectCaseTemplateModalVisible] = useState(false)  // 选择病例模板弹窗
  const [consultationType, setConsultationType] = useState(null)  // 指导类型，1图文，2视频
  const [tabType, setTabType] = useState(query.tabKey || expertAdvice.tabKey);     // tab(空间、病例)

  const [expertAdviceData, setExpertAdviceData] = useState(initExpertAdviceData)   // 专家详情数据
  const [excellentCase, setExcellentCase] = useState<any>([])                      // 病例列表数据集合
  const [spatialData, setSpatialData] = useState(initSpace)                        // 直播or会议列表数据
  const [hasMore, setHasMore] = useState(true); // 更多数据状态
  const [spaceFilter, setSpaceFilter] = useState({
    spaceJoinType: tabType==1?(myHomeSpaceFilter?.spaceJoinType||1):null, // 空间角色类型 1:作为主持 2:预约的 3:作为嘉宾
    spaceStatus: tabType==1?(myHomeSpaceFilter?.spaceStatus||null):null, // 空间状态 0:全部 1:预约中 2:进行中 3:已结束
    isBiz: tabType==2?(myHomeSpaceFilter?.isBiz||null):null, // 会议类型 1:企业空间 2:非企业空间
    isRecordingMeeting: tabType==2?(myHomeSpaceFilter?.isRecordingMeeting||null):null, // 会议录制  1:已录制，0/不传:全部
    meetingSpaceStatus: tabType==2?(myHomeSpaceFilter?.meetingSpaceStatus||1):null, // 会议状态 1:我参与的会议 2:历史记录
  })       // 我的空间列表筛选条件
  const [statePageNum, setStatePageNum] = useState(1)                              // 空间列表当前分页


  const {
    name,                 // 专家名称
    postTitleDictName,    // 职称字典名称
    depSubjectDictName,   // 科室字典
    abilityLevelDictName, // 能力等级字典名称
    organizationName,     // 机构名称
    intro,                // 介绍
    imagePhotoPathShow,   // 专家形象路径
    treatCosts,           // 新诊费
    originalTreatCosts,   // 原诊费
    isFocus,             // 是否关注 0未关注  1关注
    isExperts,           // 是否是专家：0:否，1:是
    personGdpCount,      // 个人GDP
    fansCount,           // 被关注用户数
    focusCount,          // 关注用户数
    originalPictureTreatCosts,   // 原图文指导费
    originalVideoTreatCosts,     // 原视频指导费
  } = expertAdviceData || {}

  const {
    spaceDataSource,   // 空间列表数据
    total,             // 空间数据总条数
  } = spatialData

  useEffect(() => {
    getExpertsInfo()
  }, [])

  useEffect(() => {
    if (tabType == 1||tabType == 2) {  // tabType: 1 直播 2 会议
      //  清空tab数据
      setSpatialData(initSpace)
      if(isMyPages){
        getStarSpaceListBySearchUserId(1,tabType==1?spaceFilter.spaceJoinType:null,spaceFilter.spaceStatus,spaceFilter.isBiz,tabType==2?spaceFilter.isRecordingMeeting:null,tabType==2?spaceFilter.meetingSpaceStatus:null);
        sessionStorage.getItem('myHomeSpaceFilter')&&sessionStorage.removeItem('myHomeSpaceFilter');
      }else{
        getStarSpaceListBySearchUserId(1)
      }
    }else if(tabType == 3){  // 3 专家/病例
      getCaseInfoByExpertsUserId()
    } else { // tabType: 4 帖子3合1

    }
  }, [tabType])

  useEffect(() => {
    // 微信分享配置
    if (expertAdviceData.id || expertAdviceData.id == 0) {
      onShareAppMessage()
    }
  }, [expertAdviceData])

  // 监听数据变化，如果数据达到total，则没有更多了
  useEffect(() => {
    if (spaceDataSource.length >= total) {
      setHasMore(false);
    } else {
      setHasMore(true);
    }
  }, [spaceDataSource, total]);


  // 获取专家详情
  const getExpertsInfo = () => {
    dispatch({
      type: "expertAdvice/getExpertsInfo",
      payload: {
        expertsUserId: id
      }
    }).then((res: any) => {
      let { code, content } = res || {}
      if (code == 200) {
        setExpertAdviceData(content)
      }
    }).catch((err: String) => {
      console.log(err)
    })
  }

  // 微信分享配置
  const onShareAppMessage = () => {
    const url = window.location.href
    const shareUrl = getShareUrl(url)
    console.log('shareUrl：', shareUrl)
    dispatch({
      type: 'userInfoStore/getJsapiTicket',
      payload: {
        currentUrl: url,                                   // 页面url
        appId: WxAppIdByPublicAccount,                     // 公众号appId
      },
    }).then(res => {
      if (res && res.code == 200) {
        wx.config({
          debug: false,
          appId: res.content.appId,
          timestamp: res.content.timestamp,
          nonceStr: res.content.nonceStr,
          signature: res.content.signature,
          jsApiList: [
            'updateAppMessageShareData',
            'updateTimelineShareData',
            'onMenuShareTimeline',
            'onMenuShareAppMessage',
            'onMenuShareQQ',
            'onMenuShareWeibo',
            'onMenuShareQZone',
          ],
        })
        wx.ready(() => {
          let str = isExperts == 1 ? (`${name}专家` + (intro ? `，${intro}` : '')) : `${name}用户`
          const shareDate = {
            title: '【FRIDAY医生星球】牙医都来这里学习和交流！',
            desc: str,
            link: shareUrl,
            imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/DigitalHealthShareImg.png',
          };
          console.log(shareDate)
          wx.updateAppMessageShareData(shareDate);
          wx.updateTimelineShareData(shareDate);
          wx.onMenuShareTimeline(shareDate);
          wx.onMenuShareAppMessage(shareDate);
          wx.onMenuShareQQ(shareDate);
          wx.onMenuShareWeibo(shareDate);
          wx.onMenuShareQZone(shareDate);
        })
      } else {
        // Toast.show('请求微信配置失败～！')
      }
    })
  }

  /**
   * 获取检索用户的直播or会议数据列表
   * @param pageNum  当前第几页
   */
  const getStarSpaceListBySearchUserId = async (pageNum: number,spaceJoinType:any=null,spaceStatus:any=null,isBiz:any=null,isRecordingMeeting:any=null,meetingSpaceStatus:any=null) => {
    const { spaceDataSource } = spatialData || {}
    await dispatch({
      type: "expertAdvice/getStarSpaceListBySearchUserId",
      payload: {
        pageNum,
        pageSize: 30,
        searchUserId: id,  // 检索用户id
        starSpaceType:tabType?tabType:1,   // 空间类型 1:直播 2:会议
        ...(spaceJoinType ? { spaceJoinType } : {}),
        ...(spaceStatus ? { spaceStatus } : {}),
        ...(isBiz ? { isBiz } : {}),
        ...(isRecordingMeeting ? { isRecordingMeeting } : {}),
        ...(meetingSpaceStatus ? { meetingSpaceStatus } : {}),
      }
    }).then((res: any) => {
      const { code, content } = res || {};
      const { total, resultList } = content || {};
      if (code == 200) {
        setStatePageNum(pageNum)
        let data = pageNum == 1 ? [] : spaceDataSource;
        data = data.concat(resultList);
        if (Array.isArray(data) && data.length == 0) {
          setInterfaceStatus(2)
        }
        setSpatialData({
          ...spatialData,
          spaceDataSource: [...data],
          total,
        })
      } else {
        Toast.show('数据加载失败，请稍后重试!')
        setHasMore(false);
        setInterfaceStatus(1)
      }
    }).catch((err: any) => {
      console.log(err)
      setInterfaceStatus(1)
    })
  }

  // 获取病例详情
  const getCaseInfoByExpertsUserId = () => {
    dispatch({
      type: "expertAdvice/getCaseInfoByExpertsUserId",
      payload: {
        expertsUserId: id,  // 专家id
      }
    }).then((res: any) => {
      const { code, content } = res || {};
      if (code == 200) {
        if (Array.isArray(content) && content.length) {
          setExcellentCase([...content])
          setInterfaceStatus(2)
        } else {
          setInterfaceStatus(2)
          setExcellentCase([])
        }
      } else {
        setInterfaceStatus(1)
        setExcellentCase([])
      }
    }).catch((err: any) => {
      console.log(err)
      setInterfaceStatus(1)
    })
  }



  // 提示弹框取消事件
  const tipsCancelFn = () => {
    setUnfollowVisible(false)
    followAndCheck(0)
  }

  // 提示弹框想一想事件
  const tipsNoCancelFn = () => {
    setUnfollowVisible(false)
  }

  /**
   * 点击取消关注或者点击关注（需要登录才能操作）
   * @param type 1 关注 0 取消关注
   */
  const followAndCheckFun = (type: number) => {
    // 没登录跳转登录页
    if (!localStorage.getItem('access_token')) {
      history.push({
        pathname: '/User/login',
        query: {
          redirectByPush: window.location.href,
        }
      })
      return
    }

    if (type == 1) {
      followAndCheck(type)
    } else {
      setUnfollowVisible(true)
    }
  }

  /**
   * 关注取消关注接口调用方法
   * @param type 1 关注 0 取消关注
   */
  const followAndCheck = (type: number) => {
    dispatch({
      type: "expertAdvice/followAndCheck",
      payload: {
        expertsUserId: id,   // 专家Id
        isFocus: type,
      }
    }).then((res: any) => {
      const { code, msg, error } = res || {}
      if (code == 200) {
        getExpertsInfo()
      } else if (error) {
        Toast.show(error)
      } else {
        Toast.show(msg)
      }
    }).catch((err: string) => {
      console.log(err)
    })
  }

  /**
   * 点击空间或者病例
   * @param type  1 空间 2 病例 4文章
   */
  const onClickTabFun = (type: number) => {
    if (tabType == type) {
      return
    }
    dispatch({
      type: "expertAdvice/save",
      payload: {
        tabKey:type,
        subTabKey: 1,       // 专家详情二级tab选中状态
      }
    })
    history.replace(`${history.location.pathname}?${stringify({
      ...query,
      tabKey: type,
    })}`)
    setTabType(type)
    //  清空tab数据
    setSpatialData(initSpace)
    setInterfaceStatus(2)
    //  清空筛选条件
    setSpaceFilter({
      spaceJoinType:type==1?1:null,
      spaceStatus:null,
      isBiz:null,
      isRecordingMeeting:null,
      meetingSpaceStatus:type==2?1:null,
    })
  }

  // 直播or会议列表加载更多数据
  const spaceLoadMore = async () => {
    isMyPages?await getStarSpaceListBySearchUserId(statePageNum + 1,spaceFilter.spaceJoinType,spaceFilter.spaceStatus,spaceFilter.isBiz,spaceFilter.isRecordingMeeting,spaceFilter.meetingSpaceStatus): await getStarSpaceListBySearchUserId(statePageNum + 1)
  }

  // 分享
  const shareOnClick = () => {
    setMaskVisible(true)
  }

  // 关闭筛选弹窗
  const shareCloseOnClick = () => {
    setMaskVisible(false)
  }

  // 直播or会议列表数据异常重新加载调用接口
  const retrySpaceFun = () => {
    isMyPages?getStarSpaceListBySearchUserId(1,spaceFilter.spaceJoinType,spaceFilter.spaceStatus,spaceFilter.isBiz,spaceFilter.isRecordingMeeting,spaceFilter.meetingSpaceStatus):getStarSpaceListBySearchUserId(1);

  }
  // 病例列表数据异常重新加载调用接口
  const retryCaseFun = () => {
    getCaseInfoByExpertsUserId();
  }

  // 重置接口异常状态
  const resetStatusFun = () => {
    setInterfaceStatus(2);
  }

  // 我的主页-直播or会议选择筛选条件，刷新页面
  const mySpaceChooseFilter = (spaceJoinType:any=null,spaceStatus:any=null,isBiz:any=null,isRecordingMeeting:any=null,meetingSpaceStatus:any=null) => {
    setSpatialData(initSpace);
    getStarSpaceListBySearchUserId(1,spaceJoinType,spaceStatus,isBiz,isRecordingMeeting,meetingSpaceStatus)
    // 保存子组件留下的筛选条件以备加载更多使用
    setSpaceFilter({
      spaceJoinType,
      spaceStatus,
      isBiz,
      isRecordingMeeting,
      meetingSpaceStatus
    })
  }

  // 我的主页-空间-更多操作-取消后，刷新页面
  const mySpaceMoreOperateClose = (spaceJoinType:any=null,spaceStatus:any=null,isBiz:any=null,isRecordingMeeting:any=null,meetingSpaceStatus:any=null) => {
    getExpertsInfo(); // 获取主页信息
    getStarSpaceListBySearchUserId(1,spaceJoinType,spaceStatus,isBiz,isRecordingMeeting,meetingSpaceStatus) // 获取直播or会议列表
  }

  // 选择指导方式弹窗，打开（需要登录才能操作）
  const selectConsultationMethodModalShow = () => {
    // 没登录跳转登录页
    if (!localStorage.getItem('access_token')) {
      history.push({
        pathname: '/User/login',
        query: {
          redirectByPush: window.location.href,
        }
      })
      return
    }
    setSelectConsultationMethodVisible(true)
  }

  // 选择指导方式弹窗，关闭
  const selectConsultationMethodModalHide = () => {
    setSelectConsultationMethodVisible(false)
  }

  // 选择指导方式，1图文，2视频
  const onClickConsultationMethod = (consultationType) => {
    setConsultationType(consultationType)
    setSelectConsultationMethodVisible(false)
    setSelectCaseTemplateModalVisible(true)
  }

  // 选择病例模板弹窗，关闭
  const selectCaseTemplateModalClose = () => {
    setConsultationType(null)
    setSelectCaseTemplateModalVisible(false)
  }

  const load = !!loading.effects['expertAdvice/getExpertsInfo'] ||      // 专家详情接口loading
    !!loading.effects['expertAdvice/getCaseInfoByExpertsUserId'] ||     // 专家病例列表接口loading
    !!loading.effects['expertAdvice/getStarSpaceListBySearchUserId'] || // 专家空间列表接口loading
    !!loading.effects['expertAdvice/followAndCheck'] ||                 // 关注、取关接口loading
    !!loading.effects['expertAdvice/getExpertsQrCode']                  // 专家二维码接口loading
  return <>
    <Helmet>
      <title>{isMyPages ? "我的主页" : "主页"}</title>
      <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, shrink-to-fit=no" />
    </Helmet>
    <Spin spinning={load}>
      <div className={styles.wrap} id='wrap'>
        <div className={styles.header}>
          <NavBar title={isMyPages ? "我的主页" : "主页"} style={{background: '#FFF linear-gradient(135deg, #FFF 0%, #E4EEFC 100%)'}} />
        </div>
          <div>
            <div className={styles.user_info_wrap}>
              <div className={styles.user_info_bg}>
                <div className={styles.user_info_box}>
                  <div className={styles.user_info_img}>
                    {
                      imagePhotoPathShow ? <div className={styles.head_sculpture}>
                        <img src={imagePhotoPathShow} alt="" />
                      </div> : <div className={styles.no_head_sculpture} style={{ background: randomColor(id) }}>{processNames(name)}</div>
                    }
                    {
                      !isMyPages ? <>
                        {
                          isFocus == 0 ?
                            <div className={styles.follow_text} onClick={() => { followAndCheckFun(1) }}><img src={followIcon} alt="" />关注</div> :
                            <div className={styles.unfollow_text} onClick={() => { followAndCheckFun(0) }}><img src={unfollowIcon} alt="" />取消关注</div>
                        }
                      </> : null
                    }

                  </div>
                  <div className={styles.top_box} style={isMyPages ? { marginTop: 40 } : {}}>
                    <span className={styles.name}>{name}</span>
                    <span className={styles.doctor_text}>{postTitleDictName}</span>
                    {isExperts == 1 && depSubjectDictName || abilityLevelDictName ? <span className={styles.discipline_type}>{depSubjectDictName}{abilityLevelDictName ? "·" : null}{abilityLevelDictName}</span> : null}

                  </div>
                  <div className={styles.institution}>{organizationName}</div>
                  <div className={styles.bottom_box}>
                    <div className={styles.info_data_item}>关注 {focusCount || 0}</div>
                    <div className={styles.info_data_item}>粉丝 {fansCount || 0}</div>
                    <div className={styles.info_data_item}>GDP {personGdpCount || 0}<QuestionCircleFilled onClick={() => {
                      Modal.show({
                        bodyClassName: 'gdp_modal',
                        content: global.gdpExplain!=null&&ReactHtmlParser(global.gdpExplain[0].gdpContent)||'暂无GDP介绍',
                        closeOnMaskClick: true,
                      })
                    }} style={{fontSize: 12,color: '#999',marginLeft: 4}} /></div>
                  </div>
                </div>
              </div>
              {
                isExperts == 1 ? <div className={styles.brief_introduction_wrap}>
                  <div className={styles.brief_introduction_box}>
                    <div className={styles.brief_introduction_title}>简介</div>
                    {
                      introduceRef?.current?.offsetHeight > 40 ? <>
                        {
                          !isShowBtn ?
                            <div className={styles.brief_introduction_btn} onClick={() => { setIsShowBtn(true) }}>展开简介<img src={blueDownArrowIcon} alt="" /></div> :
                            <div className={styles.brief_introduction_btn} onClick={() => { setIsShowBtn(false) }}>收起简介<img src={blueUpArrowIcon} alt="" /></div>
                        }
                      </> : null
                    }

                  </div>
                  <div className={isShowBtn ? styles.brief_introduction_show : styles.brief_introduction_hide}>
                    <span ref={introduceRef} dangerouslySetInnerHTML={{ __html: intro }}></span>
                  </div>
                </div> : null
              }

            </div>
            <div className={styles.content}>
              <div className={styles.tab_wrap}>
                {
                  tabLists.map(item => {
                    if (isExperts != 1 && item.id == 3) {
                      return
                    }
                    if (!isMyPages && item.id == 6) {
                      return
                    }
                    return <div key={item.id} className={classNames({ [styles.tab_init]: true, [styles.tab_active]: tabType == item.id })} onClick={() => { onClickTabFun(item.id) }}>{item.val}</div>
                  })
                }
              </div>
              <div className={styles.tab_content_list} ref={listRef}>
                {/* 直播 */}
                {
                  tabType == 1 ?
                    <>
                      {
                        Array.isArray(spaceDataSource)&&<div>
                          {
                            (!isMyPages) && <div style={{
                              paddingLeft: '12px',
                              fontSize: '12px',
                              marginBottom: '20px'
                            }}>共 {total || 0} 条内容</div>
                          }
                          {
                            isMyPages ? <MyHomepageSpace starSpaceType={1}  spaceDataSource={spaceDataSource} mySpaceMoreOperateClose={mySpaceMoreOperateClose} mySpaceChooseFilter={mySpaceChooseFilter} /> :
                            <SpaceList componentData={{ dataList: spaceDataSource, config: { number: 2 } }} />
                          }

                          {
                            total>0 && <InfiniteScroll loadMore={spaceLoadMore} hasMore={hasMore} threshold={100} />
                          }

                        </div>}
                        {
                          spaceDataSource.length==0?<LoadingException exceptionStyle={{ paddingTop: 50, paddingBottom: 32 }} interfaceStatus={interfaceStatus} retryFun={retrySpaceFun} resetStatusFun={resetStatusFun} />:null
                        }
                    </> : null
                }
                {/* 会议 */}
                {
                  tabType == 2 ?
                    <>
                      {
                        Array.isArray(spaceDataSource)&&<div>
                          {
                            (!isMyPages) && <div style={{
                              paddingLeft: '12px',
                              fontSize: '12px',
                              marginBottom: '20px'
                            }}>共 {total || 0} 条内容</div>
                          }
                          {
                            isMyPages ? <MyHomepageMeeting starSpaceType={2}  spaceDataSource={spaceDataSource} mySpaceMoreOperateClose={mySpaceMoreOperateClose} mySpaceChooseFilter={mySpaceChooseFilter} /> :
                              spaceDataSource.map((item: any, index: number) => {
                                return <div key={index} style={{padding: '12px 12px 0'}}><MeetingCard style={{borderBottom:'1px solid #E1E4E7'}} key={index} item={item} /></div>
                              })
                          }

                          {
                            total>0 && <InfiniteScroll loadMore={spaceLoadMore} hasMore={hasMore} threshold={100} />
                          }

                        </div>}
                      {
                        spaceDataSource.length==0?<LoadingException exceptionStyle={{ paddingTop: 50, paddingBottom: 32 }} interfaceStatus={interfaceStatus} retryFun={retrySpaceFun} resetStatusFun={resetStatusFun} />:null
                      }
                    </> : null
                }
                {/* 是专家 && 病例 */}
                {
                    tabType == 3 && isExperts == 1 ?
                      <>
                        <div style={{paddingLeft: '12px', fontSize: '12px', marginBottom: '20px'}}>共 {excellentCase && excellentCase.length} 条内容</div>
                        {
                          Array.isArray(excellentCase) && excellentCase.length ? <CaseList componentData={{ dataList: excellentCase }} isShowImage={true} /> :
                          <LoadingException exceptionStyle={{ paddingTop: 50, paddingBottom: 32 }} interfaceStatus={interfaceStatus} retryFun={retryCaseFun} resetStatusFun={resetStatusFun} />
                        }
                      </> : null
                }

                {/* 帖子3合1 */}
                {tabType == 4 ? <H5PostGroup isMyPages={isMyPages}/> : null}
              </div>
            </div>
          </div>

        {/* 分享和问专家按钮 */}
        {
          getOperatingEnv() != "5" || getOperatingEnv() != "6" ? <div className={styles.bottom_btn_wrap}>
            <div
              className={getOperatingEnv() == "1" || getOperatingEnv() == "2" || getOperatingEnv() == "7" ? styles.bottom_share_btn : styles.bottom_share_btn_no}
              onClick={getOperatingEnv() == "1" || getOperatingEnv() == "2" ? () => {
                shareOnClick()
              } : null}><img src={wxIcon} alt=""/>分享
            </div>
            {
              isExperts == 1 && !isMyPages && (originalPictureTreatCosts || originalVideoTreatCosts) ? (
                <div className={styles.ask_experts_btn} onClick={selectConsultationMethodModalShow}>
                  <div className={styles.price_style_box}>
                    <div className={styles.title_style}>问同行</div>
                  </div>
                </div>
              ) : null
            }
          </div>:null
        }

        {/* 分享引导弹窗 */}
        <Mask style={{ '--z-index': "1000" }} opacity={0.7} visible={maskVisible} onMaskClick={shareCloseOnClick} />
        <div className={classNames(styles.fixed_share_box, { [styles.fixed_share_box_show]: maskVisible })}>
          <i className={styles.icon1}></i>
          <div className={styles.message_box}>
            <div>点击右上角</div>
            <div>发送到 微信好友 或者 分享到朋友圈</div>
          </div>
          <div className={styles.icon_box}>
            <i className={styles.icon2}></i>
            <i className={styles.icon3}></i>
          </div>
        </div>

        {/* 取消关注提示弹框 */}
        <TipsModal visible={unfollowVisible} cancelFn={tipsCancelFn} noCancelFn={tipsNoCancelFn} />
        {/* 问专家-选择指导方式弹窗 */}
        <SelectConsultationMethodModal
          visible={selectConsultationMethodVisible}
          expertsUserId={id} // 专家ID
          onCancel={selectConsultationMethodModalHide} // 关闭弹窗
          onClickConsultationMethod={onClickConsultationMethod} // 选择指导方式
        />
        {/* 问专家-选择病例模板弹窗 */}
        <SelectCaseTemplateModal
          visible={selectCaseTemplateModalVisible}
          expertsUserId={id} // 专家ID
          consultationType={consultationType} // 指导类型，1图文，2视频
          onCancel={selectCaseTemplateModalClose} // 关闭弹窗
        />
      </div>
    </Spin>
  </>
}
export default connect(({ global, expertAdvice, userInfoStore, loading }: any) => ({ global, expertAdvice, userInfoStore, loading }))(Index)
