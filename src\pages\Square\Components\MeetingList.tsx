/**
 * @Description: 广场-空间
 */
import React, {useState, useEffect, useRef} from 'react';
import { connect, history } from 'umi';
import classNames from 'classnames';
import MeetingCard from '@/components/MeetingCardInfo'; // 会议卡片
import MoreOperate from './MoreOperate'; // 更多操作弹框
import PosterModal from '@/pages/Poster/PosterModal'                  // 海报弹框
import meetingListCreactIcon from '@/assets/GlobalImg/meetingListCreactIcon.png';
import eArrow from '@/assets/GlobalImg/e_arrow.png';
import noDataImg from '@/assets/GlobalImg/no_data.png'; // 无数据图片
import { Toast, InfiniteScroll } from 'antd-mobile'; // 滚动加载
import NotDataRender from '@/components/NoDataRender'; // 暂无数据
import { Spin, Typography, Button} from 'antd';
import type { DateItem } from './CollectList';
import styles from './index.less';
import { getOperatingEnv, getDAesString } from "@/utils/utils";


const initState: {
  pageNum: number;
  pageSize: number;
  total: number;
  meetingList: DateItem[];
} = {
  pageNum: 1, // 当前页
  pageSize: 30, // 每页条数
  total: 0, // 总条数
  meetingList: [], // 空间数据集合
};

const MeetingList: React.FC = (props: any) => {
  const userInfoData = JSON.parse(localStorage.getItem('userInfo') || '{}');
  const { dispatch, loading, square } = props;
  const token = localStorage.getItem('access_token');
  const posterModalRef = useRef(null);
  const isRefreshRender = useRef(false);  // 判断是否操作刷新
  const [state, setState] = useState(initState); // 列表数据
  const [ meetingTab, setMeetingTab ] = useState(square.meetingTabState||2); // 会议tab
  const [ hasMore, setHasMore] = useState(true); // 更多数据状态
  const { pageNum, pageSize, total, meetingList } = state;
  const [ moreVisible, setMoreVisible ] = useState(false); // 更多操作弹框
  const [ meetingItem, setMeetingItem] = useState<any>({}); // 当前选择的会议
  const scrollDomRef = useRef<HTMLDivElement|null>(null);


  useEffect(() => {
    if(token){
      getCollectListFn()
    }
  }, [meetingTab])

  // 滚动到底部加载更多
  useEffect(() => {
    if (meetingList.length >= total) {
      setHasMore(false);
      (isRefreshRender?.current&&pageNum==1)&&getCollectListFn(); // 刷新后重置pageNum
    } else {
      setHasMore(true);
    }
  }, [meetingList, total]);

  // 获取收藏列表
  const getCollectListFn = () => {
    return dispatch({
      type: 'square/getStarSpaceList',
      payload: {
        pageNum,
        pageSize,
      },
    }).then((res) => {
      const { code, content } = res || {};
      if (res && code == 200) {
        const { total: responseTotal, resultList } = content || {};
        setState((prevState) => ({
          ...prevState,
          meetingList: [...prevState.meetingList, ...resultList],
          total: responseTotal,
          pageNum: prevState.pageNum + 1,
        }));
      } else {
        console.log('数据失败!');
      }
    });
  };

  // 加载更多数据
  const loadMore = async () => {
    await getCollectListFn();
  };

  // 跳转会议详情
  const goMyMeetingSpace = () => {
    if(!token){
      history.push({
        pathname: '/User/login',
        query: {
          redirectByPush: window.location.href,
        }
      })
      return
    }else{
      // 进入我的会议-历史会议模块
      history.push(`/Expert/ExpertDetails?id=${userInfoData.friUserId}&tabKey=2`);
      sessionStorage.setItem('myHomeSpaceFilter', JSON.stringify({
        meetingSpaceStatus:2
      }));
    }
  };

  // 更多操作下拉弹框
  const moreOperateBtn = (item: any) => {
    setMeetingItem(item);
    // 存储当前tab
    dispatch({
      type: 'square/save',
      payload: {
        meetingTabState: meetingTab,
      },
    })
    setMoreVisible(true);
  }

  // 更多操作下拉弹框关闭按钮
  const closeMoreOperateFn = () => {
    setMoreVisible(false)
  }

  // 海报
  const posterBtn = (item: any) => {
    posterModalRef && posterModalRef.current.init(1, item)
  }

  // 分享操作
  const shareBtn = (item: any) => {
    posterModalRef && posterModalRef.current.init(2, item)
  }

  // 刷新页面
  const refreshFn = () => {
    setState(initState);
    isRefreshRender.current = true;
  }

  const onCopy = () => {
    Toast.show('复制链接成功!')
  }

  const getStarMeetingListLoading = !!loading.effects['square/getStarSpaceList']; // loading
  return (
    <div className={classNames(styles.meetingList_box,{[styles.meetingList_box_bgWhite]:!token})}>
      <div className={styles.meetingList_content} ref={scrollDomRef}>
        <Spin spinning={getStarMeetingListLoading}>
          {(meetingTab ==2)?<div style={{display:'flex',padding:'12px', justifyContent:'end', alignItems:'center'}}>
            <span style={{ color:'#0095FF',marginRight:'5px'}} onClick={goMyMeetingSpace}>历史会议<img src={eArrow} width={10} height={10} alt=""/></span>
          </div>:<div style={{display:'flex',paddingBottom:'8px'}}></div>}
          {meetingList && meetingList.length ? (
            <>
              {
                meetingList.map((item, index) => {
                  return <div key={index} className={styles.meetingList_item}>
                    {meetingTab == 2&& <div>
                      {
                        ((index>0&&item?.appointmentStartDateDescs!=meetingList[index-1].appointmentStartDateDescs||index==0))&&<h3>{item.appointmentStartDateDescs}</h3>
                      }
                      <h4>{item?.appointmentStartMinTime}-{item?.appointmentStartMaxTime}</h4>
                    </div>
                    }
                    <MeetingCard style={{padding: '12px 12px 8px'}} cardBorderType={meetingTab==2?'roundedTop':'rounded'} key={index} item={item} />
                    {
                      meetingTab == 2 && <div className={styles.item_myMeeting_box}>
                        <span className={styles.item_moreOperate_btn} onClick={() => moreOperateBtn(item)}>更多操作</span>
                        <span>
                          <span className={styles.item_poster_btn} onClick={() => posterBtn(item)}>生成海报</span>
                          {
                            (getOperatingEnv() == '2' || getOperatingEnv() == '7' || getOperatingEnv() == '5' || getOperatingEnv() == '6') ?
                              <span className={styles.item_share_btn} onClick={() => shareBtn(item)}>分享</span>
                              :
                              <span className={styles.item_copy_btn}>
                                <Typography.Paragraph copyable={{
                                  text: `${window.location.origin}/PlanetChatRoom/Meet/${item.id}?shareUserId=${userInfoData?.friUserId}&isShare=1` + (item.password ? `&pwd=${getDAesString(item.password,'arrail-dentail&2', 'arrail-dentail&3')}` : ''),
                                  icon: [<div>复制链接</div>, <div>复制链接</div>],
                                  tooltips: ['', ''],
                                  onCopy: onCopy,
                                }}></Typography.Paragraph>
                              </span>
                          }
                        </span>
                      </div>
                    }
                  </div>
                })
              }
              <InfiniteScroll loadMore={loadMore} hasMore={hasMore} threshold={30}/>
            </>
          ) : (
            token?<NotDataRender text={`${meetingTab == 1 ? '暂无公开会议' : '暂无我的会议'}`}/>:<div className={styles.meeting_nologin}>
              <img src={noDataImg} alt=""/>
              <p>登录后查看我的会议</p>
              <Button type="primary" onClick={()=>{
                // 未登录跳转至登录页
                history.push({
                  pathname: '/User/login',
                  query: {
                    redirectByPush: window.location.href,
                  }
                })
              }}>去登录</Button>
            </div>
          )}
        </Spin>
      </div>

      {/* 更多操作 */}
      <MoreOperate meetingItem={meetingItem} visible={moreVisible} close={closeMoreOperateFn} refreshFn={refreshFn} />

      {/* 海报组件 */}
      <PosterModal ref={posterModalRef}/>
    </div>
  );
};
export default connect(({ square, loading }: any) => ({ square, loading }))(MeetingList);
