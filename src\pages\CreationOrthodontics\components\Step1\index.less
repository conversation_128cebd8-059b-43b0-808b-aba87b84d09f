.page_info {

}
.title_box {
  font-size: 20px;
  font-weight: 500;
  color: #000000;
  line-height: 23px;
  text-align: center;
  margin-bottom: 32px;
  margin-top:36px;
}

.basic_information {
  width: 744px;
  height: 96px;
  background: #F8FAFD;
  border-radius: 2px 2px 2px 2px;
  opacity: 1;
  margin-bottom: 16px;
  display: flex;
  padding-left: 24px;
  padding-right: 16px;
}

.Medical_record_number_warp {
  display: flex;
}

.Medical_record_number_lable {
  font-size: 14px;
  font-weight: 400;
  color: #666666;
  line-height: 14px;
}

.Medical_record_number_value {
  font-size: 14px;
  font-weight: 400;
  color: #000000;
  line-height: 14px;
}

.basic_information_flex {
  width: calc(100% - 100px);
  display: flex;
  flex-direction: column;
  justify-content: center;

  .basic_disposition_Warp {
    display: flex;
    margin-bottom: 9px;
  }
  .basic_disposition {
    font-size: 18px;
    line-height: 18px;
    font-weight: 500;
    color: #000000;
    margin-right: 16px;
  }
}

.basic_research_btn_warp {
  width: 100px;
  display: flex;
  align-items: center;
  user-select: none;
  cursor: pointer;

  .basic_research_Icon {
    width: 17px;
    height: 17px;
    background: url("~@/assets/CreationOrthodontics/CreationOrthodontics_basic_research_Icon.png");
    background-size: 17px 17px;
    opacity: 1;
    margin-right: 10px;
    display: inline-block;
    position: relative;
    top: 2px;
  }

  .basic_research_btn {
    font-size: 16px;
    font-weight: 400;
    color: #0095FF;
    line-height: 16px;
  }
}

.basic_information_info {
  width: 744px;
  height: 100%;


  .Item_pattern {
    display: flex;
    width: 100%;
    padding-left: 11px;


    .Item_title_lable {
      font-size: 14px;
      font-weight: 400;
      color: #000000;
      line-height: 32px;
      margin-right: 8px;
      width: 110px;
      text-align: right;
    }

    .Item_title_value {
      width: calc(100% - 120px);

      .Item_title_value_content_Warp {
        width: 100%;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
      }

      .Item_title_value_content {
        display: flex;
        font-size: 14px;
        font-weight: 400;
        color: #000000;
        line-height: 26px;
        flex-direction: row;
        align-items: center;
        margin-right: 40px;
        margin-bottom: 15px;
      }
    }
  }
}

.warp_content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.submitWarp {
  display: flex;
  justify-content: space-around;
  margin-top: 48px;
  padding-bottom: 53px;
  .submitBox {
    display: flex;
    .submit_btn_Cancel {
      width: 92px;
      height: 36px;
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      border: 1px solid #0095FF;
      font-size: 14px;
      font-weight: 400;
      color: #0095FF;
      line-height: 36px;
      user-select: none;
      cursor: pointer;
      text-align: center;
      margin-right: 30px;
    }
    .submit_btn_Enter {
      width: 92px;
      height: 36px;
      background: #0095FF;
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      font-size: 14px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 36px;
      text-align: center;
      user-select: none;
      cursor: pointer;
    }
  }
}
