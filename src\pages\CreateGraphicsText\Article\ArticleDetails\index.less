@import '../../../../utils/imageText.less';
.spin {
  height: 100%;
  :global {
    .ant-spin-container {
      height: 100%;
    }
  }
}

.container {
  padding-top: 44px;
  padding-bottom: 52px;
  height: 100%;
  overflow-y: auto;
}

.article_title {
  font-size: 20px;
  color: #000;
  line-height: 23px;
  padding: 8px 16px 12px;
  font-weight: 600;
}

.user_card_wrap {
  padding: 0 16px;
}

.image_text_content_wrap {
  padding: 20px 16px 12px;
}

.kingdom_wrap {
  padding: 0 16px;
  margin-bottom: 11px;
}

.gdp_wrap {
  display: flex;
  align-items: center;
  padding: 0 16px;
  margin-bottom: 24px;
  & > i {
    width: 14px;
    height: 14px;
    margin-right: 4px;
    background: url("../../../../assets/GlobalImg/gdp.png") no-repeat center;
    background-size: 100% 100%;
  }
  & > span {
    font-size: 12px;
    color: #777;
    line-height: 18px;
  }
}

.no_data_wrap {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-y: auto;
}
