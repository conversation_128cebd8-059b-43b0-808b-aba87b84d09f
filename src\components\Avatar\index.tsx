import React from 'react';
import styles from './index.less'; // 导入组件样式
import { randomColor, processNames } from '@/utils/utils'; // 导入相关的实用函数
import _ from 'lodash';

const Avatar = ({ userInfo, size, style = {} }) => {
  size = size || 36;
  const containerStyle = {
    background: userInfo && userInfo.userId ? randomColor(userInfo.userId) : 'none',
    width: size ? size : 36,
    height: size ? size : 36,
    ...style,
  };
  return (
    <div style={containerStyle} className={styles.video_Title_box_left_avatar}>
      {!!userInfo  && userInfo.headUrlShow &&
        (<img className={styles.video_Title_box_left_avatar_img} src={userInfo.headUrlShow} alt="" />)
      }
      {
        userInfo && !userInfo.headUrlShow &&
        (
          <div
            className={styles.head_sculpture_name}
            style={{
              fontSize:_.floor(size / 2,2),
              background: userInfo && userInfo.userId ? randomColor(userInfo.userId) : 'none'
            }}
          >
            {userInfo && processNames(userInfo.name)}
          </div>
        )
      }
    </div>
  );
};

export default Avatar;
