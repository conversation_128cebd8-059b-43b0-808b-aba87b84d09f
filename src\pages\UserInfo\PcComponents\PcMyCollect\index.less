.content {
  width: 100%;
  height: calc(100vh - 343px);
  overflow: hidden;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}
.tab_wrap {
  width: 100%;
  height: 43px;
  background: #FFFFFF;
  border-radius: 8px;
  padding: 16px 20px 0;
  margin-bottom: 12px;
  display: flex;

  .tab_init {
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    line-height: 16px;
    margin-right: 16px;
    height: 27px;
    cursor: pointer;

    &.tab_active {
      font-size: 14px;
      font-weight: 600;
      color: #000000;
      line-height: 16px;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 12px;
        height: 3px;
        background: #000000;
        border-radius: 6px 6px 6px 6px;
      }
    }
  }
}

.tab_content {
  width: 100%;
  height: 100%;

  :global {
    .ant-spin-container {
      width: 100%;
      height: 100%;
    }
  }
}

// .tab_content_list {
//   width: 100%;
//   height: 680px;
//   overflow-y: auto;
//   padding: 20px 0;
//   display: flex;
//   flex-wrap: wrap;
//   background: #fff;
//   border-radius: 8px 8px 8px 8px;

//   .space_wrap {
//     width: 100%;
//     display: flex;
//     flex-wrap: wrap;
//     margin-bottom: 30px;

//     .space_list {
//       width: 189px;
//       margin-bottom: 16px;
//       margin-left: 20px;
//     }
//   }

//   .space_box {
//     width: 189px;
//   }
// }

// .tab_content_case {
//   width: 100%;
//   height: 680px;
//   overflow-y: auto;
//   display: flex;
//   flex-wrap: wrap;
//   justify-content: space-between;
//   align-items: flex-start;

//   .tab_case_list {
//     width: 422px;
//     margin-bottom: 16px;
//   }
// }

// .noDataStyle {
//   width: 100%!important;
//   justify-content: inherit;
// }

// div::-webkit-scrollbar {
//   width: 4px;
// }
// div::-webkit-scrollbar-thumb {
//   border-radius: 10px;
//   -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
//   opacity: 0.2;
//   // background: ;
// }
// div::-webkit-scrollbar-track {
//   -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
//   border-radius: 0;
//   // background: fade(@primary-color, 30%);
// }