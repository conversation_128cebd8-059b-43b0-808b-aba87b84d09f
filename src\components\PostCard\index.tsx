/**
 * @Description: 帖子卡片组件
 */
import React, { useEffect, useRef, useState } from 'react';
import { history } from 'umi';
import classNames from 'classnames';
import { stringify } from 'qs';
import { useInView } from 'react-intersection-observer';  // 判断元素是否可见插件（作用：用户停留在某一内容超过3秒，展示评论输入框）
import { ImageViewer } from 'antd-mobile';
import styles from './index.less';

import blueAssociatedIcon from '@/assets/GlobalImg/blue_associated.png'; // 关联王国小图标
import ListComments from '../ListComments'; // 点赞/评论
import UserCardByImageText from '@/components/UserCardByImageText' // 用户卡片

interface PropsType {
  style?: any;        // 样式
  pageType?: any;     // 从哪个页面过来的标识，推荐首页过来传 1:场推荐 2:话题页面 3:我的主页草稿箱 4:国王详情 5:搜索页面
  isMyPages?: any;    // 是否是我的主页
  refreshDataById?: any; // 刷新指定id的数据
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {
    style, pageType, isMyPages, item, refreshDataById,
    isShowMoreOperate = false, // 是否展示点点点更多操作
  } = props || {};

  // 元素是否可见的配置
  const { ref, inView } = useInView({
    threshold: 0.6,  // 何时触发回调的阈值
  });
  const timer = useRef<NodeJS.Timeout| null>(null); // 定时器

  const [showComments , setShowComments] = useState(false); // 是否展示评论框

  const {
    createDate,       //: [创建时间] : "2024-01-09 14:53:47"
    createUserId,     //: [创建人id] : 60
    expertsInfo,      //: [专家信息] : null
    forwardDescribe,  //: [转发描述] : null
    gdp,              //: [页面GDP] : 210
    headUrlShow,      //: [用户头像] : null
    id,               //: [主键ID] : 57
    imageTextContent, //: [文章、帖子内容] : null
    imageTitle,       //: [标题] :null
    imageType,        //: [图文类型：1.文章 2.帖子 3.外链 4.空间] : 2
    isExperts,        //: [是否是专家：0:否，1:是] : 0
    isFocus,          //: [0未关注 1已关注] : 0
    isForward,        //: [是否转发：1.转发 0，非转发] : null
    isSpotLike,       //: [是否点赞 1是 0否] : 0
    kingdomId,        //: [关联王国ID] : 1
    kingdomName,      //: [关联王国名称] : "数字化讨论"
    outerChain,       //: [外链地址] : null
    spaceId,          //: [空间ID] :null
    spaceStatus,      //: [空间状态: 1直播中、2预约中、3弹幕轰炸中] : null
    spotLikeCount,    //: [点赞数量] : 0
    spotLikeUserList, //: [点赞用户信息，最多3条] : []
    textImgList,      //: [关联的图片] : null
    topicInfoList,    //: [关联的话题信息] : null
    userName,         //: [用户名称] : "志君"
    status,           //: [状态] : 1.审核通过（已发布） 0.未审核 2.审核未通过 3.草稿
    operateDateDescs,
  } = item || {};

  useEffect(() => {
    // 是推荐首页,则展示评论框
    if (pageType == '1') {
      clearTimeout(timer.current);
      // 判断当前内容是否可见,并停留3秒时,展示评论框
      if (inView) {
        timer.current = setTimeout(() => {
          console.log('显示')
          setShowComments(true);
        }, 3000)
      } else {
        setShowComments(false);
      }
    }
  }, [inView]);

  // 跳转王国详情
  const onClickKingdom = () => {
    if (window.location.pathname == `/Kingdom/${kingdomId}`) { return; }
    history.push(`/Kingdom/${kingdomId}`)
  }


  // 查看大图
  const previewBigImage = (index) => {
    ImageViewer.Multi.show({
      defaultIndex: index,
      images: textImgList.map(it => it.imageUrlShow),
      getContainer: () => document.getElementById('root'),
    })
  }

  // 组件销毁时，关闭查看大图弹窗
  useEffect(() => {
    return () => {
      ImageViewer.clear()
    }
  },[])

  // 点击跳转详情
  const jumpDetailsFn = (e) => {
    if (e && e.target && e.target.dataset && e.target.dataset.type == 'topic') {
      // 话题，老版结构
      // 如果当前在话题主页，并且显示的就是点击的话题
      if (history.location.pathname == '/CreateGraphicsText/TopicHome' && e.target.dataset.id == history.location.query.topicId) {
        return
      }
      history.push(`/CreateGraphicsText/TopicHome?topicId=${e.target.dataset.id}`)
    } else if (e.target && e.target.parentNode && e.target.parentNode.dataset && e.target.parentNode.dataset.type == 'topic') {
      // 话题，新版结构
      // 如果当前在话题主页，并且显示的就是点击的话题
      if (history.location.pathname == '/CreateGraphicsText/TopicHome' && e.target.parentNode.dataset.id == history.location.query.topicId) {
        return
      }
      history.push(`/CreateGraphicsText/TopicHome?topicId=${e.target.parentNode.dataset.id}`)
    } else {
      // 是从我的主页过来的，需要跳转编辑页
      if (isMyPages && status == 3) {
        return history.push(`/CreateGraphicsText/CreatePost?${stringify({id: id})}`);
      }
      // 是推荐首页,则跳转详情页
      history.push(`/CreateGraphicsText/PostDetails?${stringify({id: id})}`)
    }
  }

  // 关注或取消关注回调，0 取消关注，1 关注
  const handleFollowAndCheck = (isFocus2) => {
    if (refreshDataById) {
      refreshDataById(item.id)
    }
  }

  // 删除或下架回调
  const handleDeleteOrLow = () => {
    if (refreshDataById) {
      refreshDataById(item.id)
    }
  }

  return (
    <div className={styles.post_wrap} style={style} ref={ref}>
      {/* 用户信息 */}
      <UserCardByImageText
        headUrlShow={headUrlShow}
        userName={userName}
        createUserId={createUserId}
        isExperts={isExperts}
        operateDateDescs={operateDateDescs}
        isFocus={isFocus}
        expertsInfo={expertsInfo}
        handleFollowAndCheck={handleFollowAndCheck}
        isShowMoreOperate={isShowMoreOperate}
        id={id}
        imageType={imageType}
        status={status}
        handleDeleteOrLow={handleDeleteOrLow}
        style={{marginBottom: 12}}
      />

      <div className={styles.content}>
        <div
          className={classNames(styles.post_info)}
          onClick={jumpDetailsFn}
        >
          <div className={'ql-editor'} dangerouslySetInnerHTML={{__html: imageTextContent}}></div>
        </div>
        { (Array.isArray(textImgList) && textImgList.length != 0) &&
          <div  onClick={jumpDetailsFn} className={styles.post_img_info}>
            {
              textImgList.length < 3 ?
              <div className={classNames([styles.img_wrap], {[styles[`img_length${textImgList.length}`]]: true})}>
                {
                  textImgList && textImgList.length && textImgList.map((item, ind) => (
                    <div key={ind} className={styles.img_box}
                         onClick={(e)=>{
                          e.stopPropagation();
                          previewBigImage(ind)
                          return;
                      }}>
                      <img src={item.imageUrlShow} alt="" />
                    </div>
                  ))
                }
              </div> :
              <div className={classNames([styles.img_wrap], {[styles[`img_length3`]]: true})}>
                <div className={styles.right_img} onClick={(e)=>{e.stopPropagation();previewBigImage(0);return;}}><img src={textImgList[0].imageUrlShow} alt="" /></div>
                <div className={styles.left_img}>
                  <div className={styles.img_box} onClick={(e)=>{e.stopPropagation();previewBigImage(1);return;}}><img src={textImgList[1].imageUrlShow} alt="" /></div>
                  <div className={styles.img_box} onClick={(e)=>{e.stopPropagation();previewBigImage(2);return;}}><img src={textImgList[2].imageUrlShow} alt="" /></div>
                </div>
                {textImgList.length > 3 ? <div className={styles.img_num}>+{textImgList.length - 3}</div> : null}
              </div>
            }
          </div>
        }
      </div>

      {/* 王国 */}
      {
        !!kingdomName &&
        <div className={styles.kingdom_wrap} onClick={onClickKingdom}>
          <img src={blueAssociatedIcon} width={14} height={14} alt="" />
          <span>{kingdomName}</span>
        </div>
      }

      {/* 评论组件 */}
      <ListComments
        showComments={showComments}
        pageType={pageType}
        commentJupm={jumpDetailsFn}
        item={item}
      />
    </div>
  )
}

export default Index
