.wrap {
  width: 100%;
  // height: 122px;
  background: #FFFFFF;
  padding: 0 0 16px 16px;
  .header{
    height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #E1E4E7;
    .header_icon{
      display: inline-block;
      width: 20px;
      height: 20px;
      &>img{
        width: 100%;
        height: 100%;
      }
    }
    .header_title{
      margin-left: 8px;
      font-size: 15px;
      font-weight: 500;
      color: #000000;
    }
    .header_title_tips{
      font-size: 13px;
      font-weight: 400;
      color: #999999;
      margin-left: 12px;
    }
    .header_status{
      font-size: 16px;
      font-weight: 500;
      color: #0095FF;
      flex: 1;
      text-align: right;
      padding-right: 16px;
    }
    .header_close_status{
      font-size: 16px;
      font-weight: 500;
      color: #AAAAAA;
      flex: 1;
      text-align: right;
      padding-right: 16px;
    }
  }
  .content{
    padding-top: 4px;
    &>p{
      margin-bottom: 0;
      margin-top: 8px;
      font-size: 13px;
      font-weight: 400;
      color: #999999;
    }
  }
}