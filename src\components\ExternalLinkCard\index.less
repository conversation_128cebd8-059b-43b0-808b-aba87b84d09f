.external_link_wrap {
  width: 100%;
  background: #fff;
  padding: 16px;
  margin-bottom: 10px;
}

.content {
  width: 100%;

  .title {
    font-size: 14px;
    font-weight: 400;
    color: #000000;
    line-height: 20px;
    word-break: break-all;
    margin-bottom: 8px;
  }

  .no_picture_box {
    width: 100%;
    height: 64px;
    background: #FFFFFF;
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
    border: 1px solid #EBEBEB;
    display: flex;
    padding: 8px;
    box-sizing: border-box;

    .init_img {
      width: 48px;
      height: 48px;
      margin-right: 8px;
      flex-shrink: 0;

      img {
        width: 48px;
        height: 48px;
      }
    }

    .link_content {
      flex: 1;
      overflow: hidden;

      .link_title {
        font-size: 13px;
        font-weight: 400;
        color: #666666;
        line-height: 18px;
        word-break: break-all;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .link_more {
        font-size: 13px;
        font-weight: 400;
        color: #0095FF;
        line-height: 18px;

        img {
          width: 16px;
          height: 16px;
        }
      }
    }
  }

  .picture_box {
    width: 100%;
    background: #F8F8F8;
    border-radius: 0px 0px 4px 4px;
    overflow: hidden;

    .img {
      width: 100%;
      height: 142px;
      overflow: hidden;
      .img_box {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .link_more {
      padding: 8px 6px;
      font-size: 13px;
      font-weight: 400;
      color: #0095FF;
      line-height: 18px;
      background: #F5F6F8;

      img {
        margin-right: 2px;
        width: 16px;
        height: 16px;
      }
    }
  }
}

// 王国
.kingdom_wrap {
  display: inline-flex;
  align-items: center;
  column-gap: 4px;
  height: 19px;
  padding: 0 4px;
  border-radius: 4px;
  background: #E4F0FC;
  font-size: 11px;
  color: #0095FF;
  margin-top: 8px;
}
