/**
 * @Description: PC-指导模块头部组件
 * @author: 赵斐
 */
import React from 'react';
import { history } from 'umi';
import { getArrailUrl } from '@/utils/utils'
import styles from './index.less';
import pcGobackIcon from '@/assets/GlobalImg/pc_goback.png'
interface PropsType {
  consultationType: any,  // 指导类型(1图文、2视频、3正畸)
  processNode: any,       // 指导节点
  isFinish: any,          // 当前节点是否完成(1是、0否)
  isGoback: string,       // 是否展示返回按钮
  headerStatus: number    // 支付状态(0已取消、1未支付(待评估)、2去支付(已评估)、3已支付)
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  // 是否嵌套在5i5ya的iframe中
  const isInIframe = (self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya')

  const { consultationType, processNode, isFinish, isGoback, headerStatus } = props;

  // 返回
  const goBack = () => {
    // 在5i5ya的iframe中
    if (isInIframe) {
      const postData = {
        dataType: 'goBack',       // 页面地址onchange事件
      }
      console.log('子级发送数据：', postData, getArrailUrl())
      window.parent.postMessage(postData, getArrailUrl())
      return
    }

    if (isGoback == '1') {
      return
    }
    history.goBack()
  }

  // 指导状态展示
  const TextDisplayFun = () => {
    if (consultationType == '1') {
      let type = headerStatus == 0 ? 4 : processNode == 5 ? 1 : processNode == 6 && isFinish == 0 ? 2 : processNode == 6 && isFinish == 1 ? 3 : null
      switch (type) {
        case 1:
          return "病例资料被查看"
        case 2:
          return "问题被回复并对话"
        case 3:
          return "结束指导交易成功"
        case 4:
          return "已取消"
      }
    } else if (consultationType == '2') {
      let type = headerStatus == 0 ? 7 : processNode == 5 ? 1 : processNode == 6 ? 2 : processNode == 7 ? 3 : processNode == 8 ? 4 : processNode == 9 && isFinish == 0 ? 5 : processNode == 9 && isFinish == 1 ? 6 : null
      switch (type) {
        case 1:
          return "病例资料被查看"
        case 2:
          return "预约视频会议"
        case 3:
          return "视频沟通"
        case 4:
          return "结束指导"
        case 5:
          return "支付指导费用"
        case 6:
          return "交易成功"
        case 7:
          return "已取消"
      }
    }else if (consultationType == '3') {
      switch (processNode) {
        case 7:
          return "病例资料被查看"
        case 8:
          return "审核驳回"
        case 9:
          return "审核通过"
        case 10:
          return "结束指导"
      }
    }
  }

  return (
    <div className={styles.header}>
      <div className={styles.header_title} onClick={() => { goBack() }}>
        {
          isGoback != '1' ? <img className={styles.header_title_icon} src={pcGobackIcon} alt="icon" /> : null
        }
        {
          consultationType == '3' ? <>正畸方案审核{processNode > 6 ? "-" : null}{TextDisplayFun()}</>:<>
          {consultationType == '1' ? '图文指导' :consultationType == '2' ? '视频指导':null}{processNode >= 5 ? "-" : null}{TextDisplayFun()}
          </>
        }
      </div>
    </div>
  )
}
export default Index
