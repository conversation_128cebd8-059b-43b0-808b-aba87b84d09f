import {
  addKingdom,
  addSpace,
  attendeeSearchUserByQueryKey,
  checkKingdomName,
  checkSpacePassword,
  checkSuperAccount,
  deleteSpaceFromList,
  editKingdomInfo,
  editSpaceInfo,
  editUserinfo,
  exitLogout,
  geKingdomList,
  getAttendeeSearchUserByBizGroupId,
  getBizGroupByTenantId,
  getCollectList,
  getCreateAndJoinKingdomList,
  getEchoSelfAuthInfo,
  getFriUserToScholarship,
  getH5FocusList,
  getH5UserInfo,
  getIdentityTypeDict,
  getJsapiTicket,
  getKingdomInfo,
  getKingdomInfoByEdit,
  getKingdomSpaceList,
  getMemberIconList,
  getScholarshipDetailList,
  getSelfAuthInfo,
  getSpaceCover,
  getSpaceInfoByEdit,
  getSpacePosterInfo,
  getStarSpaceCollect,
  getSwitchWaistcoatToken,
  getVodUploadSign,
  joinOrQuitKingdom,
  saveSelfAuth,
  searchUserListByQueryKey,
  setSwitchWaistcoatAccount,
  shareUpdateByType,
  unsubscribe,
  getEnterpriseUserInfoApi,
  staffResignApi,
  getInviteImgApi,
} from '@/services/userInfo'


const stateInfo = {
  editInfo: {},               // 编辑信息
  isSuperAccount: false,      // 是否为超级账号true是，false否
  createModalVisible: false,  // 是否展示创建空间王国下拉弹框
  VisibleBySelectGuest:false, // 邀请参会人弹窗
  selectCreateType: 0,        // 创建空间王国选中的tab值 1:空间,3:国王,4:帖子,5:文章,6:外链,7:会议
  selectedKingdom: null,      // 已选择-王国
  selectedCompere: null,      // 已选择主持人
  selectedGuest: [],          // 已选择嘉宾
  creatTabSelectDate: {},     // 包含创建王国空间（标题文案；是否有返回按钮；是否可以返回；是否可以选择王国（在创建成功页中创建王国空间时使用））
  spaceCoverUrl: null,        // 空间封面路径
  spaceCoverUrlView: null,    // 空间封面展示路径
  spaceAdvertisingUrl: null,  // 空间广告路径
  spacePasswordText: null,    // 密码
  spaceName: null,            // 空间名称
  spaceIntroduceVal: null,    // 空间介绍
  selectedKing: null,         // 已选择-国王
  kingdomName: null,          // 王国名称
  kingdomIntroduce: null,     // 王国介绍
  kingdomCoverUrl: null,      // 王国封面图路径
  spaceVideoId: null,         // 空间视频id
  spaceVideoUrl: null,        // 空间视频上传文件地址
  spaceVideoName: null,       // 空间视频文件名
  appointmentStartTime: null, // 空间预约时间
  isEditSpace: false,         // 是否编辑空间
  isEditKingdom: false,       // 是否编辑王国
  spaceFromEnter: {},         // 从哪里进入的创建空间/编辑空间(pageType: 1 我的主页、2 空间详情、3 王国详情； refreshFn: 刷新页面的函数； tipText: 创建成功后提示内容； isJumpOrRefresh 是否关闭弹框并跳转； spaceId: 空间id)
  kingdomFromEnter: {},       // 从哪里进入的王国入口数据(pageType: 1 我的主页、2 空间详情、3 王国详情;  refreshFn: 刷新页面的函数;   tipText: 创建成功后提示内容； KingdomId：王国id)
  isPasswordSwitchChecked: false,        // 创建空间，密码开关是否选中
  spaceTypeId: null,          // 空间类型 1专家讲课 2指导 3复杂病例讨论
  meetingLevel: null,         // 1:集团, 2:区域, 3:诊所
  meetingLevelName: null,     // 指导级别名称
  spaceTypeName: null,        // 空间类型名称
  spectatorType: 0,           // 观众类型 0默认所有人 1指定王国成员可见 2指定企业品牌用户可见
  allViewersState: true,      // 选择观众默认所有人可见状态
  kingdomListArr: [],         // 观众-王国列表数据
  selectedKingdomAudience: [], // 观众-选择可见王国
  enterpriseUserData: [],      // 选择可见企业/品牌用户，当前企业下的机构数据
  enterpriseUserTab: 0,        // 选择可见企业/品牌用户，页面tab
  enterpriseUserSelectData: [], // 选择可见企业/品牌用户，选择后的数据
  enterpriseText: null,         // 选择可见企业/品牌用户，弹框名称
  isAutoRecord:null,            // 是否自动录制
  allowApplications:null,       // 是否允许无密码进入会议申请
  topicHomeTopicId: null,       // 话题主页打开创建弹窗时，保存话题ID，跳转创建文章和帖子要带过去
  topicHomeTopicName: null,     // 话题主页打开创建弹窗时，保存话题name，跳转创建文章和帖子要带过去
  isToEnterprise:true,          // [会议]-是否面向企业内部
  isPublic: null,               // [会议]-是否允许旁听
  expectDuration: '60',         // [会议]-预计会议时长
  starSpaceType: null,          // 当前暂存类型1:直播 2:会议
  appointmentStartTime:null,    // 会议预约开始时间
  openCropperImgModal:null,     // 裁切封面弹窗
  spaceCoursewares:[],          // 课件资源列表
}

export default {
  namespace: 'userInfoStore',
  state: { ...stateInfo },

  effects: {
    // 获取用户信息
    *getUserInfo({ payload }:any, { put, call }:any) {
      const response = yield call(getH5UserInfo, payload);
      return response
    },
    // 设置切换马甲账号信息
    *setSwitchWaistcoatAccount({ payload }:any, { put, call }:any) {
      const response = yield call(setSwitchWaistcoatAccount, payload);
      return response
    },
    // 获取切换马甲新的token
    *getSwitchWaistcoatToken({ payload }:any, { put, call }:any) {
      const response = yield call(getSwitchWaistcoatToken, payload);
      return response
    },
    // 获取会员图标列表
    *getMemberIconList({ payload }:any, { put, call }:any) {
      const response = yield call(getMemberIconList, payload);
      return response
    },

    // 修改用户信息
    *editUserinfo({ payload }:any, { put, call }:any) {
      const response = yield call(editUserinfo, payload);
      return response
    },

    // H5-获取我关注的用户专家列表
    *getH5FocusList({ payload }:any, { put, call }:any) {
      const response = yield call(getH5FocusList, payload);
      return response
    },

    // 验证用户是否为超级账户
    *checkSuperAccount({ payload }:any, { put, call }:any) {
      const response = yield call(checkSuperAccount, payload);
      return response
    },

    // 根据姓名、手机号，模糊查询用户信息
    *searchUserListByQueryKey({ payload }:any, { put, call }:any) {
      const response = yield call(searchUserListByQueryKey, payload);
      return response
    },

    // 校验王国名称
    *checkKingdomName({ payload }:any, { put, call }:any) {
      const response = yield call(checkKingdomName, payload);
      return response
    },

    // 创建空间
    *addSpace({ payload }:any, { put, call }:any) {
      const response = yield call(addSpace, payload);
      return response
    },

    // 新增王国
    *addKingdom({ payload }:any, { put, call }:any) {
      const response = yield call(addKingdom, payload);
      return response
    },

    // 加入或退出王国
    *joinOrQuitKingdom({ payload }:any, { put, call }:any) {
      const response = yield call(joinOrQuitKingdom, payload);
      return response
    },

    // 查询王国详情
    *getKingdomInfo({ payload }:any, { put, call }:any) {
      const response = yield call(getKingdomInfo, payload);
      return response
    },

    // 获取王国详情下的直播or会议列表数据
    *getKingdomSpaceList({ payload }:any, { put, call }:any) {
      const response = yield call(getKingdomSpaceList, payload);
      return response
    },


    // 校验空间密码
    *checkSpacePassword({ payload }:any, { put, call }:any) {
      const response = yield call(checkSpacePassword, payload);
      return response
    },

    // 查询我创建的王国和我加入的王国
    *getCreateAndJoinKingdomList({ payload }:any, { put, call }:any) {
      const response = yield call(getCreateAndJoinKingdomList, payload);
      return response
    },

    // 我的收藏-分页获取优秀病例数据
    *getCollectList({ payload }:any, { put, call }:any) {
      const response = yield call(getCollectList, payload);
      return response
    },

    // 我的收藏-分页获取收藏空间数据
    *getStarSpaceCollect({ payload }:any, { put, call }:any) {
      const response = yield call(getStarSpaceCollect, payload);
      return response
    },

    // 注销UC账号
    *unsubscribe({ payload }:any, { put, call }:any) {
      const response = yield call(unsubscribe, payload);
      return response
    },

    // 获取空间海报信息
    *getSpacePosterInfo({ payload }:any, { put, call }:any) {
      const response = yield call(getSpacePosterInfo, payload);
      return response
    },

    // 获取微信公众号下JSAPI权限config信息
    *getJsapiTicket({ payload }:any, { put, call }:any) {
      const response = yield call(getJsapiTicket, payload);
      return response
    },

    // 分享操作更新gdp等数据
    *shareUpdateByType({ payload }:any, { put, call }:any) {
      const response = yield call(shareUpdateByType, payload);
      return response
    },

    // 获取空间视频上传签名
    *getVodUploadSign({ payload }:any, { put, call }:any) {
      const response = yield call(getVodUploadSign, payload);
      return response
    },

    // 退出登录
    *exitLogout({ payload }:any, { put, call }:any) {
      const response = yield call(exitLogout, payload);
      return response
    },

    // 空间编辑时获取基础信息
    *getSpaceInfoByEdit({ payload }:any, { put, call }:any) {
      const response = yield call(getSpaceInfoByEdit, payload);
      return response
    },

    // 编辑保存空间信息
    *editSpaceInfo({ payload }:any, { put, call }:any) {
      const response = yield call(editSpaceInfo, payload);
      return response
    },

    // 个人中心空间从列表删除空间
    *deleteSpaceFromList({ payload }:any, { put, call }:any) {
      const response = yield call(deleteSpaceFromList, payload);
      return response
    },

    // H5编辑王国获取基础信息
    *getKingdomInfoByEdit({ payload }:any, { put, call }:any) {
      const response = yield call(getKingdomInfoByEdit, payload);
      return response
    },

    // 编辑保存王国信息
    *editKingdomInfo({ payload }:any, { put, call }:any) {
      const response = yield call(editKingdomInfo, payload);
      return response
    },

    // 根据用户ID与空间ID获取品牌下所有分组信息
    *getBizGroupByTenantId({ payload }:any, { put, call }:any) {
      const response = yield call(getBizGroupByTenantId, payload);
      return response
    },

    // 王国查询图文信息
    *geKingdomList({ payload }:any, { put, call }:any) {
      const response = yield call(geKingdomList, payload);
      return response
    },
    // 获取直播,会议封面信息
    *getSpaceCover({ payload }:any, { put, call }:any) {
      const response = yield call(getSpaceCover, payload);
      return response
    },
    // 获取会议参会人
    *attendeeSearchUserByQueryKey({ payload }:any, { put, call }:any) {
      const response = yield call(attendeeSearchUserByQueryKey, payload);
      return response
    },
    // 根据区域,机构获取用户
    *getAttendeeSearchUserByBizGroupId({ payload }:any, { put, call }:any) {
      const response = yield call(getAttendeeSearchUserByBizGroupId, payload);
      return response
    },
    // 获取用户个人主页展示学习金信息
    *getFriUserToScholarship({ payload }:any, { put, call }:any) {
      const response = yield call(getFriUserToScholarship, payload);
      return response
    },
    // 获取用户学习金明细新增、核销记录（H5、APP）
    *getScholarshipDetailList({ payload }:any, { put, call }:any) {
      const response = yield call(getScholarshipDetailList, payload);
      return response
    },

    // 获取认证的身份字典-实名认证版本
    *getIdentityTypeDict({ payload }:any, { put, call }:any) {
      const response = yield call(getIdentityTypeDict, payload);
      return response
    },

    // 保存个人实名认证-星球-实名认证版本
    *saveSelfAuth({ payload }:any, { put, call }:any) {
      const response = yield call(saveSelfAuth, payload);
      return response
    },

    // 个人实名认证详情-星球-实名认证版本
    *getSelfAuthInfo({ payload }:any, { put, call }:any) {
      const response = yield call(getSelfAuthInfo, payload);
      return response
    },

    // 个人实名认证回显-星球-实名认证版本
    *getEchoSelfAuthInfo({ payload }:any, { put, call }:any) {
      const response = yield call(getEchoSelfAuthInfo, payload);
      return response
    },

    // 企业员工/管理员 - 信息
    *getEnterpriseUserInfo({ payload }:any, { put, call }:any) {
      const response = yield call (getEnterpriseUserInfoApi, payload);
      return response;
    },

    // // 企业员工 -  离职
    *staffResign({ payload }:any, { put, call }:any) {
      const response = yield call (staffResignApi, payload);
      return response;
    },

    // 邀请图片
    *getInviteImg({ payload }:any, { put, call }:any) {
      const response = yield call (getInviteImgApi, payload);
      return response;
    },
  },

  reducers: {
    // 更新状态值数据
    setTaskListState(state, { payload }) {
      return {
        ...state,
        ...payload
      }
    },

    // 清空数据
    clean(state, { payload }){
      return {
        ...state,
        ...stateInfo
      }
    }
  },
  subscriptions: {
    setup({ dispatch, history }) {
      return history.listen(({ pathname, search }) => {
        if (!(/\/UserInfo/.test(pathname)) && !(/\/CreateSpace/.test(pathname))) {
          dispatch({
            type: "clean",
            payload: {
              ...stateInfo
            }
          })
        }
      })
    }
  }
};
