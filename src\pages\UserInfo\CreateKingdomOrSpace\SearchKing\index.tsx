/**
 * @Description: 选择国王
 */
import React, { useState, useEffect } from 'react'
import { connect } from 'umi'
import { Input } from 'antd-mobile'
import styles from './index.less'
import GoBackIcon from '@/assets/GlobalImg/go_back.png' // 返回图片
import noDataImg from '@/assets/GlobalImg/no_data.png' // 无网络图片
import SearchIcon from '@/assets/GlobalImg/search.png' // 搜索图片
import { Spin } from 'antd'
import { processNames, randomColor } from '@/utils/utils';

const Index: React.FC = (props: any) => {
  const { goBack, userInfoStore, dispatch, loading } = props || {};
  const { selectedKing } = userInfoStore || {};
  const [list, setList] = useState([]); // 数据
  const [isHasData, setIsHasData] = useState(0); // 是否搜索出数据
  const [selectKings, setSelectKings] = useState({}); // 选中的国王

  // 获取国王列表
  const getListData = (val) => {
    dispatch({
      type: 'userInfoStore/searchUserListByQueryKey',
      payload: {
        queryKey: val && val.trim(),
      }
    }).then(res => {
      if(res && res.code == 200) {
        if(res.content && res.content.length) {
          setList(res.content);
          setIsHasData(0)
        } else {
          setList([]);
          setIsHasData(1)
        }
      }
    })
  }

  // 将仓库中的值赋值到state上
  useEffect(() => {
    setSelectKings(selectedKing)
  },[selectedKing])

  // input 搜索国王
  const changeInputFn = (val) => {
    if(!val || !val.trim()) return setList([]);
    getListData(val)
  }

  // 选择事件
  const selectBtnFn = (item: Record<string,any>) => {
    // 判断选的是否一致, 修改已选择展示按钮
    if(item.id == selectKings?.id) {
      setSelectKings({});
    } else {
      setSelectKings(item)
    }
  }

  // 确定
  const submitFn = () => {
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        selectedKing: selectKings.id ? selectKings : null
      }
    })
    // 跳转创建王国页面
    goBack(3)
  }

  const searchUserListByQueryKeyLoading = !!loading.effects['userInfoStore/searchUserListByQueryKey'] // loading
  return (
    <div className={styles.container}>
      <div className={styles.title_box}>
        <div className={styles.title_btn} onClick={()=>goBack(3)}>
          <img src={GoBackIcon} width={12} height={24} alt=""/>
        </div>
        <div className={styles.title}>选择国王</div>
      </div>

      <div className={styles.search_box}>
        <div className={styles.search_content}>
          <div className={styles.search_icon}>
            <img src={SearchIcon} width={20} height={20} style={{display: 'block'}} alt=""/>
          </div>
          <div className={styles.search_input}>
            <Input
              placeholder="搜索国王"
              clearable={true}
              onChange={changeInputFn}
            />
          </div>
        </div>
      </div>

      <Spin spinning={searchUserListByQueryKeyLoading}>
        <div className={styles.data_box}>
          {list && list.length >= 1 ?
            <div className={styles.list_box}>
              {
                list && list.map((item:any) => {
                  return <div key={item.id} className={styles.item_box}>
                  <div className={styles.avatar}>
                    {
                      item.headUrlShow ? 
                      <img src={item.headUrlShow} alt="" /> :
                      <div className={styles.no_comment_head} style={{background:randomColor(item?.id)}}>{processNames(item?.name)}</div>
                    }
                  </div>
                  <div className={styles.info_box}>
                    <div className={styles.info_1}>
                      <div className={styles.info_name} dangerouslySetInnerHTML={{__html: item.highlightName}}></div>
                      <div className={styles.info_phone}>{item.phone}</div>
                    </div>
                    <div className={styles.info_2}>
                      <span>{item.postTitleDictName}</span>
                      {item.organizationName ? <><span className={styles.lines}></span><span>{item.organizationName}</span></> : null}
                    </div>
                  </div>
                  {
                    <div
                      className={selectKings?.id === item.id ? styles.item_active_btn : styles.item_btn}
                      onClick={() => selectBtnFn(item)}
                    >
                      {selectKings?.id === item.id ? '已选择' : '选择'}
                    </div>
                  }
                </div>
                })
              }
            </div> : null
          }
          {
            isHasData == 1 ? 
            <div className={styles.nodata}>
              <img src={noDataImg} alt="" />
              <div className={styles.empty_title}>暂无该搜索结果</div>
              <div className={styles.empty_msg}>请试试其他搜索关键词</div>
            </div> : null
          }
        </div>
      </Spin>

      <div className={styles.fixed_box}>
        <div className={styles.btn_box}>
          <div className={styles.btn}  onClick={submitFn}>确定</div>
        </div>
      </div>
    </div>
  )
}

export default connect(({ userInfoStore, loading }: any) => ({userInfoStore, loading}))(Index)
