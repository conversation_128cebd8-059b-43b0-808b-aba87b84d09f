// @ts-ignore
/* eslint-disable */
import request  from '@/utils/request';

// 请求
export async function newRequest(options?: { [key: string]: any }) {
  return request<{
    data: API.data;
  }>('/api/xxx', {
    method: 'GET',
    ...(options || {}),
  });
}

// 获取GDP介绍文案
export async function gdpExplain() {
  return request(`/api/server/H5Base/gdpExplain`, {
    method: 'GET',
  });
}

// 企微环境下获取企微授权信息
export async function getWXWorkAuthInfo(params?: { [key: string]: any }) {
  return request(`/api/server/weCom/getUserInfo`, {
    method: 'GET',
    params
  });
}

// 企微授权注册医生星球账号
export async function signUpByWxWorkToUc(params?: { [key: string]: any }) {
  return request(`/api/fri-uc/login-before/we-com-to-uc-register`, {
    method: 'POST',
    data: params
  });
}

// 企微授权自动登录医生星球账号
export async function wxWorkLoginUserToken(params?: { [key: string]: any }) {
  return request(`/api/fri-uc/fri-auth/we-com-to-uc-login`, {
    method: 'POST',
    data: params
  });
}

// 获取微信授权信息
export async function getWechatAuthInfo(params?: { [key: string]: any }) {
  return request(`/api/fri-uc/login-before/we-chat-to-uc-register`, {
    method: 'GET',
    params
  });
}

/*
* 注册账户换取userKey
* */
export async function getWechatRegisterKey(params?: { [key: string]: any }) {
  return request(`/api/fri-uc/login-before/we-chat-perfect-info`, {
    method: 'POST',
    data: params
  });
}

// 微信授权自动登录获取token
export async function wechatLoginUserToken(params?: { [key: string]: any }) {
  return request(`/api/fri-uc/fri-auth/we-chat-to-uc-login?wxAppId=${params.wxAppId}&code=${params.code}`, {
    method: 'POST',
    data: {
      weChatToUcKey: params.weChatToUcKey
    }
  });
}

