/**
 * 企业管理 - 微信扫码授权页面 前置页面
 */

import { getWechatAuth } from "@/utils/utils";
import styles from './index.less';

import logo from '@/assets/Enterprise/logo.png';


const Index = () => {
  // 调起微信授权弹窗
  const handleWxAuth = () => {
    getWechatAuth('enterprise')
  }
  return (
    <div className={styles.enterprise_auth}>
      <div className={styles.header}>
        <div className={styles.logo}>
          <img src={logo} alt="logo" />
        </div>
      </div>

      <div className={styles.content}>
        <div className={styles.title}>FRIDAY数智化平台</div>
        <span className={styles.subtitle}>每天进步一点点</span>
        <span className={styles.login_btn} onClick={handleWxAuth}>一键登录</span>
      </div>

    </div>
  );
};

export default Index;
