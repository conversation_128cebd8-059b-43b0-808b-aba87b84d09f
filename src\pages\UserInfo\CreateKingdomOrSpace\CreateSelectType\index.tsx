/**
 * @Description: 选择创建王国/空间
 */
import React, { useState, useEffect, useCallback } from 'react';
import { connect, history } from 'umi';
import styles from './index.less';
import { Spin } from 'antd';
import WranningIcon from '@/assets/Payment/Payment_MethodPayment_Prompt.png';

import postImg from '@/assets/UserInfo/post.png'
import postSelectImg from '@/assets/UserInfo/post_active.png'
import CreateSelectType_liveBroadcast from '@/assets/UserInfo/CreateSelectType_liveBroadcast.png'
import CreateSelectType_liveBroadcast_select from '@/assets/UserInfo/CreateSelectType_liveBroadcast_select.png'
import CreateSelectType_meeting from '@/assets/UserInfo/CreateSelectType_meeting.png'
import CreateSelectType_meeting_select from '@/assets/UserInfo/CreateSelectType_meeting_select.png'
import kingdomImg from '@/assets/UserInfo/kingdom.png'
import kingdomSelectImg from '@/assets/UserInfo/kingdom_active.png'

// 空间、王国数据
const listData = [
  {
    id: 4,
    tips: '',
    title: '帖子',
    img: postImg,
    imgSelect: postSelectImg
  },
  // {
  //   id: 5,
  //   tips: '',
  //   title: '文章',
  //   img: articleImg,
  //   imgSelect: articleSelectImg
  // },
  {
    id: 1,
    tips: '直播内支持语音、共享屏幕、送花花等功能，收藏可接收该直播的动态',
    title: '直播',
    img: CreateSelectType_liveBroadcast,
    imgSelect: CreateSelectType_liveBroadcast_select
  },
  {
    id: 14,
    tips: '会议内支持语音、共享屏幕、送花花等功能，收藏可接收该会议的动态',
    title: '会议',
    img: CreateSelectType_meeting,
    imgSelect: CreateSelectType_meeting_select
  },
  {
    id: 3,
    tips: '拥有共同兴趣人的圈子，关注王国后，可收到王国内更新的通知',
    title: '王国',
    img: kingdomImg,
    imgSelect: kingdomSelectImg
  },
  // {
  //   id: 6,
  //   tips: '',
  //   title: '外链',
  //   img: linkImg,
  //   imgSelect: linkSelectImg
  // },
]

const Index: React.FC = (props: any) => {
  const [selectType, setSelectType] = useState(0); // 空间/王国/王国空间tab
  const [kingSpaceTab, setKingSpaceTab] = useState(1); // 我创建/我加入的王国tab
  const { dispatch, userInfoStore, loading } = props || {};
  const { selectCreateType, selectedKingdom, topicHomeTopicId, topicHomeTopicName } = userInfoStore || {}; // 仓库储存状态
  const [createKingdomList, setCreateKingdomList] = useState([]); // 创建的王国数据
  const [joinKingdomList, setJoinKingdomList] = useState([]); // 加入的王国数据
  const [showTypeListData, setShowTypeListData] = useState<typeof listData>([]); // 王国空间
  const [showTypeLoading, setShowTypeLoading] = useState(false); // 王国空间

  useEffect(() => {
    setSelectType(selectCreateType)
  }, [selectCreateType])

  // 获取创建王国或加入王国相关数据，根据返回数据进行tab的展示（未有加入或创建王国数据，则王国空间不展示）
  const getInitDate = useCallback(() => {
    setShowTypeLoading(true)
    dispatch({
      type: 'userInfoStore/getCreateAndJoinKingdomList',
    }).then(res => {
      setShowTypeLoading(false)
      const {code, content} = res || {};
      if(res && code == 200) {
        setCreateKingdomList(content && content[1]);
        setJoinKingdomList(content && content[2]);
        if (content) {
          if (!content[1] || (content[1]) && content[1].length < 1) {
            setKingSpaceTab(2)
          } else {
            setKingSpaceTab(1)
          }
        }
        setShowTypeListData(listData)
      }
      if(res && res.code == 400) { // 未加入和创建王国
        setShowTypeListData(listData.filter(it => it.id !== 2));
      }
    }).catch(() => setShowTypeLoading(false))
  }, [dispatch])

  useEffect( () => {
    getInitDate();
  }, [getInitDate])

  // 创建空间按钮点击后，给父组件传参
  const createKingdom = () => {
    // 如果选中王国空间，则需要在store中存储相应的王国id
    // if(selectType == 2 && !selectedKingdom) {
    //   return Toast.show({content: '请选择王国空间后进行创建'});
    // } else {
    //   dispatch({
    //     type: 'userInfoStore/setTaskListState',
    //     payload: {
    //       spaceFromEnter: {
    //         isCureentKingdom: true // 是否是当前指定王国
    //       },
    //       selectedKingdomAudience: selectedKingdom?.id ? [selectedKingdom?.id] : [], // 指定当前王国所有成员，并将当前王国的id保存到store中
    //     }
    //   })
    // }
    // 创建前先清空数据
    localStorage.removeItem('kingdomInfoData');
    switch(selectType) {
      case 1: // 直播
        return props.createBtnFn({
          id: '直播',
          type: 1,
          title: '创建直播',
          goBackType: 99,
        });

      case 14: // 会议
        return props.createBtnFn({
          id: '会议',
          type: 14,
          title: '创建会议',
          goBackType: 99,
        });

      // case 2: // 王国空间
      //   return props.createBtnFn({
      //     id: '王国空间',
      //     type: 2,
      //     title: '创建王国空间',
      //     goBackType: 99,
      //   });
      case 3: // 王国
        return props.createBtnFn({
          id: '王国',
          type: 3,
          title: '创建王国',
          goBackType: 99,
        });
    }
  }

  // 选中的tab值
  const checkTabFn = (id) => {
    console.log(topicHomeTopicId, topicHomeTopicName)
    let query = ''
    if (topicHomeTopicId && topicHomeTopicName) {
      query = `?topicId=${topicHomeTopicId}&topicName=${topicHomeTopicName}`
    }
    if (id == 4) {        // 创建帖子
      history.push(`/CreateGraphicsText/CreatePost${query}`)
      return
    } else if (id == 5) { // 创建文章
      history.push(`/CreateGraphicsText/CreateArticle${query}`)
      return
    } else if (id == 6) { // 创建外链
      history.push('/CreateGraphicsText/CreateExternalLinks')
      return
    } else if (id == 1) { // 创建直播
      dispatch({
        type: 'userInfoStore/setTaskListState',
        payload: { createModalVisible:false, }
      })
      history.push('/CreateSpace/Live')
      return
    }else if (id == 14) { // 创建会议
      dispatch({
        type: 'userInfoStore/setTaskListState',
        payload: { createModalVisible:false, }
      })
      history.push('/CreateSpace/Meet')
      return
    }

    setSelectType(id);
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        selectCreateType: id,
        selectedKingdom: null,
      }
    })
  }

  // title 名称展示
  const getTitleRender = (type: number) => {
    switch(type) {
      case 0:
        return '请选择一个创建类型';
      case 1:
        return '已选:直播';
      case 2:
        return '已选:直播';
      case 3:
        return '已选:王国';
      case 14:
        return '已选:会议';
    }
  }

  // 王国空间选中事件=>去除原无用功能
  // const checkKingHandle = useCallback((item) => {
  //   dispatch({
  //     type: 'userInfoStore/setTaskListState',
  //     payload: {
  //       selectedKingdom: item
  //     }
  //   })
  // }, [dispatch])

  return (
    <div className={styles.container}>
      <div className={styles.title}>{getTitleRender(selectType)}</div>
      <div className={styles.contentList}>
        {
          selectType ?<div className={styles.tips_box}>
            <img src={WranningIcon} width={16} height={16} alt=""/>
              {/*{tipsData[selectType]}*/}
              {listData.find(item => item.id == selectType)?.tips}
          </div> : null
        }
        <Spin spinning={showTypeLoading}>
          <div className={styles.tab_wrap}>
            {
              showTypeListData.map(item => {
                return<div className={styles.tab_list_box} key={item.id}>
                  <div className={styles.tab_item} onClick={()=>checkTabFn(item.id)}>
                    <img src={selectType === item.id ? item.imgSelect : item.img} alt="" />
                  </div>
                  <div className={styles.tab_name}>{item.title}</div>
                </div>
              })
            }
          </div>
        </Spin>
      </div>

      <div className={styles.fixed_box}>
        <div className={selectType ? styles.btn_box : styles.disable_btn_box}>
          <div className={styles.btn} onClick={selectType ? createKingdom : ()=>{}}>下一步</div>
        </div>
      </div>
    </div>
  )
}

export default connect(({ userInfoStore, loading }: any) => ({userInfoStore, loading}))(Index)
