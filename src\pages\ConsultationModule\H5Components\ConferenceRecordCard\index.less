.wrap {
  width: 100%;
  background: #FFFFFF;
  padding: 0 0 16px 16px;
  .header{
    height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #E1E4E7;
    
    .header_title{
      font-size: 16px;
      font-weight: 500;
      color: #000000;
    }
  }
  .content{
    padding-top: 4px;
    .content_detail{
      margin-top: 8px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-right: 16px;
      p:nth-child(1){
        margin-bottom: 0;
        font-size: 13px;
        font-weight: 400;
        color: #999999;
      }
      p:nth-child(2){
        margin-bottom: 0;
        font-size: 13px;
        font-weight: 400;
        color: #999999;
      }
    }
  }
}