.page_Warp {
  :global {
    .ant-popover-inner-content {
      width: 200px;
    }
  }
}

.content_warp {
  width: 100%;
  height: 100%;
  background: #F5F6F8;
}

.page_content {
  width: 100%;
  // height: 100%;
  padding-left: 16px;
  padding-right: 16px;
  padding-top: 46px;
  background: #fff;



}

.content {
  width: 100%;
  height: 100%;
  padding-bottom: 46px;
}

  .lines {
    width: 48px;
    height: 4px;
    background: #D0D4D7;
    border-radius: 4px;
    margin: 0 auto;
    margin-top: 12px;
  }

  .titleWarp {
    display: flex;
    align-items: center;
    padding: 0 16px;
    box-sizing: border-box;
    justify-content: center;
    position: relative;

    .titleBackIcon {
      position: absolute;
      top: 0;
      left: 16px;

      img {
        width: 12px;
        height: auto;
      }
    }

    .titleText {
      font-size: 17px;
      font-weight: 500;
      color: #000000;
      line-height: 24px;
    }
  }






/* end titleInfo */
.space_wrap {
  width: 100%;
  padding-left: 16px;
  box-sizing: border-box;
}

  .space_form_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    // border-bottom: 1px solid #E1E4E7;
    width: 100%;

    :global {
      .ant-form-item {
        flex: 1;
        margin-bottom: 0;
      }

      .ant-form-item-label {
        flex: 1!important;
        padding: 0;
      }

      .ant-form-item-control {
        flex: 1!important;
      }

      .ant-form-item-explain-error {
        text-align: right;
        font-size: 12px;
        font-weight: 400;
        color: #FF5F57;
        line-height: 17px;
      }

      .ant-form-item-label > label {
        height: 20px;
      }

      .ant-form-item-control-input {
        min-height: 20px;
        height: 20px;
      }

      .ant-form-item-row {
        padding-right: 16px;
      }

      .adm-input-element {
        font-size: 14px;
        font-weight: 400;
        color: #000;

        &::placeholder {
          font-size: 14px;
          font-weight: 400;
          color: #CCCCCC;
        }
      }
    }

    .space_form_title {
      font-size: 14px;
      font-weight: 500;
      color: #666666;
      line-height: 20px;
      width: 20%;
      flex-shrink: 0;

      span {
        color: #FF5F57;
      }
    }



    .space_form_content {
      display: flex;
      align-items: center;
      // padding-right: 16px;
      justify-content: flex-end;

      .space_form_item {
        text-align: right;
        display: flex;
        align-items: center;
        color: #000000;

        .kingdom_name_text {
          font-size: 14px;
          font-weight: 400;
          color: #000000;
          line-height: 20px;
          max-width: 150px;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }

        .no_comment_head{
          width: 24px;
          height: 24px;
          border-radius: 2px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: 500;
          color: #fff;
          margin-right: 8px;
          white-space: nowrap;
        }

        img {
          width: 24px;
          height: 24px;
          border-radius: 2px;
          margin-right: 8px;
        }
      }

      .space_input_init_text {
        font-size: 14px;
        font-weight: 400;
        color: #CCCCCC;
        line-height: 20px;

        .host_content {
          display: flex;
          align-items: center;
        }

        .no_comment_head{
          width: 24px;
          height: 24px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: 500;
          color: #fff;
          margin-right: 8px;
          white-space: nowrap;
        }

        img {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          margin-right: 8px;
        }

        &.selected {
          color: #000;
        }
      }

      .space_form_arrow {
        width: 16px;
        height: 16px;
        margin-left: 8px;
      }
    }
  }

  .special_wrap {
    display: flex;
    flex-direction: column;
    padding: 6px 0;
    border-bottom: 1px solid #E1E4E7;

    :global {
      .ant-form-item-label > label {
        color: #666666;
      }
    }

    .space_form_box {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      border-bottom: none;
      padding: 0;

      :global {
        .adm-input-element {
          font-size: 14px;
          font-weight: 400;
          color: #000;

          &::placeholder {
            font-size: 14px;
            font-weight: 400;
            color: #CCCCCC;
          }
        }
        input:-internal-autofill-selected {
          background: none!important;
        }
      }

      .space_form_title {
        font-size: 14px;
        font-weight: 500;
        color: #666666;
        line-height: 20px;
        flex-shrink: 0;
        width: 40%;

        span {
          color: #FF5F57;
        }
      }
    }

    .input_tips {
      font-size: 12px;
      font-weight: 400;
      color: #FF5F57;
      line-height: 17px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-right: 16px;

      img {
        width: 10px;
        height: 10px;
      }
    }
  }

  .space_guest_wrap {
    padding: 16px 0px 16px 0;
    box-sizing: border-box;

    .space_guest_box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;

      .space_guest_title {
        font-size: 14px;
        font-weight: 400;
        color: #666666;
        line-height: 20px;

      }

      .space_guest_illustrate {
        font-size: 12px;
        font-weight: 400;
        color: #999999;
        line-height: 17px;
      }
    }

    .space_guest_list_wrap {
      display: flex;
      flex-wrap: wrap;

      .space_guest_add {
        width: 40px;
        height: 40px;
        border-radius: 20px 20px 20px 20px;
        border: 1px dashed #CCCCCC;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
        margin-top: 14px;

        img {
          width: 20px;
          height: 20px;
        }
      }

      .space_guest_reduce {
        width: 40px;
        height: 40px;
        border-radius: 20px 20px 20px 20px;
        border: 1px dashed #CCCCCC;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 14px;

        img {
          width: 20px;
          height: 20px;
        }
      }

      .space_guest_item {
        width: 40px;
        position: relative;
        margin-right: 8px;
        margin-top: 14px;
        text-align: center;

        .space_guest_img {
          width: 40px;
          height: 40px;
          background: #E6F4FF;
          border-radius: 20px 20px 20px 20px;

          .no_comment_head{
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 500;
            color: #fff;
            margin-right: 8px;
            white-space: nowrap;
          }

          img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
          }
        }

        .space_guest_name {
          font-size: 12px;
          font-weight: 400;
          color: #666666;
          line-height: 17px;
          margin-top: 8px;
          word-break: break-all;
        }

        .space_guest_item_icon {
          position: absolute;
          top: -10px;
          right: -5px;
          display: none;

          &.show {
            display: block;
          }

          img {
            width: 13px;
            height: 13px;
          }
        }
      }
    }
  }

  .space_audience {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 16px 16px 16px 0;
    box-sizing: border-box;
    border-top: 1px solid #E1E4E7;

    .space_audience_title {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      line-height: 16px;
      flex-shrink: 0;
    }
  }

.space_audience_text {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .space_form_item {
    font-size: 14px;
    font-weight: 400;
    color: #000000;
    line-height: 16px;
  }

  .space_input_init_text {
    font-size: 14px;
    font-weight: 400;
    color: #CCCCCC;
    line-height: 16px;
  }

  .space_form_arrow {
    width: 16px;
    height: 16px;
    margin-left: 8px;
  }
}

  .space_type {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 16px 16px 16px 0;
    box-sizing: border-box;
    border-top: 1px solid #E1E4E7;

    .space_type_title {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      line-height: 16px;
      flex-shrink: 0;
    }

    .space_type_text {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .space_form_item {
        font-size: 14px;
        font-weight: 400;
        color: #000000;
        line-height: 16px;
      }

      .space_input_init_text {
        font-size: 14px;
        font-weight: 400;
        color: #CCCCCC;
        line-height: 16px;
      }

      .space_form_arrow {
        width: 16px;
        height: 16px;
        margin-left: 8px;
      }
    }
  }

  .space_introduction_wrap {
    padding: 16px 16px 16px 0;
    box-sizing: border-box;
    border-bottom: 1px solid #E1E4E7;

    .space_introduction {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      line-height: 16px;
      margin-bottom: 12px;
    }

    :global {
      .ant-form-item {
        margin-bottom: 0;
        word-break: break-all;
      }

      .adm-text-area-element {
        font-size: 14px;
        font-weight: 400;
        color: #000000;
        line-height: 16px;

        &::placeholder {
          font-size: 14px;
          font-weight: 400;
          color: #CCCCCC;
          line-height: 16px;
        }
      }

      .adm-text-area-count {
        font-size: 13px;
        font-weight: 400;
        color: #CCCCCC;
        line-height: 15px;
      }

      .ant-form-item-explain-error {
        text-align: right;
        font-size: 12px;
        font-weight: 400;
        color: #FF5F57;
        line-height: 17px;
      }
    }
  }
/* end space_wrap */


.upload_wrap {
  // border-top: 8px solid #F5F6F8;
  width: 100%;
  // padding-left: 16px;
  box-sizing: border-box;

  .upload_img_wrap {
    padding: 16px 16px 16px 0;
    box-sizing: border-box;
    border-bottom: 1px solid #E1E4E7;
    display: flex;
    justify-content: space-between;

    .upload_img_title {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      line-height: 20px;
      margin-bottom: 43px;
    }

    .upload_img_title_Courseware {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      // line-height: 20px;
      margin-bottom: 43px;
      width: 80px;
      display: flex;
      align-items: baseline;
    }

    .upload_img_Content_Courseware {
      width: calc(100% - 129px);
      display: flex;
      justify-content: flex-end;
    }

    .upload_img_Content_Courseware_Upload > span{
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      :global {
        .ant-upload-list {
          width: 160px;
          background: #fff;
        }
      }
    }

    .upload_img_tip {
      font-weight: 400;
      font-size: 12px;
      color: #CCCCCC;
      line-height: 12px;
      text-align: left;
      width: 135px;
    }

    .upload_img_content {
      width: 122px;
      height: 78px;
      border-radius: 8px 8px 8px 8px;
      border: 1px dashed #CCCCCC;
      position: relative;
      &.upload_video_content {
        width: 120px;
        height: 130px;
      }
      .uploading_icon_content {
        text-align: center;
      }
      .uploading_icon_text {
        // text-align: center;
        color: #ccc;
      }
      .upload_video_input {
        width: 0;
        height: 0;
      }
      .upload_box {
        width: 122px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        .upload_img {
          width: 100%;
          height: 100%;
          border-radius: 8px 8px 8px 8px;
        }

        .init_upload_img {
          width: 30px!important;
          height: 30px!important;
          flex-shrink: 0;
        }
      }

      :global {
        .ant-upload-picture-card-wrapper {
          width: 100%;
          height: 80px;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 99;
        }
        .ant-upload.ant-upload-select-picture-card {
          width: 100%;
          height: 80px;
          display: block!important;
          opacity: 0;
        }

        .adm-image-uploader-upload-button-wrap .adm-image-uploader-upload-button {
          background: none;
          width: 100%;
          height: 80px;
        }

        .adm-space-item {
          width: 100%;
          height: 80px;
        }

        .adm-image-uploader-cell {
          position: relative;
          height: 80px;

          &.adm-image-uploader-upload-button {
            position: relative;
            &::after {
              content: '';
              width: 40px;
              height: 40px;
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              margin: auto;
              background: url("https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/big_add.png") no-repeat;
              background-size: cover;
            }
          }
        }

        .adm-image-uploader-upload-button-icon {

          .antd-mobile-icon {
            display: none;
          }
        }
      }
    }
  }

  .upload_video_wrap {
    padding: 16px 16px 16px 0;
    box-sizing: border-box;
    border-bottom: 1px solid #E1E4E7;
    display: flex;
    justify-content: space-between;

    .upload_video_title {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      line-height: 20px;
      margin-bottom: 39px;
    }

    .upload_img_tip {
      font-weight: 400;
      font-size: 12px;
      color: #CCCCCC;
      line-height: 12px;
      text-align: left;
      width: 135px;
    }

    .video_content {
      // width: 100%;
      max-height: 160px;
      position: relative;

      .upload_video_content {
        width: 122px;
        height: 78px;
        position: relative;
        border-radius: 8px 8px 8px 8px;
        border: 1px dashed #CCCCCC;
        overflow: hidden;

        .uploading_icon_content {
          width: 135px;
          height: 156px;
          background: #F5F6F8;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          .init_upload_img {
            width: 32px;
            height: 32px;
          }

          .uploading_icon_text {
            font-size: 14px;
            font-weight: 400;
            color: #999999;
            line-height: 16px;
            margin-top: 8px;
          }

          .video_name_text {
            font-size: 14px;
            font-weight: 400;
            color: #999999;
            line-height: 16px;
            margin-top: 8px;
            display: flex;

            .uploading_name_text {
              display: block;
              max-width: 70px;
              white-space: nowrap;
              overflow: hidden;
            }
          }
        }

        .uploading_init_icon_content {
          width: 122px;
          height: 78px;
          display: flex;
          align-items: center;
          justify-content: center;

          .init_upload_img {
            width: 40px;
            height: 40px;
          }
        }

        .upload_video_input {
          position: absolute;
          width: 122px;
          height: 78px;
          top: 0;
          left: 0;
          display: block!important;
          opacity: 0;
        }
      }
    }
  }


}

.space_allowApplications {
  width: 12px;
  height: 12px;
  display: inline-block;
  background: url("~@/assets/PlanetChatRoom/CreateSpace_No_passwrod.png");
  background-size: 100% 100%;
  position: relative;
  top: 1px;
  left: 3px;
}

.space_password_wrap_bottm_line {
  border-bottom: 1px solid #E1E4E7;
}

.appointmentStartDateTextWarp {
  display: flex;
}

.appointmentStartDateText {
  background: #F5F6F8;
  border-radius: 4px 4px 4px 4px;
  font-weight: 400;
  font-size: 14px;
  color: #333333;
  line-height: 16px;
  text-align: right;
  font-style: normal;
  text-transform: none;
  padding: 5px 8px;
  margin-left: 8px;
}

.space_password_wrap {
  padding: 16px 0;
  box-sizing: border-box;

  .space_password_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    // padding-right: 16px;
    .space_password_title {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      line-height: 20px;
    }

    .space_password_open_select {
      // width: 98px;
      font-weight: 400;
      font-size: 14px;
      line-height: 14px;
      text-align: right;
      color: #000000;
      display: flex;
      align-items: center;
    }

    .space_password_open_select_dis {
      color: #CCCCCC;
    }

    .space_password_open_btn {
      width: 48px;
      height: 26px;

      :global {
        .adm-switch-checkbox:before {
          background: none;
        }
      }
    }
  }

  .password {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    // border: 1px solid #E1E4E7;
    padding-top: 16px;
    // padding-bottom: 16px;
    padding-right: 16px;
    border-bottom: 1px solid #E1E4E7;

    :global {
      .adm-input-element {
        font-size: 14px;
        font-weight: 400;
        color: #000;

        &::placeholder {
          color: #ccc;
        }
      }
      .ant-form-item {
        margin-bottom: 0;
      }

      .ant-form-item-control-input {
        height: 24px;
      }
    }

    .eye {
      position: relative;
      top: 3px;

      img {
        width: 20px;
        height: 20px;
      }
    }
  }
}


@media screen and (max-height: 500px) {
  .content{
    padding-bottom: 0;
    .space_btn_wrap {
      position: inherit;
    }
  }
}
.space_btn_wrap {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  display: flex;
  justify-content: space-between;
  padding:16px;
  box-sizing: border-box;
  background: #fff;
  z-index: 999;
  padding-bottom: calc(16px + constant(safe-area-inset-bottom));//兼容 IOS<11.2
  padding-bottom: calc(16px + env(safe-area-inset-bottom));//兼容 IOS>11.2

  .space_start_now_btn {
    flex: 1;
    margin-right: 8px;
    height: 40px;
    background: #EDF9FF;
    border-radius: 20px;
    font-size: 16px;
    font-weight: 400;
    color: #0095FF;
    line-height: 40px;
    text-align: center;
  }

  .space_appointment_start_btn {
    flex: 1;
    height: 40px;
    background: #0095FF;
    border-radius: 20px;
    font-size: 16px;
    font-weight: 400;
    color: #fff;
    line-height: 40px;
    text-align: center;
  }
}

.title {
  font-weight: 500;
  font-size: 16px;
  color: #333333;
  line-height: 16px;
}

.space_form_box_spaceName {
  display: flex;

  .space_form_box_cover {
    width: 140px;
    height: 80px;
    // background: #7ec1ff;
    border-radius: 4px;
    margin-right: 10px;
    position: relative;
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
    .space_form_box_btn {
      width: 60px;
      height: 24px;
      background: rgba(0,0,0,0.3);
      border-radius: 30px 30px 30px 30px;
      font-weight: 400;
      font-size: 11px;
      color: #FFFFFF;
      line-height: 24px;
      text-shadow: 0px 0px 4px rgba(0,0,0,0.5);
      text-align: center;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      user-select: none;
      cursor: pointer;
      z-index: 1;
    }
  }

  .space_form_box_title {
    font-size: 15px;
    color: #222222;
    line-height: 18px;
    text-align: left;
    width: calc(100% - 150px);
    :global {
      .adm-text-area-element {
        font-weight: 700;
      }
    }
  }
}

.box_btn_warp {
  width: 100%;
  // height: 80px;
  padding-top: 40px;
  padding-bottom: 30px;
  // padding-left: 20px;
  // padding-right: 20px;

  .box_btn_reservation {
    width: 100%;
    height: 40px;
    background: #0095FF;
    border-radius: 20px 20px 20px 20px;
    font-weight: 400;
    font-size: 16px;
    color: #FFFFFF;
    line-height: 40px;
    text-align: center;
  }
  .box_btn_reservation:active {
    background: #0181dc;
  }
}
.rightArrowIcon {
  width: 16px;
  height: 16px;
  display: inline-block;
  // background: url("~@/assets/GlobalImg/right_arrow.png");
  // background-size: 100% 100%;
  position: relative;
  color: #CCCCCC;
  top: 1px;
  left: 3px;
}
.lines_box {
  width: 100%;
  height: 1px;
  background: #E1E4E7;
}
