```

        const defaultToothInfo = {
          leftTop: [],
          rigthTop: [],
          leftBottom: [],
          rigthBottom: [],
        }

        <SelectTooth
          defaultType="1"                     // 默认展示恒牙还是乳牙
          defaultToothInfo={defaultToothInfo} // 默认展示选中的牙位
          onChangeTooth={(value,current)=>{   // 当牙位改变后的回调事件 已经选中的牙位
            console.log('value ::  ',value);
            console.log("current ::  ",current);
          }}
          onChangeType={(value)=>{            // 当前乳牙恒牙改变后的回调事件
            console.log('Tab : ',value);
          }}
        />

```
