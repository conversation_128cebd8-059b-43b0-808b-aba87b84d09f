/**
 * @Description: 移动端指导预约信息卡片
 * @author: 赵斐
 */
import React, { useState } from 'react';
import { history } from 'umi';
import { Typography, message } from 'antd';

import arrowIcon from '@/assets/Consultation/H5/arrow_icon.png'
import calendarIcon from '@/assets/Consultation/H5/calendar_icon.png'
import copyIcon from '@/assets/Consultation/H5/copy_icon.png'
// 添加客服小忆微信弹窗
import CustomerServiceWeChatModal from '../CustomerServiceWeChatModal'
import styles from './index.less'
import { Toast } from 'antd-mobile';

interface PropsType {
  data:any,                   // 展示预约数据
  editSpaceInfo: () => void,  // 改约回调
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { data, editSpaceInfo } = props;
  const [state, setState] = useState(false)  // 添加客服小忆微信弹窗状态
  const {
    videoAppointment,     // 预约时间对象
    thisUserIsExperts,    // 当前人是否是专家：0:否，1:是
    status,               // 1 取消指导
    defaultAssistantUrl,  // 小忆二维码地址
  } = data || {}
  const {
    spaceId,  // 关联的空间ID
    dateStr,  // 日期字符串
    weekStr,  // 星期字符串
    timeStr,  // 时间字符串
    password, // 空间密码
  } = videoAppointment || {}

  // 打开添加客服小忆微信弹窗
  const onClickWeChatModalShow = () => {
    if(thisUserIsExperts == 1){ // 专家
      editSpaceInfo && editSpaceInfo()
    }else{
      setState(true)
    }
  }
  // 关闭添加客服小忆微信弹窗
  const onClickWeChatModalHide = () => {
    if(thisUserIsExperts != 1){  // 专家
      setState(false)
    }
  }

  // 进入视频会议
  const enterSpaceId= () => {
    history.push(`/PlanetChatRoom/Live/${spaceId}`)
  }

  // 复制提示文案
  const onCopy = ()=>{
    message.success('复制成功')
  }
  return (
    <div className={styles.wrap}>
      <div className={styles.header}>
        <span className={styles.header_title}>预约时间</span>
        {
          password && <>
            <span className={styles.header_psd}>密码：{password}</span>
            <Typography.Paragraph copyable={{
              text: password,
              icon: [<span className={styles.header_copy}><img src={copyIcon} alt="icon" />复制</span>, <span className={styles.header_copy}><img src={copyIcon} alt="icon" />复制</span>],
              tooltips: [false, false],
              onCopy: onCopy,
            }}></Typography.Paragraph>
          </>
        }

        {
          status !=0?<>
            <span className={styles.header_status} onClick={()=>{enterSpaceId()}}>进入视频会议</span>
            <span className={styles.header_icon}><img src={arrowIcon} alt="icon" /></span>
          </>:null
        }

      </div>
      <div className={styles.content}>
        <div className={styles.reservation}>
          <p className={styles.reservation_left}>
            <span>预约时间：</span>
            <span>{spaceId?`${dateStr}${weekStr?',':null}${weekStr}${timeStr?',':null}${timeStr}`:"暂未预约时间"}</span>
          </p>
          {
            status ==1 ?<p className={styles.reservation_right}>
            <span><img src={calendarIcon} alt="" /></span>
            <span onClick={() => { onClickWeChatModalShow() }}>我要改约</span>
          </p>:null
          }

        </div>
      </div>
      {
        <CustomerServiceWeChatModal visible={state} onCancel={onClickWeChatModalHide} url={defaultAssistantUrl} />
      }
    </div>
  )
}
export default Index
