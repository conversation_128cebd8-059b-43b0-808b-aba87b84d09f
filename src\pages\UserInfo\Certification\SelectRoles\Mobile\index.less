.spin {
  height: 100%;
  & > :global(.ant-spin-container) {
    height: 100%;
  }
}

.container {
  height: 100%;
  overflow-y: auto;
  background: #F5F6F8;
  padding: 44px 16px 34px;
}

.gray_bar {
  width: 100%;
  height: 16px;
}

.role_item_wrap {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  column-gap: 12px;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 8px;
  padding: 16px 12px;
  img {
    flex-shrink: 0;
  }
  .role_item_name {
    flex: 1;
    word-break: break-all;
    font-size: 18px;
    color: #000;
    font-weight: 500;
  }
}
