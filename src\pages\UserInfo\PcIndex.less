.pc_wrap {
  width: 100%;
  height: 100vh;
  background: #EEF3F9;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.pc_head_wrap {
  width: 1228px;
  min-width: 1228px;
  height: 120px;
  background: url('https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/assets_pc_bg.png') no-repeat;
  background-size: contain;
  display: flex;
  padding: 24px;
  box-sizing: border-box;
  margin-top: 16px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;

  .head_sculpture {
    width: 72px;
    height: 72px;
    border-radius: 50%;
    flex-shrink: 0;

    img {
      width: 72px;
      height: 72px;
      border-radius: 50%;
    }
  }

  .head_sculpture_name {
    width: 72px;
    height: 72px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 24px;
    font-weight: 500;
    flex-shrink: 0;
  }
}
.pc_user_info {
  margin-left: 16px;
  flex: 1;

  .pc_top_info_wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .pc_top_info {
    display: flex;
    align-items: center;

    .name {
      font-size: 20px;
      font-weight: 600;
      color: #000000;
      line-height: 23px;
      margin-right: 8px;
    }

    .doctor {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      line-height: 16px;
      margin-right: 8px;
    }

    .tips {
      font-size: 12px;
      font-weight: 400;
      color: #06A777;
      line-height: 12px;
      background: #EEFFF9;
      border-radius: 2px 2px 2px 2px;
      border: 1px solid #B0EAD9;
      padding: 4px;
      margin-right: 8px;
      word-break: break-all;

    }

    .identity {
      width: 60px;
      margin-right: 16px;

      img {
        display: block;
        width: 60px;
        height: auto;
      }
    }
    .switch_waistcoatAccount{
      margin-right:16px;
    }

    .edit_user_info {
      font-size: 13px;
      color: #999999;
      cursor: pointer;
      display: flex;
      align-items: center;
      column-gap: 4px;
      & > span {
        height: 18px;
        line-height: 17px;
      }
    }
  }

  // 认证信息
  .certified {
    cursor: pointer;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    column-gap: 2px;
    border-radius: 14px;
    height: 21px;
    padding: 0 6px;
    font-size: 12px;
    background: #D9F0FF;
    color: #009DFF;
    &.not_certified {
      background: #E9E9E9;
      color: #666;
    }
  }

  .pc_bottom_introduce {
    margin-top: 8px;
    height: 40px;
    overflow: hidden;

    .brief_introduction_show {
      display: flex;
      flex-direction: column;
      white-space: pre-wrap;
      font-size: 13px;
      font-weight: 400;
      color: #666666;
      line-height: 20px;

      .text {
        word-break: break-all;
      }
    }

    .brief_introduction_hide {
      display: block;
      white-space: pre-wrap;
      font-size: 13px;
      font-weight: 400;
      color: #666666;
      line-height: 20px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      -webkit-line-clamp: 2;
      text-overflow: ellipsis;

      .text {
        word-break: break-all;
      }
    }
  }
}

.pc_navigation {
  width: 1228px;
  min-width: 1228px;
  height: 56px;
  display: flex;
  justify-content: space-between;
  background: #fff;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;

  .pc_tab_list {
    display: flex;
    padding: 0;
    padding: 14px 8px 4px;
    box-sizing: border-box;

    .pc_tab_item {
      display: flex;
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      line-height: 16px;
      padding: 0 6px 0 5px;
      margin-right: 48px;
      height: 38px;
      cursor: pointer;

      &.pc_tab_active_item {
        color: #009DFF;
        position: relative;

        &::after {
          content: '';
          width: 100%;
          height: 4px;
          background: #009DFF;
          border-radius: 4px 4px 4px 4px;
          position: absolute;
          bottom: 0;
          left: 0;
        }
      }

      img {
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }
    }
  }

  .pc_data_wrap {
    display: flex;
    padding: 7px 16px;
    box-sizing: border-box;

    .pc_data_list {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      margin-left: 28px;

      .pc_data_title {
        font-size: 13px;
        font-weight: 400;
        color: #999999;
        line-height: 15px;
        p{
          line-height: 20px;
        }
        :global(.ql-align-center){
          text-align: center;
        }
      }

      .pc_data_text {
        font-size: 14px;
        font-weight: 500;
        color: #666666;
        line-height: 16px;
      }
    }
  }
}

.pc_content {
  margin-top: 12px;
  display: flex;
  width: 1228px;
  min-width: 1228px;
  height: calc(100vh - 288px);
  justify-content: space-between;

  .pc_left_content {
    flex: 1;
    overflow: hidden;
  }

  .pc_right_content {
    width: 351px;
    margin-left: 16px;
    flex-shrink: 0;
    position: relative;

    .vip_member_box {
      background: #fff;
      border-radius: 8px;
      padding: 12px 0 16px;

      .vip_title {
        font-size: 16px;
        font-weight: 500;
        color: #091715;
        line-height: 19px;
        padding-left: 12px;
      }

      .vip_list {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        cursor: pointer;

        .vip_list_item {
          width: 25%;
          flex-shrink: 0;
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-top: 12px;
          padding: 0 5px;
          box-sizing: border-box;
        }

        .vip_list_item_icon {
          width: 44px;
          height: 44px;
          margin-bottom: 3px;

          img {
            width: 44px;
            height: 44px;
          }
        }

        .vip_list_item_content {
          display: flex;
          flex-direction: column;
          align-items: center;

          .vip_list_item_text {
            font-size: 12px;
            font-weight: 400;
            color: #666666;
            line-height: 17px;
            white-space: pre-wrap;
            text-align: center;
          }

          .vip_list_item_tips {
            font-size: 9px;
            font-weight: 400;
            color: #FF895F;
            line-height: 13px;
            white-space: pre-wrap;
            text-align: center;
          }
        }
      }

      .vip_button {
        width: calc(100% - 8px);
        text-align: center;
        margin-top: 16px;
        padding: 0 16px;
        cursor: pointer;

        .vip_button_style {
          width: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          background: linear-gradient(158deg, #FDF0C2 0%, #F5D18A 100%);
          border-radius: 42px 42px 42px 42px;

          .vip_button_text {
            font-size: 16px;
            font-weight: 500;
            line-height: 38px;
            background-image: -webkit-linear-gradient(-90deg, #E8975D 0%, #7D461E 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }
    }

    .character_Img {
      position: absolute;
      bottom: 0;
      right: 0;
      display: flex;
      align-items: flex-end;
      cursor: pointer;

      .QR_code_box {
        position: relative;
        top: -60px;
        width: 104px;
        height: 121px;
        background: url('https://js.5i5ya.com/public/DigitalHealth/assets/white_box.png') no-repeat;
        background-size: cover;
        display: flex;
        flex-direction: column;
        align-items: center;

        .QR_code_img {
          width: 80px;
          height: 80px;
          margin-bottom: 4px;
          margin-top: 8px;
        }

        .QR_code_text {
          font-size: 12px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #333333;
          line-height: 14px;
          white-space: nowrap;
        }
      }

      .people_img {
        width: 61px;
        height: auto;
      }
    }
  }
}

.e_wrap {
  width: 100%;
  height: calc(100vh - 343px);
  background: #fff;
  border-radius: 8px 8px 8px 8px;
  display: flex;
  justify-content: center;
  padding-top: 200px;

  .e_box {
    display: flex;
    flex-direction: column;
    align-items: center;

    .e_img {
      width: 76px;
      height: 76px;
    }

    .e_text {
      margin-top: 12px;
      font-size: 14px;
      font-weight: 400;
      color: #009DFF;
      line-height: 16px;
      cursor: pointer;

      img {
        width: 16px;
        height: 16px;
        margin-left: 4px;
        position: relative;
        top: -1px;
      }
    }
  }
}

.pc_bottom_introduce_popover {
  :global{
    .ant-popover-inner{
      width: 1100px;
    }
    .ant-popover-inner-content {
      max-width: 1100px;
    }
  }
}


/* 学习金布局 */
/* --- 学习金余额Start ---  */
.BalanceStudyFundsBoxWarp {
  width: calc(100% - 20px);
  margin-left: 10px;
}

.BalanceStudyFundsBox {
  width: 100%;
  height: 107px;
  background: linear-gradient( 96deg, #FFF6EA 0%, #FFEEDC 100%);
  border-radius: 8px 8px 8px 8px;
  margin-top: 20px;
  display: flex;
  padding: 14px;
  justify-content: space-between;
  position: relative;

  .StudyBonusBg {
    width: 79px;
    height: 65px;
    background: url('~@/assets/GlobalImg/StudyBonusBg.png');
    background-size: 100% 100%;
    position: absolute;
    right: 16px;
    bottom: 0px;
  }

  .BalanceStudyFundsLeft {

    .BalanceStudyFundsTitle {
      font-weight: 400;
      font-size: 12px;
      color: #000000;
      margin-bottom: 6px;
    }

    .BalanceStudyFundsNum {
      font-weight: 500;
      font-size: 26px;
      color: #000000;
    }

    .BalanceStudyFundsDate {
      font-weight: 400;
      font-size: 12px;
      color: #666666;
    }

  }

  .BalanceStudyFundsRight {
    .DetailBox {
      font-weight: 400;
      font-size: 12px;
      color: #0095FF;
      display: flex;
      align-items: center;
      cursor: pointer;
      user-select: none;

      .DetailBox_icon {
        margin-left: 4px;
        width: 12px;
        height: 12px;
        background: url('~@/assets/GlobalImg/BalanceStudyFund_DetailBox_icon.png');
        background-size: 100% 100%;
        position: relative;
        top: -1px;
      }
    }

  }
}
/* 学习金余额End */
