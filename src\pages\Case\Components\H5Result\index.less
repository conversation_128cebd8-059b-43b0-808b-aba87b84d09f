.wrap {
  width: 100%;
  height: auto;
}
.header{
  padding-bottom: 44px;
  .header_nav{
    background: transparent !important;
  }
}
.screen_wrap {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 16px;
  position: relative;
  border-bottom: 1px solid #EEEEEE;

  .box {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .img_switch_box {
      font-size: 14px;
      font-weight: 400;
      color: #0095FF;
      display: flex;
      align-items: center;
  
      img {
        width: 10px;
        height: 10px;
        margin-right: 4px;
      }
    }
  
    .img_init_box {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      display: flex;
      align-items: center;
  
      img {
        width: 10px;
        height: 10px;
        margin-right: 4px;
      }
    }
  
    .lines {
      width: 1px;
      height: 12px;
      background: #ccc;
      margin: 0 8px;
      position: relative;
      top: 1px;
    }
  
    .screen_list_box {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      display: flex;
      align-items: center;
  
      img {
        width: 10px;
        height: 10px;
        margin-left: 4px;
      }
    }
  
    .screen_list_highlight_box {
      font-size: 14px;
      font-weight: 400;
      color: #0095FF;
      display: flex;
      align-items: center;
  
      img {
        width: 10px;
        height: 10px;
        margin-left: 4px;
      }
    }  
  }
  .mask_box {
    position: absolute;
    top: 103%;
    left: 0;
    height: 100vh;
  }
}

.case_list_content {
  border-top: 8px solid #f5f6f8;
  height: calc(100vh - 140px);
  overflow: auto;
}