import React, { useEffect, useState, useRef } from 'react'
import { connect, history, useAliveController } from 'umi'
import { getIsIniPhoneAndWeixin } from '@/utils/utils'
import { Spin } from 'antd'
import { Toast } from 'antd-mobile'
import styles from './index.less'
import NoDataRender from '@/components/NoDataRender'
import NavBar from '@/components/NavBar'
import MoreOperate from '@/components/MoreOperate'
import UserCardByImageText from '@/components/UserCardByImageText'
import KingdomCard from '@/pages/CreateGraphicsText/ComponentsH5/KingdomCard'
import UserCardByNavBar from '@/pages/CreateGraphicsText/ComponentsH5/UserCardByNavBar'
import ExternalLinksDetailsModal from '@/pages/CreateGraphicsText/ComponentsH5/ExternalLinksDetailsModal'
import linkIcon from '@/assets/GlobalImg/link.png'
import linkBgIcon from '@/assets/GlobalImg/link_bg.png'

const Index: React.FC = (props: any) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')        // 用户信息
  const { id } = history.location.query;
  const { dispatch } = props;

  const refContainer = useRef(null)
  const refUserCard = useRef(null)

  const [detailsState, setDetailsState] = useState<any>(null)        // 详情
  const [navBarToggle, setNavBarToggle] = useState(false)            // 导航状态
  const [loadingImgTextInfoById, setLoadingImgTextInfoById] = useState(false)
  const [pageLoadStatus, setPageLoadStatus] = useState(null)
  const [externalLinksDetailsVisible, setExternalLinksDetailsVisible] = useState(false)
  const { clear } = useAliveController()

  const {
    imageTitle,                        // 标题
    kingdomInfo,                       // 王国信息
    createUserId,                      // 创建人id
    userName,                          // 用户名称
    headUrlShow,                       // 头像
    isExperts,                         // 是否是专家
    operateDateDescs,                  // 创建时间
    isFocus,                           // 是否关注
    expertsInfo,                       // 专家信息
    status,                            // 状态
    outerChainPaperwork,               // 按钮文案
    textImgList,                       // 封面图
    outerChain,                        // 链接地址
  } = detailsState || {};

  // 滚动事件
  useEffect(() => {
    document.getElementById('container') && document.getElementById('container').addEventListener('scroll', handleScroll)

    return () => {
      document.getElementById('container') && document.getElementById('container').removeEventListener('scroll', handleScroll)
    }
  }, [])

  useEffect(() => {
    imgTextInfoById()
  }, [])

  // 滚动事件
  const handleScroll = () => {
    const scrollTop = refContainer.current.scrollTop + 44
    const offset = refUserCard.current.offsetHeight + refUserCard.current.offsetTop
    if (scrollTop >= offset) {
      setNavBarToggle(true)
    } else {
      setNavBarToggle(false)
    }
  }

  // 获取图文详情
  const imgTextInfoById = (isLocalUpdate = false) => {
    if (!isLocalUpdate) {
      setLoadingImgTextInfoById(true)
    }
    dispatch({
      type: 'graphicsText/imgTextInfoById',
      payload: {
        imageTextId: id,                         // 图文主ID  修改时存在
      }
    }).then(res => {
      setLoadingImgTextInfoById(false)
      const { code, content, msg } = res
      if (code == 200 && content) {
        setDetailsState(content)
        setPageLoadStatus(1)
      } else {
        setPageLoadStatus(2)
        Toast.show(msg || '数据加载失败')
      }
    }).catch(err => {
      setPageLoadStatus(2)
    })
  }

  // 关注或取消关注回调
  const handleFollowAndCheck = () => {
    imgTextInfoById(true)
  }

  // 外链详情弹窗，关闭
  const externalLinksDetailsModalShow = () => {
    setExternalLinksDetailsVisible(true)
  }

  // 外链详情弹窗，关闭
  const externalLinksDetailsModalHide = () => {
    setExternalLinksDetailsVisible(false)
  }

  // 返回
  const handleDeleteOrLow = async () => {
    await clear()

    if (history.length > 1) {
      history.goBack()
    } else {
      history.replace('/')
    }
  }

  return (
    <Spin spinning={loadingImgTextInfoById} wrapperClassName={styles.spin}>
      {
        navBarToggle ?
          <UserCardByNavBar
            headUrlShow={headUrlShow}
            userName={userName}
            createUserId={createUserId}
            isExperts={isExperts}
            operateDateDescs={operateDateDescs}
            isFocus={isFocus}
            expertsInfo={expertsInfo}
            handleFollowAndCheck={handleFollowAndCheck}
            id={id}
            imageType={3}
            status={status}
            handleDeleteOrLow={handleDeleteOrLow}
          />
          :
          <NavBar
            title="外链详情"
            bordered
            RightRender={
              () => UserInfo.friUserId == createUserId ?
                <MoreOperate id={id} imageType={3} status={status} handleDeleteOrLow={handleDeleteOrLow}/>
                : null
            }
          />
      }
      <div id="container" className={styles.container} ref={refContainer} style={getIsIniPhoneAndWeixin() ? { paddingBottom: '86px' } : {}}>
        {
          pageLoadStatus == 1 ?
            <>
              <div ref={refUserCard} className={styles.user_card_wrap}>
                <UserCardByImageText
                  headUrlShow={headUrlShow}
                  userName={userName}
                  createUserId={createUserId}
                  isExperts={isExperts}
                  operateDateDescs={operateDateDescs}
                  isFocus={isFocus}
                  expertsInfo={expertsInfo}
                  handleFollowAndCheck={handleFollowAndCheck}
                  isShowMoreOperate={false}
                />
              </div>
              <div className={styles.image_text_content_wrap}>
                <div className={styles.image_title}>{imageTitle}</div>
                {
                  textImgList && textImgList[0] ?
                    <div onClick={externalLinksDetailsModalShow}>
                      <div className={styles.cover_image} style={{backgroundImage: `url(${textImgList && textImgList[0] && textImgList[0].imageUrlShow})`}}></div>
                      <div className={styles.btn_wrap}>
                        <img src={linkIcon} width={16} height={16} alt=""/>
                        <span>{outerChainPaperwork}</span>
                      </div>
                    </div>
                    :
                    <div className={styles.no_cover_wrap} onClick={externalLinksDetailsModalShow}>
                      <div className={styles.left}>
                        <img src={linkBgIcon} width={48} height={48} alt=""/>
                      </div>
                      <div className={styles.right}>
                        <div className={styles.title_wrap}>{imageTitle}</div>
                        <div className={styles.btn_wrap_no_wrap}>
                          <img src={linkIcon} width={16} height={16} alt=""/>
                          <span>{outerChainPaperwork}</span>
                        </div>
                      </div>
                    </div>
                }
              </div>
              {
                kingdomInfo &&
                <div className={styles.kingdom_wrap}>
                  <KingdomCard kingdomInfo={kingdomInfo}/>
                </div>
              }
            </>
            : pageLoadStatus == 2 ?
            <div className={styles.no_data_wrap}>
              <NoDataRender style={{marginTop: 0}}/>
            </div>
            : null
        }
      </div>
      <ExternalLinksDetailsModal
        visible={externalLinksDetailsVisible}
        imageTitle={imageTitle}
        outerChain={outerChain}
        onCancel={externalLinksDetailsModalHide}
      />
    </Spin>
  )
}

export default connect(({ loading }: any) => ({ loading }))(Index)

