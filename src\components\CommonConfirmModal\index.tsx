import React from 'react';
import { connect } from 'umi';
import { useThrottle } from '@/utils/utils'
import { Modal } from 'antd-mobile'
import styles from './index.less';

import WranningIcon from '@/assets/GlobalImg/wranning.png'

const Index: React.FC = (props: any) => {
  const {isVisible, title, text, cancelText='取消', okText='确定'} = props || {};

  const onCancel = () => {
    props.onCancel()
  }

  let onSubmit = () => {
    props.onSubmit()
  }

  onSubmit = useThrottle(onSubmit, 500)

  const container = document.getElementsByTagName("body")[0];
  return <>
    <Modal
      visible={isVisible}
      getContainer={() => container}
      content={
        <div className={styles.container}>
          <div className={styles.title_box}>
            <img src={WranningIcon} width={20} height={20} alt=""/>
            <div className={styles.title}>{title}</div>
          </div>
          <div className={styles.message}>{text}</div>
          <div className={styles.btn_box}>
            <div className={styles.cancel} onClick={onCancel}>{cancelText}</div>
            <div className={styles.ok} onClick={onSubmit}>{okText}</div>
          </div>
        </div>
      }
    >
    </Modal >
  </>
}
export default connect(({ expertAdvice, userInfoStore, loading }: any) => ({ expertAdvice, userInfoStore, loading }))(Index)
