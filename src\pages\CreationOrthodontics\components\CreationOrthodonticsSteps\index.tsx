/**
 * @Description: 指导步骤条公共组件，PC端
 */
import React, { useState } from 'react'
import { connect } from 'umi'
import classNames from 'classnames'
import { QuestionCircleOutlined, CheckOutlined } from '@ant-design/icons'
import styles from './index.less'



// 指导，下单流程，图文，
export const consultationStepList = ['基本信息', '检查及分析', '问题清单及诊断','治疗方案','影像资料','提交病例']

interface PropsType {
  width: any,          // 宽度
  type: number,        // 1 图文下单流程，2 图文服务流程，3 视频下单流程，4 视频服务流程
  processNode: number, // 值为指导订单的状态。流程节点(图文流程节点[1选择指导方式、2描述病例问题、3支付指导费用、4病例资料被查看、5问题被回复并对话、6结束指导交易成功];  视频流程节点[1选择指导方式、2描述病例问题、3提交指导单、4病例资料被查看、5客服预约视频会议、6在线面对面沟通、7结束指导、8确认并支付指导费用、9交易成功])
  isFinish?: any,      // 当前节点是否完成(1是、0否)
}


const Index: React.FC<PropsType> = (props: any) => {
  const { type = 1, processNode = 1, width = '100%', isFinish = 0 } = props
  const [completeProcessVisible, setCompleteProcessVisible] = useState(false)
  const stepList = consultationStepList || []

  // 打开完整服务流程弹窗
  const completeProcessModalShow = () => {
    setCompleteProcessVisible(true)
  }

  // 关闭完整服务流程弹窗
  const completeProcessModalHide = () => {
    setCompleteProcessVisible(false)
  }

  return (
    <div className={styles.step_container}>
      <div className={styles.step_content} style={{width: width}}>
        {
          stepList.map((item, index) => (
            <div
              key={index}
              className={classNames({
                [styles.step_item]:true,
                [styles.step_item_undone]: (index+1) > processNode,
                [styles.step_item_active]: (index+1) == processNode,
                [styles.step_item_finish]: (index+1) < processNode
              })}
            >
              {<div className={styles.step_item_icon}>
                {/*{ (index+1) > processNode ? index + 1 : '' }*/}
              </div>}
              <div className={styles.step_item_text}>{item}</div>
            </div>
          ))
        }
      </div>
    </div>
  )
}

export default connect(({ userInfoStore, loading }: any) => ({userInfoStore, loading}))(Index)
