import React, { useState, useEffect } from 'react';
import { connect, history } from "umi";
import classNames from 'classnames'
import styles from "./index.less";
import { Spin, Anchor, Image } from 'antd';
import ToothBit from "@/components/ToothBit/ToothBit";
import {getToothBitInfo, toothUtils} from "@/utils/ToothSelect";
import NavBar from "@/components/NavBar";
import docxIcon from '@/assets/Consultation/H5/docx_icon.png'
import xlsxIcon from '@/assets/Consultation/H5/xlsx_icon.png'
import zipIcon from '@/assets/Consultation/H5/zip_icon.png'
import pptxIcon from '@/assets/Consultation/H5/pptx_icon.png'
import pdfIcon from '@/assets/Consultation/H5/pdf_icon.png'
import stlIcon from '@/assets/Consultation/H5/stl_icon.png'
import pcGobackIcon from '@/assets/GlobalImg/pc_goback.png'
const { Link } = Anchor;

const Index: React.FC = (props: any) => {
  const { dispatch ,match ,loading} = props;
  const { params } = match;
  const { consultationId } = params;
  const { customerId, tenantId, type } = props.location.query || {}
  const [dataSource, setDataSource] = useState<any>(null)

  const {
    topicName,              // 主题
    difficultLevelDict,     // 难度等级
    depSubjectDictNameList, // 学科字典(多值)
    achievementDictName,    // 病历成就名称
    doctorUserList,         // 主诊医生(多个)
    keyWordList,            // 关键词
    checkList,              // 检查
    diagnosisList,          // 诊断
    treatmentPlanList,      // 治疗计划
    treatmentProcessList,   // 治疗过程
    summaryDiscussList,     // 总结与讨论
    collectCount,           // 收藏数量
    isCollect,              // 是否收藏 0未收藏 1已收藏
    solution,               // 治疗方案
    isAllLook,              // 是否可以全文查看 1可以 0不可以
    caseLinkTypeDict,       // 小鹅通资源类型
    floatImgShowUrl,        // 病例浮窗图片展示链接
    floatPathUrl,           // 病例浮窗链接路径
    consultationCaseInfoDto,
    type:typeByDataSource,
    orderCaseTemplate,
  } = dataSource || {};

  const {
    orthodonticCaseDictDtoList,  // 正畸病例详情数据
    age, // 年龄
    sex, // 性别(1男、2女、3未知)
    customerName,    // 患者姓名
    fileNumber,     // 病例号
    chiefComplaint,         // 主诉
    presentDisease,         // 现病史
    previousHistory,        // 既往史
    wholeHealth,            // 全身健康情况
    firstQuestion,
  } = consultationCaseInfoDto || {}

  useEffect(() => {
    if (type == 2) {
      getConsultationOrthodonticCaseInfo()
    }else {
      getOrthodonticCaseInfo()
    }
  }, [])


  /**
   * 基本信息里面的病例
   * @param id 对应数据ID
   */
  const basicCaseFun = (id: number) => {
    if (Array.isArray(orthodonticCaseDictDtoList) && orthodonticCaseDictDtoList.length) {
      const result = orthodonticCaseDictDtoList.find(item => item.id === id);
      return (
        <>
          <div className={styles.basic_title}>{result.dictName}</div>
          <div className={styles.basic_info_content_desc}>{result.inputContent || '无'}</div>
        </>
      )
    }
  }

  /**
   * 问题清单及诊断
   * @param id 对应数据ID，问题清单的id为3，诊断为4
   * @param index 显示的序号
   */
  const getIssuesDom = (id, index) => {
    if (Array.isArray(orthodonticCaseDictDtoList) && orthodonticCaseDictDtoList.length) {
      const result = orthodonticCaseDictDtoList.find(item => item.id === id);
      const { subsetList } = result || {}
      return <>
        <div key={`title_Issues${index}`} className={styles.detail_title}>{index}、{result.dictName}</div>
        <div key={`wrap_Issues${index}`} className={styles.details_wrap}>
          {
            subsetList && subsetList.length > 0 && subsetList.map((item, index) => {
              return (
                <div className={classNames(styles.detail_item, {
                  [styles.detail_item_100]: item.dictName == '其他问题' || item.dictName == '其他诊断',
                })} key={index}>
                  <div className={styles.label}>{item.dictName}：</div>
                  <div className={styles.value}>
                    {
                      item.operateType == 2 ? <span>{item.inputContent}</span> : null
                    }

                    {
                      item.subsetList && item.subsetList.length > 0 && item.subsetList.map((item2, index2) => {
                        if (item2.operateType == 1 && item2.isCheck == 1) {
                          return <span key={index2}>{item2.dictName}</span>
                        }
                        if (item2.operateType == 2 && item2.inputContent) {
                          return <span key={index2}>{item2.dictName}{item2.inputContent}{item2.unit}</span>
                        }
                      })
                    }
                  </div>
                </div>
              )
            })
          }
        </div>
      </>
    }
  }

  /**
   * 正畸、其他影像展示
   * @param id
   */
  const orthodonticImagingFun = (id: number, index) => {
    if (Array.isArray(orthodonticCaseDictDtoList) && orthodonticCaseDictDtoList.length) {
      const result = orthodonticCaseDictDtoList.find(item => item.id === id);
      const { subsetList } = result || {}
      return <div key={index} className={styles.image_data} id='image_data'>
        <div className={styles.detail_title}>{index}、{result.dictName}</div>
        <div className={styles.detail_wrap_image}>
          {
            Array.isArray(subsetList) && subsetList.length > 0 && subsetList.map((item, index) => {
              if (!item.fileUrlShow) {
                return null
              }
              return (
                <div className={styles.detail_item_image} key={index}>
                  <Image width={100} height={100} src={item.fileUrlShow} />
                  <div className={styles.image_label}>{item.dictName}</div>
                </div>
              )
            })
          }
        </div>
      </div>
    }
  }


  // 病例详情数据
  const getOrthodonticCaseInfo = () => {
    dispatch({
      type: 'consultation/getOrthodonticCaseInfo',
      payload: {
        consultationId,
        customerId,
        tenantId,
        type: 0,
      }
    }).then((res: any) => {

      if (res != undefined) {
        const { code, content } = res || {}
        if (code == 200) {
          setDataSource(content)
        }
      }
    })
  }

  const getConsultationOrthodonticCaseInfo = () => {
    dispatch({
      type: 'consultation/getConsultationOrthodonticCaseInfo',
      payload: {
        consultationId:consultationId,
        type: 0,
      }
    }).then((res: any) => {
      if (res!= undefined) {
        const { code, content } = res || {}
        if (code == 200) {
          setDataSource(content)
        }
      }
    })
  }

  /**
   * 检查
   * @param id 对应数据ID，检查的id为1
   */
  const getInspectDom = (id) => {
    if (Array.isArray(orthodonticCaseDictDtoList) && orthodonticCaseDictDtoList.length) {
      const result = orthodonticCaseDictDtoList.find(item => item.id === id);
      const { subsetList } = result || {}
      const toothItem1 = subsetList && subsetList.length > 0 && subsetList.find(item => item.id == 46)
      const toothItem2 = toothItem1 && toothItem1.subsetList && toothItem1.subsetList.length > 0 && toothItem1.subsetList.find(item => item.id == 61)
      const toothObj = toothItem2 && toothItem1.subsetList && toothItem2.subsetList.filter(item => item.toothPosition)
      return (
        <div className={styles.detail}>
          {
            Array.isArray(subsetList) && subsetList.length > 0 && subsetList.map((item, index) => {
              return <>
                <div key={'title' + index} className={styles.detail_title}>{index + 1}、{item.dictName}</div>
                <div key={'wrap' + index} className={styles.details_wrap}>
                  {
                    item.subsetList && item.subsetList.length > 0 && item.subsetList.map((item2, index2) => {
                      return (
                        <div className={styles.detail_item} key={index2}>
                          <div className={styles.label}>{item2.dictCode}.{item2.dictName}：</div>
                          <div className={styles.value}>
                            {
                              item2.operateType == 2 ? <span>{item2.inputContent}</span> : null
                            }

                            {
                              item2.subsetList && item2.subsetList.length > 0 && item2.subsetList.map((item3, index3) => {
                                if (item3.operateType == 1 && item3.isCheck == 1) {
                                  return (
                                    <span key={index3}>
                                      {item3.dictName}
                                      {
                                        item3.subsetList && item3.subsetList.length > 0 && item3.subsetList.map((item4, index4) => {
                                          if (item4.operateType == 1 && item4.isCheck == 1) {
                                            return `(${item4.dictName})`
                                          }
                                        })
                                      }
                                    </span>
                                  )
                                }
                                if (item3.operateType == 2 && item3.inputContent) {
                                  return <span key={index3}>{item3.dictName}{item3.inputContent}</span>
                                }
                                if (item3.subsetList && item3.subsetList.length > 0) {
                                  return <>
                                    {
                                      item3.subsetList.map((item4, index4) => {
                                        if (item4.operateType == 1 && item4.isCheck == 1) {
                                          return <span key={index4}>{item3.dictName}{item4.dictName}</span>
                                        }
                                      })
                                    }
                                  </>
                                }
                              })
                            }
                          </div>
                        </div>
                      )
                    })
                  }

                  {/* 牙位 */}
                  {
                    item.dictName == '口内检查' && toothObj && toothObj.length > 0 ?
                      <div key="tooth" className={classNames(styles.detail_item, styles.detail_item_tooth)}>
                        {
                          toothObj.map((item, index) => {
                            return (
                              <div className={styles.detail_item_wrap} key={index}>
                                <div className={styles.label}>{item.dictName}：</div>
                                <div className={styles.value_tooth}>
                                  <ToothBit ToothInfo={ToothBitData(item.toothPosition)} />
                                </div>
                              </div>
                            )
                          })
                        }

                      </div>
                      : null
                  }
                </div>
              </>
            })
          }
        </div>
      )
    }
  }

  // 牙位数据
  const ToothBitData = (value:any) => {
    let toothInfo = {
      leftTop: toothUtils.showTooth(1, value),
      rigthTop: toothUtils.showTooth(2, value),
      rigthBottom: toothUtils.showTooth(3, value),
      leftBottom: toothUtils.showTooth(4, value),
      rawData: value,
    }
    return getToothBitInfo(toothInfo)
  }

  /**
   * 附件展示
   * @param id
   */
  const annexCaseFun = (id: number, index) => {
    if (Array.isArray(orthodonticCaseDictDtoList) && orthodonticCaseDictDtoList.length) {
      const result = orthodonticCaseDictDtoList.find(item => item.id === id);
      const { subsetList } = result || {}
      return <>
        <div className={styles.detail_title}>{index}、{result.dictName}</div>
        <div className={styles.detail_wrap_annex}>
          {
            Array.isArray(subsetList) && subsetList.length && Array.isArray(subsetList[0].subsetList) && subsetList[0].subsetList.length && subsetList[0].subsetList.map((item, index) => {
              return (
                <div className={styles.detail_item_annex} key={index}>
                  <div className={styles.annex_icon}>
                    {annexFormatFun(item.fileSuffix)}
                  </div>
                  <div className={styles.annex_content}>
                    <div className={styles.annex_name}>{item.fileName}.{item.fileSuffix}</div>
                    <div className={styles.annex_size}>{sizeFun(item.fileSize)}kb</div>
                  </div>
                  <a className={styles.annex_btn} href={item.fileUrlShow} download={`${item.fileName}.${item.fileSuffix}`}>下载</a>
                </div>
              )
            })
          }
        </div>
      </>
    }
  }

  /**
   * 文件大小处理
   * @param val
   * @returns
   */
  const sizeFun = (val: number) => {
    let result = Math.ceil(val / 1024);
    return result.toFixed(1);
  }

  /**
   * 获取文件格式icon
   * @param suffix   文件后缀
   */
  const annexFormatFun = (suffix: string): any => {
    switch (suffix) {
      case 'docx':
        return <img src={docxIcon} width={36} height={36} alt={suffix} />
      case 'doc':
        return <img src={docxIcon} width={36} height={36} alt={suffix} />
      case 'xlsx':
        return <img src={xlsxIcon} width={36} height={36} alt={suffix} />
      case 'xls':
        return <img src={xlsxIcon} width={36} height={36} alt={suffix} />
      case 'zip':
        return <img src={zipIcon} width={36} height={36} alt={suffix} />
      case 'pptx':
        return <img src={pptxIcon} width={36} height={36} alt={suffix} />
      case 'ppt':
        return <img src={pptxIcon} width={36} height={36} alt={suffix} />
      case 'pdf':
        return <img src={pdfIcon} width={36} height={36} alt={suffix} />
      case 'stl':
        return <img src={stlIcon} width={36} height={36} alt={suffix} />
      default:
        return ''
    }
  }


  /**
   * 诊断分析
   * @param id 对应数据ID，诊断分析的id为2
   */
  const getDiagnosisDom = (id) => {
    if (Array.isArray(orthodonticCaseDictDtoList) && orthodonticCaseDictDtoList.length) {
      const result = orthodonticCaseDictDtoList.find(item => item.id === id);
      const { subsetList } = result || {}
      return (
        <div className={styles.detail}>
          {
            Array.isArray(subsetList) && subsetList.length > 0 && subsetList.map((item, index) => {
              return <>
                <div key={'diagnosis_title'+index} className={styles.detail_title}>{index + 1}、{item.dictName}</div>
                <div key={'diagnosis_wrap'+index} className={styles.details_wrap}>
                  {
                    item.subsetList && item.subsetList.length > 0 && item.subsetList.map((item2, index2) => {
                      return (
                        <div className={classNames(styles.detail_item, {
                          [styles.detail_item_100]: item2.dictName == '侧位片分析' || item2.dictName == '全景片分析',
                        })} key={index2}>
                          <div className={styles.label}>{item2.dictName}：</div>
                          <div className={styles.value}>
                            {
                              item2.operateType == 2 ? <span>{item2.inputContent}{item2.unit}</span> : null
                            }

                            {
                              item2.subsetList && item2.subsetList.length > 0 && item2.subsetList.map((item3, index3) => {
                                if (item3.operateType == 1 && item3.isCheck == 1) {
                                  return <span key={index3}>{item3.dictName}</span>
                                }
                                if (item3.operateType == 2 && item3.inputContent) {
                                  return <span key={index3}>{item3.dictName}{item3.inputContent}{item3.unit}</span>
                                }
                              })
                            }
                          </div>
                        </div>
                      )
                    })
                  }
                </div>
              </>
            })
          }
        </div>
      )
    }
  }

  /**
   * 治疗方案
   * @param id 对应数据ID，治疗目标的id为5，治疗方案为6
   * @param index 显示的序号
   */
  const getTreatmentPlanDom = (id, index) => {
    if (Array.isArray(orthodonticCaseDictDtoList) && orthodonticCaseDictDtoList.length) {
      const result = orthodonticCaseDictDtoList.find(item => item.id === id);
      const { subsetList } = result || {}
      return <>
        <div className={styles.detail_title}>{index}、{result.dictName}</div>
        <div key={index} className={styles.details_wrap}>
          {
            subsetList && subsetList.length > 0 && subsetList.map((item, index) => {
              return (
                <div className={classNames(styles.detail_item, {
                  [styles.detail_item_100]: item.dictName == '其他目标' || item.dictName == '治疗方案',
                })} key={index}>
                  <div className={styles.label}>{item.dictName}：</div>
                  <div className={styles.value}>
                    {
                      item.operateType == 2 ? <span>{item.inputContent}</span> : null
                    }

                    {
                      item.subsetList && item.subsetList.length > 0 && item.subsetList.map((item2, index2) => {
                        if (item2.operateType == 1 && item2.isCheck == 1) {
                          return <span key={index2}>{item2.dictName}</span>
                        }
                        if (item2.operateType == 2 && item2.inputContent) {
                          return <span key={index2}>{item2.dictName}{item2.inputContent}</span>
                        }
                      })
                    }
                  </div>
                </div>
              )
            })
          }
        </div>
      </>
    }
  }




  return (
    <div>
      <div className={styles.wrap}>
        <div className={styles.header}>
          <NavBar title="病例详情" className={styles.header_nav} />
        </div>
        <div className={styles.container}>
          <div className={styles.basic_info}>
            {/*-----病例提问-----*/}
            {/*病例问题*/}
            {typeByDataSource != 3 && orderCaseTemplate == 2 &&
              <div id={`anchor-0`}>
                <div className={styles.detail_title}>病例提问</div>
                <div style={{paddingLeft:'0px'}} key={'anchor-warp'} className={styles.details_wrap}>
                  {firstQuestion}
                </div>
              </div>
            }

            {/*-----基本信息-----*/}
            <div id={`anchor-1`}>
              <div className={classNames([styles.basic_header, styles.basic_info_one])}>
                <span className={styles.basic_header_title}>基础信息</span>
                <div className={styles.basic_header_back}></div>
              </div>
              {
                age || sex ? <div className={styles.basic_info_age_sex}>
                  {
                    age ? <div className={styles.basic_wrap}>
                      <span className={styles.basic_title}>年龄</span>
                      <span className={styles.basic_value}>{age}</span>
                    </div> : null
                  }
                  {
                    sex ? <div className={styles.basic_wrap}>
                      <span className={styles.basic_title}>性别</span>
                      <span className={styles.basic_value}>{sex}</span>
                    </div> : null
                  }
                </div> : null
              }
              {/* 主诉 */}
              {basicCaseFun(277)}
            </div>

            {/*-----问题清单及诊断-----*/}
            <div id={`anchor-2`} className={styles.basic_info_two}>
              <div className={styles.inspect_diagnosis_wrap}>
                <div className={styles.basic_header}>
                  <span className={styles.basic_header_title}>问题清单及诊断</span>
                  <div className={styles.basic_header_back}></div>
                </div>
                {/* 问题清单 */}
                {getIssuesDom(3, 1)}
                {/* 诊断 */}
                {getIssuesDom(4, 2)}
              </div>
            </div>

            {/*-----影像资料-----*/}
            <div id={`anchor-3`} className={classNames(styles.basic_info_two,styles.image_data)}>
              <div className={styles.inspect_diagnosis_wrap}>
                <div className={styles.basic_header}>
                  <span className={styles.basic_header_title}>影像资料</span>
                  <div className={styles.basic_header_back}></div>
                </div>
                {/* 正畸影像 */}
                {orthodonticImagingFun(7, 1)}
                {/* 其他影像 */}
                {orthodonticImagingFun(8, 2)}
                {/* 其他附件 */}
                {annexCaseFun(9, 3)}
              </div>
            </div>

            {/*-----健康信息-----*/}
            <div id={`anchor-4`} className={styles.basic_info_two}>
              <div className={styles.inspect_diagnosis_wrap}>
                <div className={styles.basic_header}>
                  <span className={styles.basic_header_title}>健康信息</span>
                  <div className={styles.basic_header_back}></div>
                </div>
                {/*现病史*/}
                {basicCaseFun(278)}
                {/*既往史*/}
                {basicCaseFun(279)}
                {/*全身健康状况*/}
                {basicCaseFun(280)}
              </div>
            </div>

            {/*----检查----*/}
            <div id={`anchor-5`} className={classNames(styles.basic_info_two,styles.inspect)}>
              <div className={styles.inspect_diagnosis_wrap}>
                <div className={styles.basic_header}>
                  <span className={styles.basic_header_title}>检查</span>
                  <div className={styles.basic_header_back}></div>
                </div>
                {/*口外检查/口内检查/颞颌关节检查*/}
                {getInspectDom(1)}
              </div>
            </div>

            {/*----诊断分析----*/}
            <div>
              <div id={`anchor-6`} className={styles.basic_info_two}>
                <div className={styles.inspect_diagnosis_wrap}>
                  <div className={styles.basic_header}>
                    <span className={styles.basic_header_title}>诊断分析</span>
                    <div className={styles.basic_header_back}></div>
                  </div>
                  {/*模型分析/侧位片分析/全景片分析*/}
                  {getDiagnosisDom(2)}
                </div>
              </div>
            </div>

            {/*----治疗方案----*/}
            <div>
              <div id={`anchor-7`} className={styles.basic_info_two}>
                <div className={styles.inspect_diagnosis_wrap}>
                  <div className={styles.basic_header}>
                    <span className={styles.basic_header_title}>诊断分析</span>
                    <div className={styles.basic_header_back}></div>
                  </div>
                  {/*治疗目标*/}
                  {getTreatmentPlanDom(5, 1)}
                  {/*治疗方案*/}
                  {getTreatmentPlanDom(6, 2)}
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  )
}
export default connect(({ consultation, loading }: any) => ({ consultation, loading }))(Index)
