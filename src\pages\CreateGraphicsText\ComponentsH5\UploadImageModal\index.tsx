import React, { useEffect, useState } from 'react'
import classNames from 'classnames'
import { Upload } from 'antd'
import { Popup, Toast } from 'antd-mobile'
import NoDataRender from '@/components/NoDataRender'       // 暂无数据
import { stringify } from 'qs'
import { cloneDeep } from 'lodash'
import request from '@/utils/request'
import { getIsIniPhoneAndWeixin, getHeaders } from '@/utils/utils'
import { PlusOutlined, CloseCircleFilled, CloseOutlined } from '@ant-design/icons'
import styles from './index.less'


interface PropsType {
  visible: boolean,                    // 弹窗是否显示
  articleTextImgList: object,          // 图片数据
  uploadImageMaxNumber: number,        // 上传数量
  handleUploadImage: () => {},
  onCancel: () => {},
}

let loadingUploadImage = 0

const Index: React.FC<PropsType> = (props: PropsType) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')        // 用户信息
  const { visible, articleTextImgList, uploadImageMaxNumber } = props

  const [currentTab, setCurrentTab] = useState(1)                      // tab值
  const [selectedTextImgList, setSelectedTextImgList] = useState([])   // 选择的图片

  useEffect(() => {

    return () => {
      loadingUploadImage = 0
    }
  }, [])

  useEffect(() => {
    // 重置数据
    if (visible) {
      setCurrentTab(1)
      setSelectedTextImgList([])
    }
  }, [visible])

  // 切换标签页
  const tabsOnChange = (value) => {
    setCurrentTab(value)
    setSelectedTextImgList([])
  }

  // 选择图片
  const selectArticleImage = (value, isSelected, index) => {
    if (isSelected && selectedTextImgList.length >= uploadImageMaxNumber) {
      return
    }
    const selectedTextImgListClone = cloneDeep(selectedTextImgList)
    if (isSelected) {
      selectedTextImgListClone.push(value)
    } else {
      selectedTextImgListClone.splice(index, 1)
    }
    setSelectedTextImgList(selectedTextImgListClone)
  }

  // 上传校验
  const beforeUpload = (file) => {
    const isSize = file.size / 1024 / 1024 < 15
    if (!isSize) {
      Toast.show('超过15M限制，不允许上传~')
      return false
    }

    const { name: fileName } = file || {}
    const suffix = fileName.substring(fileName.lastIndexOf('.')+1).toLowerCase()
    const isJpgOrPng = file.type === 'image/jpg' || file.type === 'image/jpeg' ||  file.type === 'image/png' || file.type === 'image/gif'
    const isSuffixByJpgOrPng = suffix === 'jpg' || suffix === 'jpeg' || suffix === 'png' || suffix === 'gif'
    if (!isJpgOrPng || !isSuffixByJpgOrPng) {
      Toast.show({content: '只能上传JPG、JPEG、PNG、GIF格式的图片~'})
      return false
    }

    if (selectedTextImgList.length >= uploadImageMaxNumber) {
      Toast.show(`最多上传${uploadImageMaxNumber}张`)
      return false
    }

    loadingUploadImage += 1
    return true
    // return new Promise((resolve, reject) => {
    //   const fileReaderBuffer = new FileReader();
    //   fileReaderBuffer.onload = async () => {
    //     const type = getFileType(fileReaderBuffer);
    //     if (type === 'unknown') {
    //       Toast.show('图片格式错误')
    //       reject()
    //       return;
    //     }
    //     if (type.includes('/heic')) {
    //       heic2any({ blob: file, toType: 'image/jpeg' }).then((blob) => {
    //         resolve(blob)
    //       }).catch((err) => {
    //         Toast.show('上传失败')
    //         reject()
    //       });
    //     } else {
    //       resolve(file)
    //     }
    //   };
    //   fileReaderBuffer.readAsArrayBuffer(file);
    // })
  }

  // 上传完成
  const uploadOnChange = (info) => {
    console.log('uploadOnChange',info)
    if (info && !info.file.status) {
      return
    }

    if (info.file.status === 'uploading') {
      Toast.show({
        icon: 'loading',
        content: '',
        duration: 0,
      })
    }

    if (info && info.file.status === 'error') {
      loadingUploadImage -= 1
      if (loadingUploadImage == 0) {
        Toast.clear()
      }
      Toast.show('上传失败')
      return
    }

    if (info && info.file.status === 'done') {
      loadingUploadImage -= 1
      if (loadingUploadImage == 0) {
        Toast.clear()
      }
      if (selectedTextImgList.length >= uploadImageMaxNumber) {
        Toast.show(`最多上传${uploadImageMaxNumber}张`)
        return false
      }
      const res = info.file.response || {}
      const { code, content, msg } = res
      if (code == 200 && content) {
        selectArticleImage(content.fileUrlView, true)
      } else {
        Toast.show(msg || '上传失败')
      }
    }
  }

  // 自定义上传方法
  const uploadCustomRequest = ({onProgress,onError,onSuccess,data,filename,file,withCredentials,action,headers}) => {
    const formData = new FormData()
    formData.append('file', file)
    if (/iPhone/.test(window.navigator.userAgent) && /QQBrowser/.test(window.navigator.userAgent)) {
      const random = Math.floor(Math.random()*1000) + 100
      setTimeout(() => {
        request(action, {
          method: 'POST',
          body: formData,
        }).then(res => {
          const { code } = res
          if (code == 200) {
            onSuccess(res)
          } else {
            onError(res)
          }
        })
      }, random)
    } else {
      request(action, {
        method: 'POST',
        body: formData,
      }).then(res => {
        const { code } = res
        if (code == 200) {
          onSuccess(res)
        } else {
          onError(res)
        }
      })
    }
  }

  // 删除图片
  const deleteImage = (index) => {
    const selectedTextImgListClone = cloneDeep(selectedTextImgList)
    selectedTextImgListClone.splice(index, 1)
    setSelectedTextImgList(selectedTextImgListClone)
  }

  // 确定
  const ok = () => {
    if (selectedTextImgList.length == 0) {
      Toast.show('请选择图片')
      return
    }
    props.handleUploadImage(selectedTextImgList)
  }
  return (
    <>
      <Popup
        className={styles.popup_container}
        visible={visible}
        onMaskClick={props.onCancel}
        bodyStyle={{ height: 'calc(100% - 44px)', paddingBottom: getIsIniPhoneAndWeixin() ? 34 : 16 }}
        destroyOnClose
      >
        <div className={styles.header} onClick={props.onCancel}>
          <span className={styles.gray_bar}></span>
        </div>
        <div className={styles.tabs_box}>
          <CloseOutlined onClick={props.onCancel}/>
          <div className={classNames(styles.tabs_item, {
            [styles.selected]: currentTab == 1,
          })} onClick={() => tabsOnChange(1)}>文中图片</div>
          <div className={classNames(styles.tabs_item, {
            [styles.selected]: currentTab == 2,
          })} onClick={() => tabsOnChange(2)}>手机相册</div>
        </div>
        <div className={styles.content}>
          <div className={styles.image_box}>
            {
              currentTab == 1 ?
                <>
                  {
                    articleTextImgList.length == 0 ?
                      <div className={styles.empty_wrap}>
                        <NoDataRender className={styles.noDataStyle} text={'暂无图片'} />
                      </div>
                      : articleTextImgList.map((item, index) => {
                        const selectedIndex = selectedTextImgList.findIndex(itemChild => itemChild == item)
                        return (
                          <div key={item + index} className={styles.image_item_wrap} onClick={() => selectArticleImage(item, selectedIndex == -1, selectedIndex)}>
                            <div className={classNames(styles.image_item, {
                              [styles.selected]: selectedIndex > -1,
                            })} style={{backgroundImage: `url(${item})`}}>
                              {
                                selectedIndex > -1 && <>
                                  <div className={styles.mask}></div>
                                  <span className={styles.selected_number}>{selectedIndex + 1}</span>
                                </>
                              }
                            </div>
                          </div>
                        )
                      })
                  }
                </>
                :
                <>
                  {
                    selectedTextImgList.map((item, index) => {
                      return (
                        <div key={item+index} className={styles.image_item_wrap}>
                          <div className={styles.image_item} style={{backgroundImage: `url(${item})`}}>
                            <CloseCircleFilled onClick={() => deleteImage(index)}/>
                          </div>
                        </div>
                      )
                    })
                  }
                  <div className={styles.image_item_wrap}>
                    <div className={styles.upload_item}>
                      <div className={styles.wrap}>
                        <PlusOutlined />
                        <span>上传图片</span>
                      </div>
                      <Upload
                        headers={getHeaders()}
                        accept="image/*"
                        action={`/api/server/base/uploadFile?${stringify({ fileType: 23, userId: UserInfo?.friUserId})}`}
                        onChange={uploadOnChange}
                        beforeUpload={beforeUpload}
                        showUploadList={false}
                        multiple={true}
                        customRequest={uploadCustomRequest}
                      />
                    </div>
                  </div>
                </>
            }
          </div>
        </div>
        <div className={styles.bottom_btn_box}>
          <div className={styles.btn} onClick={ok}>确定</div>
        </div>
      </Popup>
    </>
  )
}

export default Index
