import React, { useState,useEffect } from 'react';
import { history,connect } from 'umi';
import styles from './Step1.less';
import classNames from "classnames";
import {Button, Checkbox, Form, Input, message, Spin} from 'antd';
import Navigation from "@/pages/Payment/components/PC/Navigation";
import BreadcrumbByPayment from "@/pages/Payment/components/PC/BreadcrumbByPayment";
import NavBar from '@/components/NavBar'         // 导航条
import {
  getOrderPlan,      // 获取订单计划
  createOrder, confirmOrder,       // 创建订单
} from "@/services/payment/index";
import {
  getAccountInfo,
  getMsgCodeUserInfo, // 获取用户信息
} from "@/services/login/login";
import { getOperatingEnv,WxAppIdByPublicAccount } from "@/utils/utils";
import {stringify} from "qs";
import { debounce } from 'lodash';

// 填写信息表单
const InformationFrom: React.FC = (props) => {
  const {
    orderPlanData,
    vipType,
    contentByConfirmOrder,  // 订单详情
  } = props

  const [form] = Form.useForm(); // 使用Ant Design的Form组件
  const [formData, setFormData] = useState({
    isEditByEnterpriseName: true,
    isEditByPhone: true,
    scheme: null,
    enterpriseName: null,
    phone: null,
  });
  const [loading, setLoading] = useState(false);
  const [agree, setAgree] = useState(null)                        // 是否同意协议

  // 初始化表单数据
  useEffect(() => {
    let formDataByObj = sessionStorage.getItem('formDataByObj')
    const {orderOrganizationName, orderOrganizationTel} = contentByConfirmOrder || {}
    // 如果当前是回退回来的页面,则获取sessionStorage的数据回显到表单上
    if(!!formDataByObj){
      formDataByObj = JSON.parse(formDataByObj)
      console.log('formDataByObj哈哈哈 :: ',formDataByObj);
      const {
        agree,                  //: null
        enterpriseName,         //: "哈哈哈哈"
        isEditByEnterpriseName, //: true
        isEditByPhone,  //: true
        phone,          //: "13811636625"
        scheme,         //: 4
      } = formDataByObj || {}

      // 回显表单的默认值
      const defaultValues = {
        scheme: scheme ? scheme : Array.isArray(orderPlanData) ? orderPlanData[0]?.planId : null,                  // 默认选中第一个方案
        enterpriseName: enterpriseName ? enterpriseName : orderOrganizationName ? orderOrganizationName : null,    // 默认企业名称
        phone: phone ? phone : orderOrganizationTel ? orderOrganizationTel : null,                                 // 默认手机号
      };
      form.setFieldsValue(defaultValues); // 设置表单默认值
      // 设置是否点击同意协议
      setAgree(agree)
      // 设置state的默认值
      setFormData((prevFormData) => {
        return ({
          ...prevFormData,
          ...defaultValues,
          isEditByEnterpriseName: isEditByEnterpriseName,     // 默认企业名称
          isEditByPhone: isEditByPhone,                       // 默认手机号
        });
      });

      let keysBydefaultValues = Object.keys(defaultValues).filter((item)=>{return !!defaultValues[item]})
      form.validateFields(keysBydefaultValues) // 校验表单
    }else {
      // 设置表单的默认值
      const defaultValues = {
        scheme: Array.isArray(orderPlanData) ? orderPlanData[0]?.planId : null,       // 默认选中第一个方案
        enterpriseName: orderOrganizationName ? orderOrganizationName : null,         // 默认企业名称
        phone: orderOrganizationTel ? orderOrganizationTel : null,                    // 默认手机号
      };
      form.setFieldsValue(defaultValues); // 设置表单默认值
      // 设置state的默认值
      setFormData((prevFormData) => {
        return ({
          ...prevFormData,
          ...defaultValues,
          isEditByEnterpriseName: orderOrganizationName ? null : true,         // 默认企业名称
          isEditByPhone: orderOrganizationTel ? null : true,                  // 默认手机号
        });
      });
    }
  }, [contentByConfirmOrder,orderPlanData]);



  // 提交PC订单表单方法和请求创建订单
  const onFinishByPC = debounce(async (values) => {
    const userInfoData = JSON.parse(localStorage.getItem('userInfo'));
    const { friUserId:id } = userInfoData || {}  // 获取用户id

    let {
      enterpriseName:enterpriseNameByFormData,
      phone:phoneByFormData
    } = formData || {};

    // 以下是校验和拼接JSON格式的代码，你可以根据你的需求进行修改和适配
    const {
      enterpriseName,  // 企业名称
      phone,           // 手机号
      scheme,          // 确认的订单方案
    } = values || {}

    // 请求接口创建订单
    let params = {
      orderOrganizationName:enterpriseName ? enterpriseName : enterpriseNameByFormData,
      orderOrganizationTel:phone ? phone : phoneByFormData,
      wxUserId:id,
      planId:scheme,
    }
    setLoading(true); // 开启loading动画
    let DataBycreateOrder = await createOrder(params)
    setLoading(false); // 关闭loading动画
    // 返回接口格式 {"code":200,"content":25,"msg":null,"enMsg":null}
    if (DataBycreateOrder && DataBycreateOrder.code === 200 && DataBycreateOrder.content) {
      // 保存用户信息
      // {"code":200,"content":26,"msg":null,"enMsg":null}
      message.success('提交订单成功');
      history.push(`/Payment/Step2/${DataBycreateOrder.content}?random=${Math.random()}`);
    }else {
      message.error(DataBycreateOrder.msg ? DataBycreateOrder.msg : '订单创建失败');
    }
  },500);

  const getSelectPlanPrice = (selectPlanId)=>{
    let orderPlanDataByform = Array.isArray(orderPlanData) ? orderPlanData : []
    let selectByObj = orderPlanDataByform && orderPlanDataByform.find((item)=>item.planId == selectPlanId)
    const {
      descs,            //: null
      planId,           //: 4
      planName,         //: "3年"
      planNum,          //: 3
      planPrice,        //: 18000
      planServiceName,  //: "企业版3年"
      planUnit,         //: "元/年"
      remark,           //: "送价值6万智能设备三年使用权"
    } = selectByObj || {}
    return !!(planPrice * planNum) ? planPrice * planNum : 0
  }

  // 提交订单判定是否同意协议,
  const onSubmit = debounce(()=>{
    // 获取地址栏参数 vipType
    let { vipType } = props || {}
    vipType = vipType == 2 ? 2 : 1
    // vipType 1:个人版,2:企业版
    if (vipType == 2 && !agree) {
      message.warning('请勾选同意《企业版服务协议》');
      return
    }
    form && form.submit();
  },500)


  // 跳转到企业版服务协议前保存页面的临时数据
  const saveDataBySessionStorage = () => {
    let enterpriseName = form.getFieldValue('enterpriseName')
    let phone = form.getFieldValue('phone')

    let formDataByObj = {
      ...formData,
      enterpriseName:enterpriseName,
      phone:phone,
      agree:agree,
    }
    sessionStorage.setItem('formDataByObj',JSON.stringify(formDataByObj))
  }


  return (
    <div className={styles.informationBox}>
      <Spin spinning={!!loading}>
      {/* 填写信息 */}
      <div className={styles.informationBox_title}>填写信息</div>
      <Form
        form={form}
        onFinish={onFinishByPC}
      >
        <div className={styles.informationBox_from}>

          <div className={styles.informationBox_from_item}>
            <div className={styles.informationBox_from_item_lable}>订购服务：</div>
            <div className={styles.informationBox_from_item_field}>{vipType == 1 ? '个人版' : '企业版'}</div>
          </div>

          <div className={styles.informationBox_from_item}>
            <div className={styles.informationBox_from_item_lable}>企业全称：</div>
            <div className={styles.informationBox_from_item_field}>
              <div className={styles.informationBox_from_item_field_input}>
                {!formData.isEditByEnterpriseName ? (
                    // 编辑企业名
                    <div className={styles.informationBox_from_item_field_Box}>
                      <div className={styles.informationBox_from_item_field_value}>
                        {form.getFieldValue('enterpriseName')}
                      </div>
                      <i onClick={()=>{setFormData({
                        ...formData,
                        isEditByEnterpriseName:true,
                      })}}
                         className={styles.editIcon}
                      />
                    </div>
                  )
                  : (<Form.Item name="enterpriseName"
                                rules={[{
                                  required: true,
                                  message: '请输入企业全称',
                                }, { max: 30, message: '企业全称最多30个字符' }]}>
                      <Input autoComplete="off" placeholder={'请输入企业全称'}></Input>
                    </Form.Item>
                  )
                }
              </div>
            </div>
          </div>

          <div className={styles.informationBox_from_item}>
            <div className={styles.informationBox_from_item_lable}>联系电话：</div>
            <div className={styles.informationBox_from_item_field}>
              <div className={styles.informationBox_from_item_field_input}>
                {!formData.isEditByPhone ? (
                    // 编辑手机号
                    <div className={styles.informationBox_from_item_field_Box}>
                      <div className={styles.informationBox_from_item_field_value}>{form.getFieldValue('phone')}</div>
                      <i
                        onClick={()=>{setFormData({
                          ...formData,
                          isEditByPhone:true,
                        })}}
                        className={styles.editIcon}/>
                    </div>
                  ) :
                  (<Form.Item name="phone" rules={[
                    { required: true,  message: '请输入联系电话', },
                    { pattern: /^1[23456789]\d{9}$/, message: '请输入正确的联系电话' }
                  ]}>
                    <Input autoComplete="off" placeholder={'请输入联系电话'}></Input>
                  </Form.Item>)
                }
              </div>
            </div>
          </div>

          <div style={{marginTop:'20px'}} className={styles.informationBox_from_item}>
            <div className={styles.informationBox_from_item_lable}>购买方案：</div>
            <div className={styles.informationBox_from_item_field}>
              <Form.Item name="scheme">
                <div className={styles.informationBox_from_item_field_SingleOption_Wrap}>
                  {orderPlanData.map((option,index) => (
                    <div key={index} className={styles.informationBox_from_item_Warp}>
                      {option.remark &&
                        <div className={styles.informationBox_from_item_remarks}>
                          {option.remark}
                        </div>
                      }
                      <div
                        key={option.planId}
                        className={classNames({
                          [styles.informationBox_from_item_field_option]: true,
                          [styles.informationBox_from_item_field_option_active]: form.getFieldValue('scheme') == option.planId,
                        })}
                        onClick={() => {
                          form.setFieldsValue({ scheme: option.planId })
                          setFormData({ ...formData, scheme: option.planId },()=>{});
                        }}
                      >
                        <div className={styles.informationBox_from_item_field_option_text}>
                          <div className={styles.informationBox_from_item_field_option_text_num}>¥{option.planPrice}</div>
                          <div className={styles.informationBox_from_item_field_option_text_unit}>{option.planUnit}</div>
                          <div className={styles.informationBox_from_item_field_option_text_unit}>{option.planNum == 3 && "(3年起租)"}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </Form.Item>
            </div>
          </div>

          <div className={styles.informationBox_from_item}>
            <div className={styles.informationBox_from_item_lable}>订单金额：</div>
            <div className={styles.informationBox_from_item_field}>
              <div className={styles.informationBox_from_item_field_price}>
                <div className={styles.informationBox_from_item_field_price_num}>{
                  getSelectPlanPrice(form.getFieldValue('scheme'))
                }</div>
                <div className={styles.informationBox_from_item_field_price_unit}>元</div>
              </div>
            </div>
          </div>
          <div style={{height:'30px'}}></div>
          <div className={styles.isAgree_box}>
            {vipType == 2 && (
              <Form.Item name="isAgree">
                <div className={styles.isAgree_box_checkBox_wrap}>
                  <Checkbox checked={!!agree} onChange={(e)=>{
                    const { target:{ checked } } = e
                    setAgree(checked)
                  }}>
                    <div>
                      <div>同意</div>
                    </div>
                  </Checkbox>
                  <div
                    onClick={()=>{
                      saveDataBySessionStorage()
                      history.push('/User/agreement/businessAgreement');
                    }}
                    className={styles.isAgree_box_checkBox_agreement}>《企业版服务协议》</div>
                </div>
              </Form.Item>
            )}
          </div>
          <div className={styles.informationBox_from_submit_Btn}>
            <Button onClick={onSubmit} className={styles.informationBox_submit}>确认并提交</Button>
          </div>
        </div>
      </Form>
      </Spin>
    </div>
  )
}

const Payment: React.FC = (props: any) => {
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>();
  const [pageType, setPageType] = useState(null);                 // 1 PC端  2移动端
  const [contentOrderPlan, setContentOrderPlan] = useState(null)  // 数据订单计划
  const [selectPlanId, setSelectPlanId] = useState(null)          // 选中的计划id
  const [agree, setAgree] = useState(null)                        // 是否同意协议
  const [form] = Form.useForm();
  const [contentByConfirmOrder, setContentByConfirmOrder] = useState({}); // 获取确认订单的数据详情


  // ① 判定当前页面视口是否小于750 如果小于750则为移动端
  let updateType = () => {
    // let clientWidth = document.documentElement.clientWidth;
    let env = getOperatingEnv() // 获取当前操作环境 1:微信小程序中使用 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用
    let type = env == 4 ? 1 : 2;
    setPageType(type);
  };
  updateType = debounce(updateType, 100);
  window.addEventListener('resize', updateType);
  // 进入页面判定是否存在token
  useEffect(() => {
    // ① 判定当前页面视口是否小于750 如果小于750则为移动端
    updateType();
    // ② 初始化数据
    initializeData();
  }, []);

  // 初始化数据
  const initializeData = async (values)=>{
    // 首先判定环境 如果当前环境是小程序端则从url中获取token和openid
    // 获取当前操作环境 1:微信浏览器 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用
    const env = getOperatingEnv()
    let access_token = localStorage.getItem('access_token');
    // 当前跳转环境非小程序端并且无登录token则记录当前页面跳转到登录-已在request中处理

    if(env == 1){
      // 当前环境是小程序端
      // 获取地址栏上的hash值
      const hash = window.location.hash;
      if (hash) {
        const encodedMessage = hash.slice(1);
        const decodedMessage = JSON.parse(decodeURIComponent(encodedMessage));
        const {
          access_token: access_tokenByMini,
          vxOpenIdCipherText: vxOpenIdCipherTextByMini,
        } = decodedMessage || {}
        // 保存从小程序来的token值和vxOpenIdCipherText
        localStorage.setItem('access_token', access_tokenByMini);
        localStorage.setItem('vxOpenIdCipherText', vxOpenIdCipherTextByMini);
      }
    }else {
      access_token = localStorage.getItem('access_token');
    }

    let userInfo = {};
    if (env == 1) {
      // 当前环境是小程序内嵌页面,获取微信小程序传入的 vxOpenIdCipherText和access_token 并保存在本地
      const vxOpenIdCipherText = localStorage.getItem('vxOpenIdCipherText')
      setLoading(true)
      userInfo = await getAccountInfo({
        access_token: access_token,
        vxOpenIdCipherText:vxOpenIdCipherText,
      });
      setLoading(false)
    }else {
      // 如果当前环境是非微信小程序端
      setLoading(true)
      userInfo = await getMsgCodeUserInfo({token: access_token});
      setLoading(false)
    }

    setLoading(true)
    // 获取到userInfo后
    if (userInfo && userInfo.code == 200) {
      // 保存用户信息
      localStorage.setItem('userInfo', JSON.stringify({
        ...userInfo.content,
        id: userInfo?.content?.friUserId
      }));
    }else if(userInfo && userInfo.status == 401){
      // 获取用户信息失败
      // message.warning('');
      return
    }
    if (!localStorage.getItem('userInfo')) {
      message.error('获取用户信息失败!!');
      return
    }
    setLoading(false)

    const userInfoData = JSON.parse(localStorage.getItem('userInfo'));
    const { friUserId:id } = userInfoData || {} // 获取用户id
    const { location } = props || {}
    const { query } = location || {}
    let { vipType } = query || {}     // 获取地址栏参数 vipType
    vipType = vipType == 2 ? 2 : 1    // vipType 1:个人版,2:企业版



    // 获取订单方案
    setLoading(true)
    let orderPlan =  await getOrderPlan({
      wxUserId: id,       // 用户id
      memberType:vipType, // 会员类型,1个人版 2企业版
    })
    setLoading(false)

    // 获取已经提交为结算的订单详情
    const orderId =  sessionStorage.getItem('orderId');
    if(!!orderId) {
      let dataByConfirmOrder = await confirmOrder({orderId: orderId})
      if(dataByConfirmOrder.code === 200 && dataByConfirmOrder.content) {
        // 获取订单数据详情相关信息
        setContentByConfirmOrder(dataByConfirmOrder.content);
      }
    }

    // {"code":200,"content":{
    // "orderPlan":[{"planId":1,"planServiceName":"个人版1季度",
    // "planName":"1季度","planNum":1,
    // "planPrice":0.01,"planUnit":"元/季",
    // "remark":null,"descs":null},
    // {"planId":2,"planServiceName":"个人版1年",
    // "planName":"1年","planNum":1,
    // "planPrice":2000.00,"planUnit":"元/年",
    // "remark":null,"descs":null}],
    // "phone":"13811636625",
    // "serviceName":"个人版"},
    // "msg":null,"enMsg":null}
    if (orderPlan && orderPlan.code === 200) {
      // 设置获取的订单方案
      setContentOrderPlan(orderPlan.content)

      let formDataByObj = sessionStorage.getItem('formDataByObj');
      if(!!formDataByObj) {
        formDataByObj = JSON.parse(formDataByObj)
        let {
          orderOrganizationName: orderOrganizationNameByObj,
          orderOrganizationTel: orderOrganizationTelByObj,
          selectPlanId: selectPlanIdByObj,     // 选中的计划id
          agree: agreeByObj                    // 是否同意协议
        } = formDataByObj || {}

        form.setFieldsValue({
          orderOrganizationName:orderOrganizationNameByObj,
          orderOrganizationTel:orderOrganizationTelByObj,
        })
        setSelectPlanId(selectPlanIdByObj ? selectPlanIdByObj : orderPlan.content?.orderPlan[0]?.planId)
        setAgree(agreeByObj)
        let keysByformDataByObj = Object.keys(formDataByObj).filter((item)=>{return !!formDataByObj[item]})
        form.validateFields(keysByformDataByObj)  // 校验表单
      }else {
        // 设置默认选中的方案id
        setSelectPlanId(orderPlan.content?.orderPlan[0]?.planId)
      }
    }else {
      message.error(orderPlan.msg || '获取订单方案失败');
    }

    sessionStorage.removeItem('formDataByObj')
  }

  // 提交订单判定是否同意协议,
  const onSubmit = debounce(()=>{
    // 获取地址栏参数 vipType
    const { location } = props || {}
    const { query } = location || {}
    let { vipType } = query || {}
    vipType = vipType == 2 ? 2 : 1
    // vipType 1:个人版,2:企业版

    if (!selectPlanId) {
      message.error('请选择计划');
      return
    }
    if (vipType == 2 && !agree) {
      message.warning('请勾选同意《企业版服务协议》');
      return
    }
    form && form.submit();
  },500)

  // 如果是移动端点击创建订单表单提交
  const onFinishByMobile = debounce(async (values) => {
    const userInfoData = JSON.parse(localStorage.getItem('userInfo'));
    const { friUserId:id } = userInfoData || {}  // 获取用户id
    const {
      orderOrganizationName,
      orderOrganizationTel
    } = values || {}
    // 请求接口创建订单
    let params = {
      orderOrganizationName,
      orderOrganizationTel,
      wxUserId:id,
      planId:selectPlanId,
    }
    setLoading(true)
    let DataBycreateOrder = await createOrder(params)
    setLoading(false)
    // 返回接口格式 {"code":200,"content":25,"msg":null,"enMsg":null}
    if (DataBycreateOrder && DataBycreateOrder.code === 200 && DataBycreateOrder.content) {
      // 创建订单成功获取订单号
      message.success('提交订单成功');
      // 获取当前操作环境 1:微信浏览器 2:当前在微信浏览器中使用  3:移动端浏览器  4:PC浏览器使用
      const env = getOperatingEnv()
      if(env == 2) {
        // 当前操作环境是微信浏览器,并且本地不存在公众号的openId的情况
        // 先前往公众号静默授权
        // 当前在微信浏览器中使用
        // 授权后转发回此页面会携带code=081mf30w36eow03Is32w3VtcAZ3mf30C&state=STATE%23wechat_redirect
        // Step2订单确认页面使用地址栏携带的code换取用户的openid
        const openIdByWx = localStorage.getItem('openIdByWx')
        // 当前存在微信公众号OpenId 则直接使用不重复调用接口
        if(!!openIdByWx) {
          // 直接跳转步骤2订单确认页面
          history.push(`/Payment/Step2/${DataBycreateOrder.content}?random=${Math.random()}`);
          return
        }
        // 不存在微信公众号的openID则需要跳转到公众号微信授权页面
        // 授权成功后会携带code重定向到步骤2订单确认页面
        let paramsByAuthorization = {
          appid: WxAppIdByPublicAccount,
          redirect_uri: `${window.location.origin}/Payment/Step2/${DataBycreateOrder.content}?${stringify({ random:Math.random() })}`,
          response_type: 'code',  // 写死
          scope: 'snsapi_base',
          state: 'STATE#wechat_redirect',
        }
        // 跳转到公众号微信授权页面
         window.location.href = `https://www.friday.tech/getWXcodeInfo.html?${stringify(paramsByAuthorization)}`
        // window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?${stringify(paramsByAuthorization)}#wechat_redirect`
      }else {
        // 除微信浏览器环境需要公众号授权 - 其余环境直接跳转到下一步
        history.push(`/Payment/Step2/${DataBycreateOrder.content}?random=${Math.random()}`);
      }
    }else {
      message.error(DataBycreateOrder.msg ? DataBycreateOrder.msg : '订单创建失败');
    }
  },500)

  const getSelectPlanPrice = (selectPlanId)=>{
    const {
      orderPlan,    // 订单计划
    } = contentOrderPlan || {}
    let orderPlanData = Array.isArray(orderPlan) ? orderPlan : []
    let selectByObj = orderPlanData && orderPlanData.find((item)=>item.planId == selectPlanId)
   //  selectByObj.l
    const {
      descs,            //: null
      planId,           //: 4
      planName,         //: "3年"
      planNum,          //: 3
      planPrice,        //: 18000
      planServiceName,  //: "企业版3年"
      planUnit,         //: "元/年"
      remark,           //: "送价值6万智能设备三年使用权"
    } = selectByObj || {}
    return !!(planPrice * planNum) ? planPrice * planNum : 0
  }

  const {
    orderPlan,    // 订单计划
    phone,        // 电话
    serviceName,  // 订购服务名称
  } = contentOrderPlan || {}
  let orderPlanData = Array.isArray(orderPlan) ? orderPlan : []
  //
  const OperatingEnv = getOperatingEnv();

  // let {match: { params: { orderId } }} = props
  // console.log('propsprops123 :: ',props);
  // 获取地址栏参数 vipType
  const { location } = props || {}
  const { query } = location || {}
  let { vipType } = query || {}
  // vipType 1:个人版,2:企业版
  vipType = vipType == 2 ? 2 : 1


  // 跳转到企业版服务协议前保存页面的临时数据
  const saveDataByH5SessionStorage = () => {
    let orderOrganizationName = form.getFieldValue('orderOrganizationName')
    let orderOrganizationTel = form.getFieldValue('orderOrganizationTel')

    let formDataByObj = {
      orderOrganizationName:orderOrganizationName,
      orderOrganizationTel:orderOrganizationTel,
      selectPlanId:selectPlanId,  // 选中的计划id
      agree:agree                 // 是否同意协议
    }
    sessionStorage.setItem('formDataByObj',JSON.stringify(formDataByObj))
  }


  return (
    <div>
      {/* ------------PC端布局------------ */}
      {pageType == 1 &&
        <div className={styles.page_warp}>
          <Spin spinning={!!loading}>
          <div className={styles.page_content}>
            {/* ---面包屑--- */}
            <BreadcrumbByPayment items={['FRIDAY解决方案', vipType == 2? '企业版' : '个人版', '信息填写并提交']}></BreadcrumbByPayment>
            {/* ---title导航条--- */}
            <Navigation activeStep={1} />  {/* activeStep: 1表示当前处于第1个导航项, 1:信息填写并提交,2:支付方式,3:支付结果 */}
            {/* ---填写信息--- */}
            <div style={{marginTop:'30px',marginBottom:'20px'}}>
              <InformationFrom
                vipType={vipType}
                orderPlanData={orderPlanData}
                contentByConfirmOrder={contentByConfirmOrder}
              />
            </div>
          </div>
          </Spin>
        </div>
      }

      {/* ------------移动端布局------------ */}
      <div>
        {pageType == 2 &&  // 当前
          <div className={classNames(styles.Mobile_Wrap, {
            [styles.in_app_Mobile_Wrap]: OperatingEnv == 5,
          })}>
            <Spin spinning={!!loading}>
            {OperatingEnv != 1 &&  // 1 小程序端
              <div className={styles.Mobile_title_statusbar}></div>
            }

            {OperatingEnv != 1 && OperatingEnv != 2 && OperatingEnv != 5 &&  // 1 小程序端、2 微信浏览器端、5 app中 无需展示title
              <div className={styles.Mobile_title_Wrap}>
                <div className={styles.Mobile_title}>产品服务</div>
              </div>
            }

            {/* app环境中 */}
            {
              OperatingEnv == 5 &&
              <NavBar title="产品服务" style={{background: 'linear-gradient(180deg, #EDE7FF 0%, #eee9ff 100%)'}}/>
            }

            {OperatingEnv == 1 &&
              <div style={{height:'22px'}}>产品服务</div>
            }

            <div className={styles.Mobile_tab}>
              <div className={classNames({
                [styles.Mobile_tab_item]:true,
                [styles.Mobile_tab_item_active]:vipType == 1
              })}>
                <div>个人版</div>
                <div className={styles.Mobile_tab_item_active_line}></div>
              </div>
              <div className={classNames({
                [styles.Mobile_tab_item]:true,
                [styles.Mobile_tab_item_active]:vipType == 2
              })}>
                <div>企业版</div>
                <div className={styles.Mobile_tab_item_active_line}></div>
              </div>
            </div>
            <Form
              form={form}
              onFinish={onFinishByMobile}
            >
              <div className={styles.Mobile_box}>
                <div className={styles.Mobile_FormWrap}>
                  <div className={styles.Mobile_FormItem_title}>选择产品服务</div>
                  <div className={styles.Mobile_FormItem_options}>

                    {orderPlanData && orderPlanData.map((item,index)=>{
                      const {
                        planId,           // 计划id
                        planServiceName,  // 计划服务名称
                        planName,         // 计划名称
                        planNum,          // 计划数量
                        planPrice,        // 计划价格
                        planUnit,          // 计划单位
                        remark,           // 计划备注
                      } = item || {}
                      {/* 1季度 */}
                      return (
                        <div
                          key={index}
                          className={classNames(
                          {
                              [styles.Mobile_FormItem_option]:true,
                              [styles.Mobile_FormItem_option_active]:planId == selectPlanId,
                            }
                          )}
                          onClick={()=>{ setSelectPlanId(planId) }}
                        >
                          <div>
                            <div className={styles.Mobile_Title_box}>
                              <div className={styles.Mobile_Title_box_icon}>
                                {planName}
                              </div>
                            </div>
                          </div>
                          <div className={styles.Mobile_Title_box_Content}>
                            <div className={styles.Mobile_Title_box_text}>
                              <div className={styles.Mobile_Option_unit}>¥</div>
                              <div className={styles.Mobile_Option_num}>{planPrice}</div>
                              <div className={styles.Mobile_Option_unit_time}>{planUnit}</div>
                            </div>
                            { remark && <div className={styles.Mobile_Option_text_desc}>{remark}</div> }
                          </div>
                        </div>
                      )
                    })}
                  </div>

                  <div className={styles.Mobile_FormItem_title}>填写信息</div>
                  <div>
                    <div className={styles.Mobile_FormItem_Item}>
                      <div className={styles.Mobile_FormItem_Item_lable}>企业名称</div>
                      <div className={styles.Mobile_FormItem_Item_value}>
                        <Form.Item name="orderOrganizationName"
                                   rules={[
                                     { required: true, message: '请输入企业名称' },
                                     { max: 30, message: '企业名称最多30个字符' }
                                   ]}>
                          <Input autoComplete="off" placeholder="请输入企业名称"/>
                        </Form.Item>
                      </div>
                    </div>
                    <div className={styles.Mobile_FormItem_Item}>
                      <div className={styles.Mobile_FormItem_Item_lable}>联系电话</div>
                      <div className={styles.Mobile_FormItem_Item_value}>
                        <Form.Item name="orderOrganizationTel" rules={[
                          { required: true, message: '请输入联系电话' },
                          { pattern: /^1[23456789]\d{9}$/, message: '请输入正确的联系电话' }
                        ]}>
                          <Input autoComplete="off" placeholder="请输入联系电话"/>
                        </Form.Item>
                      </div>
                    </div>
                  </div>

                  <div>
                    <div className={styles.Mobile_Item_amount}>
                      <div className={styles.Mobile_Item_Title}>订单金额</div>
                      <div className={styles.Mobile_Item_amount_num_box}>
                        <span className={styles.Mobile_Item_amount_unit}>¥</span>
                        <span className={styles.Mobile_Item_amount_num}>{getSelectPlanPrice(selectPlanId)}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className={styles.Mobile_submit_warp}>
                  {vipType == 2 && // 企业版
                    <div className={styles.Mobile_submit_agree}>
                      <Checkbox checked={agree} onChange={(e)=>{
                        const { target:{ checked } } = e
                        setAgree(checked)
                      }}>同意</Checkbox>
                      <div
                        onClick={()=>{
                          saveDataByH5SessionStorage()
                          history.push('/User/agreement/businessAgreement')
                        }}
                        className={styles.Mobile_submit_agree_agreement}>《企业版服务协议》</div>
                    </div>
                  }
                  <div className={styles.Mobile_submit_btn_wrap}>
                    <div onClick={onSubmit} className={styles.Mobile_submit_btn}>提交</div>
                  </div>
                </div>
              </div>
            </Form>
            </Spin>
          </div>
        }
      </div>
    </div>
  );
};

export default connect(({ login, loading }: any) => ({
  login, loading
}))(Payment)
