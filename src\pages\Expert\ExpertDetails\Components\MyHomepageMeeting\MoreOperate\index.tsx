import React, { useState } from 'react';
import styles from './index.less';
import { history, connect } from 'umi';
import { ActionSheet, Toast } from 'antd-mobile';

import CommonConfirmModal from '@/components/CommonConfirmModal';

const Index: React.FC = (props: any) => {
  let { friUserId } = JSON.parse(localStorage.getItem('userInfo') || '{}');
  const {visible, close, meetingItem, dispatch } = props || {};
  const [offShelfVisible, setOffShelfVisible] = useState(false); // 下架二次提示弹框
  const [deleteVisible, setDeleteVisible] = useState(false); // 删除二次提示弹框
  const [deleteFromListVisible, setDeleteFromListVisible] = useState(false); // 从列表删除二次提示弹框


  // 更多操作
  const actions = (meetingItem?.isDisable!=1||meetingItem?.status==1||meetingItem?.status==3)?[
    { text: '编辑', key: 'edit', onClick: () => editClickFn()},
    { text: '下架', key: 'offShelf'},
    { text: '删除', key: 'delete'},
    { text: '取消', key: 'cancel', onClick: () => cancelClickFn() },
  ]:[
    { text: '删除', key: 'delete'},
    { text: '取消', key: 'cancel', onClick: () => cancelClickFn() },
  ]

  // 非本人主持的取消以及从列表中删除
  const isDisableShare = [
    { text: '从列表中删除', key: 'deleteFromList' },
    { text: '取消', key: 'cancel', onClick: () => cancelClickFn() },
  ]


  // 编辑会议信息
  const editClickFn = () => {
    props.close()  // 关闭弹窗
    // 跳转会议编辑页
    history.push({
      pathname: `/CreateSpace/Meet`,
      query: {
        id: meetingItem && meetingItem.id,
      }
    })
  }

  // 下架会议确定按钮
  const offShelfOkFn = () => {
    Toast.show({icon: 'loading', maskClickable: false})
    dispatch({
      type: 'userInfoStore/editSpaceInfo',
      payload: {
        id: meetingItem && meetingItem.id, // 会议id
        updateUserId: friUserId, // 操作人用户ID
        isDisable: 1, // 1是下架，下架时必传
      }
    }).then(res => {
      const { code } = res || {};
      if(res && code == 200) {
        Toast.show({content: '下架成功'})
        setOffShelfVisible(false);
        props.close && props.close() // 关闭弹框
        props.refreshFn && props.refreshFn() // 刷新页面
      } else {
        return Toast.show({content: res.msg})
      }
    }).catch(err => {
      console.log(err)
    })
  }

  // 下架取消按钮
  const offShelfCancelFn = () => {
    setOffShelfVisible(false)
  }

  // 删除会议确定按钮
  const deleteOkFn = () => {
    Toast.show({icon: 'loading', maskClickable: false})
    dispatch({
      type: 'userInfoStore/editSpaceInfo',
      payload: {
        id: meetingItem && meetingItem.id, // 会议id
        updateUserId: friUserId, // 操作人用户ID
        isDel: 1, // 1是删除，删除时必传
      }
    }).then(res => {
      const { code } = res || {};
      if(res && code == 200) {
        Toast.show({content: '删除成功'})
        setDeleteVisible(false);
        props.close && props.close() // 关闭弹框
        props.refreshFn && props.refreshFn() // 刷新页面
      } else {
        return Toast.show({content: res.msg})
      }
    }).catch(err => {
      console.log(err)
    })
  }

  // 删除取消按钮
  const deleteCancelFn = () => {
    setDeleteVisible(false)
  }

  // 从列表中删除
  const deleteFromListFn = () => {
    Toast.show({icon: 'loading', maskClickable: false})
    dispatch({
      type: 'userInfoStore/deleteSpaceFromList',
      payload: {
        spaceId: meetingItem && meetingItem.id, // 会议id
      }
    }).then(res => {
      const { code } = res || {};
      if(res && code == 200) {
        Toast.show({content: '从列表删除成功'})
        setDeleteFromListVisible(false);
        props.close && props.close() // 关闭弹框
        props.refreshFn && props.refreshFn() // 刷新页面
      } else {
        return Toast.show({content: res.msg})
      }
    }).catch(err => {
      console.log(err)
    })
  }

  // 从列表中删除取消按钮
  const deleteFromListCancelFn = () => {
    setDeleteFromListVisible(false)
  }

  // 取消
  const cancelClickFn = () => {
    props.close()
  }

  return <>
    <ActionSheet
      className={styles.wrap}
      visible={visible}
      actions={(meetingItem?.hostUserId==friUserId)?actions:isDisableShare}
      onAction={action => {
        if (action.key === 'offShelf') {
          setOffShelfVisible(true)
          return
        }
        if (action.key === 'delete') {
          setDeleteVisible(true)
          return
        }
        if (action.key === 'deleteFromList') {
          setDeleteFromListVisible(true)
          return
        }
      }}
      onClose={() => close()}
    />
    <CommonConfirmModal isVisible={offShelfVisible} title={'确定下架'} text={`确定要下架这个会议吗?`} onSubmit={offShelfOkFn} onCancel={offShelfCancelFn} />
    <CommonConfirmModal isVisible={deleteVisible} title={'确定删除'} text={`确定要删除这个会议吗?`} onSubmit={deleteOkFn} onCancel={deleteCancelFn} />
    <CommonConfirmModal isVisible={deleteFromListVisible} title={'确定删除'} text={`确定要从列表中删除这个会议吗?`} onSubmit={deleteFromListFn} onCancel={deleteFromListCancelFn} />

  </>
}
export default connect(({ square, userInfoStore, loading }: any) => ({ square, userInfoStore, loading }))(Index)
