.Mobile_Wrap {
  width: 100%;
  min-width: 285px;
  // height: 100vh;
  background: #F5F6F8;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  // padding-left: 12px;
  // padding-right: 12px;


  .Mobile_title_Wrap {
    width: 100%;
    height: 44px;
    margin-bottom: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #fff;

    .Mobile_title {
      font-size: 17px;
      font-weight: 500;
      color: #000000;
    }
  }
}


.wrap {
  width: 100%;
  height: calc(100vh - 85px);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header{
  width: 100%;
  margin: 16px 0;
  background: #fff;
  padding: 17px 24px;
  height: 62px;
  display: flex;
  border-radius: 8px;
  align-items: center;
  .header_title{
    font-size: 20px;
    font-weight: 600;
    color: #000000;
    .header_title_icon{
      width: 24px;
      height: 24px;
      margin-right: 10px;
      cursor: pointer;
      vertical-align: sub;
    }
  }
}

.wrap_box {
  width: 816px;
}

.tab_title_Warp {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;

  .StudyBonusBox {
    width: 100%;
    height: 102px;
    background: linear-gradient( 135deg, #FFF7EA 0%, #FFEEDC 100%), #FFFFFF;
    border-radius: 0px 0px 0px 0px;
    // margin-bottom: 30px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-left: 40px;
    padding-right: 40px;
    position: relative;

    .StudyBonusBg {
      width: 79px;
      height: 65px;
      background: url('~@/assets/GlobalImg/StudyBonusBg.png');
      background-size: 100% 100%;
      position: absolute;
      right: 16px;
      bottom: 0px;
    }
  }

  .StudyBonusBox_num {
    font-weight: 500;
    font-size: 26px;
    color: #000000;
    line-height: 26px;
    margin-bottom: 8px;
  }

  .StudyBonusBox_date {
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    line-height: 12px;
  }

}

.title_name {
  font-weight: 500;
  font-size: 17px;
  color: #000000;
  line-height: 20px;
  font-style: normal;
  text-transform: none;
  margin-left: 40px;
  padding-top: 20px;
}

.content_box {
  padding-left: 30px;
  padding-right: 30px;
  padding-top: 12px;
  width: 100%;
  background: #FFFFFF;

  .item_content {
    width: 100%;
    min-height: 74px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    padding: 12px 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-top: 10px;
  }

  .item_title_box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
  }

  .item_title_box_felx {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .item_title {
    font-size: 14px;
    color: #000000;
    line-height: 16px;
    font-weight: 600;

    overflow: hidden;
    display: -webkit-box;
    // -webkit-line-clamp: 2;
    // -webkit-box-orient: vertical;
    // text-overflow: ellipsis;
    white-space: normal;
    max-width: 500px;
  }

  .item_title_num {
    font-size: 14px;
    color: #000000;
    line-height: 16px;
    font-weight: 600;
  }

  .item_time {
    font-weight: 400;
    font-size: 11px;
    color: #999999;
    line-height: 11px;
  }

  .btn_box {
    font-weight: 400;
    font-size: 14px;
    color: #0095FF;
    line-height: 14px;
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
  }

  .btn_icon {
    width: 12px;
    height: 12px;
    background: url('~@/assets/GlobalImg/BalanceStudyFund_DetailBox_icon.png');
    background-size: 100% 100%;
    display: inline-block;
  }
}

.Content_box {
  background: #FFFFFF;
  height: calc(100vh - 382px);
  overflow: auto;
}

.line {
  width: 100%;
  height: 1px;
  border-radius: 0px 0px 0px 0px;
  background: #E1E4E7;
}

.loadingByInfiniteScroll {
  width: 100%;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #FFFFFF;
}

.loadingByInfiniteScrollContent {
  display: flex;
}





