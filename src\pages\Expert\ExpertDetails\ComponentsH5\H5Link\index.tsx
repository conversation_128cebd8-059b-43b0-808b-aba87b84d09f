/**
 * @Description: H5-医生主页/个人中心-外链
 */
import React, { useEffect, useState } from 'react';
import { history, connect } from 'umi';
import { InfiniteScroll, Toast } from 'antd-mobile'

import ExternalLinkCard from '@/components/ExternalLinkCard';   // 外链卡片
import NoDataRender from '@/components/NoDataRender'            // 暂无数据

interface PropsType {
  isMyPages: boolean,        // 是否是自己的主页
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { query } = history.location
  const { id } = query
  const { dispatch, isMyPages } = props
  // 页面state
  const initialState = {
    dataList: [],                  // 数据list
    total: 0,                      // 总条数
    hasMore: false,                // 是否还有更多数据
  }
  // 筛选数据
  const initialFilterState = {
    page: 1,
    size: 30,
  }
  const [state, setState] = useState(initialState)
  const [filterState, setFilterState] = useState(initialFilterState)

  useEffect(() => {
    personImageTextList()
  }, [])

  // 个人中心获取用户图文列表信息
  const personImageTextList = async (page, size) => {
    const pageResult = page || filterState.page
    const sizeResult = size || filterState.size
    await dispatch({
      type: 'expertAdvice/personImageTextList',
      payload: {
        page: pageResult,
        size: sizeResult,
        imageType: 3,                            // 图文类型：1.文章 2.帖子 3.外链 4.空间
        status: [0, 1, 2],                       // 状态：1.审核通过 0.未审核 2.审核未通过 3.草稿
        expertsUserId: isMyPages ? null : id,    //	否 专家用户ID
      }
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200 && content) {
        const { pageNum, resultList, total } = content
        let newDataList = []
        if (pageResult == 1) {
          newDataList = resultList || [];
        } else {
          newDataList = state.dataList.concat(resultList || [])
        }
        setState({
          ...state,
          dataList: newDataList,                 // 数据list
          total: total,                          // 总条数
          hasMore: total > newDataList.length,   // 是否还有更多数据
        })

        if (!resultList || resultList.length == 0) {
          // 没数据时不更新page
        } else if (page && size) {
          // 这两个参数下架后调时有值，这时不更新page
        } else {
          setFilterState({
            ...filterState,
            page: pageNum + 1,     // 页码
          })
        }
      } else {
        Toast.show(msg || '数据加载失败')
        setState({
          ...state,
          hasMore: false,
        })
      }
    }).catch(err => {})
  }

  // 加载更多
  const loadMore = async ()=> {
    console.log('加载更多')
    await personImageTextList()
  }

  // 下架后刷新事件
  const refreshFn = () => {
    personImageTextList(1, state.dataList.length)
  }

  return (
    <>
      <div style={{paddingLeft: '16px', fontSize: '12px'}}>共 {state.total} 条内容</div>
      {
        state.dataList && state.dataList.length > 0 ?
          <div style={{background: '#F5F6F8'}}>
            {
              state.dataList.map((item, index) => {
                return (
                  <ExternalLinkCard
                    key={item.id}
                    isMyPages={isMyPages}
                    item={item}
                    refreshDataById={refreshFn}
                    isShowMoreOperate={item.status == 1} // 是否展示点点点更多操作
                  />
                )
              })
            }

            {
              (state.dataList && state.dataList.length > 0 ) &&
              <InfiniteScroll
                loadMore={loadMore}
                hasMore={state.hasMore}
                threshold={100}
              />
            }
          </div>
          :
          <NoDataRender style={{marginTop: 50, paddingBottom: 70}}/>
      }

    </>
  )
}

export default connect(({ loading }: any) => ({ loading }))(Index)
