.Setup_Warp {
  width: 100%;
  height: 100vh;
  background: #F5F6F8;
  position: relative;
  padding-top: 52px;

  .Setup_box {
    width: 100%;
    background: #FFF;
    overflow: hidden;
  }

  .Setup_box_Item_line {
    width: calc(100% - 16px);
    height: 1px;
    background: #E1E4E7;
    margin-left: 16px;
  }

  .Setup_box_Item {
    width: 100%;
    padding: 20px 16px;
    background: #FFF;
    display: flex;
    align-items: center;
    justify-content: space-between;
    user-select: none;

    .Setup_box_Item_title {
      font-size: 16px;
      font-weight: 400;
      line-height: 22px;
      color: #000000;
    }

    .Setup_box_Item_Icon {
      width: 16px;
      height: 16px;
      background: url('../../../assets/GlobalImg/right_arrow.png');
      background-size: 16px 16px;
    }
  }

  .Setup_box_Item:active {
    background: #f5f5f5;
  }

  .exit_login {
    width: 100%;
    height: auto;
    padding: 16px;
    position: fixed;
    bottom: 16px;

    .exit_login_btn {
      width: 100%;
      height: 40px;
      background: #FFFFFF;
      border-radius: 20px 20px 20px 20px;
      text-align: center;
      font-size: 16px;
      font-weight: 400;
      color: #FF5F57;
      line-height: 40px;
    }
  }
  .DownloadAppCardBody{
    width: 100%;
    position: absolute;
    top:0;
    z-index: 999;
  }
}

.title_box {
  display: flex;
  flex-wrap: nowrap;
  justify-content: center;
  padding: 20px 8px 0;
  margin-bottom: 43px;
  .title {
    font-size: 17px;
    color: #000;
    font-weight: 500;
    line-height: 24px;
    padding-top: 1px;
    margin-left: 8px;
  }
}
.message {
  margin-bottom: 32px;
  font-size: 14px;
  color: #666;
  text-align: center;
}
.btn_box {
  padding: 0 8px 4px;
  display: flex;
  flex-wrap: nowrap;
  & > div {
    flex: 1;
    text-align: center;
    height: 37px;
    line-height: 37px;
    font-size: 15px;
    border-radius: 20px;
  }
  .cancel {
    background: #EDF9FF;
    color: #0095FF;
    margin-right: 14px;
  }
  .ok {
    background: #0095FF;
    color: #fff;
  }
}
