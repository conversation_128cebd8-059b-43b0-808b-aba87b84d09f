/**
 * request 网络请求工具
 * 更详细的 api 文档: https://github.com/umijs/umi-request
 */
import { extend } from 'umi-request';
import { message } from 'antd';
import { history } from 'umi';
import { getOperatingEnv, openLoginInApp, getArrailUrl } from '@/utils/utils';
import { stringify } from 'qs';

const codeMessage = {
  200: '服务器成功返回请求的数据。',
  201: '新建或修改数据成功。',
  202: '一个请求已经进入后台排队（异步任务）。',
  204: '删除数据成功。',
  400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
  401: '登录超时！请重新登录',
  403: '用户得到授权，但是访问是被禁止的。',
  404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
  406: '请求的格式不可得。',
  410: '请求的资源被永久删除，且不会再得到的。',
  422: '当创建一个对象时，发生一个验证错误。',
  500: '服务器发生错误，请检查服务器。',
  502: '网关错误。',
  503: '服务不可用，服务器暂时过载或维护。',
  504: '网关超时。',
};

message.config({
  // maxCount: 1,
});
// userToken失效
export const userTokenInvalid = (pathname) => {
  // token失效 判定当前环境 小程序端跳转小程序用户页面,非小程序跳转到登录页面
  // 5表示friday app环境，不跳转登录页，不做任何处理
  if (getOperatingEnv() == '1') {
    wx && wx.miniProgram.redirectTo({ url: '/pages/Home/index' });
  } else if (getOperatingEnv() == '5') {
    // APP跳转登录
    openLoginInApp();
  } else {
    // 是否嵌套在5i5ya的iframe中
    const isInIframe = self != top;
    localStorage.clear();

    if (isInIframe) {
      const postData = {
        dataType: 'logout', // 登出
      };
      console.log('子级发送数据：', postData, getArrailUrl());
      window.parent.postMessage(postData, getArrailUrl());
    } else {
      if (pathname != '/User/login' && pathname != '/User/login/') {
        history.replace(
          `/User/login?${stringify({
            redirect: window.location.href,
            random: Math.random(),
            isShare: 1,
          })}`,
        );
      }
    }
  }
};

/**
 * 异常处理程序
 */
const errorHandler = (error) => {
  const { response } = error;

  if (response && response.status) {
    const errorText = codeMessage[response.status] || response.statusText;
    const { status, url } = response;
    const { pathname, query } = history.location;

    // 企业管理-微信扫码登陆流程: 中转页  未登录  去微信一键登录页面
    if (status == 401 && url.includes('/fri-uc/friBizManage/qrBind')) {
      if (getOperatingEnv() == '2') {
        history.push('/User/enterpriseQRcodeTip');
        return { status: 401, code: 401, msg: '请重新登录!' }
      }
    }
    // 除用户
    if (
      pathname != '/User/login' &&
      pathname != '/User/login/' &&
      pathname != '/Payment/Step1' &&
      pathname != '/UserInfo' &&
      !pathname.includes('/PlanetChatRoom/Live')
    ) {
      message.error(errorText);
    }

    if (status == 401) {
      sessionStorage.removeItem('orderId');
      if (pathname && pathname.indexOf('/PlanetChatRoom') != -1) {
        return { status: 401, code: 401, msg: '请求结果超时' };
      }
      // userToken失效的处理方案
      userTokenInvalid(pathname);
      return {
        status: 401,
        msg: '请求结果超时',
      };
    }
  } else if (!response) {
    return {
      status: 504,
      msg: '请求结果超时',
    };
  }
  return response;
};

// params地址拼接参数 null值过滤
const ParamsEmpty = (params) => {
  Object.keys(params).forEach((key) => {
    if (params[key] == null) {
      delete params[key];
    }
  });
  return params;
};

// 创建request请求
function createRequest(options) {
  return extend({
    errorHandler,
    // timeout: 3000,    // 延迟时长 3秒
    credentials: 'include', // 默认请求是否带上cookie
    ...options,
  });
}
const request = createRequest(); // request 登录后的请求

// 下载excel img方法

// 添加公共参数
const addCommonParams = (options) => {
  const env = getOperatingEnv();
  const UserInfo = JSON.parse(localStorage.getItem('userInfo')) || {};
  if (options.isNoNeedCommonParams) {
    return options;
  }
  if (options.params) {
    options.params = {
      // updateId: localStorage.getItem('user_id'),
      userId: UserInfo?.friUserId || 0,
      // createId: localStorage.getItem('user_id'),
      userName: UserInfo && UserInfo.name,
      // wxUserId: UserInfo && UserInfo.id,
      ...options.params,
    };
  }
  if (options.data) {
    options.data = {
      // updateId: localStorage.getItem('user_id'),
      userId: UserInfo?.friUserId || 0,
      // createId: localStorage.getItem('user_id'),
      userName: UserInfo && UserInfo.name,
      wxUserId: UserInfo && UserInfo.friUserId,
      ...options.data,
    };
  }

  if (options.headers) {
    // 5 表示在FRIDAY APP中
    // FRIDAY APP中本地存储值：access_token、client、phone、user_name、wxuserId、userInfo
    options.headers = {
      access_token: localStorage.getItem('access_token'),
      userId: UserInfo && UserInfo.friUserId,
      username:
        env == 5
          ? localStorage.getItem('user_name')
          : localStorage.getItem('vxOpenIdCipherText')
          ? localStorage.getItem('vxOpenIdCipherText')
          : UserInfo?.phone,
      // client: env == 5 ? localStorage.getItem('client') : 'WX',
      type: env == 1 ? '' : 1,
      ...options.headers,
    };
  }

  return options;
};

const requestDownload = createRequest({
  responseType: 'blob', // 返回结果
});

// request全局处理  地址拼接参数为null的处理
request.interceptors.request.use((url, options) => {
  options.params = ParamsEmpty(options.params);
  if (!!localStorage.getItem('access_token')) {
    options = addCommonParams(options);
  }
  return {
    url: url,
    options: { ...options },
  };
});

export { requestDownload };
export default request;
