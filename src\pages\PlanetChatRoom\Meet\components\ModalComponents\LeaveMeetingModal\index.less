.popup_container {
  :global {
    .adm-popup-body {
      border-radius: 16PX 16PX 0 0;
    }
  }
}

.container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: nowrap;
  flex-direction: column;
}

// 头部
.header_line {
  flex-shrink: 0;
  width: 100%;
  height: 28PX;
  display: flex;
  align-items: center;
  justify-content: center;
  .header_line_bar {
    width: 48PX;
    height: 4PX;
    background: #D0D4D7;
    border-radius: 4PX;
  }
}

// 内容-按钮
.content {
  width: 100%;
  padding-top: 16PX;
  .btn {
    padding: 16PX 0;
    font-size: 15PX;
    color: #000;
    font-weight: 500;
    line-height: 21PX;
    text-align: center;
    &:active {
      background: #eee;
    }
  }
}
