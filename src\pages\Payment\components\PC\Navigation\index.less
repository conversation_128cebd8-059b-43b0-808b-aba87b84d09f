// -------title导航条-------
.nav_title_warp {
  display: flex;
  justify-content: space-around;
  margin-top: 30px;
}

.nav_title {
  width: 604px;
  display: flex;
  justify-content: space-between;
  position: relative;

  .nav_item {
    text-align: center;
    width: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .nav_item_circle {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #3C89FD;
      border: 1px solid #3C89FD;
      z-index: 10;
      font-size: 16px;
      font-weight: bold;
    }
    .nav_item_circle_active {
      background-color: #3C89FD;
      color: #fff;
    }
    .nav_item_circle_complete {
      background: url('../../../../../assets/Payment/Payment_nav_item_circle_active.png');
      width: 40px;
      height: 40px;
      background-size: 40px 40px;
      border: none;
    }
    .nav_item_text {
      margin-top: 11px;
      font-size: 14px;
      font-weight: 400;
      color: #999999;
    }
    .nav_item_text_active {
      font-size: 14px;
      font-weight: 500;
      color: #333333;
    }
  }

  .nav_line {
    height: 1px;
    opacity: 1;
    background: #CCCCCC;
    width: calc(100% - 80px);
    position: absolute;
    left: 40px;
    top: 20px;
  }
}
// -------title导航条-------end
