import React from 'react';
import { Breadcrumb } from 'antd';
import styles from './index.less';

interface BreadcrumbProps {
  items: string[];
}

const MyBreadcrumb: React.FC<BreadcrumbProps> = ({ items }) => {
  return (
    <div className={styles.BreadcrumbWarp}>
      <Breadcrumb>
        {items.map((item, index) => (
          <Breadcrumb.Item key={index}>{item}</Breadcrumb.Item>
        ))}
      </Breadcrumb>
    </div>
  );
};

export default MyBreadcrumb;
