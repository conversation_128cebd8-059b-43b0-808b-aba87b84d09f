/**
 * @Description: 完整服务流程弹窗公共组件
 */
import React, { useState } from 'react'
import classNames from 'classnames'
import { Popup } from 'antd-mobile'
import { CloseOutlined } from '@ant-design/icons'
import styles from './CompleteProcessModal.less'

const tabs = [
  { code: 1, value: '图文指导' },
  { code: 2, value: '视频指导' },
]
const imageTextConsultation1 = [
  { code: 1, value: '选择指导方式', text: '提供图文指导和视频指导' },
  { code: 2, value: '描述病例和问题', text: '填写病例信息及对病例的疑惑等' },
  { code: 3, value: '支付指导费用', text: '支付相应的指导费用' },
]
const imageTextConsultation2 = [
  { code: 4, value: '病例资料被查看', text: '专家正在查看病例资料' },
  { code: 5, value: '问题被回复并对话', text: '针对病例问题与专家进行沟通' },
  { code: 6, value: '结束指导交易成功', text: '解决您对病例的疑惑并结束指导' },
]

const videoConsultation1 = [
  { code: 1, value: '选择指导方式', text: '提供图文指导和视频指导' },
  { code: 2, value: '描述病例和问题', text: '填写病例信息及对病例的疑惑等' },
  { code: 3, value: '提交指导单', text: '提交成功后，24小时内将有客服联系您' },
]
const videoConsultation2 = [
  { code: 4, value: '病例资料被查看', text: '专家正在查看病例资料' },
  { code: 5, value: '预约视频会议', text: '客服与您沟通确定视频会议时间' },
  { code: 6, value: '视频沟通', text: '专家与您在线面对面沟通解决问题' },
  { code: 7, value: '结束指导', text: '解决您对病例的疑惑并结束指导' },
  { code: 8, value: '支付指导费用', text: '指导结束后需要支付相应指导费用' },
  { code: 9, value: '交易成功', text: '交易成功' },
]

interface PropsType {
  visible: boolean,                    // true，false
  onCancel: any,                       // 关闭弹窗
}

const Index: React.FC<PropsType> = (props: any) => {
  const { visible } = props
  const [checkedTab, setCheckedTab] = useState(1)

  // 切换tab
  const tabOnChange = (value) => {
    setCheckedTab(value)
  }

  return (
    <Popup
      visible={visible}
      onMaskClick={props.onCancel}
      className={styles.popup_container}
      destroyOnClose
    >
      <div className={styles.header_line} onClick={props.onCancel}>
        <div className={styles.header_line_bar}></div>
      </div>
      <div className={styles.header_title}>
        完整服务流程
        <div className={styles.close_icon} onClick={props.onCancel}>
          <CloseOutlined/>
        </div>
      </div>

      <div className={styles.tab_box}>
        {
          tabs.map(item => (
            <div
              key={item.code}
              className={classNames(styles.tab_item, {[styles.active]: checkedTab == item.code})}
              onClick={() => tabOnChange(item.code)}
            >
              {item.value}
            </div>
          ))
        }
      </div>

      {
        checkedTab == 1 ?
          <>
            <div className={styles.process_box}>
              <div className={styles.process_name}>下单流程</div>
              {
                imageTextConsultation1.map(item => (
                  <div key={item.code} className={styles.process_item}>
                    <div className={styles.process_item_icon}></div>
                    <div className={styles.process_item_number}>第{item.code}步：</div>
                    <div className={styles.process_item_content}>
                      <div className={styles.content_title}>{item.value}</div>
                      <div className={styles.content_text}>{item.text}</div>
                    </div>
                  </div>
                ))
              }
            </div>
            <div className={classNames(styles.process_box, styles.process_box_last)}>
              <div className={styles.process_name}>服务流程</div>
              {
                imageTextConsultation2.map(item => (
                  <div key={item.code} className={styles.process_item}>
                    <div className={styles.process_item_icon}></div>
                    <div className={styles.process_item_number}>第{item.code}步：</div>
                    <div className={styles.process_item_content}>
                      <div className={styles.content_title}>{item.value}</div>
                      <div className={styles.content_text}>{item.text}</div>
                    </div>
                  </div>
                ))
              }
            </div>
          </>
          : checkedTab == 2 ?
          <>
            <div className={styles.process_box}>
              <div className={styles.process_name}>下单流程</div>
              {
                videoConsultation1.map(item => (
                  <div key={item.code} className={styles.process_item}>
                    <div className={styles.process_item_icon}></div>
                    <div className={styles.process_item_number}>第{item.code}步：</div>
                    <div className={styles.process_item_content}>
                      <div className={styles.content_title}>{item.value}</div>
                      <div className={styles.content_text}>{item.text}</div>
                    </div>
                  </div>
                ))
              }
            </div>
            <div className={classNames(styles.process_box, styles.process_box_last)}>
              <div className={styles.process_name}>服务流程</div>
              {
                videoConsultation2.map(item => (
                  <div key={item.code} className={styles.process_item}>
                    <div className={styles.process_item_icon}></div>
                    <div className={styles.process_item_number}>第{item.code}步：</div>
                    <div className={styles.process_item_content}>
                      <div className={styles.content_title}>{item.value}</div>
                      <div className={styles.content_text}>{item.text}</div>
                    </div>
                  </div>
                ))
              }
            </div>
          </>
          : null
      }
    </Popup>
  )
}

export default Index
