//处理月份是动态的 根据数据返回的生成月份

import React, { useState, useEffect, useRef } from 'react';
import classNames from 'classnames';
import styles from './index.less';

interface IMonth {
  year: string;
  month: string;
}

interface IProps {
  monthsList: IMonth[];
  pcOrMobileMode: string;
  getFilterParams: (type: string, params: any) => void;
}
const MonthTab: React.FC<IProps> = ({ monthsList, getFilterParams, pcOrMobileMode }) => {
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());  // 当前年份
  const [selectedMonth, setSelectedMonth] = useState<string>();  // 选中的年月
  const monthListRef = useRef<HTMLDivElement>(null);

  // 处理年份/ 月份   默认将当前月份滚动到最前面
  useEffect(() => {
    const year = new Date().getFullYear();
    setCurrentYear(year);
    const currentMonth = new Date().getMonth() + 1;
    
    // 在monthsList中找到当前月份对应的项
    if (monthsList && monthsList.length > 0) {
      const currentMonthItem = monthsList.find(item => 
        parseInt(item.month) === currentMonth && parseInt(item.year) === year
      );
      
      // 如果找到当前月份，滚动到该位置
      if (currentMonthItem && monthListRef.current) {
        const monthKey = `${currentMonthItem.month}/${currentMonthItem.year}`;
        const monthElements = monthListRef.current.querySelectorAll(`[data-month="${monthKey}"]`);
        
        if (monthElements.length > 0) {
          const targetElement = monthElements[0] as HTMLElement;
          const scrollOffset = targetElement.offsetLeft - 100; // 100px from the left
          monthListRef.current.scrollTo({
            left: scrollOffset,
            behavior: 'smooth'
          });
        }
      }
    }
  }, [monthsList]);

  // 滚动到选中的月份
  useEffect(() => {
    if (selectedMonth && monthListRef.current) {
      const selectedItem = monthListRef.current.querySelector(`[data-month="${selectedMonth}"]`) as HTMLElement;
      if (selectedItem) {
        const scrollContainer = monthListRef.current;
        const selectedItemOffset = selectedItem.offsetLeft;
        const scrollOffset = selectedItemOffset - 100; // 100px from the left
        scrollContainer.scrollTo({
          left: scrollOffset,
          behavior: 'smooth'
        });
      }
    }
  }, [selectedMonth]);

  // 选择月份  -- 更新和数据筛选
  const handleSelectedMonth = (month: string, year: string) => {
    setSelectedMonth(`${month}/${year}`)
    //5需要变成  05
    const newMonth = month.length == 2 ? month : `0${month}`
    getFilterParams('date', `${year}-${newMonth}`)
  }

  return (
    <div
      ref={monthListRef}
      className={classNames(styles.major_header, {
        [styles.major_mobile_header]: pcOrMobileMode === 'mobile',
        [styles.major_pc_header]: pcOrMobileMode === 'pc'
      })}>
      {monthsList.map(item => <div
        key={`${item.month}/${item.year}`}
        data-month={`${item.month}/${item.year}`}
        className={classNames(styles.month_item, {
          [styles.active]: selectedMonth === `${item.month}/${item.year}`,
        })}
        onClick={() => handleSelectedMonth(item.month, item.year)}
      > {`${item.month}月`}{currentYear !== parseInt(item.year) &&
        <span className={styles.small_year}>/{item.year}</span>
        }</div>)}
    </div>
  );
};

export default MonthTab;