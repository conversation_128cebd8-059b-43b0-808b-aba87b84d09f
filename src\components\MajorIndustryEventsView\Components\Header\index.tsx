//处理月份是动态的 根据数据返回的生成月份

import React, { useState, useEffect, useRef } from 'react';
import classNames from 'classnames';
import styles from './index.less';

interface IMonth {
  year: string;
  month: string;
}

interface IProps {
  monthsList: IMonth[];
  pcOrMobileMode: string;
  getFilterParams: (type: string, params: any) => void;
}
// 找到最适合的默认月份
const findBestDefaultMonth = (months: IMonth[], year: number, month: number) => {
  if (!months || months.length === 0) return null;

  // 如果只有一个月份，直接返回
  if (months.length === 1) {
    return months[0];
  }

  // 先查找当前月份
  const currentMonthItem = months.find(item =>
    parseInt(item.month) === month && parseInt(item.year) === year
  );

  if (currentMonthItem) {
    return currentMonthItem;
  }

  // 当前月份不存在，查找当前月份后面最近的月份
  const currentDate = new Date(year, month - 1); // month - 1 因为Date的月份从0开始

  let nearestFutureMonth = null;
  let nearestPastMonth = null;
  let minFutureDiff = Infinity;
  let minPastDiff = Infinity;

  months.forEach(item => {
    const itemDate = new Date(parseInt(item.year), parseInt(item.month) - 1);
    const timeDiff = itemDate.getTime() - currentDate.getTime();

    if (timeDiff > 0) {
      // 未来的月份
      if (timeDiff < minFutureDiff) {
        minFutureDiff = timeDiff;
        nearestFutureMonth = item;
      }
    } else {
      // 过去的月份
      const pastDiff = Math.abs(timeDiff);
      if (pastDiff < minPastDiff) {
        minPastDiff = pastDiff;
        nearestPastMonth = item;
      }
    }
  });

  // 优先返回未来最近的月份，如果没有则返回过去最近的月份
  return nearestFutureMonth || nearestPastMonth;
};

const MonthTab: React.FC<IProps> = ({ monthsList, getFilterParams, pcOrMobileMode }) => {
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());  // 当前年份
  const [selectedMonth, setSelectedMonth] = useState<string>();  // 选中的年月
  const monthListRef = useRef<HTMLDivElement>(null);

  // 处理年份/ 月份   默认选中最适合的月份并滚动到该位置
  useEffect(() => {
    const year = new Date().getFullYear();
    setCurrentYear(year);
    const currentMonth = new Date().getMonth() + 1;

    // 找到最适合的默认月份
    if (monthsList && monthsList.length > 0 && !selectedMonth) {
      const bestDefaultMonth = findBestDefaultMonth(monthsList, year, currentMonth);

      if (bestDefaultMonth) {
        // 设置选中的月份并触发数据筛选
        const monthKey = `${bestDefaultMonth.month}/${bestDefaultMonth.year}`;
        setSelectedMonth(monthKey);

        // 触发数据筛选
        const newMonth = bestDefaultMonth.month.length === 2 ? bestDefaultMonth.month : `0${bestDefaultMonth.month}`;
        getFilterParams('date', `${bestDefaultMonth.year}-${newMonth}`);

        // 滚动到该位置
        setTimeout(() => {
          if (monthListRef.current) {
            const monthElements = monthListRef.current.querySelectorAll(`[data-month="${monthKey}"]`);

            if (monthElements.length > 0) {
              const targetElement = monthElements[0] as HTMLElement;
              const scrollOffset = targetElement.offsetLeft - 100; // 100px from the left
              monthListRef.current.scrollTo({
                left: scrollOffset,
                behavior: 'smooth'
              });
            }
          }
        }, 100); // 延迟执行确保DOM已渲染
      }
    }
  }, [monthsList]);

  // 滚动到选中的月份
  useEffect(() => {
    if (selectedMonth && monthListRef.current) {
      const selectedItem = monthListRef.current.querySelector(`[data-month="${selectedMonth}"]`) as HTMLElement;
      if (selectedItem) {
        const scrollContainer = monthListRef.current;
        const selectedItemOffset = selectedItem.offsetLeft;
        const scrollOffset = selectedItemOffset - 100; // 100px from the left
        scrollContainer.scrollTo({
          left: scrollOffset,
          behavior: 'smooth'
        });
      }
    }
  }, [selectedMonth]);

  // 选择月份  -- 更新和数据筛选
  const handleSelectedMonth = (month: string, year: string) => {
    setSelectedMonth(`${month}/${year}`)
    //5需要变成  05
    const newMonth = month.length == 2 ? month : `0${month}`
    getFilterParams('date', `${year}-${newMonth}`)
  }

  return (
    <div
      ref={monthListRef}
      className={classNames(styles.major_header, {
        [styles.major_mobile_header]: pcOrMobileMode === 'mobile',
        [styles.major_pc_header]: pcOrMobileMode === 'pc'
      })}>
      {monthsList.map(item => <div
        key={`${item.month}/${item.year}`}
        data-month={`${item.month}/${item.year}`}
        className={classNames(styles.month_item, {
          [styles.active]: selectedMonth === `${item.month}/${item.year}`,
        })}
        onClick={() => handleSelectedMonth(item.month, item.year)}
      > {`${item.month}月`}{currentYear !== parseInt(item.year) &&
        <span className={styles.small_year}>/{item.year}</span>
        }</div>)}
    </div>
  );
};

export default MonthTab;