/**
 * @Description: PC-专家主页
 */
import React, { useEffect, useRef, useState } from 'react';
import { connect, history } from 'umi';
import InfiniteScroll from 'react-infinite-scroller';
import { throttle } from 'lodash';
import { stringify } from "qs"
import classNames from 'classnames';
import { processNames, randomColor, getDAesString } from '@/utils/utils';
import {Spin, message, Modal, Popover, Checkbox, Button, Typography} from 'antd';
import { QuestionCircleFilled } from '@ant-design/icons';
import styles from './PcIndex.less';

// 图标、icon
import followIcon from '@/assets/Expert/follow.png'; // 关注小图标
import unfollowIcon from '@/assets/Expert/unfollow.png'; // 关注小图标

import NoDataRender from '@/components/NoDataRender';      // 暂无数据组件
import PcHead from '@/componentsByPc/PcHeader';            // 头部导航组件
import CaseCard from '@/componentsByPc/CaseCard';          // 病例列表组件
import SpaceCard from '@/componentsByPc/SpaceCard';        // 空间列表组件
import MeetingCardInfoByPC from "@/components/MeetingCardInfoByPC";  // 会议卡片组件
import MyHomePostGroup from './Components/MyHomePostGroup';// 帖子3和1组件
import PosterModalByPc from '@/pages/Poster/PosterModalByPc';
import screenIcon from "@/assets/Expert/screen_icon.png";
import noScreenIcon from "@/assets/Expert/no_screen_icon.png";   // PC端海报弹窗
import MoreOperateByPc from '@/pages/UserInfo/PcComponents/PcMyHomepage/PcMyHomepageMeeting/MoreOperateByPc'   // 更多操作下拉菜单

const { confirm } = Modal;

// 直播筛选数据
const spaceJoinTypeList = [
  {
    id: 1,
    name: '我主持的直播',
  },
  {
    id: 2,
    name: '我预约的直播',
  },
  {
    id: 3,
    name: '历史记录',
  }
]

// 会议筛选数据
const meetingSpaceStatusList = [
  {
    id: 1,
    name: '我参与的会议',
  },
  {
    id: 2,
    name: '历史记录',
  },
]

// 直播、会议状态筛选
const spaceStatusList = [
  {
    id: null,
    name: '全部',
  },
  {
    id: 2,
    name: '预约中',
  },
  {
    id: 1,
    name: '进行中',
  },
  {
    id: 3,
    name: '已结束',
  }
]

// 会议筛选条件
const isBizList = [
  {
    id: 1,
    name: '企业会议',
  },
  {
    id: 0,
    name: '非企业会议',
  }
]

const isRecordingMeetingList = [
  {
    id: 0,
    name: '全部',
  },
  {
    id: 1,
    name: '已录制会议',
  }
]

const initState = {
  total: 0,
  spaceLists: [],
}

const Index: React.FC = (props: any) => {
  const { global, dispatch, pcAttention, loading } = props;
  const { query } = history.location
  const { id } = query;

  const userInfoData = JSON.parse(localStorage.getItem('userInfo') || '{}');   // 用户信息
  const { friUserId: userId } = userInfoData || {}                             // 获取用户id
  const isMyPages = userId == id                                               // 判断是否是当前用户
  // tab页签数据
  const tabLists = isMyPages?[
    { id: 1, val: '直播' },
    { id: 2, val: '会议' },
    { id: 3, val: '病例' },
    { id: 4, val: '帖子' },
  ]:[
    { id: 1, val: '直播' },
    { id: 3, val: '病例' },
    { id: 4, val: '帖子' },
  ]
  let myHomeSpaceFilter = sessionStorage.getItem('myHomeSpaceFilter')!='undefined'?JSON.parse(sessionStorage.getItem('myHomeSpaceFilter')):{};  // 空间筛选条件
  const initStatePage = {
    pageNum: 1,
    hasMore: true,  // 加载更多
    loadMore: false,
    spaceJoinType:(query.tabKey || pcAttention.tabState)==1?(myHomeSpaceFilter?.spaceJoinType||1):null,   // 空间角色类型 1:作为主持 2:预约的 3:作为嘉宾
    spaceStatus:myHomeSpaceFilter?.spaceStatus||null,    // 空间状态 1:预约中 2:进行中 3:已结束
    isBiz:myHomeSpaceFilter?.isBiz||null,                // 空间类型 1:企业空间 2:非企业空间
    isRecordingMeeting:(query.tabKey || pcAttention.tabState)==2?myHomeSpaceFilter?.isRecordingMeeting:null, // 会议录制 1:已录制，0/不传:全部
    meetingSpaceStatus:(query.tabKey || pcAttention.tabState)==2?(myHomeSpaceFilter?.meetingSpaceStatus||1):null, // 会议状态 1:预约中 2:进行中 3:已结束
  }

  const introduceRef = useRef<any>(null);   // 专家介绍ref
  const [tabType, setTabType] = useState(query.tabKey || pcAttention.tabState || 1);  // tab切换
  const [expertAdviceData, setExpertAdviceData] = useState<any>({})   // 专家详情数据
  const [caseLists, setCaseLists] = useState([]);            // 病例列表数据
  const [state, setState] = useState(initState)              // 空间列表数据
  const [statePage, setStatePage] = useState(initStatePage)  // 当前分页
  const [isModalOpen, setIsModalOpen] = useState(false)      // 筛选弹窗状态
  const [selectBizList, setSelectBizList] = useState(initStatePage.isBiz!=null&&initStatePage?.isBiz.split(',').map(Number)||[])  // 选中的空间分类
  const [selectRecordingMeeting, setSelectRecordingMeeting] = useState(initStatePage.isRecordingMeeting!=null&&initStatePage?.isRecordingMeeting||[])  // 选中的会议录制
  const [screenHeaderHighlight, setScreenHeaderHighlight] = useState((initStatePage.spaceStatus!=null||initStatePage.isBiz!=null||initStatePage.isRecordingMeeting!=null)?true:false)  // 筛选高亮
  const [visiblePosterModal, setVisiblePosterModal] = useState(false)  // 生成海报弹窗
  const [spaceId, setSpaceId]= useState(null)   //当前生成海报的会议id

  const { total, spaceLists } = state; // 空间列表数据和总条数
  const { pageNum, hasMore, loadMore, spaceJoinType, spaceStatus, isBiz, isRecordingMeeting, meetingSpaceStatus } = statePage || {}

  useEffect(() => {
    getExpertsInfo()
  }, [])

  // 获取专家详情
  const getExpertsInfo = () => {
    dispatch({
      type: "expertAdvice/getExpertsInfo",
      payload: {
        expertsUserId: id,             // 专家ID
      }
    }).then((res: any) => {
      let { code, content } = res || {}
      if (code == 200) {
        setExpertAdviceData(content)
      }
    }).catch((err: String) => {
      console.log(err)
    })
  }

  useEffect(() => {
    // 空间
    if(tabType == 1||tabType== 2){
      setState(initState)
      sessionStorage.getItem('myHomeSpaceFilter')&&sessionStorage.removeItem('myHomeSpaceFilter');
      getStarSpaceListBySearchUserId(1)
    } else if (tabType == 3) {
      // 病例
      getCaseInfoByExpertsUserId()
    }
  }, [tabType, spaceJoinType, spaceStatus, isBiz, isRecordingMeeting, meetingSpaceStatus])

  // 获取检索用户的直播or会议数据列表
  const getStarSpaceListBySearchUserId = (page:any, size:number) => {
    dispatch({
      type: "expertAdvice/getStarSpaceListBySearchUserId",
      payload: {
        pageNum: page,
        pageSize: size || 30,
        searchUserId: id,  // 检索用户id
        starSpaceType: tabType ==2? 2 : 1,  // 空间类型 1-直播 2-会议
        ...(spaceJoinType ? { spaceJoinType } : {}),   // 空间角色类型 1:作为主持 2:预约的 3:作为嘉宾
        ...(spaceStatus ? { spaceStatus } : {}),   // 空间状态 1:预约中 2:进行中 3:已结束
        ...(isBiz ? { isBiz } : {}), // 空间类型 1:企业空间 2:非企业空间
        ...(isRecordingMeeting ? { isRecordingMeeting } : {}), // 会议录制 1:已录制，0/不传:全部
        ...(meetingSpaceStatus ? { meetingSpaceStatus } : {}), // 会议空间状态 1:我参与的会议 2:历史
      }
    }).then((res: any) => {
      const { code, content } = res || {};
      const { total: resTotal, resultList, pageNum: resPageNum, pageSize: resPageSize } = content || {};

      if (res && code == 200) {
        setState({
          ...state,
          spaceLists: page === 1 ? [...resultList] : [...state.spaceLists,...resultList],
          total: resTotal,
        })
        setStatePage({
          ...statePage,
          loadMore: false,
          hasMore: resPageNum * resPageSize < total,
          pageNum: page && size ? statePage.pageNum : resPageNum,
        })
      }
    }).catch((err: any) => {
      message.error(err)
    })
  }

  // 获取病例详情
  const getCaseInfoByExpertsUserId = () => {
    dispatch({
      type: "expertAdvice/getCaseInfoByExpertsUserId",
      payload: {
        expertsUserId: id,  // 专家id
      }
    }).then((res: any) => {
      const { code, content, msg } = res || {};
      if (code == 200) {
        setCaseLists(content)
      } else {
        message.error(msg || '数据加载失败')
      }
    }).catch((err: any) => {
      message.error(err)
    })
  }

  // 提示弹框-狠心取消
  const tipsCancelFn = () => {
    followAndCheck(0)
  }

  // 提示弹框-我再想想
  const tipsNoCancelFn = () => {
  }

  /**
 * 点击取消关注或者点击关注（需要登录才能操作）
 * @param type 1 关注 0 取消关注
 */
  const followAndCheckFun = (type: number) => {
    // 没登录跳转登录页
    if (!localStorage.getItem('access_token')) {
      history.push({
        pathname: '/User/login',
        query: {
          redirectByPush: window.location.href,
        }
      })
      return
    }

    if(type == 1){
      followAndCheck(type)
    }else{
      confirm({
        title: '取消关注',
        content: '取消关注后将无法接收该用户动态(王国、空间的更新等)',
        okText: '狠心取消',
        cancelText: '我再想想',
        onCancel: ()=>tipsNoCancelFn(),
        onOk: ()=>tipsCancelFn()
      })
    }
  }

  // 关注取消关注接口调用方法
  const followAndCheck = (type:number)=>{
    dispatch({
      type: "expertAdvice/followAndCheck",
      payload: {
        expertsUserId: id,   // 专家Id
        isFocus: type,
      }
    }).then((res: any) => {
      const { code, msg } = res || {}
      if (code == 200) {
        getExpertsInfo()
        if(type == 1) {
          message.success('关注成功')
        } else {
          message.success('取消关注成功')
        }
      } else {
        message.error(msg)
      }
    }).catch((err: string) => {
      console.log(err)
    })
  }

  // 向专家发起指导（需要登录才能操作）
  const goToStartConsultation = () => {
    // 没登录跳转登录页
    if (!localStorage.getItem('access_token')) {
      history.push({
        pathname: '/User/login',
        query: {
          redirectByPush: window.location.href,
        }
      })
      return
    }
    history.push({
      pathname: '/ConsultationModule/StartConsultation/Step1',
      query: {
        expertsUserId: id,      // 专家ID
      }
    })
  }

  // tab（直播、会议、病例）切换
  const onClickTabFun = (tabKey:any) => {
    if (tabType == tabKey) {
      return
    }
    history.replace(`${history.location.pathname}?${stringify({
      ...history.location.query,
      tabKey: tabKey,
    })}`)
    setTabType(tabKey)
    dispatch({
      type: 'pcAttention/save',
      payload: {
        tabState: tabKey,
        subTabState: 1,
      }
    })
    // 清空筛选条件
    if(tabKey == 1||tabKey == 2){
      clearFilter(tabKey)
    }
  }

  // 滚动加载分页
  let handleInfiniteOnLoad = (page) => {
    if (spaceLists.length > total - 1) {
      setStatePage({
        ...statePage,
        loadMore: false,
        hasMore: false
      })
      return;
    }
    setStatePage({
      ...statePage,
      loadMore: true,
    })
    getStarSpaceListBySearchUserId(page)
  }

  handleInfiniteOnLoad = throttle(handleInfiniteOnLoad, 100);

  // 下架成功后，刷新数据
  const refreshFn = () => {
    getStarSpaceListBySearchUserId(1, spaceLists.length)
  }

  // 打开筛选项弹窗
  const openFilter = () => {
    setIsModalOpen(true)
  }

  // 选中空间分类
  const onSelectBizChange = (val) => {
    setSelectBizList(val)
  }
  // 选中全部空间分类
  const onSelectAllBizChange = (e) => {
    setSelectBizList(e.target.checked ? [0,1] : [])
  }

  // 选中会议录制
  const onSelectRecordingMeetingChange = (val) => {
    setSelectRecordingMeeting(val.length==0?[]:[val[val.length-1]])
  }

  // 确认筛选
  const onOkFilter = () => {
    setIsModalOpen(false)
    if(selectBizList.length!=0||selectRecordingMeeting.length!=0){
      setScreenHeaderHighlight(true);
    }else{
      setScreenHeaderHighlight(false);
    }
    setStatePage({
      ...statePage,
      isBiz: (selectBizList.length==2||selectBizList.length==0)?null:selectBizList[0].toString(),
      isRecordingMeeting:selectRecordingMeeting.length==0?null: selectRecordingMeeting[0].toString()
    })
  }

  // 清空筛选条件
  const clearFilter = (tabKey=1) => {
    setStatePage({
      ...statePage,
      spaceJoinType: tabKey == 1 ? 1 : null,
      isBiz: null,
      spaceStatus: null,
      isRecordingMeeting: null,
      meetingSpaceStatus: tabKey == 2 ? 1 : null,
    })
    setScreenHeaderHighlight(false);
    setSelectBizList([]);
    setSelectRecordingMeeting([]);
    setIsModalOpen(false)
  }

  // 海报
  const posterBtn = (item: any) => {
    setVisiblePosterModal(true)
    setSpaceId(item.id)
  }

  // 复制链接
  const onCopy = () => {
    message.success('复制链接成功!')
  }

  const getCaseInfoByExpertsUserIdLoading = !!loading.effects['expertAdvice/getCaseInfoByExpertsUserId']; // loading
  const getStarSpaceListBySearchUserIdLoading = !!loading.effects['expertAdvice/getStarSpaceListBySearchUserId']; // loading
  const loadingPersonImageTextList = !!loading.effects['graphicsText/personImageTextList']; // loading

  const {
    name,                // 专家名称
    postTitleDictName,   // 职称字典名称
    depSubjectDictName,  // 科室字典
    abilityLevelDictName,// 能力等级字典名称
    organizationName,    // 机构名称
    intro,               // 介绍
    imagePhotoPathShow,  // 专家形象路径
    isFocus,             // 是否关注 0未关注  1关注
    isExperts,           // 是否是专家：0:否，1:是
    personGdpCount,      // 个人GDP
    fansCount,           // 被关注用户数
    focusCount,          // 关注用户数
    originalPictureTreatCosts,    // 原图文诊费
    originalVideoTreatCosts       // 原视频诊费
  } = expertAdviceData || {}



  const [isOverflow, setIsOverflow] = useState(false);
  // 超过两行省略，滑过展示全部内容
  useEffect(() => {
    if (introduceRef?.current?.offsetHeight > 40) {
      setIsOverflow(true);
    }else {
      setIsOverflow(false);
    }
  }, [intro])

  return <div className={styles.pc_wrap}>
    {/* 头部导航 */}
    <PcHead />
    <div className={styles.content_box}>
      {/* 专家信息 */}
      <div className={styles.header_content}>
        {/* 左侧头像和关注按钮 */}
        <div className={styles.header_left}>
          {
            imagePhotoPathShow ?
            <div className={styles.head_sculpture}><img src={imagePhotoPathShow} alt="" /></div> :
            <div className={styles.no_head_sculpture} style={{ background: randomColor(id) }}>{processNames(name)}</div>
          }
          {
            isMyPages ? null :
              <div
                className={isFocus == 0 ? styles.header_follow : styles.header_not_follow}
                onClick={isFocus == 0 ? () => {
                  followAndCheckFun(1)
                } : () => {
                  followAndCheckFun(0)
                }}
              >
                <img className={isFocus == 0 ? styles.follow_icon : styles.follow_not_icon}
                     src={isFocus == 0 ? followIcon : unfollowIcon}/>
                {isFocus == 0 ? <span className={styles.follow_bule}>关注</span> :
                  <span className={styles.follow_not_bule}>取消关注</span>}
              </div>
          }

        </div>

        {/* 专家详情 */}
        <div className={styles.header_center}>
          <div className={styles.header_name}>
            <span className={styles.name}>{name}</span>
            <span className={styles.rank}>{postTitleDictName}</span>
            {depSubjectDictName || abilityLevelDictName ? <span className={styles.grade}>{depSubjectDictName}{abilityLevelDictName ? "·" : null}{abilityLevelDictName}</span> : null}
          </div>
          <div className={styles.header_clinic}>{organizationName}</div>
          <div className={styles.header_gdp}>
            <span className={styles.gdp}>关注 {focusCount || 0}</span>
            <span className={styles.gdp}>粉丝 {fansCount || 0}</span>
            <span className={styles.gdp} id={'gdp'}>GDP {personGdpCount || 0}<Popover placement="bottomLeft"  getPopupContainer={() => document.getElementById('gdp')} content={<div dangerouslySetInnerHTML={{ __html: global.gdpExplain!=null&&global.gdpExplain[0].gdpContent}} style={{width: '200px'}}></div>}><QuestionCircleFilled  style={{fontSize: 14,color: '#999',marginLeft: 5}} /></Popover></span>
          </div>
          <div className={styles.header_desc}>
            {
              <Popover overlayClassName={styles.header_desc_popover} content={intro} open={isOverflow ? undefined : false}>
                <div className={styles.brief_introduction_hide}><span className={styles.text} ref={introduceRef} dangerouslySetInnerHTML={{ __html: intro}} /></div>
              </Popover>
            }
          </div>
        </div>

        {/* 返回按钮 */}
        <div className={styles.header_right} onClick={()=>history.goBack()}>返回</div>
      </div>

      <div className={styles.content}>
        {/* tab切换 */}
        <div className={styles.tab_wrap}>
          {
            isMyPages ?
            tabLists.map(item => {
              if (isExperts != 1 && item.id == 3) return;
              return <div key={item.id} style={{display: 'flex'}}>
                {item.id === 6 ? <div className={styles.lines}></div> : null}
                <div className={classNames({[styles.tab_init]: true, [styles.tab_active]: tabType == item.id })} onClick={() => { onClickTabFun(item.id) }}>{item.val}</div>
              </div>
            }) :
            tabLists.filter(it => it.id !== 6).map(item => {
              if (isExperts != 1 && item.id == 3) return;
              return <div key={item.id} className={classNames({[styles.tab_init]: true, [styles.tab_active]: tabType == item.id })} onClick={() => { onClickTabFun(item.id) }}>{item.val}</div>
            })
          }
        </div>
        {/* 列表数据 */}
        <Spin wrapperClassName={styles.tab_content} spinning={getCaseInfoByExpertsUserIdLoading || getStarSpaceListBySearchUserIdLoading || loadingPersonImageTextList}>
          {/* 直播 */}
          {tabType == 1 &&
            <div className={styles.tab_content_list} id={'tab_content_list'}>
              {isMyPages&&<div className={styles.tab_spaceRoleType_list}>{
                spaceJoinTypeList.map((item, index) => {
                  return <span key={index} onClick={() => {
                    setStatePage({
                      ...statePage,
                      spaceStatus: null, // 直播状态 0:全部 1:预约中 2:进行中 3:已结束
                      isBiz: null, // 直播类型 1:企业空间 2:非企业空间
                      isRecordingMeeting: null, // 是否录制会议 0:全部 1:是 2:否
                      meetingSpaceStatus: null,
                      spaceJoinType: item.id,
                    })
                    setScreenHeaderHighlight(false);
                    setSelectBizList([]);
                    setSelectRecordingMeeting([]);
                  }} className={item.id == spaceJoinType ? styles.spaceRoleTypeActive : ''}>{item.name}</span>
                })}</div>}
              {isMyPages&&<div className={styles.tab_spaceStatus_list}>{
                spaceStatusList.map((item, index) => {
                  return <span key={index} onClick={() => {
                    if (item.id == spaceStatus || (spaceStatus && spaceStatus.includes(item.id))) {
                      return;
                    } else {
                      let valueArr;
                      if (item.id) {
                        const ids = spaceStatus ? spaceStatus.split(',') : [];
                        if (ids.includes(item.id)) {
                          valueArr = spaceStatus;
                        } else {
                          valueArr = ids.concat(item.id).join(',');
                          // if (valueArr.split(',').length == 3) {
                          //   valueArr = null;
                          // }
                        }
                      } else {
                        valueArr = null;
                      }
                      setStatePage({
                        ...statePage,
                        spaceStatus: valueArr,
                      })
                    }
                  }} className={(item.id == spaceStatus || (spaceStatus && spaceStatus.includes(item.id))) ? styles.spaceStatusActive : ''}>{item.name}</span>
                })
              }
              </div>}
              {!isMyPages&&<div style={{paddingLeft: '20px', fontSize: '14px'}}>共 {total} 条内容</div>}
              <InfiniteScroll
                loadMore={handleInfiniteOnLoad}
                threshold={50}
                pageStart={1}
                initialLoad={false}
                hasMore={!loadMore && hasMore}
                useWindow={false}
                className={styles.scroll_box}
              >
                <div className={styles.space_wrap}>
                  {spaceLists && spaceLists.length ? (
                    spaceLists.map((item: any, ind) => (
                      <div className={styles.space_list} key={ind}>
                        {/* isMyPage 判断是否为当前用户，展示 该内容已下架标签 */}
                        <SpaceCard spaceData={item} whereFrom={2} isMyPage={expertAdviceData?.id == userId}
                                   refreshFn={refreshFn}/>
                      </div>
                    ))
                  ) : (
                    <div className={styles.no_data_wrap}><NoDataRender className={styles.noDataStyle}/></div>
                  )}
                </div>
              </InfiniteScroll>
            </div>
          }
          {/* 会议 */}
          {tabType == 2 &&
            <div className={styles.tab_content_list} id={'tab_content_list'}>
              {
                isMyPages && <div className={styles.tab_spaceRoleType_list}>{
                  meetingSpaceStatusList.map((item, index) => {
                    return <span key={index} onClick={() => {
                      setStatePage({
                        ...statePage,
                        spaceStatus: null, // 空间状态 0:全部 1:预约中 2:进行中 3:已结束
                        isBiz: null, // 空间类型 1:企业空间 2:非企业空间
                        isRecordingMeeting: null, // 是否是录播会议 0:否 1:是
                        spaceJoinType: null, //   直播类型 1:作为主持 2:预约的 3:作为嘉宾
                        meetingSpaceStatus: item.id,
                      })
                      setScreenHeaderHighlight(false);
                      setSelectBizList([]);
                      setSelectRecordingMeeting([]);
                    }} className={item.id == meetingSpaceStatus ? styles.spaceRoleTypeActive : ''}>{item.name}</span>
                  })}</div>
              }
              {
                isMyPages && <div className={styles.tab_spaceStatus_list}>
                  <span style={{backgroundColor: '#fff'}}></span>
                  <i onClick={() => openFilter()} className={screenHeaderHighlight ? styles.screen_btnActive : ''}><img
                    className={styles.screen_icon} src={screenHeaderHighlight ? screenIcon : noScreenIcon}/>筛选</i>
                </div>
              }
              {!isMyPages && <div style={{paddingLeft: '20px', fontSize: '14px'}}>共 {total} 条内容</div>}
              <InfiniteScroll
                loadMore={handleInfiniteOnLoad}
                threshold={50}
                pageStart={1}
                initialLoad={false}
                hasMore={!loadMore && hasMore}
                useWindow={false}
                className={styles.scroll_box}
              >
                <div className={styles.meeting_wrap}>
                  {spaceLists && spaceLists.length ? (
                    spaceLists.map((item: any, ind) => (
                      <div className={classNames(styles.meeting_list,
                        {
                          [styles.meetingList_item_dayFirst]:ind>0&&item.appointmentStartDateDescs!=spaceLists[ind-1].appointmentStartDateDescs,
                          [styles.meetingList_item_dayLast]:ind<=spaceLists.length-2&&item.appointmentStartDateDescs!=spaceLists[ind+1].appointmentStartDateDescs,
                        })} key={ind}>
                        {(meetingSpaceStatus == 1&&isMyPages)&& <div>
                          {
                            ((ind>0&&item.appointmentStartDateDescs!=spaceLists[ind-1].appointmentStartDateDescs)||ind==0)&&<h3>{item.appointmentStartDateDescs}</h3>
                          }
                          <h4>{item.appointmentStartMinTime}-{item.appointmentStartMaxTime}</h4>
                        </div>}
                        {/* isMyPage 判断是否为当前用户，展示 该内容已下架标签 */}
                        <MeetingCardInfoByPC key={ind} item={item} />
                        {
                          meetingSpaceStatus == 1 && <div className={styles.item_myMeeting_box}>
                            <MoreOperateByPc meetingItem={item} refreshFn={refreshFn}/>
                            <span className={styles.item_poster_btn} onClick={() => posterBtn(item)}>分享海报</span>
                            <span className={styles.item_copy_btn}>
                              <Typography.Paragraph copyable={{
                                text: `${window.location.origin}/PlanetChatRoom/Meet/${item.id}?shareUserId=${userId}&isShare=1` + (item.password ? `&pwd=${getDAesString(item.password,'arrail-dentail&2', 'arrail-dentail&3')}` : ''),
                                icon: [<div>复制链接</div>, <div>复制链接</div>],
                                tooltips: ['', ''],
                                onCopy: onCopy,
                              }}></Typography.Paragraph>
                            </span>
                          </div>
                        }
                      </div>
                    ))
                  ) : (
                    <div className={styles.no_data_wrap}><NoDataRender className={styles.noDataStyle}/></div>
                  )}
                </div>
              </InfiniteScroll>
            </div>
          }
          {/* 判断是否是专家, 并且是否切换到病例 */}
          {
            isExperts == 1 && tabType == 3 ?
              <div className={styles.tab_content_case_box}>
                <div className={styles.tab_content_case}>
                  {
                    caseLists && caseLists.length ?
                      caseLists.map((item, ind) => <div key={ind} className={styles.tab_case_list}><CaseCard
                        caseData={item}/></div>) :
                      <NoDataRender className={styles.noDataStyle}/>
                  }
                </div>
              </div> : null
          }
          {/* 帖子3合1 */}
          {tabType == 4 && <MyHomePostGroup isMyPages={isMyPages} expertsUserId={id}/>}
        </Spin>
      </div>

      {/* 按钮 */}
      {
        isExperts == 1 && !isMyPages && (originalPictureTreatCosts || originalVideoTreatCosts) ?
          <div className={styles.ask_experts_btn} onClick={goToStartConsultation}>
            <div className={styles.price_style_box}>
              <div className={styles.title_style}>问同行</div>
            </div>
          </div>
          : null
      }
      <Modal
        title="筛选"
        open={isModalOpen}
        footer={null}
        onCancel={() => {
          setIsModalOpen(false);
        }}
        className={styles.screen_modal}
        destroyOnClose={true}
        getContainer={() => document.getElementById('tab_content_list')}
      >
        <div className={styles.screen_box}>
          <div className={styles.isBizTitle}>会议分类</div>
          <div className={styles.isBizSelectBox}>
            <Checkbox
              indeterminate={selectBizList.length == 1}
              onChange={onSelectAllBizChange}
              checked={selectBizList.length == 2}
            >全部</Checkbox>
            <Checkbox.Group value={selectBizList} style={{width: '100%'}} onChange={(val) => {
              onSelectBizChange(val)
            }}>
              {isBizList.map((item, idx) => {
                return <div key={idx} className={styles.screen_child}>
                  <Checkbox value={item.id}>{item.name}</Checkbox>
                </div>
              })
              }
            </Checkbox.Group>
          </div>
        </div>
        <div className={styles.screen_box}>
          <div className={styles.isBizTitle} style={{marginTop: '10px'}}>会议录制</div>
          <div className={styles.isBizSelectBox}>
            <Checkbox.Group value={selectRecordingMeeting} style={{width: '100%'}} onChange={(val) => {
              onSelectRecordingMeetingChange(val)
            }}>
              {isRecordingMeetingList.map((item, idx) => {
                return <div key={idx} className={styles.screen_child}>
                  <Checkbox value={item.id}>{item.name}</Checkbox>
                </div>
              })
              }
            </Checkbox.Group>
          </div>
        </div>
        <div className={styles.footer}>
          <Button onClick={() => clearFilter()}>取消</Button>
          <Button onClick={() => onOkFilter()} type="primary" className={styles.btn_primary}>确认</Button>
        </div>
      </Modal>
    </div>

    {/* 海报弹窗 */}
    <PosterModalByPc
      visible={visiblePosterModal}
      spaceId={spaceId}
      onCancel={() => {
        setVisiblePosterModal(false);
        setSpaceId(null);
      }}
    />
  </div>
}
export default connect(({global, expertAdvice, pcAttention, loading}: any) => ({
  global,
  expertAdvice,
  pcAttention,
  loading
}))(Index)
