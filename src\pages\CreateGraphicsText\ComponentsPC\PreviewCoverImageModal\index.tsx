import React from 'react'
import classNames from 'classnames'
import dayjs from 'dayjs'
import { processNames, randomColor } from '@/utils/utils'
import { Button, Modal } from 'antd'
import styles from './index.less'

interface PropsType {
  visible: boolean,                    // 弹窗是否显示
  coverImageNumber: number,            // 图片数量
  imageTitle: string,                  // 标题
  kingdomName: string,                 // 王国名
  textImgList: object,                 // 封面图
  onCancel: () => {},
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')        // 用户信息

  const {
    visible,
    coverImageNumber,
    imageTitle,
    kingdomName,
    textImgList,
  } = props

  return (
    <Modal
      visible={visible}
      className={styles.modal}
      onCancel={props.onCancel}
      destroyOnClose
      footer={null}
      width={1119}
    >
      <div className={styles.container}>
        <div className={styles.modal_title}>推荐流预览</div>
        <div className={styles.box}>
          {
            coverImageNumber == 1 ?
              <div className={classNames(styles.inner, styles.horizontal)}>
                <div className={styles.left}>
                  <div className={styles.article_title}>{imageTitle}</div>
                  {
                    kingdomName ?
                      <div className={styles.article_kingdom_box}>
                        <div>
                          <i></i>
                          <span>{kingdomName}</span>
                        </div>
                      </div>
                      : null
                  }

                  <div className={styles.article_user_box}>
                    <i style={UserInfo.headUrl ? {backgroundImage: `url(${UserInfo.headUrl})`} : {backgroundColor: `${randomColor(UserInfo.friUserId)}`}}>
                      {UserInfo.headUrl ? '' : processNames(UserInfo.name)}
                    </i>
                    <span>{UserInfo.name}</span>
                    <span>{dayjs().format('MM-DD')}</span>
                  </div>
                </div>
                <div className={styles.right} style={textImgList.length > 0 ? {backgroundImage: `url(${textImgList[0].imageUrlShow})`} : {backgroundColor: '#f5f5f5'}}>
                  {textImgList.length > 0 ? null : '暂无图片'}
                </div>
              </div>
              :
              <div className={classNames(styles.inner, styles.vertical)}>
                <div className={styles.article_title}>{imageTitle}</div>
                <div className={styles.article_image_box}>
                  {
                    textImgList.length == 0 ?
                      <div className={styles.article_image_item} style={{backgroundColor: '#f5f5f5'}}>暂无图片</div>
                      : textImgList.map((item, index) => {
                        return (
                          <div key={index} className={styles.article_image_item} style={{backgroundImage: `url(${item.imageUrlShow})`}}></div>
                        )
                      })
                  }
                </div>
                {
                  kingdomName ?
                    <div className={styles.article_kingdom_box}>
                      <div>
                        <i></i>
                        <span>{kingdomName}</span>
                      </div>
                    </div>
                    : null
                }
                <div className={styles.article_user_box}>
                  <i style={UserInfo.headUrl ? {backgroundImage: `url(${UserInfo.headUrl})`} : {backgroundColor: `${randomColor(UserInfo.friUserId)}`}}>
                    {UserInfo.headUrl ? '' : processNames(UserInfo.name)}
                  </i>
                  <span>{UserInfo.name}</span>
                  <span>{dayjs().format('MM-DD')}</span>
                </div>
              </div>
          }

        </div>
        <div className={styles.footer}>
          <Button type="primary" size="large" onClick={props.onCancel}>关闭预览</Button>
        </div>
      </div>
    </Modal>
  )
}

export default Index
