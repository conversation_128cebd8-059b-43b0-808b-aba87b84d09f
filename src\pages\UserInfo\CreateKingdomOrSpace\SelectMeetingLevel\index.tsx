/**
 * @Description: 选择空间类型
 */
import React, { useState } from 'react'
import { connect } from 'umi'
import styles from './index.less'
import GoBackIcon from '@/assets/GlobalImg/go_back.png' // 返回图片
import checkIcon from '@/assets/GlobalImg/check_icon.png'; // 勾选小图标

const Index: React.FC = (props: any) => {
  const { goBack, userInfoStore, dispatch } = props || {};
  const { meetingLevel } = userInfoStore || {};
  const [ checkedId, setCheckedId ] = useState(meetingLevel); // 勾选中的id

  const listData = [
    {id: 1, text: '集团'},
    {id: 2, text: '区域'},
    {id: 3, text: '诊所'},
  ]

  // 勾选事件
  const checkedFn = (item: any) => {
    setCheckedId(item?.id)
    dispatch({
      type: 'userInfoStore/setTaskListState',
      payload: {
        meetingLevel:item?.id,         // 会议级别 1集团 2区域 3诊所
        meetingLevelName: item?.text,  // 指导级别名称
        createModalVisible: false, // 是否展示创建空间王国下拉弹框
      }
    })
  }

  return (
    <div className={styles.container}>
      <div className={styles.title_box}>
        <div className={styles.title_btn} onClick={()=>{
          dispatch({
            type: 'userInfoStore/setTaskListState',
            payload: { createModalVisible:false, }
          })
        }}>
          <img src={GoBackIcon} width={12} height={24} alt=""/>
        </div>
        <div className={styles.title}>选择会议级别</div>
      </div>
      <div className={styles.wrap}>
        {
          listData && listData.map(item => {
            return <div className={styles.list} key={item.id} onClick={() => checkedFn(item)}>
              <div className={styles.list_title}>{item.text}</div>
              {checkedId == item.id ? <img className={styles.right_check} src={checkIcon} alt="" /> : null}
            </div>
          })
        }
      </div>
    </div>
  )
}

export default connect(({ userInfoStore, loading }: any) => ({userInfoStore, loading}))(Index)
