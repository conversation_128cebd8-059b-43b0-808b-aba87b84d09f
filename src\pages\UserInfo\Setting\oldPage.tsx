/**
 * @Description: 设置
 */
import React, { useState, useEffect } from 'react';
import { connect, history } from 'umi';
import {
  checkExistPassword
} from "@/services/login/login";
import styles from './oldPage.less';
import NavBar from '@/components/NavBar'; // 头部返回
import { getOperatingEnv } from '@/utils/utils';
import { Modal, Toast } from 'antd-mobile'
import DownloadAppCard from '@/components/DownloadAppCard';
import DownloadAppBtn from "@/components/DownloadAppBtn";
import WranningIcon from '@/assets/GlobalImg/wranning.png'
import {message} from "antd";

const Setting = (props:any) => {
  const {dispatch} = props;
  const [isVisible, setIsVisible] = useState(false);  // 判断是否存在密码
  const [existPassword, setExistPassword] = useState(null);

  useEffect(() => {
    checkPassword()
    if (getOperatingEnv() === '4' ) {
      // 设置pc tab页为设置
      dispatch({
        type: 'pcAccount/save',
        payload: {
          tabState: 5,
          subTabState: null,
        }
      })
      history.replace('/UserInfo')
    }
  }, [dispatch]);

  // 退出登录按钮
  const exitLoginBtn = () => {
    setIsVisible(true)
  }

  // 二次弹框取消按钮
  const onCancel = () => {
    setIsVisible(false)
  }

  // 二次弹框确定按钮
  const onSubmit = () => {
    setIsVisible(false)

    dispatch({
      type: 'userInfoStore/exitLogout',
      payload: {}
    }).then((res: any) => {
      if(res && res.code == 200) {
        localStorage.clear(); // 清空本地存储
        history.replace('/Square')
        return;
      } else {
        return Toast.show({content: res.msg || '数据加载失败'})
      }
    }).catch((err: any) => {
      console.log(err)
    })
  }

  // 检查是否设置过密码
  const checkPassword = async () => {
    try {
      const res = await checkExistPassword();
      if(res && res.code == 200) {
        setExistPassword(res.content)
      } else {
        setExistPassword(res.content)
      }
    } catch (err) {
      message.error('无法确认当前账户是否设置过密码，请稍后重试!')
    }
  }

  return (
    <div className={styles.Setup_Warp}>
      <NavBar title={'设置'} onBack={()=>{history.replace('/UserInfo')}}></NavBar>
      <div className={styles.Setup_box}>
        <div
          onClick={()=>{
            // 前往隐私政策页面
            history.push('/UserInfo/Agreement')
          }}
          className={styles.Setup_box_Item}>
          <div className={styles.Setup_box_Item_title}>隐私政策</div>
          <div className={styles.Setup_box_Item_Icon}> </div>
        </div>

        <div className={styles.Setup_box_Item_line}></div>
        <div onClick={()=>{
          // 前往账号注销页面
          history.push('/userInfo/logout')
        }} className={styles.Setup_box_Item}>
          <div className={styles.Setup_box_Item_title}>账号注销</div>
          <div className={styles.Setup_box_Item_Icon}> </div>
        </div>
        <div className={styles.Setup_box_Item_line}></div>
        {
          existPassword!=null&&<div onClick={()=>{
            // 前往设置密码&忘记密码页
            history.push(`/User/forgetPassword?from=Setting&exist=${existPassword}`)
          }} className={styles.Setup_box_Item}>
            <div className={styles.Setup_box_Item_title}>{existPassword?'修改登录密码':'设置登录密码'}</div>
            <div className={styles.Setup_box_Item_Icon}> </div>
          </div>
        }
      </div>
      <div className={styles.exit_login}>
        <div className={styles.exit_login_btn} onClick={exitLoginBtn}>退出登录</div>
      </div>
      <Modal
        visible={isVisible}
        content={
          <div className={styles.container}>
            <div className={styles.title_box}>
              <img src={WranningIcon} width={20} height={20} alt=""/>
              <div className={styles.title}>确定退出登录?</div>
            </div>
            <div className={styles.btn_box}>
              <div className={styles.cancel} onClick={onCancel}>取消</div>
              <div className={styles.ok} onClick={onSubmit}>确定</div>
            </div>
          </div>
        }
      >
      </Modal >
      {/*引导下载Friday App下载==顶部浮窗*/}
      {/*<div className={styles.DownloadAppCardBody}><DownloadAppCard /></div>*/}
      {/*<DownloadAppBtn InnerStyle={{height:'40px',lineHeight:'40px',padding:'0px',textAlign:'center'}} info={{roomId:'932'}} type={1} />*/}
		</div>
  )
}

export default connect(({ userInfoStore, loading }: any) => ({ userInfoStore, loading}))(Setting)
