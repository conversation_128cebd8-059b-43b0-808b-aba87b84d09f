.modal {
  :global {
    .ant-modal-header {
      border-bottom: 0;
    }
    .ant-modal-title {
      font-size: 20px;
      color: #000;
    }
    .ant-modal-close-x {
      font-size: 20px;
    }
    .ant-modal-body {
      padding: 0;
    }
  }
}
.content {
  padding: 13px 24px 24px 24px;

  .input_box {
    width: 100%;
    height: 40px;
    position: relative;
    display: flex;
    align-items: center;
    padding: 0 12px;
    box-sizing: border-box;
    background: #F5F5F5;
    border-radius: 4px;

    .search_icon {
      flex-shrink: 0;
      width: 20px;
      height: 20px;
      margin-right: 8px;
      flex-shrink: 0;
    }

    :global {
      .adm-input {
        flex: 1;
      }

      .adm-input-element {
        font-size: 14px;
        font-weight: 400;
        color: #000;

        &::placeholder {
          color: #CCCCCC;
        }
      }
    }
  }
}

.selectByBrand {
  width: 100%;
  height: 41px;
  background: #FFFFFF;
  border-radius: 0px 0px 0px 0px;
  // border-bottom: 1px solid #DDDDDD;
  display: flex;
  // padding-left: 6px;
  // padding-right: 6px;
  margin-top: 10px;
}

.selectByBrandItem {
  // width: 32px;
  height: 41px;
  font-size: 16px;
  color: #000000;
  line-height: 41px;
  text-align: center;
  font-style: normal;
  text-transform: none;
  margin-right: 16px;
  cursor: pointer;
  user-select: none;
}

.selectByBrandItem_active {
  position: relative;
  font-weight: 600;
  display: flex;
  flex-direction: column;
  align-items: center;
  .active_line {
    position: absolute;
    bottom: -1px;
    width: 12px;
    height: 3px;
    background: #000000;
    border-radius: 6px 6px 6px 6px;
    display: block;
  }
}

.areaWarp {
  display: flex;
  align-items: center;
  margin-top: 17px;
  //  padding-left: 5px;
  padding-right: 5px;
}

.selectTitle {
  font-weight: 400;
  font-size: 14px;
  color: #000000;
  line-height: 16px;
  text-align: center;
  font-style: normal;
  text-transform: none;
  margin-right: 24px;
}

.selectBox {
  width: calc(100% - 80px);
}

.footer {
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #ddd;
  padding: 21px 24px;
  :global {
    .ant-btn + .ant-btn {
      margin-left: 24px;
    }
  }
}

.GuestListWarp {
  padding-top: 15px;
  .GuestListTitle {
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-bottom: 10px;
    // margin-left: 10px;
  }
}

.list_item {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  cursor: pointer;
  padding-bottom: 12px;
  padding-top: 12px;
  border-bottom: 1px solid #E1E4E7;

  .list_item_info_wrap {
    display: flex;
    align-items: center;

    .selectGuest_select {
      width: 22px;
      height: 22px;
      margin-right: 9px;
    }

    .selectGuest_select_user {
      display: inline-block;
      width: 22px;
      height: 22px;
      background: url('../../../../assets/PlanetChatRoom/selectGuest_select_user.png');
      background-size: 22px 22px;
    }

    .selectGuest_Unselect_user {
      display: inline-block;
      width: 22px;
      height: 22px;
      background: url('../../../../assets/PlanetChatRoom/selectGuest_Unselect_user.png');
      background-size: 22px 22px;
    }

    .list_item_img {
      width: 40px;
      height: 40px;
      margin-right: 8px;
      border-radius: 50%;

      .no_comment_head{
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 500;
        color: #fff;
        white-space: nowrap;
      }

      img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
      }
    }

    .list_item_info {
      .nameWarp {
        display: flex;
      }
      .bizUser {
        width: 32px;
        height: 21px;
        background: #EEFFF9;
        border-radius: 4px 0px 4px 0px;
        font-weight: 400;
        font-size: 12px;
        color: #06A777;
        text-align: center;
        font-style: normal;
        text-transform: none;
        line-height: 21px;
        margin-left: 11px;
      }

      .name {
        font-size: 15px;
        font-weight: 500;
        line-height: 21px;
        color: #000000;
        margin-bottom: 4px;
        word-break: break-all;

        .highlight_name {
          color: #00D78B;
        }
      }

      .phone {
        font-size: 13px;
        font-weight: 400;
        color: #999999;
        line-height: 15px;
      }
    }
  }

  .active_select {
    background: #F5F5F5;
    border-radius: 18px;
    padding: 6px 8px;
    font-size: 12px;
    font-weight: 400;
    color: #999999;
    line-height: 17px;
    flex-shrink: 0;
  }

  .init_select {
    padding: 6px 12px;
    background: #E6F4FF;
    border-radius: 18px;
    font-size: 12px;
    font-weight: 400;
    color: #0095FF;
    line-height: 17px;
    flex-shrink: 0;
  }
}

.nodata {
  font-size: 15px;
  font-weight: 400;
  color: #333333;
  line-height: 17px;
  margin-top: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  img {
    width: 150px;
    height: 113px;
  }

  .empty_title {
    font-size: 15px;
    color: #333;
    line-height: 21px;
    margin-bottom: 8px;
  }
  .empty_msg {
    font-size: 14px;
    color: #999;
    line-height: 20px;
  }
}


.box_user {
  width: 100%;
  height: 280px;
  overflow-y: auto;
}


.btn_wrap {
  width: 100%;
}

.tip {
  width: 100%;
  height: 30px;
  background: #EBF5FF;
  border-radius: 0px 0px 0px 0px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  display: flex;
  align-items: center;
  /* justify-content: center; */
  padding-left: 10px;
  padding-right: 10px;

  .tip_box {
    width: 100%;
    line-height: 12px;
    font-size: 10px;
    font-weight: 400;
    color: #666666;
    overflow: hidden; /* 确保超出容器的文本被裁剪 */
    white-space: nowrap; /* 确保文本在一行内显示 */
    text-overflow: ellipsis; /* 使用省略号表示文本超出 */
  }
}

.btn_box {
  width: 100%;
  display: flex;
  padding: 16px;
  background: #fff;

  .btn_wrap_left {
    width: calc(100% - 80px);
    height: 50px;
    overflow-y: auto;
    display: flex;
    align-items: center;
    scrollbar-width: 0;
    scrollbar-height: none;

    .item {
      width: 40px;
      height: 40px;
      margin-right: 8px;
      border-radius: 50%;
    }

    .no_comment_head{
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: 500;
      color: #fff;
      white-space: nowrap;
    }

    img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
    }
  }

  .btn_wrap_left::-webkit-scrollbar {
    height: 0;
  }
}

.btn_wrap_right {
  width: 80px;
  height: 44px;
  display: flex;
  justify-content: flex-end;

  .btn_submit {
    width: 74px;
    height: 44px;
    background: #0095FF;
    border-radius: 22px 22px 22px 22px;
    font-weight: 400;
    font-size: 16px;
    color: #FFFFFF;
    line-height: 44px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    cursor: pointer;
    user-select: none;
  }

  .btn_submit:active {
    background: #0180da;
  }
}

