import React, { useState } from 'react';
import dayjs from 'dayjs';
import { message } from 'antd';
import classNames from 'classnames';
import styles from './index.less';


interface IProps {
  itemData?: any;
  pcOrMobileMode?: string;
}
const Index: React.FC<IProps> = ({ itemData, pcOrMobileMode }) => {
  const { redirectInfo, tags, eventStartDate, eventEndDate, eventName } = itemData;
  const [msgWarning, setMsgWarning] = useState(false); //是否有外链提示弹窗，如果有不可重复点击

  // 格式化时间为月日格式
  const formatToDate = (dateStr) => {
    return dayjs(dateStr).format('MM月DD日');
  };

  // 格式化时间为时分格式
  const formatToTime = (dateStr) => {
    return dayjs(dateStr).format('HH:mm');
  };

  // 时间处理函数，根据类型返回不同的时间格式
  const majorTime = (startTime, endTime, type = 'all') => {
    let result = [];

    // 只显示一个时间
    if (startTime === endTime || type === 'only') {
      if (startTime.includes(':')) {
        result = [formatToDate(startTime), formatToTime(startTime)];
      } else {
        result.push(formatToDate(startTime))
      }
    } else {  // 显示两个时间  都不要分秒
      result.push(formatToDate(startTime))
      result.push(formatToDate(endTime))
    }
    return result;
  };


  // 跳转  -- 外链跳转需要提示
  const handleJump = () => {
    if (redirectInfo[0].redirectType === '4') {
      setMsgWarning(true)
      const messageKey = 'jumpWarning';
      if (!msgWarning) {
        message.warning('即将跳转到外站链接，请注意保护隐私等安全哦！', () => {
          window.location.href = redirectInfo[0].redirectUrl;
          message.destroy(messageKey)
          setMsgWarning(false)
        });
      }
    } else {
      window.location.href = redirectInfo[0].redirectUrl;
    }

  }

  return <div
    className={classNames(styles.calendar_item, {
      [styles.calendar_pc_item]: pcOrMobileMode === 'pc'
    })}
    onClick={handleJump}>
    <div className={styles.lt}>
      {eventStartDate && eventEndDate && (majorTime(eventStartDate, eventEndDate)).map(item => <div key={item}>{item}</div>)}
    </div>
    <div className={styles.rt}>
      <div className={styles.title}>{eventName}</div>
      {tags && (<div className={styles.tags}>
        {tags.map((v, index) => {
          return (
            <span
              key={`${v.color}${index}`}
              style={{
                background: `#${v.tagColor}1A`,
                color: `#${v.tagColor}`,
              }}
              className={styles.tagSpan}
            >
              {v.tagName}
            </span>
          );
        })}
      </div>)}
    </div>
  </div>
}

export default Index;