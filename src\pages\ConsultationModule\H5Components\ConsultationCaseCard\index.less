.wrap {
  width: 100%;
  background: #fff;
  padding-left: 16px;
  padding-top: 4px;

  .header {
    display: flex;
    justify-content: space-between;
    line-height: 22px;
    padding: 12px 16px 12px 0;
    border-bottom: 1px solid #E1E4E7;

    .header_title {
      font-size: 16px;
      font-weight: 500;
      color: #000;
    }

    .header_btn {
      font-size: 14px;
      color: #0095FF;
      padding: 0 3px;
      cursor: pointer;
    }
  }

  .content {
    padding: 12px 16px 8px 0;

    .case_title {
      font-size: 16px;
      line-height: 22px;
      font-weight: 500;
      color: #000;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-bottom: 8px;
    }

    .case_course {
      display: flex;
      flex-wrap: wrap;

      & > div {
        background: #EDF9FF;
        border-radius: 2px;
        height: 21px;
        line-height: 21px;
        font-size: 12px;
        padding: 0 4px;
        color: #0095FF;
        margin-right: 6px;
        margin-bottom: 8px;
      }
    }

    .case_desc {
      display: flex;
      flex-wrap: nowrap;
      margin-bottom: 8px;

      .desc_title {
        width: 70px;
        min-width: 70px;
        white-space: nowrap;
        font-size: 14px;
        color: #666;
        line-height: 20px;
      }

      .desc {
        flex: 1;
        font-size: 14px;
        color: #666;
        line-height: 20px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .case_img {
      padding-bottom: 2px;
      display: flex;
      flex-wrap: wrap;
      margin-right: -16px;
      max-width: 400px;
      .img {
        width: 108px;
        height: 110px;
        margin-right: 6px;
        margin-bottom: 6px;
        border-radius: 6px;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        &:nth-child(3n) {
          margin-right: 0;
        }
      }
    }


    .case_content {
      margin-bottom: 8px;
      background: #F5F5F5;
      border-radius: 6px;
      padding: 8px;
      font-size: 14px;
      color: #666;
      line-height: 22px;
      word-break: break-all;
    }
  }
}

.wrap.wrap_pc {
  border-radius: 2px 2px 0 0;
}
