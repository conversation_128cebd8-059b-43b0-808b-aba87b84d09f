/**
 * @Description: 移动端，病例详情弹窗，模板
 */
import React, { useEffect } from 'react'
import { screenData } from '@/utils/utils'
import { Popup, ImageViewer } from 'antd-mobile'
import { CloseOutlined } from '@ant-design/icons'
import styles from './index.less'

interface PropsType {
  visible: boolean,                    // true，false
  caseData: any,                       // 病例数据
  onCancel: any,                       // 关闭弹窗
}

const Index: React.FC<PropsType> = (props: any) => {
  const { visible, caseData = {} } = props

  // 查看大图
  const previewBigImage = (index) => {
    ImageViewer.Multi.show({
      defaultIndex: index,
      images: caseData.consultationCaseMediaDtoList.filter(item => item.type == 0).map(item => item.fileUrlShow),
      getContainer: () => document.getElementById('container'),
    })
  }

  // 组件销毁时，关闭查看大图弹窗
  useEffect(() => {
    return () => {
      ImageViewer.clear()
    }
  },[])

  return (
    <Popup
      visible={visible}
      onMaskClick={props.onCancel}
      className={styles.popup_container}
      bodyStyle={{height: 'calc(100% - 1px)'}}
      destroyOnClose
      style={{'--z-index': 9991}}
    >
      <div className={styles.header_line} onClick={props.onCancel}>
        <div className={styles.header_line_bar}></div>
      </div>
      <div className={styles.header_title}>
        病例详情
        <div className={styles.close_icon} onClick={props.onCancel}>
          <CloseOutlined/>
        </div>
      </div>

      <div className={styles.container} id="container">
        <div className={styles.case_title}>{caseData.caseName}</div>
        {/*
          caseData.depSubjectDictsStrList && caseData.depSubjectDictsStrList.length > 0 &&
          <div className={styles.case_tag}>
            {
              caseData.depSubjectDictsStrList.map((item, index) => <div key={index} className={styles.tag}>{item}</div>)
            }
          </div>
        */}

        <div className={styles.details_header}>基本信息<i></i></div>
        <div className={styles.details_wrap}>
          <div className={styles.details_item_horizontal}>
            <div className={styles.item_label}>年龄</div>
            <div className={styles.item_value}>{screenData(caseData.age)}</div>
          </div>
          <div className={styles.details_item_horizontal}>
            <div className={styles.item_label}>性别</div>
            <div className={styles.item_value}>{caseData.sex}</div>
          </div>
        </div>

        <div className={styles.details_item}>
          <div className={styles.item_label}>主诉</div>
          <div className={styles.item_value}>{caseData.chiefComplaint || '--'}</div>
        </div>
        <div className={styles.details_item}>
          <div className={styles.item_label}>现病史</div>
          <div className={styles.item_value}>{caseData.presentDisease || '--'}</div>
        </div>
        <div className={styles.details_item}>
          <div className={styles.item_label}>既往史</div>
          <div className={styles.item_value}>{caseData.previousHistory || '--'}</div>
        </div>
        <div className={styles.details_item}>
          <div className={styles.item_label}>全身健康情况</div>
          <div className={styles.item_value}>{caseData.wholeHealth || '--'}</div>
        </div>

        <div className={styles.details_header}>检查及诊断<i></i></div>
        <div className={styles.details_item}>
          <div className={styles.item_label}>检查</div>
          <div className={styles.item_value}>{caseData.checkUp || '--'}</div>
        </div>
        <div className={styles.details_item}>
          <div className={styles.item_label}>诊断</div>
          <div className={styles.item_value}>{caseData.diagnosis || '--'}</div>
        </div>

        <div className={styles.details_header}>治疗方案<i></i></div>
        <div className={styles.details_item}>
          <div className={styles.item_value}>
            {caseData.treatmentPlanList && caseData.treatmentPlanList.length > 0 ? caseData.treatmentPlanList[0] : '--'}
          </div>
        </div>

        <div className={styles.details_header}>影像资料<i></i></div>
        <div className={styles.details_item_img}>
          {
            caseData.consultationCaseMediaDtoList && caseData.consultationCaseMediaDtoList.filter(item => item.type == 0).length > 0 &&
            caseData.consultationCaseMediaDtoList.filter(item => item.type == 0).map((item, index) => {
              return <div key={index} className={styles.img} onClick={() => previewBigImage(index)} style={{backgroundImage: `url(${item.fileUrlShow})`}}></div>
            })
          }
        </div>
      </div>

    </Popup>
  )
}

export default Index
