.warp_content_iphone {
  padding-left: 40px;
  background: #0a0a0a;
}

.warp_content {
  min-width: 320PX;
  width: 100%;
  height: 100%;
  background: #000;

  .content {
    width: 100%;
    height: 100%;
    aspect-ratio: 16/9;
    min-width: 320PX;
    position: relative;
    overflow: hidden;

    .HorizontalLiveRoom_camera_picture_Box {
      position: absolute;
      width: 169px;
      height: 100%;
      border-radius: 0px 0px 0px 0px;
      top: 0px;
      right: -140px;
      opacity: 1;
      z-index: 2;
      overflow-y: auto;

      .HorizontalLiveRoom_camera_picture_btn {
        width: 29px;
        height: 142px;
        background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_camera_picture_btn.png') no-repeat;
        background-size: 29px 142px;
        display: block;
        position: absolute;
        top: 50%;
        left: 0px;
        transform: translateY(-50%);
        user-select: none;
        cursor: pointer;
        z-index: 1;
      }

      .HorizontalLiveRoom_camera_picture_take_back_btn {
        background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_camera_picture_take_back_btn.png') no-repeat;
        background-size: 29px 142px;
      }

      .HorizontalLiveRoom_camera_picture_camera_live {
        width: 140px;
        height: 100%;
        background: #222;
        opacity: 1;
        overflow-y: auto;
        position: absolute;
        right: 0px;
        display: flex;
        flex-direction: column;

        .noGuestsListWarp {
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;

          .noGuestsListIcon {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
          }
          .noGuestsIcon {
            width: 24px;
            height: 24px;
            display: inline-block;
            background: url('../../../../assets/PlanetChatRoom/SpatialDetail_no_guests_list_icon.png') no-repeat;
            background-size: 24px 24px;
            margin-bottom: 10px;
            border: none;
          }
          .noGuestsText {
            font-size: 12px;
            font-weight: 400;
            color: #AAAAAA;
            line-height: 12px;
          }
        }

          .HorizontalLiveRoom_camera_picture_camera_item {
            width: 140px;
            height: 90px;
            background: #efefef;
            border-radius: 0px 0px 0px 0px;
            opacity: 1;
            position: relative;
            // margin-bottom: 3px;

            .HorizontalLiveRoom_camera_picture_camera_bottom_box {
              width: 140px;
              height: 24px;
              background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 100%);
              border-radius: 0px 0px 0px 0px;
              opacity: 1;
              position: absolute;
              bottom: 0px;
              display: flex;
              justify-content: space-between;
              align-items: center;
              font-size: 12px;
              color: #FFFFFF;
              line-height: 24px;
              padding-left: 8px;
              padding-right: 8px;
            }

            .HorizontalLiveRoom_camera_picture_camera_bottom_box_lianmai {
              background: rgba(255,255,255,0.8);
            }


            .HorizontalLiveRoom_camera_picture_camera_bottom_box_Name_Warp {
              display: flex;
              align-items: center;
            }

            .HorizontalLiveRoom_camera_picture_camera_bottom_box_Name {
              max-width: 50px;
              height: 24px;
              overflow:hidden;
              text-overflow:ellipsis;
              white-space:nowrap;
            }

            .HorizontalLiveRoom_camera_picture_camera_bottom_box_Name_lianmai {
              color: #000000;
            }


            .HorizontalLiveRoom_camera_picture_mic_icon {
              width: 12px;
              height: 12px;
              background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_camera_picture_mic_icon.png') no-repeat;
              background-size: 12px 12px;
              display: inline-block;
              margin-left: 4px;
            }

            .HorizontalLiveRoom_camera_picture_forbidden_mic_icon {
              width: 12px;
              height: 12px;
              background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_camera_picture_forbidden_mic_icon.png') no-repeat;
              background-size: 12px 12px;
              display: inline-block;
              margin-left: 4px;
            }

            .HorizontalLiveRoom_camera_picture_forbidden_Lianmai {
              width: 12px;
              height: 12px;
              background: url('../../../../assets/PlanetChatRoom/SpatialDetail_MicrophoneOn_btn_Lianmai.png') no-repeat;
              background-size: 12px 12px;
              display: inline-block;
              margin-left: 4px;
            }

            .GuanZhu_btn {
              cursor:pointer;
              user-select:none;
              padding-top: 2px;
              padding-bottom: 2px;
              padding-left: 4px;
              padding-right: 4px;
              font-size: 12px;
              font-weight: 400;
              color: #0095FF;
              line-height: 12px;
              background: #EDF9FF;
              border-radius: 14px 14px 14px 14px;
              opacity: 1;
            }

            .unGuanZhu_btn {
              color: #CCCCCC;
              background: #F5F5F5;
            }

            // 强制下麦按钮
            .forceXiaMai {
              cursor:pointer;
              user-select:none;
              padding-top: 2px;
              padding-bottom: 2px;
              padding-left: 4px;
              padding-right: 4px;
              font-size: 12px;
              font-weight: 400;
              color: #FFFFFF;
              line-height: 12px;
              background: #FF5F57;
              border-radius: 14px 14px 14px 14px;
              opacity: 1;
            }
          }
          .HorizontalLiveRoom_camera_picture_camera_item_hidden {
            // height: 0px;
            overflow: hidden;

            .headUrlWarp {
              width: 100%;
              height: 100%;
              display: flex;
              justify-content: center;
              align-items: center;
            }

            .video_Title_box_left_avatar {
              width: 44px;
              height: 44px;
              background: #F9B4E3;
              opacity: 1;
              border-radius: 50%;
              // margin-right: 8PX;
              overflow: hidden;
            }

            .video_Title_box_left_avatar_img {
              width: 44px;
              height: 44px;
              border-radius: 50%;
              overflow: hidden;
            }

            .head_sculpture_name {
              width: 44px;
              height: 44px;
              font-size: 16px;
              font-weight: 500;
              color: #FFFFFF;
              display: flex;
              align-items: center;
              justify-content: center;
              white-space: nowrap;
            }
          }

          .StreamWarp {
            width: 100%;
            height: 100%;
          }

          .StreamWarpHidden {
            height: 0px;
            overflow: hidden;
          }

        }
      }
      .isShowCameraList {
        position: absolute;
        top: 0px;
        right: -0px;
      }
  }

    .HorizontalLiveRoom_send_flowers_quantity {
      width: 74px;
      height: 39px;
      background: rgba(0,0,0,0.5);
      border-radius: 0px 24px 24px 0px;
      position: absolute;
      top: 45%;
      left: 0px;
      transform: translateY(-50%);
      user-select: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      z-index: 1;

      .HorizontalLiveRoom_send_flowers_content_icon {
        width:  24px;
        height: 24px;
        background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_send_flowers_content_icon.png') no-repeat;
        background-size: 24px 24px;
        display: inline-block;
      }

      .HorizontalLiveRoom_clapinteraction_icon {
        width:  24px;
        height: 24px;
        background: url('../../../../assets/PlanetChatRoom/SpatialDetail_clap_interaction.png') no-repeat;
        background-size: 24px 24px;
        display: inline-block;
      }

      .HorizontalLiveRoom_send_flowers_quantity_rigth {
        margin-left: 4px;
        font-size: 12px;
        font-weight: 400;
        color: #FFF;
        line-height: 12px;
      }
    }


    /* 隐藏动态区域的滚动条 */
    .liveRoom_msg_list::-webkit-scrollbar {
      display: none;
    }

    .liveRoom_msg_list_Warp {
      // height: 216px;
      width: 260px;
      position: absolute;
      top: calc(40% + 3vh);
      left: 20px;
      z-index: 1;
      background: #7ec1ff;
    }
    // 消息列表
    .liveRoom_msg_list {
      width: 100%;
      height: 26vh;
      overflow-y: auto;
      background: rgba(0,0,0,0.1);
      box-shadow: 4px 4px 4px 4px rgba(0,0,0,0.1);
      scrollbar-width: none; /* 隐藏火狐浏览器的滚动条 */
      -ms-overflow-style: none; /* 隐藏 IE 和 Edge 浏览器的滚动条 */

      .avatar_msg_item {
        color: #FFFFFF;
        display: flex;
        font-size: 14px;
        margin-bottom: 6px;
        // line-height: 16px;
      }
      .avatar_msg_avatar {
        width: 24px;
        height: 24px;
        border-radius:50%;
        background: #FFF;
        margin-right: 8px;

        .msg_content_item_icon_img {
          width: 100%;
          height: 100%;
          border-radius:50%;
        }
      }

      .avatar_msg_name {
        margin-right: 0px;
        display: flex;
        .avatar_msg_name_text {
          max-width:50px;
          overflow:hidden;
          text-overflow:ellipsis;
          white-space:nowrap;
        }
      }

      .avatar_msg_content {
        margin-right: 5px;
      }

      .msg_content_item_msgContent_emcee {
        color: #0095FF;
      }

      .HorizontalLiveRoom_send_guzhang_icon {
        width: 18px;
        height: 18px;
        background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_send_guzhang_icon.png') no-repeat;
        background-size: 18px 18px;
        display: inline-block;
        margin-right: 4px;
      }

      .HorizontalLiveRoom_send_flowers_content_icon {
        width: 18px;
        height: 18px;
        background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_send_flowers_content_icon.png') no-repeat;
        background-size: 18px 18px;
        display: inline-block
      }

      .HorizontalLiveRoom_send_message_icon {
        // width: 16px;
        // height: 13px;
        // background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_send_message_icon.png') no-repeat;
        // background-size: 16px 13px;
        color: #FCBD33;
        display: inline-block;
        position: relative;
        font-style: italic;
        top: -1.5px;
        font-weight: 800;
      }
    }

    .video_content {
      width: 100%;
      height: 100%;
      position: relative;
      background: transparent;
      overflow: hidden;

      .tryPlay {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0px;
        left: 0px;
        background: rgba(0, 0, 0, 0.50);
        z-index: 3;
        display: flex;
        align-items: center;
        justify-content: space-around;

        .tryPlayContent {
          display: flex;
          flex-direction: column;
          align-items: center;
        }

        .tryPlayContent_text {
          font-size: 16px;
          font-weight: 600;
          color: #FFFFFF;
          line-height: 16px;
          margin-bottom: 5px;
        }

        .tryPlayContent_dose {
          font-size: 14px;
          font-weight: 400;
          color: #FFFFFF;
          line-height: 14px;
        }

        .tryPlayContent_btn {
          width: 88px;
          height: 32px;
          background: #0095FF;
          border-radius: 30px 30px 30px 30px;
          opacity: 1;
          font-size: 14px;
          font-weight: 400;
          color: #FFFFFF;
          line-height: 32px;
          text-align: center;
          margin-top: 3px;
          cursor: pointer;
          user-select: none;
        }

        .tryPlayContent_btn:active {
          background: #0077cc;
        }
      }

      .Live_Video {
        width: 100%;
        height: 100%;
        // background: url("https://static.jwsmed.com/public/DigitalHealth/Business/assets/BgImg/PlanetChatRoom_HorizontalLiveRoom_Live_bg.png") no-repeat;
        // background-size: 100% 100%;
        overflow: hidden;
        display: flex;
        justify-content: center;
        align-items: center;

        .spaceCoverUrlShow {
          height: 100%;
          width: 100%;
          object-fit: contain;
        }
      }

      .CameralistWarp {
        width: 100%;
        height: 100%;
        background: #222;
        .Cameralist {
          width: 100%;
          height: 100%;
          display: flex;
          flex-wrap: wrap;
          flex-direction: row;
          justify-content: center;
          align-content: center;
          .CameraItem {
            width: 25%;
            height: 25%;
            aspect-ratio: 16/9;
            border: 0.25PX solid #fff;
          }
        }
      }

      .video_box_warp {
        width: 100%;
        height: 30%;
        position: absolute;
        z-index: 1;
        top: 20%;
        color: white;
      }

      .video_HorizontalLiveRoom {
        width: 100%;
        height: 100%;
        position: absolute;
        z-index: 1;
        top: 20%;
        color: white;
      }

      /*.video_box_warp {
        width: 100%;
        height: 100%;1
        display: flex;
        position: relative;
        .video_box {
          width: calc(100% - 100px);
          height: 100%;
          background: transparent;
        }
      }*/
    }







    video::-internal-media-controls-download-button {
      display:none;
    }
    video::-webkit-media-controls-enclosure {
      overflow:hidden;
    }
    video::-webkit-media-controls-panel {
      width: calc(100% + 30px);
    }

    video::-webkit-media-controls-fullscreen-button {
      display: none;
    }

    video::-webkit-media-controls-play-button {
      display: none;
    }

    video::-webkit-media-controls-timeline {
      display: none;
    }

    video::-webkit-media-controls-current-time-display {
      display: none;
    }

    video::-webkit-media-controls-time-remaining-display {
      display: none;
    }

    video::-webkit-media-controls-mute-button {
      display: none;
    }

    video::-webkit-media-controls-toggle-closed-captions-button {
      display: none;
    }

    video::-webkit-media-controls-enclosure {
      display: none;
    }

    video::-webkit-media-controls-volume-slider {
      display: none;
    }

    .video_warp { }

    .video {
      width: 100%;
      height:100vh;
      // height: calc(100%);
      // aspect-ratio: 16/9;

      .videoJs {
        width: 100%;
        height:100vh;
      }

      :global {
        .video-js .vjs-tech {
          height:100vh;
        }

        .video-react .video-react-big-play-button {
          display: none;
        }

        .video-react-has-started .video-react-control-bar {
          display: none;
        }

        .vjs-has-started .vjs-control-bar, .vjs-audio-only-mode .vjs-control-bar {
          display: none;
        }
        .vjs-control-bar {
          display: none;
        }
        .video-js .vjs-big-play-button {
          display: none;
        }
       /* .vjs-poster {
          display: none;
        }*/
      }
    }

    .title_Warp {
      width: 100%;
      height: 100px;
      position: absolute;
      top: 0;
      left: 0;
      background: linear-gradient(180deg, #000000 0%, rgba(0,0,0,0) 100%);
      z-index: 1;
      padding-left: 20px;
      padding-right: 20px;
      padding-top: 25px;
      display: flex;
      justify-content: space-between;


      .title_content {
        display: flex;
        height: 50px;
      }

      .title_Icon_jiabin_warp {
        color: #FFFFFF;
        margin-left: 25px;
        .title_Icon_jiabin {
          display: flex;
          flex-direction: column;
          align-items: center;
          cursor: pointer;
          user-select: none;
          .title_Icon_jiabin_Icon {
            display: inline-block;
            width:20px;
            height:20px;
            background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_title_distinguished_guest_Icon.png') no-repeat;
            background-size: cover;
          }
          .title_Icon_jiabin_Icon_text {
            font-size: 12px;
            font-weight: 400;
            color: #fff;
            line-height: 12px;
            margin-top: 7px;
          }
        }
      }

      .title_Icon_shenqing_warp {
        color: #FFFFFF;
        margin-left: 25px;
        position: relative;
        .title_Icon_shenqing {
          display: flex;
          flex-direction: column;
          align-items: center;
          cursor: pointer;
          user-select: none;
          .title_Icon_shenqing_Icon {
            display: inline-block;
            width:20px;
            height:20px;
            background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_apply_lianmai_icon.png') no-repeat;
            background-size: cover;
          }
          .title_Icon_shenqing_Icon_text {
            font-size: 12px;
            font-weight: 400;
            color: #fff;
            line-height: 12px;
            margin-top: 7px;
          }
        }

        .HandUpListLength {
          display: block;
          width: 14px;
          height: 14px;
          border-radius: 50%;
          background: rgba(251, 65, 65, 0.8);
          position: absolute;
          font-size: 12px;
          line-height:14px;
          text-align: center;
          overflow: hidden;
          color: #FFFFFF;
          top: -1px;
          right: -14px;
          z-index: 10;
        }
      }

      .setConfig_warp {
        display: flex;
        justify-content: flex-end;
      }

      .setConfig_warp_margin_IsShowCameraList {
        margin-right: 140px;
      }

      .setConfig_warp_margin_IsShowApplyForLinkMicList {
        margin-right: 240px;
      }

      .title_Icon_setConfig_warp {
        color: #FFFFFF;
        margin-left: 25px;
        .title_Icon_setConfig {
          display: flex;
          flex-direction: column;
          align-items: center;
          cursor: pointer;
          user-select: none;
          .title_Icon_setConfig_Icon {
            display: inline-block;
            width:20px;
            height:20px;
            background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_title_setconfig_icon.png') no-repeat;
            background-size: cover;
          }
          .title_Icon_setConfig_Icon_text {
            font-size: 12px;
            font-weight: 400;
            color: #fff;
            line-height: 12px;
            margin-top: 7px;
          }
        }
      }

      .title {
        display: flex;
        align-items: center;
        margin-right: 10px;

        .title_Icon {
          color: #fff;
          margin-right: 10px;
          .title_Icon_box {
            width: 12px;
            height: 24px;
            background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_title_Back_icon.png') no-repeat;
            background-size: 12px 24px;
            user-select: none;
            cursor: pointer;
          }
        }
        .trtc_boxWarp {
          max-width: 30vw;
        }
        .trtc_box {
          color: #fff;
          font-size: 15px;
          margin-bottom: 3px;
          display: -webkit-box;
          overflow:hidden;
          -webkit-box-orient:vertical;
          -webkit-line-clamp:2;
          word-break: break-all;
        }
        .trtc_desc {
          font-size: 12px;
          font-weight: 400;
          color: #fff;
          line-height: 20px;
          display: flex;
          align-items: center;
        }
        .trtc_HorizontalLiveRoom_title_user_volume_Icon {
          width: 14px;
          height: 14px;
          background: url("../../../../assets/PlanetChatRoom/HorizontalLiveRoom_title_user_volume_Icon.png") no-repeat;
          display: inline-block;
          background-size: 14px 14px;
          // position: relative;
          // top: 3px;
        }

        .trtc_flow_rate {
          margin-right: 5px;
          display: -webkit-box;
          overflow: hidden;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          word-break: break-all;
        }

        .kingdomNameWarp {
          display: flex;
          align-items: center;
          user-select: none;
          cursor: pointer;

          .kingdomNameSpan {
            max-width: 90px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .video_Title_box_LeftBottom_icon {
            width: 12px;
            height:12px;
            display: inline-block;
            background: url("../../../../assets/PlanetChatRoom/PlanetChatRoom_white_icon.png") no-repeat;
            background-size: contain;
            position: relative;
            margin-left: 6px;
            font-size: 12px;
          }
        }
      }

      .title_Icon_Collect {
        display: flex;
        .title_Icon_shoucang {
          display: flex;
          flex-direction: column;
          align-items: center;
          cursor: pointer;
          user-select: none;

          .title_Icon_shoucang_Icon {
            display: inline-block;
            width:22px;
            height:22px;
            background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_title_collect_Icon.png') no-repeat;
            background-size: cover;
            margin-bottom: 7px;
          }
          .title_Icon_shoucang_Icon_text {
            font-size: 12px;
            font-weight: 400;
            color: #FFFFFF;
            line-height: 12px;
          }

          .title_Icon_shoucang_Icon_text_active {
            color: #FFFFFF;
          }

          .title_Icon_shoucang_Icon_active {
            width: 22px;
            height: 22px;
            background: url("../../../../assets/PlanetChatRoom/SpatialDetail_Collect_btn_Open.png") no-repeat;
            background-size: cover;
          }
        }
        .title_Icon_dakai {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-left: 25px;
          cursor: pointer;
          user-select: none;

          .title_Icon_dakai_Icon {
            display: inline-block;
            width:22px;
            height:22px;
            background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_title_clockin_Icon.png') no-repeat;
            background-size: cover;
          }

          .title_Icon_dakai_Icon_active {
            display: inline-block;
            width:22px;
            height:22px;
            background: url('../../../../assets/PlanetChatRoom/SpatialDetail_ClockIn_btn_complete.png') no-repeat;
            background-size: cover;
          }

          .title_Icon_shoucang_Icon_text {
            font-size: 12px;
            font-weight: 400;
            color: #fff;
            line-height: 12px;
            margin-top: 7px;
          }

          .title_Icon_shoucang_Icon_text_active {
            color: #fff;
          }
        }

        .title_Icon_Share {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-left: 15px;
          cursor: pointer;
          user-select: none;
          .title_Icon_Share_Icon {
            display: inline-block;
            width:20px;
            height:20px;
            background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_title_share_Icon.png') no-repeat;
            background-size: cover;
          }
          .title_Icon_Share_Icon_text {
            font-size: 12px;
            font-weight: 400;
            color: #fff;
            line-height: 16px;
            margin-top: 7px;
          }
        }
      }
    }
  }


  .video_ModeratorControl_VerticalLiveRoom {
    position: absolute;
    bottom: 0px;
    width: 100%;
    height: 48px;
    background: linear-gradient(180deg, rgba(0,0,0,0) 0%, #000000 100%);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 10px;
    padding-right: 10px;
    user-select: none;

    .PlayCircleOutlined_live {
      width: 17px;
      height: 17px;
      font-size: 17px;
      user-select: none;
      cursor: pointer;
      color: #fff;
    }

    .finish_live {
      width: 17px;
      height: 17px;
      display: inline-block;
      background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_close_live_btn.png') no-repeat;
      background-size: 17px 17px;
    }

    .video_ModeratorControl_box {
      // min-width: 50px;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;

      .video_ModeratorControl_box_icon {
        // width: 32px;
        height: 22px;
        // background: #fff;
        // border-radius:50%;
        margin-bottom: 4px;
      }
      .video_ModeratorControl_box_text {
        color: #fff;
      }
      .video_ModeratorControl_box_text_Blue {
        color: #9ECEFF;
      }
      .video_ModeratorControl_box_text_red {
        color: #FF7F67
      }
    }

    /*pre {
      font-family:'arial';
      line-height:auto;
    }*/

    .video_ModeratorControl_box_right {
      float: right;
    }



    .video_Progress_bar_warp {
      display: flex;
      align-items: center;
      flex: 1;
    }
    .video_Progress_bar {
      flex: 1;
    }
    .time_Progress {
      font-size: 12px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 12px;
      position: relative;
    }

    .FullScreenBtn {
      cursor: pointer;
      user-select: none;
      width: 14px;
      height: 14px;
      display: inline-block;
      background: url('../../../../assets/PlanetChatRoom/SpatialDetail_Full_screen_btn.png') no-repeat;
      background-size: 14px 14px;
      margin-left: 14px;
    }

    .FullScreenBtn_p {
      cursor: pointer;
      user-select: none;
      width: 14px;
      height: 14px;
      display: inline-block;
      background: url('../../../../assets/PlanetChatRoom/SpatialDetail_PictureInPicture_btn.png') no-repeat;
      background-size: 14px 14px;
      margin-left: 14px;
      // background: ;
    }
  }



  .video_ModeratorControl {
    width: 100%;
    height: 100px;
    background: linear-gradient(180deg, rgba(0,0,0,0) 0%, #000000 100%);
    padding-left: 20px;
    padding-right: 20px;
    position: absolute;
    bottom: 0px;

    .video_Progress_bar_warp {
      width: 100%;
      // height: 22px;
      display: flex;
      font-size: 12px;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 14px;
      align-items: center;
      padding-bottom: 12px;
    }

    .video_Progress_bar_time_left {
      font-size: 12px;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 14px;
      padding-right: 8px;
    }

    .video_Progress_bar_time_right {
      font-size: 12px;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 14px;
      padding-left: 8px;
    }

    .video_Progress_bar {
      flex: 1;
      :global {
        .ant-slider-rail,.ant-slider-track {
          height: 2px;
        }
        .ant-slider-rail{
          background-color: rgba(255,255,255,0.5);
        }
        .ant-slider-handle{
          width:10px;
          height:10px;
        }
      }
    }
    .video_control_btn_warp {
      display: flex;
      justify-content: space-between;
      height: 40px;
      width: 100%;

      .TiemText {
        font-weight: 400;
        color: #FFFFFF;
        font-size: 8px;
        opacity: 1;
      }
      .video_control_btn_left {
        display: flex;
        align-items: flex-start;
        flex: 1;
        padding-right: 16px;
      }

      .video_control_btn_left_PC {
        width: 22%;
        max-width:200px;
        display: flex;
        justify-content: flex-start;
        padding-right: 16px;
        align-items: center;
      }


      .video_control_btn_middle_PC {
        display: flex;
        width: 66%;
        max-width:550px;
        align-items: center;
        justify-content: center;

      }

      .video_control_btn_right_PC {
        width: 22%;
        max-width:200px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }

      .video_control_btn_right {
        display: flex;
        // padding-top:4px;
      }

      .btn_content_warp_mid_PC {
        margin-left: 8px;
        margin-right: 8px;
      }

      .video_control_btn_right_currenMode {
        justify-content: space-between;
        margin-bottom: 10px;
      }

      .HorizontalLiveRoom_shared_screen_btn {
        width: 32px;
        height: 32px;
        padding-top:24px;
        font-size: 8px;
        color: #FFFFFF;
        text-align: center;
        background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_shared_screen_btn.png') no-repeat;
        background-size: 22px 22px;
        opacity: 1;
        user-select: none;
        cursor: pointer;
        margin-right: 10px;
      }

      .HorizontalLiveRoom_shared_screen_active_btn {
        width: 22px;
        height: 22px;
        padding-top:24px;
        font-size: 8px;
        color: #FFFFFF;
        text-align: center;
        background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_shared_screen_active_btn.png') no-repeat;
        background-color: rgba(42, 169, 248, 0.50);
        background-size: 22px 22px;
        border-radius: 50%;
        opacity: 1;
        user-select: none;
        cursor: pointer;
        margin-right: 10px;
      }

      .HorizontalLiveRoom_record_btn {
        width: 22px;
        height: 32px;
        padding-top:24px;
        font-size: 8px;
        color: #FFFFFF;
        text-align: center;
        background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_record_btn.png') no-repeat;
        background-size: 22px 22px;
        opacity: 1;
        user-select: none;
        cursor: pointer;
        margin-right: 16px;
        border-radius: 50%;
      }

      .HorizontalLiveRoom_record_btn_active {
        background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_record_active_btn.png') no-repeat;
        background-size: 22px 22px;
      }


      .HorizontalLiveRoom_send_clap_btn {
        width: 22px;
        height: 32px;
        padding-top:24px;
        font-size: 8px;
        color: #FFFFFF;
        text-align: center;
        background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_send_clap_btn.png') no-repeat;
        background-size: 22px 22px;
        opacity: 1;
        user-select: none;
        cursor: pointer;
        margin-right: 12px;
      }

      .HorizontalLiveRoom_send_flowers_btn {
        width: 22px;
        height: 32px;
        padding-top:24px;
        font-size: 8px;
        color: #FFFFFF;
        text-align: center;
        background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_send_flowers_btn.png') no-repeat;
        background-size: 22px 22px;
        opacity: 1;
        user-select: none;
        cursor: pointer;
        margin-right: 12px;
      }

      .HorizontalLiveRoom_cancel_full_screen_btn {
        width: 22px;
        height: 32px;
        padding-top:24px;
        font-size: 8px;
        color: #FFFFFF;
        text-align: center;
        background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_cancel_full_screen_btn.png') no-repeat;
        background-size: 22px 22px;
        opacity: 1;
        user-select: none;
        cursor: pointer;
        margin-right: 12px;
      }

      .HorizontalLiveRoom_lien_mai_btn {
        height: 40px;
        padding-left: 14px;
        padding-right: 14px;
        background: #FF5F57;
        border-radius: 20px 20px 20px 20px;
        opacity: 1;
        display:flex;
        align-items: center;
        justify-content: center;
        color: #FFFFFF;
        line-height: 16px;
        user-select: none;
        cursor: pointer;
      }

      .HorizontalLiveRoom_apply_connect_Icon {
        width: 12px;
        height: 15px;
        display: inline-block;
        background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_apply_connect_Icon.png') no-repeat;
        background-size: 12px 15px;
        opacity: 1;
        user-select: none;
        cursor: pointer;
      }

      .playBtn {
        width: 32px;
        height: 32px;
        background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_play_Icon.png') no-repeat;
        background-size: cover;
        opacity: 1;
        user-select: none;
        cursor: pointer;
        margin-right: 24px;
      }

      .PauseBtn {
        width: 32px;
        height: 32px;
        background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_Pause_Icon.png') no-repeat;
        background-size: cover;
        opacity: 1;
        user-select: none;
        cursor: pointer;
        margin-right: 24px;
      }



      // 结束直播按钮
      .finish_live {
        width: 22px;
        height: 22px;
        padding-top:24px;
        font-size: 8px;
        color: #FFFFFF;
        text-align: center;
        background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_close_live_btn.png') no-repeat;
        background-size: 22px 22px;
        opacity: 1;
        user-select: none;
        cursor: pointer;
        margin-right: 12px;
      }


      .inputWarp {
        flex: 1;
        position: relative;
        .input {
          width: 100%;
          height: 32px;
          font-size: 13px;
          color: #fff;
          background: rgba(255, 255, 255, 0.3);
          border-radius: 20px 20px 20px 20px;
          opacity: 1;
          border: 1px solid rgba(255, 255, 255, 0.3);
          padding-left: 16px;
          padding-right: 16px;
        }
        .input::placeholder {
          color: #fff;
        }
        .input:focus-visible {
          border: none;
          outline: -webkit-focus-ring-color none;
        }
        .OpenDanmuBtn {
          width: 20px;
          height: 20px;
          background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_open_danmu_Icon.png') no-repeat;
          background-size: cover;
          opacity: 1;
          user-select: none;
          cursor: pointer;
          margin-right: 24px;
          position: absolute;
          right: 0;
          top:7px;
          z-index: 2;
        }

        .OpenDanmuBtnHidden {
          width: 20px;
          height: 20px;
          background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_hidden_danmu_Icon.png') no-repeat;
          background-size: cover;
          opacity: 1;
          user-select: none;
          cursor: pointer;
          margin-right: 24px;
          position: absolute;
          right: 0;
          top:7px;
          z-index: 5;
        }
      }

      .video_ModeratorControl_box {
        // min-width: 50px;
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        margin-right:10px;

        .video_ModeratorControl_box_icon {
          // width: 32px;
          height: 22px;
          margin-bottom: 4px;
          // background: #fff;
          // border-radius:50%;
        }
        .video_ModeratorControl_box_text {
          color: #fff;
        }
        .video_ModeratorControl_box_text_Blue {
          color: #9ECEFF;
        }
        .video_ModeratorControl_box_text_red {
          color: #FF7F67
        }
      }

      // 静音
      .SpatialDetail_SoundOff_btn {
        width: 22px;
        height: 22px;
        display: inline-block;
        background: url('../../../../assets/PlanetChatRoom/SpatialDetail_SoundOff_btn.png') no-repeat;
        background-size: 22px 22px;
      }

      .SpatialDetail_SoundOff_btn_Forbidden {
        width: 22px;
        height: 22px;
        display: inline-block;
        background: url('../../../../assets/PlanetChatRoom/SpatialDetail_SoundOff_btn_Forbidden.png') no-repeat;
        background-size: 22px 22px;
      }

      // 关闭摄像头
      .SpatialDetail_Camera_btn {
        width: 22px;
        height: 22px;
        display: inline-block;
        background: url('../../../../assets/PlanetChatRoom/SpatialDetail_Camera_btn.png') no-repeat;
        background-size: 22px 22px;
      }

      .SpatialDetail_Camera_btn_off {
        width: 22px;
        height: 22px;
        display: inline-block;
        background: url('../../../../assets/PlanetChatRoom/SpatialDetail_Forbidden.png') no-repeat;
        background-size: 22px 22px;
      }


    }

    .video_control_btn_warp_currentWatchModeBy2 {
      flex-direction: column;
    }

    .video_control_btn_warp_PC {
      justify-content: space-between;
    }
  }

  .video_ModeratorControl_PC {
    height: 60px;
  }


.follow_btn {
  width: 76px;
  height: 32px;
  background: rgba(255,255,255,0.3);
  border-radius: 18px 18px 18px 18px;
  opacity: 1;
  position: absolute;
  top: 100px;
  padding-left: 4px;
  font-size: 12px;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 14px;
  display: flex;
  align-items: center;
  user-select: none;
  cursor: pointer;

  .avatar {
    width: 24px;
    height: 24px;
    background: #F9B4E3;
    border-radius: 50%;
    margin-right: 4px;

    .video_Title_box_left_avatar {
      width: 24px;
      height: 24px;
      background: #F9B4E3;
      opacity: 1;
      border-radius: 50%;
      // margin-right: 8PX;
      overflow: hidden;
    }

    .video_Title_box_left_avatar_img {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      overflow: hidden;
    }

    .head_sculpture_name {
      width: 24px;
      height: 24px;
      font-size: 12px;
      font-weight: 500;
      color: #FFFFFF;
      display: flex;
      align-items: center;
      justify-content: center;
      white-space: nowrap;
    }
  }
}

// 申请连麦列表
.ApplyConnectLiveListWarp {
  width: 240px;
  height: 100%;
  background: rgba(0,0,0,0.86);
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  position: absolute;
  top: 0px;
  right: 0px;
  padding-top: 20px;
  padding-bottom: 20px;
  padding-left: 16px;
  padding-right: 16px;
  z-index: 2;

  .ApplyConnectLiveList_title {
    font-size: 15px;
    font-weight: 500;
    color: #FFFFFF;
    line-height: 18px;
    margin-bottom: 19px;
  }

  .ApplyConnectLiveList_header {
    font-size: 15px;
    font-weight: 500;
    color: #FFFFFF;
    line-height: 18px;
    margin-bottom: 19px;
    display: flex;

    .header_tab_item {
      cursor: pointer;
      user-select: none;
      margin-right: 5px;
    }
    .header_tab_item_active {
      color: #0095FF;
    }

    .tab_item_num {
      position: relative;
      width: 12px;
      height: 12px;
      background: #FF180D;
      border-radius: 14px 14px 14px 14px;
      display: inline-block;
      font-weight: 400;
      font-size: 9px;
      color: #FFFFFF;
      line-height: 12px;
      text-align: center;
      top: -6px;
      right: 3px;
    }

  }

  .ApplyConnectLiveList_noData {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #999999;
    height: 100%;

    .ApplyConnectLiveList_noData_icon_warp {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }

    .ApplyConnectLiveList_noData_icon {
      width: 40px;
      height: 40px;
      background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_ApplyConnectLiveList_noData_icon.png') no-repeat;
      background-size: 40px 40px;
      opacity: 1;
      user-select: none;
      cursor: pointer;
      margin-bottom: 8px;
    }
  }

  .ApplyConnectLiveList {
    width: 100%;
    height: 100%;
    overflow-y: auto;


    .signInItem {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
      align-items: center;
    }

    .signInItemRight {
      color: #666;
    }

    .signInItemLeft {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .signInItemLeftItem_Avatar {
        width: 24px;
        height: 24px;
        overflow: hidden;
      }

      .signInItemLeftInfo {
        .signInItemLeftInfoName {
          font-size: 14px;
          font-weight: 400;
          color: #FFFFFF;
        }
        .signInItemLeftInfoTime {
          font-size: 10px;
          color: #666;
          line-height: 12px;
        }
      }
    }

    // 申请连麦item
    .ApplyConnectLiveItem {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
      align-items: center;
    }



    .ApplyConnectLiveLeft {
      display: flex;
      align-items: center;

      .ApplyConnectLiveItem_avatar {
        width: 24px;
        height: 24px;
        background: #F9B4E3;
        border-radius: 50%;
        margin-right: 12px;

        .GuestlistItemAvatar_img {
          width: 24px;
          height: 24px;
          border-radius:50%;
          overflow: hidden;
        }
      }
      .ApplyConnectLiveItem_name {
        max-width: 60px;
        overflow:hidden;
        text-overflow:ellipsis;
        white-space:nowrap;
        font-size: 14px;
        font-weight: 400;
        color: #FFFFFF;
      }



      // 强制下麦
      .forceXiamai {
        padding-left: 8px;
        padding-right: 8px;
        height: 21px;
        line-height: 21px;
        border-radius: 14px 14px 14px 14px;
        color: #FFF;
        background: #FF5F57;
        position: relative;
        top: 3px;
        cursor: pointer;
        user-select: none;
      }

    }

    // 接受
    .ApplyConnectLiveList_receive_btn {
      height: 21px;
      line-height: 21px;
      border-radius: 14px 14px 14px 14px;
      padding-left: 8px;
      padding-right: 8px;
      font-weight: 400;
      color: #0095FF;
      background-color: #EDF9FF;
      user-select: none;
      cursor: pointer;
    }

    // 强制下麦
    .ApplyConnectLiveList_force_Cancel_btn {
      height: 21px;
      line-height: 21px;
      border-radius: 14px 14px 14px 14px;
      padding-left: 8px;
      padding-right: 8px;
      font-weight: 400;
      color: #fff;
      background-color: #FF5F57;
      user-select: none;
      cursor: pointer;
    }

    // 参与嘉宾列表
    // 取消关注
    .ApplyConnectLiveList_unfollow_btn {
      height: 21px;
      line-height: 21px;
      border-radius: 14px 14px 14px 14px;
      padding-left: 8px;
      padding-right: 8px;
      font-weight: 400;
      color: #CCCCCC;
      background-color: #F5F5F5;
      user-select: none;
      cursor: pointer;
    }

  }

  // 设置
  .ApplySetList {
    width: 100%;
    height: 100%;

    .ApplySetItem {
      font-size: 14px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 14px;
      display: flex;
      justify-content: space-between;
      margin-top: 22px;

      .ApplySetItem_title {
        display: inline-block;
      }
    }
  }

}


.isHiddenControlArea {
  display: none !important;
}
.isShowApplyForLinkMicList {
  display: none !important;
}

.ApplyConnectWheat {
  height: 40px;
  padding-left: 14px;
  padding-right: 14px;
  background: #0095FF;
  border-radius: 20px 20px 20px 20px;
  opacity: 1;
  display:flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  line-height: 16px;
  user-select: none;
  cursor: pointer;
}

.CancelApplyConnectWheat {
  background: #FF5F57;
}

.lianmaiWarp {
  display: flex;
  align-items: center;
  .SpatialDetaiLianmaiBtnIcon {
    width: 16px;
    height: 16px;
    margin-right: 4px;
    background: url('../../../../assets/PlanetChatRoom/SpatialDetai_lianmai_Btn.png') no-repeat;
    background-size: 16px 16px;
    display: inline-block;
  }
  .SpatialDetaiLianmaiBtnIconCancel {
    width: 16px;
    height: 16px;
    margin-right: 4px;
    background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_UnApply_connect_Icon.png') no-repeat;
    background-size: 16px 16px;
    display: inline-block;
  }
}

.CameraItem {

  .isModeMatrix_camera_picture_camera_item {
    width: 100%;
    height: 100%;
    background: #efefef;
    border-radius: 0px 0px 0px 0px;
    opacity: 1;
    position: relative;
    // margin-bottom: 3px;

    .HorizontalLiveRoom_camera_picture_camera_bottom_box {
      width: 100%;
      height: 24px;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 100%);
      border-radius: 0px 0px 0px 0px;
      opacity: 1;
      position: absolute;
      bottom: 0px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
      color: #FFFFFF;
      line-height: 24px;
      padding-left: 8px;
      padding-right: 8px;
    }

    .HorizontalLiveRoom_camera_picture_camera_bottom_box_lianmai {
      background: rgba(255, 255, 255, 0.8);
    }

    .HorizontalLiveRoom_camera_picture_camera_bottom_box_Name_Warp {
      display: flex;
      align-items: center;
    }

    .HorizontalLiveRoom_camera_picture_camera_bottom_box_Name {
      max-width: 50px;
      height: 24px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .HorizontalLiveRoom_camera_picture_camera_bottom_box_Name_lianmai {
      color: #000000;
    }

    .HorizontalLiveRoom_camera_picture_mic_icon {
      width: 10px;
      height: 12px;
      background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_camera_picture_mic_icon.png') no-repeat;
      background-size: 10px 12px;
      display: inline-block;
      margin-left: 4px;
    }

    .HorizontalLiveRoom_camera_picture_forbidden_mic_icon {
      width: 10px;
      height: 12px;
      background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_camera_picture_forbidden_mic_icon.png') no-repeat;
      background-size: 10px 12px;
      display: inline-block;
      margin-left: 4px;
    }

    .HorizontalLiveRoom_camera_picture_forbidden_Lianmai {
      width: 10px;
      height: 12px;
      background: url('../../../../assets/PlanetChatRoom/SpatialDetail_MicrophoneOn_btn_Lianmai.png') no-repeat;
      background-size: 10px 12px;
      display: inline-block;
      margin-left: 4px;
    }

    .GuanZhu_btn {
      cursor: pointer;
      user-select: none;
      padding-top: 2px;
      padding-bottom: 2px;
      padding-left: 4px;
      padding-right: 4px;
      font-size: 12px;
      font-weight: 400;
      color: #0095FF;
      line-height: 12px;
      background: #EDF9FF;
      border-radius: 14px 14px 14px 14px;
      opacity: 1;
    }

    .unGuanZhu_btn {
      color: #CCCCCC;
      background: #F5F5F5;
    }

    // 强制下麦按钮
    .forceXiaMai {
      cursor: pointer;
      user-select: none;
      padding-top: 2px;
      padding-bottom: 2px;
      padding-left: 4px;
      padding-right: 4px;
      font-weight: 400;
      color: #FFFFFF;
      font-size: 12px;
      line-height: 12px;
      background: #FF5F57;
      border-radius: 12px 12px;
      opacity: 1;
      white-space: nowrap;
    }

  }


  .isModeMatrix_camera_picture_camera_item_hidden {
    // height: 0px;
    overflow: hidden;
  }

  .headUrlWarp {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .video_Title_box_left_avatar {
    width: 44px;
    height: 44px;
    background: #F9B4E3;
    opacity: 1;
    border-radius: 50%;
    // margin-right: 8PX;
    overflow: hidden;
  }

  .video_Title_box_left_avatar_img {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    overflow: hidden;
  }

  .head_sculpture_name {
    width: 44px;
    height: 44px;
    font-size: 16px;
    font-weight: 500;
    color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
  }

  .StreamWarp {
    width: 100%;
    height: 100%;
  }

  .StreamWarpHidden {
    height: 0px;
    overflow: hidden;
  }
}



/* 竖屏-嘉宾摄像头列表样式*/
.HorizontalLiveRoom_camera_picture_Box_VerticalLiveRoom {
  position: absolute;
  width: 97px;
  height: 100%;
  border-radius: 0px 0px 0px 0px;
  top: 0px;
  right: -82px;
  opacity: 1;
  z-index: 2;
  overflow-y: auto;

  .HorizontalLiveRoom_camera_picture_btn {
    width: 15px;
    height: 71px;
    background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_camera_picture_btn.png') no-repeat;
    background-size: 15px 71px;
    display: block;
    position: absolute;
    top: 50%;
    left: 0px;
    transform: translateY(-50%);
    user-select: none;
    cursor: pointer;
    z-index: 1;
  }

  .HorizontalLiveRoom_camera_picture_take_back_btn {
    background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_camera_picture_take_back_btn.png') no-repeat;
    background-size: 15px 71px;
  }

  .HorizontalLiveRoom_camera_picture_camera_live {
    width: 82px;
    height: 100%;
    background: #222;
    opacity: 1;
    overflow-y: auto;
    position: absolute;
    right: 0px;
    display: flex;
    flex-direction: column;

    .noGuestsListWarp {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .noGuestsListIcon {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }
      .noGuestsIcon {
        width: 24px;
        height: 24px;
        display: inline-block;
        background: url('../../../../assets/PlanetChatRoom/SpatialDetail_no_guests_list_icon.png') no-repeat;
        background-size: 24px 24px;
        margin-bottom: 10px;
        border: none;
      }
      .noGuestsText {
        font-size: 12px;
        font-weight: 400;
        color: #AAAAAA;
        line-height: 12px;
      }
    }

    .HorizontalLiveRoom_camera_picture_camera_item {
      width: 82px;
      height: 50px;
      background: #efefef;
      border-radius: 0px 0px 0px 0px;
      opacity: 1;
      position: relative;
      // margin-bottom: 3px;

      .HorizontalLiveRoom_camera_picture_camera_bottom_box {
        width: 100%;
        height: 24px;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 100%);
        border-radius: 0px 0px 0px 0px;
        opacity: 1;
        position: absolute;
        bottom: 0px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: #FFFFFF;
        line-height: 24px;
        padding-left: 8px;
        padding-right: 8px;
      }

      .HorizontalLiveRoom_camera_picture_camera_bottom_box_lianmai {
        background: rgba(255,255,255,0.8);
      }

      .HorizontalLiveRoom_camera_picture_camera_bottom_box_Name_Warp {
        display: flex;
        align-items: center;
      }

      .HorizontalLiveRoom_camera_picture_camera_bottom_box_Name {
        max-width: 50px;
        height: 24px;
        overflow:hidden;
        text-overflow:ellipsis;
        white-space:nowrap;
      }

      .HorizontalLiveRoom_camera_picture_camera_bottom_box_Name_lianmai {
        color: #000000;
      }

      .HorizontalLiveRoom_camera_picture_mic_icon {
        width: 10px;
        height: 12px;
        background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_camera_picture_mic_icon.png') no-repeat;
        background-size: 10px 12px;
        display: inline-block;
        margin-left: 4px;
      }

      .HorizontalLiveRoom_camera_picture_forbidden_mic_icon {
        width: 10px;
        height: 12px;
        background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_camera_picture_forbidden_mic_icon.png') no-repeat;
        background-size: 10px 12px;
        display: inline-block;
        margin-left: 4px;
      }

      .HorizontalLiveRoom_camera_picture_forbidden_Lianmai {
        width: 10px;
        height: 12px;
        background: url('../../../../assets/PlanetChatRoom/SpatialDetail_MicrophoneOn_btn_Lianmai.png') no-repeat;
        background-size: 10px 12px;
        display: inline-block;
        margin-left: 4px;
      }

      .GuanZhu_btn {
        cursor:pointer;
        user-select:none;
        padding-top: 2px;
        padding-bottom: 2px;
        padding-left: 4px;
        padding-right: 4px;
        font-size: 12px;
        font-weight: 400;
        color: #0095FF;
        line-height: 12px;
        background: #EDF9FF;
        border-radius: 14px 14px 14px 14px;
        opacity: 1;
      }

      .unGuanZhu_btn {
        color: #CCCCCC;
        background: #F5F5F5;
      }

      // 强制下麦按钮
      .forceXiaMai {
        cursor:pointer;
        user-select:none;
        padding-top: 2px;
        padding-bottom: 2px;
        padding-left: 4px;
        padding-right: 4px;
        font-weight: 400;
        color: #FFFFFF;
        font-size: 12px;
        line-height: 12px;
        background: #FF5F57;
        border-radius: 12px 12px;
        opacity: 1;
        white-space:nowrap;
      }

    }
    .HorizontalLiveRoom_camera_picture_camera_item_hidden {
      // height: 0px;
      overflow: hidden;

      .headUrlWarp {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .video_Title_box_left_avatar {
        width: 32px;
        height: 32px;
        background: #F9B4E3;
        opacity: 1;
        border-radius: 50%;
        // margin-right: 8PX;
        overflow: hidden;
      }

      .video_Title_box_left_avatar_img {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        overflow: hidden;
      }

      .head_sculpture_name {
        width: 32px;
        height: 32px;
        font-size: 16px;
        font-weight: 500;
        color: #FFFFFF;
        display: flex;
        align-items: center;
        justify-content: center;
        white-space: nowrap;
      }
    }

    .StreamWarp {
      width: 100%;
      height: 100%;
    }

    .StreamWarpHidden {
      height: 0px;
      overflow: hidden;
    }
  }
}

.sketch_wrap {
  width: 100%;
  height:100vh;
  background: #7ec1ff;
}

.btn_control_Warp {
  margin-right: 12px;
}

.playBtn {
  width: 14px;
  height: 14px;
  background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_play_Icon.png') no-repeat;
  background-size: cover;
  opacity: 1;
  user-select: none;
  cursor: pointer;
  margin-right: 14px;
  cursor: pointer;
  user-select: none;
}

.PauseBtn {
  width: 14px;
  height: 14px;
  background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_Pause_Icon.png') no-repeat;
  background-size: cover;
  opacity: 1;
  user-select: none;
  cursor: pointer;
  margin-right: 14px;
  cursor: pointer;
  user-select: none;
}



.video_HorizontalLiveRoom_goPlay {
  width: 100%;
  height: 60vh;
  top: 20%;
}

.video_HorizontalLiveRoom_goPlay_isHorizontalLive {
  width: 100%;
  height: 100%;
  top: 0%;
}

.video_HorizontalLiveRoom_goPlayBox {
  position: absolute;
  z-index: 1;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video_Play_Icon {
  width: 68px;
  height: 68px;
  background: url('../../../../assets/PlanetChatRoom/HorizontalLiveRoom_video_Play_Icon.png') no-repeat;
  background-size: cover;
  user-select: none;
  cursor: pointer;
}

// 拒绝
.refuse {
  padding-left: 8px;
  padding-right: 8px;
  height: 21px;
  line-height: 21px;
  border-radius: 14px 14px 14px 14px;
  color: #FF5F57;
  background: #FFF1F1;
  position: relative;
  top: 3px;
  cursor: pointer;
  user-select: none;
  margin-right: 8px;
}

.refuse:active {
  background: #fbd5d5;
}

// 准入
.allow {
  padding-left: 8px;
  padding-right: 8px;
  height: 21px;
  line-height: 21px;
  border-radius: 14px 14px 14px 14px;
  color: #0095FF;
  background: #EDF9FF;
  position: relative;
  top: 3px;
  cursor: pointer;
  user-select: none;
}

.allow:active {
  background: #c8ebfd;
}

.GuestlistItemRigth_ApplicationMeeting {
  display: flex;
}

