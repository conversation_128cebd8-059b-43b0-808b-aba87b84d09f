/**
 * @Description: pdf预览弹窗
 */
import React from 'react'
import { Modal } from 'antd'
import styles from './index.less'

interface PropsType {
  loading: any;
  dispatch: any;
  DigitalHealth: any;
  visible: boolean,
  pdfUrl: any,
  onCancel: any;
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {
    visible,
    pdfUrl, // PDF地址
    onCancel, // 关闭弹窗
  } = props

  return (
    <Modal
      visible={visible}
      className={styles.modal}
      destroyOnClose={true}
      footer={null}
      onCancel={onCancel}
    >
      <iframe src={pdfUrl} frameborder="0" width={'100%'} height={'100%'}></iframe>
    </Modal>
  )
}

export default Index

