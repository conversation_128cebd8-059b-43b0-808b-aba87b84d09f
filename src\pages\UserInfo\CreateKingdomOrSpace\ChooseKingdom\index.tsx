/**
 * 创建空间-选择关联王国列表 （未有我创建的、我加入的王国时，不跳转此页面）
 * 父组件方法跳转调用 goBack(20) 跳转到此弹框
 */
import { Spin } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import { connect } from 'umi';
import { processNames, randomColor } from '@/utils/utils';
import classNames from 'classnames';
import styles from './index.less';
import goBackIcon from '@/assets/GlobalImg/go_back.png'; // 返回按钮小图标
import NoDataRender from '@/components/NoDataRender'; // 暂无数据组件

const Index:React.FC = (props:any) => {
  const { loading, dispatch, userInfoStore } = props || {};
  const { selectedKingdom } = userInfoStore || {}; // 仓库储存状态
  const [createKingdomList, setCreateKingdomList] = useState([]); // 创建的王国数据
  const [joinKingdomList, setJoinKingdomList] = useState([]); // 加入的王国数据
  const [kingSpaceTab, setKingSpaceTab] = useState(1); // 我创建/我加入的王国tab
  const [currenSelectItem, setCurrenSelectItem] = useState(null); // 当前选中的王国

  // 获取创建王国或加入王国相关数据
  const getInitDate = useCallback(() => {
    dispatch({
      type: 'userInfoStore/getCreateAndJoinKingdomList',
    }).then(res => {
      const {code, content} = res || {};
      if(res && code == 200) {
        setCreateKingdomList(content && content[1]);
        setJoinKingdomList(content && content[2]);
      }
    }).catch(() => {})
  }, [dispatch])

  useEffect(() => {
    getInitDate()
    setCurrenSelectItem(selectedKingdom)
  }, [getInitDate])


  // 王国空间选中事件
  const checkKingHandle = (item:any) => {
    setCurrenSelectItem(item)
  }

  // 头部返回事件
  const goBackFn = () => {
    props.goBack(1)
  }

  // 确定按钮
  const submitFn = () => {
    const { selectCreateType,creatTabSelectDate } = userInfoStore || {}; // 从仓库中取值
    if(currenSelectItem!=null){
      // 创建关联王国的空间
      dispatch({
        type: 'userInfoStore/setTaskListState',
        payload: {
          spaceFromEnter: {
            isCureentKingdom: true // 是否是当前指定王国
          },
          selectedKingdomAudience: currenSelectItem?.id ? [currenSelectItem?.id] : [], // 指定当前王国所有成员，并将当前王国的id保存到store中
          selectedKingdom: currenSelectItem, // 已选择-王国
          selectCreateType: selectCreateType, // 创建空间-类型
          creatTabSelectDate:creatTabSelectDate
        }
      })
    }else{
      // 创建开放空间
      dispatch({
        type: 'userInfoStore/setTaskListState',
        payload: {
          selectedKingdom: currenSelectItem, // 已选择-王国
          selectCreateType: selectCreateType, // 创建空间-类型
          creatTabSelectDate:creatTabSelectDate
        }
      })
    }
    props.goBack(1);
  }

  const getCreateAndJoinKingdomListLoading = !!loading.effects['userInfoStore/getCreateAndJoinKingdomList'];
  const renderKingDomList: any[] = kingSpaceTab == 1 ? createKingdomList : joinKingdomList; // 根据tab切换，来展示加入或创建王国相应的列表

  return <>
    <div className={styles.titleInfo}>
      <div className={styles.titleWarp}>
        <div className={styles.titleBackIcon} onClick={goBackFn}><img src={goBackIcon} alt="" /></div>
        <div className={styles.titleText}>选择关联王国</div>
      </div>
    </div>
    <Spin spinning={getCreateAndJoinKingdomListLoading}>
      <div className={styles.data_box}>
        <div className={styles.tabs_box}>
          <div className={classNames({[styles.tabs_item]: true, [styles.checked]: kingSpaceTab == 1})} onClick={()=>{setKingSpaceTab(1)}}>我创建的</div>
          <div className={classNames({[styles.tabs_item]: true, [styles.checked]: kingSpaceTab == 2})} onClick={()=>{setKingSpaceTab(2)}}>我加入的</div>
        </div>
        <div className={styles.list_wrap}>
          {
            renderKingDomList ?
            renderKingDomList.map(item =>
            <div className={styles.data_item} key={item.id}>
              <div className={styles.avatar}>
                {item.kingdomCoverUrlShow || item.kingImgUrlShow ?
                  <img src={item.kingdomCoverUrlShow || item.kingImgUrlShow} alt="" /> :
                  <div className={styles.no_comment_head} style={{background:randomColor(item.wxUserId)}}><span>{processNames(item?.kingName)}</span></div>
                }
              </div>
              <div className={styles.data_name}>{item.name}</div>
              <div className={classNames({
                  [styles.data_btn]: true,
                  [styles.checked]: currenSelectItem?.id === item.id
                })}
                onClick={() => checkKingHandle(item)}
              >{currenSelectItem?.id === item.id ? '已选择' : '选择'}</div>
            </div>) :
            <NoDataRender/>
          }
        </div>
      </div>
    </Spin>
    <div className={styles.fixed_box}>
      <div className={styles.btn_box}>
        <div className={styles.btn} onClick={submitFn}>确定</div>
      </div>
    </div>
  </>
}
export default connect(({ userInfoStore, loading }: any) => ({userInfoStore, loading}))(Index)
