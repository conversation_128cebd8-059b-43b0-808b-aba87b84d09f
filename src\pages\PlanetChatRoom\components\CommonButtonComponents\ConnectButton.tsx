/* 开启连麦按钮 */
// ConnectButton.js
import React from 'react';
import classNames from 'classnames';
import styles from './index.less';  // 引入自定义样式

const ConnectButton = ({
                         resetTimer,
                         handUpType,
                         openCloseHandUp,
                         setContentListType,
                         starSpaceType,
}) => {
  /* 空间类型 1-直播 2-会议 */
  const handleConnectClick = (e) => {
    e.stopPropagation();
    resetTimer();

    if (handUpType === 1) {
      openCloseHandUp({ handUpType: 2 });
      // setContentListType(TypeMsgList);
    } else {
      openCloseHandUp({ handUpType: 1 });
    }
  };

  return (
    <div onClick={handleConnectClick} className={styles.HorizontalLiveRoom_Btn_Warp}>
      <div className={styles.video_ModeratorControl_box_icon}>
        <i
          className={classNames({
            [styles.SpatialDetail_openLianMai_btn]: true,
            [styles.SpatialDetail_SoundOff_btn_off_Connection]: handUpType === 1,
          })}
        />
      </div>
      <div
        className={classNames({
          [styles.video_ModeratorControl_box_text]: true,
          [styles.video_ModeratorControl_box_text_Blue]: handUpType === 1,
        })}
      >
      </div>
      <div className={styles.text}>
        <> {starSpaceType == 1 && <>{handUpType === 1 ? "关闭连麦" : "开启连麦"}</> } </>
        <> {starSpaceType == 2 && <>{handUpType === 1 ? "关闭申请发言" : "允许申请发言"}</> } </>
      </div>
    </div>
  );
};

export default ConnectButton;
