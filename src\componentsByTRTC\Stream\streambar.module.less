.network-quality-container {
  height: 26px;
  display: flex;
  align-items: center;
  position: absolute;
  left: 6px;
  top: 4px;
  .arrow {
    width: 14px;
    height: 20px;
  }
  .network-quality {
    height: 18px;
    width: 20px;
    margin-right: 8px;
    display: flex;
    align-items: flex-end;
    justify-content: space-around;
    .network-quality-1 {
      width: 3px;
      height: 20%;
      background-color: #fff;
    }
    .network-quality-2 {
      width: 3px;
      height: 40%;
      background-color: #fff;
    }
    .network-quality-3 {
      width: 3px;
      height: 60%;
      background-color: #fff;
    }
    .network-quality-4 {
      width: 3px;
      height: 80%;
      background-color: #fff;
    }
    .network-quality-5 {
      width: 3px;
      height: 100%;
      background-color: #fff;
    }
    .green {
      background-color: #00FF02;
    }
  }
}

.bar {
  width: 100%;
  height: 40px;
  background: rgba(0,0,0,.5);
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  bottom: 0;
  &-item {
    display: flex;
    align-items: center;
    justify-content: center;
    // svg {
    //   margin: 0 2px;
    // }
  }
  &-item-screen {
    margin-right: 4px;
  }
  &-icon {
    position: absolute;
    justify-content: space-between;
    display: flex;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }
  &-name {
    color: #fff;
    margin: 0 auto;
    max-width: 140px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.local {
  justify-content: flex-end;
}
.pointer {
  cursor: pointer;
}

.audio-volume-container {
  position: relative;
  width: 24px;
  height: 24px;
  .audio-icon {
    position: absolute;
    top: 0;
    left: 0;
  }
  .volume-container {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    overflow: hidden;
    transition: height .1s ease;
    .green-audio-icon {
      position: absolute;
      bottom: 0;
    }
  }
}
