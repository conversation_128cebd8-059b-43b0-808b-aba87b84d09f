.case_container {
  position: relative;
  padding: 0 12px 4px;
  .item_box {
    position: relative;
    font-size: 14px;
    padding-bottom: 16px;
    border-bottom: 1px solid #E1E4E7;
    margin-bottom: 12px;
    .topic {
      font-size: 17px;
      font-weight: 500;
      color: #000000;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-bottom: 8px;
      line-height: 24px;
    }
    .subject_box {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 4px;
      & > span {
        display: block;
        border-radius: 2px;
        background: #EDF9FF;
        height: 21px;
        line-height: 21px;
        padding: 0 4px;
        font-size: 12px;
        font-family:  PingFang SC;
        font-weight: 400;
        color: #0095FF;
        margin-right: 6px;
        margin-bottom: 4px;
        &.achievement {
          background: #FFF7E2;
          color: #D3A221;
        }
        &.difficult1 {
          background: #E6FBF3;
          color: #00D78B;
        }
        &.difficult2 {
          background: #FFF8EC;
          color: #E39D16;
        }
        &.difficult3 {
          background: #FFF4E9;
          color: #FF921F;
        }
        &.difficult4 {
          background: #FCE9E8;
          color: #FF5F57;
        }
      }
    }
    .doctor_box {
      display: flex;
      flex-wrap: nowrap;
      font-family:  PingFang SC;
      font-weight: 400;
      color: #666666;
      margin-bottom: 8px;
      line-height: 17px;
      .label {
        white-space: nowrap;
      }
      .value {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .keywords_box {
      display: flex;
      flex-wrap: nowrap;
      font-size: 14px;
      font-family:  PingFang SC;
      font-weight: 400;
      color: #666666;
      margin-bottom: 4px;
      .label {
        white-space: nowrap;
        line-height: 24px;
      }
      .value_box {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        overflow: hidden;
        & > span {
          display: block;
          height: 24px;
          line-height: 24px;
          padding: 0 4px;
          background: #F5F5F5;
          border-radius: 4px;
          font-size: 14px;
          font-family:  PingFang SC;
          font-weight: 400;
          color: #666666;
          margin-right: 6px;
          margin-bottom: 4px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    .solution_box {
      margin-bottom: 8px;
      padding: 8px;
      position: relative;
      background: #F5F5F5;
      border-radius: 6px;
      .solution_icon {
        position: absolute;
        top: 8px;
        left: 8px;
        display: block;
        width: 57px;
        height: 20px;
        background: url("../../assets/GlobalImg/word_art.png") no-repeat center;
        background-size: 100% 100%;
      }
      .solution_text {
        font-family:  PingFang SC;
        font-weight: 400;
        color: #666666;
        text-indent: 64px;
        word-break: break-all;
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
      }
    }
    .comments {
      font-size: 13px;
      font-family:  PingFang SC;
      font-weight: 400;
      color: #999999;
      line-height: 18px;
    }

    // 图片
    .cover_img_box {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 8px;
      max-width: 420px;
      .cover_img{
        width: 104px;
        min-width: 104px;
        height: 104px;
        overflow: hidden;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        //img {
        //  width: 100%;
        //  height: 100%;
        //}
      }
    }
    .cover_img_length1 {
      .cover_img {
        width: 135px;
        min-width: 135px;
        height: 135px;
        border-radius: 8px;
      }
    }
    .cover_img_length2 {
      .cover_img {
        width: 120px;
        min-width: 120px;
        height: 120px;
      }
      .cover_img:nth-child(1) {
        border-radius: 8px 0 0 8px;
        margin-right: 4px;
      }
      .cover_img:nth-child(2) {
        border-radius: 0 8px 8px 0;
      }
    }
    .cover_img_length3 {
      .cover_img:nth-child(1) {
        border-radius: 8px 0 0 8px;
        margin-right: 4px;
      }
      .cover_img:nth-child(2) {
        margin-right: 4px;
      }
      .cover_img:nth-child(3) {
        border-radius: 0 8px 8px 0;
      }
    }
    .cover_img_length4 {
      width: 250px;
      .cover_img:nth-child(1) {
        border-radius: 8px 0 0 0;
        margin-right: 4px;
      }
      .cover_img:nth-child(2) {
        border-radius: 0 8px 0 0;
      }
      .cover_img:nth-child(3) {
        border-radius: 0 0 0 8px;
        margin-right: 4px;
        margin-top: 4px;
      }
      .cover_img:nth-child(4) {
        border-radius: 0 0 8px 0;
        margin-top: 4px;
      }
    }
    .cover_img_length5 {
      .cover_img:nth-child(1) {
        border-radius: 8px 0 0 0;
        margin-right: 4px;
      }
      .cover_img:nth-child(2) {
        margin-right: 4px;
      }
      .cover_img:nth-child(3) {
        border-radius: 0 8px 8px 0;
      }
      .cover_img:nth-child(4) {
        border-radius: 0 0 0 8px;
        margin-right: 4px;
        margin-top: 4px;
      }
      .cover_img:nth-child(5) {
        border-radius: 0 0 8px 0;
        margin-top: 4px;
      }
    }
    .cover_img_length6 {
      .cover_img:nth-child(1) {
        border-radius: 8px 0 0 0;
        margin-right: 4px;
      }
      .cover_img:nth-child(2) {
        margin-right: 4px;
      }
      .cover_img:nth-child(3) {
        border-radius: 0 8px 0 0;
      }
      .cover_img:nth-child(4) {
        border-radius: 0 0 0 8px;
        margin-right: 4px;
        margin-top: 4px;
      }
      .cover_img:nth-child(5) {
        margin-right: 4px;
        margin-top: 4px;
      }
      .cover_img:nth-child(6) {
        border-radius: 0 0 8px 0;
        margin-top: 4px;
      }
    }
    .cover_img_line3 {
      .cover_img:nth-child(1) {
        border-radius: 8px 0 0 0;
      }
      .cover_img:nth-child(3) {
        border-radius: 0 8px 0 0;
      }
      .cover_img:nth-child(7) {
        border-radius: 0 0 0 8px;
      }
      .cover_img:nth-child(9) {
        border-radius: 0 0 8px 0;
      }

      .cover_img:nth-child(3n + 2) {
        margin-right: 4px;
        margin-left: 4px;
      }
      .cover_img:nth-child(4),
      .cover_img:nth-child(5),
      .cover_img:nth-child(6) {
        margin-top: 4px;
        margin-bottom: 4px;
      }
    }
    .cover_img_length7,
    .cover_img_length8 {
      .cover_img:nth-child(6) {
        border-radius: 0 0 8px 0;
      }
    }
    .cover_img_box {
      .cover_img:last-child {
        border-bottom-right-radius: 8px;
      }
    }
    // 图片结束
  }
}
