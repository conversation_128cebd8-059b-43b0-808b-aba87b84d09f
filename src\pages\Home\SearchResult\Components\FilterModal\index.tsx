/**
 * @Description: 首页、广场搜索结果页的筛选弹窗
 */
import React, { useState, useEffect } from 'react'
import classNames from 'classnames'
import { getOperatingEnv } from '@/utils/utils'
import { cloneDeep } from 'lodash'
import { Toast, DatePicker } from 'antd-mobile'
import styles from './index.less'

const Index: React.FC = (props: any) => {

  const {
    filterListData = [],                                   // 筛选字典项数据

    spaceStatusList = [],                                  // 空间状态：1直播中、2预约中、3弹幕轰炸中
    meetingStatusList = [],                                 // 会议状态：1直播中、2预约中、3弹幕轰炸中

    achievementList = [],                                  // 病例成就
    depSubjectDictList = [],                               // 学科
    difficultLevelDictList = [],                           // 难度等级
    startDate = null,                                      // 开始日期
    endDate = null,                                        // 结束日期

    // depSubjectDictListUser = [],                        // 学科
    cityList = [],                                         // 城市
    abilityLevelDictList = [],                             // 能力等级
    postTitleDictList = [],                                // 职级
  } = props

  // 筛选条件
  const initialFilterState = {
    spaceStatusList: [],                                   // 空间状态：1直播中、2预约中、3弹幕轰炸中
    meetingStatusList: [],                                 // 会议状态：1直播中、2预约中、3弹幕轰炸中
    achievementList: [],                                   // 病例成就
    depSubjectDictList: [],                                // 学科
    difficultLevelDictList: [],                            // 难度等级
    startDate: null,                                       // 开始日期
    endDate: null,                                         // 结束日期

    // depSubjectDictListUser: [],                         // 学科
    cityList: [],                                          // 城市
    abilityLevelDictList: [],                              // 能力等级
    postTitleDictList: [],                                 // 职级
  }
  const initialModalState = {
    maskVisible: false,                                    // 筛选弹窗遮罩
    startDatePickerVisible: false,                         // 开始日期选择框遮罩
    endDatePickerVisible: false,                           // 结束日期选择框遮罩
  }

  const [filterState, setFilterState] = useState(initialFilterState)
  const [modalState, setModalState] = useState(initialModalState)

  useEffect(() => {
    if (props.visible) {
      console.log(222,props)
      setFilterState({
        spaceStatusList: cloneDeep(spaceStatusList),                   // 空间状态：1直播中、2预约中、3弹幕轰炸中
        meetingStatusList: cloneDeep(meetingStatusList),               // 会议状态：1直播中、2预约中、3弹幕轰炸中
        achievementList: cloneDeep(achievementList),                   // 病例成就
        depSubjectDictList: cloneDeep(depSubjectDictList),             // 学科
        difficultLevelDictList: cloneDeep(difficultLevelDictList),     // 难度等级
        startDate: startDate,                                          // 开始日期
        endDate: endDate,                                              // 结束日期

        // depSubjectDictListUser: [],                                 // 学科
        cityList: cloneDeep(cityList),                                 // 城市
        abilityLevelDictList: cloneDeep(abilityLevelDictList),         // 能力等级
        postTitleDictList: cloneDeep(postTitleDictList),               // 职级
      })
    } else {
      setFilterState(initialFilterState)
    }
  }, [props.visible])

  // 开始日期，打开
  const startDatePicker = () => {
    setModalState({
      ...modalState,
      startDatePickerVisible: true,                        // 开始日期弹窗遮罩
    })
  }

  // 开始日期，关闭
  const startDatePickerHide = () => {
    setModalState({
      ...modalState,
      startDatePickerVisible: false,                       // 开始日期弹窗遮罩
    })
  }

  // 选择开始日期
  const startDatePickerOnChange = (value) => {
    const startDate = value.getFullYear()
    if (filterState.endDate && startDate > filterState.endDate) {
      Toast.show('开始日期大于结束日期～')
      return
    }
    setFilterState({
      ...filterState,
      startDate,                                       // 开始日期
    })
  }

  // 结束日期，打开
  const endDatePicker = () => {
    setModalState({
      ...modalState,
      endDatePickerVisible: true,
    })
  }

  // 结束日期，关闭
  const endDatePickerHide = () => {
    setModalState({
      ...modalState,
      endDatePickerVisible: false,
    })
  }

  // 选择结束日期
  const endDatePickerOnChange = (value) => {
    const endDate = value.getFullYear()
    if (filterState.startDate && filterState.startDate > endDate) {
      Toast.show('结束日期小于开始日期～')
      return
    }
    setFilterState({
      ...filterState,
      endDate,                                         // 结束日期
    })
  }

  // 选择筛选条件
  const filterOnChange = (fieldName, value) => {
    let list = cloneDeep(filterState[fieldName])
    if (value == null) {
      list = []
    } else {
      const index = list.indexOf(value)
      if (index == -1) {
        list.push(value)
      } else {
        list.splice(index, 1)
      }
    }
    setFilterState({
      ...filterState,
      [fieldName]: list,
    })
  }

  // 点击取消
  const filterOnCancel = () => {
    props.onCancel()
  }

  // 点击确定
  const filterOnOk = () => {
    props.onOk(cloneDeep(filterState))
  }

  return (
    <>
      <DatePicker
        visible={modalState.startDatePickerVisible}
        onClose={startDatePickerHide}
        precision='year'
        onConfirm={startDatePickerOnChange}
      />
      <DatePicker
        visible={modalState.endDatePickerVisible}
        onClose={endDatePickerHide}
        precision='year'
        onConfirm={endDatePickerOnChange}
      />
      <div className={classNames(styles.filter_box, {
        [styles.filter_box_show]: props.visible,
        [styles.filter_box_pc]: getOperatingEnv() == 4,
      })}>
        <div className={styles.filter_content_box}>
          {
            filterListData.map(item => {
              if (item.name == '日期') {
                return (
                  <div key={item.code}>
                    <div className={styles.filter_title}>日期</div>
                    <div className={classNames(styles.filter_content, styles.filter_content_long, styles.filter_content_time)}>
                      <div className={styles.filter_item_box}>
                        <div className={classNames(styles.filter_item, {
                          [styles.value]: !!filterState.startDate,
                        })} onClick={startDatePicker}>{filterState.startDate || '开始日期'}</div>
                      </div>
                      <div className={styles.filter_item_separator}></div>
                      <div className={styles.filter_item_box}>
                        <div className={classNames(styles.filter_item, {
                          [styles.value]: !!filterState.endDate,
                        })} onClick={endDatePicker}>{filterState.endDate || '结束日期'}</div>
                      </div>
                    </div>
                  </div>
                )
              }
              return (
                <div key={item.code}>
                  <div className={styles.filter_title}>{item.name}</div>
                  <div className={classNames(styles.filter_content, {
                    [styles.filter_content_long]: item.code == 'achievementList',
                  })}>
                    {
                      item.children && item.children.map(itemChild => {
                        return (
                          <div key={itemChild.code} className={styles.filter_item_box}>
                            <div className={classNames(styles.filter_item, {
                              [styles.checked]: (itemChild.name == '全部' || itemChild.name == '全国') && filterState[item.code].length == 0 || filterState[item.code].indexOf(itemChild.code) > -1,
                            })} onClick={() => filterOnChange(item.code, itemChild.code)}>{itemChild.name}</div>
                          </div>
                        )
                      })
                    }
                  </div>
                </div>
              )
            })
          }
        </div>

        <div className={styles.filter_btn_box}>
          <div className={styles.filter_btn_cancel} onClick={filterOnCancel}>取消</div>
          <div className={styles.filter_btn_ok} onClick={filterOnOk}>确认</div>
        </div>
      </div>
    </>
  )
}

export default Index
