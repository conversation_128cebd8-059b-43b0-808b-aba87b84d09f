import React, { useState, useEffect, useRef } from 'react';
import { history, connect } from 'umi';
import styles from './index.less';
import { getOperatingEnv, priceFormat } from '@/utils/utils';
import { ImageViewer, NavBar } from 'antd-mobile';
import {message, Spin,Image} from 'antd';
import Header from '@/pages/ConsultationModule/PcComponents/Header';
import PcHeader from '@/componentsByPc/PcHeader';
import pcGobackIcon from '@/assets/GlobalImg/pc_goback.png';
import InfiniteScroll from 'react-infinite-scroller';
import dayjs from 'dayjs';
import NoDataRender from '@/components/NoDataRender'       // 暂无数据组件
import LookProofsModal from './LookProofsModal'  // 查看凭证弹窗
import PreviewPdfModal from './PreviewPdfModal'  // PDF浏览弹窗

const initStatePage = {
  pageNum: 1,
  hasMore: true, // 加载更多
  loadMore: false,
};
const initState = {
  total: 0,
  listDate: [],
};

// 预览PDF弹窗state
const initialPreviewPdfModalState = {
  previewPdfModalVisible: false, // 预览PDF弹窗
  pdfUrl: null, // pdf地址
}
const Index: React.FC = (props) => {
  const { global, dispatch, loading, userInfoStore } = props || {};
  const { location } = history || {};
  const { query } = location || {};

  const [state, setState] = useState(initState); // 列表数据
  const [statePage, setStatePage] = useState(initStatePage); // 当前分页
  const [loadingResultListByState, setLoadingResultListByState] = useState(null);
  const [friUserToScholarship, setFriUserToScholarship] = useState(false); // 获取学习金余额
  const [visibleByPreviewGroup, setVisibleByPreviewGroup] = useState(false); //
  const [visibleByLookProofsModal, setVisibleByLookProofsModal] = useState(false); // 查看凭证弹窗
  const [previewPdfModalState, setPreviewPdfModalState] = useState(initialPreviewPdfModalState) // 预览PDF弹窗
  const { total, listDate } = state; // 获取用户学习金明细新增、核销记录（H5、APP）
  const { pageNum, hasMore, loadMore } = statePage || {};
  const scrollParentRef = useRef<HTMLDivElement | null>(null);
  const UerInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  // 是否嵌套在5i5ya的iframe中
  const isInIframe = self != top && localStorage.getItem('iframeWhereFrom') == '5i5ya';

  // 微信浏览器使用
  let OperatingEnv = getOperatingEnv();

  // 初始化数据
  useEffect(async () => {
    await getFriUserToScholarship(); // 获取用户个人主页展示学习金信息
    await getScholarshipDetailList(1); // 首次获取用户学习金明细新增、核销记录（H5、APP）
  }, []);

  // 获取用户个人主页展示学习金信息
  let getFriUserToScholarship = async () => {
    let resBygetFriUserToScholarship = await dispatch({
      type: 'userInfoStore/getFriUserToScholarship',
      payload: {
        wxUserId: (UerInfo && UerInfo.friUserId) || '',
      },
    });
    if (
      resBygetFriUserToScholarship &&
      resBygetFriUserToScholarship.code == 200 &&
      resBygetFriUserToScholarship.content
    ) {
      setFriUserToScholarship(resBygetFriUserToScholarship.content);
    }else if(
      resBygetFriUserToScholarship &&
      resBygetFriUserToScholarship.code == 422
    ) {
      message.error(resBygetFriUserToScholarship.msg ? resBygetFriUserToScholarship.msg : '学习金获取报错!')
    }
  };

  // 获取用户学习金明细新增、核销记录（H5、APP）
  let getScholarshipDetailList = async (pages) => {
    let resBygetScholarshipDetailList = await dispatch({
      type: 'userInfoStore/getScholarshipDetailList',
      payload: {
        wxUserId: (UerInfo && UerInfo.friUserId) || '',
        pageNum: pages || 1,
        pageSize: 10,
      },
    });
    const { code, content } = resBygetScholarshipDetailList || {};
    if (code == 200 && content) {
      const { pageNum, pageSize, total, resultList } = content || {};
      if (Array.isArray(content.resultList) && content.resultList.length > 0) {
        setState({
          total: total,
          listDate: Array.isArray(resultList) ? [...listDate, ...resultList] : listDate,
        });
      }
      let hasMore = (Array.isArray(resultList) && resultList.length == 0) || !resultList;
      setStatePage({
        ...statePage,
        pageNum: pageNum,
        pageSize: pageSize,
        loadMore: false,
        hasMore: !hasMore,
      });
    }
  };

  // 加载更多
  let handleInfiniteOnLoad = () => {
    const pages = pageNum + 1;
    setStatePage({
      ...statePage,
      loadMore: true,
    });
    getScholarshipDetailList(pages);
  };

  // 查看大图
  const previewBigImage = (item) => {
    if (item && Array.isArray(item.proofs) && item.proofs.length > 0) {
      let textImgList = [];
      item.proofs.map((itByProofs) => {
        textImgList.push({
          imageUrlShow: itByProofs.proofUrlView,
          isCover: 1,
        });
      });
      ImageViewer.Multi.show({
        defaultIndex: 0,
        images: textImgList.map((it) => it.imageUrlShow),
        getContainer: () => document.getElementById('StudyWarp'),
      });
    }
  };

  // 点击预览pdf
  const onClickPreviewPdfBtn = (value) => {
    setPreviewPdfModalState({
      ...previewPdfModalState,
      previewPdfModalVisible: true,
      pdfUrl: value, // pdf链接地址
    })
  }

  // 预览pdf弹窗关闭
  const previewPdfModalClose = () => {
    setPreviewPdfModalState({
      ...previewPdfModalState,
      previewPdfModalVisible: false,
      pdfUrl: null, // pdf链接地址
    })
  }

  return (
    <div id={'StudyWarp'} className={styles.Mobile_Wrap}>
      <Spin spinning={!!loading.effects['userInfoStore/getFriUserToScholarship']}>
        {/* iframe中隐藏header */}
        {isInIframe ? null : <PcHeader />}
        <div className={styles.wrap}>
          <div className={styles.wrap_box}>
            {/* 标题 */}
            <div className={styles.header}>
              <div
                className={styles.header_title}
                onClick={() => {
                  history.goBack();
                }}
              >
                <img className={styles.header_title_icon} src={pcGobackIcon} alt="" />
                返回个人中心
              </div>
            </div>

            <div className={styles.tab_title_Warp}>
              <div className={styles.StudyBonusBox}>
                <div className={styles.StudyBonusBox_num}>
                  {friUserToScholarship && priceFormat(friUserToScholarship.currentNum)}
                </div>
                {friUserToScholarship && friUserToScholarship.validDate && (
                  <div className={styles.StudyBonusBox_date}>
                    有效期至
                    {dayjs(friUserToScholarship.validDate, 'YYYY-MM-DD').format('YYYY年MM月DD日')}
                  </div>
                )}
                <div className={styles.StudyBonusBg}></div>
              </div>
              <div id={'StudyWarpContent'} className={styles.Content_box}>
                <InfiniteScroll
                  loadMore={handleInfiniteOnLoad}
                  threshold={50}
                  pageStart={1}
                  initialLoad={false}
                  useWindow={false}
                  hasMore={!loadMore && hasMore}
                  getScrollParent={() => document.getElementById('StudyWarpContent')}
                  className={styles.scroll_box}
                >
                  <div className={styles.title_name}>学习金收支明细</div>
                  <div className={styles.content_box}>

                    {/*暂无数据展示*/}
                    {Array.isArray(listDate) && listDate.length == 0 &&
                      <div>
                        <NoDataRender text="暂无学习金明细" style={{padding: '100px 0', marginTop: 0}}/>
                      </div>
                    }

                    {Array.isArray(listDate) &&
                      listDate.map((item, index) => {
                        return (
                          <>
                            {item && (
                              <div>
                                <div key={`${item.id}_${index}`} className={styles.item_content}>
                                  <div className={styles.item_title_box}>
                                    <div className={styles.item_title}> {item.reason} </div>
                                    <div className={styles.item_title_num}>
                                      {/*driftType: 1, // 1增  0减*/}
                                      <span>{item.driftType == 1 ? ' + ' : ' - '}</span>
                                      <span> {priceFormat(item.num)} </span>
                                    </div>
                                  </div>
                                  <div className={styles.item_title_box_felx}>
                                    <div className={styles.item_time}> {item.createDate} </div>
                                    {item && item.proofs && item.proofs.length > 0 && (
                                      <Image.PreviewGroup
                                        preview={{
                                          visible: item.id == (visibleByPreviewGroup && visibleByPreviewGroup.id),
                                          // onChange: (current, prev) => console.log(`current index: ${current}, prev index: ${prev}`),
                                          onVisibleChange: (value) => {
                                            setVisibleByPreviewGroup(value);
                                          },
                                        }}
                                      >
                                        {item.proofs && item.proofs.map((proof)=>(
                                          <Image
                                            width={0}
                                            style={{display:"none"}}
                                            src={proof.proofUrlView}

                                          ></Image>
                                        ))}
                                      </Image.PreviewGroup>
                                    )}


                                    {item && item.proofs && item.proofs.length > 0 && (
                                      <div
                                        onClick={() => {
                                          setVisibleByLookProofsModal(item.proofs);
                                        }}
                                        className={styles.btn_box}
                                      >
                                        查看凭证
                                        <i className={styles.btn_icon}></i>
                                      </div>
                                    )}
                                  </div>
                                </div>
                                {listDate.length - 1 != index &&
                                  <div className={styles.line}></div>
                                }
                              </div>
                            )}
                          </>
                        )
                      })}

                    {/* ----滚动加载中的loading---- */}
                    {!!loadMore &&
                      <div className={styles.loadingByInfiniteScroll}>
                        <div className={styles.loadingByInfiniteScrollContent}>
                          <div style={{marginRight:'10px'}}>加载中...</div>
                          <div>
                            <Spin spinning={true}></Spin>
                          </div>
                        </div>
                      </div>
                    }
                    {/* ----加载中的loading----- */}
                  </div>
                </InfiniteScroll>
              </div>
            </div>
          </div>
        </div>
      </Spin>

      {/*  */}
      <LookProofsModal
        visible={visibleByLookProofsModal}
        proofs={visibleByLookProofsModal} // 已上传凭证
        onCancel={()=>{setVisibleByLookProofsModal(null);}} // 关闭弹窗
        onClickPreviewPdfBtn={onClickPreviewPdfBtn} // 点击预览PDF
      />

      {/* 预览PDF弹窗 */}
      <PreviewPdfModal
        visible={previewPdfModalState.previewPdfModalVisible}
        pdfUrl={previewPdfModalState.pdfUrl} // PDF链接地址
        onCancel={previewPdfModalClose} // 关闭弹窗
      />

    </div>
  );
};

export default connect(({ ConsultationList, pcAccount, loading }: any) => ({
  ConsultationList,
  pcAccount,
  loading,
}))(Index);
