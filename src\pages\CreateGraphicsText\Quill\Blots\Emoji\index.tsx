import { Quill } from 'react-quill'

const Parchment = Quill.import('parchment')
// const ATTRIBUTES = ['alt', 'height', 'width'];

class EmojiBlot extends Parchment.Embed {
  static create(value: string) {
    const node = super.create()
    node.setAttribute('src', value)
    node.setAttribute('data-type', 'emoji')
    return node
  }

  static value(domNode: Element) {
    return domNode.getAttribute('src');
  }

  format(name: string, value: string) {

  }

}

EmojiBlot.blotName = 'emoji';                              // 格式名
EmojiBlot.tagName = 'img';                                 // dom标签
EmojiBlot.className = 'quill_emoji_format_wrap';           // dom类名

export default EmojiBlot
