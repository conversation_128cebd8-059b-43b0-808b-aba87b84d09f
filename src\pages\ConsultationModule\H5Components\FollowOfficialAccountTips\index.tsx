/**
 * @Description: 移动端我已关注公众号，不再提示
 * @author: 赵斐
 */
import React from 'react';
import styles from './index.less'

interface PropsType {
  isPc?: boolean,                // 区分是PC、H5引用
  onClickIsFocusMp: () => void,  // 点击我已关注回调
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const { isPc = false ,onClickIsFocusMp } = props;
  return (
    <div className={styles.wrap}>
      <div className={isPc?styles.detail_pc:styles.detail_h5}>
        <div className={styles.qrCode}>
          <img src="https://static.jwsmed.com/public/DigitalHealth/Business/assets/FridayOfficialAccountsQR.png" alt="icon" />
        </div>
        <div className={styles.content}>
          <p className={styles.content_desc}>打开手机微信扫描二维码，关注FRIDAY公众号，可及时接收专家消息</p>
          <p className={styles.content_btn} onClick={()=>{onClickIsFocusMp()}}>我已关注，不再提示</p>
        </div>
      </div>
    </div>
  )
}
export default Index
