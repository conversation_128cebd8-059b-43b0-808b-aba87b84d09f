/**
 * @Description: 专家搜索结果页面
 * @author: 赵斐
 */
import React, { useEffect, useState, useRef ,lazy } from 'react';
import styles from './index.less';
import { connect } from 'umi';
import { InfiniteScroll } from 'antd-mobile'
import { Spin } from 'antd'
import { Helmet } from "react-helmet";
import upArrowIcon from '@/assets/Expert/up_arrow.png'; // 向上箭头
import downArrowIcon from '@/assets/Expert/down_arrow.png'; // 向下箭头
// 数据加载异常
import LoadingException from '@/components/LoadingException'
// 导航组件
import NavBar from '@/components/NavBar'
// 搜索组件
import SearchInput from '@/components/SearchInput';
// 专家列表组件
// import ExpertList from '@/components/ExpertList';
const ExpertList = lazy(() => import('@/components/ExpertList'))



const Index: React.FC = (props: any) => {
  const { dispatch, loading, expertAdvice, whereStatus = 999 } = props || {};// whereStatus = 999代表点击搜索跳转过来
  const initState = {
    total: 0,
    searchValue: expertAdvice.searchValue,
    dataSource: [],
  }
  const listRef = useRef<any>(null);
  const [state, setState] = useState(initState)        // 列表接口所需参数及数据
  const [statePageNum, setStatePageNum] = useState(1)  // 当前分页
  const [tabData, setTabData] = useState<any>(null)    // 学科分类数据
  const [interfaceStatus, setInterfaceStatus] = useState(0); // 接口状态  0 暂未搜索数据 1 数据加载失败
  const [screenType, setScreenType] = useState(0); // 筛选类型
  const {
    city,          // 城市
    depSubject,    // 学科
    abilityLevel,  // 能力等级
    postTitle,     // 职级
  } = tabData || {};
  const {
    total,        // 总条数
    searchValue,  // 搜索值
    dataSource,   // 专家数据
  } = state
  let {
    checkCity,          // 城市
    checkDepSubject,    // 学科
    checkAbilityLevel,  // 能力等级
    checkPostTitle,     // 职级
  } = expertAdvice
  useEffect(() => {
    getFilterDict()
  }, [])
  useEffect(() => {
    const { searchValue } = expertAdvice
    listRef.current.scrollTop = 0
    setState({
      ...state,
      searchValue
    })
    getExpertsList(1, searchValue, expertAdvice)
  }, [])

  // 按学科找专家字典数据
  const getFilterDict = () => {
    dispatch({
      type: "expertAdvice/getFilterDict",
      payload: {}
    }).then((res: any) => {
      if (res && res.code == 200) {
        const { content } = res || {}
        const { depSubject, abilityLevel, postTitle, city } = content || {}
        abilityLevel.unshift({ code: 0, name: "全部", iconName: null, children: null })
        postTitle.unshift({ code: 0, name: "全部", iconName: null, children: null })
        city.unshift("全国")
        depSubject.unshift({ code: 0, name: "全部", iconName: "", children: null })
        if (Array.isArray(depSubject) && depSubject.length) {
        }
        setTabData(content)
      } else {
        setInterfaceStatus(1)
      }
    }).catch((err: String) => {
      setInterfaceStatus(1)
      console.log(err)
    })
  }

  /**
   * 获取专家列表数据
   * @param current 当前页
   * @param searchKey 搜索值
   * @param screenObj 筛选条件
   */
  const getExpertsList = async (pageNum: number, searchKey: string = "", screenObj: any = {}) => {
    const { checkCity, checkDepSubject, checkAbilityLevel, checkPostTitle } = screenObj

    await dispatch({
      type: "expertAdvice/getExpertsList",
      payload: {
        pageNum,
        pageSize: 30,
        city: checkCity != "全国" ? checkCity : null,							  // 所属机构城市
        depSubjectDict: whereStatus == 999 ? checkDepSubject != 0 ? checkDepSubject : null : whereStatus, // 科室字典
        abilityLevelDict: checkAbilityLevel != 0 ? checkAbilityLevel : null,		// 能力等级字典
        postTitleDict: checkPostTitle != 0 ? checkPostTitle : null,      // 职称字典
        searchKey,          // 搜索关键字
      }
    }).then((res: any) => {
      if (res && res.code == 200) {
        const { content } = res || {};
        const { total, resultList } = content || {};
        let data = pageNum == 1 ? [] : dataSource;
        data = data.concat(resultList);
        if (Array.isArray(data) && data.length == 0) {
          setState({
            ...state,
            dataSource: [],
            total: 0,
            searchValue: searchKey
          })
          setInterfaceStatus(0)
          return
        }
        setState({
          ...state,
          dataSource: [...data],
          total,
          searchValue: searchKey
        })

        setStatePageNum(pageNum)
      } else {
        setInterfaceStatus(1)
      }
    }).catch((err: String) => {
      console.log(err)
    })
  }

  // 加载更多数据
  let loadMore = async () => {
    await getExpertsList(statePageNum + 1, searchValue, expertAdvice)
  }
  /**
   * 筛选城市、学科、能力等级、职级
   * @param val  当前选中数据
   * @param type 1 城市 2 学科 3 能力等级 4 职级
   */
  const onScreenFun = (val: any, type: number) => {
    listRef.current.scrollTop = 0
    let obj = expertAdvice
    if (type == 1) {
      let item = val == checkCity ? '' : val;
      obj = { ...obj, checkCity: item }
    } else if (type == 2) {
      obj = { ...obj, checkDepSubject: checkDepSubject != val ? val : null } // 学科初始数据
    } else if (type == 3) {
      obj = { ...obj, checkAbilityLevel: checkAbilityLevel != val ? val : null } // 能力等级初始数据
    } else if (type == 4) {
      obj = { ...obj, checkPostTitle: checkPostTitle != val ? val : null }// 职级初始数据
    }
    setScreenType(0)
    dispatch({
      type: "expertAdvice/save",
      payload: {
        ...obj
      }
    })
    getExpertsList(1, searchValue, obj)
  }

  /**
 * 搜索病例相关内容
 * @param value 搜索值
 */
  const onChangeSearchFun = (value: string) => {
    setState({
      ...state,
      searchValue: value,
    })
    // if (value == "") {
    //   listRef.current.scrollTop = 0
    //   dispatch({
    //     type:"expertAdvice/save",
    //     payload:{
    //       searchValue:""
    //     }
    //   })
    //   getExpertsList(1,"",expertAdvice)
    // }

  }

  // 点击完成进行搜索
  const onSearchEnterFun = () => {
    listRef.current.scrollTop = 0
    dispatch({
      type: "expertAdvice/save",
      payload: {
        searchValue
      }
    })
    getExpertsList(1, searchValue, expertAdvice)
  }

  // 清空搜索值
  const clearSearchFun = () => {
    listRef.current.scrollTop = 0
    const initCheckScreen = {
      checkCity: '',
      checkDepSubject: null,  // 学科初始数据
      checkAbilityLevel: null,  // 学科初始数据
      checkPostTitle: null,  // 学科初始数据
    }
    if (checkCity || checkDepSubject || checkAbilityLevel || checkPostTitle) {
      dispatch({
        type: "expertAdvice/clean",
      })
      setScreenType(0)
    } else {
      dispatch({
        type: "expertAdvice/save",
        payload: {
          searchValue: ''
        }
      })
    }
    getExpertsList(1, "", initCheckScreen)

  }

  // 数据加载异常
  const retryFun = () => {
    getFilterDict()
    getExpertsList(1, searchValue, expertAdvice)
  }

  // 重置接口异常状态
  const resetStatusFun = () => {
    setInterfaceStatus(0)
  }

  /**
 * 筛选显示名称处理
 * @param data  筛选数据
 * @param code  选中code
 * @returns
 */
  const screenTitleDom = (data: any, code: string) => {
    if (Array.isArray(data) && data.length) {
      var result = data.find((item: any) => item.code == code);
      return result ? result.name : null;
    }
  }

  const load = !!loading.effects['expertAdvice/getExpertsList'] ||   // 专家列表接口loading
    !!loading.effects['expertAdvice/getFilterDict']       // 学科字典接口loading
  return (
    <Spin spinning={load}>
      <Helmet>
        <title>{whereStatus != 999 ? `${screenTitleDom(depSubject, whereStatus)}` : "专家列表"}</title>
        <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, shrink-to-fit=no" />
      </Helmet>
      <div className={styles.result_wrap} id="header">
        <div className={styles.header}>
          <NavBar title={whereStatus != 999 ? screenTitleDom(depSubject, whereStatus) : ""} className={styles.header_nav} />
        </div>
        <SearchInput
          isHistoryStatus={2}
          inputPlaceholder="输入专家姓名、医院"
          defaultInputValue={searchValue}
          inputChangeFn={onChangeSearchFun}
          cancelBtnFn={clearSearchFun}
          onPressEnterFun={onSearchEnterFun}
        />
        <div className={styles.screen_header}>
          {/* 全国 */}
          <div
            className={checkCity ? styles.screen_word_active : styles.screen_word}
            style={{ marginRight: whereStatus != 999 ? 24 : 0 }}
            onClick={() => { setScreenType(1) }}
          >
            <div className={styles.screen_text}>{checkCity ? checkCity : "全国"}</div>
            <img className={styles.arrow_icon} alt='符号' src={screenType === 1 ? upArrowIcon : downArrowIcon} />
          </div>
          {/* 学科 */}
          {
            whereStatus == 999 ?
              <div
                className={checkDepSubject ? styles.screen_word_active : styles.screen_word}
                onClick={() => { setScreenType(2) }}
              >
                <div className={styles.screen_text}>{checkDepSubject ? screenTitleDom(depSubject, checkDepSubject) : "学科"}</div>
                <img className={styles.arrow_icon} alt='符号' src={screenType === 2 ? upArrowIcon : downArrowIcon} />
              </div> : null
          }
          {/* 能力等级 */}
          <div
            className={checkAbilityLevel ? styles.screen_word_active : styles.screen_word}
            style={{ marginRight: whereStatus != 999 ? 24 : 0 }}
            onClick={() => { setScreenType(3) }}
          >
            <div className={styles.screen_text}>{checkAbilityLevel ? screenTitleDom(abilityLevel, checkAbilityLevel) : "能力等级"}</div>
            <img className={styles.arrow_icon} alt='符号' src={screenType === 3 ? upArrowIcon : downArrowIcon} />
          </div>
          {/* 职级 */}
          <div
            className={checkPostTitle ? styles.screen_word_active : styles.screen_word}
            onClick={() => { setScreenType(4) }}
          >
            <div className={styles.screen_text}>{checkPostTitle ? screenTitleDom(postTitle, checkPostTitle) : "职级"}</div>
            <img className={styles.arrow_icon} alt='符号' src={screenType === 4 ? upArrowIcon : downArrowIcon} />
          </div>
          <div className={styles.mask_wrap} style={{ display: screenType ? 'block' : 'none' }} onClick={() => { setScreenType(0) }}></div>
          {/* 城市 */}
          {
            screenType == 1 && city && city.length ?
              <div className={styles.screen_child}>
                {
                  city.map((val: string, idx: number) => {
                    return (
                      <div className={styles.screen_child_wrap} key={idx}>
                        <div className={checkCity == val ? styles.screen_child_item_active : styles.screen_child_item} onClick={() => { onScreenFun(val, 1) }}>{val}</div>
                      </div>
                    )
                  })
                }
              </div> : null
          }
          {/* 学科 */}
          {
            screenType == 2 && depSubject && depSubject.length ?
              <div className={styles.screen_child}>
                {
                  depSubject.map((item: any, idx: number) => {
                    return (
                      <div className={styles.screen_child_wrap} key={idx}>
                        <div className={checkDepSubject == item.code ? styles.screen_child_item_active : styles.screen_child_item} onClick={() => { onScreenFun(item.code, 2) }}>{item.name}</div>
                      </div>
                    )
                  })
                }
              </div> : null
          }
          {/* 能力等级 */}
          {
            screenType == 3 && abilityLevel && abilityLevel.length ?
              <div className={styles.screen_child}>
                {
                  abilityLevel.map((item: any, idx: number) => {
                    return (
                      <div className={styles.screen_child_wrap} key={idx}>
                        <div className={checkAbilityLevel == item.code ? styles.screen_child_item_active : styles.screen_child_item} onClick={() => { onScreenFun(item.code, 3) }}>{item.name}</div>
                      </div>
                    )
                  })
                }
              </div> : null
          }
          {/* 职级 */}
          {
            screenType == 4 && postTitle && postTitle.length ?
              <div className={styles.screen_child}>
                {
                  postTitle.map((item: any, idx: number) => {
                    return (
                      <div className={styles.screen_child_wrap} key={idx}>
                        <div className={checkPostTitle == item.code ? styles.screen_child_item_active : styles.screen_child_item} onClick={() => { onScreenFun(item.code, 4) }}>{item.name}</div>
                      </div>
                    )
                  })
                }
              </div> : null
          }
        </div>
        <div className={styles.expert_result_box} ref={listRef}>
          {
            Array.isArray(dataSource) && dataSource.length ?
              <ExpertList dataSource={dataSource} /> :
              <LoadingException exceptionStyle={{ paddingTop: 110 }} interfaceStatus={interfaceStatus} retryFun={retryFun} resetStatusFun={resetStatusFun} />
          }
          {
            total > 0 && <InfiniteScroll loadMore={loadMore} hasMore={total > dataSource.length} threshold={100} />
          }
        </div>
      </div>
    </Spin>
  )
}
export default connect(({ expertAdvice, loading }: any) => ({ expertAdvice, loading }))(Index)
