//.header_box {
//  position: fixed;
//  top: 0;
//  left: 0;
//  width: 100%;
//  z-index: 799;
//  height: 58px;
//  padding: 0 16px;
//  padding-top: 9px;
//  i {
//    display: block;
//    width: 124px;
//    height: 26px;
//    background: url("../../assets/home_logo.png") no-repeat center;
//    background-size: 100% 100%;
//  }
//}
.container_box {
  //margin-top: -14px;
  //height: calc(100vh - 44px);
  height: calc(100vh);
  overflow: hidden;
  background: #fff;
  //border-radius: 16px 16px 0 0;
}
.container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding-top: 12px;
  &.search_container {
    padding-top: 68px;
  }
  &.home_search_container {
    padding-top: 67px;
  }
  &.tabbar_container {
    padding-bottom: 66px;
  }
  &.activity_container {
    padding-bottom: 81px;
  }
  // APP相关
  &.in_app_container {
    padding-bottom: 0;
  }
}

.container_box.container_box_in_square {
  height: auto;
  background: none;
  .container {
    padding-top: 0;
    padding-bottom: 0;
  }
}
