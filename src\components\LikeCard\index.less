.content {
  width: 100%;
  padding: 16px 0;
  background: #fff;
  margin-bottom: 10px;

  .forward_box {
    padding: 0 16px 8px;
    display: flex;
    align-items: center;
    font-size: 13px;
    font-weight: 400;
    color: #333333;
    line-height: 15px;

    .forward_name {
      font-size: 13px;
      font-weight: 500;
      color: #000000;
      line-height: 15px;
      margin-right: 2px;
    }
  }

  .forward_card {
    background: #F8F8F9;
    padding: 12px 16px;
    box-sizing: border-box;
    .forward_card_removeOriginalContent {
      font-size: 13px;
      font-weight: 400;
      color: #333333;
      line-height: 13px;
    }
  }
}


.post_content {
  padding: 8px;
  width: 100%;
  display: flex;
  flex-wrap: nowrap;
  overflow: hidden;
  border: 1px solid #EBEBEB;
  border-radius: 4px 4px 4px 4px;

  :global {
    .ql-editor {
      font-size: 14px !important;
      color: #333333 !important;
      line-height: 20px !important;
    }
  }

  .init_img {
    width: 68px;
    height: 68px;
    border-radius: 4px;
    font-size: 30px;
    color: #fff;
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    flex-shrink: 0;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
  }

  .text {
    flex: 1;
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    line-height: 18px;
    word-break: break-all;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3; /* 指定显示三行 */
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.forward_title_warp {
  padding: 0 16px 4px;
  p {
    margin-bottom: 0;
  }
  .line {
    width: 100%;
    height: 0;
    border-top: 1px solid #EEEEEE;
    margin-bottom: 8px;
  }
  .forward_title {
    font-size: 14px;
    font-weight: 400;
    color: #000000;
    line-height: 20px;

    span {
      font-size: 14px;
      color: #0095FF;
      margin-right: 4px;
    }
  }
}





.space_forward_title {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  color: #000000;
  line-height: 16px;
  margin-bottom: 8px;

  p {
    margin-bottom:0px;
  }

  span {
    font-size: 14px;
    font-weight: 400;
    color: #0095FF;
    line-height: 16px;
    margin-right: 4px;
  }
}

.space_list_content {
  width: 100%;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #EBEBEB;
  padding: 8px;
  box-sizing: border-box;
  display: flex;
  overflow: hidden;

  .space_img_box {
    width: 68px;
    flex-shrink: 0;
    margin-right: 8px;

    .space_init_img {
      width: 68px;
      height: 68px;
      border-radius: 4px;
      font-size: 30px;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      .title_in_cover_image {
        position: absolute;
        z-index: 10;
        width: 100%;
        top: 14px;
        left: 0;
        padding-left: 8px;
        font-size: 12px;
        line-height: 16px;
        color: #fff;
        font-weight: 500;
        word-break: break-all;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2; /* 指定显示行数 */
        overflow: hidden;
        text-overflow: ellipsis;
      }
      img {
        width: 100%;
        height: 100%;
        border-radius: 4px;
        object-fit: cover;
      }
    }
  }

  .space_info_box {
    flex: 1;

    .space_title {
      font-size: 13px;
      font-weight: 500;
      color: #000000;
      line-height: 18px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
    }

    .space_introduce {
      font-size: 12px;
      font-weight: 400;
      color: #333333;
      line-height: 16px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
    }
  }
}

.expertCertificationIconWarp {
  display: inline-block;
  margin-left: 1px;
  margin-right: 2px;
}

.expertCertificationIcon {
  width: 12px;
  height: 12px;
  background: url('../../assets/GlobalImg/expertCertificationIcon.png') no-repeat;
  background-size: 12px 12px;
  display: inline-block;
  position: relative;
  top: 1px;
}
