import React, { useState, useEffect, useRef } from 'react';
import StreamBar from './streamBar';
import FullscreenExitIcon from '@material-ui/icons/FullscreenExit';
import styles from './stream.module.less';
import Toast from '@/componentsByTRTC/Toast';
import { setFullscreen, exitFullscreen } from '@/utils/utilsByTRTC';

const fullscreenchangeList = ['fullscreenchange', 'webkitfullscreenchange', 'mozfullscreenchange', 'MSFullscreenChange'];
const fullscreenerrorList = ['fullscreenerror', 'webkitfullscreenerror', 'mozfullscreenerror', 'MSFullscreenError'];

/**
 * @description stream 组件
 * @param {Object} props 配置项
 * @param {Object} props.stream 流对象
 * @param {Object} props.config 设置流当前的属性状态 video: 视频 audio: 音频
 * @param {Object} props.setting 设置对应的 streamBarIcon 是否显示
 * @param {Function} props.init 将 dom 元素回调
 * @param {Function} props.onChange 用户操作 streamBar 的回调
 * @returns {Element}
 *
 */
const Stream = (props) => {
  const [statusInit, setStatusInit] = useState(false);
  const [full, setFull] = useState(false);
  const [config, setConfig] = useState(() => ({ ...props.config }));
  const refItem = useRef();

  useEffect(() => {
    fullscreenerrorList.forEach((item) => {
      document.addEventListener(item, () => {
        Toast.error('set fullscreen error', '2000');
      });
    });
    // 组件销毁时处理
    return () => {
      // 当销毁组件时则关闭流播放
      props.stream && props.stream.stop();
      [...fullscreenchangeList, ...fullscreenerrorList].forEach((item) => {
        document.removeEventListener(item, () => {});
      });
    };
  }, []);

  /**
   *
   * @param {Function} handle 设置 state 的方法
   * @param {Moudle} value 赋给对于 state 的值
   */
  const handleState = (handle, value) => {
    handle(prevValue => ({ ...prevValue, ...value }));
  };

  const handleExitFull = (e) => {
    e.preventDefault();
    exitFullscreen();
  };

  const handleResume = () => {
    props.onChange && props.onChange({
      name: 'resumeFlag',
      stream: config.stream,
    });
  };

  useEffect(() => {
    if (props.init && !statusInit) {
      props.init(refItem.current);
      const current = {
        userID: props.stream.getUserId(),
        type: props.stream.getType(),
      };
      handleState(setConfig, current);
      setStatusInit(true);
    }
    handleState(setConfig, { ...props.config });
  }, [props.type, props.init, props.stream, props, statusInit]);

  return (
    <div key={props?.key} className={`${styles.item} ${props.className}`}>
      <div ref={refItem} className={styles['item-view']}>
      </div>
      {
        config.resumeFlag
        && <div className={styles['item-play-btn-container']} onClick={handleResume}>
            <img src='./play.png' className={styles['item-play-btn']}></img>
          </div>
      }
    </div>
  );
};

export default React.memo(Stream);
