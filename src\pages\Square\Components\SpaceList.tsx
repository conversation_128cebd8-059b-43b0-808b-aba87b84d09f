/**
 * @Description: 广场-空间
 */
import React, { useState, useEffect } from 'react';
import { connect, history } from 'umi';
import SpaceLists from '@/components/SpaceList'; // 空间
import eArrow from '@/assets/GlobalImg/e_arrow.png';
import { InfiniteScroll } from 'antd-mobile'; // 滚动加载
import NotDataRender from '@/components/NoDataRender'; // 暂无数据
import { Spin } from 'antd';
import type { DateItem } from './CollectList';
import styles from './index.less';

const initState: {
  pageNum: number;
  pageSize: number;
  total: number;
  spaceList: DateItem[];
} = {
  pageNum: 1, // 当前页
  pageSize: 30, // 每页条数
  total: 0, // 总条数
  spaceList: [], // 空间数据集合
};

const SpaceList: React.FC = (props: any) => {
  const [state, setState] = useState(initState); // 列表数据
  const [hasMore, setHasMore] = useState(true); // 更多数据状态
  const { dispatch, loading, starSpaceType } = props;
  const { pageNum, pageSize, total, spaceList } = state;

  useEffect(() => {
    getCollectListFn()
  }, [])

  // 空间列表数据
  const getCollectListFn = () => {
    return dispatch({
      type: 'square/getStarSpaceList',
      payload: {
        pageNum,
        pageSize,
        starSpaceType: starSpaceType == 2 ? 2: 1,
      },
    }).then((res) => {
      const { code, content } = res || {};
      if (res && code == 200) {
        const { total: responseTotal, resultList } = content || {};
        setState((prevState) => ({
          ...prevState,
          spaceList: [...prevState.spaceList, ...resultList],
          total: responseTotal,
          pageNum: prevState.pageNum + 1,
        }));
      } else {
        console.log('数据失败!');
      }
    });
  };

  // 加载更多数据
  const loadMore = async () => {
    await getCollectListFn();
  };

  // 去我的主页的会议模块
  const goMyMeetingSpace = () => {
    const token = localStorage.getItem('access_token');
    const userInfoData = JSON.parse(localStorage.getItem('userInfo'));
    // 未登录跳转至登录页
    if(!token){
      history.push({
        pathname: '/User/login',
        query: {
          redirectByPush: window.location.href,
        }
      })
    }else{
      // 跳转至专家详情页
      history.push(`/Expert/ExpertDetails?id=${userInfoData.friUserId}&tabKey=2`);
    }
  };

  // 监听列表数据变化
  useEffect(() => {
    if (spaceList.length >= total) {
      setHasMore(false);
    } else {
      setHasMore(true);
    }
    props.handleScroll && props.handleScroll();
  }, [spaceList, total]);
  const getStarSpaceListLoading = !!loading.effects['square/getStarSpaceList']; // loading

  return (
    <div className={spaceList && spaceList.length?'':styles.squareSpaceList_box}>
      <Spin spinning={getStarSpaceListLoading}>
        {starSpaceType ==2?<div style={{display:'flex',padding:'0 12px 12px 12px', justifyContent:'space-between', alignItems:'center'}}>
          <div style={{fontSize:'20px',fontWeight:'900'}}>公开会议</div>
          <span style={{ color:'#0095FF',marginRight:'5px'}} onClick={goMyMeetingSpace}>我的会议<img src={eArrow} width={10} height={10} alt=""/></span>
        </div>:<div style={{display:'flex'}}></div>}
        {spaceList && spaceList.length ? (
          <>
            <SpaceLists activitySpace={props.activitySpace} componentData={{ dataList: spaceList, config: { number: 1 } }} />
            <InfiniteScroll loadMore={loadMore} hasMore={hasMore} threshold={30} />
          </>
        ) : (
          <NotDataRender text={`${starSpaceType==2?'暂无会议':'暂无直播'}`} />
        )}
      </Spin>
    </div>
  );
};
export default connect(({ square, loading }: any) => ({ square, loading }))(SpaceList);
