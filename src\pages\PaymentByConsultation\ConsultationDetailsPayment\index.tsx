import React, { useState,useEffect } from 'react';
import { history,connect } from 'umi';
import styles from "./index.less";
import NavBar from "@/components/NavBar";
import {
  getConsultationOrderInfo,
  submitConsultationPictureOrderPay as submitConsultationPictureOrderPayAction,
  submitConsultationVideoOrderPay as submitConsultationVideoOrderPayAction,
  getConsultationOrderPayStatus
} from "@/services/consultation/ConsultationList";
import { getOperatingEnv,WxAppIdByPublicAccount } from "@/utils/utils";
import classNames from "classnames";
import {message, Spin} from "antd";
import { stringify } from "qs";
import {getJsapiTicket, getWxMpOpenId} from "@/services/payment";
import {debounce} from "lodash";

const ConsultationDetailsPayment: React.FC = (props) => {
  const { dispatch } = props;
  let { match: { params: { id } } } = props
  const [ consultationOrderInfo,setConsultationOrderInfo ] =  useState(null);
  const [ selectPayType,setSelectPayType ] =  useState(1); // 选择支付方式 1微信、2支付宝
  const [ loading,setLoading ] = useState(false); // 支付按钮loading状态
  const userInfoStr = localStorage.getItem('userInfo')
  const userInfo = userInfoStr ? JSON.parse(userInfoStr) : {}
  const { friUserId:idByUserInfo, name} = userInfo || {}
  let pollingId = null; // 支付状态计时器id
  const {
    id:idByOrderInfo, // : "a2c99d2774a54ddf900bfcd23b2be533",//指导订单ID
    orderNumber,      // : "2023100813402475762",//订单号
    type,             // : 1,//指导类型(1图文、2视频)
    isFinish,         // : 0,
    status,           // : 1,//支付状态(0已取消、1未支付(待评估)、2去支付(已评估)、3已支付)
    amount,           // : 0.10,//账单金额
    expertsName,      // : "zhang",//指导医生名称
    vipUnitPrice,     // : 1.00,//会员单价(图文是/次，视频是/30min)
    h5BaseUserDto,    // : 指导医生信息
  } = consultationOrderInfo || {};


  // 微信浏览器使用
  let OperatingEnv = getOperatingEnv();

  useEffect(async () => {
    // 这是H5订单支付页面进入此页面一定是为了支付订单
    // 当前操作环境是微信浏览器,并且本地不存在公众号的openId的情况
    // 先前往公众号静默授权
    // 当前在微信浏览器中使用
    // 授权后转发回此页面会携带code=081mf30w36eow03Is32w3VtcAZ3mf30C&state=STATE%23wechat_redirect
    let { match: { params: { orderId } },location } = props
    let { query:query_location } = location || {}
    let { code:code_query,} = query_location || {}

    const env = getOperatingEnv()

    if(env == 2) {
      const openIdByWx = localStorage.getItem('openIdByWx')
      // 在微信浏览器中未授权状态
      if(!openIdByWx && !code_query){
        // 不存在微信公众号的openID则需要跳转到公众号微信授权页面
        // 授权成功后会携带code重定向到步骤2订单确认页面
        let paramsByAuthorization = {
          appid: WxAppIdByPublicAccount,
          redirect_uri: `${window.location.origin}/PaymentByConsultation/ConsultationDetailsPayment/${id}?${stringify({ random:Math.random() })}`,
          response_type: 'code',  // 写死
          scope: 'snsapi_base',
          state: 'STATE#wechat_redirect',
        }
        // 跳转到公众号微信授权页面
        window.location.href = `https://www.friday.tech/getWXcodeInfo.html?${stringify(paramsByAuthorization)}`
        // window.location.replace(`https://open.weixin.qq.com/connect/oauth2/authorize?${stringify(paramsByAuthorization)}#wechat_redirect`)
      }
      if (!openIdByWx && code_query) {
        // 通过code获取openId
        await getWxMpOpenIdByFunc();
      }
    }

    // 查询订单详情
    await getConsultationOrderInfoByPage();
    return () => {
      if(pollingId){
        clearInterval(pollingId)
      }
    }
  },[])

  const getWxMpOpenIdByFunc = async ()=>{
    let { match: { params: { orderId } },location } = props
    let { query:query_location } = location || {}
    let { code:code_query,} = query_location || {}

    const openIdContentByWx = await getWxMpOpenId({
      appId:WxAppIdByPublicAccount,
      code:code_query
    })

    const { code,content } = openIdContentByWx || {}
    if(code == 200) {
      // 保存微信公众号OpenId
      localStorage.setItem('openIdByWx',content);
    }else {
      message.warning(openIdContentByWx && openIdContentByWx.msg ? openIdContentByWx.msg : '微信授权code失效')
    }
  }

  useEffect(() => {
    // 查询订单详情
    // status, // : 1,//支付状态(0已取消、1未支付(待评估)、2去支付(已评估)、3已支付)
    if(consultationOrderInfo && status == 3){
      goCompleteOrder()
    }else if(consultationOrderInfo && status != 3) {
      const env = getOperatingEnv()
      if(env == 3 || env == 5 || env == 6){
        getOrderIsPayByCallBack();
      }
    }
  },[consultationOrderInfo])

  // 查询指导订单详情
  const getConsultationOrderInfoByPage = async () => {
    let getConsultationOrderInfoByData = await getConsultationOrderInfo({
      consultationId:id, // [string] 是 指导订单ID
      type:'2'  //  // [string] 是 (1:运营端订单详情, 2:H5/WEB端视频订单详情)
    })

    const {
      code,
      content,
    } = getConsultationOrderInfoByData || {};
    if(code == 200 && content) {
      setConsultationOrderInfo(content);
    }else {
      setConsultationOrderInfo(null);
    }
  }

  // 去支付
  let goPay = async () => {
    //  1 指导类型(1图文、2视频)
    if (type == 1) {
      // 图文支付
      submitConsultationPictureOrderPay();
    }else if (type == 2) {
      // message.success('视频指导支付')
      submitConsultationVideoOrderPay();
    }
  }
  goPay = debounce(goPay, 300);

    // [支付]PC扫码支付后没有支付回调需要接口轮询判定支付状态
    // 当支付二维码生成之后每5秒获取状态一次
    // 修改设置轮询器在getOrderIsPayByCallBack调用之后
    const getOrderIsPayByCallBack = async (interval) => {
    const startTime = Date.now()
    const { match: { params: { orderId } }, location } = props
    if(!!pollingId){ clearInterval(pollingId);pollingId = null }
    pollingId = null // 定义轮询定时器 ID
    let id = idByOrderInfo;

    // 定义轮询函数
    const polling = async () => {
      const dataByOrder = await getConsultationOrderPayStatus({
        wxUserId: idByUserInfo,
        userName: name,
        id:id,       // 指导订单ID
      })
      // message.info(JSON.stringify({...dataByOrder,id}))
      if (dataByOrder && dataByOrder.code === 200) {
        const { content } = dataByOrder || {}
        if (content) {
          // 清除轮询定时器
          clearInterval(pollingId)
          // 支付完成,跳转到支付完成页面
          goCompleteOrder();
        }
      }
    }
    // 开始轮询，每 5 秒钟调用一次 polling 函数
    pollingId = setInterval(polling, 5000)
  }

  // 支付完成,跳转到支付完成页面
  const goCompleteOrder = () => {
    // 支付完成从收银台跳转到支付完成页面
    history.replace(`/PaymentByConsultation/CompleteOrder/${id}`);
  }



  // 图文指导提交订单或去支付
  const submitConsultationPictureOrderPay = async () => {
    const env = getOperatingEnv()
    await setLoading(true);
    let dataBySubmitConsultationPictureOrderPay = await submitConsultationPictureOrderPayAction({
      wxUserId: idByUserInfo,
      userName: name,
      id:idByOrderInfo,       // [int] 指导订单ID
      payType: selectPayType, // [int] 是 1微信、2支付宝
      operationPlatform: 1,   // [int] 是 操作平台 1H5 2PC
      payMethod: 2,           // [int] 是 图文 1提交指导 2立即支付
      freeTimes: null,        // [int] 否 免费次数
      amount: vipUnitPrice,   // [double] 是 金额
      browserType:  env == 2 ? 2 : 1, // [int] 是 浏览器类型 1普通浏览器 2微信浏览器
      payerOpenId:localStorage.getItem('openIdByWx'), // [string] 是 会����生的
    });
    await setLoading(false);

    const { code,content,msg } = dataBySubmitConsultationPictureOrderPay || {}
    if(code == 200 && content){
      if(selectPayType == 2) {
        // 支付宝去支付
        const div = document.createElement('div');
        div.innerHTML = content;
        document.body.appendChild(div);
        if (document.forms && document.forms.punchout_form) {
          document.forms.punchout_form.submit();
          getOrderIsPayByCallBack();
        }else {
          message.error('获取支付宝订单信息失败!')
        }
      }else {
        goWxPay(content)
      }
    }else {
      message.error(msg ? msg : '获取支付信息失败!')
    }
  }

  // 微信去支付
  const goWxPay = async (content) => {
    // 微信去支付
    const env = getOperatingEnv()
    if(env == 2){
      // 微信内浏览器
      paymentByWechatPublicAccount(content)
    }else {
      // 5 FRIDAY app中使用 6 Jarvis App内使用
      const userAgent = window.navigator.userAgent;
      let openv = getOperatingEnv();
      if ((openv == 5 || openv == 6) && /iPhone|iPad|iPod/i.test(userAgent)) {
        let redirect_url = `${window.location.origin}/PaymentByConsultation/CompleteOrder/${id}?env=${openv}`
        window.location.href = `${content}&${stringify({
          redirect_url:redirect_url,
        })}`
        getOrderIsPayByCallBack();
      }else {
        window.location.href = `${content}`
        getOrderIsPayByCallBack();
      }
    }
  }

  // 微信浏览器内微信公众号支付
  const  paymentByWechatPublicAccount = async (param)=>{
    const env = getOperatingEnv()
    const userInfoData = JSON.parse(localStorage.getItem('userInfo'));
    let content = param ? JSON.parse(param) : {};
    const { friUserId:id } = userInfoData || {} // 当前登录人id
    // 请求[getJsapiTicket]获取[wx.config]的配置
    const jsapiTicketContent = await getJsapiTicket({
      appId:WxAppIdByPublicAccount,
      currentUrl:window.location.href.split('#')[0],
    })
    const { code:codeByJsapi, content:contentByJsapi} = jsapiTicketContent || {}
    if(codeByJsapi != 200){
      message.warning(jsapiTicketContent && jsapiTicketContent.msg ?  jsapiTicketContent.msg : '获取微信配置失败!')
      return
    }
    // ① 先配置[wx.config]
    wx && wx.config({
      debug: false,
      appId: contentByJsapi.appId,
      timestamp: parseInt(contentByJsapi.timestamp),
      nonceStr: contentByJsapi.nonceStr,
      signature: contentByJsapi.signature,
      jsApiList: ['chooseWXPay'],              // 必填，随意一个接口即可
      // openTagList:['wx-open-launch-weapp'], // 填入打开小程序的开放标签名
    });
    const {
      timeStamp,
      nonceStr,
      packageValue, // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=\*\*\*）
      paySign, // 支付签名
      signType,// // 微信支付V3的传入RSA,微信支付V2的传入格式与V2统一下单的签名格式保持一致
      appId,
    } = content || {}
    // ② 再调用[wx.chooseWXPay]发起支付
    wx && wx.chooseWXPay({
      appId:appId,
      timestamp: timeStamp,   // 支付签名时间戳，注意微信jssdk中的所有使用timestamp字段均为小写。但最新版的支付后台生成签名使用的timeStamp字段名需大写其中的S字符
      nonceStr: nonceStr,     // 支付签名随机串，不长于 32 位
      package: packageValue,  // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=\*\*\*）
      signType: signType,     // 微信支付V3的传入RSA,微信支付V2的传入格式与V2统一下单的签名格式保持一致
      paySign: paySign,       // 支付签名
      success: function (res) {
        // 支付成功后的回调函数
        message.success('支付成功');
        // 支付完成,跳转到支付完成页面
        goCompleteOrder();
      },
      cancel: function(res) {
        message.warning('微信支付取消')
      },
      fail:function(res){
        message.warning('微信支付失败')
      }
    });
  }

  // 视频指导提交订单或去支付
  const submitConsultationVideoOrderPay = async () => {
    const env = getOperatingEnv()
    await setLoading(true);
    let dataBySubmitConsultationVideoOrderPay = await submitConsultationVideoOrderPayAction({
        wxUserId: idByUserInfo,
        userName: name,
        id:idByOrderInfo,       // 指导订单ID
        payType: selectPayType, // [int] 是 1微信、2支付宝
        operationPlatform: 1,   // [int] 是 操作平台 1H5 2PC
        payMethod: 2,           // [int] 是 图文 1提交指导 2立即支付
        freeTimes: null,        // [int] 否 免费次数
        browserType: env == 2 ? 2 : 1, // [int] 是 浏览器类型 1普通浏览器 2微信浏览器
        payerOpenId:localStorage.getItem('openIdByWx'), // [string] 是 会����生的
    })
    await setLoading(false);

    const { code,content,msg } = dataBySubmitConsultationVideoOrderPay || {}
    if(code == 200 && content){
      if(selectPayType == 2) {
        // 支付宝去支付
        const div = document.createElement('div');
        div.innerHTML = content;
        document.body.appendChild(div);
        if (document.forms && document.forms.punchout_form) {
          document.forms.punchout_form.submit();
          getOrderIsPayByCallBack();
        }else {
          message.error('获取支付宝订单信息失败!')
        }
      }else {
        // 去微信支付
        goWxPay(content);
      }
    }else {
      message.error(msg ? msg : '获取支付信息失败!')
    }
  }



  return (
    <Spin spinning={!!loading}>
    <div className={styles.Mobile_Wrap}>
      {true &&  // 当前是小程序端
        <div className={styles.Mobile_title_statusbar}></div>
      }
      {OperatingEnv != 1 && OperatingEnv != 2 &&  // 当前是小程序端
        <div className={styles.Mobile_title_Wrap}>
          <NavBar title={'支付订单'}></NavBar>
        </div>
      }
      <div className={styles.Warp_box}>
        <div className={styles.box_payment}>
          {/*<div className={styles.box_text}>
            支付剩余时间
          </div>*/}
          <div className={styles.box_text_price}>
            <span className={styles.box_text_unit}> ¥ </span>
            <span className={styles.box_text_num}>{type == 1 ?  vipUnitPrice || '0.00' : amount || '0.00'}</span>
          </div>
          <div
            onClick={()=>{
              history.replace(`/PaymentByConsultation/MyConsultationDetails/${id}`)
            }}
            className={styles.box_text}
          >
            { h5BaseUserDto && h5BaseUserDto.name } {type == 1 ? '图文指导' : '视频指导'} 订单详情
            <div className={styles.downArrow_icon_ConsultationDetailsPayment}></div>
          </div>
        </div>

        <div className={styles.box_select_modePayment}>
          <div className={styles.box_title}>选择支付方式</div>
          <div className={styles.box_select_modePayment_warp}>
            {[
              {
                id:1,
                name:'微信支付',
                desc:'使用微信支付',
              }].map((item,index)=>{
                return (<div
                  key={index}
                  onClick={()=>{
                    setSelectPayType(item.id)
                  }}
                  className={classNames({
                    [styles.box_select_modePayment_item]:true,
                  })}>
                    <div className={styles.payWarp}>
                      <div className={styles.pay_title}>
                        <div className={classNames({
                          [styles.pay_Icon_wechatPay]:item.id == 1,
                          [styles.pay_Icon_alipay]:item.id == 2,
                        })}></div>
                        <div className={styles.pay_text}>{item.name}</div>
                      </div>
                      <div className={styles.pay_desc}>
                        { item.desc }
                      </div>
                    </div>
                    <div className={styles.select_warp}>
                      <div className={classNames({
                        [styles.select_icon]:true,
                        [styles.select_icon_active]:selectPayType == item.id,
                      })}></div>
                    </div>
                </div>
                )
            })}
          </div>
        </div>
      </div>
        <div className={styles.Mobile_box_bottom_warp}>
            <div onClick={()=>{goPay()}} className={styles.Mobile_pay}>
              确认支付
            </div>
        </div>
      </div>
    </Spin>
  )
}
export default connect(({ login, loading }: any) => ({
  login, loading
}))(ConsultationDetailsPayment)
