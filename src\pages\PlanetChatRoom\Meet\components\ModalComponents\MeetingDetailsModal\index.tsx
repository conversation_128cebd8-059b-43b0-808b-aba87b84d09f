/**
 * @Description: 会议详情弹窗
 */
import React from 'react';
import { history, connect } from 'umi'
import { Popup } from 'antd-mobile';
import styles from './index.less';

interface PropsType {
  visible: boolean,          // 弹窗是否显示
  onCancel: () => void,      // 关闭弹窗
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {
    visible,
    PlanetChatRoom, // models数据
    onCancel, // 点击取消事件
  } = props

  const {
    SpaceInfo, // 空间信息
  } = PlanetChatRoom || {}

  const {
    name: nameBySpaceInfo, // 空间名称
    intro, // 介绍
    kingdomName, // 关联王国
    initiatorUserInfo, // 发起人信息
  } = SpaceInfo || {}

  const {
    name: nameByCreateUser, // 发起人名字
  } = initiatorUserInfo || {}

  return (
    <Popup
      visible={visible}
      onMaskClick={onCancel}
      className={styles.popup_container}
      destroyOnClose
    >
      <div className={styles.container}>
        {/* 头部 */}
        <div className={styles.header_line} onClick={onCancel}>
          <div className={styles.header_line_bar}></div>
        </div>

        {/* 内容 */}
        <div className={styles.content}>
          <div className={styles.meeting_name}>{nameBySpaceInfo}</div>
          <div className={styles.meeting_info}>
            <div className={styles.meeting_info_label}>发起人</div>
            <div className={styles.meeting_info_value}>{nameByCreateUser}</div>
          </div>
          <div className={styles.meeting_info}>
            <div className={styles.meeting_info_label}>介绍</div>
            <div className={styles.meeting_info_value}>{intro || '无'}</div>
          </div>
          <div className={styles.meeting_info}>
            <div className={styles.meeting_info_label}>关联王国</div>
            <div className={styles.meeting_info_value}>{kingdomName || '无'}</div>
          </div>
        </div>
      </div>
    </Popup>
  )
}
export default connect(({ PlanetChatRoom, loading }: any) => ({PlanetChatRoom, loading}))(Index)
