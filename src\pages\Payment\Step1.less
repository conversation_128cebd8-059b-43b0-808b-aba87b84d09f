@import (reference) '~antd/es/style/themes/index';

.page_warp {
  display: flex;
  justify-content: space-around;
}
.page_content {
  width: 1000px;
  // background: #3b434b;
}

// -------title导航条-------
.nav_title_warp {
  display: flex;
  justify-content: space-around;
  margin-top: 30px;
}

.nav_title {
  width: 604px;
  display: flex;
  justify-content: space-between;
  position: relative;

  .nav_item {
    text-align: center;
    width: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .nav_item_circle {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #3C89FD;
      border: 1px solid #3C89FD;
      z-index: 10;
      font-size: 16px;
      font-weight: bold;
    }
    .nav_item_circle_active {
      background-color: #3C89FD;
      color: #fff;
    }
    .nav_item_text {
      margin-top: 11px;
      font-size: 14px;
      font-weight: 400;
      color: #999999;
    }
    .nav_item_text_active {
      font-size: 14px;
      font-weight: 500;
      color: #333333;
    }
  }

  .nav_line {
    height: 1px;
    opacity: 1;
    background: #CCCCCC;
    width: calc(100% - 80px);
    position: absolute;
    left: 40px;
    top: 20px;
  }
}
// -------title导航条-------end

// -------填写信息-------start
.informationBox {
  width: 100%;
  min-height: 357px;
  background: #FAFAFA;
  padding-left: 30px;
  padding-right: 30px;
  padding-top:17px;
  padding-bottom: 30px;

  .informationBox_title {
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    margin-bottom:24px;
  }

  .informationBox_from_item {
    display: flex;
    margin-bottom: 15px;
    align-items: center;

    .informationBox_from_item_lable {
      width: 70px;
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      margin-right: 16px;
    }

    .informationBox_from_item_field {
      width: calc(100% - 86px);
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      :global {
        .ant-form-item {
          margin-bottom: 0;
        }
      }
    }

    .informationBox_from_item_field_input {
      :global {
        .ant-input {
          width: 350px;
        }
      }
    }

    .informationBox_from_item_field_Box {
      display: flex;
      align-items: center;

      .informationBox_from_item_field_value {
        margin-right:12px
      }

      .editIcon {
        width: 16px;
        height: 16px;
        background: url("../../assets/Payment/Payment_information_isEdit_icon.png");
        display: inline-block;
        background-size: 16px 16px;
        cursor: pointer;
        user-select: none;
      }
    }

    .informationBox_from_item_field_SingleOption_Wrap {
      display: flex;

      .informationBox_from_item_Warp {
        position: relative;
      }

      .informationBox_from_item_remarks {
        position: absolute;
        top: -13px;
        font-size: 10px;
        font-weight: 400;
        color: #FFFFFF;
        background: #FFAE35;
        border-radius: 2px 2px 8px 2px;
        opacity: 1;
        padding-left: 5px;
        padding-right: 5px;
        display: inline-block;
      }


      .informationBox_from_item_field_option {
        width: 250px;
        height: 50px;
        background: #FFFFFF;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        border: 1px solid #EEEEEE;
        margin-right: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        user-select: none;
      }
      .informationBox_from_item_field_option_active {
        border: 1px solid #3C89FD;
      }
      .informationBox_from_item_field_option_text {
        display: flex;
        align-items: center;

        .informationBox_from_item_field_option_text_num {
          font-size: 24px;
          font-weight: bold;
          color: #ED3232;
          margin-right: 5px;
        }
        .informationBox_from_item_field_option_text_unit {
          font-size: 14px;
          font-weight: 400;
          color: #666666;
        }
      }
    }

    .informationBox_from_item_field_price {
      display: flex;
      font-size: 14px;
      font-weight: 500;
      .informationBox_from_item_field_price_num {
        color: #ED3232;
      }
      .informationBox_from_item_field_price_unit {
        color: #666666;
      }
    }
  }

  .informationBox_from_submit_Btn {
    display: flex;
    justify-content: space-around;
    align-items: center;


    :global {
      .ant-btn {
        width: 134px;
        height: 36px;
        background: #3C89FD;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        color: #FFFFFF;
        font-size: 16px;
        border: none;
      }
    }
  }

  .isAgree_box {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin-bottom:6px;

    :global {
      .ant-form-item {
        margin-bottom: 0;
      }
    }

    .isAgree_box_checkBox_wrap {
      display: flex;
      :global {
        .ant-checkbox + span {
          padding-right: 0px;
          padding-left: 8px;
          user-select: none;
        }
      }
    }
    .isAgree_box_checkBox_agreement {
      font-size: 14px;
      font-weight: 400;
      color: #198CFF;
      cursor: pointer;
      user-select: none;
    }
  }
}
// -------填写信息-------end


// -------购买信息-------start
.PurchaseInformationBox {
  width: 100%;
  min-height: 178px;
  background: #FAFAFA;
  padding-left: 30px;
  padding-right: 30px;
  padding-top:17px;
  padding-bottom: 30px;

  .PurchaseInformationBox_title {
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    margin-bottom:12px;
  }

  .PurchaseInformationBox_content_item {
    width: 100%;
    display: flex;
    .PurchaseInformationBox_content_item_box {
      width: 50%;
      height: 36px;
      line-height: 36px;
      color: #666666;
      display: flex;

      .PurchaseInformationBox_content_item_box_lable {
        margin-right: 4px;
      }
    }
  }
  .text_red {
    color: #ED3232;
    font-weight: 500;
  }
}
// -------购买信息-------end


// -------支付方式-------start
.MethodPaymentWrap {
  margin-left: 32px;
  .MethodPaymentItem {
    display: flex;

    .MethodPaymentLable {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      margin-right: 16px;
      padding-top: 10px;
    }
    .MethodPaymentValue_option {
      display: flex;
      .MethodPaymentValueItem {
        width: 164px;
        height: 42px;
        background: #FFFFFF;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        border: 1px solid #EEEEEE;
        margin-right: 12px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        cursor: pointer;
        user-select: none;
      }
      .MethodPaymentValueItem:active {
        border: 1px solid #3C89FD;
      }

      .MethodPaymentValueItemBox {
        display: flex;
        align-items: center;
      }

      .MethodPaymentValueItemIconWechatBay {
        width: 24px;
        height: 24px;
        background: url("../../assets/Payment/Payment_MethodPayment_WechatPay.png");
        background-size: 24px 24px;
        margin-right: 8px;
      }

      .MethodPaymentValueItemIconCorporateTransfer {
        width: 24px;
        height: 24px;
        background: url("../../assets/Payment/Payment_MethodPayment_CorporateTransfer.png");
        background-size: 24px 24px;
        margin-right: 8px;
      }

      .MethodPaymentValueItemText {
        font-size: 14px;
        font-weight: 400;
        color: #333333;
      }
    }

    .PaymentMethodPaymentPromptBox {
      display: flex;
      align-items: center;
      margin-top: 12px;
    }

    .PaymentMethodPaymentPromptIcon {
      width: 16px;
      height: 16px;
      background: url("../../assets/Payment/Payment_MethodPayment_Prompt.png");
      display: inline-block;
      background-size: 16px 16px;
      cursor: pointer;
      user-select: none;
      margin-right: 3px;
    }

    .PaymentMethodPaymentPromptText {
      font-size: 14px;
      font-weight: 400;
      color: #999999;
      display: inline-block;
    }
  }


  .WechatBay_Box {
    display: flex;
    margin-top: 28px;
    flex-direction: column;
    align-items: center;
    padding-bottom: 30px;

    .WechatBay_Box_Wrap {
      display: flex;
      align-items: center;
      margin-bottom: 23px;

      .WechatBay_Box_icon {
        width: 32px;
        height: 32px;
        background: url("../../assets/Payment/Payment_MethodPayment_WechatPay.png");
        background-size: 32px 32px;
        margin-right: 6px;
      }
      .WechatBay_title_Box {
        .WechatBay_title_text {
          font-size: 16px;
          font-weight: 400;
          color: #333333;
        }
        .WechatBay_title_Desc {
          font-size: 12px;
          font-weight: 400;
          color: #999999;
        }
      }
    }

    .WechatBay_Box_QRCode {
      background: #FFFFFF;
      border-radius: 0px 0px 0px 0px;
      opacity: 1;
      border: 1px solid #EEEEEE;
      padding-top: 20px;
      padding-left: 20px;
      padding-right: 20px;
      padding-bottom: 22px;
      display: flex;
      flex-direction: column;
      align-items: center;

      .WechatBay_Box_QRCode_img {
        width: 118px;
        height: 118px;
        background: #D9D9D9;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        margin-bottom: 16px;
      }

      .WechatBay_Box_QRCode_text {
        font-size: 12px;
        font-weight: 400;
        color: #666666;
      }

    }
  }

  .PaymentCorporateTransfer_wrap {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
  }

  .PaymentCorporateTransfer {
    width: 520px;
    margin-top: 24px;
    padding-top: 20px;
    padding-left: 24px;
    padding-right: 24px;
    padding-bottom: 24px;
    background: #FAFAFA;
    border-radius: 2px 2px 2px 2px;
    opacity: 1;

    .PaymentCorporateTransfer_item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .PaymentCorporateTransfer_item_lable {
        font-size: 14px;
        font-weight: 400;
        color: #666666;
        margin-right: 12px;
        text-align: right;
        width: 70px;
      }
      .PaymentCorporateTransfer_item_value {
        font-size: 14px;
        font-weight: 400;
        color: #666666;
        width: cale(100% - 70px - 12px);
      }
    }

    .PaymentCorporateTransfer_item_upload {
      display: flex;
      align-items: flex-start;
    }
  }



  .PaymentCorporateTransfer_submit_wrap {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    :global {
      .ant-btn {
        width: 134px;
        height: 36px;
        background: #198CFF;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        font-size: 14px;
        font-weight: 400;
        color: #FFFFFF;
      }
    }
  }
}




// 移动端布局

  .Mobile_Wrap {
    width: 100%;
    min-width: 285px;
    height: 100vh;
    background: linear-gradient(180deg, #EDE7FF 0%, #FFFFFF 100%, #FAFAFA 100%);
    border-radius: 0px 0px 0px 0px;
    opacity: 1;
    padding-left: 12px;
    padding-right: 12px;
  }
  // app中
  .in_app_Mobile_Wrap .Mobile_tab {
    margin-top: 12px;
  }

  .Mobile_title_statusbar {
    width: 100%;
    height: 44px;
  }

  .Mobile_title_Wrap {
    width: 100%;
    height: 44px;
    margin-bottom: 12px;
    display: flex;
    justify-content: center;
    align-items: center;

    .Mobile_title {
      font-size: 17px;
      font-weight: 500;
      color: #000000;
    }
  }

  .Mobile_tab {
    width: 100%;
    height: 41px;
    display: flex;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    overflow: hidden;

    .Mobile_tab_item {
      width: 50%;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
      text-align: center;
      line-height: 41px;
      background: #EDE6FF;
      position: relative;
      display: flex;
      justify-content: center;
    }

    .Mobile_tab_item_active {
      background: #FFFFFF;
      border-top-right-radius: 4px;
      .Mobile_tab_item_active_line {
        position: absolute;
        bottom: 0px;
        width: 24px;
        height: 4px;
        background: #AE66F6;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
      }
    }
  }

  .Mobile_box {
    height: calc(100vh - 44px - 44px - 41px - 12px);
    min-height: 480px;
    background-color: #FFFFFF;
    border-radius: 4px 4px 4px 4px;
    padding-top: 12px;
    padding-left: 12px;
    padding-right: 12px;
    padding-bottom: 12px;
    position: relative;


    .Mobile_submit_warp {
      width: calc(100% - 24px);
      position: absolute;
      bottom: 0px;
      padding-bottom: 30px;

      .Mobile_submit_agree {
        display: flex;
        justify-content: center;
        margin-bottom: 16px;
        :global {
          .ant-checkbox + span {
            padding-right: 0px;
          }
        }
      }

      .Mobile_submit_agree_agreement {
        color:#A95CF5
      }


      .Mobile_submit_btn_wrap {
        .Mobile_submit_btn {
          width: 100%;
          height: 40px;
          background: linear-gradient(270deg, #8966FF 0%, #6BA6FF 100%);
          border-radius: 28px 28px 28px 28px;
          opacity: 1;
          font-size: 14px;
          font-weight: 400;
          color: #FFFFFF;
          line-height: 40px;
          text-align: center;
          cursor: pointer;
          user-select: none;
        }
      }
    }
  }


  .Mobile_FormWrap {
    .Mobile_FormItem_title {
      font-size: 14px;
      font-weight: 400;
      color: #AAAAAA;
      margin-bottom: 12px;
      margin-top: 20px;
    }
    .Mobile_FormItem_options {
      display:flex;
      align-items: center;
      justify-content: space-between;

      .Mobile_FormItem_option {
        flex: 1;
        //width: calc(50% - 3px);
        height: 88px;
        background: #FBFBFB;
        border-radius: 4px 4px 4px 4px;
        opacity: 1;
        border: 1px solid #EEEEEE;
        cursor: pointer;
        user-select: none;

        .Mobile_Title_box {
          height: 23px;
          display: flex;
          justify-content: center;
        }
        .Mobile_Title_box_icon {
          width: 68px;
          height: 23px;
          background: #F1F1F1;
          border-radius: 0px 0px 12px 12px;
          opacity: 1;
          font-size: 14px;
          font-weight: 500;
          color: #333333;
          text-align: center;
        }
        .Mobile_Title_box_Content {
          width: 100%;
          height: calc(100% - 23px);
          display: flex;
          padding-top: 15px;
          align-items: center;
          flex-direction: column;

          .Mobile_Title_box_text {
            display: flex;
            height: 30px;
            align-items: flex-end;
            position: relative;
            top: -10px;

            .Mobile_Option_unit {
              font-size: 14px;
              font-weight: 400;
              color: #EB4C4C;
              // height: 30px;
            }
            .Mobile_Option_num {
              font-size: 24px;
              font-weight: 600;
              color: #EB4C4C;
              height: 30px;
            }
            .Mobile_Option_unit_time {
              font-size: 14px;
              font-weight: 500;
              color: #4A3F55;
              // height: 30px;
            }
          }
        }

        .Mobile_Option_text_desc {
          font-size: 10px;
          font-weight: 400;
          color: #AAAAAA;
        }
      }

      .Mobile_FormItem_option_active {
        background: #F1ECFF;
        opacity: 1;
        border: 1px solid #AE66F6;
        .Mobile_Title_box_icon {
          background: #AE66F6;
          color: #FFFFFF;
        }
      }
    }

    .Mobile_FormItem_Item {
      width: 100%;
      height: 60px;
      display: flex;
      border-bottom: 1px solid #F5F5F5;
      justify-content: space-between;
      align-items: center;
      margin-top: 2px;

      .Mobile_FormItem_Item_lable {
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        width: 80px;
      }
      .Mobile_FormItem_Item_value {
        width: calc(100% - 80px);
        :global {
          .ant-input {
            width: 100%;
            height: 40px;
            border: 0px;
            font-size: 14px;
            font-weight: 400;
            color: #333333;
            text-align: right;
          }
          .ant-input:focus, .ant-input-focused {
            border: 0px;
            box-shadow: none;
          }
          .ant-input:hover {
            border: 0px;
            box-shadow: none;
          }
          .ant-form-item {
            margin-bottom: 0;
          }

          .ant-form-item-with-help .ant-form-item-explain {
            text-align: right;
            position: relative;
            right: 10px;
            top: -13px;
          }
        }
      }
    }

    .Mobile_Item_amount {
      width: 100%;
      height: 29px;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      margin-top: 15px;

      .Mobile_Item_Title {
        font-size: 14px;
        font-weight: 400;
        color: #999999;
      }

      .Mobile_Item_amount_num_box {
        font-weight: 400;
        color: #EB4C4C;

        .Mobile_Item_amount_unit {
          font-size: 12px;
        }
        .Mobile_Item_amount_num {
          font-size: 24px;
          font-weight: 600;
        }
      }

    }
  }



