.drawer {
  :global {
    .ant-drawer-body {
      padding: 0;
      background: #141414;
    }
  }
}

.header {
  font-size: 15PX;
  color: #fff;
  font-weight: 500;
  line-height: 21PX;
  padding: 24PX 24PX 16PX;
}

.action_item {
  :global {
    .ant-switch {
      background: #E1E4E7;
      min-width: 48PX;
      height: 26PX;
      line-height: 26PX;
      outline: 0;
      box-shadow: none;
    }
    .ant-switch-checked {
      background-color: #1890ff;
    }
    .ant-switch-handle {
      width: 22PX;
      height: 22PX;
    }
    .ant-switch-handle::before {
      border-radius: 11PX;
    }
    .ant-switch-checked .ant-switch-handle {
      left: calc(100% - 22PX - 2PX);
    }
    .ant-click-animating-node {
      display: none;
    }
  }
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16PX;
  padding: 0 24PX;
  .action_item_label {
    font-size: 14PX;
    color: #fff;
    line-height: 20PX;
  }
}
