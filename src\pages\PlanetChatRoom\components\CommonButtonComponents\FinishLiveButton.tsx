// 开始直播组件
import React from 'react';
import { PlayCircleOutlined } from '@ant-design/icons';
import styles from './index.less';  // 引入自定义样式

const FinishLiveButton = ({ isLive, isJoined, currentUserType, resetTimer, getSpaceInfo, dispatch }) => {

  return (
    <div className={styles.HorizontalLiveRoom_Btn_Warp}>
    <div
      onClick={async (e)=>{
        // 点击后阻止事件冒泡,防止触发父元素的点击事件
        e.stopPropagation(); resetTimer();
        // 结束直播 关闭空间
        await dispatch({
          type:'PlanetChatRoom/setState',
          payload:{
            ModalVisibleByClosedSpace:true,
          }
        })
      }}
      className={styles.finish_live}/>
      <div style={{color:'#FF5F57'}} className={styles.text}>关闭</div>
    </div>
  );
};

export default FinishLiveButton;
