import { connect, history } from 'umi';
import classNames from 'classnames'
import { WxAppIdByPublicAccount, getShareUrl } from '@/utils/utils'
import React, {useEffect, useState} from 'react';
import {Input, message, Spin} from 'antd'
import { Dialog, Toast } from 'antd-mobile'
import styles from './BlankLayout.less';
import LiveSmallWindow from "../pages/PlanetChatRoom/components/LiveSmallWindow";
import { getOperatingEnv, getWXWorkAccountAuth, getCurrentWXWorkApp, getArrailUrl } from '@/utils/utils'
import TimObj from "@/components/TimObj";
import {
  getMsgCodeUserInfo,
} from "@/services/login/login";
import { getWXWorkAuthInfo, wxWorkLoginUserToken} from "@/services/common/api"
import SpaceRoomReservationCard from "@/components/SpaceRoomReservationCard";

const BlankLayout = props => {
  const {
    global,
    dispatch,
    children,
    PlanetChatRoom,
    login,
  } = props;
  const [loading, setLoading] = useState(false);  // 微信中获取用户授权信息loading
  const userInfoData = JSON.parse(localStorage.getItem('userInfo'));
  const { friUserId } = userInfoData || {}  // 获取用户
  const { isShowLiveSmallWindow } = PlanetChatRoom || {}
  // 获取地址栏
  const { pathname, search, href } = window.location;
  // pathname 不包含 PlanetChatRoom
  const isPlanetChatRoom = pathname.indexOf('PlanetChatRoom') === -1;
  // query参数获取
  const {
    ReservationId, // 预约空间id
  } = props?.location?.query || {};

  useEffect(() => {
    getHomePageLink()

    // 获取GDP说明文案
    if(global.gdpExplain==null){
      getGDPExplain()
    }
    if (pathname == '/' && /(iPhone|iPad|iPod|iOS)/i.test(window.navigator.userAgent)) {
      onShareAppMessage(window.location.origin + '/')
    }

    // 非生产环境，看fridayapp参数使用
    if (!location.host.includes('dhealth.friday.tech')&&getOperatingEnv() == 5) {
      Dialog.alert({
        title: 'app本地存储的数据',
        content: (
          <>
            <div style={{display: 'flex', flexWrap: 'nowrap'}}>
              <div style={{whiteSpace: 'nowrap'}}>access_token：</div>
              <div style={{flex: 1, wordBreak: 'break-all'}}>{localStorage.getItem('access_token')}</div>
            </div>
            <div style={{display: 'flex', flexWrap: 'nowrap'}}>
              <div style={{whiteSpace: 'nowrap'}}>client：</div>
              <div style={{flex: 1, wordBreak: 'break-all'}}>{localStorage.getItem('client')}</div>
            </div>
            <div style={{display: 'flex', flexWrap: 'nowrap'}}>
              <div style={{whiteSpace: 'nowrap'}}>phone：</div>
              <div style={{flex: 1, wordBreak: 'break-all'}}>{localStorage.getItem('phone')}</div>
            </div>
            <div style={{display: 'flex', flexWrap: 'nowrap'}}>
              <div style={{whiteSpace: 'nowrap'}}>user_name：</div>
              <div style={{flex: 1, wordBreak: 'break-all'}}>{localStorage.getItem('user_name')}</div>
            </div>
            <div style={{display: 'flex', flexWrap: 'nowrap'}}>
              <div style={{whiteSpace: 'nowrap'}}>wxuserId：</div>
              <div style={{flex: 1, wordBreak: 'break-all'}}>{localStorage.getItem('wxuserId')}</div>
            </div>
            <div style={{display: 'flex', flexWrap: 'nowrap'}}>
              <div style={{whiteSpace: 'nowrap'}}>userInfo：</div>
              <div style={{flex: 1, wordBreak: 'break-all'}}>{localStorage.getItem('userInfo')}</div>
            </div>
            <div style={{display: 'flex', flexWrap: 'nowrap'}}>
              <div style={{width: 60, wordBreak: 'break-all'}}>TRTC_checkResult：</div>
              <div style={{flex: 1, wordBreak: 'break-all'}}>{localStorage.getItem('TRTC_checkResult')}</div>
            </div>
          </>
        ),
      })
    }
  }, [])

  useEffect(() => {
    if (getOperatingEnv() == '4') {
      return
    }

    if (
      pathname == '/' ||
      pathname == '/Redirect' ||
      pathname == '/ExternalPage' ||
      pathname == '/Payment/MemberBenefitsPage' ||
      pathname == '/Expert/ExpertDetails' ||
      pathname == '/Case/CaseDetails' ||
      pathname == '/User/login' ||
      pathname == '/User/register' ||
      pathname == '/User/forgetPassword' ||
      pathname == '/Poster' ||
      pathname.indexOf('/Kingdom/') > -1 ||
      pathname.indexOf('/PlanetChatRoom/') > -1
    ) {
      return
    }
    onShareAppMessage()
  }, [pathname])

  // 企微环境授权相关=>未登录且在企微环境中
  useEffect(() => {
    if(getOperatingEnv() == '7'&&history.location.hash.indexOf('FridayHome') == -1){
      const UerInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
      const token= localStorage.getItem('access_token')
      if (!localStorage.getItem('access_token')) {
        getWXWorkAuthUserInfo()
      }
    }
  }, [])

  // 微信分享配置
  const onShareAppMessage = (originUrl) => {
    const url = window.location.href
    const shareUrl = getShareUrl(url)

    dispatch({
      type: 'userInfoStore/getJsapiTicket',
      payload: {
        currentUrl: originUrl || url,                                   // 页面url
        appId: WxAppIdByPublicAccount,                     // 公众号appId
      },
    }).then(res => {
      if (res && res.code == 200) {
        wx.config({
          debug: false,
          appId: res.content.appId,
          timestamp: res.content.timestamp,
          nonceStr: res.content.nonceStr,
          signature: res.content.signature,
          jsApiList: [
            'updateAppMessageShareData',
            'updateTimelineShareData',
            'onMenuShareTimeline',
            'onMenuShareAppMessage',
            'onMenuShareQQ',
            'onMenuShareWeibo',
            'onMenuShareQZone',
          ],
        })
        wx.ready(() => {
          const shareDate = {
            title: '【FRIDAY医生星球】牙医都来这里学习和交流！',
            desc: '空间聊天室、在线课程、全国病例库、视频问专家',
            link: shareUrl,
            imgUrl: 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/DigitalHealthShareImg.png',
          };
          console.log(shareDate)
          wx.updateAppMessageShareData(shareDate);
          wx.updateTimelineShareData(shareDate);
          wx.onMenuShareTimeline(shareDate);
          wx.onMenuShareAppMessage(shareDate);
          wx.onMenuShareQQ(shareDate);
          wx.onMenuShareWeibo(shareDate);
          wx.onMenuShareQZone(shareDate);
        })
      } else {
        // Toast.show('请求微信配置失败～！')
      }
    })
  }

  // 监听报错，刷新页面
  useEffect(() => {
    const handleLoadingChunkError = (error) => {
      // Loading chunk报错重新加载
      const loadFailed1 = new RegExp(/Loading chunk (\d)+ failed/g)
      const loadFailed2 = new RegExp(/Loading CSS chunk (\d)+ failed/g)
      if (error && error.message && (error.message.match(loadFailed1) || error.message.match(loadFailed2))) {
        window.location.reload()
      }
    }

    window.addEventListener('error', handleLoadingChunkError)
    return () => {
      window.removeEventListener('error', handleLoadingChunkError)
    }
  }, [])


  // 获取GDP说明文案
  const getGDPExplain = () => {
    dispatch({
      type: "global/gdpExplain",
    }).then((res) => {
      const { code, msg } = res || {}
      if (code != 200) {
        message.error(msg)
      }
    }).catch((err) => {
      console.log(err)
    })
  }

  // 获取企微授权用户信息
  const getWXWorkAuthUserInfo = async() => {
    setLoading(true)
    // 走授权登录逻辑
    let { location } = props;
    let { query:query_location } = location || {}
    let { code:code_query,} = query_location || {}
    if(!!code_query) {
      const wxWorkAuthInfo = await getWXWorkAuthInfo({
        code:code_query,
        appId:getCurrentWXWorkApp().appid,
      })
      const { code,content } = wxWorkAuthInfo || {}
      if(code == 200){
        localStorage.setItem('wcUserId', content.userId);  //存储企微用户id
        if(content.code==1){
          // 走自动登录逻辑
          if(content.mobile){
            wxWorkLoginToken( content.mobile, content.userKey)
          }else{
            Toast.show('请授权个人敏感信息，否则无法登录医生星球！');
            setLoading(false)
            return;
          }
        }else{
          // 走注册逻辑
          if(content.mobile){
            setLoading(false) // 关闭loading
            history.push({
              pathname: '/User/register',
              query: {
                weComToUcKey: content.userKey,
                inputPhone: content.mobile,
                redirect: window.location.href,
              }
            })
          }else{
            Toast.show('请授权个人敏感信息，否则无法登录医生星球！');
            setLoading(false)
            return;
          }
        }
      }else{
        setLoading(false) // 获取企微授权code失败，关闭loading
        message.warning(wxWorkAuthInfo && wxWorkAuthInfo.msg ? wxWorkAuthInfo.msg : '企微授权code失效')
      }
    }else{
      // 去授权页
      getWXWorkAccountAuth()
    }
  }

  // 自动登录获取token
  const wxWorkLoginToken = async (mobile, weComToUcKey) => {
    const wxWorkAuthToken = await wxWorkLoginUserToken({
      weComToUcKey
    })
    const { code,content } = wxWorkAuthToken || {};
    if(code == 200){
      // 走自动登录逻辑
      wxWorkLoginUserInfo(mobile,content.access_token)
    }else{
      setLoading(false) // 获取token失败，关闭loading
      Toast.show(wxWorkAuthToken && wxWorkAuthToken.msg ? wxWorkAuthToken.msg : '企微授权获取token失效');
      return;
    }
  }

  // 自动登录获取用户信息
  const wxWorkLoginUserInfo = async (mobile,token_text) => {
    localStorage.setItem('access_token', token_text);
    localStorage.setItem('userInfo', JSON.stringify({
      phone: mobile
    }));
    let userInfo = await getMsgCodeUserInfo({ token:token_text, username: mobile});
    if (userInfo?.code === 200) {
      setLoading(false) // 关闭loading
      // 保存用户信息
      localStorage.setItem('userInfo', JSON.stringify({
        ...userInfo.content,
        id: userInfo?.content?.friUserId
      }));
      Toast.show('企微授权登录成功！');
    }else{
      setLoading(false) // 获取用户信息失败，关闭loading
      Toast.show(userInfo?.msg ? userInfo.msg : '企微授权登录失败！');
    }
  }

  // 获取首页链接地址
  const getHomePageLink = () => {
    dispatch({
      type: 'activity/getHomePageLink',
      payload: {}
    }).then(res => {
      const { code, content, msg } = res
      if (code == 200 && content) {
        localStorage.setItem('homePageLink', content.pageUrl || '')
        localStorage.setItem('squarePageLink', content.directUrl || '')
      }
    })
  }

  return (
    <div className={styles.layoutWarp} style={{minWidth: getOperatingEnv() == 4 ? "1250px" : 'auto'}}>
      {    pathname.indexOf('PlanetChatRoom') == -1
        && pathname.indexOf('login') == -1
        && pathname.indexOf('Register') == -1
        && pathname.indexOf('register') == -1
        && pathname.indexOf('Agreement') == -1
        && pathname.indexOf('AppDownload') == -1
        && isShowLiveSmallWindow
        &&
        <div className={styles.LiveSmallWindowWarp}>
          <LiveSmallWindow/>
        </div>
      }
      <div className={classNames(styles.layout_content, {
        [styles.layout_content_pc]: getOperatingEnv() == 4 && (
          pathname == '/Expert/ExpertAdvice'
          || pathname == '/Expert/ExpertSearch'
          || pathname == '/Case/CaseInputList'
          || pathname == '/Case/CaseSearch'
          || pathname == '/Home/MiniProgramCode'
          || pathname == '/Home/Search'
          || pathname.indexOf('/Square') > -1
          || pathname.indexOf('/Kingdom') > -1
          || pathname.indexOf('/PaymentByConsultation') > -1
          || pathname == '/CreateGraphicsText/CreatePost'
          || pathname == '/CreateGraphicsText/PostDetails'
          || pathname == '/CreateGraphicsText/ArticleDetails'
          || pathname == '/CreateGraphicsText/ExternalLinksDetails'
          || pathname == '/CreateGraphicsText/SpaceDetails'
          || pathname == '/CreateGraphicsText/CreateForward'
          || pathname == '/CreateGraphicsText/ForwardDetails'
          || pathname == '/CreateGraphicsText/SelectTopic'
          || pathname == '/CreateGraphicsText/SelectKingdom'
          || pathname == '/CreateGraphicsText/TopicHome'
        )
      })}>
        <Spin spinning={loading} wrapperClassName={styles.spin}>{children}</Spin>
        {/*{children}*/}
      </div>

      {/* 解决ios系统中input输入框不能自动聚焦的隐藏input框 */}
      <div style={{height: 0, overflow: 'hidden', opacity: 0}}>
        <Input id="input_ios_focus" readOnly autoComplete="off" />
      </div>

      {/* 预约直播弹窗 */}
      {pathname.indexOf('/Square') > -1 && !!ReservationId &&
        <SpaceRoomReservationCard
          open={!!ReservationId}
          ReservationId={ReservationId}
          clearParams={()=>{
            const {
              pathname,
              query
            } = props?.location || {}
            const { ReservationId } = query || {}
            history.replace({
              pathname: pathname,
              query: {
               ...query,
                ReservationId: null,
              }
            })
          }}
        />
      }

      {/*登录后获取该用户的im秘钥信息*/}
      {/*{
        (
          pathname.indexOf('/PlanetChatRoom') != -1
          || pathname.indexOf('/ConsultationModule/ConsultationDetails') != -1
        )
        &&
       <TimObj id={id}/>
      }*/}
    </div>
  );
};

export default connect(({ global, PlanetChatRoom, login }) => ({ global, PlanetChatRoom, login }))(BlankLayout);
