.spin {
  height: 100%;
  & > :global(.ant-spin-container) {
    height: 100%;
  }
}

.container {
  height: 100%;
  overflow-y: auto;
  background: #fff;
  padding: 44px 0 34px;
  :global {
    .ant-upload.ant-upload-select-picture-card {
      margin: 0;
      width: 112px;
      height: 112px;
      border-color: #ccc;
      border-radius: 8px;
      background: #fff;
    }
    .adm-input-element {
      height: 62px;
    }
  }
}

.gray_bar {
  width: 100%;
  height: 8px;
  flex-shrink: 0;
  background: #F5F6F8;
}

.content {
  width: 100%;
  padding: 20px 0 40px 16px;

  .content_title {
    font-size: 24px;
    color: #000;
    font-weight: 500;
    line-height: 34px;
    margin-bottom: 8px;
  }

  .content_tips {
    font-size: 12px;
    color: #999;
    line-height: 17px;
    margin-bottom: 28px;
  }

  .form_item_wrap {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    column-gap: 8px;
    border-bottom: 1px solid #E1E4E7;
    padding-right: 16px;
    &.error {
      border-color: #f00;
    }
    .form_item_label {
      flex-shrink: 0;
      white-space: nowrap;
      font-size: 16px;
      color: #000;
      .remark {
        color: #F00;
      }
    }
    .form_item_content {
      flex: 1;
    }
  }

  .form_error_tips {
    font-size: 13px;
    color: #f00;
    line-height: 18px;
    margin-top: 8px;
  }

  .form_upload_img {
    padding: 20px 0 0 0;
    .upload_img_title {
      font-size: 16px;
      color: #000;
      line-height: 22px;
      margin-bottom: 8px;
    }
    .upload_img_tips {
      font-size: 12px;
      color: #999;
      line-height: 17px;
      margin-bottom: 16px;
      padding-right: 16px;
    }
    .upload_img_list {
      display: flex;
      flex-wrap: wrap;
      column-gap: 8px;
      row-gap: 8px;
      .upload_img_item {
        position: relative;
        img {
          border-radius: 8px;
          object-fit: cover;
        }
        .delete_icon_wrap {
          position: absolute;
          top: -10px;
          right: -7px;
          z-index: 1;
        }
      }
    }
  }
}

.btn_wrap {
  width: 100%;
  padding: 0 16px 11px;
  .btn {
    width: 100%;
    height: 40px;
    line-height: 40px;
    background: #0095FF;
    color: #fff;
    border-radius: 20px;
    font-size: 16px;
    text-align: center;
    &.disabled {
      background: #ccc;
    }
  }
}
