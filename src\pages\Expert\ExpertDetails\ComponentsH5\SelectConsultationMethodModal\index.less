.popup_container {
  :global {
    .adm-popup-body {
      border-radius: 16px 16px 0 0;
    }
  }
}
.header_line {
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  .header_line_bar {
    width: 48px;
    height: 4px;
    background: #D0D4D7;
    border-radius: 4px;
  }
}
.header_title {
  position: relative;
  font-size: 18px;
  line-height: 25px;
  text-align: center;
  font-weight: 500;
  margin-bottom: 12px;
  .close_icon {
    position: absolute;
    width: 32px;
    height: 32px;
    top: -3px;
    right: 16px;
    color: #000;
    display: flex;
    align-items: center;
    justify-content: center;
    :global {
      .anticon {
        font-size: 16px;
      }
    }
  }
}

.complete_process_wrap {
  padding: 0 16px;
}

.content {
  padding: 16px 16px 53px;
  .block {
    border: 1px solid #EEEEEE;
    display: flex;
    flex-wrap: nowrap;
    padding: 16px;
    margin-bottom: 16px;
    .block_icon {
      width: 40px;
      min-width: 40px;
      height: 40px;
      border-radius: 12px;
      margin-right: 12px;
      &.icon1 {
        background: url("../../../../../assets/Consultation/H5/photo_icon.png") no-repeat center;
        background-size: 100% 100%;
      }
      &.icon2 {
        background: url("../../../../../assets/Consultation/H5/video_icon.png") no-repeat center;
        background-size: 100% 100%;
      }
    }
    .block_arrow {
      width: 24px;
      min-width: 24px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #000;
      font-size: 14px;
    }
    .block_details {
      flex: 1;
      .details_title_box {
        display: flex;
        align-items: baseline;
        margin-bottom: 4px;
        .details_title {
          font-size: 16px;
          font-weight: 500;
          color: #000;
          line-height: 22px;
          margin-right: 5px;
        }
        .details_price_1 {
          font-size: 16px;
          font-weight: bold;
          color: #FF5F57;
          line-height: 22px;
        }
        .details_price_unit {
          font-size: 12px;
          color: #FF5F57;
          margin-right: 4px;
        }
        .details_price_2 {
          font-size: 11px;
          color: #999;
          text-decoration: line-through;
        }
      }
      .details_text {
        font-size: 11px;
        color: #999;
        line-height: 15px;
        word-break: break-all;
      }
    }
  }
}
.bottom_box {
  position: absolute;
  left: 0;
  right: 0;
  width: 100%;
  bottom: 0;
  .bottom {
    width: 100%;
    background: #FFF7DA;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #8C772B;
    font-size: 12px;
    height: 21px;
    line-height: 21px;
    :global {
      .anticon {
        font-size: 13px;
        margin-right: 3px;
      }
    }
    .highlight {
      color: #FF5F57;
    }
  }
}
.in_iphone {
  .content {
    padding-bottom: 87px;
  }
  .bottom_box {
    padding-bottom: 34px;
  }
}
