.content {
  width: 100%;
  height: calc(100vh - 343px);
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}
.tab_wrap {
  width: 100%;
  height: 43px;
  line-height: 43px;
  padding-left: 12px;
  margin-bottom: 12px;
  display: flex;
  border-bottom: 1px solid #E9EEF2;

  .tab_init {
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    margin-right: 24px;
    height: 27px;
    cursor: pointer;

    &.tab_active {
      font-size: 14px;
      color: #0095FF;
      position: relative;
    }
  }
}

.tab_content {
  width: 100%;
  height: calc(100vh - 343px - 43px);

  .agreementBox {
    width: 100%;
    height: 100%;
    overflow-y: scroll;
  }

  img {
    width: 100%;
    height: auto;
  }

  .logou_box {
    width: 100%;
    height: calc(100vh - 343px - 43px);
    position: relative;

    .logou_agreement_img {
      width: 100%;
      height: calc(100% - 90px);
      overflow-y: auto;
    }

    .logou_btn_box {
      width: 100%;
      height: 86px;
      position: absolute;
      bottom: 25px;
      left: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      background: #fff;

      .logou_text {
        margin-bottom: 8px;
        cursor: pointer;

        :global {
          .ant-checkbox {
            border-radius: 50%!important;
            overflow: hidden;

            .ant-checkbox-checked .ant-checkbox-inner {
              background: #44BB5E!important;
              border: #44BB5E!important;
            }
          }
        }

        span {
          font-size: 13px;
          font-weight: 400;
          color: #666666;
          border-radius: 50%;

          &.logou_agreement {
            color: #0095FF;
          }
        }
      }

      .logou_btn {
        width: 343px;
        height: 40px;
        background: #FF5F57;
        border-radius: 20px;
        text-align: center;
        font-size: 16px;
        font-weight: 400;
        color: #FFFFFF;
        border: none;

        span {
          display: block;
        }
      }
    }
  }
}

.modal_wrap {
  :global {
    .ant-modal {
      
    }
    .ant-modal-title {
      font-size: 18px;
      font-weight: 400;
      color: #303133;
      line-height: 26px;
    }
    .ant-modal-body {
      padding: 24px 16px;
      box-sizing: border-box;
      font-size: 16px;
      font-weight: 400;
      line-height: 26px;
      color: #000000;
    }

    .ant-modal-header {
      border-bottom: none;
      padding: 15px;
      box-sizing: border-box;
    }
    .ant-modal-footer {
      border-top: none;
    }
  }
  
}