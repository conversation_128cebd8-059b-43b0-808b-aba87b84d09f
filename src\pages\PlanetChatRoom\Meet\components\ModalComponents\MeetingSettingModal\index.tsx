/**
 * @Description: 会议详情-会议设置弹窗
 */
import React, { useState, useEffect } from 'react';
import { history, connect } from 'umi'
import { Modal, Switch } from 'antd-mobile';
import styles from './index.less';

interface PropsType {
  visible: boolean,                // 弹窗是否显示
  onCancel: () => void,            // 点击取消的回调
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const {
    visible,
    PlanetChatRoom,
    onChangeIsSpectatorUse, // 不允许非参会人进入开关修改
    onChangeHandUpType, // 允许申请发言开关修改
    onChangeIsShowPassword, // 展示会议密码开关修改
    onChangeIsOpenSmallWindow, // 小窗播放开关修改
  } = props

  const {
    SpaceInfo, // 直播间信息
  } = PlanetChatRoom || {}

  const {
    isSpectatorUse, // 是否允许非参会人进入 1 是 0否(页面中1为关0为开)
    handUpType, // 举手类型    0:开启 1:关闭
    isShowPassword, // 是否展示密码 1是 0否
    password, // 直播间密码
  } = SpaceInfo || {}

  const [isOpenSmallWindow, setIsOpenSmallWindow] = useState(false)   // 小窗播放开关

  // 点击小窗播放开关
  const onChangeIsOpenSmallWindowFun = (checked) => {
    setIsOpenSmallWindow(checked)
    onChangeIsOpenSmallWindow()
  }

  return (
    <Modal
      visible={visible}
      className={styles.modal}
      destroyOnClose={true}
      getContainer={() => document.body}
      closeOnMaskClick={true}
      onClose={props.onCancel}
      content={
        <div>
          {/* 标题 */}
          <div className={styles.header}>更多设置</div>

          {/* 开关 */}
          <div className={styles.action_item}>
            <div className={styles.action_item_label}>不允许非参会人进入</div>
            <Switch
              style={{
                '--checked-color': '#0095FF',
                '--height': '26PX',
                '--width': '48PX',
              }}
              checked={isSpectatorUse == 0}
              onChange={onChangeIsSpectatorUse}
            />
          </div>
          <div className={styles.action_item}>
            <div className={styles.action_item_label}>允许申请发言</div>
            <Switch
              style={{
                '--checked-color': '#0095FF',
                '--height': '26PX',
                '--width': '48PX',
              }}
              checked={handUpType == 1}
              onChange={onChangeHandUpType}
            />
          </div>
          <div className={styles.action_item}>
            <div className={styles.action_item_label}>展示会议密码</div>
            <Switch
              style={{
                '--checked-color': '#0095FF',
                '--height': '26PX',
                '--width': '48PX',
              }}
              disabled={!password}
              checked={isShowPassword == 1}
              onChange={onChangeIsShowPassword}
            />
          </div>
          <div className={styles.action_item}>
            <div className={styles.action_item_label}>小窗播放</div>
            <Switch
              style={{
                '--checked-color': '#0095FF',
                '--height': '26PX',
                '--width': '48PX',
              }}
              checked={isOpenSmallWindow}
              onChange={onChangeIsOpenSmallWindowFun}
            />
          </div>
        </div>
      }
    />
  )
}
export default connect(({ loading, PlanetChatRoom }: any) => ({ loading, PlanetChatRoom }))(Index)
