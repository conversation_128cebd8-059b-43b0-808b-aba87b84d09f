import React, { useMemo, useState, useEffect } from 'react';
import { Avatar, message } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { randomColor, processNames } from '@/utils/utils';
import styles from './index.less';

import clockImg from '@/assets/Major/clockIcon.png';
import addressImg from '@/assets/Major/addressIcon.png';

interface IProps {
  itemData?: any;
  styleType?: string;
  pcOrMobileMode?: string;
}

const Item: React.FC<IProps> = ({ itemData, styleType, pcOrMobileMode }) => {
  const { redirectInfo, tags, eventStartDate, eventEndDate, price, location, lecturersInfo, eventName, summary } = itemData;
  const [jumpType, setJumpType] = useState(true); // 点击按钮跳转 还是点击卡片跳转
  const [msgWarning, setMsgWarning] = useState(false); //是否有外链提示弹窗，如果有不可重复点击
  const [moreMaxCount, setMoreCount] = useState(false); //是否比最多显示值多，判断临界值

  // 讲师头像 1.无价格 少于5个平铺，多余5个显示省略号   2.有价格,最多显示两个，多的省略号   3.有两个按钮

  const getLecturersInfoSlice = (lecturersInfo, price, moreCountLimit) => {
    const limit = price ? moreCountLimit : moreCountLimit === 5 ? moreCountLimit : moreCountLimit;
    if (lecturersInfo.length > limit) {
      setMoreCount(true);
      return lecturersInfo.slice(0, limit);
    }
    return lecturersInfo;
  };

  const lecturersInfoMemo = useMemo(() => {
    if (redirectInfo.length < 2) {
      return getLecturersInfoSlice(lecturersInfo, price, price ? 2 : 5);
    } else {
      return getLecturersInfoSlice(lecturersInfo, price, price ? 1 : 2);
    }
  }, [lecturersInfo]);

  // 按钮的颜色排序，深蓝在最后
  const redirectInfoMemo = useMemo(() => {
    if (redirectInfo.length === 2 && redirectInfo.some(item => item.redirectColor !== '#009DFF') && redirectInfo.some(item => item.redirectColor === '#009DFF' && redirectInfo[0].redirectColor === '#009DFF')) {
      return [redirectInfo[1], redirectInfo[0]]
    } else {
      return redirectInfo
    }
  }, [redirectInfo])

  // 时间格式化函数
  const formatTime = (time) => {
    // 由于要求全部都不带年份，所以这里忽略 includeYear 参数
    return time.includes('00:00:00')
      ? dayjs(time).format('MM.DD')
      : dayjs(time).format('MM.DD HH:mm')
  };

  // 时间处理函数
  const majorTime = (startTime, endTime, type = 'all') => {

    // 如果两个时间相同，并且类型为 'only' 或者时间相同，只显示一个时间
    if ((startTime === endTime) || type === 'only') {
      return formatTime(startTime);
    }

    // 如果两个时间不同，显示两个时间
    return `${formatTime(startTime)} - ${formatTime(endTime)}`;
  };


  // 计算倒计时
  const countDown = (targetDateStr: string) => {
    const now = dayjs();
    const targetDate = dayjs(targetDateStr);
    const isPast = targetDate.isBefore(now, 'second'); // 检查目标日期是否已经过去

    if (isPast) {
      return []
    } else {
      const duration = targetDate.diff(now, 'second'); // 计算剩余的秒数
      const days = Math.floor(duration / (3600 * 24));
      const hours = Math.floor((duration % (3600 * 24)) / 3600);
      const minutes = Math.floor((duration % 3600) / 60);

      // 使用 .padStart(2, '0') 确保每个值都是两位数的字符串
      const formattedDays = String(days).padStart(2, '0');
      const formattedHours = String(hours).padStart(2, '0');
      const formattedMinutes = String(minutes).padStart(2, '0');

      return [formattedDays, formattedHours, formattedMinutes];
    }
  };


  {/* 线上： 已过期  七天内  七天后*/ }
  const timeRange = useMemo(() => {
    // 计算倒计时
    const countdown = countDown(eventStartDate);

    // 判断时间范围
    if (!countdown.length) {
      return '1'; // 已过期
    } else if (countdown[0] > 7) {
      return '3'; // 七天后
    } else {
      return '2'; // 七天内
    }
  }, [eventStartDate, styleType]);


  // 跳转  按钮或者卡片
  //选择外链时，前台跳转外链前弹窗提示：即将跳转到外站链接，请注意保护隐私等安全哦！  预设type = 4是外链 type=2 聊天室
  const handleJump = (canJump: boolean, type?: string, linkUrl?: string) => {
    if (canJump) {
      if (type === '4') {
        setMsgWarning(true)
        const messageKey = 'jumpWarning';
        if (!msgWarning) {
          message.warning('即将跳转到外站链接，请注意保护隐私等安全哦！', () => {
            window.location.href = linkUrl;
            message.destroy(messageKey)
            setMsgWarning(false)
          });
        }
      } else {
        window.location.href = linkUrl;
      }
    }
  }

  // 按钮长度为1且没有按钮文案时，点击当前整个卡片都可跳转
  // 按钮长度有多个且都没有按钮文案，点击当前整个卡片都可跳转
  useEffect(() => {
    if ((redirectInfo.length === 1 && !redirectInfo[0].redirectText) || (redirectInfo.length && redirectInfo.every(item => !item.redirectText))) setJumpType(false)
  }, [redirectInfo])


  //线上课UI
  const headerDom = () => {
    const time = countDown(eventStartDate);
    if (timeRange === '2') {
      return <div className={classNames(styles.header_redtip, styles.header_box)}>
          <span className={styles.redBg}>{time[0]}</span>天
          <span className={styles.redBg}>{time[1]}</span>时
        <span className={styles.redBg}>{time[2]}</span>分</div>
    } else if (timeRange === '3') {
      return <div className={classNames(styles.header_bluetip, styles.header_box)}>
          <span className={styles.blueBg}>{time[0]}</span>天
          <span className={styles.blueBg}>{time[1]}</span>时
        <span className={styles.blueBg}>{time[2]}</span>分</div>
    } else {
      return null
    }
  }

  return <div
      className={classNames(styles.normal_item, {
      [styles.normal_pc_item]: pcOrMobileMode === 'pc'
      })}
    onClick={jumpType ? () => handleJump(false) : () => handleJump(true, redirectInfo[0].redirectType, redirectInfo[0].redirectUrl)}>
      <div className={styles.header}>
        <div className={styles.header_box}>
          <img src={clockImg} />
          {eventStartDate && eventEndDate && <span>{majorTime(eventStartDate, eventEndDate)}</span>}
        </div>
      {(styleType === '1' && location) && <div className={styles.header_box}> <img src={addressImg} />
        <span>{location.split('/')[1]}</span></div>}

        {/* 线上： 已过期  七天内  七天后*/}
        {styleType === '2' && headerDom()}
      </div>

      <div className={styles.content}>
      {tags && (<div className={styles.tags}>
        {tags.map(v => {
              return (
                <span
                  key={v.tagName}
                  style={{
                    background: `#${v.tagColor}1A`,
                    color: `#${v.tagColor}`,
                  }}
                  className={styles.tagSpan}
                >
                  {v.tagName}
                </span>
              );
            })}
      </div>)}

        <div className={styles.title}>{eventName}</div>
        <div className={styles.detail}>{summary}</div>
      </div>

    {(price || lecturersInfoMemo?.length || (jumpType && redirectInfoMemo))&& (
        <div className={styles.footer}>
          <div className={styles.footer_box}>
            <Avatar.Group
            >
              {lecturersInfoMemo.map(
                (item: { lecturerName: string; lecturerPhotoView: string }, index: number) => {
                  if (item.lecturerPhotoView)
                    return <Avatar key={item.lecturerName} src={item.lecturerPhotoView} />;
                  return (
                    <Avatar key={item.lecturerName} style={{ backgroundColor: randomColor(index) }}>
                      {processNames(item.lecturerName)}
                    </Avatar>
                  );
                },
              )}
              {moreMaxCount && (
                <Avatar key={'more'} style={{ backgroundColor: randomColor(2) }}>
                  {'...'}
                </Avatar>
              )}
            </Avatar.Group>
          </div>
          <div className={styles.footer_box}>
            {price && <span>￥{price}/人</span>}

         {jumpType && redirectInfoMemo && redirectInfoMemo?.map(item => {
           if (item.redirectText) return (<span
                      key={item.text}
                      className={styles.footer_btn}
                      style={{
                        background: `${item.redirectColor}`,
               color: item.redirectColor === '#EDF9FF' ? '#0083D4' : '#ffffff'
                      }}
                      onClick={() => handleJump(true, item.redirectType, item.redirectUrl)}
           > {item.redirectText}</span>)
              })}
          </div>
        </div>
    ) }
   
    </div>
}

export default Item;