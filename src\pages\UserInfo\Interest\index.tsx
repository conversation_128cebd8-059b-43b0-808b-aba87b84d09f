/**
 * @Description: 关注
 */
import React, { useState, useEffect } from 'react';
import styles from './index.less';
import { Spin } from 'antd';
import { InfiniteScroll } from 'antd-mobile';
import { connect, history } from 'umi';
import NavBar from '@/components/NavBar'; // 头部返回
import ExpertList from '@/components/ExpertList';
import noDataImg from '@/assets/GlobalImg/no_data.png';
import { getOperatingEnv } from '@/utils/utils';

const tabList = [
  {id: 0, text: '全部'},
  {id: 1, text: '专家'},
  {id: 2, text: '普通用户'},
]

const initState: {
  pageNum: number;
  pageSize: number;
  total: number;
  dataList: DateItem[];
} = {
  pageNum: 1, // 当前页
  pageSize: 30, // 每页条数
  total: 0, // 总条数
  dataList: [], // 病例数据集合
};

const Index: React.FC = (props: any) => {
  const { dispatch, loading, tabState } = props || {};
  const {interestTabState} = tabState || {};
  const [tabType, setTabType] = useState(interestTabState || 0); // 当前tab切换状态
  const [state, setState] = useState(initState); // 列表数据
  const [hasMore, setHasMore] = useState(true); // 是否加载更多数据

  const {pageNum, total, dataList} = state;

  useEffect(() => {
    if (getOperatingEnv() === '4' ) {
      // 设置pc tab页为关注
      dispatch({
        type: 'pcAccount/save',
        payload: {
          tabState: 3,
          subTabState: null,
        }
      })
      history.replace('/UserInfo')
    }
  }, [dispatch]);

  useEffect(() => {
    getListData()
  }, [tabType])

  // 获取关注数据
  const getListData = async () => {
    await dispatch({
      type: 'userInfoStore/getH5FocusList',
      payload: {
        pageNum,
        pageSize: 30,
        isExperts: tabType == 2 ? 0 : tabType == 1 ? 1 : null, // 是否是专家：0:否，1:是, 不传是全部
      }
    }).then(res => {
      const { code, content} = res || {};
      if (res && code == 200) {
        const { total: responseTotal, resultList } = content || {};
        setState((prevState) => ({
          ...prevState,
          dataList: pageNum == 1 ? resultList : [...prevState.dataList, ...resultList],
          total: responseTotal,
          pageNum: prevState.pageNum + 1,
        }));
      } else {
        console.log('数据失败!');
      }
    })
  }

  // tab切换
  const tabSwitch = async (val:number) => {
    setTabType(val)
    setState((state) => ({
      ...state,
      dataList: [],
      total: 0,
      pageNum: 1,
    }));

    // 保存到仓库中当前切换的tab值
    dispatch({
      type: 'tabState/save',
      payload: {
        interestTabState: val
      }
    })
  }

  // 监听数据变化，如果数据达到total，则没有更多了
  useEffect(() => {
    if (dataList.length >= total) {
      setHasMore(false);
    } else {
      setHasMore(true);
    }
  }, [dataList, total]);

  // 加载更多
  const loadMore = async () => {
    await getListData()
  }

  const getH5FocusListLoading = !!loading.effects['userInfoStore/getH5FocusList']; // loading
  return (
    <div className={styles.interest_wrap}>
      <NavBar title={'关注'} style={{background: '#FAFAFA'}} onBack={()=>{history.replace('/UserInfo')}}></NavBar>
      <div className={styles.tab_box}>
        {
          tabList.map((item, ind) => {
            return <span key={item.id} className={tabType === ind ? styles.tab_item_active : ''} onClick={() => tabSwitch(item.id)}>{item.text}</span>
          })
        }
      </div>
      <Spin spinning={getH5FocusListLoading}>
        <div className={styles.interest_content}>
          {
            state.dataList && state.dataList.length ? 
            <>
              <ExpertList isHideContent={true} dataSource={state?.dataList}/>
              <InfiniteScroll threshold={100} loadMore={loadMore} hasMore={hasMore}/>
            </> :
            <div className={styles.none_data}>
              <img src={noDataImg} alt="" />
              暂无关注
            </div>
          }  
        </div>
      </Spin>
    </div>
  )
}
export default connect(({ tabState, userInfoStore, loading }: any) => ({tabState, userInfoStore, loading}))(Index)