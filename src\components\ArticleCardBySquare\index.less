.article_wrap {
  width: 100%;
  height: auto;
  background: #fff;
  padding: 16px;
  margin-bottom: 10px;
}

// 文章内容
.article_content {
  padding: 12px 14px;
  background: #F8F8F9;
  border-radius: 4px;
  margin-bottom: 12px;
  .article_title {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /* 指定显示2行 */
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    font-size: 15px;
    color: #222;
    font-weight: 500;
    line-height: 23px;
    margin-bottom: 4px;
  }
  .article_user {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 12px;
    color: #777;
    line-height: 18px;
    margin-bottom: 8px;
  }
  .article_img_wrap {
    display: flex;
    flex-wrap: nowrap;
    column-gap: 4px;
    margin-bottom: 8px;
    .article_img {
      flex: 1;
      img {
        object-fit: cover;
      }
    }
    .article_img:first-child {
      img {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
      }
    }
    .article_img:last-child {
      img {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
      }
    }
    video {
      width: 100%;
      height: 100%;
    }
  }
  .article_btn_wrap {
    display: flex;
    justify-content: flex-end;
    .article_btn {
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #0095FF;
      line-height: 18px;
    }
  }
}
