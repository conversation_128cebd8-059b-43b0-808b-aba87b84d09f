.modal {
  :global {
    .ant-modal-header {
      border-bottom: 0;
    }
    .ant-modal-title {
      font-size: 20px;
      color: #000;
    }
    .ant-modal-close-x {
      font-size: 20px;
    }
    .ant-modal-body {
      padding: 0;
    }
  }
}

.container {
  width: 100%;
  position: relative;
  padding-bottom: 161px;
}

.content {
  padding: 24px 0;
  display: flex;
  justify-content: center;
  img {
    border-radius: 16px;
  }
}

.bottom_wrap {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 900;
  width: 100%;
  height: 161px;
  padding: 15px 0 0;
  background: #fff;
  .bottom_title {
    margin-bottom: 8px;
    font-size: 14px;
    color: #666;
    line-height: 20px;
    padding: 0 16px;
  }
  .bottom_template_wrap {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    column-gap: 12px;
    padding: 0 16px 12px;
    .template_option {
      width: auto;
      height: auto;
      border-radius: 6px;
      position: relative;
      cursor: pointer;
      & > img {
        object-fit: cover;
        border-radius: 6px;
      }
      &.checked::after {
        content: "";
        display: block;
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        border: 2px solid #0095FF;
        border-radius: 6px;
        background-image: url("../../../assets/GlobalImg/check_icon_2.png");
        background-repeat: no-repeat;
        background-size: 24px 24px;
        background-position: 102% 103%;
      }
    }
  }
}

.footer {
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #ddd;
  padding: 21px 24px;
  :global {
    .ant-typography {
      margin-bottom: 0;
      margin-right: 24px;
    }
  }
}
