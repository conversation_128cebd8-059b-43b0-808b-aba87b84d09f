.wrap {
  width: 100%;
  height: 100vh;
  background: #fff;
  overflow-y: auto;
  .header{
    width: 100%;
    height: 68px;
    background: #FFFFFF linear-gradient(135deg, #FFFFFF 0%, #E4EEFC 100%);
    //position: absolute;
    //left: 0;
    //right: 0;
    //top: 0;
    // height: 100px;
    // background: #FFF linear-gradient(135deg, #FFF 0%, #E4EEFC 100%);
    // .nav_bar{
    //   background: transparent;
    // }
  }
  .user_info_wrap {
    border-bottom: 8px solid #f5f6f8;
    padding-bottom: 20px;
    .user_info_bg {
      // padding-top: 38px;
      width: 100%;
      min-height: 38px;
      background: #FFFFFF linear-gradient(135deg, #FFFFFF 0%, #E4EEFC 100%);
    }

    .user_info_box {
      width: 100%;
      padding: 12px 12px 0;
      background: #FFFFFF;
      border-radius: 12px 12px 0px 0px;

      .user_info_img {
        display: flex;
        justify-content: flex-end;
        position: relative;
        margin-bottom: 16px;
        .head_sculpture {
          width: 72px;
          height: 72px;
          border-radius: 50%;
          border: 3px solid #FFFFFF;
          overflow: hidden;
          position: absolute;
          left: 0;
          top: -36px;
          z-index: 998;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          font-weight: 500;
          color: #FFFFFF;
          line-height: 34px;

          img {
            width: 72px;
            height: 72px;
          }
        }
        .no_head_sculpture {
          width: 72px;
          height: 72px;
          border-radius: 50%;
          border: 3px solid #FFFFFF;
          overflow: hidden;
          position: absolute;
          left: 0;
          top: -36px;
          z-index: 998;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          font-weight: 500;
          color: #FFFFFF;
          line-height: 34px;
        }


        .follow_text {
          height: 28px;
          background: #EDF9FF;
          border-radius: 14px;
          font-size: 14px;
          font-weight: 400;
          color: #0095FF;
          line-height: 28px;
          padding: 0 8px;

          img {
            width: 16px;
            height: 16px;
            position: relative;
            top: -1px;
          }
        }

        .unfollow_text {
          height: 28px;
          background: #F5F5F5;
          border-radius: 14px;
          font-size: 14px;
          font-weight: 400;
          color: #ccc;
          line-height: 28px;
          padding: 0 8px;

          img {
            width: 16px;
            height: 16px;
            position: relative;
            top: -1px;
          }
        }
      }

      .top_box {
        display: flex;
        align-items: end;
        margin-bottom: 4px;

        .name {
          font-size: 24px;
          font-weight: 500;
          color: #000000;
          line-height: 34px;
          margin-right: 8px;
          max-width: 130px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .doctor_text {
          font-size: 14px;
          font-weight: 400;
          color: #666666;
          line-height: 20px;
          margin-right: 8px;
          flex-shrink: 0;
        }

        .discipline_type {
          padding: 2px 4px;
          background: #EEFFF9;
          border-radius: 2px;
          border: 1px solid #B0EAD9;
          font-size: 12px;
          font-weight: 400;
          color: #06A777;
          line-height: 17px;
          flex-shrink: 0;
        }
      }

      .institution {
        font-size: 14px;
        font-weight: 400;
        color: #666666;
        line-height: 20px;
        margin-bottom: 4px;
      }

      .bottom_box {
        display: flex;
        align-items: center;

        .info_data_item {
          font-size: 14px;
          font-weight: 400;
          color: #666666;
          line-height: 20px;
          margin-right: 8px;
        }
      }

    }

    .brief_introduction_wrap {
      width: 100%;
      height: auto;
      background: #fff;
      padding: 16px 12px 15px;
      box-sizing: border-box;

      .brief_introduction_box {
        width: 100%;
        height: auto;
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;

        .brief_introduction_title {
          font-size: 17px;
          font-weight: 600;
          color: #000000;
          line-height: 24px;
        }

        .brief_introduction_btn {
          font-size: 14px;
          font-weight: 400;
          color: #0095FF;
          line-height: 20px;

          img {
            width: 16px;
            height: 16px;
          }
        }
      }

      .brief_introduction_show {
        display: flex;
        flex-direction: column;
        white-space: pre-wrap;
        font-size: 14px;
        font-weight: 400;
        color: #666666;
        line-height: 20px;
      }

      .brief_introduction_hide {
        display: block;
        white-space: pre-wrap;
        font-size: 14px;
        font-weight: 400;
        color: #666666;
        line-height: 20px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        height: 40px;
        -webkit-line-clamp: 2;
        text-overflow: ellipsis;
      }
    }
  }

  .content {
    width: 100%;
    height: auto;
    background: #fff;
    margin-bottom: 58px;

    .tab_wrap {
      width: 100%;
      display: flex;
      padding: 12px 0 7px 12px;
      align-items: center;
      margin-bottom: 12px;
      border-bottom: 1px solid #DDDDDD;

      .tab_init {
        font-size: 16px;
        font-weight: 400;
        color: #666666;
        line-height: 19px;
        margin-right: 20px;

        &:nth-last-child(1) {
          margin-right: 0;
        }
      }

      .tab_active {
        font-size: 16px;
        font-weight: 400;
        color: #000;
        line-height: 19px;
        position: relative;
        font-weight: 600;

        &::after {
          content: '';
          width: 12px;
          height: 3px;
          background: #000000;
          border-radius: 6px;
          position: absolute;
          top: 130%;
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }

    .tab_content_list {
      width: 100%;
      height: auto;
    }
  }

  .bottom_btn_wrap {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 998;
    height: 58px;
    background: #fff;
    display: flex;
    padding: 9px 16px 34px;
    border-top: 1px solid #EEEEEE;

    .bottom_share_btn ,.bottom_share_btn_no {
      flex: 1;
      background: #07C160;
      border-radius: 20px;
      font-size: 16px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 40px;

      img {
        width: 24px;
        height: 24px;
        margin-right: 4px;
      }
    }
    .bottom_share_btn_no{
      background: #b5b5b5;
    }
    .ask_experts_btn {
      margin-left: 12px;
      flex-shrink: 0;
      width: 221px;
      height: 40px;
      background: #0095FF;
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 24px;
        height: 24px;
        margin-right: 4px;
      }

      .price_style_box {
        text-align: center;

        .title_style {
          font-size: 16px;
          font-weight: 400;
          color: #FFFFFF;
          line-height: 22px;
        }

        .price_style {
          font-size: 12px;
          font-weight: 500;
          color: rgba(255,255,255,0.7);
          line-height: 17px;
        }
      }
    }
  }
}

.fixed_share_box {
  display: none;
  position: fixed;
  z-index: 1000;
  right: 21px;
  top: 24px;
  &.fixed_share_box_show {
    display: block;
  }
  .icon1 {
    position: relative;
    display: block;
    background: url("../../../assets/GlobalImg/arrow_up.png") no-repeat right center;
    background-size: 122px 108px;
    width: 100%;
    height: 108px;
  }
  .message_box {
    position: relative;
    font-size: 18px;
    font-weight: 400;
    line-height: 25px;
    color: #fff;
    white-space: nowrap;
    top: -18px;
    margin-bottom: 14px;
  }
  .icon_box {
    display: flex;
    align-items: center;
    justify-content: center;
    i {
      display: block;
      width: 48px;
      height: 48px;
      &.icon2 {
        background: url("../../../assets/GlobalImg/share_one.png") no-repeat center;
        background-size: 100% 100%;
        margin-right: 24px;
      }
      &.icon3 {
        background: url("../../../assets/GlobalImg/share_more.png") no-repeat center;
        background-size: 100% 100%;
      }
    }
  }
}
