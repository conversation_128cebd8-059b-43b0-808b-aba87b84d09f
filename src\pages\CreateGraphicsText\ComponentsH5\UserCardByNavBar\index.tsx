import React from 'react';
import { history } from 'umi';
import { processNames, randomColor } from '@/utils/utils'
import Follow from '@/components/Follow'
import MoreOperate from '@/components/MoreOperate'
import styles from './index.less';
import expertCertificationIcon from '@/assets/GlobalImg/expertCertificationIcon.png'

interface PropsType {
  headUrlShow: string,                 // 头像
  userName: string,                    // 用户名
  createUserId: number,                // 用户ID
  isExperts: number,                   // 是否是专家
  operateDateDescs: string,            // 日期
  isFocus: number,                     // 是否关注
  expertsInfo: any,                    // 专家
  id?: number,                          // id
  imageType?: number,                   // 类型
  status?: number,                      // 状态
  handleFollowAndCheck: () => void,
  handleDeleteOrLow?: () => void,
}

const Index: React.FC<PropsType> = (props: PropsType) => {
  const UserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')        // 用户信息
  const {
    headUrlShow, userName, createUserId, isExperts, operateDateDescs, isFocus, expertsInfo, id, imageType, status,
    handleDeleteOrLow,
  } = props;

  // 返回
  const goBack = () => {
    if (history.length > 1) {
      history.goBack()
    } else {
      history.replace('/')
    }
  }

  return (
    <div className={styles.nav_bar}>
      <i className={styles.back_icon} onClick={goBack}></i>
      <div className={styles.header}>
        <div className={styles.header_left}>
          <div className={styles.left_avatar} style={headUrlShow ? {backgroundImage: `url(${headUrlShow})`} : {backgroundColor: `${randomColor(createUserId)}`}}>
            {headUrlShow ? '' : processNames(userName)}
          </div>
          <div className={styles.left_info_wrap}>
            <div className={styles.user_info}>
              <span className={styles.user_name}>{userName}</span>
              {
                isExperts == 1 && expertsInfo &&
                <>
                  <span className={styles.user_gray_bar}></span>
                  <span className={styles.user_grade}>{expertsInfo.postTitleDictName}</span>
                  <span className={styles.user_tag}>{expertsInfo.depSubjectDictName}·{expertsInfo.abilityLevelDictName}</span>
                </>
              }
            </div>
            <div className={styles.date}>
              {operateDateDescs}
              {isExperts == 1 && expertsInfo && `·${expertsInfo.organizationName}`}
              {
                isExperts == 1 && expertsInfo &&
                <img src={expertCertificationIcon} style={{marginLeft: 2, verticalAlign: 'text-top'}} width={12} height={12} alt=""/>
              }
             {/* {imageType == 3 && '·广告'}*/}
            </div>
          </div>
        </div>
        {
          UserInfo.friUserId != createUserId &&
          <div className={styles.header_right}>
            <Follow isFocus={isFocus} expertsUserId={createUserId} handleFollowAndCheck={props.handleFollowAndCheck}/>
          </div>
        }
      </div>
      {
        (imageType == 1 || imageType == 2 || imageType == 3) && UserInfo.friUserId == createUserId &&
        <div className={styles.right}>
          <MoreOperate id={id} imageType={imageType} status={status} handleDeleteOrLow={handleDeleteOrLow}/>
        </div>
      }
    </div>
  )
}
export default Index
