.HorizontalLiveRoom_camera_picture_Box {
  position: absolute;
  top: 0px;
  right: -140px;
  z-index: 2;
  width: 169px;
  height: 100%;
  overflow-y: auto;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;

  .HorizontalLiveRoom_camera_picture_btn {
    position: absolute;
    top: 50%;
    left: 0px;
    z-index: 1;
    display: block;
    width: 29px;
    height: 142px;
    background: url('~@/assets/PlanetChatRoom/HorizontalLiveRoom_camera_picture_btn.png') no-repeat;
    background-size: 29px 142px;
    transform: translateY(-50%);
    cursor: pointer;
    user-select: none;
  }

  .HorizontalLiveRoom_camera_picture_take_back_btn {
    background: url('~@/assets/PlanetChatRoom/HorizontalLiveRoom_camera_picture_take_back_btn.png')
      no-repeat;
    background-size: 29px 142px;
  }

  .HorizontalLiveRoom_camera_picture_camera_live {
    position: absolute;
    right: 0px;
    display: flex;
    flex-direction: column;
    width: 140px;
    height: 100%;
    overflow-y: auto;
    background: #222;
    opacity: 1;

    .noGuestsListWarp {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;

      .noGuestsListIcon {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
      .noGuestsIcon {
        display: inline-block;
        width: 24px;
        height: 24px;
        margin-bottom: 10px;
        background: url('~@/assets/PlanetChatRoom/SpatialDetail_no_guests_list_icon.png') no-repeat;
        background-size: 24px 24px;
        border: none;
      }
      .noGuestsText {
        color: #aaaaaa;
        font-weight: 400;
        font-size: 12px;
        line-height: 12px;
      }
    }

    .HorizontalLiveRoom_camera_picture_camera_item {
      position: relative;
      width: 140px;
      height: 90px;
      background: #efefef;
      border-radius: 0px 0px 0px 0px;
      opacity: 1;
      // margin-bottom: 3px;

      .HorizontalLiveRoom_camera_picture_camera_bottom_box {
        position: absolute;
        bottom: 0px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 140px;
        height: 24px;
        padding-right: 8px;
        padding-left: 8px;
        color: #ffffff;
        font-size: 12px;
        line-height: 24px;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 100%);
        border-radius: 0px 0px 0px 0px;
        opacity: 1;
      }

      .HorizontalLiveRoom_camera_picture_camera_bottom_box_lianmai {
        background: rgba(255, 255, 255, 0.8);
      }

      .HorizontalLiveRoom_camera_picture_camera_bottom_box_Name_Warp {
        display: flex;
        align-items: center;
      }

      .HorizontalLiveRoom_camera_picture_camera_bottom_box_Name {
        max-width: 50px;
        height: 24px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .HorizontalLiveRoom_camera_picture_camera_bottom_box_Name_lianmai {
        color: #000000;
      }

      .HorizontalLiveRoom_camera_picture_mic_icon {
        display: inline-block;
        width: 12px;
        height: 12px;
        margin-left: 4px;
        background: url('~@/assets/PlanetChatRoom/HorizontalLiveRoom_camera_picture_mic_icon.png')
          no-repeat;
        background-size: 12px 12px;
      }

      .HorizontalLiveRoom_camera_picture_forbidden_mic_icon {
        display: inline-block;
        width: 12px;
        height: 12px;
        margin-left: 4px;
        background: url('~@/assets/PlanetChatRoom/HorizontalLiveRoom_camera_picture_forbidden_mic_icon.png')
          no-repeat;
        background-size: 12px 12px;
      }

      .HorizontalLiveRoom_camera_picture_forbidden_Lianmai {
        display: inline-block;
        width: 12px;
        height: 12px;
        margin-left: 4px;
        background: url('~@/assets/PlanetChatRoom/SpatialDetail_MicrophoneOn_btn_Lianmai.png')
          no-repeat;
        background-size: 12px 12px;
      }

      .GuanZhu_btn {
        padding-top: 2px;
        padding-right: 4px;
        padding-bottom: 2px;
        padding-left: 4px;
        color: #0095ff;
        font-weight: 400;
        font-size: 12px;
        line-height: 12px;
        background: #edf9ff;
        border-radius: 14px 14px 14px 14px;
        cursor: pointer;
        opacity: 1;
        user-select: none;
      }

      .unGuanZhu_btn {
        color: #cccccc;
        background: #f5f5f5;
      }

      // 强制下麦按钮
      .forceXiaMai {
        padding-top: 2px;
        padding-right: 4px;
        padding-bottom: 2px;
        padding-left: 4px;
        color: #ffffff;
        font-weight: 400;
        font-size: 12px;
        line-height: 12px;
        background: #ff5f57;
        border-radius: 14px 14px 14px 14px;
        cursor: pointer;
        opacity: 1;
        user-select: none;
      }
    }
    .HorizontalLiveRoom_camera_picture_camera_item_hidden {
      // height: 0px;
      overflow: hidden;

      .headUrlWarp {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
      }

      .video_Title_box_left_avatar {
        width: 44px;
        height: 44px;
        // margin-right: 8PX;
        overflow: hidden;
        background: #f9b4e3;
        border-radius: 50%;
        opacity: 1;
      }

      .video_Title_box_left_avatar_img {
        width: 44px;
        height: 44px;
        overflow: hidden;
        border-radius: 50%;
      }

      .head_sculpture_name {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 44px;
        height: 44px;
        color: #ffffff;
        font-weight: 500;
        font-size: 16px;
        white-space: nowrap;
      }
    }

    .StreamWarp {
      width: 100%;
      height: 100%;
    }

    .StreamWarpHidden {
      height: 0px;
      overflow: hidden;
    }
  }
}
.isShowCameraList {
  position: absolute;
  top: 0px;
  right: -0px;
}
