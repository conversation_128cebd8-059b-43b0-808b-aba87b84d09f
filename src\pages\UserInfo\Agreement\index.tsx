/**
 * 隐私政策页面相关页面
 */
import React, { useEffect } from 'react';
import classNames from "classnames";
import styles from './index.less';
import NavBar from '@/components/NavBar';
import { getOperatingEnv } from '@/utils/utils';
import { connect, history } from 'umi';

const Agreement: React.FC = (props: any) => {
  const { dispatch } = props;
  // 生成8位随机数
  const pageKeyByRendom = Math.random().toString(36).substr(2, 8);

  useEffect(() => {
    if (getOperatingEnv() === '4' ) {
      // 设置pc tab页为设置
      dispatch({
        type: 'pcAccount/save',
        payload: {
          tabState: 5,
          subTabState: null,
        }
      })
      history.replace('/UserInfo')
    }
  }, [dispatch]);

  return (
    <div className={classNames({
      [styles.agreementWarp]: true,
    })}>
      <NavBar title={'隐私政策'}></NavBar>
      <div className={classNames(styles.agreementBox, {[styles.pc_agreementBox]: getOperatingEnv() === '4'})}>
        {Array.from({length: 15}).map((item, index) => {
          try {
            return (
              <img
                key={index}
                src={`https://static.jwsmed.com/public/DigitalHealth/Business/assets/privacyPolicy/${index + 1}.png?pageKeyByRendom=${pageKeyByRendom}`}
                alt=""
                onError={(e) => { e.target.style.display = 'none'; }}
              />
            )
          }catch (e) {
            console.log(e)
          }
        })}
      </div>
    </div>
  )
}
export default connect()(Agreement)