/**
 * @Description: 注销
 */
import React, {useEffect, useState} from 'react';
import { connect, history } from 'umi';
import { Spin } from 'antd';
import { Toast, Checkbox } from 'antd-mobile';
import styles from './index.less';
const Cancellation_Page_01 = 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/Cancellation_Page_01.png'; // 协议1
const Cancellation_Page_02 = 'https://static.jwsmed.com/public/DigitalHealth/Business/assets/UserInfo/Cancellation_Page_02.png'; // 协议2
import NavBar from '@/components/NavBar';
import { getOperatingEnv } from '@/utils/utils';

const imageSrcList = [Cancellation_Page_01, Cancellation_Page_02]

const Index: React.FC = (props: any) => {
	const { dispatch, loading } = props;
	const [checked, setChecked] = useState(false); // 选择状态

  useEffect(() => {
    if (getOperatingEnv() === '4' ) {
      // 设置pc tab页为设置
      dispatch({
        type: 'pcAccount/save',
        payload: {
          tabState: 5,
          subTabState: null,
        }
      })
      history.replace('/UserInfo')
    }
  }, [dispatch]);

  // 我要注销
  const onClickByCloseAccount = () => {
		if(checked) {
			dispatch({
				type: 'userInfoStore/unsubscribe'
			}).then(res => {
				if(res && res.code == 200) {
					Toast.show({content: '账号注销成功'});
					// 清除本地存储
					localStorage.clear();
					// 跳转到我的页面
					history.replace('/userInfo');
				} else {
					Toast.show({content: '账号注销失败!'})
				}
			})
		} else {
			Toast.show({content: '请勾选同意协议'})
		}
	}


	const unsubscribeLoadin = !!loading.effects['userInfoStore/unsubscribe']

  return (
    <div className={styles.Cancellation_Warp}>
      <NavBar title={'账号注销'}></NavBar>
      <div className={styles.Cancellation_Img_Warp}>
        {imageSrcList.map((item,index)=>{
          return (
            <img
              className={styles.PrivacyPolicy_item}
              src={`${item}?${Math.random()}`}
              width={'100%'}
              loading='lazy'
              key={index}
              alt=''
            />
          )
        })}
      </div>
      <div className={styles.Cancellation_Box}>
        <div className={styles.Cancellation_subheading}>
          <Checkbox style={{'--icon-size': '15px'}} onChange={(val) => {setChecked(val)}}>
            我已阅读并同意
            <span className={styles.cancellation_agreement}>
            《账号注销协议》
            </span>
          </Checkbox>
        </div>
        <Spin spinning={unsubscribeLoadin}>
            <div
              onClick={onClickByCloseAccount}
              className={styles.Cancellation_Btn}>
              我要注销
            </div>
		</Spin>
      </div>
    </div>
  )
}

export default connect(({ loading }: any) => ({loading}))(Index)
