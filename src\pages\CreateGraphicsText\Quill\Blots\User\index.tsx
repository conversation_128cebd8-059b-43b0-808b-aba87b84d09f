import { Quill } from 'react-quill'
const Parchment = Quill.import('parchment')

class UserBlot extends Parchment.Embed {
  static create(value) {
    let node = super.create();
    node.setAttribute('contenteditable', false);
    node.setAttribute('data-id', value.id);
    node.setAttribute('data-type', 'user');
    const text = document.createTextNode(value.name)
    node.appendChild(text)
    return node;
  }

  static value(domNode: Element) {
    return {
      id: domNode.dataset.id,
      name: domNode.innerText,
    }
  }

  format(name: string, value: string) {

  }
}

UserBlot.blotName = 'user';                                // 格式名
UserBlot.tagName = 'span';                                 // dom标签
UserBlot.className = 'quill_user_format_wrap';             // dom类名

export default UserBlot
